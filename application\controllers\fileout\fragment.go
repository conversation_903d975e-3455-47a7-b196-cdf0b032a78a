package fileout

import (
	"strconv"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/fileout"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/user/duser"

	"github.com/kataras/iris/v12"
)

type FragmentReq struct {
	Content string `json:"content"`
}

type FragmentResponse struct {
	UserId    uint                   `copier:"must" json:"user_id"`
	Content   string                 `copier:"must" json:"content"`
	UpdatedAt string                 `json:"updated_at"`
	CreatedAt string                 `json:"created_at"`
	User      duser.ApprovalResponse `gorm:"-" json:"user"`
}

type FragmentsResponse struct {
	FragmentResponse
}

func CreateFragment(ctx iris.Context) {
	userId, _ := dao.GetAuthId(ctx)
	fragmentReq := &FragmentReq{}
	if err := ctx.ReadJSON(fragmentReq); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*fragmentReq)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	object := map[string]interface{}{
		"Content":   fragmentReq.Content,
		"UserId":    userId,
		"CreatedAt": time.Now(),
	}
	err := easygorm.GetEasyGormDb().Model(&fileout.Fragment{}).Create(object).Error

	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	err = dao.AddLog(ctx, "文本片段外出", dao.ActionAdd, object)
	if err != nil {
		logging.ErrorLogger.Errorf("add log for creat fragment ", err)
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, fragmentReq, response.NoErr.Msg))
	return
}

// GetUsers
func GetFragments(ctx iris.Context) {
	userId, _ := dao.GetAuthId(ctx)
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	var count int64
	var fragments []*FragmentsResponse

	db := easygorm.GetEasyGormDb().Model(&fileout.Fragment{}).Where("user_id = ?", userId)
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&fragments).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		// return nil, err
	}
	list := map[string]interface{}{"items": fragments, "total": count, "limit": pageSize}
	err = dao.AddLog(ctx, "文本片段外出", dao.ActionList, list)
	if err != nil {
		logging.ErrorLogger.Errorf("add log for list fragment ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}
