# 统一MR合并操作方法设计与实现

## 任务概述

基于现有的MR合并代码，设计并实现一个统一的MR合并操作方法，具体包括双重合并策略（GitLab API + Session请求）、完整的错误处理和状态管理。

## 实施方案

### 1. 核心设计

#### 1.1 统一合并服务架构
- **文件位置**: `application/controllers/mergerequest/merge_service.go`
- **核心方法**: 
  - `UnifiedMergeRequest`: 基础合并方法
  - `UnifiedMergeRequestWithContext`: 带业务上下文的合并方法

#### 1.2 双重合并策略
1. **第一阶段**: GitLab API方式，最多重试5次
2. **第二阶段**: 如果API方式全部失败，自动切换到Session请求方式
3. **状态验证**: 每次合并后验证MR状态是否为"merged"

#### 1.3 参数结构设计
```go
type MergeRequestParams struct {
    ProjectID         int    // 项目ID
    MergeRequestIID   int    // MR IID
    SHA               string // SHA值
    GitlabSession     string // Session信息（用于Session方式）
    CSRFToken         string // CSRF Token（用于Session方式）
    PathWithNamespace string // 项目路径（用于Session方式）
}

type MergeRequestContext struct {
    MergeRequest   *dmergerequest.MergeRequest
    ControlReviews *dmergerequescontrolreview.Response
    Comment        string
    EnableSession  bool
}
```

### 2. 具体改造内容

#### 2.1 HandleMrControlReview函数改造
**改造位置**: `application/controllers/mergerequest/mergerproxy.go` 第1170-1263行

**改造前问题**:
- 有5次API重试逻辑，但没有Session备用方式
- 合并逻辑嵌入在业务逻辑中，代码复杂
- SHA校验逻辑分散

**改造后优势**:
- 统一的合并接口调用
- 自动的SHA校验和状态重置
- 保持原有的业务流程（依赖检查、邮件通知、依赖MR合并）
- 代码简化，逻辑清晰

#### 2.2 attemptToMergeMR函数改造
**改造位置**: 
- `application/controllers/mergerequest/mergerproxy.go` 第2026-2042行
- `application/controllers/appmergerequest/mergerproxy.go` 第1965-1981行

**改造前问题**:
- 没有重试逻辑
- 没有Session备用方式
- 没有SHA校验

**改造后优势**:
- 现在有了5次API重试 + Session备用
- 有了SHA校验逻辑
- 保持了原有的业务逻辑（更新审批单、发送邮件）

### 3. 保留的现有逻辑

#### 3.1 SHA值校验
- 在合并前自动进行SHA值校验
- SHA不匹配时自动重置MR状态
- 自动关闭审批单并发送邮件通知

#### 3.2 合并状态检查
- 每次合并操作后验证"merged"状态
- 支持已合并状态的检测，避免重复合并

#### 3.3 错误处理和状态重置
- 合并失败时自动重置ButtonClickStatus为0
- 清空ButtonClickUser和MergeRequestSHA
- 发送邮件通知相关人员

#### 3.4 业务流程集成
- 管控审核状态更新
- 成功邮件通知
- 依赖MR合并触发

### 4. 配置化设计

#### 4.1 MergeConfig结构
```go
type MergeConfig struct {
    MaxRetries    int  // 最大重试次数，默认5
    EnableSession bool // 是否启用Session备用
    ValidateSHA   bool // 是否进行SHA校验，默认true
}
```

#### 4.2 灵活配置
- 不同场景可以使用不同的配置
- HandleMrControlReview: 不启用Session备用
- attemptToMergeMR: 不启用Session备用（自动合并场景）
- 其他需要Session的场景: 可启用Session备用

### 5. 集成优势

#### 5.1 代码复用
- 消除了重复的合并逻辑
- 统一的错误处理和状态管理
- 一致的日志记录

#### 5.2 功能增强
- 为所有调用方提供了双重合并策略
- 统一的SHA校验逻辑
- 更强的容错能力

#### 5.3 可维护性
- 合并逻辑集中管理
- 便于后续功能扩展
- 清晰的接口设计

### 6. 测试建议

#### 6.1 单元测试场景
1. API方式5次重试成功
2. API方式失败，Session方式成功
3. SHA不匹配的处理
4. 全部失败的错误处理
5. 已合并状态的检测

#### 6.2 集成测试
1. HandleMrControlReview的完整流程测试
2. attemptToMergeMR的自动合并测试
3. 错误情况下的状态重置测试
4. 邮件通知功能测试

## 实施状态

- [x] 创建统一合并服务 `merge_service.go`
- [x] 修改 `HandleMrControlReview` 函数
- [x] 修改 `attemptToMergeMR` 函数（mergerequest模块）
- [x] 修改 `attemptToMergeMR` 函数（appmergerequest模块）
- [x] 改造 `MergeHandler` 函数（mergerequest模块）
- [x] 改造 `MergeHandler` 函数（appmergerequest模块）
- [x] 单元测试框架编写
- [ ] 集成测试验证
- [ ] 生产环境部署

## 额外完成的改造

### 3. MergeHandler函数改造
**改造位置**:
- `application/controllers/mergerequest/mergerproxy.go` 第192-220行
- `application/controllers/appmergerequest/mergerproxy.go` 第154-182行

**改造前问题**:
- 只有简单的API+Session双重策略，没有重试机制
- 错误处理不够完善
- 代码重复

**改造后优势**:
- 现在有了5次API重试 + Session备用
- 统一的错误处理和日志记录
- 返回详细的合并方法信息（API或Session）

## 注意事项

1. **向后兼容**: 保持现有API接口不变
2. **错误处理**: 确保错误信息格式与现有代码兼容
3. **并发安全**: HandleMrControlReview中的workingReviews锁机制已保留
4. **事务处理**: 数据库更新操作的事务一致性需要验证
