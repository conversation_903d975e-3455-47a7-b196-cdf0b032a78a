package opensource

import (
	"time"

	"irisAdminApi/application/models"
)

const (
	// ResponsibleUserPermissionType 负责人
	ResponsibleUserPermissionType = 1
	// ResponsibleLeaderPermissionType 专业组长
	ResponsibleLeaderPermissionType = 3
	// DelegatedUserPermissionType 委派人员
	DelegatedUserPermissionType = 4

	// VulnerabilityStatusProcessing 处理中
	VulnerabilityStatusProcessing = 1
	// VulnerabilityStatusConfirmed 已确认
	VulnerabilityStatusConfirmed = 2
	// VulnerabilityStatusIgnored 已忽略
	VulnerabilityStatusIgnored = 3

	// VulnerabilityHistoryStatusConfirmed 确认
	VulnerabilityHistoryStatusConfirmed = VulnerabilityStatusConfirmed
	// VulnerabilityHistoryStatusIgnored 忽略
	VulnerabilityHistoryStatusIgnored = VulnerabilityStatusIgnored
	// VulnerabilityHistoryStatusDelegated 委派
	VulnerabilityHistoryStatusDelegated = 4
	// VulnerabilityHistoryStatusCancelDelegated 取消委派
	VulnerabilityHistoryStatusCancelDelegated = 5

	// ComponentTempCheckStatusProcessing 组件预查中
	ComponentTempCheckStatusProcessing = 1
	// ComponentTempCheckStatusSucceeded 组件预查成功
	ComponentTempCheckStatusSucceeded = 2
	// ComponentTempCheckStatusFailed 组件预查失败
	ComponentTempCheckStatusFailed = 3
)

type OpenSourceComponent struct {
	models.ModelBase
	Name            string     `gorm:"not null; type:varchar(60)" json:"name"`
	Url             string     `gorm:"type:varchar(300)" json:"url"`
	Version         string     `gorm:"type:varchar(60)" json:"version"`
	Product         string     `gorm:"type:varchar(60)" json:"product"`
	ProductVersion  string     `gorm:"type:varchar(60)" json:"product_version"`
	Enable          bool       `gorm:"not null; default:false" json:"enable"`
	IsExternalServe bool       `gorm:"not null;default: false" json:"is_external_serve"`
	SyncTime        *time.Time `json:"sync_time"`
}

type OpenSourceComponentPermission struct {
	models.ModelBase
	ComponentID uint `gorm:"not null;index:idx_component_id" json:"component_id"`
	UserID      uint `gorm:"not null" json:"user_id"`
	Type        uint `gorm:"not null" json:"type"` // 1 负责人 3 负责人组长
}

type OpenSourceVulnerability struct {
	models.ModelBase
	ComponentID    uint       `gorm:"not null;index:idx_component_id" json:"component_id"`
	CveID          string     `gorm:"not null; type:varchar(60)" json:"cve_id"`
	CveDescription string     `gorm:"not null;" json:"cve_description"`
	CveSeverity    string     `gorm:"not null; type:varchar(60)" json:"cve_severity"`
	Status         uint       `gorm:"not null; index:idx_status" json:"status"` // 1 处理中 2 确认 3 忽略  4 转派
	EmailTime      *time.Time `json:"email_time"`
}

type OpenSourceVulnerabilityPermission struct {
	models.ModelBase
	ComponentID     uint `gorm:"not null" json:"component_id"`
	VulnerabilityID uint `gorm:"not null;index:idx_vulnerability_id" json:"vulnerability_id"`
	UserID          uint `gorm:"not null" json:"user_id"`
	Type            uint `gorm:"not null" json:"type"` // 1 负责人 3 负责人组长 4 委派人
}

type OpenSourceVulnerabilityHistory struct {
	models.ModelBase
	VulnerabilityID uint   `gorm:"not null" json:"vulnerability_id"`
	UserID          uint   `gorm:"not null" json:"user_id"`        // 处理人
	Status          uint   `gorm:"not null" json:"status"`         // 2 确认 3 忽略  4 转派 5 取消委派
	DelegatedUser   uint   `gorm:"not null" json:"delegated_user"` // 委派的人,或取消委派的人
	Comment         string `gorm:"not null" json:"comment"`        //Status为1, comment中需要包含bugid,否则无法修改状态
}

// https://www.cnvd.org.cn/shareData/list  下载xml文件，将匹配cvdid，提取信息
type OpenSourceCNVDVulnerability struct {
	models.ModelBase
	CnvdID           string `gorm:"not null; type:varchar(60)" json:"cnvd_id"`
	CveIDs           string `gorm:"not null" json:"cve_ids"`
	FormalWay        string `gorm:"not null;" json:"formal_way"`
	Description      string `gorm:"not null;" json:"description"`
	PatchName        string `gorm:"not null;" json:"patch_name"`
	PatchDescription string `gorm:"not null;" json:"patch_description"`
	ReferenceLink    string `gorm:"not null;" json:"reference_link"`
	FileIndex        int    `gorm:"not null;" json:"file_index"`
}

type OpenSourceCNVDShareXMLFile struct {
	models.ModelBase
	FileIndex   int        `gorm:"not null;index:idx_file_index" json:"file_index"`
	StartTime   *time.Time `gorm:"not null" json:"start_time"`
	EndTime     *time.Time `json:"end_time"`
	DownloadURL string     `gorm:"not null" json:"download_url"`
	SyncTime    *time.Time `gorm:"not nul" json:"sync_time"`
}

type OpenSourceComponentTempCheck struct {
	models.ModelBase
	UserID           uint   `gorm:"not null" json:"user_id"`
	ComponentName    string `gorm:"not null" json:"component_name"`
	ComponentVersion string `gorm:"not null" json:"component_version"`
	Status           uint   `gorm:"not null; index:idx_status" json:"status"` // 1 预查中 2 成功 3 失败
	ResultFilename   string `json:"result_filename"`
}
