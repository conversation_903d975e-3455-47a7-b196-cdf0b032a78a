package ddagtemplate

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/documentauto"
)

const ModelName = "模板数据表"

type Response struct {
	documentauto.DagTemplate
}

type ListResponse struct {
	Response
}

type Request struct {
	SeriesID     uint   `json:"series_id"`     //系列ID
	Version      string `json:"version"`       //版本
	TemplatePath string `json:"template_path"` //模板路径
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *documentauto.DagTemplate {
	return &documentauto.DagTemplate{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (a *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

// GetTemplateBySeriesID 根据系列ID和版本获取模板记录
func GetTemplateBySeriesID(seriesID, version string) (*Response, error) {
	var template Response
	err := easygorm.GetEasyGormDb().Where("series_id = ? AND version = ?", seriesID, version).First(&template).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

// 模板复合数据
type TemplateCompositeData struct {
	ID           uint   `json:"id"`
	SeriesName   string `json:"series_name"`
	Version      string `json:"version"`
	TemplatePath string `json:"template_path"`
	CreatedAt    string `json:"created_at"`
}

// 获取模板列表
func GetTemplateList(seriesID int, version string, page, pageSize int, orderBy, sort string) (map[string]interface{}, error) {
	var count int64
	var templates []*TemplateCompositeData
	//关联ddagserie表
	db := easygorm.GetEasyGormDb().Table("dag_templates AS dt").
		Select("dt.*, ds.name as series_name").
		Joins("LEFT JOIN dag_series AS ds ON dt.series_id = ds.id")
	if seriesID != 0 {
		db = db.Where("dt.series_id = ?", seriesID)
	}
	if version != "" {
		db = db.Where("version = ?", version)
	}
	err := db.Count(&count).Error
	if err != nil {
		return nil, err
	}
	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).Find(&templates).Error
	if err != nil {
		return nil, err
	}
	list := map[string]interface{}{"items": templates, "total": count, "limit": pageSize}
	return list, nil
}

// 创建模板
func CreateTemplate(seriesID int, version string, templatePath string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(&documentauto.DagTemplate{
		SeriesID:     uint(seriesID),
		Version:      version,
		TemplatePath: templatePath,
	}).Error
	if err != nil {
		return err
	}
	return nil
}

// 根据ID获取模板
func GetTemplateByID(id uint) (*Response, error) {
	var template Response
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id =?", id).First(&template).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}
