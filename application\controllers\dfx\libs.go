package dfx

import (
	"irisAdminApi/application/libs"

	influxdb2 "github.com/influxdata/influxdb-client-go/v2"
	"github.com/influxdata/influxdb-client-go/v2/api"
)

var QueryApi api.QueryAPI

func InitQueryAPI() api.QueryAPI {
	options := influxdb2.DefaultOptions()
	options.SetHTTPRequestTimeout(120)
	if QueryApi == nil {
		client := influxdb2.NewClientWithOptions(libs.Config.DataSync.Influxdb.Url, libs.Config.DataSync.Influxdb.Token, options)

		// Get query client
		queryApi := client.QueryAPI(libs.Config.DataSync.Influxdb.Org)
		QueryApi = queryApi
	}
	return QueryApi
}
