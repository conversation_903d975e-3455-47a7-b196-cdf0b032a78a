package featurerelease

import "irisAdminApi/application/models"

/* 检测文件列表*/
type DetectionList struct {
	models.ModelBase
	FileName           string `gorm:"not null; type:varchar(200)"`                              //文件名
	FileSize           uint   `gorm:"not null"`                                                 //文件大小
	FileMd5            string `gorm:"not null; type:varchar(200)"`                              //文件Md5值
	FileOriginName     string `gorm:"not null; type:varchar(200)" json:"file_origin_name" `     //原始文件名称
	FileUnzipDirectory string `gorm:"not null; type:varchar(200)" json:"file_unzip_directory" ` //文件解压目录

	FileType          uint   `gorm:"not null"`           //文件类型
	ResultJsonFile    string `gorm:"type:varchar(200)"`  //检测结果json文件路径
	ResultLogFile     string `gorm:"type:varchar(200)"`  //检测结果日志文件路径
	ResultLogFileName string `gorm:"type:varchar(200)"`  //检测结果日志文件文件名
	UserID            uint   `json:"user_id"`            //上传用户id
	Status            uint   `gorm:"not null,default:0"` // 检测任务状态  0:运行，1：成功， 2：失败
	DetectionStatus   uint   `gorm:"not null,default:0"` // 检测任务状态  0:运行，1：成功， 2：失败
}
