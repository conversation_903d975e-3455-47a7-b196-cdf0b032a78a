package dvulnerability

import (
	"strings"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/opensource"
	"irisAdminApi/service/dao/opensource/dcomponent"
	"irisAdminApi/service/dao/opensource/dvulnerabilitypermission"
	"irisAdminApi/service/dao/user/duser"
)

const ModelName = "组件漏洞表"

type User struct {
	ID       uint   `json:"id"`
	Name     string `json:"name"`
	Username string `json:"username"`
}

type OpenSourceVulnerability struct {
	opensource.OpenSourceVulnerability
	Component          dcomponent.OpenSourceComponent `gorm:"->" json:"component"`
	ResponsibleUsers   []*User                        `gorm:"many2many:open_source_vulnerability_permissions; foreignKey:ID; references:ID; joinForeignKey: VulnerabilityID; joinReferences: UserID" json:"responsible_users"`
	ResponsibleLeaders []*User                        `gorm:"many2many:open_source_vulnerability_permissions; foreignKey:ID; references:ID; joinForeignKey: VulnerabilityID; joinReferences: UserID" json:"responsible_leaders"`
	DelegatedUsers     []*User                        `gorm:"many2many:open_source_vulnerability_permissions; foreignKey:ID; references:ID; joinForeignKey: VulnerabilityID; joinReferences: UserID" json:"delegated_users"`
	// ResponsibleUsers   []string `gorm:"-" json:"responsible_users"`
	// ResponsibleLeaders []string `gorm:"-" json:"responsible_leaders"`
	// DelegatedUsers  []string `gorm:"-" json:"delegated_users"`
	Enable          bool `gorm:"-" json:"enable"`
	IsExternalServe bool `gorm:"-" json:"is_external_serve"`
}

type ListResponse struct {
	OpenSourceVulnerability
}

type Request struct {
	Id uint `json:"id"`
}

func (this *OpenSourceVulnerability) ModelName() string {
	return ModelName
}

func Model() *opensource.OpenSourceVulnerability {
	return &opensource.OpenSourceVulnerability{}
}

func (this *OpenSourceVulnerability) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update opensource vulnerability get err %s", err.Error())
		return err
	}
	return nil
}

func GetLatestVulnerabilityByComponentId(componentId uint) (*OpenSourceVulnerability, error) {
	var res []*OpenSourceVulnerability
	if err := easygorm.GetEasyGormDb().Model(Model()).
		Where("component_id=?", componentId).
		Order("cve_id desc").Limit(1).
		Find(&res).Error; err != nil {
		logging.ErrorLogger.Errorf("get latest opensource vulnerabilities by componentId get err %s", err.Error())
		return nil, err
	} else {
		if len(res) == 0 {
			return nil, nil
		} else {
			return res[0], nil
		}
	}
}

func ListVulnerabilities(page, pageSize int, componentName, componentVersion, productName, productVersion string,
	isEnable, isExternalServe *bool, status uint, sort, orderBy, createdAt, updatedAt string, userId uint) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	db = db.Joins("inner join (select id as c_component_id, name as c_component_name, version as c_component_version, product as c_product_name, product_version as c_product_version, enable as c_enable, is_external_serve as c_is_external_serve from open_source_components where deleted_at is null) as open_source_components on open_source_components.c_component_id=open_source_vulnerabilities.component_id")
	if userId != 0 {
		db = db.Joins("inner join (select vulnerability_id, user_id from open_source_vulnerability_permissions where deleted_at is null) as open_source_vulnerability_permissions on open_source_vulnerability_permissions.vulnerability_id=open_source_vulnerabilities.id").
			Where("open_source_vulnerability_permissions.user_id=?", userId)
	}

	if len(componentName) != 0 {
		db = db.Where("open_source_components.c_component_name like ?", "%"+componentName+"%")
	}
	if len(componentVersion) != 0 {
		db = db.Where("open_source_components.c_component_version like ?", "%"+componentVersion+"%")
	}
	if len(productName) != 0 {
		db = db.Where("open_source_components.c_product_name like ?", "%"+productName+"%")
	}
	if len(productVersion) != 0 {
		db = db.Where("open_source_components.c_product_version like ?", "%"+productVersion+"%")
	}
	if isEnable != nil {
		if *isEnable {
			db = db.Where("open_source_components.c_enable = 1")
		} else {
			db = db.Where("open_source_components.c_enable = 0")
		}
	}
	if isExternalServe != nil {
		if *isExternalServe {
			db = db.Where("open_source_components.c_is_external_serve = 1")
		} else {
			db = db.Where("open_source_components.c_is_external_serve = 0")
		}
	}
	if status != 0 {
		db = db.Where("open_source_vulnerabilities.status=?", status)
	}
	if len(createdAt) > 0 {
		array := strings.Split(createdAt, ",")
		db = db.Where("open_source_vulnerabilities.created_at between ? and ?", array[0], array[1])
	}
	if len(updatedAt) > 0 {
		array := strings.Split(updatedAt, ",")
		db = db.Where("open_source_vulnerabilities.updated_at between ? and ?", array[0], array[1])
	}
	if len(orderBy) == 0 {
		orderBy = "open_source_vulnerabilities.id"
	}
	err := db.Select("count(distinct(open_source_vulnerabilities.id))").Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("count opensource vulnerabilities get err %s", err.Error())
		return nil, err
	}
	if userId != 0 {
		db = db.Select("distinct *")
	} else {
		db = db.Select("*")
	}
	db = db.Preload("Component").
		Preload("ResponsibleUsers", "id in (select user_id from open_source_vulnerability_permissions where type = 1)").
		Preload("ResponsibleLeaders", "id in (select user_id from open_source_vulnerability_permissions where type = 3)").
		Preload("DelegatedUsers", "id in (select user_id from open_source_vulnerability_permissions where type = 4)")
	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("list opensource vulnerabilities get err %s", err.Error())
		return nil, err
	}

	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func getVulnerabilitiesComponentAndUserInfo(vulnerabilities []*ListResponse) {
	for _, vulnerability := range vulnerabilities {
		getVulnerabilityComponentAndUserInfo(&vulnerability.OpenSourceVulnerability)
	}
}

func getVulnerabilityComponentAndUserInfo(res *OpenSourceVulnerability) {
	var responsibleUsers []string
	var delegatedUsers []string
	var responsibleLeaders []string

	permissions := dvulnerabilitypermission.ListPermissionsByVulnerabilityId(res.ID)
	for _, per := range permissions {
		if per.Type == opensource.ResponsibleUserPermissionType { // 负责人
			responsibleUsers = append(responsibleUsers, per.Username)
		} else if per.Type == opensource.DelegatedUserPermissionType { // 委派人
			delegatedUsers = append(delegatedUsers, per.Username)
		} else if per.Type == opensource.ResponsibleLeaderPermissionType { // 专业组长
			responsibleLeaders = append(responsibleLeaders, per.Username)
		}
	}
	// res.DelegatedUsers = delegatedUsers
	// res.ResponsibleUsers = responsibleUsers
	// res.ResponsibleLeaders = responsibleLeaders
}

func FindById(id uint) (*OpenSourceVulnerability, error) {
	var res []*OpenSourceVulnerability
	if err := easygorm.GetEasyGormDb().Model(Model()).Preload("Component").
		Preload("ResponsibleUsers", "id in (select user_id from open_source_vulnerability_permissions where type = 1)").
		Preload("ResponsibleLeaders", "id in (select user_id from open_source_vulnerability_permissions where type = 3)").
		Preload("DelegatedUsers", "id in (select user_id from open_source_vulnerability_permissions where type = 4)").
		Where("id=?", id).Find(&res).Error; err != nil {
		logging.ErrorLogger.Errorf("find opensource vulnerability by id get err %s", err.Error())
		return nil, err
	} else if len(res) == 0 {
		return nil, nil
	} else {
		// getVulnerabilityComponentAndUserInfo(res[0])
		return res[0], nil
	}
}

func ListComponentUnprocessedVulnerabilities(componentId uint) ([]*ListResponse, error) {
	var res []*ListResponse
	if err := easygorm.GetEasyGormDb().Model(Model()).Select("id").Preload("ResponsibleUsers", "id in (select user_id from open_source_vulnerability_permissions where type = 1)").
		Preload("ResponsibleLeaders", "id in (select user_id from open_source_vulnerability_permissions where type = 3)").
		Preload("DelegatedUsers", "id in (select user_id from open_source_vulnerability_permissions where type = 4)").
		Where("component_id=?", componentId).
		Where("status=?", opensource.VulnerabilityStatusProcessing).Find(&res).Error; err != nil {
		logging.ErrorLogger.Errorf("list unporcessed opensource vulnerabilities get err %s", err.Error())
		return nil, err
	}

	return res, nil
}

type SummaryResponse struct {
	ProductName                string   `json:"product_name"`
	ProductVersion             string   `json:"product_version"`
	ResponsibleUserId          uint     `json:"responsible_user_id"`
	ResponsibleLeaderId        uint     `json:"responsible_leader_id"`
	ResponsibleUsers           []string `gorm:"-" json:"responsible_users"`
	ResponsibleLeaders         []string `gorm:"-" json:"responsible_leaders"`
	TotalVulnerabilities       string   `json:"total_vulnerabilities"`
	ProcessedVulnerabilities   string   `json:"processed_vulnerabilities"`
	UnprocessedVulnerabilities string   `json:"unprocessed_vulnerabilities"`
}

func GetSummaryByProduct() ([]*SummaryResponse, error) {
	var res []*SummaryResponse
	err := easygorm.GetEasyGormDb().Model(Model()).
		Select("product_name", "product_version",
			"SUM(CASE WHEN status != 0 and deleted_at is null THEN 1 ELSE 0 END) AS total_vulnerabilities",
			"SUM(CASE WHEN (status = 2 or status = 3) and deleted_at is null THEN 1 ELSE 0 END) AS processed_vulnerabilities",
			"SUM(CASE WHEN (status = 1 or status = 4) and deleted_at is null THEN 1 ELSE 0 END) AS unprocessed_vulnerabilities").
		Group("product_name").Group("product_version").Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get vulnerabilities summary get err %s", err.Error())
		return nil, err
	}
	return res, nil
}

func GetSummaryByResponsibleUserAndProduct() ([]*SummaryResponse, error) {
	var res []*SummaryResponse
	err := easygorm.GetEasyGormDb().Model(Model()).
		Select("open_source_components.product_name AS product_name",
			"open_source_components.product_version AS product_version",
			"r_permissions.r_user_id AS responsible_user_id",
			"l_permissions.l_user_id AS responsible_leader_id",
			"SUM(CASE WHEN open_source_vulnerabilities.status != 0 and open_source_vulnerabilities.deleted_at is null THEN 1 ELSE 0 END) AS total_vulnerabilities",
			"SUM(CASE WHEN (open_source_vulnerabilities.status = 2 or open_source_vulnerabilities.status = 3)  and open_source_vulnerabilities.deleted_at is null THEN 1 ELSE 0 END) AS processed_vulnerabilities",
			"SUM(CASE WHEN (open_source_vulnerabilities.status = 1 or open_source_vulnerabilities.status = 4)  and open_source_vulnerabilities.deleted_at is null THEN 1 ELSE 0 END) AS unprocessed_vulnerabilities").
		Joins("left join (select component_id as r_component_id,  user_id as r_user_id from open_source_component_permissions where type=1 and deleted_at is null) as r_permissions on r_permissions.r_component_id=open_source_vulnerabilities.component_id").
		Joins("left join (select component_id as l_component_id,  user_id as l_user_id from open_source_component_permissions where type=3 and deleted_at is null) as l_permissions on l_permissions.l_component_id=open_source_vulnerabilities.component_id").
		Joins("inner join (select id, product as product_name, product_version from open_source_components where deleted_at is null) as open_source_components on open_source_components.id=open_source_vulnerabilities.component_id").
		Group("open_source_components.product_name").
		Group("open_source_components.product_version").
		Group("r_permissions.r_user_id").
		Group("l_permissions.l_user_id").
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get vulnerabilities summary get err %s", err.Error())
		return nil, err
	}
	getUserInfo(res)
	return res, nil
}

func getUserInfo(summariesRes []*SummaryResponse) {
	for _, summaryRes := range summariesRes {
		if summaryRes.ResponsibleUserId != 0 {
			user := &duser.User{}
			if err := user.Find(summaryRes.ResponsibleUserId); err == nil {
				summaryRes.ResponsibleUsers = []string{user.Username}
			}
		}
		if summaryRes.ResponsibleLeaderId != 0 {
			user := &duser.User{}
			if err := user.Find(summaryRes.ResponsibleLeaderId); err == nil {
				summaryRes.ResponsibleLeaders = []string{user.Username}
			}
		}
	}
}
