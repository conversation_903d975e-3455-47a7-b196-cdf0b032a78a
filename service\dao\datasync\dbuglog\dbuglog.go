package dbuglog

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/datasync"
	"irisAdminApi/service/dao/datasync/dsyncrecord"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "BUG日志表"

type BugLogSyncResponse struct {
	/*
		{
		    "state": "SUCCESS",
		    "data": [
		        {
		            "rownum": 2,
		            "logId": 99999,                        //日志ID
		            "bugId": 88888,                        //BUGid
		            "username": "测试1",                //操作人姓名
		            "thedate": "2015-06-25 14:05:25",    //操作时间
		            "info": "日志"                        //日志内容
		        }
		    ],
		    "total":1,
		    "message": null
		}
	*/
	State   string            `json:"state"`
	Data    []*BugLogResponse `json:"data"`
	Total   int               `json:"total"`
	Message string            `json:"message"`
}

type BugLogResponse struct {
	/*
	   {
	       "rownum": 2,
	       "logId": 99999,                        //日志ID
	       "bugId": 88888,                        //BUGid
	       "username": "测试1",                //操作人姓名
	       "thedate": "2015-06-25 14:05:25",    //操作时间
	       "info": "日志"                        //日志内容
	   }
	*/

	RowNum    int    `json:"rownum"`
	BugID     int    `json:"bugId"`
	LogID     int    `json:"logId"`
	Username  string `json:"username"`
	Info      string `json:"info"`
	CreatedAt string `json:"thedate"`
}

type BugLog struct {
	datasync.BugLog
}

type ListResponse struct {
	BugLog
}

type Request struct {
	Id uint `json:"id"`
}

func (this *BugLog) ModelName() string {
	return ModelName
}

func Model() *datasync.BugLog {
	return &datasync.BugLog{}
}

func (this *BugLog) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *BugLog) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *BugLog) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *BugLog) CreateV2(object interface{}) error {
	return nil
}

func (this *BugLog) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *BugLog) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *BugLog) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *BugLog) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *BugLog) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *BugLog) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindAllLogsInBugIDs(bugIDs string) ([]*ListResponse, error) {
	logs := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id in ?", strings.Split(bugIDs, ",")).Find(&logs).Error
	return logs, err
}

func UpdateOrCreateBugLogTransaction(buglogs []*BugLogResponse, _url string, data map[string]string, method, state, errorMsg string) error {
	batchCreateObjects := []map[string]interface{}{}
	for _, log := range buglogs {
		if strings.Contains(log.Info, "修改为 RESOLVED") {
			logObject := map[string]interface{}{
				"LogID":     log.LogID,
				"BugID":     log.BugID,
				"Username":  log.Username,
				"CreatedAt": log.CreatedAt,
				"Info":      log.Info,
			}
			batchCreateObjects = append(batchCreateObjects, logObject)
		}
	}

	columns := []string{}

	body, err := json.Marshal(data)
	if err != nil {
		return err
	}

	db := easygorm.GetEasyGormDb()
	err = db.Transaction(func(tx *gorm.DB) error {
		if len(batchCreateObjects) > 0 {
			err := tx.Model(Model()).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "log_id"}},
				DoUpdates: clause.AssignmentColumns(columns),
			}).Create(&batchCreateObjects).Error
			if err != nil {
				return err
			}
		}

		if err := tx.Model(dsyncrecord.Model()).Create(map[string]interface{}{
			"url":             _url,
			"body":            body,
			"method":          method,
			"state":           state,
			"message":         errorMsg,
			"min_modify_date": data["minModifyDate"],
			"max_modify_date": data["maxModifyDate"],
			"created_at":      time.Now(),
		}).Error; err != nil {
			return err
		}

		return nil
	})
	return err
}
