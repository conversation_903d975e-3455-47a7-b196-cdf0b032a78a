  lua_need_request_body on;
  location ~* (merge_requests$) {
    default_type "text/html;charset=UTF-8";
    rewrite_by_lua_block {
      local method = ngx.req.get_method();
      if method == "POST" then
        local config = io.open("/var/opt/gitlab/nginx/conf/project.conf", "r")
        ngx.req.read_body();
        local data = ngx.req.get_post_args()
        for line in config:lines() do
          for k, v in pairs(data) do
            if type(v) ~= "table" then
              if k == "merge_request[target_project_id]" then
                if v == line then
                  ngx.say("<html>请使用<a href='http://10.51.134.126:8082/appmergerequest/'>MR表单</a>提交</html>")
                  return
                end
              end
            end
          end
        end
        config:close()
      end
    }
    proxy_cache off;
    proxy_pass http://gitlab-workhorse;
  }