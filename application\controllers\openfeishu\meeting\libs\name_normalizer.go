package libs

import (
	"regexp"
	"strings"
	"unicode"
)

// NameNormalizer 名称标准化器
type NameNormalizer struct {
	aliasMap     map[string]string // 别名映射表
	stopWords    []string          // 停用词列表
	digitPattern *regexp.Regexp    // 数字后缀模式
}

// NewNameNormalizer 创建名称标准化器
func NewNameNormalizer() *NameNormalizer {
	return &NameNormalizer{
		aliasMap:     make(map[string]string),
		stopWords:    []string{" ", "\t", "\n", "\r"},
		digitPattern: regexp.MustCompile(`^(.+?)(\d+)$`), // 匹配数字后缀
	}
}

// SetAliasMap 设置别名映射表
func (n *NameNormalizer) SetAliasMap(aliasMap map[string]string) {
	n.aliasMap = aliasMap
}

// Normalize 标准化名称
func (n *NameNormalizer) Normalize(name string) string {
	if name == "" {
		return ""
	}

	// 1. 去除前后空格
	name = strings.TrimSpace(name)
	if name == "" {
		return ""
	}

	// 2. 去除内部多余空格
	name = n.removeExtraSpaces(name)

	// 3. 处理英文名称大小写
	if n.isEnglishName(name) {
		name = strings.ToLower(name)
	}

	// 4. 去除特殊字符（保留中文、英文、数字）
	name = n.removeSpecialChars(name)

	// 5. 处理别名映射
	if alias, exists := n.aliasMap[name]; exists {
		return alias
	}

	// 6. 处理数字后缀的变体
	variants := n.generateVariants(name)
	for _, variant := range variants {
		if alias, exists := n.aliasMap[variant]; exists {
			return alias
		}
	}

	return name
}

// NormalizeList 批量标准化名称
func (n *NameNormalizer) NormalizeList(names []string) []string {
	result := make([]string, 0, len(names))
	for _, name := range names {
		normalized := n.Normalize(name)
		if normalized != "" {
			result = append(result, normalized)
		}
	}
	return result
}

// isEnglishName 判断是否为英文名称
func (n *NameNormalizer) isEnglishName(name string) bool {
	for _, r := range name {
		if unicode.Is(unicode.Han, r) {
			return false
		}
	}
	// 包含字母则认为是英文名称
	for _, r := range name {
		if unicode.IsLetter(r) {
			return true
		}
	}
	return false
}

// removeExtraSpaces 去除多余空格
func (n *NameNormalizer) removeExtraSpaces(name string) string {
	// 将多个连续空格替换为单个空格
	spacePattern := regexp.MustCompile(`\s+`)
	return spacePattern.ReplaceAllString(name, " ")
}

// removeSpecialChars 去除特殊字符
func (n *NameNormalizer) removeSpecialChars(name string) string {
	var result strings.Builder
	for _, r := range name {
		// 保留中文、英文字母、数字和空格
		if unicode.Is(unicode.Han, r) || unicode.IsLetter(r) || unicode.IsDigit(r) || r == ' ' {
			result.WriteRune(r)
		}
	}
	return result.String()
}

// generateVariants 生成名称变体
func (n *NameNormalizer) generateVariants(name string) []string {
	variants := make([]string, 0, 4)

	// 1. 原始名称
	variants = append(variants, name)

	// 2. 去除空格的版本
	noSpaceName := strings.ReplaceAll(name, " ", "")
	if noSpaceName != name {
		variants = append(variants, noSpaceName)
	}

	// 3. 处理数字后缀
	if matches := n.digitPattern.FindStringSubmatch(name); len(matches) == 3 {
		baseName := matches[1]
		digit := matches[2]

		// 添加不带数字的版本
		variants = append(variants, baseName)
		variants = append(variants, strings.ReplaceAll(baseName, " ", ""))

		// 添加带空格的数字版本
		spaceDigitName := baseName + " " + digit
		if spaceDigitName != name {
			variants = append(variants, spaceDigitName)
		}
	}

	// 4. 英文名称的大写版本
	if n.isEnglishName(name) {
		upperName := strings.ToUpper(name)
		if upperName != name {
			variants = append(variants, upperName)
		}
	}

	return variants
}

// ParseAttendeeNames 解析参会人员名称字符串
func (n *NameNormalizer) ParseAttendeeNames(attendeesStr string) []string {
	if attendeesStr == "" {
		return nil
	}

	// 使用 | 分割人员名称
	names := strings.Split(attendeesStr, "|")

	// 标准化每个名称
	normalizedNames := make([]string, 0, len(names))
	for _, name := range names {
		normalized := n.Normalize(name)
		if normalized != "" {
			normalizedNames = append(normalizedNames, normalized)
		}
	}

	return normalizedNames
}

// BuildAliasMapFromConfig 从配置构建别名映射表
func (n *NameNormalizer) BuildAliasMapFromConfig(users []UserMapping) {
	aliasMap := make(map[string]string)

	for _, user := range users {
		// 主名称映射到自己
		normalizedMain := n.basicNormalize(user.Name)
		aliasMap[normalizedMain] = user.Name

		// 别名映射到主名称
		for _, alias := range user.Aliases {
			normalizedAlias := n.basicNormalize(alias)
			if normalizedAlias != "" {
				aliasMap[normalizedAlias] = user.Name
			}
		}

		// 生成主名称的变体
		variants := n.generateVariants(normalizedMain)
		for _, variant := range variants {
			if variant != normalizedMain {
				aliasMap[variant] = user.Name
			}
		}
	}

	n.SetAliasMap(aliasMap)
}

// basicNormalize 基础标准化（不使用别名映射）
func (n *NameNormalizer) basicNormalize(name string) string {
	if name == "" {
		return ""
	}

	// 去除前后空格
	name = strings.TrimSpace(name)
	if name == "" {
		return ""
	}

	// 去除内部多余空格
	name = n.removeExtraSpaces(name)

	// 处理英文名称大小写
	if n.isEnglishName(name) {
		name = strings.ToLower(name)
	}

	// 去除特殊字符
	name = n.removeSpecialChars(name)

	return name
}

// GetNormalizedVariants 获取名称的所有标准化变体
func (n *NameNormalizer) GetNormalizedVariants(name string) []string {
	normalized := n.basicNormalize(name)
	variants := n.generateVariants(normalized)

	// 去重
	seen := make(map[string]bool)
	result := make([]string, 0, len(variants))

	for _, variant := range variants {
		if !seen[variant] && variant != "" {
			seen[variant] = true
			result = append(result, variant)
		}
	}

	return result
}
