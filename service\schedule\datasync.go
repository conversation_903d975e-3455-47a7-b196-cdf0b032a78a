package schedule

import (
	"sync"

	"irisAdminApi/application/controllers/buildfarm"
	"irisAdminApi/application/controllers/datasync"
	"irisAdminApi/application/controllers/mergerequest"
	"irisAdminApi/application/controllers/openfeishu"
	"irisAdminApi/application/controllers/todo"
	"irisAdminApi/application/logging"
)

var lock sync.Mutex

func DataSync() {
	if lock.TryLock() {
		defer lock.Unlock()

		// logging.DebugLogger.Debugf("开始同步代码评审详情至飞书")
		// if err := mergerequest.SyncCpsToFeishu(); err != nil {
		// 	logging.ErrorLogger.Errorf("同步代码评审详情至飞书失败", err)
		// } else {
		// 	logging.DebugLogger.Debugf("完成同步代码评审详情至飞书")
		// }

		logging.DebugLogger.Debugf("开始同步DFX内存归属信息")
		if err := datasync.SyncCreateDfxMemoryInfo(); err != nil {
			logging.ErrorLogger.Errorf("同步DFX内存归属信息失败", err)
		} else {
			logging.DebugLogger.Debugf("完成同步DFX内存归属信息")
		}

		// logging.DebugLogger.Debugf("开始同步项目数据")
		// datasync.ProjectSyncWorker()
		// logging.DebugLogger.Debugf("完成同步项目数据")

		logging.DebugLogger.Debugf("开始同步项目成员数据")
		datasync.PmsProjectMemberSyncWorker()

		go openfeishu.SyncProjectMemberDataV5()
		logging.DebugLogger.Debugf("完成同步项目成员数据")

		logging.DebugLogger.Debugf("开始同步代码路径数据")
		datasync.PathSyncWorker()
		logging.DebugLogger.Debugf("完成同步代码路径数据")

		logging.DebugLogger.Debugf("开始同步分支信息数据")
		datasync.BranchInfoSyncWorker()
		logging.DebugLogger.Debugf("完成同步同步分支数据")

		logging.DebugLogger.Debugf("开始同步BUG数据")
		datasync.BugSyncWorker(10)
		logging.DebugLogger.Debugf("完成同步BUG数据")

		logging.DebugLogger.Debugf("开始同步硬件项目数据")
		datasync.PmsHardwareProjectSyncWorker()
		logging.DebugLogger.Debugf("完成同步硬件项目数据")

		logging.DebugLogger.Debugf("开始同步软件项目数据")
		if err := datasync.PmsSoftwareProjectSyncWorker(); err != nil {
			logging.ErrorLogger.Errorf("同步软件项目数据失败", err)
		} else {
			logging.DebugLogger.Debugf("完成同步软件项目数据")
		}

		logging.DebugLogger.Debugf("开始同步硬件产品数据")
		datasync.PmsProductSyncWorker()
		logging.DebugLogger.Debugf("完成同步硬件产品数据")

		// logging.DebugLogger.Debugf("开始计算BUG超期时间")
		// if err := dmergerequestbugsummary.UpdateOverTime(); err != nil {
		// 	logging.ErrorLogger.Errorf("计算BUG超期时间失败", err)
		// } else {
		// 	logging.DebugLogger.Debugf("完成计算BUG超期时间")
		// }

		logging.DebugLogger.Debugf("开始同步组件代码清单")
		if err := datasync.SyncCommonComponent(); err != nil {
			logging.DebugLogger.Errorf("同步组件代码清单失败", err)
		} else {
			logging.DebugLogger.Debugf("完成同步组件代码清单")
		}

		datasync.PmsProjectTestStageSyncWorker()
		datasync.PmsCaseInfoSyncWorker()
		datasync.PmsRequestSyncWorker()
		go openfeishu.SyncAllRequestDataV5()
		go openfeishu.SyncAllWorkPacketDataV5()
		datasync.PmsProjectDocumentRequestRelationSyncWorker()

		logging.DebugLogger.Debugf("开始同步工作包")
		datasync.PmsWorkpacketInfoSyncWorker()
		datasync.PmsRequestCaseSyncWorker()
		if err := mergerequest.SyncWorkPackage(); err != nil {
			logging.ErrorLogger.Errorf("同步工作包失败", err)
		} else {
			logging.DebugLogger.Debugf("完成同步工作包")
		}

		logging.DebugLogger.Debugf("开始同步项目文档信息")
		if err := datasync.PmsProjectDocumentSyncWorker(); err != nil {
			logging.ErrorLogger.Errorf("同步项目文档信息失败", err)
		} else {
			logging.DebugLogger.Debugf("完成同步项目文档信息")
		}

		if err := datasync.RefreshItc(); err != nil {
			logging.ErrorLogger.Errorf("同步ITC数据失败", err)
		} else {
			logging.DebugLogger.Debugf("完成同步ITC数据")
		}

		// if err := buildfarm.UpdateWarningSummaryToFeishu(); err != nil {
		// 	logging.ErrorLogger.Errorf("同步waring数据失败", err)
		// } else {
		// 	logging.DebugLogger.Debugf("完成同步同步waring数据")
		// }

		// if err := datasync.GcovClean(); err != nil {
		// 	logging.ErrorLogger.Errorf("同步gcov数据失败", err)
		// } else {
		// 	logging.DebugLogger.Debugf("完成同步gcov数据")
		// }
		logging.DebugLogger.Debugf("开始同步资源数据")
		datasync.ResourceSyncCsbuWorker()
		// datasync.ResourceSyncScaleWorker()
		// datasync.ResourceDeleteSyncWorker()
		datasync.ResourceCleanWorker()
		logging.DebugLogger.Debugf("完成同步资源数据")
		datasync.SyncGcovDataToMysqlWorker()
	}
}

func DataSyncOnce() {
	lock.Lock()
	defer lock.Unlock()
	logging.DebugLogger.Debugf("开始更新工作包")
	if err := mergerequest.SyncWorkPackage(); err != nil {
		logging.ErrorLogger.Errorf("更新工作包失败", err)
	} else {
		logging.DebugLogger.Debugf("完成更新工作包")
	}
}

func RunSync() {
	datasync.SyncPmsReviewDataToFeishuWorker()
	_, err := Cron.AddFunc("30 8-23/1 * * *", func() { SyncPmsReviewDataToFeishuWorker() })
	if err != nil {
		logging.ErrorLogger.Error("add cps sync to pms cron err ", err)
	}

	_, err = Cron.AddFunc("30 8-23/1 * * *", func() { mergerequest.SyncCpsToPmsWorker() })
	if err != nil {
		logging.ErrorLogger.Error("add cps sync to pms cron err ", err)
	}

	_, err = Cron.AddFunc("55 8-23/1 * * *", func() { DataSync() })
	if err != nil {
		logging.ErrorLogger.Error("add bug sync cron err ", err)
	}

	_, err = Cron.AddFunc("15 09 * * *", func() { todo.TodoScheduler() })
	if err != nil {
		logging.ErrorLogger.Error("add todo cron err ", err)
	}

	_, err = Cron.AddFunc("00 06 * * *", func() { buildfarm.UpdateWarningSummaryToFeishuWorker() })
	if err != nil {
		logging.ErrorLogger.Error("add warning sync cron err ", err)
	}

	_, err = Cron.AddFunc("00 00 * * *", func() { datasync.SyncProductDataFromBIApi() })
	if err != nil {
		logging.ErrorLogger.Error("add warning sync cron err ", err)
	}

	_, err = Cron.AddFunc("00 6,12 * * *", func() { datasync.SyncFeisuBaseData() })
	if err != nil {
		logging.ErrorLogger.Error("add feishu base sync cron err ", err)
	}

	_, err = Cron.AddFunc("30 08,12,16 * * *", func() { ResourcePlanAndSplitWorker() })
	if err != nil {
		logging.ErrorLogger.Error("add resource split sync cron err ", err)
	}

	_, err = Cron.AddFunc("00 8-23/2 * * *", func() { ProjectDashboard() })
	if err != nil {
		logging.ErrorLogger.Error("add gcov sync cron err ", err)
	}
	// 初始化bug系统cookie获取
	datasync.WarmupBugSystem()
}

func ProjectDashboard() {
	datasync.SyncGcovDataToFeiShuWorker("NTOS1.0R11", "WIfHbqbPQaePURsubtHcizfnnYc", "tblhR4ZO2KezWMIp")
	datasync.SyncCoverityResultDataToFeiShu("NTOS1.0R11", "WIfHbqbPQaePURsubtHcizfnnYc", "tblExjGBbFLXKmWf")
	datasync.SyncWorkPackageDataToFeishu("NTOS1.0R11", "WIfHbqbPQaePURsubtHcizfnnYc", "tbleamZGUJxKLUOt")
	datasync.SyncResourceDataByProject("NTOS1.0R11", "WIfHbqbPQaePURsubtHcizfnnYc", "tbllOozedhbMwfRb")
	datasync.SyncBugDataByProject("NTOS1.0R11", "WIfHbqbPQaePURsubtHcizfnnYc", "tblm1RBsyPRGPbjm")
	datasync.SyncCaseDataByProject("NTOS1.0R11", "WIfHbqbPQaePURsubtHcizfnnYc", "tblBNP5pHL7OZLPl")

	datasync.SyncGcovDataToFeiShuWorker("NTOS1.0R10P17", "XscebigbVaKNKmsu5LVcuTFLnNe", "tblPjJsTVB1K1yEf")
	datasync.SyncCoverityResultDataToFeiShu("NTOS1.0R10P17", "XscebigbVaKNKmsu5LVcuTFLnNe", "tblQ0bzuaE0drEt9")
	datasync.SyncWorkPackageDataToFeishu("NTOS1.0R10P17", "XscebigbVaKNKmsu5LVcuTFLnNe", "tbl7Vlc4CsNOTAYb")
	datasync.SyncResourceDataByProject("NTOS1.0R10P17", "XscebigbVaKNKmsu5LVcuTFLnNe", "tblmoNZS5GHxRe0S")
	datasync.SyncBugDataByProject("NTOS1.0R10P17", "XscebigbVaKNKmsu5LVcuTFLnNe", "tblP1C6hXxi3Cbfi")
	datasync.SyncCaseDataByProject("NTOS1.0R10P17", "XscebigbVaKNKmsu5LVcuTFLnNe", "tbl63zitAl1dm1rK")
}

func ResourcePlanAndSplitWorker() {
	var wg sync.WaitGroup
	wg.Add(2)
	go func() {
		datasync.ResourceSplitSyncWorker()
		wg.Done()
	}()
	go func() {
		datasync.ResourceApplySyncWorker()
		wg.Done()
	}()
	wg.Wait()

	datasync.SyncAllResourceSplitToFeishu()
	datasync.SyncAllResourceSplitToFeishu2()
}
