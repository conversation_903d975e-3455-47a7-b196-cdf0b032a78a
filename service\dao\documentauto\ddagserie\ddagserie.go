package ddagserie

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/documentauto"
)

const ModelName = "系列数据表"

type Response struct {
	documentauto.DagSerie
}

type ListResponse struct {
	Response
}

type Request struct {
	Name string `json:"name"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *documentauto.DagSerie {
	return &documentauto.DagSerie{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (a *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

type Branch struct {
	BranchName string `json:"branch_name"`
}

func GetAllBranchData() ([]*Branch, error) {
	items := []*Branch{}
	sql := `
SELECT  DISTINCT  new_branch as branch_name  FROM  release_branches rb WHERE status =1 order by branch_name;
	`
	err := easygorm.GetEasyGormDb().Table("release_branches").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

type ReleaseInfo struct {
	ReleaseName string `json:"release_name"`
}

func GetCompileRelease(branch, start, end string) ([]*ReleaseInfo, error) {
	items := []*ReleaseInfo{}
	start = start + " 00:00:00.000"
	end = end + " 23:59:59.999"
	sql := fmt.Sprintf(`
		SELECT DISTINCT 
    CONCAT('Release(', SUBSTRING_INDEX(SUBSTRING_INDEX(software_version, 'Release(', -1), ')', 1), ')') AS release_name
FROM 
    cron_make_jobs cmj
WHERE 
    build_type = "release" 
    AND status = 1 
    AND branch = "%s" 
    and project ="basesys/build-product"
    AND created_at >= "%s"
    and created_at <="%s"
ORDER BY 
    release_name DESC;
		`, branch, start, end)
	err := easygorm.GetEasyGormDb().Table("cron_make_jobs").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

type BaselineInfo struct {
	BaselineName string `json:"baseline_name"`
}

func GetBaseline(series string) ([]*BaselineInfo, error) {
	items := []*BaselineInfo{}
	sql := fmt.Sprintf(`
		SELECT  DISTINCT  version as baseline_name from  dag_templates WHERE series_id in ("%s");
		`, series)
	err := easygorm.GetEasyGormDb().Table("dag_templates").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}
