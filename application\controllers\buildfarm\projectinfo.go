package buildfarm

import (
	"fmt"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/buildfarm/dbuildfarmprojectinfo"
	"irisAdminApi/service/dao/buildfarm/dproject"

	"github.com/kataras/iris/v12"
)

type Cmd struct {
	Command string
	Output  string
	Status  error
}

type GitlabProject struct {
	ID   uint
	Name string
	Repo string
}

type Repo struct {
	ID       uint
	Name     string
	Repo     string
	Branches []*Branch
	Cmds     []*Cmd
	TmpDir   string
}

type Branch struct {
	Name       string
	Products   []*Product
	Cmds       []*Cmd
	Defconfigs []*Defconfig
	Targets    []*Target
}

type Product struct {
	Name       string
	Defconfigs []*Defconfig
	Cmds       []*Cmd
	Feed       string
}

type Defconfig struct {
	Name string
}

type Target struct {
	Name string
}

func (r *Repo) Clone() *Repo {
	r.TmpDir = filepath.Join("/tmp", r.Name)
	cmd := Cmd{
		Command: fmt.Sprintf("rm %s -rf && git clone %s %s", r.TmpDir, r.Repo, r.TmpDir),
		Output:  "",
		Status:  nil,
	}
	cmd.Output, cmd.Status = libs.ExecCommand(cmd.Command)
	r.Cmds = append(r.Cmds, &cmd)
	return r
}

func (r *Repo) GetProductInfo() *Repo {
	var result *Cmd
	cmd := Cmd{
		Command: fmt.Sprintf(`cd %s && git branch -a|grep remotes|grep -v '\->'|awk -F '/' '{print $NF}'|grep -v cherry-pick|grep -v revert|grep -v test`, r.TmpDir),
		Output:  "",
		Status:  nil,
	}
	cmd.Output, cmd.Status = libs.ExecCommand(cmd.Command)
	if cmd.Status == nil {
		for _, branch := range strings.Split(cmd.Output, "\n") {
			if strings.TrimSpace(branch) != "" {
				b := &Branch{
					Name: branch,
					Cmds: []*Cmd{
						{
							Command: fmt.Sprintf("cd %s && git checkout %s", r.TmpDir, branch),
							Output:  "",
							Status:  nil,
						},
						{
							Command: fmt.Sprintf("cd %s && ls -ld prj_*|grep prj_|awk -F 'prj_' '{print $NF}'", r.TmpDir),
							Output:  "",
							Status:  nil,
						},
					},
					Products: []*Product{},
				}

				for _, cmd := range b.Cmds {
					cmd.Output, cmd.Status = libs.ExecCommand(cmd.Command)
					if cmd.Status != nil {
						return r
					}
					result = cmd
				}

				for _, product := range strings.Split(result.Output, "\n") {
					if strings.TrimSpace(product) != "" {
						p := &Product{
							Name: product,
							Cmds: []*Cmd{
								{
									Command: fmt.Sprintf("cd %s && ls prj_%s/configs", r.TmpDir, product),
									Output:  "",
									Status:  nil,
								},
								{
									Command: fmt.Sprintf("cd %s && cat prj_%s/project/feeds.conf.default", r.TmpDir, product),
									Output:  "",
									Status:  nil,
								},
							},
							Defconfigs: []*Defconfig{},
						}
						cmd := p.Cmds[0]
						cmd.Output, cmd.Status = libs.ExecCommand(cmd.Command)
						if cmd.Status != nil {
							return r
						}
						result = cmd

						for _, defconfig := range strings.Split(result.Output, "\n") {
							if strings.TrimSpace(defconfig) != "" {
								p.Defconfigs = append(p.Defconfigs, &Defconfig{
									Name: defconfig,
								})
							}
						}

						cmd = p.Cmds[1]
						cmd.Output, cmd.Status = libs.ExecCommand(cmd.Command)
						if cmd.Status != nil {
							return r
						}
						result = cmd
						p.Feed = result.Output

						b.Products = append(b.Products, p)
					}
				}
				r.Branches = append(r.Branches, b)
			}
		}
	}

	r.Cmds = append(r.Cmds, &cmd)
	return r
}

func (r *Repo) GetUnitpackgeInfo() *Repo {
	var result *Cmd
	cmd := Cmd{
		Command: fmt.Sprintf(`cd %s && git branch -a|grep remotes|grep -v '\->'|awk -F '/' '{print $NF}'|grep -v cherry-pick|grep -v revert|grep -v test`, r.TmpDir),
		Output:  "",
		Status:  nil,
	}
	cmd.Output, cmd.Status = libs.ExecCommand(cmd.Command)
	if cmd.Status == nil {
		for _, branch := range strings.Split(cmd.Output, "\n") {
			if strings.TrimSpace(branch) != "" {
				b := &Branch{
					Name: branch,
					Cmds: []*Cmd{
						{
							Command: fmt.Sprintf("cd %s && git checkout %s", r.TmpDir, branch),
							Output:  "",
							Status:  nil,
						},
						{
							Command: fmt.Sprintf("cd %s && if [ -d 'configs' ];then ls configs;fi", r.TmpDir),
							Output:  "",
							Status:  nil,
						},
					},
					Defconfigs: []*Defconfig{},
					Targets:    []*Target{},
				}

				for _, cmd := range b.Cmds {
					cmd.Output, cmd.Status = libs.ExecCommand(cmd.Command)
					if cmd.Status != nil {
						return r
					}
					result = cmd
				}

				for _, defconfig := range strings.Split(result.Output, "\n") {
					if strings.TrimSpace(defconfig) != "" {
						b.Defconfigs = append(b.Defconfigs, &Defconfig{
							Name: defconfig,
						})
					}
				}

				cmd := &Cmd{
					Command: fmt.Sprintf("cd %s && find ./ -name Config.in|xargs -I {} cat {} | grep package|grep source|awk -F '/' '{print $(NF-1)}'|sort -h|uniq", r.TmpDir),
					Output:  "",
					Status:  nil,
				}
				cmd.Output, cmd.Status = libs.ExecCommand(cmd.Command)
				if cmd.Status != nil {
					return r
				}
				result = cmd
				for _, target := range strings.Split(result.Output, "\n") {
					if strings.TrimSpace(target) != "" {
						b.Targets = append(b.Targets, &Target{
							Name: target,
						})
					}
				}

				b.Cmds = append(b.Cmds, cmd)
				r.Branches = append(r.Branches, b)
			}
		}
	}

	r.Cmds = append(r.Cmds, &cmd)
	return r
}

func UpdateProjectsInfo() error {
	// 获取所有项目
	now := time.Now()
	projects, err := dproject.FindAll()
	if err != nil {
		logging.ErrorLogger.Error(err)
		return err
	}

	for _, project := range projects {
		err := UpdateProjectInfo(project)
		if err != nil {
			logging.ErrorLogger.Error(err)
		}
		err = dbuildfarmprojectinfo.CleanOverTimeProjectInfo(project.Id, now)
		if err != nil {
			logging.ErrorLogger.Error(err)
		}
	}

	return nil
}

var RunningProjects sync.Map

func UpdateProjectInfo(project *dproject.Response) error {
	if _, ok := RunningProjects.Load(project.Id); ok {
		return nil
	} else {
		RunningProjects.Store(project.Id, "")
		defer RunningProjects.Delete(project.Id)
	}

	now := time.Now()
	if project.Enable == 0 {
		return nil
	}
	var items []map[string]interface{}
	repo := Repo{
		ID:   project.Id,
		Name: project.Name,
		Repo: project.Repo,
	}

	if project.TaskType == 1 {
		repo.Clone().GetProductInfo()
		for _, branch := range repo.Branches {
			for _, product := range branch.Products {
				for _, defconfig := range product.Defconfigs {
					items = append(items, map[string]interface{}{
						"project_id": repo.ID,
						"branch":     branch.Name,
						"product":    product.Name,
						"defconfig":  defconfig.Name,
						"target":     "",
						"feed":       product.Feed,
						"updated_at": now,
					})
				}
			}
		}
	}

	// if project.TaskType == 2 {
	// 	repo.Clone().GetUnitpackgeInfo()
	// 	for _, branch := range repo.Branches {
	// 		for _, defconfig := range branch.Defconfigs {
	// 			items = append(items, map[string]interface{}{
	// 				"project_id": repo.ID,
	// 				"branch":     branch.Name,
	// 				"product":    "",
	// 				"defconfig":  defconfig.Name,
	// 				"target":     "",
	// 				"updated_at": now,
	// 			})
	// 		}
	// 		for _, target := range branch.Targets {
	// 			items = append(items, map[string]interface{}{
	// 				"project_id": repo.ID,
	// 				"branch":     branch.Name,
	// 				"product":    "",
	// 				"defconfig":  "",
	// 				"target":     target.Name,
	// 				"updated_at": now,
	// 			})
	// 		}
	// 	}
	// }
	err := dbuildfarmprojectinfo.UpdateOrCreateBuildFarmProjectInfoTransaction(items)
	if err != nil {
		logging.ErrorLogger.Error(err)
		return err
	}
	return nil
}

func ManualUpdateProjectsInfo(ctx iris.Context) {
	go UpdateProjectsInfo()
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func ManualUpdateProjectInfo(ctx iris.Context) {
	projectID, _ := dao.GetId(ctx)
	project := dproject.Response{}
	err := project.Find(projectID)
	if err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	go UpdateProjectInfo(&project)
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetBranchesV3(ctx iris.Context) {
	project := ctx.FormValue("project")
	if project == "" {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	// list, err := dbuildfarmprojectinfo.GetBranches(projectID)

	list, err := GetBranchesFromGitlab(project)
	if err != nil {
		logging.ErrorLogger.Errorf("get branch err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetProductsV3(ctx iris.Context) {
	project := ctx.FormValue("project")
	branch := ctx.FormValue("branch")
	if project == "" || branch == "" {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	// list, err := dbuildfarmprojectinfo.GetProducts(projectID, branch)

	list, err := GetProductsFromGitlab(project, branch)
	if err != nil {
		logging.ErrorLogger.Errorf("get product err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetDefconfigsV3(ctx iris.Context) {
	project := ctx.FormValue("project")
	branch := ctx.FormValue("branch")
	product := ctx.FormValue("product")

	if project == "" || branch == "" {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}

	if strings.HasSuffix(project, "build-product") {
		if product == "" {
			ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
			return
		}
	}

	// list, err := dbuildfarmprojectinfo.GetDefconfigs(projectID, branch, product)
	list, err := GetDefconfigsFromGitlab(project, branch, product)
	if err != nil {
		logging.ErrorLogger.Errorf("get defconfig err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetTargetsV3(ctx iris.Context) {
	project := ctx.FormValue("project")
	branch := ctx.FormValue("branch")

	if project == "" || branch == "" {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	// list, err := dbuildfarmprojectinfo.GetTargets(projectID, branch, product, defconfig)
	list, err := GetTargetsFromFGitlab(project, branch)
	if err != nil {
		logging.ErrorLogger.Errorf("get targets err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetFeedsConfigDefaultV3(ctx iris.Context) {
	project := ctx.FormValue("project")
	branch := ctx.FormValue("branch")
	product := ctx.FormValue("product")
	if project == "" || branch == "" || product == "" {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	// ret, err := dbuildfarmprojectinfo.GetFeed(projectID, branch, product, defconfig)
	ret, err := GetFeedFromFileRaw(project, branch, product)
	if err != nil {
		logging.ErrorLogger.Errorf("get feed err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	lines := strings.Split(ret, "\n")
	result := []map[string]interface{}{}
	index := 0
	for _, line := range lines {
		arr := strings.Fields(line)
		if len(arr) > 2 {
			index++
			result = append(result, map[string]interface{}{
				"id":          index,
				"type1":       arr[0],
				"type2":       arr[1],
				"unitpackage": arr[2],
				"repo":        strings.Split(arr[3], ";")[0],
				"branch":      strings.Split(arr[3], ";")[1],
			})
		}
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}
