package dvulnerabilitycnvd

import (
	"strings"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/opensource"
)

const ModelName = "CNVD漏洞表"
const batchCreateCount = 20

type Response struct {
	opensource.OpenSourceCNVDVulnerability
}

type ListResponse struct {
	Response
}

type Request struct {
	Id uint `json:"id"`
}

func (this *Response) ModelName() string {
	return ModelName
}

func Model() *opensource.OpenSourceCNVDVulnerability {
	return &opensource.OpenSourceCNVDVulnerability{}
}

func ListCNVDVulnerabilities(page, pageSize int, cveId string, sort, orderBy, createdAt, updatedAt string) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())

	if cveId != "" {
		db = db.Where("cve_ids like ?", "%"+cveId+"%")
	}
	if len(createdAt) > 0 {
		array := strings.Split(createdAt, ",")
		db = db.Where("created_at between ? and ?", array[0], array[1])
	}
	if len(updatedAt) > 0 {
		array := strings.Split(updatedAt, ",")
		db = db.Where("updated_at between ? and ?", array[0], array[1])
	}
	if len(orderBy) == 0 {
		orderBy = "id"
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("count cnvd vulnerabilities get err %s", err.Error())
		return nil, err
	}
	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("list cnvd vulnerabilities get err %s", err.Error())
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func GetCNVDVulnerability(id uint) (*Response, error) {
	var res []*Response

	err := easygorm.GetEasyGormDb().Model(Model()).Where("id=?", id).Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get cnvd vulnerability by id get err %s", err.Error())
		return nil, err
	}
	if len(res) == 0 {
		return nil, nil
	}
	return res[0], nil
}

func CreateCNVDVulnerabilities(cnvdListRes []*Response) error {
	cnvds := make([]*opensource.OpenSourceCNVDVulnerability, len(cnvdListRes))
	for i, cnvdRes := range cnvdListRes {
		cnvds[i] = &cnvdRes.OpenSourceCNVDVulnerability
	}
	var firstIndex = 0
	var total = len(cnvds)
	for firstIndex < total {
		lastIndex := firstIndex + batchCreateCount
		if lastIndex > total {
			lastIndex = total
		}
		newCVNDs := cnvds[firstIndex:lastIndex]
		err := easygorm.GetEasyGormDb().Model(Model()).Create(&newCVNDs).Error
		if err != nil {
			logging.ErrorLogger.Errorf("create cnvd vulnerabilities get err %s", err.Error())
			return err
		}
		firstIndex = lastIndex
	}
	return nil
}

func GetLatestCNVDVulnerability() (*Response, error) {
	var res []*Response
	err := easygorm.GetEasyGormDb().Model(Model()).
		Select("id, cnvd_id, file_index").
		Order("id desc").Offset(0).Limit(1).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get latest cnvd vulnerability get err %s", err.Error())
		return nil, err
	}
	if len(res) == 0 {
		return nil, err
	}
	return res[0], nil
}
