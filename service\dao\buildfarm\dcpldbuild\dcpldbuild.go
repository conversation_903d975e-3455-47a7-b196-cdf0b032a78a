package dcpldbuild

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/buildfarm"
	"irisAdminApi/service/dao/buildfarm/dcronmakejob"
	"path/filepath"
)

const ModelName = "CPLD作业表"

type User struct {
	ID       uint   `json:"id"`
	Name     string `json:"name"`
	Username string `json:"username"`
}

type CpldBuildJob struct {
	buildfarm.CpldBuildJob
	User        *User                     `gorm:"->" json:"user"`
	CronMakeJob *dcronmakejob.CronMakeJob `gorm:"->;foreignKey:cron_make_job_id;references:id" json:"cron_make_job"`
}

type ListResponse struct {
	CpldBuildJob
}

type Request struct {
	buildfarm.CpldBuildJob
}

func (this *CpldBuildJob) ModelName() string {
	return ModelName
}

func Model() *buildfarm.CpldBuildJob {
	return &buildfarm.CpldBuildJob{}
}

func (this *CpldBuildJob) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *CpldBuildJob) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *CpldBuildJob) Create(object map[string]interface{}) error {
	object["app"] = libs.Config.Sig.App
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *CpldBuildJob) CreateV2(object interface{}) error {
	return nil
}

func (this *CpldBuildJob) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *CpldBuildJob) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *CpldBuildJob) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *CpldBuildJob) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *CpldBuildJob) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Where("id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *CpldBuildJob) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Preload("CronMakeJob").Preload("CronMakeJob.Cpu").Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func All(userID, job_id, name, md5, start, end, status, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Preload("CronMakeJob")
	if len(userID) > 0 && userID != "0" {
		db = db.Where("user_id = ?", userID)
	}

	if len(job_id) > 0 {
		db = db.Where("job_id = ? ", job_id)
	}

	if len(name) > 0 {
		db = db.Where("input_file_name like ? or output_file_name like ?", fmt.Sprintf("%%%s%%", name), fmt.Sprintf("%%%s%%", name))
	}

	if len(md5) > 0 {
		db = db.Where("input_file_md5 like ? or output_file_md5 like ?", fmt.Sprintf("%%%s%%", md5), fmt.Sprintf("%%%s%%", md5))
	}

	if len(start) > 0 {
		db = db.Where("created_at >= ?", start+" 00:00:00.000")
	}

	if len(end) > 0 {
		db = db.Where("created_at <= ?", end+" 23:59:59.999")
	}

	if len(status) > 0 {
		db = db.Where("status = ?", status)
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func FindAllQueueJobs() ([]*ListResponse, error) {
	items := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("status = 3").Find(&items).Error
	return items, err
}

func (this *CpldBuildJob) OutputDir() string {
	return filepath.Join(libs.Config.Buildfarm.Cpld.Output, this.CreatedAt.Format("20060102"), this.JobID)
}

func (this *CpldBuildJob) UploadDir() string {
	return filepath.Join(libs.Config.Buildfarm.Cpld.Upload, this.CreatedAt.Format("20060102"), this.JobID)
}
