package main

import (
	"fmt"
	"irisAdminApi/application/controllers/buildfarm"
	"irisAdminApi/application/logging"

	"os"

	"github.com/tidwall/gjson"
)

func main() {
	jsonStr, err := buildfarm.ReadUpdateJsonInZipFile(os.Args[1])
	if err != nil {
		logging.ErrorLogger.Errorf("read json fail", os.Args[1], err)
	}
	systemSoftwareVersion := gjson.Get(jsonStr, "extend.version").String()
	systemSoftwareNumber := gjson.Get(jsonStr, "extend.system_software_number").String()
	if systemSoftwareVersion != "" && systemSoftwareNumber != "" {
		fmt.Println(systemSoftwareVersion, systemSoftwareNumber)
	}
}
