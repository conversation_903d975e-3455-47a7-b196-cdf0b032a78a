package appmergerequest

import (
	"bytes"

	"golang.org/x/crypto/ssh"
)

func RunCommandOutBuffer(client *ssh.Client, command string, out *bytes.Buffer) error {
	session, err := client.NewSession()
	if err != nil {
		return err
	}
	defer session.Close()

	// 输出日志文件
	session.Stdout = out
	err = session.Start(command)
	if err != nil {
		return err
	}
	err = session.Wait()
	if err != nil {
		return err
	}
	return nil
}
