package dpmsbumember

import (
	"encoding/json"
	"fmt"
	"reflect"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/datasync"
	"irisAdminApi/service/dao/datasync/dsyncrecord"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "PMS用例信息表"

type PmsBuMemberResponse struct {
	State   string                     `json:"state"`
	Data    []*PmsBuMemberDataResponse `json:"data"`
	Total   int                        `json:"total"`
	Message string                     `json:"message"`
}

type PmsBuMemberDataResponse struct {
	RowNum              int    `json:"rownum"`
	ID                  int    `json:"id" `
	UserCn              string `json:"user_cn" `
	UserEn              string `json:"user_en" `
	Email               string `json:"email"`
	EmployeeID          string `json:"employee_id"`
	UserStatus          int    `json:"user_status"`
	OneDepartmentName   string `json:"oneDepartmentName"`
	TwoDepartmentName   string `json:"twoDepartmentName"`
	ThreeDepartmentName string `json:"threeDepartmentName"`
	FourDepartmentName  string `json:"fourDepartmentName"`
	FiveDepartmentName  string `json:"fiveDepartmentName"`
}

type PmsRequest struct {
}

type PmsBuMember struct {
	datasync.PmsBuMember
}

type ListResponse struct {
	PmsBuMember
}

type Request struct {
	Id uint `json:"id"`
}

func (this *PmsBuMember) ModelName() string {
	return ModelName
}

func Model() *datasync.PmsBuMember {
	return &datasync.PmsBuMember{}
}

func (this *PmsBuMember) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *PmsBuMember) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *PmsBuMember) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *PmsBuMember) CreateV2(object interface{}) error {
	return nil
}

func (this *PmsBuMember) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *PmsBuMember) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *PmsBuMember) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *PmsBuMember) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *PmsBuMember) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *PmsBuMember) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindAllEnabledByProjectName(projectName string) ([]*ListResponse, error) {
	items := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("PmsRequest").Where("project_name = ? and disabled = 0", projectName).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return items, err
	}
	return items, nil
}

func DeleteAll() error {
	err := easygorm.GetEasyGormDb().Unscoped().Where("1 = 1").Delete(Model()).Error
	if err != nil {
		return err
	}
	return nil
}

func UpdateOrCreatePmsBuMemberTransaction(items []*PmsBuMemberDataResponse, _url string, data map[string]string, method, state, errorMsg string) error {
	objects := []map[string]interface{}{}
	for _, item := range items {

		object := map[string]interface{}{
			"ID":                  item.ID,
			"UserCn":              item.UserCn,
			"UserEn":              item.UserEn,
			"Email":               item.Email,
			"EmployeeID":          item.EmployeeID,
			"UserStatus":          item.UserStatus,
			"OneDepartmentName":   item.OneDepartmentName,
			"TwoDepartmentName":   item.TwoDepartmentName,
			"ThreeDepartmentName": item.ThreeDepartmentName,
			"FourDepartmentName":  item.FourDepartmentName,
			"FiveDepartmentName":  item.FiveDepartmentName,
		}

		objects = append(objects, object)
	}

	columns := []string{}

	xt := reflect.TypeOf(Model())
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")
		if ok && ok2 {
			columns = append(columns, name)
		}
	}

	body, err := json.Marshal(data)
	if err != nil {
		return err
	}
	db := easygorm.GetEasyGormDb()
	err = db.Transaction(func(tx *gorm.DB) error {
		if len(objects) > 0 {
			err = tx.Model(Model()).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "id"}},
				DoUpdates: clause.AssignmentColumns(columns),
			}).Create(&objects).Error
			if err != nil {
				return err
			}
		}

		if err := tx.Model(dsyncrecord.Model()).Create(map[string]interface{}{
			"url":             _url,
			"body":            body,
			"method":          method,
			"state":           state,
			"message":         errorMsg,
			"min_modify_date": data["minModifyDate"],
			"max_modify_date": data["maxModifyDate"],
			"created_at":      time.Now(),
		}).Error; err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func All() ([]*ListResponse, error) {
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	err := db.Where("employee_id not like '%RI'").Find(&items).Error
	return items, err
}
