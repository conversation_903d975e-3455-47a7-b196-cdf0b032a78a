package user

import (
	"strconv"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/user/dgitlabtoken"
	"irisAdminApi/service/dao/user/duser"

	usermodels "irisAdminApi/application/models/user"

	"github.com/iris-contrib/middleware/jwt"
	"github.com/jameskeane/bcrypt"
	"github.com/kataras/iris/v12"
	"github.com/xanzy/go-gitlab"
)

type LoginRe struct {
	Username       string `json:"username" validate:"required,gte=2,lte=50" comment:"用户名"`
	Password       string `json:"password" validate:"required,gte=5,lte=30"  comment:"密码"`
	DisableEncrypt bool   `json:"disable_encrypt"`
}

type User struct {
	Id       uint
	Username string
	Password string
	Enable   bool
}

type Token struct {
	AccessToken string
}

func Login(ctx iris.Context) {
	loginReq := LoginRe{}
	if err := ctx.ReadJSON(&loginReq); err != nil {
		logging.ErrorLogger.Errorf("login read request json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if !loginReq.DisableEncrypt {
		decryptUsername, err1 := libs.RsaDecrypt(loginReq.Username)
		decryptPassword, err2 := libs.RsaDecrypt(loginReq.Password)
		if err1 != nil || err2 != nil {
			logging.ErrorLogger.Error("rsa decrypt error", err1, err2)
		}
		loginReq.Username = string(decryptUsername)
		loginReq.Password = string(decryptPassword)
	}

	logging.DebugLogger.Debugf("login user ", loginReq)

	validErr := libs.Validate.Struct(loginReq)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	user := User{}
	username := strings.TrimSuffix(loginReq.Username, "@ruijie.com.cn")
	err := easygorm.GetEasyGormDb().Model(usermodels.User{}).Where("username = ?", username).Find(&user).Error
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	logging.DebugLogger.Debugf("user", user)

	if user.Id == 0 {
		// ctx.JSON(response.NewResponse(response.DataEmptyErr.Code, nil, fmt.Sprintf("用户名 %s不存在", loginReq.Username)))
		ctx.JSON(response.NewResponse(response.AuthNameOrPassErr.Code, nil, response.AuthNameOrPassErr.Msg))
		return
	}

	if !user.Enable {
		ctx.JSON(response.NewResponse(response.AuthNameOrPassErr.Code, nil, "该用户已禁用，请联系管理员"))
		return
	}

	if ok := bcrypt.Match(loginReq.Password, user.Password); !ok {
		ctx.JSON(response.NewResponse(response.AuthNameOrPassErr.Code, nil, response.AuthNameOrPassErr.Msg))
		return
	}

	var token string
	token, err = duser.Login(uint64(user.Id))
	if err != nil {
		ctx.JSON(response.NewResponse(response.AuthErr.Code, nil, err.Error()))
		return
	}

	logging.DebugLogger.Debugf("user token %s", token)
	// 自动获取gitlabtoken
	go GetGitlabTokenjob(&user)
	ctx.JSON(response.NewResponse(response.NoErr.Code, &Token{AccessToken: token}, response.NoErr.Msg))
	return
}

func GetGitlabTokenjob(user *User) {
	// Gitlab配置是否存在,判断libs.Config.Gitlab.url 和 libs.Config.Gitlab.Token 是否为空
	if libs.Config.Gitlab.Url != "" && libs.Config.Gitlab.Token != "" && libs.Config.Gitlab.Enable {
		// 查找用户的gitlabToken
		userGitLabToken := dgitlabtoken.Response{}
		err := userGitLabToken.FindEx("user_id", strconv.FormatUint(uint64(user.Id), 10))
		if err != nil {
			logging.ErrorLogger.Errorf("get token for get git projects err ", err)
			return
		}
		if libs.Config.Gitlab.IsInternal {
			// 判断gitlabToken是否为空
			if len(userGitLabToken.Token) == 0 {
				// 不存在的用户去生成gitLabToken,将生成的gitLabToken请求到gitlab并保存到GitlabToken 表中
				// 创建 GitLab 客户端
				client, err := gitlab.NewClient(libs.Config.Gitlab.Token, gitlab.WithBaseURL(libs.Config.Gitlab.Url+"/api/v4"))
				if err != nil {
					logging.ErrorLogger.Errorf("Failed to create GitLab client: ", err)
				}
				// 查询指定用户名的用户
				users, _, err := client.Users.ListUsers(&gitlab.ListUsersOptions{Username: &user.Username})
				if err != nil {
					logging.ErrorLogger.Errorf("Failed to fetch user information for %s: %v", user.Username, err)
				}
				if len(users) == 0 {
					logging.ErrorLogger.Errorf("User with username %s not found", user.Username)
				}
				var foundUser *gitlab.User
				// 检查每个用户，找到完全匹配的那一个
				for _, gitlabUser := range users {
					if gitlabUser.Username == user.Username {
						foundUser = gitlabUser
						break
					}
				}
				if foundUser == nil {
					logging.ErrorLogger.Errorf("No user found with the username: %s", user.Username)
					return
				}
				// 创建 Personal Access Token 请求
				loc, _ := time.LoadLocation("Asia/Shanghai")
				expiresAt := gitlab.ISOTime(time.Date(2099, 12, 31, 23, 59, 59, 0, loc))
				createTokenRequest := &gitlab.CreatePersonalAccessTokenOptions{
					Name:      gitlab.Ptr("MyAccessToken"),
					Scopes:    &[]string{"api"},
					ExpiresAt: &expiresAt,
				}
				token, _, err := client.Users.CreatePersonalAccessToken(foundUser.ID, createTokenRequest)
				if err != nil {
					logging.ErrorLogger.Errorf("Failed to CreatePersonalAccessToken: ", err)
				}
				// 保存到GitlabToken 表中
				if userGitLabToken.Id == 0 {
					DGitlabToken := dgitlabtoken.Response{}
					err = DGitlabToken.Create(map[string]interface{}{
						"UserId":    user.Id,
						"Token":     token.Token,
						"CreatedAt": time.Now(),
						"UpdatedAt": time.Now(),
					})
					if err != nil {
						logging.ErrorLogger.Errorf("create user token get err ", err)
						return
					}
				} else {
					err = userGitLabToken.Update(userGitLabToken.Id, map[string]interface{}{
						"UserId":    user.Id,
						"Token":     token.Token,
						"UpdatedAt": time.Now(),
					})
					if err != nil {
						logging.ErrorLogger.Errorf("create user token get err ", err)
						return
					}
				}
			}
		} else {
			// 判断gitlabToken是否为空
			if len(userGitLabToken.OutToken) == 0 {
				// 不存在的用户去生成gitLabToken,将生成的gitLabToken请求到gitlab并保存到GitlabToken 表中
				// 创建 GitLab 客户端
				client, err := gitlab.NewClient(libs.Config.Gitlab.Token, gitlab.WithBaseURL(libs.Config.Gitlab.Url+"/api/v4"))
				if err != nil {
					logging.ErrorLogger.Errorf("Failed to create GitLab client: ", err)
				}
				// 查询指定用户名的用户
				users, _, err := client.Users.ListUsers(&gitlab.ListUsersOptions{Username: &user.Username})
				if err != nil {
					logging.ErrorLogger.Errorf("Failed to fetch user information for %s: %v", user.Username, err)
				}
				if len(users) == 0 {
					logging.ErrorLogger.Errorf("User with username %s not found", user.Username)
				}
				var foundUser *gitlab.User
				// 检查每个用户，找到完全匹配的那一个
				for _, gitlabUser := range users {
					if gitlabUser.Username == user.Username {
						foundUser = gitlabUser
						break
					}
				}
				if foundUser == nil {
					logging.ErrorLogger.Errorf("No user found with the username: %s", user.Username)
					return
				}
				// 创建 Personal Access Token 请求
				loc, _ := time.LoadLocation("Asia/Shanghai")
				expiresAt := gitlab.ISOTime(time.Date(2099, 12, 31, 23, 59, 59, 0, loc))
				createTokenRequest := &gitlab.CreatePersonalAccessTokenOptions{
					Name:      gitlab.Ptr("MyAccessToken"),
					Scopes:    &[]string{"api"},
					ExpiresAt: &expiresAt,
				}
				token, _, err := client.Users.CreatePersonalAccessToken(foundUser.ID, createTokenRequest)
				if err != nil {
					logging.ErrorLogger.Errorf("Failed to CreatePersonalAccessToken: ", err)
				}
				// 保存到GitlabToken 表中
				if userGitLabToken.Id == 0 {
					DGitlabToken := dgitlabtoken.Response{}
					err = DGitlabToken.Create(map[string]interface{}{
						"UserId":    user.Id,
						"OutToken":  token.Token,
						"CreatedAt": time.Now(),
						"UpdatedAt": time.Now(),
					})
					if err != nil {
						logging.ErrorLogger.Errorf("create user token get err ", err)
						return
					}
				} else {
					err = userGitLabToken.Update(userGitLabToken.Id, map[string]interface{}{
						"UserId":    user.Id,
						"OutToken":  token.Token,
						"UpdatedAt": time.Now(),
					})
					if err != nil {
						logging.ErrorLogger.Errorf("create user token get err ", err)
						return
					}
				}
			}
		}
	}
}

func Logout(ctx iris.Context) {
	value := ctx.Values().Get("jwt").(*jwt.Token)
	err := duser.Logout(value.Raw)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func Expire(ctx iris.Context) {
	value := ctx.Values().Get("jwt").(*jwt.Token)
	if err := duser.Expire(value.Raw); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func Clear(ctx iris.Context) {
	value := ctx.Values().Get("jwt").(*jwt.Token)
	if err := duser.Clear(value.Raw); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}
