package dmaterial

import (
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/license"
	"strings"
)

const ModelName = "防火墙授权物料表"

type Response struct {
	license.LicenseMaterial
}

func (r *Response) ModelName() string {
	return ModelName
}

func Model() *license.LicenseMaterial {
	return &license.LicenseMaterial{}
}

func GetByDeviceModelAndMaterialCode(deviceModel, materialCode string) (*Response, error) {
	var res []*Response
	if err := easygorm.GetEasyGormDb().Model(Model()).
		Where("device_model = ?", deviceModel).
		Where("material_code = ?", materialCode).
		Find(&res).Error; err != nil {
		return nil, err
	}
	if len(res) == 0 {
		return nil, nil
	}
	return res[0], nil
}

func ListMaterials(page, pageSize int, deviceModel, materialCode string,
	id uint, sort, orderBy, createdAt, updatedAt string) (map[string]interface{}, error) {
	var count int64
	var res []*Response

	db := easygorm.GetEasyGormDb().Model(Model())
	if id != 0 {
		db = db.Where("id =?", id)
	}
	if deviceModel != "" {
		db = db.Where("device_model =?", deviceModel)
	}
	if materialCode != "" {
		db = db.Where("material_code =?", materialCode)
	}

	if len(createdAt) > 0 {
		array := strings.Split(createdAt, ",")
		db = db.Where("created_at between ? and ?", array[0], array[1])
	}
	if len(updatedAt) > 0 {
		array := strings.Split(updatedAt, ",")
		db = db.Where("updated_at between ? and ?", array[0], array[1])
	}
	if len(orderBy) == 0 {
		orderBy = "id"
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("count materials get err %s", err.Error())
		return nil, err
	}
	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("list materials get err %s", err.Error())
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}
