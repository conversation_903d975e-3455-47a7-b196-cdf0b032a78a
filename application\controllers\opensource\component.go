package opensource

import (
	"bufio"
	"fmt"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/xuri/excelize/v2"

	"irisAdminApi/application/controllers/opensource/taskmanagers"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/opensource/dcomponent"
	"irisAdminApi/service/dao/user/duser"
	transaction "irisAdminApi/service/transaction/opensource"
)

type ImportReq struct {
	ImportData string `json:"import_data"`
}

type CreateComponentReq struct {
	Name                 string `json:"name"`
	Version              string `json:"version"`
	Url                  string `json:"url"`
	Product              string `json:"product"`
	ProductVersion       string `json:"product_version"`
	Enable               bool   `json:"enable"`
	IsExternalServe      bool   `json:"is_external_serve"`
	ResponsibleUserIDs   []uint `json:"responsible_user_ids"`
	ResponsibleLeaderIDs []uint `json:"responsible_leader_ids"`
}

func ListComponents(ctx iris.Context) {
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	name := ctx.FormValue("name")
	version := ctx.FormValue("version")
	product := ctx.FormValue("product")
	productVersion := ctx.FormValue("product_version")
	userId, _ := strconv.Atoi(ctx.FormValue("user_id"))
	sort := ctx.FormValue("sort")
	orderBy := ctx.FormValue("orderBy")
	createdAt := ctx.FormValue("created_at")
	updatedAt := ctx.FormValue("updated_at")
	export := ctx.FormValue("export")

	var isEnable = new(bool)
	if enable, err := ctx.URLParamBool("enable"); err != nil {
		isEnable = nil
	} else {
		*isEnable = enable
	}
	var isExternalServe = new(bool)
	if externalServe, err := ctx.URLParamBool("is_external_serve"); err != nil {
		isExternalServe = nil
	} else {
		*isExternalServe = externalServe
	}

	list, err := dcomponent.ListComponents(page, pageSize, name, version, product, productVersion,
		isEnable, isExternalServe, sort, orderBy, createdAt, updatedAt, uint(userId))
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if export == "1" {
		//执行导出动作
		ExportComponents(ctx, list["items"])
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func ExportComponents(ctx iris.Context, items interface{}) {
	boolMap := map[bool]string{
		true:  "是",
		false: "否",
	}
	fileName := fmt.Sprintf("组件清单_%s.xlsx", time.Now().Format("20060102150405"))
	file := excelize.NewFile()
	streamWriter, err := file.NewStreamWriter("Sheet1")

	// styleID, err := file.NewStyle(&excelize.Style{Font: &excelize.Font{Color: "#777777"}})
	if err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	header := []interface{}{"序号", "关联产品", "关联项目", "组件名称", "组件版本", "启用状态", "对外提供服务", "负责人", "专业组长"}
	cell, _ := excelize.CoordinatesToCellName(1, 1)
	if err := streamWriter.SetRow(cell, header); err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	rowNum := 1
	for _, item := range items.([]*dcomponent.ListResponse) {
		rowNum++
		cell, _ := excelize.CoordinatesToCellName(1, rowNum)
		leaders := []string{}
		users := []string{}
		for _, user := range item.ResponsibleLeaders {
			leaders = append(leaders, user.Name)
		}
		for _, user := range item.ResponsibleUsers {
			users = append(users, user.Name)
		}
		row := []interface{}{rowNum - 1, item.Product, item.ProductVersion, item.Name, item.Version, boolMap[item.Enable], boolMap[item.IsExternalServe], strings.Join(leaders, ","), strings.Join(users, ",")}
		if err := streamWriter.SetRow(cell, row); err != nil {
			logging.ErrorLogger.Error(err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
	}
	if err := streamWriter.Flush(); err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if err := file.SaveAs(filepath.Join("/tmp", fileName)); err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	defer os.Remove(filepath.Join("/tmp", fileName))
	ctx.SendFile(filepath.Join("/tmp", fileName), url.QueryEscape(fileName))
	return
}

func GetComponent(ctx iris.Context) {
	componentId, _ := ctx.Params().GetUint("id")

	res := getComponent(ctx, componentId)
	if res == nil {
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, res, response.NoErr.Msg))
}

func createComponentFromReq(ctx iris.Context, req *CreateComponentReq) *response.Response {
	if req.Name == "" {
		return response.NewResponse(response.ParamentErr.Code, nil, "组件名字不能为空")
	}
	if req.Version == "" {
		return response.NewResponse(response.ParamentErr.Code, nil, "组件版本不能为空")
	}
	if req.Product == "" {
		return response.NewResponse(response.ParamentErr.Code, nil, "关联产品不能为空")
	}
	if req.ProductVersion == "" {
		return response.NewResponse(response.ParamentErr.Code, nil, "关联项目不能为空")
	}

	if len(req.ResponsibleUserIDs) == 0 {
		return response.NewResponse(response.ParamentErr.Code, nil, "负责人参数<responsible_user_ids>不能为空")
	}
	if len(req.ResponsibleLeaderIDs) == 0 {
		return response.NewResponse(response.ParamentErr.Code, nil, "负责人组长参数<responsible_leader_ids>不能为空")
	}

	componentRes := &dcomponent.OpenSourceComponent{}
	componentRes.Name = req.Name
	componentRes.Version = req.Version
	componentRes.Url = req.Url
	componentRes.Product = req.Product
	componentRes.ProductVersion = req.ProductVersion
	componentRes.Enable = req.Enable
	componentRes.IsExternalServe = req.IsExternalServe

	err := transaction.CreateComponent(componentRes, req.ResponsibleUserIDs, req.ResponsibleLeaderIDs)
	if err != nil {
		return response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg)

	}
	logging.DebugLogger.Debugf("组件创建完成: ID-<%d>, Name-<%s>", componentRes.ID, componentRes.Name)

	// 添加定时同步漏洞任务
	logging.DebugLogger.Debugf("为组件添加定时同步漏洞任务: ID-<%d>, Name-<%s>", componentRes.ID, componentRes.Name)
	if taskmanagers.ScheduledCVESyncTaskManager != nil {
		taskmanagers.ScheduledCVESyncTaskManager.AddScheduledCVESyncTask(componentRes)
	}
	// 添加定时发送漏洞超期未处理邮件任务
	logging.DebugLogger.Debugf("为组件添加定时发送漏洞超期未处理邮件任务: ID-<%d>, Name-<%s>", componentRes.ID, componentRes.Name)
	if taskmanagers.ScheduledSendEmailTaskManager != nil {
		taskmanagers.ScheduledSendEmailTaskManager.AddScheduledSendEmailTask(componentRes)
	}
	return response.NewResponse(response.NoErr.Code, componentRes, response.NoErr.Msg)
}

func CreateComponent(ctx iris.Context) {
	req := &CreateComponentReq{}
	if err := ctx.ReadJSON(req); err != nil {
		logging.ErrorLogger.Errorf("create component read json err %s", err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(createComponentFromReq(ctx, req))
}

func ImportComponents(ctx iris.Context) {
	importFile, _, _ := ctx.FormFile("import_data")
	buff := bufio.NewReader(importFile)
	excel, err := excelize.OpenReader(buff)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	rows, err := excel.GetRows("Sheet1")
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	var componentResList []*dcomponent.OpenSourceComponent
	var responsibleUserIDsList [][]uint
	var responsibleLeaderIDsList [][]uint

	var failedLines []string
	var currentLine int
	for i, slice := range rows {
		if i == 0 {
			continue
		}

		if len(slice) == 9 {
			// 0: name, 1: version, 2: url, 3: product_name, 4: product_version,
			// 5: isEnable, 6: isExternalServe, 7: responsibleUsernames
			// 8. responsibleLeaderUsernames
			componentName := slice[0]
			if componentName == "" {
				failedLines = append(failedLines, strconv.Itoa(currentLine))
				continue
			}

			componentVersion := slice[1]
			if componentVersion == "" {
				failedLines = append(failedLines, strconv.Itoa(currentLine))
				continue
			}

			componentURL := slice[2]

			productName := slice[3]
			if productName == "" {
				failedLines = append(failedLines, strconv.Itoa(currentLine))
				continue
			}

			productVersion := slice[4]
			if productVersion == "" {
				failedLines = append(failedLines, strconv.Itoa(currentLine))
				continue
			}

			isEnable, err := strconv.ParseBool(slice[5])
			if err != nil {
				failedLines = append(failedLines, strconv.Itoa(currentLine))
				continue
			}

			isExternalServe, err := strconv.ParseBool(slice[6])
			if err != nil {
				failedLines = append(failedLines, strconv.Itoa(currentLine))
				continue
			}

			responsibleUserUsernames := strings.Split(slice[7], "/")
			var responsibleUserIDs []uint
			for _, responsibleUserUsername := range responsibleUserUsernames {
				userRes := &duser.User{}
				if err := userRes.FindByUserName(responsibleUserUsername); err == nil {
					responsibleUserIDs = append(responsibleUserIDs, userRes.ID)
				} else {
					break
				}
			}
			if len(responsibleUserIDs) != len(responsibleUserUsernames) {
				failedLines = append(failedLines, strconv.Itoa(currentLine))
				continue
			}

			responsibleLeaderUsernames := strings.Split(slice[8], "/")
			var responsibleLeaderIDs []uint
			for _, responsibleLeaderUsername := range responsibleLeaderUsernames {
				userRes := &duser.User{}
				if err := userRes.FindByUserName(responsibleLeaderUsername); err == nil {
					responsibleLeaderIDs = append(responsibleLeaderIDs, userRes.ID)
				} else {
					break
				}
			}
			if len(responsibleLeaderIDs) != len(responsibleLeaderUsernames) {
				failedLines = append(failedLines, strconv.Itoa(currentLine))
				continue
			}

			componentRes := &dcomponent.OpenSourceComponent{}
			componentRes.Name = componentName
			componentRes.Version = componentVersion
			componentRes.Url = componentURL
			componentRes.Product = productName
			componentRes.ProductVersion = productVersion
			componentRes.Enable = isEnable
			componentRes.IsExternalServe = isExternalServe

			componentResList = append(componentResList, componentRes)
			responsibleUserIDsList = append(responsibleUserIDsList, responsibleUserIDs)
			responsibleLeaderIDsList = append(responsibleLeaderIDsList, responsibleLeaderIDs)
		} else {
			failedLines = append(failedLines, strconv.Itoa(currentLine))
		}
	}

	if len(componentResList) != 0 {
		if err := transaction.CreateComponents(componentResList, responsibleUserIDsList, responsibleLeaderIDsList); err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		for _, componentRes := range componentResList {
			if taskmanagers.ScheduledCVESyncTaskManager != nil {
				taskmanagers.ScheduledCVESyncTaskManager.AddScheduledCVESyncTask(componentRes)
			}
			if taskmanagers.ScheduledSendEmailTaskManager != nil {
				taskmanagers.ScheduledSendEmailTaskManager.AddScheduledSendEmailTask(componentRes)
			}
		}
	}

	if len(failedLines) == 0 {
		ctx.JSON(response.NewResponse(response.NoErr.Code, nil, "导入成功"))
	} else {
		ctx.JSON(response.NewResponse(response.NoErr.Code, nil,
			fmt.Sprintf("部分导入成功, 失败行数列表: %s", strings.Join(failedLines, ","))))
	}
	return
}

func DeleteComponent(ctx iris.Context) {
	componentId, _ := ctx.Params().GetUint("id")

	err := transaction.DeleteComponent(componentId)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func ManualComponentCVESync(ctx iris.Context) {
	componentId, _ := ctx.Params().GetUint("id")
	componentRes := getComponent(ctx, componentId)
	if componentRes == nil {
		return
	}

	logging.DebugLogger.Debugf("组件执行手动同步漏洞: ID-<%d>, Name-<%s>", componentRes.ID, componentRes.Name)
	taskmanagers.AddManualCVESyncTask(componentRes)
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, "执行成功, 同步中"))
	return
}

type UpdateComponentReq struct {
	Name            string `json:"name"`
	Version         string `json:"version"`
	Url             string `json:"url"`
	Product         string `json:"product"`
	ProductVersion  string `json:"product_version"`
	Enable          bool   `json:"enable"`
	IsExternalServe bool   `json:"is_external_serve"`
}

func UpdateComponent(ctx iris.Context) {
	componentId, _ := ctx.Params().GetUint("id")
	componentRes := getComponent(ctx, componentId)
	if componentRes == nil {
		return
	}

	req := &UpdateComponentReq{}
	if err := ctx.ReadJSON(req); err != nil {
		logging.ErrorLogger.Errorf("update component read json err %s", err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	updateComponent := map[string]interface{}{}
	if req.Name != "" {
		updateComponent["name"] = req.Name
	}
	if req.Url != "" {
		updateComponent["url"] = req.Url
	}
	if req.Version != "" {
		updateComponent["version"] = req.Version
	}
	if req.Product != "" {
		updateComponent["product"] = req.Product
	}
	if req.ProductVersion != "" {
		updateComponent["product_version"] = req.ProductVersion
	}
	if req.Enable != componentRes.Enable {
		updateComponent["enable"] = req.Enable
	}
	if req.IsExternalServe != componentRes.IsExternalServe {
		updateComponent["is_external_serve"] = req.IsExternalServe
	}

	err := componentRes.Update(componentRes.ID, updateComponent)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	// componentRes = getComponent(ctx, componentId)
	// if componentRes == nil {
	// 	return
	// }
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

const ComponentsTemplateFile = "./files/opensource/example/components.xlsx"

func DownloadComponentImportTemplate(ctx iris.Context) {
	ctx.SendFile(ComponentsTemplateFile, "components.xlsx")
}
