package license

import "irisAdminApi/application/models"

type LicenseDeviceModel struct {
	models.ModelBase
	DeviceType         string `gorm:"not null; type:varchar(50)" json:"device_type"`
	DeviceModel        string `gorm:"not null; type:varchar(50)" json:"device_model"`
	DeviceMaterialCode string `gorm:"not null; type:varchar(50)" json:"device_material_code"`
}

type LicenseMaterial struct {
	models.ModelBase
	DeviceModel  string `gorm:"not null; type:varchar(50)" json:"device_model"`
	MaterialCode string `gorm:"not null; type:varchar(50)" json:"material_code"`
	MaterialName string `gorm:"not null; type:varchar(50)" json:"material_name"`
	AuthType     uint   `json:"auth_type"`
	Desc         string `gorm:"not null" json:"desc"`
}

type LicenseAuthTask struct {
	models.ModelBase
	DeviceSn           string `gorm:"not null; type:varchar(50)" json:"device_sn"`
	MaterialCode       string `gorm:"not null; type:varchar(50)" json:"material_code"`
	MaterialName       string `gorm:"not null; type:varchar(50)" json:"material_name"`
	DeviceModel        string `gorm:"not null; type:varchar(50)" json:"device_model"`
	DeviceType         string `gorm:"not null; type:varchar(50)" json:"device_type"`
	AuthType           uint   `json:"auth_type"`
	DeviceMaterialCode string `gorm:"not null; type:varchar(50)" json:"device_material_code"`
	TicketCode         string `gorm:"type:varchar(50)" json:"ticket_code"`
	SnCode             string `gorm:"type:varchar(50)" jjson:"sn_code"`
	AuthCode           string `gorm:"type:varchar(100)" jjson:"auth_code"`
	Finished           bool   `json:"finished"`
	IsActive           bool   `json:"is_active"`
	Message            string `json:"message"`
	UserID             uint   `gorm:"not null" json:"user_id"`
}
