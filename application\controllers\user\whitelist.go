package user

import (
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/user/duserwhitelist"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

func GetAllAutoAuditWhiteLists(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&duserwhitelist.UserWhiteList{}, ctx, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func DeleteAutoAuditWhiteList(ctx iris.Context) {
	err := dao.Delete(&duserwhitelist.UserWhiteList{}, ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func CreateOrUpdateAutoAuditWhiteList(ctx iris.Context) {
	req := duserwhitelist.Request{}
	if err := ctx.ReadJSON(&req); err != nil {
		logging.ErrorLogger.Errorf("create autoauditfile read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(req)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	err := dao.Create(&duserwhitelist.UserWhiteList{}, ctx, map[string]interface{}{
		"CreatedAt":  time.Now(),
		"UserID":     req.UserID,
		"Fileout":    req.Fileout,
		"Buildfarm":  req.Buildfarm,
		"Opensource": req.Opensource,
		"Comment":    req.Comment,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, req, response.NoErr.Msg))
	return
}

func GetAllBlackUsers(ctx iris.Context) {
	whitelist := duserwhitelist.UserWhiteList{}
	users, err := whitelist.FindAllBlackUsers()
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, users, response.NoErr.Msg))
	return
}
