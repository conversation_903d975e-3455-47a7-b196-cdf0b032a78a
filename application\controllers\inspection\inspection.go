package inspection

import (
	"encoding/json"
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/inspection/dinspectionjob"
	"irisAdminApi/service/dao/inspection/dinspectionlog"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/qifengzhang007/gooxml/color"
	"github.com/qifengzhang007/gooxml/document"
	"github.com/qifengzhang007/gooxml/measurement"
	"github.com/qifengzhang007/gooxml/schema/soo/wml"
)

func Upload(ctx iris.Context) {
	//先执行文件上传，文件上传成功后执行创建申请单操作
	fmt.Println("upload")
	request := &dinspectionlog.Request{}
	if err := ctx.ReadForm(request); err != nil {
		logging.ErrorLogger.Errorf("upload read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	f, fh, err := ctx.FormFile("file")
	defer f.Close()

	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	var upload = filepath.Join(libs.Config.FileStorage.Upload, time.Now().Format("20060102"), request.JobID)
	err = os.MkdirAll(upload, 0750)
	os.Chmod(upload, 0750)
	fp := filepath.Join(upload, fh.Filename)
	_, err = ctx.SaveFormFile(fh, fp)
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	inspection := dinspectionlog.InspectionLog{}
	err = inspection.Create(map[string]interface{}{
		"CreatedAt": time.Now(),
		"JobID":     request.JobID,
		"Hostname":  request.Hostname,
		"Ip":        request.Ip,
		"Log":       fp,
	})
	if err != nil {
		defer os.RemoveAll(fp)
		logging.ErrorLogger.Errorf("create inspect record error ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func DownloadPDF(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	job := dinspectionjob.InspectionJob{}

	err = job.Find(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if job.ID == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	report, err := GeneratePDF(&job)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.SendFile(report, filepath.Base(report))
	return
}

func Jobs(ctx iris.Context) {
	job := dinspectionjob.InspectionJob{}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	jobs, err := job.All(name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, jobs, response.NoErr.Msg))
	return
}

func Logs(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	job := dinspectionjob.InspectionJob{}
	err := job.Find(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	log := dinspectionlog.InspectionLog{}
	// name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	logs, err := log.All(job.JobID, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, logs, response.NoErr.Msg))
	return
}

type InspectionResult struct {
	Type      string       `json:"type"`
	Hostname  string       `json:"hostname"`
	IP        string       `json:"ip"`
	CreatedAt string       `json:"created_at"`
	Result    []RuleResult `json:"result"`
}

type RuleResult struct {
	RuleID string   `json:"rule_id"`
	Name   string   `json:"name"`
	Code   string   `json:"code"`
	Msg    string   `json:"msg"`
	Data   []string `json:"data"`
}

type FullRuleResult struct {
	Type      string `json:"type"`
	Hostname  string `json:"hostname"`
	IP        string `json:"ip"`
	CreatedAt string `json:"created_at"`
	RuleResult
}

func GeneratePDF(job *dinspectionjob.InspectionJob) (string, error) {
	logs, err := dinspectionlog.AllByJobID(job.JobID)
	if err != nil {
		return "", err
	}

	results := []InspectionResult{}
	for _, log := range logs {
		var command string
		var _type string
		if strings.HasPrefix(filepath.Base(log.Log), "server") {
			_type = "server"
			command = fmt.Sprintf("python3 /tmp/auto_inspection/parser_server_log.py %s", log.Log)
		} else if strings.HasPrefix(filepath.Base(log.Log), "switch") {
			_type = "switch"
			command = fmt.Sprintf("python3 /tmp/auto_inspection/parser_switch_log.py %s", log.Log)
		} else {
			_type = "unkown"
			command = "echo error: 未知类型巡检日志"
		}
		output, err := libs.ExecCommand(command)
		if err != nil {
			fmt.Println(err)
			results = append(results, InspectionResult{
				Type:      _type,
				Hostname:  log.Hostname,
				IP:        log.Ip,
				CreatedAt: job.CreatedAt.Format("2006-01-02 15:04:05"),
				Result: []RuleResult{
					{
						RuleID: "00",
						Name:   "分析巡检日志",
						Code:   "-1",
						Msg:    "分析巡检日志失败",
						Data:   []string{err.Error(), output},
					},
				},
			})
		}
		jsonData := []byte(output)
		var result InspectionResult
		err = json.Unmarshal(jsonData, &result)
		if err != nil {
			results = append(results, InspectionResult{
				Type:      _type,
				Hostname:  log.Hostname,
				IP:        log.Ip,
				CreatedAt: job.CreatedAt.Format("2006-01-02 15:04:05"),
				Result: []RuleResult{
					{
						RuleID: "00",
						Name:   "分析巡检日志",
						Code:   "-1",
						Msg:    "分析巡检日志失败",
						Data:   []string{err.Error(), string(jsonData)},
					},
				},
			})
		}
		results = append(results, result)
	}

	report, err := reportInspectionResults(job, results)
	if err != nil {
		return "", err
	}
	//
	return report, nil
}

func reportInspectionResults(job *dinspectionjob.InspectionJob, results []InspectionResult) (string, error) {
	var switchCount, serverCount, issueCount, checkCount int

	serverRulePassResults := map[string][]FullRuleResult{}
	serverRuleNotPassResults := map[string][]FullRuleResult{}
	serverRuleIDs := []string{}
	serverRuleNames := []string{}

	switchRulePassResults := map[string][]FullRuleResult{}
	switchRuleNotPassResults := map[string][]FullRuleResult{}
	switchRuleIDs := []string{}
	switchRuleNames := []string{}

	for _, result := range results {
		switch result.Type {
		case "switch":
			switchCount++
			for _, rule := range result.Result {
				checkCount++
				if !libs.InArrayS(switchRuleIDs, rule.RuleID) {
					switchRuleIDs = append(switchRuleIDs, rule.RuleID)
					switchRuleNames = append(switchRuleNames, rule.Name)
				}

				if _, ok := switchRulePassResults[rule.RuleID]; !ok {
					switchRulePassResults[rule.RuleID] = []FullRuleResult{}
				}

				if _, ok := switchRuleNotPassResults[rule.RuleID]; !ok {
					switchRuleNotPassResults[rule.RuleID] = []FullRuleResult{}
				}

				if rule.Code != "1" { // 假设 code 不为 "1" 表示有问题
					issueCount++
					switchRuleNotPassResults[rule.RuleID] = append(switchRuleNotPassResults[rule.RuleID], FullRuleResult{
						Type:       result.Type,
						Hostname:   result.Hostname,
						IP:         result.IP,
						RuleResult: rule,
					})
				} else {
					switchRulePassResults[rule.RuleID] = append(switchRulePassResults[rule.RuleID], FullRuleResult{
						Type:       result.Type,
						Hostname:   result.Hostname,
						IP:         result.IP,
						RuleResult: rule,
					})
				}
			}
		case "server":
			serverCount++
			for _, rule := range result.Result {
				checkCount++
				if !libs.InArrayS(serverRuleIDs, rule.RuleID) {
					serverRuleIDs = append(serverRuleIDs, rule.RuleID)
					serverRuleNames = append(serverRuleNames, rule.Name)
				}

				if _, ok := serverRulePassResults[rule.RuleID]; !ok {
					serverRulePassResults[rule.RuleID] = []FullRuleResult{}
				}
				if _, ok := serverRuleNotPassResults[rule.RuleID]; !ok {
					serverRuleNotPassResults[rule.RuleID] = []FullRuleResult{}
				}

				if rule.Code != "1" { // 假设 code 不为 "1" 表示有问题
					issueCount++
					serverRuleNotPassResults[rule.RuleID] = append(serverRuleNotPassResults[rule.RuleID], FullRuleResult{
						Type:       result.Type,
						Hostname:   result.Hostname,
						IP:         result.IP,
						RuleResult: rule,
					})
				} else {
					serverRulePassResults[rule.RuleID] = append(serverRulePassResults[rule.RuleID], FullRuleResult{
						Type:       result.Type,
						Hostname:   result.Hostname,
						IP:         result.IP,
						RuleResult: rule,
					})
				}
			}
		}
	}
	doc := document.New()

	para := doc.AddParagraph()
	run := para.AddRun()
	para.SetStyle("Title")
	run.AddText(fmt.Sprintf("巡检报告_%s", job.JobID))

	para = doc.AddParagraph()
	para.SetStyle("Heading1")
	run = para.AddRun()
	run.Properties().SetBold(true)
	run.AddText("概要")

	para = doc.AddParagraph()
	run = para.AddRun()
	run.AddText(fmt.Sprintf("服务器数量：%d              交换机数量: %d", serverCount, switchCount))
	para = doc.AddParagraph()
	run = para.AddRun()
	run.AddText(fmt.Sprintf("巡检规则数%d                不通过规则数: %d", checkCount, issueCount))

	para = doc.AddParagraph()
	para.SetStyle("Heading1")
	run = para.AddRun()
	run.Properties().SetBold(true)
	run.AddText("服务器")

	for i, ruleID := range serverRuleIDs {
		para = doc.AddParagraph()
		para.SetStyle("Heading2")
		run = para.AddRun()
		if len(serverRuleNotPassResults[ruleID]) > 0 {
			run.Properties().SetColor(color.Red)
		}
		run.AddText(fmt.Sprintf("%s %s 不通过：%d", ruleID, serverRuleNames[i], len(serverRuleNotPassResults[ruleID])))

		for _, result := range serverRuleNotPassResults[ruleID] {
			para = doc.AddParagraph()
			para.SetStyle("Heading3")
			run = para.AddRun()
			run.Properties().SetColor(color.Red)
			run.AddText(fmt.Sprintf("不通过：%s %s", result.Hostname, result.IP))

			var data = []string{}
			if len(result.RuleResult.Data) > 10 {
				data = result.RuleResult.Data[len(result.RuleResult.Data)-10:]
			} else {
				data = result.RuleResult.Data
			}
			for _, item := range data {
				para = doc.AddParagraph()
				run = para.AddRun()
				run.AddText(item)
			}
		}
		for _, result := range serverRulePassResults[ruleID] {
			para = doc.AddParagraph()
			para.SetStyle("Heading3")
			run = para.AddRun()
			run.AddText(fmt.Sprintf("通过：%s %s", result.Hostname, result.IP))

			var data = []string{}
			if len(result.RuleResult.Data) > 10 {
				data = result.RuleResult.Data[len(result.RuleResult.Data)-10:]
			} else {
				data = result.RuleResult.Data
			}
			for _, item := range data {
				para = doc.AddParagraph()
				run = para.AddRun()
				run.AddText(item)
			}
		}
	}

	para = doc.AddParagraph()
	para.SetStyle("Heading1")
	run = para.AddRun()
	run.Properties().SetBold(true)
	run.AddText("交换机")

	for i, ruleID := range switchRuleIDs {
		para = doc.AddParagraph()
		para.SetStyle("Heading2")
		run = para.AddRun()
		if len(switchRuleNotPassResults[ruleID]) > 0 {
			run.Properties().SetColor(color.Red)
		}

		run.AddText(fmt.Sprintf("%s %s 不通过：%d", ruleID, switchRuleNames[i], len(switchRuleNotPassResults[ruleID])))

		for _, result := range switchRuleNotPassResults[ruleID] {
			para = doc.AddParagraph()
			para.SetStyle("Heading3")
			run = para.AddRun()
			run.Properties().SetColor(color.Red)
			run.AddText(fmt.Sprintf("不通过：%s %s", result.Hostname, result.IP))

			var data = []string{}
			if len(result.RuleResult.Data) > 10 {
				data = result.RuleResult.Data[len(result.RuleResult.Data)-10:]
			} else {
				data = result.RuleResult.Data
			}
			for _, item := range data {
				para = doc.AddParagraph()
				run = para.AddRun()
				run.AddText(item)
			}
		}
		for _, result := range switchRulePassResults[ruleID] {
			para = doc.AddParagraph()
			para.SetStyle("Heading3")
			run = para.AddRun()
			run.AddText(fmt.Sprintf("通过：%s %s", result.Hostname, result.IP))

			var data = []string{}
			if len(result.RuleResult.Data) > 10 {
				data = result.RuleResult.Data[len(result.RuleResult.Data)-10:]
			} else {
				data = result.RuleResult.Data
			}
			for _, item := range data {
				para = doc.AddParagraph()
				run = para.AddRun()
				run.AddText(item)
			}
		}
	}
	report := fmt.Sprintf("/tmp/巡检报告_%s.docx", job.JobID)
	err := doc.SaveToFile(report)
	if err != nil {
		return "", err
	}
	return report, nil
}

func generateDocReport(results []InspectionResult) {

	doc := document.New()

	para := doc.AddParagraph()
	run := para.AddRun()
	para.SetStyle("Title")
	run.AddText("Simple Document Formatting")

	para = doc.AddParagraph()
	para.SetStyle("Heading1")
	run = para.AddRun()
	run.AddText("Some Heading Text")

	para = doc.AddParagraph()
	para.SetStyle("Heading2")
	run = para.AddRun()
	run.AddText("Some Heading Text")

	para = doc.AddParagraph()
	para.SetStyle("Heading3")
	run = para.AddRun()
	run.AddText("Some Heading Text")

	para = doc.AddParagraph()
	para.Properties().SetFirstLineIndent(0.5 * measurement.Inch)

	run = para.AddRun()
	run.AddText("A run is a string of characters with the same formatting. ")

	run = para.AddRun()
	run.Properties().SetBold(true)
	run.Properties().SetFontFamily("Courier")
	run.Properties().SetSize(15)
	run.Properties().SetColor(color.Red)
	run.AddText("Multiple runs with different formatting can exist in the same paragraph. ")

	run = para.AddRun()
	run.AddText("Adding breaks to a run will insert line breaks after the run. ")
	run.AddBreak()
	run.AddBreak()

	run = createParaRun(doc, "Runs support styling options:")

	run = createParaRun(doc, "small caps")
	run.Properties().SetSmallCaps(true)

	run = createParaRun(doc, "strike")
	run.Properties().SetStrikeThrough(true)

	run = createParaRun(doc, "double strike")
	run.Properties().SetDoubleStrikeThrough(true)

	run = createParaRun(doc, "outline")
	run.Properties().SetOutline(true)

	run = createParaRun(doc, "emboss")
	run.Properties().SetEmboss(true)

	run = createParaRun(doc, "shadow")
	run.Properties().SetShadow(true)

	run = createParaRun(doc, "imprint")
	run.Properties().SetImprint(true)

	run = createParaRun(doc, "highlighting")
	run.Properties().SetHighlight(wml.ST_HighlightColorYellow)

	run = createParaRun(doc, "underline")
	run.Properties().SetUnderline(wml.ST_UnderlineWavyDouble, color.Red)

	run = createParaRun(doc, "text effects")
	run.Properties().SetEffect(wml.ST_TextEffectAntsRed)

	nd := doc.Numbering.Definitions()[0]

	for i := 1; i < 5; i++ {
		p := doc.AddParagraph()
		p.SetNumberingLevel(i - 1)
		p.SetNumberingDefinition(nd)
		run := p.AddRun()
		run.AddText(fmt.Sprintf("Level %d", i))
	}
	doc.SaveToFile("/tmp/simple.docx")
}

func createParaRun(doc *document.Document, s string) document.Run {
	para := doc.AddParagraph()
	run := para.AddRun()
	run.AddText(s)
	return run
}
