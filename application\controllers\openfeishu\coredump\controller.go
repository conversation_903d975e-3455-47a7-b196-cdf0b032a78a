package coredump

import (
	"fmt"
	"strconv"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"

	"github.com/kataras/iris/v12"
)

// CoredumpController Coredump处理控制器
type CoredumpController struct {
	service   *CoredumpAutoSyncService
	scheduler *CoredumpScheduler
}

// NewCoredumpController 创建控制器
func NewCoredumpController() *CoredumpController {
	service := NewCoredumpAutoSyncService()
	scheduler := NewCoredumpScheduler(service)

	return &CoredumpController{
		service:   service,
		scheduler: scheduler,
	}
}

// ProcessCoredumps 手动触发Coredump处理
func (c *CoredumpController) ProcessCoredumps(ctx iris.Context) {
	logging.InfoLogger.Info("[COREDUMP] 收到手动处理请求")

	// 检查功能是否启用
	if !libs.Config.FeiShuDoc.CoredumpEnable {
		ctx.JSON(iris.Map{
			"success": false,
			"message": "Coredump功能未启用",
		})
		return
	}

	// 检查是否有正在运行的任务
	if c.scheduler.IsRunning() {
		ctx.JSON(iris.Map{
			"success": false,
			"message": "已有任务正在运行，请稍后再试",
		})
		return
	}

	// 解析请求参数
	var req ProcessRequest
	if err := ctx.ReadJSON(&req); err != nil {
		// 如果没有请求体，使用默认参数
		req = ProcessRequest{
			DryRun: libs.Config.FeiShuDoc.CoredumpDryRun,
		}
	}

	// 临时设置干运行模式（如果请求指定）
	originalDryRun := libs.Config.FeiShuDoc.CoredumpDryRun
	if req.DryRun != originalDryRun {
		libs.Config.FeiShuDoc.CoredumpDryRun = req.DryRun
		defer func() {
			libs.Config.FeiShuDoc.CoredumpDryRun = originalDryRun
		}()
	}

	// 执行处理
	result, err := c.service.ProcessCoredumpRecords()
	if err != nil {
		logging.ErrorLogger.Errorf("[COREDUMP] 手动处理失败: %v", err)
		ctx.JSON(iris.Map{
			"success": false,
			"message": fmt.Sprintf("处理失败: %v", err),
		})
		return
	}

	// 记录执行历史
	c.scheduler.RecordExecution(result, "manual", "")

	logging.InfoLogger.Infof("[COREDUMP] 手动处理完成: 总计=%d, 成功=%d, 失败=%d",
		result.TotalRecords, result.SuccessRecords, result.FailedRecords)

	ctx.JSON(iris.Map{
		"success": true,
		"data":    result,
		"message": "处理完成",
	})
}

// GetProcessingStatus 获取处理状态
func (c *CoredumpController) GetProcessingStatus(ctx iris.Context) {
	status := c.scheduler.GetStatus()

	ctx.JSON(iris.Map{
		"success": true,
		"data":    status,
		"message": "获取状态成功",
	})
}

// StartScheduler 启动定时任务
func (c *CoredumpController) StartScheduler(ctx iris.Context) {
	if !libs.Config.FeiShuDoc.CoredumpEnable {
		ctx.JSON(iris.Map{
			"success": false,
			"message": "Coredump功能未启用",
		})
		return
	}

	err := c.scheduler.Start()
	if err != nil {
		logging.ErrorLogger.Errorf("[COREDUMP] 启动定时任务失败: %v", err)
		ctx.JSON(iris.Map{
			"success": false,
			"message": fmt.Sprintf("启动失败: %v", err),
		})
		return
	}

	logging.InfoLogger.Info("[COREDUMP] 定时任务已启动")
	ctx.JSON(iris.Map{
		"success": true,
		"message": "定时任务已启动",
	})
}

// StopScheduler 停止定时任务
func (c *CoredumpController) StopScheduler(ctx iris.Context) {
	c.scheduler.Stop()

	logging.InfoLogger.Info("[COREDUMP] 定时任务已停止")
	ctx.JSON(iris.Map{
		"success": true,
		"message": "定时任务已停止",
	})
}

// GetSchedulerStatus 获取定时任务状态
func (c *CoredumpController) GetSchedulerStatus(ctx iris.Context) {
	status := c.scheduler.GetSchedulerStatus()

	ctx.JSON(iris.Map{
		"success": true,
		"data":    status,
		"message": "获取定时任务状态成功",
	})
}

// GetProcessingHistory 获取处理历史记录
func (c *CoredumpController) GetProcessingHistory(ctx iris.Context) {
	// 解析分页参数
	page := 1
	pageSize := 20

	if pageStr := ctx.URLParam("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if sizeStr := ctx.URLParam("page_size"); sizeStr != "" {
		if s, err := strconv.Atoi(sizeStr); err == nil && s > 0 && s <= 100 {
			pageSize = s
		}
	}

	history := c.scheduler.GetExecutionHistory(page, pageSize)

	ctx.JSON(iris.Map{
		"success": true,
		"data":    history,
		"message": "获取历史记录成功",
	})
}

// ResetLastRunTime 重置最后运行时间（管理功能）
func (c *CoredumpController) ResetLastRunTime(ctx iris.Context) {
	c.scheduler.ResetLastRunTime()

	logging.InfoLogger.Info("[COREDUMP] 最后运行时间已重置")
	ctx.JSON(iris.Map{
		"success": true,
		"message": "重置成功",
	})
}

// GetSystemInfo 获取系统信息
func (c *CoredumpController) GetSystemInfo(ctx iris.Context) {
	info := map[string]interface{}{
		"enabled":           libs.Config.FeiShuDoc.CoredumpEnable,
		"debug_mode":        libs.Config.FeiShuDoc.CoredumpDebugMode,
		"dry_run":           libs.Config.FeiShuDoc.CoredumpDryRun,
		"cron_expr":         libs.Config.FeiShuDoc.CoredumpCronExpr,
		"app_token":         maskToken(libs.Config.FeiShuDoc.CoredumpAppToken),
		"table_id":          libs.Config.FeiShuDoc.CoredumpTableID,
		"scheduler_running": c.scheduler.IsRunning(),
		"last_run_time":     c.scheduler.GetLastRunTime(),
		"next_run_time":     c.scheduler.GetNextRunTime(),
	}

	ctx.JSON(iris.Map{
		"success": true,
		"data":    info,
		"message": "获取系统信息成功",
	})
}

// ProcessRequest 处理请求参数
type ProcessRequest struct {
	DryRun     bool   `json:"dry_run"`     // 是否干运行
	Component  string `json:"component"`   // 指定组件（可选）
	TimeRange  string `json:"time_range"`  // 时间范围（可选）
	MaxRecords int    `json:"max_records"` // 最大处理记录数（可选）
}

// maskToken 遮蔽敏感信息
func maskToken(token string) string {
	if len(token) <= 8 {
		return "****"
	}
	return token[:4] + "****" + token[len(token)-4:]
}
