package codesync

import (
	"irisAdminApi/application/libs"
	"time"

	"github.com/imroc/req/v3"
)

var CodeSyncClient *req.Client

func InitClient() {
	CodeSyncClient = req.C().
		SetCommonRetryCount(3).
		// Set the retry sleep interval with a commonly used algorithm: capped exponential backoff with jitter (https://aws.amazon.com/blogs/architecture/exponential-backoff-and-jitter/).
		SetCommonRetryBackoffInterval(1*time.Second, 5*time.Second).
		AddCommonRetryCondition(func(resp *req.Response, err error) bool {
			return err != nil
		})
	if libs.Config.Debug {
		CodeSyncClient.DevMode()
	}
}

var GitlabApis = map[string]string{
	"fork_list":             "%s/api/%s/projects/%s/forks",
	"fork_create":           "%s/api/%s/projects/%s/fork",
	"project_merge_request": "%s/api/%s/projects/%s/merge_requests",
	"merge_request_commit":  "%s/api/%s/projects/%s/merge_requests/%s/commits",
}

type MergeRequestResponse struct {
	ID              int            `json:"id"`
	IID             int            `json:"iid"`
	ProjectID       int            `json:"project_id"`
	SourceProjectID int            `json:"source_project_id"`
	SourceBranch    string         `json:"source_branch"`
	TargetProjectID int            `json:"target_project_id"`
	TargetBranch    string         `json:"target_branch"`
	WebUrl          string         `json:"web_url"`
	Assignees       []UserResponse `json:"assignees"`
	Reviewers       []UserResponse `json:"reviewers"`
	Title           string         `json:"title"`
	Description     string         `json:"description"`
	State           string         `json:"state"`
	Author          UserResponse   `json:"author"`
	MergedAt        string         `json:"merged_at"`
	CreatedAt       string         `json:"created_at"`
	UpdatedAt       string         `json:"updated_at"`
}

type UserResponse struct {
	ID       int    `json:"id"`
	Username string `json:"username"`
}

type CommitsResponse struct {
	ID      string `json:"id"`
	Message string `json:"message"`
}

type ForkResponse struct {
	ID                int    `json:"id"`
	SshUrlToRepo      string `json:"ssh_url_to_repo"`
	ImportStatus      string `json:"import_status"`
	PathWithNamespace string `json:"path_with_namespace"`
}

type MergeRequestListResponse []*MergeRequestListResponse
