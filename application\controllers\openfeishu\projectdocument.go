package openfeishu

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/feishu/dfeishubitablelist"
	"irisAdminApi/service/dao/feishu/dfeishudocumenttemplate"
	"irisAdminApi/service/dao/feishu/dfeishufilelist"
	"irisAdminApi/service/dao/feishu/dfeishuprojectdocument"
	"irisAdminApi/service/dao/feishu/dfeishuprojectdocumentcomment"
	"irisAdminApi/service/dao/feishu/dfeishuprojectdocumentreviewdetail"
	"irisAdminApi/service/dao/feishu/dfeishureviewconfirmation"
)

func GetFolderFileData() error {
	err := dfeishufilelist.DeleteAll()
	if err != nil {
		logging.ErrorLogger.Errorf("delete all feishu file list error:%s", err.Error())
		return err
	}
	err = updateFolderList(libs.Config.FeiShuDoc.FolderToken, "")
	if err != nil {
		logging.ErrorLogger.Errorf("update folder list error:%s", err.Error())
		return err
	}
	return nil
}

// 批量创建项目文档数据
func BatchCreateProjectDocument(projectName, tableID string) {
	projectDocuments, err := dfeishuprojectdocument.GetInsertDataByProjectName(projectName)
	if err != nil {
		logging.ErrorLogger.Errorf("getInsertDataByProjectName error:%s", err.Error())
		return
	}
	// 分批处理新增数据
	feishuProjectDocument := dfeishuprojectdocument.FeishuProjectDocument{}
	const batchSize = 500
	for i := 0; i < len(projectDocuments); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(projectDocuments) {
			endIndex = len(projectDocuments)
		}
		batchData := projectDocuments[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, projectDocument := range batchData {
				rec := map[string]interface{}{
					"项目":          projectDocument.ProjectName,
					"文档名称":        projectDocument.DocumentName,
					"需求":          projectDocument.Requirement,
					"工作包":         projectDocument.WorkPacketName,
					"文档评审状态":      projectDocument.DocumentReviewStatus,
					"评论总数":        projectDocument.DocumentCommentNum,
					"代码量":         projectDocument.TotalCodes,
					"document_id": projectDocument.PMSDocID,
					"doc_id":      projectDocument.ID,
				}
				if projectDocument.IsSubmit == 0 {
					rec["文档入库状态"] = "未入库"
				} else {
					rec["文档入库状态"] = "已入库"
				}
				if len(projectDocument.DocumentCategoryTitle) > 0 {
					rec["文档类型"] = projectDocument.DocumentCategoryTitle
				}
				if len(projectDocument.DepartmentName) > 0 {
					rec["文档负责人所在专业组"] = projectDocument.DepartmentName
				}
				if projectDocument.DocumentCreatedTime > 0 {
					rec["文档创建时间"] = projectDocument.DocumentCreatedTime
				}
				if len(projectDocument.PacketManagerID) > 0 {
					rec["PSDPE"] = []interface{}{map[string]interface{}{"id": projectDocument.PacketManagerID}}
				}
				if len(projectDocument.PgttmUserID) > 0 {
					rec["PGTTM"] = []interface{}{map[string]interface{}{"id": projectDocument.PgttmUserID}}
				}
				if len(projectDocument.DocumentOwnerID) > 0 {
					rec["文档负责人"] = []interface{}{map[string]interface{}{"id": projectDocument.DocumentOwnerID}}
				}
				if len(projectDocument.DocumentUrl) > 0 {
					rec["链接"] = map[string]interface{}{"link": projectDocument.DocumentUrl}
				}
				records = append(records, rec)
			}
			tableRecordResp, err := BatchCreate(tableID, libs.Config.FeiShuDoc.BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("batchCreate error:%s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				for _, record := range tableRecordResp.Data.Records {
					// 更新RecordId
					documentID, ok := record.Fields["doc_id"].(float64) // 首先确保doc_id是一个float64
					if !ok {
						logging.ErrorLogger.Errorf("doc_id is not a int")
						break // 或者处理错误
					}
					feishuProjectDocument.Update(uint(documentID), map[string]interface{}{"record_id": record.RecordId})
				}
			}
			time.Sleep(150 * time.Millisecond)
		}
	}
}

// 批量更新项目文档数据
func BatchUpdateProjectDocument(projectName, tableID string) {
	projectDocuments, err := dfeishuprojectdocument.GetUpdateDataByProjectName(projectName)
	if err != nil {
		logging.ErrorLogger.Errorf("getInsertDataByProjectName error:%s", err.Error())
		return
	}
	// 分批处理新增数据
	const batchSize = 200
	for i := 0; i < len(projectDocuments); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(projectDocuments) {
			endIndex = len(projectDocuments)
		}
		batchData := projectDocuments[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将更新数据按照批次插入飞书数据表,每批次500条
			for _, projectDocument := range batchData {
				rec := map[string]interface{}{
					"项目":          projectDocument.ProjectName,
					"文档名称":        projectDocument.DocumentName,
					"需求":          projectDocument.Requirement,
					"工作包":         projectDocument.WorkPacketName,
					"评论总数":        projectDocument.DocumentCommentNum,
					"代码量":         projectDocument.TotalCodes,
					"document_id": projectDocument.PMSDocID,
					"record_id":   projectDocument.RecordID,
					"doc_id":      projectDocument.ID,
				}
				if projectDocument.IsSubmit == 0 {
					rec["文档入库状态"] = "未入库"
				} else {
					rec["文档入库状态"] = "已入库"
				}
				if projectDocument.DocumentCreatedTime > 0 {
					rec["文档创建时间"] = int(projectDocument.DocumentCreatedTime)
				}
				if len(projectDocument.DocumentCategoryTitle) > 0 {
					rec["文档类型"] = projectDocument.DocumentCategoryTitle
				}
				if len(projectDocument.DepartmentName) > 0 {
					rec["文档负责人所在专业组"] = projectDocument.DepartmentName
				}
				if len(projectDocument.PacketManagerID) > 0 {
					rec["PSDPE"] = []interface{}{map[string]interface{}{"id": projectDocument.PacketManagerID}}
				}
				if len(projectDocument.PgttmUserID) > 0 {
					rec["PGTTM"] = []interface{}{map[string]interface{}{"id": projectDocument.PgttmUserID}}
				}

				if len(projectDocument.PstlUserID) > 0 {
					// 通过|符号分割
					pstlUserIDs := strings.Split(projectDocument.PstlUserID, "|")
					// pstl人员变量循环添加
					PSTLList := []interface{}{}
					for _, pstlUserID := range pstlUserIDs {
						PSTLList = append(PSTLList, map[string]interface{}{"id": pstlUserID})
					}
					rec["PSTL"] = PSTLList
				}

				if len(projectDocument.DocumentOwnerID) > 0 {
					rec["文档负责人"] = []interface{}{map[string]interface{}{"id": projectDocument.DocumentOwnerID}}
				}
				if len(projectDocument.DocumentUrl) > 0 {
					rec["链接"] = map[string]interface{}{"link": projectDocument.DocumentUrl}
				}
				records = append(records, rec)
			}
			tableRecordResp, err := BatchUpdate(tableID, libs.Config.FeiShuDoc.BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("batchUpdate error:%s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
			time.Sleep(150 * time.Millisecond)
		}
	}
}

// 删除失效的文档数据
func BatchDeleteProjectDocument(projectName, tableID string) {
	projectDocuments, err := dfeishuprojectdocument.GetPMSProjectDocumentDisabledData(projectName)
	if err != nil {
		logging.ErrorLogger.Errorf("getInsertDataByProjectName error:%s", err.Error())
		return
	}

	for _, projectDocument := range projectDocuments {
		if projectDocument.RecordID == "" {
			continue
		}
		err := BatchDelete(tableID, libs.Config.FeiShuDoc.BiAppToken, []string{projectDocument.RecordID})
		if err != nil {
			logging.ErrorLogger.Errorf("batchDelete error:%s", err.Error())
			continue
		}
		// 数据库表中也删除
		projectdocument := dfeishuprojectdocument.FeishuProjectDocument{}
		err = projectdocument.Delete(projectDocument.ID)
		if err != nil {
			logging.ErrorLogger.Errorf("delete projectdocument error:%s", err.Error())
			continue
		}
		time.Sleep(150 * time.Millisecond)
	}
}

// 更新获取文件夹下的清单
func updateFolderList(folderToken, pageToken string) error {
	ListFileResp, err := GetFolderList(200, folderToken, pageToken)
	if err != nil {
		logging.ErrorLogger.Errorf("Failed to get folder list: %s", err.Error())
		return err
	}
	feishuFiles := []map[string]interface{}{}
	having := []string{}
	// ext:=[]string{"docx","doc","sheet","file"}
	for _, file := range ListFileResp.Data.Files {
		if !libs.InArrayS(having, *file.Token) {
			feishuFiles = append(feishuFiles, map[string]interface{}{
				"file_created_time":  *file.CreatedTime,
				"file_modified_time": *file.ModifiedTime,
				"parent_token":       *file.ParentToken,
				"token":              *file.Token,
				"type":               *file.Type,
				"url":                *file.Url,
				"name":               *file.Name,
				"owner_id":           *file.OwnerId,
			})
			if *file.Type == "folder" {
				updateFolderList(*file.Token, "")
			}
			having = append(having, *file.Token)
		}
	}
	dfeishufilelist.BatchCreate(feishuFiles)
	time.Sleep(100 * time.Millisecond)
	if *ListFileResp.Data.HasMore {
		updateFolderList(folderToken, *ListFileResp.Data.NextPageToken)
	}
	return nil
}

func MatchDocumentInfo(projectName string) error {
	fileData, err := dfeishufilelist.GetFolderFileDataByProjectName(projectName)
	if err != nil {
		logging.ErrorLogger.Errorf("Failed to get folder list: %s", err.Error())
		return err
	}
	if len(fileData) == 0 {
		return nil
	}
	for _, file := range fileData {
		updateObj := map[string]interface{}{
			"FileToken":           file.Token,
			"FileType":            file.Type,
			"DocumentCreatedTime": file.FileCreatedTime * 1000,
			"DocumentUrl":         file.Url,
		}
		// 先判断文件是否存在
		documentData, err := dfeishuprojectdocument.GetDocumentDataByFileName(projectName, file.Name)
		if err != nil {
			continue
		}
		if documentData.ID > 0 {
			// 存在则更新
			err = dfeishuprojectdocument.UpdateDocumentDataByID(documentData.ID, updateObj)
			if err != nil {
				continue
			}
		}
	}
	return nil
}

func UpdateDocumentReviewStatus(projectName, pageToken string) error {
	tableName := "文档表"
	feishubitable := dfeishubitablelist.Response{}
	err := feishubitable.FindByNameAndProjectName(tableName, projectName)
	if err != nil {
		logging.ErrorLogger.Errorf("findByNameAndProjectName table error:%s", err.Error())
		return err
	}
	tableID := feishubitable.TableID
	tableRecords, err := GetTableRecord(tableID, libs.Config.FeiShuDoc.BiAppToken, pageToken, 500, []string{"期望评审结束时间", "文档评审状态", "会议链接", "必选评委", "可选评委"})
	if err != nil {
		logging.ErrorLogger.Errorf("getTableRecord error:%s", err.Error())
		return err
	}
	if len(tableRecords.Data.Items) > 0 {
		for _, record := range tableRecords.Data.Items {
			// 根据RecordId查找
			documentRecord, err := dfeishuprojectdocument.GetDocumentReviewByRecordID(*record.RecordId)
			if err != nil {
				logging.ErrorLogger.Errorf("GetDocumentReviewByRecordID error:%s", err.Error())
				return err
			}
			updateObj := map[string]interface{}{}
			var meetingUrl string
			expectedReviewEndTime := 0
			tableReviewStatus := record.Fields["文档评审状态"].(string)
			if record.Fields["会议链接"] != nil {
				meetingData, err := ExtractRecordTextFromContent(record, "会议链接")
				if err != nil {
					logging.ErrorLogger.Errorf("ExtractRecordTextFromContent error:%s", err.Error())
					return err
				}
				fmt.Println(meetingData)
				if meetingData["link"] != nil {
					meetingUrl = meetingData["link"].(string)
					meetingNO, err := ExtractMeetingID(meetingUrl) // 提取会议号
					if err != nil {
						logging.ErrorLogger.Errorf("ExtractMeetingID error:%s", err.Error())
						goto nextStep
					}
					// 根据会议号获取会议ID
					meetingID, err := GetMeetingIDByMeetingNo(meetingNO)
					if err != nil {
						logging.ErrorLogger.Errorf("GetMeetingIDByMeetingNo error:%s", err.Error())
						goto nextStep
					}
					// 根据会议ID获取会议记录
					meetingRecord, err := GetMeetingRecordByMeetingID(meetingID)
					if err != nil {
						logging.ErrorLogger.Errorf("GetMeetingRecordByMeetingID error:%s", err.Error())
						goto nextStep
					}
					// 保存会议数据
					updateObj["MeetingUrl"] = meetingUrl
					updateObj["MeetingNO"] = meetingNO
					updateObj["MeetingID"] = meetingID
					updateObj["MeetingTopic"] = *meetingRecord.Data.Meeting.Topic
					updateObj["MeetingStartTime"] = *meetingRecord.Data.Meeting.StartTime
					updateObj["MeetingEndTime"] = *meetingRecord.Data.Meeting.EndTime

				} else {
					if meetingData["text"].(string) == "无需会议评审" {
						meetingUrl = meetingData["text"].(string)
						updateObj["MeetingUrl"] = meetingUrl
					}
				}
			}
		nextStep:
			if record.Fields["期望评审结束时间"] != nil {
				expectedReviewEndTime = int(record.Fields["期望评审结束时间"].(float64))
				updateObj["ExpectedReviewEndTime"] = expectedReviewEndTime
				// str := strconv.FormatFloat(endtime, 'f', -1, 64)
			}
			var requiredJudgeIDs, optionalJudgeIDs []string
			if record.Fields["必选评委"] != nil {
				requiredJudgeIDs, err = ExtractJudgeIDs(record.Fields["必选评委"])
				if err != nil {
					fmt.Println(err)
				}
				if len(requiredJudgeIDs) > 0 {
					updateObj["RequiredJudge"] = strings.Join(requiredJudgeIDs, "|")
				}
			}
			if record.Fields["可选评委"] != nil {
				optionalJudgeIDs, err = ExtractJudgeIDs(record.Fields["可选评委"])
				if err != nil {
					fmt.Println(err)
					continue
				}
				if len(optionalJudgeIDs) > 0 {
					updateObj["OptionalJudge"] = strings.Join(optionalJudgeIDs, "|")
				}
			}
			if len(requiredJudgeIDs) > 0 {
				// 授权评委文档阅读权限
				GrantJudegeViewPermission(documentRecord.FileToken, documentRecord.FileType, requiredJudgeIDs, optionalJudgeIDs)
			}
			// 未确认=》评审开始:验证文档链接+必选评委 必须要有数据，不符合重置为待确认=>通知评委开始评审文档，记录评审开始时间
			if documentRecord.DocumentReviewStatus == "未确认" && tableReviewStatus == "评审开始" {
				// 验证文档链接
				rec := map[string]interface{}{
					"文档评审状态": "未确认",
				}
				if len(documentRecord.DocumentUrl) > 0 {
					// 验证是否填写期望评审结束时间
					if expectedReviewEndTime == 0 {
						// 提醒文档作者填写期望评审结束时间
						messageStr := "你负责的文档《[" + documentRecord.DocumentName + "](" + documentRecord.DocumentUrl + ")》,因未填写期望评审结束时间,申请评审开始失败,状态已被重置,如需申请评审开始，请访问[文档表](https://ruijie.feishu.cn/base/KZQCbsWefa5e3MsamMccOYYPnMr?table=tblNMM7Kay3EFL2d),调整评审状态并添加必选评委"
						SendMessageToUser(documentRecord.PacketManagerID, messageStr)
						continue
					}
					// 验证文档评审人
					if len(requiredJudgeIDs) > 0 {
						updateObj["DocumentReviewStatus"] = record.Fields["文档评审状态"].(string)
						rec["文档评审状态"] = record.Fields["文档评审状态"].(string)
						// 消息通知评委-》开始评审
						// 将毫秒级时间戳转换为时间
						tm := time.Unix(0, int64(expectedReviewEndTime)*int64(time.Millisecond))
						// 将时间转换为字符串
						endTimeStr := tm.Format("2006-01-02 15:04:05.999")
						SendMessageToJudge(documentRecord, requiredJudgeIDs, optionalJudgeIDs, endTimeStr)
						messageStr := "你负责的文档《[" + documentRecord.DocumentName + "](" + documentRecord.DocumentUrl + ")》,文档评审已开始，已通知相关评委开始评审！"
						SendMessageToUser(documentRecord.PacketManagerID, messageStr)
						// 记录评审时间
						updateObj["JudgeStartTime"] = time.Now().Unix()
					} else {
						// 提醒负责人:评审状态变更失败
						messageStr := "你负责的文档《[" + documentRecord.DocumentName + "](" + documentRecord.DocumentUrl + ")》,因未选择必选评委,申请评审开始失败,状态已被重置,如需申请评审开始，请访问[文档表](https://ruijie.feishu.cn/base/KZQCbsWefa5e3MsamMccOYYPnMr?table=tblNMM7Kay3EFL2d),调整评审状态并添加必选评委"
						SendMessageToUser(documentRecord.PacketManagerID, messageStr)
					}
				}
				// 更新该数据到多维表格
				UpdateTableRecordByID(tableID, libs.Config.FeiShuDoc.BiAppToken, *record.RecordId, rec)
			}
			// 评审开始=》评审结束: 验证文档评论是否都已关闭=>创建评审确认表数据+发送评委评审信息+通知作者等待评委确认
			if documentRecord.DocumentReviewStatus == "评审开始" && tableReviewStatus == "评审结束" {
				rec := map[string]interface{}{
					"文档评审状态": "评审开始",
				}
				// 获取该篇文档的评论信息数据
				// SendCardMessageToJudge(documentRecord, requiredJudgeIDs)
				// 验证文档评论是否都已解决
				documentcomment, err := dfeishuprojectdocumentcomment.GetSolvedCommentByPMSDocID(documentRecord.PMSDocID, 0)
				if err != nil {
					logging.ErrorLogger.Errorf("GetSolvedCommentByPMSDocID err: %s", err.Error())
					goto endStep
				}
				// 判断待解决评论
				if len(documentcomment) > 0 {
					// 提醒负责人:存在未解决的评论,重置为评审开始，发送消息通知作者
					messageStr := "你负责的文档《[" + documentRecord.DocumentName + "](" + documentRecord.DocumentUrl + ")》,存在未解决的评审内容,如需变更为评审结束状态，请提醒相关评委进行关闭操作"
					SendMessageToUser(documentRecord.PacketManagerID, messageStr)
					goto endStep
				}
				// 判断会议链接是否填写
				if len(meetingUrl) == 0 {
					// 提醒负责人:评审状态变更失败
					messageStr := "你负责的文档《[" + documentRecord.DocumentName + "](" + documentRecord.DocumentUrl + ")》,因未填写会议链接(填写不正确),申请评审结束失败,状态已被重置,如需申请评审结束，请访问[文档表](https://ruijie.feishu.cn/base/KZQCbsWefa5e3MsamMccOYYPn)"
					SendMessageToUser(documentRecord.PacketManagerID, messageStr)
					goto endStep
				}
				// 创建评审结束确认数据
				if len(requiredJudgeIDs) > 0 {
					records := []map[string]interface{}{}
					if len(optionalJudgeIDs) > 0 {
						// 添加有评审意见的可选评委
						for _, optionalJudgeID := range optionalJudgeIDs {
							documentReviewData, err := dfeishuprojectdocumentreviewdetail.FindCommentByPMSDocIDAndJudgeID(documentRecord.PMSDocID, optionalJudgeID)
							if err != nil {
								logging.ErrorLogger.Errorf("FindByProjectNameAndDocID error:%s", err.Error())
								goto endStep
							}
							if documentReviewData.ID > 0 {
								requiredJudgeIDs = append(requiredJudgeIDs, optionalJudgeID)
							}
						}
					}
					for _, requiredJudgeID := range requiredJudgeIDs {
						reviewconfirmationData, err := dfeishureviewconfirmation.FindByProjectNameAndDocID(documentRecord.ProjectName, requiredJudgeID, documentRecord.PMSDocID)
						if err != nil {
							logging.ErrorLogger.Errorf("FindByProjectNameAndDocID error:%s", err.Error())
							goto endStep
						}
						if reviewconfirmationData.ID == 0 {
							record := map[string]interface{}{}
							// 创建评审结束确认数据
							newreviewconfirmationData := dfeishureviewconfirmation.Response{}
							newreviewconfirmationData.ProjectName = documentRecord.ProjectName
							newreviewconfirmationData.DocumentName = documentRecord.DocumentName
							newreviewconfirmationData.PmsDocID = uint64(documentRecord.PMSDocID)
							newreviewconfirmationData.DocID = uint64(documentRecord.ID)
							newreviewconfirmationData.JudgeID = requiredJudgeID
							newreviewconfirmationData.DocumentUrl = documentRecord.DocumentUrl
							newreviewconfirmationData.DocumentOwnerID = documentRecord.DocumentOwnerID
							dfeishureviewconfirmation := dfeishureviewconfirmation.Response{}
							if err := dfeishureviewconfirmation.CreateV3(&newreviewconfirmationData); err != nil {
								logging.ErrorLogger.Errorf("CreateV3 error:%s", err.Error())
								goto endStep
							}
							record["文档名称"] = documentRecord.DocumentName
							record["document_id"] = fmt.Sprintf("%d", documentRecord.PMSDocID)
							record["confirmation_id"] = fmt.Sprintf("%d", newreviewconfirmationData.ID)
							record["项目"] = documentRecord.ProjectName
							record["评委"] = []interface{}{map[string]interface{}{"id": requiredJudgeID}}
							record["文档负责人"] = []interface{}{map[string]interface{}{"id": documentRecord.DocumentOwnerID}}
							if len(documentRecord.DocumentUrl) > 0 {
								record["文档链接"] = map[string]interface{}{"link": documentRecord.DocumentUrl}
							}
							records = append(records, record)
						}
					}
					if len(records) > 0 {
						tableRecordResp, err := BatchCreate("tblDLgWAAPZ2wkQh", libs.Config.FeiShuDoc.BiAppToken, records)
						if err != nil {
							logging.ErrorLogger.Errorf("创建评审结束确认数据失败 error:%s", err.Error())
							goto endStep
						}
						if len(tableRecordResp.Data.Records) > 0 {
							for _, record := range tableRecordResp.Data.Records {
								// 更新RecordId
								dfeishureviewconfirmation := dfeishureviewconfirmation.Response{}
								confirmationIDStr, ok := record.Fields["confirmation_id"].(string) // 首先确保document_id是一个字符串
								if !ok {
									logging.ErrorLogger.Errorf("confirmation_id is not a string")
									goto endStep
								}
								confirmationID, err := strconv.ParseUint(confirmationIDStr, 10, 32) // 转换为无符号整数
								if err != nil {
									logging.ErrorLogger.Errorf("Failed to parse confirmation_id: %s", err.Error())
									goto endStep
								}
								dfeishureviewconfirmation.Update(uint(confirmationID), map[string]interface{}{"record_id": record.RecordId})
							}
						}
						// 发送消息给评委确认
						SendCardMessageToJudge(documentRecord, requiredJudgeIDs)
						// 发送消息给作者
						SendMessageToUser(documentRecord.DocumentOwnerID, "评审结束申请已确认，等待评委确认后，系统会自动变更状态")
					} else {
						// 发送消息给作者
						SendMessageToUser(documentRecord.DocumentOwnerID, "评审结束申请已确认，等待评委确认后，系统会自动变更状态")
					}
				}
			endStep:
				// 更新该数据到多维表格
				UpdateTableRecordByID(tableID, libs.Config.FeiShuDoc.BiAppToken, *record.RecordId, rec)
			}
			// 未确认=》评审结束: 重置为未确认
			if documentRecord.DocumentReviewStatus == "未确认" && tableReviewStatus == "评审结束" {
				rec := map[string]interface{}{
					"文档评审状态": "未确认",
				}
				// 更新该数据到多维表格
				UpdateTableRecordByID(tableID, libs.Config.FeiShuDoc.BiAppToken, *record.RecordId, rec)
			}
			// 评审开始=》未确认: 重置为未确认
			if documentRecord.DocumentReviewStatus == "评审开始" && tableReviewStatus == "未确认" {
				rec := map[string]interface{}{
					"文档评审状态": "未确认",
				}
				updateObj["DocumentReviewStatus"] = record.Fields["文档评审状态"].(string)
				// 通知评委取消评审
				// 更新该数据到多维表格
				UpdateTableRecordByID(tableID, libs.Config.FeiShuDoc.BiAppToken, *record.RecordId, rec)
			}

			// 评审结束=》未确认: 重置为评审结束
			if documentRecord.DocumentReviewStatus == "评审结束" && tableReviewStatus == "未确认" {
				rec := map[string]interface{}{
					"文档评审状态": "评审结束",
				}
				// 更新该数据到多维表格
				UpdateTableRecordByID(tableID, libs.Config.FeiShuDoc.BiAppToken, *record.RecordId, rec)
			}

			// 评审结束=》评审开始：重置为评审结束
			if documentRecord.DocumentReviewStatus == "评审结束" && tableReviewStatus == "评审开始" {
				rec := map[string]interface{}{
					"文档评审状态": "评审开始",
				}
				//“评审结束”到“评审开始”的处理逻辑
				//重置评审结束确认数据
				if err := dfeishureviewconfirmation.UpdateByProjectNameAndDocID(documentRecord.ProjectName, documentRecord.PMSDocID, map[string]interface{}{"confirmation": "未确认"}); err != nil {
					logging.ErrorLogger.Errorf("UpdateByProjectNameAndDocID error:%s", err.Error())
				}
				// 获取评审结束确认数据
				confirmationData, err := dfeishureviewconfirmation.GetConfirmationByProjectNameAndDocID(documentRecord.ProjectName, documentRecord.PMSDocID)
				if err != nil {
					logging.ErrorLogger.Errorf("GetConfirmationByProjectNameAndDocID error:%s", err.Error())
				}
				for _, confirmation := range confirmationData {
					// 更新评审结束确认数据
					record2 := map[string]interface{}{}
					record2["评审结束状态确认"] = "未确认"
					UpdateTableRecordByID("tblDLgWAAPZ2wkQh", libs.Config.FeiShuDoc.BiAppToken, confirmation.RecordID, record2)
				}
				// 更新该数据到多维表格
				UpdateTableRecordByID(tableID, libs.Config.FeiShuDoc.BiAppToken, *record.RecordId, rec)

			}
			dfeishuprojectdocument.UpdateByRecordID(*record.RecordId, updateObj)
		}
	}
	if *tableRecords.Data.HasMore {
		UpdateDocumentReviewStatus(projectName, *tableRecords.Data.PageToken)
	}
	return nil
}

// 获取文档评论信息
func GetDocumentComment(projectName string) {
	// 获取评审中的文档数据
	reviewDocument, err := dfeishuprojectdocument.GetDocumentReviewByProjectName(projectName)
	if err != nil {
		logging.ErrorLogger.Errorf("GetDocumentReviewByProjectName err:%s", err.Error())
		return
	}
	if len(reviewDocument) > 0 {
		for _, v := range reviewDocument {
			// 获取文档评论信息
			fileCommentResp, err := GetDocAllComments(v.FileToken, v.FileType, 100)
			if err != nil {
				logging.ErrorLogger.Errorf("GetDocAllComments err:%s", err.Error())
				return
			}
			fileComment := []map[string]interface{}{}
			if len(fileCommentResp.Data.Items) > 0 {
				for _, v1 := range fileCommentResp.Data.Items {
					// 获取评论回复信息
					replyContent, replyCommentType, PersonID := FormatReplies(v1.ReplyList, *v1.UserId)
					if PersonID == "" {
						PersonID = *v1.UserId
					}
					comment := map[string]interface{}{
						"PmsDocID":           v.PMSDocID,
						"ProjectName":        v.ProjectName,
						"DocumentName":       v.DocumentName,
						"FileToken":          v.FileToken,
						"FileType":           v.FileType,
						"CommentID":          v1.CommentId,
						"CommentCreatedTime": v1.CreateTime,
						"CommentUpdateTime":  v1.UpdateTime,
						"CommentUserID":      PersonID,
						"Quote":              v1.Quote,
						"CommentContent":     replyContent,
						"CommentType":        replyCommentType,
						"CreatedAt":          time.Now(),
						"UpdatedAt":          time.Now(),
						"IsSolved":           false,
						"SolvedTime":         0,
						"SolverUserID":       "",
					}
					// 验证评论是否被评论者解决
					if *v1.IsSolved {
						// 验证评论是否被评论者解决
						if *v1.SolverUserId == *v1.UserId {
							comment["IsSolved"] = v1.IsSolved
							comment["SolvedTime"] = v1.SolvedTime
							comment["SolverUserID"] = v1.SolverUserId
						} else {
							// 重置解决状态为未解决
							err := PatchFileComment(v.FileToken, *v1.CommentId, v.FileType, false)
							if err != nil {
								logging.ErrorLogger.Errorf("PatchFileComment err:%s", err.Error())
								continue
							}
							// 发送消息给解决者
							SendMessage(*v1.SolverUserId, "需要评审人员点击已解决，解决状态已重置，如问题已解决提醒评审人员点击已解决")
						}
					}
					// 验证评论详情表数据
					documentreviewdetail, err := dfeishuprojectdocumentreviewdetail.GetDocumentReviewCommentByCommentID(*v1.CommentId)
					if err != nil {
						logging.ErrorLogger.Errorf("GetDocumentReviewCommentByCommentID err:%s", err.Error())
						continue
					}
					if documentreviewdetail.ID > 0 {
						if documentreviewdetail.IsSolved && !*v1.IsSolved {
							comment["IsSolved"] = true
						}
						if replyCommentType == "缺陷" && documentreviewdetail.CommentType != "缺陷" {
							comment["CommentType"] = documentreviewdetail.CommentType
						}
					}
					// 查询评审详情情况
					fileComment = append(fileComment, comment)
				}
				// 创建评论数据
				dfeishuprojectdocumentcomment.CreateOrUpdateProjectDocumentCommentData(fileComment)
				// 更新文档评论数
				dfeishuprojectdocument.UpdateDocumentDataByProjectName(v.ProjectName, v.FileName, map[string]interface{}{
					"DocumentCommentNum": len(fileCommentResp.Data.Items),
				})
			}

		}
	}
}

func CreateDocumentCommentReviewdetail(projectName string) {
	// 创建文档评论详情数据
	err := dfeishuprojectdocumentreviewdetail.CreateOrUpdateProjectDocumentReviewdetailData(projectName)
	if err != nil {
		logging.ErrorLogger.Errorf("get ReviewdetailData error:%s", err.Error())
		return
	}
	// 查看是否存在该项目的文档评审详情表
	tableName := "评论详情表"
	feishubitable := dfeishubitablelist.Response{}
	err = feishubitable.FindByNameAndProjectName(tableName, projectName)
	if err != nil {
		logging.ErrorLogger.Errorf("findByNameAndProjectName table error:%s", err.Error())
		return
	}
	tableID := feishubitable.TableID
	if feishubitable.ID == 0 {
		// 创建飞书数据表评审详情表
		resp, err := CreateProjectDocumentReviewdetailTable(tableName, libs.Config.FeiShuDoc.BiAppToken)
		if err != nil {
			logging.ErrorLogger.Errorf("create table error:%s", err.Error())
			return
		}
		err = feishubitable.Create(map[string]interface{}{
			"name":              tableName,
			"table_id":          *resp.Data.TableId,
			"project_name":      projectName,
			"bitable_app_token": libs.Config.FeiShuDoc.BiAppToken,
		})
		tableID = *resp.Data.TableId
		if err != nil {
			logging.ErrorLogger.Errorf("create table error:%s", err.Error())
			return
		}
	}
	fmt.Print(tableID)
	// //同步数据
	BatchCreateProjectDocumentReviewdetail(projectName, tableID)
	// //批量更新数据
	BatchUpdateProjectDocumentReviewdetail(projectName, tableID)
}

// 批量创建评审详情数据
func BatchCreateProjectDocumentReviewdetail(projectName, tableID string) {
	documentReviewDetails, err := dfeishuprojectdocumentreviewdetail.GetInsertDataByProjectName(projectName)
	if err != nil {
		logging.ErrorLogger.Errorf("getInsertDataByProjectName error:%s", err.Error())
		return
	}
	// 分批处理新增数据
	feishuProjectDocumentReviewdetail := dfeishuprojectdocumentreviewdetail.FeishuProjectDocumentReviewdetail{}
	const batchSize = 500
	for i := 0; i < len(documentReviewDetails); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(documentReviewDetails) {
			endIndex = len(documentReviewDetails)
		}
		batchData := documentReviewDetails[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, reviewdetail := range batchData {
				rec := map[string]interface{}{
					"评审对象":        reviewdetail.DocumentName,
					"项目":          reviewdetail.ProjectName,
					"需求":          reviewdetail.Requirement,
					"工作包":         reviewdetail.WorkPacketName,
					"文档类型":        reviewdetail.DocumentCategoryTitle,
					"划词引用内容":      reviewdetail.Quote,
					"评论内容":        reviewdetail.CommentContent,
					"评委":          []interface{}{map[string]interface{}{"id": reviewdetail.CommentUserID}},
					"评论性质":        reviewdetail.CommentType,
					"评论创建时间":      reviewdetail.CommentCreatedTime * 1000,
					"评委所在专业组":     reviewdetail.CommentUserDepartmentName,
					"review_id":   fmt.Sprintf("%d", reviewdetail.ID),
					"document_id": fmt.Sprintf("%d", reviewdetail.PmsDocID),
				}
				if reviewdetail.IsSolved {
					rec["状态"] = "已解决"
				} else {
					rec["状态"] = "未解决"
				}
				if len(reviewdetail.DocumentOwnerID) > 0 {
					rec["文档负责人"] = []interface{}{map[string]interface{}{"id": reviewdetail.DocumentOwnerID}}
				}
				if len(reviewdetail.SolverUserID) > 0 {
					rec["解决人"] = []interface{}{map[string]interface{}{"id": reviewdetail.SolverUserID}}
				}
				if len(reviewdetail.DocumentUrl) > 0 {
					rec["文档链接"] = map[string]interface{}{"link": reviewdetail.DocumentUrl}
				}
				records = append(records, rec)
			}
			tableRecordResp, err := BatchCreate(tableID, libs.Config.FeiShuDoc.BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("batchCreate error:%s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				for _, record := range tableRecordResp.Data.Records {
					// 更新RecordId
					reviewIDStr, ok := record.Fields["review_id"].(string) // 首先确保review_id是一个字符串
					if !ok {
						logging.ErrorLogger.Errorf("review_id is not a string")
						continue // 或者处理错误
					}
					reviewID, err := strconv.ParseUint(reviewIDStr, 10, 32) // 转换为无符号整数
					if err != nil {
						logging.ErrorLogger.Errorf("Failed to parse review_id: %s", err.Error())
						continue // 或者处理错误
					}
					feishuProjectDocumentReviewdetail.Update(uint(reviewID), map[string]interface{}{"record_id": record.RecordId})
				}
			}
		}
	}
}

// 批量更新评审详情数据
func BatchUpdateProjectDocumentReviewdetail(projectName, tableID string) {
	documentReviewDetails, err := dfeishuprojectdocumentreviewdetail.GetUpdateDataByProjectName(projectName)
	if err != nil {
		logging.ErrorLogger.Errorf("getInsertDataByProjectName error:%s", err.Error())
		return
	}
	// 分批处理新增数据
	const batchSize = 500
	for i := 0; i < len(documentReviewDetails); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(documentReviewDetails) {
			endIndex = len(documentReviewDetails)
		}
		batchData := documentReviewDetails[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, reviewdetail := range batchData {
				rec := map[string]interface{}{
					"评审对象":        reviewdetail.DocumentName,
					"项目":          reviewdetail.ProjectName,
					"需求":          reviewdetail.Requirement,
					"工作包":         reviewdetail.WorkPacketName,
					"文档类型":        reviewdetail.DocumentCategoryTitle,
					"划词引用内容":      reviewdetail.Quote,
					"评论内容":        reviewdetail.CommentContent,
					"评委":          []interface{}{map[string]interface{}{"id": reviewdetail.CommentUserID}},
					"评论性质":        reviewdetail.CommentType,
					"评论创建时间":      reviewdetail.CommentCreatedTime * 1000,
					"评委所在专业组":     reviewdetail.CommentUserDepartmentName,
					"review_id":   fmt.Sprintf("%d", reviewdetail.ID),
					"record_id":   reviewdetail.RecordID,
					"document_id": fmt.Sprintf("%d", reviewdetail.PmsDocID),
				}
				if reviewdetail.IsSolved {
					rec["状态"] = "已解决"
				} else {
					rec["状态"] = "未解决"
				}
				if len(reviewdetail.DocumentOwnerID) > 0 {
					rec["文档负责人"] = []interface{}{map[string]interface{}{"id": reviewdetail.DocumentOwnerID}}
				}
				if len(reviewdetail.SolverUserID) > 0 {
					rec["解决人"] = []interface{}{map[string]interface{}{"id": reviewdetail.SolverUserID}}
				}
				if len(reviewdetail.DocumentUrl) > 0 {
					rec["文档链接"] = map[string]interface{}{"link": reviewdetail.DocumentUrl}
				}
				records = append(records, rec)
			}
			tableRecordResp, err := BatchUpdate(tableID, libs.Config.FeiShuDoc.BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("batchCreate error:%s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

// 批量创建项目文档数据
func BatchCreateProjectDocumentByType(projectName, tableID string) {
	// 获取项目文档数据
	projectDocuments, err := dfeishuprojectdocument.GetUpdateDataByProjectName(projectName)
	if err != nil {
		logging.ErrorLogger.Errorf("getInsertDataByProjectName error:%s", err.Error())
		return
	}

	// 获取项目对应的目录
	folderData, err := dfeishufilelist.GetFolderDataByProjectName(projectName)
	if err != nil {
		logging.ErrorLogger.Errorf("GetFolderDataByProjectName error:%s", err.Error())
		return
	}
	// 获取云端文件夹文件清单
	GetFolderFileData()

	// 创建文档模板类型对应数据
	templates, err := dfeishudocumenttemplate.GetAll()
	if err != nil {
		logging.ErrorLogger.Errorf("getDataByProjectName error:%s", err.Error())
		return
	}
	// 提取模板类型DocumentCategoryTitle 对应的模板数据
	templateMap := make(map[string]dfeishudocumenttemplate.Response)
	for _, template := range templates {
		templateMap[template.DocumentCategoryTitle] = *template
	}

	// 创建文档
	updateCount := 0
	for _, document := range projectDocuments {
		if document.DocumentUrl == "" && len(document.PacketManagerID) > 0 && document.DocumentCategoryTitle != "" && document.FileName != "" {
			// 查找匹配的模板文档
			templateData := templateMap[document.DocumentCategoryTitle]
			if templateData.ID == 0 {
				continue
			}

			// 复制模板到指定目录
			fileName := document.FileName
			if document.DocumentCategoryTitle == ".yang" && (document.DocumentType == "doc" || document.DocumentType == "docx") {
				fileName = document.DocumentName
				templateData = templateMap["yangdoc"]
			}
			// 使用原始文件
			if document.DocumentCategoryTitle == "CREF" {
				fileName = document.DocumentName
			}
			// 验证模板文件是否存在
			file, err := dfeishufilelist.GetDocumentDataByFileName(folderData.Token, fileName)
			if err != nil {
				continue
			}
			updateObj := map[string]interface{}{}
			if file.ID > 0 {
				updateObj["FileToken"] = file.Token
				updateObj["FileType"] = file.Type
				updateObj["DocumentCreatedTime"] = file.FileCreatedTime * 1000
				updateObj["DocumentUrl"] = file.Url
				// 更新文档信息--本地信息
				err = dfeishuprojectdocument.UpdateDocumentDataByID(document.ID, updateObj)
				if err != nil {
					logging.ErrorLogger.Errorf("UpdateDocumentDataByID error:%s", err.Error())
					continue
				}
				// 更新文档信息权限
				_, err = GrantPermission(file.Token, file.Type, document.DocumentOwnerID, "full_access")
				if err != nil {
					logging.ErrorLogger.Errorf("updateFilePermission error:%s", err.Error())
					continue
				}
			} else {
				// 复制文档
				copyResp, err := CopyFeishuFile(templateData.Token, fileName, templateData.Type, folderData.Token)
				if err != nil {
					logging.ErrorLogger.Errorf("copyFeishuFile error:%s", err.Error())
					continue
				}
				updateObj["FileToken"] = *copyResp.Data.File.Token
				updateObj["FileType"] = *copyResp.Data.File.Type
				updateObj["DocumentCreatedTime"] = time.Now().UnixNano() / 1e6
				updateObj["DocumentUrl"] = *copyResp.Data.File.Url
				// 更新文档信息--本地信息
				err = dfeishuprojectdocument.UpdateDocumentDataByID(document.ID, updateObj)
				if err != nil {
					logging.ErrorLogger.Errorf("UpdateDocumentDataByID error:%s", err.Error())
					continue
				}
				// 更新文档信息权限
				_, err = GrantPermission(*copyResp.Data.File.Token, *copyResp.Data.File.Type, document.DocumentOwnerID, "full_access")
				if err != nil {
					logging.ErrorLogger.Errorf("updateFilePermission error:%s", err.Error())
					continue
				}
				// 等待0.3秒时间
				updateCount++
				time.Sleep(300 * time.Millisecond)
			}
			// 同步信息到飞书文档
			rec := map[string]interface{}{
				"文档创建时间": updateObj["DocumentCreatedTime"],
				"链接":     map[string]interface{}{"link": updateObj["DocumentUrl"]},
			}
			// 更新该数据到多维表格
			UpdateTableRecordByID(tableID, libs.Config.FeiShuDoc.BiAppToken, document.RecordID, rec)
			// 消息通知文档作者
			messageStr := "您好，您在项目:" + projectName + "中负责的文档《[" + fileName + "](" + updateObj["DocumentUrl"].(string) + ")》,已创建完成,如需查看更多信息（若未参加试点，可忽略），请访问[文档表](https://ruijie.feishu.cn/base/KZQCbsWefa5e3MsamMccOYYPn)"
			SendMessageToUser(document.PacketManagerID, messageStr)
		}
	}
}

// 同步文档---评委数据
func SyncFeiShuProjectDocumentJudge() {
	tableID := "tbluRxMVdCqlMUwO"
	// 批量删除线上数据
	DeleteTableRecordData(tableID, []string{"项目"})
	// 获取本地数据
	judgeData, err := dfeishuprojectdocument.GetDocumentAllJudge()
	if err != nil {
		logging.ErrorLogger.Errorf("GetDocumentAllJudge error:%s", err.Error())
		return
	}
	// 批量同步到线上
	// 分批处理新增数据
	const batchSize = 500
	for i := 0; i < len(judgeData); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(judgeData) {
			endIndex = len(judgeData)
		}
		batchData := judgeData[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, reviewdetail := range batchData {
				rec := map[string]interface{}{
					"项目": reviewdetail.ProjectName,
					"评委": []interface{}{map[string]interface{}{"id": reviewdetail.OpenID}},
				}
				records = append(records, rec)
			}
			tableRecordResp, err := BatchCreate(tableID, libs.Config.FeiShuDoc.BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("DocumentJudge batchCreate error:%s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

// 同步文档--评委文档数据
func SyncFeiShuToDocumentAndJudge() {
	tableID := "tbluHuC9celu1RG1"
	// 批量删除线上数据
	DeleteTableRecordData(tableID, []string{"项目"})
	// 获取本地数据
	judgeData, err := dfeishuprojectdocument.GetDocumentAllJudgeAndDOC()
	if err != nil {
		logging.ErrorLogger.Errorf("GetDocumentAllJudgeAndDOC error:%s", err.Error())
		return
	}
	// 批量同步到线上
	// 分批处理新增数据
	const batchSize = 500
	for i := 0; i < len(judgeData); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(judgeData) {
			endIndex = len(judgeData)
		}
		batchData := judgeData[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, reviewdetail := range batchData {
				rec := map[string]interface{}{
					"项目":   reviewdetail.ProjectName,
					"文档名称": reviewdetail.DocumentName,
					"评委":   []interface{}{map[string]interface{}{"id": reviewdetail.OpenID}},
				}
				records = append(records, rec)
			}
			tableRecordResp, err := BatchCreate(tableID, libs.Config.FeiShuDoc.BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("DocumentJudge batchCreate error:%s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

func SyncFeiShuProjectDocumentWorkPackage() {
	tableID := "tbljMmqgNoForqCm"
	// 批量删除线上数据
	DeleteTableRecordData(tableID, []string{"项目"})
	// 获取本地数据
	workPacketData, err := dfeishuprojectdocument.GetDocumentWorkPacket()
	if err != nil {
		logging.ErrorLogger.Errorf("GetDocumentWorkPacket error:%s", err.Error())
		return
	}
	// 批量同步到线上
	// 分批处理新增数据
	const batchSize = 500
	for i := 0; i < len(workPacketData); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(workPacketData) {
			endIndex = len(workPacketData)
		}
		batchData := workPacketData[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, reviewdetail := range batchData {
				rec := map[string]interface{}{
					"工作包": reviewdetail.WorkPacketName,
					"项目":  reviewdetail.ProjectName,
					"代码量": reviewdetail.TotalCodes,
				}
				if len(reviewdetail.PacketManagerID) > 0 {
					rec["工作包负责人"] = []interface{}{map[string]interface{}{"id": reviewdetail.PacketManagerID}}
				}
				records = append(records, rec)
			}
			tableRecordResp, err := BatchCreate(tableID, libs.Config.FeiShuDoc.BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("DocumentWorkPacket batchCreate error:%s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

func SyncFeiShuProjectDocumentTypeWorkPackage() {
	tableID := "tblZ6VVJSd9i1zEp"
	// 批量删除线上数据
	DeleteTableRecordData(tableID, []string{"项目"})
	// 获取本地数据
	workPacketData, err := dfeishuprojectdocument.GetDocumentTypeWorkPacket()
	if err != nil {
		logging.ErrorLogger.Errorf("GetDocumentTypeWorkPacket error:%s", err.Error())
		return
	}
	// 批量同步到线上
	// 分批处理新增数据
	const batchSize = 500
	for i := 0; i < len(workPacketData); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(workPacketData) {
			endIndex = len(workPacketData)
		}
		batchData := workPacketData[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, reviewdetail := range batchData {
				rec := map[string]interface{}{
					"工作包":  reviewdetail.WorkPacketName,
					"项目":   reviewdetail.ProjectName,
					"文档类型": reviewdetail.DocumentCategoryTitle,
				}
				records = append(records, rec)
			}
			tableRecordResp, err := BatchCreate(tableID, libs.Config.FeiShuDoc.BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("GetDocumentTypeWorkPacket batchCreate error:%s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

func SyncFeiShuProjectDocumentEndReview() {
	// 批量更新评审结束状态确认
	tableID := "tblDLgWAAPZ2wkQh"
	GetDocumentReviewConfirmation(tableID, "")
	// 获取飞书评审结束确认数据
	// Dfeishureviewconfirmation := dfeishureviewconfirmation.Response{}
	confirmationdData, err := dfeishureviewconfirmation.GetDocumentAllConfirmation()
	if err != nil {
		logging.ErrorLogger.Errorf("GetDocumentAllConfirmation error:%s", err.Error())
		return
	}
	if len(confirmationdData) == 0 {
		return
	}
	for _, confirmation := range confirmationdData {
		// 查询文档数据  若是评审开始状态更新为评审结束
		document, err := dfeishuprojectdocument.GetDocumentByPMSDocID(confirmation.PmsDocID)
		if err != nil {
			logging.ErrorLogger.Errorf("GetDocumentByPMSDocID error:%s", err.Error())
			return
		}
		if document.DocumentReviewStatus == "评审开始" {
			// 验证文档评论是否都已解决
			// documentcomment, err := dfeishuprojectdocumentcomment.GetSolvedCommentByPMSDocID(uint(confirmation.PmsDocID), 0)
			// if err != nil {
			// 	logging.ErrorLogger.Errorf("GetSolvedCommentByPMSDocID err: %s", err.Error())
			// 	return
			// }
			// fmt.Println(documentcomment)
			// if len(documentcomment) > 0 {
			// 	//提醒负责人:存在未解决的评论,重置为评审开始，发送消息通知作者
			// 	// messageStr := "你负责的文档《[" + document.DocumentName + "](" + document.DocumentUrl + ")》,存在未解决的评审内容,请提醒相关评委进行关闭操作"
			// 	// SendMessageToUser(document.PacketManagerID, messageStr)
			// 	return
			// }
			// 文档作者权限回收
			err = RevokeDocumentPermissions(document.FileToken, document.FileType)
			if err != nil {
				logging.ErrorLogger.Errorf("updateFilePermission error:%s", err.Error())
			}
			// 获取该文档的评审详情数据下的评论数据
			comments, err := dfeishuprojectdocumentreviewdetail.GetDocumentReviewCommentByPMSDocID(confirmation.PmsDocID)
			if err != nil {
				logging.ErrorLogger.Errorf("GetDocumentReviewCommentByPMSDocID error:%s", err.Error())
				continue
			}
			if document.MeetingNO != "" {
				for _, comment := range comments {
					// 更新会议信息数据以及评审归属状态(会前、会中、会后)
					commentRec := map[string]interface{}{
						"会议主题":   document.MeetingTopic,
						"会议ID":   document.MeetingNO,
						"会议创建时间": document.MeetingStartTime * 1000,
						"会议结束时间": document.MeetingEndTime * 1000,
					}
					if document.MeetingStartTime > comment.CommentCreatedTime {
						commentRec["评审归属类型"] = "会前评审"
					}
					if comment.CommentCreatedTime > document.MeetingEndTime {
						commentRec["评审归属类型"] = "会后评审"
					}
					if comment.CommentCreatedTime > document.MeetingStartTime && comment.CommentCreatedTime < document.MeetingEndTime {
						commentRec["评审归属类型"] = "会中评审"
					}
					fmt.Println(comment.RecordID, commentRec)

					// 更新评审详情数据
					updateObj := map[string]interface{}{
						"MeetingTopic":      document.MeetingTopic,
						"MeetingNO":         document.MeetingNO,
						"MeetingStartTime":  document.MeetingStartTime,
						"MeetingEndTime":    document.MeetingEndTime,
						"MeetingReviewType": commentRec["评审归属类型"],
					}
					err := dfeishuprojectdocumentreviewdetail.UpdateByID(comment.ID, updateObj)
					if err != nil {
						logging.ErrorLogger.Errorf("UpdateByIDerror:%s", err.Error())
					}
					// 更新该数据到多维表格--评审详细
					UpdateTableRecordByID("tblDVIDT3EOXHP7z", libs.Config.FeiShuDoc.BiAppToken, comment.RecordID, commentRec)

				}
			}
			// 更新该数据到多维表格--文档表
			rec := map[string]interface{}{
				"文档评审状态": "评审结束",
			}
			err = dfeishuprojectdocument.UpdateByPMSDocID(document.PMSDocID, map[string]interface{}{"DocumentReviewStatus": "评审结束"})
			if err != nil {
				logging.ErrorLogger.Errorf("UpdateByPMSDocID error:%s", err.Error())
				return
			}
			UpdateTableRecordByID("tblNMM7Kay3EFL2d", libs.Config.FeiShuDoc.BiAppToken, document.RecordID, rec)

			// 通知文档作者文档评审已结束
			messageStr := "你负责的文档《[" + document.DocumentName + "](" + document.DocumentUrl + ")》,评委评审结束已结束，可以访问[文档表](https://ruijie.feishu.cn/base/KZQCbsWefa5e3MsamMccOYYPn)"
			SendMessageToUser(document.PacketManagerID, messageStr)
		}

	}
}

func GetDocumentReviewConfirmation(tableID, PageToken string) error {
	tableRecords, err := GetTableRecord(tableID, libs.Config.FeiShuDoc.BiAppToken, "", 500, []string{"项目", "confirmation_id", "document_id", "评审结束状态确认"})
	if err != nil {
		logging.ErrorLogger.Errorf("getTableRecord error:%s", err.Error())
		return err
	}
	for _, tableRecord := range tableRecords.Data.Items {
		if tableRecord.Fields["评审结束状态确认"] != nil && tableRecord.Fields["评审结束状态确认"].(string) == "确认" {
			// 更新RecordId
			dfeishureviewconfirmation := dfeishureviewconfirmation.Response{}
			confirmationsplic, ok := tableRecord.Fields["confirmation_id"].([]interface{})
			if !ok {
				logging.ErrorLogger.Errorf("类型断言失败: tableRecord.Fields['confirmation_id']不是[]interface{}类型")
				return err
			}
			confirmationMap, ok := confirmationsplic[0].(map[string]interface{})
			if !ok {
				fmt.Println("Element is not a map")
				logging.ErrorLogger.Errorf("类型断言失败:confirmationsplic[0]不是Element is not a map类型")
				return err
			}
			confirmationIDStr, ok := confirmationMap["text"].(string) // 首先确保document_id是一个字符串
			if !ok {
				logging.ErrorLogger.Errorf("confirmation_id is not a string")
				return err
			}
			confirmationID, err := strconv.ParseUint(confirmationIDStr, 10, 32) // 转换为无符号整数
			if err != nil {
				logging.ErrorLogger.Errorf("Failed to parse confirmation_id: %s", err.Error())
				return err
			}

			dfeishureviewconfirmation.Update(uint(confirmationID), map[string]interface{}{"confirmation": "确认"})
		}
	}
	if *tableRecords.Data.HasMore {
		GetDocumentReviewConfirmation(tableID, *tableRecords.Data.PageToken)
	}
	return nil
}

func GetDocumentCommentReviewData() {
	GetDocumentReviewData("tblDVIDT3EOXHP7z", "")
}

// 更新评审详情数据
func GetDocumentReviewData(tableID, PageToken string) error {
	tableRecords, err := GetTableRecord(tableID, libs.Config.FeiShuDoc.BiAppToken, "", 500, []string{"review_id", "状态", "评论性质"})
	if err != nil {
		logging.ErrorLogger.Errorf("getTableRecord error:%s", err.Error())
		return err
	}
	for _, tableRecord := range tableRecords.Data.Items {
		if tableRecord.Fields["review_id"] != nil {
			reviewIDMap, err := ExtractRecordTextFromContent(tableRecord, "review_id")
			if err != nil {
				logging.ErrorLogger.Errorf("ExtractRecordTextFromContent error:%s", err.Error())
				return err
			}
			var reviewID uint64
			if reviewIDMap["text"] != nil {
				reviewID, err = strconv.ParseUint(reviewIDMap["text"].(string), 10, 32)
				if err != nil {
					logging.ErrorLogger.Errorf("Failed to parse review_id: %s", err.Error())
					return err
				}
			}
			// 获取本地记录信息
			documentReviewDetail := dfeishuprojectdocumentreviewdetail.FeishuProjectDocumentReviewdetail{}
			err = documentReviewDetail.Find(uint(reviewID))
			if err != nil {
				logging.ErrorLogger.Errorf("Find error:%s", err.Error())
				return err
			}
			documentreviewdetailResponse := dfeishuprojectdocumentreviewdetail.FeishuProjectDocumentReviewdetail{}
			if tableRecord.Fields["状态"].(string) == "已解决" && !documentReviewDetail.IsSolved {
				// 更新评审详情数据状态值变为1
				updateObj := map[string]interface{}{
					"IsSolved": 1,
				}
				err := documentreviewdetailResponse.Update(uint(reviewID), updateObj)
				if err != nil {
					logging.ErrorLogger.Errorf("Update error:%s", err.Error())
					return err
				}
				// 更新评论基础表信息
				err = dfeishuprojectdocumentcomment.UpdateByCommentID(uint(documentReviewDetail.CommentID), updateObj)
				if err != nil {
					logging.ErrorLogger.Errorf("Update error:%s", err.Error())
					return err
				}
			}

			if tableRecord.Fields["评论性质"] != nil && tableRecord.Fields["评论性质"].(string) != documentReviewDetail.CommentType {
				// 更新评审详情数据状态值变为新的值
				updateObj := map[string]interface{}{
					"CommentType": tableRecord.Fields["评论性质"].(string),
				}
				err := documentreviewdetailResponse.Update(uint(reviewID), updateObj)
				if err != nil {
					logging.ErrorLogger.Errorf("Update error:%s", err.Error())
				}
				// 更新评论基础表信息
				err = dfeishuprojectdocumentcomment.UpdateByCommentID(uint(documentReviewDetail.CommentID), updateObj)
				if err != nil {
					logging.ErrorLogger.Errorf("Update error:%s", err.Error())
				}
			}
		}
	}
	if *tableRecords.Data.HasMore {
		GetDocumentReviewData(tableID, *tableRecords.Data.PageToken)
	}
	return nil
}

func SyncFeiShuProjectDocumentRequests() {
	tableID := "tblrC0sFwDdnOIML"
	// 批量删除线上数据
	DeleteTableRecordData(tableID, []string{"项目"})
	// 获取本地数据
	RequestData, err := dfeishuprojectdocument.GetDocumentRequest()
	if err != nil {
		logging.ErrorLogger.Errorf("GetDocumentRequest error:%s", err.Error())
		return
	}
	// 批量同步到线上
	// 分批处理新增数据
	const batchSize = 500
	for i := 0; i < len(RequestData); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(RequestData) {
			endIndex = len(RequestData)
		}
		batchData := RequestData[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, reviewdetail := range batchData {
				rec := map[string]interface{}{
					"需求名称":  reviewdetail.Requirement,
					"需求ID":  reviewdetail.RequestID,
					"项目":    reviewdetail.ProjectName,
					"PGTTM": []interface{}{map[string]interface{}{"id": reviewdetail.PgttmUserID}},
					"代码量":   reviewdetail.TotalCodes,
				}
				records = append(records, rec)
			}
			tableRecordResp, err := BatchCreate(tableID, libs.Config.FeiShuDoc.BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("DocumentWorkPacket batchCreate error:%s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

func SyncFeiShuProjectDocumentTypeRequests() {
	tableID := "tblphyXcw3zW8opK"
	// 批量删除线上数据
	DeleteTableRecordData(tableID, []string{"项目"})
	// 获取本地数据
	RequestData, err := dfeishuprojectdocument.GetDocumentTypeRequest()
	if err != nil {
		logging.ErrorLogger.Errorf("GetDocumentTypeWorkPacket error:%s", err.Error())
		return
	}
	// 批量同步到线上
	// 分批处理新增数据
	const batchSize = 500
	for i := 0; i < len(RequestData); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(RequestData) {
			endIndex = len(RequestData)
		}
		batchData := RequestData[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, reviewdetail := range batchData {
				rec := map[string]interface{}{
					"需求名称": reviewdetail.Requirement,
					"需求ID": reviewdetail.RequestID,
					"项目":   reviewdetail.ProjectName,
					"文档类型": reviewdetail.DocumentCategoryTitle,
				}
				records = append(records, rec)
			}
			tableRecordResp, err := BatchCreate(tableID, libs.Config.FeiShuDoc.BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("GetDocumentTypeWorkPacket batchCreate error:%s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

func DeleteTableRecordData(tableID string, fieldNames []string) error {
	const pageSize = 500
	for {
		// 获取表记录
		tableRecords, err := GetTableRecord(tableID, libs.Config.FeiShuDoc.BiAppToken, "", pageSize, fieldNames)
		if err != nil {
			logging.ErrorLogger.Errorf("getTableRecord error: %s", err.Error())
			return err
		}
		if len(tableRecords.Data.Items) == 0 {
			break
		}
		// 收集记录ID
		recordIds := make([]string, 0, len(tableRecords.Data.Items))
		for _, tableRecord := range tableRecords.Data.Items {
			recordIds = append(recordIds, *tableRecord.RecordId)
		}
		// 批量删除记录
		if len(recordIds) > 0 {
			err := BatchDelete(tableID, libs.Config.FeiShuDoc.BiAppToken, recordIds)
			if err != nil {
				logging.ErrorLogger.Errorf("deleteTableRecord error: %s", err.Error())
				return err
			}
		}
		time.Sleep(120 * time.Millisecond)
	}
	return nil
}

func DeleteTableRecordDataV4(appToken, tableID string, fieldNames []string) error {
	const pageSize = 500
	for {
		// 获取表记录
		tableRecords, err := GetTableRecord(tableID, appToken, "", pageSize, fieldNames)
		if err != nil {
			logging.ErrorLogger.Errorf("getTableRecord error: %s", err.Error())
			time.Sleep(5 * time.Second)
			continue
		}
		if len(tableRecords.Data.Items) == 0 {
			break
		}
		// 收集记录ID
		recordIds := make([]string, 0, len(tableRecords.Data.Items))
		for _, tableRecord := range tableRecords.Data.Items {
			recordIds = append(recordIds, *tableRecord.RecordId)
		}
		// 批量删除记录
		if len(recordIds) > 0 {
			err := BatchDelete(tableID, appToken, recordIds)
			if err != nil {
				logging.ErrorLogger.Errorf("deleteTableRecord error: %s", err.Error())
				time.Sleep(5 * time.Second)
				continue
			}
		}
		time.Sleep(120 * time.Millisecond)
	}
	return nil
}

// 同步文档--PSTL人员信息
func SyncDocumentPSTLInfo(projectName, tableID string) {
	// 获取项目文档数据
	projectDocuments, err := dfeishuprojectdocument.GetUpdateDataByProjectName(projectName)
	if err != nil {
		logging.ErrorLogger.Errorf("getInsertDataByProjectName error:%s", err.Error())
		return
	}
	for _, projectDocument := range projectDocuments {
		// 获取PSTL人员信息
		pstlDatas, err := dfeishuprojectdocument.GetDocumentPSTLByProjectName(projectDocument.ProjectName, projectDocument.DepartmentName)
		if err != nil {
			logging.ErrorLogger.Errorf("GetDocumentPSTLByProjectName error:%s", err.Error())
			continue
		}
		if len(pstlDatas) > 0 {
			var pstlIDs, pstlNames []string
			for _, pstlData := range pstlDatas {
				pstlIDs = append(pstlIDs, pstlData.OpenID)
				pstlNames = append(pstlNames, pstlData.Name)
			}
			if len(pstlIDs) > 0 {
				updateObj := map[string]interface{}{
					"PstlUserID":   strings.Join(pstlIDs, "|"),
					"PstlUserName": strings.Join(pstlNames, "|"),
				}
				err := dfeishuprojectdocument.UpdateDocumentDataByID(projectDocument.ID, updateObj)
				if err != nil {
					logging.ErrorLogger.Errorf("UpdateDocumentDataByProjectName error:%s", err.Error())
				}
				// 授权PSTL文档权限
				if projectDocument.DocumentUrl != "" {
					for _, pstlID := range pstlIDs {
						_, err := GrantPermission(projectDocument.FileToken, projectDocument.FileType, pstlID, "view")
						if err != nil {
							logging.ErrorLogger.Errorf("GrantJudegeViewPermission error:%s", err.Error())
							continue
						}
						time.Sleep(500 * time.Millisecond)
					}
				}
			}
		}
		// PGTTM人员
		if projectDocument.PgttmUserID != "" && projectDocument.DocumentUrl != "" {
			_, err := GrantPermission(projectDocument.FileToken, projectDocument.FileType, projectDocument.PgttmUserID, "view")
			if err != nil {
				logging.ErrorLogger.Errorf("GrantJudegeViewPermission error:%s", err.Error())
				continue
			}
			time.Sleep(500 * time.Millisecond)
		}
	}
}

func GrantJudegeViewPermission(fileToken, fileType string, requiredJudgeIDs, optionalJudgeIDs []string) error {
	if len(optionalJudgeIDs) > 0 {
		requiredJudgeIDs = append(requiredJudgeIDs, optionalJudgeIDs...)
	}
	if len(requiredJudgeIDs) > 0 {
		for _, judgeID := range requiredJudgeIDs {
			_, err := GrantPermission(fileToken, fileType, judgeID, "view")
			if err != nil {
				logging.ErrorLogger.Errorf("GrantJudegeViewPermission error:%s", err.Error()+fileToken)
				continue
			}
			time.Sleep(2000 * time.Millisecond)
		}
	}
	return nil
}

// 通知评委确认评审性质和评审状态
func NotifyReviewersForReviewTypeAndStatus() {
	// 判断是否是工作日
	isWorkDay, err := dfeishuprojectdocumentreviewdetail.ISWorkDay()
	if err != nil {
		logging.ErrorLogger.Errorf("ISWorkDay error:%s", err.Error())
		return
	}
	if isWorkDay {
		// 获取需要通知的评审信息
		unConfirmedInfo, err := dfeishuprojectdocumentreviewdetail.GetUnConfirmedInformation()
		if err != nil {
			logging.ErrorLogger.Errorf("GetUnConfirmedInformation error:%s", err.Error())
			return
		}
		SendCardMessageToUnConfirmedJudge(unConfirmedInfo)
	}
}

// 通知评委签核意见
func NotifyReviewersForReviewComment() {
	// 判断是否是工作日
	isWorkDay, err := dfeishuprojectdocumentreviewdetail.ISWorkDay()
	if err != nil {
		logging.ErrorLogger.Errorf("ISWorkDay error:%s", err.Error())
		return
	}
	if isWorkDay {
		// 获取未签核的评审信息
		unConfirmedInfo, err := dfeishureviewconfirmation.GetUnConfirmationData()
		if err != nil {
			logging.ErrorLogger.Errorf("GetUnConfirmationData error:%s", err.Error())
			return
		}
		if len(unConfirmedInfo) > 0 {
			for _, unConfirmed := range unConfirmedInfo {
				feishuProjectDocument := dfeishuprojectdocument.FeishuProjectDocument{}
				err := feishuProjectDocument.Find(uint(unConfirmed.DocID))
				if err != nil {
					logging.ErrorLogger.Errorf("GetUnConfirmationData error:%s", err.Error())
					continue
				}
				SendCardMessageToNotifyJudge(feishuProjectDocument, unConfirmed.JudgeID)
				time.Sleep(time.Second * 1)
			}
		}
	}
}

// 删除已删除的文档评论
func DeleteComments(projectName string) {
	documentReviewDetails, err := dfeishuprojectdocumentreviewdetail.GetUpdateDataByProjectName(projectName)
	if err != nil {
		logging.ErrorLogger.Errorf("GetUpdateDataByProjectName error:%s", err.Error())
		return
	}
	// 查看是否存在该项目的文档评审详情表
	tableName := "评论详情表"
	feishubitable := dfeishubitablelist.Response{}
	err = feishubitable.FindByNameAndProjectName(tableName, projectName)
	if err != nil {
		logging.ErrorLogger.Errorf("findByNameAndProjectName table error:%s", err.Error())
		return
	}
	tableID := feishubitable.TableID
	for _, Comment := range documentReviewDetails {
		fmt.Println(Comment.CommentID)
		if Comment.RecordID == "" {
			continue
		}
		// 请求获取飞书文档评论详情
		commentResp, err := BatchQueryComments(Comment.FileToken, Comment.FileType, []string{strconv.FormatUint(Comment.CommentID, 10)})
		if err != nil {
			logging.ErrorLogger.Errorf("batchQueryComments error:%s", err.Error())
			continue
		}
		// 1069301 该评论不存在
		if commentResp.Code == 1069301 {
			// 数据库表中也删除
			documentreviewdetail := dfeishuprojectdocumentreviewdetail.FeishuProjectDocumentReviewdetail{}
			err = documentreviewdetail.Delete(Comment.ID)
			if err != nil {
				logging.ErrorLogger.Errorf("delete documentreviewdetail error:%s", err.Error())
				continue
			}
			// 删除评论基础表数据
			err = dfeishuprojectdocumentcomment.DeleteByCommentID(uint(Comment.CommentID))
			if err != nil {
				logging.ErrorLogger.Errorf("delete projectdocument error:%s", err.Error())
				continue
			}
			err := BatchDelete(tableID, libs.Config.FeiShuDoc.BiAppToken, []string{Comment.RecordID})
			if err != nil {
				logging.ErrorLogger.Errorf("batchDelete error:%s", err.Error())
				continue
			}
			// 删除评论基础表数据
			time.Sleep(150 * time.Millisecond)
		}
		time.Sleep(150 * time.Millisecond)
	}
}

func SendMessageToJudge(documentRecord dfeishuprojectdocument.FeishuProjectDocument, requiredJudgeIDs, optionalJudgeIDs []string, endTimeStr string) {
	messageStr := "有一篇文档等待你进行评审:[" + documentRecord.DocumentName + "](" + documentRecord.DocumentUrl + "),期望评审结束时间:" + endTimeStr + "，请注意！"
	if len(optionalJudgeIDs) > 0 {
		requiredJudgeIDs = append(requiredJudgeIDs, optionalJudgeIDs...)
	}
	if len(requiredJudgeIDs) > 0 {
		for _, judgeID := range requiredJudgeIDs {
			SendMessage(judgeID, messageStr)
		}
	}
}

func SendCardMessageToJudge(documentRecord dfeishuprojectdocument.FeishuProjectDocument, requiredJudgeIDs []string) {
	for _, judgeID := range requiredJudgeIDs {
		var commentLists []Comment
		// 根据获取评论列表
		comments, err := dfeishuprojectdocumentreviewdetail.GetDocumentReviewCommentByPMSDocIDAndJudgeID(documentRecord.PMSDocID, judgeID)
		if err != nil {
			logging.ErrorLogger.Errorf("GetDocumentReviewCommentByPMSDocIDAndJudgeID error:%s", err.Error())
			continue
		}
		for _, comment := range comments {
			var commentData Comment
			switch comment.CommentType {
			case "未确认":
				commentData.CommentType = "<font color='blue'>" + comment.CommentType + "</font>"
			case "缺陷":
				commentData.CommentType = "<font color='red'>" + comment.CommentType + "</font>"
			case "建议":
				commentData.CommentType = "<font color='wathet'>" + comment.CommentType + "</font>"
			case "提问":
				commentData.CommentType = "<font color='turquoise'>" + comment.CommentType + "</font>"
			}
			commentData.Quote = comment.Quote
			commentData.Content = comment.CommentContent
			if comment.IsSolved {
				commentData.IsSolved = "<text_tag color='green'>已解决</text_tag>"
			} else {
				commentData.IsSolved = "<text_tag color='red'>未解决</text_tag>"
			}
			commentLists = append(commentLists, commentData)
		}
		documentName := "<a href='" + documentRecord.DocumentUrl + "'>" + documentRecord.DocumentName + "</a>"
		template := Template{
			Type: "template",
			Data: Data{
				TemplateID:          "AAq3Q5yP0TnHE",
				TemplateVersionName: "1.0.5",
				TemplateVariable: TemplateData{
					DocumentCategoryTitle: documentRecord.DocumentCategoryTitle,
					DocumentOwnerName:     documentRecord.DocumentOwnerID,
					DocumentName:          documentName,
					DocumentURL:           "https://ruijie.feishu.cn/base/KZQCbsWefa5e3MsamMccOYYPnMr?table=tblDVIDT3EOXHP7z",
					ProjectName:           documentRecord.ProjectName,
					PMSDocID:              documentRecord.PMSDocID,
					JudgeEndURL:           "https://ruijie.feishu.cn/base/KZQCbsWefa5e3MsamMccOYYPnMr?table=tblDLgWAAPZ2wkQh",
					CommentList:           commentLists,
				},
			},
		}
		// 将结构体序列化为 JSON 字符串
		jsonData, err := json.MarshalIndent(template, "", "  ")
		if err != nil {
			fmt.Println("Error serializing JSON:", err)
			return
		}
		SendCardMessage(judgeID, string(jsonData))
	}
}

func SendCardMessageToUnConfirmedJudge(unConfirmedInfo []*dfeishuprojectdocumentreviewdetail.UnConfirmedInformation) {
	for _, info := range unConfirmedInfo {
		var commentLists []Comment
		// 根据获取评论列表
		comments, err := dfeishuprojectdocumentreviewdetail.GetDocumentReviewCommentByPMSDocIDAndJudgeID(uint(info.PmsDocID), info.CommentUserID)
		if err != nil {
			logging.ErrorLogger.Errorf("GetDocumentReviewCommentByPMSDocIDAndJudgeID error:%s", err.Error())
			continue
		}
		for _, comment := range comments {
			var commentData Comment
			switch comment.CommentType {
			case "未确认":
				commentData.CommentType = "<font color='blue'>" + comment.CommentType + "</font>"
			case "缺陷":
				commentData.CommentType = "<font color='red'>" + comment.CommentType + "</font>"
			case "建议":
				commentData.CommentType = "<font color='wathet'>" + comment.CommentType + "</font>"
			case "提问":
				commentData.CommentType = "<font color='turquoise'>" + comment.CommentType + "</font>"
			}
			commentData.Quote = comment.Quote
			commentData.Content = comment.CommentContent
			if comment.IsSolved {
				commentData.IsSolved = "<text_tag color='green'>已解决</text_tag>"
			} else {
				commentData.IsSolved = "<text_tag color='red'>未解决</text_tag>"
			}
			commentLists = append(commentLists, commentData)
		}
		documentName := "<a href='" + info.DocumentUrl + "'>" + info.DocumentName + "</a>"
		template := Template{
			Type: "template",
			Data: Data{
				TemplateID:          "AAq06FN7URMqu",
				TemplateVersionName: "1.0.1",
				TemplateVariable: TemplateData{
					DocumentCategoryTitle: info.DocumentCategoryTitle,
					DocumentName:          documentName,
					DocumentURL:           "https://ruijie.feishu.cn/base/KZQCbsWefa5e3MsamMccOYYPnMr?table=tblDVIDT3EOXHP7z",
					ProjectName:           info.ProjectName,
					PMSDocID:              uint(info.PmsDocID),
					CommentList:           commentLists,
				},
			},
		}
		// 将结构体序列化为 JSON 字符串
		jsonData, err := json.MarshalIndent(template, "", "  ")
		if err != nil {
			fmt.Println("Error serializing JSON:", err)
			return
		}
		SendCardMessage(info.CommentUserID, string(jsonData))
		time.Sleep(time.Second * 1)
	}
}

func SendCardMessageToNotifyJudge(documentRecord dfeishuprojectdocument.FeishuProjectDocument, judgeID string) {
	var commentLists []Comment
	// 根据获取评论列表
	comments, err := dfeishuprojectdocumentreviewdetail.GetDocumentReviewCommentByPMSDocIDAndJudgeID(documentRecord.PMSDocID, judgeID)
	if err != nil {
		logging.ErrorLogger.Errorf("GetDocumentReviewCommentByPMSDocIDAndJudgeID error:%s", err.Error())
		return
	}
	for _, comment := range comments {
		var commentData Comment
		switch comment.CommentType {
		case "未确认":
			commentData.CommentType = "<font color='blue'>" + comment.CommentType + "</font>"
		case "缺陷":
			commentData.CommentType = "<font color='red'>" + comment.CommentType + "</font>"
		case "建议":
			commentData.CommentType = "<font color='wathet'>" + comment.CommentType + "</font>"
		case "提问":
			commentData.CommentType = "<font color='turquoise'>" + comment.CommentType + "</font>"
		}
		commentData.Quote = comment.Quote
		commentData.Content = comment.CommentContent
		if comment.IsSolved {
			commentData.IsSolved = "<text_tag color='green'>已解决</text_tag>"
		} else {
			commentData.IsSolved = "<text_tag color='red'>未解决</text_tag>"
		}
		commentLists = append(commentLists, commentData)
	}
	documentName := "<a href='" + documentRecord.DocumentUrl + "'>" + documentRecord.DocumentName + "</a>"
	template := Template{
		Type: "template",
		Data: Data{
			TemplateID:          "AAq0qYEg9CDKk",
			TemplateVersionName: "1.0.2",
			TemplateVariable: TemplateData{
				DocumentCategoryTitle: documentRecord.DocumentCategoryTitle,
				DocumentOwnerName:     documentRecord.DocumentOwnerID,
				DocumentName:          documentName,
				DocumentURL:           "https://ruijie.feishu.cn/base/KZQCbsWefa5e3MsamMccOYYPnMr?table=tblDVIDT3EOXHP7z",
				ProjectName:           documentRecord.ProjectName,
				PMSDocID:              documentRecord.PMSDocID,
				JudgeEndURL:           "https://ruijie.feishu.cn/base/KZQCbsWefa5e3MsamMccOYYPnMr?table=tblDLgWAAPZ2wkQh",
				CommentList:           commentLists,
			},
		},
	}
	// 将结构体序列化为 JSON 字符串
	jsonData, err := json.MarshalIndent(template, "", "  ")
	if err != nil {
		fmt.Println("Error serializing JSON:", err)
		return
	}
	// fmt.Println(judgeID)
	SendCardMessage(judgeID, string(jsonData))
}

func SendMessageToUser(openID, messageStr string) {
	SendMessage(openID, messageStr)
	// fmt.Println(openID)
	// SendMessage("ou_ff57ac72b34fc3e982ef2ba1bb59c0b3", messageStr)
}

func DeleteTableRecordDataV2(tableID string, fieldNames []string, biAppToken string) error {
	const pageSize = 500
	for {
		// 获取表记录
		tableRecords, err := GetTableRecord(tableID, biAppToken, "", pageSize, fieldNames)
		if err != nil {
			logging.ErrorLogger.Errorf("getTableRecord error: %s", err.Error())
			return err
		}
		if len(tableRecords.Data.Items) == 0 {
			break
		}
		// 收集记录ID
		recordIds := make([]string, 0, len(tableRecords.Data.Items))
		for _, tableRecord := range tableRecords.Data.Items {
			recordIds = append(recordIds, *tableRecord.RecordId)
		}
		// 批量删除记录
		if len(recordIds) > 0 {
			err := BatchDelete(tableID, biAppToken, recordIds)
			if err != nil {
				logging.ErrorLogger.Errorf("deleteTableRecord error: %s", err.Error())
				return err
			}
		}
		time.Sleep(120 * time.Millisecond)
	}
	return nil
}

func DocumentCommentTest() {
	// 获取文档评论信息
	fileCommentResp, err := GetDocAllComments("CRUwsAoMchTLkCtX0BPc44c7nke", "sheet", 100)
	if err != nil {
		logging.ErrorLogger.Errorf("GetDocAllComments err:%s", err.Error())
		return
	}
	if len(fileCommentResp.Data.Items) > 0 {
		for _, v1 := range fileCommentResp.Data.Items {
			// 获取评论回复信息
			replyContent, replyCommentType, PersonID := FormatReplies(v1.ReplyList, *v1.UserId)
			if PersonID == "" {
				PersonID = *v1.UserId
			}
			fmt.Println(PersonID, replyContent, replyCommentType)
		}
	}
}
