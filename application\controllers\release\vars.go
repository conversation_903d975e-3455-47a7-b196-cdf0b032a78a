package release

import (
	"irisAdminApi/application/libs"
	"time"

	"github.com/imroc/req/v3"
)

var ReleaseClient *req.Client

func InitClient() {
	ReleaseClient = req.C().
		SetCommonRetryCount(3).
		// Set the retry sleep interval with a commonly used algorithm: capped exponential backoff with jitter (https://aws.amazon.com/blogs/architecture/exponential-backoff-and-jitter/).
		SetCommonRetryBackoffInterval(1*time.Second, 5*time.Second).
		AddCommonRetryCondition(func(resp *req.Response, err error) bool {
			return err != nil
		})
	if libs.Config.Debug {
		ReleaseClient.DevMode()
	}
}
