package taskmanagers

import (
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/license/dauthtask"
	"net/http"
	"strings"
	"time"
)

func getBasicAuthorization() string {
	return "Basic " + base64.StdEncoding.EncodeToString(
		[]byte(libs.Config.License.BasicAuthUsername+":"+libs.Config.License.BasicAuthPassword))
}

type CodeMsgResponse struct {
	Msg  string `json:"msg"`
	Code uint   `json:"code"`
}

type TokenResponse struct {
	Msg  string `json:"msg"`
	Code uint   `json:"code"`
	Data struct {
		AccessToken  string `json:"accessToken"`
		RefreshToken string `json:"refreshToken"`
		Expires      int64  `json:"expires"`
	} `json:"data"`
}

type PasswordResponse struct {
	Msg  string `json:"msg"`
	Code uint   `json:"code"`
	Data string `json:"data"`
}

func getPassword(username, password string) (string, int64, error) {
	req := map[string]interface{}{
		"password": fmt.Sprintf("%s-%s-123456", password, username),
	}
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	statusCode, _, response, _ := libs.PostJson(libs.Config.License.AdminEndpoint+"/auth/oauth2/password", req, headers)
	if statusCode != 200 {
		logging.ErrorLogger.Errorf("获取密码失败, statusCode=<%d>, resp=<%s>, req=<%v>", statusCode, string(response), req)
		return "", 0, fmt.Errorf("获取密码失败, statusCode=<%d>, resp=<%s>", statusCode, string(response))
	}
	passwordResponse := PasswordResponse{}
	if err := json.Unmarshal(response, &passwordResponse); err != nil {
		logging.ErrorLogger.Errorf("读取密码失败响应失败, err=<%s>, resp=<%s>", err, string(response))
		return "", 0, fmt.Errorf("读取密码失败响应失败, err=<%s>, resp=<%s>", err, string(response))
	} else if passwordResponse.Code != 0 {
		logging.ErrorLogger.Errorf("获取密码失败, statusCode=<%d>, resp=<%s>", statusCode, string(response))
		return "", 0, fmt.Errorf("获取密码失败, statusCode=<%d>, resp=<%s>", statusCode, string(response))
	} else {
		return passwordResponse.Data, 0, nil
	}
}

func getAdminToken(basicAuthorization string) (string, int64, error) {
	adminTokenReq := map[string]interface{}{
		"grantType":            "password",
		"scope":                "server",
		"username":             libs.Config.License.AdminUsername,
		"password":             libs.Config.License.AdminPassword,
		"adminFlag":            1,
		"privacyAgreementTime": "hhBvjAEnO+zPKCC1zrkk1T3Vg8b5R/EbHeNslwy3Lwc=",
	}
	headers := map[string]string{
		"Authorization": basicAuthorization,
		"Content-Type":  "application/json",
	}
	statusCode, _, response, _ := libs.PostJson(libs.Config.License.AdminEndpoint+"/auth/oauth2/token", adminTokenReq, headers)
	if statusCode != 200 {
		logging.ErrorLogger.Errorf("获取管理员token失败, statusCode=<%d>, resp=<%s>, req=<%v>", statusCode, string(response), adminTokenReq)
		return "", 0, fmt.Errorf("获取管理员token失败, statusCode=<%d>, resp=<%s>", statusCode, string(response))
	}
	tokenResp := &TokenResponse{}
	if err := json.Unmarshal(response, &tokenResp); err != nil {
		logging.ErrorLogger.Errorf("读取管理员token响应失败, err=<%s>, resp=<%s>", err, string(response))
		return "", 0, fmt.Errorf("读取管理员token响应失败, err=<%s>, resp=<%s>", err, string(response))
	} else if tokenResp.Code != 0 {
		logging.ErrorLogger.Errorf("获取管理员token失败, statusCode=<%d>, resp=<%s>", statusCode, string(response))
		return "", 0, fmt.Errorf("获取管理员token失败, statusCode=<%d>, resp=<%s>", statusCode, string(response))
	} else {
		return tokenResp.Data.AccessToken, tokenResp.Data.Expires, nil
	}
}

func getUserToken(basicAuthorization string) (string, int64, error) {

	userTokenReq := map[string]interface{}{
		"grantType":            "password",
		"scope":                "server",
		"username":             libs.Config.License.Username,
		"password":             libs.Config.License.Password,
		"adminFlag":            0,
		"privacyAgreementTime": "hhBvjAEnO+zPKCC1zrkk1T3Vg8b5R/EbHeNslwy3Lwc=",
	}
	headers := map[string]string{
		"Authorization": basicAuthorization,
		"Content-Type":  "application/json",
	}
	statusCode, _, resp, _ := libs.PostJson(libs.Config.License.SecCloudEndpoint+"/auth/oauth2/token", userTokenReq, headers)
	if statusCode != 200 {
		logging.ErrorLogger.Errorf("获取用户token失败, statusCode=<%d>, resp=<%s>, req=<%v>", statusCode, string(resp), userTokenReq)
		return "", 0, fmt.Errorf("获取用户token失败, statusCode=<%d>, resp=<%s>", statusCode, string(resp))
	}
	tokenResp := &TokenResponse{}
	if err := json.Unmarshal(resp, &tokenResp); err != nil {
		logging.ErrorLogger.Errorf("读取用户token响应失败, err=<%s>, resp=<%s>", err, string(resp))
		return "", 0, fmt.Errorf("读取用户token响应失败, err=<%s>, resp=<%s>", err, string(resp))
	} else if tokenResp.Code != 0 {
		logging.ErrorLogger.Errorf("获取用户token失败, statusCode=<%d>, resp=<%s>", statusCode, string(resp))
		return "", 0, fmt.Errorf("获取用户token失败, statusCode=<%d>, resp=<%s>", statusCode, string(resp))
	} else {
		return tokenResp.Data.AccessToken, tokenResp.Data.Expires, nil
	}
}

// 防火墙设备合法化;
func deviceInfoAddDevice(adminToken string, authTaskRes *dauthtask.Response) error {
	addDeviceReq := map[string]interface{}{
		"snCode":          authTaskRes.DeviceSn,           // 用户填入的设备sn
		"materialCode":    authTaskRes.DeviceMaterialCode, // 根据deviceModel从DeviceModel中超找到的DeviceMaterialCode
		"deviceType":      authTaskRes.DeviceType,
		"deviceStatus":    1,
		"deviceModel":     authTaskRes.DeviceModel, // 用户填入的deviceModel
		"custName":        "测试-自动化工具",
		"marketSegments":  "GG",
		"projectProvince": "福建省",
		"deployRemark":    "GG",
	}
	headers := map[string]string{
		"Authorization": "Bearer " + adminToken,
		"Content-Type":  "application/json",
	}
	statusCode, _, resp, _ := libs.PostJson(libs.Config.License.SecCloudEndpoint+"/device/devInfo/addDevice", addDeviceReq, headers)
	if statusCode != 200 {
		logging.ErrorLogger.Errorf("防火墙设备合法化失败, statusCode=<%d>, resp=<%s>", statusCode, string(resp))
		return fmt.Errorf("防火墙设备合法化失败, statusCode=<%d>, resp=<%s>", statusCode, string(resp))
	}
	tokenResp := &CodeMsgResponse{}
	if err := json.Unmarshal(resp, &tokenResp); err != nil {
		logging.ErrorLogger.Errorf("读取防火墙设备合法化响应失败, err=<%s>, resp=<%s>", err, string(resp))
		return fmt.Errorf("读取用防火墙设备合法化响应失败, err=<%s>, resp=<%s>", err, string(resp))
	}
	// else if tokenResp.Code != 0 {
	// 	logging.ErrorLogger.Errorf("防火墙设备合法化失败, statusCode=<%d>, resp=<%s>", statusCode, string(resp))
	// 	return fmt.Errorf("防火墙设备合法化失败, statusCode=<%d>, resp=<%s>", statusCode, string(resp))
	// }
	return nil
}

// 解绑设备;
func unbindDevices(adminToken string, authTaskRes *dauthtask.Response) error {
	unbindDevicesReq := map[string]interface{}{
		"snList": authTaskRes.DeviceSn, // 用户输入的设备sn
	}
	headers := map[string]string{
		"Authorization": "Bearer " + adminToken,
	}

	unbingImage := []libs.UploadFile{
		// {
		// 	Name:     "imageFile",
		// 	Filepath: "/mnt/sata0/data/license/unbind.jpg",
		// },
	}

	statusCode, _, resp, _ := libs.PostFile(libs.Config.License.AdminEndpoint+"/device/unBind/unbindDevices", unbindDevicesReq, unbingImage, headers)
	if statusCode != 200 {
		logging.ErrorLogger.Errorf("设备解绑失败, statusCode=<%d>, resp=<%s>", statusCode, string(resp))
		return fmt.Errorf("设备解绑失败, statusCode=<%d>, resp=<%s>", statusCode, string(resp))
	}
	tokenResp := &CodeMsgResponse{}
	if err := json.Unmarshal(resp, &tokenResp); err != nil {
		logging.ErrorLogger.Errorf("读取防火墙设备合法化响应失败, err=<%s>, resp=<%s>", err, string(resp))
		return fmt.Errorf("读取用防火墙设备合法化响应失败, err=<%s>, resp=<%s>", err, string(resp))
	}
	// else if tokenResp.Code != 0 {
	// 	logging.ErrorLogger.Errorf("防火墙设备合法化失败, statusCode=<%d>, resp=<%s>", statusCode, string(resp))
	// 	return fmt.Errorf("防火墙设备合法化失败, statusCode=<%d>, resp=<%s>", statusCode, string(resp))
	// }
	return nil
}

func productionAddProductRecord(adminToken, ticketCode, snCode string, authTaskRes *dauthtask.Response) error {
	addProductRecordReq := map[string]interface{}{
		"ticketCode":   ticketCode,               // 根据时间戳生成的;
		"materialCode": authTaskRes.MaterialCode, // 根据用户的DeviceModel从Material列出物料选中的MaterialCode;
		"materialName": authTaskRes.MaterialName, // 根据MaterialCode从Material中查找到;
		"authType":     authTaskRes.AuthType,     // 根据MaterialCode从Material中查找到;
		"snCode":       snCode,                   // ticketCode+001
	}
	headers := map[string]string{
		"Authorization": "Bearer " + adminToken,
		"Content-Type":  "application/json",
	}
	statusCode, _, resp, _ := libs.PostJson(libs.Config.License.ProductionEndpoint+"/production/productRecord/add", addProductRecordReq, headers)
	if statusCode != 200 {
		logging.ErrorLogger.Errorf("生产授权失败, statusCode=<%d>, resp=<%s>", statusCode, string(resp))
		return fmt.Errorf("生产授权失败, statusCode=<%d>, resp=<%s>", statusCode, string(resp))
	}
	tokenResp := &CodeMsgResponse{}
	if err := json.Unmarshal(resp, &tokenResp); err != nil {
		logging.ErrorLogger.Errorf("读取生产授权响应失败, err=<%s>, resp=<%s>", err, string(resp))
		return fmt.Errorf("读取生产授权响应失败, err=<%s>, resp=<%s>", err, string(resp))
	} else if tokenResp.Code != 0 {
		logging.ErrorLogger.Errorf("生产授权失败, statusCode=<%d>, resp=<%s>", statusCode, string(resp))
		return fmt.Errorf("生产授权失败, statusCode=<%d>, resp=<%s>", statusCode, string(resp))
	}
	return nil
}

type AuthCodeResponse struct {
	Msg  string `json:"msg"`
	Code uint   `json:"code"`
	Data struct {
		Records []struct {
			ID           string `json:"id"`
			MaterialName string `json:"materialName"`
			AuthCode     string `json:"authCode"`
		} `json:"records"`
	} `json:"data"`
}

func getAuthCode(adminToken, snCode string) (string, error) {
	reqParams := map[string]string{
		"materialName":    "",
		"authType":        "",
		"authCode":        "",
		"snCode":          snCode, // 上述生成的snCode
		"authStatus":      "",
		"activeDeviceSn":  "",
		"activeStartDate": "",
		"activeEndDate":   "",
		"current":         "1",
		"size":            "10",
	}
	headers := map[string]string{
		"Authorization": "Bearer " + adminToken,
		"Content-Type":  "application/json",
	}
	statusCode, _, resp, _ := libs.Get(libs.Config.License.AuthCodeEndpoint+"/authorization/auth/manage/formal/page", reqParams, headers)
	if statusCode != 200 {
		logging.ErrorLogger.Errorf("获取授权码失败, statusCode=<%d>, resp=<%s>", statusCode, resp)
		return "", fmt.Errorf("获取授权码失败, statusCode=<%d>, resp=<%s>", statusCode, resp)
	}
	authCodeRes := &AuthCodeResponse{}
	if err := json.Unmarshal(resp, authCodeRes); err != nil {
		logging.ErrorLogger.Errorf("读取授权码响应失败, err=<%s>, , resp=<%s>", err, string(resp))
		return "", fmt.Errorf("读取授权码响应失败, err=<%s>, resp=<%s>", err, string(resp))
	} else if len(authCodeRes.Data.Records) == 0 {
		logging.ErrorLogger.Errorf("指定授权码不存在, snCode=<%s>, resp=<%s>", snCode, string(resp))
		return "", fmt.Errorf("指定授权码不存在, snCode=<%s>, resp=<%s>", snCode, string(resp))
	} else if authCodeRes.Code != 0 {
		logging.ErrorLogger.Errorf("获取授权码失败, statusCode=<%d>, resp=<%s>", statusCode, string(resp))
		return "", fmt.Errorf("获取授权码失败, statusCode=<%d>, resp=<%s>", statusCode, string(resp))
	} else {
		return authCodeRes.Data.Records[0].AuthCode, nil
	}
}

func activeAuthCode(userToken, activeDeviceSn, authCode string) error {
	method := "POST"
	url := libs.Config.License.SecCloudEndpoint + "/authorization/auth/active"
	activeAuthCodeReq := fmt.Sprintf(`[
		{
			"activeDeviceSn": "%s",
			"authCode": "%s"
		}
	]`, activeDeviceSn, authCode)

	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}
	req, err := http.NewRequest(method, url, strings.NewReader(activeAuthCodeReq))
	if err != nil {
		return fmt.Errorf("授权码激活出错, err: <%s>", err)
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", "Bearer "+userToken)

	res, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("授权码激活出错, err: <%s>", err)
	}

	defer res.Body.Close()
	resp, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return fmt.Errorf("读取授权码激活响应出错, err: <%s>", err)
	}
	if res.StatusCode != 200 {
		return fmt.Errorf("授权码激活失败, statusCode=<%d>, resp=<%s>", res.StatusCode, string(resp))
	}
	authCodeRes := &AuthCodeResponse{}
	if err := json.Unmarshal(resp, authCodeRes); err != nil {
		logging.ErrorLogger.Errorf("读取授权码激活响应失败, err=<%s>, resp=<%s>", err, string(resp))
		return fmt.Errorf("读取授权码激活响应失败, err=<%s>, resp=<%s>", err, string(resp))
	} else if authCodeRes.Code != 0 {
		logging.ErrorLogger.Errorf("授权码激活失败, statusCode=<%d>, resp=<%s>", res.StatusCode, string(resp))
		return fmt.Errorf("授权码激活失败, statusCode=<%d>, resp=<%s>", res.StatusCode, string(resp))
	}
	return nil
}

func generateTicketCodeAndSnCode() (string, string) {
	ticketCode := fmt.Sprintf("%d", time.Now().Unix())
	return ticketCode, ticketCode + "001"
}
