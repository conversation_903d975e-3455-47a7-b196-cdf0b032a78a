package dbug

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models"
	"irisAdminApi/application/models/datasync"

	"gorm.io/gorm"
)

const ModelName = "BUG表"

type BugSyncResponse struct {
	/*
		"state": "SUCCESS",
		"data": [
			{
				"rownum": 1,
				"bugId": 259201,                            //BUGID
				"bugBelong": "测试人",                        //bug归属人
				"bugOwner": "测试人",                        //bug负责人
				"bugTestCharger": "测试2",                    //测试负责人
				"bugOs": "测试项目",                        //bug操作系统
				"bugProduct": "测试产品",                    //bug产品
				"workpacketName": null,                        //bug工作包
				"summary": "描述",                            //bug描述
				"mainbugid": null,                            //bug是否是从某个BUG镜像出来的BUG，如果有直接是父bugid
				"samebugid": null,                            //bug是否是从某个BUGsameas出来的BUG，如果有直接是父bugid
				"thedate": "2015-06-23 11:20:56",            //bug创建时间
				"lastupdatedate": "2015-06-23 15:28:53",    //bug更新时间
				"bugState": "DENIAL-ByDevelopment",            //bug状态
				"disabled": null                            //bug是否无效 0或者null 表示无效；1或者true表示有效
			}
		],
		"total":1,
		"message": null
	*/
	State   string         `json:"state"`
	Data    []*BugResponse `json:"data"`
	Total   int            `json:"total"`
	Message string         `json:"message"`
}

type BugResponse struct {
	/*
		"rownum": 1,
		"bugId": 259201,                            //BUGID
		"bugBelong": "测试人",                        //bug归属人
		"bugOwner": "测试人",                        //bug负责人
		"bugTestCharger": "测试2",                    //测试负责人
		"bugOs": "测试项目",                        //bug操作系统
		"bugProduct": "测试产品",                    //bug产品
		"workpacketName": null,                        //bug工作包
		"summary": "描述",                            //bug描述
		"mainbugid": null,                            //bug是否是从某个BUG镜像出来的BUG，如果有直接是父bugid
		"samebugid": null,                            //bug是否是从某个BUGsameas出来的BUG，如果有直接是父bugid
		"thedate": "2015-06-23 11:20:56",            //bug创建时间
		"lastupdatedate": "2015-06-23 15:28:53",    //bug更新时间
		"bugState": "DENIAL-ByDevelopment",            //bug状态
		"disabled": null
	*/
	RowNum            int    `json:"rownum"`
	BugID             int    `json:"bugId"`
	BugBelong         string `json:"bugBelong"`
	BugOwner          string `json:"bugOwner"`
	BugTestCharger    string `json:"bugTestCharger"`
	BugOS             string `json:"bugOs"`
	BugProduct        string `json:"bugProduct"`
	BugWorkpacketName string `json:"workpacketName"`
	BugSummary        string `json:"summary"`
	MainBugID         int    `json:"mainbugid"`
	SameBugID         int    `json:"samebugid"`
	BugCreatedAt      string `json:"thedate"`
	BugUpdatedAt      string `json:"lastupdatedate"`
	BugState          string `json:"bugState"`
	BugDisabled       bool   `json:"disabled"`
	BugCbdAt          string `json:"bug_cbd_at"`
	BugCbtAt          string `json:"bug_cbt_at"`
	OverTime          int    `json:"over_time"`
}

type User struct {
	ID       uint   `json:"id"`
	Username string `json:"username"`
	Name     string `json:"name"`
}

type BugMirrorApproval struct {
	models.ModelBase
	BugID   int    `gorm:"not null" json:"bug_id"`
	BugOS   string `gorm:"not null" json:"bug_os"`
	Comment string `gorm:"not null; type:varchar(200)" json:"comment"`
	UserID  uint   `gorm:"not null" json:"user_id"`
	User    *User  `gorm:"->; foreignKey:UserID" json:"user"`
}

type BugMirrorApprovalHistory struct {
	models.ModelBase
	BugID   int    `gorm:"not null" json:"bug_id"`
	BugOs   string `gorm:"not null" json:"bug_os"`
	Comment string `gorm:"not null; type:varchar(200)" json:"comment"`
	UserID  uint   `gorm:"not null" json:"user_id"`
	User    *User  `gorm:"->; foreignKey:UserID" json:"user"`
	Action  string `grom:"not null" json:"action"`
}

type Response struct {
	datasync.Bug
	Children  []*ListResponse      `gorm:"-" json:"children"`
	Mirror    map[string]string    `gorm:"-" json:"mirror"`
	Approvals []*BugMirrorApproval `gorm:"->; foreignKey:BugID; references:BugID" json:"approvals"`
}

type ListResponse struct {
	Response
}

type Request struct {
	Id uint `json:"id"`
}

func (this *Response) ModelName() string {
	return ModelName
}

func Model() *datasync.Bug {
	return &datasync.Bug{}
}

func (this *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (this *Response) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

/*
| NEW                  |
| DELAY                |
| CLOSED-ByTest        |
| ASSIGNED             |
| GIVEUP               |
| REQUEST              |
| DENIAL-ByDevelopment |
| CLOSED-ByDevelopment |
| CHECKED              |
| DENIAL-ByTest        |
| DENIAL-ByPSD         |
| REOPENED             |
| RESOLVED             |
BUG判定为需要镜像处理：CLOSED-ByTest， CLOSED-ByDevelopment， RESOLVED, CHECKED, DELAY?
BUG判定为无需镜像处理: DENIAL-ByTest, DENIAL-ByPSD, DENIAL-ByDevelopment, REQUEST, GIVEUP?, NEW, ASSIGNED
*/

func FindResolveMainBug() ([]*ListResponse, error) {
	var res []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where(`main_bug_id = 0 and bug_state in ("CLOSED-ByTest", "CLOSED-ByDevelopment", "RESOLVED", "CHECKED", "DELAY")`).Find(&res).Error // and bug_workpacket_name = "遗留或delay的bug(其他)"
	return res, err
}

func FindMirrorBug(mainBugIDs []int) ([]*ListResponse, error) {
	var res []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where(`main_bug_id in ?`, mainBugIDs).Find(&res).Error
	return res, err
}

func FindAllBug(page, pageSize int, sort, orderBy string, bugOS, mirrorStatus, resolveStatus, bugBelong, bugOwner, bugOwnerGroup, bugWorkpacketName, bugTestCharger, bugState, createdAt, updatedAt, bugCbdAt, bugCbtAt string) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	where := easygorm.GetEasyGormDb().Model(Model())

	if len(bugOS) > 0 {
		where = where.Where("bug_os in ?", strings.Split(bugOS, ","))
	}
	if len(bugBelong) > 0 {
		where = where.Where("bug_belong in ?", strings.Split(bugBelong, ","))
	}
	if len(bugOwner) > 0 {
		where = where.Where("bug_owner in ?", strings.Split(bugOwner, ","))
	}
	if len(bugOwnerGroup) > 0 {
		where = where.Where("bug_owner_group in ?", strings.Split(bugOwnerGroup, ","))
	}
	if len(bugWorkpacketName) > 0 {
		where = where.Where("bug_workpacket_name in ?", strings.Split(bugWorkpacketName, ","))
	}
	if len(bugTestCharger) > 0 {
		where = where.Where("bug_test_charger in ?", strings.Split(bugTestCharger, ","))
	}
	if len(bugState) > 0 {
		where = where.Where("bug_state in ?", strings.Split(bugState, ","))
	}
	if len(mirrorStatus) > 0 {
		where = where.Where("mirror_status = ?", mirrorStatus)
	}
	if len(resolveStatus) > 0 {
		where = where.Where("resolve_status = ?", resolveStatus)
	}
	if len(createdAt) > 0 {
		array := strings.Split(createdAt, ",")
		where = where.Where("bug_created_at between ? and ?", array[0], array[1])
	}
	if len(updatedAt) > 0 {
		array := strings.Split(updatedAt, ",")
		where = where.Where("bug_updated_at between ? and ?", array[0], array[1])
	}
	if len(bugCbdAt) > 0 {
		array := strings.Split(bugCbdAt, ",")
		where = where.Where("bug_cbd_at between ? and ?", array[0], array[1])
	}
	if len(bugCbtAt) > 0 {
		array := strings.Split(bugCbtAt, ",")
		where = where.Where("bug_cbt_at between ? and ?", array[0], array[1])
	}
	if len(orderBy) == 0 {
		orderBy = "bug_created_at"
	}
	// A -> B B ->C   A->B A-C
	preCheckResult := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where(where).Select("bug_id", "main_bug_id").Find(&preCheckResult).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}
	bugIDMap := map[int]int{}
	mainBugIDs := []int{}

	for _, item := range preCheckResult {
		bugIDMap[item.BugID] = item.MainBugID
	}

	for _, item := range preCheckResult {
		mainBugIDs = append(mainBugIDs, LoopFindBugID(bugIDMap, item.BugID))
	}

	db = db.Where(where).Where("bug_id in ?", mainBugIDs)
	// 过滤忽略状态
	err = db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}
	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).Preload("Approvals").Preload("Approvals.User").
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	AddMirrorBug(res, bugOS)
	for _, bug := range res {
		bug.Mirror = map[string]string{}
		for _, o := range strings.Split(bugOS, ",") {
			status := "0"
			if o == bug.BugOS {
				status = fmt.Sprintf("%v", bug.BugID)
			} else {
				for _, c := range bug.Children {
					if c.BugOS == o {
						status = fmt.Sprintf("%v", c.BugID)
						break
					}
				}
			}

			bug.Mirror[o] = status
		}
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func AddMirrorBug(items []*ListResponse, bugOs string) ([]*ListResponse, error) {
	result := []*ListResponse{}
	bugIds := []int{}
	for _, item := range items {
		bugIds = append(bugIds, item.BugID)
	}

	mirrorBugMap := map[int][]*ListResponse{}

	mirrorBugs := []*ListResponse{}
	db := easygorm.GetEasyGormDb().Model(Model())
	if len(bugOs) > 0 {
		db = db.Where("bug_os in ?", strings.Split(bugOs, ","))
	}
	err := db.Find(&mirrorBugs).Error
	if err != nil {
		return result, err
	}

	for _, mirrorBug := range mirrorBugs {
		if _, ok := mirrorBugMap[mirrorBug.MainBugID]; !ok {
			mirrorBugMap[mirrorBug.MainBugID] = []*ListResponse{mirrorBug}
		} else {
			mirrorBugMap[mirrorBug.MainBugID] = append(mirrorBugMap[mirrorBug.MainBugID], mirrorBug)
		}
	}

	for _, item := range items {
		a := []*ListResponse{}
		a = append(a, mirrorBugMap[item.BugID]...)
		for _, _item := range mirrorBugMap[item.BugID] {
			a = append(a, mirrorBugMap[_item.BugID]...)
		}
		item.Children = a
	}

	return result, nil
}

func FindDistinctByColumn(column string) ([]string, error) {
	result := []string{}
	err := easygorm.GetEasyGormDb().Model(Model()).Distinct(column).Order(column).Pluck(column, &result).Error
	return result, err
}

func LoopFindBugID(m map[int]int, bugID ...int) int {
	var ret int
	if _mainBugID, ok := m[bugID[0]]; ok {
		if _mainBugID == 0 {
			return bugID[0]
		}
		ret = LoopFindBugID(m, _mainBugID, bugID[0])
	} else {
		if len(bugID) > 1 {
			ret = bugID[1]
		} else {
			ret = bugID[0]
		}
	}
	return ret
}

func (m *BugMirrorApproval) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Transaction(func(tx *gorm.DB) error {
		err := tx.Model(m).Create(&object).Error
		if err != nil {
			return err
		}
		object["action"] = "添加"
		object["CreatedAt"] = time.Now()
		err = tx.Model(&BugMirrorApprovalHistory{}).Create(&object).Error
		return err
	})
	return err
}

func (m *BugMirrorApproval) Delete(object map[string]interface{}) error {
	bugOS, ok1 := object["bug_os"]
	bugID, ok2 := object["bug_id"]

	if !ok1 || !ok2 {
		return errors.New("参数错误")
	}
	err := easygorm.GetEasyGormDb().Transaction(func(tx *gorm.DB) error {
		err := tx.Model(m).Where(map[string]interface{}{
			"bug_os": bugOS,
			"bug_id": bugID,
		}).Unscoped().Delete(map[string]interface{}{
			"bug_os": bugOS,
			"bug_id": bugID,
		}).Error
		if err != nil {
			return err
		}

		object["action"] = "删除"
		object["CreatedAt"] = time.Now()
		err = tx.Model(&BugMirrorApprovalHistory{}).Create(&object).Error
		return err
	})

	return err
}

func (h *BugMirrorApprovalHistory) Find(bugID int, bugOS string) ([]*BugMirrorApprovalHistory, error) {
	var items []*BugMirrorApprovalHistory
	err := easygorm.GetEasyGormDb().Model(&BugMirrorApprovalHistory{}).Where("bug_id = ? and bug_os = ?", bugID, bugOS).Find(&items).Error
	return items, err
}

type BugMirrorHistory struct {
	models.ModelBase
	MainBugID int  `gorm:"not null" json:"main_bug_id"`
	BugID     int  `gorm:"not null" json:"bug_id"`
	UserID    uint `gorm:"not null" json:"user_id"`
}

func (bmh *BugMirrorHistory) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(&object).Error
	return err
}

func MirrorCreate(userID uint, mainBugID, bugID int, bugOS string) error {
	bug := datasync.Bug{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", mainBugID).Find(&bug).Error
	bug.BugID = bugID
	bug.MainBugID = mainBugID
	bug.BugOS = bugOS
	bug.BugState = "NEW"
	bug.BugCbdAt = nil
	bug.BugCbtAt = nil
	bug.OverTime = nil

	err = easygorm.GetEasyGormDb().Transaction(func(tx *gorm.DB) error {
		err := tx.Model(Model()).Create(&bug).Error
		if err != nil {
			return err
		}

		err = tx.Model(&BugMirrorHistory{}).Create(map[string]interface{}{
			"CreatedAt": time.Now(),
			"BugID":     bugID,
			"MainBugID": mainBugID,
			"UserID":    userID,
		}).Error
		if err != nil {
			return err
		}

		return err
	})

	return err
}

func (m *BugMirrorApproval) BatchCreate(objects []map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Transaction(func(tx *gorm.DB) error {
		err := tx.Model(m).Create(&objects).Error
		if err != nil {
			return err
		}

		for _, object := range objects {
			object["action"] = "批量添加"
			object["CreatedAt"] = time.Now()
		}

		err = tx.Model(&BugMirrorApprovalHistory{}).Create(&objects).Error
		return err
	})
	return err
}
