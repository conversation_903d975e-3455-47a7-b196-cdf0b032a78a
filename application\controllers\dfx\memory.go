package dfx

import (
	"context"
	"fmt"
	"irisAdminApi/application/logging"
	"time"

	"github.com/pkg/errors"
)

/*
dfx-内存告警规则:
type为process、hm-pool、dynamic-pool、static-pool、mbuf-pool、share,总和及详情对比上周有增长超过5%?或者有新增指标，告警
max_total_usage，对比上周增长超过5%，告警
这样？
*/

func DfxMemoryWeekCheck() (map[string]map[string]map[string]map[string]map[string]map[string][][]interface{}, error) {
	result := map[string]map[string]map[string]map[string]map[string]map[string][][]interface{}{}
	err := DfxDetailWeekCheck(result)
	if err != nil {
		logging.ErrorLogger.Errorf("dfx memory detail week check: %s", err.Error())
		return result, err
	}

	err = DfxSumWeekCheck(result)
	if err != nil {
		logging.ErrorLogger.Errorf("dfx memory detail week check: %s", err.Error())
		return result, err
	}

	return result, nil
}

func DfxDetailWeekCheck(result map[string]map[string]map[string]map[string]map[string]map[string][][]interface{}) error {
	var threshold float64 = 5
	var minVal float64 = 1000

	queryAPI := InitQueryAPI()
	// Get QueryTableResult
	ret, err := queryAPI.Query(context.Background(), fmt.Sprintf(`
	a =
		from(bucket:"dfx-clean")
			|> range(start: -14d, stop: -7d)
			|> filter(fn: (r) => r["_measurement"] == "memory")
			|> filter(fn: (r) => r._field == "kbytes")
			|> filter(fn: (r) => r.type == "process" or r.type == "hm-pool" or r.type == "dynamic-pool" or r.type == "static-pool" or r.type == "mbuf-pool" or r.type == "share")
			|> filter(fn: (r) => (r.rfid =~ /tc_/ or r.rfscene =~ /tc_/) and r.rfid !~ /时间/ and r.rfscene !~ /时间/)
			|> group(columns: ["branch", "product", "type", "rfid", "rfscene", "name"])
			|>mean()
	b =
		from(bucket:"dfx-clean")
			|> range(start: -7d)
			|> filter(fn: (r) => r["_measurement"] == "memory")
			|> filter(fn: (r) => r._field == "kbytes")
			|> filter(fn: (r) => r.type == "process" or r.type == "hm-pool" or r.type == "dynamic-pool" or r.type == "static-pool" or r.type == "mbuf-pool" or r.type == "share")
			|> filter(fn: (r) => (r.rfid =~ /tc_/ or r.rfscene =~ /tc_/) and r.rfid !~ /时间/ and r.rfscene !~ /时间/)
			|> group(columns: ["branch", "product", "type", "rfid", "rfscene", "name"])
			|>mean()
	union(tables: [a, b])
		`),
	)
	if err != nil {
		logging.ErrorLogger.Errorf("influxdb query error: %s", err.Error())
		return errors.Wrap(err, "")
	}
	/*
		清洗流程：
		File ->
		select full_path from common_ntos_files where full_path  like "%FILE" ->

	*/
	// Iterate over query response
	// Check for an error
	if ret.Err() != nil {
		logging.ErrorLogger.Errorf("query parsing error: %s", ret.Err().Error())
		return errors.Wrap(ret.Err(), "")
	}

	resultMap := map[string]map[string]map[string]map[string]map[string]map[string][]map[string]interface{}{}

	var maxStop *time.Time
	for ret.Next() {
		record := ret.Record()
		var branch, product, _type, rfid, rfscene, name string
		var _stop time.Time
		var value float64

		branch = record.Values()["branch"].(string)
		if _, ok := resultMap[branch]; !ok {
			resultMap[branch] = map[string]map[string]map[string]map[string]map[string][]map[string]interface{}{}
		}

		product = record.Values()["product"].(string)
		if _, ok := resultMap[branch][product]; !ok {
			resultMap[branch][product] = map[string]map[string]map[string]map[string][]map[string]interface{}{}
		}

		rfid = record.Values()["rfid"].(string)
		if _, ok := resultMap[branch][product][rfid]; !ok {
			resultMap[branch][product][rfid] = map[string]map[string]map[string][]map[string]interface{}{}
		}

		rfscene = record.Values()["rfscene"].(string)
		if _, ok := resultMap[branch][product][rfid][rfscene]; !ok {
			resultMap[branch][product][rfid][rfscene] = map[string]map[string][]map[string]interface{}{}
		}

		_type = record.Values()["type"].(string)
		if _, ok := resultMap[branch][product][rfid][rfscene][_type]; !ok {
			resultMap[branch][product][rfid][rfscene][_type] = map[string][]map[string]interface{}{}
		}

		name = record.Values()["name"].(string)
		if _, ok := resultMap[branch][product][rfid][rfscene][_type][name]; !ok {
			resultMap[branch][product][rfid][rfscene][_type][name] = []map[string]interface{}{}
		}

		_stop = record.Values()["_stop"].(time.Time).UTC()
		if maxStop == nil {
			maxStop = &_stop
		} else {
			if maxStop.Sub(_stop) < 0 {
				maxStop = &_stop
			}
		}

		value = record.Values()["_value"].(float64)

		resultMap[branch][product][rfid][rfscene][_type][name] = append(resultMap[branch][product][rfid][rfscene][_type][name], map[string]interface{}{
			"Name":  name,
			"_Stop": _stop,
			"Value": value,
		})

	}

	for branch := range resultMap {
		if _, ok := result[branch]; !ok {
			result[branch] = map[string]map[string]map[string]map[string]map[string][][]interface{}{}
		}
		for product := range resultMap[branch] {
			if _, ok := result[branch][product]; !ok {
				result[branch][product] = map[string]map[string]map[string]map[string][][]interface{}{}
			}
			for rfid := range resultMap[branch][product] {
				if _, ok := result[branch][product][rfid]; !ok {
					result[branch][product][rfid] = map[string]map[string]map[string][][]interface{}{}
				}
				for rfscene := range resultMap[branch][product][rfid] {
					if _, ok := result[branch][product][rfid][rfscene]; !ok {
						result[branch][product][rfid][rfscene] = map[string]map[string][][]interface{}{}
					}
					for _type := range resultMap[branch][product][rfid][rfscene] {
						if _, ok := result[branch][product][rfid][rfscene][_type]; !ok {
							result[branch][product][rfid][rfscene][_type] = map[string][][]interface{}{
								"detail": {},
							}
						}
						for name := range resultMap[branch][product][rfid][rfscene][_type] {
							items := resultMap[branch][product][rfid][rfscene][_type][name]
							if len(items) > 1 {

								//计算增量
								time0 := items[0]["_Stop"].(time.Time)
								val0 := items[0]["Value"].(float64)
								time1 := items[1]["_Stop"].(time.Time)
								val1 := items[1]["Value"].(float64)

								if time1.Sub(time0) <= 0 {
									time0, time1 = time1, time0
									val0, val1 = val1, val0
								}

								if percent := (val1 - val0) * 100 / val0; percent >= threshold && val0 >= minVal {
									result[branch][product][rfid][rfscene][_type]["detail"] = append(
										result[branch][product][rfid][rfscene][_type]["detail"],
										[]interface{}{
											val1,
											fmt.Sprintf("检测到%s %s %s %s 内存类型：%s 指标:%s 当前值为: %.2f, 上周为: %.2f, 上升 %.2f%%",
												branch,
												product,
												rfid,
												rfscene,
												_type,
												name,
												val1,
												val0,
												percent,
											),
										},
									)
								}
							} else if len(items) == 1 {
								// 新增指标告警
								if *maxStop == items[0]["_Stop"].(time.Time) {
									result[branch][product][rfid][rfscene][_type]["detail"] = append(
										result[branch][product][rfid][rfscene][_type]["detail"],
										[]interface{}{
											items[0]["Value"].(float64),
											fmt.Sprintf("检测到%s %s %s %s 内存类型：%s 新增指标:%s 当前值为: %.2f",
												branch,
												product,
												rfid,
												rfscene,
												_type,
												name,
												items[0]["Value"].(float64),
											),
										},
									)

									// else {
									// 	fmt.Println(fmt.Sprintf("%s_%s_%s_%s", branch, _type, rfid, rfscene), "指标消失", name, items[0]["Value"].(float64))
									// }
								}
							}
						}
					}
				}
			}
		}
	}

	return nil
}

func DfxSumWeekCheck(result map[string]map[string]map[string]map[string]map[string]map[string][][]interface{}) error {
	var threshold float64 = 5
	// var minVal float64 = 1000
	queryAPI := InitQueryAPI()
	// Get QueryTableResult

	ret, err := queryAPI.Query(context.Background(), fmt.Sprintf(`
	a =
		from(bucket:"dfx-clean")
			|> range(start: -14d, stop: -7d)
			|> filter(fn: (r) => r["_measurement"] == "memory")
			|> filter(fn: (r) => r._field == "kbytes")
			|> filter(fn: (r) => r.type == "process" or r.type == "hm-pool" or r.type == "dynamic-pool" or r.type == "static-pool" or r.type == "mbuf-pool" or r.type == "share")
			|> filter(fn: (r) => (r.rfid =~ /tc_/ or r.rfscene =~ /tc_/) and r.rfid !~ /时间/ and r.rfscene !~ /时间/)
			|> group(columns: ["branch", "product", "type", "rfid", "rfscene", "name"])
			|> mean()
			|> group(columns: ["_stop", "branch", "product", "type", "rfid", "rfscene"])
			|> sum(column: "_value")
	b =
		from(bucket:"dfx-clean")
			|> range(start: -7d)
			|> filter(fn: (r) => r["_measurement"] == "memory")
			|> filter(fn: (r) => r._field == "kbytes")
			|> filter(fn: (r) => r.type == "process" or r.type == "hm-pool" or r.type == "dynamic-pool" or r.type == "static-pool" or r.type == "mbuf-pool" or r.type == "share")
			|> filter(fn: (r) => (r.rfid =~ /tc_/ or r.rfscene =~ /tc_/) and r.rfid !~ /时间/ and r.rfscene !~ /时间/)
			|> group(columns: ["branch", "product", "type", "rfid", "rfscene", "name"])
			|> mean()
			|> group(columns: ["_stop", "branch", "product", "type", "rfid", "rfscene"])
			|> sum(column: "_value")
	union(tables: [a, b])
		`),
	)
	if err != nil {
		logging.ErrorLogger.Errorf("influxdb query error: %s", err.Error())
		return errors.Wrap(err, "")
	}
	/*
		清洗流程：
		File ->
		select full_path from common_ntos_files where full_path  like "%FILE" ->

	*/
	// Iterate over query response
	// Check for an error
	if ret.Err() != nil {
		logging.ErrorLogger.Errorf("query parsing error: %s", ret.Err().Error())
		return errors.Wrap(ret.Err(), "")
	}

	resultMap := map[string]map[string]map[string]map[string]map[string][]map[string]interface{}{}
	var maxStop *time.Time
	for ret.Next() {
		record := ret.Record()
		var branch, product, _type, rfid, rfscene string
		var _stop time.Time
		var value float64

		branch = record.Values()["branch"].(string)
		if _, ok := resultMap[branch]; !ok {
			resultMap[branch] = map[string]map[string]map[string]map[string][]map[string]interface{}{}
		}

		product = record.Values()["product"].(string)
		if _, ok := resultMap[branch][product]; !ok {
			resultMap[branch][product] = map[string]map[string]map[string][]map[string]interface{}{}
		}

		rfid = record.Values()["rfid"].(string)
		if _, ok := resultMap[branch][product][rfid]; !ok {
			resultMap[branch][product][rfid] = map[string]map[string][]map[string]interface{}{}
		}

		rfscene = record.Values()["rfscene"].(string)
		if _, ok := resultMap[branch][product][rfid][rfscene]; !ok {
			resultMap[branch][product][rfid][rfscene] = map[string][]map[string]interface{}{}
		}

		_type = record.Values()["type"].(string)
		if _, ok := resultMap[branch][product][rfid][rfscene][_type]; !ok {
			resultMap[branch][product][rfid][rfscene][_type] = []map[string]interface{}{}
		}

		_stop = record.Values()["_stop"].(time.Time).UTC()

		if maxStop == nil {
			maxStop = &_stop
		} else {
			if maxStop.Sub(_stop) < 0 {
				maxStop = &_stop
			}
		}

		value = record.Values()["_value"].(float64)

		resultMap[branch][product][rfid][rfscene][_type] = append(resultMap[branch][product][rfid][rfscene][_type], map[string]interface{}{
			"Type":  _type,
			"_Stop": _stop,
			"Value": value,
		})

	}

	for branch := range resultMap {
		if _, ok := result[branch]; !ok {
			result[branch] = map[string]map[string]map[string]map[string]map[string][][]interface{}{}
		}
		for product := range resultMap[branch] {
			if _, ok := result[branch][product]; !ok {
				result[branch][product] = map[string]map[string]map[string]map[string][][]interface{}{}
			}
			for rfid := range resultMap[branch][product] {
				if _, ok := result[branch][product][rfid]; !ok {
					result[branch][product][rfid] = map[string]map[string]map[string][][]interface{}{}
				}
				for rfscene := range resultMap[branch][product][rfid] {
					if _, ok := result[branch][product][rfid][rfscene]; !ok {
						result[branch][product][rfid][rfscene] = map[string]map[string][][]interface{}{}
					}

					for _type := range resultMap[branch][product][rfid][rfscene] {
						if _, ok := result[branch][product][rfid][rfscene][_type]; !ok {
							result[branch][product][rfid][rfscene][_type] = map[string][][]interface{}{
								"summary": {},
							}
						}

						items := resultMap[branch][product][rfid][rfscene][_type]
						if len(items) > 1 {
							//计算增量
							time0 := items[0]["_Stop"].(time.Time)
							val0 := items[0]["Value"].(float64)
							time1 := items[1]["_Stop"].(time.Time)
							val1 := items[1]["Value"].(float64)

							if time1.Sub(time0) <= 0 {
								time0, time1 = time1, time0
								val0, val1 = val1, val0
							}

							if percent := (val1 - val0) * 100 / val0; true {
								result[branch][product][rfid][rfscene][_type]["summary"] = append(
									result[branch][product][rfid][rfscene][_type]["summary"],
									[]interface{}{
										val1,
										percent,
										threshold,
										fmt.Sprintf("检测到%s %s %s %s 内存类型：%s 指标:总和 当前值为: %.2f, 上周为: %.2f, 上升 %.2f%%",
											branch,
											product,
											rfid,
											rfscene,
											_type,
											val1,
											val0,
											percent,
										),
									},
								)
							}
						} else if len(items) == 1 {
							if *maxStop == items[0]["_Stop"].(time.Time) {
								result[branch][product][rfid][rfscene][_type]["summary"] = append(
									result[branch][product][rfid][rfscene][_type]["summary"],
									[]interface{}{
										items[0]["Value"].(float64),
										0,
										threshold,
										fmt.Sprintf("检测到%s %s %s %s 内存类型：%s 新增指标:总和 当前值为: %.2f",
											branch,
											product,
											rfid,
											rfscene,
											_type,
											items[0]["Value"].(float64),
										),
									},
								)
							}
						}
					}
				}
			}

		}
	}
	return nil
}
