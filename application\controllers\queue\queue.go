package queue

import (
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/cache"
	"strconv"
	"strings"

	"github.com/gomodule/redigo/redis"
	"github.com/kataras/iris/v12"
)

func GetQueue(ctx iris.Context) {
	rc := cache.GetRedisClusterClient()
	name := ctx.FormValue("name")
	count, _ := strconv.Atoi(ctx.FormValue("count"))
	ret, err := redis.Strings(rc.RPop(name, count))
	if err != nil {
		if !strings.Contains(err.Error(), "redigo: nil returned") {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, ret, response.NoErr.Msg))
	return
}

type EmailQueue struct {
	Key     string `json:"key"`
	From    string `json:"from"`
	To      string `json:"to"`
	Subject string `json:"subject"`
	Body    string `json:"body"`
	Cc      string `json:"cc"`
}

type EmailQueueList struct {
	EmailQueue
}

func PushQueue(ctx iris.Context) {
	req := []*EmailQueueList{}
	if err := ctx.ReadJSON(&req); err != nil {
		logging.ErrorLogger.Errorf("create gitjob read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	validErr := libs.Validate.Var(req, "required,gt=0,dive,required")
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	rc := cache.GetRedisClusterClient()
	for _, item := range req {
		msg := strings.Join([]string{item.From, item.To, item.Subject, item.Body, item.Cc}, "|")
		_, err := rc.LPush(item.Key, msg)
		if err != nil {
			logging.ErrorLogger.Error("push queue failed, %s, %s", item.Key, msg)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}
