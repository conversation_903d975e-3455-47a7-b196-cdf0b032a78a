package application

import (
	stdContext "context"
	"fmt"
	"net/http"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"

	"irisAdminApi/application/controllers/appmergerequest"
	"irisAdminApi/application/controllers/branchsearch"
	"irisAdminApi/application/controllers/bugapproval"
	"irisAdminApi/application/controllers/bugmirror"
	"irisAdminApi/application/controllers/bugsync"
	"irisAdminApi/application/controllers/buildfarm"
	"irisAdminApi/application/controllers/codesync"
	"irisAdminApi/application/controllers/common"
	"irisAdminApi/application/controllers/coredump"
	"irisAdminApi/application/controllers/datasync"
	"irisAdminApi/application/controllers/documentauto"
	"irisAdminApi/application/controllers/featurerelease"
	"irisAdminApi/application/controllers/featurerelease_11_x"
	"irisAdminApi/application/controllers/fileout"
	"irisAdminApi/application/controllers/firewallflextrans"
	"irisAdminApi/application/controllers/gcovreport"
	"irisAdminApi/application/controllers/gitlab"
	"irisAdminApi/application/controllers/inspection"
	"irisAdminApi/application/controllers/kpi"
	"irisAdminApi/application/controllers/license"
	"irisAdminApi/application/controllers/mergerequest"
	"irisAdminApi/application/controllers/opensource"
	"irisAdminApi/application/controllers/performance"
	"irisAdminApi/application/controllers/productionrelease"
	"irisAdminApi/application/controllers/qualitypoint"
	"irisAdminApi/application/controllers/queue"
	"irisAdminApi/application/controllers/release"
	"irisAdminApi/application/controllers/resource"
	"irisAdminApi/application/controllers/resourcepool"
	"irisAdminApi/application/controllers/sig"
	"irisAdminApi/application/controllers/urlpack"
	"irisAdminApi/application/controllers/user"

	"irisAdminApi/application/logging"
	"irisAdminApi/application/middleware"
	"irisAdminApi/service/cache"
	"irisAdminApi/service/schedule"

	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/middleware/pprof"
	"github.com/kataras/iris/v12/middleware/rate"
)

// HttpServer
type HttpServer struct {
	ConfigPath string
	App        *iris.Application
	Models     []interface{}
	Status     bool
}

func NewServer(config string) *HttpServer {
	app := iris.New()

	app.Logger().SetLevel(libs.Config.LogLevel)
	iris.RegisterOnInterrupt(func() {
		sql, _ := easygorm.GetEasyGormDb().DB()
		sql.Close()
	})
	httpServer := &HttpServer{
		ConfigPath: config,
		App:        app,
		Status:     false,
	}

	err := httpServer._Init()
	if err != nil {
		panic(err.Error())
	}
	return httpServer
}

// Start
func (s *HttpServer) Start() error {
	// if err := s.App.Run(
	// 	iris.Addr(fmt.Sprintf("%s:%d", libs.Config.Host, libs.Config.Port)),
	// 	iris.WithoutServerError(iris.ErrServerClosed),
	// 	iris.WithOptimizations,
	//  iris.WithTimeFormat(time.RFC3339),
	// ); err != nil {
	// 	return err
	// }
	config := iris.WithConfiguration(iris.Configuration{
		FireEmptyFormError:    false,
		DisablePathCorrection: true,
	})
	srv := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", libs.Config.Host, libs.Config.Port),
		ReadTimeout:  time.Duration(libs.Config.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(libs.Config.WriteTimeout) * time.Second,
	}
	if err := s.App.Run(iris.Server(srv), config); err != nil {
		return err
	}
	// s.Status = true
	return nil
}

// Start close the server at 3-6 seconds
func (s *HttpServer) Stop() {
	go func() {
		time.Sleep(3 * time.Second)
		ctx, cancel := stdContext.WithTimeout(stdContext.TODO(), 3*time.Second)
		defer cancel()
		s.App.Shutdown(ctx)
		s.Status = false
	}()
}

var bgCtx = stdContext.Background()

func (s *HttpServer) _Init() error {
	err := libs.InitConfig(s.ConfigPath)
	if err != nil {
		logging.ErrorLogger.Errorf("系统配置初始化失败:", err)
		return err
	}
	if libs.Config.Cache.Driver == "redis" {
		password := libs.Config.Redis.Password
		if libs.Config.Redis.Encrypt {
			_password, err := libs.RsaDecrypt(libs.Config.Redis.Password)
			if err != nil {
				logging.ErrorLogger.Errorf("系统配置初始化失败:", err)
				return err
			}
			password = string(_password)
		}
		cache.InitRedisCluster(libs.GetRedisUris(), password)
	}
	err = easygorm.Init(libs.GetGormConfig())
	if err != nil {
		logging.ErrorLogger.Errorf("数据库初始化失败:", err)
		return err
	}
	featurerelease.InitClient()
	gitlab.InitClient()
	bugsync.InitClient()
	datasync.InitClient()
	codesync.InitClient()
	mergerequest.InitClient()
	opensource.InitOpenSourceTaskManagers()
	license.InitTaskManagers()
	appmergerequest.InitClient()
	appmergerequest.InitGitLabClient()
	release.InitClient()

	// productionrelease.InitClient()
	// libs.InitData()
	s.RouteInit()
	// 测试
	go schedule.RunSchedule()
	return nil
}

// RouteInit
func (s *HttpServer) RouteInit() {
	s.App.UseRouter(middleware.CrsAuth())
	app := s.App.Party("/").AllowMethods(iris.MethodOptions)
	{

		// 开启 pprof 调试
		if libs.Config.Pprof {
			app.Get("/", func(ctx iris.Context) {
				ctx.HTML("<h1> Please click <a href='/debug/pprof'>here</a>")
			})

			p := pprof.New()
			app.Any("/debug/pprof", p)
			app.Any("/debug/pprof/{action:path}", p)
		}

		v1 := app.Party("api/v1")
		{
			// 是否开启接口请求频率限制
			if !libs.Config.Limit.Disable {
				limitV1 := rate.Limit(libs.Config.Limit.Limit, libs.Config.Limit.Burst, rate.PurgeEvery(time.Minute, 5*time.Minute))
				v1.Use(limitV1)
			}
			// v1.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) //登录验证
			v1.HandleDir("/upload", iris.Dir("./uploads"))

			// v1.Get("/users/sync", user.GetUsers).Name = "用户列表"
			v1.Get("/download", fileout.Download).Name = "文件下载"

			v1.Get("/queue", queue.GetQueue)
			v1.Post("/queue", queue.PushQueue)
			v1.Use(middleware.AccessLogger) // 访问日志收集
			v1.Get("/ignore_all_ai", mergerequest.IgnoreAllAi)

			if libs.Config.Gcov.Enable {
				v1.PartyFunc("/gcov", func(party iris.Party) {
					party.Get("/report", gcovreport.GetReport).Name = "查看文件覆盖率报告"
					party.Get("/components", gcovreport.GetCommonComponents).Name = "获取组件表"
					party.Get("/file/raw", gcovreport.GetFileRaw).Name = "获取文件内容"
					party.Get("/workpackages", gcovreport.GetProjectWorkPackageFiles).Name = "获取工作包"
					party.Get("/trunklogs", gcovreport.GetTrunkLogs).Name = "获取Trunk历史"
				})
			}

			v1.PartyFunc("/inspection", func(party iris.Party) {
				party.Post("/upload", inspection.Upload).Name = "巡检日志上传"
				party.Get("/jobs", inspection.Jobs).Name = "巡检记录"
				party.Get("/jobs/{id:uint}/logs", inspection.Logs).Name = "巡检记录"

				// party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) //登录验证

				party.Get("/jobs/{id:uint}/download", inspection.DownloadPDF).Name = "下载巡检报告"
			})

			v1.PartyFunc("/common", func(party iris.Party) {
				party.Get("/check_work_day", common.CheckWorkDay).Name = "检查是否工作日"
			})

			v1.PartyFunc("/resource", func(party iris.Party) {
				party.Get("/export", resource.ExportResource).Name = "资源详情导出"
				party.Get("/group/export", resource.ExportGroupResource).Name = "资源详情导出"
			})

			v1.PartyFunc("/datasync", func(party iris.Party) {
				party.Get("/bug", datasync.StartBugSync).Name = "手动同步最新bug数据"
				party.Post("/update_bug_cafid", datasync.UpdateBugCafID).Name = "更新BUG CafID"
				party.Get("/basedata", datasync.StartSyncFeishuBaseData).Name = "手动同步基础数据"
				party.Get("/getbuginfo", datasync.GetBugInfoAPI).Name = "获取Bug信息"
			})

			if libs.Config.Sig.Enable {
				v1.PartyFunc("/techsupport", func(party iris.Party) {
					party.Post("/jobs", sig.CreateTechSupportDecryptJob).Name = "创建作业"
					party.Get("/jobs", sig.GetTechSupportDecryptJobs).Name = "查看记录"
					party.Get("/jobs/{id:uint}", sig.GetTechSupportDecryptJob).Name = "查看单个记录"
					party.Get("/jobs/{id:uint}/{filename:string}", sig.DownloadTechSupportDecryptFile).Name = "下载加密文件"
					party.Get("/tools/{goos:string}/check", sig.CheckTechSupportDecryptToolVersion).Name = "检查版本"
					party.Get("/tools/{goos:string}/sigcli", sig.DownloadTechSupportDecryptTool).Name = "下载命令行工具"
					party.Get("/jobs/{id:uint}/log", sig.DownloadTechSupportDecryptLog).Name = "下载加密文件"

					party.Get("/perfresults", sig.GetPerfAnalysisResults).Name = "获取性能分析结果列表"
					party.Get("/perfresults/{id:uint}", sig.GetPerfAnalysisResult).Name = "获取单个性能分析结果"
					party.Get("/perfresults/{id:uint}/download", sig.DownloadPerfAnalysisResult).Name = "下载性能分析结果文件"
				})

				v1.PartyFunc("/sig", func(party iris.Party) {
					party.Post("/jobs", sig.CreateSigJob).Name = "创建作业"
					party.Get("/jobs", sig.GetSigJobs).Name = "查看记录"
					party.Get("/jobs/{id:uint}", sig.GetSigJob).Name = "查看单个记录"
					party.Get("/jobs/{id:uint}/{filename:string}", sig.DownloadSigFile).Name = "下载加密文件"
					party.Get("/tools/{goos:string}/check", sig.CheckSigToolVersion).Name = "检查版本"
					party.Get("/tools/{goos:string}/sigcli", sig.DownloadSigTool).Name = "下载命令行工具"
					party.Get("/jobs/{id:uint}/log", sig.DownloadSigLog).Name = "下载加密文件"
				})
			}

			v1.PartyFunc("/performance", func(party iris.Party) {
				party.Post("/ntos", performance.CreatePerformance).Name = "创建记录"
				party.Get("/ntos", performance.GetPerformances).Name = "查看记录"
				party.Post("/ntos/2544", performance.CreatePerformance2544).Name = "创建记录"
				party.Get("/ntos/2544", performance.GetPerformances2544).Name = "查看记录"
			})

			v1.PartyFunc("/mr", mergerequest.Party)
			v1.PartyFunc("/urlpack", urlpack.Party)
			v1.PartyFunc("/farm", buildfarm.Party)
			v1.PartyFunc("/release", release.Party)

			v1.PartyFunc("/appmr", func(party iris.Party) {
				party.Post("/systemhook", appmergerequest.SystemHook).Name = "同步MR表单"
				party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) // 登录验证
				party.Post("/mergerequests", appmergerequest.CreateMergeRequest).Name = "创建MR表单"
				party.Get("/mergerequests", appmergerequest.GetMergeRequests).Name = "获取MR表单列表"
				party.Get("/mergerequests2", appmergerequest.GetMergeRequestsV2).Name = "获取MR表单列表2"
				party.Get("/mergerequests/{id:uint}", appmergerequest.GetMergeRequest).Name = "获取MR表单详情"
				party.Get("/bugs/mirrors", bugmirror.GetBugs).Name = "获取所有BUG"
				party.Get("/bugs/branches", bugmirror.GetBranchUpdatedAt).Name = "获取分支创建时间"
				party.Get("/bugs", appmergerequest.GetBugs).Name = "获取Bug列表"
				party.Get("/bugs/{id:uint}", appmergerequest.GetBug).Name = "获取Bug详情"
				party.Post("/bugs/{id:uint}/mirror", appmergerequest.CreateBugMirror).Name = "镜像BUG"
				party.Post("/bugs/mirror/ignore", appmergerequest.IgnoreBugMirror).Name = "忽略镜像BUG"
				party.Post("/bugs/mirror/ignore/history", appmergerequest.GetBugMirrorApprovalHistory).Name = "忽略镜像BUG历史"
				party.Post("/bugs/mirror/ignore/cancel", appmergerequest.CancelIgnoreBugMirror).Name = "忽略镜像BUG"
				party.Get("/mergerequests/{id:uint}/weburl", appmergerequest.GetMergeRequestWebUrl).Name = "获取MR URL"
				party.Get("/releaseprojects", appmergerequest.GetReleaseProjects).Name = "获取在行项目列表"
				party.Get("/unitpackages", appmergerequest.GetUnitPackages).Name = "组件列表"
				party.Get("/releaseprojects/{id:uint}/branches", appmergerequest.GetTargetBranches).Name = "获取仓库分支"
				party.Get("/releaseprojects/{id:uint}/testbranches", appmergerequest.GetTestTargetBranches).Name = "获取测试分支"
				party.Get("/releaseprojects/{id:uint}/workpackages", appmergerequest.GetWorkPackages).Name = "工作包列表"
				party.Get("/releaseprojects/{id:uint}/distinctcolumn", appmergerequest.GetDistinctColumns).Name = "获取工作包需求"
				party.Post("/releaseprojects/{id:uint}/workpackages", appmergerequest.ImportWorkPackage).Name = "导入工作包"
				party.Get("/projects/{id:uint}/personrepositories", appmergerequest.GetPersonRepositories).Name = "获取个人仓库"
				party.Get("/projects/{id:uint}/repositories/branches", appmergerequest.GetBranches).Name = "获取仓库分支"
				party.Get("/projects/{id:uint}/repositories/members", appmergerequest.GetMembers).Name = "获取仓库成员"
				party.Get("/phabricator/differentials", appmergerequest.GetPhabricatorUrls).Name = "获取Phabricator Diff表单"
				party.Get("/summary/workpackages", appmergerequest.WorkPackageSummary).Name = "获取工作包统计"
				party.Get("/summary/workpackages/columns", appmergerequest.WorkPackageSumaryColumns).Name = "获取工作包统计"
				party.Get("/summary/bugs", appmergerequest.BugSummary).Name = "获取BUG统计"
				party.Get("/summary/bugs/distinctcolumn", appmergerequest.GetBugDistinctColumns).Name = "获取工作包需求"
				party.Get("/summary/bugs/columns", appmergerequest.BugSumaryColumns).Name = "获取BUG统计字段"

				party.Get("/summary/bugs/workgroup", appmergerequest.BugWorkGroupSummary).Name = "获取BUG统计"
				party.Get("/summary/bugs/workgroup/columns", appmergerequest.BugWorkGroupSumaryColumns).Name = "获取BUG统计字段"

				party.Get("/summary/bugs/owner", appmergerequest.BugOwnerSummary).Name = "获取BUG统计"
				party.Get("/summary/bugs/owner/columns", appmergerequest.BugOwnerSumaryColumns).Name = "获取BUG统计字段"

				party.Get("/summary/bugs/top/owner", appmergerequest.BugOwnerDelayTop).Name = "获取BUG超时时长统计"
				party.Get("/summary/bugs/top/group", appmergerequest.BugOwnerGroupDelayTop).Name = "获取BUG超时时长统计"

				party.Get("/bugapprovals", bugapproval.GetBugApprovals).Name = "获取BUG申请单"
				party.Get("/bugapprovals/{id:uint}", bugapproval.GetBugApproval).Name = "获取BUG申请单详情"
				party.Delete("/bugapprovals/{id:uint}", bugapproval.DeleteBugApproval).Name = "删除BUG申请单"
				party.Post("/bugapprovals", bugapproval.CreateBugApproval).Name = "创建BUG申请单"
				party.Post("/bugapprovals/{id:uint}/audit", bugapproval.AuditBugApproval).Name = "审批申请单"
				party.Get("/bugapprovals/{id:uint}/bug", bugapproval.GetBug).Name = "导入BUG"

				party.Post("/uploadImage", appmergerequest.UploadImage).Name = "上传图片"
				party.Get("/showImage/{id:uint}", appmergerequest.GetImages).Name = "查看图片"
				party.Get("/releaseprojects/{id:uint}/deploybranches", appmergerequest.GetDeployTargetBranches).Name = "获取部署目标仓库分支"
				party.Get("/projects/{id:uint}/repositories/deploybranches", appmergerequest.GetDeployBranches).Name = "获取部署源仓库分支"
				party.Get("/deployMergerequests", appmergerequest.GetMergeRequestsV3).Name = "获取部署MR表单列表"
				party.Post("/deployMergerequests", appmergerequest.CreateDeployMergeRequest).Name = "创建部署MR表单"

				party.Get("/projects/{id:uint}/dependencys", appmergerequest.GetDependencys).Name = "获取依赖MR表单列表"
			})

			v1.PartyFunc("/resourcepool", func(party iris.Party) {
				party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) // 登录验证
				party.Get("/monitors", resourcepool.GetMonitors).Name = "获取监控列表"
				party.Post("/monitors", resourcepool.CreateMonitor).Name = "创建监控记录"
				party.Post("/monitors/{id:uint}", resourcepool.UpdateMonitor).Name = "更新监控记录"
				party.Post("/monitors/{id:uint}/flag", resourcepool.UpdateMonitorFlag).Name = "更新监控记录"
				party.Delete("/monitors/{id:uint}", resourcepool.DeleteMonitor).Name = "删除监控记录"

				party.Get("/interfaces", resourcepool.GetInterfaces).Name = "获取设备列表"
				party.Post("/interfaces", resourcepool.CreateInterface).Name = "创建设备记录"
				party.Post("/interfaces/{id:uint}", resourcepool.UpdateInterface).Name = "更新设备记录"
				party.Delete("/interfaces/{id:uint}", resourcepool.DeleteInterface).Name = "删除设备记录"
				party.Post("/switchport/{id:uint}/", resourcepool.SwitchPort).Name = "更新端口状态"
				party.Post("/creatcon/", resourcepool.Creatcon).Name = "建立连接"
				party.Get("/getconbyname", resourcepool.GetConByName).Name = "获取设备对应的所有接口"

				party.Get("/resources", resourcepool.GetResources).Name = "获取资源列表"
				party.Get("/resourcesruijie", resourcepool.GetResourcesRuiJie).Name = "获取锐捷资源列表"
				party.Get("/resourcesfriend", resourcepool.GetResourcesFriend).Name = "获取友商资源列表"

				party.Get("/resourcesname", resourcepool.GetAllResourcesName).Name = "获取全部的设备信息"
				party.Get("/resourcesusers", resourcepool.GetAllUsers).Name = "获取全部的人员信息"

				party.Post("/resources", resourcepool.CreateResource).Name = "创建资源记录"
				party.Post("/resources/{id:uint}", resourcepool.UpdateResource).Name = "更新资源记录"
				party.Post("/resources/{id:uint}/flag", resourcepool.UpdateResourceFlag).Name = "更新资源记录"
				party.Delete("/resources/{id:uint}", resourcepool.DeleteResource).Name = "删除资源记录"

				party.Post("/resources/{id:uint}/console_clear", resourcepool.ConsoleClear).Name = "清空串口"
				party.Get("/myresources", resourcepool.GetMyResources).Name = "获取个人资源列表"
				party.Post("/getnewresources/{id:uint}", resourcepool.GetNewResource).Name = "占用资源"
				party.Post("/dropresources/{id:uint}", resourcepool.ReleaseResource).Name = "解除资源"
				party.Post("/giveresources/{id:uint}/reserved", resourcepool.GiveResource).Name = "转移资源"

				party.Post("/getnewresources", resourcepool.GetNewResources).Name = "占用资源"
				party.Post("/dropresources", resourcepool.ReleaseResources).Name = "解除资源"
				party.Post("/giveresources", resourcepool.GiveResources).Name = "转移资源"
				party.Post("/reyee/switchtop", resourcepool.SwitchReyeeTop).Name = "切换Top"
				party.Post("/reyee/switchtop/status", resourcepool.GetReyeeSwitchTopStatus).Name = "切换Top状态"
			})

			v1.PartyFunc("/search", func(party iris.Party) {
				party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) // 登录验证
				party.Get("/branchs", branchsearch.SearchBranch).Name = "搜索分支"
				party.Get("/projects", branchsearch.GetProjects).Name = "获取项目"
			})

			v1.PartyFunc("/coredump", func(party iris.Party) {
				party.PartyFunc("/files", func(party iris.Party) {
					party.Get("/{root:path}", coredump.GetCoredumpFiles).Name = "coredump文件浏览和下载"
				})
				party.Get("/product2buildname", coredump.TransProductToBuildname).Name = "型号转编译工程名称"
				party.Get("/jobs/{id:uint}/log", coredump.GetLog).Name = "查看堆栈日志"
				party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) // 登录验证
				party.Post("/techsupports", coredump.CreateCoredumpTechSupport).Name = "上传tech_support文件"
				party.Get("/techsupports", coredump.GetCoredumpTechSupports).Name = "获取tech_support文件记录"
				party.Get("/techsupports/{id:uint}/coredumps", coredump.GetCoredumps).Name = "获取tech_support文件记录"
				party.Get("/techsupports/{id:uint}/program", coredump.GetProgram).Name = "获取应用文件记录"
				party.Post("/techsupports/{id:uint}/bt", coredump.GetBT).Name = "创建获取堆栈作业"
				party.Get("/jobs/", coredump.GetCoredumpJobs).Name = "获取作业记录"
				party.Get("/techsupports/{id:uint}/coredumpjobs", coredump.GetCoredumpJobsByTechSupportID).Name = "查看单个tech_support的作业记录"
				party.Post("/techsupportbyDecrypt", coredump.CreateCoredumpByTechsupportDecrypt).Name = "一键解密文件转换为coredump文件"
				party.Get("/techsupports/{id:uint}/log", coredump.GetTechsupportLog).Name = "查看tech_support文件创建日志"
			})
			v1.PartyFunc("/codesync", func(party iris.Party) {
				party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) // 登录验证
				party.Get("/policies", codesync.GetCodeSyncPolicies).Name = "获取所有同步策略"
				party.Post("/policies", codesync.CreateCodeSyncPolicy).Name = "创建同步策略"
				party.Get("/policies/{id:uint}", codesync.GetCodeSyncPolicy).Name = "获取同步策略详情"
				party.Post("/policies/{id:uint}", codesync.UpdateCodeSyncPolicy).Name = "更新同步策略"
				party.Delete("/policies/{id:uint}", codesync.DeleteCodeSyncPolicy).Name = "删除同步策略"
				party.Post("/queues", codesync.GetCodeSyncQueues).Name = "获取队列"
				party.Post("/queues/{id:uint}", codesync.UpdateCodeSyncQueue).Name = "更新队列同步MR"
				party.Get("/projects", codesync.GetCodeSyncProjects).Name = "获取所有仓库"
				party.Post("/projects", codesync.CreateCodeSyncProject).Name = "添加仓库"
				party.Get("/projects/{id:uint}", codesync.GetCodeSyncProjects).Name = "获取仓库详情"
				party.Post("/projects/{id:uint}", codesync.UpdateCodeSyncProject).Name = "更新仓库"
				party.Delete("/projects/{id:uint}", codesync.DeleteCodeSyncProject).Name = "删除仓库"
			})
			v1.PartyFunc("/bugsync", func(party iris.Party) {
				party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) // 登录验证
				party.Get("/bugs", bugmirror.GetBugs).Name = "获取所有BUG"
				party.Get("/summary", bugsync.GetBugSummarys).Name = "获取所有BUG统计"

				party.Get("/filter", bugsync.GetFilter).Name = "获取过滤条件"
			})
			if libs.Config.Feature.Enable {
				v1.PartyFunc("/feature", func(party iris.Party) {
					party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) // 登录验证
					party.Get("/productmodels/fromseccloud", featurerelease.GetProductModelsFromSecCloudV2).Name = "获取云端产品型号"
					party.Get("/softversions/fromseccloud", featurerelease.GetSoftVersionsFromSecCloudV2).Name = "获取云端软件版本号"
					party.Get("/procdef", featurerelease.GetProcDef).Name = "获取发布流程"
					party.Get("/procinst/{id:uint}/prevnodes", featurerelease.GetProcPrevNodes).Name = "获取发布流程"
					party.Post("/releases", featurerelease.CreateProcInst).Name = "创建规则库记录"
					party.Get("/releases", featurerelease.GetFeatureProcInsts).Name = "获取规则库列表"
					party.Get("/tasks", featurerelease.GetFeatureProcTasks).Name = "获取任务列表"
					party.Post("/releases/{id:uint}", featurerelease.UpdateProcInst).Name = "发布规则库"
					party.Get("/releases/{id:uint}", featurerelease.GetProcInst).Name = "获取规则库详情"
					party.Get("/{id:uint}/tasks", featurerelease.GetFeatureProcInstTasks).Name = "获取进展详情"
					party.Get("/{id:uint}/descfile", featurerelease.DownloadDescFile).Name = "下载描述文件"
					party.Get("/{id:uint}/featurefile", featurerelease.DownloadFeatureFile).Name = "下载规则库文件"
					party.Get("/{id:uint}/testreport", featurerelease.DownloadTestReport).Name = "下载描述文件"
					party.Get("/{id:uint}/expreport", featurerelease.DownloadExpReport).Name = "下载描述文件"
					party.Post("/detection", featurerelease.CreateDetection).Name = "创建规则库检测记录"
					party.Get("/detection", featurerelease.GetDetectionList).Name = "获取规则库检测记录列表"
					party.Get("/detection/todolist", featurerelease.GetDetectionTodoList).Name = "获取规则库正在检测任务列表"
					party.Get("/detection/donelist", featurerelease.GetDetectionDoneList).Name = "获取规则库完成检测任务列表"
					party.Get("/detection/{id:uint}", featurerelease.GetDetectionDetail).Name = "获取规则库检测任务详情"
					party.Get("/detection/{id:uint}/logfile", featurerelease.DownloadDetectionLogFile).Name = "下载检测日志文件"
					party.Get("/detection/toolinfo", featurerelease.GetDetectionTodoInfo).Name = "获取工具信息"
					party.Get("/secclouds", featurerelease.GetSecClouds).Name = "获取所有安全云"
					party.Get("/gray/rules", featurerelease.GetGrayRules).Name = "获取灰度规则列表"
					party.Post("/gray/save", featurerelease.CreateGrayRule).Name = "创建灰度规则"
				})
				v1.PartyFunc("/feature/11_x", func(party iris.Party) {
					party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) // 登录验证
					// party.Get("/productmodels/fromseccloud", featurerelease_11_x.GetProductModelsFromSecCloud).Name = "获取云端产品型号"
					party.Get("/procdef", featurerelease_11_x.GetProcDef).Name = "获取发布流程"
					party.Get("/procinst/prevnodes", featurerelease_11_x.GetProcPrevNodes).Name = "获取发布流程"
					party.Post("/releases", featurerelease_11_x.CreateProcInst).Name = "创建规则库记录"
					party.Get("/releases", featurerelease_11_x.GetFeatureProcInsts).Name = "获取规则库列表"
					party.Get("/tasks", featurerelease_11_x.GetFeatureProcTasks).Name = "获取任务列表"
					party.Post("/releases/{id:uint}", featurerelease_11_x.UpdateProcInst).Name = "发布规则库"
					party.Get("/releases/{id:uint}", featurerelease_11_x.GetProcInst).Name = "获取规则库详情"
					party.Get("/{id:uint}/tasks", featurerelease_11_x.GetFeatureProcInstTasks).Name = "获取进展详情"
					party.Get("/{id:uint}/descfile", featurerelease_11_x.DownloadDescFile).Name = "下载描述文件"
					party.Get("/{id:uint}/featurefile", featurerelease_11_x.DownloadFeatureFile).Name = "下载描述文件"
					party.Get("/{id:uint}/testreport", featurerelease_11_x.DownloadTestReport).Name = "下载描述文件"
					party.Get("/{id:uint}/expreport", featurerelease_11_x.DownloadExpReport).Name = "下载描述文件"
				})
			}

			if libs.Config.Kpi.Eanble {
				v1.PartyFunc("/kpi", func(party iris.Party) {
					party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) // 登录验证
					party.Post("/contributions", kpi.CreateContribution).Name = "创建贡献记录"
					party.Get("/contributions", kpi.GetContributions).Name = "获取贡献列表"
					party.Get("/contributions/mine", kpi.GetMyContributions).Name = "获取贡献列表"
					party.Get("/contributions/{id:uint}", kpi.GetContribution).Name = "获取贡献详情"
					party.Post("/contributions/{id:uint}", kpi.UpdateContribution).Name = "更新贡献记录"
					party.Post("/contribution/reviews/{id:uint}", kpi.UpdateReview).Name = "更新贡献评审记录"
					party.Get("/contribution/reviews", kpi.GetMyReviews).Name = "获取贡献评审列表"
					party.Get("/contributions/{id:uint}/reviews", kpi.GetReviews).Name = "获取贡献评审记录"
					party.Get("/contributions/{id:uint}/points", kpi.GetPoints).Name = "获取贡献分数记录"
					party.Get("/contribution/reviews/{id:uint}", kpi.GetReview).Name = "获取贡献评审详情"
					party.Get("/contribution/types", kpi.GetContributionTypes).Name = "获取贡献类型"
					party.Post("/problems", kpi.CreateProblem).Name = "创建问题记录"
					party.Post("/problems/{id:uint}/proccesses", kpi.CreateProblemProccessV2).Name = "创建问题进展"
					party.Get("/problems/{id:uint}/proccesses", kpi.GetProblemProccessses).Name = "获取问题进展"
					party.Get("/problems", kpi.GetProblems).Name = "获取问题列表"
					party.Get("/problems/export", kpi.ExportProblem).Name = "导出问题列表"
					party.Get("/problems/{id:uint}", kpi.GetProblem).Name = "获取问题详情"
					party.Post("/problems/{id:uint}", kpi.UpdateProblem).Name = "更新问题详情"
					party.Delete("/problems/{id:uint}", kpi.DeleteProblem).Name = "删除问题记录"
					party.Get("/problem/sources", kpi.GetProblemSources).Name = "获取贡献类型"
					party.Get("/problems/mine", kpi.GetMyProblem).Name = "获取贡献列表"
					party.Get("/problems/mysubmit", kpi.GetMySubmitProblem).Name = "获取贡献列表"
					party.Get("/attachment/{id:uint}", kpi.Download).Name = "下载附件"
					party.Get("/dashboard/problem", kpi.DashboardProblem).Name = "问题统计"
					party.Get("/dashboard/contribution", kpi.DashboardContribution).Name = "贡献统计"
					party.Get("/dashboard/user", kpi.DashboardUser).Name = "个人统计"
				})
			}

			v1.PartyFunc("/coverity", func(party iris.Party) {
				party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) // 登录验证
				party.Get("/builds", buildfarm.GetCovAllBuilds).Name = "获取所有构建"
				party.Get("/builds/last", buildfarm.GetCovLastBuild).Name = "获取最新构建"
				party.Get("/builds/{id:uint}/output", buildfarm.GetCovBuildOutput).Name = "获取构建输出"
				party.Get("/builds/{id:uint}/stop", buildfarm.StopCovBuild).Name = "停止构建"
				party.Post("/builds/start", buildfarm.StartCovBuild).Name = "提交构建请求"
			})
			v1.PartyFunc("/gitlab", func(party iris.Party) {
				party.Post("/systemhook", gitlab.SystemHook).Name = "System Hook"
				party.Post("/projects/{project_id:string}/merge_requests/{merge_request_iid:string}/discussion", gitlab.MergeRequestDiscussions).Name = "Discussion"
				party.Get("/projects/{project_id:string}/merge_requests/{merge_request_iid:string}", gitlab.GetMergeRequset).Name = "获取MergeRequest详情"

				party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) // 登录验证
				party.Get("/project", gitlab.GetUserProjects).Name = "查看我的Gitlab项目"
				party.Get("/group", gitlab.GetUserGoups).Name = "查看Gitlab组"
				party.Get("/token", gitlab.GetUserToken).Name = "查看Gitlab Token"
				party.Post("/token", gitlab.CreateUserToken).Name = "添加Gitlab Token"
				party.Get("/project/{id:uint}/issue", gitlab.GetProjectIssues).Name = "查看Gitlab项目Issue"
				party.Get("/project/{id:uint}/issue/{issue_id:uint}/note", gitlab.GetProjectIssueNotes).Name = "查看Gitlab项目Issue Notes"
				party.Get("/project/{id:uint}/mr", gitlab.GetProjectMRs).Name = "查看Gitlab项目MR"
				party.Get("/project/{id:uint}/mr/{mr_id:uint}/note", gitlab.GetProjectMRNotes).Name = "查看Gitlab项目MR Notes"
				party.Get("/project/{id:uint}/commit", gitlab.GetProjectCommits).Name = "查看Gitlab项目Commit"
				party.Get("/commits", gitlab.GetProjectCommitsV2).Name = "查看Gitlab项目Commit"
				party.Get("/favorites", gitlab.GetUserRepositoryFavorates).Name = "获取用户收藏夹列表"
				party.Post("/favorites", gitlab.CreateUserRepositoryFavorates).Name = "新增收藏"
				party.Delete("/favorites/{id:uint}", gitlab.DeleteUserRepositoryFavorates).Name = "删除收藏"
				party.Get("/project/{id:uint}/branch", gitlab.GetProjectBranches).Name = "查看Gitlab项目Branch"
				party.Get("/group/{id:uint}/issue", gitlab.GetGroupIssues).Name = "查看Gitlab组Issue"
				party.Get("/group/{id:uint}/mr", gitlab.GetGroupMRs).Name = "查看Gitlab组MR"
				party.Get("/issue", gitlab.GetUserAllIssues).Name = "查看所有Issue"
				party.Get("/mr", gitlab.GetAllMRs).Name = "查看所有MR"
				party.Get("/project/{id:uint}/note/export", gitlab.ExportAllMRNotes).Name = "导出所有评审记录"
				party.Get("/project/{id:uint}/mr/export", gitlab.ExportAllMRs).Name = "导出MR记录"
			})

			v1.PartyFunc("/approval", func(party iris.Party) {
				party.Get("/download/index/{id:uint}", fileout.DownloadByNameAndIndex).Name = "使用索引下载用户文件"
				party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) // 登录验证
				party.Post("/", fileout.CreateApprovalV4).Name = "创建申请单"
				party.Get("/check", fileout.CheckApproval).Name = "检查文件是否已存在"
				party.Get("/mine", fileout.GetApprovals).Name = "查看我的申请单"
				party.Get("/audit", fileout.GetAllApprovals).Name = "查看我的审批单"
				party.Get("/audited", fileout.GetAllAuditedApprovals).Name = "查看我的历史审批单"
				party.Get("/download/{id:uint}", fileout.CheckDownload).Name = "下载文件"
				party.Get("/auditor", user.GetAuditors).Name = "获取审批人"
				party.Get("/allauditor", user.GetAllAuditors).Name = "获取所有审批人"
				party.Get("/checkupdate", fileout.CheckUpdateApprovalV2).Name = "检查是否弹窗"
				party.Post("/{id:uint}", fileout.UpdateApprovalV2).Name = "审批申请单"
				party.Post("/{id:uint}/updateauditor", fileout.ChangeAuditor).Name = "修改审批人"
				party.Get("/filedetail/{id:uint}", fileout.GetAllFileDetailsV2).Name = "文件异常详情"
				party.Get("/comment/{id:uint}", fileout.GetApprovalComments).Name = "申请说明"
				party.Get("/share/{id:uint}", fileout.GetApprovalShares).Name = "分享列表"
				party.Delete("/{id:uint}", fileout.DeleteApproval).Name = "删除申请单"
				party.Get("/shareuser", fileout.GetShareUsers).Name = "分享用户列表"
				party.Get("/share", fileout.GetShareApprovals).Name = "分享文件列表"
				party.Post("/fragment", fileout.CreateFragment).Name = "创建文本片段外出"
				party.Get("/fragment", fileout.GetFragments).Name = "获取文本片段外出"
			})

			v1.PartyFunc("/manage", func(party iris.Party) { // casbin for gorm                                                   // <- IMPORTANT, register the middleware.
				party.Post("/farm/crontab/check", buildfarm.CheckCronTab).Name = "检查计划任务并发"
				// party.Post("/farm/projects/updateinfo", buildfarm.ManualUpdateProjectsInfo).Name = "刷新所有项目信息缓存"
				// party.Post("/farm/projects/{id:uint}/updateinfo", buildfarm.ManualUpdateProjectInfo).Name = "刷新项目信息缓存"
				party.Get("/farm/coverity/crontabs", buildfarm.GetCronCoverityTabs).Name = "获取每日coverity计划任务"
				party.Get("/farm/coverity/windows", buildfarm.GetCronCoverityWindows).Name = "获取每日coverity窗口"
				party.Get("/farm/coverity/summary", buildfarm.GetCronCoverityWindowSummary).Name = "获取每日coverity概览"
				party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) // 登录验证

				party.Post("/farm/coverity/crontabs", buildfarm.CreateCoverityTab).Name = "创建每日coverity计划任务"
				party.Delete("/farm/coverity/crontabs", buildfarm.DeleteCoverityTab).Name = "删除每日coverity计划任务"

				party.Get("/autoauditfile", fileout.GetAllAutoAuditFiles).Name = "查看所有自动审批文件类型"
				party.Post("/autoauditfile", fileout.CreateAutoAuditFile).Name = "创建自动审批文件类型"
				party.Delete("/autoauditfile/{id:uint}", fileout.DeleteAutoAuditFile).Name = "删除自动审批文件类型"
				party.Post("/autoauditfile/{id:uint}", fileout.UpdateAutoAuditFile).Name = "更新自动审批文件类型"
				party.Post("/farm/crontab", buildfarm.CreateCronTab).Name = "添加编译计划任务"
				party.Get("/farm/crontab", buildfarm.GetCrontabs).Name = "查看编译计划任务"
				party.Post("/farm/crontab/{id:uint}", buildfarm.UpdateCrontab).Name = "更新编译计划任务"
				party.Delete("/farm/crontab/{id:uint}", buildfarm.DeleteCrontab).Name = "删除编译计划任务"

				party.Post("/farm/crontab/{id:uint}/start", buildfarm.StartCronMakeJobImmediateByCronTab).Name = "立即编译计划任务"

				party.Post("/farm/crontab/start", buildfarm.StartCronMakeJobImmediate).Name = "立即编译计划任务"
				party.Post("/farm/crontab/stop", buildfarm.StopCronMakeJobImmediateV3).Name = "立即停止编译计划任务"

				party.Post("/farm/crontab/stopall", buildfarm.StopAllCronMakeJobImmediateV2).Name = "立即停止所有每日编译"
				party.Get("/farm/project", buildfarm.GetProjects).Name = "获取编译项目"
				party.Post("/farm/project", buildfarm.CreateProjects).Name = "创建编译项目"
				party.Post("/farm/project/{id:uint}", buildfarm.UpdateProject).Name = "更新编译项目"
				party.Delete("/farm/project/{id:uint}", buildfarm.DeleteProject).Name = "删除编译项目"
				party.Get("/farm/auditblacklists", user.GetAllAutoAuditBlackLists).Name = "获取黑名单清单"
				party.Post("/farm/auditblacklists", user.CreateOrUpdateAutoAuditBlackList).Name = "创建黑名单"
				party.Post("/farm/auditblacklists/{id:uint}", user.CreateOrUpdateAutoAuditBlackList).Name = "更新黑名单"
				party.Delete("/farm/auditblacklists/{id:uint}", user.GetAllAutoAuditBlackLists).Name = "删除黑名单"

				// party.Post("/casbin/reload")
			})

			v1.PartyFunc("/audit", func(party iris.Party) { // casbin for gorm                                                   // <- IMPORTANT, register the middleware.
				party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) // 登录验证
				party.Get("/approval", fileout.AuditAllApprovals).Name = "查看所有已审批申请"
				party.Get("/fragment", fileout.AuditAllFragments).Name = "查看所有文本外出"
			})

			v1.PartyFunc("/user", user.Party)

			v1.PartyFunc("/opensource", func(party iris.Party) {
				party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) // 登录验证
				// components
				party.Get("/components", opensource.ListComponents).Name = "开源组件列表查询"
				party.Get("/components/{id:uint}", opensource.GetComponent).Name = "获取某个开源组件详情"
				party.Post("/components", opensource.CreateComponent).Name = "创建开源组件"
				party.Put("/components/{id:uint}", opensource.UpdateComponent).Name = "更新某个开源组件"
				party.Delete("/components/{id:uint}", opensource.DeleteComponent).Name = "删除某个开源组件"
				party.Get("/componentsimport/template", opensource.DownloadComponentImportTemplate).Name = "下载组件导入模板"
				party.Post("/componentsimport", opensource.ImportComponents).Name = "导入开源组件"
				party.Post("/components/{id:uint}/cvesync", opensource.ManualComponentCVESync).Name = "手动同步开源组件漏洞"
				// permissions
				party.Get("/components/{component_id:uint}/permissions", opensource.ListPermissions).Name = "组件人员列表"
				party.Delete("/components/{component_id:uint}/permissions/{id:uint}", opensource.DeletePermission).Name = "删除某个组件人员"
				party.Post("/components/{component_id:uint}/permissions", opensource.AddPermission).Name = "添加组件人员"
				// vulnerability
				party.Get("/vulnerabilities", opensource.ListVulnerabilities).Name = "CVE漏洞列表"
				party.Get("/vulnerabilities/{id:uint}", opensource.GetVulnerability).Name = "获取某个漏洞的详情"
				// vulnerability history
				party.Get("/vulnerabilityhistories", opensource.ListVulnerabilityHistories).Name = "获取漏洞处理列表"
				party.Post("/vulnerabilities/{id:uint}/process", opensource.CreateVulnerabilityHistory).Name = "进行漏洞处理"
				party.Get("/vulnerabilities/{vulnerability_id:uint}/histories/{id:uint}", opensource.GetVulnerabilityHistory).Name = "获取某个漏洞处理详情"
				// vulnerability cnvd
				party.Get("/cnvdvulnerabilities", opensource.ListCNVDVulnerabilities).Name = "CNVD漏洞列表"
				party.Get("/cnvdvulnerabilities/{id:uint}", opensource.GetCNVDVulnerability).Name = "获取某个CNVD漏洞的详情"
				// tempcheck
				party.Post("/tempchecks", opensource.CreateComponentTempCheck).Name = "组件漏洞预查"
				party.Get("/tempchecks", opensource.ListComponentTempChecks).Name = "组件漏洞预查列表"
				party.Get("/tempchecks/{id:uint}", opensource.GetComponentTempCheck).Name = "获取某个组件漏洞预查详情"
				party.Get("/tempchecks/{id:uint}/download", opensource.DownloadComponentTempCheckResult).Name = "下载组件漏洞预查结果"
				// summary
				party.Get("/summary/vulnerability/component", opensource.GetSummaryByComponent).Name = "基于组件获取漏洞统计信息"
				party.Get("/summary/vulnerability/product", opensource.GetSummaryByProduct).Name = "基于产品获取漏洞统计信息"
			})
			v1.PartyFunc("/license", func(party iris.Party) {
				party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) // 登录验证
				// deviceAuth
				party.Get("/authtasks", license.ListAuthTasks).Name = "获取安全云设备自动授权任务列表"
				party.Get("/authtasks/{id:uint}", license.GetAuthTask).Name = "获取某个安全云设备自动授权任务详情"
				party.Post("/authtasks", license.CreateAuthTask).Name = "创建安全云设备自动授权任务"
				party.Delete("/authtasks/{id:uint}", license.DeleteAuthTask).Name = "删除某个安全云设备自动授权任务"
				// material
				party.Get("/materials", license.ListMaterials).Name = "获取授权物料列表"
				// deviceModel
				party.Get("/devicemodels", license.ListDeviceModels).Name = "获取设备型号列表"
			})

			v1.PartyFunc("/production", func(party iris.Party) {
				party.Get("/synPMSData", productionrelease.SynPMSData).Name = "同步PMS数据"
				party.Get("/releases/updateDOC", productionrelease.UpdateProcInstDoc).Name = "重新生成文档"
				party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) // 登录验证
				party.Get("/procdef", productionrelease.GetProcDef).Name = "获取下生产发布流程"
				party.Get("/productmodels", productionrelease.GetProductModelsByMainProgram).Name = "获取产品型号"
				party.Get("/allproductmodels", productionrelease.GetAllProductModels).Name = "获取所有产品型号"
				party.Get("/productmodelsbytype", productionrelease.GetProductModelsByType).Name = "根据发布类型获取产品型号"
				party.Get("/procinst/{id:uint}/prevnodes", productionrelease.GetProcPrevNodes).Name = "获取下生产发布流程"
				party.Post("/releases", productionrelease.CreateProcInst).Name = "创建下生产记录"
				party.Get("/releases", productionrelease.GetFeatureProcInsts).Name = "获取下生产列表"
				party.Get("/tasks", productionrelease.GetFeatureProcTasks).Name = "获取任务列表"
				party.Post("/releases/{id:uint}", productionrelease.UpdateProcInst).Name = "发布下生产"
				party.Get("/releases/{id:uint}", productionrelease.GetProcInst).Name = "获取下生产详情"
				party.Get("/docLists/{id:uint}", productionrelease.GetDocumentList).Name = "获取下生产文档列表"
				party.Get("/{id:uint}/tasks", productionrelease.GetFeatureProcInstTasks).Name = "获取进展详情"
				party.Get("/{id:uint}/docfile", productionrelease.DownloadDocFile).Name = "下载文档文件"
				party.Get("/{id:uint}/featurefile", productionrelease.DownloadFeatureFile).Name = "下载下生产文件"
				party.Get("/{id:uint}/testreport", productionrelease.DownloadTestReport).Name = "下载描述文件"
				party.Get("/{id:uint}/expreport", productionrelease.DownloadExpReport).Name = "下载描述文件"
				party.Post("/releases/template/{id:uint}", productionrelease.UpdateProcInstTemplate).Name = "更新模板"
				party.Post("/procinst/{id:uint}/updateprocinst", productionrelease.UpdateProcInstProcess).Name = "更新下生产发布流程"
			})
			v1.PartyFunc("/qualitypoint", qualitypoint.Party)
			v1.PartyFunc("/firewallflextrans", firewallflextrans.Party)
			v1.PartyFunc("/documentauto", documentauto.Party)
		}
	}
}
