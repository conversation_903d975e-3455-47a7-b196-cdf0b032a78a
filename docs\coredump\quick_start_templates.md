# Coredump记录自动化处理系统 - 快速开始模板

## 🚀 立即开始开发

基于开发实施指南，这里提供了可以立即使用的代码模板，帮助开发团队快速启动项目。

## 📋 阶段1: 基础架构集成 - 代码模板

### 1.1 配置结构扩展

**文件**: `application/libs/config.go`

```go
// 在现有的 FeiShuDoc 结构中添加以下字段
type FeiShuDoc struct {
    // 现有字段保持不变...
    Enable     bool   `default:"false"`
    AppId      string `default:""`
    AppSecret  string `default:""`
    BiAppToken string `default:""`

    // 新增: Coredump自动同步配置
    CoredumpAutoSync CoredumpAutoSyncConfig `yaml:"coredump_auto_sync"`
}

// 新增: Coredump自动同步配置结构
type CoredumpAutoSyncConfig struct {
    Enable            bool     `yaml:"enable" default:"false"`
    CoredumpTableID   string   `yaml:"coredump_table_id"`
    CronExpr          string   `yaml:"cron_expr" default:"0 0 * * * *"`
    PageSize          int      `yaml:"page_size" default:"100"`
    ProcessingTimeout int      `yaml:"processing_timeout" default:"60"`
    MaxRunTime        int      `yaml:"max_run_time" default:"30"`
    DebugMode         bool     `yaml:"debug_mode" default:"false"`
    DryRun            bool     `yaml:"dry_run" default:"true"`
    FieldNames        []string `yaml:"field_names"`
    FieldMapping      CoredumpFieldMapping `yaml:"field_mapping"`
    BugSystem         CoredumpBugSystem    `yaml:"bug_system"`
}

// 新增: 字段映射配置
type CoredumpFieldMapping struct {
    SyncRequiredField     string `yaml:"sync_required_field" default:"是否需要同步Bug系统"`
    SyncStatusField       string `yaml:"sync_status_field" default:"是否已同步bug系统"`
    ProcessingStatusField string `yaml:"processing_status_field" default:"处理状态"`
    BugIDField           string `yaml:"bug_id_field" default:"Bug系统ID"`
    ProcessingTimeField  string `yaml:"processing_time_field" default:"处理时间"`
    ErrorMessageField    string `yaml:"error_message_field" default:"错误信息"`
    RetryCountField      string `yaml:"retry_count_field" default:"重试次数"`
    LastUpdatedField     string `yaml:"last_updated_field" default:"最后更新时间"`
    
    // 业务字段映射
    SNField                   string `yaml:"sn_field" default:"SN"`
    ComponentField            string `yaml:"component_field" default:"coredump组件"`
    SoftwareVersionField      string `yaml:"software_version_field" default:"软件版本"`
    DeviceModelField          string `yaml:"device_model_field" default:"设备型号"`
    CoredumpTimeField         string `yaml:"coredump_time_field" default:"coredump时间"`
    ComponentResponsibleField string `yaml:"component_responsible_field" default:"组件负责人"`
    ProcessResponsibleField   string `yaml:"process_responsible_field" default:"进程负责人"`
    DescriptionField          string `yaml:"description_field" default:"说明"`
}

// 新增: Bug系统配置
type CoredumpBugSystem struct {
    BaseURL    string `yaml:"base_url"`
    Username   string `yaml:"username"`
    Password   string `yaml:"password"`
    ProjectKey string `yaml:"project_key"`
    IssueType  string `yaml:"issue_type" default:"Bug"`
    Priority   string `yaml:"priority" default:"Medium"`
}
```

### 1.2 定时任务入口添加

**文件**: `service/schedule/feishu.go`

```go
// 在文件顶部添加导入
import (
    // 现有导入...
    "irisAdminApi/application/controllers/openfeishu/coredump"
)

// 在文件中添加互斥锁变量
var coredumpAutoSyncMutex sync.Mutex

// 新增: Coredump自动同步定时任务
func CoredumpAutoSync() {
    coredumpAutoSyncMutex.Lock()
    defer coredumpAutoSyncMutex.Unlock()

    // 检查功能是否启用
    if !libs.Config.FeiShuDoc.CoredumpAutoSync.Enable {
        return
    }

    logging.InfoLogger.Info("开始执行Coredump记录自动同步任务")
    startTime := time.Now()

    // 创建服务实例
    service := coredump.NewCoredumpAutoSyncService()
    if service == nil {
        logging.ErrorLogger.Error("创建Coredump自动同步服务失败")
        return
    }

    // 执行同步处理
    result, err := service.ProcessCoredumpRecords()
    if err != nil {
        logging.ErrorLogger.Errorf("Coredump自动同步失败: %v", err)
        return
    }

    duration := time.Since(startTime)
    logging.InfoLogger.Infof("Coredump自动同步完成: 处理%d条记录, 成功%d条, 失败%d条, 耗时%v",
        result.FilteredRecords, result.SuccessRecords, result.FailedRecords, duration)

    // 记录统计信息
    if result.FailedRecords > 0 {
        logging.WarningLogger.Warnf("Coredump同步存在失败记录: %d条", result.FailedRecords)
    }
}
```

### 1.3 定时任务注册

**文件**: `service/schedule/schedule.go`

```go
// 在 Start() 函数中添加新的定时任务注册
func Start() {
    // 现有任务注册代码...

    // 新增: 注册Coredump自动同步任务
    if libs.Config.FeiShuDoc.CoredumpAutoSync.Enable {
        cronExpr := libs.Config.FeiShuDoc.CoredumpAutoSync.CronExpr
        if cronExpr == "" {
            cronExpr = "0 0 * * * *" // 默认每小时执行一次
        }

        _, err := Cron.AddFunc(cronExpr, feishu.CoredumpAutoSync)
        if err != nil {
            logging.ErrorLogger.Errorf("添加Coredump自动同步定时任务失败: %v", err)
        } else {
            logging.InfoLogger.Infof("Coredump自动同步定时任务已启动: %s", cronExpr)
        }
    } else {
        logging.InfoLogger.Info("Coredump自动同步任务已禁用")
    }
}
```

### 1.4 配置文件更新

**文件**: `application.yml`

```yaml
# 在 feishudoc 配置节中添加
feishudoc:
  enable: true
  appid: "cli_xxxxxxxxxxxxxxxx"
  appsecret: "xxxxxxxxxxxxxxxxxxxxxxxx"
  biapptoken: "your_app_token"
  
  # 新增: Coredump自动同步配置
  coredump_auto_sync:
    enable: false  # 初始设为false，开发完成后再启用
    coredump_table_id: "your_coredump_table_id"
    cron_expr: "0 */5 * * * *"  # 开发期间5分钟执行一次，便于测试
    page_size: 100
    processing_timeout: 60
    max_run_time: 30
    debug_mode: true   # 开发期间启用调试模式
    dry_run: true      # 开发期间只读取不处理
    
    field_names:
      - "SN"
      - "coredump组件"
      - "软件版本"
      - "设备型号"
      - "coredump时间"
      - "组件负责人"
      - "进程负责人"
      - "说明"
      - "是否需要同步Bug系统"
      - "是否已同步bug系统"
      - "处理状态"
      - "Bug系统ID"
      - "处理时间"
      - "错误信息"
      - "重试次数"
      - "最后更新时间"
    
    field_mapping:
      sync_required_field: "是否需要同步Bug系统"
      sync_status_field: "是否已同步bug系统"
      processing_status_field: "处理状态"
      bug_id_field: "Bug系统ID"
      processing_time_field: "处理时间"
      error_message_field: "错误信息"
      retry_count_field: "重试次数"
      last_updated_field: "最后更新时间"
      sn_field: "SN"
      component_field: "coredump组件"
      software_version_field: "软件版本"
      device_model_field: "设备型号"
      coredump_time_field: "coredump时间"
      component_responsible_field: "组件负责人"
      process_responsible_field: "进程负责人"
      description_field: "说明"
    
    bug_system:
      base_url: "https://your-jira-instance.com"
      username: "your_username"
      password: "your_password"
      project_key: "COREDUMP"
      issue_type: "Bug"
      priority: "Medium"
```

## 📋 阶段2: 核心业务逻辑 - 代码模板

### 2.1 创建目录结构

```bash
mkdir -p application/controllers/openfeishu/coredump
```

### 2.2 主服务文件

**文件**: `application/controllers/openfeishu/coredump/service.go`

```go
package coredump

import (
    "context"
    "fmt"
    "time"

    lark "github.com/larksuite/oapi-sdk-go/v3"
    larkbitable "github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
    "irisAdminApi/application/libs"
    "irisAdminApi/application/logging"
    "irisAdminApi/service/openfeishu"
)

// CoredumpAutoSyncService Coredump自动同步服务
type CoredumpAutoSyncService struct {
    feishuClient  *lark.Client
    config        *libs.CoredumpAutoSyncConfig
    filterBuilder *FilterBuilder
    statusManager *StatusManager
    bugSubmitter  *BugSubmitter
}

// NewCoredumpAutoSyncService 创建Coredump自动同步服务
func NewCoredumpAutoSyncService() *CoredumpAutoSyncService {
    config := &libs.Config.FeiShuDoc.CoredumpAutoSync
    
    // 检查配置
    if config.CoredumpTableID == "" {
        logging.ErrorLogger.Error("Coredump表格ID未配置")
        return nil
    }

    feishuClient := openfeishu.GetFeishuClient()
    if feishuClient == nil {
        logging.ErrorLogger.Error("获取飞书客户端失败")
        return nil
    }

    return &CoredumpAutoSyncService{
        feishuClient:  feishuClient,
        config:        config,
        filterBuilder: NewFilterBuilder(config),
        statusManager: NewStatusManager(feishuClient, config),
        bugSubmitter:  NewBugSubmitter(&config.BugSystem),
    }
}

// ProcessResult 处理结果
type ProcessResult struct {
    TaskID          string        `json:"task_id"`
    StartTime       time.Time     `json:"start_time"`
    EndTime         time.Time     `json:"end_time"`
    Duration        time.Duration `json:"duration"`
    TotalRecords    int           `json:"total_records"`
    FilteredRecords int           `json:"filtered_records"`
    SuccessRecords  int           `json:"success_records"`
    FailedRecords   int           `json:"failed_records"`
    Errors          []string      `json:"errors"`
}

// ProcessCoredumpRecords 处理Coredump记录
func (s *CoredumpAutoSyncService) ProcessCoredumpRecords() (*ProcessResult, error) {
    taskID := fmt.Sprintf("coredump_sync_%d", time.Now().Unix())
    logging.InfoLogger.Infof("[%s] 开始处理Coredump记录", taskID)

    result := &ProcessResult{
        TaskID:    taskID,
        StartTime: time.Now(),
    }

    // 步骤1: 使用服务端筛选读取记录
    filteredRecords, err := s.readFilteredRecords()
    if err != nil {
        return nil, fmt.Errorf("读取筛选记录失败: %w", err)
    }

    result.TotalRecords = len(filteredRecords)
    result.FilteredRecords = len(filteredRecords)
    
    if s.config.DebugMode {
        logging.InfoLogger.Infof("[%s] 服务端筛选获取到 %d 条待处理记录", taskID, len(filteredRecords))
    }

    // 如果是干运行模式，只记录不处理
    if s.config.DryRun {
        logging.InfoLogger.Infof("[%s] 干运行模式: 发现 %d 条待处理记录，不执行实际处理", taskID, len(filteredRecords))
        result.EndTime = time.Now()
        result.Duration = result.EndTime.Sub(result.StartTime)
        return result, nil
    }

    // 步骤2: 处理每条记录
    for _, record := range filteredRecords {
        err := s.processRecord(record, result)
        if err != nil {
            logging.ErrorLogger.Errorf("[%s] 处理记录失败 [%s]: %v", taskID, record.RecordID, err)
            result.FailedRecords++
            result.Errors = append(result.Errors, fmt.Sprintf("记录 %s: %v", record.RecordID, err))
        } else {
            result.SuccessRecords++
        }
    }

    result.EndTime = time.Now()
    result.Duration = result.EndTime.Sub(result.StartTime)

    logging.InfoLogger.Infof("[%s] 处理完成: 总计=%d, 成功=%d, 失败=%d, 耗时=%v",
        taskID, result.FilteredRecords, result.SuccessRecords, result.FailedRecords, result.Duration)

    return result, nil
}

// readFilteredRecords 使用服务端筛选读取记录
func (s *CoredumpAutoSyncService) readFilteredRecords() ([]*CoredumpRecord, error) {
    if s.config.DebugMode {
        logging.InfoLogger.Info("开始使用服务端筛选读取Coredump记录")
    }

    var allRecords []*CoredumpRecord
    pageToken := ""
    pageSize := s.config.PageSize
    totalApiCalls := 0

    // 构建筛选条件
    filter := s.filterBuilder.BuildCoredumpFilter()

    for {
        totalApiCalls++

        // 构建请求
        reqBuilder := larkbitable.NewSearchAppTableRecordReqBuilder().
            AppToken(libs.Config.FeiShuDoc.BiAppToken).
            TableId(s.config.CoredumpTableID)

        bodyBuilder := larkbitable.NewSearchAppTableRecordReqBodyBuilder().
            PageSize(pageSize).
            FieldNames(s.config.FieldNames).
            Filter(filter)

        if pageToken != "" {
            bodyBuilder.PageToken(pageToken)
        }

        req := reqBuilder.Body(bodyBuilder.Build()).Build()

        // 调用API
        resp, err := s.feishuClient.Bitable.AppTableRecord.Search(context.Background(), req)
        if err != nil {
            return nil, fmt.Errorf("调用飞书API失败 (第%d次调用): %w", totalApiCalls, err)
        }

        if !resp.Success() {
            return nil, fmt.Errorf("飞书API返回错误 (第%d次调用): %s", totalApiCalls, resp.Msg)
        }

        // 解析记录
        records, err := s.parseRecords(resp.Data.Items)
        if err != nil {
            return nil, fmt.Errorf("解析记录失败 (第%d次调用): %w", totalApiCalls, err)
        }

        allRecords = append(allRecords, records...)
        
        if s.config.DebugMode {
            logging.DebugLogger.Debugf("第%d次API调用获取到 %d 条记录", totalApiCalls, len(records))
        }

        // 检查是否还有更多数据
        if !resp.Data.HasMore {
            break
        }

        pageToken = *resp.Data.PageToken

        // API限流控制
        time.Sleep(100 * time.Millisecond)
    }

    if s.config.DebugMode {
        logging.InfoLogger.Infof("服务端筛选完成: 共%d次API调用, 获取%d条记录",
            totalApiCalls, len(allRecords))
    }

    return allRecords, nil
}

// processRecord 处理单条记录
func (s *CoredumpAutoSyncService) processRecord(record *CoredumpRecord, result *ProcessResult) error {
    if s.config.DebugMode {
        logging.InfoLogger.Infof("开始处理记录 [%s]: SN=%s", record.RecordID, record.SN)
    }

    // 步骤1: 标记为处理中
    err := s.statusManager.UpdateProcessingStatus(record.RecordID, "处理中", "", "")
    if err != nil {
        logging.ErrorLogger.Errorf("更新处理状态失败 [%s]: %v", record.RecordID, err)
        // 继续处理，不因状态更新失败而中断
    }

    // 步骤2: 构建Bug数据
    bugData, err := s.buildBugData(record)
    if err != nil {
        return fmt.Errorf("构建Bug数据失败: %w", err)
    }

    // 步骤3: 提交Bug
    bugID, err := s.bugSubmitter.SubmitBug(bugData)
    if err != nil {
        // 更新为失败状态
        statusErr := s.statusManager.UpdateProcessingStatus(record.RecordID, "失败", "", err.Error())
        if statusErr != nil {
            logging.ErrorLogger.Errorf("更新失败状态失败 [%s]: %v", record.RecordID, statusErr)
        }
        return fmt.Errorf("Bug提交失败: %w", err)
    }

    // 步骤4: 更新为成功状态
    err = s.statusManager.UpdateProcessingStatus(record.RecordID, "成功", bugID, "")
    if err != nil {
        logging.ErrorLogger.Errorf("状态更新失败 [%s]: %v", record.RecordID, err)
        // 注意：这里不返回错误，因为Bug已经提交成功
    }

    if s.config.DebugMode {
        logging.InfoLogger.Infof("记录处理成功 [%s]: Bug ID=%s", record.RecordID, bugID)
    }
    
    return nil
}

// parseRecords 解析飞书API返回的记录数据
func (s *CoredumpAutoSyncService) parseRecords(items []*larkbitable.AppTableRecord) ([]*CoredumpRecord, error) {
    records := make([]*CoredumpRecord, 0, len(items))

    for _, item := range items {
        record, err := s.parseRecord(item)
        if err != nil {
            logging.ErrorLogger.Errorf("解析记录失败 [%s]: %v", *item.RecordId, err)
            continue
        }
        records = append(records, record)
    }

    return records, nil
}

// parseRecord 解析单条记录
func (s *CoredumpAutoSyncService) parseRecord(item *larkbitable.AppTableRecord) (*CoredumpRecord, error) {
    record := &CoredumpRecord{
        RecordID: *item.RecordId,
    }

    // 解析字段数据
    fields := item.Fields
    fieldMapper := NewFieldMapper(s.config)

    // 使用字段映射器解析各个字段
    if err := fieldMapper.MapFields(fields, record); err != nil {
        return nil, fmt.Errorf("字段映射失败: %w", err)
    }

    return record, nil
}

// buildBugData 构建Bug提交数据
func (s *CoredumpAutoSyncService) buildBugData(record *CoredumpRecord) (*BugData, error) {
    bugData := &BugData{
        Summary:     fmt.Sprintf("Coredump异常 - %s [%s] SN:%s", record.Component, record.DeviceModel, record.SN),
        Description: s.buildBugDescription(record),
        IssueType:   s.config.BugSystem.IssueType,
        Priority:    s.config.BugSystem.Priority,
        ProjectKey:  s.config.BugSystem.ProjectKey,
        Reporter:    "coredump_system",
        Components:  record.Component,
        Labels:      []string{"coredump", "auto-generated"},
    }

    return bugData, nil
}

// buildBugDescription 构建Bug描述
func (s *CoredumpAutoSyncService) buildBugDescription(record *CoredumpRecord) string {
    description := fmt.Sprintf(`
## Coredump异常信息

**设备信息:**
- SN: %s
- 设备型号: %s
- 软件版本: %s

**异常信息:**
- 组件: %s
- 发生时间: %s
- 组件负责人: %s
- 进程负责人: %s

**问题描述:**
%s

---
*此Bug由Coredump自动同步系统创建*
`, record.SN, record.DeviceModel, record.SoftwareVersion,
        record.Component, record.CoredumpTime.Format("2006-01-02 15:04:05"),
        record.ComponentResponsible, record.ProcessResponsible, record.Description)

    return description
}
```

### 2.3 数据模型文件

**文件**: `application/controllers/openfeishu/coredump/models.go`

```go
package coredump

import "time"

// CoredumpRecord Coredump记录
type CoredumpRecord struct {
    RecordID                string    `json:"record_id"`
    SN                      string    `json:"sn"`
    Component               string    `json:"component"`
    SoftwareVersion         string    `json:"software_version"`
    DeviceModel             string    `json:"device_model"`
    CoredumpTime            time.Time `json:"coredump_time"`
    ComponentResponsible    string    `json:"component_responsible"`
    ProcessResponsible      string    `json:"process_responsible"`
    Description             string    `json:"description"`
    SyncRequired            string    `json:"sync_required"`
    SyncStatus              string    `json:"sync_status"`
    ProcessingStatus        string    `json:"processing_status"`
    BugID                   string    `json:"bug_id"`
    ProcessingTime          time.Time `json:"processing_time"`
    ErrorMessage            string    `json:"error_message"`
    RetryCount              int       `json:"retry_count"`
    LastUpdated             time.Time `json:"last_updated"`
}

// BugData Bug提交数据
type BugData struct {
    Summary      string                 `json:"summary"`
    Description  string                 `json:"description"`
    IssueType    string                 `json:"issue_type"`
    Priority     string                 `json:"priority"`
    ProjectKey   string                 `json:"project_key"`
    Reporter     string                 `json:"reporter"`
    Assignee     string                 `json:"assignee"`
    Components   string                 `json:"components"`
    Labels       []string               `json:"labels"`
    CustomFields map[string]interface{} `json:"custom_fields"`
}
```

### 2.4 筛选条件构建器

**文件**: `application/controllers/openfeishu/coredump/filter_builder.go`

```go
package coredump

import "irisAdminApi/application/libs"

// FilterBuilder 筛选条件构建器
type FilterBuilder struct {
    config *libs.CoredumpAutoSyncConfig
}

// NewFilterBuilder 创建筛选条件构建器
func NewFilterBuilder(config *libs.CoredumpAutoSyncConfig) *FilterBuilder {
    return &FilterBuilder{
        config: config,
    }
}

// BuildCoredumpFilter 构建Coredump记录筛选条件
func (fb *FilterBuilder) BuildCoredumpFilter() map[string]interface{} {
    return map[string]interface{}{
        "conjunction": "and",
        "children": []map[string]interface{}{
            // 条件组1: 是否需要同步Bug系统 = "Y"
            {
                "conjunction": "and",
                "conditions": []map[string]interface{}{
                    {
                        "field_name": fb.config.FieldMapping.SyncRequiredField,
                        "operator":   "is",
                        "value":      []string{"Y"},
                    },
                },
            },
            // 条件组2: 是否已同步bug系统 为空或 = "N"
            {
                "conjunction": "or",
                "conditions": []map[string]interface{}{
                    {
                        "field_name": fb.config.FieldMapping.SyncStatusField,
                        "operator":   "isEmpty",
                        "value":      []string{},
                    },
                    {
                        "field_name": fb.config.FieldMapping.SyncStatusField,
                        "operator":   "is",
                        "value":      []string{"N"},
                    },
                },
            },
            // 条件组3: 处理状态 为空、待处理或失败
            {
                "conjunction": "or",
                "conditions": []map[string]interface{}{
                    {
                        "field_name": fb.config.FieldMapping.ProcessingStatusField,
                        "operator":   "isEmpty",
                        "value":      []string{},
                    },
                    {
                        "field_name": fb.config.FieldMapping.ProcessingStatusField,
                        "operator":   "is",
                        "value":      []string{"待处理"},
                    },
                    {
                        "field_name": fb.config.FieldMapping.ProcessingStatusField,
                        "operator":   "is",
                        "value":      []string{"失败"},
                    },
                },
            },
        },
    }
}
```

## 📋 阶段1验证脚本

### 验证配置加载

**文件**: `test_config.go` (临时测试文件)

```go
package main

import (
    "fmt"
    "irisAdminApi/application/libs"
)

func main() {
    // 加载配置
    libs.LoadConfig("application.yml")
    
    // 验证配置
    config := libs.Config.FeiShuDoc.CoredumpAutoSync
    
    fmt.Printf("Coredump自动同步配置:\n")
    fmt.Printf("  启用状态: %v\n", config.Enable)
    fmt.Printf("  表格ID: %s\n", config.CoredumpTableID)
    fmt.Printf("  Cron表达式: %s\n", config.CronExpr)
    fmt.Printf("  页面大小: %d\n", config.PageSize)
    fmt.Printf("  调试模式: %v\n", config.DebugMode)
    fmt.Printf("  干运行模式: %v\n", config.DryRun)
    fmt.Printf("  字段数量: %d\n", len(config.FieldNames))
    
    if len(config.FieldNames) > 0 {
        fmt.Printf("  字段列表:\n")
        for i, field := range config.FieldNames {
            fmt.Printf("    %d. %s\n", i+1, field)
        }
    }
    
    fmt.Printf("配置加载验证完成!\n")
}
```

### 验证定时任务

**文件**: `test_scheduler.go` (临时测试文件)

```go
package main

import (
    "fmt"
    "time"
    "irisAdminApi/service/schedule"
)

func main() {
    fmt.Println("启动定时任务测试...")
    
    // 启动调度器
    schedule.Start()
    
    fmt.Println("定时任务已启动，等待执行...")
    fmt.Println("请检查日志输出，确认Coredump自动同步任务是否正常注册")
    
    // 等待一段时间观察日志
    time.Sleep(10 * time.Second)
    
    fmt.Println("测试完成")
}
```

## 🚀 快速启动步骤

### 步骤1: 应用配置更改
```bash
# 1. 修改 application/libs/config.go，添加新的配置结构
# 2. 修改 application.yml，添加coredump_auto_sync配置
# 3. 编译测试配置加载
go run test_config.go
```

### 步骤2: 集成定时任务
```bash
# 1. 修改 service/schedule/feishu.go，添加CoredumpAutoSync函数
# 2. 修改 service/schedule/schedule.go，注册新任务
# 3. 测试定时任务注册
go run test_scheduler.go
```

### 步骤3: 创建业务逻辑框架
```bash
# 1. 创建目录结构
mkdir -p application/controllers/openfeishu/coredump

# 2. 创建基础文件
touch application/controllers/openfeishu/coredump/service.go
touch application/controllers/openfeishu/coredump/models.go
touch application/controllers/openfeishu/coredump/filter_builder.go

# 3. 复制模板代码到对应文件
```

### 步骤4: 验证基础功能
```bash
# 1. 编译项目
go build

# 2. 启动服务
./your-service-name

# 3. 检查日志，确认：
#    - 配置加载正常
#    - 定时任务注册成功
#    - 如果启用了任务，应该看到执行日志
```

## 📝 开发检查清单

### 阶段1完成标准
- [ ] 配置结构扩展完成，编译无错误
- [ ] 配置文件更新完成，配置加载正常
- [ ] 定时任务入口添加完成
- [ ] 定时任务注册完成
- [ ] 目录结构创建完成
- [ ] 基础文件创建完成
- [ ] 服务启动正常，无错误日志
- [ ] 定时任务日志输出正常（如果启用）

### 常见问题排查
1. **配置加载失败**: 检查YAML语法，确保缩进正确
2. **编译错误**: 检查导入路径，确保包名正确
3. **定时任务未注册**: 检查配置enable字段，确保为true
4. **日志无输出**: 检查日志级别配置

## 🎯 下一步计划

完成阶段1后，继续按照开发实施指南进行：
1. **阶段2**: 实现核心业务逻辑（飞书API集成、服务端筛选）
2. **阶段3**: 实现状态管理和Bug系统集成
3. **阶段4**: 完善错误处理和优化
4. **阶段5**: 测试和部署

每个阶段都有详细的代码模板和验证方法，确保开发过程顺利进行。

---

**快速开始模板版本**: v1.0  
**适用阶段**: 开发实施阶段1-2  
**预计完成时间**: 2-3天  
**难度等级**: 初级 ⭐⭐