package dpmscaseinfo

import (
	"encoding/json"
	"fmt"
	"reflect"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/datasync"
	"irisAdminApi/service/dao/datasync/dsyncrecord"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "PMS用例信息表"

type PmsCaseInfoSyncResponse struct {
	State   string                 `json:"state"`
	Data    []*PmsCaseInfoResponse `json:"data"`
	Total   int                    `json:"total"`
	Message string                 `json:"message"`
}

type PmsCaseInfoResponse struct {
	RowNum               int    `json:"rownum"`
	ID                   int    `gorm:"not null; index:idx_unique, unique" json:"id" `
	CaseID               int    `gorm:"not null; index:idx_unique, unique" json:"caseId" `
	Version              int    `gorm:"not null" json:"version" update:"1"`
	CaseLevelID          int    `gorm:"not null" json:"caseLevelId" update:"1"`
	CaseName             string `gorm:"not null; type:varchar(100)" json:"caseName" update:"1"`
	CasePacketID         int    `gorm:"not null" json:"casePacketId" update:"1"`
	CasePacketName       string `gorm:"not null; type:varchar(100)" json:"casePacketName" update:"1"`
	TestProjectStageName string `gorm:"not null; type:varchar(100)" json:"testProjectStageName" update:"1"`
	ExecutionResult      string `gorm:"not null; type:varchar(100)" json:"executionResult" update:"1"`
	FailReason           string `gorm:"not null; type:varchar(100)" json:"failReason" update:"1"`

	ProjectID     int    `gorm:"not null" json:"projectId" update:"1"`
	ProjectName   string `gorm:"not null; type:varchar(100)" json:"projectName" update:"1"`
	ProductName   string `gorm:"not null; type:varchar(100)" json:"productName" update:"1"`
	ExecutionTime string `json:"executionTime" update:"1"`

	AssignUserName string `gorm:"not null; type:varchar(100)" json:"assignUserName" update:"1"`
}

type PmsCaseInfo struct {
	datasync.PmsCaseInfo
}

type ListResponse struct {
	PmsCaseInfo
}

type Request struct {
	Id uint `json:"id"`
}

func (this *PmsCaseInfo) ModelName() string {
	return ModelName
}

func Model() *datasync.PmsCaseInfo {
	return &datasync.PmsCaseInfo{}
}

func (this *PmsCaseInfo) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *PmsCaseInfo) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *PmsCaseInfo) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *PmsCaseInfo) CreateV2(object interface{}) error {
	return nil
}

func (this *PmsCaseInfo) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *PmsCaseInfo) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *PmsCaseInfo) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *PmsCaseInfo) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *PmsCaseInfo) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *PmsCaseInfo) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func UpdateOrCreatePmsCaseInfoTransaction(items []*PmsCaseInfoResponse, _url string, data map[string]string, method, state, errorMsg string) error {
	objects := []map[string]interface{}{}
	for _, item := range items {
		object := map[string]interface{}{
			"ID":                   item.ID,
			"CaseID":               item.CaseID,
			"Version":              item.Version,
			"CaseLevelID":          item.CaseLevelID,
			"CaseName":             item.CaseName,
			"CasePacketID":         item.CasePacketID,
			"CasePacketName":       item.CasePacketName,
			"TestProjectStageName": item.TestProjectStageName,
			"ExecutionResult":      item.ExecutionResult,
			"FailReason":           item.FailReason,
			"ProjectID":            item.ProjectID,
			"ProjectName":          item.ProjectName,
			"ProductName":          item.ProductName,

			"AssignUserName": item.AssignUserName,
		}
		if len(item.ExecutionTime) > 0 {
			object["ExecutionTime"] = item.ExecutionTime
		}
		objects = append(objects, object)
	}

	columns := []string{}

	xt := reflect.TypeOf(Model())
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")
		if ok && ok2 {
			columns = append(columns, name)
		}
	}

	body, err := json.Marshal(data)
	if err != nil {
		return err
	}
	db := easygorm.GetEasyGormDb()
	err = db.Transaction(func(tx *gorm.DB) error {
		if len(objects) > 0 {
			err := tx.Model(Model()).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "id"}, {Name: "case_id"}},
				DoUpdates: clause.AssignmentColumns(columns),
			}).Create(&objects).Error
			if err != nil {
				return err
			}
		}

		if err := tx.Model(dsyncrecord.Model()).Create(map[string]interface{}{
			"url":             _url,
			"body":            body,
			"method":          method,
			"state":           state,
			"message":         errorMsg,
			"min_modify_date": data["minModifyDate"],
			"max_modify_date": data["maxModifyDate"],
			"created_at":      time.Now(),
		}).Error; err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func FindByProject(project string) ([]*ListResponse, error) {
	items := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("project_name = ?", project).Find(&items).Error
	return items, err
}
