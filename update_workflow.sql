-- 备份原始流程定义
CREATE TABLE IF NOT EXISTS production_proc_defs_backup LIKE production_proc_defs;
INSERT INTO production_proc_defs_backup SELECT * FROM production_proc_defs WHERE name = '下生产流程';

-- 更新流程定义，添加 PM 节点
UPDATE production_proc_defs 
SET resource = '[{"name":"创建下生产","nodeId":"start","prevNodeId":"","assignee":0},{"name":"PM提交程序","nodeId":"pm_submit","prevNodeId":"start","assignee":100},{"name":"PGTTL提交系统模板","nodeId":"system_template","prevNodeId":"pm_submit","assignee":100},{"name":"平台负责人","nodeId":"platform_mg","prevNodeId":"system_template","assignee":100},{"name":"生测负责人","nodeId":"chief_tester","prevNodeId":"system_template","assignee":100},{"name":"CPLD在线升级","nodeId":"cpld_check","prevNodeId":"system_template","assignee":100},{"name":"PGTTL审核文档","nodeId":"pgttl_check","prevNodeId":"cpld_check","assignee":100},{"name":"PGTTL审核文档","nodeId":"pgttl_check","prevNodeId":"chief_tester","assignee":100},{"name":"PGTTL审核文档","nodeId":"pgttl_check","prevNodeId":"platform_mg","assignee":100},{"name":"硬件代表审核文档","nodeId":"hardware_rep","prevNodeId":"pgttl_check","assignee":100},{"name":"测试代表审核文档","nodeId":"test_rep","prevNodeId":"pgttl_check","assignee":100},{"name":"测试验收","nodeId":"test_check","prevNodeId":"test_rep","assignee":100},{"name":"硬件验收","nodeId":"hardware_audit","prevNodeId":"hardware_rep","assignee":100},{"name":"QA审核","nodeId":"qa_audit","prevNodeId":"hardware_audit","assignee":100},{"name":"QA审核","nodeId":"qa_audit","prevNodeId":"test_check","assignee":100}]'
WHERE name = '下生产流程';

-- 回滚脚本（如果需要）
-- UPDATE production_proc_defs 
-- SET resource = (SELECT resource FROM production_proc_defs_backup WHERE name = '下生产流程' LIMIT 1)
-- WHERE name = '下生产流程'; 