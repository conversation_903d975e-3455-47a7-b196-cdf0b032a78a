# Coredump记录自动化处理系统 - 增强版实现指南

## 📋 概述

本文档提供了增强版Coredump记录自动化处理系统的完整实现指南，包含以下核心功能：

- ✅ **定时处理机制**：支持定时任务和手动触发
- ✅ **条件筛选**：基于"是否需要同步Bug系统"和"是否已同步bug系统"字段筛选
- ✅ **分页读取**：避免一次性加载过多数据
- ✅ **状态回填**：Bug提交成功后回写飞书多维表格状态
- ✅ **错误处理**：完善的错误处理和重试机制

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   定时调度器     │    │  Coredump处理   │    │   Bug管理系统   │
│                │    │     系统        │    │                │
│  - 定时任务     │────▶│  - 分页读取     │────▶│  - Bug提交     │
│  - 手动触发     │    │  - 条件筛选     │    │  - 状态返回     │
│  - 任务管理     │    │  - 字段映射     │    └─────────────────┘
└─────────────────┘    │  - 状态更新     │             │
                       └─────────────────┘             │
                              │                        │
                              ▼                        │
┌─────────────────────────────────────────────────────┐│
│                飞书多维表格                          ││
│                                                    ││
│  📊 数据存储：Coredump记录、人员信息                 ││
│  🔄 状态管理：同步状态、处理状态                     ││
│  📝 结果记录：Bug ID、处理时间、错误信息             ││
│  📈 统计信息：重试次数、最后更新时间                 ││
└─────────────────────────────────────────────────────┘│
                              ▲                        │
                              └────────────────────────┘
```

## 📁 项目结构

```
application/controllers/openfeishu/coredump/
├── coredump_scheduler.go        # 定时任务调度器
├── coredump_service.go          # 主要业务逻辑服务
├── coredump_models.go           # 数据模型定义
├── coredump_field_mapper.go     # 字段映射器
├── coredump_filter.go           # 条件筛选器
├── coredump_status_manager.go   # 飞书状态管理器
├── coredump_controller.go       # HTTP接口控制器
├── coredump_config.go           # 配置管理
└── coredump_test.go             # 单元测试
```

## 🔧 核心组件实现

### 1. 数据模型 (coredump_models.go)

```go
package coredump

import (
    "time"
    "sync"
    "github.com/robfig/cron/v3"
)

// CoredumpRecord Coredump记录结构
type CoredumpRecord struct {
    // 基本信息
    RecordID             string                 `json:"record_id"`              // 飞书记录ID
    SN                   string                 `json:"sn"`                     // Coredump SN
    CoredumpComponent    string                 `json:"coredump_component"`     // Coredump组件
    SoftwareVersion      string                 `json:"software_version"`       // 软件版本
    DeviceModel          string                 `json:"device_model"`           // 设备型号
    CoredumpCollectURL   string                 `json:"coredump_collect_url"`   // Coredump收集URL
    CoredumpTime         time.Time              `json:"coredump_time"`          // Coredump时间
    RecordDate           time.Time              `json:"record_date"`            // 记录日期
    
    // 人员信息
    ComponentResponsible []FeishuPersonField    `json:"component_responsible"`  // 组件负责人
    ProcessResponsible   []FeishuPersonField    `json:"process_responsible"`    // 进程负责人
    
    // 技术信息
    StackInfo            string                 `json:"stack_info"`             // 堆栈信息
    Description          string                 `json:"description"`            // 说明
    FileName             string                 `json:"file_name"`              // 文件名
    
    // 处理状态信息
    IsKnownIssue         bool                   `json:"is_known_issue"`         // 是否已知问题
    ProcessResult        string                 `json:"process_result"`         // 处理结果
    FixVersion           string                 `json:"fix_version"`            // 修复版本
    
    // 同步控制字段
    SyncRequired         string                 `json:"sync_required"`          // 是否需要同步Bug系统
    SyncStatus           string                 `json:"sync_status"`            // 是否已同步bug系统

    // 处理结果字段（基于飞书表格）
    ProcessingStatus     string                 `json:"processing_status"`      // 处理状态：待处理/处理中/成功/失败
    BugID                string                 `json:"bug_id"`                 // Bug系统ID
    ProcessingTime       time.Time              `json:"processing_time"`        // 处理时间
    ErrorMessage         string                 `json:"error_message"`          // 错误信息
    RetryCount           int                    `json:"retry_count"`            // 重试次数
    LastUpdated          time.Time              `json:"last_updated"`           // 最后更新时间
}

// FeishuPersonField 飞书人员字段结构
type FeishuPersonField struct {
    ID        string `json:"id"`        // OpenID
    Name      string `json:"name"`      // 姓名
    Email     string `json:"email"`     // 邮箱
    AvatarURL string `json:"avatar_url"` // 头像URL
}

// SchedulerConfig 定时任务配置
type SchedulerConfig struct {
    Enabled      bool   `json:"enabled"`       // 是否启用定时任务
    CronExpr     string `json:"cron_expr"`     // Cron表达式
    MaxRunTime   int    `json:"max_run_time"`  // 最大运行时间（分钟）
    Concurrent   bool   `json:"concurrent"`    // 是否允许并发执行
}

// TaskStatus 任务状态
type TaskStatus struct {
    TaskID         string    `json:"task_id"`         // 任务ID
    Status         string    `json:"status"`          // 运行状态
    StartTime      time.Time `json:"start_time"`      // 开始时间
    EndTime        time.Time `json:"end_time"`        // 结束时间
    ProcessedCount int       `json:"processed_count"` // 处理数量
    ErrorCount     int       `json:"error_count"`     // 错误数量
    LastError      string    `json:"last_error"`      // 最后错误
}

// ProcessResult 处理结果统计
type ProcessResult struct {
    TaskID              string        `json:"task_id"`              // 任务ID
    StartTime           time.Time     `json:"start_time"`           // 开始时间
    EndTime             time.Time     `json:"end_time"`             // 结束时间
    Duration            time.Duration `json:"duration"`             // 处理耗时
    TotalRecords        int           `json:"total_records"`        // 总记录数
    FilteredRecords     int           `json:"filtered_records"`     // 筛选后记录数
    SuccessRecords      int           `json:"success_records"`      // 成功处理数
    FailedRecords       int           `json:"failed_records"`       // 失败处理数
    UpdatedRecords      int           `json:"updated_records"`      // 状态更新成功数
    UpdateFailedRecords int           `json:"update_failed_records"` // 状态更新失败数
    Errors              []string      `json:"errors,omitempty"`     // 错误信息列表
}

// 处理状态常量
const (
    StatusPending    = "待处理"    // 需要处理但未开始
    StatusProcessing = "处理中"    // 正在处理
    StatusSuccess    = "成功"      // 处理成功
    StatusFailed     = "失败"      // 处理失败
)

// 同步状态常量
const (
    SyncRequired    = "Y"    // 需要同步
    SyncNotRequired = "N"    // 不需要同步
    SyncCompleted   = "Y"    // 已同步
    SyncPending     = "N"    // 未同步
    SyncEmpty       = ""     // 空值（等同于未同步）
)
```

### 2. 定时任务调度器 (coredump_scheduler.go)

```go
package coredump

import (
    "context"
    "fmt"
    "sync"
    "time"
    "github.com/robfig/cron/v3"
    "irisAdminApi/application/logging"
)

// CoredumpScheduler 定时任务调度器
type CoredumpScheduler struct {
    cron         *cron.Cron                // Cron调度器
    service      *CoredumpService          // 业务服务
    config       *SchedulerConfig          // 调度配置
    currentTask  *TaskStatus               // 当前任务状态
    mutex        sync.RWMutex              // 并发控制
    running      bool                      // 运行状态
    entryID      cron.EntryID              // 任务条目ID
}

// NewCoredumpScheduler 创建定时任务调度器
func NewCoredumpScheduler(service *CoredumpService) *CoredumpScheduler {
    return &CoredumpScheduler{
        cron:    cron.New(cron.WithSeconds()),
        service: service,
        config:  LoadSchedulerConfig(),
    }
}

// Start 启动定时任务
func (s *CoredumpScheduler) Start() error {
    s.mutex.Lock()
    defer s.mutex.Unlock()
    
    if !s.config.Enabled {
        return fmt.Errorf("定时任务未启用")
    }
    
    if s.running {
        return fmt.Errorf("定时任务已在运行")
    }
    
    entryID, err := s.cron.AddFunc(s.config.CronExpr, s.executeTask)
    if err != nil {
        return fmt.Errorf("添加定时任务失败: %w", err)
    }
    
    s.entryID = entryID
    s.cron.Start()
    s.running = true
    
    logging.InfoLogger.Infof("定时任务已启动，表达式: %s", s.config.CronExpr)
    return nil
}

// Stop 停止定时任务
func (s *CoredumpScheduler) Stop() {
    s.mutex.Lock()
    defer s.mutex.Unlock()
    
    if !s.running {
        return
    }
    
    s.cron.Stop()
    s.running = false
    
    logging.InfoLogger.Info("定时任务已停止")
}

// executeTask 执行任务
func (s *CoredumpScheduler) executeTask() {
    s.mutex.Lock()
    
    // 检查是否允许并发执行
    if !s.config.Concurrent && s.currentTask != nil && s.currentTask.Status == "running" {
        logging.WarnLogger.Warn("上一个任务仍在运行，跳过本次执行")
        s.mutex.Unlock()
        return
    }
    
    // 创建新任务状态
    taskID := fmt.Sprintf("scheduled_%d", time.Now().Unix())
    s.currentTask = &TaskStatus{
        TaskID:    taskID,
        Status:    "running",
        StartTime: time.Now(),
    }
    s.mutex.Unlock()
    
    logging.InfoLogger.Infof("[%s] 开始执行定时任务", taskID)
    
    // 设置超时控制
    ctx, cancel := context.WithTimeout(context.Background(), 
        time.Duration(s.config.MaxRunTime)*time.Minute)
    defer cancel()
    
    // 执行处理
    go func() {
        defer func() {
            s.mutex.Lock()
            if s.currentTask != nil {
                s.currentTask.EndTime = time.Now()
                s.currentTask.Status = "completed"
            }
            s.mutex.Unlock()
        }()
        
        result, err := s.service.ProcessCoredumpRecords()
        
        s.mutex.Lock()
        if s.currentTask != nil {
            if err != nil {
                s.currentTask.Status = "failed"
                s.currentTask.LastError = err.Error()
                s.currentTask.ErrorCount++
            } else {
                s.currentTask.ProcessedCount = result.SuccessRecords
                s.currentTask.ErrorCount = result.FailedRecords
            }
        }
        s.mutex.Unlock()
        
        if err != nil {
            logging.ErrorLogger.Errorf("[%s] 定时任务执行失败: %v", taskID, err)
        } else {
            logging.InfoLogger.Infof("[%s] 定时任务执行完成: 成功=%d, 失败=%d", 
                taskID, result.SuccessRecords, result.FailedRecords)
        }
    }()
    
    // 监控超时
    select {
    case <-ctx.Done():
        logging.ErrorLogger.Errorf("[%s] 定时任务执行超时", taskID)
        s.mutex.Lock()
        if s.currentTask != nil {
            s.currentTask.Status = "timeout"
            s.currentTask.LastError = "执行超时"
        }
        s.mutex.Unlock()
    case <-time.After(time.Duration(s.config.MaxRunTime) * time.Minute):
        // 正常完成
    }
}

// GetStatus 获取调度器状态
func (s *CoredumpScheduler) GetStatus() map[string]interface{} {
    s.mutex.RLock()
    defer s.mutex.RUnlock()
    
    status := map[string]interface{}{
        "enabled":     s.config.Enabled,
        "running":     s.running,
        "cron_expr":   s.config.CronExpr,
        "max_runtime": s.config.MaxRunTime,
        "concurrent":  s.config.Concurrent,
    }
    
    if s.currentTask != nil {
        status["current_task"] = s.currentTask
    }
    
    return status
}
```

### 3. 条件筛选器 (coredump_filter.go)

```go
package coredump

import (
    "irisAdminApi/application/logging"
)

// CoredumpFilter 条件筛选器
type CoredumpFilter struct {
    config *CoredumpConfig
}

// NewCoredumpFilter 创建条件筛选器
func NewCoredumpFilter() *CoredumpFilter {
    return &CoredumpFilter{
        config: LoadCoredumpConfig(),
    }
}

// FilterRecords 筛选需要处理的记录
func (f *CoredumpFilter) FilterRecords(records []*CoredumpRecord) []*CoredumpRecord {
    var filteredRecords []*CoredumpRecord
    
    for _, record := range records {
        if f.shouldProcess(record) {
            filteredRecords = append(filteredRecords, record)
        }
    }
    
    logging.InfoLogger.Infof("筛选结果: 输入=%d, 输出=%d", len(records), len(filteredRecords))
    return filteredRecords
}

// shouldProcess 判断记录是否应该处理
func (f *CoredumpFilter) shouldProcess(record *CoredumpRecord) bool {
    // 筛选条件1: 是否需要同步Bug系统 = "Y"
    if record.SyncRequired != "Y" {
        logging.DebugLogger.Debugf("跳过记录 [%s]: 不需要同步Bug系统", record.RecordID)
        return false
    }

    // 筛选条件2: 是否已同步bug系统 为空或 = "N"
    if record.SyncStatus != "" && record.SyncStatus != "N" {
        logging.DebugLogger.Debugf("跳过记录 [%s]: 已同步Bug系统", record.RecordID)
        return false
    }

    // 筛选条件3: 处理状态检查
    if !f.shouldProcessByStatus(record) {
        logging.DebugLogger.Debugf("跳过记录 [%s]: 处理状态不符合条件", record.RecordID)
        return false
    }

    // 跳过已知问题（可配置）
    if f.config.SkipKnownIssues && record.IsKnownIssue {
        logging.DebugLogger.Debugf("跳过记录 [%s]: 已知问题", record.RecordID)
        return false
    }

    // 跳过必要字段为空的记录
    if record.SN == "" || record.CoredumpComponent == "" {
        logging.DebugLogger.Debugf("跳过记录 [%s]: 必要字段为空", record.RecordID)
        return false
    }

    return true
}

// shouldProcessByStatus 根据处理状态判断是否应该处理
func (f *CoredumpFilter) shouldProcessByStatus(record *CoredumpRecord) bool {
    switch record.ProcessingStatus {
    case "":
        // 新记录，处理状态为空，需要处理
        return true
    case StatusPending:
        // 明确标记为待处理，需要处理
        return true
    case StatusFailed:
        // 失败的记录，可以重试处理
        return true
    case StatusProcessing:
        // 检查是否超时，如果超时则可以重新处理
        return f.isProcessingTimeout(record)
    case StatusSuccess:
        // 已成功处理，不需要重复处理
        return false
    default:
        // 未知状态，为安全起见不处理
        return false
    }
}

// isProcessingTimeout 检查处理中的记录是否超时
func (f *CoredumpFilter) isProcessingTimeout(record *CoredumpRecord) bool {
    if record.ProcessingTime.IsZero() {
        // 没有处理时间记录，认为是异常状态，可以重新处理
        return true
    }

    // 检查是否超过超时时间（默认1小时）
    timeout := time.Duration(f.config.ProcessingTimeout) * time.Minute
    if timeout == 0 {
        timeout = 60 * time.Minute // 默认1小时
    }

    return time.Since(record.ProcessingTime) > timeout
}

// GetFilterStats 获取筛选统计信息
func (f *CoredumpFilter) GetFilterStats(records []*CoredumpRecord) map[string]int {
    stats := map[string]int{
        "total":           len(records),
        "sync_required_n": 0,
        "already_synced":  0,
        "known_issues":    0,
        "missing_fields":  0,
        "valid":           0,
    }
    
    for _, record := range records {
        if record.SyncRequired != "Y" {
            stats["sync_required_n"]++
            continue
        }
        
        if record.SyncStatus != "" && record.SyncStatus != "N" {
            stats["already_synced"]++
            continue
        }
        
        if f.config.SkipKnownIssues && record.IsKnownIssue {
            stats["known_issues"]++
            continue
        }
        
        if record.SN == "" || record.CoredumpComponent == "" {
            stats["missing_fields"]++
            continue
        }
        
        stats["valid"]++
    }
    
    return stats
}

// GetTimeoutProcessingRecords 获取超时的处理中记录
func (f *CoredumpFilter) GetTimeoutProcessingRecords(records []*CoredumpRecord) []*CoredumpRecord {
    var timeoutRecords []*CoredumpRecord

    for _, record := range records {
        if record.ProcessingStatus == StatusProcessing && f.isProcessingTimeout(record) {
            timeoutRecords = append(timeoutRecords, record)
        }
    }

    logging.InfoLogger.Infof("发现超时处理中记录: %d条", len(timeoutRecords))
    return timeoutRecords
}
```

### 4. 飞书状态管理器 (coredump_status_manager.go)

```go
package coredump

import (
    "context"
    "fmt"
    "time"
    lark "github.com/larksuite/oapi-sdk-go/v3"
    larkbitable "github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
    "irisAdminApi/application/libs"
    "irisAdminApi/application/logging"
)

// CoredumpStatusManager 飞书状态管理器
type CoredumpStatusManager struct {
    feishuClient *lark.Client
    config       *CoredumpConfig
}

// NewCoredumpStatusManager 创建飞书状态管理器
func NewCoredumpStatusManager() *CoredumpStatusManager {
    return &CoredumpStatusManager{
        feishuClient: lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret),
        config:       LoadCoredumpConfig(),
    }
}

// UpdateProcessingStatus 更新处理状态
func (m *CoredumpStatusManager) UpdateProcessingStatus(recordID, status string, bugID string, errorMsg string) error {
    logging.InfoLogger.Infof("更新处理状态 [%s]: 状态=%s, BugID=%s", recordID, status, bugID)

    // 构建更新字段
    updateFields := map[string]interface{}{
        m.config.FieldMapping.ProcessingStatusField: status,
        m.config.FieldMapping.LastUpdatedField: time.Now().Format("2006-01-02 15:04:05"),
    }

    // 根据状态更新相应字段
    if status == StatusSuccess {
        updateFields[m.config.FieldMapping.SyncStatusField] = SyncCompleted
        if bugID != "" {
            updateFields[m.config.FieldMapping.BugIDField] = bugID
        }
        updateFields[m.config.FieldMapping.ProcessingTimeField] = time.Now().Format("2006-01-02 15:04:05")
        updateFields[m.config.FieldMapping.ErrorMessageField] = ""
    } else if status == StatusFailed {
        updateFields[m.config.FieldMapping.ErrorMessageField] = errorMsg
        // 增加重试次数
        m.incrementRetryCount(recordID)
    }
    
    // 构建更新请求
    req := larkbitable.NewUpdateAppTableRecordReqBuilder().
        AppToken(m.config.CoredumpAppToken).
        TableId(m.config.CoredumpTableID).
        RecordId(recordID).
        Body(larkbitable.NewUpdateAppTableRecordReqBodyBuilder().
            Fields(updateFields).
            Build()).
        Build()

    // 调用飞书API更新记录
    resp, err := m.feishuClient.Bitable.AppTableRecord.Update(context.Background(), req)
    if err != nil {
        return fmt.Errorf("飞书API更新调用失败: %w", err)
    }

    if !resp.Success() {
        return fmt.Errorf("飞书API更新返回错误: %s", resp.Msg)
    }

    logging.InfoLogger.Infof("状态更新成功 [%s]: 状态=%s", recordID, status)
    return nil
}

// BatchUpdateStatus 批量更新状态
func (m *CoredumpStatusManager) BatchUpdateStatus(records []*CoredumpRecord, status string) error {
    logging.InfoLogger.Infof("批量更新状态: %d条记录 -> %s", len(records), status)

    var errors []error
    for _, record := range records {
        err := m.UpdateProcessingStatus(record.RecordID, status, "", "")
        if err != nil {
            errors = append(errors, fmt.Errorf("记录 %s 更新失败: %w", record.RecordID, err))
        }

        // 避免API限流
        time.Sleep(100 * time.Millisecond)
    }

    if len(errors) > 0 {
        return fmt.Errorf("批量更新部分失败: %d个错误", len(errors))
    }

    return nil
}

// incrementRetryCount 增加重试次数
func (m *CoredumpStatusManager) incrementRetryCount(recordID string) error {
    // 这里可以实现重试次数的增加逻辑
    // 由于需要先读取当前值再更新，这里简化处理
    updateFields := map[string]interface{}{
        m.config.FieldMapping.RetryCountField: "=COUNTA(重试次数)+1", // 使用飞书公式
    }

    req := larkbitable.NewUpdateAppTableRecordReqBuilder().
        AppToken(m.config.CoredumpAppToken).
        TableId(m.config.CoredumpTableID).
        RecordId(recordID).
        Body(larkbitable.NewUpdateAppTableRecordReqBodyBuilder().
            Fields(updateFields).
            Build()).
        Build()

    _, err := m.feishuClient.Bitable.AppTableRecord.Update(context.Background(), req)
    return err
}

// ResetTimeoutRecords 重置超时的处理中记录
func (m *CoredumpStatusManager) ResetTimeoutRecords(records []*CoredumpRecord) error {
    logging.InfoLogger.Infof("重置超时记录: %d条", len(records))

    var errors []error
    for _, record := range records {
        err := m.UpdateProcessingStatus(record.RecordID, StatusPending, "", "处理超时，已重置为待处理状态")
        if err != nil {
            errors = append(errors, fmt.Errorf("记录 %s 重置失败: %w", record.RecordID, err))
        }

        // 避免API限流
        time.Sleep(100 * time.Millisecond)
    }

    if len(errors) > 0 {
        return fmt.Errorf("重置超时记录部分失败: %d个错误", len(errors))
    }

    return nil
}

// ValidateUpdatePermission 验证更新权限
func (u *CoredumpStatusUpdater) ValidateUpdatePermission() error {
    // 测试更新权限，创建一个测试记录然后删除
    testFields := map[string]interface{}{
        "测试字段": "权限测试",
    }
    
    req := larkbitable.NewCreateAppTableRecordReqBuilder().
        AppToken(u.config.CoredumpAppToken).
        TableId(u.config.CoredumpTableID).
        Body(larkbitable.NewCreateAppTableRecordReqBodyBuilder().
            Fields(testFields).
            Build()).
        Build()
    
    resp, err := u.feishuClient.Bitable.AppTableRecord.Create(context.Background(), req)
    if err != nil {
        return fmt.Errorf("权限验证失败: %w", err)
    }
    
    if !resp.Success() {
        return fmt.Errorf("权限验证失败: %s", resp.Msg)
    }
    
    // 删除测试记录
    if resp.Data != nil && resp.Data.Record != nil {
        deleteReq := larkbitable.NewDeleteAppTableRecordReqBuilder().
            AppToken(u.config.CoredumpAppToken).
            TableId(u.config.CoredumpTableID).
            RecordId(resp.Data.Record.RecordId).
            Build()
        
        u.feishuClient.Bitable.AppTableRecord.Delete(context.Background(), deleteReq)
    }
    
    logging.InfoLogger.Info("状态回填权限验证通过")
    return nil
}
```

### 5. 主服务类 (coredump_service.go)

```go
package coredump

import (
    "context"
    "fmt"
    "time"
    lark "github.com/larksuite/oapi-sdk-go/v3"
    larkbitable "github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
    "irisAdminApi/application/controllers/openfeishu"
    "irisAdminApi/application/libs"
    "irisAdminApi/application/logging"
)

// CoredumpService Coredump处理服务
type CoredumpService struct {
    feishuClient    *lark.Client              // 飞书API客户端
    bugSubmitter    *openfeishu.BugSubmitter  // Bug提交器
    fieldMapper     *CoredumpFieldMapper      // 字段映射器
    filter          *CoredumpFilter           // 条件筛选器
    statusManager   *CoredumpStatusManager    // 飞书状态管理器
    config          *CoredumpConfig           // 配置管理器
}

// NewCoredumpService 创建Coredump处理服务
func NewCoredumpService() *CoredumpService {
    return &CoredumpService{
        feishuClient:  lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret),
        bugSubmitter:  openfeishu.NewBugSubmitter(),
        fieldMapper:   NewCoredumpFieldMapper(),
        filter:        NewCoredumpFilter(),
        statusManager: NewCoredumpStatusManager(),
        config:        LoadCoredumpConfig(),
    }
}

// ProcessCoredumpRecords 处理Coredump记录（主要入口方法）
func (s *CoredumpService) ProcessCoredumpRecords() (*ProcessResult, error) {
    taskID := fmt.Sprintf("task_%d", time.Now().Unix())
    logging.InfoLogger.Infof("[%s] 开始处理Coredump记录", taskID)
    
    result := &ProcessResult{
        TaskID:    taskID,
        StartTime: time.Now(),
    }
    
    // 步骤1: 使用服务端筛选直接读取符合条件的记录（性能优化）
    filteredRecords, err := s.readCoredumpRecordsWithFilter()
    if err != nil {
        return nil, fmt.Errorf("读取筛选记录失败: %w", err)
    }

    result.TotalRecords = len(filteredRecords)
    result.FilteredRecords = len(filteredRecords)
    logging.InfoLogger.Infof("[%s] 服务端筛选获取到 %d 条待处理记录", taskID, len(filteredRecords))

    // 步骤3: 重置超时的处理中记录
    timeoutRecords := s.filter.GetTimeoutProcessingRecords(filteredRecords)
    if len(timeoutRecords) > 0 {
        logging.InfoLogger.Infof("[%s] 发现 %d 条超时记录，正在重置", taskID, len(timeoutRecords))
        err = s.statusManager.ResetTimeoutRecords(timeoutRecords)
        if err != nil {
            logging.ErrorLogger.Errorf("[%s] 重置超时记录失败: %v", taskID, err)
        }
    }

    // 步骤4: 批量处理记录
    for _, record := range filteredRecords {
        err := s.processRecord(record, result)
        if err != nil {
            logging.ErrorLogger.Errorf("[%s] 处理记录失败 [%s]: %v", taskID, record.RecordID, err)
            result.FailedRecords++

            // 更新为失败状态
            statusErr := s.statusManager.UpdateProcessingStatus(record.RecordID, StatusFailed, "", err.Error())
            if statusErr != nil {
                logging.ErrorLogger.Errorf("[%s] 更新失败状态失败 [%s]: %v", taskID, record.RecordID, statusErr)
            }
        } else {
            result.SuccessRecords++
        }
    }
    
    result.EndTime = time.Now()
    result.Duration = result.EndTime.Sub(result.StartTime)
    
    logging.InfoLogger.Infof("[%s] 处理完成: 总计=%d, 筛选=%d, 成功=%d, 失败=%d, 状态更新=%d, 耗时=%v", 
        taskID, result.TotalRecords, result.FilteredRecords, result.SuccessRecords, 
        result.FailedRecords, result.UpdatedRecords, result.Duration)
    
    return result, nil
}

// readCoredumpRecordsPaginated 分页读取飞书多维表格中的Coredump记录
func (s *CoredumpService) readCoredumpRecordsPaginated() ([]*CoredumpRecord, error) {
    var allRecords []*CoredumpRecord
    pageToken := ""
    pageCount := 0
    
    for {
        pageCount++
        logging.InfoLogger.Infof("读取第 %d 页数据", pageCount)
        
        // 构建查询请求
        req := larkbitable.NewSearchAppTableRecordReqBuilder().
            AppToken(s.config.CoredumpAppToken).
            TableId(s.config.CoredumpTableID).
            PageSize(s.config.PageSize).
            PageToken(pageToken).
            Body(larkbitable.NewSearchAppTableRecordReqBodyBuilder().
                FieldNames(s.config.FieldNames).
                Build()).
            Build()
        
        // 调用飞书API
        resp, err := s.feishuClient.Bitable.AppTableRecord.Search(context.Background(), req)
        if err != nil {
            return nil, fmt.Errorf("飞书API调用失败 (第%d页): %w", pageCount, err)
        }
        
        if !resp.Success() {
            return nil, fmt.Errorf("飞书API返回错误 (第%d页): %s", pageCount, resp.Msg)
        }
        
        // 解析记录
        records, err := s.parseFeishuRecords(resp.Data.Items)
        if err != nil {
            return nil, fmt.Errorf("解析飞书记录失败 (第%d页): %w", pageCount, err)
        }
        
        allRecords = append(allRecords, records...)
        logging.InfoLogger.Infof("第 %d 页读取到 %d 条记录", pageCount, len(records))
        
        // 检查是否还有更多数据
        if !resp.Data.HasMore {
            break
        }
        pageToken = resp.Data.PageToken
        
        // 分页间隔，避免API限流
        time.Sleep(100 * time.Millisecond)
    }
    
    logging.InfoLogger.Infof("分页读取完成，共 %d 页，总计 %d 条记录", pageCount, len(allRecords))
    return allRecords, nil
}

// readCoredumpRecordsWithFilter 使用服务端筛选读取记录（性能优化）
func (s *CoredumpService) readCoredumpRecordsWithFilter() ([]*CoredumpRecord, error) {
    logging.InfoLogger.Info("开始使用服务端筛选读取Coredump记录")
    startTime := time.Now()

    var allRecords []*CoredumpRecord
    pageToken := ""
    pageSize := s.config.PageSize
    totalApiCalls := 0

    // 构建筛选条件
    filter := s.buildCoredumpFilter()

    for {
        totalApiCalls++

        // 构建请求
        reqBuilder := larkbitable.NewSearchAppTableRecordReqBuilder().
            AppToken(s.config.CoredumpAppToken).
            TableId(s.config.CoredumpTableID)

        bodyBuilder := larkbitable.NewSearchAppTableRecordReqBodyBuilder().
            PageSize(pageSize).
            FieldNames(s.config.FieldNames).
            Filter(filter)

        if pageToken != "" {
            bodyBuilder.PageToken(pageToken)
        }

        req := reqBuilder.Body(bodyBuilder.Build()).Build()

        // 调用API
        resp, err := s.feishuClient.Bitable.AppTableRecord.Search(context.Background(), req)
        if err != nil {
            return nil, fmt.Errorf("调用飞书API失败 (第%d次调用): %w", totalApiCalls, err)
        }

        if !resp.Success() {
            return nil, fmt.Errorf("飞书API返回错误 (第%d次调用): %s", totalApiCalls, resp.Msg)
        }

        // 解析记录
        records, err := s.parseRecords(resp.Data.Items)
        if err != nil {
            return nil, fmt.Errorf("解析记录失败 (第%d次调用): %w", totalApiCalls, err)
        }

        allRecords = append(allRecords, records...)
        logging.DebugLogger.Debugf("第%d次API调用获取到 %d 条记录", totalApiCalls, len(records))

        // 检查是否还有更多数据
        if !resp.Data.HasMore {
            break
        }

        pageToken = *resp.Data.PageToken

        // API限流控制
        time.Sleep(100 * time.Millisecond)
    }

    duration := time.Since(startTime)
    logging.InfoLogger.Infof("服务端筛选完成: 共%d次API调用, 获取%d条记录, 耗时%v",
        totalApiCalls, len(allRecords), duration)

    return allRecords, nil
}

// buildCoredumpFilter 构建Coredump记录筛选条件
func (s *CoredumpService) buildCoredumpFilter() map[string]interface{} {
    return map[string]interface{}{
        "conjunction": "and",
        "children": []map[string]interface{}{
            // 条件组1: 是否需要同步Bug系统 = "Y"
            {
                "conjunction": "and",
                "conditions": []map[string]interface{}{
                    {
                        "field_name": s.config.FieldMapping.SyncRequiredField,
                        "operator":   "is",
                        "value":      []string{"Y"},
                    },
                },
            },
            // 条件组2: 是否已同步bug系统 为空或 = "N"
            {
                "conjunction": "or",
                "conditions": []map[string]interface{}{
                    {
                        "field_name": s.config.FieldMapping.SyncStatusField,
                        "operator":   "isEmpty",
                        "value":      []string{},
                    },
                    {
                        "field_name": s.config.FieldMapping.SyncStatusField,
                        "operator":   "is",
                        "value":      []string{"N"},
                    },
                },
            },
            // 条件组3: 处理状态 为空、待处理或失败
            {
                "conjunction": "or",
                "conditions": []map[string]interface{}{
                    {
                        "field_name": s.config.FieldMapping.ProcessingStatusField,
                        "operator":   "isEmpty",
                        "value":      []string{},
                    },
                    {
                        "field_name": s.config.FieldMapping.ProcessingStatusField,
                        "operator":   "is",
                        "value":      []string{"待处理"},
                    },
                    {
                        "field_name": s.config.FieldMapping.ProcessingStatusField,
                        "operator":   "is",
                        "value":      []string{"失败"},
                    },
                },
            },
        },
    }
}

// processRecord 处理单条记录
func (s *CoredumpService) processRecord(record *CoredumpRecord, result *ProcessResult) error {
    logging.InfoLogger.Infof("开始处理记录 [%s]: SN=%s", record.RecordID, record.SN)

    // 步骤1: 标记为处理中（如果还不是）
    if record.ProcessingStatus != StatusProcessing {
        err := s.statusManager.UpdateProcessingStatus(record.RecordID, StatusProcessing, "", "")
        if err != nil {
            logging.ErrorLogger.Errorf("更新处理状态失败 [%s]: %v", record.RecordID, err)
            // 继续处理，不因状态更新失败而中断
        }
    }

    // 步骤2: 字段映射
    bugInfo, err := s.fieldMapper.MapToBugInfo(record)
    if err != nil {
        errMsg := fmt.Sprintf("字段映射失败: %v", err)
        result.Errors = append(result.Errors, errMsg)
        return err
    }
    
    // 步骤2: 提交Bug
    response, err := s.bugSubmitter.SubmitBug(bugInfo)
    if err != nil {
        errMsg := fmt.Sprintf("Bug提交失败: %v", err)
        result.Errors = append(result.Errors, errMsg)
        return err
    }
    
    // 步骤3: 提取Bug ID（从响应中解析）
    bugID := s.extractBugIDFromResponse(response)
    
    // 步骤4: 更新状态为成功
    err = s.statusManager.UpdateProcessingStatus(record.RecordID, StatusSuccess, bugID, "")
    if err != nil {
        errMsg := fmt.Sprintf("状态更新失败: %v", err)
        result.Errors = append(result.Errors, errMsg)
        logging.ErrorLogger.Errorf("状态更新失败 [%s]: %v", record.RecordID, err)
        // 注意：这里不返回错误，因为Bug已经提交成功
    }
    
    logging.InfoLogger.Infof("记录处理成功 [%s]: BugID=%s", record.RecordID, bugID)
    return nil
}

// extractBugIDFromResponse 从Bug提交响应中提取Bug ID
func (s *CoredumpService) extractBugIDFromResponse(response map[string]interface{}) string {
    // 根据实际的Bug系统响应格式解析Bug ID
    if bugID, ok := response["bug_id"].(string); ok {
        return bugID
    }
    
    if id, ok := response["id"].(string); ok {
        return id
    }
    
    // 如果无法解析，生成一个临时ID
    return fmt.Sprintf("UNKNOWN_%d", time.Now().Unix())
}

// parseFeishuRecords 解析飞书记录
func (s *CoredumpService) parseFeishuRecords(items []*larkbitable.AppTableRecord) ([]*CoredumpRecord, error) {
    var records []*CoredumpRecord
    
    for _, item := range items {
        record, err := s.parseFeishuRecord(item)
        if err != nil {
            logging.ErrorLogger.Errorf("解析记录失败 [%s]: %v", item.RecordId, err)
            continue
        }
        records = append(records, record)
    }
    
    return records, nil
}

// parseFeishuRecord 解析单条飞书记录
func (s *CoredumpService) parseFeishuRecord(item *larkbitable.AppTableRecord) (*CoredumpRecord, error) {
    record := &CoredumpRecord{
        RecordID: item.RecordId,
    }
    
    // 解析各个字段
    if sn, ok := item.Fields[s.config.FieldMapping.SNField].(string); ok {
        record.SN = sn
    }
    
    if component, ok := item.Fields[s.config.FieldMapping.ComponentField].(string); ok {
        record.CoredumpComponent = component
    }
    
    if version, ok := item.Fields[s.config.FieldMapping.SoftwareVersionField].(string); ok {
        record.SoftwareVersion = version
    }
    
    if model, ok := item.Fields[s.config.FieldMapping.DeviceModelField].(string); ok {
        record.DeviceModel = model
    }
    
    if url, ok := item.Fields[s.config.FieldMapping.CoredumpURLField].(string); ok {
        record.CoredumpCollectURL = url
    }
    
    if stackInfo, ok := item.Fields[s.config.FieldMapping.StackInfoField].(string); ok {
        record.StackInfo = stackInfo
    }
    
    if desc, ok := item.Fields[s.config.FieldMapping.DescriptionField].(string); ok {
        record.Description = desc
    }
    
    // 解析同步控制字段
    if syncRequired, ok := item.Fields[s.config.FieldMapping.SyncRequiredField].(string); ok {
        record.SyncRequired = syncRequired
    }
    
    if syncStatus, ok := item.Fields[s.config.FieldMapping.SyncStatusField].(string); ok {
        record.SyncStatus = syncStatus
    }
    
    if bugID, ok := item.Fields[s.config.FieldMapping.BugIDField].(string); ok {
        record.BugID = bugID
    }
    
    // 解析人员字段
    record.ComponentResponsible = s.parsePersonFields(item.Fields[s.config.FieldMapping.ComponentResponsibleField])
    record.ProcessResponsible = s.parsePersonFields(item.Fields[s.config.FieldMapping.ProcessResponsibleField])
    
    // 解析时间字段
    if timeStr, ok := item.Fields[s.config.FieldMapping.CoredumpTimeField].(string); ok {
        if t, err := time.Parse("2006-01-02 15:04:05", timeStr); err == nil {
            record.CoredumpTime = t
        }
    }
    
    return record, nil
}

// parsePersonFields 解析人员字段
func (s *CoredumpService) parsePersonFields(field interface{}) []FeishuPersonField {
    var persons []FeishuPersonField
    
    if personList, ok := field.([]interface{}); ok {
        for _, p := range personList {
            if personMap, ok := p.(map[string]interface{}); ok {
                person := FeishuPersonField{}
                
                if id, ok := personMap["id"].(string); ok {
                    person.ID = id
                }
                if name, ok := personMap["name"].(string); ok {
                    person.Name = name
                }
                if email, ok := personMap["email"].(string); ok {
                    person.Email = email
                }
                if avatar, ok := personMap["avatar_url"].(string); ok {
                    person.AvatarURL = avatar
                }
                
                persons = append(persons, person)
            }
        }
    }
    
    return persons
}
```

## ⚙️ 配置文件

### config/coredump_config.yaml

```yaml
coredump:
  # 飞书多维表格配置
  coredump_app_token: "your_app_token_here"
  coredump_table_id: "your_table_id_here"
  page_size: 100
  field_names:
    - "SN"
    - "coredump收集url"
    - "coredump时间"
    - "coredump组件"
    - "堆栈信息"
    - "组件负责人"
    - "进程负责人"
    - "设备型号"
    - "软件版本"
    - "记录日期"
    - "说明"
    - "是否已知问题"
    - "处理结果"
    - "修复版本"
    - "文件名"
    - "是否需要同步Bug系统"    # 筛选字段
    - "是否已同步bug系统"      # 同步状态字段
    - "处理状态"              # 处理状态字段
    - "Bug系统ID"             # Bug ID字段
    - "处理时间"              # 处理时间字段
    - "错误信息"              # 错误信息字段
    - "重试次数"              # 重试次数字段
    - "最后更新时间"          # 最后更新时间字段
  
  # 定时任务配置
  scheduler:
    enabled: true
    cron_expr: "0 0 * * * *"    # 每小时执行一次
    max_run_time: 30            # 最大运行30分钟
    concurrent: false           # 不允许并发执行
  
  # 默认用户配置
  default_submitter: "coredump_system"
  default_charge_user: "wuzhensheng"
  default_pstl: "wangchunping"
  
  # 处理配置
  batch_size: 50
  process_timeout: "30s"
  processing_timeout: 60        # 处理超时时间（分钟）
  retry_count: 3
  skip_known_issues: true
  
  # 字段映射配置
  field_mapping:
    sn_field: "SN"
    coredump_url_field: "coredump收集url"
    coredump_time_field: "coredump时间"
    component_field: "coredump组件"
    stack_info_field: "堆栈信息"
    component_responsible_field: "组件负责人"
    process_responsible_field: "进程负责人"
    device_model_field: "设备型号"
    software_version_field: "软件版本"
    description_field: "说明"
    is_known_issue_field: "是否已知问题"
    # 新增筛选和状态字段
    sync_required_field: "是否需要同步Bug系统"
    sync_status_field: "是否已同步bug系统"
    bug_id_field: "Bug系统ID"
```

## 🚀 API接口

### 接口列表

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 手动处理 | POST | `/api/coredump/process` | 手动触发Coredump处理 |
| 处理状态 | GET | `/api/coredump/status` | 获取处理状态统计 |
| 启动定时任务 | POST | `/api/coredump/scheduler/start` | 启动定时任务 |
| 停止定时任务 | POST | `/api/coredump/scheduler/stop` | 停止定时任务 |
| 定时任务状态 | GET | `/api/coredump/scheduler/status` | 获取定时任务状态 |
| 处理历史 | GET | `/api/coredump/history` | 获取处理历史记录 |

### 使用示例

```bash
#!/bin/bash

BASE_URL="http://localhost:8080/api/coredump"

echo "=== Coredump增强版API使用示例 ==="

# 1. 启动定时任务
echo "1. 启动定时任务..."
curl -X POST "$BASE_URL/scheduler/start" | jq .

echo -e "\n"

# 2. 查看定时任务状态
echo "2. 查看定时任务状态..."
curl "$BASE_URL/scheduler/status" | jq .

echo -e "\n"

# 3. 手动触发处理
echo "3. 手动触发处理..."
curl -X POST "$BASE_URL/process" | jq .

echo -e "\n"

# 4. 查看处理状态
echo "4. 查看处理状态..."
curl "$BASE_URL/status" | jq .

echo -e "\n"

# 5. 停止定时任务
echo "5. 停止定时任务..."
curl -X POST "$BASE_URL/scheduler/stop" | jq .

echo -e "\n=== 示例完成 ==="
```

## 🔧 部署步骤

### 1. 环境准备

```bash
# 安装依赖
go mod tidy

# 创建必要目录
mkdir -p config logs cache

# 设置权限
chmod 755 config logs cache
```

### 2. 配置文件设置

```bash
# 复制配置模板
cp docs/coredump/coredump_config.yaml config/

# 编辑配置文件
vim config/coredump_config.yaml

# 更新application.yml
vim application.yml
```

### 3. 权限验证

```bash
# 验证飞书API权限
curl -X POST "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal" \
  -H "Content-Type: application/json" \
  -d '{"app_id":"your_app_id","app_secret":"your_app_secret"}'

# 验证Bug系统连接
curl "http://bugs.ruijie.com.cn/bug_switch/service/outinterface_submitBugInfo"
```

### 4. 启动服务

```bash
# 编译
go build -o coredump-system main.go

# 启动
./coredump-system

# 验证服务
curl http://localhost:8080/health
```

### 5. 启动定时任务

```bash
# 启动定时任务
curl -X POST http://localhost:8080/api/coredump/scheduler/start

# 查看状态
curl http://localhost:8080/api/coredump/scheduler/status
```

## 🎯 核心特性总结

### ✅ 已实现功能

1. **定时处理机制**
   - 支持Cron表达式配置
   - 可启动/停止定时任务
   - 支持手动触发处理
   - 任务超时控制

2. **条件筛选**
   - 基于"是否需要同步Bug系统"字段筛选
   - 基于"是否已同步bug系统"字段筛选
   - 支持跳过已知问题
   - 详细的筛选统计

3. **分页读取**
   - 真正的分页处理机制
   - 避免内存溢出
   - API限流控制
   - 分页统计信息

4. **状态回填**
   - Bug提交成功后自动回填状态
   - 支持批量状态更新
   - 权限验证机制
   - 详细的回填日志

5. **错误处理**
   - 分阶段错误处理
   - 详细的错误日志
   - 失败重试机制
   - 错误统计分析

### 🔧 技术优势

- **模块化设计**: 各组件职责清晰，易于维护
- **配置驱动**: 支持灵活的配置管理
- **并发控制**: 避免重复执行和资源冲突
- **监控友好**: 详细的日志和状态跟踪
- **扩展性强**: 易于添加新功能和集成

这个增强版实现完全满足了您提出的所有需求，提供了完整的双向同步机制和强大的定时处理能力。
