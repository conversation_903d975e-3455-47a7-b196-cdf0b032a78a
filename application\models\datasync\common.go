package datasync

import "irisAdminApi/application/models"

type BugSyncRecord struct {
	models.ModelBase
	Url           string `gorm:"not null; type:varchar(300)" json:"url"`
	Body          string `gorm:"not null; type:varchar(300)" json:"body"`
	Method        string `gorm:"not null; type:varchar(60)" json:"method"`
	MinModifyDate string `gorm:"not null; type:varchar(60)" json:"min_modify_date"`
	MaxModifyDate string `gorm:"not null; type:varchar(60)" json:"max_modify_date"`
	State         string `gorm:"not null; type:varchar(60)" json:"state"`
	Message       string `json:"message"`
}

type CommonNtosFile struct {
	models.ModelBase
	FullPath string `gorm:"not null; type:varchar(500)" json:"full_path"`
	File     string `gorm:"not null; type:varchar(200)" json:"file"`
}

type CommonComponent struct {
	models.ModelBase
	Path            string `gorm:"uniqueIndex: idx_path_component; not null; type:varchar(500)" json:"path"`
	ComponentPacket string `gorm:"uniqueIndex: idx_path_component; not null; type:varchar(200)" json:"component_packet" update:"1"`
	Component       string `gorm:"uniqueIndex: idx_path_component; not null; type:varchar(200)" json:"component" update:"1"`
	Owner           string `gorm:"type:varchar(50)" json:"owner" update:"1"`
	Comment         string `gorm:"type:varchar(50)" json:"comment" update:"1"`
}

type CommonNtosFileComponent struct {
	models.ModelBase
	FullPath        string `gorm:"index; not null; type:varchar(500)" json:"full_path"`
	File            string `gorm:"index; not null; type:varchar(200)" json:"file"`
	Path            string `gorm:"index; not null; type:varchar(500)" json:"path"`
	ComponentPacket string `gorm:"index; not null; type:varchar(200)" json:"component_packet"`
	Component       string `gorm:"index; not null; type:varchar(200)" json:"component"`
}

type CommonComponentUser struct {
	models.ModelBase
	ComponentID uint `json:"component_id"`
	UserID      uint `json:"component"`
}

type CommonNtosFileWorkPackage struct {
	models.ModelBase
	FullPath       string `gorm:"index; not null; type:varchar(500)" json:"full_path"`
	WorkPackage    string `gorm:"index; not null; type:varchar(200)" json:"work_package"`
	TargetBranch   string `gorm:"index; not null; type:varchar(200)" json:"target_branch"`
	TargetProject  string `gorm:"index; not null; type:varchar(200)" json:"target_project"`
	ReleaseProject string `gorm:"index; not null; type:varchar(200)" json:"release_project"`
	MergeRequestID uint   `gorm:"not null" json:"merge_request_id"`
}
