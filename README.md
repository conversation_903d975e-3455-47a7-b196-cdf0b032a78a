<h1 align="center">irisAdminApi</h1>

<div align="center">
    <a href="https://codecov.io/gh/snowlyg/IrisAdminApi"><img src="https://codecov.io/gh/snowlyg/IrisAdminApi/branch/master/graph/badge.svg" alt="Code Coverage"></a>
    <a href="https://goreportcard.com/report/github.com/snowlyg/IrisAdminApi"><img src="https://goreportcard.com/badge/github.com/snowlyg/IrisAdminApi" alt="Go Report Card"></a>
    <a href="https://godoc.org/github.com/snowlyg/IrisAdminApi"><img src="https://godoc.org/github.com/snowlyg/IrisAdminApi?status.svg" alt="GoDoc"></a>
    <a href="https://github.com/snowlyg/IrisAdminApi/blob/master/LICENSE"><img src="https://img.shields.io/github/license/snowlyg/IrisAdminApi" alt="Licenses"></a>
</div>

> 简单项目仅供学习，欢迎指点！

[IRIS V12 中文文档](https://www.snowlyg.com/chapter/1)

###### `Iris-go` 学习交流 QQ 群 ：`676717248`

<a target="_blank" href="//shang.qq.com/wpa/qunwpa?idkey=cc99ccf86be594e790eacc91193789746af7df4a88e84fe949e61e5c6d63537c"><img border="0" src="http://pub.idqqimg.com/wpa/images/group.png" alt="Iris-go" title="Iris-go"></a>
---

#### 项目开发过程详解

1.[Iris-go 项目登陆 API 构建细节实现过程](https://blog.snowlyg.com/posts/iris-go-api-1/)

2.[iris + casbin 从陌生到学会使用的过程](http://localhost:1313/posts/iris-go-api-2/)

---

- 安装项目依赖

>加载依赖管理包 (解决国内下载依赖太慢问题)
>使用国内七牛云的 go module 镜像。
>
>参考 <https://github.com/goproxy/goproxy.cn。>
>
>阿里： <https://mirrors.aliyun.com/goproxy/>
>
>官方： <https://goproxy.io/>
>
>中国：<https://goproxy.cn>
>
>其他：<https://gocenter.io>

##### golang 1.13+ 可以直接执行

```shell script
go env -w GO111MODULE=on
go env -w GOPROXY=https://goproxy.cn,direct
```

- 复制配置文件

```
cp application.example.yml application.yml
```

> 修改配置文件 `application.yml`

- 运行项目

>如果想使用 `go run main.go --config` 命令运行,注意不用 --config 指定配置路径，将无法加载配置文件

```
 go run main.go --config your_config_path
```

>推荐使用 air 热编译工具

```
# 安装工具 air
go get -u github.com/cosmtrek/air

cp .air.example.conf  .air.conf # 复制后修改 .air.conf 文件，默认为 mac 环境

air
```

- 填充数据, 注意配置文件同项目配置文件，权限数据位于 tools/seed/data

```
go build -o seed tools/seed/main.go
./seed --config your_config_path --path youer_seed_data_path
```

#### postman 接口

```text
https://www.getpostman.com/collections/048078cdfd16667352b0
```

#### 运行测试

```
go test ./...
```

#### 感谢

[JetBrains](https://www.jetbrains.com/?from=IrisAdminApi) 对本项目的支持。

#### 创建数据库
CREATE DATABASE IF NOT EXISTS iris DEFAULT CHARSET utf8 COLLATE utf8_general_ci;
CREATE DATABASE IF NOT EXISTS gfast DEFAULT CHARSET utf8 COLLATE utf8_general_ci;
CREATE DATABASE IF NOT EXISTS fileOutTest DEFAULT CHARSET utf8 COLLATE utf8_general_ci;
<!-- rizla main.go --config .\application.yml -->
go run main.go --config application.yml
go run ./tools/seed/main.go --path ./tools/seed/data --config ./application.yml

交叉编译
SET CGO_ENABLED=0
SET GOOS=linux
SET GOARCH=amd64

go build -o fileOutApi main.go
go build -o seed tools/seed/main.go

docker run --restart=always -d -p 6060:80 --name nginx-test-web   -v /root/nginx/www:/var/www   -v /root/nginx/conf/:/etc/nginx/   -v /root/nginx/logs:/var/log/nginx   nginx

查看mysql建表语句
show create table

insert into feature_proc_defs (name, version, resource) values ("规则库发布流程", "20220214", '[{"name": "创建规则库","nodeId": "start","prevNodeId": ""},{"name": "测试验证","nodeId": "test_check","prevNodeId": "start","assignee": 1},{"name": "试点验证","nodeId": "experiment","prevNodeId": "start","assignee": 1},{"name": "功能开发","nodeId": "feature_dev","prevNodeId": "start","assignee": 1},{"name": "QA审计","nodeId": "qa_audit","prevNodeId": "feature_dev","assignee": 1},{"name": "QA审计","nodeId": "qa_audit","prevNodeId": "test_check","assignee": 1},{"name": "QA审计","nodeId": "qa_audit","prevNodeId": "experiment","assignee": 1},{"name": "CMA发布","nodeId": "cmd_release","prevNodeId": "qa_audit","assignee": 1}]');

# 添加 PM 节点到下生产流程

本文档说明如何将 PM 节点添加到下生产流程中，以及如何验证更改是否成功。

## 背景

原先的下生产流程是从"创建下生产"直接到"PGTTL提交系统模板"，现在需要在中间添加一个"PM提交程序"节点，用于处理程序信息。

## 更改内容

1. 在"创建下生产"和"PGTTL提交系统模板"之间添加"PM提交程序"节点
2. 修改"PGTTL提交系统模板"节点的前置节点为"PM提交程序"

## 应用更改

执行以下步骤来应用更改：

1. 备份数据库（重要！）
2. 执行 `update_workflow.sql` 脚本，更新流程定义：

```bash
mysql -u [用户名] -p[密码] [数据库名] < update_workflow.sql
```

## 验证更改

执行以下步骤来验证更改是否成功：

1. 查询数据库，确认流程定义已更新：

```sql
SELECT resource FROM production_proc_defs WHERE name = '下生产流程';
```

2. 创建一个新的下生产流程实例，确认流程中包含 PM 节点
3. 测试完整的流程，确保系统能够正确处理新的流程

## 回滚

如果需要回滚更改，执行以下步骤：

1. 执行 `rollback_workflow.sql` 脚本，恢复原始流程定义：

```bash
mysql -u [用户名] -p[密码] [数据库名] < rollback_workflow.sql
```

## 注意事项

- 更改流程定义后，只有新创建的流程实例会使用新的定义，已有的流程实例不受影响
- 确保系统中的代码已经支持处理 PM 节点，包括 `handlePMSubmitNode` 函数和在 `UpdateProcInst` 函数中添加对 PM 节点的处理逻辑
