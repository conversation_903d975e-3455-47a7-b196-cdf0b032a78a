#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
import email.header
from email import message_from_string
import base64
import gitlab
import logging


logging.basicConfig(level=logging.DEBUG,
                    filename='/tmp/push_queue.log',
                    filemode='a',
                    format='%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s')


try:
    gl = gitlab.Gitlab.from_config('git', ['/etc/python-gitlab.cfg'])
except Exception as e:
    exit(1)


def decode_mime_words(s):
    return u''.join(
        word.decode(encoding or 'utf8') if isinstance(word, bytes) else word
        for word, encoding in email.header.decode_header(s))


mail = sys.argv[1]
_from = '<EMAIL>'


def filter_mail(to, body, project_id, mr_iid):
    project = gl.projects.get(project_id)
    mr = project.mergerequests.get(mr_iid)
    maintainers = []
    reviewers = []
    for i in project.members.list():
        if i.access_level == 40:
            maintainers.append(i.username + '@ruijie.com.cn')
    author = mr.author.get('username') + '@ruijie.com.cn'
    for i in mr.reviewers:
        reviewers.append(i.get('username') + '@ruijie.com.cn')

    if 'start a new discussion' in body or f'Merge Request !{mr_iid} was merged' in body:
        if to != author:
            logging.debug('start a new discussion, %s %s', to, author)
            return False

    if 'pushed new commits' in body:
        if to not in reviewers:
            logging.debug('pushed new commits, %s, %s', to, reviewers)
            return False

    if 'commented on a discussion' in body:
        return False
    return True



def parser_mail_data(mail):
    msg = message_from_string(mail)
    sub = msg.get('subject').split("|")
    sub = "".join(['[{}]'.format(i.strip()) for i in sub])
    sub = '[gitlab]' + sub
    _from = '<EMAIL>'
    to = msg.get('to')

    project_id = msg.get('X-GitLab-Project-Id')
    mr_iid = msg.get('X-GitLab-MergeRequest-IID')
    '''
    if to not in ['<EMAIL>']:
        to = '<EMAIL>'
    '''
    body = parseEmail(msg)
    try:
        logging.debug('%s %s %s %s', to, body, project_id, mr_iid)
        if not filter_mail(to, body, project_id, mr_iid):
            exit(1)
    except Exception as e:
        logging.error(str(e))
        exit(1)

    to = '<EMAIL>'
    print(f'{_from}|{to}|{sub}|{body}')


def parseEmail(e):
    # 解析邮件/信体
    # 循环信件中的每一个mime的数据块
    body = ''
    for part in e.walk():
        # 这里要判断是否是multipart，是的话，里面的数据是一个message 列表
        if not part.is_multipart():
            contenttype = part.get_content_type()
            # 如果是附件，这里就会取出附件的文件名
            if contenttype == 'text/plain':
                logging.debug("%s %s", contenttype, part.get('Content-Transfer-Encoding'))
                if part.get('Content-Transfer-Encoding') == 'base64':
                    body = part.get_payload(decode=False)
                    body = base64.b64decode(body).decode('utf8')
                else:
                    body = part.get_payload(decode=True).decode('utf-8')
    if body:
        logging.debug('body is =======> %s', body)
        _body = body.replace("\r\n", "\n").split('\n')
        __body = []
        for i in _body:
            if i and not i.startswith('>') and ('http://**************:8080' in i or len(i) < 200):
                __body.append(i)
        result = []
        if len(__body[0]) > 51:
            result.append('{}......(已隐藏后续内容，详情请登陆gitlab)'.format(__body[0][:51]))
        else:
            result.append('{}'.format(__body[0]))
        result = result + __body[-2:]

        body = "".join(['<p>{}</p>'.format(i) for i in result])
        logging.debug('result is =======>%s', body)

    return body


parser_mail_data(mail)
