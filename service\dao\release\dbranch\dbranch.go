package dbranch

import (
	"errors"
	"fmt"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/release"
	unitpackage "irisAdminApi/service/dao/buildfarm/dproject"
	"irisAdminApi/service/dao/release/dbranchaudit"
	"irisAdminApi/service/dao/release/dproject"
	"irisAdminApi/service/dao/user/duser"
)

const ModelName = "项目分支管理"

type ReleaseBranch struct {
	ID               uint                    `json:"id"`
	ReleaseProjectID uint                    `json:"release_project_id"`
	Status           uint                    `json:"status"` // 0:申请中 1：评审结束 2：取消
	UnitPackage      uint                    `json:"unit_package"`
	Package          *unitpackage.Response   `gorm:"-" json:"package"`
	WorkPackage      uint                    `json:"work_package"`
	BranchType       uint                    `json:"branch_type"` // 0:code, 1:docs
	HandleType       uint                    `json:"handle_type"` // 0:新建分支
	BaseBranch       string                  `json:"base_branch"`
	BaseVerison      string                  `json:"base_version"`
	NewBranch        string                  `json:"new_branch"`
	BranchComment    string                  `json:"branch_comment"`
	UserID           uint                    `json:"user_id"`
	ReleaseProject   *dproject.Response      `gorm:"-" json:"project"`
	UpdatedAt        time.Time               `json:"updated_at"`
	CreatedAt        time.Time               `json:"created_at"`
	Notices          []uint                  `gorm:"-" json:"notices,omitempty"`
	Writes           []uint                  `gorm:"-" json:"writes,omitempty"`
	Auditors         []uint                  `gorm:"-" json:"auditors,omitempty"`
	User             *duser.ApprovalResponse `gorm:"-" json:"user,omitempty"`
	AutoCreate       uint                    `json:"auto_create"`
	Writable         bool                    `gorm:"-" json:"writable"`
	MrControlStatus  uint                    `json:"mr_control_status"`
}

type ListResponse struct {
	ReleaseBranch
}

type Request struct {
	ReleaseProjectID uint   `json:"release_project_id"`
	Status           uint   `json:"status"`
	UnitPackage      uint   `json:"unit_package"`
	WorkPackage      uint   `json:"work_package"`
	BranchType       uint   `json:"branch_type"` // 1:code, 2:docs
	HandleType       uint   `json:"handle_type"` // 1:新建分支
	BaseBranch       string `json:"base_branch"`
	BaseVerison      string `json:"base_version"`
	NewBranch        string `json:"new_branch"`
	BranchComment    string `json:"branch_comment"`
	WriteIds         []uint `json:"write_ids" form:"write_ids"`
	NoticeIds        []uint `json:"notice_ids" form:"notice_ids"`
	AuditorIds       []uint `json:"auditor_ids" form:"auditor_ids"`
}

func (a *ReleaseBranch) ModelName() string {
	return ModelName
}

func Model() *release.ReleaseBranch {
	return &release.ReleaseBranch{}
}

func (a *ReleaseBranch) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	getProjects(items)
	getUnitPackages(items)
	getUsers(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *ReleaseBranch) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *ReleaseBranch) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *ReleaseBranch) CreateV2(object interface{}) error {
	return nil
}

func (a *ReleaseBranch) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *ReleaseBranch) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *ReleaseBranch) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *ReleaseBranch) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *ReleaseBranch) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	getProject(u)
	GetDetail(u)
	return nil
}

func (u *ReleaseBranch) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}

	return nil
}

func getProject(branch *ReleaseBranch) {
	project := dproject.Response{}
	project.Find(branch.ReleaseProjectID)
	branch.ReleaseProject = &project
}

func getUnitPackages(branches []*ListResponse) {
	_packages, _ := unitpackage.FindAll()
	projectMap := make(map[uint]*unitpackage.Response)
	for _, _package := range _packages {
		projectMap[_package.GitlabId] = _package
	}
	for _, branch := range branches {
		branch.Package = projectMap[branch.UnitPackage]
	}
}

func getProjects(branches []*ListResponse) {
	projectIds := []uint{}
	for _, branch := range branches {
		projectIds = append(projectIds, branch.ReleaseProjectID)
	}

	projects, _ := dproject.FindInIds(projectIds)
	projectMap := make(map[uint]*dproject.Response)
	for _, project := range projects {
		projectMap[project.ID] = project
	}
	for _, branch := range branches {
		branch.ReleaseProject = projectMap[branch.ReleaseProjectID]
	}
}

func getUsers(branches []*ListResponse) {
	userIds := []uint{}
	for _, branch := range branches {
		userIds = append(userIds, branch.UserID)
	}
	users, _ := duser.FindSimpleInIds(userIds)
	userMap := make(map[uint]*duser.ApprovalResponse)
	for _, user := range users {
		userMap[user.Id] = user
	}
	for _, branch := range branches {
		branch.User = userMap[branch.UserID]
	}
}

func AppendAuditors(id uint, ids []uint) error {
	if len(ids) == 0 {
		return nil
	}
	users, err := duser.FindInIds(ids)
	if err != nil {
		return err
	}
	ReleaseBranchAuditors := []release.ReleaseBranchAuditor{}
	for _, item := range users {
		ReleaseBranchAuditors = append(ReleaseBranchAuditors, release.ReleaseBranchAuditor{
			ReleaseBranchID: id,
			UserID:          item.ID,
		})
	}

	err = easygorm.GetEasyGormDb().Model(&release.ReleaseBranchAuditor{}).Where("release_branch_id", id).Delete(&release.ReleaseBranchAuditor{}).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	err = easygorm.GetEasyGormDb().Model(&release.ReleaseBranchAuditor{}).Create(ReleaseBranchAuditors).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}
	return nil
}

func AppendNotices(id uint, ids []uint) error {
	if len(ids) == 0 {
		return nil
	}
	users, err := duser.FindInIds(ids)
	if err != nil {
		return err
	}
	ReleaseBranchNotices := []release.ReleaseBranchNotice{}
	for _, item := range users {
		ReleaseBranchNotices = append(ReleaseBranchNotices, release.ReleaseBranchNotice{
			ReleaseBranchID: id,
			UserID:          item.ID,
		})
	}
	err = easygorm.GetEasyGormDb().Where("release_branch_id", id).Delete(&release.ReleaseBranchNotice{}).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}
	err = easygorm.GetEasyGormDb().Create(ReleaseBranchNotices).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}
	return nil
}

func AppendWrites(id uint, ids []uint) error {
	if len(ids) == 0 {
		return nil
	}
	users, err := duser.FindInIds(ids)
	if err != nil {
		return err
	}
	ReleaseBranchWrites := []release.ReleaseBranchWrite{}
	for _, item := range users {
		ReleaseBranchWrites = append(ReleaseBranchWrites, release.ReleaseBranchWrite{
			ReleaseBranchID: id,
			UserID:          item.ID,
		})
	}
	err = easygorm.GetEasyGormDb().Where("release_branch_id", id).Delete(&release.ReleaseBranchWrite{}).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	err = easygorm.GetEasyGormDb().Create(ReleaseBranchWrites).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}
	return nil
}

func GetDetail(branch *ReleaseBranch) {
	var _notices []*release.ReleaseBranchNotice
	var _auditors []*release.ReleaseBranchAuditor
	var _writes []*release.ReleaseBranchWrite
	if err := easygorm.GetEasyGormDb().Select("user_id").Find(&_notices, "release_branch_id = ?", branch.ID).Error; err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
	}
	for _, item := range _notices {
		branch.Notices = append(branch.Notices, item.UserID)
	}

	if err := easygorm.GetEasyGormDb().Select("user_id").Find(&_auditors, "release_branch_id = ?", branch.ID).Error; err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
	}
	for _, item := range _auditors {
		branch.Auditors = append(branch.Auditors, item.UserID)
	}

	if err := easygorm.GetEasyGormDb().Select("user_id").Find(&_writes, "release_branch_id = ?", branch.ID).Error; err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
	}
	for _, item := range _writes {
		branch.Writes = append(branch.Writes, item.UserID)
	}
}

func FindAllAudited(userId uint, name, status, project, unit_package, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse
	db := easygorm.GetEasyGormDb().Model(Model())
	if userId == 1 {
		db = db.Where("status = ?", status)
	} else {
		audits, err := dbranchaudit.FindAllByUserId(userId)
		if err != nil {
			logging.ErrorLogger.Errorf("get list count err ", err)
			return nil, err
		}

		var branchIds []uint
		for _, audit := range audits {
			branchIds = append(branchIds, audit.ReleaseBranchID)
		}
		projects, err := dproject.FindMyProject(userId, "", "", "", 1, -1)
		var projectIds []uint
		for _, project := range projects["items"].([]*dproject.ListResponse) {
			projectIds = append(projectIds, project.ID)
		}
		db = db.Where("(id in ? or release_project_id in ?) and status = ?", branchIds, projectIds, status)
	}

	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}

	if len(project) > 0 {
		db = db.Where("release_project_id = ?", project)
	}

	if len(unit_package) > 0 {
		db = db.Where("unit_package = ?", unit_package)
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	getProjects(items)
	getUnitPackages(items)
	getUsers(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func FindAllAuditedPassedByProjectID(id uint, name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	db = db.Where("release_project_id = ? and status = 1", id)

	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	getProjects(items)
	getUnitPackages(items)
	getUsers(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func FindAllAuditByUserID(userId uint, name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse
	var _auditors []*release.ReleaseBranchAuditor

	// err := easygorm.GetEasyGormDb().Select("id").Find(&projects, "pmo = ? or pm = ?", userId, userId).Error
	err := easygorm.GetEasyGormDb().Select("release_branch_id").Find(&_auditors, "user_id = ?", userId).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}
	branchesMap := map[uint]bool{}
	for _, _auditor := range _auditors {
		branchesMap[_auditor.ReleaseBranchID] = true
	}
	audits, err := dbranchaudit.FindAllByUserId(userId)
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}
	for _, audit := range audits {
		if _, ok := branchesMap[audit.ReleaseBranchID]; ok {
			delete(branchesMap, audit.ReleaseBranchID)
		}
	}
	branchIds := []uint{}

	for key := range branchesMap {
		branchIds = append(branchIds, key)
	}
	db := easygorm.GetEasyGormDb().Model(Model())
	db = db.Where("id in ? and status = 0", branchIds)
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err = db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	getProjects(items)
	getUnitPackages(items)
	getUsers(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func FindMyBranch(userId uint, name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	db = db.Where("user_id = ?", userId)

	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	getProjects(items)
	getUnitPackages(items)
	getUsers(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func CheckBranchExists(unitpackage uint, newbranch string) error {
	var item ReleaseBranch
	db := easygorm.GetEasyGormDb().Model(Model())
	db = db.Where("unit_package = ? and new_branch = ? and status in ? ", unitpackage, newbranch, []uint{0, 1}).Find(&item)
	if item.ID > 0 {
		return errors.New("已经存在相同分支未审核，请确认！")
	}
	return nil
}

func FindReleaseBranches() ([]*ListResponse, error) {
	var items []*ListResponse

	unitPackage, err := unitpackage.FindProduct()
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return items, err
	}
	err = easygorm.GetEasyGormDb().Model(Model()).Where("unit_package = ? and status = 1", unitPackage.GitlabId).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return items, err
	}
	getProjects(items)
	getUnitPackages(items)
	getUsers(items)
	return items, nil
}

func FindAuditedPassedByProjectID(id uint) (unitpackage.Response, []*ListResponse, error) {
	branches := []*ListResponse{}
	build_product := unitpackage.Response{}
	err := build_product.FindEx("name", "basesys/build-product")
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return build_product, branches, err
	}

	db := easygorm.GetEasyGormDb().Model(Model())
	db = db.Where("release_project_id = ? and status = 1 and unit_package = ?", id, build_product.GitlabId).Order("id desc").Limit(1).Find(&branches)
	if err := db.Error; err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return build_product, branches, err
	}
	return build_product, branches, nil
}

func FindBranchByProjectIDAndStatus(releaseProjectId, status uint) ([]*ListResponse, error) {
	items := []*ListResponse{}
	db := easygorm.GetEasyGormDb().Model(Model())
	db = db.Where("release_project_id = ? and status = ?", releaseProjectId, status).Order("id desc").Find(&items)
	if err := db.Error; err != nil {
		logging.ErrorLogger.Errorf("find branch by id and status err ", err)
		return items, err
	}
	return items, nil
}

func FindBranchByProjectIDAndBaseBranchAndStatus(releaseProjectId uint, baseBranch string, status uint) ([]*ListResponse, error) {
	items := []*ListResponse{}
	db := easygorm.GetEasyGormDb().Model(Model())
	db = db.Where("release_project_id = ? and base_branch = ? and status = ?", releaseProjectId, baseBranch, status).Order("id desc").Find(&items)
	if err := db.Error; err != nil {
		logging.ErrorLogger.Errorf("find branch by id and status err ", err)
		return items, err
	}
	return items, nil
}

func FindBranchByProjectIDAndStatusAndUnitPackage(releaseProjectId, unitPackageID, status uint) ([]*ListResponse, error) {
	items := []*ListResponse{}
	db := easygorm.GetEasyGormDb().Model(Model())
	db = db.Where("release_project_id = ? and unit_package = ? and status = ?", releaseProjectId, unitPackageID, status).Order("id desc").Find(&items)
	if err := db.Error; err != nil {
		logging.ErrorLogger.Errorf("find branch by id and status err ", err)
		return items, err
	}
	return items, nil
}

func FindBranchByUnitPackageAndBaseBranchAndStatus(unitPackageID uint, baseBranch string, status uint) ([]*ListResponse, error) {
	items := []*ListResponse{}
	db := easygorm.GetEasyGormDb().Model(Model())
	db = db.Where("unit_package = ? and base_branch = ? and status = ?", unitPackageID, baseBranch, status).Order("id desc").Find(&items)
	if err := db.Error; err != nil {
		logging.ErrorLogger.Errorf("find branch by id and status err ", err)
		return items, err
	}
	return items, nil
}

func FindBranchByUnitPackageAndNewBranchAndStatus(unitPackgeID uint, newBranch string) ([]*ListResponse, error) {
	items := []*ListResponse{}
	db := easygorm.GetEasyGormDb().Model(Model())
	db = db.Where("unit_package = ? and new_branch = ? and status = 1", unitPackgeID, newBranch).Order("id desc").Find(&items)
	return items, db.Error
}

func GetBaseBranch(unitPackgeID uint, newBranch string) (string, error) {
	item := ReleaseBranch{}
	db := easygorm.GetEasyGormDb().Model(Model())
	db = db.Where("unit_package = ? and new_branch = ? and status = 1", unitPackgeID, newBranch).Order("id desc").Find(&item)
	return item.BaseBranch, db.Error
}

func FindBranchInNames(names []string) ([]*ListResponse, error) {
	items := []*ListResponse{}
	db := easygorm.GetEasyGormDb().Model(Model())
	db = db.Where("new_branch in ? and status = 1", names).Find(&items).Order("updated_at desc")
	getUnitPackages(items)
	return items, db.Error
}

func FindBranchByProjectIDAndStatusV2(releaseProjectId, status uint) ([]*ListResponse, error) {
	items := []*ListResponse{}
	db := easygorm.GetEasyGormDb().Model(Model())
	db = db.Where("release_project_id = ? and status = ?", releaseProjectId, status).Find(&items)
	if err := db.Error; err != nil {
		logging.ErrorLogger.Errorf("find branch by id and status err ", err)
		return items, err
	}
	getUnitPackages(items)
	return items, nil
}

func FindBranchByUnitpackageIDAndStatusV2(unitpackageID, status uint) ([]*ListResponse, error) {
	items := []*ListResponse{}
	db := easygorm.GetEasyGormDb().Model(Model())
	db = db.Where("unit_package_id = ? and status = ?", unitpackageID, status).Find(&items)
	if err := db.Error; err != nil {
		logging.ErrorLogger.Errorf("find branch by id and status err ", err)
		return items, err
	}
	getUnitPackages(items)
	return items, nil
}

func GetBranchByReleaseProjectID(ReleaseProjectID uint, newBranch string) (ReleaseBranch, error) {
	item := ReleaseBranch{}
	db := easygorm.GetEasyGormDb().Model(Model())
	db = db.Where("release_project_id = ? and new_branch = ? and status = ?", ReleaseProjectID, newBranch, 1).Find(&item)
	return item, db.Error
}

func FindBranchesByUnitpackage(unitpackage string) ([]*ListResponse, error) {
	items := []*ListResponse{}
	db := easygorm.GetEasyGormDb().Model(Model())
	db = db.Where("unit_package = ? and status = ?", unitpackage, 1).Find(&items)
	return items, db.Error
}

func All(name, projectID, unitPackage, newBranch, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	if projectID != "" {
		db = db.Where("release_project_id = ?", projectID)
	}

	if unitPackage != "" {
		db = db.Where("unit_package = ?", unitPackage)
	}

	if newBranch != "" {
		db = db.Where("new_branch = ?", newBranch)
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	getProjects(items)
	getUnitPackages(items)
	getUsers(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}
