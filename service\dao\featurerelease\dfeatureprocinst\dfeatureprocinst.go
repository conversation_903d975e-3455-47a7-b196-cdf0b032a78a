package dfeatureprocinst

import (
	"encoding/json"
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/featurerelease"
	"irisAdminApi/service/dao/featurerelease/dfeature"
	"irisAdminApi/service/dao/featurerelease/dfeatureproctask"
	"irisAdminApi/service/dao/user/duser"
	"strings"
)

const ModelName = "规则库流程实例"

type FeatureProcInst struct {
	ID    uint   `json:"id"`
	Title string `json:"title"`
	// 当前节点
	NodeID string `json:"node_id"`
	// 审批人
	// Candidate string `json:"candidate"`
	// 当前任务
	TaskID      int                              `json:"task_id"`
	StartUserID uint                             `json:"start_user_id"`
	FeatureID   uint                             `json:"feature_id"`
	Status      uint                             `json:"status"`
	Resource    string                           `gorm:"size:10000" json:"resource,omitempty"`
	User        *duser.ApprovalResponse          `gorm:"-" json:"user"`
	Feature     *dfeature.Response               `gorm:"-" json:"feature"`
	Tasks       []*dfeatureproctask.ListResponse `gorm:"-" json:"tasks"`
}

type ListResponse struct {
	FeatureProcInst
}

type Request struct {
	Title string `json:"title"`
	// 当前节点
	NodeID string `json:"node_id"`
	// 审批人
	// Candidate string `json:"candidate"`
	// 当前任务
	TaskID      int    `json:"task_id"`
	StartUserID string `json:"start_user_id"`
	FeatureID   string `json:"feature_id"`
	Resource    string `gorm:"size:10000" json:"resource,omitempty"`
	IsFinished  bool   `gorm:"default:false" json:"is_finished"`
}

type Node struct {
	Name       string `json:"name,omitempty"`
	NodeID     string `json:"nodeId,omitempty"`
	PrevNodeID string `json:"prevNodeId,omitempty"`
	Assignee   uint   `json:"assignee"`
}

func (a *FeatureProcInst) ModelName() string {
	return ModelName
}

func Model() *featurerelease.FeatureProcInst {
	return &featurerelease.FeatureProcInst{}
}

func (a *FeatureProcInst) All(name, status, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	if len(status) > 0 {
		db = db.Where("status in ?", strings.Split(status, ","))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	formatResponses(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *FeatureProcInst) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *FeatureProcInst) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *FeatureProcInst) CreateV2(object interface{}) error {
	return nil
}

func (a *FeatureProcInst) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *FeatureProcInst) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *FeatureProcInst) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *FeatureProcInst) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *FeatureProcInst) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	formatResponse(u)
	return nil
}

func (u *FeatureProcInst) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindAll() ([]*FeatureProcInst, error) {
	var items []*FeatureProcInst

	if err := easygorm.GetEasyGormDb().Model(Model()).Find(&items).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return items, err
	}
	return items, nil
}

func FindInIds(ids []uint) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id in ?", ids).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	formatResponses(items)
	return items, nil
}

func DeleteByUUID(uuid string) error {
	err := easygorm.GetEasyGormDb().Unscoped().Where("uuid = ?", uuid).Delete(Model()).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func UpdateStatus(id uint, object map[string]interface{}) error {

	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

// func FormatResponse(items []*ListResponse) {
// 	userIds := []uint{}
// 	for _, item := range items {
// 		userIds = append(userIds, item.UserID)
// 	}
// 	users, _ := duser.FindSimpleInIds(userIds)
// 	var userMap = make(map[uint]*duser.ApprovalResponse)
// 	for _, user := range users {
// 		userMap[user.Id] = user
// 	}
// 	for _, item := range items {
// 		item.User = userMap[item.UserID]
// 	}
// }

func createProcInst(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func GetPrevNodeIDs(resource string, nodeID string) ([]string, error) {
	prevNodeIDs := []string{}

	nodes := []*Node{}
	err := json.Unmarshal([]byte(resource), &nodes)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return prevNodeIDs, err
	}
	for _, node := range nodes {
		if node.NodeID == nodeID {
			prevNodeIDs = append(prevNodeIDs, node.PrevNodeID)
		}
	}
	return prevNodeIDs, nil
}

func GetNextNodeIDs(resource string, nodeID string) ([]string, error) {
	nextNodeIDs := []string{}

	nodes := []*Node{}
	err := json.Unmarshal([]byte(resource), &nodes)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return nextNodeIDs, err
	}
	for _, node := range nodes {
		if node.PrevNodeID == nodeID {
			nextNodeIDs = append(nextNodeIDs, node.NodeID)
		}
	}
	return nextNodeIDs, nil
}

func GetNode(resource string, nodeID string) (*Node, error) {
	nodes := []*Node{}

	err := json.Unmarshal([]byte(resource), &nodes)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return nil, err
	}
	for _, node := range nodes {
		if node.NodeID == nodeID {
			return node, nil
		}
	}
	return nil, nil
}

func GetNodes(resource string) ([]*Node, error) {
	nodes := []*Node{}

	err := json.Unmarshal([]byte(resource), &nodes)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return nodes, err
	}

	return nodes, nil
}

func formatResponses(items []*ListResponse) {
	featureIds := []uint{}
	procInstIDs := []uint{}
	userIds := []uint{}
	featureMap := map[uint]*dfeature.Response{}
	for _, item := range items {
		featureIds = append(featureIds, item.FeatureID)
		userIds = append(userIds, item.StartUserID)
		procInstIDs = append(procInstIDs, item.ID)
	}
	features, err := dfeature.FindInIds(featureIds)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return
	}

	for _, feature := range features {
		featureMap[feature.ID] = &feature.Response
	}

	users, _ := duser.FindSimpleInIds(userIds)
	var userMap = make(map[uint]*duser.ApprovalResponse)
	for _, user := range users {
		userMap[user.Id] = user
	}
	taskMap := map[uint][]*dfeatureproctask.ListResponse{}
	tasks, err := dfeatureproctask.FindInProcInstIDs(procInstIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("find tasks err ", err)
		return
	}
	for _, task := range tasks {
		taskMap[task.ProcInstID] = append(taskMap[task.FeatureProcTask.ProcInstID], task)
	}
	for _, item := range items {
		item.Feature = featureMap[item.FeatureID]
		item.User = userMap[item.StartUserID]
		item.Tasks = taskMap[item.ID]
	}

}

func formatResponse(item *FeatureProcInst) {
	featureIds := []uint{}
	procInstIDs := []uint{}
	userIds := []uint{}
	featureMap := map[uint]*dfeature.Response{}
	featureIds = append(featureIds, item.FeatureID)
	userIds = append(userIds, item.StartUserID)
	procInstIDs = append(procInstIDs, item.ID)

	features, err := dfeature.FindInIds(featureIds)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return
	}

	for _, feature := range features {
		featureMap[feature.ID] = &feature.Response
	}

	users, _ := duser.FindSimpleInIds(userIds)
	var userMap = make(map[uint]*duser.ApprovalResponse)
	for _, user := range users {
		userMap[user.Id] = user
	}
	taskMap := map[uint][]*dfeatureproctask.ListResponse{}
	tasks, err := dfeatureproctask.FindInProcInstIDs(procInstIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("find tasks err ", err)
		return
	}
	for _, task := range tasks {
		taskMap[task.ProcInstID] = append(taskMap[task.FeatureProcTask.ProcInstID], task)
	}
	item.Feature = featureMap[item.FeatureID]
	item.User = userMap[item.StartUserID]
	item.Tasks = taskMap[item.ID]

}

var FeatureProcInstUserMap = map[uint]uint{}
var FeatureIDProcInstIDMap = map[uint]uint{}

func UpdateFeatureProccessingCache(featureIds []uint) error {
	featureProcInsts := []*ListResponse{}
	if err := easygorm.GetEasyGormDb().Model(Model()).Where("feature_id in ?", featureIds).Find(&featureProcInsts).Error; err != nil {
		return err
	}
	for _, inst := range featureProcInsts {
		FeatureProcInstUserMap[inst.FeatureID] = inst.StartUserID
		FeatureIDProcInstIDMap[inst.FeatureID] = inst.ID
	}
	return nil
}
