package cache

import (
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/fileout"
	"sync"
)

type Response struct {
	Id           uint   `json:"id"`
	Name         string `json:"name"`
	Objdump      bool   `json:"objdump"`
	FileCheck    string `json:"file_check"`
	FileMime     string `json:"file_mime"`
	Ext          string `json:"ext"`
	CustomHeader string `json:"custom_header"`
	Size         int    `json:"size"`
	Users        string `json:"users"`
}

var AutoAuditFiles []*Response

func GetAutoAuditFileCache() error {
	logging.DebugLogger.Debug("update auto audit file cache")
	AutoAuditFiles = []*Response{}
	err := easygorm.GetEasyGormDb().Model(&fileout.AutoAuditFile{}).Find(&AutoAuditFiles).Error
	if err != nil {
		return err
	}
	return nil
}

type AutoAduitMd5Response struct {
	Md5 string `json:"md5"`
}

var AutoAuditMd5Cache sync.Map
var AutoAuditMd5Change int64

func GetAutoAuditMd5Cache() error {
	var rc = GetRedisClusterClient()
	defer rc.Close()
	var AutoAuditMd5s []*AutoAduitMd5Response
	logging.DebugLogger.Debug("update auto audit md5 cache")
	err := easygorm.GetEasyGormDb().Model(&fileout.AutoAuditMd5{}).Where("id > ?", AutoAuditMd5Change).Find(&AutoAuditMd5s).Error
	if err != nil {
		return err
	}

	for _, md5 := range AutoAuditMd5s {
		_, err := rc.Set(fmt.Sprintf("FC:%s", md5.Md5), "1")
		if err != nil {
			logging.ErrorLogger.Error("update auto audit md5 cache err", err)
			return err
		}
	}

	return nil
}
