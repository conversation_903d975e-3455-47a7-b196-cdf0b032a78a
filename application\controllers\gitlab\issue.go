package gitlab

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/user/dgitlabtoken"
	"net/http"
	"strconv"

	"github.com/kataras/iris/v12"
)

func GetUserAllIssues(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	token := dgitlabtoken.Response{}
	err = token.FindEx("user_id", strconv.FormatUint(uint64(id), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("get token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if len(token.Token) == 0 {
		logging.ErrorLogger.Errorf("user doesn't hava token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "没有可用的gitlab token，请前往用户资料进行添加"))
		return
	}
	url := fmt.Sprintf("%s/api/%s/issues?private_token=%s&scope=all", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, token.Token)

	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))

	if page > 0 {
		url = fmt.Sprintf("%s&page=%d", url, page)
	}
	if pageSize > 0 {
		url = fmt.Sprintf("%s&per_page=%d", url, pageSize)
	}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	result, err := libs.HandlerRequest(req)
	if err != nil {
		logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	for _, item := range result["items"].([]map[string]interface{}) {
		if len([]byte(item["title"].(string))) > 512 {
			item["title"] = string([]byte(item["title"].(string)[0:512])) + "****(长度过长)"
		}
		if len([]byte(item["description"].(string))) > 512 {
			item["description"] = string([]byte(item["description"].(string)[0:512])) + "****(长度过长)"
		}
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func GetProjectIssueNotes(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get id for get git project issue notes err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	projectId, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project id for get git project issue notes err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	issueId, err := GetIssueId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get issue id for get git project issue notes err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	token := dgitlabtoken.Response{}
	err = token.FindEx("user_id", strconv.FormatUint(uint64(id), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("get token for get git project issue notes err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if len(token.Token) == 0 {
		logging.ErrorLogger.Errorf("user doesn't hava token for get git project issue notes err ", err)
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "没有可用的gitlab token，请前往用户资料进行添加"))
		return
	}
	///projects/:id/issues/:issue_iid/notes?sort=asc&order_by=updated_at
	url := fmt.Sprintf("%s/api/%s/projects/%d/issues/%d/notes?state=all&private_token=%s&sort=asc&order_by=updated_at", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, projectId, issueId, token.Token)

	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))

	if page > 0 {
		url = fmt.Sprintf("%s&page=%d", url, page)
	}
	if pageSize > 0 {
		url = fmt.Sprintf("%s&per_page=%d", url, pageSize)
	}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		logging.ErrorLogger.Errorf("create reuqest for get git project issue notes err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	result, err := libs.HandlerRequest(req)
	if err != nil {
		logging.ErrorLogger.Errorf("handle reuqest for get git project issue notes err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	for _, item := range result["items"].([]map[string]interface{}) {
		if len([]byte(item["body"].(string))) > 512 {
			item["body"] = string([]byte(item["body"].(string)[0:512])) + "****(长度过长)"
		}
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func GetIssueId(ctx iris.Context) (uint, error) {
	id, err := ctx.Params().GetUint("issue_id")
	if err != nil {
		logging.ErrorLogger.Errorf("dao get id get err ", err)
		return 0, err
	}
	return id, nil
}
