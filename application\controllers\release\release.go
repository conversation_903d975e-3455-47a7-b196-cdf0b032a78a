package release

import (
	"archive/zip"
	"bytes"
	"fmt"
	"io"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	buildfarm_project "irisAdminApi/service/dao/buildfarm/dproject"
	"irisAdminApi/service/dao/release/dbuildname"
	"irisAdminApi/service/dao/release/dbuildtime"
	"irisAdminApi/service/dao/release/dcomplieinfo"
	"irisAdminApi/service/dao/release/dproductmodel"
	"irisAdminApi/service/dao/release/dproject"
	"irisAdminApi/service/dao/release/drelease"
	"irisAdminApi/service/dao/release/dreleaseattr"
	"irisAdminApi/service/dao/release/dstatus"
	"irisAdminApi/service/transaction/release/transrelease"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/tidwall/gjson"
)

func GetReleases(ctx iris.Context) {
	// id, err := dao.GetAuthId(ctx)
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	softversion := ctx.FormValue("softversion")
	softnumber := ctx.FormValue("softnumber")
	start := ctx.FormValue("start") //开始日期
	end := ctx.FormValue("end")     //结束日期
	releaseattrID, _ := strconv.Atoi(ctx.FormValue("release_attr_id"))
	projectID, _ := strconv.Atoi(ctx.FormValue("project_id"))
	buildNameID, _ := strconv.Atoi(ctx.FormValue("build_name_id"))
	productModelID, _ := strconv.Atoi(ctx.FormValue("product_model_id"))

	list, err := drelease.AllArchiveReleases("all", name, sort, orderBy, softversion, softnumber, start, end, page, pageSize, releaseattrID, projectID, buildNameID, productModelID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetAllArchiveReleases(ctx iris.Context) {
	// id, err := dao.GetAuthId(ctx)
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }

	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	softversion := ctx.FormValue("softversion")
	softnumber := ctx.FormValue("softnumber")
	start := ctx.FormValue("start") //开始日期
	end := ctx.FormValue("end")     //结束日期
	releaseattrID, _ := strconv.Atoi(ctx.FormValue("release_attr_id"))
	projectID, _ := strconv.Atoi(ctx.FormValue("project_id"))
	buildNameID, _ := strconv.Atoi(ctx.FormValue("build_name_id"))
	productModelID, _ := strconv.Atoi(ctx.FormValue("product_model_id"))

	list, err := drelease.AllArchiveReleases("archive", name, sort, orderBy, softversion, softnumber, start, end, page, pageSize, releaseattrID, projectID, buildNameID, productModelID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetRelease(ctx iris.Context) {
	info := drelease.ReleaseRelease{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

func CreateRelease(ctx iris.Context) {

	userId, _ := dao.GetAuthId(ctx)
	request := &drelease.Request{}
	if err := ctx.ReadJSON(request); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	uuid := libs.GetUUID()
	object := map[string]interface{}{
		"UserID":    userId,
		"CreatedAt": time.Now(),
		"Uuid":      uuid,
	}
	project := dproject.Response{}
	if err := project.Find(request.ReleaseProjectID); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "该项目不存在！"))
		return
	}

	status := dstatus.Response{}
	err := status.FindEx("name", "申请中")
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "查找项目状态异常，请重试。"))
		return
	}

	if project.StatusID == status.ID {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "该项目状态为申请中，无法发布"))
		return
	}

	if userId != 1 && userId != project.CmaID {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "只允许CMA进行发布操作！"))
		return
	}

	var buildInfo string

	if libs.HasPrefix(request.BuildFarmLink, "http://aqyfzx.ruijie.net") {
		if !libs.HasSuffix(request.BuildFarmLink, "/") {
			request.BuildFarmLink = request.BuildFarmLink + "/"
		}
		// if strings.Contains(request.BuildFarmLink, "http://") {
		url := fmt.Sprintf("%s/%s", request.BuildFarmLink, "build_info")
		// jobId = strings.Split(url, "/")[4]
		req, err := http.NewRequest("GET", url, nil)
		if err != nil {
			logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "编译农场链接异常，无法获取信息，请确认！"))
			return
		}
		result, err := libs.HandlerRequest(req)
		if err != nil {
			logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "编译农场链接异常，无法获取信息，请确认！"))
			return
		}
		if data, ok := result["data"]; ok {
			buildInfo = data.(string)
		}
	} else {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "编译农场链接异常，无法获取信息，请确认！"))
		return
	}

	if buildInfo == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "编译农场链接异常，无法获取信息，请确认！"))
		return
	}

	t := reflect.TypeOf(*request)
	v := reflect.ValueOf(*request)
	for k := 0; k < t.NumField(); k++ {
		if t.Field(k).Type == reflect.TypeOf([]uint{}) {
			continue
		}
		object[t.Field(k).Name] = v.Field(k).Interface()
	}
	buildNameId, err := dbuildname.GetOrCreate(request.BuildNameID)
	if err != nil {
		logging.ErrorLogger.Errorf("create release project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	object["BuildNameID"] = buildNameId

	productModelId, err := dproductmodel.GetOrCreate(buildNameId, request.ProductModelID)
	if err != nil {
		logging.ErrorLogger.Errorf("create release productModel get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	object["ProductModelID"] = productModelId
	err = dao.Create(&drelease.ReleaseRelease{}, ctx, object)
	if err != nil {
		logging.ErrorLogger.Errorf("create release get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	release := drelease.ReleaseRelease{}
	err = release.FindEx("uuid", uuid)
	if err != nil {
		logging.ErrorLogger.Errorf("create release project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	buildInfoObjects := []map[string]interface{}{}
	var buildTimeStr string
	for _, item := range strings.Split(buildInfo, "\n") {
		_project := map[string]interface{}{}
		if item == "" {
			continue
		}
		if strings.Contains(item, "BuildTime") {
			buildTimeStr = strings.Split(item, " ")[1] + " " + strings.Split(item, " ")[2]
			continue
		}
		slice := strings.Split(strings.Replace(item, "git:", "git ", -1), " ")
		_project["version"] = slice[len(slice)-1]
		unit := libs.PickProjectFromBuildInfo(item)
		git_project := buildfarm_project.Response{}
		err = git_project.FindEx("name", unit)
		var openMr string
		if err != nil {
			openMr = "查询编译仓库信息错误"
		} else {
			if git_project.Id > 0 {
				url := fmt.Sprintf("%s/api/%s/projects/%d/merge_requests?private_token=%s&simple=true&per_page=10&scope=all&target_branch=%s&state=opened", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, git_project.GitlabId, libs.Config.Buildfarm.Token, slice[2])
				req, err := http.NewRequest("GET", url, nil)
				if err != nil {
					logging.ErrorLogger.Errorf("query mr for project err ", err)
				}

				_result, err := libs.HandlerRequest(req)
				if err != nil {
					logging.ErrorLogger.Errorf("handle query mr for project err ", err)
				}
				if _, ok := _result["total"]; ok {
					if _result["total"] == 0 {
						openMr = "无"
					} else {
						openMr = "存在未关闭mr"
					}
				} else {
					openMr = "查询MR失败"
				}
			} else {
				openMr = "未查到此仓库信息"
			}

		}
		buildInfoObjects = append(buildInfoObjects, map[string]interface{}{
			"ReleaseID":     release.ID,
			"Version":       slice[len(slice)-1],
			"Url":           slice[1],
			"ReleaseBranch": slice[2],
			"Unit":          unit,
			"OpenMR":        openMr,
		})
	}

	err = dcomplieinfo.BatchCreate(buildInfoObjects)
	if err != nil {
		release.Delete(release.ID)
		logging.ErrorLogger.Errorf("create release project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	buildTime := dbuildtime.Response{}
	err = buildTime.Create(map[string]interface{}{
		"ReleaseID": release.ID,
		"BuildTime": buildTimeStr,
	})

	if err != nil {
		release.Delete(release.ID)
		logging.ErrorLogger.Errorf("create release project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	// go ArchiveBuildFile(request.BuildFarmLink, "NTOS", release.Project.Name, release.BuildName.Name, release.ReleasedAt, release.SystemSoftwareNumber, release.ID)

	ctx.JSON(response.NewResponse(response.NoErr.Code, request, response.NoErr.Msg))
	return
}

func UpdateRelease(ctx iris.Context) {

	userId, _ := dao.GetAuthId(ctx)
	release := drelease.ReleaseRelease{}
	err := dao.Find(&release, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	request := &drelease.Request{}
	if err := ctx.ReadJSON(request); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	object := map[string]interface{}{
		"UserID":    userId,
		"UpdatedAt": time.Now(),
	}

	var buildInfo string
	if libs.HasPrefix(request.BuildFarmLink, "http://") {
		if !libs.HasSuffix(request.BuildFarmLink, "/") {
			request.BuildFarmLink = request.BuildFarmLink + "/"
		}
		// if strings.Contains(request.BuildFarmLink, "http://") {
		url := fmt.Sprintf("%s%s", request.BuildFarmLink, "build_info")
		// jobId = strings.Split(url, "/")[4]
		req, err := http.NewRequest("GET", url, nil)
		if err != nil {
			logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
		}
		result, err := libs.HandlerRequest(req)
		if err != nil {
			logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
		}
		if data, ok := result["data"]; ok {
			buildInfo = data.(string)
		}
	}
	if buildInfo == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "编译农场链接异常，无法获取信息，请确认！"))
		return
	}
	t := reflect.TypeOf(*request)
	v := reflect.ValueOf(*request)
	for k := 0; k < t.NumField(); k++ {
		if t.Field(k).Type == reflect.TypeOf([]uint{}) {
			continue
		}
		object[t.Field(k).Name] = v.Field(k).Interface()
	}
	buildNameId, err := dbuildname.GetOrCreate(request.BuildNameID)
	if err != nil {
		logging.ErrorLogger.Errorf("create release project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	object["BuildNameID"] = buildNameId

	productModelId, err := dproductmodel.GetOrCreate(buildNameId, request.ProductModelID)
	if err != nil {
		logging.ErrorLogger.Errorf("create release project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	object["ProductModelID"] = productModelId
	err = dao.Create(&drelease.ReleaseRelease{}, ctx, object)
	if err != nil {
		logging.ErrorLogger.Errorf("create release project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	buildInfoObjects := []map[string]interface{}{}
	var buildTimeStr string
	for _, item := range strings.Split(buildInfo, "\n") {
		_project := map[string]interface{}{}
		if item == "" {
			continue
		}
		if strings.Contains(item, "BuildTime") {
			buildTimeStr = strings.Split(item, " ")[1] + " " + strings.Split(item, " ")[2]
			continue
		}
		slice := strings.Split(strings.Replace(item, "git:", "git ", -1), " ")
		_project["version"] = slice[len(slice)-1]
		unit := libs.PickProjectFromBuildInfo(item)
		git_project := buildfarm_project.Response{}
		err = git_project.FindEx("name", unit)
		var openMr string
		if err != nil {
			openMr = "查询编译仓库信息错误"
		} else {
			if git_project.Id > 0 {
				url := fmt.Sprintf("%s/api/%s/projects/%d/merge_requests?private_token=%s&simple=true&per_page=10&scope=all&target_branch=%s&state=opened", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, git_project.GitlabId, libs.Config.Buildfarm.Token, slice[2])
				req, err := http.NewRequest("GET", url, nil)
				if err != nil {
					logging.ErrorLogger.Errorf("query mr for project err ", err)
					openMr = "查询MR失败"
				}

				_result, err := libs.HandlerRequest(req)
				if err != nil {
					logging.ErrorLogger.Errorf("handle query mr for project err ", err)
					openMr = "查询MR失败"
				}

				if _, ok := _result["total"]; ok {
					if _result["total"] == 0 {
						openMr = "无"
					} else {
						openMr = "存在未关闭mr"
					}
				} else {
					openMr = "查询MR失败"
				}
			} else {
				openMr = "未查到此仓库信息"
			}
		}
		buildInfoObjects = append(buildInfoObjects, map[string]interface{}{
			"ReleaseID":     release.ID,
			"Version":       slice[len(slice)-1],
			"Url":           slice[1],
			"ReleaseBranch": slice[2],
			"Unit":          unit,
			"OpenMR":        openMr,
		})
	}
	err = dcomplieinfo.BatchCreate(buildInfoObjects)
	if err != nil {
		release.Delete(release.ID)
		logging.ErrorLogger.Errorf("create release project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	buildTime := dbuildtime.Response{}
	err = buildTime.Create(map[string]interface{}{
		"ReleaseID": release.ID,
		"BuildTime": buildTimeStr,
	})

	if err != nil {
		release.Delete(release.ID)
		logging.ErrorLogger.Errorf("create release project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	// go ArchiveBuildFile(request.BuildFarmLink, "NTOS", release.Project.Name, release.BuildName.Name, release.ReleasedAt, release.SystemSoftwareNumber, release.ID)

}

func GetReleaseAttrs(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dreleaseattr.ListResponse{}, ctx, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetBuildNames(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dbuildname.ListResponse{}, ctx, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetProductModels(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dproductmodel.ListResponse{}, ctx, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

var ArchiveChan = make(chan *drelease.ReleaseRelease)

func ArchiveWorker() {
	for {
		release := <-ArchiveChan
		logging.DebugLogger.Debugf("archive start", release.ID)
		err := ArchiveBuildFile(release.BuildFarmLink, "NTOS", release.Project.Name, release.BuildName.Name, release.ReleasedAt, release.ID, release)
		if err != nil {
			logging.ErrorLogger.Errorf("archive get err", err)
			release.Update(release.ID, map[string]interface{}{
				"Archive": 2,
			})
		}
		// 清理2天前文件
		var out bytes.Buffer
		var stderr bytes.Buffer
		command := "find /tmp/output -maxdepth 1 -mindepth 1 ctime +2 | xargs rm -rf"
		cmd := exec.Command("bash", "-c", command)
		cmd.Stdout = &out
		cmd.Stderr = &stderr

		err = cmd.Run()
		if err != nil {
			logging.ErrorLogger.Errorf("rm old file before one day get err ", err, stderr.String())
		}
	}
}

func RestartWorker() {
	releases, err := drelease.FindAllReleasesByStatus(0)
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
	}
	for _, release := range releases {
		ArchiveChan <- release
	}
}

func ReadUpdateJsonInZipFile(zfp string) (string, error) {
	// 打开 ZIP 文件
	zipFile, err := os.Open(zfp)
	if err != nil {
		return "", err
	}
	defer zipFile.Close()

	// 获取 ZIP 文件的信息
	stat, err := zipFile.Stat()
	if err != nil {
		return "", err
	}

	// 创建一个 zip.Reader
	zipReader, err := zip.NewReader(zipFile, stat.Size())
	if err != nil {
		return "", err
	}

	// 要读取的文件名
	targetFileName := "update.json"

	// 遍历 ZIP 文件中的所有文件
	for _, file := range zipReader.File {
		if file.Name == targetFileName {
			// 打开文件
			rc, err := file.Open()
			if err != nil {
				return "", err
			}
			defer rc.Close()

			// 读取文件内容
			var buf bytes.Buffer
			_, err = io.Copy(&buf, rc)
			if err != nil {
				return "", err
			}
			return buf.String(), nil
		}
	}
	return "", fmt.Errorf("未找到update.json")
}

func ArchiveBuildFile(url, systemName, projectName, buildName, releasedAt string, releaseId uint, release *drelease.ReleaseRelease) error {
	var out bytes.Buffer
	var stderr bytes.Buffer

	// productModel := dproductmodel.ReleaseProductModel{}
	// err := productModel.Find(release.ProductModelID)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf(fmt.Sprintf("update status error: %s", err.Error()))
	// 	release.Update(releaseId, map[string]interface{}{
	// 		"Archive": 2,
	// 	})
	// 	return
	// }

	productModel := release.ProductModel

	jobID := strings.Split(url, "/")[4]
	tmpDir := filepath.Join("/tmp/output", jobID)

	if _, err := os.Stat(tmpDir); os.IsNotExist(err) {
		cmd := exec.Command("wget", "-r", "-np", "-nH", "-R", "index.html", "-e", "robots=off", url)
		cmd.Dir = "/tmp/"
		cmd.Stdout = &out
		cmd.Stderr = &stderr
		err = cmd.Run()
		if err != nil {
			if err := os.RemoveAll(tmpDir); err != nil {
				logging.ErrorLogger.Errorf("remove temp dir get err ", err)
			}
			logging.ErrorLogger.Errorf("wget get err ", err, stderr.String())
			return err
		}
		command := fmt.Sprintf("find %s -name index.html.tmp | xargs rm -rf {}", tmpDir)
		cleanCmd := exec.Command("bash", "-c", command)
		cleanCmd.Run()
	}

	// 修改mv为cp,防止z86归档异常，增加清理1天前文件
	out = bytes.Buffer{}
	stderr = bytes.Buffer{}

	_urlSlice := strings.Split(strings.Replace(strings.TrimSuffix(url, "/"), "http://", "", -1), "/")
	urlSlice := []string{"/tmp"}
	urlSlice = append(urlSlice, _urlSlice[1:]...)

	var packageName, systemSoftwareVersion, systemSoftwareNumber string

	releaseTempPath := filepath.Join(tmpDir, urlSlice[len(urlSlice)-1])
	releaseIdTempPath := filepath.Join(releaseTempPath, "releaseID-bin")

	if release.ReleaseAttr.Name == "正式发布版本" {
		releaseFiles, err := os.ReadDir(releaseTempPath)
		if err != nil {
			logging.ErrorLogger.Errorf("read releaseTempPath fail", releaseTempPath, err)
			return err
		}
		for _, f := range releaseFiles {
			if strings.Contains(f.Name(), strings.ReplaceAll(release.Project.Name, "NTOS", "")) && strings.HasSuffix(f.Name(), ".zip") {
				jsonStr, err := ReadUpdateJsonInZipFile(filepath.Join(releaseTempPath, f.Name()))
				if err != nil {
					return err
				}
				systemSoftwareVersion = gjson.Get(jsonStr, "extend.version").String()
				systemSoftwareNumber = gjson.Get(jsonStr, "extend.system_software_number").String()
				packageName = gjson.Get(jsonStr, "primary.file_name").String()
				break
			}
		}
	} else {
		releaseIdFiles, err := os.ReadDir(releaseIdTempPath)
		if err != nil {
			logging.ErrorLogger.Errorf("read releaseIdTempPath fail", releaseIdTempPath, err)
			return err
		}

		for _, f := range releaseIdFiles {
			if strings.Contains(f.Name(), strings.ReplaceAll(release.Project.Name, "NTOS", "")) && strings.HasSuffix(f.Name(), ".zip") {
				jsonStr, err := ReadUpdateJsonInZipFile(filepath.Join(releaseIdTempPath, f.Name()))
				if err != nil {
					return err
				}
				systemSoftwareVersion = gjson.Get(jsonStr, "extend.version").String()
				systemSoftwareNumber = gjson.Get(jsonStr, "extend.system_software_number").String()
				packageName = gjson.Get(jsonStr, "primary.file_name").String()
				break
			}
		}
	}

	if systemSoftwareVersion == "" || systemSoftwareNumber == "" || packageName == "" {
		return fmt.Errorf("json解析错误 %s %s %s", systemSoftwareVersion, systemSoftwareNumber, packageName)
	}

	dest := filepath.Join(libs.Config.Release.Releasepath, systemName, projectName, buildName, strings.Replace(productModel.Name, " ", "_", -1), strings.Replace(releasedAt, "-", "", -1)+"_"+systemSoftwareNumber)
	err := os.MkdirAll(dest, 0755)
	os.Chmod(dest, 0755)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("mkdir before scp error: %s", err.Error()))
		return err
	}

	src := filepath.Join(filepath.Join(urlSlice...), "*")
	out = bytes.Buffer{}
	stderr = bytes.Buffer{}
	command := fmt.Sprintf("cp -r %s %s", src, dest)
	mvCmd := exec.Command("bash", "-c", command)
	mvCmd.Stdout = &out
	mvCmd.Stderr = &stderr
	err = mvCmd.Run()
	if err != nil {
		logging.ErrorLogger.Errorf("cp get err ", err, stderr.String())
		return err
	}

	updateReleaseObject := map[string]interface{}{
		"Archive":               1,
		"ReleaseLink":           fmt.Sprintf("%s/%s", libs.Config.Release.Host, strings.Replace(dest, libs.Config.Release.Releasepath, "", -1)),
		"SystemSoftwareVersion": systemSoftwareVersion,
		"SystemSoftwareNumber":  systemSoftwareNumber,
		"PackageName":           packageName,
	}

	err = transrelease.UpdateReleaseTransaction(releaseId, updateReleaseObject)
	if err != nil {
		return err
	}
	return nil
}

func ArchiveRelease(ctx iris.Context) {
	release := drelease.ReleaseRelease{}
	err := dao.Find(&release, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if release.Archive == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "正在归档中，请忽重复提交"))
		return
	}
	if release.Archive == 1 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "已归档成功，无法重复归档"))
		return
	}

	err = release.Update(release.ID, map[string]interface{}{
		"Archive": 0,
	})
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("update status error: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ArchiveChan <- &release
	// go ArchiveBuildFile(release.BuildFarmLink, "NTOS", release.Project.Name, release.BuildName.Name, release.ReleasedAt, release.SystemSoftwareNumber, release.ID, &release)
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func DeleteRelease(ctx iris.Context) {
	userId, _ := dao.GetAuthId(ctx)
	release := drelease.ReleaseRelease{}
	err := dao.Find(&release, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if release.Archive == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "正在归档中，无法删除"))
		return
	}
	if release.Archive == 1 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "已归档成功，无法删除"))
		return
	}
	if release.UserID != userId {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "仅允许创建者删除"))
		return
	}

	err = dao.Delete(&release, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func UpdateManufacture(ctx iris.Context) {
	userId, _ := dao.GetAuthId(ctx)

	release := drelease.ReleaseRelease{}
	err := dao.Find(&release, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	project := dproject.Response{}
	if err := project.Find(release.ReleaseProjectID); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "该项目不存在！"))
		return
	}
	if !libs.InArrayUint([]uint{project.PmID, project.PmoID}, userId) {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "只允许PM/PMO进行更新！"))
		return
	}
	request := &drelease.ManufactureRequest{}
	if err := ctx.ReadJSON(request); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	err = dao.Update(&release, ctx, map[string]interface{}{"Manufacture": request.Manufacture})
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return

}
