package fileout

import "irisAdminApi/application/models"

type AutoAuditFile struct {
	models.ModelBase
	Name         string `gorm:"index;not null; type:varchar(60)" json:"name"`
	Objdump      bool   `json:"objdump"`
	FileCheck    string `gorm:"index;not null; type:varchar(60)" json:"file_check"`
	FileMime     string `gorm:"index;not null; type:varchar(60)" json:"file_mime"`
	CustomHeader string `gorm:"type:varchar(60)" json:"custom_header"`
	Ext          string `gorm:"type:varchar(60)" json:"ext"`
	Size         int64  `gorm:"not null;type:bigint(20);default:0" json:"size"`
	Users        string `json:"users"`
}

type AutoAuditMd5 struct {
	models.ModelBase
	Md5 string `gorm:"uniqueIndex;not null; type:varchar(60)" json:"md5"`
}

type UserWhiteList struct {
	models.ModelBase
	UserID      uint   `gorm:"uniqueIndex; not null;" json:"user_id"`
	Fileout     bool   `gorm:"not null;default:false" json:"fileout"`
	Buildfarm   bool   `gorm:"not null;default:false" json:"buildfarm"`
	Opensource  bool   `gorm:"not null;default:false" json:"opensource"`
	KvmDownload bool   `gorm:"not null;default:false" json:"kvm_download"`
	Comment     string `json:"comment"`
}
