package documentauto

import (
	"irisAdminApi/application/models"
)

type DagSerie struct {
	models.ModelBase
	Name          string `gorm:"not null; type:varchar(300)" json:"name" form:"name"`
	Title1        string `gorm:"not null; type:varchar(300)" json:"title1" form:"title1"`
	Title2        string `gorm:"not null; type:varchar(300)" json:"title2" form:"title2"`
	VersionPrefix string `gorm:"not null; type:varchar(300)" json:"version_prefix" form:"version_prefix"`
	DocName       string `gorm:"not null; type:varchar(300)" json:"doc_name" form:"doc_name"`
	DocType       uint   `gorm:"not null; default:0" json:"doc_type" form:"doc_type"`
}

type DagVersion struct {
	models.ModelBase
	VersionNumber string `gorm:"not null; type:varchar(200)" json:"version_number"`
	ReleaseDate   string `gorm:"not null; type:varchar(300)" json:"release_date"`
	Status        string `gorm:"not null; type:varchar(300)" json:"status"`
	Sort          uint   `gorm:"not null; default:0" json:"sort"`
}

type DagTemplate struct {
	models.ModelBase
	SeriesID     uint   `gorm:"not null" json:"series_id"`
	Version      string `gorm:"not null; type:varchar(300)" json:"version"`
	TemplatePath string `gorm:"not null; type:varchar(300)" json:"template_path"`
}

type DagTask struct {
	models.ModelBase
	SeriesIDs       string `gorm:"not null; type:varchar(300)" json:"series_ids"`
	BuildRecordIDs  string `gorm:"not null; type:varchar(300)" json:"build_record_ids"`
	ReleaseVersion  string `gorm:"not null; type:varchar(300)" json:"release_version"`
	VersionNumber   string `gorm:"not null; type:varchar(300)" json:"version_number"`
	ReleaseType     string `gorm:"not null; type:varchar(300)" json:"release_type"`
	BaselineVersion string `gorm:"not null; type:varchar(300)" json:"baseline_version"`
	Description     string `gorm:"not null; type:varchar(300)" json:"description"`
	UserID          uint   `gorm:"not null" json:"user_id"`
	Status          uint   `gorm:"not null; default:0" json:"status"`
	UUID            string `gorm:"not null; type:varchar(300)" json:"uuid"`
	StartDate       string `gorm:"not null; type:varchar(300)" json:"start_date"`
	EndDate         string `gorm:"not null; type:varchar(300)" json:"end_date"`
}

type DagSeriesBuildName struct {
	models.ModelBase
	SeriesID     uint   `gorm:"not null" json:"series_id"`
	BuildName    string `gorm:"not null; type:varchar(300)" json:"build_name"`
	ProductModel string `gorm:"not null; type:varchar(300)" json:"product_model"`
}

type DagDocument struct {
	models.ModelBase
	TaskID                   uint    `gorm:"not null" json:"task_id"`
	SeriesID                 uint    `gorm:"not null" json:"series_id"`
	VersionNumber            string  `gorm:"not null; type:varchar(300)" json:"version_number"`
	BuildRecordIDs           string  `gorm:"not null; type:varchar(300)" json:"build_record_ids"`
	BuildName                string  `gorm:"not null; type:varchar(300)" json:"build_name"`
	ProductModel             string  `gorm:"not null; type:varchar(300)" json:"product_model"`
	SoftwareVersion          string  `gorm:"not null; type:varchar(300)" json:"software_version"`
	ReleaseType              string  `gorm:"not null; type:varchar(300)" json:"release_type"`
	BaselineVersion          string  `gorm:"not null; type:varchar(300)" json:"baseline_version"`
	ScreenshotPath           string  `gorm:"not null; type:varchar(300)" json:"screenshot_path"`
	TemplatePath             string  `gorm:"not null; type:varchar(300)" json:"template_path"`
	DocumentDownloadURLWord  string  `gorm:"not null; type:varchar(300)" json:"document_download_url_word"`
	DocumentDownloadURLExcel string  `gorm:"not null; type:varchar(300)" json:"document_download_url_excel"`
	DocumentVersionNumber    float64 `gorm:"not null" json:"document_version_number"`
	Status                   uint    `gorm:"not null; default:0" json:"status"`
	UserID                   uint    `gorm:"not null" json:"user_id"`
	ReleaseReason            string  `gorm:"not null; type:varchar(300)" json:"release_reason"`
}

type DagScreenshot struct {
	models.ModelBase
	SeriesID       uint   `gorm:"not null" json:"series_id"`
	Version        string `gorm:"not null; type:varchar(300)" json:"version"`
	ScreenshotPath string `gorm:"not null; type:varchar(300)" json:"screenshot_path"`
}

type DagBinFile struct {
	models.ModelBase
	DocumentID      uint   `gorm:"not null" json:"document_id"`
	SeriesID        uint   `gorm:"not null" json:"series_id"`
	Version         string `gorm:"not null; type:varchar(300)" json:"version"`
	BuildRecordID   uint   `gorm:"not null;" json:"build_record_id"`
	BuildName       string `gorm:"not null; type:varchar(300)" json:"build_name"`
	BinFileName     string `gorm:"not null; type:varchar(300)" json:"bin_file_name"`
	BinFileSize     uint   `gorm:"not null" json:"bin_file_size"`
	BinFileMd5      string `gorm:"not null; type:varchar(300)" json:"bin_file_md5"`
	BinFileURL      string `gorm:"not null; type:varchar(300)" json:"bin_file_url"`
	ProductModel    string `gorm:"not null; type:varchar(300)" json:"product_model"`
	SoftwareVersion string `gorm:"not null; type:varchar(300)" json:"software_version"`
	CPU             string `gorm:"not null; type:varchar(300)" json:"cpu"`
	BaselineVersion string `gorm:"not null; type:varchar(300)" json:"baseline_version"`
}

type DagDocumentVersion struct {
	models.ModelBase
	SeriesID       uint    `gorm:"not null" json:"series_id"`
	VersionNumber  float64 `gorm:"not null" json:"version_number"`
	ReleaseVersion string  `gorm:"not null; type:varchar(300)" json:"release_version"`
	VersionDesc    string  `gorm:"not null; type:varchar(300)" json:"version_desc"`
	VersionEditor  string  `gorm:"not null; type:varchar(300)" json:"version_editor"`
}

type DagHistoricalVersion struct {
	models.ModelBase
	SeriesID    uint   `gorm:"not null" json:"series_id"`
	VersionID   uint   `gorm:"not null" json:"version_id"`
	ReleaseDate string `gorm:"not null; type:varchar(300)" json:"release_date"`
}

type DagTaskSerie struct {
	models.ModelBase
	TaskID   uint `gorm:"not null" json:"task_id"`
	SeriesID uint `gorm:"not null" json:"series_id"`
}
