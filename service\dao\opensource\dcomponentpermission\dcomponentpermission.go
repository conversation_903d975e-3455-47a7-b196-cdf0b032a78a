package dcomponentpermission

import (
	"strings"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/opensource"
	"irisAdminApi/service/dao/user/duser"
)

const ModelName = "组件人员表"

type Response struct {
	opensource.OpenSourceComponentPermission
	Username       string `gorm:"-" json:"username"`
	Name           string `gorm:"-" json:"name"`
	PermissionType string `gorm:"-" json:"permission_type"`
}

type ListResponse struct {
	Response
}

type Request struct {
	Id uint `json:"id"`
}

func (this *Response) ModelName() string {
	return ModelName
}

func Model() *opensource.OpenSourceComponentPermission {
	return &opensource.OpenSourceComponentPermission{}
}

func ListPermissions(page, pageSize int, componentId uint, permissionType uint, sort, orderBy, createdAt, updatedAt string) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Where("component_id=?", componentId)

	if permissionType > 0 {
		db = db.Where("type=?", permissionType)
	}
	if len(createdAt) > 0 {
		array := strings.Split(createdAt, ",")
		db = db.Where("created_at between ? and ?", array[0], array[1])
	}
	if len(updatedAt) > 0 {
		array := strings.Split(updatedAt, ",")
		db = db.Where("updated_at between ? and ?", array[0], array[1])
	}
	if len(orderBy) == 0 {
		orderBy = "created_at"
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("count opensource component permissions get err %s", err.Error())
		return nil, err
	}
	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("list opensource component permissions get err %s", err.Error())
		return nil, err
	}
	getPermissionsUsernames(res)
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func FindById(id uint) (*Response, error) {
	var res []*Response

	db := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id)
	err := db.Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find opensource component permission by id get err %s", err.Error())
		return nil, err
	}
	if len(res) == 0 {
		return nil, nil
	}
	return res[0], nil
}

func FindByComponentIdAndUserIdAndType(componentId uint, userId uint, componentType uint) (*Response, error) {
	var res []*Response

	db := easygorm.GetEasyGormDb().Model(Model()).
		Where("component_id=?", componentId).
		Where("user_id = ?", userId).
		Where("type=?", componentType)
	err := db.Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find opensource component permission by component-id and user-id and type get err %s", err.Error())
		return nil, err
	}

	if len(res) == 0 {
		return nil, nil
	}
	getPermissionUsername(res[0])
	return res[0], nil
}

func ListByComponentIdAndUserId(componentId uint, userId uint) ([]*ListResponse, error) {
	var res []*ListResponse

	err := easygorm.GetEasyGormDb().Model(Model()).
		Where("component_id=?", componentId).
		Where("user_id = ?", userId).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("list opensource component permissions by component-id and user-id get err: %s", err.Error())
		return nil, err
	}
	getPermissionsUsernames(res)
	return res, nil
}

func Create(res *Response) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(&res.OpenSourceComponentPermission).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create opensource component permission get err: %s", err.Error())
		return err
	}
	getPermissionUsername(res)
	return nil
}

func ListPermissionsByComponentId(componentId uint) ([]*ListResponse, error) {
	var res []*ListResponse

	err := easygorm.GetEasyGormDb().Model(Model()).
		Where("component_id=?", componentId).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("list permissions by component id get err: %s", err.Error())
		return nil, err
	}
	getPermissionsUsernames(res)
	return res, nil
}

func getPermissionUsername(res *Response) {
	user := &duser.User{}
	err := user.Find(res.UserID)
	if err != nil {
		return
	}

	res.Username = user.Username
	res.Name = user.Name
	switch res.Type {
	case 1:
		res.PermissionType = "负责人"
	case 2:
		res.PermissionType = "抄送人"
	case 3:
		res.PermissionType = "专业组长"
	}
}

func getPermissionsUsernames(res []*ListResponse) {
	for _, per := range res {
		getPermissionUsername(&per.Response)
	}
}
