package openfeishu

import (
	"fmt"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/appmergerequest/dappmergerequestdashboard"
	"irisAdminApi/service/dao/bugsync/dbug"
	"irisAdminApi/service/dao/datasync/dpmshardwareproject"
	"irisAdminApi/service/dao/datasync/dpmsprojectmember"
	"irisAdminApi/service/dao/datasync/dpmsrequest"
	"irisAdminApi/service/dao/datasync/dpmssoftwareproject"
	"irisAdminApi/service/dao/datasync/dpmsworkpacketinfo"
	"irisAdminApi/service/dao/datasync/dresource"
	"irisAdminApi/service/dao/datasync/dresourceclean"
	"irisAdminApi/service/dao/feishu/dfeishupmsprojectlistdata"
	"irisAdminApi/service/dao/feishu/dfeishupmsreviewdata"
	"irisAdminApi/service/dao/mergerequest/dmergerequest"
	"irisAdminApi/service/dao/user/daccesslog"
)

func SyncPMSData() {
	logging.DebugLogger.Debugf("开始同步BUG数据到飞书")
	SyncAllBugData() // BUG数据同步
	logging.DebugLogger.Debugf("完成同步BUG数据到飞书")

	logging.DebugLogger.Debugf("开始同步BUG数据到飞书--2")
	SyncAllBugDataV2("tbl9WQZVpeBXKPF1", "KmVRbOUQEa1MyIsDGGQcULohnMb") // BUG数据同步
	logging.DebugLogger.Debugf("完成同步BUG数据到飞书--2")

	logging.DebugLogger.Debugf("开始同步BUG数据到飞书--3")
	SyncAllBugDataV3()
	logging.DebugLogger.Debugf("完成同步BUG数据到飞书--3")

	logging.DebugLogger.Debugf("开始应软MR数据到飞书")
	SyncAllUserMRWorkpacketData()
	logging.DebugLogger.Debugf("完成应软MR数据到飞书")

	logging.DebugLogger.Debugf("开始同步日志数据")
	SyncAllAccessLogData()
	SyncAllAccessLogDataMonth()
	SyncAllAccessLogAndActionData()
	logging.DebugLogger.Debugf("完成同步日志数据")
}

func SyncAllResourceData() {
	logging.DebugLogger.Debugf("开始同步人员任务数据")
	SyncAllUserTaskData()
	logging.DebugLogger.Debugf("完成同步人员任务数据")

	logging.DebugLogger.Debugf("开始同步实际代码输出数据")
	SyncActualCodeOutputData()
	logging.DebugLogger.Debugf("完成同步实际代码输出数据")

	logging.DebugLogger.Debugf("开始同步资源详情数据")
	SyncLastWeekResourceDetailsData()
	logging.DebugLogger.Debugf("完成资源详情数据")

	logging.DebugLogger.Debugf("开始同步原始数据")
	SyncOriginalResourceData()
	logging.DebugLogger.Debugf("完成原始数据")

	logging.DebugLogger.Debugf("开始同步上周原始数据")
	SyncLastWeekOriginalResourceData()
	logging.DebugLogger.Debugf("完成上周原始数据")
}

func SyncAllBugData() {
	tableID := "tblDPXSP8v7vGGWu"
	// 批量删除线上数据
	DeleteTableRecordData(tableID, []string{"操作系统"})
	// 获取本地数据
	page := 1
	pageSize := 500
	for {
		bug := dbug.Response{}
		lastYear := time.Now().AddDate(-1, 0, 0)
		// lastYearFirstDay := time.Date(lastYear.Year(), time.January, 1, 0, 0, 0, 0, lastYear.Location()).Format("2006-01-02 00:00:00")
		lastYearFirstDay := lastYear.Format("2006-01-02 00:00:00")
		where := []map[string]string{{"column": "bug_created_at", "condition": ">=", "value": lastYearFirstDay}}
		data, err := bug.AllEx(where, "", "bug_created_at", page, pageSize)
		if err != nil {
			logging.ErrorLogger.Errorf("GetbugDatas error:%s", err.Error())
			return
		}
		items := data["items"].([]*dbug.ListResponse)
		if len(items) > 0 {
			records := []map[string]interface{}{}
			// 将数据按照批次插入飞书数据表,每批次500条
			for _, bugData := range items {
				rec := map[string]interface{}{
					"BUGID": map[string]interface{}{"text": fmt.Sprintf("%d", bugData.BugID), "link": "http://bugs.ruijie.com.cn/bug_switch/bug/main?bugId=" + fmt.Sprintf("%d", bugData.BugID)},
					"状态":    bugData.BugState,
					"BUG简介": bugData.BugSummary,
					"操作系统":  bugData.BugOS,
				}
				if len(bugData.BugPriority) > 0 {
					rec["严重性"] = bugData.BugPriority
				}
				if len(bugData.BugSeverity) > 0 {
					rec["优先级"] = bugData.BugSeverity
				}
				switch bugData.BugRepro {
				case 1:
					rec["重复性"] = "必现"
				case 2:
					rec["重复性"] = "有时重现"
				case 3:
					rec["重复性"] = "未尝试重现"
				case 4:
					rec["重复性"] = "尝试单未重现"
				}
				if len(bugData.BugSubmitter) > 0 {
					rec["提交者"] = bugData.BugSubmitter
				}
				if len(bugData.BugSubmitterGroup) > 0 {
					rec["提交人专业组"] = bugData.BugSubmitterGroup
				}
				if len(bugData.BugOwner) > 0 {
					rec["BUG负责人"] = bugData.BugOwner
				}
				if len(bugData.BugOwnerGroup) > 0 {
					rec["负责人专业组"] = bugData.BugOwnerGroup
				}
				if len(bugData.BugWorkpacketName) > 0 {
					rec["工作包名称"] = bugData.BugWorkpacketName
				}
				if len(bugData.BugTestCharger) > 0 {
					rec["测试负责人"] = bugData.BugTestCharger
				}
				if !bugData.BugCreatedAt.IsZero() {
					rec["提交时间"] = bugData.BugCreatedAt.UnixMilli()
				}
				if !bugData.BugUpdatedAt.IsZero() {
					rec["最后更新时间"] = bugData.BugUpdatedAt.UnixMilli()
				}
				if len(bugData.BugCbdAt) > 0 {
					CBDTimestamp, err := parseAndConvertToTimestamp(bugData.BugCbdAt)
					if err != nil {
						logging.ErrorLogger.Errorf("GetbugDatas parseAndConvertToTimestamp error:%s", err.Error())
					}
					if CBDTimestamp > 0 {
						rec["CBD时间"] = CBDTimestamp * 1000
					}
				}
				if len(bugData.BugCbtAt) > 0 {
					CBTTimestamp, err := parseAndConvertToTimestamp(bugData.BugCbtAt)
					if err != nil {
						logging.ErrorLogger.Errorf("GetbugDatas parseAndConvertToTimestamp error:%s", err.Error())
					}
					if CBTTimestamp > 0 {
						rec["CBT时间"] = CBTTimestamp * 1000
					}
				}
				if !bugData.BugResolvedAt.IsZero() {
					rec["解决时间"] = bugData.BugResolvedAt.UnixMilli()
				}
				if len(bugData.BugSource) > 0 {
					rec["BUG来源"] = bugData.BugSource
				}
				if bugData.SameBugID > 0 {
					rec["镜像BugID"] = map[string]interface{}{"text": fmt.Sprintf("%d", bugData.SameBugID), "link": "http://bugs.ruijie.com.cn/bug_switch/bug/main?bugId=" + fmt.Sprintf("%d", bugData.SameBugID)}
				}
				if bugData.MainBugID > 0 {
					rec["主BugID"] = map[string]interface{}{"text": fmt.Sprintf("%d", bugData.MainBugID), "link": "http://bugs.ruijie.com.cn/bug_switch/bug/main?bugId=" + fmt.Sprintf("%d", bugData.MainBugID)}
				}
				records = append(records, rec)
			}
			tableRecordResp, err := BatchCreate(tableID, libs.Config.FeiShuDoc.BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("GetbugDatas batchCreate error:%s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
			time.Sleep(120 * time.Millisecond)
		}
		// 检查是否还有更多数据
		if len(items) < pageSize {
			break
		}
		page++
	}
}

func SyncAllBugDataV2(tableID, biAppToken string) {
	// 批量删除线上数据
	DeleteTableRecordDataV2(tableID, []string{"操作系统"}, biAppToken)
	// 获取本地数据
	page := 1
	pageSize := 500
	for {
		bug := dbug.Response{}
		lastYear := time.Now().AddDate(-1, 0, 0)
		// lastYearFirstDay := time.Date(lastYear.Year(), time.January, 1, 0, 0, 0, 0, lastYear.Location()).Format("2006-01-02 00:00:00")
		lastYearFirstDay := lastYear.Format("2006-01-02 00:00:00")
		where := []map[string]string{{"column": "bug_created_at", "condition": ">=", "value": lastYearFirstDay}}
		data, err := bug.AllEx(where, "", "bug_created_at", page, pageSize)
		if err != nil {
			logging.ErrorLogger.Errorf("GetbugDatas error:%s", err.Error())
			return
		}
		items := data["items"].([]*dbug.ListResponse)
		if len(items) > 0 {
			records := []map[string]interface{}{}
			// 将数据按照批次插入飞书数据表,每批次500条
			for _, bugData := range items {
				rec := map[string]interface{}{
					"BUGID": map[string]interface{}{"text": fmt.Sprintf("%d", bugData.BugID), "link": "http://bugs.ruijie.com.cn/bug_switch/bug/main?bugId=" + fmt.Sprintf("%d", bugData.BugID)},
					"状态":    bugData.BugState,
					"BUG简介": bugData.BugSummary,
					"操作系统":  bugData.BugOS,
				}
				if len(bugData.BugPriority) > 0 {
					rec["严重性"] = bugData.BugPriority
				}
				if len(bugData.BugSeverity) > 0 {
					rec["优先级"] = bugData.BugSeverity
				}
				switch bugData.BugRepro {
				case 1:
					rec["重复性"] = "必现"
				case 2:
					rec["重复性"] = "有时重现"
				case 3:
					rec["重复性"] = "未尝试重现"
				case 4:
					rec["重复性"] = "尝试单未重现"
				}
				if len(bugData.BugSubmitter) > 0 {
					rec["提交者"] = bugData.BugSubmitter
				}
				if len(bugData.BugSubmitterGroup) > 0 {
					rec["提交人专业组"] = bugData.BugSubmitterGroup
				}
				if len(bugData.BugOwner) > 0 {
					rec["BUG负责人"] = bugData.BugOwner
				}
				if len(bugData.BugOwnerGroup) > 0 {
					rec["负责人专业组"] = bugData.BugOwnerGroup
				}
				if len(bugData.BugWorkpacketName) > 0 {
					rec["工作包名称"] = bugData.BugWorkpacketName
				}
				if len(bugData.BugTestCharger) > 0 {
					rec["测试负责人"] = bugData.BugTestCharger
				}
				if !bugData.BugCreatedAt.IsZero() {
					rec["提交时间"] = bugData.BugCreatedAt.UnixMilli()
				}
				if !bugData.BugUpdatedAt.IsZero() {
					rec["最后更新时间"] = bugData.BugUpdatedAt.UnixMilli()
				}
				if len(bugData.BugCbdAt) > 0 {
					CBDTimestamp, err := parseAndConvertToTimestamp(bugData.BugCbdAt)
					if err != nil {
						logging.ErrorLogger.Errorf("GetbugDatas parseAndConvertToTimestamp error:%s", err.Error())
					}
					if CBDTimestamp > 0 {
						rec["CBD时间"] = CBDTimestamp * 1000
					}
				}
				if len(bugData.BugCbtAt) > 0 {
					CBTTimestamp, err := parseAndConvertToTimestamp(bugData.BugCbtAt)
					if err != nil {
						logging.ErrorLogger.Errorf("GetbugDatas parseAndConvertToTimestamp error:%s", err.Error())
					}
					if CBTTimestamp > 0 {
						rec["CBT时间"] = CBTTimestamp * 1000
					}
				}
				if !bugData.BugResolvedAt.IsZero() {
					rec["解决时间"] = bugData.BugResolvedAt.UnixMilli()
				}
				if len(bugData.BugSource) > 0 {
					rec["BUG来源"] = bugData.BugSource
				}
				if bugData.SameBugID > 0 {
					rec["镜像BugID"] = map[string]interface{}{"text": fmt.Sprintf("%d", bugData.SameBugID), "link": "http://bugs.ruijie.com.cn/bug_switch/bug/main?bugId=" + fmt.Sprintf("%d", bugData.SameBugID)}
				}
				if bugData.MainBugID > 0 {
					rec["主BugID"] = map[string]interface{}{"text": fmt.Sprintf("%d", bugData.MainBugID), "link": "http://bugs.ruijie.com.cn/bug_switch/bug/main?bugId=" + fmt.Sprintf("%d", bugData.MainBugID)}
				}
				records = append(records, rec)
			}
			tableRecordResp, err := BatchCreate(tableID, biAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("GetbugDatas batchCreate error:%s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
			time.Sleep(120 * time.Millisecond)
		}
		// 检查是否还有更多数据
		if len(items) < pageSize {
			break
		}
		page++
	}
}

func SyncAllBugDataV3() {
	tableID := "tblpg02Jhfxglc0W"
	// 批量删除线上数据
	DeleteTableRecordData(tableID, []string{"操作系统"})
	// 获取本地数据
	page := 1
	pageSize := 500
	for {
		bug := dbug.Response{}
		// lastYear := time.Now().AddDate(-1, 0, 0)
		// lastYearFirstDay := time.Date(lastYear.Year(), time.January, 1, 0, 0, 0, 0, lastYear.Location()).Format("2006-01-02 00:00:00")
		// lastYearFirstDay := lastYear.Format("2006-01-02 00:00:00")
		where := []map[string]string{{"column": "bug_source", "condition": "=", "value": "内部测试"}}
		data, err := bug.AllEx(where, "", "bug_created_at", page, pageSize)
		if err != nil {
			logging.ErrorLogger.Errorf("GetbugDatas error:%s", err.Error())
			return
		}
		items := data["items"].([]*dbug.ListResponse)
		if len(items) > 0 {
			records := []map[string]interface{}{}
			// 将数据按照批次插入飞书数据表,每批次500条
			for _, bugData := range items {
				rec := map[string]interface{}{
					"BUGID": map[string]interface{}{"text": fmt.Sprintf("%d", bugData.BugID), "link": "http://bugs.ruijie.com.cn/bug_switch/bug/main?bugId=" + fmt.Sprintf("%d", bugData.BugID)},
					"状态":    bugData.BugState,
					"BUG简介": bugData.BugSummary,
					"操作系统":  bugData.BugOS,
				}
				if len(bugData.BugPriority) > 0 {
					rec["严重性"] = bugData.BugPriority
				}
				if len(bugData.BugSeverity) > 0 {
					rec["优先级"] = bugData.BugSeverity
				}
				switch bugData.BugRepro {
				case 1:
					rec["重复性"] = "必现"
				case 2:
					rec["重复性"] = "有时重现"
				case 3:
					rec["重复性"] = "未尝试重现"
				case 4:
					rec["重复性"] = "尝试单未重现"
				}
				if len(bugData.BugSubmitter) > 0 {
					rec["提交者"] = bugData.BugSubmitter
				}
				if len(bugData.BugSubmitterGroup) > 0 {
					rec["提交人专业组"] = bugData.BugSubmitterGroup
				}
				if len(bugData.BugOwner) > 0 {
					rec["BUG负责人"] = bugData.BugOwner
				}
				if len(bugData.BugOwnerGroup) > 0 {
					rec["负责人专业组"] = bugData.BugOwnerGroup
				}
				if len(bugData.BugWorkpacketName) > 0 {
					rec["工作包名称"] = bugData.BugWorkpacketName
				}
				if len(bugData.BugTestCharger) > 0 {
					rec["测试负责人"] = bugData.BugTestCharger
				}
				if !bugData.BugCreatedAt.IsZero() {
					rec["提交时间"] = bugData.BugCreatedAt.UnixMilli()
				}
				if !bugData.BugUpdatedAt.IsZero() {
					rec["最后更新时间"] = bugData.BugUpdatedAt.UnixMilli()
				}
				if len(bugData.BugCbdAt) > 0 {
					CBDTimestamp, err := parseAndConvertToTimestamp(bugData.BugCbdAt)
					if err != nil {
						logging.ErrorLogger.Errorf("GetbugDatas parseAndConvertToTimestamp error:%s", err.Error())
					}
					if CBDTimestamp > 0 {
						rec["CBD时间"] = CBDTimestamp * 1000
					}
				}
				if len(bugData.BugCbtAt) > 0 {
					CBTTimestamp, err := parseAndConvertToTimestamp(bugData.BugCbtAt)
					if err != nil {
						logging.ErrorLogger.Errorf("GetbugDatas parseAndConvertToTimestamp error:%s", err.Error())
					}
					if CBTTimestamp > 0 {
						rec["CBT时间"] = CBTTimestamp * 1000
					}
				}
				if !bugData.BugResolvedAt.IsZero() {
					rec["解决时间"] = bugData.BugResolvedAt.UnixMilli()
				}
				if len(bugData.BugSource) > 0 {
					rec["BUG来源"] = bugData.BugSource
				}
				if bugData.SameBugID > 0 {
					rec["镜像BugID"] = map[string]interface{}{"text": fmt.Sprintf("%d", bugData.SameBugID), "link": "http://bugs.ruijie.com.cn/bug_switch/bug/main?bugId=" + fmt.Sprintf("%d", bugData.SameBugID)}
				}
				if bugData.MainBugID > 0 {
					rec["主BugID"] = map[string]interface{}{"text": fmt.Sprintf("%d", bugData.MainBugID), "link": "http://bugs.ruijie.com.cn/bug_switch/bug/main?bugId=" + fmt.Sprintf("%d", bugData.MainBugID)}
				}
				records = append(records, rec)
			}
			tableRecordResp, err := BatchCreate(tableID, libs.Config.FeiShuDoc.BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("GetbugDatas batchCreate error:%s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
			time.Sleep(120 * time.Millisecond)
		}
		// 检查是否还有更多数据
		if len(items) < pageSize {
			break
		}
		page++
	}
}

func SyncAllWorkPacketData() {
	tableID := "tbllD4K79JjpLmcT"
	// 批量删除线上数据
	items, err := dpmsworkpacketinfo.GetWorkPacketInfoData()
	if err != nil {
		logging.ErrorLogger.Errorf("GetbugDatas error:%s", err.Error())
		return
	}
	DeleteTableRecordData(tableID, []string{"项目"})
	// 获取本地数据
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将数据按照批次插入飞书数据表,每批次500条
			for _, workPacketData := range batchData {
				rec := map[string]interface{}{
					"工作包":        workPacketData.WorkPacketName,
					"项目":         workPacketData.ProjectName,
					"总计代码量(K)":   workPacketData.TotalCodes,
					"新增代码量(K)":   workPacketData.AddCodes,
					"移植(正式项)(K)": workPacketData.PortedCodes,
					"工作包进度":      workPacketData.FinishPercent,
				}
				if len(workPacketData.PacketManagerName) > 0 {
					rec["工作包负责人"] = workPacketData.PacketManagerName
				}
				if len(workPacketData.GroupName) > 0 {
					rec["所属专业组"] = workPacketData.GroupName
				}

				records = append(records, rec)
			}

			tableRecordResp, err := BatchCreate(tableID, libs.Config.FeiShuDoc.BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("GetworkPackets batchCreate error:%s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
			time.Sleep(120 * time.Millisecond)
		}
	}
}

// 同步应软MR信息数据
func SyncAllUserMRWorkpacketData() {
	tableID := "tblkcrz78Fsg7pRz"
	// 批量删除线上数据
	DeleteTableRecordData(tableID, []string{"项目"})
	// 获取本地数据
	items, err := dappmergerequestdashboard.GeUserMRWorkpacketInfo()
	if err != nil {
		logging.ErrorLogger.Errorf("GeUserMRWorkpacketInfo error:%s", err.Error())
		return
	}
	// 批量同步到线上
	// 分批处理新增数据
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, reviewdetail := range batchData {
				rec := map[string]interface{}{
					"项目":         reviewdetail.ReleaseProject,
					"新增移植代码量(K)": reviewdetail.PortedCodeQuantityAdd,
					"新增代码量(K)":   reviewdetail.CodeQuantityAdd,
					"删除移植代码量(K)": reviewdetail.PortedCodeQuantityRemove,
					"删除代码量(K)":   reviewdetail.CodeQuantityRemove,
					"提交次数":       reviewdetail.TotalCount,
				}
				if len(reviewdetail.Openid) > 0 {
					rec["人员"] = []interface{}{map[string]interface{}{"id": reviewdetail.Openid}}
				}
				if len(reviewdetail.Requirement) > 0 {
					rec["需求"] = reviewdetail.Requirement
				}
				if len(reviewdetail.WorkGroup) > 0 {
					rec["专业组"] = reviewdetail.WorkGroup
				}
				if len(reviewdetail.WorkPackage) > 0 {
					rec["工作包"] = reviewdetail.WorkPackage
				}
				records = append(records, rec)
			}
			tableRecordResp, err := BatchCreate(tableID, libs.Config.FeiShuDoc.BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("GetDocumentTypeWorkPacket batchCreate error:%s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

// 同步人员任务信息数据
func SyncAllUserTaskData() {
	tableID := "tbl3jI5OHe2iyghK"
	// 批量删除线上数据
	DeleteTableRecordData(tableID, []string{"人员"})
	// 获取本地数据
	items, err := dpmsworkpacketinfo.GetUserTaskData()
	if err != nil {
		logging.ErrorLogger.Errorf("GetUserTaskData error:%s", err.Error())
		return
	}
	// 批量同步到线上
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, taskData := range batchData {
				rec := map[string]interface{}{
					"人员":   taskData.PacketManagerName,
					"任务规模": taskData.TotalCodes,
					"任务进度": taskData.FinishPercent,
				}
				if len(taskData.DepartmentName) > 0 {
					rec["部门"] = taskData.DepartmentName
				}
				if len(taskData.GroupName) > 0 {
					rec["专业组"] = taskData.GroupName
				}
				if len(taskData.ProjectName) > 0 {
					rec["项目"] = taskData.ProjectName
				}
				if len(taskData.WorkPacketName) > 0 {
					rec["任务名称"] = taskData.WorkPacketName
				}
				records = append(records, rec)
			}
			tableRecordResp, err := BatchCreate(tableID, libs.Config.FeiShuDoc.BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("SyncAllUserTaskData batchCreate error:%s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

func SyncActualCodeOutputData() {
	tableID := "tblLFfDZr0Cbn7cj"
	// 批量删除线上数据
	DeleteTableRecordData(tableID, []string{"项目"})

	// 获取本地数据
	items, err := dmergerequest.GetCodeOutputData()
	if err != nil {
		logging.ErrorLogger.Errorf("获取实际代码产出数据失败: %s", err.Error())
		return
	}

	// 批量同步到线上
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将数据按照批次插入飞书数据表,每批次500条
			for _, item := range batchData {
				rec := map[string]interface{}{
					"项目":          item.ReleaseProject,
					"增加代码(klocs)": item.CodeAdd,
					"折算增加代码":      item.DiscountAdd,
					"删除代码(klocs)": item.Remove,
					"有效代码(klocs)": item.Total,
				}
				if len(item.WorkPackage) > 0 {
					rec["工作包"] = item.WorkPackage
				}
				if len(item.UserName) > 0 {
					rec["人员"] = item.UserName
				}
				records = append(records, rec)
			}

			tableRecordResp, err := BatchCreate(tableID, libs.Config.FeiShuDoc.BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("批量创建记录失败: %s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

// 同步上周资源详情数据
func SyncLastWeekResourceDetailsData() {
	tableID := "tbl1ulO5AZ74z32Y"
	// 批量删除线上数据
	DeleteTableRecordData(tableID, []string{"项目"})
	start, end := getLastWeekRange()
	// 获取本地数据
	items, err := dresource.GetResourceDetailData(start.Format("2006-01-02"), end.Format("2006-01-02"))
	if err != nil {
		logging.ErrorLogger.Errorf("获取上周资源详情数据失败: %s", err.Error())
		return
	}
	// 批量同步到线上
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, item := range batchData {
				rec := map[string]interface{}{
					"文本":   item.UserName,
					"字段 1": item.DivisionName,
					"字段 2": item.DepartmentName,
					"字段 3": item.GroupName,
					"年":    item.Year,
					"月":    item.Month,
					"模板ID": item.TemplateID,
					"工作时间": item.WorkTime,
					"加班时间": item.AddTime,
					"总时间":  item.TotalTime,
				}
				if len(item.ProjectName) > 0 {
					rec["项目"] = item.ProjectName
				}
				if len(item.WorkClassName) > 0 {
					rec["类别"] = item.WorkClassName
				}
				if len(item.StageName) > 0 {
					rec["阶段"] = item.StageName
				}
				if len(item.ActivityName) > 0 {
					rec["活动"] = item.ActivityName
				}
				if len(item.TaskName) > 0 {
					rec["任务"] = item.TaskName
				}
				if len(item.ProduceValue) > 0 {
					rec["产出"] = item.ProduceValue
				}
				if len(item.ReportDate) > 0 {
					reportDate, _ := parseAndConvertToTimestampV2(item.ReportDate)
					rec["日期"] = reportDate * 1000
				}
				if len(item.Remarks) > 0 {
					rec["字段 17"] = item.Remarks
				}
				if len(item.StageNameClean) > 0 {
					rec["清洗后阶段"] = item.StageNameClean
				}
				records = append(records, rec)
			}

			tableRecordResp, err := BatchCreate(tableID, libs.Config.FeiShuDoc.BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("批量创建记录失败: %s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

func SyncOriginalResourceData() {
	tableID := "tblfbqRZ7j2riktz"
	// 批量删除线上数据
	DeleteTableRecordData(tableID, []string{"项目"})
	// 获取本地数据
	items, err := dresourceclean.GetOriginalResourceData()
	if err != nil {
		logging.ErrorLogger.Errorf("获取资源原始数据失败: %s", err.Error())
		return
	}
	// 批量同步到线上
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, item := range batchData {
				rec := map[string]interface{}{
					"项目":     item.ProjectName,
					"资源(小时)": item.WorkTime,
				}
				if len(item.DepartmentName) > 0 {
					rec["部门"] = item.DepartmentName
				}
				if len(item.GroupName) > 0 {
					rec["专业组"] = item.GroupName
				}
				if len(item.UserName) > 0 {
					rec["人员"] = item.UserName
				}
				if len(item.Week) > 0 {
					rec["周"] = item.Week
				}
				records = append(records, rec)
			}

			tableRecordResp, err := BatchCreate(tableID, libs.Config.FeiShuDoc.BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("批量创建记录失败: %s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

func SyncLastWeekOriginalResourceData() {
	tableID := "tblMlHTDNLbXcBkk"
	// 批量删除线上数据
	DeleteTableRecordData(tableID, []string{"项目"})
	// 获取本地数据
	items, err := dresourceclean.GetLastWeekOriginalResourceData()
	if err != nil {
		logging.ErrorLogger.Errorf("获取资源原始数据失败: %s", err.Error())
		return
	}
	// 批量同步到线上
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, item := range batchData {
				rec := map[string]interface{}{
					"项目":     item.ProjectName,
					"资源(小时)": item.WorkTime,
				}
				if len(item.DepartmentName) > 0 {
					rec["部门"] = item.DepartmentName
				}
				if len(item.GroupName) > 0 {
					rec["专业组"] = item.GroupName
				}
				if len(item.UserName) > 0 {
					rec["人员"] = item.UserName
				}
				if len(item.Week) > 0 {
					rec["周"] = item.Week
				}
				records = append(records, rec)
			}

			tableRecordResp, err := BatchCreate(tableID, libs.Config.FeiShuDoc.BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("批量创建记录失败: %s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

var ServerMap = map[string]string{
	"appmr":        "应软MR表单",
	"coredump":     "NTOS故障分析系统",
	"techsupport":  "一键收集解密",
	"sig":          "加密作业",
	"performance":  "性能分析",
	"mr":           "MR表单",
	"resourcepool": "测试组资源管理系统",
	"search":       "11.X分析检索",
	"codesync":     "代码同步",
	"bugsync":      "bug同步",
	"feature":      "规则库发布系统",
	"feature/11_x": "11.X规则库发布系统",
	"kpi":          "质量管理系统",
	"release":      "版本发布管理系统",
	"farm":         "编译农场",
	"coverity":     "编译农场(构建)",
	"gitlab":       "gitlab操作",
	"approval":     "文件外出系统",
	"manage":       "文件外出系统",
	"audit":        "文件外出系统",
	"user":         "用户模块(公共)",
	"opensource":   "开源组件漏洞预警",
	"production":   "NTOS下生产系统",
	"license":      "授权自助系统",
}

func SyncAllAccessLogData() {
	tableID := "tblZ9kmJEUCeaK7Q"
	// 批量删除线上数据
	DeleteTableRecordData(tableID, []string{"应用名称"})
	// 获取本地数据
	items, err := daccesslog.GetAccessLogByDay()
	if err != nil {
		logging.ErrorLogger.Errorf("GetbugDatas error:%s", err.Error())
		return
	}
	// 批量同步到线上
	// 分批处理新增数据
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, reviewdetail := range batchData {
				appName, ok := ServerMap[reviewdetail.ServerName]
				if !ok {
					appName = reviewdetail.ServerName
				}
				rec := map[string]interface{}{
					"应用名称": appName,
					"请求次数": reviewdetail.VisitCount,
					"访问人数": reviewdetail.UniqueUserCount,
					"应用标识": reviewdetail.ServerName,
				}
				if len(reviewdetail.TimePeriod) > 0 {
					TimePeriodTimestamp, err := parseAndConvertToTimestampV2(reviewdetail.TimePeriod)
					if err != nil {
						logging.ErrorLogger.Errorf("GetbugDatas parseAndConvertToTimestamp error:%s", err.Error())
					}
					if TimePeriodTimestamp > 0 {
						rec["日期"] = TimePeriodTimestamp * 1000
					}
				}
				records = append(records, rec)
			}
			tableRecordResp, err := BatchCreate(tableID, libs.Config.FeiShuDoc.BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("GetDocumentTypeWorkPacket batchCreate error:%s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

func SyncAllAccessLogDataMonth() {
	tableID := "tbloNBluKA9Tg1PM"
	// 批量删除线上数据
	DeleteTableRecordData(tableID, []string{"应用名称"})
	// 获取本地数据
	items, err := daccesslog.GetAccessLogByMonth()
	if err != nil {
		logging.ErrorLogger.Errorf("GetbugDatas error:%s", err.Error())
		return
	}
	// 批量同步到线上
	// 分批处理新增数据
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, reviewdetail := range batchData {
				appName, ok := ServerMap[reviewdetail.ServerName]
				if !ok {
					appName = reviewdetail.ServerName
				}
				rec := map[string]interface{}{
					"应用名称": appName,
					"请求次数": reviewdetail.VisitCount,
					"访问人数": reviewdetail.UniqueUserCount,
					"应用标识": reviewdetail.ServerName,
				}
				if len(reviewdetail.TimePeriod) > 0 {
					rec["日期"] = reviewdetail.TimePeriod
				}
				records = append(records, rec)
			}
			tableRecordResp, err := BatchCreate(tableID, libs.Config.FeiShuDoc.BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("GetDocumentTypeWorkPacket batchCreate error:%s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

func SyncAllAccessLogAndActionData() {
	tableID := "tblyKort7z8suPkz"
	// 批量删除线上数据
	DeleteTableRecordData(tableID, []string{"应用名称"})
	// 获取本地数据
	items, err := daccesslog.GetAccessLogByDayAndAction()
	if err != nil {
		logging.ErrorLogger.Errorf("GetbugDatas error:%s", err.Error())
		return
	}
	// 批量同步到线上
	// 分批处理新增数据
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, reviewdetail := range batchData {
				appName, ok := ServerMap[reviewdetail.ServerName]
				if !ok {
					appName = reviewdetail.ServerName
				}
				rec := map[string]interface{}{
					"应用名称": appName,
					"请求次数": reviewdetail.VisitCount,
					"访问人数": reviewdetail.UniqueUserCount,
					"应用标识": reviewdetail.ServerName,
					"功能名称": reviewdetail.RouteName,
					"请求路径": reviewdetail.RequestAction,
				}
				if len(reviewdetail.TimePeriod) > 0 {
					TimePeriodTimestamp, err := parseAndConvertToTimestampV2(reviewdetail.TimePeriod)
					if err != nil {
						logging.ErrorLogger.Errorf("GetbugDatas parseAndConvertToTimestamp error:%s", err.Error())
					}
					if TimePeriodTimestamp > 0 {
						rec["日期"] = TimePeriodTimestamp * 1000
					}
				}
				records = append(records, rec)
			}
			tableRecordResp, err := BatchCreate(tableID, libs.Config.FeiShuDoc.BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("GetDocumentTypeWorkPacket batchCreate error:%s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

func SyncBugDataV4() {
	appToken := "ZfoVbWxqTaI09Rs3Ml1cRzRCnuf"
	tableID1 := "tblXBFZHub86jWnh"
	tableID2 := "tblcCvbsaJJkTK1Z"
	bugStateFilters := []string{"NEW", "ASSIGNED", "VERIFIED", "RENEW", "REOPENED", "REQUEST-REPRODUCE", "REQUEST-NO_TO_SOLVE", "REQUEST-CHANGE_REQ", "RESOLVED", "CHECKED"}
	// 批量删除线上数据

	// 获取本地数据

	bugs, err := dbug.FinBugSyncToFeishu()
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		return
	}

	DeleteTableRecordDataV4(appToken, tableID1, []string{"操作系统"})
	DeleteTableRecordDataV4(appToken, tableID2, []string{"操作系统"})

	limit := 500
	for loop := 0; true; loop++ {
		start := loop * limit
		if start > len(bugs) {
			break
		}
		end := (loop + 1) * limit
		if len(bugs) <= (loop+1)*limit {
			end = len(bugs)
		}
		records1 := []map[string]interface{}{}
		records2 := []map[string]interface{}{}
		for _, bug := range bugs[start:end] {
			rec := map[string]interface{}{
				"BUGID": map[string]interface{}{
					"text": fmt.Sprintf("%d", bug.BugID),
					"link": "http://bugs.ruijie.com.cn/bug_switch/bug/main?bugId=" + fmt.Sprintf("%d", bug.BugID),
				},
				"状态":    bug.BugState,
				"BUG简介": bug.BugSummary,
				"操作系统":  bug.BugOS,
			}
			if len(bug.BugPriority) > 0 {
				rec["严重性"] = bug.BugPriority
			}
			if len(bug.BugSeverity) > 0 {
				rec["优先级"] = bug.BugSeverity
			}
			switch bug.BugRepro {
			case 1:
				rec["重复性"] = "必现"
			case 2:
				rec["重复性"] = "有时重现"
			case 3:
				rec["重复性"] = "未尝试重现"
			case 4:
				rec["重复性"] = "尝试单未重现"
			}
			if len(bug.BugSubmitter) > 0 {
				rec["提交者"] = bug.BugSubmitter
			}
			if len(bug.BugSubmitterGroup) > 0 {
				rec["提交人专业组"] = bug.BugSubmitterGroup
			}
			if len(bug.BugOwner) > 0 {
				rec["BUG负责人"] = bug.BugOwner
			}
			if len(bug.BugOwnerGroup) > 0 {
				rec["负责人专业组"] = bug.BugOwnerGroup
			}
			if len(bug.BugWorkpacketName) > 0 {
				rec["工作包名称"] = bug.BugWorkpacketName
			}
			if len(bug.BugTestCharger) > 0 {
				rec["测试负责人"] = bug.BugTestCharger
			}
			if !bug.BugCreatedAt.IsZero() {
				rec["提交时间"] = bug.BugCreatedAt.UnixMilli()
			}
			if !bug.BugUpdatedAt.IsZero() {
				rec["最后更新时间"] = bug.BugUpdatedAt.UnixMilli()
			}
			if len(bug.BugCbdAt) > 0 {
				CBDTimestamp, err := parseAndConvertToTimestamp(bug.BugCbdAt)
				if err != nil {
					logging.ErrorLogger.Errorf("GetbugDatas parseAndConvertToTimestamp error:%s", err.Error())
				}
				if CBDTimestamp > 0 {
					rec["CBD时间"] = CBDTimestamp * 1000
				}
			}
			if len(bug.BugCbtAt) > 0 {
				CBTTimestamp, err := parseAndConvertToTimestamp(bug.BugCbtAt)
				if err != nil {
					logging.ErrorLogger.Errorf("GetbugDatas parseAndConvertToTimestamp error:%s", err.Error())
				}
				if CBTTimestamp > 0 {
					rec["CBT时间"] = CBTTimestamp * 1000
				}
			}
			if !bug.BugResolvedAt.IsZero() {
				rec["解决时间"] = bug.BugResolvedAt.UnixMilli()
			}
			if len(bug.BugSource) > 0 {
				rec["BUG来源"] = bug.BugSource
			}
			if bug.SameBugID > 0 {
				rec["镜像BugID"] = map[string]interface{}{"text": fmt.Sprintf("%d", bug.SameBugID), "link": "http://bugs.ruijie.com.cn/bug_switch/bug/main?bugId=" + fmt.Sprintf("%d", bug.SameBugID)}
			}
			if bug.MainBugID > 0 {
				rec["主BugID"] = map[string]interface{}{"text": fmt.Sprintf("%d", bug.MainBugID), "link": "http://bugs.ruijie.com.cn/bug_switch/bug/main?bugId=" + fmt.Sprintf("%d", bug.MainBugID)}
			}
			if bug.BugSource == "内部测试" {
				records1 = append(records1, rec)
			}

			if libs.InArrayS(bugStateFilters, bug.BugState) {
				records2 = append(records2, rec)
			}
		}
		if len(records1) > 0 {
			resp, err := BatchCreate(tableID1, appToken, records1)
			if err != nil {
				logging.ErrorLogger.Errorf("GetbugDatas batchCreate error:%s", err.Error(), string(resp.RawBody))
			}
		}

		if len(records2) > 0 {
			resp, err := BatchCreate(tableID2, appToken, records2)
			if err != nil {
				logging.ErrorLogger.Errorf("GetbugDatas batchCreate error:%s", err.Error(), string(resp.RawBody))
			}
		}
		time.Sleep(120 * time.Millisecond)
	}
}

// 同步上周资源详情数据
func SyncLastWeekResourceDetailsDataV4() {
	appToken := "ZfoVbWxqTaI09Rs3Ml1cRzRCnuf"
	tableID := "tblfFKCHxn1SHGkE"
	// 批量删除线上数据
	DeleteTableRecordDataV4(appToken, tableID, []string{"项目"})
	start, end := getLastWeekRange()
	// 获取本地数据
	items, err := dresource.GetResourceDetailData(start.Format("2006-01-02"), end.Format("2006-01-02"))
	if err != nil {
		logging.ErrorLogger.Errorf("获取上周资源详情数据失败: %s", err.Error())
		return
	}
	// 批量同步到线上
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, item := range batchData {
				rec := map[string]interface{}{
					"文本":   item.UserName,
					"字段 1": item.DivisionName,
					"字段 2": item.DepartmentName,
					"字段 3": item.GroupName,
					"年":    item.Year,
					"月":    item.Month,
					"模板ID": item.TemplateID,
					"工作时间": item.WorkTime,
					"加班时间": item.AddTime,
					"总时间":  item.TotalTime,
				}
				if len(item.ProjectName) > 0 {
					rec["项目"] = item.ProjectName
				}
				if len(item.WorkClassName) > 0 {
					rec["类别"] = item.WorkClassName
				}
				if len(item.StageName) > 0 {
					rec["阶段"] = item.StageName
				}
				if len(item.ActivityName) > 0 {
					rec["活动"] = item.ActivityName
				}
				if len(item.TaskName) > 0 {
					rec["任务"] = item.TaskName
				}
				if len(item.ProduceValue) > 0 {
					rec["产出"] = item.ProduceValue
				}
				if len(item.ReportDate) > 0 {
					reportDate, _ := parseAndConvertToTimestampV2(item.ReportDate)
					rec["日期"] = reportDate * 1000
				}
				if len(item.Remarks) > 0 {
					rec["字段 17"] = item.Remarks
				}
				if len(item.StageNameClean) > 0 {
					rec["清洗后阶段"] = item.StageNameClean
				}
				records = append(records, rec)
			}

			_, err := BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("批量创建记录失败: %s", err.Error())
				continue
			}
		}
	}
}

func SyncLastWeekOriginalResourceDataV4() {
	appToken := "ZfoVbWxqTaI09Rs3Ml1cRzRCnuf"
	tableID := "tblfFKCHxn1SHGkE"
	// 批量删除线上数据
	DeleteTableRecordDataV4(appToken, tableID, []string{"项目"})
	// 获取本地数据
	items, err := dresourceclean.GetLastWeekOriginalResourceData()
	if err != nil {
		logging.ErrorLogger.Errorf("获取资源原始数据失败: %s", err.Error())
		return
	}
	// 批量同步到线上
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, item := range batchData {
				rec := map[string]interface{}{
					"项目":     item.ProjectName,
					"资源(小时)": item.WorkTime,
				}
				if len(item.DepartmentName) > 0 {
					rec["部门"] = item.DepartmentName
				}
				if len(item.GroupName) > 0 {
					rec["专业组"] = item.GroupName
				}
				if len(item.UserName) > 0 {
					rec["人员"] = item.UserName
				}
				if len(item.Week) > 0 {
					rec["周"] = item.Week
				}
				records = append(records, rec)
			}

			tableRecordResp, err := BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("批量创建记录失败: %s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

func SyncAllOriginalResourceDataV4() {
	appToken := "ZfoVbWxqTaI09Rs3Ml1cRzRCnuf"
	tableID := "tblYTy3wuCUCr1dw"
	// 批量删除线上数据
	DeleteTableRecordDataV4(appToken, tableID, []string{"项目"})
	// 获取本地数据
	items, err := dresourceclean.GetAllMonthResourceData()
	if err != nil {
		logging.ErrorLogger.Errorf("获取资源原始数据失败: %s", err.Error())
		return
	}
	// 批量同步到线上
	now := time.Now()
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, item := range batchData {
				rec := map[string]interface{}{
					"项目":       item.ProjectName,
					"上班时间(小时)": item.WorkTime,
					"加班时间(小时)": item.AddTime,
					"总时间(小时)":  item.TotalTime,
					"数据过期时间":   now.Add(12 * time.Hour).UnixMilli(),
				}
				if len(item.DepartmentName) > 0 {
					rec["部门"] = item.DepartmentName
				}
				if len(item.GroupName) > 0 {
					rec["专业组"] = item.GroupName
				}
				if len(item.UserName) > 0 {
					rec["人员"] = item.UserName
				}

				rec["年"] = item.Year

				rec["月"] = item.Month

				records = append(records, rec)
			}

			tableRecordResp, err := BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("批量创建记录失败: %s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

func SyncAllOriginalResourceDataV4_2() {
	appToken := "Hi7rbhcSxaAEvnsm7HrcQDHkn1e"
	tableID := "tblG6SPhZxtu4IBj"
	// 批量删除线上数据
	DeleteTableRecordDataV4(appToken, tableID, []string{"项目"})
	// 获取本地数据
	items, err := dresourceclean.GetAllMonthResourceDetailData()
	if err != nil {
		logging.ErrorLogger.Errorf("获取资源原始数据失败: %s", err.Error())
		return
	}
	// 批量同步到线上
	now := time.Now()
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, item := range batchData {
				rec := map[string]interface{}{
					"项目":       item.ProjectName,
					"上班时间(小时)": item.WorkTime,
					"加班时间(小时)": item.AddTime,
					"总时间(小时)":  item.TotalTime,
					"数据过期时间":   now.Add(12 * time.Hour).UnixMilli(),
				}
				if len(item.DepartmentName) > 0 {
					rec["部门"] = item.DepartmentName
				}
				if len(item.GroupName) > 0 {
					rec["专业组"] = item.GroupName
				}
				if len(item.UserName) > 0 {
					rec["人员"] = item.UserName
				}
				if len(item.WorkClassName) > 0 {
					rec["工作大类"] = item.WorkClassName
				}
				if len(item.StageName) > 0 {
					rec["项目阶段"] = item.StageName
				}
				if len(item.ActivityName) > 0 {
					rec["项目工序"] = item.ActivityName
				}
				if len(item.TaskName) > 0 {
					rec["任务名称"] = item.TaskName
				}

				rec["年"] = item.Year

				rec["月"] = item.Month

				records = append(records, rec)
			}

			tableRecordResp, err := BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("批量创建记录失败: %s", err.Error(), string(tableRecordResp.RawBody))
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

func SyncAllOriginalResourceDataV4_3() {
	appToken := "Hi7rbhcSxaAEvnsm7HrcQDHkn1e"
	tableID := "tbl5AXmjxdHKzf3h"
	// 批量删除线上数据

	// 获取本地数据
	now := time.Now()
	// 获取当前季度的月份
	month := now.Month()
	var months []int

	switch {
	case month >= 1 && month <= 3:
		tableID = "tbl7zzSr5fguzn77"
		months = []int{1, 2, 3}
	case month >= 4 && month <= 6:
		tableID = "tblCGJOwxsBBwqMC"
		months = []int{4, 5, 6}
	case month >= 7 && month <= 9:
		tableID = "tbl5AXmjxdHKzf3h"
		months = []int{7, 8, 9}
	case month >= 10 && month <= 12:
		tableID = "tblDvJ9eH5w7zaNY"
		months = []int{10, 11, 12}
	}
	DeleteTableRecordDataV4(appToken, tableID, []string{"项目"})
	items, err := dresourceclean.GetAllMonthResourceDetailDataWithWorkpacketInMonth(now.Year(), months)
	if err != nil {
		logging.ErrorLogger.Errorf("获取资源原始数据失败: %s", err.Error())
		return
	}
	// 批量同步到线上

	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, item := range batchData {
				reportTime, err := time.Parse("2006-01-02", item.ReportDate)
				if err != nil {
					logging.ErrorLogger.Error(err)
				}
				rec := map[string]interface{}{
					"项目":       item.ProjectName,
					"上班时间(小时)": item.WorkTime,
					"加班时间(小时)": item.AddTime,
					"总时间(小时)":  item.TotalTime,
					"数据过期时间":   now.Add(12 * time.Hour).UnixMilli(),
					"工作包":      item.WorkpacketName,
					"日期":       reportTime.UnixMilli(),
				}

				if len(item.DepartmentName) > 0 {
					rec["部门"] = item.DepartmentName
				}
				if len(item.GroupName) > 0 {
					rec["专业组"] = item.GroupName
				}
				if len(item.UserName) > 0 {
					rec["人员"] = item.UserName
				}
				if len(item.WorkClassName) > 0 {
					rec["工作大类"] = item.WorkClassName
				}
				if len(item.StageName) > 0 {
					rec["项目阶段"] = item.StageName
				}
				if len(item.ActivityName) > 0 {
					rec["项目工序"] = item.ActivityName
				}
				if len(item.TaskName) > 0 {
					rec["任务名称"] = item.TaskName
				}

				rec["年"] = item.Year

				rec["月"] = item.Month

				records = append(records, rec)
			}

			tableRecordResp, err := BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("批量创建记录失败: %s", err.Error(), string(tableRecordResp.RawBody))
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

func SyncAllOriginalResourceDataV5(projectNames []string) {
	appToken := "ZfoVbWxqTaI09Rs3Ml1cRzRCnuf"
	tableID := "tblB0im2zBPFDSew"
	// 批量删除线上数据
	DeleteTableRecordDataV4(appToken, tableID, []string{"项目"})
	// 获取本地数据
	items, err := dresourceclean.GetAllMonthResourceDetailDataWithWorkpacket(projectNames)
	if err != nil {
		logging.ErrorLogger.Errorf("获取资源原始数据失败: %s", err.Error())
		return
	}
	// 批量同步到线上
	now := time.Now()
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, item := range batchData {
				reportTime, err := time.Parse("2006-01-02", item.ReportDate)
				if err != nil {
					logging.ErrorLogger.Error(err)
				}
				rec := map[string]interface{}{
					"项目":       item.ProjectName,
					"上班时间(小时)": item.WorkTime,
					"加班时间(小时)": item.AddTime,
					"总时间(小时)":  item.TotalTime,
					"数据过期时间":   now.Add(12 * time.Hour).UnixMilli(),
					"工作包":      item.WorkpacketName,
					"日期":       reportTime.UnixMilli(),
				}

				if len(item.DepartmentName) > 0 {
					rec["部门"] = item.DepartmentName
				}
				if len(item.GroupName) > 0 {
					rec["专业组"] = item.GroupName
				}
				if len(item.UserName) > 0 {
					rec["人员"] = item.UserName
				}
				if len(item.WorkClassName) > 0 {
					rec["工作大类"] = item.WorkClassName
				}
				if len(item.StageName) > 0 {
					rec["项目阶段"] = item.StageName
				}
				if len(item.ActivityName) > 0 {
					rec["项目工序"] = item.ActivityName
				}
				if len(item.TaskName) > 0 {
					rec["任务名称"] = item.TaskName
				}

				rec["年"] = item.Year

				rec["月"] = item.Month

				records = append(records, rec)
			}

			tableRecordResp, err := BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("批量创建记录失败: %s", err.Error(), string(tableRecordResp.RawBody))
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

func SyncAllWorkPacketDataV4() []string {
	appToken := "ZfoVbWxqTaI09Rs3Ml1cRzRCnuf"
	tableID := "tblKt47leRohRXsp"
	// 批量删除线上数据
	projectNames := []string{}
	items, err := dpmsworkpacketinfo.GetWorkPacketInfoData()
	if err != nil {
		logging.ErrorLogger.Errorf("GetbugDatas error:%s", err.Error())
		return projectNames
	}
	DeleteTableRecordDataV4(appToken, tableID, []string{"项目"})
	// 获取本地数据

	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]

		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将数据按照批次插入飞书数据表,每批次500条
			for _, workPacketData := range batchData {
				if !libs.InArrayS(projectNames, workPacketData.ProjectName) {
					projectNames = append(projectNames, workPacketData.ProjectName)
				}
				rec := map[string]interface{}{
					"工作包":        workPacketData.WorkPacketName,
					"需求":         workPacketData.RequestName,
					"项目":         workPacketData.ProjectName,
					"总计代码量(K)":   workPacketData.TotalCodes,
					"新增代码量(K)":   workPacketData.AddCodes,
					"移植(正式项)(K)": workPacketData.PortedCodes,
					"工作包进度":      workPacketData.FinishPercent,
					"计划时间(h)":    workPacketData.PlanTime,
					"实际耗时(h)":    workPacketData.UseTime,
				}
				if len(workPacketData.PacketManagerName) > 0 {
					rec["工作包负责人"] = workPacketData.PacketManagerName
				}
				if len(workPacketData.GroupName) > 0 {
					rec["所属专业组"] = workPacketData.GroupName
				}

				if workPacketData.PlanStartDate != nil {
					rec["计划开始时间"] = workPacketData.PlanStartDate.UnixMilli()
				}
				if workPacketData.PlanEndDate != nil {
					rec["计划结束时间"] = workPacketData.PlanEndDate.UnixMilli()
				}
				if workPacketData.ActStartDate != nil {
					rec["实际开始时间"] = workPacketData.ActStartDate.UnixMilli()
				}
				if workPacketData.ActEndDate != nil {
					rec["实际结束时间"] = workPacketData.ActEndDate.UnixMilli()
				}

				records = append(records, rec)
			}

			tableRecordResp, err := BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("GetworkPackets batchCreate error:%s", err.Error(), string(tableRecordResp.RawBody))
				continue
			}

			time.Sleep(120 * time.Millisecond)
		}
	}
	return projectNames
}

func SyncAllWorkPacketDataV5() []string {
	appToken := "YvAObMrWPaT4rHsfczFcmVAOnjg"
	tableID := "tbluZLwHIIXo25pB"
	// 批量删除线上数据
	projectNames := []string{}
	items, err := dpmsworkpacketinfo.GetWorkPacketInfoData()
	if err != nil {
		logging.ErrorLogger.Errorf("GetbugDatas error:%s", err.Error())
		return projectNames
	}
	DeleteTableRecordDataV4(appToken, tableID, []string{"项目"})
	// 获取本地数据
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]

		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将数据按照批次插入飞书数据表,每批次500条
			for _, workPacketData := range batchData {
				if !libs.InArrayS(projectNames, workPacketData.ProjectName) {
					projectNames = append(projectNames, workPacketData.ProjectName)
				}
				rec := map[string]interface{}{
					"工作包":        workPacketData.WorkPacketName,
					"工作包名称":      workPacketData.WorkPacketName,
					"需求":         workPacketData.RequestName,
					"项目":         workPacketData.ProjectName,
					"总计代码量(K)":   workPacketData.TotalCodes,
					"新增代码量(K)":   workPacketData.AddCodes,
					"移植(正式项)(K)": workPacketData.PortedCodes,
					"工作包进度":      workPacketData.FinishPercent,
				}
				if len(workPacketData.PacketManagerName) > 0 {
					rec["工作包负责人"] = workPacketData.PacketManagerName
				}
				if len(workPacketData.GroupName) > 0 {
					rec["所属专业组"] = workPacketData.GroupName
				}

				records = append(records, rec)
			}

			tableRecordResp, err := BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("GetworkPackets batchCreate error:%s", err.Error(), string(tableRecordResp.RawBody))
				continue
			}

			time.Sleep(120 * time.Millisecond)
		}
	}
	return projectNames
}

func SyncAllRequestDataV4() []string {
	appToken := "ZfoVbWxqTaI09Rs3Ml1cRzRCnuf"
	tableID := "tbls90EctTIx6RIS"
	// 批量删除线上数据
	DeleteTableRecordDataV4(appToken, tableID, []string{"项目名称"})
	// 获取本地数据
	page := 1
	pageSize := 500
	projectNames := []string{}
	for {
		items, err := dpmsrequest.GetRequestData(page, pageSize)
		if err != nil {
			logging.ErrorLogger.Errorf("GetbugDatas error:%s", err.Error())
			return projectNames
		}

		if len(items) > 0 {
			records := []map[string]interface{}{}
			// 将数据按照批次插入飞书数据表,每批次500条
			for _, item := range items {
				rec := map[string]interface{}{
					"需求ID":  fmt.Sprintf("%v", item.RequestID),
					"项目ID":  item.ProjectID,
					"项目名称":  item.ProjectName,
					"需求名称":  item.RequestName,
					"需求状态":  item.RequestStatus,
					"变更状态":  item.ChangeType,
					"PGTTM": item.PgttmUserName,
				}
				if item.Disabled {
					rec["是否无效"] = 1
				} else {
					rec["是否无效"] = 0
				}

				records = append(records, rec)
			}

			tableRecordResp, err := BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("requests batchCreate error:%s", err.Error(), string(tableRecordResp.RawBody))
				continue
			}

			time.Sleep(120 * time.Millisecond)
		}
		// 检查是否还有更多数据
		if len(items) < pageSize {
			break
		}
		page++
	}
	return projectNames
}

func SyncAllRequestDataV5() []string {
	appToken := "YvAObMrWPaT4rHsfczFcmVAOnjg"
	tableID := "tbl4hgNs1sfyV7Mj"
	// 批量删除线上数据
	DeleteTableRecordDataV4(appToken, tableID, []string{"项目名称"})
	// 获取本地数据
	page := 1
	pageSize := 500
	projectNames := []string{}
	for {
		items, err := dpmsrequest.GetRequestData(page, pageSize)
		if err != nil {
			logging.ErrorLogger.Errorf("GetbugDatas error:%s", err.Error())
			return projectNames
		}

		if len(items) > 0 {
			records := []map[string]interface{}{}
			// 将数据按照批次插入飞书数据表,每批次500条
			for _, item := range items {
				rec := map[string]interface{}{
					"需求ID":  fmt.Sprintf("%v", item.RequestID),
					"项目ID":  item.ProjectID,
					"项目名称":  item.ProjectName,
					"需求名称":  item.RequestName,
					"需求状态":  item.RequestStatus,
					"变更状态":  item.ChangeType,
					"PGTTM": item.PgttmUserName,
				}
				if item.Disabled {
					rec["是否无效"] = 1
				} else {
					rec["是否无效"] = 0
				}

				records = append(records, rec)
			}

			tableRecordResp, err := BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("requests batchCreate error:%s", err.Error(), string(tableRecordResp.RawBody))
				continue
			}

			time.Sleep(120 * time.Millisecond)
		}
		// 检查是否还有更多数据
		if len(items) < pageSize {
			break
		}
		page++
	}
	return projectNames
}

func GetSyncProjectNamesAndMap() ([]string, map[string]*dfeishupmsprojectlistdata.ListResponse) {
	projectNames := []string{}
	projectStatusMap := map[string]*dfeishupmsprojectlistdata.ListResponse{}

	projectListData, err := dfeishupmsprojectlistdata.AllProjectListData()
	if err != nil {
		logging.ErrorLogger.Errorf("GetbugDatas error:%s", err.Error())
		return projectNames, projectStatusMap
	}

	for _, item := range projectListData {
		projectStatusMap[item.ProjectName] = item
		if item.ProjectStatus == "进行中" {
			projectNames = append(projectNames, item.ProjectName)
		}
		if item.ProjectStatus == "完成" {
			if !item.StopUpdateAt.IsZero() && time.Until(item.StopUpdateAt).Hours() > -3*24 {
				projectNames = append(projectNames, item.ProjectName)
			} else if item.StopUpdateAt.IsZero() {
				projectNames = append(projectNames, item.ProjectName)
			}
		}
	}
	return projectNames, projectStatusMap
}

func SyncAllSoftProjectDataV4(projectStatusMap map[string]*dfeishupmsprojectlistdata.ListResponse) {
	appToken := "ZfoVbWxqTaI09Rs3Ml1cRzRCnuf"
	tableID := "tblEtlLlx5zihjFb"

	// 批量删除线上数据
	DeleteTableRecordDataV4(appToken, tableID, []string{"项目名称"})
	// 获取本地数据
	page := 1
	pageSize := 500

	for {
		items, err := dpmssoftwareproject.GetSoftwareProjectData(page, pageSize)
		if err != nil {
			logging.ErrorLogger.Errorf("GetbugDatas error:%s", err.Error())
			return
		}

		if len(items) > 0 {
			records := []map[string]interface{}{}
			// 将数据按照批次插入飞书数据表,每批次500条
			for _, item := range items {
				if item.ProjectName == "" {
					continue
				}
				rec := map[string]interface{}{
					"项目名称": item.ProjectName,
					"产品线":  item.SetUpType,
					"项目类别": item.Classification,
					"项目类型": item.ProjectType,
					"基线项目": item.BaseProject,
					"客户列表": item.CustomList,
					"项目需求": item.ProjectRequire,

					"备注":     item.ProjectNotice,
					"CA":     item.CaName,
					"PM":     item.PmName,
					"PTM":    item.PtmName,
					"PQA":    item.PqaName,
					"CMA":    item.CmaName,
					"项目状态":   item.ProjectStatus,
					"申请时间":   item.ApplyTime.Unix() * 1000,
					"最后更新时间": item.LastUpdateTime.Unix() * 1000,
				}

				if status, ok := projectStatusMap[item.ProjectName]; ok {
					rec["项目状态"] = status.ProjectStatus
				}

				if !item.ProjectEndTime.IsZero() {
					rec["项目结束时间"] = item.ProjectEndTime.Unix() * 1000
				}

				if !item.ProjectBeginTime.IsZero() {
					rec["项目开始时间"] = item.ProjectBeginTime.Unix() * 1000
				}

				if !item.Fcs.IsZero() {
					rec["FCS"] = item.Fcs.Unix() * 1000
				}

				if item.IsNormal {
					rec["正式项目"] = "是"
				} else {
					rec["正式项目"] = "否"
				}
				if item.Disabled {
					rec["无效"] = "是"
				} else {
					rec["无效"] = "否"
				}
				records = append(records, rec)
			}

			tableRecordResp, err := BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("software porject batchCreate error:%s", err.Error(), string(tableRecordResp.RawBody))
				continue
			}

			time.Sleep(120 * time.Millisecond)
		}
		// 检查是否还有更多数据
		if len(items) < pageSize {
			break
		}
		page++
	}
	return
}

func SyncAllHardwareProjectDataV4() []string {
	appToken := "ZfoVbWxqTaI09Rs3Ml1cRzRCnuf"
	tableID := "tblEiMYlYkfLy8qc"
	// 批量删除线上数据
	DeleteTableRecordDataV4(appToken, tableID, []string{"项目名称"})
	// 获取本地数据
	page := 1
	pageSize := 500
	projectNames := []string{}
	for {
		items, err := dpmshardwareproject.GetHardwareProjectData(page, pageSize)
		if err != nil {
			logging.ErrorLogger.Errorf("GetbugDatas error:%s", err.Error())
			return projectNames
		}

		if len(items) > 0 {
			records := []map[string]interface{}{}
			// 将数据按照批次插入飞书数据表,每批次500条
			for _, item := range items {
				rec := map[string]interface{}{
					"项目名称":    item.ProjectName,
					"父级硬件项目":  item.HardProjectName,
					"产品线名称":   item.ProductLineName,
					"项目大类/等级": item.ProjectAttribute,
					"版本属性":    item.CategoryName,
					"基线产品名称":  item.BaseProductName,
					"产品大类":    item.ProductClass,
					"产品类型":    item.ProductAttribute1,
					"工程类别":    item.EngineeringCategoryName,
					"创建人":     item.CreateUserName,
					"项目状态":    item.ProjectStatus,
				}
				if item.HardProjectName == "" {
					rec["父级硬件项目"] = item.ProjectName
				}
				if item.Disabled {
					rec["是否无效"] = "1"
				} else {
					rec["是否无效"] = "0"
				}
				if item.ActStartDate != nil && !item.ActStartDate.IsZero() {
					rec["项目开始时间"] = item.ActStartDate.Unix() * 1000
				}
				if item.ActEndDate != nil && !item.ActEndDate.IsZero() {
					rec["项目结束时间"] = item.ActEndDate.Unix() * 1000
				}

				records = append(records, rec)
			}

			tableRecordResp, err := BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("hardware project batchCreate error:%s", err.Error(), string(tableRecordResp.RawBody))
				continue
			}

			time.Sleep(120 * time.Millisecond)
		}
		// 检查是否还有更多数据
		if len(items) < pageSize {
			break
		}
		page++
	}
	return projectNames
}

func SyncProjectMemberDataV4() {
	appToken := "ZfoVbWxqTaI09Rs3Ml1cRzRCnuf"
	tableID := "tblDgu4NNTCJ2bBm"
	// 批量删除线上数据
	items, err := dpmsprojectmember.FindRunningProjectsMembers()
	if err != nil {
		logging.ErrorLogger.Errorf("find running pms project member error:%s", err.Error())
		return
	}

	DeleteTableRecordDataV4(appToken, tableID, []string{"项目名称"})

	// 获取本地数据

	// 批量同步到线上
	// 分批处理新增数据
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, item := range batchData {
				if item.RoleRemark == "" {
					item.RoleRemark = item.UserRole
				}
				rec := map[string]interface{}{
					"ID":          item.ID,
					"项目名称":        item.ProjectName,
					"姓名":          item.Username,
					"角色":          item.UserRole,
					"角色(中文)":      item.RoleRemark,
					"cas_user_id": item.CasUserID,
				}

				records = append(records, rec)
			}
			tableRecordResp, err := BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("pms project member batchCreate error:%s", err.Error(), string(tableRecordResp.RawBody))
				continue
			}
		}
	}
}

func SyncProjectMemberDataV5() {
	appToken := "YvAObMrWPaT4rHsfczFcmVAOnjg"
	tableID := "tblE92ToZ8aR6LBD"
	// 批量删除线上数据
	items, err := dpmsprojectmember.FindRunningProjectsMembers()
	if err != nil {
		logging.ErrorLogger.Errorf("find running pms project member error:%s", err.Error())
		return
	}

	DeleteTableRecordDataV4(appToken, tableID, []string{"项目名称"})

	// 获取本地数据

	// 批量同步到线上
	// 分批处理新增数据
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, item := range batchData {
				if item.RoleRemark == "" {
					item.RoleRemark = item.UserRole
				}
				rec := map[string]interface{}{
					"ID":          item.ID,
					"项目名称":        item.ProjectName,
					"姓名":          item.Username,
					"角色":          item.UserRole,
					"角色(中文)":      item.RoleRemark,
					"cas_user_id": item.CasUserID,
				}

				records = append(records, rec)
			}
			tableRecordResp, err := BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("pms project member batchCreate error:%s", err.Error(), string(tableRecordResp.RawBody))
				continue
			}
		}
	}
}

func SyncReviewDataV4(projectNames []string) []string {
	appToken := "PIWvbp1DZaVcYMsHsvzcWiHqnbd"
	tableID := "tblj7Ezn4KQ8Cvff"
	// 批量删除线上数据
	DeleteTableRecordDataV4(appToken, tableID, []string{"项目"})
	// 获取本地数据
	page := 1
	pageSize := 500
	for {
		items, err := dfeishupmsreviewdata.FindByProjectNames(projectNames, page, pageSize)
		if err != nil {
			logging.ErrorLogger.Errorf("GetbugDatas error:%s", err.Error())
			return projectNames
		}

		if len(items) > 0 {
			records := []map[string]interface{}{}
			// 将数据按照批次插入飞书数据表,每批次500条
			for _, item := range items {
				rec := map[string]interface{}{
					"文档名称":   item.EntityName,
					"文档类型":   item.DocumentCategoryName,
					"文档负责人":  item.DocumentChargeName,
					"评审类型":   item.FlawType,
					"是否有效":   fmt.Sprintf("%v", item.IsValidReview),
					"项目":     item.ProjectName,
					"问题类型":   item.QuestionType,
					"问题级别":   item.SeverityLevel,
					"评审标题":   item.ReviewSubject,
					"评审描述":   item.ReviewDescription,
					"评审人":    item.ReviewUserName,
					"评审人专业组": item.ReviewUserGroup,
					"回复状态":   item.AcceptStatus,
					"回复内容":   item.AcceptContent,
					"来源":     item.Source,
				}
				if item.CreateDate != 0 {
					rec["创建时间"] = item.CreateDate
				}
				if item.ModifyDate != 0 {
					rec["更新时间"] = item.ModifyDate
				}
				if item.CloseDate != 0 {
					rec["关闭时间"] = item.CloseDate
				}

				records = append(records, rec)
			}

			tableRecordResp, err := BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("hardware project batchCreate error:%s", err.Error(), string(tableRecordResp.RawBody))
				continue
			}

			time.Sleep(120 * time.Millisecond)
		}
		// 检查是否还有更多数据
		if len(items) < pageSize {
			break
		}
		page++
	}
	return projectNames
}

// 品控数据同步函数
func SyncQualityControlData() {
	logging.InfoLogger.Info("开始执行品控数据同步任务...")

	// 检查品控系统配置
	if !ValidateQualityControlConfig() {
		logging.ErrorLogger.Error("品控系统配置验证失败，跳过同步")
		return
	}

	// 创建品控数据处理器
	config := &QualityControlConfig{
		EnableClosedLoop: true, // 启用闭环管理
		BatchSize:        50,   // 批处理大小
		MaxRetries:       3,    // 最大重试次数
	}

	processor := NewQualityControlProcessor(config)

	// 执行同步
	statistics, err := processor.ExecuteSync()
	if err != nil {
		logging.ErrorLogger.Errorf("品控数据同步失败: %v", err)
		return
	}

	// 记录同步结果
	logging.InfoLogger.Infof("品控数据同步完成 - 统计信息:")
	logging.InfoLogger.Infof("  开始时间: %s", statistics.StartTime)
	logging.InfoLogger.Infof("  结束时间: %s", statistics.EndTime)
	logging.InfoLogger.Infof("  耗时: %s", statistics.Duration)
	logging.InfoLogger.Infof("  总记录数: %d", statistics.TotalRecords)
	logging.InfoLogger.Infof("  匹配记录数: %d", statistics.MatchedRecords)
	logging.InfoLogger.Infof("  更新记录数: %d", statistics.UpdatedRecords)
	logging.InfoLogger.Infof("  跳过记录数: %d", statistics.SkippedRecords)
	logging.InfoLogger.Infof("  错误记录数: %d", statistics.ErrorRecords)

	// 如果有错误，记录详细信息
	if statistics.ErrorRecords > 0 {
		logging.ErrorLogger.Errorf("品控数据同步存在 %d 个错误", statistics.ErrorRecords)

		// 获取同步结果详情
		results := processor.GetSyncResults()
		errorCount := 0
		for _, result := range results {
			if result.Status == "error" && errorCount < 10 { // 只记录前10个错误
				logging.ErrorLogger.Errorf("  错误详情 - 品控单号: %s, 记录ID: %s, 错误: %s",
					result.QualityNumber, result.RecordID, result.Message)
				errorCount++
			}
		}

		if len(results) > 10 {
			logging.ErrorLogger.Errorf("  ... 还有 %d 个错误未显示", len(results)-10)
		}
	}

	logging.InfoLogger.Info("品控数据同步任务完成")
}

// 验证品控系统配置
func ValidateQualityControlConfig() bool {
	config := libs.Config.FeiShuDoc

	// 检查必需的配置项
	if config.QualityControlAppToken == "" {
		logging.ErrorLogger.Error("品控系统配置错误: QualityControlAppToken 未设置")
		return false
	}

	if config.QualityControlTableID == "" {
		logging.ErrorLogger.Error("品控系统配置错误: QualityControlTableID 未设置")
		return false
	}

	if config.QualityControlURL == "" {
		logging.ErrorLogger.Error("品控系统配置错误: QualityControlURL 未设置")
		return false
	}

	if config.PmsUser == "" || config.PmsPass == "" {
		logging.ErrorLogger.Error("品控系统配置错误: PmsUser 或 PmsPass 未设置")
		return false
	}

	logging.InfoLogger.Info("品控系统配置验证通过")
	return true
}
