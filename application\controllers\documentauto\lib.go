package documentauto

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/buildfarm/dcronmakejob"
	"irisAdminApi/service/dao/documentauto/ddagdocument"
	"irisAdminApi/service/dao/documentauto/ddagscreenshot"
	"irisAdminApi/service/dao/documentauto/ddagseriesbuildname"
	"irisAdminApi/service/dao/documentauto/ddagtemplate"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

// handleFileUploads 处理文件上传（截图和Word模板）
// handleFileUploads 处理文件上传（截图和Word模板）
// 区分创建和重新创建
func handleFileUploads(ctx iris.Context, request ReleaseDocument, uuid string, isCreate bool) (map[int]string, map[int]string, error) {
	screenshots := make(map[int]string)   // seriesId -> file path
	wordTemplates := make(map[int]string) // seriesId -> file path

	// 上传截图
	if request.UploadScreenshot {
		seriesData := strings.Split(request.Series, ",")
		for _, seriesID := range seriesData {
			seriesID = strings.TrimSpace(seriesID)
			if seriesID == "" {
				continue
			}
			screenshotPath, err := uploadScreenshot(ctx, seriesID, request.Baseline, uuid, request.Version, isCreate)
			if err != nil {
				return nil, nil, fmt.Errorf("上传系列ID %s 的截图时出错: %v", seriesID, err)
			}
			seriesIDInt, convErr := strconv.Atoi(seriesID)
			if convErr != nil {
				return nil, nil, fmt.Errorf("无效的系列ID: %s", seriesID)
			}
			screenshots[seriesIDInt] = screenshotPath
		}
	}

	// 上传Word模板
	if request.UploadWordTemplate {
		seriesData := strings.Split(request.Series, ",")
		for _, seriesID := range seriesData {
			seriesID = strings.TrimSpace(seriesID)
			if seriesID == "" {
				continue
			}
			wordTemplatePath, err := uploadWordTemplate(ctx, seriesID, request.Version, uuid, isCreate)
			if err != nil {
				return nil, nil, fmt.Errorf("上传系列ID %s 的Word模板时出错: %v", seriesID, err)
			}
			seriesIDInt, convErr := strconv.Atoi(seriesID)
			if convErr != nil {
				return nil, nil, fmt.Errorf("无效的系列ID: %s", seriesID)
			}
			wordTemplates[seriesIDInt] = wordTemplatePath
		}
	}

	return screenshots, wordTemplates, nil
}

// uploadScreenshot 处理截图上传
func uploadScreenshot(ctx iris.Context, seriesID, baseline, uuid, version string, isCreate bool) (string, error) {
	var screenshotPath string
	fieldName := fmt.Sprintf("screenshots[%s]", seriesID)
	file, info, err := ctx.FormFile(fieldName)
	if err != nil {
		// 未上传文件，尝试从数据库获取已有截图
		screenshotPathData, err := ddagscreenshot.GetScreenshotBySeriesID(seriesID, baseline)
		if err != nil && err.Error() != "record not found" {
			logging.ErrorLogger.Errorf("获取截图出错: %v", err)
			return "", fmt.Errorf("获取截图出错: %v", err)
		}
		if screenshotPathData.ID > 0 {
			screenshotPath = screenshotPathData.ScreenshotPath
		} else {
			screenshotPath = ""
		}
		return screenshotPath, nil
	}
	defer file.Close()

	// 验证文件大小和类型
	if err := validateFile(info, []string{".jpg", ".jpeg", ".png"}, libs.Config.DocumentAuto.MaxFileSize); err != nil {
		return "", err
	}

	// 保存文件
	saveDir := filepath.Join(libs.Config.DocumentAuto.ScreenshotPath, time.Now().Format("20060102"), uuid)
	fileName := info.Filename
	screenshotPath, err = saveFile(file, saveDir, fileName)
	if err != nil {
		logging.ErrorLogger.Errorf("保存截图文件出错: %v", err)
		return "", fmt.Errorf("保存截图文件时出错")
	}

	// 保存到数据库
	dagScreenshot := ddagscreenshot.Response{}
	err = dagScreenshot.Create(map[string]interface{}{
		"SeriesID":       seriesID,
		"Version":        version,
		"ScreenshotPath": screenshotPath,
		"CreatedAt":      time.Now(),
		"UpdatedAt":      time.Now(),
	})
	if err != nil {
		return "", fmt.Errorf("保存截图到数据库时出错: %v", err)
	}
	return screenshotPath, nil
}

// uploadWordTemplate 处理Word模板上传
func uploadWordTemplate(ctx iris.Context, seriesID, version, uuid string, isCreate bool) (string, error) {
	fieldName := fmt.Sprintf("wordTemplates[%s]", seriesID)
	file, info, err := ctx.FormFile(fieldName)
	if err != nil {
		//如果是重新创建，则不返回错误
		if !isCreate {
			return "", nil
		}
		logging.ErrorLogger.Errorf("读取Word模板文件出错: %v", err)
		// ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "读取Word模板文件时出错"))
		return "", err
	}
	defer file.Close()

	// 验证文件大小和类型
	if err := validateFile(info, []string{".doc", ".docx"}, libs.Config.DocumentAuto.MaxFileSize); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return "", err
	}

	// 保存文件
	saveDir := filepath.Join(libs.Config.DocumentAuto.WordTemplatePath, time.Now().Format("20060102"), uuid)
	err = os.MkdirAll(saveDir, 0750)
	if err != nil {
		logging.ErrorLogger.Errorf("创建保存目录出错: %v", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "创建保存目录时出错"))
		return "", err
	}
	// fileName := fmt.Sprintf("%s(%s-%s).docx", strings.TrimSuffix(filepath.Base(info.Filename), filepath.Ext(info.Filename)), seriesID, version)
	fileName := info.Filename
	wordTemplatePath, err := saveFile(file, saveDir, fileName)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "保存Word模板文件时出错"))
		return "", err
	}

	// 保存到数据库
	dagTemplate := ddagtemplate.Response{}
	err = dagTemplate.Create(map[string]interface{}{
		"SeriesID":     seriesID,
		"Version":      version,
		"TemplatePath": wordTemplatePath,
	})
	if err != nil {
		return "", err
	}

	return wordTemplatePath, nil
}

// validateFile 验证文件大小和类型
func validateFile(info *multipart.FileHeader, allowedExts []string, maxSize int64) error {
	if info.Size > maxSize {
		return fmt.Errorf("文件大小超过限制（最大%dMB）", maxSize>>20)
	}
	ext := strings.ToLower(filepath.Ext(info.Filename))
	for _, allowedExt := range allowedExts {
		if ext == allowedExt {
			return nil
		}
	}
	return fmt.Errorf("文件类型不支持")
}

// validateBuildRecords 验证系列编译名称是否全部存在于编译记录名称中
func validateBuildRecords(seriesBuildNames []*ddagseriesbuildname.Response, buildRecordNames []*dcronmakejob.ListResponse, ctx iris.Context) error {
	for _, seriesBuildName := range seriesBuildNames {
		found := false
		for _, buildRecordName := range buildRecordNames {
			if seriesBuildName.BuildName == buildRecordName.Product {
				found = true
				break
			}
		}
		if !found {
			logging.ErrorLogger.Errorf("系列编译名称未在编译记录名称中找到: %v", seriesBuildName.BuildName)
			// ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "系列编译名称和编译记录名称不一致"))
			return fmt.Errorf("系列编译名称和编译记录名称不一致,未在编译记录名称中找到编译记录:%s", seriesBuildName.BuildName)
		}
	}
	return nil
}

// createSeriesToBuildRecordMap 创建系列ID到编译记录ID的映射
func createSeriesToBuildRecordMap(seriesBuildNames []*ddagseriesbuildname.Response, buildRecordNames []*dcronmakejob.ListResponse) map[int][]int {
	seriesToBuildRecordMap := make(map[int][]int)
	buildNameToRecordIDs := make(map[string][]int)
	for _, buildRecord := range buildRecordNames {
		buildNameToRecordIDs[buildRecord.Product] = append(buildNameToRecordIDs[buildRecord.Product], int(buildRecord.Id))
	}

	for _, seriesBuildName := range seriesBuildNames {
		recordIDs, exists := buildNameToRecordIDs[seriesBuildName.BuildName]
		if exists {
			seriesToBuildRecordMap[int(seriesBuildName.SeriesID)] = append(seriesToBuildRecordMap[int(seriesBuildName.SeriesID)], recordIDs...)
		} else {
			// 理论上不会发生，因为之前已经验证过
			logging.ErrorLogger.Errorf("未找到编译名称对应的编译记录: %s", seriesBuildName.BuildName)
		}
	}
	return seriesToBuildRecordMap
}

// makeSeriesIDToBuildNamesMap 创建系列ID到编译名称的映射
func makeSeriesIDToBuildNamesMap(seriesBuildNames []*ddagseriesbuildname.Response) map[int]string {
	seriesIDToBuildNames := make(map[int]string)
	for _, seriesBuildName := range seriesBuildNames {
		seriesID := int(seriesBuildName.SeriesID)
		buildName := seriesBuildName.BuildName

		if existing, exists := seriesIDToBuildNames[seriesID]; exists {
			seriesIDToBuildNames[seriesID] = existing + "|" + buildName
		} else {
			seriesIDToBuildNames[seriesID] = buildName
		}
	}
	return seriesIDToBuildNames
}

// makeBuildNameToProductModelMap 创建编译名称到产品型号的映射
func makeBuildNameToProductModelMap(seriesBuildNames []*ddagseriesbuildname.Response) map[string]string {
	buildNameToProductModel := make(map[string]string)
	for _, seriesBuildName := range seriesBuildNames {
		buildNameToProductModel[seriesBuildName.BuildName] = seriesBuildName.ProductModel
	}
	return buildNameToProductModel
}

// createWordDocument 创建 Word 文档
func createWordDocument(docComposite *ddagdocument.DocumentCompositeData, replaceMap map[string]string, outputDir string, buildSoftwareVersion string) (string, string, error) {
	var outputFileName string
	if docComposite.DocType == 1 {
		outputFileName = fmt.Sprintf("%s %s 版本发行说明(%s).docx", docComposite.DocName, buildSoftwareVersion, replaceMap["{{docRev}}"])
	} else {
		outputFileName = fmt.Sprintf("%s %s 国际版本发行说明(%s).docx", docComposite.DocName, buildSoftwareVersion, replaceMap["{{docRev}}"])
	}

	outputFilePath := filepath.Join(outputDir, outputFileName)
	replacementsJSON, err := json.Marshal(replaceMap)
	if err != nil {
		return "", "", fmt.Errorf("序列化替换映射出错: %v", err)
	}

	shell := filepath.Join(libs.Config.DocumentAuto.Plugin, "modify_docx.py")
	command := fmt.Sprintf("python3 %s '%s' '%s' '%s'", shell, docComposite.TemplatePath, outputFilePath, replacementsJSON)
	output, err := libs.ExecCommand(command)
	if err != nil {
		return "", "", fmt.Errorf("执行modify_docx.py脚本出错: %v", err)
	}
	logging.InfoLogger.Infof("modify_docx.py输出: %s", string(output))

	return outputFileName, outputFilePath, nil
}

// createExcelDocument 创建 Excel 文档
func createExcelDocument(docComposite *ddagdocument.DocumentCompositeData, replaceMap map[string]string, outputDir string, buildSoftwareVersion string) (string, string, error) {
	var versionTransferExcelFileName string
	if docComposite.DocType == 1 {
		versionTransferExcelFileName = fmt.Sprintf("%s %s 版本信息传递单(%s).xlsx", docComposite.DocName, buildSoftwareVersion, replaceMap["{{docRev}}"])
	} else {
		versionTransferExcelFileName = fmt.Sprintf("%s %s 国际版本信息传递单(%s).xlsx", docComposite.DocName, buildSoftwareVersion, replaceMap["{{docRev}}"])
	}

	versionTransferExcelFilePath := filepath.Join(outputDir, versionTransferExcelFileName)
	replacementsJSON, err := json.Marshal(replaceMap)
	if err != nil {
		return "", "", fmt.Errorf("序列化替换映射出错: %v", err)
	}

	shell := filepath.Join(libs.Config.DocumentAuto.Plugin, "modify_excel.py")
	tempExcelTemplatePath := filepath.Join(libs.Config.DocumentAuto.SavePath, "temp.xlsx")
	command := fmt.Sprintf("python3 %s '%s' '%s' '%s'", shell, tempExcelTemplatePath, versionTransferExcelFilePath, replacementsJSON)
	output, err := libs.ExecCommand(command)
	if err != nil {
		return "", "", fmt.Errorf("执行modify_excel.py脚本出错: %v", err)
	}
	logging.InfoLogger.Infof("modify_excel.py输出: %s", string(output))

	return versionTransferExcelFileName, versionTransferExcelFilePath, nil
}

// checkAllDocumentsSuccess 检查所有文档是否处理成功
func checkAllDocumentsSuccess(taskID uint) (bool, error) {
	docRecords, err := ddagdocument.GetDocumentByTaskID(taskID)
	if err != nil {
		return false, err
	}

	for _, doc := range docRecords {
		if doc.Status != 1 {
			return false, nil
		}
	}
	return true, nil
}

// checkAllDocumentsFailed 检查所有文档是否处理失败
func checkAllDocumentsFailed(taskID uint) (bool, error) {
	docRecords, err := ddagdocument.GetDocumentByTaskID(taskID)
	if err != nil {
		return false, err
	}

	for _, doc := range docRecords {
		if doc.Status != 2 {
			return false, nil
		}
	}
	return true, nil
}

// downloadBinFile 根据下载地址下载bin文件，并计算文件大小和md5值，完成后删除文件
func downloadBinFile(binDownloadURL string) (string, int, string, error) {
	// 创建临时文件
	tempDir := filepath.Join(libs.Config.DocumentAuto.SavePath, time.Now().Format("20060102"))
	err := os.MkdirAll(tempDir, 0750)
	if err != nil {
		return "", 0, "", fmt.Errorf("创建临时目录失败: %v", err)
	}
	fileName := filepath.Base(binDownloadURL)
	tempFilePath := filepath.Join(tempDir, fileName)

	// 创建文件
	out, err := os.Create(tempFilePath)
	if err != nil {
		return "", 0, "", fmt.Errorf("创建临时文件失败: %v", err)
	}
	defer out.Close()

	// 发起HTTP GET请求
	resp, err := http.Get(binDownloadURL)
	if err != nil {
		return "", 0, "", fmt.Errorf("下载文件失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return "", 0, "", fmt.Errorf("下载文件失败，状态码: %d", resp.StatusCode)
	}

	// 计算MD5和写入文件
	hash := md5.New()
	multiWriter := io.MultiWriter(out, hash)

	bytesWritten, err := io.Copy(multiWriter, resp.Body)
	if err != nil {
		return "", 0, "", fmt.Errorf("写入临时文件失败: %v", err)
	}

	// 计算MD5值
	md5Sum := hex.EncodeToString(hash.Sum(nil))

	// 删除临时文件
	err = os.Remove(tempFilePath)
	if err != nil {
		return "", 0, "", fmt.Errorf("删除临时文件失败: %v", err)
	}

	// 返回文件大小和md5值
	return fileName, int(bytesWritten), md5Sum, nil
}

// constructBinFileURL 构建 bin 文件下载 URL
func constructBinFileURL(buildRecord *dcronmakejob.ListResponse, releaseType string) string {
	if releaseType == "正式发布" {
		return fmt.Sprintf("%s/output/%s/%s/", libs.Config.DocumentAuto.DownloadServer, buildRecord.JobId, buildRecord.Cpu.Cpu)
	}
	return fmt.Sprintf("%s/output/%s/%s/releaseID-bin/", libs.Config.DocumentAuto.DownloadServer, buildRecord.JobId, buildRecord.Cpu.Cpu)
}

// formatSoftwareVersion 格式化软件版本
func formatSoftwareVersion(buildRecord *dcronmakejob.ListResponse, releaseType string) string {
	var softwareVersion string
	if releaseType == "正式发布" {
		re := regexp.MustCompile(`, Release\(\d{8}\)`)
		softwareVersion = re.ReplaceAllString(buildRecord.SoftwareVersion, "")
	} else {
		softwareVersion = buildRecord.SoftwareVersion
	}
	return softwareVersion
}

// saveFile 将上传的文件保存到指定目录，并返回文件路径
func saveFile(file io.Reader, folder, filename string) (string, error) {
	// 定义保存目录
	saveDir := filepath.Join(folder)
	// 创建目录（如果不存在）
	if err := os.MkdirAll(saveDir, os.ModePerm); err != nil {
		return "", err
	}

	// 构建完整的文件路径
	savePath := filepath.Join(saveDir, filename)

	// 创建文件
	out, err := os.Create(savePath)
	if err != nil {
		return "", err
	}
	defer out.Close()

	// 复制文件内容
	_, err = io.Copy(out, file)
	if err != nil {
		return "", err
	}

	return savePath, nil
}
