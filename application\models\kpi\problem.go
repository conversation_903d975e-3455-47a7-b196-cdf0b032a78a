package kpi

import "irisAdminApi/application/models"

type Problem struct {
	models.ModelBase
	Description     string `gorm:"not null; type:varchar(512)"`
	ProblemSourceID uint   `gorm:"not null"`
	DiscoveredAt    string `gorm:"not null; type:varchar(30)"`
	PlanCloseAt     string `gorm:"not null; type:varchar(30)"`
	UserID          uint   `gorm:"not null"`
	Status          uint   `gorm:"not null; default:0"` //0:未闭环  1:已闭环
	OwnerID         uint   `gorm:"not null"`
	DepartmentID    uint   `gorm:"not null"`
}

type ProblemSource struct {
	models.ModelBase
	Name string `gorm:"not null; type:varchar(60)"`
}

type ProblemProccess struct {
	models.ModelBase
	ProblemID   uint   `gorm:"not null"`
	Description string `gorm:"not null; type:varchar(512)"`
	UserID      uint   `gorm:"not null"`
	Status      uint   `gorm:"not null; default:0"`
}

type ProblemAttachment struct {
	models.ModelBase
	ProblemID         uint   `gorm:"not null"`
	ProblemProccessID uint   `gorm:"not null"`
	OriginName        string `gorm:"not null; type:varchar(200)"`
	Name              string `gorm:"index;not null; type:varchar(300)"`
	Type              uint   `gorm:"not null"` // 1: problem
}

type ProblemVersion struct {
	models.ModelBase
	ProblemID  uint `gorm:"uniqueIndex; not null"`
	ProblemIDs string
}
