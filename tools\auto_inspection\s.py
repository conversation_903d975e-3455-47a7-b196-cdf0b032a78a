from http.server import BaseHTTPRequestHandler, HTTPServer
import time
import datetime
import os


# 用于接收巡检日志，
# 创建自定义的请求处理类
class FileUploadHandler(BaseHTTPRequestHandler):

    def do_POST(self):
        start_time = time.time()
        content_length = int(self.headers["Content-Length"])
        # 读取客户端发送的二进制文件数据
        file_data = self.rfile.read(content_length)
        filename = self.headers.get("File-Name", "unknown")

        # 在这里可以对接收到的文件数据进行处理，例如保存到磁盘
        with open(
            os.path.join(
                "/mnt/sata0/auto_inspection_logs/",
                datetime.datetime.now().strftime("%Y%m%d"),
                filename,
            ),
            "wb",
        ) as f:
            f.write(file_data)

        self.send_response(200)
        self.end_headers()

        end_time = time.time()
        time_elapsed_ms = int((end_time - start_time) * 1000)
        self.wfile.write(f"File uploaded successfully in {time_elapsed_ms} ms")


# 启动服务器
def run_server():
    server_address = ("*************", 8093)  # 可以根据需要修改端口号
    httpd = HTTPServer(server_address, FileUploadHandler)
    print("Server running on port 8093...")
    httpd.serve_forever()


# 运行服务器
run_server()
