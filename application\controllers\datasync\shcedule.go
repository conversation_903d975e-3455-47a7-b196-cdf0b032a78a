package datasync

import (
	"sync"

	"irisAdminApi/application/controllers/openfeishu"
	"irisAdminApi/application/logging"

	"github.com/pkg/errors"
)

func GetSyncResourceProjectFromFeishu() ([]string, error) {
	projectNames := []string{}
	records, err := GetTableData("ZfoVbWxqTaI09Rs3Ml1cRzRCnuf", "tblIkkE5WlXux8I7")
	if err != nil {
		logging.ErrorLogger.Error(err)
		return nil, errors.Wrap(err, "")
	}

	for _, record := range records {
		if value, ok := record.Fields["项目"]; ok {
			if value != nil {
				if vv, ok := value.(string); ok && vv != "" {
					projectNames = append(projectNames, vv)
				}
			}
		}
	}
	return projectNames, nil
}

func SyncPmsReviewDataToFeishuWorker() {
	projectNames, _ := GetSyncResourceProjectFromFeishu()
	openfeishu.SyncAllOriginalResourceDataV5(projectNames)
}

var syncFeisuBaseDataLock sync.Mutex

func SyncFeisuBaseData() {
	if syncFeisuBaseDataLock.TryLock() {
		defer syncFeisuBaseDataLock.Unlock()

		logging.DebugLogger.Debugf("开始同步组织架构数据到飞书--安全产品事业部基础数据")
		PmsBuMemberSyncWorker()
		openfeishu.SyncFeiShuBuMemberDatasV4()

		logging.DebugLogger.Debugf("开始同步项目数据到飞书--安全产品事业部基础数据")
		openfeishu.GetPMSProjectListData()
		openfeishu.SyncAllHardwareProjectDataV4()

		logging.DebugLogger.Debugf("开始同步项目里程碑及测评分析表数据到飞书--安全产品事业部基础数据")
		projectNames, projectMap := openfeishu.GetSyncProjectNamesAndMap()
		openfeishu.SyncAllSoftProjectDataV4(projectMap)
		go GetPMSAnalysisDataWorker(projectNames)
		go openfeishu.GetPMSMilestoneData(projectNames)
		openfeishu.SyncAllRequestDataV4()
		openfeishu.SyncAllWorkPacketDataV4()

		logging.DebugLogger.Debugf("开始同步项目需求及工作包数据到飞书--安全产品事业部基础数据")
		openfeishu.SyncAllRequestDataV4()
		openfeishu.SyncAllWorkPacketDataV4()

		logging.DebugLogger.Debugf("开始同步项目资源数据到飞书--安全产品事业部基础数据")
		openfeishu.SyncLastWeekResourceDetailsDataV4()
		openfeishu.SyncAllOriginalResourceDataV4()
		openfeishu.SyncAllOriginalResourceDataV4_2()
		openfeishu.SyncAllOriginalResourceDataV4_3()
		SyncPmsReviewDataToFeishuWorker()
		logging.DebugLogger.Debugf("开始同步项目BUG数据到飞书--安全产品事业部基础数据")
		openfeishu.SyncBugDataV4()

		logging.DebugLogger.Debugf("开始同步硬件项目里程碑数据到飞书--安全产品事业部基础数据")
		PmsHardwareProjectMilestoneSyncWorker()
		SyncFeiShuHardwareProjectMilestoneDatasV4()

		openfeishu.SyncProjectMemberDataV4()
		logging.DebugLogger.Debugf("完成同步数据到飞书--安全产品事业部基础数据")
	}
}

func GetPMSAnalysisDataWorker(projectNames []string) {
	openfeishu.GetPMSAnalysisDataV4(projectNames)
	openfeishu.SyncFeiShuAnalysisDatasV4()
}
