package main

import (
	"encoding/json"
	"fmt"
	"strings"
)

type InspectionResult struct {
	Type      string       `json:"type"`
	Hostname  string       `json:"hostname"`
	IP        string       `json:"ip"`
	CreatedAt string       `json:"created_at"`
	Result    []RuleResult `json:"result"`
}

type RuleResult struct {
	RuleID string   `json:"rule_id"`
	Name   string   `json:"name"`
	Code   string   `json:"code"`
	Msg    string   `json:"msg"`
	Data   []string `json:"data"`
}

// 假设有两个 JSON 文件，一个用于服务器巡检结果，一个用于交换机巡检结果
// 这里仅提供一个示例，如何读取和解析一个 JSON 文件
func main() {
	// 读取 JSON 文件
	// filePath := "inspection.json" // 替换为实际 JSON 文件的路径
	// jsonData, err := ioutil.ReadFile(filePath)
	// if err != nil {
	// 	fmt.Printf("Error reading JSON file: %s\n", err)
	// 	return
	// }

	jsonData := `{"type": "server", "hostname": "bookstack-1", "ip": "************", "created_at": "20240415162923.log", "result": [{"rule_id": "01", "name": "\u68c0\u67e5categraf\u662f\u5
	426\u5b89\u88c5", "code": "1", "msg": "\u5df2\u5b89\u88c5categraf", "data": []}, {"rule_id": "02", "name": "\u68c0\u67e5categraf\u8fdb\u7a0b\u662f\u5426\u8fd0\u884c", "cod
	e": "1", "msg": "\u5df2\u8fd0\u884ccategraf", "data": []}, {"rule_id": "04", "name": "\u68c0\u67e5\u53ef\u767b\u5f55\u5e10\u53f7", "code": "-1", "msg": "\u53ef\u4ee5\u767b
	\u5f55\u5e10\u53f7\u6570\u91cf\u5927\u4e8e3\u4e2a", "data": ["root:x:0:0:root:/root:/bin/bash", "sync:x:4:65534:sync:/bin:/bin/sync", "bookstack-1:x:1000:1000:bookstack-1:
	/home/<USER>/bin/bash", "git:x:996:997::/var/opt/gitlab:/bin/sh", "gitlab-psql:x:994:995::/var/opt/gitlab/postgresql:/bin/sh", "gitlab-prometheus:x:993:994::/var/opt
	/gitlab/prometheus:/bin/sh"]}, {"rule_id": "05", "name": "\u68c0\u67e5\u53ef\u767b\u5f55\u5e10\u53f7\u4e0a\u6b21\u4fee\u6539\u5bc6\u7801\u65f6\u95f4", "code": "-1", "msg":
	 "\u5b58\u5728\u5e10\u53f7\u8fc7\u8d8590\u5929\u672a\u4fee\u6539\u5bc6\u7801", "data": ["Last password change\t\t\t\t\t: Dec 01, 2022", "Last password change\t\t\t\t\t: Ju
	l 31, 2020", "Last password change\t\t\t\t\t: Mar 22, 2023", "Last password change\t\t\t\t\t: Jan 28, 2021", "Last password change\t\t\t\t\t: Jan 28, 2021", "Last password
	 change\t\t\t\t\t: Jan 28, 2021"]}, {"rule_id": "06", "name": "\u786c\u4ef6\u544a\u8b66\u65e5\u5fd7", "code": "1", "msg": "\u786c\u4ef6\u65e0\u544a\u8b66\u65e5\u5fd7", "da
	ta": []}, {"rule_id": "07", "name": "\u5185\u6838\u9519\u8bef\u65e5\u5fd7", "code": "-1", "msg": "\u5185\u6838\u5b58\u5728\u544a\u8b66\u65e5\u5fd7", "data": ["[    0.67518
	9] HEST: Enabling Firmware First mode for corrected errors.", "[    1.343673] ERST: Error Record Serialization Table (ERST) support is initialized.", "[    7.043781] RAS:
	Correctable Errors collector initialized."]}, {"rule_id": "08", "name": "\u7cfb\u7edf\u9519\u8bef\u65e5\u5fd7", "code": "1", "msg": "\u7cfb\u7edf\u65e0\u9519\u8bef\u65e5\u
	5fd7", "data": []}, {"rule_id": "09", "name": "\u6302\u8f7d\u70b9\u4fe1\u606f", "code": "1", "msg": "\u6587\u4ef6\u7cfb\u7edf\u4f7f\u7528\u7387\u65e0\u5f02\u5e38", "data":
	 []}, {"rule_id": "10", "name": "smartctl\u68c0\u67e5", "code": "-1", "msg": "\u78c1\u76d8\u72b6\u6001\u5f02\u5e38", "data": ["/dev/sdg: SMART overall-health self-assessme
	nt test result: Not"]}]}`
	// 解析 JSON 数据
	var results []InspectionResult
	err := json.Unmarshal([]byte(strings.ReplaceAll(jsonData, "\n", "")), &results)
	if err != nil {
		fmt.Printf("Error parsing JSON data: %s\n", err)
		return
	}

	// 统计和报告
	reportInspectionResults(results)
}

func reportInspectionResults(results []InspectionResult) {
	var switchCount, serverCount, issueCount int
	var issues []string

	for _, result := range results {
		switch result.Type {
		case "switch":
			switchCount++
		case "server":
			serverCount++
		}

		for _, rule := range result.Result {
			if rule.Code != "1" { // 假设 code 不为 "1" 表示有问题
				issueCount++
				issues = append(issues, fmt.Sprintf("Hostname: %s, Rule: %s, Message: %s", result.Hostname, rule.Name, rule.Msg))
			}
		}
	}

	// 打印报告
	fmt.Printf("Inspection Report Summary:\n")
	fmt.Printf("Total Switches Inspected: %d\n", switchCount)
	fmt.Printf("Total Servers Inspected: %d\n", serverCount)
	fmt.Printf("Total Issues Found: %d\n", issueCount)
	if issueCount > 0 {
		fmt.Println("Issues List:")
		for _, issue := range issues {
			fmt.Println(issue)
		}
	}
}
