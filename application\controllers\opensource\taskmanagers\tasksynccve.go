package taskmanagers

import (
	"strconv"
	"strings"
	"time"

	"github.com/gocolly/colly"

	"k8s.io/client-go/util/workqueue"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/opensource"
	"irisAdminApi/service/dao/opensource/dcomponent"
	"irisAdminApi/service/dao/opensource/dvulnerability"
	transaction "irisAdminApi/service/transaction/opensource"
)

var ScheduledCVESyncTaskManager *scheduledCVESyncTaskManager

func InitScheduledCVESyncTaskManager() {
	ScheduledCVESyncTaskManager = newScheduledCVESyncTaskManager()
	go func() {
		if components, err := dcomponent.ListAllComponents(); err == nil {
			for _, componentRes := range components {
				logging.DebugLogger.Debugf("组件添加定时同步漏洞任务: ID-<%d>, Name-<%s>", componentRes.ID, componentRes.Name)
				ScheduledCVESyncTaskManager.AddScheduledCVESyncTask(componentRes)
			}
		}
	}()
	ScheduledCVESyncTaskManager.run()
}

func getVersionPatternStr(componentVersion string) string {
	patternStr := ""
	for i := range componentVersion {
		if string(componentVersion[i]) >= "0" && string(componentVersion[i]) <= "9" {
			patternStr += "[0-9]"
		} else if i == '.' {
			patternStr += "."
		} else {
			patternStr += string(componentVersion[i])
		}
	}
	return patternStr
}

func getPageInfo(componentName, componentVersion string, pageNum, total int) ([][]string, int, error) {
	c := colly.NewCollector()
	url := "https://www.tenable.com/cve/search?q=" +
		componentName + "+" + componentVersion +
		"+AND+impact.baseMetricV3.cvssV3.baseSeverity%3A%28CRITICAL+OR+HIGH%29&sort=newest&page=" + strconv.Itoa(pageNum)

	var dataList [][]string

	count := 0
	var innerList []string

	if total == 0 {
		c.OnHTML(".page-link.page-text", func(e *colly.HTMLElement) {
			parts := strings.Split(e.Text, "•")
			total, _ = strconv.Atoi(strings.TrimSpace(strings.Split(parts[0], "of")[1]))
		})
	}

	c.OnHTML("tr:nth-child(n+1) td", func(e *colly.HTMLElement) {
		innerList = append(innerList, e.Text)
		count++
		if count == 3 {
			dataList = append(dataList, innerList)
			innerList = []string{}
			count = 0
		}
	})

	err := c.Visit(url)
	if err != nil {
		return nil, 0, err
	}
	return dataList, total, nil
}

// 爬虫主体
func cveSpider(componentRes *dcomponent.OpenSourceComponent, responsibleUsers, responsibleLeaders []uint,
	latestCveId string, pageNum, total int, syncTime time.Time) []*dvulnerability.OpenSourceVulnerability {
	lowerName := strings.ToLower(componentRes.Name)
	var dataList [][]string
	dataList, total, _ = getPageInfo(componentRes.Name, componentRes.Version, pageNum, total)

	var vulnerabilities []*dvulnerability.OpenSourceVulnerability
	for _, data := range dataList {
		content := strings.ToLower(data[1])
		if strings.Contains(content, lowerName) {
			//// 不建议匹配正则表达式
			//patternStr := getVersionPatternStr(componentRes.Version)
			//matched, _ := regexp.MatchString(patternStr, data[1])
			//if err != nil || !matched{
			//	continue
			//}

			if latestCveId == "" || latestCveId < data[0] {
				logging.DebugLogger.Debugf("组件发现漏洞: ID-<%d>, Name-<%s>, CVE-<%s>, Severity-<%s>",
					componentRes.ID, componentRes.Name, data[0], data[2])
				vulnerabilityRes := &dvulnerability.OpenSourceVulnerability{}
				vulnerabilityRes.CveID = data[0]
				vulnerabilityRes.CveDescription = data[1]
				vulnerabilityRes.CveSeverity = data[2]
				vulnerabilityRes.Status = opensource.VulnerabilityStatusProcessing
				vulnerabilityRes.ComponentID = componentRes.ID
				vulnerabilityRes.EmailTime = &syncTime
				vulnerabilities = append(vulnerabilities, vulnerabilityRes)
			} else {
				return vulnerabilities
			}
		}
	}

	if total > pageNum {
		list := cveSpider(componentRes, responsibleUsers, responsibleLeaders, latestCveId, pageNum+1, total, syncTime)
		vulnerabilities = append(vulnerabilities, list...)
	}
	return vulnerabilities
}

func cveSyncTask(componentRes *dcomponent.OpenSourceComponent, syncTime time.Time) {
	logging.DebugLogger.Debugf("组件执行漏洞同步: ID-<%d>, Name-<%s>", componentRes.ID, componentRes.Name)

	responsibleUserIDs := make([]uint, len(componentRes.ResponsibleUsers))
	responsibleUserUsernames := make([]string, len(componentRes.ResponsibleUsers))
	for i, userInfo := range componentRes.ResponsibleUsers {
		responsibleUserIDs[i] = userInfo.ID
		responsibleUserUsernames[i] = userInfo.Username
	}

	responsibleLeaderIDs := make([]uint, len(componentRes.ResponsibleLeaders))
	responsibleLeaderUsernames := make([]string, len(componentRes.ResponsibleLeaders))
	for i, userInfo := range componentRes.ResponsibleLeaders {
		responsibleLeaderIDs[i] = userInfo.ID
		responsibleLeaderUsernames[i] = userInfo.Username
	}

	res, _ := dvulnerability.GetLatestVulnerabilityByComponentId(componentRes.ID)
	latestCveId := ""
	if res != nil {
		latestCveId = res.CveID
	}

	vulnerabilities := cveSpider(componentRes, responsibleUserIDs, responsibleLeaderIDs, latestCveId, 1, 0, syncTime)
	err := transaction.CreateVulnerabilities(vulnerabilities, responsibleUserIDs, responsibleLeaderIDs)
	if err != nil {
		logging.DebugLogger.Debugf("组件漏洞同步失败: ID-<%d>, Name-<%s>", componentRes.ID, componentRes.Name)
		return
	}

	logging.DebugLogger.Debugf("组件漏洞同步完成, 发送邮件通知: ID-<%d>, Name-<%s>", componentRes.ID, componentRes.Name)
	if len(vulnerabilities) != 0 {
		sendVulnerabilitiesEmail(vulnerabilityFoundSubject(componentRes.Name, componentRes.Version,
			componentRes.Product, componentRes.ProductVersion),
			vulnerabilities, responsibleUserUsernames, responsibleLeaderUsernames, nil)
	}
	updateSyncTime := map[string]interface{}{
		"sync_time": syncTime,
	}
	componentRes.Update(componentRes.ID, updateSyncTime)
}

type scheduledCVESyncTaskManager struct {
	Queue workqueue.DelayingInterface
}

func newScheduledCVESyncTaskManager() *scheduledCVESyncTaskManager {
	return &scheduledCVESyncTaskManager{
		Queue: workqueue.NewNamedDelayingQueue("组件定时同步CVE漏洞队列"),
	}
}

func (manager *scheduledCVESyncTaskManager) AddScheduledCVESyncTask(componentRes *dcomponent.OpenSourceComponent) {
	manager.Queue.Add(componentRes.ID)
}

func (manager *scheduledCVESyncTaskManager) run() {
	var cveSyncTaskWorkers = libs.Config.OpenSource.CVESyncTaskWorkers
	for i := 0; i < cveSyncTaskWorkers; i++ {
		go manager.runCVESync()
	}
}

func (manager *scheduledCVESyncTaskManager) runCVESync() {
	for manager.processNextCVESyncTask() {
	}
}

func (manager *scheduledCVESyncTaskManager) processNextCVESyncTask() bool {
	item, quit := manager.Queue.Get()
	if quit {
		return false
	}
	componentId, ok := item.(uint)
	if !ok || componentId == 0 {
		// item错误, 删除;
		manager.Queue.Done(item)
		return true
	}

	now := time.Now()
	componentRes, err := dcomponent.FindById(componentId)
	if err != nil {
		manager.Queue.Add(item)
		return true
	} else if componentRes == nil {
		// 组件不存在; 删除;
		manager.Queue.Done(item)
		return true
	}

	var cveSyncTaskInterval = time.Duration(libs.Config.OpenSource.CVESyncIntervalInHours) * time.Hour
	if componentRes.SyncTime == nil || now.Sub(*componentRes.SyncTime) > cveSyncTaskInterval {
		cveSyncTask(componentRes, now)
		manager.Queue.AddAfter(item, cveSyncTaskInterval)
	} else {
		manager.Queue.AddAfter(item, now.Sub(*componentRes.SyncTime))
	}
	return true
}

func AddManualCVESyncTask(componentRes *dcomponent.OpenSourceComponent) {
	go cveSyncTask(componentRes, time.Now())
}
