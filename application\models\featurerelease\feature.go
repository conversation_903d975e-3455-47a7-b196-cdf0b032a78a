package featurerelease

import (
	"irisAdminApi/application/models"
)

type Feature struct {
	models.ModelBase
	FileName                 string `gorm:"not null; type:varchar(200)" json:"file_name" form:"file_name"`
	FileSize                 uint   `gorm:"not null" json:"file_size" form:"file_size"`
	FileMd5                  string `gorm:"not null; type:varchar(200)" json:"file_md5" form:"file_md5"`
	IncrementName            string `gorm:"not null; type:varchar(200)" json:"increment_name" form:"increment_name"`
	IncrementSize            uint   `gorm:"not null" json:"increment_size" form:"increment_size"`
	IncrementMd5             string `gorm:"not null; type:varchar(200)" json:"increment_md5" form:"increment_md5"`
	Version                  string `gorm:"not null; type:varchar(60)" json:"version" form:"version"`
	ProductModels            string `json:"product_models" form:"product_models"`
	SoftVersions             string `json:"soft_versions" form:"soft_versions"`
	HardVersions             string `json:"hard_versions" form:"hard_versions"`
	FeatureType              string `gorm:"not null; type:varchar(60)" json:"feature_type" form:"feature_type"`
	FeatureVersions          string `json:"feature_versions" form:"feature_versions"`
	FeatureBaseVersion       string `gorm:"type:varchar(60)" json:"feature_base_version" form:"feature_base_version"`
	FileType                 uint   `gorm:"not null" json:"file_type" form:"file_type"`
	ReleaseDate              string `gorm:"not null; type:varchar(60)" json:"release_date" form:"release_date"`
	VersionDesc              string `gorm:"not null type:varchar(256)" json:"version_desc" form:"version_desc"`
	Desc                     string `json:"desc" form:"desc"`
	Sign                     string `json:"sign" form:"sign"`
	Uuid                     string `gorm:"not null; type:varchar(60)" json:"uuid" form:"uuid"`
	Status                   uint   `gorm:"not null, default:0" json:"status" form:"status"` //0:失败 1:成功 4: 废弃
	UpdateToMain             bool   `gorm:"not null, default:false" json:"update_to_main"  form:"update_to_main"`
	OverTimeNotice           bool   `gorm:"not null, default: false" json:"over_time_notice" form:"over_time_notice"`
	SecCloud                 string `gorm:"not null" json:"sec_cloud" form:"sec_cloud"`
	Urgency                  bool   `gorm:"not null, default: false" json:"urgency" form:"urgency"`
	GrayRuleID               string `gorm:"not null type:varchar(200)" json:"gray_rule_id" form:"gray_rule_id"` //灰度规则ID
	FeatureSecCloudConfigIDs string `gorm:"not null; type:varchar(200)" json:"feature_sec_cloud_config_ids"`
	IsNewFormat              bool   `gorm:"not null, default: false" json:"is_new_format" form:"is_new_format"` //0:旧数据 1:新数据
}

type FeatureProcDef struct {
	models.ModelBase
	Name    string `json:"name,omitempty"`
	Version int    `json:"version,omitempty"`
	// 流程定义json字符串
	Resource      string `gorm:"size:10000" json:"resource,omitempty"`
	ProductModels string `gorm:"type:varchar(500)" json:"product_models"`
}

type FeatureProcInst struct {
	models.ModelBase
	// 流程定义ID
	// ProcDefID int `json:"proc_def_id"`
	// title 标题
	Title string `json:"title"`
	// 当前节点
	NodeID string `json:"node_id"`
	// 审批人
	// Candidate string `json:"candidate"`
	// 当前任务
	TaskID      uint   `json:"task_id"`
	StartUserID uint   `json:"start_user_id"`
	FeatureID   uint   `json:"feature_id"`
	Status      uint   `gorm:"not null,default:0"` //0:进行中 1:完成 2：失败
	Resource    string `gorm:"size:10000" json:"resource,omitempty"`
}

type FeatureProcTask struct {
	models.ModelBase
	// 当前执行流所在的节点
	NodeName   string `json:"nodeName"`
	PrevNodeID string `json:"prevNodeId"`
	NodeID     string `json:"nodeId"`
	// Step   int    `json:"step"`
	// 流程实例id
	ProcInstID uint `json:"procInstID"`
	Assignee   uint `json:"assignee"`
	// 还未审批的用户数，等于0代表会签已经全部审批结束，默认值为1
	// MemberCount   int8 `json:"memberCount" gorm:"default:1"`
	// UnCompleteNum int8 `json:"unCompleteNum" gorm:"default:1"`
	//审批通过数
	// AgreeNum int8 `json:"agreeNum"`
	// and 为会签，or为或签，默认为or
	// ActType    string `json:"actType" gorm:"default:'or'"`
	Comment        string `json:"comment"`
	Status         uint   `gorm:"not null,default:0"` //0:进行中 1:通过 2：转派 3:回退
	Flag           bool   `gorm:"not null,default:true"`
	Attachment     string `gorm:"type:varchar(300)"`
	OverTimeNotice bool   `gorm:"not null, default: false"`
}

type SecCloud struct {
	models.ModelBase
	Name          string `gorm:"not null; default:''; type:varchar(60)" json:"name"`
	Url           string `gorm:"not null; default:''; type:varchar(2000)" json:"url"`
	ProductModels string `gorm:"not null; default:''; type:varchar(2000)" json:"product_models"`
	Status        bool   `gorm:"not null; default:true" json:"status"`
	Username      string `gorm:"not null; default:''; type:varchar(60)" json:"username"`
	Password      string `gorm:"not null; default:''; type:varchar(60)" json:"Password"`
	AccessToken   string `gorm:"not null" json:"access_token"`
	RefreshToken  string `gorm:"not null" json:"refresh_token"`
	Expires       int    `gorm:"not null" json:"expires"`
	Type          string `gorm:"not null; type:varchar(60)" json:"type"`
}

type FeatureSecCloudConfig struct {
	models.ModelBase
	SecCloud      string `json:"sec_cloud"`
	ProductModels string `gorm:"type:varchar(256)" json:"product_models"`
	SoftVersions  string `gorm:"type:varchar(256)" json:"soft_versions"`
	GrayRuleID    string `gorm:"type:varchar(256)" json:"gray_rule_id"`
}

type FeatureCloudConfigRelation struct {
	models.ModelBase
	FeatureID uint `gorm:"not null" json:"feature_id"`
	ConfigID  uint `gorm:"not null" json:"config_id"`
}
