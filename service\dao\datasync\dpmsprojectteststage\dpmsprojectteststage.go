package dpmsprojectteststage

import (
	"encoding/json"
	"fmt"
	"reflect"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/datasync"
	"irisAdminApi/service/dao/datasync/dsyncrecord"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "PMS项目阶段表"

type PmsProjectTestStageSyncResponse struct {
	State   string                         `json:"state"`
	Data    []*PmsProjectTestStageResponse `json:"data"`
	Total   int                            `json:"total"`
	Message string                         `json:"message"`
}

type PmsProjectTestStageResponse struct {
	RowNum        int    `json:"rownum"`
	ID            int    `gorm:"primarykey; autoIncrement:false" json:"id" `
	ProjectID     int    `gorm:"not null; index:idx_unique, unique" json:"projectId" `
	ProjectName   string `gorm:"not null; type:varchar(100)" json:"projectName"`
	StageName     string `gorm:"not null; type:varchar(100)" json:"stageName"`
	PlanStartTime string `gorm:"not null; type:varchar(100)" json:"planStartTime"`
	PlanEndTime   string `gorm:"not null; type:varchar(100)" json:"planEndTime"`
	ActStartTime  string `gorm:"not null; type:varchar(100)" json:"actStartTime"`
	ActEndTime    string `gorm:"not null; type:varchar(100)" json:"actEndTime"`
}

type PmsProjectTestStage struct {
	datasync.PmsProjectTestStage
}

type ListResponse struct {
	PmsProjectTestStage
}

type Request struct {
	Id uint `json:"id"`
}

func (this *PmsProjectTestStage) ModelName() string {
	return ModelName
}

func Model() *datasync.PmsProjectTestStage {
	return &datasync.PmsProjectTestStage{}
}

func (this *PmsProjectTestStage) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *PmsProjectTestStage) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *PmsProjectTestStage) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *PmsProjectTestStage) CreateV2(object interface{}) error {
	return nil
}

func (this *PmsProjectTestStage) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *PmsProjectTestStage) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *PmsProjectTestStage) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *PmsProjectTestStage) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *PmsProjectTestStage) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *PmsProjectTestStage) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func UpdateOrCreatePmsProjectTestStageTransaction(items []*PmsProjectTestStageResponse, _url string, data map[string]string, method, state, errorMsg string) error {
	objects := []map[string]interface{}{}
	for _, item := range items {
		object := map[string]interface{}{
			"ID":          item.ID,
			"ProjectID":   item.ProjectID,
			"ProjectName": item.ProjectName,
			"StageName":   item.StageName,
		}

		if len(item.PlanStartTime) > 0 {
			object["PlanStartTime"] = item.PlanStartTime
		}
		if len(item.PlanEndTime) > 0 {
			object["PlanEndTime"] = item.PlanEndTime
		}
		if len(item.ActStartTime) > 0 {
			object["ActStartTime"] = item.ActStartTime
		}
		if len(item.ActEndTime) > 0 {
			object["ActEndTime"] = item.ActEndTime
		}
		objects = append(objects, object)
	}

	columns := []string{}

	xt := reflect.TypeOf(Model())
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")
		if ok && ok2 {
			columns = append(columns, name)
		}
	}

	body, err := json.Marshal(data)
	if err != nil {
		return err
	}
	db := easygorm.GetEasyGormDb()
	err = db.Transaction(func(tx *gorm.DB) error {
		if len(objects) > 0 {
			err := tx.Model(Model()).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "id"}},
				DoUpdates: clause.AssignmentColumns(columns),
			}).Create(&objects).Error
			if err != nil {
				return err
			}
		}

		if err := tx.Model(dsyncrecord.Model()).Create(map[string]interface{}{
			"url":             _url,
			"body":            body,
			"method":          method,
			"state":           state,
			"message":         errorMsg,
			"min_modify_date": data["minModifyDate"],
			"max_modify_date": data["maxModifyDate"],
			"created_at":      time.Now(),
		}).Error; err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}
