package datasync

import (
	"errors"
	"fmt"
	"strconv"
	"time"

	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/datasync/dpmsworkpacketinfo"
)

func PmsWorkpacketInfoSyncWorker() error {
	err := dpmsworkpacketinfo.DeleteAll()
	if err != nil {
		logging.ErrorLogger.Errorf("delete all workinfo error", err.Error())
		return err
	}
	// todo: 检查同步记录，获取同步时间，如果没有，从七天前开始，按小时同步
	// modifyDateArray := []string{}
	_url := "https://dataware.ruijie.com.cn/api/public/data-api/safe_product_line_pms_workpacket_info/list.data"
	// records, err := dsyncrecord.FindLastSuccessSyncRecordWithoutDate(_url)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("get last sync records", err.Error())
	// 	return err
	// }

	page := 1
	rows := 1000

	// if len(records) > 0 {
	// 	body := Body{}
	// 	err := json.Unmarshal([]byte(records[0].Body), &body)
	// 	if err != nil {
	// 		logging.ErrorLogger.Errorf("unmarshal body err", err.Error())
	// 	}
	// 	page, err = strconv.Atoi(body.Page)
	// 	if err != nil {
	// 		logging.ErrorLogger.Errorf("page a to i err", err.Error())
	// 	}
	// 	rows, err = strconv.Atoi(body.Rows)
	// 	if err != nil {
	// 		logging.ErrorLogger.Errorf("rows a to i err", err.Error())
	// 	}
	// }

	for {
		data := map[string]string{
			"sid":           "ZmYyNjJiOGMz",
			"minModifyDate": "",
			"maxModifyDate": "",
			"page":          strconv.Itoa(page),
			"rows":          strconv.Itoa(rows),
		}

		var result dpmsworkpacketinfo.PmsWorkpacketInfoSyncResponse
		var errMsg dpmsworkpacketinfo.PmsWorkpacketInfoSyncResponse

		resp, err := SyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(data).Get(_url)
		if err != nil {
			logging.ErrorLogger.Errorf("get pms work packet infos error", err.Error())
			return err
		}
		if resp.IsSuccessState() {
			if result.State == "SUCCESS" {
				err := dpmsworkpacketinfo.UpdateOrCreatePmsWorkpacketInfoTransaction(result.Data, _url, data, resp.Request.Method, result.State, result.Message)
				if err != nil {
					logging.ErrorLogger.Errorf("update or create pms work packet infos error", err.Error())
					return err
				}
			} else {
				logging.ErrorLogger.Errorf("get pms work packet infos failed", result.State, result.Message)
				return fmt.Errorf("get pms work packet infos failed, %s, %s", result.State, result.Message)
			}
		} else {
			logging.ErrorLogger.Errorf("get pms work packet infos unkown error")
			return errors.New("unkown error")
		}

		time.Sleep(5 * time.Second)
		if result.Total > rows*page {
			page++
		} else {
			break
		}
	}

	return nil
}
