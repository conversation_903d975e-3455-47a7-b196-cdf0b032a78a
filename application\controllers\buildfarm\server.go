package buildfarm

import (
	"bytes"
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/buildfarm/dserver"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

func TestConnection(ctx iris.Context) {
	serverReq := &dserver.ServerReq{}
	if err := ctx.ReadJSON(serverReq); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*serverReq)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	serverReq.Connection = "succeed"
	err := TestSSHJob(serverReq.Host, serverReq.Port, serverReq.Username, serverReq.Password)
	if err != nil {
		serverReq.Connection = "failed"
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, serverReq, response.NoErr.Msg))
	return
}

func TestSSHJob(sshHost string, sshPort int, sshUser, sshPass string) error {
	_, err := libs.SSHConnect(sshUser, sshPass, sshHost, sshPort)

	if err != nil {
		logging.ErrorLogger.Errorf("ssh connection get err ", err)
		return err
	}
	return nil
}

func SSHCopyIdJob(sshHost string, sshPort int, sshUser, sshPass string) error {
	privateKeyFile := libs.Config.Rsa.Privatekey
	if _, err := os.Stat(privateKeyFile); os.IsNotExist(err) {
		cmd := exec.Command("ssh-keygen", "-t", "rsa", "-N", "", "-f", privateKeyFile, "-q")
		err := cmd.Run()
		if err != nil {
			logging.ErrorLogger.Errorf("create ssh keygen err ", err)
			return err
		}
	}
	c := fmt.Sprintf("sshpass -p %s ssh-copy-id -o StrictHostKeyChecking=no %s -p %s", sshPass, fmt.Sprintf("%s@%s", sshUser, sshHost), strconv.Itoa(sshPort))
	cmd := exec.Command("bash", "-c", c)
	var out bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &stderr
	err := cmd.Run()

	if err != nil {
		logging.ErrorLogger.Errorf("ssh copyid get err ", err, stderr.String())
		return err
	}
	return nil
}

func CreateServer(ctx iris.Context) {
	serverReq := &dserver.ServerReq{}
	if err := ctx.ReadJSON(serverReq); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*serverReq)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	err := SSHCopyIdJob(serverReq.Host, serverReq.Port, serverReq.Username, serverReq.Password)
	if err != nil {
		logging.ErrorLogger.Errorf("create server get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	err = dao.Create(&dserver.Response{}, ctx, map[string]interface{}{
		"Host":       serverReq.Host,
		"Port":       serverReq.Port,
		"Username":   serverReq.Username,
		"Desc":       serverReq.Desc,
		"Name":       serverReq.Name,
		"Key":        true,
		"CreatedAt":  time.Now(),
		"UpdatedAt":  time.Now(),
		"Enable":     serverReq.Enable,
		"Parallel":   serverReq.Parallel,
		"Prometheus": serverReq.Prometheus,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, "", response.NoErr.Msg))
	return
}

// GetUsers
func GetServers(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dserver.Response{}, ctx, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetServerStatus(ctx iris.Context) {
	serverStatus, err := ServerStatus()
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, serverStatus, response.NoErr.Msg))
	return
}

func GetServerPerf(ctx iris.Context) {
	// resp, err := http.Get("http://192.168.1.243:9100/metrics")
}
