package dresourceclean

import (
	"fmt"
	"reflect"
	"strings"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/models/datasync"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "资源表"

type Resource struct {
	datasync.Resource
}

type ResourceClean struct {
	datasync.ResourceClean
}

type ListResponse struct {
	WorkClassName     string  `gorm:"not null; type:varchar(100); index:idx_unique,unique" json:"work_class_name" xlsx:"类别"`
	ProjectName       string  `gorm:"not null; type:varchar(100); index:idx_unique,unique" json:"project_name" xlsx:"项目"`
	DepartmentName    string  `gorm:"not null; type:varchar(100)" json:"department_name" xlsx:"部门"`
	GroupName         string  `gorm:"not null; type:varchar(100); index:idx_unique,unique" json:"group_name" xlsx:"专业组"`
	UserName          string  `gorm:"not null; type:varchar(100); index:idx_unique,unique" json:"user_name" xlsx:"工程师"`
	StageName         string  `gorm:"not null; type:varchar(100); index:idx_unique,unique" json:"stage_name" xlsx:"阶段"`
	Year              int     `gorm:"not null; index:idx_unique,unique" json:"year" xlsx:"年"`
	Season            int     `gorm:"not null; index:idx_unique,unique" json:"seaon" xlsx:"季"`
	Month             int     `gorm:"not null; index:idx_unique,unique" json:"month" xlsx:"月"`
	Week              string  `gorm:"not null; type:varchar(100); index:idx_unique,unique" json:"week" xlsx:"周"`
	ReportDate        string  `gorm:"not null; type:varchar(100); index:idx_unique,unique" json:"report_date" xlsx:"汇报日期"`
	WorkTime          float32 `gorm:"not null" json:"work_time" update:"1" xlsx:"工作时间"`
	AddTime           float32 `gorm:"not null" json:"add_time" update:"1" xlsx:"加班时间"`
	TotalTime         float32 `gorm:"not null" json:"total_time" update:"1" xlsx:"总计时间"`
	BugSubmittedCount int     `gorm:"not null" json:"bug_submitted_count" update:"1"`
	BugResolvedCount  int     `gorm:"not null" json:"bug_resolved_count" update:"1"`
	BugClosedCount    int     `gorm:"not null" json:"bug_closed_count" update:"1"`
	CodeAdd           float32 `gorm:"not null" json:"code_add" update:"1"`
	CodeDel           float32 `gorm:"not null" json:"code_del" update:"1"`
	CaseRunCount      int     `gorm:"not null" json:"case_run_count" update:"1"`
	TaskName          string  `json:"task_name" xlsx:"任务名称"`
	Remarks           string  `json:"remarks" xlsx:"任务进展"`
}

func (this *Resource) ModelName() string {
	return ModelName
}

func Model() *datasync.ResourceClean {
	return &datasync.ResourceClean{}
}

func Clean() error {
	results := []*ListResponse{}
	/*
	   sql := `SELECT
	   res.project_name as project_name,
	   d.p_name AS department_name,
	   res.group_name as group_name,
	   res.work_class_name as work_class_name,
	   res.stage_name as stage_name,
	   res.year as year,
	   res.month as month,
	   rc2.stage_start_at AS stage_start_at,
	   rc2.stage_end_at AS stage_end_at,
	   (case when res.month <= 3 then 1
	         when res.month > 3 AND res.month <= 6 then 2
	         when res.month > 6 AND res.month <= 9 then 3
	         ELSE 4
	         END) AS season,
	   concat(SUBDATE(CURDATE(), WEEKDAY(CURDATE())), "~", DATE_ADD(SUBDATE(CURDATE(), WEEKDAY(CURDATE())), interval 7 DAY)) `week`,
	   ifnull(res.total_add_time, 0) add_time,
	   ifnull(res.total_work_time, 0) work_time,
	   ifnull(res.total_total_time, 0) total_time,
	   ifnull(mr.code_add, 0) code_add,
	   ifnull(mr.code_remove, 0) code_del,
	   ifnull(b1.resolved_count, 0) resolve_bug,
	   ifnull(b2.created_count, 0) submit_bug
	   -- if (res.group_name = b.bug_owner_group, bug_count, 0) resolve_bugs,
	   -- if (res.group_name = b.bug_submitter_group, bug_count, 0) submit_bugs
	   FROM (
	   SELECT project_name, group_name, work_class_name, stage_name_clean stage_name, year, month, SUM(add_time) AS total_add_time, SUM(work_time) AS total_work_time, SUM(total_time) AS total_total_time
	   FROM resources
	   -- WHERE group_name REGEXP '研发一部|研发二部|研发三部|安全架构部|安全产品测试'
	   GROUP BY project_name, group_name, work_class_name, stage_name_clean, year, month
	   ) res
	   LEFT JOIN
	   (
	   SELECT
	   mr.release_project,
	   d.name group_name,
	   stage_name,
	   DATE_FORMAT(mr.created_at, '%Y') YEAR,
	   DATE_FORMAT(mr.created_at, '%m') month,
	   SUM(code_quantity_add*discount) AS code_add,
	   SUM(code_quantity_remove*discount) AS code_remove
	   FROM (
	       SELECT
	           release_project,
	           mr.release_project_id,
	           mr.user_id,
	           mr.created_at,
	           code_quantity_add,
	           code_quantity_remove,
	           discount,
	           if(mr.created_at < rc.integrate_start_at, '2-执行阶段', if(mr.created_at >= rc.integrate_start_at AND mr.created_at < rc.first_bug_fix_start_at, '3-集成阶段', if(mr.created_at >= rc.first_bug_fix_start_at AND mr.created_at < rc.experiment_release_at, '4-内测阶段', NULL))) stage_name
	       FROM merge_requests mr
	       LEFT JOIN release_project_configs rc ON rc.release_project_id = mr.release_project_id
	       WHERE STATUS =3
	   ) mr
	   LEFT JOIN release_project_configs rc ON rc.release_project_id = mr.release_project_id
	   LEFT JOIN user_departments ud ON ud.user_id = mr.user_id
	   LEFT JOIN departments d ON d.id = ud.department_id
	   LEFT JOIN users u ON u.id = mr.user_id
	   GROUP BY release_project, d.name, DATE_FORMAT(mr.created_at, '%Y'), DATE_FORMAT(mr.created_at, '%m'), stage_name

	   ) mr ON mr.release_project = res.project_name AND mr.year = res.year AND mr.month = res.month AND res.group_name = mr.group_name AND res.stage_name = mr.stage_name

	   LEFT JOIN
	   (
	      SELECT
	      bug_os,
	      bug_owner_group,
	      stage_name,
	      DATE_FORMAT(bug_resolved_at, '%Y') year,
	      DATE_FORMAT(bug_resolved_at, '%m') month,
	      COUNT(*) resolved_count
	      from
	       (
	          SELECT
	          bug_os,
	          bug_owner_group,
	          (case when bug_cbd_at >= IFNULL(bug_dbd_at, 0) AND bug_cbd_at >= IFNULL(bug_giveup_at, 0) AND bug_cbd_at >= IFNULL(bug_delay_at, 0) then bug_cbd_at
	                   when bug_dbd_at >= IFNULL(bug_cbd_at, 0) AND bug_dbd_at >= IFNULL(bug_giveup_at, 0) AND bug_dbd_at >= IFNULL(bug_delay_at, 0) then bug_dbd_at
	                   when bug_giveup_at >= IFNULL(bug_dbd_at, 0) AND bug_giveup_at >= IFNULL(bug_cbd_at, 0) AND bug_giveup_at >= IFNULL(bug_delay_at, 0) then bug_giveup_at
	                   else bug_delay_at
	            end
	           ) bug_resolved_at,
	          -- bug_created_at,
	           -- if(bug_created_at < rc.integrate_start_at, '1-启动阶段', if(bug_created_at >= rc.integrate_start_at AND bug_created_at < rc.first_bug_fix_start_at, '2-执行阶段', if(bug_created_at >= rc.first_bug_fix_start_at AND bug_created_at < rc.experiment_release_at, '内测', NULL))) created_stage_name,
	           if(	   (case when bug_cbd_at >= IFNULL(bug_dbd_at, 0) AND bug_cbd_at >= IFNULL(bug_giveup_at, 0) AND bug_cbd_at >= IFNULL(bug_delay_at, 0) then bug_cbd_at
	                   when bug_dbd_at >= IFNULL(bug_cbd_at, 0) AND bug_dbd_at >= IFNULL(bug_giveup_at, 0) AND bug_dbd_at >= IFNULL(bug_delay_at, 0) then bug_dbd_at
	                   when bug_giveup_at >= IFNULL(bug_dbd_at, 0) AND bug_giveup_at >= IFNULL(bug_cbd_at, 0) AND bug_giveup_at >= IFNULL(bug_delay_at, 0) then bug_giveup_at
	                   else bug_delay_at
	            end
	           ) < rc.integrate_start_at, '2-执行阶段', if(	   (case when bug_cbd_at >= IFNULL(bug_dbd_at, 0) AND bug_cbd_at >= IFNULL(bug_giveup_at, 0) AND bug_cbd_at >= IFNULL(bug_delay_at, 0) then bug_cbd_at
	                   when bug_dbd_at >= IFNULL(bug_cbd_at, 0) AND bug_dbd_at >= IFNULL(bug_giveup_at, 0) AND bug_dbd_at >= IFNULL(bug_delay_at, 0) then bug_dbd_at
	                   when bug_giveup_at >= IFNULL(bug_dbd_at, 0) AND bug_giveup_at >= IFNULL(bug_cbd_at, 0) AND bug_giveup_at >= IFNULL(bug_delay_at, 0) then bug_giveup_at
	                   else bug_delay_at
	            end
	           ) >= rc.integrate_start_at AND 	   (case when bug_cbd_at >= IFNULL(bug_dbd_at, 0) AND bug_cbd_at >= IFNULL(bug_giveup_at, 0) AND bug_cbd_at >= IFNULL(bug_delay_at, 0) then bug_cbd_at
	                   when bug_dbd_at >= IFNULL(bug_cbd_at, 0) AND bug_dbd_at >= IFNULL(bug_giveup_at, 0) AND bug_dbd_at >= IFNULL(bug_delay_at, 0) then bug_dbd_at
	                   when bug_giveup_at >= IFNULL(bug_dbd_at, 0) AND bug_giveup_at >= IFNULL(bug_cbd_at, 0) AND bug_giveup_at >= IFNULL(bug_delay_at, 0) then bug_giveup_at
	                   else bug_delay_at
	            end
	           ) < rc.first_bug_fix_start_at, '3-集成阶段', if(	   (case when bug_cbd_at >= IFNULL(bug_dbd_at, 0) AND bug_cbd_at >= IFNULL(bug_giveup_at, 0) AND bug_cbd_at >= IFNULL(bug_delay_at, 0) then bug_cbd_at
	                   when bug_dbd_at >= IFNULL(bug_cbd_at, 0) AND bug_dbd_at >= IFNULL(bug_giveup_at, 0) AND bug_dbd_at >= IFNULL(bug_delay_at, 0) then bug_dbd_at
	                   when bug_giveup_at >= IFNULL(bug_dbd_at, 0) AND bug_giveup_at >= IFNULL(bug_cbd_at, 0) AND bug_giveup_at >= IFNULL(bug_delay_at, 0) then bug_giveup_at
	                   else bug_delay_at
	            end
	           ) >= rc.first_bug_fix_start_at AND 	   (case when bug_cbd_at >= IFNULL(bug_dbd_at, 0) AND bug_cbd_at >= IFNULL(bug_giveup_at, 0) AND bug_cbd_at >= IFNULL(bug_delay_at, 0) then bug_cbd_at
	                   when bug_dbd_at >= IFNULL(bug_cbd_at, 0) AND bug_dbd_at >= IFNULL(bug_giveup_at, 0) AND bug_dbd_at >= IFNULL(bug_delay_at, 0) then bug_dbd_at
	                   when bug_giveup_at >= IFNULL(bug_dbd_at, 0) AND bug_giveup_at >= IFNULL(bug_cbd_at, 0) AND bug_giveup_at >= IFNULL(bug_delay_at, 0) then bug_giveup_at
	                   else bug_delay_at
	            end
	           ) < rc.experiment_release_at, '4-内测阶段', NULL))) stage_name

	          FROM
	           bugs
	           LEFT JOIN release_projects rp ON rp.name = bugs.bug_os
	           LEFT JOIN release_project_configs rc ON rc.release_project_id = rp.id
	           -- WHERE bug_owner_group REGEXP '研发一部|研发二部|研发三部|安全架构部|安全产品测试'
	       ) bugs

	       GROUP BY bug_os, bug_owner_group, DATE_FORMAT(bug_resolved_at, '%Y'), DATE_FORMAT(bug_resolved_at, '%m'), stage_name
	   ) b1 ON res.project_name = b1.bug_os AND res.group_name = b1.bug_owner_group  AND res.year = b1.year AND res.month = b1.month AND res.stage_name = b1.stage_name
	   LEFT JOIN
	   (
	      SELECT
	      bug_os,
	      bug_submitter_group,
	      stage_name,
	      DATE_FORMAT(bug_created_at, '%Y') year,
	      DATE_FORMAT(bug_created_at, '%m') month,
	      COUNT(*) created_count
	      from
	       (
	          SELECT
	          bug_os,
	          bug_submitter_group,
	          bug_created_at,
	           if(bug_created_at < rc.integrate_start_at, '2-执行阶段', if(bug_created_at >= rc.integrate_start_at AND bug_created_at < rc.first_bug_fix_start_at, '3-集成阶段', if(bug_created_at >= rc.first_bug_fix_start_at AND bug_created_at < rc.experiment_release_at, '4-内测阶段', NULL))) stage_name

	          FROM
	           bugs
	           LEFT JOIN release_projects rp ON rp.name = bugs.bug_os
	           LEFT JOIN release_project_configs rc ON rc.release_project_id = rp.id
	           -- WHERE bug_owner_group REGEXP '研发一部|研发二部|研发三部|安全架构部|安全产品测试'
	       ) bugs

	       GROUP BY bug_os, bug_submitter_group, DATE_FORMAT(bug_created_at, '%Y'), DATE_FORMAT(bug_created_at, '%m'), stage_name
	       -- HAVING bug_submitter_group LIKE '%测试%' AND bug_os = 'NTOS1.0R7'
	   ) b2 ON res.project_name = b2.bug_os AND res.group_name = b2.bug_submitter_group  AND res.year = b2.year AND res.month = b2.month AND res.stage_name = b2.stage_name
	   LEFT JOIN
	   (
	       SELECT d1.NAME name, d2.name p_name FROM departments d1
	       LEFT JOIN departments d2 ON d1.parent_id = d2.id
	   ) d
	    ON res.group_name = d.name
	   LEFT join
	   ( SELECT rp.name AS project_name, "2-执行阶段" AS stage_name, NULL as stage_start_at, rc.integrate_start_at AS stage_end_at FROM release_projects rp
	   LEFT JOIN release_project_configs rc ON rc.release_project_id = rp.id
	   UNION
	   SELECT rp.name AS project_name,"3-集成阶段" AS stage_name, rc.integrate_start_at as stage_start_at, rc.test_start_at AS stage_end_at FROM release_projects rp
	   LEFT JOIN release_project_configs rc ON rc.release_project_id = rp.id
	   union
	   SELECT rp.name AS project_name, "4-内测阶段" AS stage_name, rc.test_start_at as stage_start_at, rc.experiment_release_at AS stage_end_at FROM release_projects rp
	   LEFT JOIN release_project_configs rc ON rc.release_project_id = rp.id
	   ) rc2 ON rc2.project_name = res.project_name AND rc2.stage_name = res.stage_name
	   `
	*/
	sql := `
    SELECT
    res.project_name,
    res.department_name,
    group_name,
    res.user_name,
    work_class_name,
    stage_name,
    res.year,
    res.month,
    res.report_date,
    concat(SUBDATE(res.report_date, WEEKDAY(res.report_date)), "~", DATE_ADD(SUBDATE(res.report_date, WEEKDAY(res.report_date)), interval 6 DAY)) week,
    add_time,
    work_time,
    total_time,
    code_add,
    code_remove code_del,
    bug_resolved_count,
    bug_submitted_count,
    bug_closed_count,
    case_run_count
    FROM
    (
        SELECT project_name, department_name, group_name, user_name, work_class_name, stage_name, YEAR, MONTH, report_date, SUM(add_time) add_time, SUM(work_time) work_time, SUM(total_time) total_time
        FROM
        resources
        GROUP BY project_name,  department_name, group_name, user_name, work_class_name, stage_name, YEAR, MONTH, report_date
    ) res
    LEFT JOIN
    (
        SELECT
        mr.release_project,
        mr.name user_name,
        DATE_FORMAT(mr.created_at, '%Y-%m-%d') report_date,
        SUM(code_quantity_add*discount) AS code_add,
        SUM(code_quantity_remove*discount) AS code_remove
        FROM (
            SELECT
                release_project,
                mr.release_project_id,
                mr.user_id,
                u.name,
                mr.created_at,
                code_quantity_add,
                code_quantity_remove,
                discount
            FROM merge_requests mr
            LEFT JOIN users u ON u.id = mr.user_id
            WHERE STATUS =3
        ) mr

        GROUP BY release_project, mr.name, DATE_FORMAT(mr.created_at, '%Y-%m-%d')
    ) mr ON mr.release_project = res.project_name and mr.user_name = res.user_name AND res.report_date = mr.report_date

    LEFT JOIN
    (
       SELECT
       bug_os,
       bug_owner,
        DATE_FORMAT(bug_resolved_at, '%Y-%m-%d') report_date,
       COUNT(*) bug_resolved_count
       from
        (
           SELECT
           bug_os,
           bug_owner,
           (case when bug_cbd_at >= IFNULL(bug_dbd_at, 0) AND bug_cbd_at >= IFNULL(bug_giveup_at, 0) AND bug_cbd_at >= IFNULL(bug_delay_at, 0) then bug_cbd_at
                    when bug_dbd_at >= IFNULL(bug_cbd_at, 0) AND bug_dbd_at >= IFNULL(bug_giveup_at, 0) AND bug_dbd_at >= IFNULL(bug_delay_at, 0) then bug_dbd_at
                    when bug_giveup_at >= IFNULL(bug_dbd_at, 0) AND bug_giveup_at >= IFNULL(bug_cbd_at, 0) AND bug_giveup_at >= IFNULL(bug_delay_at, 0) then bug_giveup_at
                    else bug_delay_at
             end
            ) bug_resolved_at

           FROM
            bugs
        ) bugs

        GROUP BY bug_os, bug_owner, DATE_FORMAT(bug_resolved_at, '%Y-%m-%d')
    ) b1 ON res.project_name = b1.bug_os AND res.report_date = b1.report_date AND res.user_name = b1.bug_owner

    LEFT JOIN
    (
       SELECT
       bug_os,
       bug_submitter,
       DATE_FORMAT(bug_created_at, '%Y-%m-%d') report_date,
       COUNT(*) bug_submitted_count
       from
         bugs
       where bug_state NOT LIKE 'DENIAL%'
       GROUP BY bug_os, bug_submitter, DATE_FORMAT(bug_created_at, '%Y-%m-%d')
        -- HAVING bug_submitter_group LIKE '%测试%' AND bug_os = 'NTOS1.0R7'
    ) b2 ON res.project_name = b2.bug_os AND res.report_date = b2.report_date and res.user_name = b2.bug_submitter

    LEFT JOIN
    (
       SELECT
       bug_os,
       bug_test_charger,
       DATE_FORMAT(bug_cbt_at, '%Y-%m-%d') report_date,
       COUNT(*) bug_closed_count
       from
        bugs

        GROUP BY bug_os, bug_test_charger, DATE_FORMAT(bug_cbt_at, '%Y-%m-%d')
        -- HAVING bug_submitter_group LIKE '%测试%' AND bug_os = 'NTOS1.0R7'
    ) b3 ON res.project_name = b3.bug_os AND res.report_date = b3.report_date and res.user_name = b3.bug_test_charger

    LEFT JOIN
    (
        SELECT
        project_name,
        assign_user_name,
        DATE_FORMAT(execution_time, '%Y-%m-%d') report_date,
        COUNT(*) case_run_count
        FROM pms_case_infos WHERE execution_result != 'SKIP' AND execution_time IS NOT NULL
        GROUP BY project_name, assign_user_name, DATE_FORMAT(execution_time, '%Y-%m-%d')
    ) pci on pci.project_name = res.project_name and res.report_date = pci.report_date and res.user_name = pci.assign_user_name

     ORDER BY user_name, report_date desc
    `
	err := easygorm.GetEasyGormDb().Table("resources").Raw(sql).Scan(&results).Error
	if err != nil {
		return err
	}
	/*
	   models.ModelBase
	   WorkClassName     string  `gorm:"not null; type:varchar(200); index:idx_unique,unique" json:"work_class_name"`
	   ProjectName       string  `gorm:"not null; type:varchar(200); index:idx_unique,unique" json:"project_name"`
	   DepartmentName    string  `gorm:"not null; type:varchar(100)" json:"department_name"`
	   GroupName         string  `gorm:"not null; type:varchar(100); index:idx_unique,unique" json:"group_name"`
	   UserName          string  `gorm:"not null; type:varchar(100); index:idx_unique,unique" json:"user_name" `
	   StageName         string  `gorm:"not null; type:varchar(200); index:idx_unique,unique" json:"stage_name"`
	   TaskName          string  `gorm:"not null; type:varchar(200); index:idx_unique,unique" json:"task_name"`
	   Year              int     `gorm:"not null; index:idx_unique,unique" json:"year"`
	   Season            int     `gorm:"not null; index:idx_unique,unique" json:"seaon"`
	   Month             int     `gorm:"not null; index:idx_unique,unique" json:"month"`
	   ReportDate        string  `gorm:"not null; type:varchar(100); index:idx_unique,unique" json:"report_date"`
	   WorkTime          float32 `gorm:"not null" json:"work_time" update:"1"`
	   AddTime           float32 `gorm:"not null" json:"add_time" update:"1"`
	   TotalTime         float32 `gorm:"not null" json:"total_time" update:"1"`
	   BugSubmittedCount int     `gorm:"not null" json:"bug_submitted_count" update:"1"`
	   BugResolvedCount  int     `gorm:"not null" json:"bug_resolved_count" update:"1"`
	   CodeAdd           int     `gorm:"not null" json:"code_add" update:"1"`
	   CodeDel           int     `gorm:"not null" json:"code_del" update:"1"`
	*/

	batchCreateObjects := []map[string]interface{}{}
	for _, result := range results {
		// departmentName := result.DepartmentName
		// // 数据清洗
		// if strings.HasSuffix(result.GroupName, "本部") {
		// 	departmentName = strings.Replace(result.GroupName, "本部", "", -1)
		// 	if departmentName == "研发三部" {
		// 		departmentName = "安全产品研发三部"
		// 	}
		// }

		item := map[string]interface{}{
			"WorkClassName":     result.WorkClassName,
			"ProjectName":       result.ProjectName,
			"DepartmentName":    result.DepartmentName,
			"GroupName":         result.GroupName,
			"UserName":          result.UserName,
			"StageName":         TransStageName(result.StageName),
			"Year":              result.Year,
			"Season":            GetSeason(result.Month),
			"Month":             result.Month,
			"ReportDate":        result.ReportDate,
			"Week":              result.Week,
			"WorkTime":          result.WorkTime,
			"AddTime":           result.AddTime,
			"TotalTime":         result.TotalTime,
			"BugSubmittedCount": result.BugSubmittedCount,
			"BugResolvedCount":  result.BugResolvedCount,
			"BugClosedCount":    result.BugClosedCount,
			"CodeAdd":           result.CodeAdd,
			"CodeDel":           result.CodeDel,
			"CaseRunCount":      result.CaseRunCount,
		}

		batchCreateObjects = append(batchCreateObjects, item)
	}

	columns := []string{}

	xt := reflect.TypeOf(Model())
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")
		if ok && ok2 {
			columns = append(columns, name)
		}
	}

	db := easygorm.GetEasyGormDb()
	err = db.Transaction(func(tx *gorm.DB) error {
		if len(batchCreateObjects) > 0 {
			for i := 0; i < len(batchCreateObjects); i += 1000 {
				end := i + 1001
				if end > len(batchCreateObjects) {
					end = len(batchCreateObjects)
				}
				err := tx.Model(Model()).Clauses(clause.OnConflict{
					Columns: []clause.Column{
						{Name: "WorkClassName"},
						{Name: "ProjectName"},
						{Name: "GroupName"},
						{Name: "StageName"},
						{Name: "UserName"},
						{Name: "Year"},
						{Name: "Month"},
						{Name: "Season"},
						{Name: "ReportDate"},
					},
					DoUpdates: clause.AssignmentColumns(columns),
				}).Create(batchCreateObjects[i:end]).Error
				if err != nil {
					return err
				}
			}
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func TransStageName(stageName string) string {
	stageMap := map[string]string{
		"启动": "1-启动阶段",
		"计划": "1-启动阶段",
		"模块": "2-执行阶段",
		"集成": "3-集成阶段",
		"内部": "4-内测阶段",
		"发布": "5-发布阶段",
	}

	var _stageName string
	_stageNameArray := strings.Split(stageName, "")
	if len(_stageNameArray) > 2 {
		_stageName = strings.Join(_stageNameArray[0:2], "")
	}

	if v, ok := stageMap[_stageName]; ok {
		return v
	} else {
		return stageName
	}
}

func GetSeason(month int) int {
	switch month {
	case 1, 2, 3:
		return 1
	case 4, 5, 6:
		return 2
	case 7, 8, 9:
		return 3
	case 10, 11, 12:
		return 4
	}
	return 0
}

func Export(year, season, month, week, project, department, group, user string) (error, []interface{}, [][]interface{}) {
	xt := reflect.TypeOf(&ListResponse{})
	// xv := reflect.ValueOf(s)

	rows := [][]interface{}{}
	headers := []interface{}{}
	for i := 0; i < xt.Elem().NumField(); i++ {
		head, ok := xt.Elem().Field(i).Tag.Lookup("xlsx")
		if ok {
			headers = append(headers, head)
		}
	}
	rows = append(rows, headers)
	items := []*ListResponse{}
	db := easygorm.GetEasyGormDb().Table(
		`
(SELECT
r1.work_class_name,
r2.project_name,
r1.user_name,
r1.department_name,
r1.group_name,
r1.stage_name,
r1.year,
r1.season,
r1.month,
r1.week,
r1.report_date,
r2.task_name,
r2.work_time,
r2.add_time,
r2.total_time,
r2.remarks
FROM
resource_cleans r1
LEFT JOIN
resources r2
ON
r1.work_class_name = r2.work_class_name AND
r1.project_name = r2.project_name
AND r1.user_name = r2.user_name
AND r1.report_date = r2.report_date)
as resource_cleans
`,
	).Where("year = ?", year)
	if season != "" && strings.ToLower(season) != "all" {
		db = db.Where("season in (?) ", strings.Split(season, ","))
	}
	if month != "" && strings.ToLower(month) != "all" {
		db = db.Where("month in (?) ", strings.Split(month, ","))
	}
	if week != "" && strings.ToLower(week) != "all" {
		db = db.Where("week in (?) ", strings.Split(week, ","))
	}

	if project != "" {
		switch project {
		case "hard":
			db = db.Where("work_class_name = '硬件项目'")
		case "soft":
			db = db.Where("work_class_name = '软件项目'")
		default:
			db = db.Where("work_class_name != '软件项目' AND work_class_name != '硬件项目' AND work_class_name != '请假'")
		}
	}
	if department != "" {
		db = db.Where("department_name = ?", department)
	}
	if group != "" {
		db = db.Where("group_name = ?", group)
	}
	if user != "" {
		db = db.Where("user_name = ?", user)
	}
	err := db.Find(&items).Error
	if err != nil {
		return err, nil, nil
	}
	for _, e := range items {
		cells := []interface{}{}
		xv := reflect.ValueOf(e)
		for i := 0; i < xv.Elem().NumField(); i++ {
			_, ok := xt.Elem().Field(i).Tag.Lookup("xlsx")
			if ok {
				cells = append(cells, xv.Elem().Field(i).Interface())
			}

		}
		rows = append(rows, cells)
	}
	return nil, headers, rows
}

type GroupResourceResponse struct {
	UserName          string  `json:"user_name" xlsx:"工程师"`
	WorkDays          float32 `json:"work_days" xlsx:"应投入资源"`
	HardTotalTime     float32 `json:"hard_total_time" xlsx:"硬件项目总资源(天)"`
	SoftTotalTime     float32 `json:"soft_total_time" xlsx:"软件项目总资源(天)"`
	OtherTotalTime    float32 `json:"other_total_time" xlsx:"其他总资源(天)"`
	BreakTotalTime    float32 `json:"break_total_time" xlsx:"请假(天)"`
	AddTime           float32 `json:"add_time" update:"1" xlsx:"加班时间"`
	CodeAdd           float32 `json:"code_add" update:"1" xlsx:"新增代码量(KLOC)"`
	CodeDel           float32 `json:"code_del" update:"1" xlsx:"删除代码量(KLOC)"`
	BugSubmittedCount int     `json:"bug_submitted_count" update:"1"  xlsx:"提交bug数"`
	BugSubmittedRate  float32 `json:"bug_submitted_rate" update:"1"  xlsx:"提BUG人效(个/天)"`
	BugResolvedCount  int     `json:"bug_resolved_count" update:"1" xlsx:"解决bug数"`
	BugClosedCount    int     `gorm:"not null" json:"bug_closed_count" update:"1"  xlsx:"关闭bug数"`

	CaseRunCount int     `json:"case_run_count" update:"1" xlsx:"测试用例执行次数"`
	CaseRunRate  float32 `json:"case_run_rate" update:"1" xlsx:"测试用例人效(次/天)"`
}

func ExportGroupResource(year, season, month, week, department, group, user string) (error, []interface{}, [][]interface{}) {
	xt := reflect.TypeOf(&GroupResourceResponse{})
	// xv := reflect.ValueOf(s)

	rows := [][]interface{}{}
	headers := []interface{}{}
	for i := 0; i < xt.Elem().NumField(); i++ {
		head, ok := xt.Elem().Field(i).Tag.Lookup("xlsx")
		if ok {
			headers = append(headers, head)
		}
	}
	rows = append(rows, headers)
	items := []*GroupResourceResponse{}

	where := easygorm.GetEasyGormDb()
	if year != "" && strings.ToLower(year) != "all" {
		where = where.Where("year = ?", year)
	}
	if season != "" && strings.ToLower(season) != "all" {
		where = where.Where("season in (?) ", strings.Split(season, ","))
	}
	if month != "" && strings.ToLower(month) != "all" {
		where = where.Where("month in (?) ", strings.Split(month, ","))
	}
	if week != "" && strings.ToLower(week) != "all" {
		where = where.Where("week in (?) ", strings.Split(week, ","))
	}

	// if project != "" {
	// 	switch project {
	// 	case "hard":
	// 		db = db.Where("work_class_name = '硬件项目'")
	// 	case "soft":
	// 		db = db.Where("work_class_name = '软件项目'")
	// 	default:
	// 		db = db.Where("work_class_name != '软件项目' AND work_class_name != '硬件项目' AND work_class_name != '请假'")
	// 	}
	// }
	if department != "" {
		where = where.Where("department_name = ?", department)
	}
	if group != "" {
		where = where.Where("group_name in (?)", strings.Split(group, ","))
	}
	if user != "" {
		where = where.Where("user_name = ?", user)
	}

	workDayDB := easygorm.GetEasyGormDb().Table(`resource_cleans`).
		Select("count(DISTINCT(report_date)) work_days").
		Where("report_date IN (SELECT DATE FROM workdays WHERE is_work_day = 1)").Where(where)
	/*
	   SELECT
	   res.user_name `工程师`,
	   avg(wd.work_days) `应投入资源`,
	   Round(SUM(IF(res.work_class_name = '硬件项目', total_time, 0))/8, 2) AS `硬件项目总资源(天)`,
	   Round(SUM(IF(res.work_class_name = '软件项目', total_time, 0))/8, 2) AS `软件项目总资源(天)`,
	   Round(SUM(IF(res.work_class_name != '软件项目' AND work_class_name != '硬件项目' AND res.work_class_name != '请假', total_time, 0))/8, 2) AS `其他总资源(天)`,
	   Round(SUM(IF(res.work_class_name = '请假', total_time, 0))/8, 2) AS `请假(天)`,
	   Round(SUM(add_time)/8, 2) AS `加班(天)`,
	   round(SUM(code_add)/1000, 3) `新增代码量(KLOC)`,
	   round(SUM(code_del)/1000, 3) `删除代码量(KLOC)`,
	   SUM(bug_submitted_count) `提交bug数`,
	   Round(SUM(bug_submitted_count)/(SUM(IF(res.work_class_name = '软件项目', total_time, 0))/8), 2) `提BUG人效(个/天)`,
	   sum(bug_resolved_count) `解决bug数`,
	   sum(bug_closed_count) `关闭bug数`,
	   sum(case_run_count) `测试用例执行次数`,
	   Round(sum(case_run_count)/(SUM(IF(res.work_class_name = '软件项目', total_time, 0))/8), 2) `测试用例人效(次/天)`
	   FROM resource_cleans res

	   left join
	   (
	   SELECT count(DISTINCT(report_date)) work_days
	   FROM
	   resource_cleans
	   WHERE report_date IN (SELECT DATE FROM workdays WHERE is_work_day = 1) and year = '$year' and season in ($season) and month in ($month) and week in ($week)
	   ) wd on 1 =1
	   WHERE year = '$year'  and season in ($season) and month in ($month) and week in ($week) and department_name  = '$department' and group_name in ($group)
	   GROUP BY res.user_name
	*/
	selects := []string{
		"res.user_name user_name",
		"avg(wd.work_days) work_days",
		"Round(SUM(IF(res.work_class_name = '硬件项目', total_time, 0))/8, 2) hard_total_time",
		"Round(SUM(IF(res.work_class_name = '软件项目', total_time, 0))/8, 2) soft_total_time",
		"Round(SUM(IF(res.work_class_name != '软件项目' AND work_class_name != '硬件项目' AND res.work_class_name != '请假', total_time, 0))/8, 2) other_total_time",
		"Round(SUM(IF(res.work_class_name = '请假', total_time, 0))/8, 2) break_total_time",
		"Round(SUM(add_time)/8, 2) add_time",
		"round(SUM(code_add)/1000, 3) code_add",
		"round(SUM(code_del)/1000, 3) code_del",
		"SUM(bug_submitted_count) bug_submitted_count",
		"Round(SUM(bug_submitted_count)/(SUM(IF(res.work_class_name = '软件项目', total_time, 0))/8), 2) bug_submitted_rate",
		"sum(bug_resolved_count) bug_resolved_count",
		"sum(bug_closed_count) bug_closed_count",
		"sum(case_run_count) case_run_count",
		"Round(sum(case_run_count)/(SUM(IF(res.work_class_name = '软件项目', total_time, 0))/8), 2) case_run_rate",
	}

	db := easygorm.GetEasyGormDb().Table(`resource_cleans as res`).Select(selects).
		Joins("left join (?) wd on 1 = 1", workDayDB).
		Group("res.user_name")

	err := db.Where(where).Find(&items).Error
	if err != nil {
		return err, nil, nil
	}
	for _, e := range items {
		cells := []interface{}{}
		xv := reflect.ValueOf(e)
		for i := 0; i < xv.Elem().NumField(); i++ {
			_, ok := xt.Elem().Field(i).Tag.Lookup("xlsx")
			if ok {
				cells = append(cells, xv.Elem().Field(i).Interface())
			}

		}
		rows = append(rows, cells)
	}
	return nil, headers, rows
}

type OriginalResource struct {
	ProjectName    string  `json:"project_name"`
	DepartmentName string  `json:"department_name"`
	GroupName      string  `json:"group_name"`
	UserName       string  `json:"user_name"`
	Week           string  `json:"week"`
	WorkTime       float32 `json:"work_time"`
}

type MonthResource struct {
	ID             uint    `json:"id"`
	ProjectName    string  `json:"project_name"`
	DepartmentName string  `json:"department_name"`
	GroupName      string  `json:"group_name"`
	UserName       string  `json:"user_name"`
	WorkClassName  string  `json:"work_class_name"`
	StageName      string  `json:"stage_name"`
	ActivityName   string  `json:"activity_name"`
	TaskName       string  `json:"task_name"`
	Year           uint    `json:"year"`
	Month          uint    `json:"month"`
	WorkTime       float32 `json:"work_time"`
	AddTime        float32 `json:"add_time"`
	TotalTime      float32 `json:"total_time"`
	ReportDate     string  `gorm:"not null; type:varchar(60)" json:"report_date" update:"1"`
	WorkpacketName string  `gorm:"not null; type:varchar(200)" json:"workpacket_name" update:"1"`
}

func GetOriginalResourceData() ([]*OriginalResource, error) {
	items := []*OriginalResource{}
	sql := `
	SELECT
	if(project_name != '',
	project_name,
	'其他') as project_name,
	department_name,
	group_name,
	user_name,
	WEEK as 'week',
	round(SUM(work_time), 1) as work_time
FROM
	resource_cleans
GROUP BY
	project_name,
	department_name,
	group_name,
	user_name,
	week
ORDER BY
	week DESC
	`
	err := easygorm.GetEasyGormDb().Table("resource_cleans").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func GetLastWeekOriginalResourceData() ([]*OriginalResource, error) {
	items := []*OriginalResource{}
	sql := `
SELECT
    IF(project_name != '', project_name, '其他') AS project_name,
    department_name,
    group_name,
    user_name,
    WEEK as 'week',
    ROUND(SUM(work_time), 1)  as work_time
FROM
    resource_cleans
WHERE
    YEARWEEK(report_date, 1) = YEARWEEK(CURDATE() - INTERVAL 1 WEEK, 1)
GROUP BY
    project_name,
    department_name,
    group_name,
    user_name,
    week
ORDER BY
    week DESC;
	`
	err := easygorm.GetEasyGormDb().Table("resource_cleans").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func GetAllMonthResourceData() ([]*MonthResource, error) {
	items := []*MonthResource{}
	sql := fmt.Sprintf(`
SELECT
    IF(project_name != '', project_name, '其他') AS project_name,
    department_name,
    group_name,
    user_name,
    year as 'year',
    month as 'month',
    ROUND(SUM(work_time), 1)  as work_time,
    ROUND(SUM(add_time), 1)  as add_time,
    ROUND(SUM(total_time), 1)  as total_time
FROM
    resource_cleans
Where year = %d
GROUP BY
    project_name,
    department_name,
    group_name,
    user_name,
    year,
    month
ORDER BY
    year DESC,
    month desc;
	`, time.Now().Year())
	err := easygorm.GetEasyGormDb().Table("resource_cleans").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func GetAllMonthResourceDetailData() ([]*MonthResource, error) {
	items := []*MonthResource{}
	sql := fmt.Sprintf(`
		SELECT
		IF(project_name != '', project_name, '其他') AS project_name,
		department_name,
		group_name,
		user_name,
		work_class_name,
		stage_name,
		activity_name,
		task_name,
		year as 'year',
		month as 'month',
    ROUND(SUM(work_time), 1)  as work_time,
    ROUND(SUM(add_time), 1)  as add_time,
    ROUND(SUM(total_time), 1)  as total_time
	FROM
		resources
	Where year = %d
	GROUP BY
		project_name,
		department_name,
		group_name,
		user_name,
		work_class_name,
		stage_name,
		activity_name,
		task_name,
		year,
		MONTH
	ORDER BY
		year DESC,
		month desc;
		`, time.Now().Year())
	err := easygorm.GetEasyGormDb().Table("resources").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func GetAllOriginalResourceData() ([]*OriginalResource, error) {
	items := []*OriginalResource{}
	sql := `
SELECT
    IF(project_name != '', project_name, '其他') AS project_name,
    department_name,
    group_name,
    user_name,
    WEEK as 'week',
    ROUND(SUM(work_time), 1)  as work_time
FROM
    resource_cleans
GROUP BY
    project_name,
    department_name,
    group_name,
    user_name,
    week
ORDER BY
    week DESC;
	`
	err := easygorm.GetEasyGormDb().Table("resource_cleans").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func GetAllMonthResourceDetailDataWithWorkpacket(projectNames []string) ([]*MonthResource, error) {
	items := []*MonthResource{}
	db := easygorm.GetEasyGormDb().Raw(`
		SELECT
		resource_id id,
		IF(project_name != '', project_name, '其他') AS project_name,
		department_name,
		group_name,
		user_name,
		work_class_name,
		stage_name,
		activity_name,
		task_name,
		workpacket_name,
		report_date,
		year as 'year',
		month as 'month',
    work_time,
    add_time,
    total_time
	FROM
		resources
	Where project_name in (?)
	ORDER BY
		id DESC;
		`, projectNames)
	err := db.Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func GetAllMonthResourceDetailDataWithWorkpacketInMonth(year int, monthes []int) ([]*MonthResource, error) {
	items := []*MonthResource{}
	sql := fmt.Sprintf(`
		SELECT
		resource_id id,
		IF(project_name != '', project_name, '其他') AS project_name,
		department_name,
		group_name,
		user_name,
		work_class_name,
		stage_name,
		activity_name,
		task_name,
		workpacket_name,
		report_date,
		year as 'year',
		month as 'month',
    work_time,
    add_time,
    total_time
	FROM
		resources
	Where year = ? and month in (?)
	ORDER BY
		id DESC;
		`)
	err := easygorm.GetEasyGormDb().Raw(sql, year, monthes).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}
