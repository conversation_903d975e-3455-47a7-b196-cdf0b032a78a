package dbuildfarmjob

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/buildfarm"
	"path/filepath"
	"strings"
)

const ModelName = "编译农场编译信息表"

type BuildfarmProductCpu struct {
	Product string `json:"product"`
	Cpu     string `json:"cpu"`
}

type User struct {
	ID       uint   `json:"id"`
	Name     string `json:"name"`
	Username string `json:"username"`
}

type BuildfarmJob struct {
	buildfarm.BuildfarmJob
	User *User               `json:"user"`
	Cpu  BuildfarmProductCpu `gorm:"->;foreignKey:Product;references:Product" json:"cpu"`
}

type ListResponse struct {
	BuildfarmJob
}

type Request struct {
	Source    uint   `json:"source" form:"source"`
	ProjectID uint   `json:"project_id" form:"project_id"`
	Project   string `json:"project" form:"project"`
	Repo      string `json:"repo" form:"repo"`
	Branch    string `json:"branch" form:"branch"`
	TaskType  uint   `json:"task_type" form:"task_type"`
	Product   string `json:"product" form:"product"`
	Defconfig string `json:"defconfig" form:"defconfig"`
	Target    string `json:"target" form:"target"`

	BuildType string `json:"build_type" form:"build_type"` // 编译类型： debug, performance, factory
}

func (this *BuildfarmJob) ModelName() string {
	return ModelName
}

func Model() *buildfarm.BuildfarmJob {
	return &buildfarm.BuildfarmJob{}
}

func (this *BuildfarmJob) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *BuildfarmJob) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *BuildfarmJob) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *BuildfarmJob) CreateV2(object interface{}) error {
	return nil
}

func (this *BuildfarmJob) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmJob) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmJob) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmJob) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmJob) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Where("bug_id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmJob) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmJob) FindSameJob(repo, branch, product, defconfig, target string, userID uint) error {
	err := easygorm.
		GetEasyGormDb().
		Model(Model()).
		Preload("User").
		Where(
			"repo = ? and branch = ? and product = ? and defconfig = ? and target = ? and user_id = ? and (status = 3 or status = 0)",
			repo,
			branch,
			product,
			defconfig,
			target,
			userID,
		).Find(this).Error
	return err
}

func FindQueueJobs() ([]*BuildfarmJob, error) {
	jobs := []*BuildfarmJob{}
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Where("status = 3").Order("id asc").Find(&jobs).Error
	return jobs, err
}

func (job *BuildfarmJob) GetCompileDir() string {
	compileDir := filepath.Join(libs.Config.Buildfarm.Compilepath, job.JobID)
	return compileDir
}

func (job *BuildfarmJob) GetArchiveDir() string {
	archiveDir := filepath.Join(libs.Config.Buildfarm.Archivepath, job.JobID)
	return archiveDir
}

func All(userID, source, taskType, buildType, repo, branch, product, defconfig, target, start, end, status, archive, softnum, softversion, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("Cpu").Preload("User")
	if len(userID) > 0 && userID != "0" {
		db = db.Where("user_id = ?", userID)
	}

	if len(source) > 0 {
		db = db.Where("source = ?", source)
	}

	if len(taskType) > 0 {
		db = db.Where("task_type = ?", taskType)
		// db = db.Where("target = ?", target)
	}

	if len(buildType) > 0 {
		db = db.Where("build_type = ?", buildType)
		// db = db.Where("target = ?", target)
	}

	if len(repo) > 0 {
		db = db.Where("repo like ?", fmt.Sprintf("%%%s%%", repo))
	}

	if len(branch) > 0 {
		db = db.Where("branch = ?", branch)
	}

	if len(start) > 0 {
		db = db.Where("created_at >= ?", start+" 00:00:00.000")
	}

	if len(end) > 0 {
		db = db.Where("created_at <= ?", end+" 23:59:59.999")
	}

	if len(status) > 0 {
		db = db.Where("status = ?", status)
	}

	if len(product) > 0 {
		db = db.Where("product in ?", strings.Split(product, ","))
		// db = db.Where("status = ?", status)
	}

	if len(defconfig) > 0 {
		db = db.Where(fmt.Sprintf("defconfig like '%%%s%%'", defconfig))
		// db = db.Where("defconfig = ?", defconfig)
	}

	if len(target) > 0 {
		db = db.Where(fmt.Sprintf("target like '%%%s%%'", target))
		// db = db.Where("target = ?", target)
	}

	if len(softnum) > 0 {
		db = db.Where("software_number = ?", softnum)
		// db = db.Where("target = ?", target)
	}

	if len(softversion) > 0 {
		db = db.Where(fmt.Sprintf("software_version like '%%%s%%'", softversion))
		// db = db.Where("target = ?", target)
	}

	if len(archive) > 0 {
		db = db.Where("job_id in (?)", easygorm.GetEasyGormDb().Table("release_releases").Select(`SUBSTRING_INDEX(SUBSTRING_INDEX(build_farm_link, "/", 5), "/", -1) as job_id`))
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func DeleMakeJobByJobId(jobID string) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), "job_id = ? and status != 1", jobID).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}
