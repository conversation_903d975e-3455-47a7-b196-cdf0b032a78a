package opensource

import (
	"strconv"

	"github.com/kataras/iris/v12"

	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/opensource/dcomponentpermission"
	transaction "irisAdminApi/service/transaction/opensource"
)

type AddPermissionReq struct {
	UserId         uint `json:"user_id"`
	PermissionType uint `json:"type"`
}

func isAllowedProcessPermission(ctx iris.Context, componentId, authUserId uint, isAdmin bool) bool {
	if !isAdmin {
		// 对于普通用户, 只有与组件相关联才允许操作
		existPermissions, err := dcomponentpermission.ListByComponentIdAndUserId(componentId, authUserId)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return false
		}
		if len(existPermissions) != 0 {
			ctx.JSON(response.NewResponse(response.AuthActionErr.Code, nil, "你未拥有当前操作权限，请联系管理员"))
			return false
		}
	}
	return true
}

func AddPermission(ctx iris.Context) {
	componentId, _ := ctx.Params().GetUint("component_id")

	// 校验组件是否存在
	res := getComponent(ctx, componentId)
	if res == nil {
		return
	}

	req := &AddPermissionReq{}
	if err := ctx.ReadJSON(req); err != nil {
		logging.ErrorLogger.Errorf("create component read json err %s", err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	existPermission, err := dcomponentpermission.FindByComponentIdAndUserIdAndType(componentId, req.UserId, req.PermissionType)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if existPermission != nil {
		ctx.JSON(response.NewResponse(response.DuplicateErr.Code, nil, "已经存在, 不要重复添加"))
		return
	}

	componentPermissionRes := &dcomponentpermission.Response{}
	componentPermissionRes.Type = req.PermissionType
	componentPermissionRes.UserID = req.UserId
	componentPermissionRes.ComponentID = componentId

	err = transaction.AddComponentPermission(componentPermissionRes)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, componentPermissionRes, response.NoErr.Msg))
	return
}

func DeletePermission(ctx iris.Context) {
	componentId, _ := ctx.Params().GetUint("component_id")
	// 判断是否有权操作;
	authUserId, _ := dao.GetAuthId(ctx)
	isAdmin := isAdminUser(authUserId)
	if !isAllowedProcessPermission(ctx, componentId, authUserId, isAdmin) {
		return
	}
	// 校验组件是否存在
	res := getComponent(ctx, componentId)
	if res == nil {
		return
	}

	id, _ := ctx.Params().GetUint("id")
	existPermission, err := dcomponentpermission.FindById(id)
	if err != nil {
		logging.ErrorLogger.Errorf("get opensource component permission get err %s", err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	} else if existPermission == nil {
		// 不存在
		ctx.JSON(response.NewResponse(response.DataEmptyErr.Code, nil, "不存在"))
		return
	} else if !isAdmin && existPermission.UserID == authUserId {
		// 不允许删除当前用户自己
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "不允许删除自己"))
		return
	}

	err = transaction.DeleteComponentPermission(id, componentId, existPermission.UserID, existPermission.Type)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func ListPermissions(ctx iris.Context) {
	componentId, _ := ctx.Params().GetUint("component_id")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	permissionType, _ := strconv.ParseUint(ctx.FormValue("type"), 10, strconv.IntSize)
	sort := ctx.FormValue("sort")
	orderBy := ctx.FormValue("orderBy")
	createdAt := ctx.FormValue("created_at")
	updatedAt := ctx.FormValue("updated_at")

	// 校验组件是否存在
	res := getComponent(ctx, componentId)
	if res == nil {
		return
	}

	list, err := dcomponentpermission.ListPermissions(page, pageSize, componentId, uint(permissionType), sort, orderBy, createdAt, updatedAt)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}
