package buildfarm

import (
	"archive/zip"
	"bytes"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/buildfarm"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/buildfarm/dbaseline"
	"irisAdminApi/service/dao/buildfarm/dbuildfarmproductcpu"
	"irisAdminApi/service/dao/buildfarm/dcroncoverityschedule"
	"irisAdminApi/service/dao/buildfarm/dcronmakejob"
	"irisAdminApi/service/dao/buildfarm/dcrontab"
	"irisAdminApi/service/dao/buildfarm/dproject"
	"irisAdminApi/service/dao/buildfarm/dserver"
	"irisAdminApi/service/dao/mergerequest/dmergerequest"
	"irisAdminApi/service/dao/release/dreleaseprojectconfig"

	"github.com/kataras/iris/v12"
	"github.com/pkg/errors"
	"github.com/robfig/cron/v3"
	"github.com/tidwall/gjson"
	"golang.org/x/crypto/ssh"
)

var (
	CronTabLock sync.Mutex
	CronPaser   = cron.NewParser(cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow)
)

// var CronRuningMap = map[uint]*ssh.Client{}
var CronRuningMap sync.Map

func AddNewCronTab() error {
	// 清空计划任务
	CronTabLock.Lock()
	defer CronTabLock.Unlock()

	logging.DebugLogger.Debugf("清空计划任务")
	for _, entry := range libs.Cron.Entries() {
		libs.Cron.Remove(entry.ID)
	}

	logging.DebugLogger.Debugf("添加计划任务")
	crontabs, err := dcrontab.FindAllEnableCrontabs()
	if err != nil {
		logging.ErrorLogger.Errorf("find all cron err ", err)
	}
	for _, crontab := range crontabs {
		if crontab.Status == 1 {
			c := crontab
			_, err := libs.Cron.AddFunc(crontab.Cron, func() { CreateCronMakeJob(c, 0) })
			if err != nil {
				logging.ErrorLogger.Errorf("add cron err ", err, c)
			}
		}
	}
	// 增加coverity计划任务
	covCrontabs, err := dcroncoverityschedule.FindCronCoverityWindow(0, false)
	if err != nil {
		logging.ErrorLogger.Errorf("find all cov cron err ", err)
	}
	for _, crontab := range covCrontabs {
		if crontab.CronCoveritySchedule != nil && crontab.CronCoveritySchedule.ID != 0 {
			schedule := crontab.CronCoveritySchedule
			releaseProjectConfig, err := dreleaseprojectconfig.FindConfigByName(schedule.Project)
			if err != nil {
				logging.ErrorLogger.Errorf("add cron err ", err)
				continue
			}
			if releaseProjectConfig.ID == 0 {
				logging.ErrorLogger.Errorf("未找到%s项目配置", schedule.Project)
				continue
			}

			branch := releaseProjectConfig.BuildProjectBranch
			if branch == nil || *branch == "" {
				logging.ErrorLogger.Errorf("%s项目未配置编译工程", schedule.Project)
				continue
			}

			params := &CovBuildParams{
				Project:     schedule.Project,
				Branch:      *branch,
				Product:     schedule.Product,
				BaseProject: releaseProjectConfig.ReleaseProject.BaseProject.Name,
				BaseProduct: schedule.BaseProduct,
			}

			cron := fmt.Sprintf("00 %d * * %d", crontab.Time, crontab.WeekDay)
			_, err = libs.Cron.AddFunc(cron, func() { CovBuildRequest(params) })
			if err != nil {
				logging.ErrorLogger.Errorf("add cron err ", err)
			}
		}
	}

	// 恢复运行中任务
	return nil
}

func RestoreRunningJob() {
	runningJobs, err := dcronmakejob.FindRuningJobs()
	if err != nil {
		logging.ErrorLogger.Errorf("get all running job get err ", err)
		return
	}

	for _, makejob := range runningJobs {
		go StartCronJob(makejob, true)
	}
}

func StartCronJob(cronMakeJob *dcronmakejob.CronMakeJob, restart bool) {
	client, err := SSHClient(cronMakeJob.ServerId)
	// CronRuningMap[cronMakeJob.Id] = client
	CronRuningMap.Store(cronMakeJob.Id, client)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		updateCronMakeJob(cronMakeJob, 2, "", false, false)
		return
	}

	if !restart {
		if err := CronGitJob(client, cronMakeJob); err != nil {
			return
		}
	}

	if cronMakeJob.TaskType == 1 {
		CronMakeProductJob(client, cronMakeJob, restart)
	}

	if cronMakeJob.TaskType == 2 {
		CronMakeJob(client, cronMakeJob, restart)
	}
	// defer client.Close()
}

func CreateCronMakeJob(crontab *dcrontab.CronTab, serverID uint) error {
	mutex.Lock()
	defer mutex.Unlock()
	result, err := dcronmakejob.FindAllCronMakeJobs(crontab.Repo, crontab.Branch, "", "", "0", "", crontab.Product, "", "", crontab.BuildType, "", "", "", "", 1, -1)
	if err != nil {
		logging.ErrorLogger.Errorf("find running cron make job get err ", err)
		return err
	}
	runningJobs := result["items"].([]*dcronmakejob.ListResponse)
	if len(runningJobs) > 0 {
		// 停止所有已经启动相同每日编译
		for _, runningJob := range runningJobs {
			// if time.Now().Second()-runningJob.CreatedAt.Second() <= 1800 {
			// 	logging.ErrorLogger.Errorf("半小时内已经启动相同编译")
			// 	return errors.New("半小时内已经启动相同编译，跳过")
			// }
			err := StopCronJob(runningJob)
			if err != nil {
				return errors.Wrap(err, "清理作业失败")
			}
		}
	}

	for _, product := range strings.Split(crontab.Product, ",") {
		if serverID == 0 {
			serverID = ChooseServerV3("2", true)
			if serverID == 0 {
				logging.ErrorLogger.Errorf("没有可用的服务器")
				return errors.New("没有可用的服务器")
			}
		}
		parentPath := libs.Config.Buildfarm.Compilepath

		tempName := libs.GetUUID()

		tempDir := filepath.Join(parentPath, tempName)

		cronMakeJob := &dcronmakejob.CronMakeJob{}
		err = cronMakeJob.Create(map[string]interface{}{
			"JobId":     tempName,
			"ServerId":  serverID,
			"CreatedAt": time.Now(),
			"Project":   crontab.Project,
			"Repo":      crontab.Repo,
			"Branch":    crontab.Branch,
			"TaskType":  crontab.TaskType,
			"Product":   product,
			"Defconfig": crontab.Defconfig,
			"Target":    crontab.Target,
			"BuildType": crontab.BuildType,
			"Dir":       tempDir,
			"Status":    0,
			"Version":   "",
			"CronTabID": crontab.ID,
		})
		if err != nil {
			logging.ErrorLogger.Errorf("create cron make job get err ", err)
			return err
		}

		err = easygorm.GetEasyGormDb().Model(&buildfarm.CronMakeJob{}).Where("job_id = ?", tempName).Find(&cronMakeJob).Error
		if err != nil {
			logging.ErrorLogger.Errorf("create job get err ", err)
			return err
		}

		go StartCronJob(cronMakeJob, false)
		serverID = 0
	}

	return nil
}

func CronGitJob(client *ssh.Client, cronMakeJob *dcronmakejob.CronMakeJob) error {
	f, err := os.OpenFile(filepath.Join(libs.Config.Buildfarm.Logpath, cronMakeJob.JobId+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
	if err != nil {
		logging.ErrorLogger.Errorf("create temp dir err ", err)
		updateCronMakeJob(cronMakeJob, 2, "", false, false)
		return err
	}
	defer f.Close()
	command := fmt.Sprintf(`echo 当前编译服务器： $(hostname)`)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", command, err.Error())
		updateCronMakeJob(cronMakeJob, 2, "", false, false)
		return err
	}
	// repo_url := strings.Replace(cronMakeJob.Repo, "http://", fmt.Sprintf("http://oauth2:%s@", token), -1)
	repo_url := cronMakeJob.Repo
	command = fmt.Sprintf("git clone -b %s %s %s -v && cd %s", cronMakeJob.Branch, repo_url, cronMakeJob.Dir, cronMakeJob.Dir)
	f.WriteString("执行拉取项目操作，并切换分支\r\n")

	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("拉取项目失败", err)
		updateCronMakeJob(cronMakeJob, 2, "", false, false)
		return err
	}

	command = fmt.Sprintf("cd %s && git rev-parse HEAD", cronMakeJob.Dir)
	var stdOut bytes.Buffer
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("获取版本号失败", err)
		updateCronMakeJob(cronMakeJob, 2, "", false, false)
		return err
	}

	version := strings.Replace(stdOut.String(), "\n", "", -1)
	if len(version) == 40 {
		f.WriteString(fmt.Sprintf("当前版本为：%s\r\n", version))
	} else {
		updateCronMakeJob(cronMakeJob, 2, "", false, false)
		logging.ErrorLogger.Errorf("获取版本号异常", cronMakeJob.JobId)
		f.WriteString(fmt.Sprintf("获取版本号异常：%s\r\n", version))
		return errors.New(fmt.Sprintf("获取版本号异常 %s", cronMakeJob.JobId))
	}

	f.WriteString(fmt.Sprintf("拉取项目任务完成\r\n"))
	return nil
}

func CheckCronTab(ctx iris.Context) {
	max, err := GetTotalParallel("2")
	if err != nil {
		logging.ErrorLogger.Errorf("find all cron err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	CrontabReq := &dcrontab.CrontabReq{}
	if err := ctx.ReadJSON(CrontabReq); err != nil {
		logging.ErrorLogger.Errorf("create gitjob read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*CrontabReq)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		logging.ErrorLogger.Errorf("create gitjob read json err ", strings.Join(errs, ";"))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	crontabs, err := dcrontab.FindAllEnableCrontabs()
	if err != nil {
		logging.ErrorLogger.Errorf("find all cron err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	// 预处理，取本任务和所有其它任务各未来N次执行时间（每次带前后60min区间）
	N := 10
	// 初始统计
	total := 0
	add := 0
	if CrontabReq.Status == 1 {
		add = len(strings.Split(CrontabReq.Product, ","))
	}

	uniqueWindows := map[string]int{}

	// 1. 获取本任务的未来N次时间窗口（±60分钟）
	ownWindows := make([][2]time.Time, 0, N)
	sch, err := CronPaser.Parse(CrontabReq.Cron)
	if err != nil {
		logging.ErrorLogger.Errorf("cron not valid ", err)
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "计划任务时间格式错误"))
		return
	}
	now := time.Now()
	schNext := sch.Next(now)
	next := []string{schNext.Format("2006-01-02 15:04:05")}

	for i := 0; i < N; i++ {
		winStart := schNext.Add(-90 * time.Minute)
		winEnd := schNext.Add(90 * time.Minute)
		ownWindows = append(ownWindows, [2]time.Time{winStart, winEnd})
		schNext = sch.Next(schNext)
		if len(next) < 5 {
			next = append(next, schNext.Format("2006-01-02 15:04:05"))
		}
	}

	// 2. 对每一条crontab，取其N次，将每次时间与上述窗口交集、去重
	for _, cron := range crontabs {
		if cron.ID == CrontabReq.ID {
			continue
		}
		entry, err := CronPaser.Parse(cron.Cron)
		if err != nil {
			continue
		}
		_sch := entry.Next(now)
		for i := 0; i < N; i++ {
			// 是否在窗口内
			for _, win := range ownWindows {
				windowTag := fmt.Sprintf("%s~%s", win[0].Format("2006-01-02 15:04:05"), win[1].Format("2006-01-02 15:04:05"))
				if _sch.After(win[0]) && _sch.Before(win[1]) {
					uniqueWindows[windowTag] += len(strings.Split(cron.Product, ","))
				}
			}
			_sch = entry.Next(_sch)
		}
	}
	window := ""
	for k, v := range uniqueWindows {
		if total == 0 || v > total {
			total = v
			window = k
		}
	}
	total += add
	ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]interface{}{"add": add, "total": total, "max": max, "next": next, "window": window, "info": uniqueWindows}, response.NoErr.Msg))
	return
}

func CreateCronTab(ctx iris.Context) {
	userID, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.AuthErr.Code, nil, response.AuthErr.Msg))
		return
	}

	CrontabReq := &dcrontab.CrontabReq{}
	if err := ctx.ReadJSON(CrontabReq); err != nil {
		logging.ErrorLogger.Errorf("create gitjob read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*CrontabReq)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		logging.ErrorLogger.Errorf("create gitjob read json err ", strings.Join(errs, ";"))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	project := dproject.Response{}
	if err := project.FindEx("name", CrontabReq.Project); err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if project.Id == 0 {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "未找到该项目"))
		return
	}

	if project.Enable == 0 {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "此项目不支持编译"))
		return
	}

	data := map[string]interface{}{
		"ProjectID": project.Id,
		"Project":   project.Name,
		"Repo":      project.Repo,
		"Branch":    CrontabReq.Branch,
		"TaskType":  project.TaskType,
		"Product":   CrontabReq.Product,
		"Defconfig": CrontabReq.Defconfig,
		"Target":    CrontabReq.Target,
		"Status":    CrontabReq.Status,
		"BuildType": CrontabReq.BuildType,
		"Cron":      CrontabReq.Cron,
		"CreatedAt": time.Now(),
		"UserID":    userID,
	}

	_, err = CronPaser.Parse(CrontabReq.Cron)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "计划任务时间格式错误"))
		return
	}

	err = dao.Create(&dcrontab.CronTab{}, ctx, data)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	// //更新计划任务

	AddNewCronTab()

	ctx.JSON(response.NewResponse(response.NoErr.Code, CrontabReq, response.NoErr.Msg))
	return
}

func updateCronMakeJob(cronMakeJob *dcronmakejob.CronMakeJob, status int, version string, updateStartedAt, updateFinishedAt bool) {
	// cronMakeJobID := cronMakeJob.Id
	// cronMakeJob = &dcronmakejob.CronMakeJob{}
	// if err := cronMakeJob.Find(cronMakeJobID); err != nil {
	// 	logging.ErrorLogger.Errorf("find cron make job to update err ", err)
	// 	return
	// }
	if _, ok := CronRuningMap.Load(cronMakeJob.Id); ok {
		data := map[string]interface{}{
			"Status":    status,
			"UpdatedAt": time.Now(),
			"Version":   version,
		}

		if updateStartedAt {
			data["StartedAt"] = time.Now()
		}

		if updateFinishedAt {
			data["FinishedAt"] = time.Now()
		}

		if err := cronMakeJob.Update(cronMakeJob.Id, data); err != nil {
			logging.ErrorLogger.Errorf("update cron make job err ", err)
			return
		}

		if status == 2 {
			CronPushMailQueue(cronMakeJob, status)
		}
	}
}

func CronMakeJob(client *ssh.Client, cronMakeJob *dcronmakejob.CronMakeJob, restart bool) {
	defer client.Close()
	version := ""
	if !restart {
		updateCronMakeJob(cronMakeJob, 0, version, true, false)
	}

	f, err := os.OpenFile(filepath.Join(libs.Config.Buildfarm.Logpath, cronMakeJob.JobId+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
	if err != nil {
		logging.ErrorLogger.Errorf("create log err ", err)
		return
	}
	defer f.Close()
	command := fmt.Sprintf(`echo 当前编译服务器： $(hostname)`)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", command, err.Error())
		updateCronMakeJob(cronMakeJob, 2, "", false, false)
		return
	}

	command = fmt.Sprintf("cd %s && git rev-parse HEAD", cronMakeJob.Dir)
	var stdOut bytes.Buffer
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("获取版本号失败", err)
		updateCronMakeJob(cronMakeJob, 2, version, false, false)
		return
	}

	getVersion := strings.Replace(stdOut.String(), "\n", "", -1)
	if len(getVersion) == 40 {
		version = getVersion
	} else {
		logging.ErrorLogger.Errorf("获取版本号异常", cronMakeJob.Id)
		updateCronMakeJob(cronMakeJob, 2, version, false, false)
		return
	}
	f.WriteString(fmt.Sprintf("当前版本为：%s\r\n", version))

	command = fmt.Sprintf("cd %s && make %s", cronMakeJob.Dir, cronMakeJob.Target)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("make err", err)
		updateCronMakeJob(cronMakeJob, 2, version, false, false)
		return
	}
	// 查找输出目录
	// stdOut = *bytes.NewBuffer([]byte{})
	// command = fmt.Sprintf("cd %s/output/images/ && cd $(ls|head -n 1) && pwd", cronMakeJob.Dir)
	// if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
	// 	logging.ErrorLogger.Errorf("run command error", command, err.Error())
	// 	return
	// }

	// CronArchiveOutput(cronMakeJob)
	ArchiveOutputV2(cronMakeJob.ServerId, cronMakeJob.JobId, fmt.Sprintf("%s/output/images/", cronMakeJob.Dir), cronMakeJob.Repo, cronMakeJob.Branch, cronMakeJob.Target)
	// 如果是baseos,则增加归档到172.28.244.101
	updateCronMakeJob(cronMakeJob, 1, version, false, true)
}

func CronMakeProductJob(client *ssh.Client, cronMakeJob *dcronmakejob.CronMakeJob, restart bool) {
	defer func() {
		CronRuningMap.Delete(cronMakeJob.Id)
		client.Close()
	}()

	f, err := os.OpenFile(filepath.Join(libs.Config.Buildfarm.Logpath, cronMakeJob.JobId+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
	if err != nil {
		logging.ErrorLogger.Errorf("create log err ", err)
		return
	}
	defer f.Close()

	version := ""
	if !restart {
		updateCronMakeJob(cronMakeJob, 0, version, true, false)
	}

	command := fmt.Sprintf("cd %s && git rev-parse HEAD", cronMakeJob.Dir)
	var stdOut bytes.Buffer
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("run command error", command, err.Error())
		updateCronMakeJob(cronMakeJob, 2, version, false, false)
		return
	}

	getVersion := strings.Replace(stdOut.String(), "\n", "", -1)
	if len(getVersion) == 40 {
		version = getVersion
	} else {
		logging.ErrorLogger.Errorf("获取版本号异常", cronMakeJob.Id)
		updateCronMakeJob(cronMakeJob, 2, version, false, false)
		return
	}
	f.WriteString(fmt.Sprintf("当前版本为：%s\r\n", version))

	// 获取KERNEL_VERSION
	command = fmt.Sprintf("cd %s/prj_%s/configs/ && cat %s_defconfig |grep 'BR2_LINUX_KERNEL_VERSION'", cronMakeJob.Dir, cronMakeJob.Product, cronMakeJob.Product)
	stdOut = *bytes.NewBuffer([]byte{})
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("run command error", command, err.Error())
		updateCronMakeJob(cronMakeJob, 2, version, false, false)
		return
	}

	lineArr := strings.Split(strings.ReplaceAll(stdOut.String(), "\n", ""), "=")
	kernelVersion := strings.ReplaceAll(lineArr[len(lineArr)-1], `"`, ``)

	// todo: 判断是否存在缓存，如果存在，则挂载使用
	sourceEnvsetupCommand := "source env-setup"
	if cronMakeJob.UseCache && cronMakeJob.Branch == "Trunk" && cronMakeJob.BuildType != "release" {
		sourceEnvsetupCommand = fmt.Sprintf("source env-setup %s %s %s && rm output/build/*/baseos -rf && rm output/build/*/toolchain* -rf && rm output/build/*/linux-%s -rf", cronMakeJob.BuildType, cronMakeJob.Branch, cronMakeJob.Product, kernelVersion)
	}

	command = fmt.Sprintf("cd %s/prj_%s && %s && make download", cronMakeJob.Dir, cronMakeJob.Product, sourceEnvsetupCommand)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", command, err.Error())
		updateCronMakeJob(cronMakeJob, 2, version, false, false)
		return
	}

	// 性能编译及调试编译动作
	err = BuildTypePrevTask(client, f, cronMakeJob.Dir, cronMakeJob.Product, cronMakeJob.JobId, cronMakeJob.BuildType)
	if err != nil {
		logging.ErrorLogger.Errorf("switch defconfig failed", err.Error())
		updateCronMakeJob(cronMakeJob, 2, version, false, false)
		return
	}

	// 增加更新产品与CPU对应关系，暂定以BR_PACKAGE_BASEOS_URL为准
	command = fmt.Sprintf("cd %s/prj_%s/configs/ && cat %s_defconfig |grep 'BR2_PACKAGE_BASEOS_URL'", cronMakeJob.Dir, cronMakeJob.Product, cronMakeJob.Product)
	stdOut = *bytes.NewBuffer([]byte{})
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("run command error", command, err.Error())
		updateCronMakeJob(cronMakeJob, 2, version, false, false)
		return
	}

	lineArr = strings.Split(strings.ReplaceAll(stdOut.String(), "\n", ""), "/")
	cpu := strings.ReplaceAll(lineArr[len(lineArr)-1], `"`, ``)
	if cpu != cronMakeJob.Cpu.Cpu {
		// 更新产品CPU关系至数据库
		buildfarmProductCpu := dbuildfarmproductcpu.BuildfarmProductCpu{}
		err = buildfarmProductCpu.FindByProductAndCpu(cronMakeJob.Product, cpu)

		if err != nil {
			f.WriteString(fmt.Sprintf("更新产品CPU关系失败：查询数据库错误 %s\r\n", err.Error()))
		} else {
			if buildfarmProductCpu.ID == 0 {
				cronMakeJob.Cpu.Cpu = cpu
				cronMakeJob.Cpu.Product = cronMakeJob.Product

				err = buildfarmProductCpu.Create(map[string]interface{}{
					"Cpu":     cpu,
					"Product": cronMakeJob.Product,
				})
				if err != nil {
					f.WriteString(fmt.Sprintf("更新产品CPU关系失败：创建记录数据库错误 %s\r\n", err.Error()))
				}
				f.WriteString(fmt.Sprintf("更新产品CPU关系成功：%s %s\r\n", cronMakeJob.Product, cpu))
			}
		}
	}

	command = fmt.Sprintf("cd %s/prj_%s && source env-setup && if [ -f ./output/host/%s/relocate-sdk.sh ]; then ./output/host/%s/relocate-sdk.sh; fi", cronMakeJob.Dir, cronMakeJob.Product, cpu, cpu)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", command, err.Error())
		updateCronMakeJob(cronMakeJob, 2, version, false, false)
		return
	}

	command = fmt.Sprintf("cd %s/prj_%s && source env-setup && make %s_defconfig && make && make pack-all", cronMakeJob.Dir, cronMakeJob.Product, cronMakeJob.Product)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", command, err.Error())
		updateCronMakeJob(cronMakeJob, 2, version, false, false)
		if cronMakeJob.Dir != "" {
			command = fmt.Sprintf("df -h |grep %s |awk '{print $NF}' | xargs -i umount {} && rm -rf %s", cronMakeJob.JobId, cronMakeJob.Dir)
			if err := RunCommandOutFile(client, command, f); err != nil {
				logging.ErrorLogger.Errorf("run command error", command, err.Error())
				return
			}
		}
		return
	}

	// 归档 mkinstpkg
	if err := ArchiveMkinstpkg(cronMakeJob.ServerId, cronMakeJob.JobId, fmt.Sprintf("%s/prj_%s", cronMakeJob.Dir, cronMakeJob.Product), cpu); err != nil {
		f.WriteString(fmt.Sprintf("归档mkinstpkg失败：%s\r\n", err.Error()))
	}

	if err := ArchiveOutputV2(cronMakeJob.ServerId, cronMakeJob.JobId, fmt.Sprintf("%s/prj_%s/output/images/", cronMakeJob.Dir, cronMakeJob.Product), cronMakeJob.Repo, cronMakeJob.Branch, cronMakeJob.Target); err == nil {
		updateCronMakeJobSoftwareVersionAndNumber(cronMakeJob)
		UpdateBaseline(cronMakeJob)
		updateCronMakeJob(cronMakeJob, 1, version, false, true)
		BuildTypePostTask(client, f, cronMakeJob.JobId, cronMakeJob.BuildType)
		if cronMakeJob.UseCache && cronMakeJob.Branch == "Trunk" && cronMakeJob.BuildType != "release" {
			command = fmt.Sprintf("cd %s/prj_%s && source env-setup && make prepare-cache", cronMakeJob.Dir, cronMakeJob.Product)
			if err := RunCommandOutFile(client, command, f); err != nil {
				logging.ErrorLogger.Errorf("run command error", command, err.Error())
				command = fmt.Sprintf("df -h |grep %s |awk '{print $NF}' | xargs -i umount {} && rm -rf %s", cronMakeJob.JobId, cronMakeJob.Dir)
				if err := RunCommandOutFile(client, command, f); err != nil {
					logging.ErrorLogger.Errorf("run command error", command, err.Error())
					return
				}
				return
			}

			now := time.Now()
			// 同步编译结果做为缓存'
			tmpName := fmt.Sprintf("%s_%s", version, now.Format("20060102150405"))

			command = fmt.Sprintf("cd %s && ls | grep logs | xargs -i rm {} -rf", filepath.Join(cronMakeJob.Dir, fmt.Sprintf("prj_%s", cronMakeJob.Product), "output", "gcov"))
			if err := RunCommandOutFile(client, command, f); err != nil {
				logging.ErrorLogger.Errorf("run command error", command, err.Error())
			}

			command = fmt.Sprintf("mkdir -p /mnt/sata0/overlay/cache/%s/%s/%s/%s", cronMakeJob.Branch, cronMakeJob.Product, cronMakeJob.BuildType, tmpName)
			if err := RunCommandOutFile(client, command, f); err != nil {
				logging.ErrorLogger.Errorf("run command error", command, err.Error())
			}

			command = fmt.Sprintf("rsync -aH  %s/ /mnt/sata0/overlay/cache/%s/%s/%s/%s/output/", filepath.Join(cronMakeJob.Dir, fmt.Sprintf("prj_%s", cronMakeJob.Product), "output"), cronMakeJob.Branch, cronMakeJob.Product, cronMakeJob.BuildType, tmpName)
			if err := RunCommandOutFile(client, command, f); err != nil {
				logging.ErrorLogger.Errorf("run command error", command, err.Error())
			}

			// command = fmt.Sprintf("rsync -aH  %s/ /mnt/sata0/overlay/cache/%s/%s/%s/%s/dl/", filepath.Join(cronMakeJob.Dir, "dl"), cronMakeJob.Branch, cronMakeJob.Product, cronMakeJob.BuildType, tmpName)
			// if err := RunCommandOutFile(client, command, f); err != nil {
			// 	logging.ErrorLogger.Errorf("run command error", command, err.Error())
			// }

			command = fmt.Sprintf("cd /mnt/sata0/overlay/cache/%s/%s/%s/ && rm next_version -rf && ln -sf %s next_version && mv -Tf next_version %s", cronMakeJob.Branch, cronMakeJob.Product, cronMakeJob.BuildType, tmpName, version)
			if err := RunCommandOutFile(client, command, f); err != nil {
				logging.ErrorLogger.Errorf("run command error", command, err.Error())
			}

			command = fmt.Sprintf("cd /mnt/sata0/overlay/cache/%s/%s/%s/ && ls | grep -vE '%s$|%s$'|xargs -i rm {} -rf", cronMakeJob.Branch, cronMakeJob.Product, cronMakeJob.BuildType, tmpName, version)
			if err := RunCommandOutFile(client, command, f); err != nil {
				logging.ErrorLogger.Errorf("run command error", command, err.Error())
			}
		}
		// 同步给其他服务器
	}

	command = fmt.Sprintf("df -h |grep %s |awk '{print $NF}' | xargs -i umount {} && rm -rf %s", cronMakeJob.JobId, cronMakeJob.Dir)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", command, err.Error())
		return
	}
	// SendTestLink(cronMakeJob)
}

func CronArchiveOutput(cronMakeJob *dcronmakejob.CronMakeJob) error {
	server := &dserver.Response{}
	err := server.Find(cronMakeJob.ServerId)
	if err != nil {
		logging.ErrorLogger.Errorf("create gitjob find server get err ", err)
		return err
	}
	archivePath := filepath.Join(libs.Config.Buildfarm.Archivepath, cronMakeJob.JobId)
	err = os.MkdirAll(archivePath, 0o755)
	os.Chmod(archivePath, 0o755)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("mkdir before scp error: %s", err.Error()))
		return err
	}
	cmd := exec.Command("scp", "-o", "stricthostkeychecking=no", "-r", "-P", strconv.Itoa(int(server.Port)), fmt.Sprintf("%s@%s:/%s/output/images/", server.Username, server.Host, cronMakeJob.Dir), archivePath)
	if cronMakeJob.TaskType == 1 {
		cmd = exec.Command("scp", "-o", "stricthostkeychecking=no", "-r", "-P", strconv.Itoa(int(server.Port)), fmt.Sprintf("%s@%s:/%s/prj_%s/output/images/", server.Username, server.Host, cronMakeJob.Dir, cronMakeJob.Product), archivePath)
	}

	var out bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &stderr
	err = cmd.Run()
	if err != nil {
		logging.ErrorLogger.Errorf("scp get err ", err, stderr.String())
		return err
	}
	return nil
}

func UpdateBaseline(cronMakeJob *dcronmakejob.CronMakeJob) {
	archivePath := filepath.Join(libs.Config.Buildfarm.Archivepath, cronMakeJob.JobId)
	commnad := fmt.Sprintf("find %s -name *.bz2", archivePath)
	cmd := exec.Command("/bin/bash", "-c", commnad)
	var out bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &stderr
	err := cmd.Run()
	if err != nil {
		logging.ErrorLogger.Errorf("scp get err ", err, stderr.String())
		return
	}

	response := dbaseline.Response{}
	err = response.Create(map[string]interface{}{
		"JobId":     cronMakeJob.JobId,
		"CreatedAt": time.Now(),
		"Project":   cronMakeJob.Project,
		"Repo":      cronMakeJob.Repo,
		"Branch":    cronMakeJob.Branch,
		"TaskType":  cronMakeJob.TaskType,
		"BuildType": cronMakeJob.BuildType,
		"Product":   cronMakeJob.Product,
		"Defconfig": cronMakeJob.Defconfig,
		"Target":    cronMakeJob.Target,
		"Dir":       strings.Replace(strings.Replace(out.String(), archivePath, "", -1), "\n", "", -1),
		"Status":    1,
		"Version":   cronMakeJob.Version,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("scp get err ", err, stderr.String())
		return
	}
}

func GetCronMakeJobs(ctx iris.Context) {
	// id, err := dao.GetAuthId(ctx)
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	repo := ctx.FormValue("repo")
	branch := ctx.FormValue("branch")
	// server := ctx.FormValue("server")

	start := ctx.FormValue("start")
	end := ctx.FormValue("end")
	status := ctx.FormValue("status") // 已发布归档，状态为3
	product := ctx.FormValue("product")

	defconfig := ctx.FormValue("defconfig")
	target := ctx.FormValue("target")
	build_type := ctx.FormValue("build_type")
	softnum := ctx.FormValue("softnum")
	softversion := ctx.FormValue("softversion")

	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	archive := ctx.FormValue("archive")

	makejobs, err := dcronmakejob.FindAllCronMakeJobs(repo, branch, start, end, status, archive, product, defconfig, target, build_type, softnum, softversion, sort, orderBy, page, pageSize)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, makejobs, response.NoErr.Msg))
	return
}

func GetCrontabs(ctx iris.Context) {
	project := ctx.FormValue("project")
	branch := ctx.FormValue("branch")
	buildType := ctx.FormValue("build_type")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	list, err := dcrontab.FilterCrontabs(project, branch, buildType, page, pageSize, orderBy, sort)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func DeleteCrontab(ctx iris.Context) {
	err := dao.Delete(&dcrontab.CronTab{}, ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	AddNewCronTab()
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func UpdateCrontab(ctx iris.Context) {
	userID, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.AuthErr.Code, nil, response.AuthErr.Msg))
		return
	}

	CrontabReq := &dcrontab.CrontabReq{}
	if err := ctx.ReadJSON(CrontabReq); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*CrontabReq)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	project := dproject.Response{}
	if err := project.FindEx("name", CrontabReq.Project); err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if project.Id == 0 {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "未找到该项目"))
		return
	}

	if project.Enable == 0 {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "此项目不支持编译"))
		return
	}

	data := map[string]interface{}{
		"ProjectID": project.Id,
		"Project":   project.Name,
		"Repo":      project.Repo,
		"Branch":    CrontabReq.Branch,
		"TaskType":  project.TaskType,
		"Product":   CrontabReq.Product,
		"Defconfig": CrontabReq.Defconfig,
		"Target":    CrontabReq.Target,
		"Status":    CrontabReq.Status,
		"Cron":      CrontabReq.Cron,
		"BuildType": CrontabReq.BuildType,
		"UpdatedAt": time.Now(),
		"UserID":    userID,
	}

	err = dao.Update(&dcrontab.CronTab{}, ctx, data)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	AddNewCronTab()
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

var mailTimeFmt = "2006-01-02 15:04:05"

func CronPushMailQueue(cronMakeJob *dcronmakejob.CronMakeJob, status int) {
	if libs.Config.Mail.Enable {
		// 查找MR表单数据，并推送关联人员

		// rc := cache.GetRedisClusterClient()
		from := "编译农场"
		to := strings.Split(libs.Config.Buildfarm.CronMailReceivers, ",")
		// subject := fmt.Sprintf("[编译农场][每日编译][作业ID:%s][%s]", cronMakeJob.JobId, statusMap[status])
		subject := fmt.Sprintf("[编译农场][每日编译][%s][%s][%s][%s]", cronMakeJob.Branch, cronMakeJob.Product, cronMakeJob.BuildType, statusMap[status])
		logfile := filepath.Join(libs.Config.Buildfarm.Logpath, cronMakeJob.JobId+".log")
		command := fmt.Sprintf(`grep "error:" -A 10 %s|tail -n 20`, logfile)
		log, _ := libs.ExecCommand(command)
		if log == "" {
			command := fmt.Sprintf(`cat %s|tail -n 20`, logfile)
			log, _ = libs.ExecCommand(command)
		}
		body := fmt.Sprintf(
			`<p>作业ID：%s</p>
			<p>编译分支：%s</p>
			<p>编译产品：%s</p>
			<p>编译类型：%s</p>
			<p>编译状态：%s<p>
			<p>启动时间：%s<p>
			<p>最后更新时间：%s<p>
			<p>部分编译日志：%s</p>
			<p>完整编译日志: <a href="http://%s:%d/logs/%s.log">完整编译日志</a><p>`,
			cronMakeJob.JobId,
			cronMakeJob.Branch,
			cronMakeJob.Product,
			cronMakeJob.BuildType,
			statusMap[status],
			cronMakeJob.CreatedAt.Format(mailTimeFmt),
			cronMakeJob.UpdatedAt.Format(mailTimeFmt),
			log,
			libs.Config.Nginx.HOST,
			libs.Config.Nginx.Port,
			cronMakeJob.JobId,
		)

		successCronMakeJobs, err := dcronmakejob.FindLastSuccessCronMake(cronMakeJob.Branch, cronMakeJob.Product, "", cronMakeJob.CreatedAt)
		if err != nil {
			logging.ErrorLogger.Errorf("查找上次编译成功记录失败")
		} else {
			if len(successCronMakeJobs) > 0 && time.Now().Sub(successCronMakeJobs[0].CreatedAt).Hours() < float64(48) {
				lastSuccessCronMakeJob := successCronMakeJobs[0]
				targetBranch := lastSuccessCronMakeJob.Branch
				var mergeRequests []*dmergerequest.ListResponse
				if targetBranch == "Trunk" {
					mergeRequests, err = dmergerequest.FindMergeRequests("", targetBranch, lastSuccessCronMakeJob.CreatedAt, cronMakeJob.CreatedAt)
				} else {
					mergeRequests, err = dmergerequest.FindMergeRequests(targetBranch, "", lastSuccessCronMakeJob.CreatedAt, cronMakeJob.CreatedAt)
				}

				if err != nil {
					logging.ErrorLogger.Errorf("查找上次编译成功记录失败")
				} else {
					if len(mergeRequests) > 0 {
						mrMsg := ""
						for _, mr := range mergeRequests {
							if !libs.InArrayS(to, mr.User.Username+"@ruijie.com.cn") {
								to = append(to, mr.User.Username+"@ruijie.com.cn")
							}
							mrMsg = fmt.Sprintf(`%s<p>  <a href="http://10.51.135.102:8080/%s/-/merge_requests/%d">%s</a> %s:%s -> %s:%s  @%s</p>`, mrMsg, mr.TargetProject, mr.MergeRequestIID, mr.Title, mr.SourceProject, mr.SourceBranch, mr.TargetProject, mr.TargetBranch, mr.User.Name)
						}
						body = fmt.Sprintf("%s<p>上次编译成功之后合入MR如下: <div>%s</div>", body, mrMsg)
					}
				}
			}
		}

		// if status == 1 {
		// 	// http://*************:9090/output/6836284b1703e4433505de56022d8ca5/
		// 	body = fmt.Sprintf(`%s<br>编译结果: <a href="http://%s:%d/output/%s">编译结果</a>`, body, libs.Config.Nginx.HOST, libs.Config.Nginx.Port, cronMakeJob.JobId)
		// }
		// msg := strings.Join([]string{from, to, subject, body}, "|")
		// _, err := rc.LPush(libs.Config.Mail.Queue, msg)
		// if err != nil {
		// 	logging.ErrorLogger.Error(err)
		// }
		if len(to) > 0 {
			libs.SendMailRedis(from, to, subject, body, []string{})
		}
	}
}

func ReadUpdateJsonInZipFile(zfp string) (string, error) {
	// 打开 ZIP 文件
	zipFile, err := os.Open(zfp)
	if err != nil {
		return "", err
	}
	defer zipFile.Close()

	// 获取 ZIP 文件的信息
	stat, err := zipFile.Stat()
	if err != nil {
		return "", err
	}

	// 创建一个 zip.Reader
	zipReader, err := zip.NewReader(zipFile, stat.Size())
	if err != nil {
		return "", err
	}

	// 要读取的文件名
	targetFileName := "update.json"

	// 遍历 ZIP 文件中的所有文件
	for _, file := range zipReader.File {
		if file.Name == targetFileName {
			// 打开文件
			rc, err := file.Open()
			if err != nil {
				return "", err
			}
			defer rc.Close()

			// 读取文件内容
			var buf bytes.Buffer
			_, err = io.Copy(&buf, rc)
			if err != nil {
				return "", err
			}
			return buf.String(), nil
		}
	}
	return "", fmt.Errorf("未找到update.json")
}

func updateCronMakeJobSoftwareVersionAndNumber(cronMakeJob *dcronmakejob.CronMakeJob) {
	archivePath := filepath.Join(libs.Config.Buildfarm.Archivepath, cronMakeJob.JobId)
	var systemSoftwareVersion, systemSoftwareNumber, releaseBinDir string
	var dirs []string
	dirs, err := libs.GetAllDir(archivePath, dirs)
	if err != nil {
		logging.ErrorLogger.Error("read fail", err)
		return
	}

	for _, f := range dirs {
		if strings.HasSuffix(f, "releaseID-bin") {
			releaseBinDir = f
			break
		}
	}

	if releaseBinDir == "" {
		logging.ErrorLogger.Errorf("releaseID-bin not found")
		return
	}

	releaseIdFiles, err := os.ReadDir(releaseBinDir)
	if err != nil {
		logging.ErrorLogger.Errorf("read releaseIdTempPath fail", releaseBinDir, err)
	}

	for _, f := range releaseIdFiles {
		if strings.HasSuffix(f.Name(), ".zip") {
			jsonStr, err := ReadUpdateJsonInZipFile(filepath.Join(releaseBinDir, f.Name()))
			if err != nil {
				logging.ErrorLogger.Errorf("read json fail", releaseBinDir, err)
				return
			}
			systemSoftwareVersion = gjson.Get(jsonStr, "extend.version").String()
			systemSoftwareNumber = gjson.Get(jsonStr, "extend.system_software_number").String()
			break
		}
	}

	err = cronMakeJob.Update(cronMakeJob.Id, map[string]interface{}{
		"UpdatedAt":       time.Now(),
		"SoftwareVersion": systemSoftwareVersion,
		"SoftwareNumber":  systemSoftwareNumber,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("update git job err ", err)
	}
}

func StartCronMakeJobImmediateByCronTab(ctx iris.Context) {
	mutexApi.Lock()
	defer mutexApi.Unlock()

	id, err := dao.GetId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	cronTab := dcrontab.CronTab{}

	err = cronTab.Find(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if cronTab.ID == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "计划任务不存在"))
		return
	}

	err = CreateCronMakeJob(&cronTab, 0)
	if err != nil {
		logging.ErrorLogger.Errorf("get cron tab id err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func StartCronMakeJobImmediate(ctx iris.Context) {
	mutexApi.Lock()
	defer mutexApi.Unlock()

	CrontabReq := &dcrontab.CrontabReq{}
	if err := ctx.ReadJSON(CrontabReq); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*CrontabReq)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	project := dproject.Response{}
	if err := project.FindEx("name", CrontabReq.Project); err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if project.Id == 0 {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "未找到该项目"))
		return
	}

	if project.Enable == 0 {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "此项目不支持编译"))
		return
	}

	CrontabReq.ProjectID = project.Id
	CrontabReq.Project = project.Name
	CrontabReq.Repo = project.Repo

	err := CreateCronMakeJob(&CrontabReq.CronTab, CrontabReq.ServerID)
	if err != nil {
		logging.ErrorLogger.Errorf("get cron tab id err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func StopCronJob(cronMakeJob *dcronmakejob.ListResponse) error {
	// err := cronMakeJob.Update(cronMakeJob.Id, map[string]interface{}{"status": 9})
	// if err != nil {
	// 	return err
	// }
	updateCronMakeJob(&cronMakeJob.CronMakeJob, 9, "", false, false)

	if _client, ok := CronRuningMap.Load(cronMakeJob.Id); ok {
		if client, ok := _client.(*ssh.Client); ok {
			client.Close()
			CronRuningMap.Delete(cronMakeJob.Id)
		}
	}

	go func() {
		err := CleanCronJob(cronMakeJob)
		if err != nil {
			logging.ErrorLogger.Errorf(err.Error(), cronMakeJob.JobId)
		}
	}()
	return nil
}

func CleanCronJob(cronMakeJob *dcronmakejob.ListResponse) error {
	serverId := cronMakeJob.ServerId
	dir := cronMakeJob.Dir
	client, err := SSHClient(serverId)
	if err != nil {
		return err
	}

	defer client.Close()
	var stdOut bytes.Buffer
	i := 0
	for i = 0; i < 600; i++ {
		stdOut.Reset()

		command := fmt.Sprintf("ps -ef | grep -v grep | grep %s|wc -l", cronMakeJob.JobId)
		if err = RunCommandOutBuffer(client, command, &stdOut); err != nil {
			continue
		}

		if strings.TrimSpace(stdOut.String()) == "0" {
			command = fmt.Sprintf("df -h |grep %s |awk '{print $NF}' | xargs -i umount {}", cronMakeJob.JobId)
			if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
				continue
			}

			command = fmt.Sprintf("rm %s -rf", dir)
			if err = RunCommandOutBuffer(client, command, &stdOut); err != nil {
				continue
			}

			err = cronMakeJob.Delete(cronMakeJob.Id)
			if err != nil {
				continue
			}
		}
		time.Sleep(2 * time.Second)
	}
	if err == nil {
		return nil
	}
	return fmt.Errorf("作业ID：%s 未成功清理临时文件 %d %s %v", cronMakeJob.JobId, i, stdOut.String(), err)
}

func StopCronMakeJobImmediateV3(ctx iris.Context) {
	mutexApi.Lock()
	defer mutexApi.Unlock()

	CrontabReq := &dcrontab.CrontabReq{}
	if err := ctx.ReadJSON(CrontabReq); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*CrontabReq)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	project := dproject.Response{}
	if err := project.FindEx("name", CrontabReq.Project); err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if project.Id == 0 {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "未找到该项目"))
		return
	}

	if project.Enable == 0 {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "此项目不支持编译"))
		return
	}

	result, err := dcronmakejob.FindAllCronMakeJobs(project.Repo, CrontabReq.Branch, "", "", "0", "", CrontabReq.Product, "", "", CrontabReq.BuildType, "", "", "", "", 1, -1)
	if err != nil {
		logging.ErrorLogger.Errorf("get cron tab id err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	runningJobs := result["items"].([]*dcronmakejob.ListResponse)

	// 停止所有已经启动相同每日编译
	for _, runningJob := range runningJobs {
		err := StopCronJob(runningJob)
		if err != nil {
			logging.ErrorLogger.Errorf("stop cron job err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func StopAllCronMakeJobImmediateV2(ctx iris.Context) {
	mutexApi.Lock()
	defer mutexApi.Unlock()

	result, err := dcronmakejob.FindAllCronMakeJobs("", "", "", "", "0", "", "", "", "", "", "", "", "", "", 1, -1)
	if err != nil {
		logging.ErrorLogger.Errorf("get cron tab id err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	runningJobs := result["items"].([]*dcronmakejob.ListResponse)

	// 停止所有已经启动相同每日编译
	for _, runningJob := range runningJobs {
		err := StopCronJob(runningJob)
		if err != nil {
			logging.ErrorLogger.Errorf("stop cron job err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetCronMakeJobPackages(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	cronMakeJob := dcronmakejob.CronMakeJob{}
	err := cronMakeJob.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("get cron job by id err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	if cronMakeJob.Id == 0 {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "未到找记录"))
		return
	}

	command := fmt.Sprintf(`find %s -name "*.rpm" -printf "%s\n"`, filepath.Join(libs.Config.Buildfarm.Archivepath, cronMakeJob.JobId), "%f")
	output, err := libs.ExecCommand(command)
	if err != nil {
		logging.ErrorLogger.Errorf("get cron job by id err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": command + err.Error()}, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, strings.Split(output, "\n"), response.NoErr.Msg))
	return
}
