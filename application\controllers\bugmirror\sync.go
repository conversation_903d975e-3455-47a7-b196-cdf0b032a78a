package bugmirror

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/bugsync/dbug"
	"irisAdminApi/service/dao/bugsync/dmirrorrecord"
	"irisAdminApi/service/dao/datasync/dproject"
	release_dproject "irisAdminApi/service/dao/release/dproject"
	"strings"
	"time"
)

type BugSyncSummary struct {
	BugID              int
	BugOS              string
	Bug                *dbug.ListResponse
	NeedMirrorProjects []string
	MirroredProjects   []string
	ResolvedProjects   []string
	MirrorStatus       uint //镜像状态： 0: 未同步  1:已同步  2:部分同步
	ResolveStatus      uint //所有同步BUG是否已经全部解决    0: 未解决   1: 已解决
}

type Project struct {
	Name      string
	Baseline  string
	CreatedAt time.Time
}

func BugMirrorWorker() {
	checkProjects, err := dproject.FindAllRunningProjects()
	if err != nil {
		logging.ErrorLogger.Errorf("find running projects err ", err.Error())
		return
	}
	runningProjects, err := dproject.FindRunningProjects()
	if err != nil {
		logging.ErrorLogger.Errorf("find running projects err ", err.Error())
		return
	}
	ntosRunningProjects, err := release_dproject.FindRunningProject()
	if err != nil {
		logging.ErrorLogger.Errorf("find running projects err ", err.Error())
		return
	}
	runningProjectMap := map[string][]string{
		"11.9": {},
		"NTOS": {},
	}

	for _, runningProject := range runningProjects {
		for key := range runningProjectMap {
			if strings.HasPrefix(runningProject.ProjectName, key) && !strings.Contains(key, "NTOS") {
				runningProjectMap[key] = append(runningProjectMap[key], runningProject.ProjectName)
			}
		}
	}
	for _, runningProject := range ntosRunningProjects {
		for key := range runningProjectMap {
			if strings.HasPrefix(runningProject.ProjectName, key) && strings.Contains(key, "NTOS") {
				runningProjectMap[key] = append(runningProjectMap[key], runningProject.ProjectName)
			}
		}
	}
	root := dproject.GenerateTree(runningProjects)
	rootNtos := release_dproject.GenerateTree(ntosRunningProjects)
	dproject.PrintTreeV1(root, 1, []string{})
	release_dproject.PrintTreeV1(rootNtos, 1, []string{})
	// 获取数据库中所有非镜像、bug状态不为new\assign数据、遗留工作包、在行项目BUG
	originBugs, err := dbug.FindResolveMainBug()
	if err != nil {
		return
	}
	mainBugIDs := []int{}
	originBugsMap := map[int]*dbug.ListResponse{}

	bugSyncSumaryMap := map[int]*BugSyncSummary{}
	for _, originBug := range originBugs {
		originBugsMap[originBug.BugID] = originBug

		bugSyncSumaryMap[originBug.BugID] = &BugSyncSummary{
			BugID:              originBug.BugID,
			BugOS:              originBug.BugOS,
			Bug:                originBug,
			NeedMirrorProjects: []string{},
			MirroredProjects:   []string{},
			ResolvedProjects:   []string{},
			ResolveStatus:      originBug.ResolveStatus,
			MirrorStatus:       originBug.MirrorStatus,
		}
		mainBugIDs = append(mainBugIDs, originBug.BugID)
	}

	// 获取数据库中所有镜像bug
	mirrorBugs, err := dbug.FindMirrorBug(mainBugIDs)
	if err != nil {
		return
	}

	mirrorBugsMap := map[int][]*dbug.ListResponse{}
	for _, mirrorBug := range mirrorBugs {
		if _, ok := mirrorBugsMap[mirrorBug.MainBugID]; !ok {
			mirrorBugsMap[mirrorBug.MainBugID] = []*dbug.ListResponse{mirrorBug}
		} else {
			mirrorBugsMap[mirrorBug.MainBugID] = append(mirrorBugsMap[mirrorBug.MainBugID], mirrorBug)
		}
	}

	for _, originBug := range originBugsMap {
		for _, mirrorBug := range mirrorBugsMap[originBug.BugID] {
			if !libs.InArrayS(bugSyncSumaryMap[originBug.BugID].MirroredProjects, mirrorBug.BugOS) && originBug.BugOS != mirrorBug.BugOS {
				bugSyncSumaryMap[originBug.BugID].MirroredProjects = append(bugSyncSumaryMap[originBug.BugID].MirroredProjects, mirrorBug.BugOS)
			}

			if libs.InArrayS([]string{}, mirrorBug.BugTestCharger) && libs.InArrayS([]string{"DENIAL-ByDevelopment", "DENIAL-ByTest", "GIVEUP", "CLOSED-ByTest"}, mirrorBug.BugState) {
				bugSyncSumaryMap[originBug.BugID].ResolvedProjects = append(bugSyncSumaryMap[originBug.BugID].ResolvedProjects, mirrorBug.BugOS)
			} else if !libs.InArrayS([]string{}, mirrorBug.BugTestCharger) && libs.InArrayS([]string{"DENIAL-ByDevelopment", "DENIAL-ByTest", "GIVEUP", "CLOSED-CLOSED-ByDevelopment"}, mirrorBug.BugState) {
				bugSyncSumaryMap[originBug.BugID].ResolvedProjects = append(bugSyncSumaryMap[originBug.BugID].ResolvedProjects, mirrorBug.BugOS)
			}
		}
	}

	for _, item := range bugSyncSumaryMap {
		if originBugsMap[item.BugID].BugWorkpacketName == "遗留或delay的bug(其他)" {
			for key, value := range runningProjectMap {
				//判定当前BugOS是否属于需要处理的系列，目前为(11.9, NTOS)
				if strings.HasPrefix(item.BugOS, key) {
					for _, runningProjectName := range value {
						if strings.HasPrefix(runningProjectName, key) {
							if item.BugOS == runningProjectName || !libs.InArrayS(checkProjects, runningProjectName) {
								continue
							}
							if !libs.InArrayS(item.NeedMirrorProjects, runningProjectName) {
								item.NeedMirrorProjects = append(item.NeedMirrorProjects, runningProjectName)
							}
							// fmt.Println(item.MirroredProjects, runningProjectName)
							if !libs.InArrayS(item.MirroredProjects, runningProjectName) {
								if err := BugMirror(item.Bug, runningProjectName); err == nil {
									item.MirroredProjects = append(item.MirroredProjects, runningProjectName)
								}

								// time.Sleep(1 * time.Second)
							}
						}
					}
				}
			}
		} else {
			// 查找以BugOS为基线的子项目包括孙项目
			if strings.HasPrefix(item.BugOS, "NTOS") {
				node := release_dproject.FindNodeByName(rootNtos, item.BugOS)
				if node != nil {
					targetProjects := release_dproject.FindNodes(node, []*release_dproject.Node{})
					// todo：非遗留工作包需要增加时间维度判定
					for _, targetProject := range targetProjects {
						if item.BugOS == targetProject.ProjectName || !libs.InArrayS(checkProjects, targetProject.ProjectName) {
							continue
						}
						if !libs.InArrayS(item.NeedMirrorProjects, targetProject.ProjectName) && item.Bug.BugCreatedAt.Sub(targetProject.CreatedAt) > 0 {
							item.NeedMirrorProjects = append(item.NeedMirrorProjects, targetProject.ProjectName)
						}
						if !libs.InArrayS(item.MirroredProjects, targetProject.ProjectName) && item.Bug.BugCreatedAt.Sub(targetProject.CreatedAt) > 0 {
							if err := BugMirror(item.Bug, targetProject.ProjectName); err == nil {
								item.MirroredProjects = append(item.MirroredProjects, targetProject.ProjectName)
							}
							// time.Sleep(1 * time.Second)
						}
					}
				}
			} else {
				node := dproject.FindNodeByName(root, item.BugOS)
				if node != nil {
					targetProjects := dproject.FindNodes(node, []*dproject.Node{})
					// todo：非遗留工作包需要增加时间维度判定
					for _, targetProject := range targetProjects {
						if item.BugOS == targetProject.ProjectName {
							continue
						}
						if !libs.InArrayS(item.NeedMirrorProjects, targetProject.ProjectName) && item.Bug.BugCreatedAt.Sub(targetProject.CreatedAt) > 0 {
							item.NeedMirrorProjects = append(item.NeedMirrorProjects, targetProject.ProjectName)
						}
						if !libs.InArrayS(item.MirroredProjects, targetProject.ProjectName) && item.Bug.BugCreatedAt.Sub(targetProject.CreatedAt) > 0 {
							if err := BugMirror(item.Bug, targetProject.ProjectName); err == nil {
								item.MirroredProjects = append(item.MirroredProjects, targetProject.ProjectName)
							}
							// time.Sleep(1 * time.Second)
						}
					}
				}
			}

		}
		// if len(item.MirroredProjects) > 0 {
		// 	fmt.Println(item.BugID, originBugsMap[item.BugID].BugSummary, originBugsMap[item.BugID].BugWorkpacketName, originBugsMap[item.BugID].BugOS, item.MirroredProjects, item.ResolvedProjects, originBugsMap[item.BugID].BugSummary)
		// }
	}
	// todo: update summary to sql
	for _, item := range bugSyncSumaryMap {
		bug := dbug.Response{}
		data := map[string]interface{}{
			"MirroredProjects":   strings.Join(item.MirroredProjects, ","),
			"ResolvedProjects":   strings.Join(item.ResolvedProjects, ","),
			"NeedMirrorProjects": strings.Join(item.NeedMirrorProjects, ","),
			"ResolveStatus":      item.ResolveStatus,
			"MirrorStatus":       item.MirrorStatus,
		}
		for key := range runningProjectMap {
			if strings.HasPrefix(item.BugOS, key) {
				if len(item.MirroredProjects) >= len(item.NeedMirrorProjects) {
					data["MirrorStatus"] = 1
				} else if len(item.MirroredProjects) > 0 {
					data["MirrorStatus"] = 2
				} else if len(item.MirroredProjects) == 0 {
					data["MirrorStatus"] = 0
				}

				if len(item.ResolvedProjects) >= len(item.NeedMirrorProjects) {
					data["ResolveStatus"] = 1
				} else if len(item.ResolvedProjects) > 0 {
					data["ResolveStatus"] = 2
				} else if len(item.ResolvedProjects) == 0 {
					data["ResolveStatus"] = 0
				}
			}
		}
		bug.Update(uint(item.BugID), data)
	}
	// BugSyncWorker()
}

func BugMirror(item *dbug.ListResponse, bugOS string) error {
	//http://bugs.ruijie.com.cn/bug_switch/interface/bug_mirror?bugId=697891&osName=12.5SPR7&submitUser=zhang_lin
	bug := dbug.Response{}
	mirrorBugID := time.Now().Nanosecond()
	err := bug.Create(map[string]interface{}{
		"BugID":             mirrorBugID,
		"BugSummary":        item.BugSummary,
		"MainBugID":         item.BugID,
		"BugOS":             bugOS,
		"BugBelong":         item.BugBelong,
		"BugTestCharger":    item.BugTestCharger,
		"BugState":          "NEW",
		"BugDisabled":       item.BugDisabled,
		"BugCreatedAt":      time.Now().Format("2006-01-02 15:04:05"),
		"BugUpdatedAt":      time.Now().Format("2006-01-02 15:04:05"),
		"BugOwner":          item.BugOwner,
		"BugWorkpacketName": item.BugWorkpacketName,
		"BugProduct":        item.BugProduct,
		"SameBugID":         0,
	})
	if err != nil {
		fmt.Println(err)
	}
	mirrorRecord := dmirrorrecord.Response{}
	err = mirrorRecord.Create(map[string]interface{}{
		"CreatedAt":   time.Now(),
		"UpdatedAt":   time.Now(),
		"MainBugID":   item.BugID,
		"MirrorBugID": mirrorBugID,
	})
	if err != nil {
		fmt.Println(err)
	}
	return nil
}
