package luckydraw

import (
	"irisAdminApi/application/models"
)

type LuckydrawEmployee struct {
	models.ModelBase
	WorkNumber string `gorm:"not null; type:varchar(200)" json:"work_number" form:"work_number"`
	Name       string `gorm:"not null; type:varchar(200)" json:"name" form:"name"`
	City       uint   `gorm:"not null" json:"city" form:"city"`
}

type LuckydrawLotteryRecord struct {
	models.ModelBase
	WorkNumber string `gorm:"not null; type:varchar(200)" json:"work_number" form:"work_number" xlsx:"工号" idx:"1" `
	Name       string `gorm:"not null; type:varchar(200)" json:"name" form:"name" xlsx:"姓名" idx:"2" `
	Zodiac     uint   `gorm:"not null" json:"zodiac" form:"zodiac"`
	ZodiacName string `gorm:"not null" json:"zodiac_name" form:"zodiac_name" xlsx:"生肖" idx:"3" `
	City       uint   `gorm:"not null" json:"city" form:"city"`
	CityName   string `gorm:"not null" json:"city_name" form:"city_name" xlsx:"所在城市" idx:"4" `
}

type LuckydrawCardConfig struct {
	models.ModelBase
	Name     string `gorm:"not null; type:varchar(200)" json:"name" form:"name"`
	Zodiac   uint   `gorm:"not null" json:"zodiac" form:"zodiac"`
	Quantity uint   `gorm:"not null" json:"quantity" form:"quantity"`
	Total    uint   `gorm:"not null" json:"total" form:"total"`
	City     uint   `gorm:"not null" json:"city" form:"city"`
}
