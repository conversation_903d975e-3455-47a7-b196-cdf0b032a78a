package mergerequest

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/mergerequest/dmergerequest"
	"irisAdminApi/service/dao/mergerequest/dmergerequestdiscussioncps"
	"irisAdminApi/service/dao/mergerequest/dmergerequestworkpackage"
	"irisAdminApi/service/dao/release/dreleaseprojectconfig"

	"github.com/kataras/iris/v12"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	larkbitable "github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
	"github.com/pkg/errors"
	"github.com/xuri/excelize/v2"
)

var gitlabSessionPattern = regexp.MustCompile(`_gitlab_session=([a-zA-Z0-9]+)`)

type PositionResponse struct {
	OldPath string `json:"old_path"`
	NewPath string `json:"new_path"`
	OldLine *uint  `json:"old_line"`
	NewLine *uint  `json:"new_line"`
}

type AuthorResponse struct {
	ID       uint   `json:"id"`
	Username string `json:"username"`
}

type NoteResponse struct {
	ID        uint             `json:"id"`
	Type      string           `json:"type"`
	Author    AuthorResponse   `json:"author"`
	System    bool             `json:"system"`
	Body      string           `json:"body"`
	Position  PositionResponse `json:"position"`
	CreatedAt time.Time        `json:"created_at"`
}

type DiscussionCpsResponse struct {
	ID        string           `json:"id"`
	ProjectID uint             `json:"project_id"`
	Notes     []NoteResponse   `json:"notes"`
	Position  PositionResponse `json:"position"`
}

func GetGitlabCookie(ctx iris.Context) (http.Cookie, error) {
	var cookie http.Cookie
	headers := ctx.Request().Header
	_cookie, ok := headers["Cookie"]

	if !ok {
		return cookie, fmt.Errorf("无法获取gitlab cookie")
	}

	var gitlabSession string
	matches := gitlabSessionPattern.FindStringSubmatch(strings.Join(_cookie, " "))
	if len(matches) > 1 {
		// 提取 _gitlab_session 的值
		gitlabSession = matches[1]
	} else {
		return cookie, fmt.Errorf("无法获取gitlab session")
	}

	cookie = http.Cookie{
		Name:     "_gitlab_session",
		Value:    gitlabSession,
		Path:     "/",
		Domain:   libs.Config.Gitlab.Url,
		MaxAge:   36000,
		HttpOnly: false,
		Secure:   false,
	}
	return cookie, nil
}

func GetUsernameBySession(ctx iris.Context) (string, error) {
	cookie, err := GetGitlabCookie(ctx)
	if err != nil {
		return "", errors.Wrap(err, "")
	}
	user := AuthorResponse{}
	url := fmt.Sprintf("%s/api/%s/user", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version)
	resp, err := GitlabWebClient.R().SetCookies(&cookie).SetSuccessResult(&user).Get(url)
	if err != nil {
		return "", errors.Wrap(err, resp.String())
	}
	return user.Username, nil
}

func GetGitlabProjectID(pathWithNameSpace string) (int, error) {
	project := ProjectResponse{}
	url := fmt.Sprintf("%s/api/%s/projects/%s?private_token=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, pathWithNameSpace, libs.Config.Gitlab.Token)
	resp, err := GitlabWebClient.R().SetSuccessResult(&project).Get(url)
	if err != nil {
		return 0, errors.Wrap(err, resp.String())
	}
	return project.ID, nil
}

func CreateDiscussionCps(ctx iris.Context) {
	request := dmergerequestdiscussioncps.Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	username, err := GetUsernameBySession(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get username by session err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	// endpoint: /gitlab-instance-aa5fe906/Monitoring/-/merge_requests/33/discussions/4286fe5d628c82315c99f6281db00776514608cc/resolve
	var mergeRequestIID, discussionID string
	endpointSlice := strings.Split(request.Endpoint, "/")

	libs.ReverseSlice(endpointSlice)
	if len(endpointSlice) > 3 {
		discussionID = endpointSlice[1]
		mergeRequestIID = endpointSlice[3]
	} else {
		logging.ErrorLogger.Errorf("created discussion by endpoint err ", request.Endpoint)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": "created discussion by endpoint err"}, response.SystemErr.Msg))
		return
	}

	// discussion, discussionJson, err := GoGitlabClient.Discussions.GetMergeRequestDiscussion(request.ProjectID, mergeRequestIID, discussionID)
	discussion := DiscussionCpsResponse{}
	url := fmt.Sprintf("%s/api/%s/projects/%d/merge_requests/%s/discussions/%s?private_token=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, request.ProjectID, mergeRequestIID, discussionID, libs.Config.Gitlab.Token)

	resp, err := GitlabWebClient.R().SetSuccessResult(&discussion).Get(url)
	if err != nil {
		logging.ErrorLogger.Errorf("get discussion by id request err ", err, resp.String())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error() + resp.String()}, response.SystemErr.Msg))
		return
	}

	if resp.IsErrorState() {
		logging.ErrorLogger.Errorf("get discussion by id response err ", resp.String())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": resp.String()}, response.SystemErr.Msg))
		return
	}

	var object string

	discussionNotes := []string{}
	reviewer := ""
	// for _, note := range discussion.Notes {
	// 	if !note.System {

	// 		if len(note.Position.NewPath) > 0 {
	// 			object = note.Position.NewPath
	// 			object = fmt.Sprintf("new:%s:%d", object, *note.Position.NewLine)
	// 		} else {
	// 			object = note.Position.OldPath
	// 			object = fmt.Sprintf("old:%s:%d", object, *note.Position.OldLine)
	// 		}

	// 		discussionNotes = append(discussionNotes, fmt.Sprintf("%s:%s", note.Author.Username, note.Body))
	// 		if len(reviewer) == 0 {
	// 			reviewer = note.Author.Username
	// 		}
	// 	}
	// }
	for _, note := range discussion.Notes {
		if !note.System && (note.Type == "DiffNote" || note.Type == "DiscussionNote") {
			if len(note.Position.NewPath) > 0 && note.Position.NewLine != nil {
				object = note.Position.NewPath
				object = fmt.Sprintf("new:%s:%d", object, *note.Position.NewLine)

			} else if len(note.Position.OldPath) > 0 && note.Position.OldLine != nil {
				object = note.Position.OldPath
				object = fmt.Sprintf("old:%s:%d", object, *note.Position.OldLine)
			}

			discussionNotes = append(discussionNotes, fmt.Sprintf("%s:%s", note.Author.Username, note.Body))
			if len(reviewer) == 0 {
				reviewer = note.Author.Username
			}
		}
	}

	// cps := dmergerequestdiscussioncps.MergeRequestDiscussionCps{}
	// err = cps.FindEx("discussion_id", discussionID)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("find discussion by id err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
	// 	return
	// }

	// if cps.Category == 1 {
	// 	reviewer := discussion.Notes[0].Author.Username
	// 	if username != reviewer && reviewer != "buildfarm" {
	// 		ctx.JSON(response.NewResponse(response.ParamentErr.Code, map[string]string{"err_msg": fmt.Sprintf("缺陷只允许评委: %s 修改", reviewer)}, response.ParamentErr.Msg))
	// 		return
	// 	}
	// }

	if request.State == 1 {
		mergerequest := MergeRequestResponse{}
		url := fmt.Sprintf("%s/api/%s/projects/%d/merge_requests/%s?private_token=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, request.ProjectID, mergeRequestIID, libs.Config.Gitlab.Token)

		resp, err := GitlabWebClient.R().SetSuccessResult(&mergerequest).Get(url)
		if err != nil {
			logging.ErrorLogger.Errorf("get discussion by id request err ", err, resp.String())
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error() + resp.String()}, response.SystemErr.Msg))
			return
		}

		if resp.IsErrorState() {
			logging.ErrorLogger.Errorf("get discussion by id response err ", resp.String())
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": resp.String()}, response.SystemErr.Msg))
			return
		}
		assigneeFlag := false
		reviewerFlag := false
		// author := mergerequest.Author.Username

		for _, assignee := range mergerequest.Assignees {
			if username == assignee.Username {
				assigneeFlag = true
				break
			}
		}

		for _, r := range mergerequest.Reviewers {
			if username == r.Username {
				reviewerFlag = true
				break
			}
		}

		switch reviewer {
		case "buildfarm":
			if !assigneeFlag && !reviewerFlag {
				ctx.JSON(response.NewResponse(response.ParamentErr.Code, map[string]string{"err_msg": "只允许合并人/评审委关闭"}, response.ParamentErr.Msg))
				return
			}
		case "aicodereviewer":
		default:
			if username != reviewer && !assigneeFlag {
				ctx.JSON(response.NewResponse(response.ParamentErr.Code, map[string]string{"err_msg": fmt.Sprintf("只允许合并人以及评委: %s 关闭", reviewer)}, response.ParamentErr.Msg))
				return
			}
		}
	}

	discussionNoteString := strings.Join(discussionNotes, "\n")

	data := map[string]interface{}{
		"CreatedAt":       time.Now(),
		"UpdatedAt":       time.Now(),
		"TargetProjectID": request.ProjectID,
		"MergeRequestIID": mergeRequestIID,
		"Object":          object,
		"Discussion":      discussionNoteString,
		"DiscussionID":    discussionID,
		"Reviewer":        reviewer,
		"Category":        request.Category,
		"Severity":        request.Severity,
		"Confirm":         request.Confirm,
		"Introduction":    request.Introduction,
		"Condition":       request.Condition,

		"Comment":            request.Comment,
		"State":              request.State,
		"LastChangeUsername": username,
	}

	discussionCps := dmergerequestdiscussioncps.MergeRequestDiscussionCps{}
	err = discussionCps.Create(data)
	if err != nil {
		logging.ErrorLogger.Errorf("create cps err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetDiscussionCps(ctx iris.Context) {
	var lastFetchAt int
	var err error
	_lastFetchAt := ctx.GetHeader("X-Last-Fetched-At")
	if _lastFetchAt != "" {
		lastFetchAt, err = strconv.Atoi(_lastFetchAt)
		if err != nil {
			lastFetchAt = 0
		}
	}
	request := dmergerequestdiscussioncps.Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if request.Endpoint == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Endpoint is null"))
		return
	}
	// endpoint: /gitlab-instance-aa5fe906/Monitoring/-/merge_requests/33/discussions.json
	var projectID, mergeRequestIID string
	endpointSlice := strings.Split(request.Endpoint, "/")
	libs.ReverseSlice(endpointSlice)
	projectSlice := strings.Split(request.Endpoint, "/-/merge_requests/")
	if len(projectSlice) > 1 {
		projectID = projectSlice[0]
		projectID = strings.TrimPrefix(projectID, "/")
		projectID = strings.TrimSuffix(projectID, "/")
		projectID = url.PathEscape(projectID)
	}
	mergeRequestIID = endpointSlice[1]
	targetProjectID, err := GetGitlabProjectID(projectID)
	if err != nil {
		logging.ErrorLogger.Errorf("get project int id err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	cpses, err := dmergerequestdiscussioncps.FindCpsByProjectAndMergerequestIID(targetProjectID, mergeRequestIID, lastFetchAt)
	if err != nil {
		logging.ErrorLogger.Errorf("create cps err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	result := map[string]*dmergerequestdiscussioncps.ListResponse{}
	for _, cps := range cpses {
		result[cps.DiscussionID] = cps
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func DeleteDiscussionCps(ctx iris.Context) {
	discussionID := ctx.Params().GetString("discussion_id")

	_, err := GetUsernameBySession(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get username by session err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	// endpoint: /gitlab-instance-aa5fe906/Monitoring/-/merge_requests/33/discussions.json
	err = dmergerequestdiscussioncps.DeleteCpsByDiscussionID(discussionID)
	if err != nil {
		logging.ErrorLogger.Errorf("delete cps by discussion id err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

var addCommitPattern = regexp.MustCompile(`added \d+ commit`)

func SyncCpsWorker(project, workpacket string) error {
	mrs, err := dmergerequest.FindByProjetAndWorkpacketForSync(project, workpacket)
	if err != nil {
		return errors.Wrap(err, "")
	}

	m := map[uint][]uint{}
	h := map[uint][]uint{}
	for _, mr := range mrs {
		if _, ok := m[mr.TargetProjectID]; ok {
			m[mr.TargetProjectID] = append(m[mr.TargetProjectID], mr.MergeRequestIID)
			if mr.Status == 3 {
				h[mr.TargetProjectID] = append(h[mr.TargetProjectID], mr.MergeRequestIID)
			}
		} else {
			m[mr.TargetProjectID] = []uint{mr.MergeRequestIID}
			h[mr.TargetProjectID] = []uint{}
		}
	}

	for k := range m {
		// 获取数据库已有记录
		cps, err := dmergerequestdiscussioncps.FindCpsByProject(int(k))
		if err != nil {
			return errors.Wrap(err, "")
		}
		// 转成map
		cpsMap := map[string]*dmergerequestdiscussioncps.ListResponse{}
		for _, c := range cps {
			cpsMap[c.DiscussionID] = c
		}

		for _, mergerequestIID := range m[k] {
			// 数据库无记录的，收集gitlab数据，并更新至数据库
			historyObjects := []map[string]interface{}{}
			if libs.InArrayUint(h[k], mergerequestIID) {
				historyObjects = []map[string]interface{}{
					{
						"TargetProjectID": k,
						"MergeRequestIID": mergerequestIID,
					},
				}
			}

			createObjects := []map[string]interface{}{}
			createReviewerMap := map[string]bool{}
			commitObjects := []map[string]interface{}{}
			commitCount := 1

			discussions, err := GetGitlabDiscussionsByProjectAndMergeRequest(k, mergerequestIID)
			if err != nil {
				return errors.Wrap(err, "")
			}
			for _, discussion := range discussions {
				if _, ok := cpsMap[discussion.ID]; !ok {

					var object string
					var createdAt time.Time
					discussionNotes := []string{}
					reviewer := ""
					if len(discussion.Notes) > 0 && discussion.Notes[0].Type == "DiffNote" {
						createdAt = discussion.Notes[0].CreatedAt
						for _, note := range discussion.Notes {
							if !note.System && note.Type == "DiffNote" {
								if len(note.Position.NewPath) > 0 && note.Position.NewLine != nil {
									object = note.Position.NewPath
									object = fmt.Sprintf("new:%s:%d", object, *note.Position.NewLine)
								} else {
									object = note.Position.OldPath
									object = fmt.Sprintf("old:%s:%d", object, *note.Position.OldLine)
								}

								discussionNotes = append(discussionNotes, fmt.Sprintf("%s:%s", note.Author.Username, note.Body))
								if len(reviewer) == 0 {
									reviewer = note.Author.Username
								}
							}
						}

						discussionNoteString := strings.Join(discussionNotes, "\n")
						if len(discussionNoteString) > 512 {
							discussionNoteString = discussionNoteString[:512]
						}
						data := map[string]interface{}{
							"TargetProjectID":    k,
							"MergeRequestIID":    mergerequestIID,
							"Object":             object,
							"Discussion":         discussionNoteString,
							"CreatedAt":          createdAt,
							"DiscussionID":       discussion.ID,
							"Reviewer":           reviewer,
							"Category":           0,
							"Severity":           0,
							"Confirm":            0,
							"Introduction":       0,
							"Condition":          0,
							"State":              0,
							"LastChangeUsername": "system",
						}
						createReviewerMap[reviewer] = true
						createObjects = append(createObjects, data)
					} else {
						continue
					}
				}
			}

			notes, err := GetGitlabNotesByProjectAndMergeRequest(k, mergerequestIID)
			if err != nil {
				return errors.Wrap(err, "")
			}
			for _, note := range notes {
				if note.Type == "" {
					if strings.HasPrefix(note.Body, "approved") {
						data := map[string]interface{}{
							"TargetProjectID":    k,
							"MergeRequestIID":    mergerequestIID,
							"Object":             "",
							"Discussion":         "",
							"CreatedAt":          note.CreatedAt,
							"DiscussionID":       fmt.Sprintf("%d", note.ID),
							"Reviewer":           note.Author.Username,
							"Category":           0,
							"Severity":           0,
							"Confirm":            0,
							"Introduction":       0,
							"Condition":          0,
							"State":              0,
							"LastChangeUsername": "system",
						}

						if _, ok := createReviewerMap[note.Author.Username]; !ok {
							createObjects = append(createObjects, data)
						}

					} else if len(addCommitPattern.FindAllString(note.Body, -1)) > 0 {
						commitCount++
					}
				}
			}
			if commitCount > 1 {
				data := map[string]interface{}{
					"TargetProjectID": k,
					"MergeRequestIID": mergerequestIID,
					"CommitCount":     commitCount,
				}
				commitObjects = append(commitObjects, data)
			}

			discussionCps := dmergerequestdiscussioncps.MergeRequestDiscussionCps{}
			err = discussionCps.BatchCreate(createObjects, historyObjects, commitObjects)
			if err != nil {
				errors.Wrap(err, "")
			}

		}
	}

	return nil
}

func GetGitlabDiscussionsByProjectAndMergeRequest(projectID, mergeRequestIID uint) ([]*DiscussionCpsResponse, error) {
	discussions := []*DiscussionCpsResponse{}
	page := 1
	pageSize := 100
	for {
		_discussions := []*DiscussionCpsResponse{}
		url := fmt.Sprintf("%s/api/%s/projects/%d/merge_requests/%d/discussions?private_token=%s&per_page=%d&per_page=%d", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, projectID, mergeRequestIID, libs.Config.Gitlab.Token, pageSize, page)

		resp, err := GitlabWebClient.R().SetSuccessResult(&_discussions).Get(url)
		if err != nil {
			return discussions, errors.Wrap(err, "")
		}
		if resp.IsErrorState() {
			return discussions, fmt.Errorf(resp.String())
		}
		discussions = append(discussions, _discussions...)
		totalPages, ok2 := resp.Header["X-Total-Pages"]
		if ok2 && len(totalPages) > 0 {
			t, err2 := strconv.Atoi(totalPages[0])
			if err2 == nil {
				page++
				if page > t {
					break
				}
			}
		} else {
			return discussions, fmt.Errorf("获取gitlab api total pages失败")
		}
		time.Sleep(1 * time.Second)
	}
	return discussions, nil
}

func GetGitlabNotesByProjectAndMergeRequest(projectID, mergeRequestIID uint) ([]*NoteResponse, error) {
	notes := []*NoteResponse{}
	page := 1
	pageSize := 100
	for {
		_notes := []*NoteResponse{}
		url := fmt.Sprintf("%s/api/%s/projects/%d/merge_requests/%d/notes?private_token=%s&per_page=%d&per_page=%d", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, projectID, mergeRequestIID, libs.Config.Gitlab.Token, pageSize, page)

		resp, err := GitlabWebClient.R().SetSuccessResult(&_notes).Get(url)
		if err != nil {
			return notes, errors.Wrap(err, "")
		}
		if resp.IsErrorState() {
			return notes, fmt.Errorf(resp.String())
		}
		notes = append(notes, _notes...)
		totalPages, ok2 := resp.Header["X-Total-Pages"]
		if ok2 && len(totalPages) > 0 {
			t, err2 := strconv.Atoi(totalPages[0])
			if err2 == nil {
				page++
				if page > t {
					break
				}
			}
		} else {
			return notes, fmt.Errorf("获取gitlab api total pages失败")
		}
		time.Sleep(1 * time.Second)
	}
	return notes, nil
}

var SyncLock sync.Mutex

func SyncCpsSchedule() error {
	SyncLock.Lock()
	defer SyncLock.Unlock()
	// 获取项目列表，查找打开通知的项目
	enabledNoticeProjects, err := dreleaseprojectconfig.AllEnabledConfig()
	if err != nil {
		logging.ErrorLogger.Error("get project config err ", err)
		return err
	}
	for _, project := range enabledNoticeProjects {
		workpackets, err := dmergerequestworkpackage.FindAllByReleaseProjectID(project.ReleaseProjectID)
		if err != nil {
			logging.ErrorLogger.Error("get workpackets by release project id err ", err)
			return err
		}
		for _, workpacket := range workpackets {
			err := SyncCpsWorker(project.ReleaseProject.Name, workpacket.Name)
			if err != nil {
				logging.ErrorLogger.Error("get sync discussion err ", err)
				return err
			}
		}
	}

	return nil
}

// 创建数据表
func CreateTable() {
	// 创建 Client
	fmt.Println(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret, libs.Config.FeiShuDoc.BiAppToken)
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)
	// 创建请求对象
	req := larkbitable.NewCreateAppTableReqBuilder().
		AppToken(libs.Config.FeiShuDoc.BiAppToken).
		Body(larkbitable.NewCreateAppTableReqBodyBuilder().
			Table(larkbitable.NewReqTableBuilder().
				Name("代码评审详情表").
				Fields([]*larkbitable.AppTableCreateHeader{
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`cps_id`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`mr_id`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`MR标题`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`MR创建时间`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`增加行数`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`删除行数`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`MR负责人`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`项目`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`需求`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`工作包`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`评论对象`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`评论内容`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`评委`).
						Type(11).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`类别`).Property(larkbitable.NewAppTableFieldPropertyBuilder().
						Options([]*larkbitable.AppTableFieldPropertyOption{
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`未确认`).
								Build(),
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`缺陷`).
								Build(),
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`建议`).
								Build(),
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`提问`).
								Build(),
						}).
						Build()).
						Type(3).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`严重程度`).Property(larkbitable.NewAppTableFieldPropertyBuilder().
						Options([]*larkbitable.AppTableFieldPropertyOption{
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`未确认`).
								Build(),
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`严重`).
								Build(),
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`一般`).
								Build(),
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`轻微`).
								Build(),
						}).
						Build()).
						Type(3).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`问题确认`).Property(larkbitable.NewAppTableFieldPropertyBuilder().
						Options([]*larkbitable.AppTableFieldPropertyOption{
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`未确认`).
								Build(),
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`接受`).
								Build(),
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`拒绝`).
								Build(),
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`重复`).
								Build(),
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`答复`).
								Build(),
						}).
						Build()).
						Type(3).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`缺陷引入`).Property(larkbitable.NewAppTableFieldPropertyBuilder().
						Options([]*larkbitable.AppTableFieldPropertyOption{
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`未确认`).
								Build(),
							// larkbitable.NewAppTableFieldPropertyOptionBuilder().
							// 	Name(`需求(遗漏)`).
							// 	Build(),
							// larkbitable.NewAppTableFieldPropertyOptionBuilder().
							// 	Name(`需求(错误)`).
							// 	Build(),
							// larkbitable.NewAppTableFieldPropertyOptionBuilder().
							// 	Name(`需求(不符合用户需求)`).
							// 	Build(),
							// larkbitable.NewAppTableFieldPropertyOptionBuilder().
							// 	Name(`总体设计(遗漏)`).
							// 	Build(),
							// larkbitable.NewAppTableFieldPropertyOptionBuilder().
							// 	Name(`总体设计(与需求不一致)`).
							// 	Build(),
							// larkbitable.NewAppTableFieldPropertyOptionBuilder().
							// 	Name(`详细设计(遗漏)`).
							// 	Build(),
							// larkbitable.NewAppTableFieldPropertyOptionBuilder().
							// 	Name(`详细设计(存在缺陷)`).
							// 	Build(),
							// larkbitable.NewAppTableFieldPropertyOptionBuilder().
							// 	Name(`详细设计(与总体设计不一致)`).
							// 	Build(),
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`编码(基础问题)`).
								Build(),
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`编码(业务问题)`).
								Build(),
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`编码(与设计不一致)`).
								Build(),
							// larkbitable.NewAppTableFieldPropertyOptionBuilder().
							// 	Name(`配置(版本/参数/宏配置错误等)`).
							// 	Build(),
							// larkbitable.NewAppTableFieldPropertyOptionBuilder().
							// 	Name(`文档(文档/描述/文字错误)`).
							// 	Build(),
							// larkbitable.NewAppTableFieldPropertyOptionBuilder().
							// 	Name(`第三方代码`).
							// 	Build(),
							// larkbitable.NewAppTableFieldPropertyOptionBuilder().
							// 	Name(`硬件缺陷`).
							// 	Build(),
						}).
						Build()).
						Type(3).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`有条件关闭`).Property(larkbitable.NewAppTableFieldPropertyBuilder().
						Options([]*larkbitable.AppTableFieldPropertyOption{
							// larkbitable.NewAppTableFieldPropertyOptionBuilder().
							// 	Name(`未确认`).
							// 	Build(),
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`否`).
								Build(),
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`是`).
								Build(),
						}).
						Build()).
						Type(3).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`状态`).Property(larkbitable.NewAppTableFieldPropertyBuilder().
						Options([]*larkbitable.AppTableFieldPropertyOption{
							// larkbitable.NewAppTableFieldPropertyOptionBuilder().
							// 	Name(`未确认`).
							// 	Build(),
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`开启`).
								Build(),
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`关闭`).
								Build(),
						}).
						Build()).
						Type(3).
						Build(),
				}).
				Build()).
			Build()).
		Build()

	// 发起请求
	resp, err := client.Bitable.AppTable.Create(context.Background(), req)
	// 处理错误
	if err != nil {
		fmt.Println(err)
		return
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		return
	}

	// 业务处理
	fmt.Println(larkcore.Prettify(resp))
}

// 创建记录
func BatchCreateCpsRecord(data []*dmergerequestdiscussioncps.CpsFeishuResponse) error {
	// 创建 Client
	err := CleanTable("tblbTRhZXzVkwjYx")
	if err != nil {
		return errors.Wrap(err, "")
	}
	limit := 500
	for loop := 0; true; loop++ {
		// cpsIDs := []uint{}
		start := loop * limit
		if start > len(data) {
			break
		}
		end := (loop + 1) * limit
		if len(data) <= (loop+1)*limit {
			end = len(data)
		}
		/*
			type CpsFeishuResponse struct {
				CpsID              uint      `json:"cps_id"`
				MrID               uint      `json:"mr_id"`
				Title              string    `json:"title"`
				CreatedAt          time.Time `json:"created_at"`
				ReleaseProject     string    `json:"release_project"`
				Requirement        string    `json:"requirement"`
				WorkPackage        string    `json:"work_package"`
				CodeQuantityAdd    uint      `json:"code_quantity_add"`
				CodeQuantityRemove uint      `json:"code_quantity_remove"`
				Object             string    `json:"object"`
				Discussion         string    `json:"discussion"`
				Reviewer           string    `json:"reviewer"`
				Category           uint      `json:"category"`
				Severity           uint      `json:"severity"`
				Confirm            uint      `json:"confirm"`
				Introduction       uint      `json:"introduction"`
				State              uint      `json:"state"`
			}
		*/
		records := []*larkbitable.AppTableRecord{}
		for _, record := range data[start:end] {
			// cpsIDs = append(cpsIDs, record.CpsID)
			records = append(records, larkbitable.NewAppTableRecordBuilder().
				Fields(map[string]interface{}{
					"cps_id": fmt.Sprintf("%d", record.CpsID),
					"mr_id":  fmt.Sprintf("%d", record.MrID),
					"MR标题":   record.Title,
					"MR创建时间": record.CreatedAt.Format("2006-01-02 15:04:05"),
					"增加行数":   record.CodeQuantityAdd,
					"删除行数":   record.CodeQuantityRemove,
					"MR负责人":  record.Owner,
					"项目":     record.ReleaseProject,
					"需求":     record.Requirement,
					"工作包":    record.WorkPackage,
					"评论对象":   record.Object,
					"评论内容":   record.Discussion,
					"评委":     record.Reviewer,
					"类别":     record.GetCategory(record.Category),
					"严重程度":   record.GetSeverity(record.Severity),
					"问题确认":   record.GetConfirm(record.Confirm),
					"缺陷引入":   record.GetIntroduction(record.Introduction),
					"有条件关闭":  record.GetCondition(record.Condition),
					"状态":     record.GetState(record.State),
				}).
				Build())
		}

		body := larkbitable.NewBatchCreateAppTableRecordReqBodyBuilder().Records(records).Build()

		// 创建请求对象
		req := larkbitable.NewBatchCreateAppTableRecordReqBuilder().
			AppToken(libs.Config.FeiShuDoc.BiAppToken).
			TableId("tblbTRhZXzVkwjYx").
			Body(body).
			Build()

		// 发起请求
		resp, err := FeiShuClient.Bitable.AppTableRecord.BatchCreate(context.Background(), req)
		// 处理错误
		if err != nil {
			return errors.Wrap(err, "")
		}

		// 服务端错误处理
		if !resp.Success() {
			return fmt.Errorf("飞书服务端返回错误 %v %v %v", resp.Code, resp.Msg, resp.RequestId())
		}
		// 更新ID
		time.Sleep(1 * time.Second)
	}
	return nil
}

// 创建记录
func BatchCreateReviewerRecord(data []*dmergerequestdiscussioncps.CpsReviewerSummaryFeishuResponse) error {
	if len(data) == 0 {
		return nil
	}

	err := CleanTable("tblBQygRiS98h6D9")
	if err != nil {
		return errors.Wrap(err, "")
	}
	// 创建 Client
	limit := 500

	for loop := 0; true; loop++ {
		start := loop * limit
		if start > len(data) {
			break
		}
		end := (loop + 1) * limit
		if len(data) <= (loop+1)*limit {
			end = len(data)
		}
		/*
			type CpsFeishuResponse struct {
				CpsID              uint      `json:"cps_id"`
				MrID               uint      `json:"mr_id"`
				Title              string    `json:"title"`
				CreatedAt          time.Time `json:"created_at"`
				ReleaseProject     string    `json:"release_project"`
				Requirement        string    `json:"requirement"`
				WorkPackage        string    `json:"work_package"`
				CodeQuantityAdd    uint      `json:"code_quantity_add"`
				CodeQuantityRemove uint      `json:"code_quantity_remove"`
				Object             string    `json:"object"`
				Discussion         string    `json:"discussion"`
				Reviewer           string    `json:"reviewer"`
				Category           uint      `json:"category"`
				Severity           uint      `json:"severity"`
				Confirm            uint      `json:"confirm"`
				Introduction       uint      `json:"introduction"`
				State              uint      `json:"state"`
			}
		*/
		records := []*larkbitable.AppTableRecord{}
		for _, record := range data[start:end] {
			records = append(records, larkbitable.NewAppTableRecordBuilder().
				Fields(map[string]interface{}{
					"评委":         record.Name,
					"项目":         record.ReleaseProject,
					"平均响应时间(小时)": record.AvgResponseEscape,
				}).
				Build())
		}

		body := larkbitable.NewBatchCreateAppTableRecordReqBodyBuilder().Records(records).Build()

		// 创建请求对象
		req := larkbitable.NewBatchCreateAppTableRecordReqBuilder().
			AppToken(libs.Config.FeiShuDoc.BiAppToken).
			TableId("tblBQygRiS98h6D9").
			Body(body).
			Build()

		// 发起请求
		resp, err := FeiShuClient.Bitable.AppTableRecord.BatchCreate(context.Background(), req)
		// 处理错误
		if err != nil {
			return errors.Wrap(err, "")
		}

		// 服务端错误处理
		if !resp.Success() {
			return fmt.Errorf("飞书服务端返回错误 %v %v %v", resp.Code, resp.Msg, resp.RequestId())
		}
		time.Sleep(1 * time.Second)
		// 更新ID
	}
	return nil
}

// 创建记录
func BatchCreateProjectRecord(data []*dmergerequestdiscussioncps.ProjectFeishuResponse) error {
	if len(data) == 0 {
		return nil
	}

	err := CleanTable("tblvfEOoB5d7mHqk")
	if err != nil {
		return errors.Wrap(err, "")
	}
	// 创建 Client
	limit := 500

	for loop := 0; true; loop++ {
		start := loop * limit
		if start > len(data) {
			break
		}
		end := (loop + 1) * limit
		if len(data) <= (loop+1)*limit {
			end = len(data)
		}
		/*
			type CpsFeishuResponse struct {
				CpsID              uint      `json:"cps_id"`
				MrID               uint      `json:"mr_id"`
				Title              string    `json:"title"`
				CreatedAt          time.Time `json:"created_at"`
				ReleaseProject     string    `json:"release_project"`
				Requirement        string    `json:"requirement"`
				WorkPackage        string    `json:"work_package"`
				CodeQuantityAdd    uint      `json:"code_quantity_add"`
				CodeQuantityRemove uint      `json:"code_quantity_remove"`
				Object             string    `json:"object"`
				Discussion         string    `json:"discussion"`
				Reviewer           string    `json:"reviewer"`
				Category           uint      `json:"category"`
				Severity           uint      `json:"severity"`
				Confirm            uint      `json:"confirm"`
				Introduction       uint      `json:"introduction"`
				State              uint      `json:"state"`
			}
		*/
		records := []*larkbitable.AppTableRecord{}
		for _, record := range data[start:end] {
			records = append(records, larkbitable.NewAppTableRecordBuilder().
				Fields(map[string]interface{}{
					"项目":  record.ReleaseProject,
					"工作包": record.WorkPackage,
				}).
				Build())
		}

		body := larkbitable.NewBatchCreateAppTableRecordReqBodyBuilder().Records(records).Build()

		// 创建请求对象
		req := larkbitable.NewBatchCreateAppTableRecordReqBuilder().
			AppToken(libs.Config.FeiShuDoc.BiAppToken).
			TableId("tblvfEOoB5d7mHqk").
			Body(body).
			Build()

		// 发起请求
		resp, err := FeiShuClient.Bitable.AppTableRecord.BatchCreate(context.Background(), req)
		// 处理错误
		if err != nil {
			return errors.Wrap(err, "")
		}

		// 服务端错误处理
		if !resp.Success() {
			return fmt.Errorf("飞书服务端返回错误 %v %v %v", resp.Code, resp.Msg, resp.RequestId())
		}
		time.Sleep(1 * time.Second)
		// 更新ID
	}
	return nil
}

func SyncFeishuWorker() error {
	// discussionCps := dmergerequestdiscussioncps.MergeRequestDiscussionCps{}
	cps, err := dmergerequestdiscussioncps.FindCpsWithoutSyncFeishu()
	if err != nil {
		return errors.Wrapf(err, "find cps err")
	}
	err = BatchCreateCpsRecord(cps)
	if err != nil {
		return errors.Wrapf(err, "batch create cps record err")
	}

	reviewerSummary, err := dmergerequestdiscussioncps.FindReviewerSummary()
	if err != nil {
		return errors.Wrapf(err, "find reviewer summary err")
	}

	err = BatchCreateReviewerRecord(reviewerSummary)
	if err != nil {
		return errors.Wrapf(err, "batch create reviewer record err")
	}

	projectSummary, err := dmergerequestdiscussioncps.FindProjectSummary()
	if err != nil {
		return errors.Wrapf(err, "find project summary err")
	}

	err = BatchCreateProjectRecord(projectSummary)
	if err != nil {
		return errors.Wrapf(err, "batch create project record err")
	}

	return nil
}

func SyncCpsToFeishu() error {
	SyncLock.Lock()
	defer SyncLock.Unlock()

	err := SyncFeishuWorker()
	if err != nil {
		logging.ErrorLogger.Error(err)
		return err
	}

	return nil
}

func GitlabDiscussionResolve(ctx iris.Context) {
	// cookie, err := GetGitlabCookie(ctx)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("get username by session err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	username, err := GetUsernameBySession(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get username by session err ", err)
		ctx.StatusCode(iris.StatusBadRequest)
		ctx.ContentType("application/json")
		ctx.JSON("{}")
		return
	}

	origin := ctx.Request().Header.Get("Origin")
	originUrl := ctx.Request().Header.Get("Origin-Url")
	if originUrl == "" || !strings.HasSuffix(originUrl, "/resolve") {
		logging.ErrorLogger.Errorf("get resolve referer err ", originUrl)
		ctx.StatusCode(iris.StatusBadRequest)
		ctx.ContentType("application/json")
		ctx.JSON("{}")
		return
	}

	var projectID, mergeRequestIID, discussionID string
	endpointSlice := strings.Split(originUrl, "/")
	projectSlice := strings.Split(originUrl, "/-/merge_requests/")
	if len(projectSlice) > 1 {
		projectID = projectSlice[0]
		projectID = strings.TrimPrefix(projectID, "/")
		projectID = strings.TrimSuffix(projectID, "/")
		projectID = url.PathEscape(projectID)
	}
	libs.ReverseSlice(endpointSlice)
	discussionID = endpointSlice[1]
	mergeRequestIID = endpointSlice[3]
	discussion := DiscussionCpsResponse{}
	url := fmt.Sprintf("%s/api/%s/projects/%s/merge_requests/%s/discussions/%s?private_token=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, projectID, mergeRequestIID, discussionID, libs.Config.Gitlab.Token)

	resp, err := GitlabWebClient.R().SetSuccessResult(&discussion).Get(url)
	if err != nil {
		logging.ErrorLogger.Errorf("get discussion by id request err ", err, resp.String())
		ctx.StatusCode(iris.StatusBadRequest)
		ctx.ContentType("application/json")
		ctx.JSON("{}")
		return
	}

	if resp.IsErrorState() {
		logging.ErrorLogger.Errorf("get discussion by id response err ", resp.String())
		ctx.StatusCode(iris.StatusBadRequest)
		ctx.ContentType("application/json")
		ctx.JSON("{}")
		return
	}
	if len(discussion.Notes) == 0 {
		logging.ErrorLogger.Errorf("get discussion note err ", discussion)
		ctx.StatusCode(iris.StatusBadRequest)
		ctx.ContentType("application/json")
		ctx.JSON("{}")
		return
	}
	cps := dmergerequestdiscussioncps.MergeRequestDiscussionCps{}
	err = cps.FindEx("discussion_id", discussionID)
	if err != nil {
		logging.ErrorLogger.Errorf("find discussion by id err ", err)
		ctx.StatusCode(iris.StatusBadRequest)
		ctx.ContentType("application/json")
		ctx.JSON("{}")
		return
	}

	reviewer := discussion.Notes[0].Author.Username
	if username != reviewer && reviewer != "buildfarm" && username != "buildfarm" && reviewer != "aicodereviewer" {
		ctx.StatusCode(iris.StatusForbidden)
		ctx.ContentType("application/json")
		ctx.JSON("{}")
		return
	}

	r := GitlabWebClient.R()

	resp, err = r.SetHeader("X-CSRF-Token", ctx.Request().Header.Get("X-CSRF-Token")).SetCookies(ctx.Request().Cookies()...).Post(origin + originUrl + "?real=1")
	if err != nil {
		logging.ErrorLogger.Errorf("resolve discussion by id request err ", err, resp.String())
		ctx.StatusCode(iris.StatusBadRequest)
		ctx.ContentType("application/json")
		ctx.JSON("{}")
		return
	}

	if resp.IsErrorState() {
		logging.ErrorLogger.Errorf("resolve discussion by id response err ", resp.String())
		ctx.StatusCode(iris.StatusBadRequest)
		ctx.ContentType("application/json")
		ctx.JSON("{}")
		return
	}

	ctx.StatusCode(resp.StatusCode)
	ctx.ContentType(resp.Header.Get("Content-Type"))
	ctx.WriteString(resp.String())
	return
}

func CpsExport(ctx iris.Context) {
	releaseProjectrojectID, _ := strconv.Atoi(ctx.FormValue("release_project_id"))
	workPackageID, _ := strconv.Atoi(ctx.FormValue("work_package_id"))
	// start := ctx.FormValue("start")
	// end := ctx.FormValue("end")

	list, err := dmergerequest.All(0, 0, uint(releaseProjectrojectID), uint(workPackageID), "", "", "", "3", 1, -1)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	mrs := list["items"].([]*dmergerequest.ListResponse)
	filters := map[uint][]uint{}
	for _, mr := range mrs {
		if _, ok := filters[mr.TargetProjectID]; ok {
			filters[mr.TargetProjectID] = append(filters[mr.TargetProjectID], mr.MergeRequestIID)
		} else {
			filters[mr.TargetProjectID] = []uint{mr.MergeRequestIID}
		}
	}

	results := [][]string{}

	for k, v := range filters {
		cps, err := dmergerequestdiscussioncps.FindCpsByProjectAndMergerequestIIDs(k, v)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		for _, cp := range cps {
			results = append(results, []string{
				cp.Reviewer,
				cp.Discussion,
				cp.Object,
				cp.GetCategory(),
				cp.GetSeverity(),
				cp.GetConfirm(),
				cp.GetIntroduction(),
				cp.GetState(),
				"",
				"",
			})
		}
	}
	if len(mrs) > 0 && len(results) > 0 {
		releaseProject := mrs[0].ReleaseProject
		workPackageName := mrs[0].WorkPackage
		src := "./uploads/cps_template.xlsx"
		dst := fmt.Sprintf("/tmp/%s_%s-CPS.xlsx", releaseProject, workPackageName)
		_, err := libs.CopyFileWithBuffer(src, dst)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		defer os.RemoveAll(dst)
		f, err := excelize.OpenFile(dst)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		f.Close()
		cols := []string{"B", "C", "H", "K", "L", "M", "N", "O", "P"}

		rowNum := 5

		for _, line := range results {
			rowNum++
			for idx, col := range cols {
				f.SetCellValue("Sheet1", fmt.Sprintf("%s%d", col, rowNum), line[idx])
			}
		}
		if err := f.Save(); err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		ctx.SendFile(dst, filepath.Base(dst))
		return
	}
	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "无数据"))
	return
}
