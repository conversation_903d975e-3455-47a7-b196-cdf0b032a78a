package datasync

type PmsDocumentRequestRelation struct {
	ID                int    `gorm:"primarykey; autoIncrement:false" json:"id" `
	ProjectID         uint   `gorm:"not null;" json:"project_id"  update:"1"`
	ProjectName       string `gorm:"not null; type:varchar(200)" json:"project_name"  update:"1"`
	RequestDocumentID uint   `gorm:"not null;" json:"request_document_id"  update:"1"`
	ProjectRequestID  uint   `gorm:"not null;" json:"project_request_id"  update:"1"`
	Disabled          bool   `grom:"not null; default:false" json:"disabled" update:"1"`
}
