package dmakejob

import (
	"fmt"
	"path/filepath"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/buildfarm"
	"irisAdminApi/service/dao/release/dproductmodel"

	"github.com/pkg/errors"
)

const ModelName = "Make任务管理"

type BuildfarmProductCpu struct {
	Product string `json:"product"`
	Cpu     string `json:"cpu"`
}

type User struct {
	ID       uint   `json:"id"`
	Username string `json:"username"`
	Name     string `json:"name"`
}

type GitJob struct {
	buildfarm.GitJob
	User User `grom:"->; foreignKey:UserID; references:ID" json:"user"`
}

type MakeJob struct {
	buildfarm.MakeJob
	GitJob *GitJob             `gorm:"->; foreignKey:JobID; references:JobID" json:"git_job"`
	Cpu    BuildfarmProductCpu `gorm:"->;foreignKey:Product;references:Product" json:"cpu"`
	User   User                `grom:"->; foreignKey:UserID; references:ID" json:"user"`
}

type ListResponse struct {
	MakeJob
}

type GitJobReq struct {
	Id        uint   `json:"id"`
	UserId    uint   `json:"user_id"`
	Defconfig string `json:"defconfig"`
	Target    string `json:"Target"`
	Baseline  string `json:"baseline"`
	Status    uint   `json:"status"`  // 作业状态 0：运行，1：成功， 2：失败
	Version   string `json:"version"` // 版本hash值,初始为0
}

func (a *MakeJob) ModelName() string {
	return ModelName
}

func Model() *buildfarm.MakeJob {
	return &buildfarm.MakeJob{}
}

func (a *MakeJob) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("GitJob").Preload("Cpu").Preload("User")
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *MakeJob) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *MakeJob) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *MakeJob) CreateV2(object interface{}) error {
	return nil
}

func (a *MakeJob) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	return err
}

func (a *MakeJob) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *MakeJob) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *MakeJob) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *MakeJob) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	if u.ID == 0 {
		return errors.New(fmt.Sprint("record not find ", id))
	}
	return nil
}

func (u *MakeJob) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (m *MakeJob) GetTempPatchDir() string {
	return filepath.Join(libs.Config.Buildfarm.Temp, m.JobID, "patches")
}

func (m *MakeJob) GetTempDefconfigPath() string {
	return filepath.Join(libs.Config.Buildfarm.Temp, m.JobID, "defconfig", m.Product+"_defconfig")
}

func FindTMakeJobsByStatusAndTaskType(status []uint, taskType []uint) ([]*MakeJob, error) {
	var makeJobs []*MakeJob
	db := easygorm.GetEasyGormDb().Model(Model()).Preload("GitJob").Preload("GitJob.User").Preload("User")
	db = db.Where("status in ? and task_type in ?", status, taskType).Order("created_at ASC")

	err := db.Find(&makeJobs).Error
	return makeJobs, err
}

func FindQueueJobs() ([]*MakeJob, error) {
	var queueMakeJobs []*MakeJob
	db := easygorm.GetEasyGormDb().Model(Model()).Debug().Preload("GitJob")
	db = db.Where("status = ?", 3).Order("created_at ASC")

	err := db.Find(&queueMakeJobs).Error
	return queueMakeJobs, err
}

func FindGitJobMakeJobs(jobId string) ([]*MakeJob, error) {
	var allJobs []*MakeJob
	db := easygorm.GetEasyGormDb().Model(Model())
	db = db.Where("job_id = ?", jobId).Order("created_at DESC")

	err := db.Find(&allJobs).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	return allJobs, nil
}

func FindMakeJobsInJobIds(jobIds []string, taskType, start, end, status, product, defconfig, target, buildType, softnum, softversion, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var makejobs []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("GitJob").Preload("Cpu").Preload("User")
	if len(start) > 0 {
		db = db.Where("created_at >= ?", start+" 00:00:00.000")
	}
	if len(end) > 0 {
		db = db.Where("created_at <= ?", end+" 23:59:59.999")
	}
	if len(status) > 0 {
		db = db.Where("status = ?", status)
	}
	if len(product) > 0 {
		db = db.Where(fmt.Sprintf("product like '%%%s%%'", product))
		// db = db.Where("status = ?", status)
	}
	if len(defconfig) > 0 {
		db = db.Where(fmt.Sprintf("defconfig like '%%%s%%'", defconfig))
		// db = db.Where("defconfig = ?", defconfig)
	}
	if len(target) > 0 {
		db = db.Where(fmt.Sprintf("target like '%%%s%%'", target))
		// db = db.Where("target = ?", target)
	}
	if len(buildType) > 0 {
		db = db.Where("build_type = ?", buildType)
		// db = db.Where("target = ?", target)
	}
	if len(softnum) > 0 {
		db = db.Where("software_number = ?", softnum)
		// db = db.Where("target = ?", target)
	}
	if len(softversion) > 0 {
		db = db.Where(fmt.Sprintf("software_version like '%%%s%%'", softversion))
		// db = db.Where("target = ?", target)
	}
	if len(taskType) > 0 {
		db = db.Where("task_type = ?", taskType)
	} else {
		db = db.Where("task_type in (1, 2)")
	}
	db = db.Where("job_id in ?", jobIds)
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&makejobs).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": makejobs, "total": count, "limit": pageSize}
	return list, nil
}

func FindMakeJobByTaskId(taskId string) (MakeJob, error) {
	item := MakeJob{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("task_id = ?", taskId).Find(&item).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return item, err
	}
	return item, nil
}

func FindMakeJobByJobId(jobId string) (MakeJob, error) {
	item := MakeJob{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("job_id = ?", jobId).Find(&item).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return item, err
	}
	return item, nil
}

func DeleMakeJobByJobId(jobId string) error {
	err := easygorm.GetEasyGormDb().Delete(Model(), "job_id = ? and status != 1", jobId).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func FindRunningCoverityJob() ([]*ListResponse, error) {
	items := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("task_type = 3 and status = 0").Find(&items).Error
	return items, err
}

func FindMakeJobsByLikeReleaseID(branch, releaseID string) ([]*ListResponse, error) {
	items := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("Cpu").Where("branch = ? and software_version like ? and customized = 0", branch, fmt.Sprintf("%%%s%%", releaseID)).Find(&items).Error
	return items, errors.Wrap(err, "")
}

type ReleaseRelease struct {
	ID            uint   `json:"uint"`
	BuildFarmLink string `json:"build_farm_link"`
}

func FindMakeJobsToClean() ([]string, error) {
	// 获取已经发布作业ID
	result := []string{}
	rawSql := `
		select 
			task_id
		from
			(
			select 
				task_id
			FROM 
				make_jobs
			WHERE
				id NOT IN (
				SELECT
					cron_make_job_id
				FROM
					patch_jobs)
				AND
				created_at <= DATE_ADD(NOW(), INTERVAL - 14 DAY)
				and created_at >= DATE_ADD(NOW(), INTERVAL - 30 DAY)
		) mjs
		left join release_releases rrs on
			rrs.build_farm_link like concat('%', mjs.task_id, '%')
		where 
			rrs.id is null
	`
	err := easygorm.GetEasyGormDb().Raw(rawSql).Scan(&result).Error
	if err != nil {
		return nil, err
	}
	return result, nil
}

// FindMakeJobsByProductAndSoftware 根据产品名称、软件版本号和软件版本查询Makejob信息
func FindMakeJobsByProductAndSoftware(product, softnum, softversion string) (*MakeJob, error) {
	// 构建查询
	db := easygorm.GetEasyGormDb().Model(Model()).Preload("GitJob").Preload("Cpu").Preload("User")

	logging.InfoLogger.Infof("查询参数: product=%s, softnum=%s, softversion=%s", product, softnum, softversion)

	// 尝试将产品名称转换为BuildName
	if len(product) > 0 {
		// 尝试从dproductmodel查找对应的BuildName
		var productModel dproductmodel.ReleaseProductModel
		err := productModel.FindByName(product)
		if err == nil && productModel.ID > 0 && len(productModel.ReleaseBuildName.Name) > 0 {
			buildName := productModel.ReleaseBuildName.Name
			// 使用模糊查询匹配产品名称
			db = db.Where("product LIKE ?", "%"+buildName+"%")
		} else {
			// 如果没有找到对应的BuildName，直接使用product参数进行模糊查询
			db = db.Where("product LIKE ?", "%"+product+"%")
		}
	}

	// 添加软件版本号过滤条件（如果提供）
	if len(softnum) > 0 {
		db = db.Where("software_number LIKE ?", "%"+softnum+"%")
	}

	// 添加软件版本过滤条件（如果提供）
	if len(softversion) > 0 {
		db = db.Where("software_version LIKE ?", "%"+softversion+"%")
	}

	// 添加状态条件，优先查找成功的构建
	db = db.Where("status = ?", 1)

	// 按ID倒序排序以获取最新记录
	db = db.Order("id DESC")

	// 执行查询，只获取第一条记录
	var item MakeJob
	err := db.Limit(1).First(&item).Error
	// 查询没有找到记录时不返回错误
	if err != nil {
		if err.Error() == "record not found" {
			logging.InfoLogger.Infof("查询未找到匹配的记录")
			return nil, nil
		}
		logging.ErrorLogger.Errorf("查询出错: %v", err)
		return nil, err
	}
	return &item, nil
}

type MakejobReq struct {
	ID                 uint   `json:"id" form:"id"`
	ProjectID          uint   `json:"project_id" form:"project_id"`
	Project            string `json:"project" form:"project"`
	Repo               string `json:"repo" form:"repo"`
	Branch             string `json:"branch" form:"branch"`
	Customized         uint   `json:"customized" form:"customized"`
	TaskType           uint   `json:"task_type" form:"task_type"`
	Defconfig          string `json:"defconfig" form:"defconfig"`
	Target             string `json:"target" form:"target"`
	Product            string `json:"product" form:"product"`
	Baseline           string `json:"baseline" form:"baseline"`
	PatchEnable        bool   `json:"patch_enable" form:"patch_enable"`
	Patches            string `json:"patches" form:"patches"`
	CustomEnable       bool   `json:"custom_enable" form:"custom_enable"`
	SwitchBranches     string `json:"switch_branches" form:"switch_branches"`
	CustomDefconfig    string `json:"custom_defconfig" form:"custom_defconfig"`
	CustomKernelConfig string `json:"custom_kernel_config" form:"custom_kernel_config"`
	BuildType          string `json:"build_type" form:"build_type"`   // 编译类型： debug, performance, factory
	BuildOspkg         bool   `json:"build_ospkg" form:"build_ospkg"` // bsp仓库可能需要make build-ospkg
	DirClean           bool   `json:"dir_clean" form:"dir_clean"`     // bsp仓库可能需要make build-ospkg
	KernelRepo         string `json:"kernel_repo" form:"kernel_repo"`
	KernelBranch       string `json:"kernel_branch" form:"kernel_branch"`
	Comment            string `json:"comment" form:"comment"`
}

var statusMap = map[int]string{
	0: "编译中",
	1: "编译完成",
	2: "编译失败",
	3: "排队中",
	4: "待审批",
}
