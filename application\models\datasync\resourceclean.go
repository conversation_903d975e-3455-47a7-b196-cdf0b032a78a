package datasync

import "irisAdminApi/application/models"

type ResourceClean struct {
	models.ModelBase
	WorkClassName     string  `gorm:"not null; type:varchar(100); index:idx_unique,unique" json:"work_class_name" xlsx:"类别"`
	ProjectName       string  `gorm:"not null; type:varchar(100); index:idx_unique,unique" json:"project_name" xlsx:"项目"`
	DepartmentName    string  `gorm:"not null; type:varchar(100)" json:"department_name" xlsx:"部门" update:"1"`
	GroupName         string  `gorm:"not null; type:varchar(100); index:idx_unique,unique" json:"group_name" xlsx:"专业组"`
	UserName          string  `gorm:"not null; type:varchar(100); index:idx_unique,unique" json:"user_name" xlsx:"工程师"`
	StageName         string  `gorm:"not null; type:varchar(100); index:idx_unique,unique" json:"stage_name" xlsx:"阶段"`
	Year              int     `gorm:"not null; index:idx_unique,unique" json:"year" xlsx:"年"`
	Season            int     `gorm:"not null; index:idx_unique,unique" json:"seaon" xlsx:"季"`
	Month             int     `gorm:"not null; index:idx_unique,unique" json:"month" xlsx:"月"`
	Week              string  `gorm:"not null; type:varchar(100); index:idx_unique,unique" json:"week" xlsx:"周"`
	ReportDate        string  `gorm:"not null; type:varchar(100); index:idx_unique,unique" json:"report_date" xlsx:"汇报日期"`
	WorkTime          float32 `gorm:"not null" json:"work_time" update:"1" xlsx:"工作时间"`
	AddTime           float32 `gorm:"not null" json:"add_time" update:"1" xlsx:"加班时间"`
	TotalTime         float32 `gorm:"not null" json:"total_time" update:"1" xlsx:"总计时间"`
	BugSubmittedCount int     `gorm:"not null" json:"bug_submitted_count" update:"1"`
	BugResolvedCount  int     `gorm:"not null" json:"bug_resolved_count" update:"1"`
	BugClosedCount    int     `gorm:"not null" json:"bug_closed_count" update:"1"`
	CodeAdd           float32 `gorm:"not null" json:"code_add" update:"1"`
	CodeDel           float32 `gorm:"not null" json:"code_del" update:"1"`
	CaseRunCount      int     `gorm:"not null" json:"case_run_count" update:"1"`
}
