<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<meta charset="utf-8">
	<meta name="author" content="">
	<meta name="copyright" content="">
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<title>提交BUG</title>
	<link rel="stylesheet" type="text/css" href="http://res.ruijie.com.cn:81/resources/easyui/easyui-1.5/css/jquery.easyui.min.css">
	<link rel="stylesheet" type="text/css" href="http://res.ruijie.com.cn:81/resources/common/icons/icons.common.css">
	<link rel="stylesheet" type="text/css" href="http://res.ruijie.com.cn:81/resources/common/icons/icons.extension.css">
	<link rel="stylesheet" type="text/css" href="http://res.ruijie.com.cn:81/resources/common/icons/easyui.common.css">
	<link rel="stylesheet" type="text/css" href="http://res.ruijie.com.cn:81/resources/notiflix/3.2.2/notiflix-3.2.2.min.css">
    <script type="text/javascript">
        var eu = eu || {};
        eu.basePath = '/bug_switch';
    </script>
	<script type="text/javascript" src="http://res.ruijie.com.cn:81/resources/jquery/v1.11.3/jquery.min.js"></script>
	<script type="text/javascript" src="http://res.ruijie.com.cn:81/resources/jquery/v1.11.3/jquery.tools.js"></script>
	<script type="text/javascript" src="http://res.ruijie.com.cn:81/resources/jquery/v1.11.3/jquery.validate.js"></script>
	<script type="text/javascript" src="http://res.ruijie.com.cn:81/resources/notiflix/3.2.2/notiflix-3.2.2.min.js"></script>
	<script type="text/javascript" src="http://res.ruijie.com.cn:81/resources/easyui/easyui-1.5/js/jquery.easyui.min.js"></script>
	<script type="text/javascript" src="http://res.ruijie.com.cn:81/resources/easyui/easyui-1.5/js/easyui-lang-zh_CN.js"></script>
	<script type="text/javascript" src="http://res.ruijie.com.cn:81/resources/easyui/easyui-1.5/js/datagrid-export.js"></script>
	<script type="text/javascript" src="http://res.ruijie.com.cn:81/resources/utils/common.utils.js"></script>
	<script type="text/javascript" src="http://res.ruijie.com.cn:81/resources/easyui/easyui-ex/easyui.extend.v1.0.3.js"></script>
	<script type="text/javascript" src="http://res.ruijie.com.cn:81/resources/easyui/easyui-ex/easyui.format.utils.v1.0.0.js"></script>
	<script type="text/javascript" src="/bug_switch/resources/pms/common/js/easyui.page.utils.v1.0.0.js"></script>
	<script type="text/javascript" src="/bug_switch/resources/pms/common/js/easyui.switchbug.js?v=5"></script>
	<!-- Matomo -->
	<script type="text/javascript">
        var _paq = window._paq = window._paq || [];
        /* tracker methods like "setCustomDimension" should be called before "trackPageView" */
        _paq.push(['trackPageView']);
        _paq.push(['enableLinkTracking']);
        (function() {
            var u="//matomo.ruijie.net/piwik/";
            _paq.push(['setTrackerUrl', u+'matomo.php']);
            _paq.push(['setSiteId', '9']);
            var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
            g.type='text/javascript'; g.async=true; g.src=u+'matomo.js'; s.parentNode.insertBefore(g,s);
        })();
	</script>
	<!-- End Matomo Code --></head>
<script type="text/javascript" src="/bug_switch/resources/ueditor/utf8-jsp/ueditor.config.js?v=1"></script>
<script type="text/javascript" src="/bug_switch/resources/ueditor/utf8-jsp/ueditor.all.js?v=2"></script>
<script type="text/javascript" src="/bug_switch/resources/common/layui/2.6.8/layui.js"></script>
<script type="text/javascript" src="/bug_switch/resources/pms/common/js/easyui.bug.extend.js?v=3"></script>
<script type="text/javascript" src="http://res.ruijie.com.cn:81/resources/utils/clipboard.min.js"></script>
<link rel="stylesheet" href="/bug_switch/resources/pms/common/css/buginfo.css" type="text/css" />
<link rel="stylesheet" href="/bug_switch/resources/common/layui/2.6.8/css/layui.css" media="all">
<style type="text/css">
    .displayfieldimg {cursor: pointer;width: 14px;height: 14px;margin-left: 5px;margin-bottom: 3px}
    .layui-form-label{width: 110px !important;line-height: 7px !important;font-size: 12px;color: black}
    .layui-input-inline{width: calc(100% - 140px)}
    .tips{cursor: pointer}
    input{padding: 2px}
    input::-webkit-input-placeholder{color: rgb(170, 170, 170);}
    .layui-input-block {min-height: 28px;}
    .layui-col-space5{padding-top: 5px !important;}
    .log-record li{list-style: decimal;}
    .log-record ol{padding-left: 15px}
    .loginfoTable td{border-bottom:white 3px solid; border-right:white 3px solid;font-size: 13px;padding: 5px;padding-left: 25px;padding-right: 15px;color: black}
    .loginfoTable .log-content{overflow: auto;line-height: 25px}
    .loginfoTable .l-btn-text{font-size: 13px;color: black}
    .loginfoTable img{max-width: 100%;}
    .layui-form-radio{line-height: 20px;}
    .layui-form-radio * {font-size: 12px}
    .layui-form-radio>i {font-size: 18px;}
    .layui-table td, .layui-table th {font-size: 12px;line-height: 22px;padding: 5px 10px;color: black}
    .layui-menu li {line-height: 20px;font-size: 13px;}
    .mask{z-index: 99999;position: fixed;top: 0;left: 0;bottom: 0;right: 0;opacity:0.7;background-color: #F8FAFF;background-image: url('/bug_switch/resources/pms/common/images/loading-1.gif');background-repeat: no-repeat;background-position: center;background-size: 75px 75px;}
</style><script type="text/javascript">
	var tabIndex = null;
	if(parent.__tabs != null){
		tabIndex = parent.__tabs.tabs("getTabIndex",parent.__tabs.tabs('getSelected'));
	}
	$(window).scroll(function(){
		//父页面值
		if ($(document).scrollTop() > 0 && tabIndex != null) {
			parent.scrollMap.set(tabIndex, $(document).scrollTop());
		}
	});
</script><body>
    <div class="mask"></div>
    <div id="floatDiv" align="center" style="position: fixed; buttom: 0px; right: 10px;top: 50%;z-index: 99999">
        <div><img src="/bug_switch/resources/pms/common/images/scrollTop.PNG"  style="cursor: pointer;" title="返回顶部" onclick="goTop()" /></div>
        <div><img src="/bug_switch/resources/pms/common/images/scrollDown.PNG" style="cursor: pointer;" title="到底部,查看最新纪录" onclick="goBottom()" /></div>
    </div>
    <div style="z-index:4000;height:35px;margin:0 auto;top:0;position:fixed;left:0;right:0;background: #bed2ec" class="ec-datagrid-toolbar">
        <div style="margin:5px;">
            <button onclick="sameAs('sameAs')" type="button" class="easyui-linkbutton" style="" data-options="iconCls: 'icon_copy',plain:true">SameAs</button>
            <button onclick="sameAs('clear')" type="button" class="easyui-linkbutton" style="display:none" data-options="iconCls: 'icon_copy',plain:true">清除SameAs:</button>
            <button id="mirrorBug" type="button" class="easyui-linkbutton" data-options="iconCls: 'icon_mirror',plain:true">BUG镜像</button>
            <button id="updateProgress" type="button" class="easyui-linkbutton" data-options="iconCls: 'icon_process-16',plain:true">进展更新</button>
            <button id="bugReview" type="button" class="easyui-linkbutton" data-options="iconCls: 'icon-withdraw-2',plain:true">三方评审</button>
            <button id="bugModule" type="button" class="easyui-linkbutton" data-options="iconCls: 'icon-location-1',plain:true">视图定位</button>
            <button id="cancelAttentionBug" onclick="attentionBug('cancel')" style="float: right;display:none" type="button" class="easyui-linkbutton" data-options="iconCls: 'icon_attention-1',plain:true">关注BUG</button>
            <button id="attentionBug" onclick="attentionBug('attention')" style="float: right;" type="button" class="easyui-linkbutton" data-options="iconCls: 'icon_attention',plain:true">关注BUG</button>
            <button onclick="window.location.reload();" style="float: right;" type="button" class="easyui-linkbutton" data-options="iconCls: 'icon-refresh-1',plain:true">刷新</button>
            <button id="copyLink" style="float: right" type="button" class="easyui-linkbutton" data-options="iconCls: 'icon_copy-1',plain:true">复制BUG链接</button>
            <button id="bugView" style="float: right" type="button" class="easyui-linkbutton" data-options="iconCls: 'icon-repeat-1',plain:true">BUG视图</button>
        </div>
    </div>
    <form id="inputForm" action="/bug_switch/bug/bug_info_save" method="post">
        <input id="id" name="id" type="hidden" value="1352718">
        <input id="lastupdatedate" name="lastupdatedate" type="hidden" value="2025-04-21 17:52:27">
        <fieldset class="layui-elem-field layui-field-title" style="margin-top: 40px;" id="base">
            <legend>
                <span>BUG基本信息：1352718</span>
                
            </legend>
        </fieldset>
        <div style="padding: 30px;padding-top: inherit">
            <div class="layui-row layui-col-space15">
                <div class="layui-panel">
<div class="layui-row layui-col-space5">
    <div class="layui-col-md4">
        <div class="grid-demo grid-demo-bg1">
            <label class="layui-form-label"><font>BUG负责人:</font></label>
            <div class="layui-input-inline">
                <input id="owner" name="owner" column="BUG负责人" placeholder="请选择BUG负责人"/>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>测试负责人:</font></label>
            <div class="layui-input-inline">
                <input id="testcharger" name="testcharger" placeholder="请选择测试负责人"/>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>BUG状态:</font></label>
            <div class="layui-input-inline">
                <input id="state" name="state"  column="BUG状态" style="width: 70%"/>
            </div>
        </div>
    </div>
</div>
<div class="layui-row layui-col-space5">
    <div class="layui-col-md4">
        <div class="grid-demo grid-demo-bg1">
            <label class="layui-form-label"><font>BUG归属人1:</font></label>
            <div class="layui-input-inline">
                <input id="belong" name="belong" column="BUG归属人1" placeholder="请选择BUG归属人1"/>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>审核人1:</font></label>
            <div class="layui-input-inline">
                <input id="checkowner" name="checkowner" class="checkowner" placeholder="请选择审核人1"/>
                <input id="isblgchecked" name="isblgchecked" value="1" class="isblgchecked" type="checkbox" style="margin-left: 10px">
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>PSTL:</font></label>
            <div class="layui-input-inline">
                <input id="pstl" name="pstl" placeholder="请选择PSTL"/>
            </div>
        </div>
    </div>
</div>
<div class="layui-row layui-col-space5">
    <div class="layui-col-md4">
        <div class="grid-demo grid-demo-bg1">
            <label class="layui-form-label"><font>BUG归属人2:</font></label>
            <div class="layui-input-inline">
                <input id="belong1" name="belong1" placeholder="请选择BUG归属人2"/>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>审核人2:</font></label>
            <div class="layui-input-inline">
                <input id="checkowner1" name="checkowner1" class="checkowner" placeholder="请选择审核人2"/>
                <input id="isblgchecked1" name="isblgchecked1" value="1" class="isblgchecked" type="checkbox" style="margin-left: 10px">
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>关键字:</font></label>
            <div class="layui-input-inline">
                <input id="keyWord" name="keyWord" class="easyui-textbox" data-options="value:'',prompt:'填写BUG标识信息，用于查询时筛选'" style="width: 70%"/>
            </div>
        </div>
    </div>
</div>
<div class="layui-row layui-col-space5">
    <div class="layui-col-md4">
        <div class="grid-demo grid-demo-bg1">
            <label class="layui-form-label"><font>BUG归属人3:</font></label>
            <div class="layui-input-inline">
                <input id="belong2" name="belong2" placeholder="请选择BUG归属人3"/>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>审核人3:</font></label>
            <div class="layui-input-inline">
                <input id="checkowner2" name="checkowner2" class="checkowner" placeholder="请选择审核人3"/>
                <input id="isblgchecked2" name="isblgchecked2" value="1" class="isblgchecked" type="checkbox" style="margin-left: 10px">
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>影响里程碑:</font></label>
            <div class="layui-input-inline">
                <input id="milestone" name="milestone" placeholder="请选择影响里程碑"/>
            </div>
        </div>
    </div>
</div>
<div class="layui-row layui-col-space5">
    <div class="layui-col-md4">
        <div class="grid-demo grid-demo-bg1">
            <label class="layui-form-label"><font>BUG归属人4:</font></label>
            <div class="layui-input-inline">
                <input id="belong3" name="belong3" placeholder="请选择BUG归属人4"/>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>审核人4:</font></label>
            <div class="layui-input-inline">
                <input id="checkowner3" name="checkowner3" class="checkowner" placeholder="请选择审核人4"/>
                <input id="isblgchecked3" name="isblgchecked3" value="1" class="isblgchecked" type="checkbox" style="margin-left: 10px">
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>资源消耗:</font></label>
            <div class="layui-input-inline">
                <input id="resourceConsumption" name="resourceConsumption" class="easyui-textbox" style="width: 70%"/>
            </div>
        </div>
    </div>
</div>
<div class="layui-row layui-col-space5">
    <label class="layui-form-label"><font>BUG简介:</font></label>
    <div class="layui-input-block">
        <input id="summary" name="summary" column="BUG简介"  class="easyui-textbox" style="width:91%"/>
    </div>
</div>
<div class="layui-row layui-col-space5">
    <label class="layui-form-label"><font>BUG当前处理人:</font></label>
    <div class="layui-input-block">
        <input id="dealUserIds" name="dealUserIds" placeholder="请选择BUG当前处理人"/>
    </div>
</div>
<div class="layui-row layui-col-space5">
    <label class="layui-form-label"><font>抄送人:</font></label>
    <div class="layui-input-block">
        <input id="ccUserIds" name="ccUserIds" placeholder="请选择BUG抄送人"/>
    </div>
</div>
<div class="layui-row layui-col-space5">
    <label class="layui-form-label"><font>附件:</font></label>
    <div class="layui-input-block">
        <a style="margin-top: -5px;" data-options="iconCls: 'icon_import-16',plain:false" class="easyui-linkbutton" onClick="importFile();">上传附件</a>
        <span id="addFileName" style="width:auto;line-height:30px;padding-left: 15px"></span>
        <input id="fileId"  name="uploadfile" type="hidden" />
    </div>
</div>
<div class="layui-row layui-col-space5">
    <label class="layui-form-label"><font>更改内容:</font></label>
    <div class="layui-input-block" style="width: 85%">
        <textarea id="changeMessage" name="changeMessage" style="display: none"></textarea>
        <textarea id="changeMessageContent" style="padding-left: 27px"></textarea>
    </div>
</div>
<div class="layui-row layui-col-space5 checkedView" style="display: none">
    <label class="layui-form-label">
        <span id="noticeStage" style="font-weight: bold;"></span>
    </label>
</div>
<div class="layui-row layui-col-space5 checkedView" style="color: red; display: none">
    <label class="layui-form-label"></label>
    <div class="layui-input-block">
        <div class="notifyTipPreview"></div>
        <div class="notifyTipSelf"></div>
        <div class="notifyTipSource"></div>
    </div>
</div>
<div class="layui-row layui-col-space5 checkedView" style="display: none">
    <label class="layui-form-label"><label id="bugNotifyLabel">发送BUG通告:</label></label>
    <div class="layui-input-block">
        <input id="bugNotify" name="bugNotify" column="发送BUG通告" class="independent"  placeholder="请选择"/>
    </div>
</div>
<div class="layui-row layui-col-space5 checkedView" style="display: none">
    <label class="layui-form-label">判断BUG通告理由:</label>
    <div class="layui-input-block">
        <textarea id="notifyReason" name="notifyReason" column="判断BUG通告理由"
                  class="c-textarea-content-edit independent" style="width:91%;resize: vertical" rows="3"></textarea>
    </div>
</div>
<div class="layui-row layui-col-space5 resolveView" style="display: none">
    <label class="layui-form-label">分析过程:</label>
    <div class="layui-input-block">
        <textarea id="resolvedAnalyse" name="resolvedAnalyse" column="分析过程" class="c-textarea-content-edit" style="width:91%;resize: vertical" rows="3"></textarea>
    </div>
</div>
<div class="layui-row layui-col-space5 resolveView" style="display: none">
    <label class="layui-form-label">解决方案:</label>
    <div class="layui-input-block">
        <textarea id="resolvedSolution" name="resolvedSolution" column="解决方案" class="c-textarea-content-edit" style="width:91%;resize: vertical" rows="3"></textarea>
    </div>
</div>
<div class="layui-row layui-col-space5 resolveView" style="display: none">
    <label class="layui-form-label">验证解决方案:</label>
    <div class="layui-input-block">
        <textarea id="resolvedVerification" name="resolvedVerification" column="验证解决方案" class="c-textarea-content-edit" style="width:91%;resize: vertical" rows="3"></textarea>
    </div>
</div>
<div class="layui-row layui-col-space5 resolveView" style="display: none">
    <label class="layui-form-label">对其他模块影响:</label>
    <div class="layui-input-block">
        <textarea id="resolvedModuleAffect" name="resolvedModuleAffect" column="对其他模块影响" class="c-textarea-content-edit" style="width:91%;resize: vertical" rows="3"></textarea>
    </div>
</div>
<div class="layui-row layui-col-space5 resolveView" style="display: none">
    <label class="layui-form-label">对其他产品影响:</label>
    <div class="layui-input-block">
        <textarea id="resolvedProductAffect" name="resolvedProductAffect" column="对其他产品影响" class="c-textarea-content-edit" style="width:91%;resize: vertical" rows="3"></textarea>
    </div>
</div>
<script type="text/javascript">
    var changeMessage;
    var stateOld = '13';

    /**
     * 根据ID刷新必填显示
     * @param id
     */
    function refreshRequired(id){
        let $item = $("#"+id)
        if($item.hasClass("required")){
            $item.closest('div').prev().each(function (){
                $(this).find("span").remove();
                var content = "<span class='star' style='color: red;margin-right: 2px'> *</span>" + $(this).html();
                $(this).html(content);
            })
        }else {
            $item.closest('div').prev().each(function (){
                $(this).find("span").remove();
            })
        }
    }

    function removeRequired(id){
        $("#" + id).removeClass('required');
        refreshRequired(id);
    }

    /**
     * 检查是否需要遗留类型的输入
     */
    function checkLegacyTypeRequired() {
        // 获取选择的遗留 ID
        let legacy = $("#savebugid").commonchooser("getValue");
        // 获取当前状态的值
        let state = $("#state").combobox('getValue');
        // 初始化 req 为 false，表示是否需要遗留类型输入
        let req = false;

        // 根据状态判断是否为遗留 BUG
        if (state == '9' || state == '5') {
            // 检查遗留 ID 是否为 '2'
            if (legacy == '2') {
                req = true; // 设置 req 为 true，表示需要遗留类型输入
            }
        }


    }

    /**
     * 初始化 Bug 通知下拉框
     */
    function initBugNotify() {
        // 初始化 combobox 组件
        $('#bugNotify').combobox({
            // 设置下拉框的值字段
            valueField: 'value',
            // 设置下拉框的文本字段
            textField: 'text',
            // 设置数据源 URL，用于获取下拉框选项
            url: '/bug_switch/bug_notice_record/notify_list',
            //设置下拉框高度
            panelHeight: 50,
            // 当选项变化时触发的事件处理函数
            onChange: function (newV, oldV) {
                if(newV != null && newV != ''){
                    if (newV == 1) {
                        let noticeId = '145979'
                        if(noticeId){
                            Notiflix.Notify.warning('当前已存在BUG通告ID,将不会重复发布新通告（若当前BUG通告已废弃，将会被还原）');
                        }
                        // 移除必填类，表示不再需要理由
                        $('#notifyReason').removeClass('required');
                    } else {
                        // 否则，添加必填类，表示需要理由
                        $('#notifyReason').addClass('required');
                    }
                    // 刷新 notifyReason 的必填状态
                    refreshRequired('notifyReason');
                }
            },
            onLoadSuccess: function (data) {
                storeNotifyData()
            }
        });
    }

    /**
     * 回填数据
     */
    function storeNotifyData(){
        let preStage = null
        if(noticeStagePre()){
            preStage = true
        }else if (noticeStageReCheck()) {
            preStage = false
        }else {
            return
        }

        postAjax(
            '/bug_switch/bug_notice_record/load_last_info',
            {
                bugId: '1352718',
                userId: '26293',
                preStage,
            },
            function (rs) {
                if(rs.data){
                    let record = rs.data
                    $('#bugNotify').combobox('setValue', record.notify);
                    $('#notifyReason').val(record.notifyReason);
                }
            }
        )
    }

    function initBugInfoChooser(){
        $("#owner").userPermissionChooser({inputWidth:'70%'});
        $("#belong").userPermissionChooser({inputWidth:'70%'});
        $("#belong1").userPermissionChooser({inputWidth:'70%'});
        $("#belong2").userPermissionChooser({inputWidth:'70%'});
        $("#belong3").userPermissionChooser({inputWidth:'70%'});
        $("#testcharger").userPermissionChooser({inputWidth:'70%'});
        $("#checkowner").userPermissionChooser(
            {
                inputWidth:'60%',
                onAfterAppend: function (row) {}
            }
        )
        $("#checkowner1").userPermissionChooser(
            {
                inputWidth:'60%',
                onAfterAppend: function (row) {}
            }
        )
        $("#checkowner2").userPermissionChooser(
            {
                inputWidth:'60%',
                onAfterAppend: function (row) {}
            }
        )
        $("#checkowner3").userPermissionChooser(
            {
                inputWidth:'60%',
                onAfterAppend: function (row) {}
            }
        )
        $("#pstl").userPermissionChooser({inputWidth:'70%'});
        $("#ccUserIds").userPermissionChooser({multiple:true, inputWidth:'155px'});
        $("#dealUserIds").userPermissionChooser({multiple:true, inputWidth:'155px'});
        changeMessage = UE.getEditor('changeMessageContent',{
            initialFrameHeight:200,
            wordCount:false
        });
        $.initDictionary({ id : 'milestone', dropdownHeight : 270, inputWidth : '70%' });
        $('#state').combobox({
            editable: false,
            valueField: 'stateid',
            textField: 'statename',
            url:'/bug_switch/bug/bug_state/load_bug_state_list',
        })
    }
    function initBugInfoData(){
            $("#owner").userPermissionChooser("setValue",  {ID:26052,username:'郑江宏'});
            $("#belong").userPermissionChooser("setValue", {ID:26052,username:'郑江宏'});
            $("#testcharger").userPermissionChooser("setValue",{ID:19369,username:'纪姮'});
            $("#checkowner").userPermissionChooser("setValue",{ID:29183,username:'谢臻杰'});
            $("#pstl").userPermissionChooser("setValue",{ID:26052,username:'郑江宏'});
            var ccUserArr = [];
                var ccUserObj = {ID:28131,username:'陈友福'}
                ccUserArr.push(ccUserObj);
                var ccUserObj = {ID:24820,username:'林国富'}
                ccUserArr.push(ccUserObj);
            if(ccUserArr != null && ccUserArr.length > 0){
                $("#ccUserIds").userPermissionChooser("setValues",ccUserArr);
            }
            var dealUserArr = [];
                var dealUserObj = {ID:19369,username:'纪姮'}
                dealUserArr.push(dealUserObj);
            if(dealUserArr != null && dealUserArr.length > 0){
                $("#dealUserIds").userPermissionChooser("setValues", dealUserArr);
            }
            $("#isblgchecked").attr("checked","true");
            $("#checkowner").userPermissionChooser("setDisabled", true);
        changeMessage.ready(function() {
            changeMessage.setContent("");
        });
        $("#state").combobox("setValue","13");
        $("#summary").textbox("setValue","【市场问题】部分云南公网ip无法正常识别为云南省地区对象，导致策略匹配不中");
        $.initDictionary({ id : 'priority', dropdownHeight : '150', inputWidth : '70%'});
        initAccessory();
    }

    function initAccessory(){
        var param = {
            bugId : '1352718'
        };
        var url = "/bug_switch/accessory_link/load_bug_accessory_list";
        postAjax(url, param, function (rows){
            var fileIds = '';
            for(var i=0; i < rows.length; i++) {
                var migFileSysId = rows[i].migFileSysId;
                fileIds = fileIds + "," + rows[i].id;
                if(migFileSysId != null && migFileSysId != ''){
                    var url = "<div style='display: inline-block;padding-left: 25px'><a href='javascript:downloadFile(\""+ migFileSysId +"\")'>"+ rows[i].file_name +"</a><a onclick='removeFile(this,"+ rows[i].id +")'><img style='' title='点击移除' src='/bug_switch/resources/pms/common/images/btn_delete.png' class='displayfieldimg'></a></div>";
                }else{
                    var url = "<div style='display: inline-block;padding-left: 25px'><a target='top' href='http://accessory.ruijie.net:8080/accessory_server/servlet/downloadAccessory?systemFileName="+ rows[i].system_name +"'>"+ rows[i].file_name +"</a><a onclick='removeFile(this,"+ rows[i].id +")'><img style='' title='点击移除' src='/bug_switch/resources/pms/common/images/btn_delete.png' class='displayfieldimg'></a></div>";
                }
                finishImportFile(url);
            }
            $("#fileId").val(fileIds);
        })
    }

    $('#resourceConsumption').textbox({
        onChange: function(value) {
            if(value != null && value != ''){
                var resourceConsumption = $("#resourceConsumption").textbox("getValue");
                if(!/^\d+([.]{1}[0-9]+)?[w|W|d|D|h|H|m|M]?$/.test(resourceConsumption)){
                    Notiflix.Notify.info('【资源耗时】填写有误,请填写正确的格式');
                    $("#resourceConsumption").textbox("setValue","");
                }
            }
        }
    });

    /**
     * 这样设置不会触发onChange方法
     * 从而阻止循环触发
     * @param value
     */
    function setStateValue(value){
        $("#state").combobox(
            {
                value
            }
        )
    }

    /**
     * 真正改变状态处理方法
     * @param newValue
     * @param initContent
     */
    function handleRealStateChange(newValue,initContent){
        setStateValue(newValue)
        storeTpl (newValue,initContent)

        judgeNotifyShow()
    }

    /**
     * 处理是否真的需要改变状态
     * @param newValue
     */
    function checkChangeState(newValue){
        //CHECKED
        if(newValue == '14'){
            if(stateOld != '5'){
                Notiflix.Notify.info('把BUG设置为CHECK状态，必须先把BUG置于RESOLVED状态');
                return;
            }
            var flag = true;
            let msg = '';
            $(".checkowner").each(function (index){
                var checkowner = $(this).userPermissionChooser("getValue");
                if(checkowner != null){
                    flag = false;
                    if(!$(this).parent().parent().siblings("input").is(':checked')){
                        msg = '审核人'+ (index + 1) +'还未审核通过,无法设置为CHECK状态'
                    }
                }
            })
            if(msg){
                Notiflix.Notify.info(msg);
                return;
            }
            if(flag){
                Notiflix.Notify.info('还未审核通过,无法设置为CHECK状态');
                return;
            }
            handleRealStateChange(newValue)
        }
        //CBD
        else if(newValue == '13'){
            if(stateOld != '14'){
                Notiflix.Notify.info('把BUG设置为CLOSED-ByDevelopment状态，必须先把BUG置于CHECK状态');
                return;
            }
            var savebugid = $("#savebugid").commonchooser("getValue");
            var wbsName = $("#wbs").workpacketchooser("getInputText");
            var wbssaveid = wbsName.indexOf("遗留或delay的bug") != -1 ? '2' : '1';
            if(savebugid != wbssaveid){
                let content = "当前【工作包】的遗留状态与【遗留BUG】状态不一致，请开发与测试达成一致后，再将BUG状态变更为CBD!"
                    + "<br><font color='red'>* 如对规则有疑问请联系【马晓靖】</font>"
                    + "<br>*【遗留BUG】只能由【测试负责人】修订"
                    + "<br>*【遗留BUG】的【工作包】应设置为【遗留或delay的bug】";
                Notiflix.Report.warning('数据校验未通过', content, '确定');
                return;
            }

            let initContent = '验证方法:<br/><br/>测试结果:<br/><br/>相关测试:<br/><br/>验证版本:<br/><br/>提交日期:';
            handleRealStateChange(newValue,initContent)
        }
        //CLOSED-ByTest
        else if(newValue == '7'){
            if(stateOld != '13'){
                Notiflix.Notify.info('把BUG设置为CLOSED-ByTest状态，必须先把BUG置于CLOSE-ByDevelopment状态');
                return;
            }
            var initContent = '验证方法:<br/><br/>测试结果:<br/><br/>相关测试:<br/><br/>验证版本:<br/><br/>提交日期:<br/><br/>受此bug影响的用例执行确认:';
            handleRealStateChange(newValue,initContent)
        }
        //DENIAL-ByDevelopment
        else if(newValue == '8'){
            handleRealStateChange(newValue)
        }
        //GIVEUP
        else if(newValue == '22'){
            var html = 'BUG状态变更为 GIVEUP 需要发起三方评审, 如无需强制评审, 请联系PM进行配置, 是否前往发起三方评审?';
            Notiflix.Confirm.show('状态变更', html, '确定', '稍后评审',
                function(){
                    bugReview('FORM_REVIEW_GIVEUP');
                },
                function (){
                }
            );
        }
        //DELAY
        else if(newValue == '9'){

            var html = 'BUG状态变更为 DELAY 需要发起三方评审, 如无需强制评审, 请联系PM进行配置, 是否前往发起三方评审?';
            Notiflix.Confirm.show('状态变更', html, '确定', '稍后评审',
                function(){
                    bugReview('FORM_REVIEW_DELAY');
                },
                function (){
                }
            );
        }
        //RESOLVED
       else if(newValue == '5'){
            Notiflix.Confirm.show(
                '状态变更',
                '重新修改成 RESOLVED 将会清空评审结果，是否要继续?',
                '确定',
                '取消',
                function() {
                    $(".isblgchecked").removeAttr('checked');
                    $(".resolveView").css("display","");

                    handleRealStateChange(newValue)
                },
                function (){}
            );
        }else {
            handleRealStateChange(newValue)
        }
        if(newValue != '5'){
            $(".resolveView").css("display","none");
        }

    }


    const STAGE_NOTIFY_PRE = '初步判断:'
    const STAGE_NOTIFY_CHECKED = 'CHECKED审核:'
    /**
     * 是否是bug通告初步判断
     */
    function noticeStagePre(){
        let state = $("#state").combobox("getValue");
        return state == STATE_RESOLVED && stateOld != STATE_RESOLVED
    }
    /**
     * 是否是bug通告复核
     */
    function noticeStageReCheck(){
        let state = $("#state").combobox("getValue");
        return stateOld == STATE_RESOLVED && (state == STATE_CHECKED || state == STATE_RESOLVED)
    }

    /**
     * 判断是否显示bug通告
     */
    function judgeNotifyShow(){
        let stage = null

        if (checkLegacy()) {
            /**
             * 这里状态包含两个类型
             * 1.其他转resolved时(bug通告初步判断时期）
             * 2.resolved不变时和resovled转checked时(bug通告审核时期）
             */
            if(noticeStagePre()){
                stage = STAGE_NOTIFY_PRE
            }else if (noticeStageReCheck()) {
                let isChecker = judgeIsChecker()
                if(isChecker){
                    stage = STAGE_NOTIFY_CHECKED
                }
            }

        }

        if(stage){
            openBugNotify(stage)
        }else {
            closeBugNotify()
        }
    }

    $("#state").combobox({
        onChange:function (newValue, oldValue){
            if(stateOld != newValue){
                setStateValue(oldValue)
                checkChangeState(newValue)
            }
            setBugInfoRequired(newValue);
            judgeNotifyShow()
        }
    })

    /**
     *
     * 设置字段模板
     * @param state bug状态
     */
    function storeTpl(state,initContent){
        let osId = $("#os").projectchooser('getValue')
        $.ajax({
            url: '/bug_switch/bug_field_tpl/bug_field_tpl_query_os',
            type: 'GET',
            data: {
                'state': state,
                'osId': osId
            },
            success: function (rs) {
                //如果状态已经还原了，就不需要去设置
                if (state === stateOld) {
                    return
                }
                if(rs.data&&rs.data.length>0){
                    let data = rs.data[0]
                    let txt = data.changeMessage
                    if(!changeMessage.getContent()&&txt){
                        let htmlContent = txt.replace(/\n/g, '<br>');
                        changeMessage.setContent(htmlContent);
                    }
                    let repairInfluence = $('#repairInfluence').val()
                    if(!repairInfluence&&data.repairInfluence){
                        $('#repairInfluence').val(data.repairInfluence)
                    }

                    let testVerification = $('#testVerification').val()
                    if(!testVerification&&data.testVerification){

                        $('#testVerification').val(data.testVerification)
                    }
                    let locationResult = $('#locationResult').val()
                    if(!locationResult&&data.locationResult){
                        $('#locationResult').val(data.locationResult)
                    }
                }else {
                    if(initContent){
                        changeMessage.setContent(initContent);
                    }
                }


            }
        })

    }
    //BUG状态必填字段配置
    var stateRequireObj = {
        //全状态必填字段
        '0' : ['owner', 'belong', 'state', 'os'],
        //RESOLVED必填字段
        '5' : ['wbs', 'effectbug', 'closeproperty', 'resolvedok', 'savebugid', 'repairOrder', 'repairInfluence', 'testVerification', 'needCollect', 'locationResult'],
        //CHECKED必填字段
        '14' : ['wbs', 'effectbug', 'leibie', 'needCollect', 'isRevise'],
        //CLOSED-ByDevelopment必填字段
        '13' : ['effectbug', 'needCollect', 'resolvedVersion'],
        //CLOSED-ByTest必填字段
        '7' : ['effectbug', 'needCollect', 'location'],
        //VERIFIED必填字段
        '6' : ['locationResult'],
    };

    function setBugInfoRequired(state){
        $(".required").each(function (){
            if(!$(this).hasClass('independent')){
                $(this).removeClass("required");
            }
        })
        $(".star").remove();
        var baseArr = stateRequireObj['0'];
        var columnArr = baseArr.concat(stateRequireObj[state]);
        for(var i=0; i < columnArr.length; i++){
            var disabled = $("#" + columnArr[i]).attr("disabled");
            if(disabled == null || disabled == false){
                $("#" + columnArr[i]).addClass("required");
            }
        }
        if(state == 5 && stateOld != 5){
            $("#resolvedAnalyse").addClass("required");
            $("#resolvedSolution").addClass("required");
            $("#resolvedVerification").addClass("required");
            $("#resolvedModuleAffect").addClass("required");
            $("#resolvedProductAffect").addClass("required");
        }

        $(".required").closest('div').prev().each(function (){
            $(this).find("span").remove();
            var content = "<span class='star' style='color: red;margin-right: 2px'> *</span>" + $(this).html();
            $(this).html(content);
        })
    }
    var map = {
        isblgchecked  : 'checkowner',
        isblgchecked1 : 'checkowner1',
        isblgchecked2 : 'checkowner2',
        isblgchecked3 : 'checkowner3'
    };
    var dataMap = {
        isblgchecked  : '1',
        isblgchecked1 : '',
        isblgchecked2 : '',
        isblgchecked3 : ''
    };
    $(".isblgchecked").on('change',function (){
        var id = map[$(this).attr("id")];
        var userId = $("#" + id).userPermissionChooser("getValue");
        var flag = false;
        if(userId == null || userId == ''){
            Notiflix.Notify.info('请先配置该审核人');
            flag = true;
        }
        if(!flag && userId != '26293'){
            Notiflix.Notify.info('您不是审核人，无权审核');
            flag = true;
        }
        if(flag){
            var result = dataMap[$(this).attr("id")];
            if(result == '1'){
                $(this).prop("checked",true);
            }else{
                $(this).removeAttr("checked");
            }
            return;
        }
        if($(this).is(':checked')){
            $("#" + id).userPermissionChooser("setDisabled", true);
        }else{

            $("#" + id).userPermissionChooser("setDisabled", false);
        }

        dataMap[$(this).attr("id")] = $(this).is(':checked') ? 1 : 0;

        judgeNotifyShow()
    })

    function openBugNotify(stage){
        let req = $('#bugNotify').hasClass('required')
        if (!req) {
            $('#noticeStage').html(stage)
            $('.checkedView').show()
            $('#bugNotify').addClass('required');
            // 刷新 bugNotify 的必填状态
            refreshRequired('bugNotify');
            initBugNotify()
            setTimeout(function (){
                showTipCustom('bugNotifyLabel')
            },500)

            $('#bugNotifyLabel').on('click',function (){
                showTipCustom('bugNotifyLabel')
            })
        }
    }

    function closeBugNotify(){
        if ($("#bugNotify").hasClass('required')) {
            $('.checkedView').hide()
            removeRequired('bugNotify');
            removeRequired('notifyReason');
            $("#bugNotify").combobox('setValue','')
            $('#notifyReason').val('')
        }
    }
    function findCheckUserByCheckId(checkId){
        let id = map[checkId];
        return $("#" + id).userPermissionChooser("getValue")
    }
    function checkLegacy(){
        let legacy = $("#savebugid").commonchooser('getValue');
        return legacy == '2';

    }

    /**
     * 检查当前用户是否是已勾选的审核人
     * @returns {boolean}
     */
    function judgeIsChecker(){
        for (let checkId in dataMap) {
            if (dataMap[checkId] === 1) {
                let userId = findCheckUserByCheckId(checkId);
                if (userId == '26293') {
                    return true
                }
            }
        }
        return false
    }
</script>                </div>
            </div>
        </div>


        <fieldset class="layui-elem-field layui-field-title" id="test">
            <legend style="font: bold 15px tahoma, arial, helvetica, sans-serif;color: #15428b;">测试人员关注视图</legend>
        </fieldset>
        <div style="padding: 30px;padding-top: inherit">
            <div class="layui-row layui-col-space15">
                <div class="layui-panel">
<div class="layui-row layui-col-space5">
    <div class="layui-col-md4">
        <div class="grid-demo grid-demo-bg1">
            <label class="layui-form-label"><font>测试方法:</font></label>
            <div class="layui-input-inline">
                <input id="testmethod" name="testmethod" column="测试方法" placeholder="请选择测试方法"/>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>测试用例:</font></label>
            <div class="layui-input-inline">
                <input id="platform" name="platform" column="测试用例" placeholder="请选择测试用例"/>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>可重复性:</font></label>
            <div class="layui-input-inline">
                <input id="repro" name="repro" column="可重复性" placeholder="请选择可重复性"/>
            </div>
        </div>
    </div>
</div>
<div class="layui-row layui-col-space5">
    <div class="layui-col-md4">
        <div class="grid-demo grid-demo-bg1">
            <label class="layui-form-label"><font>产品名称:</font></label>
            <div class="layui-input-inline">
                <input id="product" name="product" column="产品名称" placeholder="请选择产品名称"/>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>来源:</font></label>
            <div class="layui-input-inline">
                <input id="comefrom" name="comefrom" column="来源"/>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>严重性:</font></label>
            <div class="layui-input-inline">
                <input id="priority" name="priority" column="严重性" placeholder="请选择严重性"/>
            </div>
        </div>
    </div>
</div>
<div class="layui-row layui-col-space5">
    <div class="layui-col-md4">
        <div class="grid-demo grid-demo-bg1">
            <label class="layui-form-label"><font>操作系统:</font></label>
            <div class="layui-input-inline">
                <input id="os" name="os" column="操作系统"/>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>专项:</font></label>
            <div class="layui-input-inline">
                <input id="special" name="special" class="easyui-textbox" style="width: 70%" data-options="prompt:'请填写专项'"/>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label" title=""><font>优先级:</font></label>
            <div class="layui-input-inline">
                <input id="severity" name="severity" column="优先级" placeholder="请选择优先级"/>
            </div>
        </div>
    </div>
</div>
<div class="layui-row layui-col-space5">
    <div class="layui-col-md4">
        <div class="grid-demo grid-demo-bg1">
            <label class="layui-form-label"><font>测试用例编号:</font></label>
            <div class="layui-input-inline">
                <input id="caseno" name="caseno" class="easyui-textbox" style="width: 70%" data-options="prompt:'请填写测试用例编号'"/>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>是否一次定位:</font></label>
            <div class="layui-input-inline">
                <input id="location" name="location" column="是否一次定位" placeholder="请选择是否一次定位"/>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>定位时间(min):</font></label>
            <div class="layui-input-inline">
                <input id="locationTime" name="locationTime" value="" data-options=" prompt:'请填写定位时间'" class="easyui-textbox" style="width: 70%"/>
            </div>
        </div>
    </div>
</div>
<div class="layui-row layui-col-space5">
    <div class="layui-col-md4">
        <div class="grid-demo grid-demo-bg1">
            <label class="layui-form-label"><font>影响用例执行数:</font></label>
            <div class="layui-input-inline">
                <input id="affectCaseCount" name="affectCaseCount" style="width: 70%" data-options="value:'0',prompt:'请填写0或0以上数字'" class="easyui-textbox"/>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>关联操作系统:</font></label>
            <div class="layui-input-inline">
                <input id="osSpare" name="osSpare" placeholder="请选择关联操作系统"/>
            </div>
        </div>
    </div>
</div>

<div class="layui-row layui-col-space5">
    <div class="layui-col-md4">
        <div class="grid-demo grid-demo-bg1">
            <label class="layui-form-label"><font>遗留BUG:</font></label>
            <div class="layui-input-inline" onclick="showLegacyTip()">
                <input id="savebugid" name="savebugid" column="遗留BUG" placeholder="请选择遗留BUG"/>
            </div>
        </div>
    </div>
    <div class="layui-col-md4" >
        <div id="legacyTypeWrap" >
            <label class="layui-form-label"><font>遗留类型:</font></label>
            <div class="layui-input-inline" >
                <input id="legacyType" name="legacyType" class="independent" column="遗留类型" placeholder="请选择遗留类型"/>
            </div>
        </div>
    </div>
</div>
<div class="layui-row layui-col-space5">
    <label class="layui-form-label"><font>使用定位手册:</font></label>
    <div class="layui-input-block">
        <input id="userGuide" name="userGuide" data-options="prompt:'用于描述当前BUG是否使用了以下链接中的定位手册进行定位，如果使用，将手册url链接地址填入'" column="使用定位手册" class="easyui-textbox" style="width:84%;"/>
        <button type="button" onclick="openGuideAddr();" class="layui-btn layui-btn-sm layui-btn-primary" style="height: 26px;line-height: 26px;border-radius: 5px 5px 5px 5px;">前往查找手册</button>
    </div>
</div>
<script type="text/javascript">
    function showLegacyTip(){
    }

    function initTestChooser(){
        $("#product").productchooser({ inputWidth : '70%' });
        $("#platform").casepacketchooser({ inputWidth : '70%' });
        $("#osSpare").projectchooser({ inputWidth : '70%',
            onAfterAppend: function (row) {
                if (row != undefined && row != null) {
                    $("#wbs").workpacketchooser('getSelectBy',{osSpareIdArr : row.ID});
                }
            }
        });
        $("#os").projectchooser({
             disabled : true, 
            inputWidth : '70%',
            onAfterAppend: function (row) {
                if (row != undefined && row != null) {
                    $("#wbs").workpacketchooser('getSelectBy', {osIdArr : row.ID});
                    $("#effectbug").introducedchooser('getSelectBy', {category : row.bug_category});
                }
            }
        });
        $('#testmethod').testmethodchooser({ inputWidth : '70%' });
        $('#comefrom').bugsourcechooser({inputWidth : '70%'});
        $.initDictionary({ id : 'repro', inputWidth : '70%' });
        $.initDictionary({ id : 'priority', dropdownHeight : '150', inputWidth : '70%'});
        $.initDictionary({ id : 'severity', dropdownHeight : '100', inputWidth : '70%' });
        $.initDictionary({ id : 'location', dropdownHeight : '100', inputWidth : '70%'});
        $.initDictionary({ id : 'savebugid', dropdownHeight : '100', inputWidth : '70%',
                disabled: true,
            onAfterAppend: function (row) {
                var state = $("#state").combobox("getValue");
                if(row.dictText == '是'){
                    setBugInfoRequired(state);
                    $('#legacyTypeWrap').css("display","");
                    $('#legacyType').addClass('required');
                    // 刷新遗留类型的必填状态
                    refreshRequired('legacyType');
                }else{
                    setBugInfoRequired(state);
                    $('#legacyTypeWrap').css("display","none");
                    removeRequired('legacyType');
                    $("#legacyType").commonchooser('setValue',{dictData: '',dictText:''});
                }

                var workPacketName = $("#wbs").workpacketchooser("getInputText");
                if(workPacketName != null && workPacketName != ''){
                    var wbssaveid = workPacketName.indexOf("遗留或delay的bug") != -1 ? '2' : '1';
                    if(row.dictData != wbssaveid){
                        layer.tips("当前[工作包]的遗留状态与[遗留BUG]状态不一致，请确认！", "#savebugid");
                    }
                }
                judgeNotifyShow()
            }
        });

        $.initDictionary({
            id : 'legacyType',
            dropdownHeight : '100',
            inputWidth : '70%',
        });
        initNumberTextBox('locationTime', '定位时间', function (value){});
        initNumberTextBox('affectCaseCount', '影响用例执行数');
    }

    function initTestData(){
            $("#platform").casepacketchooser('setValue',{ID:13051,platform:'NTOS-地区对象-GN-TP'});
            $("#product").productchooser('setValue',{ID:19963,productName:'RG-NBR-N7204-E V1.00'});
            $("#os").projectchooser('setValue',{ID:14045,os:'NTOS1.0R10P7'});
            $("#testmethod").testmethodchooser('setValue',{id:24,testmethodcn:'易用性测试'});
            $("#repro").commonchooser('setValue',{dictData:1,dictText:'必现'});
            var comefromObj = {
                id : 54,
                source_name : '测试内测（功能测试）'
            };
            $("#comefrom").bugsourcechooser('setValue', comefromObj);
        $("#priority").commonchooser('setValue',{dictData:5,dictText:'Major'});
        $("#severity").commonchooser('setValue',{dictData:5,dictText:'Normal'});
        $("#special").textbox('setValue','');
        $("#caseno").textbox('setValue','NTOS-web认证-710');
        $("#userGuide").textbox('setValue','');
        $("#savebugid").commonchooser('setValue',{dictData:2,dictText:'是'});

        $("#legacyType").commonchooser('setValue',{dictData:1,dictText:'版本类遗留'});
    }
</script>                </div>
            </div>
        </div>
        <fieldset class="layui-elem-field layui-field-title" id="develop">
            <legend style="font: bold 15px tahoma, arial, helvetica, sans-serif;color: #15428b;">开发人员视图</legend>
        </fieldset>
        <div class="layui-bg-gray" style="padding: 30px;padding-top: inherit">
            <div class="layui-row layui-col-space15">
                <div class="layui-panel">
<div class="layui-row layui-col-space5">
    <div class="layui-col-md4">
        <div class="grid-demo grid-demo-bg1">
            <label class="layui-form-label"><font>工作包:</font></label>
            <div class="layui-input-inline">
                <input id="wbs" name="wbs" column="工作包" placeholder="请选择工作包"/>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>是否修订引入:</font></label>
            <div class="layui-input-inline">
                <input id="isRevise" name="isRevise" column="是否修订引入" placeholder="请选择是否修订引入"/>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>功能模块:</font></label>
            <div class="layui-input-inline">
                <input id="module" name="module" style="width: 70%"/>
            </div>
        </div>
    </div>
</div>
<div class="layui-row layui-col-space5">
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>BUG引入状态:</font></label>
            <div class="layui-input-inline">
                <input id="effectbug" name="effectbug" column="BUG引入状态" placeholder="请选择BUG引入状态"/>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>BUG类别:</font></label>
            <div class="layui-input-inline">
                <input id="leibie" name="leibie" column="BUG类别" placeholder="请选择BUG类别" style="width: 70%"/>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>热补丁修复:</font></label>
            <div class="layui-input-inline">
                <input id="hotfix" name="hotfix" column="热补丁修复" placeholder="请选择热补丁修复"/>
            </div>
        </div>
    </div>
</div>
<div class="layui-row layui-col-space5">
    <div class="layui-col-md4">
        <div class="grid-demo grid-demo-bg1">
            <label class="layui-form-label"><font>解决方式:</font></label>
            <div class="layui-input-inline">
                <input id="closeproperty" name="closeproperty" column="解决方式" placeholder="请选择解决方式"/>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>解决Revision:</font></label>
            <div class="layui-input-inline">
                <input id="resolvedVersion" name="resolvedVersion" column="解决Revision" data-options="prompt:'请填写解决Revision'" style="width: 70%" class="easyui-textbox"/>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>是否用户接口修订:</font></label>
            <div class="layui-input-inline">
                <input id="resolvedok" name="resolvedok" column="是否用户接口修订" placeholder="请选择是否用户接口修订"/>
            </div>
        </div>
    </div>
</div>
<div class="layui-row layui-col-space5">
    <div class="layui-col-md4">
        <div class="grid-demo grid-demo-bg1">
            <label class="layui-form-label"><font>是否项目遗留:</font></label>
            <div class="layui-input-inline">
                <input id="developIsDelay" name="developIsDelay" column="是否项目遗留" placeholder="请选择是否项目遗留"/>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>Delay到版本:</font></label>
            <div class="layui-input-inline">
                <input id="delayid" name="delayid" placeholder="请选择Delay到版本"/>
            </div>
        </div>
    </div>

</div>
<div class="layui-row layui-col-space5">
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>修订量:</font></label>
            <div class="layui-input-inline">
                <input id="repairOrder" name="repairOrder" column="修订量" data-options="value:'3', prompt:'请填写修订量'" style="width: 70%" class="easyui-textbox"/>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="grid-demo">
            <label class="layui-form-label"><font>使用一键收集信息:</font></label>
            <div class="layui-input-inline">
                <input id="needCollect" name="needCollect" column="是否使用一键收集信息" placeholder="请选择是否使用一键收集"/>
            </div>
        </div>
    </div>
</div>
<div class="layui-row layui-col-space5">
    <div class="layui-col-md4 reviseBugId" style="display: none">
        <div class="grid-demo grid-demo-bg1">
            <label class="layui-form-label"><font>源BUGID:</font></label>
            <div class="layui-input-inline">
                <input id="reviseBugId" name="reviseBugId" column="源BUGID" class="easyui-textbox" data-options="value:'', prompt:'请填写源BUGID,多个逗号隔开'" style="width: 70%"/>
            </div>
        </div>
    </div>
</div>
<div class="layui-row layui-col-space5">
    <label class="layui-form-label"><font>修订影响面:</font></label>
    <div class="layui-input-block">
        <textarea id="repairInfluence" name="repairInfluence" column="修订影响面" class="c-textarea-content-edit" style="width:91%;resize: vertical" rows="3" placeholder="修订这个BUG代码的影响分析，包括但不限于对本模块（本业务/功能）或者其他模块(本业务/功能)的影响,对用户使用的影响等">地区对象、安全策略</textarea>
    </div>
</div>
<div class="layui-row layui-col-space5">
    <label class="layui-form-label"><font>测试验证点:</font></label>
    <div class="layui-input-block">
        <textarea id="testVerification" name="testVerification" column="测试验证点" class="c-textarea-content-edit" style="width:91%;resize: vertical" rows="3" placeholder="修订影响面分析的影响点分析验证点，每个影响点对应至少一个验证点">1、查询220.197.232.148是否有正常查询到云南省的数据
2、打大流，查看是否有正常识别，如安全日志的源地区和目的地区</textarea>
    </div>
</div>
<div class="layui-row layui-col-space5">
    <label class="layui-form-label"><font>根因定位结论:</font></label>
    <div class="layui-input-block">
        <textarea id="locationResult" name="locationResult" column="根因定位结论" class="c-textarea-content-edit" style="width:91%;resize: vertical" rows="3" placeholder="请填写根因定位结论">我们在处理查询数据的时候，也是有点问题的（遇到市级数据的长度为0。需要继续处理后面的数据。）</textarea>
    </div>
</div>
<div class="layui-row layui-col-space5">
    <label class="layui-form-label"><font>关联产品:</font></label>
    <div class="layui-input-block">
        <input id="productIds" name="productIds" placeholder="请选择关联产品"/>
    </div>
</div>
<div class="layui-row layui-col-space5">
    <label class="layui-form-label" style="width: 130px !important;padding: 9px 4px;"><font>关联Gerrit/CAF表单ID:</font></label>
    <div class="layui-input-block">
        <input id="cafRelationIds" name="cafRelationIds" type="hidden" column="关联Gerrit/CAF表单ID"/>
        <input id="cafIds" name="cafIds" class="easyui-textbox" style="width:157px;">
        <button type="button" onclick="addCafIds();" class="layui-btn layui-btn-sm layui-btn-primary" style="height: 26px;line-height: 26px;border-radius: 5px 5px 5px 5px;">新增</button>
        <div class="eu-tags-block" id="addCafIds" style="display: inline-block;line-height: 25px;padding-top: 4px;padding-bottom: 4px;">

        </div>
    </div>
</div>
<div class="layui-row layui-col-space5">
    <label class="layui-form-label"><font>发现BUG的组件:</font></label>
    <div class="layui-input-block">
        <input id="comInfoId" placeholder="请选择"/>
        <input id="comPathId" data-options="prompt:'请先选择组件后再选择分支路径'" style="width:50%;"/>
        <button id="relationBtn" type="button" onclick="addCom(this);" class="layui-btn layui-btn-sm layui-btn-primary" style="height: 26px;line-height: 26px;border-radius: 5px 5px 5px 5px;">关联</button>
        <span style="padding-left: 20px;position: absolute;padding-top: 3px;color: red">选择后，别忘记点击"关联"</span>
    </div>
</div>
<div class="layui-row layui-col-space5">
    <label class="layui-form-label"></label>
    <div class="layui-input-block">
        <div class="layui-form" style="margin-left: 25px">
            <table class="layui-table" style="margin: 0px">
                <tbody id="addComInfos"></tbody>
            </table>
        </div>
    </div>
</div>
<script type="text/javascript">
    function initDevelopChooser(){
        $("#wbs").workpacketchooser({
            inputWidth:'70%',
            onBeforeLoad : function (param){
                var arr = [];
                var osId = $("#os").projectchooser("getValue");
                if(osId != null){
                    arr.push(osId);
                }
                var osSpareId = $("#osSpare").projectchooser("getValue");
                if(osSpareId != null){
                    arr.push(osSpareId);
                }
                param.osIdArr = arr.join(",");
            },
            onAfterAppend: function (row) {
                if (row != undefined && row != null) {
                    var savebugid = $("#savebugid").commonchooser("getValue");
                    var wbssaveid = (row.workPacketName).indexOf("遗留或delay的bug") != -1 ? '2' : '1';
                    if(savebugid != wbssaveid){
                        setTimeout(function (){
                            layer.tips("当前[工作包]的遗留状态与[遗留BUG]状态不一致，请确认！", "#wbs");
                        }, 200)
                    }
                }
            }
        });
        $("#effectbug").introducedchooser({
            dropdownWidth:300,
            inputWidth:'70%',
            onAfterAppend: function (row) {
                if (row != undefined && row != null) {
                    $("#leibie").bugcategorychooser('reset');
                    $("#leibie").bugcategorychooser('getSelectBy',{pid : row.id});
                }
            }
        });
        $("#leibie").bugcategorychooser({
            inputWidth:'70%',
            onBeforeLoad : function (param){
                param.pid = $("#effectbug").introducedchooser("getValue");
            }
        });
        $("#delayid").projectchooser({inputWidth:'70%'});
        $("#productIds").productchooser({multiple:true,inputWidth:'130px'});
        $.initDictionary({ id : 'isRevise', dropdownHeight : 110, inputWidth : '70%',
            onAfterAppend : function(row){
                var state = $("#state").combobox("getValue");
                if(row != null && row.dictText == '是'){
                    $(".reviseBugId").css("display","");
                    $("#reviseBugId").textbox("resize",{width:'70%'});
                    $("#leibie").bugcategorychooser("setDisabled", true);
                    setBugInfoRequired(state);
                    if(bugVersion >= 1){
                        addRequiredText("reviseBugId");
                    }
                }else{
                    $(".reviseBugId").css("display","none");
                    $("#reviseBugId").textbox("clear");
                    $("#leibie").bugcategorychooser("setDisabled", false);
                    setBugInfoRequired(state);
                }
            }
        });
        // $.initDictionary({ id : 'needsendbugreport', dropdownHeight : '120', inputWidth : '70%'});
        $.initDictionary({ id : 'needCollect', dropdownHeight : '120', inputWidth : '70%'});
        $.initDictionary({ id : 'developIsDelay', dropdownHeight : '110', inputWidth : '70%'});
        $.initDictionary({ id : 'closeproperty', inputWidth : '70%'});
        $.initDictionary({ id : 'hotfix', dropdownHeight : '190', inputWidth : '70%'});
        $.initDictionary({ id : 'resolvedok', dropdownHeight : 110, inputWidth : '70%',
            onAfterAppend : function(row){
                if(!row){
                    return;
                }
                let oldV = '3'
                if(row.dictData == oldV){
                    return;
                }
                let state = '13'
                //定义在include_bug_info中
                let allowStates = [STATE_NEW, STATE_ASSIGNED, STATE_VERIFIED, STATE_RESOLVED, STATE_CHECKED];
                if (allowStates.indexOf(state) === -1) {
                    $("#resolvedok").commonchooser('setValue',{dictData:'3',dictText:'非用户接口修订'})
                    Notiflix.Notify.failure("操作失败,只有【BUG状态】是NEW,ASSIGNED,VERIFIED,RESOLVED或CHECKED时才能修改此字段!");
                    return;
                }
                let reviewSwt = ''
                if(reviewSwt !== '1' && oldV !== '4' && row.dictData == '4'){
                    $("#resolvedok").commonchooser('setValue',{dictData:'3',dictText:'非用户接口修订'})
                    var html = '选择 用户接口修订 需要发起三方评审, 如无需强制评审, 请联系PM进行配置, 是否前往发起三方评审?';
                    Notiflix.Confirm.show('状态变更', html, '确定', '稍后评审',
                        function(){
                            bugReview('FORM_REVIEW_USER_CHANGE');
                        },
                        function (){}
                    );

                }

            }
        });
        initNumberTextBox('repairOrder', '修订量', function (value){});
        initFunctionModule('module');
        $("#comInfoId").cominfochooser({
            inputWidth:'155px',
            onAfterAppend: function (row) {
                if (row != undefined && row != null) {
                    $("#comPathId").combobox('clear');
                    $('#comPathId').combobox('reload');
                }
            }
        });
        $('#comPathId').combobox({
            valueField: 'com_path',
            textField: 'com_path',
            mode: 'remote',
            url:'/bug_switch/bug/bug_cominfo/load_bug_cominfo_list?page=1&rows=2000',
            onBeforeLoad:function (param){
                var cominfo = $("#comInfoId").cominfochooser('getValue');
                if(cominfo == null || cominfo == ''){
                    return false;
                }
                param.comInfo = cominfo;
            },
            loadFilter:function(data){
                return data.rows;
            }
        })
    }

    function initDevelopData(){
            $("#wbs").workpacketchooser('setValue',{id : 87326, workPacketName : '遗留或delay的bug(其他)'});
            $("#effectbug").introducedchooser('setValue',{id:73,Introduced_state:'编码阶段'});
            $("#leibie").bugcategorychooser('setValue',{id:91,leibie:'模块内部错误'});
            $("#closeproperty").commonchooser('setValue',{dictData:4,dictText:'正常解决'});
            $("#resolvedok").commonchooser('setValue',{dictData:3,dictText:'非用户接口修订'});
            $("#isRevise").commonchooser('setValue',{dictData:2,dictText:'否'});

            $("#needCollect").commonchooser('setValue',{dictData:15,dictText:'不需要'});
            $("#resolvedVersion").textbox('setValue','20250418');
        $('#module').combotree('setValue','975');
            var bugBugProductArr = [];
            if(bugBugProductArr != null && bugBugProductArr.length > 0){
                $("#productIds").productchooser("setValues", bugBugProductArr);
            }
            var html = '';
                var cafNum = cafHtml('SP24477');
                html += cafNum;
                var cafNum = cafHtml('SP24480');
                html += cafNum;
                var cafNum = cafHtml('SP24481');
                html += cafNum;
                var cafNum = cafHtml('SP24482');
                html += cafNum;
                var cafNum = cafHtml('SP24491');
                html += cafNum;
                var cafNum = cafHtml('SP24610');
                html += cafNum;
                var cafNum = cafHtml('SP24611');
                html += cafNum;
            $("#cafRelationIds").val('SP24477,SP24480,SP24481,SP24482,SP24491,SP24610,SP24611');
            $("#addCafIds").html(html);
            var html = '';
                    var comNum = comHtml('NTOS-CONTRL-PLANE','ssh://git@172.18.141.102:8022/dataplane/fast-path.git');
                    html += comNum;
            $("#addComInfos").append(html);
    }

    //关联CAF表单
    function addCafIds(){
        var cafId = $("#cafIds").textbox("getValue");
        if(cafId == null || cafId == ''){
            Notiflix.Notify.info('请先输入CAF/GREEIT表单ID');
            return;
        }
        var firstChar = cafId.substring(0, 1);
        var secondChar = cafId.substring(0, 2);
        if(firstChar != 'N' && firstChar != 'G' && secondChar != 'SG' && secondChar != 'SP'){
            Notiflix.Notify.info('请输入正确的表单ID：<br>CAF表单以\'N\'开头<br>GREEIT表单以\'G\'开头<br>SMBGREEIT表单以\'SG\'开头<br>安全GITLAB表单以\'SP\'开头');
            return;
        }
        var cafIds = $("#cafRelationIds").val();
        $("#cafRelationIds").val(cafIds + "," + cafId);
        var cafNum = cafHtml(cafId);
        var html = $("#addCafIds").html() + cafNum;
        $("#addCafIds").html(html);
        $("#cafIds").textbox('clear');
    }
    //CAF表单内容
    function cafHtml(cafId){
        var cafHtml = "";
        cafHtml += '<span class="eu-tag eu-tag-info eu-tag-pill" style="margin-right: 3px">';
        cafHtml += "    <a style='color: white' href='javascript:cafForm(\""+ cafId +"\")'>"+ cafId +"</a>";
        cafHtml += '    <a style="color: red;padding-left: 5px;cursor: pointer;" onclick="removeCaf(this,\''+ cafId + '\')" title="删除">×</a>';
        cafHtml += '</span>';
        return cafHtml;
    }
    
    //移除CAF表单
    function removeCaf(obj, cafId){
        Notiflix.Confirm.show(
            '移除CAF/GEEIT表单',
            '是否确定要移除：' + cafId,
            '确定',
            '取消',
            function(){
                $(obj).parent().remove();
                var cafIds = $("#cafRelationIds").val();
                $("#cafRelationIds").val(cafIds.replace(cafId, ""));
            }
        );
    }
    
    //跳转至CAF系统
    function cafForm(cafId){
        var firstChar = cafId.substring(0,1);
        var secondChar = cafId.substring(0, 2);
        if(secondChar == 'SG'){
        	var gerritId = cafId.replace("SG","");
            window.open('http://gerrit.smb.ruijie.net:8081/' + gerritId + "/");
        } else if(firstChar == 'G'){
            var gerritId = cafId.replace("G","");
            window.open('http://gerrit.ruijie.work/login/' + gerritId + "/");
        } else if(secondChar == 'SP'){

        } else{
            window.open('https://pim.ruijie.com.cn/pim//servlet/caf/initForm?type=form_info&crid=' + cafId);
        }
    }
    
    //关联组件
    function addCom(obj){
        var comInfo = $("#comInfoId").cominfochooser("getValue");
        var comPath = $("#comPathId").combobox("getValue");
        var comInfoArr = $("input[name='comInfo']");
        var comPathArr = $("input[name='comPath']");
        for(var i=0; i < comInfoArr.length; i++){
            var comInfoOld = $(comInfoArr[i]).val();
            var comPathOld = $(comPathArr[i]).val();
            if(comInfo == comInfoOld && comPath == comPathOld){
                Notiflix.Notify.info('你已关联过该组件，请勿重复关联');
                return;
            }
        }
        if(comInfo == null || comInfo == ''){
            Notiflix.Notify.info('请选择组件');
            return;
        }
        if(comPath == null || comPath == ''){
            Notiflix.Notify.info('请选择组件所对应的分支路径');
            return;
        }
        var html = comHtml(comInfo, comPath);
        $("#addComInfos").append(html);
    }
    
    //组件内容
    function comHtml(comInfo, comPath){
        var html = '';
        html += '<tr>';
        html += '   <td width="170px" style="white-space: nowrap;font-size: 12px;color: blue;font-weight: 600">' + comInfo + '<input name="comInfo" type="hidden" value="'+ comInfo +'"></td>';
        html += '   <td style="white-space: nowrap;font-size: 13px;color: black;font-weight: 500">'+ comPath +'<input name="comPath" type="hidden" value="'+ comPath +'"></td>';
        html += '   <td width="50px" style="white-space: nowrap;font-size: 12px" align="center"><a style="cursor: pointer;color: red" onclick="removeComInfo(this)">移除</a></td>';
        html += '</tr>';
        return html;
    }
    
    //移除组件
    function removeComInfo(obj){
        Notiflix.Confirm.show(
            '移除发现BUG的组件',
            '是否确定要删除该组件?' ,
            '确定',
            '取消',
            function(){
                $(obj).parent().parent().remove();
            }
        );
    }
</script>                </div>
            </div>
        </div>
        <fieldset class="layui-elem-field layui-field-title" id="bugNotifyView">
            <legend style="font: bold 15px tahoma, arial, helvetica, sans-serif;color: #15428b;">BUG通告视图</legend>
        </fieldset>
        <div class="layui-bg-gray" style="padding: 30px;padding-top: inherit">
            <div class="layui-row layui-col-space15">
                <div class="layui-panel">



<div class="layui-row layui-col-space5" style="color: black;">
    <div class="layui-col-md3" >
        <input type="hidden" id="needsendbugreport" name="needsendbugreport" value="1">
        <input type="hidden" id="bugreportid" name="bugreportid" value="145979">
        <span>
                <span id="needNotify" style="font-weight: bold;">需要发BUG通告:</span>
                <span style="margin: 10px;">
                        <span>是</span>
                </span>
        </span>
        <span style="margin-left: 30px;">
                <span style="font-weight: bold;">BUG通告ID:</span>
                <span id="noticeId">
                    NA
                </span>
        </span>

    </div>
</div>
<div class="layui-row layui-col-space5">
    <div class="notifyTipSource"></div>
</div>
<div class="layui-row layui-col-space5" >
  <div id="noticeRecordDatagrid"></div>
</div>

<div id="notifyEdit" style="display: none;">

    <div class="layui-row" style="margin: 5px">
        <label class="layui-form-label">发送BUG通告:</label>
        <div class="layui-input-block">
            <input id="notifyIdEdit" type="hidden" class="independent" placeholder="请选择"/>
            <input id="bugNotifyEdit"  class="independent" placeholder="请选择"/>
        </div>
    </div>
    <div class="layui-row" style="margin: 5px" >
        <label class="layui-form-label">判断BUG通告理由:</label>
        <div class="layui-input-block">
            <textarea id="notifyReasonEdit"
                      class="c-textarea-content-edit independent" style="width:91%;resize: vertical" rows="3"></textarea>
        </div>
    </div>
    <div class="layui-row" style="margin: 5px" >
        <label class="layui-form-label"></label>
        <div class="layui-input-block">
            <button type="button" class="easyui-linkbutton" data-options="iconCls: 'icon_save-16'" onclick="updateNotify()">保存</button>
        </div>
    </div>
</div>
<script>
    $('#notifyEdit').window({
        title: 'BUG通告编辑',
        width: 600,
        height: 220,
        closed: true,
        cache: false,
        modal: true
    });

    /**
     * 初始化
     */
    $(function (){
        loadNoticeTable()
        loadTip()

    })

    /**
     * 通告编辑框显示
     * @param e
     * @param index
     */
    function notifyEditShow(e,index){
        //为了阻止自动聚焦行为，产生的跳转
        e.preventDefault()
        let row = $('#noticeRecordDatagrid').datagrid('getRows')[index]

        $('#notifyEdit').window('open')
        $('#notifyReasonEdit').val(row.notifyReason)
        $('#notifyIdEdit').val(row.id)
        $('#bugNotifyEdit').combobox({
            value: row.notify,
            valueField: 'value',
            textField: 'text',
            url: '/bug_switch/bug_notice_record/notify_list',
            panelHeight: 50,
        })
        let scrollTop = $(window).scrollTop();
        // 调整窗口位置到当前
        $('#notifyEdit').window('resize', {
            top: scrollTop + 150
        });
    }

    /**
     * 更新通告
     */
    function updateNotify(){
        let data = {
            notify: $('#bugNotifyEdit').combobox('getValue'),
            notifyReason: $('#notifyReasonEdit').val().trim(),
            id:  $('#notifyIdEdit').val()
        }
        if(data.notify == 2 && !data.notifyReason){
            Notiflix.Notify.failure("操作失败，不发送时，请填写BUG通告理由");
            return
        }
        postAjax(
            '/bug_switch/bug_notice_record/update_notice',
            data,
            function(rs) {
                if(rs.content){
                    Notiflix.Notify.failure(rs.content);
                }else {
                    Notiflix.Notify.success("操作成功,系统于2秒后自动刷新!");
                    setTimeout(function(){
                        window.location.reload();
                    },2000)
                }

            }
        )
    }

    /**
     * 初始化表
     * @param dataList
     */
    function genNotifyTable(dataList){
        const state_delay = '9'
        const state_cbd = '13'
        const state_cbt = '7'
        let notifyColumn = [
            {
                field: 'action',
                align: 'center',
                width: 50,
                title: '操作',
                formatter: function (value, row, index) {
                    //在delay，cbd,cbt状态，禁止修改

                    if(row.userId != '26293'){
                        return ''
                    }
                    if(stateOld == state_delay || stateOld == state_cbd || stateOld == state_cbt){
                        return '<span title="当前BUG状态禁止编辑" style="color: gray;">编辑</span>'
                    }
                    let content = '<a href="#" style="color: blue" onclick="notifyEditShow(event,{0})">编辑</a>'.format(index);
                    return content
                }
            },{
                field: 'id',
                align: 'center',
                width: 60,
                title: '<div style="margin: 10px">判断ID</div>',
                formatter:function (value){
                    return value
                },
            },{
                field: 'userRole',
                align: 'center',
                width: 100,
                title: '<div style="margin: 10px">角色</div>',
                formatter:function (value){
                    if(!value|| value === 'NA'){
                        value = ''
                    }
                    return value
                },
                headerStyler: function () {
                    return 'background-color:yellow;color:red;';
                }
            },{
                field: 'username',
                align: 'center',
                width: 100,
                title: '判断人'
            },{
                field: 'notify',
                align: 'center',
                width: 100,
                title: '判断是否发送BUG通告',
                formatter: function (value) {
                    return value === 1 ? '是' : '否';
                },
            },{
                field: 'notifyReason',
                align: 'center',
                width: 200,
                title: '判断BUG通告理由',
                formatter: function (value) {
                    if(!value){
                        value = ''
                    }
                    return '<div style="overflow: clip "><span title="{0}">{1}</span></div>'.format(value,value)
                },
            },{
                field: 'auditType',
                align: 'center',
                width: 100,
                title: '判断类型',
                formatter: function (value) {
                    if (value === 14) {
                        return 'CHECKED审核';
                    } else if (value === 9) {
                        return 'DELAY审核';
                    } else if(value === 5){
                        return 'RESOLVED初步判断'
                    } else if(value === 100){
                        return 'DELAY初步判断'
                    }
                    return value;
                }
            },{
                field: 'createTime',
                align: 'center',
                width: 100,
                title: '创建时间',
                formatter: GridFormat.formatTime
            },{
                field: 'updateTime',
                align: 'center',
                width: 100,
                title: '修改时间',
                formatter: GridFormat.formatTime
            },
        ];
        $('#noticeRecordDatagrid').datagrid({
            border: false,
            fitColumns:true,
            rownumbers: false,
            remoteSort: false,
            singleSelect: true,
            pagination: false,
            idField:'id',
            data: dataList,
            columns: [notifyColumn],
            onLoadSuccess: function (data) {
            }
        })
    }

    /**
     * 创建通告表格
     */
    function loadNoticeTable(){
        let url = '/bug_switch/bug_notice_record/notify_bug_list?bugId=1352718'
        postAjax(url, {}, function (data){
            let dataList = data.rows
            genNotifyTable(dataList)
        })
    }

    /**
     * 加载BUG通告相关提示
     */
    function loadTip(){
        postAjax(
            '/bug_switch/bug_notice_record/notify_tip',
            {
                bugId: '1352718'
            },
            function (res){
                if(res.data){
                    let {self,sourceList,preview} = res.data
                    let selfTip = noticeTipWrap(self,'BUG:')
                    if(selfTip){
                        selfTip = '<span style="color: red">该BUG已发送过BUG通告：'+selfTip+'</span>'
                    }
                    $('.notifyTipSelf').html(selfTip)
                    let sourceTip = ''

                    let sourceTipArr = []
                    for (const source of sourceList) {
                        let tip = noticeTipWrap(source,'源BUG:')
                        if(tip){
                            sourceTipArr.push(tip)
                        }
                    }
                    sourceTip = sourceTipArr.join('<span style="margin-left: 20px;margin-right: 20px">|</span>')
                    if(sourceTip){
                        sourceTip = '<span style="color: red">源BUG中已发送过BUG通告：'+sourceTip+'</span>'
                    }
                    $('.notifyTipSource').html(sourceTip)
                    if(preview){
                        let preTip = '<span style="color: red">{0}初步判断{1}发送BUG通告，理由：{2}</span>'
                        $('.notifyTipPreview').html(preTip.format(preview.username,preview.notify == 1?'需要':'不需要',
                            preview.notifyReason?preview.notifyReason:'NA'))
                    }

                    let selfA = noticeATag(self)
                    if(selfA){
                        $('#noticeId').html(selfA)
                    }

                }
            }
        );
    }

    /**
     * 提示通告内容
     * @param notice
     * @returns {string}
     */
    function noticeATag(notice){
        if(notice.taskStageName === '未知' ){
            return ''
        }else if(notice.available && notice.available === '0') {
            return '<span style="margin-left: 10px;text-decoration: line-through;color: black" title="已废弃" >'+notice.id+'</span>'
        }else {
            return '<a style="margin-left: 10px;color: blue" target="_blank" href="'+notice.url+'">'+notice.id+'('+notice.taskStageName+')</a>'
        }
    }

    /**
     * 提示字段样式
     * @param notice
     * @param field
     * @returns {string}
     */
    function noticeTipWrap(notice,field){
        let str = noticeATag(notice);
        if(str){
            return '<span style="font-weight: bold">'+field+'</span><span style="margin-left: 10px;color: black;">'+notice.bugId+'</span>' +
                '<span style="font-weight: bold;margin-left: 10px">通告ID:</span>'+ str
        }else {
            return ''
        }
    }
</script>

                </div>
            </div>
        </div>
        <fieldset class="layui-elem-field layui-field-title" id="log">
            <legend style="font: bold 15px tahoma, arial, helvetica, sans-serif;color: #15428b;">日志信息</legend>
        </fieldset>
        <div style="padding: 30px;padding-top: inherit">
            <div class="layui-row layui-col-space15">
                <div class="layui-panel">
<table class="loginfoTable" style="table-layout: fixed;width: 100%;overflow: scroll;">
    <tr class="loginfoTableUserInfo">
        <td width="100" align="right">提交者:</td>
        <td width="300">郑江宏</td>
        <td width="100" align="right">提交时间: </td>
        <td name="thedate">
            2025-04-17 15:48:32
        </td>
    </tr>
    <tr class="loginfoTableLogMsg">
        <td width="100" align="right">测试拓扑:</td>
        <td colspan="3" class="log-content" name="testtopo"><p>MACC--FW--AP--终端</p></td>
    </tr>
    <tr class="loginfoTableLogMsg">
        <td width="100" align="right">拓扑描述:</td>
        <td colspan="3" class="log-content" name="topodesc">MACC--FW--AP--终端</td>
    </tr>
    <tr class="loginfoTableLogMsg">
        <td width="100" align="right">描述信息:</td>
        <td colspan="3" class="log-content" name="description"><p>ip查找，结果为中国，实际上应该为云南省<br/><img src="http://resource.ruijie.com.cn:8080/pms_image/ueditor/jsp/upload/image/20250417/1744876060133053328.jpg" title="1744876060133053328.jpg" alt="img_v3_02le_14a611c3-79e2-41a4-90ec-c90e97d3eddg.jpg"/></p></td>
    </tr>
    <tr class="loginfoTableLogMsg">
        <td width="100" align="right">一键收集信息:</td>
        <td colspan="3" class="log-content" name="oneKeyCollectionInfo"></td>
    </tr>
    <tr class="loginfoTableLogMsg">
        <td width="100" align="right">Debug信息:</td>
        <td colspan="3" class="log-content" name="debuginfo">NA</td>
    </tr>
    <tr class="loginfoTableLogMsg" id="ver-view">
        <td width="100" align="right">
            <button type="button" class="easyui-linkbutton" title="点击编辑版本信息" style="margin-top: -3px;" data-options="iconCls: 'icon_edit-1',plain:true" onclick="updateVer();"></button>版本信息:
        </td>
        <td colspan="3" class="log-content" name="ver">
            Ruijie> show product<br/>show-product<br/>    start-time "2024-12-25 11:39:57"<br/>    uptime "1 hour, 58 minutes"<br/>    productname N7406-E<br/>    hardwarever 1.00<br/>    serialnum MACCZZCN7406E<br/>    ethaddr 00:D0:F8:03:16:77<br/>    softwarever "NBR_NTOS 1.0R10P3O1, Release(04242504)"<br/>    softwarenum M04021312252024<br/>    boot-version 1.1.0.696752c07<br/>    ..<br/>
        </td>
    </tr>
    <tr class="loginfoTableLogMsg">
        <td width="100" align="right">测试程序:</td>
        <td colspan="3" class="log-content" name="testfile"></td>
    </tr>
    <tr class="loginfoTableLogMsg">
        <td width="100" align="right">被测设备配置:</td>
        <td colspan="3" class="log-content" name="expectedresult">NA</td>
    </tr>
    <tr class="loginfoTableLogMsg">
        <td width="100" align="right">定位信息:</td>
        <td colspan="3" class="log-content" name="locate"></td>
    </tr>
    <tr class="loginfoTableLogMsg">
        <td width="100" align="right">编译链接:</td>
        <td colspan="3" class="log-content">
        </td>
    </tr>
    <tr class="loginfoTableLogMsg" id="reviewFormOld">
        <td width="100" align="right">评审表单:</td>
        <td colspan="3" class="log-content">
            <ul class="layui-timeline" style="width: 90%">
            </ul>
        </td>
    </tr>
</table>
<script type="text/javascript">
    function initLog(){
        var url = '/bug_switch/bug/bug_record/load_record_list';
        $.ajax({
            url: url,
            type: 'POST',
            cache: false,
            data: {bugId : '1352718'},
            beforeSend:function(res){
                Notiflix.Block.circle('.loginfoTable','加载中...');
            },
            success: function (data) {
                Notiflix.Block.remove('.loginfoTable');
                var html = '';
                if(data != null && data.rows != null){
                    for(var i=0; i < data.rows.length; i++){
                        html += '<tr class="loginfoTableUserInfo">';
                        html += '   <td width="100" align="right">处理人:</td>';
                        html += '   <td width="100">' + data.rows[i].userName + '</td>';
                        html += '   <td width="100" align="right">处理时间:</td>';
                        html += '   <td width="100">' + GridFormat.formatTime(data.rows[i].thedate,null,null) + '</td>';
                        html += '</tr>';
                        html += '<tr class="loginfoTableLogMsg">';
                        html += '   <td align="right">操作日志:</td>';
                        html += '   <td colspan="3" class="log-content log-record">' + data.rows[i].info + '</td>';
                        html += '</tr>';
                    }
                }
                $(".loginfoTable").append(html);
            },
            error: function (res) {
                Notiflix.Block.remove('.loginfoTable');
                Notiflix.Notify.failure('日志加载失败，请联系管理员');
            }
        });
    }

    function getOutput(systemSoftwareNumber){
        var url = "/bug_switch/bug/bug_record/get_out_put?systemSoftwareNumber=" + systemSoftwareNumber;
        postAjax(url, null, function (data){
            if(data != null && data.length > 0){
                var content="";
                for(var i=0; i< data.length; i++){
                    content += "<br><a target=_blank style='color:red' href="+ data[i].output_url +">"+ data[i].output_url +"</a>";
                }
                $("#output_url").html(content);
            }else{
                Notiflix.Notify.info("抱歉，未检索到相关信息");
            }
        });
    }

    function loadUrl(obj){
        var url = "/bug_switch/bug_link_url/load_bug_link_url_list?bugId=1352718";
        postAjax(url, null, function (data){
            var html = '';
            if(data.length == 1){
                Notiflix.Notify.info("没有查询到更多的编译链接");
                return;
            }
            for(var i=0; i < data.length; i++){
                html += '<li class="layui-timeline-item">';
                html += '   <div class="layui-timeline-content layui-text">';
                html += '       <i class="layui-icon layui-timeline-axis"></i>';
                html += '       <h3 class="layui-timeline-title" style="font-size: 14px">'+ GridFormat.formatTime(data[i].createDate) +'</h3>';
                html += '       <p style="margin: unset;font-size: 14px"><a href="'+ data[i].outPutUrl +'" target="_blank">'+ data[i].outPutUrl +'</a></p>';
                html += '   </div>';
                html += '</li>';
            }
            $(".bugLinkUrl").html(html);
            $(obj).remove();
        });
    }
    function loadReviewUser(bugReviewFormId, formTypeCode, obj){
        var url = '/bug_switch/bug_review_user/load_bug_review_user_list?formId=' + bugReviewFormId;
        postAjax(url, null, function (data){
            var html = '';
	        if(formTypeCode == 'FORM_INTERFACE_CHANGE_CBD' || formTypeCode == 'FORM_INTERFACE_CHANGE_CBT'){
                var url = '/bug_switch/bug_review_field_value/get_form_field_value?formId=' + bugReviewFormId;
                postSyncAjax(url, null, function (data){
                    for (let segName in data) {
                        html += '<table class="layui-table" style="margin: 0px;margin-top: 15px">';
                        html += '   <thead>';
                        html += '       <tr>';
                        html += '           <th colspan="6">';
                        html += '               <span style="text-align: center;font-weight: 600;padding-left: 40px;">' + segName + '</span>';
                        html += '           </th>';
                        html += '       </tr>';
                        html += '   </thead>';
                        html += '   <tbody>';
                        for(var i=0; i < data[segName].length; i++){
                            html += '   <tr>';
                            html += '       <td style="width: 200px;text-align: center">';
                            html += '           <span>'
                            if(data[segName][i].fieldType == '1'){
                                html += data[segName][i].fieldName + ' - 变更修订:';
                            }else if(data[segName][i].fieldType == '2'){
                                html += data[segName][i].fieldName + ' - 链接:';
                            }else{
                                html += data[segName][i].fieldName;
                            }
                            html += '           </span>';
                            html += '       </td>';
                            if(data[segName][i].fieldType == '1') {
                                html += '       <td style="width: 50px;text-align: center">';
                                html += '           <span>';
                                if(data[segName][i]['valueType'] == 'change'){
                                    if(data[segName][i].fieldValue == '1'){
                                        html += '       <font color="blue">是</font>';
                                    }else{
                                        html += '       <font color="red">否</font>';
                                    }
                                }else{
                                    html += data[segName][i].fieldValue;
                                }
                                html += '           </span>';
                                html += '       </td>';
                                html += '       <td style="width: 80px;text-align: center;">变更说明:</td>';
                                html += '       <td style="width: 400px;"><span>'+ data[segName][i].changeText +'</span></td>';
                                html += '       <td style="width: 80px;text-align: center;">测试验收结果:</td>';
                                html += '       <td>'
                                html += '           <span>';
                                if(data[segName][i].testResult == '1'){
                                    html += '       <font color="blue">通过</font>';
                                }else if(data[segName][i].testResult == '0'){
                                    html += '       <font color="red">不通过</font>';
                                }else{
                                    html += '       <font>未验收</font>';
                                }
                                html += '           </span>';
                                html += '       </td>';
                            }else if(data[segName][i].fieldType == '2' && data[segName][i].fieldValue != ''){
                                html += '       <td colspan="5">';
                                html += '           <a style="padding-left: 5px" target="_blank" style="color: blue;" href="'+ data[segName][i].fieldValue +'">点击查看</a>';
                                html += '       </td>';
                            }else if(data[segName][i].fieldType == '3'){
                                html += '       <td colspan="5">' + data[segName][i].fieldValue + '</td>';
                            }
                            html += '   </tr>';
                        }
                        html += '   </tbody>';
                        html += '</table>';
                    }
                });
	        }
            html += '<table class="layui-table" style="margin: 0px;margin-top: 15px">';
            html += '   <tr>';
            html += '       <td width="130" style="text-align: center;font-weight: 600">评审角色</td>';
            html += '       <td width="100" style="text-align: center;font-weight: 600">评审人</td>';
            html += '       <td width="100" style="text-align: center;font-weight: 600">评审结果</td>';
            html += '       <td width="150" style="text-align: center;font-weight: 600">评审时间</td>';
            html += '       <td width="200" style="text-align: center;font-weight: 600">评审原因</td>';
            html += '   </tr>';
            for(var i=0; i < data.rows.length; i++){
                var reviewStatus = data.rows[i].reviewStatus == null ? '未评审' : data.rows[i].reviewStatus;
                var reason = data.rows[i].reason == null ? '' : data.rows[i].reason;
                html += '   <tr>';
                html += '       <td style="text-align: center;">'+ data.rows[i].userRole +'</td>';
                html += '       <td style="text-align: center;">'+ data.rows[i].username +'</td>';
                html += '       <td style="text-align: center;">'+ reviewStatus +'</td>';
                html += '       <td style="text-align: center;">'+ GridFormat.formatTime(data.rows[i].updateTime) +'</td>';
                html += '       <td style="text-align: center;">'+ reason +'</td>';
                html += '   </tr>';
            }
            html += '</table>';
            $(obj).closest('div').append(html);
            $(obj).attr("onclick", 'closeReviewUser('+ bugReviewFormId +',\''+ formTypeCode +'\', this)');
            $(obj).html("&#xe67e;");
        });
    }

    function closeReviewUser(bugReviewFormId, formTypeCode, obj){
        $(obj).html("&#xe624;");
        $(obj).closest('div').children('table').remove();
        $(obj).attr("onclick", 'loadReviewUser('+ bugReviewFormId +', \''+ formTypeCode +'\', this)');
    }

    function updateVer(){
        var textTitle = "更新版本信息:1352718";
        var textUrl = '/bug_switch/bug/bug_ver_edit?id=1352718';
        Pages.showWindow(textTitle,textUrl,800,470);
    }
</script>                </div>
            </div>
        </div>


        <div style="height:30px;"></div>
        <div style="padding:5px;">
            <div style="z-index:9000;height:34px;margin:0 auto;bottom:0;position:fixed;left:0;right:0;background: #eeeeee" class="ec-datagrid-toolbar">
                <div style="text-align:center;margin:5px;">
                    <button id="submitBtn" type="button" class="easyui-linkbutton" data-options="iconCls: 'ci-search-16'">提交</button>
                </div>
            </div>
        </div>
    </form>
<div id="_TCEUploadDialog">
	<form id="fileFormUpload" enctype="multipart/form-data" style="display: inline-block;" method="post">
		<table class="ec-table ec-table-fluid">
			<tr>
				<input name="userName" type="hidden" value="苏晨1">
				<td class="ec-field">上传文件</td>
				<td align="left">
					<input id="file" name="file" class="easyui-filebox" data-options="validType:'fileSize[500,\'MB\']', width: 330,buttonText: '选择文件',accept: '', required: true, multiple:true,missingMessage: '请选择要上传的文件', tipPosition: 'bottom'">
				</td>
			</tr>
			<tr>
				<td class="ec-field"></td>
				<td align="left">
					<button type="submit" class="easyui-linkbutton" data-options="iconCls:'icon_import-16'">上传</button>
				</td>
			</tr>
		</table>
	</form>
</div>
<script type="text/javascript">
 	var importUploadDialog = $('#_TCEUploadDialog').dialog({
		title: '上传附件',
		width: 450,
		modal: true,
		closed: true,
		shadow: false
	});

	function importFile() {
		showFileWindow();
		var url = "/bug_switch/file/get_upload_address";
		postAjax(url, null, function (url){
			if(url == null || url == ''){
				Notiflix.Notify.failure('未能获取上传地址,上传失败!');
			}else{
				var redirectUrl = 'http://bugs.ruijie.com.cn/bug_switch/interface/file_call_back';
				$("#fileFormUpload").attr("action", url + "&redirectUrl=" + redirectUrl);
			}
		});
	}

	$('#fileFormUpload').form({
		novalidate: true,
		onSubmit: function() {
			$(this).form('enableValidation');
			var valid = $(this).form('validate');
			if (valid) {
				$.messager.progress({ msg: '文件上传中，请稍候&nbsp;.&nbsp;.&nbsp;.' })
			}
			return valid
		},
		success: function(message) {
			$.messager.progress('close');
			if(message != null && message != ''){
				var res = eval("("+ message +")");
				if(res.type == 'success'){
					importUploadDialog.dialog('close');
					Notiflix.Notify.success("附件上传成功");
					for(var i=0; i < res.data.length; i++){
						var fileIds = $("#fileId").val();
						$("#fileId").val(fileIds + "," + res.data[i].id);
						var url = "<div style='display: inline-block;padding-left: 25px'><a href='javascript:downloadFile(\""+ res.data[i].migFileSysId +"\")'>"+ res.data[i].fileName +"</a><a onclick='removeFile(this,"+ res.data[i].id +")'><img style='' title='点击移除' src='/bug_switch/resources/pms/common/images/btn_delete.png' class='displayfieldimg'></a></div>";
						finishImportFile(url);
					}
				}else{
					Notiflix.Notify.failure(res.content);
				}
			}else{
				Notiflix.Notify.failure("上传失败，请联系管理员!");
			}
		},
		error: function (res){
			$.messager.progress('close');
			Notiflix.Notify.failure("系统异常，请联系管理员!");
		}
	});

	function showFileWindow() {
		importUploadDialog
				.dialog('open')
				.panel("move",{top:$(document).scrollTop() + ($(window).height()-200) * 0.5});
	}

	function finishImportFile(fileNames) {
		var tmpFileName = $("#addFileName").html();
		tmpFileName += fileNames;
		$("#addFileName").html(tmpFileName);
	}

	function downloadFile(migFileSysId){
		var url = "/bug_switch/file/get_down_address?migFileSysId=" + migFileSysId;
		postAjax(url, null, function (url){
			if(url == null || url == ''){
				Notiflix.Notify.failure('未能获取下载地址,下载失败!');
			}else{
				window.open(url);
			}
		})
	}

	function removeFile(obj,fileId) {
		$.messager.confirm('提示', '是否确认删除该文件?',
				function(r) {
					if (r) {
						$(obj).parent().remove();
						var fileIds = $("#fileId").val();
						$("#fileId").val(fileIds.replace(fileId,""));
					}
				});
	}

	$.extend($.fn.validatebox.defaults.rules, {
		fileSize : {
			validator : function(value, array) {
				var size = array[0];
				var unit = array[1];
				if (!size || isNaN(size) || size == 0) {
					$.error('验证文件大小的值不能为 "' + size + '"');
				} else if (!unit) {
					$.error('请指定验证文件大小的单位');
				}
				var index = -1;
				var unitArr = new Array("bytes", "kb", "mb", "gb", "tb", "pb", "eb", "zb", "yb");
				for (var i = 0; i < unitArr.length; i++) {
					if (unitArr[i] == unit.toLowerCase()) {
						index = i;
						break;
					}
				}
				if (index == -1) {
					$.error('请指定正确的验证文件大小的单位：["bytes", "kb", "mb", "gb", "tb", "pb", "eb", "zb", "yb"]');
				}
				// 转换为bytes公式
				var formula = 1;
				while (index > 0) {
					formula = formula * 1024;
					index--;
				}
				var bool = $(this).next().get(0).files[0].size < parseFloat(size) * formula;
				if(!bool){
					Notiflix.Notify.warning("文件大小必须小于" + size + unit);
				}
				return bool;
			},
			message : '文件大小必须小于 {0}{1}'
		}
	});
</script></body>
<script type="text/javascript">
    const STATE_NEW = '2';
    const STATE_ASSIGNED = '3'
    const STATE_VERIFIED = '6';
    const STATE_RESOLVED = '5';
    const STATE_CHECKED = '14';
    const STATE_CBD = '13';
    const STATE_CBT = '7';
    function initChooser(){
        initBugInfoChooser();
        initTestChooser();
        initDevelopChooser();
    }

    function initData() {
        initBugInfoData();
        initTestData();
        initDevelopData();
    }
    let notifyTipInfo = '一般来说，以下几类问题可以认为是重大问题（包括但不限于）：</br>' +
        '   &emsp;1、导致断网/断流;</br>' +
        '   &emsp;2、导致设备复位;</br>' +
        '   &emsp;3、导致配置丢失;</br>' +
        '   &emsp;4、导致基础功能失效（基本为1/2/3级规格）;</br>' +
        '   &emsp;5、导致coredump;</br>' +
        '   &emsp;6、导致设备挂死或进程挂死/死循环;</br>' +
        '   &emsp;7、导致内存泄漏;</br>' +
        '   &emsp;8、其它由专业组长及项目组判断（按需判断）;'
    //字段提示信息配置
    var tipsArr = [
        {id : 'state',          width: '1050px', time: '250000', closeBtn: '1', tips :
               '&emsp;1) NEW：新提交Bug的初始状态<br>'+
               '&emsp;2) ASSIGNED：Bug已指派给负责工程师解决<br>' +
               '&emsp;3) REQUEST-REPRODUCE：该状态可用于向测试工程师请求Bug复现、补充信息<br>' +
               '&emsp;4) REQUEST-NO_TO_SOLVE：当准备在本项目不解决BUG时，此状态用于审批过程<br>' +
               '&emsp;5) REQUEST-CHANGE_REQ：当准备变更指标时，此状态用于审批过程<br>' +
               '&emsp;6) VERIFIED：该状态表示BUG已经定位，但还未解决<br>' +
               '&emsp;7) RESOLVED：该状态表示已经给出解决方法<br>' +
               '&emsp;8) CHECKED：当工程师将Bug置成Resolved状态时，需要由Bug审核人来审核，<br>审核通过后，该Bug将处于Checked的状态<br>' +
               '&emsp;9) CLOSED-ByDevelopment：表示工程师对产生该Bug的相关软件模块进行了修改，<br>代码提交到配置库，开发工程师使用配置库中的最新代码编译软件版本，并验证Bug现象已经解决。<br>在这以后，将Bug状态置为Closed-ByDevelopment<br>' +
               '&emsp;10) CLOSED-ByTest：表示测试工程师已经在正式提交的软件版本上，对Bug进行了验证，确认Bug已经解决。<br>该状态是Bug最终状态之一，说明测试工程师认为该Bug已经完全解决<br>' +
               '&emsp;11) REOPENED：该状态表示此Bug准备进入Closed-ByDevelopment状态时，经测试工程师验证后发现并未修正或者没有被完全修正，使用REOPENED状态<br>' +
               '&emsp;12) RENEW：该状态表示在开发工程师将Bug状态置于deny、delay，或者其他已解决状态后，最终未能通过审批的，或者有新的证据或新的观点，采用该状态<br>' +
               '&emsp;13) Denial-ByTest：该状态表示此Bug被测试工程师撤回，为误报，或者认可开发工程师的Denial-ByDevelopment的认定<br>' +
               '&emsp;14) Denial-ByDevelopment：该状态表示此Bug信息被开发工程师撤回，为误报<br>' +
               '&emsp;15) Denial-ByCA：当涉及到不明确的外部需求，由CA进行裁定后认为不需解决的，设置最终状态为Denial-ByCA<br>' +
               '&emsp;16) GIVEUP：该状态表示此Bug放弃。可放弃Bug的判断原则：未能重现，在当前条件下无法解决及无法规避。需要PM、PTM和CA都审核后才可完成GIVEUP状态<br>' +
               '&emsp;17) DELAY：该状态表示此Bug在该项目中不予修正，延迟到后续项目中修正；需要PM、PTM和CA都审核后才完成入Delay状态<br>' +
	           '<img style="width: 445px;position: absolute;top: 0;right: 0" src="http://resource.ruijie.com.cn:8080/pms_image/ueditor/jsp/upload/image/20250324/1742817153359049826.png">'
        },
        {id : 'owner',          tips : '负责修复BUG的人,一般确定后不更改'},
        {id : 'belong',         tips : '产生BUG的人,一般确定后不更改'},
        {id : 'os',             tips : '如果操作系统填错等原因需要非NEW状态下变更,请联系管理员进行协助修订'},
        {id : 'locationTime',   tips : '定位该BUG所花费的时间,单位是分钟'},
        {id : 'osSpare',        tips : '该BUG遗留来源的操作系统，或者直接关联的PI操作系统'},
        {id : 'userGuide',      tips : '用于描述当前BUG是否使用了以下链接中的定位手册进行定位，如果使用，将手册url链接地址填入'},
        {id : 'developIsDelay', tips : '由平台项目遗留到或者遗漏到事业部版本的BUG为项目遗漏BUG'},
        {id : 'location',       tips : '测试人员填写判断要点：开发人员仅接收测试按故障诊断手册要求收集的定位信息，不需要测试提供环境，也不需要再做第二次的复现支持，就判定为”可一次性定位”，否则就是“不可一次性定位”'},
        {id : 'leibie',         tips : '根据 [BUG引入状态] 的值会有不同的 [BUG类别]'},
        {id : 'cafIds',         width: '330px', tips : '1.CAF表单ID规则：N+单号，如 N20220811591 <br>2.平台Gerrit：G+单号，如 G123456 <br>3.SMB Gerrit：SG+单号，如 SG123456<br>4.安全 GitLab：SP+单号，如 SP123456'},
        {id : 'severity',       width: '350px', tips : 'Blocking : 已阻塞测试工作进程，需要绝对优先解决<br>Normal : 正常处理'},
        {id : 'priority',       width: '750px', time: '10000', closeBtn: '1', tips :
                'Critical : 非常严重，大范围功能失效</br>' +
                '   &emsp;(1) BUG后果严重，使系统崩溃、死机或资源严重不足;</br>'+
                '   &emsp;(2) BUG影响面广，如导致特定场景下的功能均不能运行;</br>' +
                'Major : 较为严重，影响较大</br>'+
                '   &emsp;(1) 某项功能的关键性指标未实现，严重影响该功能使用，没有办法更正，但不影响其它功能运行;</br>'+
                '   &emsp;(2) BUG影响面较多，如导致特定场景下的多数用例不能运行;</br>' +
                'Normal : 问题较小，不影响执行工作或功能实现</br>'+
                '   &emsp;(1) 影响某项功能的部分使用或不完全符合指标要求，但不影响该功能的基本运行，问题较小，不影响执行工作或功能实现;</br>' +
                'Trival : 问题较小，不影响执行工作或功能实现</br>'+
                '   &emsp;(1) 使操作者不方便或遇到麻烦，但它不影响执行工作或功能实现，多指易用性方面的问题;</br>'+
                '   &emsp;(2) 改进性的建议;'
        },
        {
            id : 'legacyType',
            width: '750px',
            time: '10000',
            closeBtn: '1',
            tips : '1.版本类遗留：项目基线存在且具备触发条件 //即同等条件基线也能测出一样的问题来</br>'
                    +'2.文档遗留类</br>'
                    + '说明：项目基线存在但不具备触发条件 //即同等条件基线不会测出问题，不认为是遗留；例如A模块存在问题，但B模块的修订触发的A模块的问题出现，认为非遗留。'

        },
    ];

    let customTip = {
        'bugNotifyLabel':{
            width: '750px',
            time: '10000',
            closeBtn: '1',
            tips : notifyTipInfo
        },
        'notifyTitle':{
            width: '750px',
            time: '10000',
            closeBtn: '1',
            tips : notifyTipInfo
        },
        'needNotify':{
            width: '750px',
            time: '10000',
            closeBtn: '1',
            tips : '开发对bug CBD/DELAY时，只要有一个审核人确认要发bug通告，系统自动创建bug通告初稿'
        }
    }

    function addNotifyTip(){
        setTimeout(function (){
            for (let id in customTip) {
                initCustomTip(id)
            }
        },500)
    }

    function initCustomTip(id){
        $("#" + id)
            .addClass("tips")
            .css("cursor", "pointer")
            .css("border-bottom", "1px dashed #000");
        $("#" + id).on('click',function (){
            showTipCustom(id)
        })
    }

    /**
     * 显示TIP custom
     * @param id
     */
    function showTipCustom(id){
        let item = customTip[id]
        let time = item.time
        let width = item.width
        let closeBtn = item.closeBtn
        let config = {
            tips : [3, '#3595CC'],
            time,
            closeBtn
        };
        if(width != null){
            config['area'] = [width];
        }
        layer.tips(item.tips, $('#'+id)[0], config);
    }
    var bugVersion = 1;
    var reviewing = false;
    $().ready(function() {
        try{
            initChooser();          //加载组件
            initData();             //加载数据
            initLog();              //加载日志
            addNotifyTip();
            initHelpTips(tipsArr, 'div');  //加载字段提示信息
        }catch (err){
            console.error(err);
            Notiflix.Report.failure('初始化失败提醒', '页面初始化失败，请联系管理员或稍后再试！', '确定');
        }finally {
            $(".mask").remove();
            setTimeout(function (){
                if(reviewing){
                    Notiflix.Confirm.show(
                        '三方评审提醒',
                        '当前BUG存在需要您评审的表单, 是否前往评审?',
                        '确定',
                        '取消',
                        function(){
                            locationView("review");
                        }
                    );
                }
            }, 500);
        }
    });

    //复制BUG链接
    $("#copyLink").click(function (){
        var bugUrl = "http://bugs.ruijie.com.cn/bug_switch/bug/main?bugId=1352718";
        new ClipboardJS('#copyLink',{text:function(trigger) { return bugUrl;} });
        Notiflix.Notify.success('BUG链接地址复制成功');
    })

    function goTop(){
        $("html,body").animate({ "scrollTop": 0}, 200);
    }

    function goBottom(){
        var windowHeight = parseInt($("body").css("height"));
        $("html,body").animate({ "scrollTop": windowHeight }, 200);
    }

    $("#mirrorBug").click(function (){
        var textTitle = "Bug镜像";
        var textUrl = '/bug_switch/bug_mirror/bug_mirror_view?bugId=1352718';
        Pages.showWindow(textTitle, textUrl, 285, 360);
    })

    $("#updateProgress").click(function (){
        var textTitle = "更新BUG进展";
        var textUrl = '/bug_switch/bug_progress/bug_progress_edit?progressId=&bugId=1352718&close=1';
        Pages.showWindow(textTitle,textUrl,660,290);
    })

    //加载顶部下拉菜单
    layui.use(['dropdown'], function(){
        layui.dropdown.render({
            elem: '#bugReview',
            trigger: 'hover',
            data: [
            				{ id: 'FORM_REVIEW_DELAY', title: 'DELAY审核' },
            				{ id: 'FORM_REVIEW_GIVEUP', title: 'GIVEUP审核' },
            				{ id: 'FORM_REVIEW_USER_CHANGE', title: '用户接口变化审核' },
            				{ id: 'FORM_REVIEW_BUGTOREQUIRE', title: 'BUG转需求池跟踪评审' },
                { type: '-' },
                { id: 'OTHER', title: '其他类型审批', 
                	child: [
	                				{
                                        id: 'FORM_REVIEW_RESTRICTION_MANUAL',
                                        title: '限制手册评审'
                                    },
	                				{
                                        id: 'FORM_REVIEW_SCHEME',
                                        title: '方案评审'
                                    },
                	]
                }
            ],
            click: function(obj){
                let state  = '13'
                let typeCode = obj.id
                if(typeCode === 'FORM_REVIEW_USER_CHANGE'){
                    //定义在include_bug_info中
                    let allowStates = [STATE_NEW, STATE_ASSIGNED, STATE_VERIFIED, STATE_RESOLVED, STATE_CHECKED];
                    if (allowStates.indexOf(state) === -1) {
                        Notiflix.Report.failure('操作失败',"只有【BUG状态】是NEW,ASSIGNED,VERIFIED,RESOLVED或CHECKED时才能发起此评审!",'确定');
                        return;
                    }else{
                        bugReview(obj.id);
                    }
                } else if(typeCode === 'FORM_INTERFACE_CHANGE_CBT'){
                    if(checkUserInterfaceReveiw()&& state === STATE_CBD) {
                        $("#state").combobox("setValue", STATE_CBT);
                        let rs = validateData()
                        $("#state").combobox({
                            value: state
                        });
                        if (rs) {
                            bugReview(obj.id);
                        }
                    }else {
                        Notiflix.Report.failure('操作失败', '根据配置，只有当前状态为CBD并且【是否用户接口修订】为是时才能发起此评审', '确定');
                        return;
                    }
                }else if(obj.id === 'FORM_INTERFACE_CHANGE_CBD'){
                    if(checkUserInterfaceReveiw()&& state ===STATE_CHECKED) {
                        $("#state").combobox("setValue", STATE_CBD);
                        let rs = validateData()
                        $("#state").combobox({
                            value: state
                        });
                        if (rs) {
                            bugReview(obj.id);
                        }
                    }else {
                        Notiflix.Report.failure('操作失败', '根据配置，只有当前状态为CHECKED并且【是否用户接口修订】为是时才能发起此评审', '确定');
                        return;
                    }
                }else {
                    bugReview(obj.id);
                }

            }
        });

        layui.dropdown.render({
            elem: '#bugView',
            trigger: 'hover',
            data: [
                { id: 'repeat',         title: 'BUG重复视图'},
                { id: 'relation',       title: 'BUG关系视图'},
                { id: 'noSync',         title: '永不同步视图'},
                { id: 'tempNoSync',     title: '暂不同步视图'},
                { id: 'revise',         title: '修订引入视图'},
            ],
            click: function(obj){
                bugView(obj.id);
            }
        });

        layui.dropdown.render({
            elem: '#bugModule',
            trigger: 'hover',
            data: [
                { id: 'base',           title: '基本信息视图'},
                { id: 'review',         title: '三方评审视图'},
                { id: 'test',           title: '测试人员视图'},
                { id: 'develop',        title: '开发人员视图'},
                { id: 'log',            title: '日志信息视图'},
                { id: 'ver-view',       title: '版本信息'},
                { id: 'reviewFormOld',  title: '历史评审表单'},
                { id: 'bugNotifyView',  title: 'BUG通告视图'},
                { id: 'interfaceDocView',  title: '接口文档变化视图'}
            ],
            click: function(obj){
                locationView(obj.id);
            }
        });

        window.locationView = function (id){
            var offset = $("#" + id).offset();
            if(!offset || offset == null){
                Notiflix.Notify.info("未找到该视图");
            }else{
                $("html, body").animate({scrollTop: (offset.top - 50)}, 200);
            }
        }

        window.bugReview = function(formTypeCode){
            var textTitle = "发起三方评审:1352718";
            var textUrl = '/bug_switch/bug_review_form/bug_review_form_apply?bugId=1352718&formTypeCode=' + formTypeCode;
            Pages.openTab({ title:textTitle, iconCls: 'icon-bug-1', url: textUrl});
        }

        function bugView(type){
            if(type == 'repeat'){
                var textTitle = "重复BUG：1352718";
                var textUrl = '/bug_switch/bug/bug_repeat_list?bugId=1352718';
                Pages.openTab({ title:textTitle, iconCls: 'icon-bug-1', url: textUrl});
            }else if(type == 'relation'){
                window.open("https://dataware.ruijie.com.cn/api/public/view/render/MDAwMDAwZjc=?bugId=1352718");
            }else if(type == 'noSync'){
                var textTitle='BUG永不同步视图:1352718';
                var textUrl='/bug_switch/bug_mirror/bug_nosyn_list?bugId=1352718';
                Pages.openTab({ title:textTitle, iconCls: 'icon_bug-16', url: textUrl});
            }else if(type == 'tempNoSync'){
                var textTitle='BUG暂不同步视图:1352718';
                var textUrl='/bug_switch/bug_mirror/bug_temp_not_mirror_list?bugId=1352718';
                Pages.openTab({ title:textTitle, iconCls: 'icon_bug-16', url: textUrl});
            }else if(type == 'revise'){
                var textTitle='BUG修订引入视图:1352718';
                var textUrl='/bug_switch/bug/bug_attention_list?baseBugId=1352718';
                Pages.openTab({ title:textTitle, iconCls: 'icon_bug-16', url: textUrl});
            }
        }
    });

    function closeCallBack(){
        Pages.closeWindow();
        Notiflix.Notify.success("进展更新成功, 系统于2秒后自动刷新");
        setTimeout(function(){
            window.location.reload();
        },2000);
    }

    function sameAs(type){
        if(type == 'sameAs'){
            var textTitle = "Bug SameAs";
            var textUrl = '/bug_switch/bug/bug_same_as_view?bugId=1352718';
            Pages.showWindow(textTitle, textUrl, 500, 210);
        }else{
            var url = "/bug_switch/bug/bug_same_as_del";
            var data = {
                bugId : 1352718
            };
            postAjax(url, data, function (data){
                if(data.type == 'success'){
                    Notiflix.Notify.success("清空SameAsBug操作成功. 系统于2秒后自动刷新");
                    setTimeout(function(){
                        window.location.reload();
                    },2000);
                }else{
                    Notiflix.Report.failure( '清除SameAsBug失败', data.content, '确定');
                }
            })
        }
    }

    //关注BUG
    function attentionBug(type){
        if(type == 'attention'){
            var url = '/bug_switch/bug/bug_attention'
        }else{
            var url = '/bug_switch/bug/bug_attention_cancel';
        }
        var data = {
            bugIds : 1352718
        }
        postAjax(url, data, function (res){
            if(res.type == 'success'){
                if(type == 'attention'){
                    $("#cancelAttentionBug").css("display","");
                    $("#attentionBug").css("display","none");
                    Notiflix.Notify.success('BUG关注成功');
                }else{
                    $("#cancelAttentionBug").css("display","none");
                    $("#attentionBug").css("display","");
                    Notiflix.Notify.success('BUG已取消关注');
                }
            }else{
                Notiflix.Notify.failure(res.content);
            }
        })
    }
    function checkUserInterfaceReveiw() {
        let reviewSwt =  ''
        let swt = '1'
        let resolvedok =  '3'
        return reviewSwt !== '1' && swt === '1' && resolvedok === '4'
    }

    function changeToState(){
        let stateNow =$('#state').combobox('getValue')
	        if(stateNow != STATE_CBD){
	            return null
	        }
        let oldState = '13'
        if (stateNow != oldState) {

            return stateNow
        }
        return null;
    }
    let shouldReload = false
    // 监听页面可见性变化事件
    document.addEventListener('focus', function() {
        debugger
        if(shouldReload){
            window.location.reload();
        }


    });
    /**
     *
     */
    function pageReload(){
        Notiflix.Notify.success("修订BUG信息成功,系统于2秒后自动刷新!");
        setTimeout(
            function(){
                window.location.reload();
            },2000);
    }

    function handleReview(reviewState, reload){
        if(reviewState == STATE_CBD){
            let content = '该项目BUG状态变更为 CBD 且【是否用户接口修订】为是, 需要发起三方评审, 如无需强制评审, 请联系PM进行配置, 是否前往发起三方评审?';
            Notiflix.Confirm.show('状态变更', content, '确定', '稍后评审',
                function () {
                    bugReview('FORM_INTERFACE_CHANGE_CBD');
                    if(reload){
                        // shouldReload = true

                    }
                },
                function () {
                    if(reload){
                        pageReload()
                    }
                }
            );
        }else if(reviewState == STATE_CBT){
            let content = '该项目BUG状态变更为 CBT 且【是否用户接口修订】为是, 需要发起三方评审, 如无需强制评审, 请联系PM进行配置, 是否前往发起三方评审?';
            Notiflix.Confirm.show('状态变更', content, '确定', '稍后评审',
                function () {
                    shouldReload = true
                    bugReview('FORM_INTERFACE_CHANGE_CBT');
                },
                function () {
                    pageReload()
                }
            );
        }
    }
    function validateData(){
        var bool = false;
        let msg = "如下数据未填写(点击字段名称进行定位)：<br>";
        $("#changeMessage").val(changeMessage.getContent());
        $(".required").each(function () {
            var id = $(this).attr("id");
            var classz = $(this).attr("class");
            let item = null;
            if($(this).hasClass('selfValueValidate')){
                item = this
            }else {
                let nameItems = document.getElementsByName(id)
                if(nameItems.length>0){
                    item = nameItems[0];
                }
            }
            if(item == null || item.value == null || item.value == ''){
                var column = $(this).attr("column");
                msg = msg + "【<a href='javaScript:findIndex(\"" + id + "\",\"" + classz + "\",\"" + column +"\")'>" + column + "</a>】";
                bool = true;
            }
        });

        if(bool){
            Notiflix.Report.warning('数据校验未通过', msg, '确定');
            return false;
        }
        var state = $("#state").combobox("getValue");
        //RESOLVED
        if(state == STATE_RESOLVED){
            var savebugid = $("#savebugid").commonchooser("getValue");
            var wbsName = $("#wbs").workpacketchooser("getInputText");
            var wbssaveid = wbsName.indexOf("遗留或delay的bug") != -1 ? '2' : '1';
            if(savebugid != wbssaveid){
                var content = "当前【工作包】的遗留状态与【遗留BUG】状态不一致，请开发与测试达成一致后，再将BUG状态变更为RESOLVED!"
                    + "<br><font color='red'>* 如对规则有疑问请联系【马晓靖】</font>"
                    + "<br>*【遗留BUG】只能由【测试负责人】修订"
                    + "<br>*【遗留BUG】的【工作包】应设置为【遗留或delay的bug】";
                Notiflix.Report.warning('数据校验未通过', content, '确定');
                return false;
            }
        }
        if(state == STATE_RESOLVED || state == STATE_CBD){
            var comInfoArr = $("input[name='comInfo']").val();
            if(comInfoArr == null){
                Notiflix.Report.warning('数据校验未通过', '【发现BUG的组件】还未填写，选择后，别忘记点击"关联"按钮！', '确定',function (){
                    layer.tips("发现BUG的组件", "#relationBtn");
                    $("#relationBtn").focus();
                });
                return false;
            }
        }
        return true;
    }
    //提交表单信息
    $("#submitBtn").click(function (){
        let pass = validateData();
        if(!pass){
            return
        }
        let reviewState = null
        if(checkUserInterfaceReveiw()) {
            let toState = changeToState()
            if(toState){
                $('#state').combobox('setValue','13')
            }
            reviewState = toState
        }

        var url = $("#inputForm").attr("action");
        var data = $("#inputForm").serialize();


        postAjax(url,data,function(res){
            if (res.type == 'success') {
                if(reviewState){
                    handleReview(reviewState, true)
                }else {
                    pageReload()
                }
            }else if(res.data != null){
                var html = '当前BUG于:' + res.content + '被更新！<br/>' +
                    '更新日志:<br/>' +
                    '============================<br/>' +
                    res.data + '<br/>' +
                    '============================<br/>' +
                    '<font style="color: red">' +
                    '【查看最新BUG信息】将会打开一个新的窗口,请留意当前BUG的页面！<br/>' +
                    '请保存好您的更新内容,前往新页面更新！' +
                    '</font>';
                Notiflix.Confirm.show(
                    '<a>系统提示</a>',
                    html,
                    '查看最新BUG信息',
                    '取消',
                    function(){
                        openBugTab(1352718);
                    }
                );
            }else{
                if(reviewState && res.msg == 'no_change'){
                    handleReview(reviewState, false)
                    return
                }
                let errMsg = res.content;
                if(!errMsg){
                    errMsg = '未知错误,请联系管理员';
                }
                Notiflix.Report.failure('bug更新失败', errMsg, '确定');
            }
        });
    });
</script>
</html>
