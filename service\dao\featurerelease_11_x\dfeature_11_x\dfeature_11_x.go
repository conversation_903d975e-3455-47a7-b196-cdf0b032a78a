package dfeature_11_x

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/featurerelease_11_x"
	// "irisAdminApi/service/dao/user/duser"
)

const ModelName = "规则库管理"

type Response struct {
	ID uint `json:"id"`

	FileName           string `json:"file_name"`
	FileSize           uint   `json:"file_size"`
	FileMd5            string `json:"file_md5"`
	Version            string `json:"version"`
	ProductModels      string `json:"product_models"`
	SoftVersions       string `json:"soft_versions"`
	FeatureType        string `json:"feature_type"`
	FeatureVersions    string `json:"feature_versions"`
	FeatureBaseVersion string `json:"feature_base_version"`
	FileType           uint   `json:"file_type"`
	ReleaseDate        string `json:"release_date"`
	VersionDesc        string `json:"version_desc"`
	Sign               string `json:"sign"`
	UpdatedAt          string `json:"updated_at"`
	CreatedAt          string `json:"created_at"`
	Status             uint   `json:"status"`
	UpdateToMain       bool   `json:"update_to_main"`
}

type ListResponse struct {
	Response
}

type Request struct {
	FileName           string `json:"file_name" form:"file_name"`
	FileSize           uint   `json:"file_size" form:"file_size"`
	FileMd5            string `json:"file_md5" form:"file_md5"`
	Version            string `json:"version" form:"version"`
	ProductModels      string `json:"product_models" form:"product_models"`
	SoftVersions       string `json:"soft_versions" form:"soft_versions"`
	FeatureType        string `json:"feature_type" form:"feature_type"`
	FeatureVersions    string `json:"feature_versions" form:"feature_versions"`
	FeatureBaseVersion string `json:"feature_base_version" form:"feature_base_version"`
	FileType           uint   `json:"file_type" form:"file_type"`
	ReleaseDate        string `json:"release_date" form:"release_date"`
	VersionDesc        string `json:"version_desc" form:"version_desc"`
	Sign               string `json:"sign" form:"sign"`
	Resource           string `gorm:"size:10000" json:"resource,omitempty" form:"resource,omitempty"`
	UpdateToMain       bool   `json:"update_to_main" form:"update_to_main"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *featurerelease_11_x.Feature_11_X {
	return &featurerelease_11_x.Feature_11_X{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindAll() ([]*Response, error) {
	var items []*Response

	if err := easygorm.GetEasyGormDb().Model(Model()).Find(&items).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return items, err
	}
	return items, nil
}

func FindInIds(ids []uint) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id in ?", ids).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	return items, nil
}

func DeleteByUUID(uuid string) error {
	err := easygorm.GetEasyGormDb().Unscoped().Where("uuid = ?", uuid).Delete(Model()).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func UpdateStatus(id uint, object map[string]interface{}) error {

	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

// func FormatResponse(items []*ListResponse) {
// 	userIds := []uint{}
// 	for _, item := range items {
// 		userIds = append(userIds, item.UserID)
// 	}
// 	users, _ := duser.FindSimpleInIds(userIds)
// 	var userMap = make(map[uint]*duser.ApprovalResponse)
// 	for _, user := range users {
// 		userMap[user.Id] = user
// 	}
// 	for _, item := range items {
// 		item.User = userMap[item.UserID]
// 	}
// }
