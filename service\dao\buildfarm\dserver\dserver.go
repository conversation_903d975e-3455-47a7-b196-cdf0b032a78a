package dserver

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/buildfarm"
	"irisAdminApi/service/dao/buildfarm/dcronmakejob"
	"irisAdminApi/service/dao/buildfarm/dmakejob"
)

const ModelName = "编译服务器管理"

type Response struct {
	Id         uint   `json:"id"`
	UpdatedAt  string `json:"updated_at"`
	CreatedAt  string `json:"created_at"`
	Name       string `json:"name"`
	Desc       string `json:"desc"`
	Host       string `json:"host"`
	Port       uint   `json:"port"`
	Username   string `json:"username"`
	Key        bool   `json:"key"`
	Enable     bool   `json:"enable"`
	Type       string `json:"type"`
	Parallel   uint   `json:"parallel"`
	Prometheus uint   `json:"prometheus"`
}

type StatusResponse struct {
	Id           uint                        `json:"id"`
	Name         string                      `json:"name"`
	Desc         string                      `json:"desc"`
	Host         string                      `json:"host"`
	Enable       bool                        `json:"enable"`
	Parallel     uint                        `json:"parallel"`
	Type         string                      `json:"type"`
	Running      []*dmakejob.MakeJob         `gorm:"-" json:"running"`
	Queue        []*dmakejob.MakeJob         `gorm:"-" json:"queue"`
	CronRunning  []*dcronmakejob.CronMakeJob `gorm:"-" json:"cron_running"`
	RuningCount  int                         `gorm:"-" json:"running_count"`
	QueuingCount int                         `gorm:"-" json:"queuing_count"`
}

type StatusResponseV2 struct {
	Id           uint   `json:"id"`
	Name         string `json:"name"`
	Desc         string `json:"desc"`
	Host         string `json:"host"`
	Enable       bool   `json:"enable"`
	Parallel     uint   `json:"parallel"`
	Type         string `json:"type"`
	RunningCount uint   `json:"running_count"`
}

type ListResponse struct {
	Response
}

type ServerReq struct {
	Name       string `json:"name"`
	Desc       string `json:"desc"`
	Host       string `json:"host"`
	Port       int    `json:"port"`
	Username   string `json:"username"`
	Password   string `json:"password"`
	Connection string `json:"connection"`
	Enable     bool   `json:"enable"`
	Parallel   int    `json:"parallel"`
	Prometheus int    `json:"prometheus"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *buildfarm.Server {
	return &buildfarm.Server{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindAllServer() ([]*Response, error) {
	var servers []*Response
	db := easygorm.GetEasyGormDb().Model(Model())

	err := db.Find(&servers).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	return servers, nil
}

func GetServerStatus() ([]*StatusResponseV2, error) {
	status := []*StatusResponseV2{}
	err := easygorm.GetEasyGormDb().Table(`
	(SELECT
		server_id id, NAME, HOST, ENABLE, parallel, TYPE, COUNT(u.id) running_count
	FROM
	(
		select
			j.id,
			j.status,
			j.source,
			s.id server_id,
			s.name,
			s.host,
			s.enable,
			s.parallel,
			s.type
		from
			servers s
		left join
		(
			select
				m.id, m.status, g.server_id, 1 as source
			from
				make_jobs m
			left join git_jobs g on g.job_id = m.job_id
			where m.status in (0,3) and m.deleted_at is null
			union
			select
				c.id, c.status, c.server_id, 2 as source
			from
				cron_make_jobs c
			where c.status in (0,3) and c.deleted_at is null
		) j on j.server_id = s.id
	) u
		GROUP BY server_id,NAME, HOST, ENABLE, parallel, TYPE
		ORDER BY running_count DESC) as servers`,
	).Find(&status).Error
	if err != nil {
		return status, err
	}
	return status, nil
}
