package codesync

import (
	"errors"
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/cache"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/codesync/dcodesynchistory"
	"irisAdminApi/service/dao/codesync/dcodesyncpolicy"
	"irisAdminApi/service/dao/codesync/dcodesyncqueue"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

func GetCodeSyncPolicies(ctx iris.Context) {
	// id, err := dao.GetAuthId(ctx)
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dcodesyncpolicy.ListResponse{}, ctx, name, sort, orderBy, page, pageSize)
	// list, err := dproblem.AllProblems(name, sort, orderBy, page, pageSize, status, start, end, department)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetCodeSyncPolicy(ctx iris.Context) {
	info := dcodesyncpolicy.Response{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

func CreateCodeSyncPolicy(ctx iris.Context) {
	// program, err := buildfarmProject.FindProduct()
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("create release project get err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未找到工程文件，请联系管理员"))
	// 	return
	// }

	request := &dcodesyncpolicy.Request{}
	if err := ctx.ReadJSON(request); err != nil {
		logging.ErrorLogger.Errorf("create policy read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	/*
		SourceRepositoryID   uint   `gorm:"not null;" json:"source_repository_id"`
		SourceRepositoryPath string `gorm:"not null; type:varchar(300)" json:"source_repository_path"`
		SourceBranch         string `gorm:"not null; type:varchar(300)" json:"source_branch"`
		TargetRepositoryID   uint   `gorm:"not null;" json:"target_repository_id"`
		TargetRepositoryPath string `gorm:"not null; type:varchar(300)" json:"target_repository_path"`
		TargetBranch         string `gorm:"not null; type:varchar(300)" json:"target_branch"`
		ExpiredAt            string `gorm:"not null; type:varchar(60)" json:"expired_at"`
		Enable               bool   `gorm:"not null; default:'false'" json:"enable"`
	*/
	if request.UpdatedAfter == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "同步开始时间不能为空"))
		return
	}
	err := dao.Create(&dcodesyncpolicy.Response{}, ctx, map[string]interface{}{
		"RepositoryID":   request.RepositoryID,
		"RepositoryPath": request.RepositoryPath,
		"RepositoryName": request.RepositoryName,
		"SourceBranch":   request.SourceBranch,
		"TargetBranch":   request.TargetBranch,
		"ExpiredAt":      request.ExpiredAt,
		"UpdatedAfter":   request.UpdatedAfter,
		"Enable":         request.Enable,
		"CreatedAt":      time.Now(),
		"UpdatedAt":      time.Now(),
	})
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, request, response.NoErr.Msg))
	return
}

func UpdateCodeSyncPolicy(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	request := &dcodesyncpolicy.Request{}
	if err := ctx.ReadJSON(request); err != nil {
		logging.ErrorLogger.Errorf("create approval read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	if request.UpdatedAfter == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "同步开始时间不能为空"))
		return
	}
	object := map[string]interface{}{
		"ID":             id,
		"RepositoryID":   request.RepositoryID,
		"RepositoryPath": request.RepositoryPath,
		"RepositoryName": request.RepositoryName,
		"SourceBranch":   request.SourceBranch,
		"TargetBranch":   request.TargetBranch,
		"ExpiredAt":      request.ExpiredAt,
		"UpdatedAfter":   request.UpdatedAfter,
		"Enable":         request.Enable,
		"CreatedAt":      time.Now(),
		"UpdatedAt":      time.Now(),
	}

	err := dao.Update(&dcodesyncpolicy.Response{}, ctx, object)
	// err = transproblem.UpdateProblem(uId, id, object)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func DeleteCodeSyncPolicy(ctx iris.Context) {
	info := dcodesyncpolicy.Response{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	err = dao.Delete(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

func CodeSyncWorker() {
	// 获取所有策略，遍历策略
	loc, _ := time.LoadLocation("Asia/Shanghai")
	policy := dcodesyncpolicy.Response{}
	policies, err := policy.All("", "", "", 10, -1)
	if err != nil {
		logging.ErrorLogger.Errorf("get policies err ", err)
	}
	if _, ok := policies["items"]; ok {
		items := policies["items"].([]*dcodesyncpolicy.ListResponse)
		for _, item := range items {
			if item.Enable {
				if item.ExpiredAt != "" {
					expiredAt, err := time.ParseInLocation("2006-01-02 15:04:05", item.ExpiredAt, loc)
					if err != nil {
						logging.ErrorLogger.Errorf("parse expire time err ", err)
						continue
					}
					if time.Now().UnixNano() < expiredAt.UnixNano() {
						CodeSyncJobWorker(item)
					}
				} else {
					CodeSyncJobWorker(item)
				}
			}
		}
	}
}

func CodeSyncJobWorker(policy *dcodesyncpolicy.ListResponse) {
	history := dcodesynchistory.Response{}
	repositoryID := strconv.FormatInt(int64(policy.RepositoryID), 10)
	historys, err := dcodesynchistory.FindHistoryByRepositoryIDAndStatus(repositoryID, 1)
	if err != nil {
		logging.ErrorLogger.Errorf("get history err ", err)
	}

	// //获取所有队列，状态为 进行中或者成功
	// queue := dcodesyncqueue.CodeSyncQueue{}
	// queues, err := queue.FindAllQueue()
	// queueMergeRequestIDs := []string{}
	// for _, queue := range queues {
	// 	queueMergeRequestIDs = append(queueMergeRequestIDs, queue.SourceMergeRequestID)
	// }

	// 获取历史记录中最后一条成功记录，取updated_until时间为起始时间，当前时间为结束时间，过滤所有MR列表

	loc, _ := time.LoadLocation("Asia/Shanghai")
	var updatedAfter, updatedBefore string
	if len(historys) > 0 {
		last := historys[0]
		updatedAfterTime, err := time.ParseInLocation("2006-01-02T15:04:05Z", last.UpdatedBefore, loc)
		if err != nil {
			logging.ErrorLogger.Errorf("parse updated after time get err ", err)
			return
		}
		fixDelta, _ := time.ParseDuration("-1h")
		updatedAfter = updatedAfterTime.Add(fixDelta).UTC().Format("2006-01-02T15:04:05Z")
		updatedBefore = time.Now().UTC().Format("2006-01-02T15:04:05Z")
	} else {
		if policy.UpdatedAfter != "" {
			updatedAfterTime, err := time.ParseInLocation("2006-01-02 15:04:05", policy.UpdatedAfter, loc)
			if err != nil {
				logging.ErrorLogger.Errorf("parse updated after time get err ", err)
				return
			}

			updatedAfter = updatedAfterTime.UTC().Format("2006-01-02T15:04:05Z")
		}
		updatedBefore = time.Now().UTC().Format("2006-01-02T15:04:05Z")
	}

	// 获取策略的源仓库ID下所有MR列表
	results, url, err := GetMergeRequestList(repositoryID, updatedAfter, updatedBefore)
	if err != nil {
		logging.ErrorLogger.Errorf("get merge request list", err)
		return
	}
	// 生成历史记录
	historyStatus := 2
	for _, mr := range results {
		// 判断当前MR是否未处理
		queue := dcodesyncqueue.CodeSyncQueue{}
		err := queue.FindQueue(mr.ID, policy.SourceBranch, policy.TargetBranch)
		if err != nil {
			logging.ErrorLogger.Errorf("get merge request list", err)
			return
		}

		// 检查是否系统生成的MR
		queueCreateBySystem := dcodesyncqueue.CodeSyncQueue{}
		err = queueCreateBySystem.FindEx("target_merge_request_id", strconv.FormatInt(int64(mr.ID), 10))
		if err != nil {
			logging.ErrorLogger.Errorf("get merge request list", err)
			return
		}

		switch mr.State {
		case "merged":

			if mr.TargetBranch == policy.SourceBranch && queue.ID == 0 && queueCreateBySystem.ID == 0 {
				if err := CodeSyncHandler(mr, policy); err != nil {
					logging.ErrorLogger.Errorf("code sync handler err", err)
				}
			}

			if mr.TargetBranch == policy.TargetBranch {
				err := queue.Update(uint(queueCreateBySystem.ID), map[string]interface{}{
					"Status": 3,
				})
				if err != nil {
					logging.ErrorLogger.Errorf("update queue status err ", err)
				} else {
					SendMail(&queue, mr, 3)
				}
			}

		case "closed":
			if mr.TargetBranch == policy.TargetBranch {
				err := queue.Update(uint(queueCreateBySystem.ID), map[string]interface{}{
					"Status": 4,
				})
				if err != nil {
					logging.ErrorLogger.Errorf("update history status", err)
				}
			}
		default:
			if mr.TargetBranch == policy.TargetBranch && mr.State != "opened" {
				err := queue.Update(uint(queueCreateBySystem.ID), map[string]interface{}{
					"Status": 5,
				})
				if err != nil {
					logging.ErrorLogger.Errorf("update history status", err)
				}
			}
		}
	}
	historyStatus = 1
	defer func() {
		history.Create(map[string]interface{}{
			"Url":           url,
			"RepositoryID":  repositoryID,
			"UpdatedAfter":  updatedAfter,
			"UpdatedBefore": updatedBefore,
			"Status":        historyStatus,
		})
	}()

}

func CodeSyncHandler(mr *MergeRequestResponse, policy *dcodesyncpolicy.ListResponse) error {
	queue := dcodesyncqueue.CodeSyncQueue{}
	err := queue.Create(map[string]interface{}{
		"SourceMergeRequestID":       strconv.FormatInt(int64(mr.ID), 10),
		"SourceMergeRequestIID":      strconv.FormatInt(int64(mr.IID), 10),
		"SourceMergeRequestUsername": mr.Author.Username,
		"SourceWebUrl":               mr.WebUrl,
		"RepositoryID":               strconv.FormatInt(int64(mr.TargetProjectID), 10),
		"RepositoryPath":             policy.RepositoryPath,
		"RepositoryName":             policy.RepositoryName,
		"SourceBranch":               policy.SourceBranch,
		"TargetBranch":               policy.TargetBranch,
		"Status":                     0,
		"CreatedAt":                  time.Now(),
		"UpdatedAt":                  time.Now(),
	})
	if err != nil {
		logging.ErrorLogger.Errorf("create code sync queue error", err)
		return err
	}
	err = queue.FindEx("source_merge_request_id", strconv.FormatInt(int64(mr.ID), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("create code sync queue error", err)
		return err
	}
	// if ReplaceUser(&queue, mr) {
	// 	SendMail(&queue, mr, 0)
	// }
	ReplaceUser(&queue, mr)
	return CodeSyncCreateMergeRequest(&queue, mr, policy)
}

func UpdateQueue(id uint, data map[string]interface{}) {
	queue := dcodesyncqueue.CodeSyncQueue{}
	err := queue.Update(id, data)
	if err != nil {
		logging.ErrorLogger.Errorf("update queue status error", err, id, data)
	}
}

var statusMap = map[uint]map[string]interface{}{
	// // 0 处理中 1 创建MR成功 2 创建MR失败 3 MR已合并 4 MR已关闭 5 未知状态
	0: {
		"name": "开始同步",
		"type": "通知",
	},
	1: {
		"name": "创建MR成功",
		"type": "待处理",
	},
	2: {
		"name": "创建MR失败",
		"type": "待处理",
	},
	3: {
		"name": "MR已合并",
		"type": "通知",
	},
	4: {
		"name": "MR已关闭",
		"type": "通知",
	},
	5: {
		"name": "未知状态",
		"type": "待处理",
	},
}

func ReplaceUser(queue *dcodesyncqueue.CodeSyncQueue, mr *MergeRequestResponse) bool {
	usernames := []string{}
	mailTo := []string{}
	for _, assignee := range mr.Assignees {
		usernames = append(usernames, assignee.Username)
		mailTo = append(mailTo, assignee.Username+"@ruijie.com.cn")
	}
	if len(usernames) > 0 {
		err := queue.ReplaceUsers(usernames)
		if err != nil {
			logging.ErrorLogger.Errorf("association user to queue err ", err)
			return false
		}
		return true
	}
	return false
}

func SendMail(queue *dcodesyncqueue.CodeSyncQueue, mr *MergeRequestResponse, status uint) {
	mailTo := []string{}
	for _, assignee := range mr.Assignees {
		mailTo = append(mailTo, assignee.Username+"@ruijie.com.cn")
	}

	mailTo = append(mailTo, "<EMAIL>")
	subject := fmt.Sprintf("[代码同步系统][%s][%s][%s]", mr.Title, statusMap[status]["name"], statusMap[status]["type"])
	var repo, sourceBranch, targetBranch, log string
	repo = queue.RepositoryName
	if status == 0 {
		sourceBranch = queue.SourceBranch
		targetBranch = queue.TargetBranch
	} else {
		sourceBranch = mr.SourceBranch
		targetBranch = mr.TargetBranch
	}

	err := queue.Find(queue.ID)
	if err != nil {
		log = "日志获取异常: " + err.Error()
	} else {
		log = strings.Replace(queue.Log, "\n", "\n<br>", -1)
	}
	body := fmt.Sprintf(`
			<p>检测到需要同步的MR: %s</p>
			<p>仓库：%s</p>
			<p>源分支：%s</p>
			<p>目标分支：%s</p>
			<p>标题：%s</p>
			<p>描述: %s</p>
			<p>当前同步状态：%s</p>

			<p>同步MR地址：%s</p>
			<p>同步日志：%s</p>
			<p><a href="http://aqyfzx.ruijie.net:9090/codesync/">代码同步系统</a><p>`,
		mr.WebUrl,
		repo,
		sourceBranch,
		targetBranch,
		mr.Title,
		mr.Description,
		statusMap[status]["name"],
		queue.TargetWebUrl,
		log,
	)

	rc := cache.GetRedisClusterClient()
	from := "代码同步系统"
	to := strings.Join(mailTo, ",")

	msg := strings.Join([]string{from, to, subject, body}, "|")
	if libs.Config.CodeSync.Mail {
		_, err = rc.LPush(libs.Config.Mail.Queue, msg)
		if err != nil {
			logging.ErrorLogger.Error(err)
		}
	}
	logging.DebugLogger.Debugf("send mail", mailTo, subject, body, err)
}

func CodeSyncCreateMergeRequest(queue *dcodesyncqueue.CodeSyncQueue, mr *MergeRequestResponse, policy *dcodesyncpolicy.ListResponse) error {
	status := 2
	var log []string
	defer func() {
		UpdateQueue(queue.ID, map[string]interface{}{
			"Status": status,
			"Log":    strings.Join(log, "\n"),
		})
		SendMail(queue, mr, uint(status))
		time.Sleep(10 * time.Second)
	}()
	repositoryID := strconv.FormatInt(int64(policy.RepositoryID), 10)
	commits, err := GetMergeRequestCommits(repositoryID, strconv.FormatInt(int64(mr.IID), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("delete code sync temp dir error", err)
		return err
	}

	// fork target repository
	// 检查是否已经存在FORK仓库
	fork, err := CheckProjectFork(repositoryID)
	if err != nil {
		logging.ErrorLogger.Errorf("delete code sync temp dir error", err)
		return err
	}

	workDir := filepath.Join("/mnt/sata0/codesync/repository", strings.Split(policy.RepositoryName, "/")[1])
	patchDir := filepath.Join("/mnt/sata0/codesync/patch", strings.Split(policy.RepositoryName, "/")[1])

	patchName := fmt.Sprintf("%s_%s_%d.patch", policy.SourceBranch, policy.TargetBranch, mr.ID)

	if _, err := os.Stat(patchDir); err != nil {
		err = os.MkdirAll(patchDir, 0755)
		os.Chmod(patchDir, 0755)
		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("mkdir error: %s", err.Error()))
			return err
		}
	}

	// 优化同步方式
	if _, err := os.Stat(workDir); err != nil {
		command := fmt.Sprintf("git clone %s %s && cd %s && git remote add upstream %s ", fork.SshUrlToRepo, workDir, workDir, policy.RepositoryPath)
		output, err := libs.ExecCommand(command)
		log = append(log, "初始化，拉取Fork项目", command, output)
		if err != nil {
			logging.ErrorLogger.Errorf("git clone get err ", err)
			return err
		}
	}
	command := fmt.Sprintf("cd %s && git am --abort", workDir)
	output, _ := libs.ExecCommand(command)
	log = append(log, "初始化应用Patch状态", command, output)

	command = fmt.Sprintf("cd %s && git checkout %s && git fetch upstream && git rebase upstream/%s", workDir, policy.SourceBranch, policy.SourceBranch)
	output, err = libs.ExecCommand(command)
	log = append(log, "同步主仓源分支提交", command, output)
	if err != nil {
		logging.ErrorLogger.Errorf("git rebase get err ", err)
		return err
	}
	if len(commits) > 1 {
		command = fmt.Sprintf("cd %s && git format-patch %s^..%s --stdout > %s", workDir, commits[len(commits)-1].ID, commits[0].ID, filepath.Join(patchDir, patchName))
	} else {
		command = fmt.Sprintf("cd %s && git format-patch %s -1 --stdout > %s", workDir, commits[0].ID, filepath.Join(patchDir, patchName))
	}

	output, err = libs.ExecCommand(command)
	log = append(log, "生成patch文件", command, output)
	if err != nil {
		logging.ErrorLogger.Errorf("git format-patch get err ", err)
		return err
	}

	command = fmt.Sprintf("cd %s && git checkout origin/%s && git checkout -B %s && git fetch upstream && git rebase upstream/%s && git am %s --whitespace=fix", workDir, policy.TargetBranch, fmt.Sprintf("%s_%s_%d", policy.SourceBranch, policy.TargetBranch, mr.ID), policy.TargetBranch, filepath.Join(patchDir, patchName))
	output, err = libs.ExecCommand(command)
	log = append(log, "应用patch文件", command, output)
	if err != nil {
		logging.ErrorLogger.Errorf("git apply patch get err ", err)
		return err
	}
	// git remote add upstream url

	// command = fmt.Sprintf(`cd %s && git remote add upstream %s && git checkout -b %s`, workDir, fork.SshUrlToRepo, fmt.Sprintf("%s-sync", policy.TargetBranch))
	// output, err = libs.ExecCommand(command)
	// log = append(log, "初始化，创建新分支", command, output)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("git cherry-pick get err ", err)
	// 	return err
	// }

	// output, err = libs.ExecCommand(command)
	// log = append(log, "使用cherry-pick快速导入commits", command, output)
	// if err != nil {
	// 	if strings.Contains(output, "conflicts") {
	// 		// 冲突，执行冲突操作
	// 		// todo:
	// 		/*
	// 			git checkout --theirs path/to/file
	// 			git add path/to/file
	// 			git cherry-pick --continue
	// 		*/
	// 		status = 2
	// 	}
	// 	logging.ErrorLogger.Errorf("git cherry-pick get err ", err)
	// 	return nil
	// }
	command = fmt.Sprintf("cd %s && git push -f origin %s", workDir, fmt.Sprintf("%s_%s_%d", policy.SourceBranch, policy.TargetBranch, mr.ID))
	output, err = libs.ExecCommand(command)
	log = append(log, "推送修订至Fork仓库", command, output)
	if err != nil {
		logging.ErrorLogger.Errorf("git push get err ", err)
		return err
	}
	// create merge request
	assigneeIds := []int{}
	reviewerIds := []int{}
	for _, assignee := range mr.Assignees {
		assigneeIds = append(assigneeIds, assignee.ID)
	}
	for _, reviewer := range mr.Reviewers {
		reviewerIds = append(reviewerIds, reviewer.ID)
	}
	if !libs.InArrayInt(reviewerIds, mr.Author.ID) {
		reviewerIds = append(reviewerIds, mr.Author.ID)
	}

	newDescription := strings.Join([]string{
		mr.Description,
		"【自动同步MR】",
		fmt.Sprintf("源MR:%s", mr.WebUrl),
	}, "  \n")
	log = append(log, "开始创建MR...")
	// fmt.Sprintf("%s\n%s\n%s", mr.Description, "自动同步MR", )
	_mr, err := CreateMergeRequest(strconv.FormatInt(int64(fork.ID), 10), strconv.FormatInt(int64(policy.RepositoryID), 10), fmt.Sprintf("%s_%s_%d", policy.SourceBranch, policy.TargetBranch, mr.ID), policy.TargetBranch, fmt.Sprintf("【自动同步MR】%s", mr.Title), newDescription, assigneeIds, reviewerIds)
	if err != nil {
		logging.ErrorLogger.Errorf("create merge request err ", err, strconv.FormatInt(int64(fork.ID), 10), strconv.FormatInt(int64(policy.RepositoryID), 10), fmt.Sprintf("%s_%s_%d", policy.SourceBranch, policy.TargetBranch, mr.ID), policy.TargetBranch, mr.Title, mr.Description, assigneeIds, reviewerIds)
		log = append(log, fmt.Sprintf("创建MR失败: %s", err.Error()))
		return err
	} else {
		log = append(log, fmt.Sprintf("创建MR成功,等待合并,%s", _mr.WebUrl))
	}
	UpdateQueue(queue.ID, map[string]interface{}{
		"TargetMergeRequestID":  _mr.ID,
		"TargetMergeRequestIID": _mr.IID,
		"TargetWebUrl":          _mr.WebUrl,
	})
	status = 1
	return nil
}

func GetMergeRequestList(repositoryID, updatedAfter, updatedBefore string) ([]*MergeRequestResponse, string, error) {
	url := fmt.Sprintf("%s/api/%s/projects/%s/merge_requests", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, repositoryID)

	var total []*MergeRequestResponse
	var errMsg string
	data := map[string]string{
		"private_token":  libs.Config.CodeSync.Token,
		"updated_after":  updatedAfter,
		"updated_before": updatedBefore,
		"scope":          "all",
		"order_by":       "updated_at",
		"sort":           "asc",
	}
	page := 1
	for {
		var result []*MergeRequestResponse
		resp, err := CodeSyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(data).SetQueryParam("page", strconv.FormatInt(int64(page), 10)).Get(url)
		if err != nil {
			return result, url, errors.New(fmt.Sprintf("%s, %s", err.Error(), errMsg))
		}
		if resp.IsSuccessState() {
			total = append(total, result...)
		}
		if resp.Header.Get("X-Total-Pages") == strconv.FormatInt(int64(page), 10) {
			break
		}
		page++
	}
	sort.SliceStable(total, func(i, j int) bool {
		if total[i].MergedAt < total[j].MergedAt {
			return true
		}
		return false
	})
	return total, url, nil
}

func CreateMergeRequest(repositoryID, targetProjectId, sourceBranch, targetBranch, title, description string, assigneeIds, reviewerIds []int) (MergeRequestResponse, error) {
	url := fmt.Sprintf("%s/api/%s/projects/%s/merge_requests", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, repositoryID)
	var result MergeRequestResponse
	var errMsg string
	params := map[string]string{
		"private_token": libs.Config.CodeSync.Token,
	}
	data := map[string]interface{}{
		"target_project_id": targetProjectId,
		"title":             title,
		"description":       description,
		"source_branch":     sourceBranch,
		"target_branch":     targetBranch,
		"assignee_ids":      assigneeIds,
		"reviewer_ids":      reviewerIds,
	}
	resp, err := CodeSyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(params).SetBody(data).Post(url)
	if err != nil {
		return result, errors.New(fmt.Sprintf("%s, %s", err.Error(), errMsg))
	}
	if resp.IsSuccessState() {
		return result, nil
	}
	return result, errors.New(fmt.Sprintf("%s, %s", "未知错误", errMsg))
}

func GetMergeRequestCommits(repositoryID, mergeRequestIID string) ([]*CommitsResponse, error) {
	url := fmt.Sprintf("%s/api/%s/projects/%s/merge_requests/%s/commits", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, repositoryID, mergeRequestIID)
	var result []*CommitsResponse
	var errMsg string
	data := map[string]string{
		"private_token": libs.Config.CodeSync.Token,
	}
	resp, err := CodeSyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(data).Get(url)
	if err != nil {
		return result, errors.New(fmt.Sprintf("%s, %s", err.Error(), errMsg))
	}
	if resp.IsSuccessState() {
		return result, nil
	}
	return result, errors.New(fmt.Sprintf("%s, %s", "未知错误", errMsg))
}

func GetMergeRequestDiffs(repositoryID, mergeRequestIID string) ([]*CommitsResponse, error) {
	url := fmt.Sprintf("%s/api/%s/projects/%s/merge_requests/%s/commits", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, repositoryID, mergeRequestIID)
	var result []*CommitsResponse
	var errMsg string
	data := map[string]string{
		"private_token": libs.Config.CodeSync.Token,
	}
	resp, err := CodeSyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(data).Get(url)
	if err != nil {
		return result, errors.New(fmt.Sprintf("%s, %s", err.Error(), errMsg))
	}
	if resp.IsSuccessState() {
		return result, nil
	}
	return result, errors.New(fmt.Sprintf("%s, %s", "未知错误", errMsg))
}

func CreateProjectFork(repositoryID string) (ForkResponse, error) {
	url := fmt.Sprintf("%s/api/%s/projects/%s/fork", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, repositoryID)
	var result ForkResponse
	var errMsg string
	data := map[string]string{
		"private_token": libs.Config.CodeSync.Token,
	}
	resp, err := CodeSyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(data).Post(url)
	if err != nil {
		return result, errors.New(fmt.Sprintf("%s, %s", err.Error(), errMsg))
	}
	if resp.IsSuccessState() {
		return result, nil
	}
	return result, errors.New(fmt.Sprintf("%s, %s", "未知错误", errMsg))
}

func GetProjectFork(repositoryID string) ([]*ForkResponse, error) {
	url := fmt.Sprintf("%s/api/%s/projects/%s/forks", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, repositoryID)
	var result []*ForkResponse
	var errMsg string
	data := map[string]string{
		"private_token": libs.Config.CodeSync.Token,
	}
	resp, err := CodeSyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(data).Get(url)
	if err != nil {
		return result, errors.New(fmt.Sprintf("%s, %s", err.Error(), errMsg))
	}
	if resp.IsSuccessState() {
		return result, nil
	}
	return result, errors.New(fmt.Sprintf("%s, %s", "未知错误", errMsg))
}

func DeleteProject(projectID int) error {
	url := fmt.Sprintf("%s/api/%s/projects/%d", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, projectID)
	var result string
	var errMsg string
	data := map[string]string{
		"private_token": libs.Config.CodeSync.Token,
	}
	resp, err := CodeSyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(data).Delete(url)
	if err != nil {
		return errors.New(fmt.Sprintf("%s, %s", err.Error(), errMsg))
	}
	if resp.IsSuccessState() {
		if strings.Contains(result, "Accepted") {
			return nil
		}
	}
	return errors.New(fmt.Sprintf("%s, %s", "未知错误", errMsg))
}

func CheckProjectFork(repositoryID string) (*ForkResponse, error) {
	var _fork *ForkResponse
	forked := false
	finished := false
	for i := 0; i <= 10; i++ {
		forks, err := GetProjectFork(repositoryID)
		if err != nil {
			logging.ErrorLogger.Errorf("delete code sync temp dir error", err)
			return _fork, err
		}

		for _, fork := range forks {
			forked = strings.HasPrefix(fork.PathWithNamespace, "buildfarm/")
			if forked {
				_fork = fork
				break
			}
		}
		if !forked && i == 0 {
			CreateProjectFork(repositoryID)
			continue
		}
		finished = _fork.ImportStatus == "finished"
		if finished {
			break
		}

		time.Sleep(5 * time.Second)
	}
	if forked && finished {
		return _fork, nil
	}
	return _fork, errors.New("查询fork项目失败: 未知错误")
}
