package documentauto

import (
	"irisAdminApi/application/middleware"

	"github.com/kataras/iris/v12"
)

var Party = func(party iris.Party) {
	party.Get("/downloadDocument", DownloadDocument).Name = "下载文档"
	party.Get("/templates/{id:uint}/downloadTemplate", DownloadTemplate).Name = "下载模板"
	party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) //登录验证
	party.Get("/getSeries", GetSerieData).Name = "获取系列数据"
	party.Get("/getBranchs", GetBranchs).Name = "获取分支数据"
	party.Get("/getCompileReleases", GetCompileReleases).Name = "获取Release数据"
	party.Get("/getBaselines", GetBaselines).Name = "获取基线版本数据"
	party.Get("/getBinFiles", GetBinFiles).Name = "获取bin文件"
	party.Post("/submitReleaseDocTask", SubmitReleaseDocTask).Name = "提交发行文档任务"
	party.Get("/getReleaseDocTask", GetReleaseDocTask).Name = "获取发行文档任务"
	party.Get("/{id:uint}/getDocumentLists", GetDocumentLists).Name = "获取文档列表"
	party.Post("/regenerateTask", ReSubmitReleaseDocTask).Name = "重新提交发行文档任务"
	party.Get("/getTemplateLists", GetTemplateLists).Name = "获取模板列表"
	party.Post("/createTemplate", CreateNewTemplate).Name = "创建模板"
	party.Get("/version/getVersionLists", GetVersionLists).Name = "获取版本列表"
	party.Post("/version/createVersion", CreateVersion).Name = "创建版本"
	party.Post("/version/{id:uint}/updateVersion", UpdateVersion).Name = "更新版本"
	party.Post("/version/{id:uint}/deleteVersion", DeleteVersion).Name = "删除版本"
	party.Get("/history/getHistoryReleaseLists", GetHistoryLists).Name = "获取历史版本发行列表"
	party.Post("/history/createHistoryRelease", CreateHistoryRelease).Name = "创建历史版本发行记录"
	party.Post("/history/{id:uint}/updateHistoryRelease", UpdateHistoryRelease).Name = "更新历史版本发行记录"
	party.Post("/history/{id:uint}/deleteHistoryRelease", DeleteHistoryRelease).Name = "删除历史版本发行记录"

}
