package coredump

import (
	"fmt"
	"sync"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"

	"github.com/robfig/cron/v3"
)

// CoredumpScheduler Coredump定时任务调度器
type CoredumpScheduler struct {
	service          *CoredumpAutoSyncService
	cron             *cron.Cron
	entryID          cron.EntryID
	running          bool
	executing        bool
	mutex            sync.RWMutex
	lastRunTime      time.Time
	nextRunTime      time.Time
	executionHistory []ExecutionRecord
	historyMutex     sync.RWMutex
}

// ExecutionRecord 执行记录
type ExecutionRecord struct {
	ID           string         `json:"id"`
	StartTime    time.Time      `json:"start_time"`
	EndTime      time.Time      `json:"end_time"`
	Duration     time.Duration  `json:"duration"`
	TriggerType  string         `json:"trigger_type"` // "scheduled" 或 "manual"
	TriggerUser  string         `json:"trigger_user"` // 触发用户（手动触发时）
	Result       *ProcessResult `json:"result"`
	Success      bool           `json:"success"`
	ErrorMessage string         `json:"error_message"`
}

// SchedulerStatus 调度器状态
type SchedulerStatus struct {
	Running         bool      `json:"running"`
	Executing       bool      `json:"executing"`
	LastRunTime     time.Time `json:"last_run_time"`
	NextRunTime     time.Time `json:"next_run_time"`
	CronExpr        string    `json:"cron_expr"`
	TotalExecutions int       `json:"total_executions"`
	SuccessCount    int       `json:"success_count"`
	FailureCount    int       `json:"failure_count"`
}

// ExecutionHistory 执行历史
type ExecutionHistory struct {
	Total   int               `json:"total"`
	Page    int               `json:"page"`
	Size    int               `json:"size"`
	Records []ExecutionRecord `json:"records"`
}

// NewCoredumpScheduler 创建调度器
func NewCoredumpScheduler(service *CoredumpAutoSyncService) *CoredumpScheduler {
	return &CoredumpScheduler{
		service:          service,
		cron:             cron.New(cron.WithSeconds()),
		executionHistory: make([]ExecutionRecord, 0),
	}
}

// Start 启动定时任务
func (s *CoredumpScheduler) Start() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !libs.Config.FeiShuDoc.CoredumpEnable {
		return fmt.Errorf("Coredump功能未启用")
	}

	if s.running {
		return fmt.Errorf("定时任务已在运行")
	}

	cronExpr := libs.Config.FeiShuDoc.CoredumpCronExpr
	if cronExpr == "" {
		cronExpr = "0 0 * * * *" // 默认每小时执行一次
	}

	entryID, err := s.cron.AddFunc(cronExpr, s.executeScheduledTask)
	if err != nil {
		return fmt.Errorf("添加定时任务失败: %w", err)
	}

	s.entryID = entryID
	s.cron.Start()
	s.running = true

	// 计算下次运行时间
	s.updateNextRunTime()

	logging.InfoLogger.Infof("[COREDUMP] 定时任务已启动，表达式: %s", cronExpr)
	return nil
}

// Stop 停止定时任务
func (s *CoredumpScheduler) Stop() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.running {
		return
	}

	s.cron.Stop()
	s.running = false
	s.nextRunTime = time.Time{}

	logging.InfoLogger.Info("[COREDUMP] 定时任务已停止")
}

// IsRunning 检查是否正在运行
func (s *CoredumpScheduler) IsRunning() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.executing
}

// executeScheduledTask 执行定时任务
func (s *CoredumpScheduler) executeScheduledTask() {
	s.mutex.Lock()
	if s.executing {
		s.mutex.Unlock()
		logging.ErrorLogger.Errorf("[COREDUMP] 上一个任务仍在执行中，跳过本次执行")
		return
	}
	s.executing = true
	s.lastRunTime = time.Now()
	s.mutex.Unlock()

	defer func() {
		s.mutex.Lock()
		s.executing = false
		s.updateNextRunTime()
		s.mutex.Unlock()
	}()

	logging.InfoLogger.Info("[COREDUMP] 开始执行定时任务")

	result, err := s.service.ProcessCoredumpRecords()

	// 记录执行历史
	s.RecordExecution(result, "scheduled", "")

	if err != nil {
		logging.ErrorLogger.Errorf("[COREDUMP] 定时任务执行失败: %v", err)
	} else {
		logging.InfoLogger.Infof("[COREDUMP] 定时任务执行完成: 总计=%d, 成功=%d, 失败=%d",
			result.TotalRecords, result.SuccessRecords, result.FailedRecords)
	}
}

// RecordExecution 记录执行历史
func (s *CoredumpScheduler) RecordExecution(result *ProcessResult, triggerType, triggerUser string) {
	s.historyMutex.Lock()
	defer s.historyMutex.Unlock()

	record := ExecutionRecord{
		ID:          fmt.Sprintf("%d", time.Now().UnixNano()),
		StartTime:   result.StartTime,
		EndTime:     result.EndTime,
		Duration:    result.Duration,
		TriggerType: triggerType,
		TriggerUser: triggerUser,
		Result:      result,
		Success:     len(result.Errors) == 0,
	}

	if len(result.Errors) > 0 {
		record.ErrorMessage = fmt.Sprintf("%d个错误", len(result.Errors))
	}

	// 添加到历史记录（保持最近100条）
	s.executionHistory = append([]ExecutionRecord{record}, s.executionHistory...)
	if len(s.executionHistory) > 100 {
		s.executionHistory = s.executionHistory[:100]
	}
}

// GetStatus 获取调度器状态
func (s *CoredumpScheduler) GetStatus() SchedulerStatus {
	s.mutex.RLock()
	s.historyMutex.RLock()
	defer s.mutex.RUnlock()
	defer s.historyMutex.RUnlock()

	successCount := 0
	failureCount := 0
	for _, record := range s.executionHistory {
		if record.Success {
			successCount++
		} else {
			failureCount++
		}
	}

	return SchedulerStatus{
		Running:         s.running,
		Executing:       s.executing,
		LastRunTime:     s.lastRunTime,
		NextRunTime:     s.nextRunTime,
		CronExpr:        libs.Config.FeiShuDoc.CoredumpCronExpr,
		TotalExecutions: len(s.executionHistory),
		SuccessCount:    successCount,
		FailureCount:    failureCount,
	}
}

// GetSchedulerStatus 获取定时任务状态
func (s *CoredumpScheduler) GetSchedulerStatus() map[string]interface{} {
	status := s.GetStatus()
	return map[string]interface{}{
		"running":          status.Running,
		"executing":        status.Executing,
		"last_run_time":    status.LastRunTime,
		"next_run_time":    status.NextRunTime,
		"cron_expr":        status.CronExpr,
		"total_executions": status.TotalExecutions,
		"success_count":    status.SuccessCount,
		"failure_count":    status.FailureCount,
	}
}

// GetExecutionHistory 获取执行历史
func (s *CoredumpScheduler) GetExecutionHistory(page, pageSize int) ExecutionHistory {
	s.historyMutex.RLock()
	defer s.historyMutex.RUnlock()

	total := len(s.executionHistory)
	start := (page - 1) * pageSize
	end := start + pageSize

	if start >= total {
		return ExecutionHistory{
			Total:   total,
			Page:    page,
			Size:    pageSize,
			Records: []ExecutionRecord{},
		}
	}

	if end > total {
		end = total
	}

	return ExecutionHistory{
		Total:   total,
		Page:    page,
		Size:    pageSize,
		Records: s.executionHistory[start:end],
	}
}

// GetLastRunTime 获取最后运行时间
func (s *CoredumpScheduler) GetLastRunTime() time.Time {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.lastRunTime
}

// GetNextRunTime 获取下次运行时间
func (s *CoredumpScheduler) GetNextRunTime() time.Time {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.nextRunTime
}

// ResetLastRunTime 重置最后运行时间
func (s *CoredumpScheduler) ResetLastRunTime() {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.lastRunTime = time.Time{}
}

// updateNextRunTime 更新下次运行时间
func (s *CoredumpScheduler) updateNextRunTime() {
	if !s.running {
		s.nextRunTime = time.Time{}
		return
	}

	entries := s.cron.Entries()
	for _, entry := range entries {
		if entry.ID == s.entryID {
			s.nextRunTime = entry.Next
			break
		}
	}
}
