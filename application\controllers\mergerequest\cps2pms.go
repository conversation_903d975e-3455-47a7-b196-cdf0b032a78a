package mergerequest

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"sync"
	"time"

	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/mergerequest/dmergerequest"
	"irisAdminApi/service/dao/mergerequest/dmergerequestdiscussioncps"

	"github.com/kataras/iris/v12"
	"github.com/xanzy/go-gitlab"
)

var TimePattern = regexp.MustCompile(`(\d{4}-\d{2}-\d{2})T(\d{2}:\d{2}:\d{2})\.\d{3}Z`)

type CpsExtend struct {
	ProjectName    string `json:"project_name"`
	WorkPacketName string `json:"work_packet_name"`
	PS             string `json:"PS"`
}

type CpsCommitAuthor struct {
	Name  string `json:"name"`
	Email string `json:"email"`
}

type CpsCommit struct {
	*gitlab.Commit
	Author CpsCommit<PERSON><PERSON><PERSON> `json:"author"`
}

type CpsNoteAttributes struct {
	*gitlab.Note
	DiscussionID     string               `json:"discussion_id"`
	OriginalPosition *gitlab.NotePosition `json:"original_position"`
	Body             string               `json:"note"`
	Description      string               `json:"description"`
	LineCode         string               `json:"line_code"`
}

type CpsDiscussion struct {
	ObjectKind       string             `json:"object_kind"`
	EventType        string             `json:"event_type"`
	User             interface{}        `json:"user"`
	Project          *gitlab.Project    `json:"project"`
	ObjectAttributes *CpsNoteAttributes `json:"object_attributes"`
	MergeRequest     NoteMergeRequest   `json:"merge_request"`
	/*
	   "project_name": "NTOS1.0R9",
	   "work_packet_name": "DHCP-SERVER-SYNC",
	   "PS": "#缺陷#编码(基础问题)#严重#关闭#",
	*/
	Extend CpsExtend `json:"extend"`
}

type NoteMergeRequest struct {
	*gitlab.MergeRequest
	Source     *gitlab.Project `json:"source"`
	Target     *gitlab.Project `json:"target"`
	LastCommit *CpsCommit      `json:"last_commit"`
}

func GetMrCpses(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	mr := dmergerequest.MergeRequest{}
	err = mr.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	cpses, err := dmergerequestdiscussioncps.FindCpsByProjectAndMergerequestIID(int(mr.TargetProjectID), fmt.Sprintf("%v", mr.MergeRequestIID), 0)
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if len(cpses) == 0 {
		ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]interface{}{"items": []interface{}{}}, response.NoErr.Msg))
		return
	}

	// mrs from merge_requests table
	// for _, mr := range mrs
	//    cps from cps table

	//    for _, cp := range cps

	// sourceProject, _, err := git.Projects.GetProject(57, &gitlab.GetProjectOptions{})
	// // fmt.Println(sourceProject)

	targetProject, _, err := GoGitlabClient.Projects.GetProject(int(mr.TargetProjectID), &gitlab.GetProjectOptions{})
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	gitlabMr, _, err := GoGitlabClient.MergeRequests.GetMergeRequest(int(mr.TargetProjectID), int(mr.MergeRequestIID), &gitlab.GetMergeRequestsOptions{})
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	cpsDiscussions := []string{}

	for _, cps := range cpses {
		if cps.Discussion == "" ||
			cps.Reviewer == "buildfarm" ||
			cps.Reviewer == "aicodereviewer" ||
			!cps.DeletedAt.Time.IsZero() ||
			cps.Confirm == 5 {
			continue
		}

		var ps string

		if cps.GetCondition() == "否" {
			ps = fmt.Sprintf("#%s#%s#%s#%s#", cps.GetCategory(), cps.GetIntroduction(), cps.GetSeverity(), "关闭")
		} else {
			ps = fmt.Sprintf("#%s#%s#%s#%s#", cps.GetCategory(), cps.GetIntroduction(), cps.GetSeverity(), "")
		}

		discussion, _, err := GoGitlabClient.Discussions.GetMergeRequestDiscussion(int(mr.TargetProjectID), int(mr.MergeRequestIID), cps.DiscussionID)
		if err != nil {
			logging.ErrorLogger.Errorf(err.Error())
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}

		var note *gitlab.Note
		for _, _note := range discussion.Notes {
			if !_note.System && (_note.Type == "DiffNote" || _note.Type == "DiscussionNote") {
				note = _note
				break
			}
		}

		if note == nil || note.Position == nil {
			ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]interface{}{"items": []interface{}{}}, response.NoErr.Msg))
			return
		}

		commit, _, err := GoGitlabClient.Commits.GetCommit(int(mr.TargetProjectID), note.Position.HeadSHA)
		lastCommit := CpsCommit{
			Commit: commit,
			Author: CpsCommitAuthor{
				Name:  commit.AuthorName,
				Email: commit.AuthorEmail,
			},
		}

		note.Author.Email = note.Author.Username + "@ruijie.com.cn"
		bodys := []string{}

		for _, _note := range discussion.Notes {
			if !_note.System && (_note.Type == "DiffNote" || _note.Type == "DiscussionNote") {
				body := fmt.Sprintf("%s: %s", _note.Author.Name, _note.Body)
				if len(body) > 512 {
					body = body[:512]
				}
				bodys = append(bodys, body)
			}
		}

		if cps.Comment != "" {
			bodys = append(bodys, "【关闭说明】: "+cps.Comment)
		}

		attr := CpsNoteAttributes{
			Note:             note,
			DiscussionID:     discussion.ID,
			Body:             bodys[0],
			Description:      bodys[0],
			OriginalPosition: note.Position,
		}

		cpsDiscussion := CpsDiscussion{
			ObjectKind: "note",
			EventType:  "note",
			User:       &note.Author,
			Project:    targetProject,
			MergeRequest: NoteMergeRequest{
				MergeRequest: gitlabMr,
				LastCommit:   &lastCommit,
			},
			ObjectAttributes: &attr,
			Extend: CpsExtend{
				ProjectName:    mr.ReleaseProject,
				WorkPacketName: mr.WorkPackage,
				PS:             ps,
			},
		}
		jsonByte, err := json.Marshal(&cpsDiscussion)
		if err != nil {
			panic(err)
		}

		// 替换为所需格式
		jsonStr := TimePattern.ReplaceAllString(string(jsonByte), `$1 $2 UTC`)
		cpsDiscussions = append(cpsDiscussions, jsonStr)

		if len(bodys) > 1 {
			attr := CpsNoteAttributes{
				Note:             note,
				DiscussionID:     discussion.ID,
				Body:             strings.Join(bodys[1:], "\n"),
				Description:      strings.Join(bodys[1:], "\n"),
				OriginalPosition: note.Position,
			}

			cpsDiscussion := CpsDiscussion{
				ObjectKind: "note",
				EventType:  "note",
				User:       &note.Author,
				Project:    targetProject,
				MergeRequest: NoteMergeRequest{
					MergeRequest: gitlabMr,
					LastCommit:   &lastCommit,
				},
				ObjectAttributes: &attr,
				Extend: CpsExtend{
					ProjectName:    mr.ReleaseProject,
					WorkPacketName: mr.WorkPackage,
					PS:             ps,
				},
			}
			jsonByte, err := json.Marshal(&cpsDiscussion)
			if err != nil {
				panic(err)
			}

			// 替换为所需格式
			jsonStr := TimePattern.ReplaceAllString(string(jsonByte), `$1 $2 UTC`)
			cpsDiscussions = append(cpsDiscussions, jsonStr)
		} else {
			attr := CpsNoteAttributes{
				Note:             note,
				DiscussionID:     discussion.ID,
				Body:             "未回复，默认同意",
				Description:      "未回复，默认同意",
				OriginalPosition: note.Position,
			}

			cpsDiscussion := CpsDiscussion{
				ObjectKind: "note",
				EventType:  "note",
				User:       &note.Author,
				Project:    targetProject,
				MergeRequest: NoteMergeRequest{
					MergeRequest: gitlabMr,
					LastCommit:   &lastCommit,
				},
				ObjectAttributes: &attr,
				Extend: CpsExtend{
					ProjectName:    mr.ReleaseProject,
					WorkPacketName: mr.WorkPackage,
					PS:             ps,
				},
			}
			jsonByte, err := json.Marshal(&cpsDiscussion)
			if err != nil {
				panic(err)
			}

			// 替换为所需格式
			jsonStr := TimePattern.ReplaceAllString(string(jsonByte), `$1 $2 UTC`)
			cpsDiscussions = append(cpsDiscussions, jsonStr)
		}

		// jsonByte, err := json.Marshal(&cpsNote)
		// if err != nil {
		// 	panic(err)
		// }

		// re := regexp.MustCompile(`(\d{4}-\d{2}-\d{2})T(\d{2}:\d{2}:\d{2})\.\d{3}Z`)
		// // 替换为所需格式
		// jsonStr := re.ReplaceAllString(string(jsonByte), `$1 $2 UTC`)
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]interface{}{"items": cpsDiscussions}, response.NoErr.Msg))
	return
}

func GetMergeRequestsToSync(ctx iris.Context) {
	mrs, err := dmergerequest.FindMergeRequestsToSync()
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]interface{}{"items": mrs}, response.NoErr.Msg))
	return
}

type IDResponse struct {
	Code int64 `json:"code"`
	Data struct {
		Items []uint `json:"items"`
	} `json:"data"`
}

type CpsResponse struct {
	Code int64 `json:"code"`
	Data struct {
		Items []string `json:"items"`
	} `json:"data"`
}

/*
{"type":"success","content":"操作成功"}
*/
type PmsResponse struct {
	Type    string `json:"type"`
	Content string `json:"content"`
}

var SyncCpsToPmsLock sync.Mutex

func SyncCpsToPmsWorker() {
	if SyncCpsToPmsLock.TryLock() {
		defer SyncCpsToPmsLock.Unlock()
		logging.DebugLogger.Debugf("开始同步CPS数据至PMS系统")
		idUrl := "http://10.51.135.15:9090/mergerequest-api/api/v1/mr/mergerequests/tosync"

		idResponse := IDResponse{}
		resp, err := GitlabWebClient.R().SetSuccessResult(&idResponse).Get(idUrl)
		if err != nil {
			logging.ErrorLogger.Errorf(err.Error())
			return
		}
		if resp.IsErrorState() || idResponse.Code != 20000 {
			logging.ErrorLogger.Errorf(resp.String())
			return
		}

		for _, id := range idResponse.Data.Items {
			SyncCpsToPmsJob(id)
			SyncCodeQuantityToPmsJob(id)
			time.Sleep(5 * time.Second)
		}
		logging.DebugLogger.Debugf("完成同步CPS数据至PMS系统")
	}
}

func SyncCodeQuantityToPmsJob(id uint) error {
	cpsUrl := fmt.Sprintf("http://10.51.135.15:9090/mergerequest-api/api/v1/mr/mergerequests/%d/codequantity", id)
	cpsResponse := CpsResponse{}
	resp, err := GitlabWebClient.R().SetSuccessResult(&cpsResponse).Get(cpsUrl)
	if err != nil {
		logging.ErrorLogger.Errorf(cpsUrl, err.Error())
		return err
	}

	if resp.IsErrorState() || cpsResponse.Code != 20000 {
		logging.ErrorLogger.Error(cpsUrl, resp.String())
		return fmt.Errorf("%s", resp.String())
	}

	failCount := 0
	for _, cps := range cpsResponse.Data.Items {
		err := PostCodeQuantityToPms(id, cps)
		logging.DebugLogger.Debug(err, id, cps)
		if err != nil {
			failCount++
		}
		time.Sleep(1 * time.Second)
	}

	if failCount == 0 {
		mr := dmergerequest.MergeRequest{}
		err = mr.UpdateOmitUpdatedAt(id, map[string]interface{}{"PmsSync": true})
		if err != nil {
			logging.ErrorLogger.Errorf(err.Error())
			return err
		}
	}

	return nil
}

func PostCodeQuantityToPms(id uint, cps string) error {
	pmsResponse := PmsResponse{}
	syncUrl := "http://build.ruijie.com.cn/code_measure/interface/git_lab/commit"
	resp, err := GitlabWebClient.R().SetBodyJsonString(cps).SetSuccessResult(&pmsResponse).Post(syncUrl)
	if err != nil {
		logging.ErrorLogger.Errorf(syncUrl, err.Error())
		return err
	}
	if resp.IsErrorState() || pmsResponse.Type != "success" {
		logging.ErrorLogger.Errorf(syncUrl, resp.String())
		return fmt.Errorf(resp.String())
	}

	return nil
}

func SyncCpsToPmsJob(id uint) error {
	cpsUrl := fmt.Sprintf("http://10.51.135.15:9090/mergerequest-api/api/v1/mr/mergerequests/%d/discussion", id)
	cpsResponse := CpsResponse{}
	resp, err := GitlabWebClient.R().SetSuccessResult(&cpsResponse).Get(cpsUrl)
	if err != nil {
		logging.ErrorLogger.Errorf(cpsUrl, err.Error())
		return err
	}

	if resp.IsErrorState() || cpsResponse.Code != 20000 {
		logging.ErrorLogger.Errorf(cpsUrl, resp.String())
		return fmt.Errorf(resp.String())
	}

	failCount := 0
	for _, cps := range cpsResponse.Data.Items {
		err := PostCpsToPms(id, cps)
		if err != nil {
			failCount++
		}
		time.Sleep(1 * time.Second)
	}

	if failCount == 0 {
		mr := dmergerequest.MergeRequest{}
		err = mr.UpdateOmitUpdatedAt(id, map[string]interface{}{"PmsSync": true})
		if err != nil {
			logging.ErrorLogger.Errorf(err.Error())
			return err
		}
	}

	return nil
}

func PostCpsToPms(id uint, cps string) error {
	pmsResponse := PmsResponse{}
	syncUrl := "http://build.ruijie.com.cn/code_measure/interface/git_lab/comment"
	resp, err := GitlabWebClient.R().SetBodyJsonString(cps).SetSuccessResult(&pmsResponse).Post(syncUrl)
	if err != nil {
		logging.ErrorLogger.Errorf(syncUrl, err.Error())
		return err
	}
	if resp.IsErrorState() || pmsResponse.Type != "success" {
		logging.ErrorLogger.Errorf(syncUrl, resp.String())
		return fmt.Errorf(resp.String())
	}

	return nil
}
