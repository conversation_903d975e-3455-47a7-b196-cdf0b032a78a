package schedule

import (
	"irisAdminApi/application/controllers/datasync"
	"irisAdminApi/application/controllers/openfeishu"
	"irisAdminApi/application/logging"

	"github.com/pkg/errors"
)

func GetSyncProjectFromFeishu() ([]string, error) {
	projectNames := []string{}
	records, err := datasync.GetTableData("PIWvbp1DZaVcYMsHsvzcWiHqnbd", "tblSJThkgRXQs0Hg")
	if err != nil {
		logging.ErrorLogger.Error(err)
		return nil, errors.Wrap(err, "")
	}

	for _, record := range records {
		if value, ok := record.Fields["项目名称"]; ok {
			if value != nil {
				if vv, ok := value.(string); ok && vv != "" {
					projectNames = append(projectNames, vv)
				}
			}
		}
	}
	return projectNames, nil
}

func SyncPmsReviewDataToFeishuWorker() {
	projectNames, _ := GetSyncProjectFromFeishu()
	openfeishu.GetPMSReviewData(projectNames)
	openfeishu.SyncReviewDataV4(projectNames)
}
