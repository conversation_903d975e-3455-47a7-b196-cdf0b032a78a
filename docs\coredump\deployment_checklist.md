# Coredump记录自动化处理系统 - 部署检查清单

## 📋 部署前检查

### 🔧 环境准备

- [ ] **Go环境**: 确保Go版本 >= 1.19
- [ ] **网络连接**: 确保能访问飞书API和Bug系统
- [ ] **权限配置**: 确保有飞书多维表格的读写权限
- [ ] **目录权限**: 确保应用有配置文件读取权限

### 📊 飞书多维表格配置

#### 必需字段检查
- [ ] **SN** (文本) - 设备序列号
- [ ] **coredump组件** (文本) - 组件名称
- [ ] **软件版本** (文本) - 版本信息
- [ ] **设备型号** (文本) - 设备型号
- [ ] **coredump时间** (日期时间) - 发生时间
- [ ] **组件负责人** (人员) - 负责人信息
- [ ] **进程负责人** (人员) - 负责人信息

#### 同步控制字段
- [ ] **是否需要同步Bug系统** (单选: Y/N) - 用户控制字段

#### 系统管理字段（需要添加）
- [ ] **是否已同步bug系统** (单选: Y/N/"") - 系统管理
- [ ] **处理状态** (单选: ""/待处理/处理中/成功/失败) - 系统管理
- [ ] **Bug系统ID** (文本) - 系统填入
- [ ] **处理时间** (日期时间) - 系统填入
- [ ] **错误信息** (多行文本) - 系统填入
- [ ] **重试次数** (数字) - 系统填入
- [ ] **最后更新时间** (日期时间) - 系统填入

### 🔑 API权限配置

#### 飞书API权限
- [ ] **应用凭证**: AppID 和 AppSecret 已配置
- [ ] **多维表格权限**: 
  - [ ] 读取记录权限
  - [ ] 更新记录权限
  - [ ] 字段访问权限
- [ ] **API限流**: 了解并配置合理的调用频率

#### Bug系统API权限
- [ ] **接口地址**: Bug系统API地址可访问
- [ ] **认证配置**: Bug系统认证信息已配置
- [ ] **提交权限**: 确保有Bug提交权限

### ⚙️ 配置文件检查

#### application.yml
```yaml
feishudoc:
  enable: true
  appid: "cli_xxxxxxxxxxxxxxxx"          # ✅ 已配置
  appsecret: "xxxxxxxxxxxxxxxxxxxxxxxx"  # ✅ 已配置
  coredumpapptoken: "your_app_token"     # ✅ 已配置
  coredumptableid: "your_table_id"       # ✅ 已配置
  coredumpenable: true                   # ✅ 已启用
```

#### coredump_config.yaml
```yaml
coredump:
  page_size: 100                         # ✅ 已配置
  field_names:                           # ✅ 字段列表完整
    - "SN"
    - "coredump组件"
    # ... 其他字段
  
  scheduler:                             # ✅ 定时任务配置
    enabled: true
    cron_expr: "0 0 * * * *"
    max_run_time: 30
    concurrent: false
  
  field_mapping:                         # ✅ 字段映射配置
    sync_required_field: "是否需要同步Bug系统"
    processing_status_field: "处理状态"
    # ... 其他映射
```

## 🚀 部署步骤

### 1. 代码部署
- [ ] **源码下载**: 获取最新版本代码
- [ ] **依赖安装**: `go mod tidy`
- [ ] **编译构建**: `go build -o coredump-system main.go`
- [ ] **权限设置**: `chmod +x coredump-system`

### 2. 配置部署
- [ ] **配置文件**: 复制并修改配置文件
- [ ] **环境变量**: 设置必要的环境变量
- [ ] **日志目录**: 创建日志目录并设置权限

### 3. 服务启动
- [ ] **手动启动**: 先手动启动测试
- [ ] **功能验证**: 运行基础功能测试
- [ ] **服务注册**: 注册为系统服务（可选）

## ✅ 部署后验证

### 🔍 基础功能测试

#### 1. 服务状态检查
```bash
# 检查服务是否启动
curl http://localhost:8080/health

# 检查API状态
curl http://localhost:8080/api/coredump/status
```

#### 2. 飞书连接测试
```bash
# 测试飞书API连接
curl -X POST http://localhost:8080/api/coredump/process
```

#### 3. 定时任务测试
```bash
# 查看定时任务状态
curl http://localhost:8080/api/coredump/scheduler/status

# 启动定时任务
curl -X POST http://localhost:8080/api/coredump/scheduler/start
```

### 📊 数据流程验证

#### 1. 创建测试记录
在飞书多维表格中创建一条测试记录：
- [ ] 填写必填字段（SN、组件、版本等）
- [ ] 设置"是否需要同步Bug系统" = "Y"
- [ ] 确保"处理状态"为空（新记录）

#### 2. 触发处理
- [ ] 手动触发处理：`POST /api/coredump/process`
- [ ] 观察记录状态变化：空 → 处理中 → 成功/失败
- [ ] 检查Bug系统是否收到提交

#### 3. 状态验证
- [ ] **成功情况**:
  - [ ] 处理状态 = "成功"
  - [ ] 是否已同步bug系统 = "Y"
  - [ ] Bug系统ID 已填入
  - [ ] 处理时间 已记录
- [ ] **失败情况**:
  - [ ] 处理状态 = "失败"
  - [ ] 错误信息 已记录
  - [ ] 重试次数 已更新

### 🔄 异常情况测试

#### 1. 网络异常测试
- [ ] 断开网络连接，观察错误处理
- [ ] 恢复网络，验证重试机制

#### 2. 超时测试
- [ ] 模拟长时间处理，验证超时机制
- [ ] 检查超时记录是否被正确重置

#### 3. 并发测试
- [ ] 同时触发多个处理任务
- [ ] 验证并发控制是否正常

## 📈 监控配置

### 🔍 日志监控
- [ ] **应用日志**: 配置日志级别和输出位置
- [ ] **错误日志**: 重点监控ERROR级别日志
- [ ] **性能日志**: 监控处理耗时和成功率

### 📊 指标监控
- [ ] **处理成功率**: 监控Bug提交成功率
- [ ] **处理耗时**: 监控平均处理时间
- [ ] **API调用频率**: 监控飞书API调用频率
- [ ] **错误率**: 监控各类错误的发生频率

### 🚨 告警配置
- [ ] **处理失败告警**: 失败率超过阈值时告警
- [ ] **服务异常告警**: 服务不可用时告警
- [ ] **API限流告警**: 接近API限流时告警

## 🛠️ 运维工具

### 📝 管理脚本
- [ ] **启动脚本**: `start.sh`
- [ ] **停止脚本**: `stop.sh`
- [ ] **重启脚本**: `restart.sh`
- [ ] **状态检查脚本**: `status.sh`

### 🧪 测试脚本
- [ ] **API测试脚本**: `api_test_script.sh`
- [ ] **功能测试脚本**: `function_test.sh`
- [ ] **性能测试脚本**: `performance_test.sh`

### 📊 统计脚本
- [ ] **处理统计脚本**: 统计处理成功率
- [ ] **错误分析脚本**: 分析常见错误
- [ ] **性能分析脚本**: 分析性能瓶颈

## 🔒 安全检查

### 🔑 认证安全
- [ ] **API密钥**: 确保密钥安全存储
- [ ] **权限最小化**: 只授予必要的权限
- [ ] **密钥轮换**: 定期更换API密钥

### 🛡️ 网络安全
- [ ] **HTTPS**: 使用HTTPS连接
- [ ] **防火墙**: 配置必要的防火墙规则
- [ ] **访问控制**: 限制API访问来源

### 📝 审计日志
- [ ] **操作日志**: 记录所有重要操作
- [ ] **访问日志**: 记录API访问情况
- [ ] **错误日志**: 记录所有错误和异常

## 📚 文档准备

### 📖 用户文档
- [ ] **使用说明**: 如何使用系统
- [ ] **字段说明**: 各字段的含义和用法
- [ ] **常见问题**: FAQ文档

### 🔧 运维文档
- [ ] **部署文档**: 详细的部署步骤
- [ ] **配置文档**: 配置参数说明
- [ ] **故障排查**: 常见问题的解决方案

### 👥 培训材料
- [ ] **用户培训**: 面向最终用户的培训材料
- [ ] **管理员培训**: 面向系统管理员的培训材料
- [ ] **开发文档**: 面向开发人员的技术文档

## ✅ 上线检查清单

### 🚀 上线前最终检查
- [ ] 所有配置文件已正确配置
- [ ] 所有必需字段已在飞书表格中创建
- [ ] API权限已正确配置并测试
- [ ] 基础功能测试全部通过
- [ ] 异常情况测试全部通过
- [ ] 监控和告警已配置
- [ ] 文档已准备完整
- [ ] 相关人员已完成培训

### 📋 上线后验证
- [ ] 服务正常启动并运行
- [ ] 定时任务正常执行
- [ ] 处理流程正常工作
- [ ] 监控指标正常
- [ ] 用户反馈良好

### 🔄 持续改进
- [ ] 收集用户反馈
- [ ] 监控系统性能
- [ ] 定期检查和优化
- [ ] 版本更新和维护

---

**注意**: 此检查清单应该在每次部署时使用，确保系统的稳定性和可靠性。建议将此清单作为部署流程的一部分，逐项检查并记录结果。