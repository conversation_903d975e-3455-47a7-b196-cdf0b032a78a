package urlpack

import (
	"fmt"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/urlpack/durlpackcategory"
	"regexp"
	"strconv"
	"strings"

	"github.com/kataras/iris/v12"
	"github.com/xuri/excelize/v2"
)

// 获取单元格的引用
func getCellRef(col, row int) string {
	return fmt.Sprintf("%c%d", 'A'+col, row+1)
}

func CheckBool(v string) bool {
	if v == "1" {
		return true
	}
	return false
}

func ImportCategory(ctx iris.Context) {
	file, _, err := ctx.FormFile("file")

	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	defer file.Close()

	f, err := excelize.OpenReader(file)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	defer f.Close()

	sheetName := f.GetSheetName(0)

	if sheetName == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "导入的分类文件不存在表格"))
		return
	}

	// 获取 Sheet1 上所有单元格
	rows, err := f.GetRows(sheetName)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "处理excel失败，请重试。"+err.Error()))
		return
	}
	if len(rows) < 2 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "导入的分类文件没有有效内容"))
		return
	}

	templateHeaders := []string{"分组编号", "分组中文名", "分组英文名", "分类编号", "分类中文名", "分类英文名", "中文描述", "英文描述", "海外大库", "海外小库", "大库", "小库", "预定义阻断类"}
	for i := range templateHeaders {
		if rows[0][i] != templateHeaders[i] {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "导入的分类文件不是有效的文件"))
			return
		}
	}

	length := len(rows[0])
	var categorys []map[string]interface{}
	regexStr := "[`" + `~!#%^$&*+|{};:"/\\<>?]`
	pattern := regexp.MustCompile(regexStr)
	for rowNum := range rows[1:] {
		rowData := []string{}
		for col := 0; col < length; col++ {
			cell := getCellRef(col, rowNum+1)
			value, _ := f.GetCellValue(sheetName, cell)
			rowData = append(rowData, value)
		}
		if len(pattern.FindStringIndex(rowData[6])) > 0 {
			errStr := []string{}
			for _, idx := range pattern.FindStringIndex(rowData[6]) {
				errStr = append(errStr, rowData[idx])
			}
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("导入数据失败, %s分类的中文描述包含以下字符%s", rowData[4], strings.Join(errStr, ","))))
			return
		} else if len(pattern.FindStringIndex(rowData[7])) > 0 {
			errStr := []string{}
			for _, idx := range pattern.FindStringIndex(rowData[6]) {
				errStr = append(errStr, rowData[idx])
			}
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("导入数据失败，%s分类的英文描述包含以下字符%s", rowData[4], strings.Join(errStr, ","))))
			return
		}
		category := map[string]interface{}{
			"GroupID":        rowData[0],
			"GroupNameCH":    rowData[1],
			"GroupNameEN":    rowData[2],
			"CategoryID":     rowData[3],
			"CategoryNameCH": rowData[4],
			"CategoryNameEN": rowData[5],
			"DescriptionCH":  rowData[6],
			"DescriptionEN":  rowData[7],
			"OverseaLarge":   CheckBool(rowData[8]),
			"OverseaSmall":   CheckBool(rowData[9]),
			"Large":          CheckBool(rowData[10]),
			"Small":          CheckBool(rowData[11]),
			"PreDefBlock":    CheckBool(rowData[12]),
		}

		categorys = append(categorys, category)
	}

	if err := durlpackcategory.InitCategoryTable(categorys); err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "导入数据失败，请重试, "+err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetCategories(ctx iris.Context) {
	name := ctx.FormValue("search")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&durlpackcategory.UrlPackCategory{}, ctx, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}
