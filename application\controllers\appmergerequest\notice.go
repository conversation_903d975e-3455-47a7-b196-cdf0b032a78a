package appmergerequest

import (
	"encoding/json"
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/cache"
	"irisAdminApi/service/dao/appmergerequest/dappmergerequest"
	"irisAdminApi/service/dao/appmergerequest/dappmergerequestbugownersummary"
	"irisAdminApi/service/dao/appmergerequest/dappmergerequestbugrequirementsummary"
	"irisAdminApi/service/dao/appmergerequest/dappmergerequestbugsummary"
	"irisAdminApi/service/dao/appmergerequest/dappmergerequestbugworkgroupsummary"
	"irisAdminApi/service/dao/appmergerequest/dappmergerequestnotice"
	"irisAdminApi/service/dao/datasync/dsyncrecord"
	"irisAdminApi/service/dao/release/dreleaseprojectconfig"
	"strings"
	"time"
)

func CheckMergeRequestOverTime() {
	mrs, err := dappmergerequest.MergeRequestFilterOverTimeNotMerged()
	if err != nil {
		logging.ErrorLogger.Error("find mr error", err)
	}

	workPackageMRMap := libs.KvMap[string, []*dappmergerequest.AppMergeRequest]{}
	for _, mr := range mrs {
		now := time.Now()
		if mr.Status == 0 || mr.Status == 1 {
			if mr.Notice == nil || now.Sub(mr.Notice.UpdatedAt).Hours() >= 24 {
				if mr.RelateWorkPackage.Pstl.ID != 0 {
					if _, ok := workPackageMRMap[mr.RelateWorkPackage.Pstl.Username]; ok {
						workPackageMRMap[mr.RelateWorkPackage.Pstl.Username] = append(workPackageMRMap[mr.RelateWorkPackage.Pstl.Username], mr)
					} else {
						workPackageMRMap[mr.RelateWorkPackage.Pstl.Username] = []*dappmergerequest.AppMergeRequest{mr}
					}
				}
				if _, ok := workPackageMRMap[mr.User.Username]; ok {
					workPackageMRMap[mr.User.Username] = append(workPackageMRMap[mr.User.Username], mr)
				} else {
					workPackageMRMap[mr.User.Username] = []*dappmergerequest.AppMergeRequest{mr}
				}
			}
		}
	}
	SendMergeRequestOverTimeMail(workPackageMRMap)
}

func SendMergeRequestOverTimeMail(workPackageMRMap libs.KvMap[string, []*dappmergerequest.AppMergeRequest]) error {
	rc := cache.GetRedisClusterClient()
	from := "MR表单"
	for pstl, mrs := range workPackageMRMap {
		subject := "MR超过三天未合并提醒"
		mailTo, updates, body := FormShow(mrs)

		mailTo = append(mailTo, pstl+"@ruijie.com.cn", "<EMAIL>")
		cc := []string{}
		if libs.Config.Debug {
			mailTo = []string{"<EMAIL>"}
			if err := libs.SendMail(mailTo, subject, body, cc); err != nil {
				return err
			}
		}

		msg := strings.Join([]string{from, strings.Join(mailTo, ","), subject, body}, "|")
		_, err := rc.LPush(libs.Config.Mail.Queue, msg)
		if err != nil {
			logging.ErrorLogger.Error(err)

		}
		if len(updates) > 0 {
			err := dappmergerequestnotice.BatchUpdate(updates)
			if err != nil {
				logging.ErrorLogger.Error("update notice err ", err)
			}
		}
	}

	return nil
}

const MailMess = // 定义邮件表格样式
`<style>
	table {
		border-collapse: collapse;
	}
	th {
		background-color: #007fff;
		color: white;
	}
	table, th, td {
		border: 1px solid black;
		padding: 5px;
		text-align: left;
	}
</style>
<table>
	<tr>
		<th>项目</th>
		<th>标题</th>
		<th>工作包</th>
		<th>类型</th>
		<th>提交人</th>
		<th>合并人</th>
		<th>评审人</th>
	</tr>`

func FormShow(mrs []*dappmergerequest.AppMergeRequest) ([]string, []map[string]interface{}, string) { // 调用接口传过来的数据，拼接表格
	var mailMessage = MailMess
	updates := []map[string]interface{}{}
	mailTo := []string{}
	for _, mr := range mrs {
		assigneeNames := []string{}
		reviewerNames := []string{}

		for _, a := range mr.Assignees {
			assigneeNames = append(assigneeNames, a.Name)

		}
		for _, r := range mr.Reviewers {
			reviewerNames = append(reviewerNames, r.Name)

		}
		if !libs.InArrayS(mailTo, mr.RelateReleaseProject.Pm.Username+"@ruijie.com.cn") {
			mailTo = append(mailTo, mr.RelateReleaseProject.Pm.Username+"@ruijie.com.cn")
		}
		mailMessage = mailMessage + fmt.Sprintf("<td>%v</td><td>%v</td><td>%v</td><td>%v</td><td>%v</td><td>%v</td><td>%v</td></tr>",
			mr.ReleaseProject, mr.Title, mr.RelateWorkPackage.Name, mr.Type, mr.User.Name, strings.Join(assigneeNames, ","), strings.Join(reviewerNames, ","))
		notice := map[string]interface{}{
			"CreatedAt":      time.Now(),
			"UpdatedAt":      time.Now(),
			"MergeRequestID": mr.ID,
		}
		updates = append(updates, notice)
	}
	mailMessage = mailMessage + "</table><br></br>"
	return mailTo, updates, mailMessage
}

const BugWorkGroupMess = // 定义邮件表格样式
`<style>
	table {
		border-collapse: collapse;
	}
	th {
		background-color: #007fff;
		color: white;
	}
	table, th, td {
		border: 1px solid black;
		padding: 5px;
		text-align: left;
	}
</style >
<table style="width: 2300px">
<tr class="">
<th colspan="1" rowspan="1" class="el-table_1_column_1  is-center   is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);width: 200px"><div class="cell">项目</div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_2  is-center   is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);width: 200px"><div class="cell">所属专业组</div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_3  is-center   is-leaf el-table__cell" style="background-color: rgb(175, 204, 253); color: rgb(0, 0, 0);width: 100px"><div class="cell">合计提交</div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_4  is-center   is-leaf el-table__cell" style="background-color: rgb(175, 204, 253); color: rgb(0, 0, 0);width: 100px"><div class="cell">未CBD</div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_5  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(175, 204, 253); color: rgb(0, 0, 0);width: 100px"><div class="cell">所有BUG未决率</div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_6  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(175, 204, 253); color: rgb(0, 0, 0);width: 100px"><div class="cell">未关联工作包</div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_7  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(175, 204, 253); color: rgb(0, 0, 0);width: 100px"><div class="cell">checked+resolved</div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_8  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);width: 100px"><div class="cell"><span><span>测试提</span><br><span>Blocking</span><br><span>超期未决</span></span></div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_9  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);width: 100px"><div class="cell"><span><span>测试提</span><br><span>Critical</span><br><span>超期未决</span></span></div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_10  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);width: 100px"><div class="cell"><span><span>测试提</span><br><span>Major</span><br><span>超期未决</span></span></div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_11  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);width: 100px"><div class="cell"><span><span>测试提</span><br><span>Normal</span><br><span>超期未决</span></span></div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_12  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);width: 100px"><div class="cell"><span><span>开发提</span><br><span>超期未决</span><br><span></span></span></div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_13  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(251, 197, 123); color: rgb(0, 0, 0);width: 100px"><div class="cell">Blocking未决</div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_14  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(251, 197, 123); color: rgb(0, 0, 0);width: 100px"><div class="cell">Critical未决</div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_15  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(251, 197, 123); color: rgb(0, 0, 0);width: 100px"><div class="cell">Major未决</div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_16  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(251, 197, 123); color: rgb(0, 0, 0);width: 100px"><div class="cell">Normal未决</div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_17  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(251, 197, 123); color: rgb(0, 0, 0);width: 100px"><div class="cell">Trivial未决</div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_18  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);width: 100px"><div class="cell">测试提交BUG数</div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_19  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);width: 100px"><div class="cell">测试提交未CBD</div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_20  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);width: 100px"><div class="cell">测试提交未决率</div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_21  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);width: 100px"><div class="cell">开发提交BUG数</div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_22  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);width: 100px"><div class="cell">开发提交未CBD</div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_23  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);width: 100px"><div class="cell">开发提交未决率</div></th>
</tr>`

const BugOwnerMess = // 定义邮件表格样式
`<style>
		table {
			border-collapse: collapse;
		}
		th {
			background-color: #007fff;
			color: white;
		}
		table, th, td {
			border: 1px solid black;
			padding: 5px;
			text-align: left;
		}
	</style>
	<table style="width: 1800px">
<tr class="">
<th colspan="1" rowspan="1" class="el-table_2_column_24  is-center   is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);"><div class="cell">项目</div></th>
<th colspan="1" rowspan="1" class="el-table_2_column_25  is-center   is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);"><div class="cell">工程师</div></th>
<th colspan="1" rowspan="1" class="el-table_2_column_26  is-center   is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);"><div class="cell">所属专业组</div></th>
<th colspan="1" rowspan="1" class="el-table_2_column_27  is-center   is-leaf el-table__cell" style="background-color: rgb(175, 204, 253); color: rgb(0, 0, 0);"><div class="cell">合计提交</div></th>
<th colspan="1" rowspan="1" class="el-table_2_column_28  is-center   is-leaf el-table__cell" style="background-color: rgb(175, 204, 253); color: rgb(0, 0, 0);"><div class="cell">未CBD</div></th>
<th colspan="1" rowspan="1" class="el-table_2_column_29  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(175, 204, 253); color: rgb(0, 0, 0);"><div class="cell">所有BUG未决率</div></th>
<th colspan="1" rowspan="1" class="el-table_2_column_30  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(175, 204, 253); color: rgb(0, 0, 0);"><div class="cell">未关联工作包</div></th>
<th colspan="1" rowspan="1" class="el-table_2_column_31  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);"><div class="cell"><span><span>测试提</span><br><span>Blocking</span><br><span>超期未决</span></span></div></th>
<th colspan="1" rowspan="1" class="el-table_2_column_32  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);"><div class="cell"><span><span>测试提</span><br><span>Critical</span><br><span>超期未决</span></span></div></th>
<th colspan="1" rowspan="1" class="el-table_2_column_33  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);"><div class="cell"><span><span>测试提</span><br><span>Major</span><br><span>超期未决</span></span></div></th>
<th colspan="1" rowspan="1" class="el-table_2_column_34  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);"><div class="cell"><span><span>测试提</span><br><span>Normal</span><br><span>超期未决</span></span></div></th>
<th colspan="1" rowspan="1" class="el-table_2_column_35  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);"><div class="cell"><span><span>开发提</span><br><span>超期未决</span><br><span></span></span></div></th>
<th colspan="1" rowspan="1" class="el-table_2_column_36  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(251, 197, 123); color: rgb(0, 0, 0);"><div class="cell">NEW</div></th>
<th colspan="1" rowspan="1" class="el-table_2_column_37  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(251, 197, 123); color: rgb(0, 0, 0);"><div class="cell">ASSIGNED</div></th>
<th colspan="1" rowspan="1" class="el-table_2_column_38  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(251, 197, 123); color: rgb(0, 0, 0);"><div class="cell">REQUEST-NO_TO_SOLVE</div></th>
<th colspan="1" rowspan="1" class="el-table_2_column_39  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(251, 197, 123); color: rgb(0, 0, 0);"><div class="cell">RESOLVED</div></th>
<th colspan="1" rowspan="1" class="el-table_2_column_40  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(251, 197, 123); color: rgb(0, 0, 0);"><div class="cell">CHECKED</div></th>
<th colspan="1" rowspan="1" class="el-table_2_column_41  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(251, 197, 123); color: rgb(0, 0, 0);"><div class="cell">REOPENED</div></th>
</tr>`

const SummaryMailMess = // 定义邮件表格样式
`<style>
	table {
		border-collapse: collapse;
	}
	th {
		background-color: #007fff;
		color: white;
	}
	table, th, td {
		border: 1px solid black;
		padding: 5px;
		text-align: left;
	}
</style>
<table>
`

func Test() error {
	summary1 := dappmergerequestbugrequirementsummary.BugRequirementSummary{}
	items1, err := summary1.All("NTOS1.0R6P1", "", 0, "desc", "total")
	if err != nil {
		return err
	}
	fmt.Println(items1)
	return nil
}

func BugSummaryWorker() error {
	fmt.Println("发送BUG超期提醒邮件")

	// 获取项目列表，查找打开通知的项目
	enabledNoticeProjects, err := dreleaseprojectconfig.AllEnabledConfig()
	if err != nil {
		logging.ErrorLogger.Error("get project config err ", err)
		return err
	}

	// 增加提示，显示BUG数据同步时间
	var updatedAt string
	_url := "https://dataware.ruijie.com.cn/api/public/data-api/security_product_sw_bug/list.data"
	records, err := dsyncrecord.FindLastSuccessSyncRecord(_url)
	if err != nil {
		logging.ErrorLogger.Errorf("get last sync records", err.Error())
		return err
	}
	if len(records) > 0 {
		updatedAt = records[0].CreatedAt.Format("2006-01-02 15:04:05")
	}

	for _, config := range enabledNoticeProjects {
		err := SendBugSummary(config, updatedAt)
		if err != nil {
			logging.ErrorLogger.Error("send bug summary err ", err)
		}
		err = SendBugOverTimeTopSummary(config, updatedAt)
		if err != nil {
			logging.ErrorLogger.Error("send bug overtime top summary err ", err)
		}
	}

	return nil
}

func SendBugSummary(config *dreleaseprojectconfig.ReleaseProjectConfig, updatedAt string) error {
	mailTo := []string{"<EMAIL>"}
	cc := []string{}
	summaryLabel := []map[string]string{
		{"label": "合计BUG", "key": "total"},
		{"label": "new+assigned", "key": "new_assigned"},
		{"label": "测试提Blocking超期未决", "key": "overtime_blocking_by_test"},
		{"label": "已决BUG", "key": "cbd_count"},
		{"label": "Checked+Resolved", "key": "checked_resolved"},
		{"label": "测试提Critical超期未决", "key": "overtime_critical_by_test"},
		{"label": "未决BUG", "key": "not_cbd_count"},
		{"label": "Blocking未决", "key": "blocking_not_cbd"},
		{"label": "测试提Major超期未决", "key": "overtime_major_by_test"},
		{"label": "未决率", "key": "not_cbd_percent"},
		{"label": "Critical未决", "key": "critical_not_cbd"},
		{"label": "测试提Normal超期未决", "key": "overtime_normal_by_test"},
		{"label": "待决策", "key": "request"},
		{"label": "Major未决", "key": "major_not_cbd"},
		{"label": "开发提超期未决", "key": "overtime_by_dev"},
	}
	bugOs := config.ReleaseProject.Name
	// project := config.ReleaseProject
	// recieverIDmap := map[uint]bool{}
	// recieverIDs := []uint{project.CapoID, project.PmID, project.PmoID, project.PtmID, project.PqaID, project.CmaID}
	// if len(config.Workpackages) > 0 {
	// 	for _, workpackage := range config.Workpackages {
	// 		if _, ok := recieverIDmap[workpackage.PstlID]; !ok {
	// 			recieverIDmap[workpackage.PstlID] = true
	// 		}
	// 	}
	// }
	// for key := range recieverIDmap {
	// 	recieverIDs = append(recieverIDs, key)
	// }
	// recievers, err := duser.FindInIds(recieverIDs)
	// if err != nil {
	// 	logging.ErrorLogger.Error("get project config err ", err)
	// 	return err
	// }
	// for _, reciever := range recievers {
	// 	mailTo = append(mailTo, reciever.Username+"@ruijie.com.cn")
	// }
	mailTo = append(mailTo, strings.Split(config.Receivers, ",")...)
	var mailMessage = fmt.Sprintf(`<p >大家好：<br />
	%s项目BUG进展更新。统计基于%s同步的BUG数据<br />
【BUG统计分析】<br /><p>`, bugOs, updatedAt)
	mailMessage = mailMessage + SummaryMailMess
	summary3 := dappmergerequestbugsummary.BugSummary{}
	err := summary3.All(bugOs, 0, "desc", "total")
	if err != nil {
		return err
	}
	data, _ := json.Marshal(&summary3)
	m := make(map[string]interface{})
	json.Unmarshal(data, &m)

	for i := 0; i < len(summaryLabel)/3; i++ {
		mailMessage = mailMessage + fmt.Sprintf(`
			<tr class="el-descriptions-row">
			<th colspan="1" class="el-descriptions-item__cell el-descriptions-item__label is-bordered-label " style="font-weight: bolder; color: black; text-align: center; width: 200px;">%v</th>
			<td colspan="1" class="el-descriptions-item__cell el-descriptions-item__content" style="color: black; text-align: center; width: 100px;"> %v </td>
			<th colspan="1" class="el-descriptions-item__cell el-descriptions-item__label is-bordered-label " style="font-weight: bolder; color: black; text-align: center; width: 200px;">%v</th>
			<td colspan="1" class="el-descriptions-item__cell el-descriptions-item__content" style="color: black; text-align: center; width: 100px;"> %v </td>
			<th colspan="1" class="el-descriptions-item__cell el-descriptions-item__label is-bordered-label " style="font-weight: bolder; color: black; text-align: center; width: 200px;">%v</th>
			<td colspan="1" class="el-descriptions-item__cell el-descriptions-item__content" style="color: black; text-align: center; width: 100px;"> %v </td>
			</tr>
			`,
			summaryLabel[i*3]["label"],
			m[summaryLabel[i*3]["key"]],
			summaryLabel[i*3+1]["label"],
			m[summaryLabel[i*3+1]["key"]],
			summaryLabel[i*3+2]["label"],
			m[summaryLabel[i*3+2]["key"]],
		)
	}
	mailMessage = mailMessage + "</table><br></br>"
	mailMessage = mailMessage + BugWorkGroupMess

	summary1 := dappmergerequestbugworkgroupsummary.BugGroupSummary{}
	items1, err := summary1.All(bugOs, "", 0, "desc", "total")
	if err != nil {
		return err
	}

	for _, item := range items1 {
		mailMessage = mailMessage + fmt.Sprintf(`
				<tr>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				</tr>`,
			item.BugOs,
			item.BugOwnerGroup,
			item.Total,
			item.NotCbdCount,
			fmt.Sprintf("%v%s", item.NotCbdPercent, "%"),
			item.NoWorkpacket,
			item.CheckedResolved,
			item.OvertimeBlockingByTest,
			item.OvertimeCriticalByTest,
			item.OvertimeMajorByTest,
			item.OvertimeNormalByTest,
			item.OvertimeByDev,
			item.BlockingNotCbd,
			item.CriticalNotCbd,
			item.MajorNotCbd,
			item.NormalNotCbd,
			item.TrivialNotCbd,
			item.TotalByTest,
			item.NotCbdCountByTest,
			fmt.Sprintf("%v%s", item.NotCbdPercentByTest, "%"),
			item.TotalByDev,
			item.NotCbdCountByDev,
			fmt.Sprintf("%v%s", item.NotCbdPercentByDev, "%"),
		)
	}
	mailMessage = mailMessage + "</table><br></br>"

	summary2 := dappmergerequestbugownersummary.BugOwnerSummary{}
	items2, err := summary2.All(bugOs, "", 0, "desc", "total")
	if err != nil {
		return err
	}
	mailMessage = mailMessage + BugOwnerMess
	for _, item := range items2 {
		mailMessage = mailMessage + fmt.Sprintf(`
				<tr>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				</tr>`,
			item.BugOs,
			item.BugOwner,
			item.BugOwnerGroup,
			item.Total,
			item.NotCbdCount,
			fmt.Sprintf("%v%s", item.NotCbdPercent, "%"),
			item.NoWorkpacket,
			item.OvertimeBlockingByTest,
			item.OvertimeCriticalByTest,
			item.OvertimeMajorByTest,
			item.OvertimeNormalByTest,
			item.OvertimeByDev,
			item.New,
			item.Assigned,
			item.Request,
			item.Resolved,
			item.Checked,
			item.Reopened,
		)
	}

	mailMessage = mailMessage + "</table><br></br>" + "详情查看：http://10.51.135.15:9090/mergerequest/#/dashboard/bugs" //地址需要调整
	if libs.Config.Debug {
		mailTo = []string{"<EMAIL>"}
		if err = libs.SendMail(mailTo, fmt.Sprintf("%s BUG进展", bugOs), mailMessage, cc); err != nil {
			return err
		}
	}

	rc := cache.GetRedisClusterClient()
	from := "MR表单"
	msg := strings.Join([]string{from, strings.Join(mailTo, ","), fmt.Sprintf("%s BUG进展", bugOs), mailMessage, strings.Join(cc, ",")}, "|")
	_, err = rc.LPush(libs.Config.Mail.Queue, msg)
	if err != nil {
		return err
	}
	return nil
}

const BugOvertimeTopWorkGMess = // 定义邮件表格样式
`<style>
	table {
		border-collapse: collapse;
	}
	th {
		background-color: #007fff;
		color: white;
	}
	table, th, td {
		border: 1px solid black;
		padding: 5px;
		text-align: left;
	}
</style >
<table>
<tr class="">
<th colspan="1" rowspan="1" class="el-table_1_column_1  is-center   is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);width: 200px"><div class="cell">项目</div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_2  is-center   is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);width: 200px"><div class="cell">BUG负责人</div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_3  is-center   is-leaf el-table__cell" style="background-color: rgb(175, 204, 253); color: rgb(0, 0, 0);width: 100px"><div class="cell">专业组</div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_4  is-center   is-leaf el-table__cell" style="background-color: rgb(175, 204, 253); color: rgb(0, 0, 0);width: 100px"><div class="cell">超时BUG数</div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_5  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(175, 204, 253); color: rgb(0, 0, 0);width: 100px"><div class="cell">超时累计天数</div></th>
<th colspan="1" rowspan="1" class="el-table_1_column_5  is-center   is-hidden is-leaf el-table__cell" style="background-color: rgb(175, 204, 253); color: rgb(0, 0, 0);width: 100px"><div class="cell">延期次数</div></th>

</tr>`

const BugOverTimeTopOwnerGroupMess = // 定义邮件表格样式
`<style>
		table {
			border-collapse: collapse;
		}
		th {
			background-color: #007fff;
			color: white;
		}
		table, th, td {
			border: 1px solid black;
			padding: 5px;
			text-align: left;
		}
	</style>
	<table>
<tr class="">
<th colspan="1" rowspan="1" class="el-table_2_column_24  is-center   is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);"><div class="cell">项目</div></th>
<th colspan="1" rowspan="1" class="el-table_2_column_25  is-center   is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);"><div class="cell">专业组</div></th>
<th colspan="1" rowspan="1" class="el-table_2_column_26  is-center   is-leaf el-table__cell" style="background-color: rgb(192, 227, 60); color: rgb(0, 0, 0);"><div class="cell">超时BUG数</div></th>
<th colspan="1" rowspan="1" class="el-table_2_column_27  is-center   is-leaf el-table__cell" style="background-color: rgb(175, 204, 253); color: rgb(0, 0, 0);"><div class="cell">超时累计天数</div></th>
<th colspan="1" rowspan="1" class="el-table_2_column_27  is-center   is-leaf el-table__cell" style="background-color: rgb(175, 204, 253); color: rgb(0, 0, 0);"><div class="cell">延期次数</div></th>

</tr>`

func SendBugOverTimeTopSummary(config *dreleaseprojectconfig.ReleaseProjectConfig, updatedAt string) error {
	mailTo := []string{"<EMAIL>"}
	cc := []string{"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"}
	bugOs := config.ReleaseProject.Name
	// project := config.ReleaseProject
	// recieverIDmap := map[uint]bool{}
	// recieverIDs := []uint{project.CapoID, project.PmID, project.PmoID, project.PtmID, project.PqaID, project.CmaID}
	// if len(config.Workpackages) > 0 {
	// 	for _, workpackage := range config.Workpackages {
	// 		if _, ok := recieverIDmap[workpackage.PstlID]; !ok {
	// 			recieverIDmap[workpackage.PstlID] = true
	// 		}
	// 	}
	// }
	// for key := range recieverIDmap {
	// 	recieverIDs = append(recieverIDs, key)
	// }
	// recievers, err := duser.FindInIds(recieverIDs)
	// if err != nil {
	// 	logging.ErrorLogger.Error("get project config err ", err)
	// 	return err
	// }
	// for _, reciever := range recievers {
	// 	mailTo = append(mailTo, reciever.Username+"@ruijie.com.cn")
	// }
	mailTo = append(mailTo, strings.Split(config.Receivers, ",")...)
	var mailMessage = fmt.Sprintf(`<p >大家好：<br />
	%s项目BUG超时统计。统计基于%s同步的BUG数据<br />
【BUG超时统计】<br /><p>`, bugOs, updatedAt)
	mailMessage = mailMessage + BugOvertimeTopWorkGMess

	items1, err := dappmergerequestbugsummary.GetBugOwnerDelayTop(bugOs, "", "desc", "total", false)
	if err != nil {
		return err
	}

	for _, item := range items1 {
		mailMessage = mailMessage + fmt.Sprintf(`
				<tr>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				</tr>`,
			item.BugOs,
			item.BugOwnerGroup,
			item.BugOwner,
			item.Count,
			item.Total,
			item.Round,
		)
	}
	mailMessage = mailMessage + "</table><br></br>"

	items2, err := dappmergerequestbugsummary.GetBugOwnerGroupDelayTop(bugOs, "", "desc", "total", false)
	if err != nil {
		return err
	}

	mailMessage = mailMessage + BugOverTimeTopOwnerGroupMess
	for _, item := range items2 {
		mailMessage = mailMessage + fmt.Sprintf(`
				<tr>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				<td>%v</td>
				</tr>`,
			item.BugOs,
			item.BugOwnerGroup,
			item.Count,
			item.Total,
			item.Round,
		)
	}
	mailMessage = mailMessage + "</table><br></br>" + "详情查看：http://10.51.135.15:9090/mergerequest/#/dashboard/bugs"
	if libs.Config.Debug {
		mailTo = []string{"<EMAIL>"}
		if err = libs.SendMail(mailTo, fmt.Sprintf("%s BUG超时统计", bugOs), mailMessage, cc); err != nil {
			return err
		}
	}

	rc := cache.GetRedisClusterClient()
	from := "MR表单"
	msg := strings.Join([]string{from, strings.Join(mailTo, ","), fmt.Sprintf("%s BUG超时统计", bugOs), mailMessage, strings.Join(cc, ",")}, "|")
	_, err = rc.LPush(libs.Config.Mail.Queue, msg)
	if err != nil {
		return err
	}
	return nil
}
