package opensource

import (
	"fmt"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/xuri/excelize/v2"

	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/opensource"
	"irisAdminApi/service/dao/opensource/dvulnerability"
	"irisAdminApi/service/dao/opensource/dvulnerabilitypermission"
)

func ListVulnerabilities(ctx iris.Context) {
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	componentName := ctx.FormValue("component_name")
	componentVersion := ctx.FormValue("component_version")
	productName := ctx.FormValue("product_name")
	productVersion := ctx.FormValue("product_version")
	userId, _ := strconv.Atoi(ctx.FormValue("user_id"))
	status, _ := strconv.Atoi(ctx.FormValue("status"))
	sort := ctx.FormValue("sort")
	orderBy := ctx.FormValue("orderBy")
	createdAt := ctx.FormValue("created_at")
	updatedAt := ctx.FormValue("updated_at")
	export := ctx.FormValue("export")

	var isEnable = new(bool)
	if enable, err := ctx.URLParamBool("enable"); err != nil {
		isEnable = nil
	} else {
		*isEnable = enable
	}

	var isExternalServe = new(bool)
	if externalServe, err := ctx.URLParamBool("is_external_serve"); err != nil {
		isExternalServe = nil
	} else {
		*isExternalServe = externalServe
	}

	list, err := dvulnerability.ListVulnerabilities(page, pageSize, componentName,
		componentVersion, productName, productVersion, isEnable, isExternalServe,
		uint(status), sort, orderBy, createdAt, updatedAt, uint(userId))
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if export == "1" {
		ExportVulnerabilities(ctx, list["items"])
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func ExportVulnerabilities(ctx iris.Context, items interface{}) {
	boolMap := map[bool]string{
		true:  "是",
		false: "否",
	}
	statusMap := map[uint]string{
		1: "处理中",
		2: "确认",
		3: "忽略",
		4: "转派",
	}
	fileName := fmt.Sprintf("漏洞清单_%s.xlsx", time.Now().Format("20060102150405"))
	file := excelize.NewFile()
	streamWriter, err := file.NewStreamWriter("Sheet1")

	// styleID, err := file.NewStyle(&excelize.Style{Font: &excelize.Font{Color: "#777777"}})
	if err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	header := []interface{}{"序号", "CVE编号", "组件名称", "组件版本", "关联产品", "关联项目", "漏洞描述", "漏洞等级", "漏洞状态", "启用状态", "对外提供服务", "通知时间", "负责人", "专业组长"}
	cell, _ := excelize.CoordinatesToCellName(1, 1)
	if err := streamWriter.SetRow(cell, header); err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	rowNum := 1
	for _, item := range items.([]*dvulnerability.ListResponse) {
		rowNum++
		cell, _ := excelize.CoordinatesToCellName(1, rowNum)
		leaders := []string{}
		owners := []string{}
		for _, leader := range item.ResponsibleLeaders {
			leaders = append(leaders, leader.Name)
		}
		for _, owner := range item.ResponsibleUsers {
			owners = append(owners, owner.Name)
		}
		row := []interface{}{rowNum - 1, item.CveID, item.Component.Name, item.Component.Version, item.Component.Product, item.Component.ProductVersion, item.CveDescription, item.CveSeverity, statusMap[item.Status], boolMap[item.Component.Enable], boolMap[item.Component.IsExternalServe], item.EmailTime.Format("2006-01-02 15:04:05"), strings.Join(owners, ","), strings.Join(leaders, ",")}
		if err := streamWriter.SetRow(cell, row); err != nil {
			logging.ErrorLogger.Error(err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
	}
	if err := streamWriter.Flush(); err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if err := file.SaveAs(filepath.Join("/tmp", fileName)); err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	defer os.Remove(filepath.Join("/tmp", fileName))
	ctx.SendFile(filepath.Join("/tmp", fileName), url.QueryEscape(fileName))
	return
}

func isAllowedProcessVulnerability(ctx iris.Context, vulnerabilityId, authUserId uint, isAdmin bool) bool {
	if !isAdmin {
		// 对于普通用户, 只有漏洞的负责人/负责人组长/委派人才允许进行处理
		existPermissions, err := dvulnerabilitypermission.ListPermissionsByVulnerabilityIdAndUserId(
			vulnerabilityId, authUserId)
		if err != nil {
			logging.ErrorLogger.Errorf("list vulnerability permissions by vulnerability-id and user-id get err %s", err.Error())
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return false
		}
		for _, permissionRes := range existPermissions {
			if permissionRes.Type == opensource.ResponsibleLeaderPermissionType ||
				permissionRes.Type == opensource.ResponsibleUserPermissionType ||
				permissionRes.Type == opensource.DelegatedUserPermissionType {
				return true
			}
		}
		ctx.JSON(response.NewResponse(response.AuthActionErr.Code, nil, "你未拥有处理该CVE漏洞的权限，请联系管理员"))
		return false
	}
	return true
}

func GetVulnerability(ctx iris.Context) {
	vulnerabilityId, _ := ctx.Params().GetUint("id")
	vul, err := dvulnerability.FindById(vulnerabilityId)
	if err != nil {
		logging.ErrorLogger.Errorf("get opensource vulnerability get err %s", err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	} else if vul == nil {
		ctx.JSON(response.NewResponse(response.DataEmptyErr.Code, nil, fmt.Sprintf("漏洞-<%d>不存在", vulnerabilityId)))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, vul, response.NoErr.Msg))
}

func GetSummaryByProduct(ctx iris.Context) {
	res, err := dvulnerability.GetSummaryByProduct()
	if err != nil {
		logging.ErrorLogger.Errorf("get opensource vulnerability summary get err %s", err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, res, response.NoErr.Msg))
}

func GetSummaryByComponent(ctx iris.Context) {
	res, err := dvulnerability.GetSummaryByResponsibleUserAndProduct()
	if err != nil {
		logging.ErrorLogger.Errorf("get opensource vulnerability summary get err %s", err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, res, response.NoErr.Msg))
}
