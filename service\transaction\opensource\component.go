package opensource

import (
	"gorm.io/gorm"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/opensource"
	"irisAdminApi/service/dao/opensource/dcomponent"
	"irisAdminApi/service/dao/opensource/dcomponentpermission"
	"irisAdminApi/service/dao/opensource/dvulnerability"
	"irisAdminApi/service/dao/opensource/dvulnerabilitypermission"
)

const batchCreateCount = 20

func CreateComponent(componentRes *dcomponent.OpenSourceComponent, responsibleUserIDs, responsibleLeaderIDs []uint) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(dcomponent.Model()).Create(&componentRes.OpenSourceComponent).Error; err != nil {
			logging.ErrorLogger.Errorf("transaction create component get err %s", err.Error())
			return err
		}

		responsibleUserPermissions := make([]*opensource.OpenSourceComponentPermission, len(responsibleUserIDs))
		for i, responsibleUserID := range responsibleUserIDs {
			permissionRes := &opensource.OpenSourceComponentPermission{}
			permissionRes.UserID = responsibleUserID
			permissionRes.ComponentID = componentRes.ID
			permissionRes.Type = opensource.ResponsibleUserPermissionType
			responsibleUserPermissions[i] = permissionRes
		}
		responsibleLeaderPermissions := make([]*opensource.OpenSourceComponentPermission, len(responsibleLeaderIDs))
		for i, responsibleLeaderID := range responsibleLeaderIDs {
			permissionRes := &opensource.OpenSourceComponentPermission{}
			permissionRes.UserID = responsibleLeaderID
			permissionRes.ComponentID = componentRes.ID
			permissionRes.Type = opensource.ResponsibleLeaderPermissionType
			responsibleLeaderPermissions[i] = permissionRes
		}

		componentPermissions := append(responsibleUserPermissions, responsibleLeaderPermissions...)
		if err := tx.Model(dcomponentpermission.Model()).Create(&componentPermissions).Error; err != nil {
			logging.ErrorLogger.Errorf("transaction create component permission get err %s", err.Error())
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}

	return nil
}

func DeleteComponent(id uint) error {
	db := easygorm.GetEasyGormDb()
	return db.Transaction(func(tx *gorm.DB) error {
		// 删除组件
		err := tx.Delete(dcomponent.Model(), id).Error
		if err != nil {
			logging.ErrorLogger.Errorf("delete opensource component err %s", err)
			return err
		}
		// 删除组件关联的用户
		err = tx.Delete(dcomponentpermission.Model(), "component_id=?", id).Error
		if err != nil {
			logging.ErrorLogger.Errorf("delete opensource component permission err %s", err)
			return err
		}
		// 删除组件相关漏洞
		err = tx.Delete(dvulnerability.Model(), "component_id=?", id).Error
		if err != nil {
			logging.ErrorLogger.Errorf("delete opensource vulnerability err %s", err)
			return err
		}
		// 删除组件漏洞关联的用户
		err = tx.Delete(dvulnerabilitypermission.Model(), "component_id=?", id).Error
		if err != nil {
			logging.ErrorLogger.Errorf("delete opensource vulnerability permissions err %s", err)
			return err
		}
		// 删除组件相关的漏洞处理记录
		// err = tx.Delete(dvulnerabilityhistory.Model(), "component_id=?", id).Error
		// if err != nil {
		// 	logging.ErrorLogger.Errorf("delete opensource vulnerability permissions err %s", err)
		// 	return err
		// }
		return nil
	})
}

func DeleteComponentPermission(id uint, componentId, userId, permissionType uint) error {
	db := easygorm.GetEasyGormDb()
	return db.Transaction(func(tx *gorm.DB) error {
		// 删除组件的permission
		var res = &dcomponentpermission.Response{}
		res.ID = id
		res.ComponentID = componentId
		if err := easygorm.GetEasyGormDb().Model(dcomponentpermission.Model()).
			Delete(&res.OpenSourceComponentPermission).Error; err != nil {
			return err
		}

		// 删除漏洞的permission
		if err := easygorm.GetEasyGormDb().Where("component_id=?", componentId).
			Where("user_id=?", userId).
			Where("type=?", permissionType).
			Delete(dvulnerabilitypermission.Model()).Error; err != nil {
			return err
		}
		return nil
	})
}

func CreateComponents(componentResList []*dcomponent.OpenSourceComponent, responsibleUserIDsList, responsibleLeaderIDsList [][]uint) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		components := make([]*opensource.OpenSourceComponent, len(componentResList))
		for i, componentRes := range componentResList {
			components[i] = &componentRes.OpenSourceComponent
		}
		if err := tx.Model(dcomponent.Model()).Create(&components).Error; err != nil {
			logging.ErrorLogger.Errorf("transaction create components, create components get err %s", err.Error())
			return err
		}

		for i, component := range components {
			responsibleUserIDs := responsibleUserIDsList[i]
			responsibleLeaderIDs := responsibleLeaderIDsList[i]
			responsibleUserPermissions := make([]*opensource.OpenSourceComponentPermission, len(responsibleUserIDs))
			for i, responsibleUserID := range responsibleUserIDs {
				permissionRes := &opensource.OpenSourceComponentPermission{}
				permissionRes.UserID = responsibleUserID
				permissionRes.ComponentID = component.ID
				permissionRes.Type = opensource.ResponsibleUserPermissionType
				responsibleUserPermissions[i] = permissionRes
			}
			responsibleLeaderPermissions := make([]*opensource.OpenSourceComponentPermission, len(responsibleLeaderIDs))
			for i, responsibleLeaderID := range responsibleLeaderIDs {
				permissionRes := &opensource.OpenSourceComponentPermission{}
				permissionRes.UserID = responsibleLeaderID
				permissionRes.ComponentID = component.ID
				permissionRes.Type = opensource.ResponsibleLeaderPermissionType
				responsibleLeaderPermissions[i] = permissionRes
			}

			componentPermissions := append(responsibleUserPermissions, responsibleLeaderPermissions...)
			if err := tx.Model(dcomponentpermission.Model()).Create(&componentPermissions).Error; err != nil {
				logging.ErrorLogger.Errorf("transaction create components, create component permissions get err %s", err.Error())
				return err
			}
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func AddComponentPermission(componentPermissionRes *dcomponentpermission.Response) error {
	db := easygorm.GetEasyGormDb()
	return db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(dcomponentpermission.Model()).Create(&componentPermissionRes.OpenSourceComponentPermission).Error; err != nil {
			logging.ErrorLogger.Errorf("transaction add component permission, create component permission get err %s", err.Error())
			return err
		}

		var vulnerabilities []*opensource.OpenSourceVulnerability
		if err := tx.Model(dvulnerability.Model()).
			Where("component_id=?", componentPermissionRes.ComponentID).Find(&vulnerabilities).Error; err != nil {
			logging.ErrorLogger.Errorf("transaction add component permission, list vulnerabilities get err %s", err.Error())
			return err
		}
		var vulnerabilityPermissions = make([]*opensource.OpenSourceVulnerabilityPermission, len(vulnerabilities))
		for i, vulnerability := range vulnerabilities {
			vulnerabilityPermissions[i] = &opensource.OpenSourceVulnerabilityPermission{
				ComponentID:     componentPermissionRes.ComponentID,
				VulnerabilityID: vulnerability.ID,
				UserID:          componentPermissionRes.UserID,
				Type:            componentPermissionRes.Type,
			}
		}
		var total = len(vulnerabilityPermissions)
		var firstIndex = 0
		for firstIndex < total {
			lastIndex := firstIndex + batchCreateCount
			if lastIndex > total {
				lastIndex = total
			}
			newVulnerabilityPermissions := vulnerabilityPermissions[firstIndex:lastIndex]
			if err := tx.Model(dvulnerabilitypermission.Model()).Create(&newVulnerabilityPermissions).Error; err != nil {
				logging.ErrorLogger.Errorf("transaction add component permission, create vulnerability permissions get err %s", err.Error())
				return err
			}
			firstIndex = lastIndex
		}
		return nil
	})
}
