package schedule

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/featurerelease/dfeature"
	"irisAdminApi/service/dao/featurerelease/dfeatureprocinst"
	"irisAdminApi/service/dao/featurerelease/dfeatureproctask"
	"irisAdminApi/service/dao/user/duser"
	"time"
)

var TaskLimit = 60 * 60 * 24
var FeatureLimit = 3 * 60 * 60 * 24

var UrgencyTaskLimit = 60 * 60 * 3
var UrgencyFeatureLimit = 60 * 60 * 24

func ChecTaskskOverTime() {
	t := time.NewTicker(60 * time.Minute)
	for {
		<-t.C
		dfeatureproctask.UpdateFeatureTasksProccessingCache()
		urgencyProcInstIDs := []uint{}
		for _, item := range dfeature.FeatureProccessingCache {
			if item.Urgency {
				if id, ok := dfeatureprocinst.FeatureIDProcInstIDMap[item.ID]; ok {
					urgencyProcInstIDs = append(urgencyProcInstIDs, id)
				}
			}
		}
		now := time.Now().Unix()
		limit := int64(TaskLimit)
		for _, item := range dfeatureproctask.FeatureTasksProccessingCache {
			if !item.OverTimeNotice {
				createdAt, err := time.ParseInLocation("2006-01-02T15:04:05.999+08:00", item.CreatedAt, time.Local)
				if err != nil {
					logging.ErrorLogger.Errorf("parser time error", err)
				}
				urgency := libs.InArrayUint(urgencyProcInstIDs, item.ProcInstID)
				if urgency {
					limit = int64(UrgencyTaskLimit)
				} else {
					limit = int64(TaskLimit)
				}
				if now-createdAt.Unix() > int64(limit) {
					if err := SendTaskOverTimeMail(item, urgency); err != nil {
						continue
					}
					err := item.FeatureProcTask.Update(item.ID, map[string]interface{}{"OverTimeNotice": 1})
					if err != nil {
						logging.ErrorLogger.Errorf("parser time error", err)
					}
				}
			}
		}
	}
}

func ChecFeaturekOverTime() {
	t := time.NewTicker(60 * time.Minute)
	for {
		<-t.C
		dfeature.UpdateFeatureProccessingCache()
		featureIds := []uint{}
		urgencyFeatureIDs := []uint{}
		for _, item := range dfeature.FeatureProccessingCache {
			featureIds = append(featureIds, item.ID)
			if item.Urgency {
				urgencyFeatureIDs = append(urgencyFeatureIDs, item.ID)
			}
		}
		dfeatureprocinst.UpdateFeatureProccessingCache(featureIds)
		now := time.Now().Unix()
		limit := int64(FeatureLimit)
		for _, item := range dfeature.FeatureProccessingCache {
			if !item.OverTimeNotice {
				createdAt := item.CreatedAt
				urgency := libs.InArrayUint(urgencyFeatureIDs, item.ID)
				if urgency {
					limit = int64(UrgencyFeatureLimit)
				} else {
					limit = int64(FeatureLimit)
				}
				if now-createdAt.Unix() > limit {
					if err := SendFeatureOverTimeMail(item, urgency); err != nil {
						continue
					}
					err := item.Response.Update(item.ID, map[string]interface{}{"OverTimeNotice": 1})
					if err != nil {
						logging.ErrorLogger.Errorf("parser time error", err)
					}
				}
			}
		}
	}
}

// todo: 增加紧急状态通知提示
func SendTaskOverTimeMail(task *dfeatureproctask.ListResponse, urgency bool) error {
	var subject string
	mailTo := []string{fmt.Sprintf("%<EMAIL>", duser.UserMap[task.Assignee].Username), "<EMAIL>"}
	cc := []string{}
	if urgency {
		subject = fmt.Sprintf("[紧急][规则库管理系统][规则库ID:%d][%s][已超时]", task.ProcInst.FeatureID, task.NodeName)
	} else {
		subject = fmt.Sprintf("[规则库管理系统][规则库ID:%d][%s][已超时]", task.ProcInst.FeatureID, task.NodeName)
	}
	// body := fmt.Sprintf(`%s<br><p>规则库链接: <a href="http://feature.aqyfzx.ruijie.net/feature/#/ntos/todo">http://feature.aqyfzx.ruijie.net/feature/#/ntos/todo</a><p>`, subject)
	body := fmt.Sprintf(
		`规则库ID: %d<br>
		规则库文件名: %s<br>
		规则库类型: %s<br>
		版本描述: %s<br>
		适配产品型号: %s<br>
		适配软件版本: %s<br>
		发布时间: %s<br>
		发布安全云: %s<br>
		<p>规则库链接: <a href="http://feature.aqyfzx.ruijie.net/feature/#/ntos/list">http://feature.aqyfzx.ruijie.net/feature/#/ntos/list</a><p><p>详细状态请前往安全云查看</p>
		`,
		task.ProcInst.FeatureID,
		task.ProcInst.Feature.FileName,
		task.ProcInst.Feature.FeatureType,
		task.ProcInst.Feature.VersionDesc,
		task.ProcInst.Feature.ProductModels,
		task.ProcInst.Feature.SoftVersions,
		task.ProcInst.Feature.ReleaseDate,
		task.ProcInst.Feature.SecCloud,
	)
	err := libs.SendMail(mailTo, subject, body, cc)
	// err := libs.SendMail([]string{fmt.Sprintf("%<EMAIL>", "linjiakai")}, subject, body)
	if err != nil {
		logging.ErrorLogger.Error(err)
		return err
	}

	logging.DebugLogger.Debugf("send mail", mailTo, subject, body)
	return nil
}

// todo: 增加紧急状态通知提示
func SendFeatureOverTimeMail(feature *dfeature.ListResponse, urgency bool) error {
	var subject string
	mailTo := []string{"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"}
	mailTo = append(mailTo, fmt.Sprintf("%<EMAIL>", duser.UserMap[dfeatureprocinst.FeatureProcInstUserMap[feature.ID]].Username))
	cc := []string{"<EMAIL>"}
	// mailTo := []string{"<EMAIL>"}
	if urgency {
		subject = fmt.Sprintf("[紧急][规则库管理系统][规则库ID:%d][发布流程已超时]", feature.ID)
	} else {
		subject = fmt.Sprintf("[规则库管理系统][规则库ID:%d][发布流程已超时]", feature.ID)
	}
	body := fmt.Sprintf(
		`规则库ID: %d<br>
		规则库文件名: %s<br>
		规则库类型: %s<br>
		版本描述: %s<br>
		适配产品型号: %s<br>
		适配软件版本: %s<br>
		发布时间: %s<br>
		发布安全云: %s<br>
		<p>规则库链接: <a href="http://feature.aqyfzx.ruijie.net/feature/#/ntos/list">http://feature.aqyfzx.ruijie.net/feature/#/ntos/list</a><p><p>详细状态请前往安全云查看</p>
		`,
		feature.ID,
		feature.FileName,
		feature.FeatureType,
		feature.VersionDesc,
		feature.ProductModels,
		feature.SoftVersions,
		feature.ReleaseDate,
		feature.SecCloud,
	)

	err := libs.SendMail(mailTo, subject, body, cc)
	// err := libs.SendMail([]string{fmt.Sprintf("%<EMAIL>", "linjiakai")}, subject, body)
	if err != nil {
		logging.ErrorLogger.Error(err)
		return err
	}

	logging.DebugLogger.Debugf("send mail", mailTo, subject, body)
	return nil
}
