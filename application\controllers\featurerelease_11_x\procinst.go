package featurerelease_11_x

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	dfeature "irisAdminApi/service/dao/featurerelease_11_x/dfeature_11_x"
	dfeatureproc "irisAdminApi/service/dao/featurerelease_11_x/dfeatureproc_11_x"
	dfeatureprocdef "irisAdminApi/service/dao/featurerelease_11_x/dfeatureprocdef_11_x"
	dfeatureprocinst "irisAdminApi/service/dao/featurerelease_11_x/dfeatureprocinst_11_x"
	dfeatureproctask "irisAdminApi/service/dao/featurerelease_11_x/dfeatureproctask_11_x"
	transfeaturerelease "irisAdminApi/service/transaction/featurerelease_11_x/transfeaturerelease_11_x"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

func CreateProcInst(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	uuid := libs.GetUUID()
	request := &dfeature.Request{}
	if err := ctx.ReadForm(request); err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	check := dfeature.Response{}
	err = check.FindEx("file_md5", request.FileMd5)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if check.ID > 0 {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "已经存在相同文件发布记录"))
		return
	}

	f, fh, err := ctx.FormFile("file")
	defer f.Close()

	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	//构造文件名称
	fileName := fh.Filename

	// 创建最终存放路径以及保存文件
	var upload = filepath.Join(libs.Config.FeatureRelease.Upload, time.Now().Format("20060102"), request.FileMd5)
	err = os.MkdirAll(upload, 0750)
	os.Chmod(upload, 0750)

	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	//创建申请单
	tempFn := filepath.Join(upload, fileName)
	_, err = ctx.SaveFormFile(fh, tempFn)
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	err = transfeaturerelease.CreateFeatureTransaction(id, map[string]interface{}{
		"FileName":           request.FileName,
		"FileSize":           request.FileSize,
		"FileMd5":            request.FileMd5,
		"Version":            request.Version,
		"FeatureType":        request.FeatureType,
		"FeatureVersions":    request.FeatureVersions,
		"FeatureBaseVersion": request.FeatureBaseVersion,
		"ProductModels":      request.ProductModels,
		"SoftVersions":       request.SoftVersions,
		"FileType":           request.FileType,
		"ReleaseDate":        request.ReleaseDate,
		"VersionDesc":        request.VersionDesc,
		"CreatedAt":          time.Now(),
		"UpdatedAt":          time.Now(),
		"Uuid":               uuid,
		"Resource":           request.Resource,
		"UpdateToMain":       request.UpdateToMain,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetProcDef(ctx iris.Context) {
	name := ctx.FormValue("name")
	resource, err := dfeatureprocdef.GetResource(name)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	nodes, err := dfeatureprocdef.GetNodes(resource)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nodes, response.NoErr.Msg))
	return
}

func GetFeatureProcInsts(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dfeatureprocinst.ListResponse{}, ctx, name, sort, orderBy, page, pageSize)

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetFeatureProcTasks(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")

	list, err := dfeatureproc.AllTasksByAssignee(uId, name, sort, orderBy, page, pageSize, status, start, end)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetFeatureProcInstTasks(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")

	list, err := dfeatureproc.AllTasksByProcInst(id, name, sort, orderBy, page, pageSize, status, start, end)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetProcInst(ctx iris.Context) {
	info := dfeatureprocinst.Response{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

type TaskRequest struct {
	Comment       string `form:"comment"`
	Status        uint   `form:"status"`
	NodeID        string `form:"nodeId"`
	NextNodeID    string `form:"nextNodeID"`
	TaskID        uint   `form:"taskId"`
	UserID        uint   `form:"userId"`
	ProductModels string `json:"product_models" form:"product_models"`
	SoftVersions  string `json:"soft_versions" form:"soft_versions"`
}

func UpdateProcInst(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	procInst := dfeatureprocinst.Response{}

	err = procInst.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	// uuid := libs.GetUUID()
	request := TaskRequest{}
	if err := ctx.ReadForm(&request); err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	featureObject := map[string]interface{}{
		"UpdatedAt": time.Now(),
	}

	fileName := ""
	logging.DebugLogger.Debugf("update", request.NodeID, request.Status)
	if request.NodeID == "test_check" && request.Status == 1 {
		if libs.Config.FeatureRelease.Enable {
			md5 := procInst.Feature.FileMd5
			sign, err := getSign(md5)
			if err != nil {
				logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
				return
			}
			featureObject["Sign"] = sign
		}

		f, fh, err := ctx.FormFile("file")
		defer f.Close()

		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}

		//构造文件名称
		fileName = fh.Filename

		// 创建最终存放路径以及保存文件
		var upload = filepath.Join(libs.Config.FeatureRelease.Upload, strings.Replace(strings.Split(procInst.Feature.CreatedAt, "T")[0], "-", "", -1), procInst.Feature.FileMd5)
		err = os.MkdirAll(upload, 0750)
		os.Chmod(upload, 0750)

		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		//创建申请单
		tempFn := filepath.Join(upload, fileName)
		_, err = ctx.SaveFormFile(fh, tempFn)
		if err != nil {
			logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	}

	if request.NodeID == "experiment" && request.Status == 1 {
		f, fh, err := ctx.FormFile("file")
		defer f.Close()

		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}

		//构造文件名称
		fileName = fh.Filename

		// 创建最终存放路径以及保存文件
		var upload = filepath.Join(libs.Config.FeatureRelease.Upload, strings.Replace(strings.Split(procInst.Feature.CreatedAt, "T")[0], "-", "", -1), procInst.Feature.FileMd5)
		err = os.MkdirAll(upload, 0750)
		os.Chmod(upload, 0750)

		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		//创建申请单
		tempFn := filepath.Join(upload, fileName)
		_, err = ctx.SaveFormFile(fh, tempFn)
		if err != nil {
			logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	}

	if request.NodeID == "cmd_release" && request.Status == 1 {
		featureObject["Status"] = 1

		// if libs.Config.FeatureRelease.Enable {
		// 	desc := DescFile{
		// 		Version:            procInst.Feature.Version,
		// 		FileName:           procInst.Feature.FileName,
		// 		FileSize:           procInst.Feature.FileSize,
		// 		FileMd5:            procInst.Feature.FileMd5,
		// 		ProductModels:      strings.Split(procInst.Feature.ProductModels, "|"),
		// 		SoftVersions:       strings.Split(procInst.Feature.SoftVersions, "|"),
		// 		FeatureType:        procInst.Feature.FeatureType,
		// 		FeatureVersions:    strings.Split(procInst.Feature.FeatureVersions, "|"),
		// 		FeatureBaseVersion: procInst.Feature.FeatureBaseVersion,
		// 		FileType:           procInst.Feature.FileType,
		// 		ReleaseDate:        procInst.Feature.ReleaseDate,
		// 		VersionDesc:        procInst.Feature.VersionDesc,
		// 		Sign:               procInst.Feature.Sign,
		// 	}

		// 	var upload = filepath.Join(libs.Config.FeatureRelease.Upload, strings.Replace(strings.Split(procInst.Feature.CreatedAt, "T")[0], "-", "", -1), procInst.Feature.FileMd5)
		// 	err = os.MkdirAll(upload, 0750)
		// 	if err != nil {
		// 		logging.ErrorLogger.Errorf("create approval get err ", err)
		// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		// 		return
		// 	}
		// 	os.Chmod(upload, 0750)
		// 	os.RemoveAll(filepath.Join(upload, "featureDesc.txt"))
		// 	desc_file, err := os.OpenFile(filepath.Join(upload, "featureDesc.txt"), os.O_RDWR|os.O_CREATE, 0666)
		// 	if err != nil {
		// 		logging.ErrorLogger.Errorf("create approval get err ", err)
		// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		// 		return
		// 	}
		// 	jsonBytes, err := json.Marshal(desc)
		// 	if err != nil {
		// 		logging.ErrorLogger.Errorf("create approval get err ", err)
		// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		// 		return
		// 	}
		// 	_, err = desc_file.WriteString(string(jsonBytes))
		// 	defer desc_file.Close()
		// 	logging.DebugLogger.Debugf("start upload", request.NodeID, request.Status)
		// 	err = UploadV2(filepath.Join(upload, procInst.Feature.FileName), filepath.Join(upload, "featureDesc.txt"), procInst.Feature.FeatureType)
		// 	if err != nil {
		// 		logging.ErrorLogger.Errorf("create approval get err ", err)
		// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		// 		return
		// 	}
		// 	featureObject["Status"] = 1
		// }
	}

	if request.NodeID == "feature_dev" && request.Status == 1 {
		featureObject["ProductModels"] = request.ProductModels
		featureObject["SoftVersions"] = request.SoftVersions
	}

	taskObject := map[string]interface{}{
		"NodeID":     request.NodeID,
		"UpdatedAt":  time.Now(),
		"Status":     request.Status,
		"NextNodeID": request.NextNodeID,
		"Comment":    request.Comment,
		"Attachment": fileName,
		"UserID":     request.UserID,
	}

	err = transfeaturerelease.UpdateFeatureTransaction(uId, id, request.TaskID, featureObject, taskObject)
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetProcPrevNodes(ctx iris.Context) {
	name := ctx.FormValue("name")
	nodeID := ctx.FormValue("nodeId")
	resource, err := dfeatureprocdef.GetResource(name)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	nodes, err := dfeatureprocdef.GetNodes(resource)
	result, _ := dfeatureprocdef.GetBeforeNodes(nodes, nodeID)
	// nodes, err := dfeatureprocdef.GetPrevNodes(resource, nodeID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func DownloadDescFile(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	procInst := dfeatureprocinst.Response{}

	err := procInst.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	desc := DescFile{
		Version:            procInst.Feature.Version,
		FileName:           procInst.Feature.FileName,
		FileSize:           procInst.Feature.FileSize,
		FileMd5:            procInst.Feature.FileMd5,
		ProductModels:      strings.Split(procInst.Feature.ProductModels, "|"),
		SoftVersions:       strings.Split(procInst.Feature.SoftVersions, "|"),
		FeatureType:        procInst.Feature.FeatureType,
		FeatureVersions:    strings.Split(procInst.Feature.FeatureVersions, "|"),
		FeatureBaseVersion: procInst.Feature.FeatureBaseVersion,
		FileType:           procInst.Feature.FileType,
		ReleaseDate:        procInst.Feature.ReleaseDate,
		VersionDesc:        procInst.Feature.VersionDesc,
		Sign:               procInst.Feature.Sign,
	}

	var upload = filepath.Join(libs.Config.FeatureRelease.Upload, strings.Replace(strings.Split(procInst.Feature.CreatedAt, "T")[0], "-", "", -1), procInst.Feature.FileMd5)
	err = os.MkdirAll(upload, 0750)
	os.Chmod(upload, 0750)
	os.RemoveAll(filepath.Join(upload, "featureDesc.txt"))
	desc_file, err := os.OpenFile(filepath.Join(upload, "featureDesc.txt"), os.O_RDWR|os.O_CREATE, 0666)

	jsonBytes, err := json.Marshal(desc)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	_, err = desc_file.WriteString(string(jsonBytes))
	defer desc_file.Close()
	ctx.SendFile(filepath.Join(upload, "featureDesc.txt"), procInst.Feature.FileMd5+"_featureDesc.txt")
	return
}

func DownloadFeatureFile(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	procInst := dfeatureprocinst.Response{}

	err := procInst.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	var upload = filepath.Join(libs.Config.FeatureRelease.Upload, strings.Replace(strings.Split(procInst.Feature.CreatedAt, "T")[0], "-", "", -1), procInst.Feature.FileMd5)

	ctx.SendFile(filepath.Join(upload, procInst.Feature.FileName), url.QueryEscape(procInst.Feature.FileName))
	return
}

func DownloadTestReport(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	procInst := dfeatureprocinst.Response{}

	err := procInst.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	attachment, err := dfeatureproctask.FindAttachmentByProc(id, "test_check")
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	var upload = filepath.Join(libs.Config.FeatureRelease.Upload, strings.Replace(strings.Split(procInst.Feature.CreatedAt, "T")[0], "-", "", -1), procInst.Feature.FileMd5)

	ctx.SendFile(filepath.Join(upload, attachment), url.QueryEscape(attachment))
	return
}

func DownloadExpReport(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	procInst := dfeatureprocinst.Response{}

	err := procInst.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	attachment, err := dfeatureproctask.FindAttachmentByProc(id, "experiment")
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if attachment == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "没有找到文件"))
		return
	}
	var upload = filepath.Join(libs.Config.FeatureRelease.Upload, strings.Replace(strings.Split(procInst.Feature.CreatedAt, "T")[0], "-", "", -1), procInst.Feature.FileMd5)

	ctx.SendFile(filepath.Join(upload, attachment), url.QueryEscape(attachment))
	return
}

func getSign(md5 string) (string, error) {
	// url := "https://47.99.121.215/api/updateCommon/encrypt/md5"
	url := fmt.Sprintf("%s/api/updateCommon/encrypt/md5", libs.Config.FeatureRelease.Url)
	jsonStr, _ := json.Marshal(map[string]interface{}{
		"md5": md5,
	})
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	if err != nil {
		logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
		return "", err
	}
	req.Header.Add("iamfeign", "1")
	req.Header.Add("Content-Type", "application/json")
	if err != nil {
		logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
		return "", err
	}
	result, err := libs.HandlerRequest(req)
	if err != nil {
		logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
		return "", err
	}

	if result["success"].(bool) {
		sign := result["result"].(string)
		return sign, nil
	}
	return "", errors.New("获取签名失败")
}

func UploadV2(featureFile, descFile, featureType string) error {
	bodyBuffer := &bytes.Buffer{}
	bodyWriter := multipart.NewWriter(bodyBuffer)

	err := bodyWriter.WriteField("featureType", featureType)
	if err != nil {
		return errors.New("上传规则库文件失败")
	}
	// file1
	fileWriter1, _ := bodyWriter.CreateFormFile("featureFile", featureFile)
	file1, err := os.Open(featureFile)
	if err != nil {
		return errors.New("找不到规则库文件")
	}
	defer file1.Close()
	io.Copy(fileWriter1, file1)

	// file2
	fileWriter2, _ := bodyWriter.CreateFormFile("descFile", descFile)
	file2, err := os.Open(descFile)
	if err != nil {
		return errors.New("找不到规则库描述文件")
	}
	defer file2.Close()
	io.Copy(fileWriter2, file2)

	contentType := bodyWriter.FormDataContentType()
	bodyWriter.Close()
	if libs.Config.FeatureRelease.Url == "" {
		return errors.New("未配置云平台地址，请联系管理员")
	}

	url := fmt.Sprintf("%s/api/featureManager/addVersion", libs.Config.FeatureRelease.Url)
	req, err := http.NewRequest("POST", url, bodyBuffer)
	req.Header.Add("Content-Type", contentType)
	req.Header.Add("iamfeign", "1")

	if err != nil {
		logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
		return err
	}
	result, err := libs.HandlerRequest(req)
	if err != nil {
		logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
		return err
	}
	logging.DebugLogger.Debugf("upload", result)
	if result["success"].(bool) {
		if strings.Contains(result["result"].(string), "文件转发成功") {
			return nil
		}
	}

	return errors.New("发布失败，云平台返回失败！")
}
