package resourcepool

import (
	"strconv"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/resourcepool/dresourcepoolmonitor"
	"irisAdminApi/service/dao/user/duser"

	"github.com/kataras/iris/v12"
)

func GetMonitors(ctx iris.Context) {
	uId, _ := dao.GetAuthId(ctx)
	name := ctx.FormValue("search")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dresourcepoolmonitor.All(uId, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetMonitor(ctx iris.Context) {
	info := dresourcepoolmonitor.ResourcePoolMonitor{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

func CreateMonitor(ctx iris.Context) {
	userId, _ := dao.GetAuthId(ctx)
	request := dresourcepoolmonitor.Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	validErr := libs.Validate.Struct(&request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	// delta, _ := time.ParseDuration("24*60h")
	/*
		MgtIp      string `gorm:"not null; type:varchar(30)" json:"mgt_ip"`
		MgtPort    uint   `gorm:"not null;default:22" json:"mgt_port"`
		ConIp      string `gorm:"not null; type:varchar(30)" json:"con_ip"`
		ConPort    uint   `gorm:"not null;" json:"con_port"`
		Reserved   string `gorm:"not null; type:varchar(30)" json:"reserved"`
		Status     string `gorm:"not null; type:varchar(30); default:'active'" json:"status"`
		Location   string `gorm:"not null; type:varchar(60)" json:"location"`
		Comments   string `gorm:"not null; type:varchar(200)" json:"comments"`
		RootPasswd string `gorm:"not null; type:varchar(60)" json:"root_passwd"`
		Cycle      uint   `gorm:"not null;" json:"cycle"`
		Key        string `gorm:"not null; type:varchar(60)" json:"key"`
		Flag       bool   `gorm:"not null;default:'true'" json:"flag"`
	*/
	user := duser.User{}
	err := user.Find(userId)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if request.Status == "" {
		request.Status = "active"
	}
	err = dao.Create(&dresourcepoolmonitor.ResourcePoolMonitor{}, ctx, map[string]interface{}{
		"CreatedAt":  time.Now(),
		"UpdatedAt":  time.Now(),
		"MgtIp":      request.MgtIp,
		"MgtPort":    request.MgtPort,
		"ConIp":      request.ConIp,
		"ConPort":    request.ConPort,
		"Reserved":   user.Name,
		"Status":     request.Status,
		"Location":   request.Location,
		"Comments":   request.Comments,
		"RootPasswd": request.RootPasswd,
		"Cycle":      request.Cycle,
		"FsKey":      request.FsKey,
		"Flag":       request.Flag,
		"UserID":     userId,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, request, response.NoErr.Msg))
	return
}

func UpdateMonitor(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}

	request := dresourcepoolmonitor.Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("create approval read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(&request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	monitor := dresourcepoolmonitor.ResourcePoolMonitor{}
	err = monitor.Find(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if uId != 54 && uId != 1 && monitor.UserID != uId {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "禁止更新他人创建的记录"))
		return
	}

	err = dao.Update(&dresourcepoolmonitor.ResourcePoolMonitor{}, ctx, map[string]interface{}{
		"UpdatedAt":  time.Now(),
		"MgtIp":      request.MgtIp,
		"MgtPort":    request.MgtPort,
		"ConIp":      request.ConIp,
		"ConPort":    request.ConPort,
		"Status":     request.Status,
		"Location":   request.Location,
		"Comments":   request.Comments,
		"RootPasswd": request.RootPasswd,
		"Cycle":      request.Cycle,
		"FsKey":      request.FsKey,
		"Flag":       request.Flag,
		"UserID":     uId,
	})
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

type StatusRequest struct {
	Flag bool `json:"flag"`
}

func UpdateMonitorFlag(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}

	request := StatusRequest{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("create approval read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(&request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	monitor := dresourcepoolmonitor.ResourcePoolMonitor{}
	err = monitor.Find(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if uId != 54 && uId != 1 && monitor.UserID != uId {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "禁止更新他人创建的记录"))
		return
	}

	err = dao.Update(&dresourcepoolmonitor.ResourcePoolMonitor{}, ctx, map[string]interface{}{
		"UpdatedAt": time.Now(),
		"Flag":      request.Flag,
		"UserID":    uId,
	})
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func DeleteMonitor(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}

	monitor := dresourcepoolmonitor.ResourcePoolMonitor{}
	err = monitor.Find(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if uId != 54 && uId != 1 && monitor.UserID != uId {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "禁止删除他人创建的记录"))
		return
	}

	err = dao.Delete(&dresourcepoolmonitor.ResourcePoolMonitor{}, ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}
