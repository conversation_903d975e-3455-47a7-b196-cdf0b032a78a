package dcomplieinfo

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/release"
	"irisAdminApi/service/dao/release/drelease"
)

const ModelName = "编译信息表"

type Response struct {
	ID            uint                     `json:"id"`
	Release       *drelease.ReleaseRelease `gorm:"-" json:"release"`
	ReleaseID     uint                     `json:"release_id"`
	ReleaseBranch string                   `json:"release_branch"`
	Version       string                   `json:"version"`
	Url           string                   `json:"url"`
	Unit          string                   `json:"unit"`
	OpenMR        string                   `json:"open_mr"`
}

type ListResponse struct {
	Response
}

type Request struct {
	ReleaseID     uint   `json:"release_id"`
	ReleaseBranch string `json:"release_branch"`
	Version       string `json:"version"`
	Url           string `json:"url"`
	Unit          string `json:"unit"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *release.ReleaseCompileInfo {
	return &release.ReleaseCompileInfo{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	getReleases(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindAll() ([]*Response, error) {
	var items []*Response

	if err := easygorm.GetEasyGormDb().Model(Model()).Find(&items).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return items, err
	}
	return items, nil
}

func FindInIds(ids []uint) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id in ?", ids).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	return items, nil
}

func GetOrCreate(id string) (uint, error) {
	var item release.ReleaseBuildName
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(&item).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return 0, err
	}
	if item.ID == 0 {
		item = release.ReleaseBuildName{Name: id}
		err := easygorm.GetEasyGormDb().Model(Model()).Create(&item).Error
		if err != nil {
			logging.ErrorLogger.Errorf("find approval err ", err)
			return 0, err
		}
	}
	return item.ID, nil
}

func getRelease(item Response) error {
	var release drelease.ReleaseRelease
	err := release.Find(item.ReleaseID)
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	item.Release = &release
	return nil
}

func getReleases(items []*ListResponse) error {
	var releaseIds []uint
	for _, item := range items {
		releaseIds = append(releaseIds, item.ReleaseID)
	}

	releaseMap := make(map[uint]*drelease.ListResponse)
	releases, err := drelease.FindReleaseInIds(releaseIds)
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	for _, release := range releases {
		releaseMap[release.ID] = release
	}
	for _, item := range items {
		if ListResponse, ok := releaseMap[item.ReleaseID]; ok {
			item.Release = &ListResponse.ReleaseRelease
		}
	}
	return nil
}

func FindByBranchName(name string) ([]*ListResponse, error) {
	var items []*ListResponse

	err := easygorm.GetEasyGormDb().Model(Model()).Where("release_branch = ?", name).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	getReleases(items)
	return items, nil
}

func BatchCreate(object []map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func FindByReleaseID(id uint) ([]*ListResponse, error) {
	var items []*ListResponse

	err := easygorm.GetEasyGormDb().Model(Model()).Where("release_id = ?", id).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	getReleases(items)
	return items, nil
}

func FindByUnit(name string) ([]*ListResponse, error) {
	var items []*ListResponse

	err := easygorm.GetEasyGormDb().Model(Model()).Where("unit = ?", name).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	getReleases(items)
	return items, nil
}

func FindByUnitAndBranch(unit, branch string) ([]*ListResponse, error) {
	var items []*ListResponse

	err := easygorm.GetEasyGormDb().Model(Model()).Where("unit = ? and release_branch = ?", unit, branch).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	getReleases(items)
	return items, nil
}
