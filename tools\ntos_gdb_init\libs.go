package main

import (
	"archive/tar"
	"compress/bzip2"
	"compress/gzip"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/PuerkitoBio/goquery"
)

func createFile(name string) (*os.File, error) {
	name = filepath.ToSlash(name)
	err := os.MkdirAll(string([]rune(name)[0:strings.LastIndex(name, "/")]), 0755)
	if err != nil {
		return nil, err
	}
	return os.Create(name)
}

func createDir(name string) error {
	name = filepath.ToSlash(name)
	err := os.MkdirAll(name, 0755)
	if err != nil {
		return err
	}
	return nil
}

func DeCompress(src, dest string) error {
	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()
	gr, err := gzip.NewReader(srcFile)
	if err != nil {
		return err
	}
	defer gr.Close()
	tr := tar.NewReader(gr)
	for {
		hdr, err := tr.Next()
		if err == io.EOF {
			break
		}
		if err != nil {
			return err
		}
		filename := filepath.Join(dest, hdr.Name)
		fi := hdr.FileInfo()
		if fi.IsDir() {
			err := createDir(filename)
			if err != nil {
				return err
			}
			continue
		}

		if hdr.Typeflag == tar.TypeSymlink {
			// 创建软链接
			if err := os.Symlink(hdr.Linkname, filename); err != nil {
				return err
			}
		} else {
			file, err := createFile(filename)
			if err != nil {
				return err
			}
			io.Copy(file, tr)
		}
	}
	return nil
}

func DeCompressBzip2(src, dest string) error {
	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()
	br := bzip2.NewReader(srcFile)
	// //创建空文件，准备写入解压后的数据
	// _, fn := filepath.Split(src)
	// filename := filepath.Join(dest, strings.Replace(fn, ".gz", "", -1))
	// fmt.Println(filename)
	// file, err := createFile(filename)
	// if err != nil {
	// 	fmt.Println(err)
	// 	return err
	// }
	// _, err = io.Copy(file, br)
	// if err != nil {
	// 	fmt.Println(err.Error())
	// 	return err
	// }
	tr := tar.NewReader(br)
	for {
		hdr, err := tr.Next()

		if err == io.EOF {
			break
		}
		if err != nil {
			return err
		}
		filename := filepath.Join(dest, hdr.Name)
		fi := hdr.FileInfo()
		if fi.IsDir() {
			err := createDir(filepath.Join(dest, hdr.Name))
			if err != nil {
				return err
			}
			continue
		}

		if hdr.Typeflag == tar.TypeSymlink {
			// 创建软链接
			if err := os.Symlink(hdr.Linkname, filename); err != nil {
				return err
			}
		} else {
			file, err := createFile(filename)
			if err != nil {
				return err
			}
			io.Copy(file, tr)
		}
	}
	return nil
}

func GetReleaseID(dest string) (string, error) {
	files, err := os.ReadDir(dest)
	if err != nil {
		return "", err
	}
	for _, f := range files {
		if f.Name() == "version.txt" {
			f, err := os.ReadFile(filepath.Join(dest, f.Name()))
			if err != nil {
				fmt.Println("read version.txt err", err)
				return "", err
			}
			versionInfo := strings.Split(string(f), "\n")
			for _, line := range versionInfo {
				if strings.Contains(line, "Release") {
					releaseLine := strings.Replace(strings.Replace(line, "(", " ", -1), ")", "", -1)
					releaseInfo := strings.Split(releaseLine, " ")
					releaseID := releaseInfo[len(releaseInfo)-1]
					return releaseID, nil
				}
			}
		}
	}
	return "", nil
}

func GetSoftVersion(dest string) (string, error) {
	files, err := os.ReadDir(dest)
	if err != nil {
		return "", err
	}
	for _, f := range files {
		if f.Name() == "version.txt" {
			f, err := os.ReadFile(filepath.Join(dest, f.Name()))
			if err != nil {
				fmt.Println("read version.txt err", err)
				return "", err
			}
			versionInfo := strings.Split(string(f), "\n")
			for _, line := range versionInfo {
				if strings.Contains(line, "NTOS") || strings.Contains(line, "IDP") {
					// releaseLine := strings.Replace(line, " ", "", -1)
					// return releaseLine, nil
					return line, nil
				}
			}
		}
	}
	return "", nil
}

func GetSoftNum(dest string) (string, error) {
	files, err := os.ReadDir(dest)
	if err != nil {
		return "", err
	}
	for _, f := range files {
		if f.Name() == "version.txt" {
			f, err := os.ReadFile(filepath.Join(dest, f.Name()))
			if err != nil {
				fmt.Println("read version.txt err", err)
				return "", err
			}
			versionInfo := strings.Split(string(f), "\n")
			for _, line := range versionInfo {
				if strings.HasPrefix(line, "softnum:") {
					softnum := strings.Trim(strings.Split(line, ":")[1], " ")
					return softnum, nil
				}
			}
		}
	}
	return "", nil
}

func GetProduct(dest string) (string, error) {
	files, err := os.ReadDir(dest)
	if err != nil {
		return "", err
	}
	for _, f := range files {
		if f.Name() == "version.txt" {
			f, err := os.ReadFile(filepath.Join(dest, f.Name()))
			if err != nil {
				fmt.Println("read version.txt err", err)
				return "", err
			}
			versionInfo := strings.Split(string(f), "\n")
			for _, line := range versionInfo {
				if strings.HasPrefix(line, "ProductName:") {
					product := strings.Trim(strings.Split(line, ":")[1], " ")
					return strings.ToLower(product), nil
				}
			}
		}
	}
	return "", nil
}

func GetBranch(dest string) (string, error) {
	files, err := os.ReadDir(dest)
	if err != nil {
		return "", err
	}
	for _, f := range files {
		if f.Name() == "version.txt" {
			f, err := os.ReadFile(filepath.Join(dest, f.Name()))
			if err != nil {
				fmt.Println("read version.txt err", err)
				return "", err
			}
			versionInfo := strings.Split(string(f), "\n")
			for _, line := range versionInfo {
				if strings.Contains(line, "Release") {
					releaseLine := strings.Replace(strings.Replace(strings.Replace(line, "(", " ", -1), ")", "", -1), ",", "", -1)
					releaseInfo := strings.Split(releaseLine, " ")
					releaseID := releaseInfo[1]
					if releaseID == "Trunk" {
						return releaseID, nil
					} else {
						return "NTOS" + releaseID, nil
					}
				}
			}
		}
	}
	return "", nil
}

func GetVersion(dest string) (string, error) {
	files, err := os.ReadDir(dest)
	if err != nil {
		return "", err
	}
	for _, f := range files {
		if f.Name() == "version.txt" {
			f, err := os.ReadFile(filepath.Join(dest, f.Name()))
			if err != nil {
				fmt.Println("read version.txt err", err)
				return "", err
			}
			versionInfo := strings.Split(string(f), "\n")
			for _, line := range versionInfo {
				if strings.Contains(line, "Release") {
					releaseLine := strings.Replace(strings.Replace(strings.Replace(line, "(", " ", -1), ")", "", -1), ",", "", -1)
					releaseInfo := strings.Split(releaseLine, " ")
					releaseID := releaseInfo[1]
					if releaseID == "Trunk" {
						return releaseID, nil
					} else {
						return "NTOS" + releaseID, nil
					}
				}
			}
		}
	}
	return "", nil
}

func DonwloadRootfsAndImage(output, downloadUrl, rootfsDebugOutput string) error {
	var err error
	resp, _ := client.SetOutputDirectory(output).R().Get(downloadUrl)
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		fmt.Println("get download url err", err)
		return err
	}
	successCount := 0
	var rootfsDebugFile, imageFile string
	doc.Find("a").EachWithBreak(func(i int, s *goquery.Selection) bool {
		// For each item found, get the title
		title := s.Text()
		if strings.HasSuffix(title, "rootfs-debug.tar.gz") {
			filename := s.Text()
			rootfsDebugFile = filename
			if _, err = client.R().SetOutputFile(filename).Get(downloadUrl + filename); err != nil {
				fmt.Println("download rootfs debug file failed", err)
				return false
			}
			successCount++
		}
		if strings.HasSuffix(title, "image.tar.bz2") {
			filename := s.Text()
			imageFile = filename
			// if _, err = client.R().SetOutputFile(filename).Get(downloadUrl + filename); err != nil {
			// 	fmt.Println("download image file failed", err)
			// 	return false
			// }
			successCount++
		}
		return true
	})
	if len(rootfsDebugFile) <= 0 {
		return fmt.Errorf("Rootf下载地址不存在")
	}
	if len(imageFile) <= 0 {
		return fmt.Errorf("Image下载地址不存在")
	}
	if successCount == 2 {
		if err := DeCompressBzip2(filepath.Join(output, rootfsDebugFile), rootfsDebugOutput); err != nil {
			return fmt.Errorf("Rootf解压失败")
		}
		// if err := DeCompressBzip2(filepath.Join(output, imageFile), output); err != nil {
		// 	return fmt.Errorf("Image解压失败")
		// 	return err
		// }
		return nil
	}
	return fmt.Errorf("Rootf下载失败")
	// return fmt.Errorf("Rootf、Image下载失败")
}

func GetProductBuildName(product string) (string, error) {
	// 构造请求URL
	url := fmt.Sprintf("%s/coredump-api/api/v1/coredump/product2buildname?product=%s", coredumpServer, product)

	// 发送GET请求
	resp, err := http.Get(url)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("server returned non-200 status: %d %s", resp.StatusCode, resp.Status)
	}

	// 读取响应体
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	// 转换响应体为字符串
	buildName := string(bodyBytes)

	return buildName, nil
}
