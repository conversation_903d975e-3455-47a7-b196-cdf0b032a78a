package mergerequest

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/datasync/dbug"
	"irisAdminApi/service/dao/datasync/dbuglog"
	"irisAdminApi/service/dao/mergerequest/dmergerequest"

	"github.com/kataras/iris/v12"
	"github.com/pkg/errors"
)

type SyncRequest struct {
	ID      uint          `json:"id"`
	Targets []*SyncTarget `json:"targets"`
}

type SyncTarget struct {
	ReleaseProjectID   uint   `json:"release_project_id"`
	ReleaseProjectName string `json:"release_project_name"`
	BranchName         string `json:"branch_name"`
	BugID              string `json:"bug_id"`
}

func BugCheckForSync(request *SyncTarget) error {
	if !bugidPattern.MatchString(request.BugID) {
		return fmt.Errorf("请输入BUGID, 多个BUGID以','分隔")
	}
	err := CheckBugIDsForSync(request)
	if err == nil {
		return nil
	}
	resp, err := BugWebClient.R().Get("http://10.51.134.126:9008/api/v1/datasync/bug")
	if err != nil {
		return err
	}
	if resp.IsErrorState() {
		return fmt.Errorf("%s", resp.String())
	}

	// return CheckBugIDs(request)
	return CheckBugByAPIForSync(request)
}

func CheckBugIDsForSync(request *SyncTarget) error {
	bugIDs := strings.Split(request.BugID, ",")

	bugMap := map[string]*dbug.Bug{}
	bugs, err := dbug.FindBugInBugIDs(bugIDs)
	if err != nil {
		return errors.Wrap(err, "查询错误")
	}

	for _, bug := range bugs {
		bugMap[fmt.Sprintf("%d", bug.BugID)] = bug
	}

	for _, bugID := range bugIDs {
		if bug, ok := bugMap[bugID]; ok {
			if bug.BugOS != request.ReleaseProjectName {
				return fmt.Errorf("%s 不属于项目 %s", bugID, request.ReleaseProjectName)
			}

			if !libs.InArrayS([]string{"RESOLVED", "CHECKED", "DELAY"}, bug.BugState) {
				return fmt.Errorf("%s 状态为%s, 需要推进至RESOLVED/CHECKED", bugID, bug.BugState)
			}
		} else {
			return fmt.Errorf("%s Bug未找到, 请确认", bugID)
		}
	}
	return nil
}

// CheckBugByAPI 通过API接口方式获取bug信息并验证
func CheckBugByAPIForSync(request *SyncTarget) error {
	if !bugidPattern.MatchString(request.BugID) {
		return fmt.Errorf("请输入BUGID, 多个BUGID以','分隔")
	}
	releaseProject := request.ReleaseProjectName
	// 调用API获取Bug信息
	bugInfos, err := GetBugInfoByAPI(request.BugID)
	if err != nil {
		return err
	}

	// 检查所有返回的Bug信息
	for _, bugInfo := range bugInfos {
		if bugInfo.BugStatus != "" { // 如果Bug状态为空，则不进行检查
			// 检查bug状态是否在允许的列表中
			if !libs.InArrayS([]string{"RESOLVED", "CHECKED", "DELAY"}, bugInfo.BugStatus) {
				errorMsg := fmt.Sprintf("%s 状态为%s, 需要推进至RESOLVED/CHECKED/DELAY", bugInfo.BugID, bugInfo.BugStatus)
				logging.ErrorLogger.Error(errorMsg)
				return fmt.Errorf(errorMsg)
			}
			// 检查bug的OS是否与请求的ReleaseProject一致
			if releaseProject != "" && bugInfo.OS != "" && bugInfo.OS != releaseProject {
				errorMsg := fmt.Sprintf("%s 不属于项目 %s", bugInfo.BugID, releaseProject)
				logging.ErrorLogger.Error(errorMsg)
				return fmt.Errorf(errorMsg)
			}
		}
	}
	return nil
}

func MergeRequestSyncWorker(ctx iris.Context) {
	token, _ := dao.GetGitlabToken(ctx)
	if token == "" {
		logging.ErrorLogger.Errorf("未配置gitlab token, 请前往个人资料添加gitlab token")
		return
	}

	gitlabToken := libs.Config.Gitlab.Token
	if gitlabToken == "" {
		logging.ErrorLogger.Errorf("未配置gitlab token, 请联系管理员")
		return
	}

	request := SyncRequest{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	mr := dmergerequest.MergeRequest{}
	err := mr.Find(request.ID)
	if err != nil {
		logging.ErrorLogger.Errorf("find source mr get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if mr.ID == 0 {
		logging.ErrorLogger.Errorf("source mr not found ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	for _, target := range request.Targets {
		if mr.Type == "bugfix" && libs.Config.MergeRequest.BugCheckEnable {
			// 记录开始时间
			startTime := time.Now()
			err := BugCheckForSync(target)
			// 记录结束时间和耗时
			elapsedTime := time.Since(startTime)
			logging.InfoLogger.Info(fmt.Sprintf("BugCheck execution time: %d ms", elapsedTime.Milliseconds()))
			if err != nil {
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
				return
			}
		}
	}

	targetProjectID := mr.TargetProjectID
	mergeRequestIID := mr.MergeRequestIID

	targetProjectDetail, err := GetGitlabProjectDetail(targetProjectID)
	if err != nil {
		logging.ErrorLogger.Errorf("get target project detail err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	mrDetail, err := GetMergeRequestDetail(targetProjectID, mergeRequestIID)
	if err != nil {
		logging.ErrorLogger.Errorf("get mr detail err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	members, err := GetRepositoryMembers(mr.TargetProjectID)
	if err != nil {
		logging.ErrorLogger.Errorf("get mr detail err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	memberMap := map[string]string{}
	for _, member := range members {
		memberMap[fmt.Sprintf("%d", member.ID)] = member.Username
	}
	reviewerIDs := strings.Split(mr.ReviewerIDs, ",")
	reviewerIDs = append(reviewerIDs, fmt.Sprintf("%d", mrDetail.Author.ID))
	reviewerUsernames := []string{memberMap[mrDetail.Author.Username]}
	assigneeUsernames := []string{}
	for _, assigneeID := range strings.Split(mr.AssigneeIDs, ",") {
		reviewerUsernames = append(reviewerUsernames, memberMap[assigneeID])
	}

	for _, reviewerID := range strings.Split(mr.ReviewerIDs, ",") {
		assigneeUsernames = append(assigneeUsernames, memberMap[reviewerID])
	}

	userID, _ := dao.GetAuthId(ctx)
	mrTargetMap := map[string]*dmergerequest.MergeRequest{}
	syncObjects := []*dmergerequest.MergeRequest{}
	// 查找是否存在记录

	for _, target := range request.Targets {
		_mr, err := dmergerequest.FindBySourceIDAndTargetBranch(mr.ID, target.BranchName)
		if err != nil {
			logging.ErrorLogger.Errorf("find mr by sourceid and target branch get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		if _mr.ID > 0 && libs.InArrayInt([]int{1, 2, 3}, _mr.Status) {
			logging.ErrorLogger.Errorf("alread exists mr ", _mr.ID)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("%s 已存在未合并/未关闭MR, 请确认！", target.ReleaseProjectName)))
			return
		}

		targetBranch := target.BranchName
		sourceBranch := fmt.Sprintf("sync_%v_%v", mr.ID, targetBranch)

		workPakcage := "遗留或delay的bug(其他)"

		var title string
		switch mr.Type {
		case "new":
			// [新增][工作包][TITLE]
			title = fmt.Sprintf("[%s][自动同步][%s][%s][%s][%s]", target.ReleaseProjectName, mr.ReleaseProject, workPakcage, TypeMap[mr.Type], mr.OriginTitle)
		case "bugfix":
			// [BUG修订][BUGID][工作包][TITLE]
			title = fmt.Sprintf("[%s][自动同步][%s][%s][%s][%s][%s]", target.ReleaseProjectName, mr.ReleaseProject, workPakcage, TypeMap[mr.Type], target.BugID, mr.OriginTitle)
		default:
			ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "变更类型: "+response.ParamentErr.Msg))
			return
		}

		bugid := target.BugID
		if target.BugID == "" {
			bugid = "N/A"
		}

		localBuildPass := "是"
		if !mr.LocalBuildPass {
			localBuildPass = "否"
		}
		preCheck := "是"
		if !mr.PreCheck {
			preCheck = "否"
		}
		portable := "是"
		if !mr.Portable {
			portable = "否"
		}
		// 为了格式正确，请匆修订对齐
		description := fmt.Sprintf(
			`[修订类型]：%s
[BUGID]：%s
[工作包]：%s
[移植]：%s
[本地编译]：%s
[代码检查]：%s
[合入后评审]：%s
[BugResolve描述]:

`,
			TypeMap[mr.Type]+"  ",
			bugid+"  ",
			workPakcage+"  ",
			portable+"  ",
			localBuildPass+"  ",
			preCheck+"  ",
			MfalMap[mr.MFAL]+"  ",
			// bugResolveDesc+"  ",
			// request.OriginDescription+"  ",
		)
		// 获取Bug resolve 描述
		logs, err := dbuglog.FindAllLogsInBugIDs(target.BugID)
		if err != nil {
			logging.ErrorLogger.Errorf("find bug log get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		for _, log := range logs {
			// bugResolveDesc = fmt.Sprintf(`%s\n[%d]: %s`, bugResolveDesc, log.BugID, log.Info)
			description = fmt.Sprintf(
				`%s
			%s
`,
				description,
				fmt.Sprintf("【%v】", log.BugID)+"  ",
			)
			for _, line := range strings.Split(log.Info, "\n") {
				for _, keyword := range []string{"根因定位结论:", "分析过程:", "解决方案:", "对其他模块影响:"} {
					if strings.HasPrefix(line, keyword) {
						description = fmt.Sprintf(
							`%s
						%s
`,
							description,
							line+"  ",
						)
					}
				}
			}
		}

		description = fmt.Sprintf(
			`【自动同步】%s!%d  
%s  
%s  
`,
			mr.TargetProject,
			mr.MergeRequestIID,
			description+"  ",
			mr.OriginDescription+"  ",
		)

		// create merge request
		// 	// 批量创建记录表
		object := dmergerequest.MergeRequest{
			MainID:           mr.ID,
			ReleaseProjectID: target.ReleaseProjectID,
			ReleaseProject:   target.ReleaseProjectName,
			WorkPackageID:    0,
			WorkPackage:      "",

			TargetProjectID: mr.TargetProjectID,
			TargetProject:   mr.TargetProject,
			TargetBranch:    targetBranch,

			SourceProjectID: 0,
			SourceProject:   "",
			SourceBranch:    sourceBranch,
			Type:            mr.Type,
			BugID:           target.BugID,
			LocalBuildPass:  mr.LocalBuildPass,
			Portable:        mr.Portable,
			PreCheck:        mr.PreCheck,
			MFAL:            mr.MFAL,
			ScreenshotFile:  mr.ScreenshotFile,

			Title:              title,
			OriginTitle:        mr.OriginTitle,
			Description:        description,
			OriginDescription:  mr.OriginDescription,
			UserID:             userID,
			Status:             0,
			MergeRequestID:     0,
			MergeRequestIID:    0,
			PipelineStatus:     0,
			PhabricatorStatus:  0,
			CodeQuantityAdd:    0,
			CodeQuantityRemove: 0,
			ReviewerIDs:        strings.Join(reviewerIDs, ","),
			ReviewerUsernames:  reviewerUsernames,
			AssigneeIDs:        mr.AssigneeIDs,
			AssigneeUsernames:  assigneeUsernames,
			Phabricator:        mr.Phabricator,
			Discount:           mr.Discount,
			DependencyIDs:      "",
		}

		mrTargetMap[targetBranch] = &object
		syncObjects = append(syncObjects, &object)
	}

	err = mr.BatchCreate(syncObjects)
	if err != nil {
		logging.ErrorLogger.Errorf("alread exists mr ", mr.ID)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	go func() {
		patchPath := filepath.Join("/mnt/sata0/codesync/patch", fmt.Sprintf("%v.patch", mr.ID))
		err = GeneratePatch(targetProjectDetail, mrDetail, &mr, patchPath)
		if err != nil {
			logging.ErrorLogger.Errorf("generate path get err ", err)
			for _, _mr := range syncObjects {
				err := _mr.Update(_mr.ID, map[string]interface{}{
					"Status": -1,
					"ErrMsg": "生成Patch失败: " + err.Error(),
				})
				if err != nil {
					logging.ErrorLogger.Errorf("update merge request get err ", err)
				}
			}

			return
		}
		for _, target := range request.Targets {
			go HandelSyncCreateMergeRequestError(gitlabToken, targetProjectDetail, mrDetail, mrTargetMap[target.BranchName], patchPath, target)
		}
	}()

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GeneratePatch(targetProjectDetail *ProjectResponse, mrDetail *MergeRequestResponse, mr *dmergerequest.MergeRequest, patchPath string) error {
	workDir := filepath.Join("/mnt/sata0/codesync/repository", fmt.Sprintf("gen_patch_%v", mr.ID))

	defer os.RemoveAll(workDir)
	// fmt.Println("start code check ")

	targetBranch := mr.TargetBranch

	// fmt.Println("start code check ", sourceProjectID, mr.SourceBranch, targetProjectID, targetBranch)

	targetProjectUrl := targetProjectDetail.SshUrlToRepo

	// clone repo
	var command string

	command = fmt.Sprintf("rm %s -rf && git clone -b %s %s %s", workDir, targetBranch, targetProjectUrl, workDir)
	_, err := libs.ExecCommand(command)
	if err != nil {
		return err
	}

	command = fmt.Sprintf("cd %s && git fetch origin merge-requests/%d/head:mr-origin-%d && git checkout mr-origin-%d", workDir, mr.MergeRequestIID, mr.MergeRequestIID, mr.MergeRequestIID)
	_, err = libs.ExecCommand(command)
	if err != nil {
		return err
	}

	command = fmt.Sprintf("cd %s && git diff --binary %s..%s > %s", workDir, mrDetail.DiffRefs.BaseSha, mrDetail.DiffRefs.HeadSha, patchPath)
	_, err = libs.ExecCommand(command)
	if err != nil {
		return err
	}
	return nil
}

func HandelSyncCreateMergeRequestError(token string, targetProjectDetail *ProjectResponse, mrDeatil *MergeRequestResponse, mr *dmergerequest.MergeRequest, patchPath string, target *SyncTarget) {
	err := CodeSyncCreateMergeRequest(targetProjectDetail, mrDeatil, mr, patchPath, target)
	if err != nil {
		err = mr.Update(mr.ID, map[string]interface{}{
			"Status": -1,
			"ErrMsg": "创建MR失败: " + err.Error(),
		})
		if err != nil {
			logging.ErrorLogger.Errorf("update merge request get err ", err)
			return
		}
		return
	}
	CreateGitlabMergeRequestWorker(token, []*dmergerequest.MergeRequest{mr})
}

func CodeSyncCreateMergeRequest(targetProjectDetail *ProjectResponse, mrDeatil *MergeRequestResponse, mr *dmergerequest.MergeRequest, patchPath string, target *SyncTarget) error {
	// fork target repository
	// 检查是否已经存在FORK仓库
	targetBranch := target.BranchName
	fork, err := CheckProjectFork(mr.TargetProjectID)
	if err != nil {
		logging.ErrorLogger.Errorf("delete code sync temp dir error", err)
		return errors.Wrap(err, "Fork仓库失败")
	}
	sourceBranch := fmt.Sprintf("sync_%v_%v", mr.MainID, targetBranch)
	workDir := filepath.Join("/mnt/sata0/codesync/repository", fmt.Sprintf("sync_%v_%v", mr.MainID, targetBranch))

	targetProjectUrl := targetProjectDetail.SshUrlToRepo
	forkProjectUrl := fork.SshUrlToRepo

	command := fmt.Sprintf("rm %s -rf && git clone -b %s %s %s && cd %s && git remote add upstream %s ", workDir, targetBranch, targetProjectUrl, workDir, workDir, forkProjectUrl)
	output, err := libs.ExecCommand(command)
	if err != nil {
		logging.ErrorLogger.Errorf("git clone get err ", err)
		return errors.Wrapf(err, "Clone仓库失败 %s", output)
	}

	command = fmt.Sprintf("cd %s && git checkout -b %s", workDir, sourceBranch)
	output, err = libs.ExecCommand(command)
	if err != nil {
		logging.ErrorLogger.Errorf("git rebase get err ", err)
		return errors.Wrapf(err, "创建分支失败 %s", output)
	}

	command = fmt.Sprintf("cd %s && git apply --3way %s", workDir, patchPath)
	output, err = libs.ExecCommand(command)
	if err != nil {
		logging.ErrorLogger.Errorf("git apply patch get err ", err)
		// 尝试使用wiggle强制应用patch
		command = fmt.Sprintf(`cd %s && find ./ -name "*.rej"`, workDir)
		output2, err2 := libs.ExecCommand(command)
		if err2 != nil {
			return errors.Wrapf(err, "%s %s %s %s", err2.Error(), "查找冲突文件失败", output, output2)
		}
		for _, line := range strings.Split(output2, "\n") {
			rejFilePath := strings.TrimSpace(line)
			originFilePath := strings.TrimSuffix(rejFilePath, ".rej")
			command2 := fmt.Sprintf("cd %s && wiggle -r %s %s", workDir, originFilePath, rejFilePath)
			output3, err3 := libs.ExecCommand(command2)
			if err3 != nil {
				return errors.Wrapf(err, "%s %s %s %s %s", err3.Error(), "使用Wiggle应用Patch失败", output, output2, output3)
			}
		}
	}

	command = fmt.Sprintf("cd %s && git status|grep modified:|awk '{print $NF}'|xargs git add &&  git commit -a -m '%s'", workDir, fmt.Sprintf("sync from mr %v", mr.MainID))
	output, err = libs.ExecCommand(command)
	if err != nil {
		logging.ErrorLogger.Errorf("git apply patch get err ", err)
		return errors.Wrapf(err, "Commit失败 %s", output)
	}

	command = fmt.Sprintf("cd %s && git push -f upstream %s", workDir, sourceBranch)
	output, err = libs.ExecCommand(command)
	if err != nil {
		logging.ErrorLogger.Errorf("git push get err ", err)
		return errors.Wrapf(err, "Push失败 %s", output)
	}
	// create merge request

	err = mr.Update(mr.ID, map[string]interface{}{
		"SourceProjectID": fork.ID,
		"SourceProject":   fork.PathWithNamespace,
	})
	if err != nil {
		return errors.Wrapf(err, "数据库更新失败")
	}
	mr.SourceProjectID = uint(fork.ID)
	mr.SourceProject = fork.PathWithNamespace

	return nil
}

func CheckProjectFork(repositoryID uint) (*ForkResponse, error) {
	var _fork *ForkResponse
	forked := false
	finished := false
	for i := 0; i <= 10; i++ {
		forks, err := GetProjectFork(repositoryID)
		if err != nil {
			logging.ErrorLogger.Errorf("delete code sync temp dir error", err)
			return _fork, err
		}

		for _, fork := range forks {
			forked = strings.HasPrefix(fork.PathWithNamespace, "buildfarm/")
			if forked {
				_fork = fork
				break
			}
		}

		if !forked && i == 0 {
			CreateProjectFork(repositoryID)
			continue
		}
		if _fork != nil {
			finished = _fork.ImportStatus == "finished"
			if finished {
				break
			}
		}

		time.Sleep(5 * time.Second)
	}
	if forked && finished {
		return _fork, nil
	}
	return _fork, errors.New("查询fork项目失败: 未知错误")
}

func CreateProjectFork(repositoryID uint) (ForkResponse, error) {
	url := fmt.Sprintf("%s/api/%s/projects/%d/fork", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, repositoryID)
	var result ForkResponse
	var errMsg string
	data := map[string]string{
		"private_token": libs.Config.Gitlab.Token,
	}
	resp, err := CodeSyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(data).Post(url)
	if err != nil {
		return result, errors.New(fmt.Sprintf("%s, %s", err.Error(), errMsg))
	}
	if resp.IsSuccessState() {
		return result, nil
	}
	return result, errors.New(fmt.Sprintf("%s, %s", "未知错误", errMsg))
}

func GetProjectFork(repositoryID uint) ([]*ForkResponse, error) {
	var result []*ForkResponse
	var errMsg string
	url := fmt.Sprintf("%s/api/%s/projects/%d/forks", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, repositoryID)
	page := 1
	pageSize := 100
	for {
		var _result []*ForkResponse
		data := map[string]string{
			"private_token": libs.Config.Gitlab.Token,
			"page":          fmt.Sprintf("%v", page),
			"per_page":      fmt.Sprintf("%v", pageSize),
		}
		resp, err := CodeSyncClient.R().SetSuccessResult(&_result).SetErrorResult(&errMsg).SetQueryParams(data).Get(url)
		if err != nil {
			return result, errors.New(fmt.Sprintf("%s, %s", err.Error(), errMsg))
		}
		if resp.IsSuccessState() {
			if len(_result) == 0 {
				break
			}
			result = append(result, _result...)
			page++
			time.Sleep(1 * time.Second)
			continue
		}
		return result, errors.New(fmt.Sprintf("%s, %s", "未知错误", errMsg))
	}
	return result, nil
}

func GetRepositoryMembers(id uint) ([]*MemberResponse, error) {
	// /projects/:id/repository/branches
	var result []*MemberResponse
	var errMsg MessageResponse
	page := 1
	for {
		var _result []*MemberResponse
		url := fmt.Sprintf("%s/api/%s/projects/%d/members/all?private_token=%s&search=%s&page=%d&per_page=100", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, id, libs.Config.Gitlab.Token, "", page)

		resp, err := GitlabWebClient.R().SetSuccessResult(&_result).SetErrorResult(&errMsg).Get(url)
		if err != nil {
			return result, err
		}
		if resp.IsSuccessState() {
			page++
			if len(_result) > 0 {
				result = append(result, _result...)
			} else {
				break
			}
		} else {
			return result, fmt.Errorf(errMsg.Message)
		}
	}
	return result, nil
}
