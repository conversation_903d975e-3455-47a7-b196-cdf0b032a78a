package firewallflextrans

import "irisAdminApi/application/models"

type FirewallFlexTran struct {
	models.ModelBase
	JobID string `gorm:"not null; type:varchar(60)" json:"job_id"`

	InputFileName string `gorm:"not null; type:varchar(200)" json:"intput_file_name"`
	InputMD5      string `gorm:"not null; type:varchar(60)" json:"input_md5"`

	OutputFileName string `gorm:"not null; type:varchar(200)" json:"output_file_name"`
	OutputMD5      string `gorm:"not null; type:varchar(200)" json:"output_md5"`

	WithLib string `gorm:"not null; type:varchar(60); default:'no'" json:"with_lib"`
	Status  uint   `gorm:"not null" json:"status"` // 0: 进行中 1: 成功 2: 失败
	UserID  uint   `gorm:"not null" json:"user_id"`
	App     string `gorm:"not null; type:varchar(60); default:''" json:"app"`
}

