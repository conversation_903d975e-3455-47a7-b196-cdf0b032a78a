@echo off
setlocal enabledelayedexpansion

:: 设置版本信息
set BUILD_VERSION=v1.1.1

:: 获取构建时间（ISO格式）
for /f "tokens=2 delims==" %%a in ('wmic os get localdatetime /value 2^>nul') do set datetime=%%a
set BUILD_TIME=%datetime:~0,4%-%datetime:~4,2%-%datetime:~6,2% %datetime:~8,2%:%datetime:~10,2%:%datetime:~12,2%

:: 生成构建名称（app_YYYYMMDDHH）
set BUILD_NAME=app_%datetime:~0,8%%datetime:~8,2%

:: 获取Git提交SHA1
for /f "delims=" %%c in ('git rev-parse HEAD 2^>nul') do set COMMIT_SHA1=%%c

:: 构建LDFLAGS参数
set LDFLAGS="-X main.BuildVersion=%BUILD_VERSION% -X main.BuildTime=%BUILD_TIME% -X main.BuildName=%BUILD_NAME% -X main.CommitID=%COMMIT_SHA1%"

:: 清理输出目录
if exist output\ (
    echo Cleaning output directory...
    rmdir /s /q output
)
mkdir output

:: 主构建命令
echo Building main program...
go build -o output\buildFarmApi.exe main.go
if errorlevel 1 (
    echo Failed to build main program
    exit /b 1
)

@REM :: 构建工具
@REM echo Building ntos_gdb_init...
@REM go build -o output\ntos_gdb_init.exe tools\ntos_gdb_init\*.go
@REM if errorlevel 1 (
@REM     echo Failed to build ntos_gdb_init
@REM     exit /b 1
@REM )

@REM echo Building noticer...
@REM go build -o output\noticer.exe tools\noticer\*.go
@REM if errorlevel 1 (
@REM     echo Failed to build noticer
@REM     exit /b 1
@REM )

echo Build successful
endlocal