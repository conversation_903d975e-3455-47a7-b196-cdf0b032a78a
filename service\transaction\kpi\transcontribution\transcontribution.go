package transcontribution

import (
	"errors"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/service/dao/kpi/dcontribution"
	"irisAdminApi/service/dao/kpi/dcontributionpoint"
	"irisAdminApi/service/dao/kpi/dcontributionreview"
	"irisAdminApi/service/dao/kpi/dcontributionversion"

	"strconv"
	"time"

	"gorm.io/gorm"
)

func CreateContributionTransaction(contributionObjects map[string]interface{}, pointObjects []map[string]interface{}, reviewObjects []map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		if err := tx.Model(dcontribution.Model()).Create(contributionObjects).Error; err != nil {
			// 返回任何错误都会回滚事务
			return err
		}
		contribution := dcontribution.Response{}
		if err := tx.Model(dcontribution.Model()).Where("uuid = ?", contributionObjects["Uuid"]).Find(&contribution).Error; err != nil {
			return err
		}
		if contribution.ID == 0 {
			return errors.New("创建贡献记录失败")
		}
		for _, pointObject := range pointObjects {
			pointObject["ContributionID"] = contribution.ID
		}
		if err := tx.Model(dcontributionpoint.Model()).Create(pointObjects).Error; err != nil {
			return err
		}
		if len(reviewObjects) > 0 {
			for _, reviewObject := range reviewObjects {
				reviewObject["ContributionID"] = contribution.ID
			}

			if err := tx.Model(dcontributionreview.Model()).Create(reviewObjects).Error; err != nil {
				return err
			}
		}

		if err := tx.Model(dcontributionversion.Model()).Create(map[string]interface{}{
			"CreatedAt":       time.Now(),
			"ContributionID":  contribution.ID,
			"ContributionIDs": 0,
		}).Error; err != nil {
			return err
		}
		// 返回 nil 提交事务
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func UpdateContributionTransaction(id uint, contributionObjects map[string]interface{}, pointObjects []map[string]interface{}, reviewObjects []map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		// 检查是否已经是历史版本，禁止更新历史版本
		checkLastContribution := dcontribution.Response{}
		if err := tx.Model(dcontribution.Model()).Where("contribution_id = ?", id).Find(&checkLastContribution).Error; err != nil {
			return err
		}
		if checkLastContribution.ID > 0 {
			return errors.New("禁止更新历史版本，请刷新后重试")
		}
		lastContribution := dcontribution.Response{}
		if err := tx.Model(dcontribution.Model()).Where("id = ?", id).Find(&lastContribution).Error; err != nil {
			return err
		}

		if err := tx.Model(dcontribution.Model()).Create(contributionObjects).Error; err != nil {
			// 返回任何错误都会回滚事务
			return err
		}

		newContribution := dcontribution.Response{}
		if err := tx.Model(dcontribution.Model()).Where("uuid = ?", contributionObjects["Uuid"]).Find(&newContribution).Error; err != nil {
			return err
		}
		if newContribution.ID == 0 || lastContribution.ID == 0 {
			return errors.New("创建失败")
		}

		if len(pointObjects) == 0 {
			return errors.New("贡献分详情记录不能为空")
		}
		for _, pointObject := range pointObjects {
			pointObject["ContributionID"] = newContribution.ID
		}
		if err := tx.Model(dcontributionpoint.Model()).Create(pointObjects).Error; err != nil {
			return err
		}
		if len(reviewObjects) > 0 {
			for _, reviewObject := range reviewObjects {
				reviewObject["ContributionID"] = newContribution.ID
			}
			if err := tx.Model(dcontributionreview.Model()).Create(reviewObjects).Error; err != nil {
				return err
			}
		}

		contributionVersion := dcontributionversion.Response{}
		if err := tx.Model(dcontributionversion.Model()).Where("contribution_id = ?", lastContribution.ID).Find(&contributionVersion).Error; err != nil {
			return err
		}
		if contributionVersion.ID > 0 {
			if err := tx.Model(dcontributionversion.Model()).Where("contribution_id = ?", lastContribution.ID).Updates(map[string]interface{}{
				"ContributionID":  newContribution.ID,
				"ContributionIDs": contributionVersion.ContributionIDs + "," + strconv.FormatUint(uint64(lastContribution.ID), 10),
			}).Error; err != nil {
				return err
			}
		} else {
			if err := tx.Model(dcontributionversion.Model()).Create(map[string]interface{}{
				"ContributionID":  newContribution.ID,
				"ContributionIDs": strconv.FormatUint(uint64(lastContribution.ID), 10),
			}).Error; err != nil {
				return err
			}
		}

		// 返回 nil 提交事务
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func UpdateReviewTransaction(id uint, reviewObject map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		// 检查是否已经是历史版本，禁止更新历史版本
		review := dcontributionreview.Response{}
		if err := tx.Model(dcontributionreview.Model()).Where("id = ?", id).Find(&review).Error; err != nil {
			return err
		}
		if review.ID == 0 {
			return errors.New("找不到这条评审记录,无法更新")
		}
		if review.ReviewerID != reviewObject["ReviewerID"].(uint) {
			return errors.New("此记录不需要您的评审，请确认")
		}
		contribution := dcontribution.Response{}
		if err := tx.Model(dcontribution.Model()).Where("contribution_id = ?", review.ContributionID).Find(&contribution).Error; err != nil {
			return err
		}
		if contribution.ID > 0 {
			return errors.New("禁止更新历史版本，请刷新后重试")
		}

		if err := tx.Model(dcontributionreview.Model()).Where("id = ?", id).Updates(reviewObject).Error; err != nil {
			// 返回任何错误都会回滚事务
			return err
		}
		reviews := []*dcontributionreview.ListResponse{}
		err := tx.Model(dcontributionreview.Model()).Where("contribution_id = ?", review.ContributionID).Find(&reviews).Error
		if err != nil {
			return err
		}
		if len(reviews) > 0 {
			Pass := 0
			NotPass := 0
			NotReview := 0

			for _, review := range reviews {
				if review.Judge == 1 {
					Pass++
				}
				if review.Judge == 2 {
					NotPass++
				}
				if review.Judge == 0 {
					NotReview++
				}
			}

			if Pass == len(reviews) {
				if err := tx.Model(dcontribution.Model()).Where("id = ?", review.ContributionID).Update("status", 1).Error; err != nil {
					// 返回任何错误都会回滚事务
					return err
				}
			}
			if NotPass > 0 && NotReview == 0 {
				if err := tx.Model(dcontribution.Model()).Where("id = ?", review.ContributionID).Update("status", 2).Error; err != nil {
					// 返回任何错误都会回滚事务
					return err
				}
			}
		}

		// 返回 nil 提交事务
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}
