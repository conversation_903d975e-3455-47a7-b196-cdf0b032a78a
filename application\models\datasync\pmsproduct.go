package datasync

type PmsProduct struct {
	ID              int    `gorm:"primarykey; autoIncrement:false" json:"id" `
	ProductName     string `gorm:"not null; type:varchar(200)" json:"product_name"  update:"1"`
	ProductLineName string `gorm:"not null; type:varchar(200)" json:"product_line_name"  update:"1"`
	Version         string `gorm:"not null; type:varchar(200)" json:"version"  update:"1"`
	Disabled        bool   `grom:"not null; default:false" json:"disabled" update:"1"`
}
