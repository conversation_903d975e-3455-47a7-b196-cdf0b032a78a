package dproject

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/buildfarm"

	"github.com/pkg/errors"
)

const ModelName = "编译农场项目管理"

type Response struct {
	Id           uint   `json:"id"`
	UpdatedAt    string `json:"updated_at"`
	CreatedAt    string `json:"created_at"`
	Name         string `json:"name"`
	GitlabId     uint   `json:"gitlab_id"`
	Repo         string `json:"repo"`
	TaskType     uint   `json:"task_type"`      //编译类型，1：产品编译  2：组件编译
	Enable       uint   `json:"enable"`         // 是否启用 0：禁用， 1：启用
	PatchEnable  uint   `json:"patch_enable"`   // 启用 0：禁用， 1：启用
	PatchDirName string `json:"patch_dir_name"` // patch命令执行的最上级目录名，即在pro_xxxx目录中的目录名
	Owner        uint   `json:"owner"`
}

type ListResponse struct {
	Response
}

type Request struct {
	Id           uint   `json:"id"`
	Name         string `json:"name"`
	GitlabId     uint   `json:"gitlab_id"`
	Repo         string `json:"repo"`
	TaskType     uint   `json:"task_type"`      //编译类型，1：产品编译  2：组件编译
	Enable       uint   `json:"enable"`         // 是否启用 0：禁用， 1：启用
	PatchEnable  uint   `json:"patch_enable"`   // 启用 0：禁用， 1：启用
	PatchDirName string `json:"patch_dir_name"` // patch命令执行的最上级目录名，即在pro_xxxx目录中的目录名
	Owner        uint   `json:"owner"`
}

func (res *Response) ModelName() string {
	return ModelName
}

func Model() *buildfarm.Project {
	return &buildfarm.Project{}
}

func (this *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (this *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*Response

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (this *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (this *Response) Update(id uint, object map[string]interface{}) error {
	err := this.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindAllEnableProjects() ([]*ListResponse, error) {
	var list []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("enable = 1").Find(&list).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return nil, err
	}
	return list, nil
}

func FindAll() ([]*Response, error) {
	var list []*Response
	err := easygorm.GetEasyGormDb().Model(Model()).Find(&list).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return nil, err
	}
	return list, nil
}

func FindProduct() (*Response, error) {
	var item *Response
	err := easygorm.GetEasyGormDb().Model(Model()).Where("task_type = 1").Find(&item).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return nil, err
	}
	return item, nil
}

func FindByRepoPathID(repoPathID string) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("repo like ?", fmt.Sprintf("%%%s%%", repoPathID)).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return items, err
	}
	return items, nil
}

func FindRepoByLikeName(name string) (Response, error) {
	var item Response
	err := easygorm.GetEasyGormDb().Model(Model()).Where("name like ?", fmt.Sprintf("%%%s%%", name)).Find(&item).Error
	return item, errors.Wrap(err, "")
}

func FindAllEanbledCovProjects() ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("enabled_cov = true").Find(&items).Error
	return items, errors.Wrap(err, "")
}
