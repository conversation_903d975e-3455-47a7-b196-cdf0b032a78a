import os
import sys
import json
import logging
from docx import Document
from paddleocr import PaddleOCR
from PIL import Image

# 抑制 PaddleOCR 的调试信息
logging.getLogger("ppocr").setLevel(logging.ERROR)

# 初始化 OCR，支持中英文
ocr = PaddleOCR(use_angle_cls=True, lang='ch')

# 定义关键词和保存目录
keywords_groups = {
    "系列防火墙": ["在线更新", "本地导入"],
    "第五代NBR网关": ["在线更新", "本地导入"],
    "下一代智能网关": ["在线更新", "本地导入"],
    "Series Firewall":["Online","Local"],
    "IDP入侵检测防御系统": ["在线更新", "本地导入"],

}

# 将中文关键词映射到英文路径名
output_dir_mapping = {
    "系列防火墙": "FirewallSeries",
    "第五代NBR网关": "FifthGenNBRGateway",
    "下一代智能网关": "NextGenSmartGateway",
    "Series Firewall":"FirewallSeries",
    "IDP入侵检测防御系统":"IDPSystem"
}

# 子关键词映射到英文文件名
sub_keyword_mapping = {
    "在线更新": "OnlineUpdate",
    "本地导入": "LocalImport",
    "Online": "OnlineUpdate",
    "Local": "LocalImport"
}

# 从命令行参数获取 Word 文档路径和补丁名称
doc_path = sys.argv[1]
patch_name = sys.argv[2]
doc_dir = os.path.dirname(doc_path)
output_base_dir = doc_dir  # 将输出路径设为 Word 文档同目录

# 用于记录每个关键词组合是否已保存过
saved_combinations = {main_keyword: {sub_keyword: False for sub_keyword in sub_keywords} 
                      for main_keyword, sub_keywords in keywords_groups.items()}

# 读取 Word 文档
doc = Document(doc_path)

# 组织生成的图片信息
image_info = []

# 提取和处理图片
for i, rel in enumerate(doc.part.rels.values()):
    if "image" in rel.target_ref:
        # 保存图片到临时文件
        image_bytes = rel.target_part.blob
        image_path = f"{output_base_dir}/temp_image_{i}.png"
        with open(image_path, "wb") as img_file:
            img_file.write(image_bytes)

        # OCR 识别图片内容
        result = ocr.ocr(image_path, cls=True)
        
        # 确保 OCR 返回结果有效
        if result and result[0] is not None:
            all_text = " ".join([line[1][0] for line in result[0]])  # 拼接图片中的文字
            
            # 检查是否包含特定关键词和子关键词
            if patch_name in all_text:
                for main_keyword, sub_keywords in keywords_groups.items():
                    if main_keyword in all_text:
                        category = output_dir_mapping[main_keyword]
                        category_info = next((item for item in image_info if item["category"] == category), None)
                        
                        # 如果大类还未添加到 image_info 中，则初始化该大类
                        if not category_info:
                            category_info = {"category": category}
                            image_info.append(category_info)
                        
                        for sub_keyword in sub_keywords:
                            if sub_keyword in all_text and not saved_combinations[main_keyword][sub_keyword]:
                                # 如果该组合未保存，则保存图片并标记为已保存
                                group_dir = f"{output_base_dir}/{category}"
                                os.makedirs(group_dir, exist_ok=True)
                                final_image_path = f"{group_dir}/{category}_{sub_keyword_mapping[sub_keyword]}.png"
                                Image.open(image_path).save(final_image_path)
                                
                                # 将图片路径添加到对应子类中
                                category_info[sub_keyword_mapping[sub_keyword]] = final_image_path
                                saved_combinations[main_keyword][sub_keyword] = True

            # 删除临时图片文件
            os.remove(image_path)

# 输出 JSON 供 Golang 程序读取
print(json.dumps(image_info, ensure_ascii=False, indent=2))
