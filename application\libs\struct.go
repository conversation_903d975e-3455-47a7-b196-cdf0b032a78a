package libs

import (
	"encoding/json"
	"reflect"
	"strings"
)

/**
 * 结构体转成json 字符串
 * @method StruckToString
 * @param  {[type]}       data interface{} [description]
 */
func StructToString(data interface{}) string {
	b, err := json.Marshal(data)
	if err != nil {
		return err.Error()
	} else {
		return string(b)
	}
}

/**
 * 结构体转换成map对象
 * @method func
 * @param  {[type]} t *Tools        [description]
 * @return {[type]}   [description]
 */
func StructToMap(obj interface{}) map[string]interface{} {
	k := reflect.TypeOf(obj)
	v := reflect.ValueOf(obj)

	var data = make(map[string]interface{})
	for i := 0; i < k.NumField(); i++ {
		data[strings.ToLower(k.Field(i).Name)] = v.Field(i).Interface()
	}
	return data
}

/**
 * 结构体转换成map对象
 * @method func
 * @param  {[type]} t *Tools        [description]
 * @return {[type]}   [description]
 */
func StructToMapV2(obj interface{}) map[string]interface{} {
	k := reflect.TypeOf(obj)
	v := reflect.ValueOf(obj)

	var data = make(map[string]interface{})
	for i := 0; i < k.NumField(); i++ {
		name, ok2 := k.Field(i).Tag.Lookup("json")
		if ok2 {
			data[name] = v.Field(i).Interface()
		} else {
			data[strings.ToLower(k.Field(i).Name)] = v.Field(i).Interface()
		}
	}
	return data
}

func Slice2Map(slice interface{}, keyName string) interface{} {
	sliceType := reflect.TypeOf(slice)
	if sliceType.Kind() != reflect.Slice {
		return nil
	}
	valOf := reflect.ValueOf(slice)
	length := valOf.Len()
	if length == 0 {
		return nil
	}
	// 获得map对应类型
	mapT := reflect.MapOf(valOf.Index(0).FieldByName(keyName).Type(), valOf.Index(0).Type())
	mapV := reflect.MakeMap(mapT)
	for i := 0; i < length; i++ {
		structType := reflect.TypeOf(valOf.Index(i))
		if structType.Kind() != reflect.Struct {
			return nil
		}
		//mapV[struOf.FieldByName(keyName)] = struOf
		mapV.SetMapIndex(valOf.Index(i).FieldByName(keyName), valOf.Index(i))
	}
	return mapV
}

func AddStringToString(orignStr, newStr, sep string) string {
	if orignStr == "" {
		return newStr
	} else {
		array := strings.Split(orignStr, sep)
		array = append(array, newStr)
		return strings.Join(array, sep)
	}
}

type KvMap[K comparable, V any] map[K]V

func (kv KvMap[K, V]) Keys() []K {
	j := 0
	keys := make([]K, len(kv))
	for k := range kv {
		keys[j] = k
		j++
	}
	return keys
}
