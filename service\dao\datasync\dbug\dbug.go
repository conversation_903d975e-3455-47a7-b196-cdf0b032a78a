package dbug

import (
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"strings"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/datasync"
	"irisAdminApi/application/vars"
	"irisAdminApi/service/dao/datasync/dsyncrecord"
	"irisAdminApi/service/dao/release/dreleaseprojectconfig"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "BUG表"

type BugSyncResponse struct {
	/*
		"state": "SUCCESS",
		"data": [
			{
				"rownum": 1,
				"bugId": 259201,                            //BUGID
				"bugBelong": "测试人",                        //bug归属人
				"bugOwner": "测试人",                        //bug负责人
				"bugTestCharger": "测试2",                    //测试负责人
				"bugOs": "测试项目",                        //bug操作系统
				"bugProduct": "测试产品",                    //bug产品
				"workpacketName": null,                        //bug工作包
				"summary": "描述",                            //bug描述
				"mainbugid": null,                            //bug是否是从某个BUG镜像出来的BUG，如果有直接是父bugid
				"samebugid": null,                            //bug是否是从某个BUGsameas出来的BUG，如果有直接是父bugid
				"thedate": "2015-06-23 11:20:56",            //bug创建时间
				"lastupdatedate": "2015-06-23 15:28:53",    //bug更新时间
				"bugState": "DENIAL-ByDevelopment",            //bug状态
				"disabled": null                            //bug是否无效 0或者null 表示无效；1或者true表示有效
			}
		],
		"total":1,
		"message": null
	*/
	State   string         `json:"state"`
	Data    []*BugResponse `json:"data"`
	Total   int            `json:"total"`
	Message string         `json:"message"`
}

type BugResponse struct {
	/*
	   "bugId": 1000920,
	   "productLineName": "安全产品事业部",
	   "bugBelong": "傅境煌",
	   "groupName": "研发五部应软开发1组",
	   "pstlName": "赵尊圣",
	   "bugOwner": "傅境煌",
	   "bugTestCharger": "傅境煌",
	   "bugOs": "安全云管理平台2.1.0",
	   "bugProduct": "其他",
	   "workpacketName": "数据查询--设备运营后端开发",
	   "summary": "【安全云管理平台2.1.0】 搜索历史_删除 参数id为空的时候，会报系统异常",
	   "mainbugid": null,
	   "samebugid": null,
	   "thedate": "2023-07-12 19:05:30",
	   "lastupdatedate": "2023-07-12 19:05:30",
	   "bugState": "NEW",
	   "disabled": null,
	   "bugSource": null,
	   "projectName": "安全云管理平台2.1.0",
	   "refProjectName": null,
	   "ownerName": "傅境煌",
	   "ownerGroupName": "研发五部应软开发1组",
	   "submitterName": "傅境煌",
	   "submitterGroupName": "研发五部应软开发1组",
	   "testchargerName": "傅境煌",
	   "testchargerGroupName": "研发五部应软开发1组",
	   "submitDate": "2023-07-12 19:05:30",
	   "saveBugId": 2,
	   "testMethod": "功能测试",
	   "platform": "SCP_设备运营中心-GN-TP",
	   "repro": 1,
	   "priority": "normal",
	   "isRevise": null,
	   "location": null,
	   "cbdDate": null,
	   "cbtDate": null,
	   "severity": "Normal"
	*/
	/*
		// models.ModelBase
		BugID               int       `gorm:"primarykey; autoIncrement:false" json:"bug_id"`
		BugSummary          string    `gorm:"not null; type:varchar(300)" json:"bug_summary"`
		BugOwner            string    `gorm:"not null; type:varchar(60)" json:"bug_owner"`
		BugOwnerGroup       string    `gorm:"not null; type:varchar(60)" json:"bug_owner_group"`
		BugSubmitter        string    `gorm:"not null; type:varchar(60)" json:"bug_submitter"`
		BugSubmitterGroup   string    `gorm:"not null; type:varchar(60)" json:"bug_submitter_group"`
		BugBelong           string    `gorm:"not null; type:varchar(60)" json:"bug_belong"`
		BugTestCharger      string    `gorm:"not null; type:varchar(60)" json:"bug_test_charger"`
		BugTestChargerGroup string    `grom:"not null; type:varchar(60)" json:"bug_test_charger_group"`
		BugPstlName         string    `gorm:"not null; type:varchar(60)" json:"bug_pstl_name"`
		BugGroupName        string    `gorm:"not null; type:varchar(60)" json:"bug_group_name"`
		BugOS               string    `gorm:"not null; type:varchar(60)" json:"bug_os"`
		BugProduct          string    `gorm:"not null; type:varchar(60)" json:"bug_product"`
		BugWorkpacketName   string    `gorm:"not null; type:varchar(60)" json:"bug_workpacket_name"`
		MainBugID           int       `gorm:"not null" json:"main_bug_id"`
		SameBugID           int       `gorm:"not null" json:"same_bug_id"`
		BugCreatedAt        time.Time `gorm:"not null; type:varchar(60)" json:"bug_created_at"`
		BugUpdatedAt        time.Time `gorm:"not null; type:varchar(60)" json:"bug_updated_at"`
		BugState            string    `gorm:"not null; type:varchar(60)" json:"bug_state"`
		BugDisabled         bool      `gorm:"not null" json:"bug_disabled"`
		BugPriority         string    `gorm:"not null; type:varchar(60)" json:"bug_priority"`
		BugSeverity         string    `grom:"not null; type:varchar(60)" json:"bug_severity"`
		BugRepro            int       `gorm:"not null" json:"bug_repro"` //可重复性 1必现，2有时重现 3未尝试重现 4尝试但未重现
		MirroredProjects    string    `gorm:"type:varchar(600)" json:"mirrored_projects"`
		ResolvedProjects    string    `gorm:"type:varchar(600)" json:"resolved_projects"`
		NeedMirrorProjects  string    `gorm:"type:varchar(600)" json:"need_mirror_projects"`
		MirrorStatus        uint      `gorm:"type:varchar(60)" json:"mirror_status"`  //镜像状态： 0: 未同步  1:已同步  2:部分同步
		ResolveStatus       uint      `gorm:"type:varchar(60)" json:"resolve_status"` //所有同步BUG是否已经全部解决    0: 未解决   1: 已解决
	*/
	RowNum              int    `json:"rownum"`
	BugID               int    `json:"bugId"`
	BugBelong           string `json:"bugBelong"`
	BugGroupName        string `gorm:"not null; type:varchar(60)" json:"groupName"`
	BugOwner            string `json:"bugOwner"`
	BugOwnerGroup       string `gorm:"not null; type:varchar(60)" json:"ownerGroupName"`
	BugSubmitter        string `gorm:"not null; type:varchar(60)" json:"submitterName"`
	BugSubmitterGroup   string `gorm:"not null; type:varchar(60)" json:"submitterGroupName"`
	BugTestCharger      string `json:"bugTestCharger"`
	BugTestChargerGroup string `grom:"not null; type:varchar(60)" json:"testchargerGroupName"`
	BugPstlName         string `gorm:"not null; type:varchar(60)" json:"pstlName"`
	BugOS               string `json:"bugOs"`
	BugProduct          string `json:"bugProduct"`
	BugWorkpacketName   string `json:"workpacketName"`
	BugSummary          string `json:"summary"`
	MainBugID           int    `json:"mainbugid"`
	SameBugID           int    `json:"samebugid"`
	BugCreatedAt        string `json:"thedate"`
	BugUpdatedAt        string `json:"lastupdatedate"`
	BugState            string `json:"bugState"`
	BugDisabled         bool   `json:"disabled"`
	BugPriority         string `gorm:"not null; type:varchar(60)" json:"priority"`
	BugSeverity         string `grom:"not null; type:varchar(60)" json:"severity"`
	BugRepro            int    `gorm:"not null" json:"repro"` // 可重复性 1必现，2有时重现 3未尝试重现 4尝试但未重现
	BugCbdAt            string `json:"cbdDate"`
	BugCbtAt            string `json:"cbtDate"`
	BugDbdAt            string `json:"dbdDate"`
	BugDbtAt            string `json:"dbtDate"`
	BugGiveupAt         string `json:"giveupDate"`
	BugDelayAt          string `json:"delayDate"`
	BugResolvedAt       string `json:"resolvedDate"`
	Bugreportid         string `json:"bugreportid"`
	Resolvedok          string `json:"resolvedok"`
	Needsendbugreport   string `json:"needsendbugreport"`
	CheckOwner          string `json:"checkOwner"`
	BugSource           string `json:"bugSource"`
}

type Bug struct {
	datasync.Bug
	Children []*ListResponse `gorm:"-" json:"children"`
}

type BugSummary struct {
	BugOs         string `json:"bug_os"`
	BugOwner      string `json:"bug_owner"`
	BugOwnerGroup string `json:"bug_owner_group"`
	Total         int    `json:"total"`
}

type ListResponse struct {
	Bug
	FixCreatedAt string `json:"fix_created_at"`
	Round        int    `json:"round"`
	TestStartAt  string `json:"test_start_at"`
	OverTimeAt   string `json:"over_time_at"`
	ApprovalType string `gorm:"->" json:"approval_type"`
}

type Request struct {
	Id uint `json:"id"`
}

func (this *Bug) ModelName() string {
	return ModelName
}

func Model() *datasync.Bug {
	return &datasync.Bug{}
}

func (this *Bug) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *Bug) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *Bug) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Bug) CreateV2(object interface{}) error {
	return nil
}

func (this *Bug) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *Bug) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *Bug) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *Bug) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *Bug) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *Bug) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

/*
| NEW                  |
| DELAY                |
| CLOSED-ByTest        |
| ASSIGNED             |
| GIVEUP               |
| REQUEST              |
| DENIAL-ByDevelopment |
| CLOSED-ByDevelopment |
| CHECKED              |
| DENIAL-ByTest        |
| DENIAL-ByPSD         |
| REOPENED             |
| RESOLVED             |
BUG判定为需要镜像处理：CLOSED-ByTest， CLOSED-ByDevelopment， RESOLVED, CHECKED, DELAY?
BUG判定为无需镜像处理: DENIAL-ByTest, DENIAL-ByPSD, DENIAL-ByDevelopment, REQUEST, GIVEUP?, NEW, ASSIGNED
*/

func FindResolveMainBug() ([]*ListResponse, error) {
	var res []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where(`main_bug_id = 0 and bug_state in ("CLOSED-ByTest", "CLOSED-ByDevelopment", "RESOLVED", "CHECKED", "DELAY")`).Find(&res).Error // and bug_workpacket_name = "遗留或delay的bug(其他)"
	return res, err
}

func FindMirrorBug(mainBugIDs []int) ([]*ListResponse, error) {
	var res []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where(`main_bug_id in ?`, mainBugIDs).Find(&res).Error
	return res, err
}

func FindAllBug(page, pageSize int, sort, orderBy string, bugOS, mirrorStatus, resolveStatus, bugBelong, bugOwner, bugTestCharger, bugState, createdAt, updatedAt string) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Where(`main_bug_id = 0`)

	if len(bugOS) > 0 {
		db = db.Where("bug_os = ?", bugOS)
	}
	if len(bugBelong) > 0 {
		db = db.Where("bug_belong = ?", bugBelong)
	}
	if len(bugOwner) > 0 {
		db = db.Where("bug_owner = ?", bugOwner)
	}

	if len(bugTestCharger) > 0 {
		db = db.Where("bug_test_charger = ?", bugTestCharger)
	}
	if len(bugState) > 0 {
		db = db.Where("bug_state = ?", bugState)
	}
	if len(mirrorStatus) > 0 {
		db = db.Where("mirror_status = ?", mirrorStatus)
	}
	if len(resolveStatus) > 0 {
		db = db.Where("resolve_status = ?", resolveStatus)
	}
	if len(createdAt) > 0 {
		array := strings.Split(createdAt, ",")
		db = db.Where("bug_created_at between ? and ?", array[0], array[1])
	}
	if len(updatedAt) > 0 {
		array := strings.Split(updatedAt, ",")
		db = db.Where("bug_updated_at between ? and ?", array[0], array[1])
	}
	if len(orderBy) == 0 {
		orderBy = "bug_created_at"
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}
	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	AddMirrorBug(res)
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func AddMirrorBug(items []*ListResponse) {
	bugIds := []int{}
	for _, item := range items {
		bugIds = append(bugIds, item.BugID)
	}
	mirrorBugs := []*ListResponse{}
	mirrorBugMap := map[int][]*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("main_bug_id in ?", bugIds).Find(&mirrorBugs).Error
	if err != nil {
		fmt.Println(err)
	}
	for _, mirrorBug := range mirrorBugs {
		if _, ok := mirrorBugMap[mirrorBug.MainBugID]; !ok {
			mirrorBugMap[mirrorBug.MainBugID] = []*ListResponse{mirrorBug}
		} else {
			mirrorBugMap[mirrorBug.MainBugID] = append(mirrorBugMap[mirrorBug.MainBugID], mirrorBug)
		}
	}
	for _, item := range items {
		item.Children = mirrorBugMap[item.BugID]
	}
}

func FindDistinctByColumn(column string) ([]string, error) {
	result := []string{}
	err := easygorm.GetEasyGormDb().Model(Model()).Distinct(column).Order(column).Pluck(column, &result).Error
	return result, err
}

/*
BugSummary          string    `gorm:"not null; type:varchar(300)" json:"bug_summary"`

BugOwner            string    `gorm:"not null; type:varchar(60)" json:"bug_owner"`
BugOwnerGroup       string    `gorm:"not null; type:varchar(60)" json:"bug_owner_group"`
BugSubmitter        string    `gorm:"not null; type:varchar(60)" json:"bug_submitter"`
BugSubmitterGroup   string    `gorm:"not null; type:varchar(60)" json:"bug_submitter_group"`
BugBelong           string    `gorm:"not null; type:varchar(60)" json:"bug_belong"`
BugTestCharger      string    `gorm:"not null; type:varchar(60)" json:"bug_test_charger"`
BugTestChargerGroup string    `grom:"not null; type:varchar(60)" json:"bug_test_charger_group"`
BugPstlName         string    `gorm:"not null; type:varchar(60)" json:"bug_pstl_name"`
BugGroupName        string    `gorm:"not null; type:varchar(60)" json:"bug_group_name"`
BugOS               string    `gorm:"not null; type:varchar(60)" json:"bug_os"`
BugProduct          string    `gorm:"not null; type:varchar(60)" json:"bug_product"`
BugWorkpacketName   string    `gorm:"not null; type:varchar(60)" json:"bug_workpacket_name"`
MainBugID           int       `gorm:"not null" json:"main_bug_id"`
SameBugID           int       `gorm:"not null" json:"same_bug_id"`
BugCreatedAt        time.Time `gorm:"not null; type:varchar(60)" json:"bug_created_at"`
BugUpdatedAt        time.Time `gorm:"not null; type:varchar(60)" json:"bug_updated_at"`
BugState            string    `gorm:"not null; type:varchar(60)" json:"bug_state"`
BugDisabled         bool      `gorm:"not null" json:"bug_disabled"`
BugPriority         string    `gorm:"not null; type:varchar(60)" json:"bug_priority"`
BugSeverity         string    `grom:"not null; type:varchar(60)" json:"bug_severity"`
BugRepro            int       `gorm:"not null" json:"bug_repro"` //可重复性 1必现，2有时重现 3未尝试重现 4尝试但未重现
MirroredProjects    string    `gorm:"type:varchar(600)" json:"mirrored_projects"`
ResolvedProjects    string    `gorm:"type:varchar(600)" json:"resolved_projects"`
NeedMirrorProjects  string    `gorm:"type:varchar(600)" json:"need_mirror_projects"`
MirrorStatus        uint      `gorm:"type:varchar(60)" json:"mirror_status"`  //镜像状态： 0: 未同步  1:已同步  2:部分同步
ResolveStatus       uint      `gorm:"type:varchar(60)" json:"resolve_status"` //所有同步BUG是否已经全部解决    0: 未解决   1: 已解决
*/
func BatchCreateOrUpdate(records []map[string]interface{}) error {
	xt := reflect.TypeOf(&datasync.Bug{})
	columns := []string{}
	for i := 0; i < xt.Elem().NumField(); i++ {
		key, ok := xt.Elem().Field(i).Tag.Lookup("update")
		if ok {
			columns = append(columns, key)
		}
	}
	db := easygorm.GetEasyGormDb().Model(Model())

	err := db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "bug_id"}},
		DoUpdates: clause.AssignmentColumns(columns),
	}).Create(&records).Error
	if err != nil {
		return err
	}

	return nil
}

func GetDistinctColumns(bugOs, name string) ([]string, error) {
	items := []string{}
	db := easygorm.GetEasyGormDb().Model(Model())
	err := db.Distinct(name).Where("bug_os = ?", bugOs).Find(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

/*
BugID               int        `gorm:"primarykey; autoIncrement:false" json:"bug_id" `
BugSummary          string     `gorm:"not null; type:varchar(300)" json:"bug_summary" update:"1"`
BugOwner            string     `gorm:"not null; type:varchar(60)" json:"bug_owner" update:"1"`
BugOwnerGroup       string     `gorm:"not null; type:varchar(60)" json:"bug_owner_group" update:"1"`
BugSubmitter        string     `gorm:"not null; type:varchar(60)" json:"bug_submitter" update:"1"`
BugSubmitterGroup   string     `gorm:"not null; type:varchar(60)" json:"bug_submitter_group" update:"1"`
BugBelong           string     `gorm:"not null; type:varchar(60)" json:"bug_belong" update:"1"`
BugTestCharger      string     `gorm:"not null; type:varchar(60)" json:"bug_test_charger" update:"1"`
BugTestChargerGroup string     `gorm:"not null; type:varchar(60)" json:"bug_test_charger_group" update:"1"`
BugPstlName         string     `gorm:"not null; type:varchar(60)" json:"bug_pstl_name" update:"1"`
BugGroupName        string     `gorm:"not null; type:varchar(60)" json:"bug_group_name" update:"1"`
BugOS               string     `gorm:"not null; type:varchar(60)" json:"bug_os" update:"1"`
BugProduct          string     `gorm:"not null; type:varchar(60)" json:"bug_product" update:"1"`
BugWorkpacketName   string     `gorm:"not null; type:varchar(60)" json:"bug_workpacket_name" update:"1"`
MainBugID           int        `gorm:"not null" json:"main_bug_id" update:"1"`
SameBugID           int        `gorm:"not null" json:"same_bug_id" update:"1"`
BugCreatedAt        time.Time  `gorm:"not null;" json:"bug_created_at" update:"1"`
BugUpdatedAt        time.Time  `gorm:"not null;" json:"bug_updated_at" update:"1"`
BugState            string     `gorm:"not null; type:varchar(60)" json:"bug_state" update:"1"`
BugDisabled         bool       `gorm:"not null" json:"bug_disabled" update:"1"`
BugPriority         string     `gorm:"not null; type:varchar(60)" json:"bug_priority" update:"1"`
BugSeverity         string     `gorm:"not null; type:varchar(60)" json:"bug_severity" update:"1"`
BugRepro            int        `gorm:"not null" json:"bug_repro" update:"1"` //可重复性 1必现，2有时重现 3未尝试重现 4尝试但未重现
MirroredProjects    string     `gorm:"type:varchar(600)" json:"mirrored_projects"`
ResolvedProjects    string     `gorm:"type:varchar(600)" json:"resolved_projects"`
NeedMirrorProjects  string     `gorm:"type:varchar(600)" json:"need_mirror_projects"`
MirrorStatus        uint       `gorm:"type:varchar(60)" json:"mirror_status"`  //镜像状态： 0: 未同步  1:已同步  2:部分同步
ResolveStatus       uint       `gorm:"type:varchar(60)" json:"resolve_status"` //所有同步BUG是否已经全部解决    0: 未解决   1: 已解决
BugCbdAt            *time.Time `json:"bug_cbd_at" update:"1"`
BugCbtAt            *time.Time `json:"bug_cbt_at" update:"1"`
OverTime            *int       `json:"over_time"`
*/

func All(bugOwner, bugOs, bugOwnerGroup, name, sort, orderBy, status string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse
	selects := []string{
		`IF(a.approval_type ='延期',
			IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_severity = 'Blocking' AND b.bug_repro=1 and main_bug_id = 0,
				DATE_ADD(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), INTERVAL + 1*a.round DAY),
				IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_priority = 'critical' AND b.bug_repro=1 and main_bug_id = 0,
					getWorkDay(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), 3*a.round),
					IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_priority = 'major' AND b.bug_repro=1 and main_bug_id = 0,
						getWorkDay(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), 5*a.round),
						getWorkDay(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), 7*a.round)
					)
				)
			),
			IF(a.approval_type = '专项', a.created_at, null)
		) as fix_created_at`,
		"a.round as round",
		"c.test_start_at as test_start_at",
		`IF(a.approval_type ='延期' || a.approval_type is null,
			IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_severity = 'Blocking' AND b.bug_repro=1 and main_bug_id = 0,
				DATE_ADD(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), INTERVAL + IF(a.approval_type ='延期', 1*(a.round+1)+1, 1) DAY),
				IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_priority = 'critical' and b.bug_severity != 'Blocking' AND b.bug_repro=1 and main_bug_id = 0,
					getWorkDay(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), IF(a.approval_type ='延期', 3*(a.round+1)+1, 4)),
					IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_priority = 'major' and b.bug_severity != 'Blocking' AND b.bug_repro=1 and main_bug_id = 0,
						getWorkDay(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), IF(a.approval_type ='延期', 5*(a.round+1)+1, 6)),
						getWorkDay(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), IF(a.approval_type ='延期', 7*(a.round+1)+1, 8))
					)
				)
			),
			IF(a.approval_type = '专项', a.plan_finish_at, null)
		) as over_time_at`,
		"a.approval_type as approval_type",
	}

	xt := reflect.TypeOf(&datasync.Bug{})
	for i := 0; i < xt.Elem().NumField(); i++ {
		key, ok := xt.Elem().Field(i).Tag.Lookup("json")
		if ok {
			selects = append(selects, "b."+key)
		}
	}

	db := easygorm.GetEasyGormDb().
		Table(`
		(
			SELECT  *, (case when bug_cbd_at >= IFNULL(bug_dbd_at, 0) AND bug_cbd_at >= IFNULL(bug_giveup_at, 0) AND bug_cbd_at >= IFNULL(bug_delay_at, 0) then bug_cbd_at
							when bug_dbd_at >= IFNULL(bug_cbd_at, 0) AND bug_dbd_at >= IFNULL(bug_giveup_at, 0) AND bug_dbd_at >= IFNULL(bug_delay_at, 0) then bug_dbd_at
							when bug_giveup_at >= IFNULL(bug_dbd_at, 0) AND bug_giveup_at >= IFNULL(bug_cbd_at, 0) AND bug_giveup_at >= IFNULL(bug_delay_at, 0) then bug_giveup_at
							else bug_delay_at
						END) AS bug_closed_at
						FROM bugs
		) as b`).
		Joins("left join (select `round`, created_at, bug_id, `status`, approval_type, plan_finish_at from bug_approvals where id in (select max(id) as id from bug_approvals where status = 1 group by bug_id)) a on b.bug_id = a.bug_id").
		Joins("left join (select r.id as id, r.name as name, c.test_start_at as test_start_at from release_projects r left join release_project_configs c on r.id = c.release_project_id) c on c.name = b.bug_os").
		Select(selects)
	where := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		where = where.Where("bug_summary like ?", fmt.Sprintf("%%%s%%", name))
	}

	if len(bugOwner) > 0 {
		where = where.Where("bug_owner = ?", bugOwner)
	}
	if len(bugOs) > 0 {
		where = where.Where("bug_os = ?", bugOs)
	}

	if len(bugOwnerGroup) > 0 {
		if bugOwnerGroup == "其他专业组" {
			where = where.Where("bug_owner_group not in ?", vars.FilterBugOwnerGroups)
		} else {
			where = where.Where("bug_owner_group = ?", bugOwnerGroup)
		}
	}

	/*
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' AND b.bug_priority = 'blocking' AND b.bug_repro=1 AND ((a.approval_type ='delay' AND b.bug_created_at < getWorkDay(now(), -3*a.round)) OR (a.approval_type is null and b.bug_created_at < getWorkDay(now(), -3))), b.bug_id, NULL)) AS  overtime_blocking_by_test`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' AND b.bug_priority = 'critical' AND b.bug_repro=1  AND ((a.approval_type ='delay' AND b.bug_created_at < getWorkDay(now(), -3*a.round)) OR (a.approval_type is null and b.bug_created_at < getWorkDay(now(), -3))), b.bug_id, NULL)) AS  overtime_critical_by_test`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' AND b.bug_priority = 'major' AND b.bug_repro=1  AND ((a.approval_type ='delay' AND b.bug_created_at < getWorkDay(now(), -5*a.round)) OR (a.approval_type is null and b.bug_created_at < getWorkDay(now(), -5))), b.bug_id, NULL)) AS  overtime_major_by_test`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' AND b.bug_priority = 'normal' AND b.bug_repro=1  AND ((a.approval_type ='delay' AND b.bug_created_at <  DATE_ADD(NOW(), INTERVAL -7*a.round DAY)) OR (a.approval_type is null and b.bug_created_at <  DATE_ADD(NOW(), INTERVAL -7 DAY))), b.bug_id, NULL)) AS  overtime_normal_by_test`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group NOT LIKE '%测试%' AND b.bug_repro=1 AND ((a.approval_type='delay' AND b.bug_created_at < DATE_ADD(NOW(), INTERVAL -7*a.round DAY)) OR (a.approval_type is null and b.bug_created_at < DATE_ADD(NOW(), INTERVAL -7 DAY))), b.bug_id, NULL)) AS  overtime_by_dev`,

	*/
	if len(status) > 0 {
		switch status {
		case "need_bug_report_check":
			where = where.Where(`b.bug_state not like "DENIAL%"
			and
			(b.bug_workpacket_name = '遗留或delay的bug(其他)' or b.bug_state = 'DELAY')
			and
			(
				b.resolvedok is null
				or b.needsendbugreport is null
				or (b.needsendbugreport = '是' and b.bugreportid = 0)
				or (b.resolvedok = '用户接口修订' and (b.bugreportid = 0 or b.needsendbugreport != '是'))
				or (b.check_owner not in (select name from users where id in (select v0 from casbin_rule where ptype = 'g' and v1 in (select id from roles where name = '组长'))))
			)`)
		case "created_by_test_not_closed_in_first_round":
			if len(bugOs) > 0 {
				// 首轮BUG总关闭率
				config, err := dreleaseprojectconfig.FindConfigByName(bugOs)
				if err == nil {
					where = where.Where(
						fmt.Sprintf(
							`b.main_bug_id = 0 and b.bug_submitter_group LIKE '%%测试%%' and b.bug_created_at < '%s' and ((b.bug_state not LIKE 'CLOSED%%' and b.bug_state not LIKE 'DENIAL%%' and b.bug_state not IN ("DELAY", "GIVEUP")) or b.bug_closed_at > '%s')`,
							config.FirstBugFixStartAt,
							config.SecondTestStartAt,
						),
					)
				}
			}
		case "created_by_test_in_first_round":
			if len(bugOs) > 0 {
				// 首轮BUG总关闭率
				config, err := dreleaseprojectconfig.FindConfigByName(bugOs)
				if err == nil {
					where = where.Where(
						fmt.Sprintf(
							`b.main_bug_id = 0 and b.bug_submitter_group LIKE '%%测试%%' and b.bug_created_at < '%s'`,
							config.FirstBugFixStartAt,
						),
					)
				}
			}
		case "not_closed_in_first_round":
			if len(bugOs) > 0 {
				// 首轮BUG总关闭率
				config, err := dreleaseprojectconfig.FindConfigByName(bugOs)
				if err == nil {
					where = where.Where(
						fmt.Sprintf(
							`b.bug_created_at < '%s' and ((b.bug_state not LIKE 'CLOSED%%' and b.bug_state not LIKE 'DENIAL%%' and b.bug_state not IN ("DELAY", "GIVEUP")) or b.bug_closed_at > '%s')`,
							config.FirstBugFixStartAt,
							config.SecondTestStartAt,
						),
					)
				}
			}
		case "created_in_first_round":
			if len(bugOs) > 0 {
				// 首轮BUG总关闭率
				config, err := dreleaseprojectconfig.FindConfigByName(bugOs)
				if err == nil {
					where = where.Where(
						fmt.Sprintf(
							`b.bug_created_at < '%s'`,
							config.FirstBugFixStartAt,
						),
					)
				}
			}
		case "major_not_cbd_in_first_round":
			if len(bugOs) > 0 {
				// 首轮BUG总关闭率
				config, err := dreleaseprojectconfig.FindConfigByName(bugOs)
				if err == nil {
					where = where.Where(
						fmt.Sprintf(
							`(b.bug_severity = 'Blocking' or b.bug_priority = 'major' or b.bug_priority = 'critical') and b.bug_created_at < '%s' and  ((b.bug_state not LIKE 'CLOSED%%' and b.bug_state not LIKE 'DENIAL%%' and b.bug_state not IN ("DELAY", "GIVEUP")) or b.bug_closed_at > '%s')`,
							config.FirstBugFixStartAt,
							config.SecondTestStartAt,
						),
					)
				}
			}
		case "major_in_first_round":
			if len(bugOs) > 0 {
				// 首轮BUG总关闭率
				config, err := dreleaseprojectconfig.FindConfigByName(bugOs)
				if err == nil {
					where = where.Where(
						fmt.Sprintf(
							`(b.bug_severity = 'Blocking' or b.bug_priority = 'major' or b.bug_priority = 'critical') and b.bug_created_at < '%s'`,
							config.FirstBugFixStartAt,
						),
					)
				}
			}
		case "created_by_test_not_closed_in_second_round":
			if len(bugOs) > 0 {
				// 首轮BUG总关闭率
				config, err := dreleaseprojectconfig.FindConfigByName(bugOs)
				if err == nil {
					where = where.Where(
						fmt.Sprintf(
							`b.main_bug_id = 0 and b.bug_submitter_group LIKE '%%测试%%' and b.bug_created_at < '%s' and ((b.bug_state not LIKE 'CLOSED%%' and b.bug_state not LIKE 'DENIAL%%' and b.bug_state not IN ("DELAY", "GIVEUP")) or b.bug_closed_at > '%s')`,
							config.SecondBugFixStartAt,
							config.RegressTestStartAt,
						),
					)
				}
			}
		case "created_by_test_in_second_round":
			if len(bugOs) > 0 {
				// 首轮BUG总关闭率
				config, err := dreleaseprojectconfig.FindConfigByName(bugOs)
				if err == nil {
					where = where.Where(
						fmt.Sprintf(
							`b.main_bug_id = 0 and b.bug_submitter_group LIKE '%%测试%%' and b.bug_created_at < '%s'`,
							config.SecondBugFixStartAt,
						),
					)
				}
			}
		case "not_closed_in_second_round":
			if len(bugOs) > 0 {
				// 首轮BUG总关闭率
				config, err := dreleaseprojectconfig.FindConfigByName(bugOs)
				if err == nil {
					where = where.Where(
						fmt.Sprintf(
							`b.bug_created_at < '%s' and ((b.bug_state not LIKE 'CLOSED%%' and b.bug_state not LIKE 'DENIAL%%' and b.bug_state not IN ("DELAY", "GIVEUP")) or b.bug_closed_at > '%s')`,
							config.SecondBugFixStartAt,
							config.RegressTestStartAt,
						),
					)
				}
			}
		case "created_in_second_round":
			if len(bugOs) > 0 {
				// 首轮BUG总关闭率
				config, err := dreleaseprojectconfig.FindConfigByName(bugOs)
				if err == nil {
					where = where.Where(
						fmt.Sprintf(
							`b.bug_created_at < '%s'`,
							config.SecondBugFixStartAt,
						),
					)
				}
			}
		case "major_not_cbd_in_second_round":
			if len(bugOs) > 0 {
				// 首轮BUG总关闭率
				config, err := dreleaseprojectconfig.FindConfigByName(bugOs)
				if err == nil {
					where = where.Where(
						fmt.Sprintf(
							`b.bug_created_at < '%s' and (b.bug_severity = 'Blocking' or b.bug_priority = 'major' or b.bug_priority = 'critical') and ((b.bug_state not LIKE 'CLOSED%%' and b.bug_state not LIKE 'DENIAL%%' and  b.bug_state not IN ("DELAY", "GIVEUP")) or b.bug_closed_at > '%s')`,
							config.SecondBugFixStartAt,
							config.RegressTestStartAt,
						),
					)
				}
			}
		case "major_in_second_round":
			if len(bugOs) > 0 {
				// 首轮BUG总关闭率
				config, err := dreleaseprojectconfig.FindConfigByName(bugOs)
				if err == nil {
					where = where.Where(
						fmt.Sprintf(
							`(b.bug_severity = 'Blocking' or b.bug_priority = 'major' or b.bug_priority = 'critical') and b.bug_created_at < '%s'`,
							config.SecondBugFixStartAt,
						),
					)
				}
			}

		case "created_by_test_not_closed_before_experiment_release":
			if len(bugOs) > 0 {
				// 首轮BUG总关闭率
				config, err := dreleaseprojectconfig.FindConfigByName(bugOs)
				if err == nil {
					where = where.Where(
						fmt.Sprintf(
							`b.main_bug_id = 0 and b.bug_submitter_group LIKE '%%测试%%' and b.bug_created_at < '%s' and ((b.bug_state not LIKE 'CLOSED%%' and b.bug_state not LIKE 'DENIAL%%' and b.bug_state not IN ("DELAY", "GIVEUP")) or b.bug_closed_at > '%s')`,
							config.ExperimentReleaseAt+" 23:59:59",
							config.ExperimentReleaseAt+" 23:59:59",
						),
					)
				}
			}
		case "created_by_test_before_experiment_release":
			if len(bugOs) > 0 {
				// 首轮BUG总关闭率
				config, err := dreleaseprojectconfig.FindConfigByName(bugOs)
				if err == nil {
					where = where.Where(
						fmt.Sprintf(
							`b.main_bug_id = 0 and b.bug_submitter_group LIKE '%%测试%%' and b.bug_created_at < '%s'`,
							config.ExperimentReleaseAt+" 23:59:59",
						),
					)
				}
			}
		case "not_closed_before_experiment_release":
			if len(bugOs) > 0 {
				// 首轮BUG总关闭率
				config, err := dreleaseprojectconfig.FindConfigByName(bugOs)
				if err == nil {
					where = where.Where(
						fmt.Sprintf(
							`b.bug_created_at < '%s' and ((b.bug_state not LIKE 'CLOSED%%' and b.bug_state not LIKE 'DENIAL%%' and b.bug_state not IN ("DELAY", "GIVEUP")) or b.bug_closed_at > '%s')`,
							config.ExperimentReleaseAt+" 23:59:59",
							config.ExperimentReleaseAt+" 23:59:59",
						),
					)
				}
			}
		case "created_before_experiment_release":
			if len(bugOs) > 0 {
				// 首轮BUG总关闭率
				config, err := dreleaseprojectconfig.FindConfigByName(bugOs)
				if err == nil {
					where = where.Where(
						fmt.Sprintf(
							`b.bug_created_at < '%s'`,
							config.ExperimentReleaseAt+" 23:59:59",
						),
					)
				}
			}
		case "major_not_cbd_before_experiment_release":
			if len(bugOs) > 0 {
				// 首轮BUG总关闭率
				config, err := dreleaseprojectconfig.FindConfigByName(bugOs)
				if err == nil {
					where = where.Where(
						fmt.Sprintf(
							`b.bug_created_at < '%s' and (b.bug_severity = 'Blocking' or b.bug_priority = 'major' or b.bug_priority = 'critical') and ((b.bug_state not LIKE 'CLOSED%%' and b.bug_state not LIKE 'DENIAL%%' and  b.bug_state not IN ("DELAY", "GIVEUP")) or b.bug_closed_at > '%s')`,
							config.ExperimentReleaseAt+" 23:59:59",
							config.ExperimentReleaseAt+" 23:59:59",
						),
					)
				}
			}
		case "major_before_experiment_release":
			if len(bugOs) > 0 {
				// 首轮BUG总关闭率
				config, err := dreleaseprojectconfig.FindConfigByName(bugOs)
				if err == nil {
					where = where.Where(
						fmt.Sprintf(
							`(b.bug_severity = 'Blocking' or b.bug_priority = 'major' or b.bug_priority = 'critical') and b.bug_created_at < '%s'`,
							config.ExperimentReleaseAt+" 23:59:59",
						),
					)
				}
			}
		case "overtime":
			where = where.Where("over_time > 0")
		case "over_time_cbd":
			where = where.Where("status = 3 and over_time > 0")
		case "over_time_not_cbd":
			where = where.Where("status in (1, 2) and over_time > 0")
		case "not_cbd":
			where = where.Where(`bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP")`)
		case "no_workpacket":
			where = where.Where(`bug_workpacket_name =''`)
		case "checked_resolved":
			where = where.Where(`bug_state IN ('CHECKED', 'RESOLVED')`)
		case "overtime_blocking_by_test":
			where = where.Where(`b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' and b.main_bug_id = 0 AND b.bug_severity = 'Blocking' AND b.bug_repro=1 AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < DATE_ADD(NOW(), INTERVAL -1*(a.round+1) DAY)) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < DATE_ADD(NOW(), INTERVAL -1 DAY)))`)
		case "overtime_critical_by_test":
			where = where.Where(`b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' and b.main_bug_id = 0 AND b.bug_priority = 'critical' AND b.bug_repro=1  AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -3*(a.round+1))) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -3)))`)
		case "overtime_major_by_test":
			where = where.Where(`b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' and b.main_bug_id = 0 AND b.bug_priority = 'major' AND b.bug_repro=1  AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -5*(a.round+1))) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -5)))`)
		case "overtime_normal_by_test":
			where = where.Where(`b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' and b.main_bug_id = 0 AND b.bug_priority = 'normal' AND b.bug_repro=1  AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) <  getWorkDay(now(), -7*(a.round+1))) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -7)))`)
		case "overtime_by_dev":
			where = where.Where(`b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and (b.bug_submitter_group not LIKE '%测试%' or b.main_bug_id != 0) AND b.bug_repro=1  AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) <  getWorkDay(now(), -7*(a.round+1))) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -7)))`)
		case "blocking_not_cbd":
			where = where.Where(`bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_severity = 'Blocking'`)
		case "critical_not_cbd":
			where = where.Where(`bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_priority = 'critical'`)
		case "major_not_cbd":
			where = where.Where(`bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_priority = 'major'`)
		case "normal_not_cbd":
			where = where.Where(`bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_priority = 'normal'`)
		case "trivial_not_cbd":
			where = where.Where(`bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_priority = 'trivial'`)
		case "by_test":
			where = where.Where(`bug_submitter_group LIKE '%测试%'`)
		case "not_cbd_by_test":
			where = where.Where(`bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_submitter_group LIKE '%测试%'`)
		case "by_dev":
			where = where.Where(`bug_submitter_group not LIKE '%测试%'`)
		case "not_cbd_by_dev":
			where = where.Where(`bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_submitter_group not LIKE '%测试%'`)
		case "delay":
			where = where.Where(`b.bug_id in (?)`, easygorm.GetEasyGormDb().Table("bug_approvals").Select("bug_id"))
		default:
			where = where.Where(`bug_state = ?`, status)
		}
	}
	db = db.Where(where)
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

/*
BugID               int       `gorm:"primarykey; autoIncrement:false" json:"bug_id" `
BugSummary          string    `gorm:"not null; type:varchar(300)" json:"bug_summary" update:"1"`
BugOwner            string    `gorm:"not null; type:varchar(60)" json:"bug_owner" update:"1"`
BugOwnerGroup       string    `gorm:"not null; type:varchar(60)" json:"bug_owner_group" update:"1"`
BugSubmitter        string    `gorm:"not null; type:varchar(60)" json:"bug_submitter" update:"1"`
BugSubmitterGroup   string    `gorm:"not null; type:varchar(60)" json:"bug_submitter_group" update:"1"`
BugBelong           string    `gorm:"not null; type:varchar(60)" json:"bug_belong" update:"1"`
BugTestCharger      string    `gorm:"not null; type:varchar(60)" json:"bug_test_charger" update:"1"`
BugTestChargerGroup string    `gorm:"not null; type:varchar(60)" json:"bug_test_charger_group" update:"1"`
BugPstlName         string    `gorm:"not null; type:varchar(60)" json:"bug_pstl_name" update:"1"`
BugGroupName        string    `gorm:"not null; type:varchar(60)" json:"bug_group_name" update:"1"`
BugOS               string    `gorm:"not null; type:varchar(60)" json:"bug_os" update:"1"`
BugProduct          string    `gorm:"not null; type:varchar(60)" json:"bug_product" update:"1"`
BugWorkpacketName   string    `gorm:"not null; type:varchar(60)" json:"bug_workpacket_name" update:"1"`
MainBugID           int       `gorm:"not null" json:"main_bug_id" update:"1"`
SameBugID           int       `gorm:"not null" json:"same_bug_id" update:"1"`
BugCreatedAt        time.Time `gorm:"not null; type:varchar(60)" json:"bug_created_at" update:"1"`
BugUpdatedAt        time.Time `gorm:"not null; type:varchar(60)" json:"bug_updated_at" update:"1"`
BugState            string    `gorm:"not null; type:varchar(60)" json:"bug_state" update:"1"`
BugDisabled         bool      `gorm:"not null" json:"bug_disabled" update:"1"`
BugPriority         string    `gorm:"not null; type:varchar(60)" json:"bug_priority" update:"1"`
BugSeverity         string    `gorm:"not null; type:varchar(60)" json:"bug_severity" update:"1"`
BugRepro            int       `gorm:"not null" json:"bug_repro" update:"1"` //可重复性 1必现，2有时重现 3未尝试重现 4尝试但未重现
MirroredProjects    string    `gorm:"type:varchar(600)" json:"mirrored_projects"`
ResolvedProjects    string    `gorm:"type:varchar(600)" json:"resolved_projects"`
NeedMirrorProjects  string    `gorm:"type:varchar(600)" json:"need_mirror_projects"`
MirrorStatus        uint      `gorm:"type:varchar(60)" json:"mirror_status"`  //镜像状态： 0: 未同步  1:已同步  2:部分同步
ResolveStatus       uint      `gorm:"type:varchar(60)" json:"resolve_status"` //所有同步BUG是否已经全部解决    0: 未解决   1: 已解决
*/

func UpdateOrCreateBugTransaction(bugs []*BugResponse, _url string, data map[string]string, method, state, errorMsg string) error {
	batchCreateObjects := []map[string]interface{}{}
	for _, bug := range bugs {
		bugObject := map[string]interface{}{
			"BugID":               bug.BugID,
			"BugBelong":           bug.BugBelong,
			"BugOwner":            bug.BugOwner,
			"BugOwnerGroup":       bug.BugOwnerGroup,
			"BugSubmitter":        bug.BugSubmitter,
			"BugSubmitterGroup":   bug.BugSubmitterGroup,
			"BugTestCharger":      bug.BugTestCharger,
			"BugTestChargerGroup": bug.BugTestChargerGroup,
			"BugPstlName":         bug.BugPstlName,
			"BugGroupName":        bug.BugGroupName,
			"BugOS":               bug.BugOS,
			"BugProduct":          bug.BugProduct,
			"BugWorkpacketName":   bug.BugWorkpacketName,
			"BugSummary":          bug.BugSummary,
			"MainBugID":           bug.MainBugID,
			"SameBugID":           bug.SameBugID,
			"BugCreatedAt":        bug.BugCreatedAt,
			"BugUpdatedAt":        bug.BugUpdatedAt,
			"BugState":            bug.BugState,
			"BugDisabled":         bug.BugDisabled,
			"BugPriority":         bug.BugPriority,
			"BugSeverity":         bug.BugSeverity,
			"BugRepro":            bug.BugRepro,
			"Bugreportid":         bug.Bugreportid,
			"Resolvedok":          bug.Resolvedok,
			"Needsendbugreport":   bug.Needsendbugreport,
			"CheckOwner":          bug.CheckOwner,
			"BugSource":           bug.BugSource,
		}
		if len(bug.BugCbdAt) > 0 {
			bugObject["BugCbdAt"] = bug.BugCbdAt
		}

		if len(bug.BugCbtAt) > 0 {
			bugObject["BugCbtAt"] = bug.BugCbtAt
		}

		if len(bug.BugDbdAt) > 0 {
			bugObject["BugDbdAt"] = bug.BugDbdAt
		}

		if len(bug.BugDbtAt) > 0 {
			bugObject["BugDbtAt"] = bug.BugDbtAt
		}

		if len(bug.BugGiveupAt) > 0 {
			bugObject["BugGiveupAt"] = bug.BugGiveupAt
		}

		if len(bug.BugDelayAt) > 0 {
			bugObject["BugDelayAt"] = bug.BugDelayAt
		}

		if len(bug.BugResolvedAt) > 0 {
			bugObject["BugResolvedAt"] = bug.BugResolvedAt
		}

		batchCreateObjects = append(batchCreateObjects, bugObject)
	}

	columns := []string{}

	xt := reflect.TypeOf(&datasync.Bug{})
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")
		if ok && ok2 {
			columns = append(columns, name)
		} else if !ok2 {
			return errors.New("未定义json标签")
		}
	}

	body, err := json.Marshal(data)
	if err != nil {
		return err
	}
	db := easygorm.GetEasyGormDb()
	err = db.Transaction(func(tx *gorm.DB) error {
		if len(batchCreateObjects) > 0 {
			err := tx.Model(&datasync.Bug{}).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "bug_id"}},
				DoUpdates: clause.AssignmentColumns(columns),
			}).Create(&batchCreateObjects).Error
			if err != nil {
				return err
			}
		}

		if err := tx.Model(dsyncrecord.Model()).Create(map[string]interface{}{
			"url":             _url,
			"body":            body,
			"method":          method,
			"state":           state,
			"message":         errorMsg,
			"min_modify_date": data["minModifyDate"],
			"max_modify_date": data["maxModifyDate"],
			"created_at":      time.Now(),
		}).Error; err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func GetBugSummary() ([]*BugSummary, error) {
	items := []*BugSummary{}
	selects := []string{
		"b.bug_os bug_os",
		"b.bug_owner bug_owner",
		"b.bug_owner_group bug_owner_group",
		"count(*) total",
	}
	db := easygorm.GetEasyGormDb().
		Table("bugs b").
		Select(selects).
		Group("b.bug_os, b.bug_owner, b.bug_owner_group")

	where := easygorm.GetEasyGormDb().Where(`bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP")`)
	err := db.Where(where).Find(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func FindCbdBugInTime(project string, start, end time.Time) ([]*ListResponse, error) {
	items := []*ListResponse{}
	selects := []string{
		"bug_id",
		"bug_state",
		"bug_summary",
		"bug_os",
		"bug_priority",
		"bug_severity",
		"bug_repro",
		"bug_submitter",
		"bug_owner",
		"bug_workpacket_name",
		"bug_cbd_at",
	}
	db := easygorm.GetEasyGormDb().Model(Model())
	err := db.Where("bug_os = ? and bug_cbd_at >= ? and bug_cbd_at <= ?", project, start, end).Select(selects).Find(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func FindBugInBugIDs(bugIDs []string) ([]*Bug, error) {
	items := []*Bug{}
	db := easygorm.GetEasyGormDb().Model(Model()).Debug()
	err := db.Where("bug_id in ?", bugIDs).Find(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}
