package dcnvdsharexmlfile

import (
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/opensource"
)

const ModelName = "CNVD-XML共享文件表"

type Response struct {
	opensource.OpenSourceCNVDShareXMLFile
}

type ListResponse struct {
	Response
}

type Request struct {
	Id uint `json:"id"`
}

func (this *Response) ModelName() string {
	return ModelName
}

func Model() *opensource.OpenSourceCNVDShareXMLFile {
	return &opensource.OpenSourceCNVDShareXMLFile{}
}

func GetLatestCNVDShareXMLFile() (*Response, error) {
	var res []*Response
	err := easygorm.GetEasyGormDb().Model(Model()).
		Order("file_index desc").Offset(0).Limit(1).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get latest cnvd share xml file get err: %s", err.Error())
		return nil, err
	}
	if len(res) == 0 {
		return nil, nil
	}
	return res[0], nil
}

func CreateCNVDShareXMLFile(res *Response) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(&res.OpenSourceCNVDShareXMLFile).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create cnvd share xml file get err: %s", err.Error())
		return err
	}
	return nil
}
