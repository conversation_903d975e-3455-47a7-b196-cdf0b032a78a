package datasync

import (
	"context"
	"fmt"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/datasync/dbug"
	"irisAdminApi/service/dao/datasync/dpmsrequest"
	"irisAdminApi/service/dao/datasync/dpmsworkpacketinfo"
	"irisAdminApi/service/dao/datasync/dresource"
	"irisAdminApi/service/dao/datasync/dresourceclean"

	larkbitable "github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
	"github.com/pkg/errors"
)

func ResourceCleanWorker() error {
	// todo: 检查同步记录，获取同步时间，如果没有，从七天前开始，按小时同步
	err := dresourceclean.Clean()
	if err != nil {
		logging.ErrorLogger.Error(err)
		return err
	}
	return nil
}

func ResourceJobCleanWorker() error {
	resourceSummary, err := dresource.GetLastWeekResourceSummary("project_name", "asc")
	if err != nil {
		return errors.Wrap(err, "")
	}

	bugSummary, err := dbug.GetBugSummary()
	if err != nil {
		return errors.Wrap(err, "")
	}

	workPacketSummary, err := dpmsworkpacketinfo.GetPmsWorkpacketInfoSummary()
	if err != nil {
		return errors.Wrap(err, "")
	}

	requestSummary, err := dpmsrequest.GetPmsRequestSummary()
	if err != nil {
		return errors.Wrap(err, "")
	}

	projectExistMap := map[string]bool{}
	ntosProjects := []string{}
	otherProjects := []string{}
	userGroupMap := map[string]string{}
	users := []string{}

	resourceUserMap := map[string]float32{}
	bugUserMap := map[string]int{}

	wokrPacketUserMap := map[string][]string{}

	requestUserMap := map[string][]string{}

	for _, item := range resourceSummary {
		if _, ok := projectExistMap[item.ProjectName]; !ok {
			if strings.Contains(item.ProjectName, "NTOS") {
				ntosProjects = append(ntosProjects, item.ProjectName)
			} else {
				otherProjects = append(otherProjects, item.ProjectName)
			}
			projectExistMap[item.ProjectName] = true
		}

		if _, ok := userGroupMap[item.UserName]; !ok {
			users = append(users, item.UserName)
			userGroupMap[item.UserName] = item.GroupName
		}
		resourceUserMap[fmt.Sprintf("%s_%s", item.ProjectName, item.UserName)] = item.WorkTime
	}

	for _, item := range bugSummary {
		bugUserMap[fmt.Sprintf("%s_%s", item.BugOs, item.BugOwner)] = item.Total
	}

	for _, item := range workPacketSummary {
		if _, ok := wokrPacketUserMap[fmt.Sprintf("%s_%s", item.ProjectName, item.PacketManagerName)]; ok {
			wokrPacketUserMap[fmt.Sprintf("%s_%s", item.ProjectName, item.PacketManagerName)] = append(
				wokrPacketUserMap[fmt.Sprintf("%s_%s", item.ProjectName, item.PacketManagerName)],
				fmt.Sprintf("【工作包】%s:%.2fklocs(%d%%)", item.WorkPacketName, item.TotalCodes, item.FinishPercent),
			)
		} else {
			wokrPacketUserMap[fmt.Sprintf("%s_%s", item.ProjectName, item.PacketManagerName)] = []string{fmt.Sprintf("【工作包】%s:%.2fklocs(%d%%)", item.WorkPacketName, item.TotalCodes, item.FinishPercent)}
		}
	}

	for _, item := range requestSummary {
		if _, ok := requestUserMap[fmt.Sprintf("%s_%s", item.ProjectName, item.PgttmUserName)]; ok {
			requestUserMap[fmt.Sprintf("%s_%s", item.ProjectName, item.PgttmUserName)] = append(
				requestUserMap[fmt.Sprintf("%s_%s", item.ProjectName, item.PgttmUserName)],
				fmt.Sprintf("需求%s:%.2fklocs", item.RequestName, item.TotalCodes),
			)
		} else {
			requestUserMap[fmt.Sprintf("%s_%s", item.ProjectName, item.PgttmUserName)] = []string{fmt.Sprintf("需求%s:%.2fklocs", item.RequestName, item.TotalCodes)}
		}
	}

	row0 := []string{"", "", ""}
	row0 = append(row0, ntosProjects...)
	row0 = append(row0, otherProjects...)

	rows := [][]interface{}{}
	for _, user := range users {
		if group, ok := userGroupMap[user]; ok {
			// 资源
			row := []interface{}{user, group, "资源"}
			for _, project := range row0[3:] {
				if _, ok := resourceUserMap[fmt.Sprintf("%s_%s", project, user)]; ok {
					row = append(row, resourceUserMap[fmt.Sprintf("%s_%s", project, user)])
				} else {
					row = append(row, "")
				}
			}
			rows = append(rows, row)
			// Bug
			row = []interface{}{user, group, "Bug"}
			for _, project := range row0[3:] {
				if _, ok := bugUserMap[fmt.Sprintf("%s_%s", project, user)]; ok {
					row = append(row, bugUserMap[fmt.Sprintf("%s_%s", project, user)])
				} else {
					row = append(row, "")
				}
			}
			rows = append(rows, row)
			// 任务
			row = []interface{}{user, group, "任务"}
			for _, project := range row0[3:] {
				_cell := []string{}
				if _, ok := requestUserMap[fmt.Sprintf("%s_%s", project, user)]; ok {
					_cell = append(_cell, requestUserMap[fmt.Sprintf("%s_%s", project, user)]...)
				} else if _, ok := wokrPacketUserMap[fmt.Sprintf("%s_%s", project, user)]; ok {
					_cell = append(_cell, wokrPacketUserMap[fmt.Sprintf("%s_%s", project, user)]...)
				}
				row = append(row, strings.Join(_cell, "\n"))
			}
			rows = append(rows, row)
		}
	}
	// for _, row := range rows {
	// 	fmt.Println(row)
	// }
	// data := []map[string]interface{}{}
	// BatchCreateRecord("tbln0gATJyyMqyG4", data)
	return nil
}

// 创建记录
func BatchCreateRecord(tableID string, data []map[string]interface{}) error {
	limit := 500
	for loop := 0; true; loop++ {
		start := loop * limit
		if start > len(data) {
			break
		}
		end := (loop + 1) * limit
		if len(data) <= (loop+1)*limit {
			end = len(data)
		}

		records := []*larkbitable.AppTableRecord{}
		for _, record := range data[start:end] {
			records = append(records, larkbitable.NewAppTableRecordBuilder().
				Fields(map[string]interface{}{
					"warning详细信息": record["Detail"],
					"warning位置":   record["Position"],
					"warning文件":   record["Filename"],
					"warning文件目录": record["SourceDir"],
					"项目":          record["Project"],
					"工程分支":        record["Branch"],
					"产品":          record["Product"],
					"编译类型":        record["BuildType"],
					"代码路径":        record["FullPath"],
					"组件":          record["Component"],
					"组件包":         record["ComponentPacket"],
					"是否处理":        record["Comment"],
					"负责人":         record["Owner"],
					"状态":          record["Status"],
				}).
				Build())
		}

		body := larkbitable.NewBatchCreateAppTableRecordReqBodyBuilder().Records(records).Build()

		// 创建请求对象
		req := larkbitable.NewBatchCreateAppTableRecordReqBuilder().
			AppToken(libs.Config.FeiShuDoc.BiAppToken).
			TableId(tableID).
			Body(body).
			Build()

		// 发起请求
		resp, err := FeiShuClient.Bitable.AppTableRecord.BatchCreate(context.Background(), req)
		// 处理错误
		if err != nil {
			return errors.Wrap(err, "")
		}

		// 服务端错误处理
		if !resp.Success() {
			return fmt.Errorf("飞书服务端返回错误 %v %v %v", resp.Code, resp.Msg, resp.RequestId())
		}
		// 更新ID
		time.Sleep(1 * time.Second)
	}
	return nil
}
