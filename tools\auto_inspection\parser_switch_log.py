import re
import sys
import os
import json

# 常量定义
'''
code: 1
  收集正常，解析检查结果判定为正常
code: -1
  收集正常，解析检查结果判定为异常
code: -2:
  配置异常，无法解析
'''

result = []
summary = {}
# 假设日志文本存储在一个名为 log_text 的字符串中
if len(sys.argv) > 1:
    filename = os.path.basename(sys.argv[1])
    a = filename.split("_")
    if len(a) == 4:
        _type = a[0]
        hostname = a[1]
        ip = a[2]
        created_at = a[3]
        summary = {
            "type": _type,
            "hostname": hostname,
            "ip": ip,
            "created_at": created_at,
            "result": result
        }
if not summary:
    print("error: 文件名不符合type_hostname_ip_timestamp.log要求。")
with open(sys.argv[1], 'r', encoding='utf8') as f:
    log_text = f.read()

# 使用正则表达式匹配 "start" 和 "end" 之间的段落
pattern = re.compile(r'=+ start (\d+) (\w+) =+\n(.*?)\n=+ end (\d+) (\w+) =+', re.DOTALL)

matches = pattern.finditer(log_text)

for match in matches:

    start_num, start_type, content, end_num, end_type = match.groups()

    content = match.group()  # 获取段落内容
    lines = content.split("\n")
    # print(lines)
    s = lines[0].split()
    # print(len(s))
    if len(s) == 5:
        if s[1] == "start":
            rule_id = s[2]
            name = s[3]
    else:
        if s[1] == "start":
            rule_id = s[2]
            result.append({
                "rule_id": rule_id,
                "name": "",
                "code": "-2",
                "msg": "配置异常，无法采集",
                "data": [],
            })
        continue
    if rule_id == "01":
        _lines = []
        _err_lines = []
        pattern = re.compile(r'(\d+)\s+([\w\-]+)\s+([\w\-]+)')
        num_pattern = re.compile(r'\d+')
        for line in lines:
            _matches = pattern.findall(line)
            if list(_matches):
                _lines.append(line)
                if list(_matches)[0][-1] != 'ok' and not num_pattern.findall(list(_matches)[0][-1]):
                    _err_lines.append(line)
        if len(_err_lines) > 0:
            result.append({
                "rule_id": rule_id,
                "name": name,
                "code": "-1",
                "msg": "硬件状态异常",
                "data": lines[1: -1],
            })
        else:
            result.append({
                "rule_id": rule_id,
                "name": name,
                "code": "1",
                "msg": "硬件状态正常",
                "data": lines[1: -1],
            })
        continue
    if rule_id == "02":
        _lines = []
        for line in lines:
            if line.startswith("*"):
                if "ARP" in line:
                    continue
                _lines.append(line)
        if not _lines:
            result.append({
                "rule_id": rule_id,
                "name": name,
                "code": "1",
                "msg": "无异常日志",
                "data": _lines,
            })
        else:
            result.append({
                "rule_id": rule_id,
                "name": name,
                "code": "-1",
                "msg": "存在异常日志",
                "data": _lines,
            })


print(json.dumps(summary))

    # 检查是否存在错误
    # has_error = re.search(r'E! exec_command: .*\.sh error', content) or re.search(r'Server returned error NXDOMAIN', content)

    # if has_error:
    #     print(f"Error found in section {start_num} {start_type}:")
    #     print(content)
    # else:
    #     print(f"No error found in section {start_num} {start_type}.")