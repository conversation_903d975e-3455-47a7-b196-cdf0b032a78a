package release

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/buildfarm/dcronmakejob"
	"irisAdminApi/service/dao/datasync/dbug"
	"irisAdminApi/service/dao/datasync/dpmsprojectmember"
	"irisAdminApi/service/dao/datasync/dsyncrecord"
	"irisAdminApi/service/dao/release/dreleaseprojectconfig"
)

type SendTestLinkRule struct {
	First   Rule `json:"first"`
	Second  Rule `json:"second"`
	Regress Rule `json:"regress"`
}

type Rule struct {
	Shell bool     `json:"shell"`
	Types []string `json:"types"`
}

func SendTestLinkWorker() {
	now := time.Now()
	nowStr := now.Format("2006-01-02")

	enabledNoticeProjects, err := dreleaseprojectconfig.AllEnabledConfig()
	if err != nil {
		logging.ErrorLogger.Error("get project config err ", err)
		return
	}
	for _, config := range enabledNoticeProjects {
		testLinkRoles := strings.Split(config.TestLinkRoles, ",")
		testLinkReceivers := strings.Split(config.TestLinkReceivers, ",")

		mailTo := []string{}
		mailTo = append(mailTo, testLinkReceivers...)

		if len(testLinkRoles) > 0 && len(testLinkReceivers) == 0 {
			continue
		}

		members, err := dpmsprojectmember.FindMembersByProjectAndRole(config.ReleaseProject.Name, testLinkRoles)
		if err != nil {
			logging.ErrorLogger.Error("find members err ", err)
			continue
		}

		for _, member := range members {
			if len(member.CasUserID) > 0 {
				mailTo = append(mailTo, member.CasUserID+"@ruijie.com.cn")
			} else {
				logging.ErrorLogger.Error("member: %s  without casUserId ", member.Username)
			}
		}

		rule := SendTestLinkRule{}
		if len(config.SendTestLinkRule) > 0 {
			err := json.Unmarshal([]byte(config.SendTestLinkRule), &rule)
			if err != nil {
				logging.ErrorLogger.Error("unmarshal send test link rule err", err)
				continue
			}

			if nowStr >= config.TestStartAt && nowStr < config.SecondTestStartAt {
				withShell := rule.First.Shell
				types := rule.First.Types
				if len(types) > 0 {
					err := SendTestLink("首轮测试", config.ReleaseProject.Name, config.BuildProject.Name, *config.BuildProjectBranch, withShell, types, mailTo)
					if err != nil {
						logging.ErrorLogger.Error("send test link err", err, config.ReleaseProject.Name, config.BuildProject.Name, config.BuildProjectBranch, withShell, types, config.Receivers)
						continue
					}
				}

			} else if nowStr >= config.SecondTestStartAt && nowStr < config.RegressTestStartAt {
				withShell := rule.Second.Shell
				types := rule.Second.Types
				if len(types) > 0 {
					err := SendTestLink("次轮测试", config.ReleaseProject.Name, config.BuildProject.Name, *config.BuildProjectBranch, withShell, types, mailTo)
					if err != nil {
						logging.ErrorLogger.Error("send test link err", err, config.ReleaseProject.Name, config.BuildProject.Name, config.BuildProjectBranch, withShell, types, config.Receivers)
						continue
					}
				}

			} else if nowStr >= config.RegressTestStartAt {
				withShell := rule.Regress.Shell
				types := rule.Regress.Types
				if len(types) > 0 {
					err := SendTestLink("回归测试", config.ReleaseProject.Name, config.BuildProject.Name, *config.BuildProjectBranch, withShell, types, mailTo)
					if err != nil {
						logging.ErrorLogger.Error("send test link err", err, config.ReleaseProject.Name, config.BuildProject.Name, config.BuildProjectBranch, withShell, types, config.Receivers)
						continue
					}
				}

			}
			// else if nowStr > config.ExperimentReleaseAt {
			// 	withShell := rule.Regress.Shell
			// 	types := rule.Regress.Types
			// 	err := SendTestLink("试点发布", config.ReleaseProject.Name, config.BuildProject.Name, config.BuildProjectBranch, withShell, types, config.Receivers)
			// 	if err != nil {
			// 		logging.ErrorLogger.Error("send test link err", err, config.ReleaseProject.Name, config.BuildProject.Name, config.BuildProjectBranch, withShell, types, config.Receivers)
			// 		continue
			// 	}
			// }
		}
	}
}

const TestLinkStyle = `
<style>
	table {
		border-collapse: collapse;
	}
	th {
		background-color: #007fff;
		color: white;
	}
	table, th, td {
		border: 1px solid black;
		padding: 5px;
		text-align: left;
	}
</style>
`

func SendTestLink(name, releaseProject, buildProject, buildBranch string, withShell bool, types []string, mailTo []string) error {
	now := time.Now()
	result := [][]interface{}{{"项目", "产品", "下载地址", "文件名", "编译类型", "文件MD5", "文件大小", "冒烟状态"}}
	start := now.Add(-24 * time.Hour)
	end := now

	cronMakeJobs, err := dcronmakejob.FindAllJobByProjectAndBranch(buildProject, buildBranch, types, start)
	if err != nil {
		logging.ErrorLogger.Errorf("find all job by cron tab id error", err.Error(), buildProject, buildBranch, types)
		return err
	}

	if len(cronMakeJobs) == 0 {
		logging.ErrorLogger.Errorf("未找到相关编译作业")
		return fmt.Errorf("未找到相关编译作业")
	}
	// 检查是否有未完成作业
	// count := 0
	// for _, job := range cronMakeJobs {
	// 	if job.Status == 1 {
	// 		count++
	// 	}
	// }
	// if count != len(cronMakeJobs) {
	// 	fmt.Println(count, len(cronMakeJobs))
	// 	return
	// }

	// 所有作业完成, 查询时间周期内所有CBD状态BUG
	check := map[string]bool{}
	for _, job := range cronMakeJobs {
		if job.Cpu.Cpu == "" {
			logging.ErrorLogger.Errorf("%s Cpu映射未添加，请联系管理员添加", job.Product)
			return err
		}

		var targetPath, urlPrefix string
		// if crontab.WithRelease {
		targetPath = filepath.Join(libs.Config.Buildfarm.Archivepath, job.JobId, job.Cpu.Cpu, "releaseID-bin")
		urlPrefix = fmt.Sprintf("http://%s:%d/output/%s/%s/releaseID-bin", libs.Config.Nginx.HOST, libs.Config.Nginx.Port, job.JobId, job.Cpu.Cpu)
		// } else {
		// 	targetPath = filepath.Join(libs.Config.Buildfarm.Archivepath, job.JobId, job.Cpu.Cpu)
		// 	urlPrefix = fmt.Sprintf("http://%s:%d/output/%s/%s", libs.Config.Nginx.HOST, libs.Config.Nginx.Port, job.JobId, job.Cpu.Cpu)
		// }

		if _, err := os.Stat(targetPath); os.IsNotExist(err) {
			logging.ErrorLogger.Errorf("%s 文件不存在", job.JobId)
			return err
		}

		entries, err := os.ReadDir(targetPath)
		if err != nil {
			logging.ErrorLogger.Errorf("%s 获取文件错误: %s", job.JobId, err.Error())
			return err
		}

		for _, entry := range entries {
			info, err := entry.Info()
			if err != nil {
				logging.ErrorLogger.Errorf("%s 获取文件错误: %s", job.JobId, err.Error())
				return err
			}
			fn := info.Name()
			if strings.HasSuffix(fn, "_install.bin") && strings.Contains(fn, "NTOS") {
				if (withShell && strings.Contains(fn, "shell")) || (!withShell && !strings.Contains(fn, "shell")) {
					md5, err := libs.GetFileMd5(filepath.Join(targetPath, fn))
					if err != nil {
						logging.ErrorLogger.Errorf("%s 获取文件错误: %s", job.JobId, err.Error())
						return err
					}
					if _, ok := check[fmt.Sprintf("%s_%s", job.Product, job.BuildType)]; !ok {
						result = append(result, []interface{}{
							job.Branch,
							job.Product,
							fmt.Sprintf("%s/%s", urlPrefix, fn),
							fn,
							job.BuildType,
							md5,
							info.Size(),
							job.SmokeStatus,
						})
						check[fmt.Sprintf("%s_%s", job.Product, job.BuildType)] = true
					}
				}
			}
		}
	}

	if libs.Config.Mail.Enable {
		from := "编译农场"
		subject := fmt.Sprintf("关于%s%s版本更新【%s】", releaseProject, name, now.Format("2006-01-02"))

		mailMsg := TestLinkStyle + `<h2>转测版本</h2>`

		mailMsg = mailMsg + `<table><tr>`
		// 	result := [][]interface{}{{"分支", "产品", "下载地址", "文件名", "编译类型", "文件MD5", "文件大小", "冒烟状态"}}
		// for _, item := range result[0] {
		// 	mailMsg = mailMsg + fmt.Sprintf(`<th style="width: 200px">%s</th>`, item)
		// }
		mailMsg += `
<th style="width: 200px">分支</th>
<th style="width: 200px">产品</th>
<th style="width: 800px">下载地址</th>
<th style="width: 200px">编译类型</th>
<th style="width: 400px">文件MD5</th>
<th style="width: 200px">文件大小</th>
<th style="width: 800px">冒烟状态</th>
		`
		mailMsg += `</tr>`

		for _, data := range result[1:] {
			// mailMsg = mailMsg + `<tr>`
			// for _, item := range data {
			// 	mailMsg = mailMsg + fmt.Sprintf(`<td>%v</td>`, item)
			// }
			mailMsg = fmt.Sprintf(`%s
				<tr>
					<td>%v</td>
					<td>%v</td>
					<td><a href="%v">%v</a></td>
					<td>%v</td>
					<td>%v</td>
					<td>%v</td>
					<td>%v</td>
				</tr>`,
				mailMsg,
				data[0],
				data[1],
				data[2],
				data[3],
				data[4],
				data[5],
				data[6],
				data[7],
			)
			// mailMsg = mailMsg + `</tr>`
		}
		mailMsg = mailMsg + `</table>`

		// 附加24小时内CBD的BUG信息
		// 增加提示，显示BUG数据同步时间
		var updatedAt string
		_url := "https://dataware.ruijie.com.cn/api/public/data-api/security_product_sw_bug/list.data"
		records, err := dsyncrecord.FindLastSuccessSyncRecord(_url)
		if err != nil {
			logging.ErrorLogger.Errorf("get last sync records", err.Error())
			return err
		}
		if len(records) > 0 {
			updatedAt = records[0].CreatedAt.Format("2006-01-02 15:04:05")
		}

		bugs, err := dbug.FindCbdBugInTime(releaseProject, start, end)
		if err != nil {
			mailMsg = mailMsg + "<h2>Bug查询错误</h2>"
		} else {
			mailMsg = mailMsg + fmt.Sprintf(`<h2>Bug CBD 【%v个】。统计基于%s同步的BUG数据</h2>`, len(bugs), updatedAt)
			if len(bugs) > 0 {
				mailMsg += `<table><tr>`
				// for _, item := range []string{"BUGID", "状态", "BUG简介", "操作系统", "严重性", "优先级", "重复性", "提交者", "BUG负责人", "工作包名称", "CBD时间"} {
				// 	mailMsg = mailMsg + fmt.Sprintf(`<th style="width: 200px">%s</th>`, item)
				// }
				mailMsg += `
<th style="width: 120px">BUGID</th>
<th style="width: 150px">状态</th>
<th style="width: 800px">BUG简介</th>
<th style="width: 120px">操作系统</th>
<th style="width: 100px">严重性</th>
<th style="width: 100px">优先级</th>
<th style="width: 100px">重复性</th>
<th style="width: 200px">提交者</th>
<th style="width: 200px">BUG负责人</th>
<th style="width: 400px">工作包名称</th>
<th style="width: 400px">CBD时间</th>
				`
				mailMsg += `</tr>`
			}
			for _, bug := range bugs {
				mailMsg = fmt.Sprintf(`%s
					<tr>
					<td><a href="http://bugs.ruijie.com.cn/bug_switch/bug/main?bugId=%v">%v</a></td>
					<td>%v</td>
					<td>%v</td>
					<td>%v</td>
					<td>%v</td>
					<td>%v</td>
					<td>%v</td>
					<td>%v</td>
					<td>%v</td>
					<td>%v</td>
					<td>%v</td>
					</tr>`,
					mailMsg,
					bug.BugID,
					bug.BugID,
					bug.BugState,
					bug.BugSummary,
					bug.BugOS,
					bug.BugPriority,
					bug.BugSeverity,
					bug.BugRepro,
					bug.BugSubmitter,
					bug.BugOwner,
					bug.BugWorkpacketName,
					bug.BugCbdAt.Format("2006-01-02 15:04:05"),
				)
			}
			mailMsg = mailMsg + `</table>`
		}

		if len(mailTo) > 0 {
			err = libs.SendMailRedis(from, mailTo, subject, mailMsg, []string{})
			return err
		}
	}
	return nil
}
