package opensource

import (
	"gorm.io/gorm"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/opensource"
	"irisAdminApi/service/dao/opensource/dvulnerability"
	"irisAdminApi/service/dao/opensource/dvulnerabilityhistory"
	"irisAdminApi/service/dao/opensource/dvulnerabilitypermission"
)

func CreateVulnerabilities(vulnerabilitiesRes []*dvulnerability.OpenSourceVulnerability, responsibleUserIDs, responsibleLeaderIDs []uint) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		vulnerabilities := make([]*opensource.OpenSourceVulnerability, len(vulnerabilitiesRes))
		for i, vulnerabilityRes := range vulnerabilitiesRes {
			vulnerabilities[i] = &vulnerabilityRes.OpenSourceVulnerability
		}
		var total = len(vulnerabilities)
		var firstIndex = 0
		for firstIndex < total {
			lastIndex := firstIndex + batchCreateCount
			if lastIndex > total {
				lastIndex = total
			}
			newVulnerabilities := vulnerabilities[firstIndex:lastIndex]
			if err := tx.Model(dvulnerability.Model()).Create(&newVulnerabilities).Error; err != nil {
				logging.ErrorLogger.Errorf("transaction create vulnerabilities, create vulnerabilities get err %s", err.Error())
				return err
			}
			firstIndex = lastIndex
		}

		responsiblePermissions := []*opensource.OpenSourceVulnerabilityPermission{}
		for _, vulnerability := range vulnerabilities {
			var his1, his2 []uint
			for _, responsibleUserID := range responsibleUserIDs {
				permissionRes := &opensource.OpenSourceVulnerabilityPermission{
					UserID:          responsibleUserID,
					ComponentID:     vulnerability.ComponentID,
					VulnerabilityID: vulnerability.ID,
					Type:            opensource.ResponsibleUserPermissionType,
				}
				if !libs.InArrayUint(his1, responsibleUserID) {
					responsiblePermissions = append(responsiblePermissions, permissionRes)
					his1 = append(his1, responsibleUserID)
				}

			}
			for _, responsibleLeaderID := range responsibleLeaderIDs {
				permissionRes := &opensource.OpenSourceVulnerabilityPermission{
					UserID:          responsibleLeaderID,
					ComponentID:     vulnerability.ComponentID,
					VulnerabilityID: vulnerability.ID,
					Type:            opensource.ResponsibleLeaderPermissionType,
				}
				if !libs.InArrayUint(his2, responsibleLeaderID) {
					responsiblePermissions = append(responsiblePermissions, permissionRes)
					his2 = append(his2, responsibleLeaderID)
				}

			}
		}

		total = len(responsiblePermissions)
		firstIndex = 0
		for firstIndex < total {
			lastIndex := firstIndex + batchCreateCount
			if lastIndex > total {
				lastIndex = total
			}
			newVulnerabilityPermissions := responsiblePermissions[firstIndex:lastIndex]
			if err := tx.Model(dvulnerabilitypermission.Model()).Create(&newVulnerabilityPermissions).Error; err != nil {
				logging.ErrorLogger.Errorf("transaction create vulnerabilities, create vulnerability permissions get err %s", err.Error())
				return err
			}
			firstIndex = lastIndex
		}
		return nil
	})
	return err
}

func DelegateVulnerability(vulnerabilityPermissionRes *dvulnerabilitypermission.Response,
	vulnerabilityHistoryRes *dvulnerabilityhistory.OpenSourceVulnerabilityHistory) error {
	db := easygorm.GetEasyGormDb()
	return db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(dvulnerabilitypermission.Model()).
			Create(&vulnerabilityPermissionRes.OpenSourceVulnerabilityPermission).Error; err != nil {
			return err
		}

		if err := tx.Model(dvulnerabilityhistory.Model()).
			Create(&vulnerabilityHistoryRes.OpenSourceVulnerabilityHistory).Error; err != nil {
			return err
		}

		return nil
	})
}

func CancelDelegateVulnerability(vulnerabilityPermissionRes *dvulnerabilitypermission.Response,
	vulnerabilityHistoryRes *dvulnerabilityhistory.OpenSourceVulnerabilityHistory) error {
	db := easygorm.GetEasyGormDb()
	return db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(dvulnerabilitypermission.Model()).
			Delete(&vulnerabilityPermissionRes.OpenSourceVulnerabilityPermission).Error; err != nil {
			return err
		}

		if err := tx.Model(dvulnerabilityhistory.Model()).
			Create(&vulnerabilityHistoryRes.OpenSourceVulnerabilityHistory).Error; err != nil {
			return err
		}
		return nil
	})
}

func ConfirmOrIgnoreVulnerability(vulnerabilityRes *dvulnerability.OpenSourceVulnerability, vulnerabilityHistoryRes *dvulnerabilityhistory.OpenSourceVulnerabilityHistory) error {
	db := easygorm.GetEasyGormDb()
	return db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(dvulnerabilityhistory.Model()).
			Create(&vulnerabilityHistoryRes.OpenSourceVulnerabilityHistory).Error; err != nil {
			return err
		}

		var updatedVulnerabilityStatus int
		if vulnerabilityHistoryRes.Status == opensource.VulnerabilityHistoryStatusConfirmed {
			updatedVulnerabilityStatus = opensource.VulnerabilityStatusConfirmed
		} else if vulnerabilityHistoryRes.Status == opensource.VulnerabilityHistoryStatusIgnored {
			updatedVulnerabilityStatus = opensource.VulnerabilityStatusIgnored
		} else {
			return nil
		}

		if err := tx.Model(dvulnerability.Model()).
			Where("id=?", vulnerabilityRes.ID).
			Update("status", updatedVulnerabilityStatus).Error; err != nil {
			return nil
		}
		return nil
	})
}
