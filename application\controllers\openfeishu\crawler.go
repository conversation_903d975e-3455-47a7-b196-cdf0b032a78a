package openfeishu

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/http/cookiejar"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/datasync/dpmsbumember"
	"irisAdminApi/service/dao/feishu/dfeishupmsanalysisdata"
	"irisAdminApi/service/dao/feishu/dfeishupmsmilestonedata"
	"irisAdminApi/service/dao/feishu/dfeishupmsprojectlistdata"

	"github.com/go-rod/rod"
	"github.com/go-rod/rod/lib/launcher"
	"github.com/go-rod/rod/lib/proto"
	"github.com/go-rod/rod/lib/utils"
)

// 配置URL
const (
	PMSLoginURL    = "https://sid.ruijie.com.cn/login?service=http:%2F%2Fpms.ruijie.com.cn%2Fshiro-cas"
	afterLoginURL  = "http://pms.ruijie.com.cn"
	evaluationURL  = "http://pms.ruijie.com.cn/evaluationAnalysis/eval.jhtml"
	milestoneUrl   = "http://pms.ruijie.com.cn/project/milestone/list.jhtml"
	projectListUrl = "http://pms.ruijie.com.cn/channel/qa/load_project_attention_list.jhtml?CATEGORY=ALL&page=1&rows=1000&productLineId=26&projectName="
	KMLoginURL     = "https://sid.ruijie.com.cn/login?service=https:%2F%2Fkm.ruijie.com.cn%2Fsys%2Fportal%2Fpage.jsp"
	KMDocViewUrl   = "https://km.ruijie.com.cn/kms/multidoc/kms_multidoc_knowledge/kmsMultidocKnowledge.do?j_lang=zh-CN&method=view&fdId="
	KMDownloadURL  = "https://km.ruijie.com.cn/sys/attachment/sys_att_main/sysAttMain.do?method=view&fdId="
)

func GetPMSAnalysisData(projectName string) {
	l := launcher.MustNewManaged("") // 远程调用docker容器内浏览器
	browser := rod.New().Client(l.MustClient()).MustConnect()
	// browser := rod.New().MustConnect()//连接本地浏览器
	defer browser.MustClose()
	// 初始化浏览器
	page := setupPage(browser)
	// 登录PMS系统
	err := logIn(page, libs.Config.FeiShuDoc.PmsUser, libs.Config.FeiShuDoc.PmsPass, PMSLoginURL)
	if err != nil {
		fmt.Println("登录PMS系统失败：", err)
		logging.ErrorLogger.Errorf("登录PMS系统失败：", err)
		return
	}
	// 获取评测分析表数据
	analysisData := fetchAnalysisData(page, projectName)
	// 提取评测分析表数据
	err = processAnalysisData(analysisData)
	if err != nil {
		fmt.Println("PMS数据获取失败：", err)
		return
	}
	fmt.Println("PMS数据获取成功")
	// 本地文档数据统计
	// 评测分析表数据与本地数据合并
	// 同步到飞书文档上
	SyncFeiShuAnalysisDatas()
}

func GetPMSAnalysisDataV4(projectNames []string) {
	l := launcher.MustNewManaged("") // 远程调用docker容器内浏览器
	browser := rod.New().Client(l.MustClient()).MustConnect()
	// browser := rod.New().MustConnect()//连接本地浏览器
	defer browser.MustClose()
	// 初始化浏览器
	page := setupPage(browser)
	// 登录PMS系统
	err := logIn(page, libs.Config.FeiShuDoc.PmsUser, libs.Config.FeiShuDoc.PmsPass, PMSLoginURL)
	if err != nil {
		logging.ErrorLogger.Errorf("登录PMS系统失败：", err)
		return
	}
	// 获取评测分析表数据
	for _, projectName := range projectNames {
		analysisData := fetchAnalysisData(page, projectName)
		// 提取评测分析表数据
		err = processAnalysisData(analysisData)
		if err != nil {
			logging.ErrorLogger.Errorf("PMS数据获取失败：", err, projectName)
			continue
		}
		time.Sleep(5 * time.Second)
	}
	// 本地文档数据统计
	// 评测分析表数据与本地数据合并
	// 同步到飞书文档上
	// SyncFeiShuAnalysisDatas()
}

func GetPMSProjectListData() {
	l := launcher.MustNewManaged("") // 远程调用docker容器内浏览器
	browser := rod.New().Client(l.MustClient()).MustConnect()
	// browser := rod.New().MustConnect()//连接本地浏览器
	defer browser.MustClose()
	// 初始化浏览器
	page := setupPage(browser)
	// 登录PMS系统
	err := logIn(page, libs.Config.FeiShuDoc.PmsUser, libs.Config.FeiShuDoc.PmsPass, PMSLoginURL)
	if err != nil {
		fmt.Println("登录PMS系统失败：", err)
		logging.ErrorLogger.Errorf("登录PMS系统失败：", err)
		return
	}
	// 获取评测分析表数据
	projectListData := fetchProjectListData(page)
	// 提取评测分析表数据
	err = processProjectListData(projectListData)
	if err != nil {
		fmt.Println("PMS数据获取失败：", err)
		return
	}
	fmt.Println("PMS数据获取成功")
	// 本地文档数据统计
	// 评测分析表数据与本地数据合并
	// 同步到飞书文档上
}

func GetPMSMilestoneData(projectNames []string) {
	l := launcher.MustNewManaged("") // 远程调用docker容器内浏览器
	browser := rod.New().Client(l.MustClient()).MustConnect()
	// browser := rod.New().MustConnect()//连接本地浏览器
	defer browser.MustClose()
	// 初始化浏览器
	page := setupPage(browser)
	// 登录PMS系统
	err := logIn(page, libs.Config.FeiShuDoc.PmsUser, libs.Config.FeiShuDoc.PmsPass, PMSLoginURL)
	if err != nil {
		fmt.Println("登录PMS系统失败：", err)
		logging.ErrorLogger.Errorf("登录PMS系统失败：", err)
		return
	}
	// 获取评测分析表数据
	projectListData, err := dfeishupmsprojectlistdata.AllProjectListData()
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		return
	}

	projectNameIdMap := map[string]int{}
	projectIdNameMap := map[int]string{}
	for _, item := range projectListData {
		projectNameIdMap[item.ProjectName] = item.ProjectID
		projectIdNameMap[item.ProjectID] = item.ProjectName
	}

	for _, projectName := range projectNames {
		if projectID, ok := projectNameIdMap[projectName]; ok {
			milestoneData := fetchMilestoneData(page, projectID)
			// 提取评测分析表数据
			err = processMilestoneData(milestoneData)
			if err != nil {
				fmt.Println("PMS里程碑数据取失败：", err)
				continue
			}
			fmt.Println("PMS里程碑数据获取成功")
			time.Sleep(5 * time.Second)
		}
	}
	SyncFeiShuMilestoneDatasV4(projectIdNameMap)
}

func setupPage(browser *rod.Browser) *rod.Page {
	page := browser.MustPage("about:blank")
	userAgent := "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
	page.MustSetUserAgent(&proto.NetworkSetUserAgentOverride{
		UserAgent:      userAgent,
		AcceptLanguage: "zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7",
	})
	return page
}

func logIn(page *rod.Page, username, password, url string) error {
	page.MustNavigate(url)
	page.MustWaitNavigation()
	page.MustWaitStable()
	if username == "" || password == "" {
		return fmt.Errorf("用户名或密码不能为空")
	}
	page.MustElement(`input[name="username"]`).MustInput(username)
	page.MustElement(`input[type="password"]`).MustInput(password)
	page.MustElement(`button[type="submit"]`).MustClick()
	page.MustWaitStable()
	return nil
}

func fetchAnalysisData(page *rod.Page, projectName string) AnalysisData {
	var analysisData AnalysisData
	page.Race().Element("#sessionInfoDiv").MustHandle(func(e *rod.Element) {
		cookies, err := proto.NetworkGetAllCookies{}.Call(page)
		if err != nil {
			logging.ErrorLogger.Errorf("获取Cookie失败：", err)
			return
		}
		jar, _ := cookiejar.New(nil)
		client := &http.Client{
			Jar: jar,
		}
		storeCookiesInJar(jar, cookies)
		analysisData = fetchFromURL(client, evaluationURL, projectName)
	}).Element(".error-msg").MustHandle(func(e *rod.Element) {
		log.Println(e.MustText())
	}).MustDo()
	return analysisData
}

func fetchProjectListData(page *rod.Page) []*ProjectData {
	var data []*ProjectData
	page.Race().Element("#sessionInfoDiv").MustHandle(func(e *rod.Element) {
		cookies, err := proto.NetworkGetAllCookies{}.Call(page)
		if err != nil {
			logging.ErrorLogger.Errorf("获取Cookie失败：", err)
			return
		}
		jar, _ := cookiejar.New(nil)
		client := &http.Client{
			Jar: jar,
		}
		storeCookiesInJar(jar, cookies)
		data = fetchProjectListDataFromURL(client, projectListUrl)
	}).Element(".error-msg").MustHandle(func(e *rod.Element) {
		log.Println(e.MustText())
	}).MustDo()
	return data
}

func fetchMilestoneData(page *rod.Page, projectId int) []MilestoneData {
	var data []MilestoneData
	page.Race().Element("#sessionInfoDiv").MustHandle(func(e *rod.Element) {
		cookies, err := proto.NetworkGetAllCookies{}.Call(page)
		if err != nil {
			logging.ErrorLogger.Errorf("获取Cookie失败：", err)
			return
		}
		jar, _ := cookiejar.New(nil)
		client := &http.Client{
			Jar: jar,
		}
		storeCookiesInJar(jar, cookies)
		data = fetchMilestoneDataFromURL(client, milestoneUrl, projectId)
	}).Element(".error-msg").MustHandle(func(e *rod.Element) {
		log.Println(e.MustText())
	}).MustDo()
	return data
}

func storeCookiesInJar(jar *cookiejar.Jar, cookies *proto.NetworkGetAllCookiesResult) {
	u, _ := url.Parse(afterLoginURL)
	for _, cookie := range cookies.Cookies {
		jar.SetCookies(u, []*http.Cookie{
			{
				Name:  cookie.Name,
				Value: cookie.Value,
			},
		})
	}
}

func fetchFromURL(client *http.Client, url, projectName string) AnalysisData {
	data := []byte("projectName=" + projectName)
	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(data))
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	resp, err := client.Do(req)
	if err != nil {
		logging.ErrorLogger.Errorf("发送请求失败：", err)
		return AnalysisData{} // 返回空的AnalysisData
	}
	defer resp.Body.Close()
	body, _ := io.ReadAll(resp.Body)
	return parseResponseBody(body)
}

func fetchProjectListDataFromURL(client *http.Client, _url string) []*ProjectData {
	projects := []*ProjectData{}

	// page=14&rows=50&sort=projectName&order=desc&productLineId=26&projectName=
	// url = fmt.Sprintf("%s&page=1&rows=2000&sort=projectName&order=desc&productLineId=26&projectName=", url)
	projectStatuses := []string{"进行中", "立项前", "取消", "申请中", "完成", "已立项", "已预订", "暂停", "终止"}

	for _, status := range projectStatuses {

		req, _ := http.NewRequest("POST", _url+"&projectStatus="+url.QueryEscape(status), nil)
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
		resp, err := client.Do(req)
		if err != nil {
			logging.ErrorLogger.Errorf("发送请求失败：", err)
			return nil // 返回空的AnalysisData
		}
		defer resp.Body.Close()
		body, _ := io.ReadAll(resp.Body)
		if resp.StatusCode == 200 {
			ret := parseProjectListDataResponseBody(body)
			projects = append(projects, ret.Rows...)
		} else {
			logging.ErrorLogger.Errorf(_url, string(body))
		}
	}

	return projects
}

func fetchMilestoneDataFromURL(client *http.Client, url string, projectId int) []MilestoneData {
	// data := []byte(fmt.Sprintf("projectId=%d", projectId))
	url = fmt.Sprintf("%s?projectId=%d", url, projectId)
	req, _ := http.NewRequest("POST", url, nil)
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	resp, err := client.Do(req)
	if err != nil {
		logging.ErrorLogger.Errorf("发送请求失败：", err)
		return nil // 返回空的AnalysisData
	}
	defer resp.Body.Close()
	body, _ := io.ReadAll(resp.Body)
	if resp.StatusCode == 200 {
		return parseMilestoneDataResponseBody(body)
	} else {
		logging.ErrorLogger.Errorf(url, string(body))
		return nil
	}

	// return parseMilestoneDataResponseBody(body)
}

func parseProjectListDataResponseBody(body []byte) ProjectListData {
	var data ProjectListData
	if err := json.Unmarshal(body, &data); err != nil {
		logging.ErrorLogger.Errorf("解析项目列表数据失败: %v", err)
		return data
	}
	return data
}

func parseMilestoneDataResponseBody(body []byte) []MilestoneData {
	var data []MilestoneData
	if err := json.Unmarshal(body, &data); err != nil {
		logging.ErrorLogger.Errorf("解析里程碑数据失败: %v", err)
		return data
	}
	return data
}

func parseResponseBody(body []byte) AnalysisData {
	var analysisData AnalysisData
	if err := json.Unmarshal(body, &analysisData); err != nil {
		logging.ErrorLogger.Errorf("解析分析数据失败: %v", err)
		return analysisData
	}
	return analysisData
}

func processAnalysisData(analysisData AnalysisData) error {
	if len(analysisData.Data) > 0 {
		analysisList := []map[string]interface{}{}
		for i, item := range analysisData.Data {
			switch i {
			case 0:
				var dataType1 DataType1
				if err := json.Unmarshal(item, &dataType1); err != nil {
					logging.ErrorLogger.Errorf("解析数据类型1失败: %v", err)
					return err
				}
				// 将dataType1导入数据库
				data := map[string]interface{}{
					"BsIrDefect":     &dataType1.BsIrDefect,
					"StUtDefect":     &dataType1.StUtDefect,
					"StIrDefect":     &dataType1.StIrDefect,
					"SDdDefect":      &dataType1.SDdDefect,
					"SSdDefect":      &dataType1.SSdDefect,
					"SRaDefect":      &dataType1.SRaDefect,
					"StIgDefect":     &dataType1.StIgDefect,
					"SCsDefect":      &dataType1.SCsDefect,
					"STdDefect":      &dataType1.STdDefect,
					"ProjectName":    dataType1.ProjectName,
					"ProjectID":      dataType1.ProjectId,
					"CreatedAt":      time.Now(),
					"UpdatedAt":      time.Now(),
					"WorkPacketName": "质量目标",
					"WorkPacketID":   0,
					"RecordType":     1,
				}
				analysisList = append(analysisList, data)
			case 1:
				var dataType2 DataType2
				if err := json.Unmarshal(item, &dataType2); err != nil {
					logging.ErrorLogger.Errorf("解析数据类型2失败: %v", err)
					return err
				}
				// 将dataType2导入数据库
				data := map[string]interface{}{
					"BsTtA":         &dataType2.BsTtA,
					"WpStCount":     &dataType2.WpStCount,
					"StIr0":         &dataType2.StIr0,
					"SDdDefect":     &dataType2.SDdDefect,
					"StIr1":         &dataType2.StIr1,
					"CodeTransTemp": &dataType2.CodeTransTemp,
					"StIr2":         &dataType2.StIr2,
					"BsIgHDefect":   &dataType2.BsIgHDefect,
					"CodeTrans":     &dataType2.CodeTrans,
					"SSdCount":      &dataType2.SSdCount,
					"CodeNew":       &dataType2.CodeNew,
					"STdCount":      &dataType2.STdCount,
					"WpStDefect":    &dataType2.WpStDefect,
					"SCsRCount":     &dataType2.SCsRCount,
					"SDdCount":      &dataType2.SDdCount,
					"SSdDefect":     &dataType2.SSdDefect,
					"STdDefect":     &dataType2.STdDefect,
					"StUt0":         &dataType2.StUt0,
					"StUtDefect":    &dataType2.StUtDefect,
					"SRaCount":      &dataType2.SRaCount,
					"BsIgA":         &dataType2.BsIgA,
					"BsTtADefect":   &dataType2.BsTtADefect,
					"CodeTotal":     &dataType2.CodeTotal,
					"SCsBCount":     &dataType2.SCsBCount,
					"SRaDefect":     &dataType2.SRaDefect,
					"SCsCount":      &dataType2.SCsCount,
					"BsIrDefect":    &dataType2.BsIrDefect,
					"BsIrCount":     &dataType2.BsIrCount,
					"StIrDefect":    &dataType2.StIrDefect,
					"BsIgADefect":   &dataType2.BsIgADefect,
					"StIg0":         &dataType2.StIg0,
					"BsIgH":         &dataType2.BsIgH,
					"StIgDefect":    &dataType2.StIgDefect,
					"SCsDefect":     &dataType2.SCsDefect,

					"ProjectName":    dataType2.ProjectName,
					"ProjectID":      dataType2.ProjectId,
					"CreatedAt":      time.Now(),
					"UpdatedAt":      time.Now(),
					"WorkPacketName": "[" + dataType2.ProjectName + "]项目汇总",
					"WorkPacketID":   0,
					"RecordType":     2,
				}
				analysisList = append(analysisList, data)
			default:
				var dataTypeRest DataTypeRest
				if err := json.Unmarshal(item, &dataTypeRest); err != nil {
					logging.ErrorLogger.Errorf("解析数据类型Rest失败: %v", err)
					return err
				}
				// 将dataTypeRest导入数据库
				data := map[string]interface{}{
					"StIr0":             &dataTypeRest.StIr0,
					"CodeTransTemp":     &dataTypeRest.CodeTransTemp,
					"StIr1":             &dataTypeRest.StIr1,
					"BsIgHDefect":       &dataTypeRest.BsIgHDefect,
					"StIr2":             &dataTypeRest.StIr2,
					"CodeTrans":         &dataTypeRest.CodeTrans,
					"SRa0":              &dataTypeRest.SRa0,
					"SCbDc3":            &dataTypeRest.SCbDc3,
					"SCbDc4":            &dataTypeRest.SCbDc4,
					"STdDefect":         &dataTypeRest.STdDefect,
					"BsTtADefect":       &dataTypeRest.BsTtADefect,
					"SRaDefect":         &dataTypeRest.SRaDefect,
					"SCsBCount":         &dataTypeRest.SCsBCount,
					"SSd0":              &dataTypeRest.SSd0,
					"StIrDefect":        &dataTypeRest.StIrDefect,
					"BsIrCount":         &dataTypeRest.BsIrCount,
					"SSd1":              &dataTypeRest.SSd1,
					"BsIgADefect":       &dataTypeRest.BsIgADefect,
					"StIg0":             &dataTypeRest.StIg0,
					"StIr1Defect":       &dataTypeRest.StIr1Defect,
					"SCsDefect":         &dataTypeRest.SCsDefect,
					"BsTtA":             &dataTypeRest.BsTtA,
					"WpStCount":         &dataTypeRest.WpStCount,
					"SDdDefect":         &dataTypeRest.SDdDefect,
					"CodeNew":           &dataTypeRest.CodeNew,
					"SSdCount":          &dataTypeRest.SSdCount,
					"STdCount":          &dataTypeRest.STdCount,
					"SCr0":              &dataTypeRest.SCr0,
					"WpStDefect":        &dataTypeRest.WpStDefect,
					"SCr2":              &dataTypeRest.SCr2,
					"SCsRCount":         &dataTypeRest.SCsRCount,
					"SCr1":              &dataTypeRest.SCr1,
					"SSdDefect":         &dataTypeRest.SSdDefect,
					"SDdCount":          &dataTypeRest.SDdCount,
					"SCr3":              &dataTypeRest.SCr3,
					"StUt0":             &dataTypeRest.StUt0,
					"StUtDefect":        &dataTypeRest.StUtDefect,
					"CodeTotal":         &dataTypeRest.CodeTotal,
					"BsIgA":             &dataTypeRest.BsIgA,
					"SRaCount":          &dataTypeRest.SRaCount,
					"SDd1":              &dataTypeRest.SDd1,
					"SDd0":              &dataTypeRest.SDd0,
					"SCbDc0":            &dataTypeRest.SCbDc0,
					"SCbDc1":            &dataTypeRest.SCbDc1,
					"SDd2":              &dataTypeRest.SDd2,
					"SCbDc2":            &dataTypeRest.SCbDc2,
					"OBs0":              &dataTypeRest.OBs0,
					"SCsCount":          &dataTypeRest.SCsCount,
					"BsIrDefect":        &dataTypeRest.BsIrDefect,
					"STd1":              &dataTypeRest.STd1,
					"STd0":              &dataTypeRest.STd0,
					"WorkPacketManager": &dataTypeRest.WorkPacketManager,
					"BsIgH":             &dataTypeRest.BsIgH,
					"StIgDefect":        &dataTypeRest.StIgDefect,
					"PSTLUserName":      dataTypeRest.PSTLUserName,

					"ProjectName":    dataTypeRest.ProjectName,
					"ProjectID":      dataTypeRest.ProjectId,
					"CreatedAt":      time.Now(),
					"UpdatedAt":      time.Now(),
					"WorkPacketName": dataTypeRest.WorkPacketName,
					"WorkPacketID":   dataTypeRest.WorkPacketId,
					"RecordType":     3,
				}
				analysisList = append(analysisList, data)
			}
		}
		err := dfeishupmsanalysisdata.CreateOrUpdateAnalysisData(analysisList)
		if err != nil {
			return err
		}

	}
	return nil
}

/*
ProjectID            int      `json:"projectId" update:"1"`
Orders               int      `json:"orders" update:"1"`
MileStoneValue       string   `gorm:"type:varchar(255)" json:"mileStoneValue" update:"1"`
MilestoneName        string   `gorm:"type:varchar(255)" json:"milestoneName" update:"1"`
LatestReviewFormCode string   `gorm:"type:varchar(255)" json:"latestReviewFormCode" update:"1"`
ReviewStatus         string   `gorm:"type:varchar(255)" json:"reviewStatus" update:"1"`
CreateDate           uint64   `json:"create_date" update:"1"`
ModifyDate           uint64   `json:"modifyDate" update:"1"`
PlanDate             uint64   `json:"planDate" update:"1"`
ActDate              uint64   `json:"actDate" update:"1"`
Disabled             bool     `json:"disabled" update:"1"`
ChangeNum            *float64 `json:"changeNum" update:"1"`
Status               string   `gorm:"type:varchar(255)" json:"status" update:"1"`
FinishedTaskNums     *float64 `json:"finishedTaskNums" update:"1"`
Progress             *float64 `json:"progress" update:"1"`
TaskNums             *float64 `json:"taskNums" update:"1"`
*/
func processMilestoneData(milestoneData []MilestoneData) error {
	if len(milestoneData) > 0 {
		items := []map[string]interface{}{}
		for _, item := range milestoneData {
			// 将dataType1导入数据库
			data := map[string]interface{}{
				"ID":                   item.ID,
				"ProjectID":            item.ProjectID,
				"Orders":               item.Orders,
				"MileStoneValue":       item.MileStoneValue,
				"MilestoneName":        item.MilestoneName,
				"LatestReviewFormCode": item.LatestReviewFormCode,
				"ReviewStatus":         item.ReviewStatus,
				"CreateDate":           item.CreateDate,
				"ModifyDate":           item.ModifyDate,
				"PlanDate":             item.PlanDate,
				"ActDate":              item.ActDate,
				"Disabled":             item.Disabled,
				"ChangeNum":            item.ChangeNum,
				"Status":               item.Status,
				"FinishedTaskNums":     item.FinishedTaskNums,
				"Progress":             item.Progress,
				"TaskNums":             item.TaskNums,
				"CreatedAt":            time.Now(),
				"UpdatedAt":            time.Now(),
			}
			items = append(items, data)
		}
		err := dfeishupmsmilestonedata.CreateOrUpdateMilestoneData(items)
		if err != nil {
			return err
		}

	}
	return nil
}

func processProjectListData(projectData []*ProjectData) error {
	if len(projectData) > 0 {
		items := []map[string]interface{}{}
		projectNames := []string{}
		for _, item := range projectData {
			// 将dataType1导入数据库
			data := map[string]interface{}{
				"ProjectID":     item.ProjectID,
				"ProjectName":   item.ProjectName,
				"ProjectStatus": item.ProjectStatus,
				"CreatedAt":     time.Now(),
				"UpdatedAt":     time.Now(),
			}
			projectNames = append(projectNames, item.ProjectName)
			items = append(items, data)
		}
		err := dfeishupmsprojectlistdata.CreateOrUpdateProjectListData(items, projectNames)
		if err != nil {
			return err
		}

	}
	return nil
}

func SyncFeiShuAnalysisDatas() {
	tableID := "tblqymiijlhqw5gk"
	// 批量删除线上数据
	DeleteTableRecordData(tableID, []string{"项目"})
	// 获取本地数据
	page := 1
	pageSize := 500
	for {
		analysisData := dfeishupmsanalysisdata.Response{}
		data, err := analysisData.All("", "", "created_at", page, pageSize)
		if err != nil {
			logging.ErrorLogger.Errorf("GetAnalysisDatas error:%s", err.Error())
			return
		}
		items := data["items"].([]*dfeishupmsanalysisdata.ListResponse)
		if len(items) > 0 {

			records := []map[string]interface{}{}
			// 将数据按照批次插入飞书数据表,每批次500条
			for _, item := range items {
				rec := map[string]interface{}{
					"table_id":    item.ID,
					"record_type": item.RecordType,
					"项目":          item.ProjectName,
					"工作包":         item.WorkPacketName,
					"工作包负责人":      item.WorkPacketManager,
				}
				if item.PSTLUserName != "" {
					rec["PSTL"] = item.PSTLUserName
				}
				if item.CodeNew != nil {
					rec["新增代码量"] = item.CodeNew
				}
				if item.CodeTrans != nil {
					rec["移植(正式)"] = item.CodeTrans
				}
				if item.CodeTransTemp != nil {
					rec["移植(临时)"] = item.CodeTransTemp
				}
				if item.CodeTotal != nil {
					rec["总计代码量"] = item.CodeTotal
				}
				if item.SRaDefect != nil {
					rec["需求分析缺陷密度"] = item.SRaDefect
				}
				if item.SSdDefect != nil {
					rec["系统设计缺陷密度"] = item.SSdDefect
				}
				if item.STdDefect != nil {
					rec["总体设计缺陷密度"] = item.STdDefect
				}
				if item.SDdDefect != nil {
					rec["详细设计缺陷密度"] = item.SDdDefect
				}
				if item.SCsDefect != nil {
					rec["编码(评审+BUG系统) 缺陷密度"] = item.SCsDefect
				}
				if item.StUtDefect != nil {
					rec["单元测试缺陷密度"] = item.StUtDefect
				}
				if item.StIgDefect != nil {
					rec["集成测试缺陷密度"] = item.StIgDefect
				}
				if item.BsIrDefect != nil {
					rec["内测前缺陷密度"] = item.BsIrDefect
				}
				if item.StIrDefect != nil {
					rec["内测缺陷密度"] = item.StIrDefect
				}
				if item.WpStDefect != nil {
					rec["项目周期缺陷密度"] = item.WpStDefect
				}
				records = append(records, rec)
			}
			tableRecordResp, err := BatchCreate(tableID, libs.Config.FeiShuDoc.BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("GetAnalysisDatas batchCreate error:%s", err.Error(), string(tableRecordResp.RawBody))
				continue
			}
		}
		// 检查是否还有更多数据
		if len(items) < pageSize {
			break
		}
		time.Sleep(300 * time.Millisecond)
		page++
	}
}

func CheckIfTemplateIsLatest() {
	// l := launcher.MustNewManaged("") //远程调用docker容器内浏览器
	// browser := rod.New().Client(l.MustClient()).MustConnect()
	// u := launcher.New().Bin("E:/Program Files/360ChromeX/Chrome/Application/360ChromeX.exe").MustLaunch()
	// browser := rod.New().ControlURL(u).MustConnect() //连接本地浏览器
	browser := rod.New().MustConnect() // 连接本地浏览器
	defer browser.MustClose()
	// 初始化浏览器
	page := setupPage(browser)
	// 登录KMS系统
	err := logIn(page, libs.Config.FeiShuDoc.PmsUser, libs.Config.FeiShuDoc.PmsPass, KMLoginURL)
	if err != nil {
		fmt.Println("登录KMS系统失败：", err)
		logging.ErrorLogger.Errorf("登录KMS系统失败：", err)
		return
	}
	// 访问文档地址
	kmsDocUrl := KMDocViewUrl + "189f309e261a158c844a7e24d709afbd" //
	fmt.Println(kmsDocUrl)
	page.MustNavigate(kmsDocUrl)
	page.MustWaitStable()
	// 解析 URL
	parsedURL, err := url.Parse(page.MustInfo().URL)
	if err != nil {
		fmt.Println("Error parsing URL:", err)
		return
	}
	// 获取查询参数
	queryParams := parsedURL.Query()
	// 获取 fdId 的值
	fdId := queryParams.Get("fdId")
	fmt.Println(parsedURL, fdId)
	// 打开下载页面
	// 获取文件名称
	el := page.MustElement("#spreadBox > div.lui-fm-flexibleL-inner > div > div.lui_form_content_top_frame > div.lui_form_title_frame.lui_form_title_frame_able > div.lui_form_title_box > div.kms_multidoc_view_title > font")
	fileName := el.MustText()
	el.MustClick()
	page.MustWaitStable()
	page.Mouse.MustScroll(0, 11800)
	frame01 := page.MustElement("#lui-id-11 > iframe").MustFrame()
	if frame01.MustHas("#seperateOpenTool > ul > li > a") {
		link := frame01.MustElement("#seperateOpenTool > ul > li > a")
		linkUrl := link.MustProperty("href").Val().(string)
		page.MustNavigate(linkUrl)
		page.MustWaitStable()
		// 创建临时存放文件夹
		tempDir := libs.Config.FileStorage.Upload + "FeishuTemplate/" + time.Now().Format("20060102") + "/" + libs.GetUUID() + "/"
		err = os.MkdirAll(tempDir, 0o750)
		os.Chmod(tempDir, 0o750)
		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while downloading: %s", err.Error()))
			return
		}
		downLink := page.MustElement("#readerTop > div.top_left > div > ul > li > a:nth-child(4)")
		tempFn := filepath.Join(tempDir, fileName)
		// 获取并修改函数
		jsCode := `() =>{
				if (typeof window.downloadAttAndLog === 'function') {
					const originalFunc = window.downloadAttAndLog.toString();
					const modifiedFunc = originalFunc.replace("Com_OpenWindow(downloadUrl, \"_blank\");", "window.location.href = downloadUrl;");
					eval("window.downloadAttAndLog = " + modifiedFunc);
					return true;
				} else {
					return false;
				}
           };`
		result := page.MustEval(jsCode).Bool()
		if !result {
			logging.ErrorLogger.Errorf("downloadAttAndLog函数未找到")
		} else {
			wait := browser.MustWaitDownload()
			downLink.MustClick()
			_ = utils.OutputFile(tempFn, wait())
			fmt.Println(tempFn + "下载成功")
		}
	}
}

func ConvertToCSSSelector(value string) string {
	var sb strings.Builder
	for i, char := range value {
		if i == 0 && char >= '0' && char <= '9' {
			sb.WriteString(fmt.Sprintf("\\%X ", char))
		} else {
			sb.WriteRune(char)
		}
	}
	return sb.String()
}

func SyncFeiShuAnalysisDatasV4() {
	appToken := "ZfoVbWxqTaI09Rs3Ml1cRzRCnuf"
	tableID := "tblL09KXUCwefL14"
	// 批量删除线上数据
	DeleteTableRecordDataV4(appToken, tableID, []string{"项目"})
	// 获取本地数据
	page := 1
	pageSize := 500
	for {
		analysisData := dfeishupmsanalysisdata.Response{}
		data, err := analysisData.All("", "", "created_at", page, pageSize)
		if err != nil {
			logging.ErrorLogger.Errorf("GetAnalysisDatas error:%s", err.Error())
			return
		}
		items := data["items"].([]*dfeishupmsanalysisdata.ListResponse)
		if len(items) > 0 {

			records := []map[string]interface{}{}
			// 将数据按照批次插入飞书数据表,每批次500条
			for _, item := range items {
				rec := map[string]interface{}{
					"table_id":    item.ID,
					"record_type": item.RecordType,
					"项目":          item.ProjectName,
					"工作包":         item.WorkPacketName,
					"工作包名称":       item.WorkPacketName,
					"工作包负责人":      item.WorkPacketManager,
				}
				if item.PSTLUserName != "" {
					rec["PSTL"] = item.PSTLUserName
				}
				if item.CodeNew != nil {
					rec["新增代码量"] = item.CodeNew
				}
				if item.CodeTrans != nil {
					rec["移植(正式)"] = item.CodeTrans
				}
				if item.CodeTransTemp != nil {
					rec["移植(临时)"] = item.CodeTransTemp
				}
				if item.CodeTotal != nil {
					rec["总计代码量"] = item.CodeTotal
				}
				if item.SRaDefect != nil {
					rec["需求分析缺陷密度"] = item.SRaDefect
				}
				if item.SSdDefect != nil {
					rec["系统设计缺陷密度"] = item.SSdDefect
				}
				if item.STdDefect != nil {
					rec["总体设计缺陷密度"] = item.STdDefect
				}
				if item.SDdDefect != nil {
					rec["详细设计缺陷密度"] = item.SDdDefect
				}
				if item.SCsDefect != nil {
					rec["编码(评审+BUG系统) 缺陷密度"] = item.SCsDefect
				}
				if item.StUtDefect != nil {
					rec["单元测试缺陷密度"] = item.StUtDefect
				}
				if item.StIgDefect != nil {
					rec["集成测试缺陷密度"] = item.StIgDefect
				}
				if item.BsIrDefect != nil {
					rec["内测前缺陷密度"] = item.BsIrDefect
				}
				if item.StIrDefect != nil {
					rec["内测缺陷密度"] = item.StIrDefect
				}
				if item.WpStDefect != nil {
					rec["项目周期缺陷密度"] = item.WpStDefect
				}
				records = append(records, rec)
			}
			tableRecordResp, err := BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("GetAnalysisDatas batchCreate error:%s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
		// 检查是否还有更多数据
		if len(items) < pageSize {
			break
		}
		time.Sleep(300 * time.Millisecond)
		page++
	}
}

func SyncFeiShuAnalysisDatasV5() {
	appToken := "YvAObMrWPaT4rHsfczFcmVAOnjg"
	tableID := "tbly2t8C7rNNlV3j"
	// 批量删除线上数据
	DeleteTableRecordDataV4(appToken, tableID, []string{"项目"})
	// 获取本地数据
	page := 1
	pageSize := 500
	for {
		analysisData := dfeishupmsanalysisdata.Response{}
		data, err := analysisData.All("", "", "created_at", page, pageSize)
		if err != nil {
			logging.ErrorLogger.Errorf("GetAnalysisDatas error:%s", err.Error())
			return
		}
		items := data["items"].([]*dfeishupmsanalysisdata.ListResponse)
		if len(items) > 0 {

			records := []map[string]interface{}{}
			// 将数据按照批次插入飞书数据表,每批次500条
			for _, item := range items {
				rec := map[string]interface{}{
					"table_id":    item.ID,
					"record_type": item.RecordType,
					"项目":          item.ProjectName,
					"工作包":         item.WorkPacketName,
					"工作包名称":       item.WorkPacketName,
					"工作包负责人":      item.WorkPacketManager,
				}
				if item.PSTLUserName != "" {
					rec["PSTL"] = item.PSTLUserName
				}
				if item.CodeNew != nil {
					rec["新增代码量"] = item.CodeNew
				}
				if item.CodeTrans != nil {
					rec["移植(正式)"] = item.CodeTrans
				}
				if item.CodeTransTemp != nil {
					rec["移植(临时)"] = item.CodeTransTemp
				}
				if item.CodeTotal != nil {
					rec["总计代码量"] = item.CodeTotal
				}
				if item.SRaDefect != nil {
					rec["需求分析缺陷密度"] = item.SRaDefect
				}
				if item.SSdDefect != nil {
					rec["系统设计缺陷密度"] = item.SSdDefect
				}
				if item.STdDefect != nil {
					rec["总体设计缺陷密度"] = item.STdDefect
				}
				if item.SDdDefect != nil {
					rec["详细设计缺陷密度"] = item.SDdDefect
				}
				if item.SCsDefect != nil {
					rec["编码(评审+BUG系统) 缺陷密度"] = item.SCsDefect
				}
				if item.StUtDefect != nil {
					rec["单元测试缺陷密度"] = item.StUtDefect
				}
				if item.StIgDefect != nil {
					rec["集成测试缺陷密度"] = item.StIgDefect
				}
				if item.BsIrDefect != nil {
					rec["内测前缺陷密度"] = item.BsIrDefect
				}
				if item.StIrDefect != nil {
					rec["内测缺陷密度"] = item.StIrDefect
				}
				if item.WpStDefect != nil {
					rec["项目周期缺陷密度"] = item.WpStDefect
				}
				records = append(records, rec)
			}
			tableRecordResp, err := BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("GetAnalysisDatas batchCreate error:%s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
		// 检查是否还有更多数据
		if len(items) < pageSize {
			break
		}
		time.Sleep(300 * time.Millisecond)
		page++
	}
}

func SyncFeiShuMilestoneDatasV4(projectIdNameMap map[int]string) {
	appToken := "ZfoVbWxqTaI09Rs3Ml1cRzRCnuf"
	tableID := "tbl6PbcP7pIRhbCG"

	// 获取本地数据

	milestoneData, err := dfeishupmsmilestonedata.All()
	if err != nil {
		logging.ErrorLogger.Errorf("GetAnalysisDatas error:%s", err.Error())
		return
	}

	projectIdDataMap := map[int][]*dfeishupmsmilestonedata.ListResponse{}

	for _, item := range milestoneData {
		if _, ok := projectIdDataMap[item.ProjectID]; ok {
			projectIdDataMap[item.ProjectID] = append(projectIdDataMap[item.ProjectID], item)
		} else {
			projectIdDataMap[item.ProjectID] = []*dfeishupmsmilestonedata.ListResponse{item}
		}
	}
	records := []map[string]interface{}{}
	for projectId, items := range projectIdDataMap {
		rec := map[string]interface{}{
			"项目": projectIdNameMap[projectId],
		}
		for _, item := range items {
			SetMileStoneColumnAndValue(&rec, item.MilestoneName, item.Disabled, item.PlanDate, item.ActDate, item.Status, item.ReviewStatus)
		}
		records = append(records, rec)
		// 将数据按照批次插入飞书数据表,每批次500条
	}

	// 批量删除线上数据
	DeleteTableRecordDataV4(appToken, tableID, []string{"项目"})
	page := 1
	pageSize := 500

	for {
		start := (page - 1) * pageSize
		end := page * pageSize
		if end > len(records) {
			end = len(records)
		}

		tableRecordResp, err := BatchCreate(tableID, appToken, records[start:end])
		if err != nil {
			logging.ErrorLogger.Errorf("Milestone batchCreate error:%s", err.Error(), string(tableRecordResp.RawBody))
		}

		// 检查是否还有更多数据

		time.Sleep(300 * time.Millisecond)
		if page*pageSize > len(records) {
			break
		}
		page++
	}
}

func SyncFeiShuBuMemberDatasV4() {
	appToken := "ZfoVbWxqTaI09Rs3Ml1cRzRCnuf"
	tableID := "tbloP1zaTdwAoMPv"

	// 获取本地数据

	members, err := dpmsbumember.All()
	if err != nil {
		logging.ErrorLogger.Errorf("GetAnalysisDatas error:%s", err.Error())
		return
	}

	records := []map[string]interface{}{}
	for _, member := range members {
		rec := map[string]interface{}{
			"用户名":  member.UserCn,
			"邮箱":   member.Email,
			"一级部门": member.OneDepartmentName,
			"二级部门": member.TwoDepartmentName,
			"三级部门": member.ThreeDepartmentName,
			"四级部门": member.FourDepartmentName,
			"五级部门": member.FiveDepartmentName,
		}

		if member.UserStatus == 2 {
			rec["状态"] = "无效"
		} else {
			rec["状态"] = "有效"
		}

		records = append(records, rec)
		// 将数据按照批次插入飞书数据表,每批次500条
	}

	// 批量删除线上数据
	DeleteTableRecordDataV4(appToken, tableID, []string{"用户名"})
	page := 1
	pageSize := 500

	for {
		start := (page - 1) * pageSize
		end := page * pageSize
		if end > len(records) {
			end = len(records)
		}

		tableRecordResp, err := BatchCreate(tableID, appToken, records[start:end])
		if err != nil {
			logging.ErrorLogger.Errorf("GetAnalysisDatas batchCreate error:%s", err.Error(), string(tableRecordResp.RawBody))
		}

		// 检查是否还有更多数据

		time.Sleep(300 * time.Millisecond)
		if page*pageSize > len(records) {
			break
		}
		page++
	}
}

func SetMileStoneColumnAndValue(record *map[string]interface{}, column string, disabled, planDate, actDate, status, reviewStatus interface{}) {
	skipColumns := []string{"立项", "CC", "EC", "转测试", "试点发布", "完成(正式发布)"}
	if !libs.InArrayS(skipColumns, column) {
		return
	}

	if disabled, ok := disabled.(bool); ok {
		if disabled {
			(*record)[column+"_无效"] = "1"
		} else {
			(*record)[column+"_无效"] = "0"
		}
	}

	if value, ok := planDate.(uint64); ok && value != 0 {
		(*record)[column+"_计划时间"] = value
	}

	if value, ok := actDate.(uint64); ok && value != 0 {
		(*record)[column+"_实际时间"] = value
	}

	(*record)[column+"_状态"] = status
	(*record)[column+"_评审状态"] = reviewStatus
}

func SetHardwareProjectMileStoneColumnAndValue(record *map[string]interface{}, column string, disabled, planDate, actDate, status interface{}) {
	if disabled, ok := disabled.(bool); ok {
		if disabled {
			(*record)[column+"_无效"] = "1"
		} else {
			(*record)[column+"_无效"] = "0"
		}
	}

	if value, ok := planDate.(*time.Time); ok && value != nil {
		(*record)[column+"_计划时间"] = value.Unix() * 1000
	}

	if value, ok := actDate.(*time.Time); ok && value != nil {
		(*record)[column+"_实际时间"] = value.Unix() * 1000
	}

	(*record)[column+"_状态"] = status
}
