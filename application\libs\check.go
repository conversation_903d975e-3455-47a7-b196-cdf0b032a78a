package libs

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"irisAdminApi/application/logging"
	"net/http"
)

type ProductResponse struct {
	Status int    `json:"status"`
	Err    string `json:"err"`
	Count  int    `json:"count"`
}

// {"sysid":"0ea45d606c6434cb26ea8b9688f0ea48","tid":"TL201","where":"PRODUCT_BUSINESS_CODE ='000029' and BEGIN_DATE='20190108'","PageSize":"100","PageIndex":"1"}
func CheckProductSeries(name string) error {
	url := "http://bjapi.itnb.cc/dprapi/BIAPI/GetReportData"
	jsonStr, _ := json.Marshal(map[string]interface{}{
		"sysid":    "0ea45d606c6434cb26ea8b9688f0ea48",
		"tid":      "TL201",
		"where":    fmt.Sprintf("PRODUCT_BUSINESS_CODE ='000029' and PRODUCT_SERIES='%s'", name),
		"PageSize": "10", "PageIndex": "1",
	})
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	if err != nil {
		logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
		return err
	}
	req.Header.Add("sysid", "0ea45d606c6434cb26ea8b9688f0ea48")
	req.Header.Add("Content-Type", "application/json")

	out, err := HandlerRequestCommon(req)
	result := ProductResponse{}
	err = json.Unmarshal(out, &result)
	if err != nil {
		logging.ErrorLogger.Errorf("umarshal response err ", err, string(out))
		return err
	}

	if result.Status == 200 {
		if result.Count > 0 {
			return nil
		}
		return errors.New("产品型号不合规")
	}

	return errors.New("产品线合规则检查失败, 请重试，如多次失败，请联系管理员")
}

func CheckProductModel(name string) error {
	url := "http://bjapi.itnb.cc/dprapi/BIAPI/GetReportData"
	jsonStr, _ := json.Marshal(map[string]interface{}{
		"sysid":    "0ea45d606c6434cb26ea8b9688f0ea48",
		"tid":      "TL201",
		"where":    fmt.Sprintf("PRODUCT_BUSINESS_CODE ='000029' and PRODUCT_MODEL='%s'", name),
		"PageSize": "10", "PageIndex": "1",
	})
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	if err != nil {
		logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
		return err
	}
	req.Header.Add("sysid", "0ea45d606c6434cb26ea8b9688f0ea48")
	req.Header.Add("Content-Type", "application/json")

	out, err := HandlerRequestCommon(req)
	result := ProductResponse{}
	err = json.Unmarshal(out, &result)
	if err != nil {
		logging.ErrorLogger.Errorf("umarshal response err ", err, string(out))
		return err
	}
	if result.Status == 200 {
		if result.Count > 0 {
			return nil
		}
		return errors.New("产品型号不合规")
	}

	return errors.New("产品型号合规则检查失败, 请重试，如多次失败，请联系管理员")
}
