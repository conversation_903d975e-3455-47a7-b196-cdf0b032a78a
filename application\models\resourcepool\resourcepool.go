package resourcepool

import (
	"irisAdminApi/application/models"
	"time"
)

/*
id：INTEGER PRIMARY KEY UNIQUE，序号
mgt_ip：TEXT，管理ip，新增和编辑页面ipv4点分十进制表示，必填
mgt_port：INTEGER，ssh端口，新增和编辑页面选填，默认22
con_ip：TEXT，备用，串口ip，备用，所有页面无
con_port：INTEGER，备用，串口端口，备用，所有页面无
reserved：TEXT，拥有者，录入登录用户名，新增和编辑页面无，显示页面显示
status：TEXT，备用，通讯状态，默认active，页面无
location：TEXT，新增和编辑页面选填，默认Fuzhou
comments：TEXT，新增和编辑页面选填，默认 0d96cefd-b0cf-4d8b-8e4b-9318ffbd79c5
root_passwd：TEXT，root密码，入库加密，新增和编辑页面必填
cycle：INTEGER，检查周期（几分钟检查一次），新增和编辑页面可选择1、3、5、10、15、20、25、30分钟
key：TEXT，飞书机器人key，新增和编辑页面选填，不填默认key
flag：Bool，是否启用，新增和编辑页面必选，默认启用
*/

type ResourcePoolMonitor struct {
	models.ModelBase
	MgtIp      string `gorm:"not null; type:varchar(30)" json:"mgt_ip" validate:"required"`
	MgtPort    uint   `gorm:"not null;default:22" json:"mgt_port" validate:"required"`
	ConIp      string `gorm:"not null; type:varchar(30)" json:"con_ip"`
	ConPort    uint   `gorm:"not null;" json:"con_port"`
	Reserved   string `gorm:"not null; type:varchar(30)" json:"reserved"`
	Status     string `gorm:"not null; type:varchar(30); default:'active'" json:"status"`
	Location   string `gorm:"not null; type:varchar(60)" json:"location"`
	Comments   string `gorm:"not null; type:varchar(200)" json:"comments"`
	RootPasswd string `gorm:"not null; type:varchar(60)" json:"root_passwd" validate:"required"`
	Cycle      uint   `gorm:"not null;" json:"cycle"`
	FsKey      string `gorm:"not null; type:varchar(60)" json:"fs_key"`
	Flag       bool   `gorm:"not null;default:true" json:"flag"`
	UserID     uint   `gorm:"not null" json:"user_id"`
}

type ResourcePoolInterface struct {
	models.ModelBase
	Name         string `gorm:"not null;type:varchar(32)" json:"name"`
	Interface    string `gorm:"type:varchar(32);default:null" json:"interface"`
	Driver       string `gorm:"type:varchar(16);default:virtio" json:"driver"`
	Mac          string `gorm:"type:varchar(64);default:null" json:"mac"`
	SwName       string `gorm:"type:varchar(32);default:null" json:"sw_name"`
	SwPort       string `gorm:"type:varchar(32);default:null" json:"sw_port"`
	VlanId       string `gorm:"type:varchar(32);default:null" json:"vlan_id"`
	VlanMode     string `gorm:"type:varchar(16);default:access" json:"vlan_mode"`
	Type         string `gorm:"type:varchar(16);default:traffic" json:"type"`
	Connections  string `gorm:"type:varchar(64);default:null" json:"connections"`
	Comments     string `gorm:"type:varchar(64);default:null" json:"comments"`
	SwPortStatus string `gorm:"type:varchar(10);default:active" json:"sw_port_status"`
}

type ResourcePoolResource struct {
	models.ModelBase
	Name       string     `gorm:"not null; type:varchar(32)" json:"name"`
	Domain     string     `gorm:"type:varchar(64); default:null" json:"domain"`
	Model      string     `json:"model"`
	Make       string     `gorm:"type:varchar(32); default:ruijie" json:"make"`
	Osname     string     `gorm:"type:varchar(32); default:null" json:"osname"`
	MgtIp      string     `gorm:"type:varchar(64); default:null" json:"mgt_ip"`
	MgtPort    int        `gorm:"type:int; default:22" json:"mgt_port"`
	ConIp      string     `gorm:"type:varchar(64); default:null" json:"con_ip"`
	ConPort    int        `gorm:"type:int; default:null" json:"con_port"`
	Testbed    string     `gorm:"type:varchar(64); default:bjlab" json:"testbed"`
	Type       string     `gorm:"type:varchar(64); default:null" json:"type"`
	Status     string     `gorm:"type:varchar(32); default:active" json:"status"`
	Rack       string     `gorm:"type:varchar(32); default:null" json:"rack"`
	Slot       int        `gorm:"type:int; default:null" json:"slot"`
	Reserved   string     `gorm:"type:varchar(64); default:null" json:"reserved"`
	StartTime  *time.Time `gorm:"type:datetime; default:null" json:"start_time"`
	EndTime    *time.Time `gorm:"type:datetime; default:null" json:"end_time"`
	SN         string     `gorm:"type:varchar(64); default:null" json:"sn"`
	ResourceId string     `gorm:"type:varchar(64); default:null" json:"resource_id"`
	Location   string     `gorm:"type:varchar(64); default:Beijing" json:"location"`
	Room       string     `gorm:"type:varchar(32); default:null" json:"room"`
	Comments   string     `gorm:"type:varchar(64); default:null" json:"comments"`
}

type ResourcePoolJob struct {
	models.ModelBase
	Name   string `gorm:"not null; type:varchar(64)" json:"name"`
	Data   string `json:"data"`
	Status uint   `gorm:"not null" json:"status"` // 0: running 1: success 2: fail
	UserID uint   `gorm:"not null" json:"user_id"`
}
