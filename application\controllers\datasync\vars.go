package datasync

import (
	"time"

	"irisAdminApi/application/controllers/mergerequest"

	"github.com/imroc/req/v3"
)

var SyncClient *req.Client

func InitClient() {
	SyncClient = req.C().
		SetCommonRetryCount(3).
		// Set the retry sleep interval with a commonly used algorithm: capped exponential backoff with jitter (https://aws.amazon.com/blogs/architecture/exponential-backoff-and-jitter/).
		SetCommonRetryBackoffInterval(1*time.Second, 5*time.Second).
		AddCommonRetryCondition(func(resp *req.Response, err error) bool {
			return err != nil
		}).
		SetTimeout(60 * time.Second)
	// if libs.Config.Debug {
	// 	SyncClient.DevMode().SetProxyURL("http://10.51.134.126:3128")
	// }
}

var FeiShuClient = mergerequest.FeiShuClient
