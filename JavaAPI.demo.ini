public static void main(String[] args) {
String url = "http://bugs.ruijie.com.cn/bug_switch/service/outinterface_submitBugInfo"
JSONObject json = new JSONObject();
json.put("os","一键收集专项");//操作系统
json.put("testMethod","场景测试");//测试方法
json.put("product","10.4(3b13)p2T14");//产品名称
json.put("testCase","OPJ2-cav质里专项-TP");//测试用例
json.put("pstlCasUserid", "wangchunping"); // PSTL
json.put("chargeCasUserid","wuzhensheng");//bug负责人
json.put("source","开发测试(单元测试)");//来源
json.put("testCaseNum","xxx_"+Math.random()+"");//测试用例编号
json.put("summary","自动提交BUG001"+Math.random());//bug简介
json.put("testTopo","测试拓扑");//测试拓扑
json.put("topoDesc","拓扑描述");//拓扑描述
json.put("debugMess","http://192.168.5.161:8080/resManage/");//debug信息(约定传入ur飞链接)
json.put("versionMess","版本信息");//版本信息
json.put("deviceUnderTestConfig","被测设备配置");//被测设备配置
json.put("repeat","有时重现");//可重复性
json.put("priority","Major");//严重性
json.put("severity","Normal");//优先级
json.put("legacyBug","否");// 遗留Bug
json.put("affectCaseCount","0");//影响用例执行数
json.put("testabilitySpecial","");//可测试性专题(操作系统是11.0开头的,且提交人员专业组包含测试时必填,否则不需要
json.put("locate","");//定位信息(操作系统是11.0开头的,且提交人员专业组包含测试时必填,否则不需要)
json.put("submitterCasUserid","admin");//提交者
json.put("ccusers","");//抄送者
json.put("pkIds", "35561");
json.put("bugdescription","1<p>1、说明预期结果:1</p><p><p><br/></p><p>2说明实际结果:1</p><p><br/></p><p>3、说明问题出现步骤:1</p><p><br/></p><p><br/></p>");//
json.put("oneKeyCollectionInfo","<p>1、说明预期结果:1</p><p><br/></p><<p>2、说明实际结果:1</p><p><br/></p><p>3、说明问题出现步骤:1</p><p><br/></p><p><br/></p>")
System.out.println("请求的JSON参数:===>\r\n"+json.tostrin(١));

JSONObject jsonobj = HttpUtils.post(url, json);
if (jsonObj != null) {
    System.out.println(jsonObj.get("message"));
    System.out.println(jsonObj.get("type");
}