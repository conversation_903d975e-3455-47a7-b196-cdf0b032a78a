package fileout

import (
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/cache"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/fileout/dautoauditfile"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

func GetAllAutoAuditFiles(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dautoauditfile.Response{}, ctx, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func DeleteAutoAuditFile(ctx iris.Context) {
	err := dao.Delete(&dautoauditfile.Response{}, ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	cache.GetAutoAuditFileCache()
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func CreateAutoAuditFile(ctx iris.Context) {
	Req := &dautoauditfile.AutoAduitFileReq{}
	if err := ctx.ReadJSON(Req); err != nil {
		logging.ErrorLogger.Errorf("create autoauditfile read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*Req)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	err := dao.Create(&dautoauditfile.Response{}, ctx, map[string]interface{}{
		"Name":         Req.Name,
		"Objdump":      Req.Objdump,
		"FileCheck":    Req.FileCheck,
		"FileMime":     Req.FileMime,
		"Ext":          Req.Ext,
		"CustomHeader": Req.CustomHeader,
		"CreatedAt":    time.Now(),
		"UpdatedAt":    time.Now(),
		"Size":         Req.Size,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	cache.GetAutoAuditFileCache()
	ctx.JSON(response.NewResponse(response.NoErr.Code, Req, response.NoErr.Msg))
	return
}

func UpdateAutoAuditFile(ctx iris.Context) {
	Req := &dautoauditfile.AutoAduitFileReq{}
	if err := ctx.ReadJSON(Req); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*Req)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	data := map[string]interface{}{
		"Name":         Req.Name,
		"Objdump":      Req.Objdump,
		"FileCheck":    Req.FileCheck,
		"FileMime":     Req.FileMime,
		"Ext":          Req.Ext,
		"CustomHeader": Req.CustomHeader,
		"UpdatedAt":    time.Now(),
		"Size":         Req.Size,
	}

	err := dao.Update(&dautoauditfile.Response{}, ctx, data)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	cache.GetAutoAuditFileCache()
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}
