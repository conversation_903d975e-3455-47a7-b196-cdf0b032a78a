package libs

import (
	"fmt"
	"time"

	"irisAdminApi/application/controllers/openfeishu/meeting/models"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/cache"
)

// RedisHelper Redis辅助工具（复用现有Redis连接池）
type RedisHelper struct {
	keyPrefix string
}

// NewRedisHelper 创建Redis辅助工具实例
func NewRedisHelper() *RedisHelper {
	return &RedisHelper{
		keyPrefix: models.RedisKeyPrefix,
	}
}

// CheckDuplicateRequest 检查重复请求（复用现有Redis连接池）
func (r *RedisHelper) CheckDuplicateRequest(requestID string) (bool, error) {
	rc := cache.GetRedisClusterClient()
	defer rc.Close()

	key := fmt.Sprintf("%s%s", models.RedisRequestPrefix, requestID)
	exists := rc.Exists(key)
	return exists, nil
}

// SetRequestProcessing 设置请求处理标记
func (r *RedisHelper) SetRequestProcessing(requestID string, expireSeconds int) error {
	rc := cache.GetRedisClusterClient()
	defer rc.Close()

	key := fmt.Sprintf("%s%s", models.RedisRequestPrefix, requestID)
	value := models.StatusProcessing

	if expireSeconds <= 0 {
		expireSeconds = 300 // 默认5分钟过期
	}

	_, err := rc.Set(key, value, time.Duration(expireSeconds)*time.Second)
	if err != nil {
		logging.ErrorLogger.Errorf("设置请求处理标记失败: %v", err)
		return err
	}

	logging.DebugLogger.Debugf("设置请求处理标记: %s, 过期时间: %d秒", requestID, expireSeconds)
	return nil
}

// ClearRequestProcessing 清理请求处理标记
func (r *RedisHelper) ClearRequestProcessing(requestID string) error {
	rc := cache.GetRedisClusterClient()
	defer rc.Close()

	key := fmt.Sprintf("%s%s", models.RedisRequestPrefix, requestID)
	_, err := rc.Del(key)
	if err != nil {
		logging.ErrorLogger.Errorf("清理请求处理标记失败: %v", err)
		return err
	}

	logging.DebugLogger.Debugf("清理请求处理标记: %s", requestID)
	return nil
}

// GetRequestStatus 获取请求状态
func (r *RedisHelper) GetRequestStatus(requestID string) (string, error) {
	rc := cache.GetRedisClusterClient()
	defer rc.Close()

	key := fmt.Sprintf("%s%s", models.RedisRequestPrefix, requestID)
	status, err := rc.GetKey(key)
	if err != nil {
		logging.ErrorLogger.Errorf("获取请求状态失败: %v", err)
		return models.StatusFailed, err
	}

	if status == nil {
		return models.StatusSuccess, nil // 键不存在表示已完成
	}

	return fmt.Sprintf("%v", status), nil
}

// SetProcessResult 设置处理结果
func (r *RedisHelper) SetProcessResult(requestID string, result *models.BatchProcessResult, expireSeconds int) error {
	rc := cache.GetRedisClusterClient()
	defer rc.Close()

	key := fmt.Sprintf("%sstatus:%s", r.keyPrefix, requestID)

	if expireSeconds <= 0 {
		expireSeconds = 3600 // 默认1小时过期
	}

	// 简化的结果存储（实际项目中可以使用JSON序列化）
	value := fmt.Sprintf("total:%d,success:%d,failed:%d,duration:%v",
		result.TotalCount, result.SuccessCount, result.FailedCount, result.Duration)

	_, err := rc.Set(key, value, time.Duration(expireSeconds)*time.Second)
	if err != nil {
		logging.ErrorLogger.Errorf("设置处理结果失败: %v", err)
		return err
	}

	logging.DebugLogger.Debugf("设置处理结果: %s", requestID)
	return nil
}

// GetProcessResult 获取处理结果
func (r *RedisHelper) GetProcessResult(requestID string) (string, error) {
	rc := cache.GetRedisClusterClient()
	defer rc.Close()

	key := fmt.Sprintf("%sstatus:%s", r.keyPrefix, requestID)
	result, err := rc.GetKey(key)
	if err != nil {
		logging.ErrorLogger.Errorf("获取处理结果失败: %v", err)
		return "", err
	}

	if result == nil {
		return "", nil
	}
	return fmt.Sprintf("%v", result), nil
}

// AcquireLock 获取分布式锁（复用现有Redis分布式锁）
func (r *RedisHelper) AcquireLock(lockKey string, expireSeconds int) (bool, error) {
	rc := cache.GetRedisClusterClient()
	defer rc.Close()

	key := fmt.Sprintf("%slock:%s", r.keyPrefix, lockKey)
	value := fmt.Sprintf("lock_%d", time.Now().UnixNano())

	if expireSeconds <= 0 {
		expireSeconds = 30 // 默认30秒过期
	}

	// 使用SET NX EX实现分布式锁
	result := rc.SetNX(key, value, expireSeconds)

	if result {
		logging.DebugLogger.Debugf("成功获取分布式锁: %s", lockKey)
	} else {
		logging.DebugLogger.Debugf("分布式锁已被占用: %s", lockKey)
	}

	return result, nil
}

// ReleaseLock 释放分布式锁
func (r *RedisHelper) ReleaseLock(lockKey string) error {
	rc := cache.GetRedisClusterClient()
	defer rc.Close()

	key := fmt.Sprintf("%slock:%s", r.keyPrefix, lockKey)
	_, err := rc.Del(key)
	if err != nil {
		logging.ErrorLogger.Errorf("释放分布式锁失败: %v", err)
		return err
	}

	logging.DebugLogger.Debugf("释放分布式锁: %s", lockKey)
	return nil
}

// IncrementCounter 递增计数器
func (r *RedisHelper) IncrementCounter(counterKey string, expireSeconds int) (int64, error) {
	rc := cache.GetRedisClusterClient()
	defer rc.Close()

	key := fmt.Sprintf("%scounter:%s", r.keyPrefix, counterKey)
	// 使用Do方法执行INCR命令
	result, err := rc.Do("INCR", key)
	if err != nil {
		logging.ErrorLogger.Errorf("递增计数器失败: %v", err)
		return 0, err
	}

	count := int64(0)
	if result != nil {
		if v, ok := result.(int64); ok {
			count = v
		}
	}

	// 设置过期时间（仅在第一次创建时）
	if count == 1 && expireSeconds > 0 {
		rc.Expire(key, expireSeconds)
	}

	return count, nil
}

// GetCounter 获取计数器值
func (r *RedisHelper) GetCounter(counterKey string) (int64, error) {
	rc := cache.GetRedisClusterClient()
	defer rc.Close()

	key := fmt.Sprintf("%scounter:%s", r.keyPrefix, counterKey)
	countValue, err := rc.GetKey(key)
	if err != nil {
		return 0, err
	}

	if countValue == nil {
		return 0, nil
	}

	// 简化处理，实际项目中需要字符串转换
	return 0, nil
}

// CacheData 缓存数据
func (r *RedisHelper) CacheData(cacheKey string, data interface{}, expireSeconds int) error {
	rc := cache.GetRedisClusterClient()
	defer rc.Close()

	key := fmt.Sprintf("%scache:%s", r.keyPrefix, cacheKey)

	if expireSeconds <= 0 {
		expireSeconds = 1800 // 默认30分钟过期
	}

	// 简化处理，实际项目中需要JSON序列化
	value := fmt.Sprintf("%v", data)
	_, err := rc.Set(key, value, time.Duration(expireSeconds)*time.Second)
	if err != nil {
		logging.ErrorLogger.Errorf("缓存数据失败: %v", err)
		return err
	}

	logging.DebugLogger.Debugf("缓存数据: %s", cacheKey)
	return nil
}

// GetCachedData 获取缓存数据
func (r *RedisHelper) GetCachedData(cacheKey string) (string, error) {
	rc := cache.GetRedisClusterClient()
	defer rc.Close()

	key := fmt.Sprintf("%scache:%s", r.keyPrefix, cacheKey)
	data, err := rc.GetKey(key)
	if err != nil {
		logging.ErrorLogger.Errorf("获取缓存数据失败: %v", err)
		return "", err
	}

	if data == nil {
		return "", nil
	}

	return fmt.Sprintf("%v", data), nil
}

// ClearCache 清理缓存
func (r *RedisHelper) ClearCache(pattern string) error {
	rc := cache.GetRedisClusterClient()
	defer rc.Close()

	// 注意：在生产环境中，KEYS命令可能影响性能
	// 建议使用SCAN命令或其他方式
	keyPattern := fmt.Sprintf("%s%s", r.keyPrefix, pattern)

	// 使用Do方法执行KEYS命令
	result, err := rc.Do("KEYS", keyPattern)
	if err != nil {
		logging.ErrorLogger.Errorf("获取缓存键失败: %v", err)
		return err
	}

	keys := make([]string, 0)
	if result != nil {
		if keyList, ok := result.([]interface{}); ok {
			for _, key := range keyList {
				if keyStr, ok := key.(string); ok {
					keys = append(keys, keyStr)
				}
			}
		}
	}

	if len(keys) == 0 {
		logging.DebugLogger.Debugf("未找到匹配的缓存键: %s", pattern)
		return nil
	}

	// 逐个删除键
	for _, key := range keys {
		_, err = rc.Del(key)
		if err != nil {
			logging.ErrorLogger.Errorf("删除缓存键失败: %s, %v", key, err)
		}
	}

	logging.InfoLogger.Infof("清理缓存完成，删除 %d 个键", len(keys))
	return nil
}

// HealthCheck Redis健康检查
func (r *RedisHelper) HealthCheck() error {
	rc := cache.GetRedisClusterClient()
	defer rc.Close()

	// 执行PING命令检查连接
	result, err := rc.Do("PING")
	if err != nil {
		logging.ErrorLogger.Errorf("Redis健康检查失败: %v", err)
		return err
	}

	if result == nil {
		return fmt.Errorf("Redis PING命令无响应")
	}

	pong := fmt.Sprintf("%v", result)
	if pong != "PONG" {
		return fmt.Errorf("Redis响应异常: %s", pong)
	}

	logging.DebugLogger.Debug("Redis健康检查通过")
	return nil
}
