package appmergerequest

import (
	"fmt"
	"strconv"
	"strings"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/appmergerequest/dappproject"

	"github.com/kataras/iris/v12"
)

func GetDeployTargetBranches(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, map[string]string{"err_msg": err.Error()}, response.ParamentErr.Msg))
		return
	}
	project := dappproject.Response{}
	err = project.Find(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	if ctx.FormValue("unit_package_id") == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if ctx.FormValue("work_package_id") == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	unitPackageID, _ := strconv.Atoi(ctx.FormValue("unit_package_id"))
	workPackageID, _ := strconv.Atoi(ctx.FormValue("work_package_id"))
	token := libs.Config.Gitlab.Token
	items := []string{}
	if token == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置gitlab token, 请联系管理员"))
		return
	}
	var result []*BranchResponse
	var errMsg MessageResponse
	var searchValue string
	switch workPackageID {
	case 191:
		searchValue = "test"
	case 192:
		searchValue = "test"
	case 193:
		searchValue = "develop"
	case 194:
		searchValue = ""
	default:
		searchValue = ""
	}
	if len(result) == 0 {
		page := 1
		for {
			var _result []*BranchResponse
			url := fmt.Sprintf("%s/api/%s/projects/%d/repository/branches?private_token=%s&search=%s&page=%d", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, unitPackageID, token, searchValue, page)
			resp, err := GitlabWebClient.R().SetResult(&_result).SetError(&errMsg).Get(url)
			if err != nil {
				ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error() + errMsg.Message}, response.SystemErr.Msg))
				return
			}
			if resp.IsSuccess() {
				page++
				if len(_result) > 0 {
					result = append(result, _result...)
				} else {
					break
				}
			} else {
				ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": errMsg.Message}, response.SystemErr.Msg))
				return
			}
		}
	}

	for _, _item := range result {
		if !libs.InArrayS(items, _item.Name) {
			switch workPackageID {
			case 191:
				if strings.HasPrefix(_item.Name, "test") {
					if _item.Name == "test" {
						items = append(items, _item.Name)
					}
				}
			case 192:
				if strings.HasPrefix(_item.Name, "test") {
					if _item.Name != "test" {
						items = append(items, _item.Name)
					}
				}
			case 193:
				if strings.HasPrefix(_item.Name, "develop") {
					if _item.Name == "develop" {
						items = append(items, _item.Name)
					}
				}
			case 194:
				items = append(items, _item.Name)
			default:
				items = append(items, _item.Name)
			}
		}
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, items, response.NoErr.Msg))
	return
}

func GetDeployBranches(ctx iris.Context) {
	search := ""
	id, err := dao.GetId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	token, _ := dao.GetGitlabToken(ctx)
	if token == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置gitlab token, 请前往个人资料添加gitlab token"))
		return
	}
	if ctx.FormValue("work_package_id") == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	workPackageID, _ := strconv.Atoi(ctx.FormValue("work_package_id"))
	if workPackageID == 194 {
		search = "develop"
	}

	var result []*BranchResponse
	var errMsg MessageResponse
	page := 1
	for {
		var _result []*BranchResponse
		url := fmt.Sprintf("%s/api/%s/projects/%d/repository/branches?private_token=%s&search=%s&page=%d", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, id, token, search, page)
		resp, err := GitlabWebClient.R().SetResult(&_result).SetError(&errMsg).Get(url)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error() + errMsg.Message}, response.SystemErr.Msg))
			return
		}
		if resp.IsSuccess() {
			page++
			if len(_result) > 0 {
				result = append(result, _result...)
			} else {
				break
			}
		} else {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": errMsg.Message}, response.SystemErr.Msg))
			return
		}
	}
	items := []string{}
	for _, _item := range result {
		if !libs.InArrayS(items, _item.Name) {
			switch workPackageID {
			case 194:
				if strings.HasPrefix(_item.Name, "develop") {
					if _item.Name == "develop" {
						items = append(items, _item.Name)
					}
				}
			default:
				items = append(items, _item.Name)
			}
		}
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, items, response.NoErr.Msg))
	return
}
