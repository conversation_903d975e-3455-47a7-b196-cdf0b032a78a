package release

import "irisAdminApi/application/models"

type ReleaseBranch struct {
	models.ModelBase
	ReleaseProjectID uint   `gorm:"not null"`
	Status           uint   `gorm:"not null"` // 0:申请中, 1:评审结束, 2:取消
	UnitPackage      uint   `gorm:"not null"`
	WorkPackage      uint   `gorm:"not null"`
	BranchType       uint   `gorm:"not null"` // 1:code, 2:docs
	HandleType       uint   `gorm:"not null"` // 1:新建分支
	BaseBranch       string `gorm:"not null; type:varchar(60)"`
	BaseVerison      string `gorm:"not null; type:varchar(60)"`
	NewBranch        string `gorm:"not null; type:varchar(60)"`
	BranchComment    string `gorm:"not null"`
	UserID           uint   `gorm:"not null" json:"user_id"`
	Uuid             string `gorm:"not null; type:varchar(100)"`
	AutoCreate       uint   `gorm:"not null; default:0"` //  0: init ,1:created,2:failed
	MrControlStatus  uint   `gorm:"not null" json:"mr_control_status"`
}

type ReleaseWorkPackage struct {
	models.ModelBase
	Name string `gorm:"not null; type:varchar(60)"`
}

type ReleaseBranchAuditor struct {
	models.ModelBase
	ReleaseBranchID uint `gorm:"primaryKey"`
	UserID          uint `gorm:"primaryKey"`
}

type ReleaseBranchWrite struct {
	models.ModelBase
	ReleaseBranchID uint `gorm:"primaryKey"`
	UserID          uint `gorm:"primaryKey"`
}

type ReleaseBranchNotice struct {
	models.ModelBase
	ReleaseBranchID uint `gorm:"primaryKey"`
	UserID          uint `gorm:"primaryKey"`
}

type ReleaseBranchAudit struct {
	models.ModelBase
	Status          uint
	Comment         string `gorm:"not null; type:varchar(200)"`
	ReleaseBranchID uint   `gorm:"primaryKey"`
	UserID          uint   `gorm:"primaryKey"`
}

type ReleaseBranchSwitch struct {
	models.ModelBase
	ReleaseProjectID uint   `gorm:"primaryKey"`
	UserID           uint   `gorm:"not null"`
	AuditorID        uint   `gorm:"not null"`
	Content          string `gorm:"not null; type:varchar(2000)"`
	Comment          string `gorm:"not null; type:varchar(2000)"`
	Status           uint   `gorm:"not null; default: 0"`
	Uuid             string `gorm:"not null; type:varchar(100)"`
	Result           string `gorm:"not null"`
}
