package transluckydraw

import (
	"errors"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/service/dao/luckydraw/dluckydrawcardconfig"
	"irisAdminApi/service/dao/luckydraw/dluckydrawlotteryrecord"
	"math/rand"
	"time"

	"gorm.io/gorm"
)

func CreateDraw(EmployeeID string, Name string, CityID int) (err error, randomNumber int) {
	db := easygorm.GetEasyGormDb()
	randomNumber = 0
	cityName := map[int]string{
		1: "福州",
		2: "北京",
		3: "成都",
	}
	err = db.Transaction(func(tx *gorm.DB) error {
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		cards := []dluckydrawcardconfig.Response{}
		if err := tx.Model(dluckydrawcardconfig.Model()).Where("city = ? AND quantity > 0", CityID).Find(&cards).Error; err != nil {
			return err
		}
		if len(cards) == 0 {
			return errors.New("生肖卡已分配完毕")
		}
		src := rand.NewSource(time.Now().UnixNano())
		r := rand.New(src)
		selectedIndex := r.Intn(len(cards))
		selectedCard := cards[selectedIndex]
		// 减少卡片数量
		if err := tx.Model(dluckydrawcardconfig.Model()).Where("quantity > 0").Where("id = ? ", selectedCard.ID).Update("quantity", gorm.Expr("quantity - ?", 1)).Error; err != nil {
			return err
		}
		randomNumber = int(selectedCard.Zodiac)
		// 创建抽奖记录
		LuckyObjects := []map[string]interface{}{}
		LuckyObjects = append(LuckyObjects, map[string]interface{}{
			"CreatedAt":  time.Now(),
			"WorkNumber": EmployeeID,
			"Name":       Name,
			"ZodiacName": selectedCard.Name,
			"Zodiac":     randomNumber,
			"CityName":   cityName[CityID],
			"City":       CityID,
			"UpdatedAt":  time.Now(),
		})
		if err := tx.Model(dluckydrawlotteryrecord.Model()).Create(&LuckyObjects).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return err, randomNumber
	}
	return nil, randomNumber
}
