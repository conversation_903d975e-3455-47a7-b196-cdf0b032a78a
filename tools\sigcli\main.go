package main

import (
	"archive/tar"
	"errors"
	"flag"
	"fmt"
	"io"
	"io/fs"
	"io/ioutil"
	"irisAdminApi/application/libs"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"runtime"

	"github.com/imroc/req/v3"
)

var sigServer = "http://*************/sig-api"
var client = req.C(). // Use C() to create a client and set with chainable client settings.
			SetTimeout(60 * time.Second)

// DevMode()

func Tar(source, target string) error {
	filename := filepath.Base(source)
	target = filepath.Join(target, fmt.Sprintf("%s.tar", filename))

	tarfile, err := os.Create(target)
	if err != nil {
		return err
	}
	defer tarfile.Close()

	tarball := tar.NewWriter(tarfile)
	defer tarball.Close()

	// info, err := os.Stat(source)
	// if err != nil {
	// 	return nil
	// }

	// var baseDir string
	// if info.IsDir() {
	// 	baseDir = filepath.Base(source)
	// }

	return filepath.Walk(source, func(path string, info os.FileInfo, err error) error {
		if info.IsDir() {
			return nil
		}

		header, err := tar.FileInfoHeader(info, info.Name())
		if err != nil {
			return err
		}

		// header.Name = strings.TrimPrefix(path, source)
		header.Name = filepath.Base(path)
		if header.Name != "." {
			if err := tarball.WriteHeader(header); err != nil {
				return err
			}
			file, err := os.Open(path)
			if err != nil {
				return err
			}
			defer file.Close()
			_, err = io.Copy(tarball, file)
			return err
		}
		return nil
	})
}

type PostSigJobsReponse struct {
	Code int `json:"code"`
	Data struct {
		ID    int    `json:"id"`
		JobID string `json:"job_id"`
	} `json:"data"`
	Msg string `json:"msg"`
}

type SigJobReponse struct {
	Code int `json:"code"`
	Data struct {
		ID             int    `json:"id"`
		JobID          string `json:"job_id"`
		OutputFileName string `json:"output_file_name"`
		Status         int    `json:"status"`
	} `json:"data"`
	Msg string `json:"msg"`
}

var withLibMap = map[bool]string{
	true:  "yes",
	false: "no",
}

func PostSigJobs(fp, output, outputFileName string, withLib bool) error {

	ret := PostSigJobsReponse{}
	url := fmt.Sprintf("%s/api/v1/sig/jobs", sigServer)
	resp, err := client.R().
		SetFile("file", fp).
		SetFormData(map[string]string{
			"output_file_name": outputFileName,
			"with_lib":         withLibMap[withLib],
		}).Post(url)
	if err != nil {
		return err
	}
	if resp.StatusCode != 200 {
		return errors.New("请求失败")
	}

	err = resp.Into(&ret)
	if err != nil {
		return err
	}
	if ret.Code != 20000 {
		return errors.New("提交加密作业失败")
	}
	id := ret.Data.ID
	jobID := ret.Data.JobID
	fmt.Println("提交加密作业成功...")
	fmt.Println("作业ID:", jobID)

	for {
		fmt.Println("正在加密...")
		status, _ := GetSigJob(id)
		if status == 1 {
			fmt.Println("加密成功...")
			fmt.Println("正在下载加密文件...")
			DownloadSigFile(id, output, outputFileName)
			return nil
		} else if status == 2 {
			return errors.New("加密作业执行失败，请重试")
		}
		time.Sleep(5 * time.Second)
	}
}

func GetSigJob(id int) (int, error) {
	ret := SigJobReponse{}
	url := fmt.Sprintf("%s/api/v1/sig/jobs/%v", sigServer, id)
	resp, err := client.R().Get(url)
	if err != nil {
		return 0, err
	}
	if resp.StatusCode != 200 {
		return 0, errors.New("请求失败")
	}
	err = resp.Into(&ret)
	if err != nil {
		return 0, err
	}
	if ret.Code != 20000 {
		return 0, errors.New("提交加密作业失败")
	}
	return ret.Data.Status, nil
}

func DownloadSigFile(id int, output, outputFileName string) error {
	url := fmt.Sprintf("%s/api/v1/sig/jobs/%v/%s", sigServer, id, outputFileName)
	resp, err := client.R().SetOutputFile(filepath.Join(output, outputFileName)).Get(url)
	if err != nil {
		return err
	}
	if resp.StatusCode != 200 {
		return errors.New("请求失败")
	}
	fmt.Println(outputFileName, "下载成功")
	fp, _ := filepath.Abs(filepath.Join(output, outputFileName))
	fmt.Println("加密文件位置：", fp)

	return nil
}

func help() {
	fmt.Println("Usage:")
	fmt.Println("sigcli -s http://*************/sig -d /tmp/test/ -t sig_20231219.1337_sig.zip -o ./")
}

func CheckVersion() {
	goos := runtime.GOOS
	toolName := filepath.Base(os.Args[0])
	md5, err := libs.GetFileMd5(filepath.Join(libs.CWD(), toolName))
	if err != nil {
		fmt.Println("文件异常,跳过更新版本...")
		return
	}
	//
	url := fmt.Sprintf("%s/api/v1/sig/tools/%s/check", sigServer, goos)

	resp, err := client.R().Get(url)
	if err != nil {
		fmt.Println("网络异常,跳过更新版本...")
		return
	}
	if resp.StatusCode != 200 {
		fmt.Println("服务器异常,跳过更新版本...")
		return
	}

	if strings.Contains(resp.String(), "code") {
		fmt.Println("检查版本更新错误,跳过更新版本...")
		fmt.Println(resp.String())
		return
	}

	if md5 != resp.String() {
		fmt.Println("检测到新版本,正在更新版本...")
		Update()
	} else {
		fmt.Println("已经是最新版本,跳过更新...")
	}

	return
}

func Update() error {
	goos := runtime.GOOS
	toolName := filepath.Base(os.Args[0])

	err := os.Rename(filepath.Join(libs.CWD(), toolName), filepath.Join(libs.CWD(), toolName+"_tmp"))
	if err != nil {
		fmt.Print(err)
		return err
	}

	url := fmt.Sprintf("%s/api/v1/sig/tools/%s/sigcli", sigServer, goos)
	resp, err := client.R().SetOutputFile(filepath.Join(libs.CWD(), toolName)).Get(url)
	if err != nil {
		return err
	}
	if resp.StatusCode != 200 {
		return errors.New("请求失败")
	}
	fmt.Println("更新成功,请重新运行工具...")

	if goos == "windows" {
		daemon := fmt.Sprintf("del %s &", filepath.Join(libs.CWD(), toolName+"_tmp"))
		_ = exec.Command("cmd.exe", "/C", daemon).Start()
	} else {
		os.Remove(filepath.Join(libs.CWD(), toolName+"_tmp"))
	}
	os.Exit(0)
	return nil
}

func main() {

	var (
		customServer, dir, targetName, output string
		force, update, withLib                bool
	)

	// var err error
	flag.StringVar(&customServer, "s", "", fmt.Sprintf("指定服务器地址, 默认为http://*************/sig"))
	flag.StringVar(&dir, "d", "", "要加密的目录,必填")
	// flag.StringVar(&zip, "z", "", "要加密的压缩文件，仅支持zip/tar，目录与文件只允许指定一个")
	flag.StringVar(&targetName, "t", "", "加密后文件名,必填")
	flag.StringVar(&output, "o", "./", "打包及加密后文件存放目录")
	flag.BoolVar(&withLib, "with_lib", false, "是否带上动态库")
	flag.BoolVar(&force, "f", false, "忽略目标目录检查，即使目录为空仍然打包")
	flag.BoolVar(&update, "u", true, "使用 -u=false 忽略更新")
	flag.Parse()

	if update {
		CheckVersion()
	}

	output, err := filepath.Abs(output)
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
	if targetName == "" {
		fmt.Println("加密后文件名不能为空")
		os.Exit(1)
	}

	if dir == "" {
		fmt.Println("请指定目录")
		os.Exit(1)
	}

	if dir != "" {
		if dir == output {
			fmt.Println("指定目录与输出目录不能相同")
			os.Exit(1)
		}
		filename := filepath.Base(dir)
		if _, err := os.Stat(dir); errors.Is(err, fs.ErrNotExist) {
			fmt.Println("目标目录不存在")
			os.Exit(1)
		}
		if !force {
			fis, err := ioutil.ReadDir(dir)
			if err != nil {
				fmt.Println(err.Error())
				os.Exit(1)
			}
			if len(fis) == 1 && fis[0].IsDir() {
				fmt.Println("目标目录下只有一个目录，请确认路径是否正确")
				os.Exit(1)
			} else if len(fis) == 0 {
				fmt.Println("目标目录为空，请确认路径是否正确")
				os.Exit(1)
			}
		}

		target := filepath.Join(output, fmt.Sprintf("%s.tar", filename))
		if _, err := os.Stat(target); err == nil || errors.Is(err, fs.ErrExist) {
			for i := 0; i < 10; i++ {
				fmt.Printf("%s 已经存在，%v秒后删除并打包\n", target, 10-i)
				time.Sleep(1 * time.Second)
			}
		}

		os.RemoveAll(target)

		fmt.Printf("目标目录: %s\n", dir)
		fmt.Printf("打包生成目录: %s\n", output)
		fmt.Printf("加密文件下载目录: %s\n", output)
		fmt.Println("开始打包...")
		if err := Tar(dir, output); err != nil {
			fmt.Println(err.Error() + "\n")
			os.Exit(1)
		}
		fmt.Println("开始提交加密作业...")
		if err := PostSigJobs(target, output, targetName, withLib); err != nil {
			fmt.Println(err.Error())
			os.Exit(1)
		}
	}
}
