package dreleaseodm

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/release"
	"irisAdminApi/service/dao/release/dreleaseattr"
)

const ModelName = "ODM发布管理"

type Response struct {
	ID                    uint                             `json:"id"`
	ProjectName           string                           `json:"project_name"`
	ProductModel          string                           `json:"product_model"`
	ReleaseAttrID         uint                             `json:"release_attr_id"`
	ReleasedAt            string                           `json:"released_at"`
	SystemSoftwareVersion string                           `json:"system_software_version"`
	PackageName           string                           `json:"package_name"`
	ReleaseLink           string                           `json:"release_link"`
	Comment               string                           `json:"comment"`
	UserID                uint                             `json:"user_id"`
	Uuid                  string                           `json:"uuid"`
	ReleaseAttr           *dreleaseattr.ReleaseReleaseAttr `gorm:"-" json:"release_attr"`
	Archive               uint                             `json:"archive"`
}

type ListResponse struct {
	Response
}

type Request struct {
	ProjectName           string `json:"project_name"`
	ProductModel          string `json:"product_model"`
	ReleaseAttrID         uint   `json:"release_attr_id"`
	ReleasedAt            string `json:"released_at"`
	SystemSoftwareVersion string `json:"system_software_version"`
	PackageName           string `json:"package_name"`
	ReleaseLink           string `json:"release_link"`
	Comment               string `json:"comment"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *release.ReleaseReleaseODM {
	return &release.ReleaseReleaseODM{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	GetDetails(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	GetDetail(u)
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	GetDetail(u)
	return nil
}

func FindReleaseInIds(ids []uint) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id in ?", ids).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return items, err
	}
	GetDetails(items)
	return items, nil
}

func GetDetail(release *Response) {
	attr := dreleaseattr.ReleaseReleaseAttr{}
	if err := attr.Find(release.ReleaseAttrID); err != nil {
		logging.ErrorLogger.Errorf("get status get err ", err)
	}
	release.ReleaseAttr = &attr

}

func GetDetails(items []*ListResponse) {

	attrs, _ := dreleaseattr.FindAll()
	var attrMap = make(map[uint]*dreleaseattr.ReleaseReleaseAttr)
	for _, attr := range attrs {
		attrMap[attr.ID] = attr
	}

	for _, item := range items {
		item.ReleaseAttr = attrMap[item.ReleaseAttrID]
	}

}

func AllArchiveReleases(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Where("archive = 1")
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	GetDetails(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func AllReleaseodms(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	GetDetails(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}
