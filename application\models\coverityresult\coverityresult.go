package coverityresult

import "irisAdminApi/application/models"

type CoverityResult struct {
	models.ModelBase
	Project          string `gorm:"index: idx_result; not null; type:varchar(100)" json:"project"`
	Product          string `gorm:"index: idx_result; not null; type:varchar(100)" json:"product"`
	CID              uint   `gorm:"index: idx_result; not null" json:"cid"`
	Mergekey         string `gorm:"index: idx_result; not null; type:varchar(100)" json:"mergekey"`
	Status           string `gorm:"index: idx_result; not null; type:varchar(100)" json:"status"`
	ComponentName    string `gorm:"index: idx_result; not null; type:varchar(100)" json:"component_name"`
	CheckerName      string `gorm:"index: idx_result; not null; type:varchar(100)" json:"checker_name"`
	EventDescription string `gorm:"not null" json:"event_description"`
	ContentsMD5      string `gorm:"index: idx_result; not null; type:varchar(100)" json:"contents_md5"`
	FilePathName     string `gorm:"not null; type:varchar(300)" json:"file_path_name"`
	LineNumber       uint   `gorm:"index: idx_result; not null" json:"line_number"`
	ReportFileName   string `gorm:"index: idx_result; not null; type:varchar(100)" json:"report_file_name"`
}

type CoverityRule struct {
	models.ModelBase
	CheckerName string `gorm:"not null; type:varchar(100)" json:"checker_name"`
	EpgLevel    string `gorm:"not null; type:varchar(100)" json:"epg_level"`
}

type CoverityReportHistory struct {
	models.ModelBase
	Filename string `gorm:"not null; type:varchar(100)" json:"filename"`
	Status   bool   `gorm:"not null; default:false" json:"status"`
}
