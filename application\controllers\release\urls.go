package release

import (
	"irisAdminApi/application/controllers/buildfarm"
	"irisAdminApi/application/controllers/mergerequest"
	"irisAdminApi/application/middleware"

	"github.com/kataras/iris/v12"
)

var Party = func(party iris.Party) {
	party.Get("/pms_project_role", GetPmsProjectRoles).Name = "获取所有项目角色"
	party.Get("/gcov/schedule/start", StartGcovSchedule).Name = "启动所有计划任务"
	party.Post("/GetReleaseArchivedData", GetReleaseArchivedData).Name = "获取版本归档数据(接口)"
	party.Get("/branches", GetBranches).Name = "获取分支列表"
	party.Get("/basebranch/{id:uint}", GetBaseBranch).Name = "获取基线分支名"
	party.Get("/releases/archived", GetAllArchiveReleases).Name = "获取已归档发布列表"
	party.Get("/projects/tree", GetProjectTreesV3).Name = "获取项目树"
	party.Get("/project", GetProjects).Name = "获取项目列表"

	party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) // 登录验证

	party.Get("/branch/tree", GetBranchTreesV2).Name = "获取分支树"
	party.Post("/project", CreateProject).Name = "创建项目"
	party.Get("/project/{id:uint}/config", GetProjectConfig).Name = "获取项目配置"
	party.Post("/project/{id:uint}/config", CreateOrUpdateProjectConfig).Name = "创建或者更新项目配置"
	party.Get("/project/audited", GetAuditedProjects).Name = "获取项目列表"
	party.Get("/project/audit", GetAuditProjects).Name = "获取项目列表"
	party.Get("/project/mine", GetMyProjects).Name = "获取项目列表"
	party.Get("/project/{id:uint}", GetProject).Name = "查看项目详情"
	party.Post("/project/{id:uint}", UpdateProject).Name = "更新项目详情"
	party.Post("/project/{id:uint}/branches/close", BatchBrachClose).Name = "关闭仓库权限"
	party.Post("/project/{id:uint}/branches/open", BatchBrachOpen).Name = "打开仓库权限"
	party.Get("/project/{id:uint}/branches/status", BatchBrachStatus).Name = "查询仓库权限"
	party.Post("/handlebranch/batch", BatchBrachStatus).Name = "分支批量操作"
	party.Post("/branches", CreateBranch).Name = "创建分支"

	party.Get("/project/{id:uint}/mrstatus", GetMrControlStatusAll).Name = "查询项目下的分支和工作包的MR权限"
	party.Post("/project/{id:uint}/open", ProjecMrControlStatuOpen).Name = "项目MR权限设置为打开"
	party.Post("/project/{id:uint}/close", ProjectMrControlStatuClose).Name = "项目MR权限设置为关闭"
	party.Post("/project/{id:uint}/control", ProjectMrControlStatuControl).Name = "项目MR权限设置为管控"
	party.Get("/branches/{id:uint}/mrstatus", GetBranchesMrControlStatus).Name = "查询分支的MR权限"
	party.Post("/handlebranchmrstatus/{id:uint}", HandleBranchMrControlStatus).Name = "分支MR权限操作"
	party.Get("/branches/getworkpackages", GetMRWorkPackages).Name = "获取工作包MR列表"
	party.Get("/workpackages/{id:uint}/mrstatus", GetWorkPackageMrControlStatus).Name = "查询工作包的MR权限"
	party.Post("/handleworkpackagemrstatus/{id:uint}", HandleWorkPackageMrControlStatus).Name = "工作包MR权限操作"
	party.Get("/branches/getMRreviews", mergerequest.GetMRReviews).Name = "获取MR管控评审列表"
	party.Get("/branches/MRreview/{id:uint}", mergerequest.GetMRReview).Name = "查看MR管控评审详情"
	party.Post("/branches/handleMRreview/{id:uint}", mergerequest.HandleMrControlReview).Name = "MR管控评审操作"

	party.Get("/branches/audit", GetAuditBranches).Name = "获取待评审分支列表"
	party.Get("/branches/audited", GetAuditedBranches).Name = "获取已评审分支列表"
	party.Get("/branches/mine", GetMyBranches).Name = "获取分支列表"
	party.Get("/branches/{id:uint}", GetBranch).Name = "查看分支详情"
	party.Post("/branches/{id:uint}", UpdateBranch).Name = "更新分支详情"
	party.Post("/branches/{id:uint}/audit", CreateBranchAudit).Name = "创建分支评审"
	party.Post("/branches/{id:uint}/auditbypm", AuditBranchByPM).Name = "PM一键通过"
	party.Get("/branches/{id:uint}/audit", GetBranchAudits).Name = "获取分支评审"
	party.Get("/unitpackages", buildfarm.GetEnableProjects).Name = "获取组件包列表"
	party.Get("/statuses", GetStatuses).Name = "获取状态列表"
	party.Get("/types", GetTypes).Name = "获取类型列表"
	party.Get("/type/{id:uint}/attrs", GetType).Name = "获取属性列表"
	party.Get("/classes", GetClasses).Name = "获取分类列表"
	party.Get("/directions", GetDirections).Name = "获取市场方向列表"
	party.Get("/demandsrcs", GetDemandSrcs).Name = "获取需求来源列表"
	party.Get("/attachment/{id:uint}", Download).Name = "下载附件"
	party.Get("/releases/{id:uint}", GetRelease).Name = "获取发布详情"
	party.Delete("/releases/{id:uint}", DeleteRelease).Name = "删除发布记录"
	party.Get("/releases", GetReleases).Name = "获取发布列表"

	party.Post("/releases", CreateRelease).Name = "创建发布信息"
	party.Post("/{id:uint}/manufacture", UpdateManufacture).Name = "更新下生产状态"
	party.Get("/attrs", GetReleaseAttrs).Name = "获取发布属性列表"
	party.Get("/buildnames", GetBuildNames).Name = "获取编译工程名称列表"
	party.Get("/productmodels", GetProductModels).Name = "获取产品型号列表"
	party.Get("/basebranches", GetBaseBranches).Name = "获取基线分支列表"
	party.Get("/basecommits", GetBaseBranchCommits).Name = "获取基线分支CommitID列表"
	party.Get("/{id:uint}/branchinfo", GetBranchInfo).Name = "获取分支信息"
	party.Get("/{id:uint}/compileinfo", GetCompileInfo).Name = "获取编译信息"
	party.Get("/{id:uint}/archive", ArchiveRelease).Name = "版本发布归档"
	party.Get("/releasebranches", GetReleaseBranches).Name = "获取发布分支列表"
	party.Get("/releaseversions", GetReleaseVersion).Name = "获取发布版本号列表"
	party.Post("/handlebranch/{id:uint}", HandleBranchPermition).Name = "分支操作"

	party.Get("/project/{id:uint}/newbranches", GetNewBranches).Name = "获取新分支列表"
	party.Get("/branches/switches", GetSwitchBranches).Name = "获取分支切换记录"
	party.Post("/branches/switches", CreateSwitchBranches).Name = "创建分支切换记录"

	party.Get("/releasesODM", GetReleasesODM).Name = "获取ODM发布列表"
	party.Get("/releasesODM/archived", GetAllArchiveReleasesODM).Name = "获取ODM已归档发布列表"
	party.Post("/releasesODM", CreateReleaseODM).Name = "创建ODM发布信息"
	party.Get("/releasesODM/{id:uint}", GetReleaseODM).Name = "获取ODM发布详情"
	party.Delete("/releasesODM/{id:uint}", DeleteReleaseODM).Name = "删除ODM发布记录"
	party.Get("/{id:uint}/archiveODM", ArchiveReleaseODM).Name = "ODM版本发布归档"
	party.Get("/productfamily", GetProductFamily).Name = "获取产品系列数据"
	party.Get("/getBIproductmodels", GetBIProductModel).Name = "获取BI产品型号数据"
	party.Post("/updatereleasesODM/{id:uint}", UpdateReleaseODM).Name = "更新ODM发布信息"
	party.Get("/releasesODM/exportodmreleases", ExportReleaseODM).Name = "导出ODM发布信息"
}
