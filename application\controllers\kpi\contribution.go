package kpi

import (
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/kpi/dcontribution"
	"irisAdminApi/service/dao/kpi/dcontributionpoint"
	"irisAdminApi/service/dao/kpi/dcontributionreview"
	"irisAdminApi/service/dao/kpi/dcontributiontype"
	"irisAdminApi/service/dao/kpi/dcontributionversion"
	"irisAdminApi/service/dao/user/duserdepartment"
	"irisAdminApi/service/transaction/kpi/transcontribution"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

func GetContributions(ctx iris.Context) {
	// id, err := dao.GetAuthId(ctx)
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dcontributionversion.ListResponse{}, ctx, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetContribution(ctx iris.Context) {
	info := dcontribution.Response{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

func CreateContribution(ctx iris.Context) {
	// program, err := buildfarmProject.FindProduct()
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("create release project get err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未找到工程文件，请联系管理员"))
	// 	return
	// }

	userId, _ := dao.GetAuthId(ctx)
	request := &dcontribution.Request{}
	if err := ctx.ReadJSON(request); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	if len(request.Points) == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "请输入贡献分详情"))
		return
	}
	uuid := libs.GetUUID()

	contributionObject := map[string]interface{}{
		"CreatedAt":          time.Now(),
		"Description":        request.Description,
		"ContributedAt":      request.ContributedAt,
		"ContributionTypeID": request.ContributionTypeID,
		"UserID":             userId,
		// "Status":             request.Status,
		"Status":         1,
		"Uuid":           uuid,
		"ContributionID": 0,
	}
	pointObjects := []map[string]interface{}{}
	contributorIds := []uint{}
	for _, point := range request.Points {
		userDepartment, err := duserdepartment.FindByUserId(point.ContributorID)
		if err != nil {
			logging.ErrorLogger.Errorf("create user get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		object := map[string]interface{}{
			"ContributorID": point.ContributorID,
			"DepartmentID":  userDepartment.DepartmentID,
			"Point":         point.Point,
			"CreatedAt":     time.Now(),
		}
		pointObjects = append(pointObjects, object)
		contributorIds = append(contributorIds, point.ContributorID)
	}

	// contributors, err := duser.FindInUintIds(contributorIds)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("create user get err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
	// 	return
	// }

	reviewObjects := []map[string]interface{}{}
	// for _, contributor := range contributors {
	// 	reviewers, err := duser.FindReviewers(contributor.Id)
	// 	if err != nil || len(reviewers) == 0 {
	// 		logging.ErrorLogger.Errorf("create user get err ", err)
	// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("未找到 %s 的组长", contributor.Name)))
	// 		return
	// 	}
	// 	object := map[string]interface{}{
	// 		"ReviewerID": reviewers[0].Id,
	// 		"CreatedAt":  time.Now(),
	// 		"Judge":      0,
	// 	}
	// 	reviewObjects = append(reviewObjects, object)
	// }
	err := transcontribution.CreateContributionTransaction(contributionObject, pointObjects, reviewObjects)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, request, response.NoErr.Msg))
	return
}

func UpdateContribution(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}

	info := dcontribution.Response{}
	err = dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	request := &dcontribution.Request{}
	if err := ctx.ReadJSON(request); err != nil {
		logging.ErrorLogger.Errorf("create approval read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	uuid := libs.GetUUID()
	contributionObject := map[string]interface{}{
		"CreatedAt":          time.Now(),
		"Description":        request.Description,
		"ContributedAt":      request.ContributedAt,
		"ContributionTypeID": request.ContributionTypeID,
		"UserID":             uId,
		// "Status":             request.Status,
		"Status":         1,
		"Uuid":           uuid,
		"ContributionID": info.ID,
	}
	pointObjects := []map[string]interface{}{}
	contributorIds := []uint{}
	for _, point := range request.Points {
		userDepartment, err := duserdepartment.FindByUserId(point.ContributorID)
		if err != nil {
			logging.ErrorLogger.Errorf("create user get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		object := map[string]interface{}{
			"ContributorID": point.ContributorID,
			"DepartmentID":  userDepartment.DepartmentID,
			"Point":         point.Point,
			"CreatedAt":     time.Now(),
		}
		pointObjects = append(pointObjects, object)
		contributorIds = append(contributorIds, point.ContributorID)
	}
	// contributors, err := duser.FindInUintIds(contributorIds)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("create user get err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
	// 	return
	// }

	reviewObjects := []map[string]interface{}{}
	// for _, contributor := range contributors {
	// 	reviewers, err := duser.FindReviewers(contributor.Id)
	// 	if err != nil || len(reviewers) == 0 {
	// 		logging.ErrorLogger.Errorf("create user get err ", err)
	// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("未找到 %s 的组长", contributor.Name)))
	// 		return
	// 	}
	// 	object := map[string]interface{}{
	// 		"ReviewerID": reviewers[0].Id,
	// 		"CreatedAt":  time.Now(),
	// 		"Judge":      0,
	// 	}
	// 	reviewObjects = append(reviewObjects, object)
	// }
	err = transcontribution.UpdateContributionTransaction(info.ID, contributionObject, pointObjects, reviewObjects)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, request, response.NoErr.Msg))
	return
}

func UpdateReview(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}

	request := &dcontributionreview.Request{}
	if err := ctx.ReadJSON(request); err != nil {
		logging.ErrorLogger.Errorf("create approval read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	reviewObject := map[string]interface{}{
		"UpdatedAt":  time.Now(),
		"Judge":      request.Judge,
		"Comment":    request.Comment,
		"ReviewerID": uId,
	}

	err = transcontribution.UpdateReviewTransaction(id, reviewObject)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, request, response.NoErr.Msg))
	return
}

func GetReview(ctx iris.Context) {
	info := dcontributionreview.Response{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

func GetPoints(ctx iris.Context) {
	contributionId, _ := dao.GetId(ctx)
	info, err := dcontributionpoint.FindByContributionID(contributionId)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

func GetReviews(ctx iris.Context) {
	contributionId, _ := dao.GetId(ctx)
	info, err := dcontributionreview.FindByContributionID(contributionId)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

func GetMyReviews(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	name := ctx.FormValue("name")
	judge := ctx.FormValue("judge")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dcontributionversion.FindAllReviewsByUserID(uId, judge, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetContributionTypes(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dcontributiontype.Response{}, ctx, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetMyContributions(ctx iris.Context) {
	uid, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dcontributionversion.AllByUserID(uid, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}
