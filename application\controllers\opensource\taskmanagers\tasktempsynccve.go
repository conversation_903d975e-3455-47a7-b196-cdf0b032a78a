package taskmanagers

import (
	"fmt"
	"strings"
	"time"

	"github.com/johnfercher/maroto/pkg/color"
	"github.com/johnfercher/maroto/pkg/consts"
	"github.com/johnfercher/maroto/pkg/pdf"
	"github.com/johnfercher/maroto/pkg/props"
	"k8s.io/client-go/util/workqueue"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/opensource"
	"irisAdminApi/service/dao/opensource/dcomponent"
	"irisAdminApi/service/dao/opensource/dcomponenttempcheck"
)

const customArialFontName = "CustomArial"
const customArialFontFile = "./files/opensource/fonts/arial-unicode-ms.ttf"
const componentTempCheckPdfFileFormat = "./files/opensource/tempcheck/%d.pdf"

var TempSyncCVETaskManager *tempSyncCVETaskManager

func InitTempSyncCVETaskManager() {
	TempSyncCVETaskManager = newTempSyncCVETaskManager()
	go func() {
		if componentTempChecksRes, err := dcomponenttempcheck.ListUnfinishedComponentTempChecks(); err == nil {
			for _, componentTempCheckRes := range componentTempChecksRes {
				logging.DebugLogger.Debugf("添加组件预查任务, 组件名-<%d>, 组件版本-<%s>", componentTempCheckRes.ComponentName, componentTempCheckRes.ComponentName)
				TempSyncCVETaskManager.AddComponentTempSyncTask(componentTempCheckRes)
			}
		}
	}()
	TempSyncCVETaskManager.run()
}

type tempSyncCVETaskManager struct {
	Queue workqueue.Interface
}

func tempSyncCVE(componentName, componentVersion string, pageNum, total int) [][]string {
	var cveList [][]string
	lowerName := strings.ToLower(componentName)
	var dataList [][]string
	dataList, total, _ = getPageInfo(componentName, componentVersion, pageNum, total)

	for _, data := range dataList {
		content := strings.ToLower(data[1])
		if strings.Contains(content, lowerName) {
			//// 不建议匹配正则表达式
			//patternStr := getVersionPatternStr(componentRes.Version)
			//matched, _ := regexp.MatchString(patternStr, data[1])
			//if err != nil || !matched{
			//	continue
			//}
			logging.DebugLogger.Debugf("组件发现漏洞: ComponentName-<%s>, CVE-<%s>, Severity-<%s>", componentName, data[0], data[2])
			cveList = append(cveList, data)
		}
	}

	if total > pageNum {
		list := tempSyncCVE(componentName, componentVersion, pageNum+1, total)
		cveList = append(cveList, list...)
	}
	return cveList
}

func newTempSyncCVETaskManager() *tempSyncCVETaskManager {
	return &tempSyncCVETaskManager{
		Queue: workqueue.NewNamed("组件CVE漏洞预查任务队列"),
	}
}

func componentTempCheckTask(componentTempCheckRes *dcomponenttempcheck.Response) bool {
	logging.DebugLogger.Debugf("执行组件预查, 组件名: %s, 组件版本: %s", componentTempCheckRes.ComponentName, componentTempCheckRes.ComponentVersion)
	cveList := tempSyncCVE(componentTempCheckRes.ComponentName, componentTempCheckRes.ComponentVersion, 1, 0)
	err := writePdf(componentTempCheckRes.ID, componentTempCheckRes.ComponentName, componentTempCheckRes.ComponentVersion, cveList)
	updateStatus := map[string]interface{}{"status": opensource.ComponentTempCheckStatusSucceeded}
	if err != nil {
		updateStatus["status"] = opensource.ComponentTempCheckStatusFailed
	} else {
		updateStatus["result_filename"] = fmt.Sprintf(componentTempCheckPdfFileFormat, componentTempCheckRes.ID)
	}
	if err := componentTempCheckRes.Update(componentTempCheckRes.ID, updateStatus); err != nil {
		return false
	}
	logging.DebugLogger.Debugf("组件预查结束, 组件名: %s, 组件版本: %s", componentTempCheckRes.ComponentName, componentTempCheckRes.ComponentVersion)
	return true
}

func (manager *tempSyncCVETaskManager) AddComponentTempSyncTask(componentTempCheckRes *dcomponenttempcheck.Response) {
	manager.Queue.Add(componentTempCheckRes.ID)
}

func (manager *tempSyncCVETaskManager) AddScheduledCVESyncTask(componentRes *dcomponent.OpenSourceComponent) {
	manager.Queue.Add(componentRes.ID)
}

func (manager *tempSyncCVETaskManager) run() {
	var tempSyncCVETaskWorkers = libs.Config.OpenSource.TempCheckTaskWorkers
	for i := 0; i < tempSyncCVETaskWorkers; i++ {
		go manager.runCVESync()
	}
}

func (manager *tempSyncCVETaskManager) runCVESync() {
	for manager.processNextCVESyncTask() {
	}
}

func (manager *tempSyncCVETaskManager) processNextCVESyncTask() bool {
	item, quit := manager.Queue.Get()
	if quit {
		return false
	}
	tempCheckId, ok := item.(uint)
	if !ok || tempCheckId == 0 {
		// item出错, 删除;
		manager.Queue.Done(item)
		return true
	}

	componentTempCheckRes, err := dcomponenttempcheck.GetComponentTempCheck(tempCheckId)
	if err != nil {
		manager.Queue.Add(item)
		return true
	} else if componentTempCheckRes == nil || componentTempCheckRes.Status != opensource.VulnerabilityStatusProcessing {
		// 预查不存在或者已经完成; 删除;
		manager.Queue.Done(item)
		return true
	}

	if componentTempCheckTask(componentTempCheckRes) {
		// 完成, 删除;
		manager.Queue.Done(item)
	} else {
		manager.Queue.Add(item)
	}
	return true
}

func writePdf(componentCheckId uint, componentName, componentVersion string, cveList [][]string) error {
	m := pdf.NewMaroto(consts.Portrait, consts.A4)

	m.AddUTF8Font(customArialFontName, consts.Normal, customArialFontFile)
	m.AddUTF8Font(customArialFontName, consts.Italic, customArialFontFile)
	m.AddUTF8Font(customArialFontName, consts.Bold, customArialFontFile)
	m.AddUTF8Font(customArialFontName, consts.BoldItalic, customArialFontFile)
	m.SetDefaultFontFamily(customArialFontName)
	m.SetPageMargins(20, 10, 20)

	m.Row(10, func() {
		m.Col(12, func() {
			m.Text("组件CVE漏洞预查结果", props.Text{
				Top:   3,
				Size:  18,
				Style: consts.Bold,
				Align: consts.Center,
				Color: getDarkPurpleColor(),
			})
		})
	})

	m.SetBackgroundColor(getTealColor())
	m.Row(10, func() {
		m.Col(12, func() {
			m.Text("组件名:"+componentName, props.Text{
				Top:   3,
				Size:  13,
				Style: consts.Bold,
				Align: consts.Center,
				Color: color.NewWhite(),
			})
		})
	})
	m.Row(10, func() {
		m.Col(12, func() {
			m.Text("组件版本:"+componentVersion, props.Text{
				Top:   2,
				Size:  13,
				Style: consts.Bold,
				Align: consts.Center,
				Color: color.NewWhite(),
			})
		})
	})
	m.Row(10, func() {
		m.Col(12, func() {
			m.Text("预查时间:"+time.Now().Format("2006-01-02 15:04:05"), props.Text{
				Top:   2,
				Size:  13,
				Style: consts.Bold,
				Align: consts.Center,
				Color: color.NewWhite(),
			})
		})
	})
	m.SetBackgroundColor(color.NewWhite())

	lightPurpleColor := getLightPurpleColor()
	tableHeadings := []string{"CVE", "Description", "Severity"}
	m.TableList(tableHeadings, cveList, props.TableList{
		HeaderProp: props.TableListContent{
			Size:      9,
			GridSizes: []uint{2, 8, 2},
		},
		ContentProp: props.TableListContent{
			Size:      8,
			GridSizes: []uint{2, 8, 2},
		},
		Align:                consts.Left,
		AlternatedBackground: &lightPurpleColor,
		HeaderContentSpace:   1,
		Line:                 true,
	})
	err := m.OutputFileAndClose(fmt.Sprintf(componentTempCheckPdfFileFormat, componentCheckId))
	if err != nil {
		logging.ErrorLogger.Errorf("failed to save component temp check result pdf, componentCheckId: %d, err: %s", componentCheckId, err.Error())
		return err
	}
	return nil
}

func getDarkPurpleColor() color.Color {
	return color.Color{
		Red:   88,
		Green: 80,
		Blue:  99,
	}
}

func getLightPurpleColor() color.Color {
	return color.Color{
		Red:   210,
		Green: 200,
		Blue:  230,
	}
}

func getTealColor() color.Color {
	return color.Color{
		Red:   3,
		Green: 166,
		Blue:  166,
	}
}
