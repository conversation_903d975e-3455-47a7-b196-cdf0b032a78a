package datasync

import (
	"fmt"
	"irisAdminApi/service/dao/datasync/ddfxmemoryinfo"
	"reflect"
	"time"

	"github.com/pkg/errors"
)

// 创建记录
func SyncCreateDfxMemoryInfo() error {
	// 创建 Client
	records, err := GetTableData("KZQCbsWefa5e3MsamMccOYYPnMr", "tbl1NZLLEoC8H0Xk")
	if err != nil {
		return errors.Wrap(err, "")
	}

	objects := []map[string]interface{}{}

	kvMap := map[string]string{
		"名称":  "Name",
		"类型":  "Type",
		"组件名": "Component",
		"负责人": "Owner",
	}
	createdAt := time.Now()
	updatedAt := time.Now()

mainLoop:
	for _, record := range records {

		object := map[string]interface{}{}
		object["CreatedAt"] = createdAt
		object["UpdatedAt"] = updatedAt
		for k, v := range kvMap {
			fmt.Println(k, v, record.Fields[k])
			if value, ok := record.Fields[k]; ok {
				if value == nil {
					object[v] = ""
					if k == "名称" {
						continue mainLoop
					}
				} else if k == "负责人" {
					object[v] = GetFeishUsername(value)
				} else {
					object[v] = value
				}
			} else {
				return fmt.Errorf("字段%s不存在", k)
			}
		}
		objects = append(objects, object)
	}
	fmt.Println(objects)
	return ddfxmemoryinfo.UpdateOrCreate(objects)
}

func GetFeishUsername(name interface{}) interface{} {

	switch name.(type) {
	case []interface{}:
		if len(name.([]interface{})) > 0 {
			_name := name.([]interface{})[0]
			switch _name.(type) {
			case map[string]interface{}:
				if value, ok := _name.(map[string]interface{})["name"]; ok {
					return value
				}

				return ""
			case map[string]string:
				if value, ok := _name.(map[string]string)["name"]; ok {
					return value
				}

				return ""
			default:
				return fmt.Sprintf("错误类型%s,需要string/map", reflect.TypeOf(_name))
			}

		}
		return ""

	case string:
		return name
	default:
		return fmt.Sprintf("错误类型%s,需要string/map", reflect.TypeOf(name))
	}
}

func GetFeishUserOpenID(name interface{}) interface{} {

	switch name.(type) {
	case []interface{}:
		if len(name.([]interface{})) > 0 {
			_name := name.([]interface{})[0]
			switch _name.(type) {
			case map[string]interface{}:
				if value, ok := _name.(map[string]interface{})["open_id"]; ok {
					return value
				}

				return ""
			case map[string]string:
				if value, ok := _name.(map[string]string)["open_id"]; ok {
					return value
				}

				return ""
			default:
				return fmt.Sprintf("错误类型%s,需要string/map", reflect.TypeOf(_name))
			}

		}
		return ""

	default:
		return fmt.Sprintf("错误类型%s,需要string/map", reflect.TypeOf(name))
	}
}
