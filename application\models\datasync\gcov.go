package datasync

import (
	"time"

	"gorm.io/gorm"
)

type Gcov struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	CreatedAt time.Time      `gorm:"uniqueIndex: idx_gcov_branch_releaseid_file_host" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at"`
	Branch    string         `gorm:"uniqueIndex: idx_gcov_branch_releaseid_file_host; not null; type:varchar(100)" json:"branch"`
	ReleaseID string         `gorm:"uniqueIndex: idx_gcov_branch_releaseid_file_host; not null; type:varchar(100)" json:"release_id"`
	File      string         `gorm:"uniqueIndex: idx_gcov_branch_releaseid_file_host; not null; type:varchar(200)" json:"file"`
	Host      string         `gorm:"uniqueIndex: idx_gcov_branch_releaseid_file_host; not null; type:varchar(200)" json:"host"`
	Type      string         `gorm:"uniqueIndex: idx_gcov_branch_releaseid_file_host; not null; type:varchar(200)" json:"type"`
	Gcov      float32        `json:"gcov" update:"1"`
}
