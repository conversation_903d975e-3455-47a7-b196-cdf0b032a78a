package sig

import "irisAdminApi/application/models"

type SigJob struct {
	models.ModelBase
	JobID string `gorm:"not null; type:varchar(60)" json:"job_id"`

	InputFileName string `gorm:"not null; type:varchar(200)" json:"intput_file_name"`
	InputMD5      string `gorm:"not null; type:varchar(60)" json:"input_md5"`

	OutputFileName string `gorm:"not null; type:varchar(200)" json:"output_file_name"`
	OutputMD5      string `gorm:"not null; type:varchar(200)" json:"output_md5"`

	WithLib string `gorm:"not null; type:varchar(60); default:'no'" json:"with_lib"`
	Status  uint   `gorm:"not null" json:"status"` // 0: 进行中 1: 成功 2: 失败
	UserID  uint   `gorm:"not null" json:"user_id"`
	App     string `gorm:"not null; type:varchar(60); default:''" json:"app"`
}

type SigTechSupportDecryptJob struct {
	models.ModelBase
	JobID string `gorm:"not null; type:varchar(60)" json:"job_id"`

	InputFileName string `gorm:"not null; type:varchar(200)" json:"intput_file_name"`
	InputMD5      string `gorm:"not null; type:varchar(60)" json:"input_md5"`

	OutputFileName string `gorm:"not null; type:varchar(200)" json:"output_file_name"`
	OutputMD5      string `gorm:"not null; type:varchar(200)" json:"output_md5"`

	Status     uint   `gorm:"not null" json:"status"`                 // 0: 进行中 1: 成功 2: 失败
	PerfStatus uint   `gorm:"not null; default:0" json:"perf_status"` // 0: 未发现perf文件 1: 进行中 2: 完成 3: 失败
	UserID     uint   `gorm:"not null" json:"user_id"`
	App        string `gorm:"not null; type:varchar(60); default:''" json:"app"`
}

// SigPerfAnalysisResult 表示perf分析任务的结果
type SigPerfAnalysisResult struct {
	models.ModelBase
	JobID        string `gorm:"not null; type:varchar(60); index" json:"job_id"`   // 关联解密任务ID
	DecryptJobID uint   `gorm:"not null" json:"decrypt_job_id"`                    // 关联解密任务数据库ID
	PerfFileName string `gorm:"not null; type:varchar(200)" json:"perf_file_name"` // 原始perf文件名
	AnalysisType string `gorm:"not null; type:varchar(20)" json:"analysis_type"`   // 分析类型
	ResultPath   string `gorm:"not null; type:varchar(500)" json:"result_path"`    // perflog文件路径
	Status       uint   `gorm:"not null" json:"status"`                            // 0: 进行中 1: 成功 2: 失败
	ErrorMessage string `gorm:"type:text" json:"error_message"`                    // 分析失败时错误信息
	FileSize     int64  `gorm:"default:0" json:"file_size"`                        // 结果文件大小
	App          string `gorm:"not null; type:varchar(60); default:''" json:"app"` // 应用标识
}
