package urlpack

import (
	"database/sql"
	"fmt"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/service/dao/urlpack/durlpackdb"
	"strconv"

	"github.com/kataras/iris/v12"
)

type UrlResponse struct {
	ID        int    `json:"id"`
	Url       string `json:"url"`
	ClassNtos string `json:"class_ntos"`
}

func QueryUrl(ctx iris.Context) {
	url := ctx.FormValue("search")
	_page := ctx.FormValue("page")
	_pageSize := ctx.FormValue("pageSize")

	var page, pageSize = 1, 10
	var err error
	if len(_page) > 0 {
		page, err = strconv.Atoi(_page)
		if err != nil {
			ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
			return
		}
	}
	if len(_pageSize) > 0 {
		pageSize, err = strconv.Atoi(_pageSize)
		if err != nil {
			ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
			return
		}
	}
	exact := ctx.URLParamBoolDefault("exact", false)
	dbConfig := durlpackdb.UrlPackDB{}
	err = dbConfig.FindLast()
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	conn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8&parseTime=True&loc=Local", dbConfig.Username, dbConfig.Password, dbConfig.Host, dbConfig.Port, dbConfig.Database)
	// fmt.Println(conn)
	if UrlDb == nil {
		UrlDb = &UrlDbServer{
			adapter: "mysql",
			conn:    conn,
		}
		err := UrlDb.Init()
		if err != nil {
			fmt.Println(err)
			UrlDb = nil
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		if UrlDb.db == nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "URL数据库初始化失败"))
			return
		}
	}
	var total int
	var rows *sql.Rows

	if !exact {
		err = UrlDb.db.Raw("select count(*) from url_out_lib_out where url like ?", fmt.Sprintf("%%%s%%", url)).Scan(&total).Error
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		rows, err = UrlDb.db.Raw("select id, url, class_ntos from url_out_lib_out where url like ?  limit ? offset ?", fmt.Sprintf("%%%s%%", url), pageSize, (page-1)*pageSize).Rows()
		// err = UrlDb.db.Debug().Model(&UrlOutLibOut{}).Select("url").Where("class = ?", "软件更新").Limit(10).Find(&result).Error
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}

	} else {
		err = UrlDb.db.Raw("select count(*) from url_out_lib_out where url = ?", url).Scan(&total).Error
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		rows, err = UrlDb.db.Raw("select id, url, class_ntos from url_out_lib_out where url = ?  limit ? offset ?", url, pageSize, (page-1)*pageSize).Rows()
		// err = UrlDb.db.Debug().Model(&UrlOutLibOut{}).Select("url").Where("class = ?", "软件更新").Limit(10).Find(&result).Error
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
	}
	defer rows.Close()
	items := []*UrlResponse{}

	for rows.Next() {
		url := UrlResponse{}
		UrlDb.db.ScanRows(rows, &url)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		items = append(items, &url)
	}
	result := map[string]interface{}{
		"items":    &items,
		"total":    total,
		"page":     page,
		"pageSize": pageSize,
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}
