package transapproval

import (
	"encoding/json"
	"errors"
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/fileout"
	"irisAdminApi/service/cache"
	"irisAdminApi/service/dao/fileout/dapproval"
	"irisAdminApi/service/dao/fileout/dapprovalcomment"
	"irisAdminApi/service/dao/fileout/dapprovalshare"
	"irisAdminApi/service/dao/fileout/dfilestruction"
	"os"
	"path/filepath"
	"time"

	"gorm.io/gorm"
)

func CreateApprovalTransaction(approvalObject map[string]interface{}, commentObjects []map[string]interface{}, shareObjects []map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		if err := tx.Model(dapproval.Model()).Create(approvalObject).Error; err != nil {
			// 返回任何错误都会回滚事务
			return err
		}
		approval := dapproval.Response{}
		if err := tx.Model(dapproval.Model()).Where("name = ?", approvalObject["Name"]).Find(&approval).Error; err != nil {
			return err
		}
		if approval.Id == 0 {
			return errors.New("创建外出申请单失败")
		}

		if len(commentObjects) > 0 {
			for _, commentObject := range commentObjects {
				commentObject["ApprovalId"] = approval.Id
			}
			if err := tx.Model(dapprovalcomment.Model()).Create(commentObjects).Error; err != nil {
				return err
			}
		}

		if len(shareObjects) > 0 {
			for _, shareObject := range shareObjects {
				shareObject["ApprovalId"] = approval.Id
			}
			if err := tx.Model(dapprovalshare.Model()).Create(shareObjects).Error; err != nil {
				return err
			}
		}

		// 返回 nil 提交事务
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

type AutoAuditMd5 struct {
	Md5 string `json:"md5"`
}

func UpdateApprovalTransaction(id uint, approvalObject map[string]interface{}, commentObjects []map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	var rc = cache.GetRedisClusterClient()
	defer rc.Close()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		approval := dapproval.Response{}
		if err := tx.Model(dapproval.Model()).Where("id = ?", id).Find(&approval).Error; err != nil {
			return err
		}
		if approval.Id == 0 {
			return errors.New("更新外出申请单失败")
		}

		if len(commentObjects) > 0 {
			for _, commentObject := range commentObjects {
				commentObject["ApprovalId"] = approval.Id
			}
			if err := tx.Model(dapprovalcomment.Model()).Create(commentObjects).Error; err != nil {
				return err
			}
		}

		if approvalObject["Permit"] == true || approvalObject["permit"] == true {
			fileStruction := dfilestruction.Response{}
			if err := tx.Model(dfilestruction.Model()).Where("approval_id = ?", approval.Id).Find(&fileStruction).Error; err != nil {
				return err
			}
			if fileStruction.Id > 0 {
				details := []dfilestruction.Detail{}
				err := json.Unmarshal([]byte(fileStruction.Detail), &details)
				if err != nil {
					return err
				}

				md5s := []map[string]interface{}{}
				his := []string{}
				for _, item := range details {
					value, err := rc.GetKey(fmt.Sprintf("FC:%s", item.Md5))
					if err == nil && value == nil && !libs.InArrayS(his, item.Md5) {
						md5s = append(md5s, map[string]interface{}{
							"md5": item.Md5,
						})
						his = append(his, item.Md5)
					}
					if err != nil {
						logging.ErrorLogger.Error("get cache md5 err", err)
						return err
					}
				}
				if len(md5s) > 0 {
					if err := tx.Model(fileout.AutoAuditMd5{}).Create(md5s).Error; err != nil {
						return err
					}
				}
				for _, md5 := range md5s {
					_, err := rc.Set(fmt.Sprintf("FC:%s", md5["md5"]), "1")
					if err != nil {
						logging.ErrorLogger.Error("cache md5 err", err)
						return err
					}
				}
				if err := tx.Model(dfilestruction.Model()).Where("approval_id = ?", approval.Id).Updates(map[string]interface{}{"status": 1}).Error; err != nil {
					return err
				}
			}
		}

		if err := tx.Model(dapproval.Model()).Where("id = ?", id).Updates(approvalObject).Error; err != nil {
			return err
		}

		// 返回 nil 提交事务
		return nil
	})

	if err != nil {
		logging.ErrorLogger.Errorf("transaction update approval err ", err)
		return err
	}

	return nil
}

func DeleteApprovalTransaction(approvalID uint) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		approval := dapproval.Response{}
		err := tx.Model(dapproval.Model()).Where("id=?", approvalID).Find(&approval).Error
		if err != nil {
			return err
		}
		err = tx.Unscoped().Delete(dfilestruction.Model(), "approval_id=?", approvalID).Error
		if err != nil {
			return err
		}
		err = tx.Unscoped().Delete(dapprovalcomment.Model(), "approval_id=?", approvalID).Error
		if err != nil {
			return err
		}
		err = tx.Unscoped().Delete(dapproval.Model(), "id=?", approvalID).Error
		if err != nil {
			return err
		}
		var upload = libs.Config.FileStorage.Upload + time.Now().Format("20060102") + "/"
		os.Remove(filepath.Join(upload, approval.Name))
		return nil
	})
	if err != nil {
		return err
	}

	return nil
}

func UpdateAuditorTransaction(id uint, approvalObject map[string]interface{}, commentObject map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		approval := dapproval.Response{}
		if err := tx.Model(dapproval.Model()).Where("id = ?", id).Find(&approval).Error; err != nil {
			return err
		}
		if approval.Id == 0 {
			return errors.New("外出审请单不存在")
		}

		if err := tx.Model(dapprovalcomment.Model()).Create(commentObject).Error; err != nil {
			return err
		}

		if err := tx.Model(dapproval.Model()).Where("id = ?", id).Updates(approvalObject).Error; err != nil {
			return err
		}

		// 返回 nil 提交事务
		return nil
	})

	if err != nil {
		return err
	}

	return nil
}
