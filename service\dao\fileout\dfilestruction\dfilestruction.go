package dfilestruction

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/fileout"
)

const ModelName = "文件详情"

type Detail struct {
	File       string `json:"file"`
	Ext        string `json:"ext"`
	Info       string `json:"info"`
	Mime       string `json:"mime"`
	Md5        string `json:"md5"`
	Size       int64  `json:"size"`
	Hash       string `json:"hash"`
	Objdump    bool   `json:"objdump"`
	Similarity int64  `json:"similarity"`
	Pass       bool   `json:"pass"`
	AiCheck    int    `json:"ai_check"`
	Err        string `json:"err"`
}

type Response struct {
	Id         uint   `json:"id"`
	ApprovalID uint   `json:"approval_id"`
	Name       string `json:"name" `
	Detail     string `json:"detail" `
	Status     string `json:"status" `
}

type ListResponse struct {
	Response
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *fileout.FileStruction {
	return &fileout.FileStruction{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*Response

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*Response

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find filedetail err ", err)
		return err
	}
	return nil
}

func FindDetailByApprovalId(aId uint) (Response, error) {
	var count int64
	var details Response
	db := easygorm.GetEasyGormDb().Model(Model()).Where("approval_id = ?", aId)
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get filedetail count err ", err)
		return details, err
	}
	err = db.Find(&details).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get filedetail err ", err)
		return details, err
	}
	return details, nil
}

func BatchCreate(objects []map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(objects).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}
	return nil
}

func FindByName(name string) (Response, error) {
	item := Response{}
	err := easygorm.GetEasyGormDb().Model(Model()).Last(&item, "name=? and status = 1", name).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return item, err
	}
	return item, nil
}

func FindFileConstruction(userId uint, name string) (Response, error) {
	item := Response{}
	err := easygorm.GetEasyGormDb().Model(Model()).Last(&item, "name=? and status = 1 and user_id = ?", name, userId).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find file construction err ", err)
		return item, err
	}
	return item, nil
}

func FindFileConstructions(userId uint, name string) ([]*ListResponse, error) {
	items := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("name = ? and status = 1", name).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find file construction err ", err)
		return items, err
	}
	return items, nil
}
