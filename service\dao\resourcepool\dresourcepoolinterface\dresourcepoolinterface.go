package dresourcepoolinterface

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/resourcepool"
	"irisAdminApi/service/dao/resourcepool/dresourcepoolresource"
	"strings"
)

const ModelName = "设备管理"

/*
	Description     string `gorm:"not null; type:varchar(512)"`
	ProblemSourceID uint   `gorm:"not null"`
	PlanCloseAt     string `gorm:"not null; type:varchar(30)"`
	UserID          uint   `gorm:"not null"`
	Status          uint   `gorm:"not null；default:0"` //0:未闭环  1:已闭环
	OwnerID         uint   `gorm:"not null"`
*/

type ResourcePoolInterface struct {
	resourcepool.ResourcePoolInterface
}

type Request struct {
	resourcepool.ResourcePoolInterface
}

func (a *ResourcePoolInterface) ModelName() string {
	return ModelName
}

func Model() *resourcepool.ResourcePoolInterface {
	return &resourcepool.ResourcePoolInterface{}
}

func GetNowSwPortStatus(userId uint) (string, error) {
	var username string
	if err := easygorm.GetEasyGormDb().Raw("SELECT sw_port_status FROM resource_pool_interfaces WHERE id = ?", userId).Scan(&username).Error; err != nil {
		logging.ErrorLogger.Errorf("get user name err ", err)
		return "", err
	}
	return username, nil
}

func GetByNameAndInterface(name string, interface_ string) (*ResourcePoolInterface, error) {
	var res ResourcePoolInterface
	if err := easygorm.GetEasyGormDb().Raw("SELECT * FROM resource_pool_interfaces WHERE name = ? AND interface = ? LIMIT 1;", name, interface_).Scan(&res).Error; err != nil {
		logging.ErrorLogger.Errorf("get user name err ", err)
		return nil, err
	}
	return &res, nil
}

func UpdateByNameAndInterface(name string, interface_ string, vlanid string, vlanmode string, comments string) error {
	db := easygorm.GetEasyGormDb().Exec("UPDATE resource_pool_interfaces SET vlan_id = ?, vlan_mode = ?, comments = ? WHERE name = ? AND interface = ?;", vlanid, vlanmode, comments, name, interface_)
	// if err := db.Commit().Error; err != nil {
	// 	fmt.Println(err)
	// 	logging.ErrorLogger.Errorf("commit err %v", err)
	// 	return err
	// }
	rowsAffected := db.RowsAffected
	if db.Error != nil {
		logging.ErrorLogger.Errorf("update err %v", db.Error)
		return db.Error
	}
	if rowsAffected == 0 {
		logging.ErrorLogger.Errorf("no rows affected ")
		return nil
	}
	return nil
}

func AllInterfaceByName(name string) (map[string]interface{}, error) {
	rows, err := easygorm.GetEasyGormDb().Raw("SELECT DISTINCT interface FROM resource_pool_interfaces WHERE name = ?", name).Rows()
	if err != nil {
		logging.ErrorLogger.Errorf("get resource name err ", err)
		return nil, err
	}
	var names []string
	for rows.Next() {
		var interfaces string
		if err := rows.Scan(&interfaces); err != nil {
			logging.ErrorLogger.Errorf("get resource name err ", err)
			return nil, err
		}
		names = append(names, interfaces)
	}

	list := map[string]interface{}{"interfaces": names}
	return list, nil
}

func (a *ResourcePoolInterface) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ResourcePoolInterface

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("mgt_ip like ?", fmt.Sprintf("%%%s%%", name)).Or("reserved like ?", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *ResourcePoolInterface) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ResourcePoolInterface

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *ResourcePoolInterface) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *ResourcePoolInterface) CreateV2(object interface{}) error {
	return nil
}

func (a *ResourcePoolInterface) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("find interface by id error: %v", err)
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update interface by id error: %v", err)
		return err
	}
	return nil
}

func (a *ResourcePoolInterface) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find interface by user_id error: %v", err)
		return err
	}
	return nil
}

func (a *ResourcePoolInterface) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find interface by auditor_id error: %v", err)
		return err
	}
	return nil
}

func (a *ResourcePoolInterface) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete interface by id error: %v", err)
		return err
	}
	return nil
}

func (u *ResourcePoolInterface) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find interface by id error: %v", err)
		return err
	}
	return nil
}

func (u *ResourcePoolInterface) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find interface by %v error: %v", col, err)
		return err
	}
	return nil
}

func All(userId uint, name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ResourcePoolInterface

	db := easygorm.GetEasyGormDb().Model(Model())
	where := easygorm.GetEasyGormDb().Model(Model())
	if len(libs.Config.Resourcepool.Testbed) > 0 {
		db = db.Where("name in (?)", easygorm.GetEasyGormDb().Model(dresourcepoolresource.Model()).Select("name").Where("testbed in ?", strings.Split(libs.Config.Resourcepool.Testbed, ",")))
	}
	if len(name) > 0 {
		where = where.Where("name like ?", fmt.Sprintf("%%%s%%", name)).
			Or("interface like ?", fmt.Sprintf("%%%s%%", name)).
			Or("driver like ?", fmt.Sprintf("%%%s%%", name)).
			Or("mac like ?", fmt.Sprintf("%%%s%%", name)).
			Or("sw_name like ?", fmt.Sprintf("%%%s%%", name)).
			Or("sw_port like ?", fmt.Sprintf("%%%s%%", name)).
			Or("vlan_id like ?", fmt.Sprintf("%%%s%%", name)).
			Or("vlan_mode like ?", fmt.Sprintf("%%%s%%", name)).
			Or("type like ?", fmt.Sprintf("%%%s%%", name)).
			Or("connections like ?", fmt.Sprintf("%%%s%%", name)).
			Or("comments like ?", fmt.Sprintf("%%%s%%", name)).
			Or("sw_port_status like ?", fmt.Sprintf("%%%s%%", name))
	}

	db = db.Where(where)

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	sort = "ASC"
	orderBy = "id"
	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}
