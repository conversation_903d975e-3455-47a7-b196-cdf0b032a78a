package libs

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"io"
	"io/ioutil"
	"irisAdminApi/application/logging"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
)

var httpClient = &http.Client{
	Transport: &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	},
}

type UploadFile struct {
	// 表单名称
	Name string
	// 文件全路径
	Filepath string
}

func Get(reqUrl string, reqParams map[string]string, headers map[string]string) (statusCode int, head map[string][]string, body []byte, err error)  {
	var respCode int
	var respHead map[string][]string
	var respBody []byte
	urlParams := url.Values{}
	Url, _ := url.Parse(reqUrl)
	for key, val := range reqParams {
		urlParams.Set(key, val)
	}
	//如果参数中有中文参数,这个方法会进行URLEncode
	Url.RawQuery = urlParams.Encode()
	// 得到完整的url，http://xx?query
	urlPath := Url.String()

	httpRequest,_ := http.NewRequest(http.MethodGet, urlPath, nil)
	//添加请求头
	if headers != nil {
		for k, v := range headers {
			httpRequest.Header.Add(k,v)
		}
	}
	//发送请求
	resp, err := httpClient.Do(httpRequest)
	if err != nil {
		logging.ErrorLogger.Errorf("httpCommRequest get response err ", err)
		return respCode,respHead, respBody,nil 
	}
	defer resp.Body.Close()
	respCode = resp.StatusCode
    respHead = resp.Header
    respBody, _ = ioutil.ReadAll(resp.Body)
	return respCode,respHead, respBody,nil 
}

func PostForm(reqUrl string, reqParams map[string]interface{}, headers map[string]string) (statusCode int, head map[string][]string, body []byte, err error) {
	return post(reqUrl, reqParams, "application/x-www-form-urlencoded", nil, headers)
}

func PostJson(reqUrl string, reqParams map[string]interface{}, headers map[string]string) (statusCode int, head map[string][]string, body []byte, err error) {
	return post(reqUrl, reqParams, "application/json", nil, headers)
}

func PostFile(reqUrl string, reqParams map[string]interface{}, files []UploadFile, headers map[string]string) (statusCode int, head map[string][]string, body []byte, err error) {
	return post(reqUrl, reqParams, "multipart/form-data", files, headers)
}

// @title    post
// @description   http的Post请求
// @param         reqParams       map[string]interface{}  "请求参数"
// @param         contentType     string                  "请求类型"  
// @param         headers         map[string]string       "请求头部内容"
// @return        statusCode      int                     "响应状态码"
// @return        head            map[string][]string     "响应头部内容"
// @return        body            []byte                  "响应体数据流"
// @return        err             error                   "错误信息"
func post(reqUrl string, reqParams map[string]interface{}, contentType string, files []UploadFile, headers map[string]string) (statusCode int, head map[string][]string, body []byte, err error)  {
	var respCode int
	var respHead map[string][]string
	var respBody []byte
	requestBody, realContentType , readerErr:= getReader(reqParams, contentType, files)
	if readerErr != nil {
		return respCode,respHead, respBody, readerErr
	}
	httpRequest,_ := http.NewRequest(http.MethodPost, reqUrl, requestBody)
	// 添加请求头
	httpRequest.Header.Add("Content-Type", realContentType)
	if headers != nil {
		for k, v := range headers {
			httpRequest.Header.Add(k,v)
		}
	}
	// 发送请求
	resp, err := httpClient.Do(httpRequest)
	if err != nil {
		return respCode,respHead, respBody, err
	}
	defer resp.Body.Close()
	bodyData, err := SwitchContentEncoding(resp)
	if err != nil {
		logging.ErrorLogger.Errorf("decode response err ", err)
		return respCode,respHead, respBody, err
	}
	respCode = resp.StatusCode
    respHead = resp.Header
    respBody, _ = ioutil.ReadAll(bodyData)
	return respCode,respHead, respBody,nil 
}

func getReader(reqParams map[string]interface{}, contentType string, files []UploadFile) (io.Reader, string,error)  {
	if strings.Index(contentType, "json") > -1 {
		bytesData, _ := json.Marshal(reqParams)
		return bytes.NewReader(bytesData), contentType,nil
	} else if files != nil {
		body := &bytes.Buffer{}
		// 文件写入 body
		writer := multipart.NewWriter(body)
		for _, uploadFile := range files {
			file, err := os.Open(uploadFile.Filepath)
			if err != nil {
				logging.ErrorLogger.Errorf("httpCommRequest getReader openfile err ", err)
				return nil, "", err
			}
			part, err := writer.CreateFormFile(uploadFile.Name, filepath.Base(uploadFile.Filepath))
			if err != nil {
				logging.ErrorLogger.Errorf("httpCommRequest getReader CreateFormFile err ", err)
				return nil, "", err
			}
			_, err = io.Copy(part, file)
			file.Close()
		}
		// 其他参数列表写入 body
		for k, v := range reqParams {
			if err := writer.WriteField(k, v.(string)); err != nil {
				logging.ErrorLogger.Errorf("httpCommRequest getReader WriteField err ", err)
				return nil, "", err
			}
		}
		if err := writer.Close(); err != nil {
			logging.ErrorLogger.Errorf("httpCommRequest getReader WriteClose err ", err)
			return nil, "", err
		}
		// 上传文件需要自己专用的contentType
		return body, writer.FormDataContentType(),nil
	} else {
		urlValues := url.Values{}
		for key, val := range reqParams {
			urlValues.Set(key, val.(string))
		}
		reqBody:= urlValues.Encode()
		return strings.NewReader(reqBody), contentType,nil
	}
}

func RespDataDeal(StatusCode int, Header map[string][]string, Body []byte , Err error) (Headers map[string][]string, Res map[string]interface{}, Rerr error){
	if Err !=nil{
		return Header,nil, Err
	}
	if StatusCode !=200{
		return Header,nil, Err
	}
	result := []map[string]interface{}{}
	err := json.Unmarshal(Body, &result)
	if err != nil {
		result := map[string]interface{}{}
		err = json.Unmarshal(Body, &result)
		if err != nil {
			logging.ErrorLogger.Errorf("httpCommRequest respDataDeal  umarshal response err ", err, string(Body))
			result := map[string]interface{}{
				"data": string(Body),
			}
			return Header,result, nil
		}
		return Header,result, nil
	}
	return Header,nil, err
}
