package ddagversion

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/documentauto"

	"gorm.io/gorm"
)

const ModelName = "版本数据表"

type Response struct {
	documentauto.DagVersion
}

type ListResponse struct {
	Response
}

type Request struct {
	VersionNumber string `json:"version_number"` //版本号
	ReleaseDate   string `json:"release_date"`   //发布时间
	Status        string `json:"status"`         //状态
	Sort          uint   `json:"sort"`           //排序
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *documentauto.DagVersion {
	return &documentauto.DagVersion{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (a *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

// 获取版本数据
func GetVersionData(versionNumber string) (*documentauto.DagVersion, error) {
	var version documentauto.DagVersion
	err := easygorm.GetEasyGormDb().Model(Model()).
		Where("version_number = ?", versionNumber).
		First(&version).Error

	if err != nil {
		logging.ErrorLogger.Errorf("get version data err ", err)
		return nil, err
	}

	return &version, nil
}

func UpdateVersionSorts(sortValue uint) error {
	db := easygorm.GetEasyGormDb()
	// 使用事务确保数据一致性
	return db.Transaction(func(tx *gorm.DB) error {
		// 检查是否有排序冲突
		var count int64
		if err := tx.Model(Model()).Where("sort = ?", sortValue).Count(&count).Error; err != nil {
			logging.ErrorLogger.Errorf("check existing sort err ", err)
			return err
		}

		// 使用批量更新提高性能
		err := tx.Model(Model()).Where("sort >= ?", sortValue).
			Update("sort", gorm.Expr("sort + 1")).Error
		if err != nil {
			logging.ErrorLogger.Errorf("update version sorts err ", err)
			return err
		}
		return nil
	})
}

func UpdateVersionSortsOnEdit(oldSort, newSort uint) error {
	db := easygorm.GetEasyGormDb()

	// 使用事务确保数据一致性
	return db.Transaction(func(tx *gorm.DB) error {
		if newSort > oldSort {
			err := tx.Model(Model()).
				Where("sort > ? AND sort <= ?", oldSort, newSort).
				Update("sort", gorm.Expr("sort - 1")).Error
			if err != nil {
				logging.ErrorLogger.Errorf("update version sorts (moving down) err ", err)
				return err
			}
		} else if newSort < oldSort {
			err := tx.Model(Model()).
				Where("sort >= ? AND sort < ?", newSort, oldSort).
				Update("sort", gorm.Expr("sort + 1")).Error
			if err != nil {
				logging.ErrorLogger.Errorf("update version sorts (moving up) err ", err)
				return err
			}
		}

		return nil
	})
}

func DecrementVersionSorts(sortValue uint) error {
	db := easygorm.GetEasyGormDb()

	// 使用事务确保数据一致性
	return db.Transaction(func(tx *gorm.DB) error {
		// 使用批量更新提高性能
		err := tx.Model(Model()).Where("sort > ?", sortValue).
			Update("sort", gorm.Expr("sort - 1")).Error
		if err != nil {
			logging.ErrorLogger.Errorf("decrement version sorts err ", err)
			return err
		}
		return nil
	})
}

// 检查是否超过了合理范围或与现有版本冲突
func ValidateSortValue(sortValue uint) (bool, string, error) {
	db := easygorm.GetEasyGormDb()

	// 获取最大排序值
	var maxSort struct {
		MaxSort uint
	}
	err := db.Model(Model()).Select("COALESCE(MAX(sort), 0) as max_sort").Scan(&maxSort).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get max sort value err ", err)
		return false, "获取最大排序值失败", err
	}

	// 检查排序值是否超出合理范围
	if sortValue > maxSort.MaxSort+1 {
		return false, fmt.Sprintf("排序值过大，当前最大排序值为 %d", maxSort.MaxSort), nil
	}

	return true, "", nil
}

// 确保没有重复的排序值，并且排序值是连续的
func CheckSortConsistency() (bool, []string, error) {
	db := easygorm.GetEasyGormDb()
	var versions []*documentauto.DagVersion
	var issues []string

	// 获取所有版本按排序值排序
	err := db.Model(Model()).Order("sort asc").Find(&versions).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get all versions err ", err)
		return false, issues, err
	}

	// 检查排序值是否连续且没有重复
	sortMap := make(map[uint]bool)
	var lastSort uint = 0

	for _, version := range versions {
		// 检查重复排序值
		if _, exists := sortMap[version.Sort]; exists {
			issues = append(issues, fmt.Sprintf(
				"重复的排序值: %d，版本号: %s 与其他版本冲突",
				version.Sort, version.VersionNumber))
		}
		sortMap[version.Sort] = true

		// 检查排序值是否连续
		if lastSort > 0 && version.Sort > lastSort+1 {
			issues = append(issues, fmt.Sprintf(
				"排序值不连续: 版本 %s 的排序值为 %d，上一个排序值为 %d",
				version.VersionNumber, version.Sort, lastSort))
		}

		lastSort = version.Sort
	}

	return len(issues) == 0, issues, nil
}

func GetVersionList(name string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var versions []*documentauto.DagVersion

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("version_number like ?", "%"+name+"%")
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get version list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, "asc", "sort")
	err = db.Scopes(paginateScope).Find(&versions).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get version list err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": versions, "total": count, "limit": pageSize}
	return list, nil
}
