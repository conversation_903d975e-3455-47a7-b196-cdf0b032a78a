package dpmsprojectdocument

import (
	"encoding/json"
	"fmt"
	"reflect"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/datasync"
	"irisAdminApi/service/dao/datasync/dsyncrecord"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "PMS文档表"

type PmsProjectDocumentSyncResponse struct {
	State   string                        `json:"state"`
	Data    []*PmsProjectDocumentResponse `json:"data"`
	Total   int                           `json:"total"`
	Message string                        `json:"message"`
}

type PmsProjectDocumentResponse struct {
	RowNum                int    `json:"rownum"`
	ID                    int    `gorm:"not null; index:idx_unique, unique" json:"id" `
	ProjectName           string `gorm:"not null; type:varchar(100)" json:"projectName"  update:"1"`
	DocumentName          string `gorm:"not null; type:varchar(100); default:''" json:"documentName"  update:"1"`
	WorkPacketName        string `gorm:"not null; type:varchar(200); default:''" json:"workPacketName"  update:"1"`
	ChangeTypeName        string `gorm:"type:varchar(100)" json:"changeTypeName" update:"1"`
	DocumentSubmitStatus  string `gorm:"type:varchar(100)" json:"documentSubmitStatus" update:"1"`
	ReviewStatus          string `gorm:"type:varchar(100)" json:"reviewStatus" update:"1"`
	IsSubmit              int    `gorm:"not null; default:0" json:"isSubmit" update:"1"`
	IsInstorage           *bool  `gorm:"not null; default:false" json:"isInstorage" update:"1"`
	DocumentStatus        string `gorm:"type:varchar(100)" json:"documentStatus" update:"1"`
	ChargeUserName        string `gorm:"not null; type:varchar(200); default:''" json:"chargeUserName"  update:"1"`
	ComponentPacketName   string `gorm:"not null; type:varchar(200); default:''" json:"componentPacketName"  update:"1"`
	Disabled              bool   `grom:"not null; default:false" json:"disabled" update:"1"`
	DocumentCategoryTitle string `gorm:"not null; type:varchar(100); default:''" json:"documentCategoryTitle" update:"1"`
}

type PmsProjectDocument struct {
	datasync.PmsProjectDocument
}

type ListResponse struct {
	PmsProjectDocument
}

type Request struct {
	Id uint `json:"id"`
}

func (this *PmsProjectDocument) ModelName() string {
	return ModelName
}

func Model() *datasync.PmsProjectDocument {
	return &datasync.PmsProjectDocument{}
}

func (this *PmsProjectDocument) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *PmsProjectDocument) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *PmsProjectDocument) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *PmsProjectDocument) CreateV2(object interface{}) error {
	return nil
}

func (this *PmsProjectDocument) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *PmsProjectDocument) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *PmsProjectDocument) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *PmsProjectDocument) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *PmsProjectDocument) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *PmsProjectDocument) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func DeleteAll() error {
	err := easygorm.GetEasyGormDb().Unscoped().Where("1 = 1").Delete(Model()).Error
	if err != nil {
		return err
	}
	return nil
}

func UpdateOrCreatePmsProjectDocumentTransaction(items []*PmsProjectDocumentResponse, _url string, data map[string]string, method, state, errorMsg string) error {
	objects := []map[string]interface{}{}
	for _, item := range items {

		object := map[string]interface{}{
			"ID":                   item.ID,
			"ProjectName":          item.ProjectName,
			"DocumentName":         item.DocumentName,
			"WorkPacketName":       item.WorkPacketName,
			"ChangeTypeName":       item.ChangeTypeName,
			"DocumentSubmitStatus": item.DocumentSubmitStatus,
			"ReviewStatus":         item.ReviewStatus,

			"DocumentStatus":        item.DocumentStatus,
			"ChargeUserName":        item.ChargeUserName,
			"ComponentPacketName":   item.ComponentPacketName,
			"DocumentCategoryTitle": item.DocumentCategoryTitle,
			"Disabled":              item.Disabled,

			"CreatedAt": time.Now(),
			"UpdatedAt": time.Now(),
		}

		if item.IsSubmit == 1 {
			object["IsSubmit"] = true
		} else {
			object["IsSubmit"] = false
		}

		isInstorage := item.IsInstorage
		if isInstorage == nil {
			object["IsInstorage"] = false
		} else {
			object["IsInstorage"] = *isInstorage
		}

		objects = append(objects, object)
	}

	columns := []string{"updated_at"}

	xt := reflect.TypeOf(Model())
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")
		if ok && ok2 {
			columns = append(columns, name)
		}
	}

	body, err := json.Marshal(data)
	if err != nil {
		return err
	}
	db := easygorm.GetEasyGormDb()
	err = db.Transaction(func(tx *gorm.DB) error {
		if len(objects) > 0 {
			err := tx.Model(Model()).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "id"}},
				DoUpdates: clause.AssignmentColumns(columns),
			}).Create(&objects).Error
			if err != nil {
				return err
			}
		}

		if err := tx.Model(dsyncrecord.Model()).Create(map[string]interface{}{
			"url":             _url,
			"body":            body,
			"method":          method,
			"state":           state,
			"message":         errorMsg,
			"min_modify_date": data["minModifyDate"],
			"max_modify_date": data["maxModifyDate"],
			"created_at":      time.Now(),
		}).Error; err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func FindByProjectName(projectName string) ([]*PmsProjectDocument, error) {
	var items []*PmsProjectDocument

	if err := easygorm.GetEasyGormDb().Model(Model()).Where("project_name =?", projectName).Find(&items).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return items, err
	}
	return items, nil
}
