# Coredump记录自动化处理系统 - 部署和测试指南

## 1. 部署准备

### 1.1 环境要求

#### 1.1.1 系统要求

| 组件 | 最低要求 | 推荐配置 | 备注 |
|------|----------|----------|------|
| **操作系统** | Linux/Windows Server | CentOS 7+/Ubuntu 18+ | 支持Docker部署 |
| **Go版本** | Go 1.19+ | Go 1.21+ | 需要支持泛型 |
| **内存** | 2GB | 4GB+ | 根据处理量调整 |
| **CPU** | 2核 | 4核+ | 支持并发处理 |
| **磁盘** | 20GB | 50GB+ | 包含日志和数据存储 |
| **网络** | 100Mbps | 1Gbps | 稳定的网络连接 |

#### 1.1.2 依赖服务

| 服务 | 版本要求 | 用途 | 配置要求 |
|------|----------|------|----------|
| **MySQL** | 5.7+ / 8.0+ | 数据存储 | InnoDB引擎，UTF8编码 |
| **Redis** | 5.0+ | 缓存服务 | 可选，用于性能优化 |
| **飞书开放平台** | API v3+ | 数据源 | 需要应用权限 |
| **Bug管理系统** | - | 目标系统 | HTTP API访问 |

#### 1.1.3 网络要求

```bash
# 需要访问的外部服务
- 飞书开放平台API: https://open.feishu.cn
- Bug管理系统: http://bugs.ruijie.com.cn
- 邮件服务器: smtp.company.com:587 (如果启用邮件告警)
- Webhook服务: 根据配置 (如果启用Webhook告警)
```

### 1.2 权限配置

#### 1.2.1 飞书应用权限

```yaml
# 需要的飞书应用权限
required_permissions:
  - "bitable:app"          # 多维表格应用权限
  - "bitable:app:readonly" # 多维表格只读权限
  - "contact:user.id:readonly" # 用户信息读取权限
  - "contact:user.email:readonly" # 用户邮箱读取权限
```

#### 1.2.2 系统用户权限

```bash
# 创建专用用户
sudo useradd -r -s /bin/false coredump
sudo mkdir -p /opt/coredump-system
sudo chown coredump:coredump /opt/coredump-system

# 设置日志目录权限
sudo mkdir -p /var/log/coredump-system
sudo chown coredump:coredump /var/log/coredump-system
```

## 2. 安装部署

### 2.1 源码部署

#### 2.1.1 获取源码

```bash
# 克隆项目代码
git clone <repository-url> /opt/coredump-system
cd /opt/coredump-system

# 检查Go环境
go version
go mod tidy
```

#### 2.1.2 编译项目

```bash
# 编译项目
go build -o coredump-system main.go

# 验证编译结果
./coredump-system --version
```

#### 2.1.3 配置文件设置

```bash
# 创建配置目录
mkdir -p config

# 复制配置模板
cp docs/coredump/coredump_config.yaml config/
cp docs/coredump/error_handling.yaml config/

# 编辑配置文件
vim config/application.yml
```

**application.yml 配置示例：**

```yaml
# 数据库配置
database:
  host: "localhost"
  port: 3306
  username: "coredump_user"
  password: "secure_password"
  database: "coredump_system"
  charset: "utf8mb4"
  max_idle_conns: 10
  max_open_conns: 100

# 飞书配置
feishudoc:
  enable: true
  appid: "cli_xxxxxxxxxxxxxxxx"
  appsecret: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
  
  # Coredump专用配置
  coredumpapptoken: "your_coredump_app_token"
  coredumptableid: "your_coredump_table_id"
  coredumpenable: true

# 服务配置
server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"

# 日志配置
logging:
  level: "info"
  file: "/var/log/coredump-system/app.log"
  max_size: 100
  max_age: 30
  max_backups: 10
```

#### 2.1.4 数据库初始化

```sql
-- 创建数据库
CREATE DATABASE coredump_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'coredump_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON coredump_system.* TO 'coredump_user'@'localhost';
FLUSH PRIVILEGES;

-- 创建表结构（系统会自动创建，也可手动创建）
USE coredump_system;

CREATE TABLE processed_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    record_id VARCHAR(255) UNIQUE NOT NULL COMMENT '飞书记录ID',
    sn VARCHAR(255) COMMENT 'Coredump SN',
    bug_id VARCHAR(255) COMMENT '提交的Bug ID',
    processed_at TIMESTAMP COMMENT '处理时间',
    status VARCHAR(50) NOT NULL COMMENT '处理状态: success/failed',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_record_id (record_id),
    INDEX idx_sn (sn),
    INDEX idx_status (status),
    INDEX idx_processed_at (processed_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='已处理记录跟踪表';
```

### 2.2 Docker部署

#### 2.2.1 Dockerfile

```dockerfile
# Dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o coredump-system main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

# 创建必要目录
RUN mkdir -p /var/log/coredump-system
RUN mkdir -p /etc/coredump-system

COPY --from=builder /app/coredump-system .
COPY --from=builder /app/config /etc/coredump-system/

EXPOSE 8080

CMD ["./coredump-system"]
```

#### 2.2.2 docker-compose.yml

```yaml
version: '3.8'

services:
  coredump-system:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=coredump_user
      - DB_PASSWORD=secure_password
      - DB_NAME=coredump_system
    volumes:
      - ./config:/etc/coredump-system
      - ./logs:/var/log/coredump-system
    depends_on:
      - mysql
      - redis
    restart: unless-stopped
    networks:
      - coredump-network

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: coredump_system
      MYSQL_USER: coredump_user
      MYSQL_PASSWORD: secure_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    restart: unless-stopped
    networks:
      - coredump-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped
    networks:
      - coredump-network

volumes:
  mysql_data:

networks:
  coredump-network:
    driver: bridge
```

#### 2.2.3 Docker部署命令

```bash
# 构建和启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f coredump-system

# 停止服务
docker-compose down
```

### 2.3 系统服务配置

#### 2.3.1 Systemd服务配置

```ini
# /etc/systemd/system/coredump-system.service
[Unit]
Description=Coredump Record Processing System
After=network.target mysql.service

[Service]
Type=simple
User=coredump
Group=coredump
WorkingDirectory=/opt/coredump-system
ExecStart=/opt/coredump-system/coredump-system
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=coredump-system

# 环境变量
Environment=CONFIG_PATH=/etc/coredump-system
Environment=LOG_LEVEL=info

# 资源限制
LimitNOFILE=65536
LimitNPROC=32768

[Install]
WantedBy=multi-user.target
```

#### 2.3.2 服务管理命令

```bash
# 重载systemd配置
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable coredump-system

# 启动服务
sudo systemctl start coredump-system

# 查看服务状态
sudo systemctl status coredump-system

# 查看服务日志
sudo journalctl -u coredump-system -f

# 重启服务
sudo systemctl restart coredump-system

# 停止服务
sudo systemctl stop coredump-system
```

## 3. 配置验证

### 3.1 配置文件验证

#### 3.1.1 配置验证脚本

```bash
#!/bin/bash
# config_validator.sh

echo "=== Coredump系统配置验证 ==="

CONFIG_DIR="/etc/coredump-system"
ERRORS=0

# 检查配置文件是否存在
check_config_file() {
    local file=$1
    if [ -f "$CONFIG_DIR/$file" ]; then
        echo "✓ 配置文件存在: $file"
    else
        echo "✗ 配置文件缺失: $file"
        ERRORS=$((ERRORS + 1))
    fi
}

# 检查YAML格式
check_yaml_format() {
    local file=$1
    if command -v yq &> /dev/null; then
        if yq eval '.' "$CONFIG_DIR/$file" > /dev/null 2>&1; then
            echo "✓ YAML格式正确: $file"
        else
            echo "✗ YAML格式错误: $file"
            ERRORS=$((ERRORS + 1))
        fi
    else
        echo "⚠ 无法验证YAML格式，请安装yq工具"
    fi
}

# 检查必要配置项
check_required_config() {
    local config_file="$CONFIG_DIR/application.yml"
    
    # 检查数据库配置
    if grep -q "database:" "$config_file"; then
        echo "✓ 数据库配置存在"
    else
        echo "✗ 数据库配置缺失"
        ERRORS=$((ERRORS + 1))
    fi
    
    # 检查飞书配置
    if grep -q "feishudoc:" "$config_file"; then
        echo "✓ 飞书配置存在"
    else
        echo "✗ 飞书配置缺失"
        ERRORS=$((ERRORS + 1))
    fi
}

# 执行检查
check_config_file "application.yml"
check_config_file "coredump_config.yaml"
check_config_file "error_handling.yaml"

check_yaml_format "application.yml"
check_yaml_format "coredump_config.yaml"
check_yaml_format "error_handling.yaml"

check_required_config

# 输出结果
if [ $ERRORS -eq 0 ]; then
    echo "✓ 所有配置验证通过"
    exit 0
else
    echo "✗ 发现 $ERRORS 个配置错误"
    exit 1
fi
```

### 3.2 连接性测试

#### 3.2.1 数据库连接测试

```bash
#!/bin/bash
# test_database.sh

echo "测试数据库连接..."

DB_HOST="localhost"
DB_PORT="3306"
DB_USER="coredump_user"
DB_PASSWORD="secure_password"
DB_NAME="coredump_system"

# 测试连接
mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME; SELECT 1 as test;" 2>/dev/null

if [ $? -eq 0 ]; then
    echo "✓ 数据库连接成功"
    
    # 检查表是否存在
    TABLE_COUNT=$(mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -D "$DB_NAME" -e "SHOW TABLES LIKE 'processed_records';" 2>/dev/null | wc -l)
    
    if [ "$TABLE_COUNT" -gt 0 ]; then
        echo "✓ 数据表存在"
    else
        echo "⚠ 数据表不存在，系统将自动创建"
    fi
else
    echo "✗ 数据库连接失败"
    exit 1
fi
```

#### 3.2.2 飞书API连接测试

```bash
#!/bin/bash
# test_feishu_api.sh

echo "测试飞书API连接..."

APP_ID="your_app_id"
APP_SECRET="your_app_secret"

# 获取访问令牌
TOKEN_RESPONSE=$(curl -s -X POST "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal" \
  -H "Content-Type: application/json" \
  -d "{\"app_id\":\"$APP_ID\",\"app_secret\":\"$APP_SECRET\"}")

if echo "$TOKEN_RESPONSE" | grep -q "tenant_access_token"; then
    echo "✓ 飞书API认证成功"
    
    # 提取访问令牌
    ACCESS_TOKEN=$(echo "$TOKEN_RESPONSE" | jq -r '.tenant_access_token')
    
    # 测试多维表格访问
    APP_TOKEN="your_app_token"
    TABLE_ID="your_table_id"
    
    TABLE_RESPONSE=$(curl -s -X GET "https://open.feishu.cn/open-apis/bitable/v1/apps/$APP_TOKEN/tables/$TABLE_ID/records" \
      -H "Authorization: Bearer $ACCESS_TOKEN" \
      -H "Content-Type: application/json")
    
    if echo "$TABLE_RESPONSE" | grep -q "records"; then
        echo "✓ 多维表格访问成功"
    else
        echo "✗ 多维表格访问失败"
        echo "响应: $TABLE_RESPONSE"
    fi
else
    echo "✗ 飞书API认证失败"
    echo "响应: $TOKEN_RESPONSE"
    exit 1
fi
```

#### 3.2.3 Bug系统连接测试

```bash
#!/bin/bash
# test_bug_system.sh

echo "测试Bug系统连接..."

BUG_SYSTEM_URL="http://bugs.ruijie.com.cn/bug_switch/service/outinterface_submitBugInfo"

# 测试连接
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$BUG_SYSTEM_URL")

if [ "$HTTP_CODE" -eq 200 ] || [ "$HTTP_CODE" -eq 405 ]; then
    echo "✓ Bug系统连接成功 (HTTP $HTTP_CODE)"
else
    echo "✗ Bug系统连接失败 (HTTP $HTTP_CODE)"
    exit 1
fi
```

## 4. 功能测试

### 4.1 单元测试

#### 4.1.1 运行单元测试

```bash
# 运行所有单元测试
go test ./application/controllers/openfeishu/coredump -v

# 运行特定测试
go test ./application/controllers/openfeishu/coredump -run TestCoredumpFieldMapper -v

# 生成测试覆盖率报告
go test ./application/controllers/openfeishu/coredump -coverprofile=coverage.out
go tool cover -html=coverage.out -o coverage.html
```

#### 4.1.2 测试用例示例

```go
// coredump_integration_test.go
package coredump

import (
    "testing"
    "time"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/suite"
)

type CoredumpIntegrationTestSuite struct {
    suite.Suite
    service *CoredumpService
    tracker *CoredumpTracker
}

func (suite *CoredumpIntegrationTestSuite) SetupSuite() {
    // 设置测试环境
    suite.service = NewCoredumpService()
    suite.tracker = NewCoredumpTracker()
}

func (suite *CoredumpIntegrationTestSuite) TestCompleteProcessingFlow() {
    // 创建测试记录
    testRecord := &CoredumpRecord{
        RecordID:          "test_record_001",
        SN:                "TEST123456",
        CoredumpComponent: "测试组件",
        SoftwareVersion:   "v1.0.0-test",
        DeviceModel:       "TEST-DEVICE",
        CoredumpCollectURL: "http://test.example.com/coredump/123",
        ComponentResponsible: []FeishuPersonField{
            {Email: "<EMAIL>", Name: "测试用户"},
        },
        CoredumpTime: time.Now(),
    }
    
    // 测试字段映射
    bugInfo, err := suite.service.fieldMapper.MapToBugInfo(testRecord)
    suite.NoError(err)
    suite.Equal("测试组件", bugInfo.Product)
    suite.Contains(bugInfo.Summary, "TEST123456")
    
    // 测试状态跟踪
    processed, err := suite.tracker.IsProcessed(testRecord.RecordID)
    suite.NoError(err)
    suite.False(processed)
    
    // 标记为已处理
    err = suite.tracker.MarkAsProcessed(testRecord.RecordID, testRecord.SN, "TEST-BUG-001")
    suite.NoError(err)
    
    // 验证状态
    processed, err = suite.tracker.IsProcessed(testRecord.RecordID)
    suite.NoError(err)
    suite.True(processed)
    
    // 清理测试数据
    suite.tracker.ResetProcessedStatus([]string{testRecord.RecordID})
}

func (suite *CoredumpIntegrationTestSuite) TestErrorHandling() {
    // 测试错误处理
    invalidRecord := &CoredumpRecord{
        RecordID: "invalid_record",
        // 缺少必要字段
    }
    
    _, err := suite.service.fieldMapper.MapToBugInfo(invalidRecord)
    suite.Error(err)
}

func TestCoredumpIntegrationTestSuite(t *testing.T) {
    suite.Run(t, new(CoredumpIntegrationTestSuite))
}
```

### 4.2 API测试

#### 4.2.1 API测试脚本

```bash
#!/bin/bash
# api_test.sh

BASE_URL="http://localhost:8080"
API_BASE="$BASE_URL/api/coredump"

echo "=== Coredump API 功能测试 ==="

# 测试健康检查
echo "1. 测试健康检查..."
HEALTH_RESPONSE=$(curl -s "$BASE_URL/health")
echo "健康检查响应: $HEALTH_RESPONSE"

if echo "$HEALTH_RESPONSE" | grep -q "healthy"; then
    echo "✓ 健康检查通过"
else
    echo "✗ 健康检查失败"
    exit 1
fi

# 测试处理状态查询
echo "2. 测试处理状态查询..."
STATUS_RESPONSE=$(curl -s "$API_BASE/status")
echo "状态查询响应: $STATUS_RESPONSE"

if echo "$STATUS_RESPONSE" | grep -q "success"; then
    echo "✓ 状态查询成功"
else
    echo "✗ 状态查询失败"
fi

# 测试已处理记录查询
echo "3. 测试已处理记录查询..."
RECORDS_RESPONSE=$(curl -s "$API_BASE/processed?page=1&page_size=5")
echo "记录查询响应: $RECORDS_RESPONSE"

if echo "$RECORDS_RESPONSE" | grep -q "success"; then
    echo "✓ 记录查询成功"
else
    echo "✗ 记录查询失败"
fi

# 测试手动处理触发（谨慎使用）
echo "4. 测试手动处理触发..."
read -p "是否执行手动处理测试？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    PROCESS_RESPONSE=$(curl -s -X POST "$API_BASE/process")
    echo "处理响应: $PROCESS_RESPONSE"
    
    if echo "$PROCESS_RESPONSE" | grep -q "success"; then
        echo "✓ 手动处理触发成功"
    else
        echo "✗ 手动处理触发失败"
    fi
else
    echo "跳过手动处理测试"
fi

echo "=== API测试完成 ==="
```

#### 4.2.2 性能测试

```bash
#!/bin/bash
# performance_test.sh

echo "=== Coredump API 性能测试 ==="

API_URL="http://localhost:8080/api/coredump/status"
CONCURRENT_USERS=10
REQUESTS_PER_USER=100

# 使用Apache Bench进行性能测试
if command -v ab &> /dev/null; then
    echo "使用Apache Bench进行性能测试..."
    ab -n $((CONCURRENT_USERS * REQUESTS_PER_USER)) -c $CONCURRENT_USERS "$API_URL"
elif command -v wrk &> /dev/null; then
    echo "使用wrk进行性能测试..."
    wrk -t$CONCURRENT_USERS -c$CONCURRENT_USERS -d30s "$API_URL"
else
    echo "未找到性能测试工具，请安装ab或wrk"
    
    # 简单的并发测试
    echo "执行简单并发测试..."
    for i in $(seq 1 $CONCURRENT_USERS); do
        (
            for j in $(seq 1 10); do
                curl -s "$API_URL" > /dev/null
            done
        ) &
    done
    wait
    echo "简单并发测试完成"
fi
```

### 4.3 集成测试

#### 4.3.1 端到端测试

```bash
#!/bin/bash
# e2e_test.sh

echo "=== 端到端集成测试 ==="

# 1. 准备测试数据
echo "1. 准备测试环境..."

# 创建测试用的飞书记录（需要手动在飞书多维表格中创建）
TEST_RECORD_ID="test_e2e_$(date +%s)"
echo "测试记录ID: $TEST_RECORD_ID"

# 2. 执行处理流程
echo "2. 执行处理流程..."
PROCESS_RESPONSE=$(curl -s -X POST "http://localhost:8080/api/coredump/process")

if echo "$PROCESS_RESPONSE" | grep -q "success"; then
    echo "✓ 处理流程执行成功"
    
    # 提取处理结果
    SUCCESS_COUNT=$(echo "$PROCESS_RESPONSE" | jq -r '.data.success_records // 0')
    FAILED_COUNT=$(echo "$PROCESS_RESPONSE" | jq -r '.data.failed_records // 0')
    
    echo "成功处理: $SUCCESS_COUNT 条记录"
    echo "处理失败: $FAILED_COUNT 条记录"
else
    echo "✗ 处理流程执行失败"
    echo "响应: $PROCESS_RESPONSE"
    exit 1
fi

# 3. 验证处理结果
echo "3. 验证处理结果..."
sleep 5  # 等待处理完成

RECORDS_RESPONSE=$(curl -s "http://localhost:8080/api/coredump/processed?page=1&page_size=10")
if echo "$RECORDS_RESPONSE" | grep -q "success"; then
    RECORD_COUNT=$(echo "$RECORDS_RESPONSE" | jq -r '.data.records | length')
    echo "✓ 找到 $RECORD_COUNT 条已处理记录"
else
    echo "✗ 获取处理记录失败"
fi

# 4. 检查错误日志
echo "4. 检查错误日志..."
if [ -f "/var/log/coredump-system/error.log" ]; then
    ERROR_COUNT=$(grep -c "ERROR" /var/log/coredump-system/error.log 2>/dev/null || echo 0)
    echo "错误日志条数: $ERROR_COUNT"
    
    if [ "$ERROR_COUNT" -gt 0 ]; then
        echo "最近的错误:"
        tail -n 5 /var/log/coredump-system/error.log
    fi
else
    echo "错误日志文件不存在"
fi

echo "=== 端到端测试完成 ==="
```

## 5. 监控和维护

### 5.1 监控脚本

#### 5.1.1 系统监控脚本

```bash
#!/bin/bash
# system_monitor.sh

MONITOR_LOG="/var/log/coredump-system/monitor.log"
API_URL="http://localhost:8080/api"

log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$MONITOR_LOG"
}

# 检查服务状态
check_service_status() {
    if systemctl is-active --quiet coredump-system; then
        log_message "✓ 服务运行正常"
        return 0
    else
        log_message "✗ 服务未运行"
        return 1
    fi
}

# 检查API健康状态
check_api_health() {
    HEALTH_RESPONSE=$(curl -s --max-time 10 "$API_URL/health" 2>/dev/null)
    
    if echo "$HEALTH_RESPONSE" | grep -q "healthy"; then
        log_message "✓ API健康检查通过"
        return 0
    else
        log_message "✗ API健康检查失败"
        return 1
    fi
}

# 检查系统资源
check_system_resources() {
    # CPU使用率
    CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
    
    # 内存使用率
    MEMORY_USAGE=$(free | awk 'NR==2{printf "%.1f", $3*100/$2}')
    
    # 磁盘使用率
    DISK_USAGE=$(df -h / | awk 'NR==2{print $5}' | sed 's/%//')
    
    log_message "系统资源 - CPU: ${CPU_USAGE}%, 内存: ${MEMORY_USAGE}%, 磁盘: ${DISK_USAGE}%"
    
    # 检查资源使用是否过高
    if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
        log_message "⚠ CPU使用率过高: ${CPU_USAGE}%"
    fi
    
    if (( $(echo "$MEMORY_USAGE > 80" | bc -l) )); then
        log_message "⚠ 内存使用率过高: ${MEMORY_USAGE}%"
    fi
    
    if [ "$DISK_USAGE" -gt 80 ]; then
        log_message "⚠ 磁盘使用率过高: ${DISK_USAGE}%"
    fi
}

# 检查处理统计
check_processing_stats() {
    STATS_RESPONSE=$(curl -s --max-time 10 "$API_URL/coredump/status" 2>/dev/null)
    
    if echo "$STATS_RESPONSE" | grep -q "success"; then
        PROCESSED_COUNT=$(echo "$STATS_RESPONSE" | jq -r '.data.processed_count // 0')
        FAILED_COUNT=$(echo "$STATS_RESPONSE" | jq -r '.data.failed_count // 0')
        
        log_message "处理统计 - 成功: $PROCESSED_COUNT, 失败: $FAILED_COUNT"
        
        # 检查失败率
        if [ "$PROCESSED_COUNT" -gt 0 ] && [ "$FAILED_COUNT" -gt 0 ]; then
            FAILURE_RATE=$(echo "scale=2; $FAILED_COUNT * 100 / ($PROCESSED_COUNT + $FAILED_COUNT)" | bc)
            if (( $(echo "$FAILURE_RATE > 10" | bc -l) )); then
                log_message "⚠ 失败率过高: ${FAILURE_RATE}%"
            fi
        fi
    else
        log_message "✗ 无法获取处理统计"
    fi
}

# 主监控循环
main() {
    log_message "开始系统监控检查"
    
    check_service_status
    check_api_health
    check_system_resources
    check_processing_stats
    
    log_message "监控检查完成"
}

# 如果作为脚本直接运行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
```

#### 5.1.2 定时监控任务

```bash
# 添加到crontab
# 每5分钟执行一次监控检查
*/5 * * * * /opt/coredump-system/scripts/system_monitor.sh

# 每小时生成监控报告
0 * * * * /opt/coredump-system/scripts/generate_report.sh

# 每天清理旧日志
0 2 * * * /opt/coredump-system/scripts/cleanup_logs.sh
```

### 5.2 维护脚本

#### 5.2.1 日志清理脚本

```bash
#!/bin/bash
# cleanup_logs.sh

LOG_DIR="/var/log/coredump-system"
RETENTION_DAYS=30

echo "开始清理日志文件..."

# 清理应用日志
find "$LOG_DIR" -name "*.log" -type f -mtime +$RETENTION_DAYS -delete
echo "清理了超过 $RETENTION_DAYS 天的应用日志"

# 清理监控日志
find "$LOG_DIR" -name "monitor.log.*" -type f -mtime +$RETENTION_DAYS -delete
echo "清理了超过 $RETENTION_DAYS 天的监控日志"

# 清理压缩日志
find "$LOG_DIR" -name "*.gz" -type f -mtime +$RETENTION_DAYS -delete
echo "清理了超过 $RETENTION_DAYS 天的压缩日志"

# 显示当前日志目录大小
LOG_SIZE=$(du -sh "$LOG_DIR" | cut -f1)
echo "当前日志目录大小: $LOG_SIZE"

echo "日志清理完成"
```

#### 5.2.2 数据库维护脚本

```bash
#!/bin/bash
# database_maintenance.sh

DB_HOST="localhost"
DB_USER="coredump_user"
DB_PASSWORD="secure_password"
DB_NAME="coredump_system"

echo "开始数据库维护..."

# 清理过期的处理记录（保留90天）
mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWORD" -D "$DB_NAME" << EOF
DELETE FROM processed_records 
WHERE processed_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
EOF

echo "清理了90天前的处理记录"

# 优化表
mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWORD" -D "$DB_NAME" << EOF
OPTIMIZE TABLE processed_records;
EOF

echo "优化了数据表"

# 备份数据库
BACKUP_FILE="/backup/coredump_system_$(date +%Y%m%d_%H%M%S).sql"
mkdir -p /backup

mysqldump -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" > "$BACKUP_FILE"
gzip "$BACKUP_FILE"

echo "数据库备份完成: ${BACKUP_FILE}.gz"

# 清理旧备份（保留7天）
find /backup -name "coredump_system_*.sql.gz" -type f -mtime +7 -delete
echo "清理了7天前的备份文件"

echo "数据库维护完成"
```

### 5.3 故障恢复

#### 5.3.1 自动恢复脚本

```bash
#!/bin/bash
# auto_recovery.sh

SERVICE_NAME="coredump-system"
MAX_RESTART_ATTEMPTS=3
RESTART_DELAY=30

log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a /var/log/coredump-system/recovery.log
}

# 检查服务状态
check_service() {
    systemctl is-active --quiet "$SERVICE_NAME"
    return $?
}

# 重启服务
restart_service() {
    log_message "尝试重启服务: $SERVICE_NAME"
    systemctl restart "$SERVICE_NAME"
    sleep $RESTART_DELAY
    
    if check_service; then
        log_message "服务重启成功"
        return 0
    else
        log_message "服务重启失败"
        return 1
    fi
}

# 检查API健康状态
check_api_health() {
    local response=$(curl -s --max-time 10 "http://localhost:8080/health" 2>/dev/null)
    echo "$response" | grep -q "healthy"
    return $?
}

# 主恢复逻辑
main() {
    log_message "开始自动恢复检查"
    
    # 检查服务状态
    if ! check_service; then
        log_message "服务未运行，尝试恢复"
        
        for attempt in $(seq 1 $MAX_RESTART_ATTEMPTS); do
            log_message "重启尝试 $attempt/$MAX_RESTART_ATTEMPTS"
            
            if restart_service; then
                # 等待服务完全启动
                sleep 10
                
                # 检查API健康状态
                if check_api_health; then
                    log_message "服务恢复成功"
                    exit 0
                else
                    log_message "服务启动但API不健康"
                fi
            fi
            
            if [ $attempt -lt $MAX_RESTART_ATTEMPTS ]; then
                log_message "等待 $RESTART_DELAY 秒后重试"
                sleep $RESTART_DELAY
            fi
        done
        
        log_message "自动恢复失败，需要人工干预"
        # 发送告警
        curl -X POST "http://localhost:8080/api/alert" \
            -H "Content-Type: application/json" \
            -d '{"level":"critical","message":"Coredump系统自动恢复失败"}' 2>/dev/null
        
        exit 1
    else
        log_message "服务运行正常"
        
        # 检查API健康状态
        if ! check_api_health; then
            log_message "API健康检查失败，尝试重启服务"
            restart_service
        fi
    fi
    
    log_message "自动恢复检查完成"
}

main "$@"
```

## 6. 升级和回滚

### 6.1 升级脚本

```bash
#!/bin/bash
# upgrade.sh

CURRENT_VERSION=$(./coredump-system --version 2>/dev/null | grep -o 'v[0-9]\+\.[0-9]\+\.[0-9]\+' || echo "unknown")
NEW_VERSION="$1"
BACKUP_DIR="/backup/coredump-system"

if [ -z "$NEW_VERSION" ]; then
    echo "用法: $0 <新版本号>"
    echo "当前版本: $CURRENT_VERSION"
    exit 1
fi

echo "=== Coredump系统升级 ==="
echo "当前版本: $CURRENT_VERSION"
echo "目标版本: $NEW_VERSION"

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 1. 备份当前版本
echo "1. 备份当前版本..."
BACKUP_FILE="$BACKUP_DIR/coredump-system-$CURRENT_VERSION-$(date +%Y%m%d_%H%M%S).tar.gz"
tar -czf "$BACKUP_FILE" -C /opt coredump-system
echo "备份完成: $BACKUP_FILE"

# 2. 停止服务
echo "2. 停止服务..."
systemctl stop coredump-system
echo "服务已停止"

# 3. 下载新版本
echo "3. 下载新版本..."
NEW_BINARY="coredump-system-$NEW_VERSION"
if [ -f "$NEW_BINARY" ]; then
    echo "使用本地文件: $NEW_BINARY"
else
    echo "从远程下载: $NEW_BINARY"
    # wget "https://releases.example.com/$NEW_BINARY" -O "$NEW_BINARY"
    echo "请手动下载新版本文件"
    exit 1
fi

# 4. 备份配置文件
echo "4. 备份配置文件..."
cp -r /etc/coredump-system "$BACKUP_DIR/config-$(date +%Y%m%d_%H%M%S)"

# 5. 替换二进制文件
echo "5. 替换二进制文件..."
chmod +x "$NEW_BINARY"
cp "$NEW_BINARY" /opt/coredump-system/coredump-system

# 6. 数据库迁移（如果需要）
echo "6. 检查数据库迁移..."
# 这里可以添加数据库迁移逻辑

# 7. 启动服务
echo "7. 启动服务..."
systemctl start coredump-system

# 8. 验证升级
echo "8. 验证升级..."
sleep 10

if systemctl is-active --quiet coredump-system; then
    NEW_RUNNING_VERSION=$(./coredump-system --version 2>/dev/null | grep -o 'v[0-9]\+\.[0-9]\+\.[0-9]\+' || echo "unknown")
    echo "✓ 服务启动成功"
    echo "✓ 当前运行版本: $NEW_RUNNING_VERSION"
    
    # API健康检查
    if curl -s --max-time 10 "http://localhost:8080/health" | grep -q "healthy"; then
        echo "✓ API健康检查通过"
        echo "升级成功完成！"
    else
        echo "✗ API健康检查失败"
        echo "升级可能存在问题，请检查日志"
    fi
else
    echo "✗ 服务启动失败"
    echo "升级失败，请检查日志或执行回滚"
    exit 1
fi
```

### 6.2 回滚脚本

```bash
#!/bin/bash
# rollback.sh

BACKUP_DIR="/backup/coredump-system"
BACKUP_FILE="$1"

if [ -z "$BACKUP_FILE" ]; then
    echo "可用的备份文件:"
    ls -la "$BACKUP_DIR"/*.tar.gz 2>/dev/null || echo "没有找到备份文件"
    echo
    echo "用法: $0 <备份文件路径>"
    exit 1
fi

if [ ! -f "$BACKUP_FILE" ]; then
    echo "备份文件不存在: $BACKUP_FILE"
    exit 1
fi

echo "=== Coredump系统回滚 ==="
echo "使用备份文件: $BACKUP_FILE"

read -p "确认执行回滚操作？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "回滚操作已取消"
    exit 0
fi

# 1. 停止服务
echo "1. 停止服务..."
systemctl stop coredump-system

# 2. 恢复备份
echo "2. 恢复备份..."
cd /opt
tar -xzf "$BACKUP_FILE"
echo "备份恢复完成"

# 3. 启动服务
echo "3. 启动服务..."
systemctl start coredump-system

# 4. 验证回滚
echo "4. 验证回滚..."
sleep 10

if systemctl is-active --quiet coredump-system; then
    echo "✓ 服务启动成功"
    
    # API健康检查
    if curl -s --max-time 10 "http://localhost:8080/health" | grep -q "healthy"; then
        echo "✓ API健康检查通过"
        echo "回滚成功完成！"
    else
        echo "✗ API健康检查失败"
        echo "回滚可能存在问题，请检查日志"
    fi
else
    echo "✗ 服务启动失败"
    echo "回滚失败，请手动检查"
    exit 1
fi
```

## 7. 常见问题和解决方案

### 7.1 常见问题排查

#### 7.1.1 服务启动失败

```bash
# 问题排查步骤
echo "=== 服务启动失败排查 ==="

# 1. 检查服务状态
systemctl status coredump-system

# 2. 查看服务日志
journalctl -u coredump-system -n 50

# 3. 检查配置文件
echo "检查配置文件..."
if [ -f "/etc/coredump-system/application.yml" ]; then
    echo "✓ 配置文件存在"
    # 检查YAML格式
    python -c "import yaml; yaml.safe_load(open('/etc/coredump-system/application.yml'))" 2>/dev/null && echo "✓ YAML格式正确" || echo "✗ YAML格式错误"
else
    echo "✗ 配置文件不存在"
fi

# 4. 检查端口占用
echo "检查端口占用..."
netstat -tlnp | grep :8080 || echo "端口8080未被占用"

# 5. 检查文件权限
echo "检查文件权限..."
ls -la /opt/coredump-system/coredump-system
```

#### 7.1.2 数据库连接问题

```bash
# 数据库连接问题排查
echo "=== 数据库连接问题排查 ==="

DB_HOST="localhost"
DB_PORT="3306"
DB_USER="coredump_user"
DB_PASSWORD="secure_password"
DB_NAME="coredump_system"

# 1. 检查数据库服务
systemctl status mysql

# 2. 测试网络连接
telnet "$DB_HOST" "$DB_PORT" < /dev/null && echo "✓ 网络连接正常" || echo "✗ 网络连接失败"

# 3. 测试数据库登录
mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" 2>/dev/null && echo "✓ 数据库登录成功" || echo "✗ 数据库登录失败"

# 4. 检查数据库权限
mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -e "SHOW GRANTS;" 2>/dev/null

# 5. 检查表结构
mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -D "$DB_NAME" -e "SHOW TABLES;" 2>/dev/null
```

#### 7.1.3 飞书API连接问题

```bash
# 飞书API连接问题排查
echo "=== 飞书API连接问题排查 ==="

# 1. 检查网络连接
curl -s --max-time 10 "https://open.feishu.cn" > /dev/null && echo "✓ 飞书服务可访问" || echo "✗ 飞书服务不可访问"

# 2. 检查应用配置
APP_ID="your_app_id"
APP_SECRET="your_app_secret"

if [ -z "$APP_ID" ] || [ -z "$APP_SECRET" ]; then
    echo "✗ 飞书应用配置缺失"
else
    echo "✓ 飞书应用配置存在"
    
    # 3. 测试认证
    TOKEN_RESPONSE=$(curl -s -X POST "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal" \
      -H "Content-Type: application/json" \
      -d "{\"app_id\":\"$APP_ID\",\"app_secret\":\"$APP_SECRET\"}")
    
    if echo "$TOKEN_RESPONSE" | grep -q "tenant_access_token"; then
        echo "✓ 飞书认证成功"
    else
        echo "✗ 飞书认证失败"
        echo "响应: $TOKEN_RESPONSE"
    fi
fi
```

### 7.2 性能优化建议

#### 7.2.1 数据库优化

```sql
-- 数据库性能优化建议

-- 1. 添加索引
CREATE INDEX idx_processed_records_composite ON processed_records(status, processed_at);
CREATE INDEX idx_processed_records_sn_status ON processed_records(sn, status);

-- 2. 分析表统计信息
ANALYZE TABLE processed_records;

-- 3. 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';

-- 4. 优化配置建议
-- 在my.cnf中添加:
-- [mysqld]
-- innodb_buffer_pool_size = 1G
-- innodb_log_file_size = 256M
-- max_connections = 200
-- query_cache_size = 64M
```

#### 7.2.2 应用优化

```yaml
# 应用性能优化配置
performance:
  # 数据库连接池
  database:
    max_idle_conns: 20
    max_open_conns: 100
    conn_max_lifetime: "1h"
  
  # HTTP客户端
  http_client:
    timeout: "30s"
    max_idle_conns: 100
    max_idle_conns_per_host: 10
  
  # 并发处理
  processing:
    worker_count: 10
    batch_size: 50
    queue_size: 1000
  
  # 缓存配置
  cache:
    enabled: true
    ttl: "1h"
    max_size: 1000
```

这个部署和测试指南提供了：

1. **完整的部署流程**：从环境准备到服务配置
2. **多种部署方式**：源码部署和Docker部署
3. **全面的测试方案**：单元测试、API测试、集成测试
4. **监控和维护**：系统监控、日志管理、故障恢复
5. **升级和回滚**：版本管理和安全升级流程
6. **问题排查**：常见问题的诊断和解决方案
7. **性能优化**：数据库和应用层面的优化建议

该指南确保了系统的可靠部署、稳定运行和持续维护。