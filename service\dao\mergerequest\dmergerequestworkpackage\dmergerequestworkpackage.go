package dmergerequestworkpackage

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models"
	"irisAdminApi/application/models/mergerequest"
	"irisAdminApi/service/dao/user/duser"

	"github.com/pkg/errors"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "MR表单工作包"

type User struct {
	ID       uint   `json:"id"`
	Name     string `json:"name"`
	Username string `json:"username"`
}

type ReleaseProject struct {
	models.ModelBase
	Name string `gorm:"uniqueIndex; not null; type:varchar(60)" json:"name"`
}

type MergeRequestWorkPackage struct {
	mergerequest.MergeRequestWorkPackage
	Owner          User           `gorm:"->" json:"owner"`
	Pstl           User           `gorm:"->" json:"pstl"`
	ReleaseProject ReleaseProject `gorm:"->" json:"release_project"`
}

type ListResponse struct {
	MergeRequestWorkPackage
}

type Request struct {
	mergerequest.MergeRequest
	Targets []string `json:"targets"`
}

func (a *MergeRequestWorkPackage) ModelName() string {
	return ModelName
}

func Model() *mergerequest.MergeRequestWorkPackage {
	return &mergerequest.MergeRequestWorkPackage{}
}

func (a *MergeRequestWorkPackage) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	where := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		where = where.Where("group_name_ch like ?", fmt.Sprintf("%%%s%%", name)).
			Or("group_name_en like ?", fmt.Sprintf("%%%s%%", name)).
			Or("category_name_ch like ?", fmt.Sprintf("%%%s%%", name)).
			Or("category_name_en like ?", fmt.Sprintf("%%%s%%", name)).
			Or("description_ch like ?", fmt.Sprintf("%%%s%%", name)).
			Or("description_en like ?", fmt.Sprintf("%%%s%%", name))
		if name == "海" || name == "海外" || name == "海外库" {
			where = where.Or("oversea = 1")
		}
		if name == "大" || name == "大库" {
			where = where.Or("large = 1")
		}
		if name == "小" || name == "小库" {
			where = where.Or("small = 1")
		}
		if name == "中" || name == "中库" {
			where = where.Or("middle = 1")
		}
		if name == "默" || name == "默认" || name == "默认阻" || name == "默认阻断" || name == "阻" || name == "阻断" {
			where = where.Or("pre_def_block = 1")
		}
	}
	db = db.Where(where)
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *MergeRequestWorkPackage) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *MergeRequestWorkPackage) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *MergeRequestWorkPackage) CreateV2(object interface{}) error {
	return nil
}

func (a *MergeRequestWorkPackage) BatchCreate(object []map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (a *MergeRequestWorkPackage) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *MergeRequestWorkPackage) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *MergeRequestWorkPackage) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *MergeRequestWorkPackage) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *MergeRequestWorkPackage) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *MergeRequestWorkPackage) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindByReleaseProjectID(releaseProjectID uint, page, pageSize int, sort, orderBy string) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse
	db := easygorm.GetEasyGormDb().Model(Model()).Preload("Owner").Preload("Pstl").Preload("ReleaseProject").Where("release_project_id = ?", releaseProjectID)

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	if count == 0 {
		db = easygorm.GetEasyGormDb().Model(Model()).Preload("Owner").Preload("Pstl").Preload("ReleaseProject").Where("release_project_id = 0")
		err = db.Count(&count).Error
		if err != nil {
			logging.ErrorLogger.Errorf("get list data err ", err)
			return nil, err
		}
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func ImportWorkPackage(releaseProjectID uint, items []map[string]interface{}) error {
	workpackages := []map[string]interface{}{}
	// project := ReleaseProject{}
	// err := easygorm.GetEasyGormDb().Model(&project).Where("name = ?", items[0]["Project"]).Find(&project).Error
	// if err != nil {
	// 	return err
	// }
	// if project.ID == 0 {
	// 	return errors.New(fmt.Sprintf("未到此项目: %s", items[0]["Project"].(string)))
	// }
	names := []string{}
	for _, item := range items {
		names = append(names, item["User"].(string))
		names = append(names, item["PSTL"].(string))
	}
	users, err := duser.FindInNames(names)
	if err != nil {
		logging.ErrorLogger.Errorf("find users by name err ", err)
		return err
	}
	userMap := map[string]uint{}
	for _, user := range users {
		userMap[user.Name] = user.ID
		userMap[user.Username] = user.ID
	}

	for _, item := range items {
		if _, ok := userMap[item["User"].(string)]; !ok && item["User"].(string) != "" {
			return errors.Errorf("负责人:%s 不存在, 请确认！", item["User"].(string))
		}
		if _, ok := userMap[item["PSTL"].(string)]; !ok && item["PSTL"].(string) != "" {
			return errors.Errorf("PSTL:%s 不存在, 请确认！", item["PSTL"].(string))
		}

		workPackage := map[string]interface{}{
			"ReleaseProjectID":            releaseProjectID,
			"Name":                        item["Name"].(string),
			"CodeQuantity":                float32(item["CodeQuantity"].(float64)),
			"PortedCodeQuantity":          float32(item["PortedCodeQuantity"].(float64)),
			"TemporaryPortedCodeQuantity": float32(item["TemporaryPortedCodeQuantity"].(float64)),
			"TotalCodeQuantity":           float32(item["TotalCodeQuantity"].(float64)),
			"WorkGroup":                   item["WorkGroup"].(string),
			"Requirement":                 item["Requirement"].(string),
			"OwnerID":                     userMap[item["User"].(string)],
			"PstlID":                      userMap[item["PSTL"].(string)],
		}

		workpackages = append(workpackages, workPackage)
	}

	db := easygorm.GetEasyGormDb().Model(Model())
	err = db.Transaction(func(tx *gorm.DB) error {
		err = tx.Model(Model()).Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "name"}, {Name: "release_project_id"}},
			DoUpdates: clause.AssignmentColumns([]string{"code_quantity", "ported_code_quantity", "temporary_ported_code_quantity", "total_code_quantity", "owner_id", "work_group", "requirement", "pstl_id"}),
		}).Create(&workpackages).Error
		if err != nil {
			logging.ErrorLogger.Errorf("create workpackage err ", err)
			return err
		}
		return nil
	})
	return err
}

func SyncWorkPackageFromPms(releaseProjectID uint, items []map[string]interface{}) error {
	workpackages := []map[string]interface{}{}
	// project := ReleaseProject{}
	// err := easygorm.GetEasyGormDb().Model(&project).Where("name = ?", items[0]["Project"]).Find(&project).Error
	// if err != nil {
	// 	return err
	// }
	// if project.ID == 0 {
	// 	return errors.New(fmt.Sprintf("未到此项目: %s", items[0]["Project"].(string)))
	// }
	names := []string{}
	workpackageNames := []string{}
	for _, item := range items {
		names = append(names, item["User"].(string))
		names = append(names, item["PSTL"].(string))
	}
	users, err := duser.FindInNames(names)
	if err != nil {
		logging.ErrorLogger.Errorf("find users by name err ", err)
		return err
	}
	userMap := map[string]uint{}
	for _, user := range users {
		userMap[user.Name] = user.ID
		userMap[user.Username] = user.ID
	}

	for _, item := range items {
		var ownerID, PstlID uint
		if _, ok := userMap[item["User"].(string)]; !ok && item["User"].(string) != "" {
			ownerID = 0
		} else {
			ownerID = userMap[item["User"].(string)]
		}
		if _, ok := userMap[item["PSTL"].(string)]; !ok && item["PSTL"].(string) != "" {
			PstlID = 0
		} else {
			PstlID = userMap[item["PSTL"].(string)]
		}

		workPackage := map[string]interface{}{
			"ReleaseProjectID":            releaseProjectID,
			"Name":                        item["Name"].(string),
			"CodeQuantity":                float32(item["CodeQuantity"].(float32)),
			"PortedCodeQuantity":          float32(item["PortedCodeQuantity"].(float32)),
			"TemporaryPortedCodeQuantity": float32(item["TemporaryPortedCodeQuantity"].(float32)),
			"TotalCodeQuantity":           float32(item["TotalCodeQuantity"].(float32)),
			"WorkGroup":                   item["WorkGroup"].(string),
			"Requirement":                 item["Requirement"].(string),
			"OwnerID":                     ownerID,
			"PstlID":                      PstlID,
		}

		workpackageNames = append(workpackageNames, item["Name"].(string))
		workpackages = append(workpackages, workPackage)
	}

	db := easygorm.GetEasyGormDb().Model(Model())
	err = db.Transaction(func(tx *gorm.DB) error {
		// 删除不在workpackageNames中的记录以及没有关联的MR的记录
		err = tx.Model(Model()).
			Where("name NOT IN ?", workpackageNames).
			Where("release_project_id = ?", releaseProjectID).
			Where("id NOT IN (?)", tx.Table("merge_requests").Distinct("work_package_id").Where("release_project_id = ?", releaseProjectID)).
			Unscoped().Delete(Model()).Error
		err = tx.Model(Model()).Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "name"}, {Name: "release_project_id"}},
			DoUpdates: clause.AssignmentColumns([]string{"code_quantity", "ported_code_quantity", "temporary_ported_code_quantity", "total_code_quantity", "work_group", "requirement", "owner_id"}),
		}).Create(&workpackages).Error
		if err != nil {
			logging.ErrorLogger.Errorf("create workpackage err ", err)
			return err
		}
		return nil
	})
	return err
}

func GetDistinctColumns(releaseProjectID uint, name string) ([]string, error) {
	items := []string{}
	db := easygorm.GetEasyGormDb().Model(Model())
	err := db.Distinct(name).Where("release_project_id = ?", releaseProjectID).Find(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

type PmsWorkpacketInfo struct {
	WorkPacketID       uint    `json:"work_packet_id"`
	RelationID         uint    `json:"relation_id"`
	ProjectName        string  `json:"project_name"`
	WorkPacketName     string  `json:"work_packet_name"`
	RequestName        string  `json:"request_name"`
	AddCodes           float32 `json:"add_codes"`
	TransplantCodes    float32 `json:"transplant_codes"`
	TransplantCodesTmp float32 `json:"transplant_codes_tmp"`
	TotalCodes         float32 `json:"total_codes"`
}

func UpdateWorkPackages() {
}

func FindAllByReleaseProjectID(releaseProjectID uint) ([]*ListResponse, error) {
	var items []*ListResponse
	db := easygorm.GetEasyGormDb().Model(Model()).Preload("Owner").Preload("Pstl").Preload("ReleaseProject").Where("release_project_id = ?", releaseProjectID).Find(&items)
	return items, errors.Wrap(db.Error, "")
}

func FindByReleaseProjectIDV2(releaseProjectID, page, pageSize int, sort, orderBy, name string) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse
	db := easygorm.GetEasyGormDb().Model(Model()).Preload("Owner").Preload("Pstl").Preload("ReleaseProject")
	if releaseProjectID > 0 {
		db = db.Where("release_project_id = ?", releaseProjectID)
	} else {
		db = db.Where("release_project_id != 0")
	}

	if name != "" {
		db = db.Where("name like ?", "%"+name+"%")
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	if orderBy == "" {
		orderBy = "id"
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func FindInIds(ids []uint) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("Owner").Preload("Pstl").Preload("ReleaseProject").
		Where("id in ?", ids).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	return items, nil
}

func FindIDAndReleaseProjectID(id, releaseProjectID uint) (MergeRequestWorkPackage, error) {
	u := MergeRequestWorkPackage{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Where("release_project_id = ?", releaseProjectID).Find(&u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return u, err
	}
	return u, nil
}
