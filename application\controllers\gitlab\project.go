package gitlab

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/gitlab/dgitlabuserrepositoryfavorite"
	"irisAdminApi/service/dao/user/dgitlabtoken"
	"net/http"
	"sort"
	"strconv"
	"strings"

	"github.com/kataras/iris/v12"
)

func GetUserProjects(ctx iris.Context) {
	token, err := dao.GetGitlabToken(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置gitlab token,请联系管理员"))
		return
	}
	url := fmt.Sprintf("%s/api/%s/projects?private_token=%s&simple=true", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, token)

	// page, _ := strconv.Atoi(ctx.FormValue("page"))
	// pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))

	// if page > 0 {
	// 	url = fmt.Sprintf("%s&page=%d", url, page)
	// }
	// if pageSize > 0 {
	// 	url = fmt.Sprintf("%s&per_page=%d", url, pageSize)
	// }
	url = fmt.Sprintf("%s&per_page=%d", url, 100)

	var items []map[string]interface{}
	list := map[string]interface{}{}
	var page = 1
	for i := 0; i <= 10; i++ {
		_url := fmt.Sprintf("%s&page=%d", url, page)
		req, err := http.NewRequest("GET", _url, nil)
		if err != nil {
			logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
			continue
		}

		_result, err := libs.HandlerRequest(req)
		if err != nil {
			logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
			continue
		}
		items = append(items, _result["items"].([]map[string]interface{})...)
		list["total"] = _result["total"]
		list["limit"] = _result["limit"]
		page++
		if _result["total"].(int) <= len(items) {
			break
		}
	}
	list["items"] = items
	// req, err := http.NewRequest("GET", url, nil)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }

	// result, err := libs.HandlerRequest(req)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetProjectIssues(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	projectId, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	token := dgitlabtoken.Response{}
	err = token.FindEx("user_id", strconv.FormatUint(uint64(id), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("get token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if len(token.Token) == 0 {
		logging.ErrorLogger.Errorf("user doesn't hava token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "没有可用的gitlab token，请前往用户资料进行添加"))
		return
	}
	url := fmt.Sprintf("%s/api/%s/projects/%d/issues?private_token=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, projectId, token.Token)

	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))

	if page > 0 {
		url = fmt.Sprintf("%s&page=%d", url, page)
	}
	if pageSize > 0 {
		url = fmt.Sprintf("%s&per_page=%d", url, pageSize)
	}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	result, err := libs.HandlerRequest(req)
	if err != nil {
		logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	for _, item := range result["items"].([]map[string]interface{}) {
		if len([]byte(item["title"].(string))) > 2 {
			item["title"] = string([]byte(item["title"].(string)[0:2])) + "****(长度过长)"
		}
		if len([]byte(item["description"].(string))) > 2 {
			item["description"] = string([]byte(item["description"].(string)[0:2])) + "****(长度过长)"
		}
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func GetProjectCommits(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	projectId, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	token := dgitlabtoken.Response{}
	err = token.FindEx("user_id", strconv.FormatUint(uint64(id), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("get token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if len(token.Token) == 0 {
		logging.ErrorLogger.Errorf("user doesn't hava token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "没有可用的gitlab token，请前往用户资料进行添加"))
		return
	}
	url := fmt.Sprintf("%s/api/%s/projects/%d/repository/commits?private_token=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, projectId, token.Token)

	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	ref_name := ctx.FormValue("ref_name")
	if len(ref_name) > 0 {
		url = fmt.Sprintf("%s&ref_name=%s", url, ref_name)
	}
	if page > 0 {
		url = fmt.Sprintf("%s&page=%d", url, page)
	}
	if pageSize > 0 {
		url = fmt.Sprintf("%s&per_page=%d", url, pageSize)
	}
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	result, err := libs.HandlerRequest(req)
	if err != nil {
		logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func GetProjectBranches(ctx iris.Context) {
	search := ctx.FormValue("search")
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	projectId, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	token := dgitlabtoken.Response{}
	err = token.FindEx("user_id", strconv.FormatUint(uint64(id), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("get token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if len(token.Token) == 0 {
		logging.ErrorLogger.Errorf("user doesn't hava token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "没有可用的gitlab token，请前往用户资料进行添加"))
		return
	}
	url := fmt.Sprintf("%s/api/%s/projects/%d/repository/branches?private_token=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, projectId, token.Token)

	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))

	if len(search) > 0 {
		url = fmt.Sprintf("%s&search=%s", url, search)
	}
	var items []map[string]interface{}
	if pageSize == -1 {
		page = 1
		for {
			url = fmt.Sprintf("%s&page=%d", url, page)
			req, err := http.NewRequest("GET", url, nil)
			if err != nil {
				logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
				return
			}
			result, err := libs.HandlerRequest(req)
			if err != nil {
				logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
				return
			}

			for _, item := range result["items"].([]map[string]interface{}) {
				items = append(items, item)
			}
			if result["total"].(int) <= result["limit"].(int)*page {
				break
			}
			page++
		}
	} else {
		if page > 0 {
			url = fmt.Sprintf("%s&page=%d", url, page)
		}

		if pageSize > 0 {
			url = fmt.Sprintf("%s&per_page=%d", url, pageSize)
		}

		req, err := http.NewRequest("GET", url, nil)
		if err != nil {
			logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		result, err := libs.HandlerRequest(req)
		if err != nil {
			logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}

		for _, item := range result["items"].([]map[string]interface{}) {
			items = append(items, item)
		}
	}

	sort.Slice(items, func(i, j int) bool {
		return items[i]["name"].(string) > items[j]["name"].(string)
	})

	ctx.JSON(response.NewResponse(response.NoErr.Code, items, response.NoErr.Msg))
	return
}

func GetProjectCommitsV2(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	token := dgitlabtoken.Response{}
	err = token.FindEx("user_id", strconv.FormatUint(uint64(id), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("get token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if len(token.Token) == 0 {
		logging.ErrorLogger.Errorf("user doesn't hava token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "没有可用的gitlab token，请前往用户资料进行添加"))
		return
	}
	// token := libs.Config.Buildfarm.Token
	// if len(token) == 0 {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置gitlab token,请联系管理员"))
	// 	return
	// }
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	since := ctx.FormValue("since")
	until := ctx.FormValue("until")
	favorates, _ := dgitlabuserrepositoryfavorite.FindAllByUserId(id)
	if len(favorates) == 0 {
		logging.ErrorLogger.Errorf("user doesn't hava token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "未发现收藏的项目与分支，请先执行收藏操作"))
		return
	}
	var items = map[string]interface{}{
		"items": []map[string]interface{}{},
		"total": 0,
		"limit": pageSize,
	}

	for _, item := range favorates {
		for i := 0; i < 5; i++ {
			_items := []map[string]interface{}{}
			url := fmt.Sprintf("%s/api/%s/projects/%d/repository/commits?private_token=%s&ref_name=%s&page=1&per_page=1000", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, item.RepositoryId, token.Token, item.RepositoryBranch)
			if since != "" {
				url = fmt.Sprintf("%s&since=%s", url, since)
			}
			if until != "" {
				url = fmt.Sprintf("%s&until=%s", url, until)
			}
			req, err := http.NewRequest("GET", url, nil)
			if err != nil {
				logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
				continue
			}

			_result, err := libs.HandlerRequest(req)
			if err != nil {
				logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
				continue
			}
			_items = append(_items, _result["items"].([]map[string]interface{})...)
			items["total"] = items["total"].(int) + len(_result["items"].([]map[string]interface{}))
			for _, i := range _items {
				i["repository_name"] = item.RepositoryName
				i["repository_branch"] = item.RepositoryBranch
				items["items"] = append(items["items"].([]map[string]interface{}), i)
			}
			break

			// url := fmt.Sprintf("%s/api/%s/projects/%d/repository/commits?private_token=%s&ref_name=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, item.RepositoryId, token.Token, item.RepositoryBranch)

			// if since != "" {
			// 	url = fmt.Sprintf("%s&since=%s", url, since)
			// }
			// if until != "" {
			// 	url = fmt.Sprintf("%s&until=%s", url, until)
			// }
			// req, err := http.NewRequest("GET", url, nil)
			// if err != nil {
			// 	logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
			// }
			// // list := map[string]interface{}{"items": result, "total": total, "limit": limit}
			// result, err := libs.HandlerRequest(req)
			// if err != nil {
			// 	logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
			// }
			// for _, i := range result["items"].([]map[string]interface{}) {
			// 	i["repository_name"] = item.RepositoryName
			// 	i["repository_branch"] = item.RepositoryBranch
			// 	items["items"] = append(items["items"].([]map[string]interface{}), i)
			// }
			// items["total"] = items["total"] + result["total"].(int)
			// break
		}
	}
	if page > 0 && pageSize > 0 {
		if page*pageSize >= items["total"].(int) {
			items["items"] = items["items"].([]map[string]interface{})[(page-1)*pageSize : items["total"].(int)]
		} else {
			items["items"] = items["items"].([]map[string]interface{})[(page-1)*pageSize : page*pageSize]
		}
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, items, response.NoErr.Msg))
	return
}

func GetUserRepositoryFavorates(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	favorates, err := dgitlabuserrepositoryfavorite.FindAllByUserId(id)
	if err != nil {
		logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, favorates, response.NoErr.Msg))
	return
}

func CreateUserRepositoryFavorates(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	request := dgitlabuserrepositoryfavorite.Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	err = dao.Create(&dgitlabuserrepositoryfavorite.Response{}, ctx, map[string]interface{}{
		"UserId":           id,
		"RepositoryId":     request.RepositoryId,
		"RepositoryName":   request.RepositoryName,
		"RepositoryBranch": request.RepositoryBranch,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, request, response.NoErr.Msg))
	return
}

func DeleteUserRepositoryFavorates(ctx iris.Context) {
	err := dao.Delete(&dgitlabuserrepositoryfavorite.Response{}, ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}
