package release

import (
	"fmt"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/release/dreleaseprojectconfig"
)

func GcovSchedule() error {
	configs, err := dreleaseprojectconfig.AllGcovEnabledConfig()
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		return err
	}

	for _, config := range configs {
		go func() {
			if config.BuildProjectBranch != nil && *(config.BuildProjectBranch) != "" {
				var command string
				if *(config.BuildProjectBranch) == "Trunk" {
					command = fmt.Sprintf(`ssh root@%s "ps -ef|grep -v grep|grep gcov-server|grep calculate|grep %s$ > /dev/null || /usr/local/bin/gcov-server --calculate --project %s &"`, libs.Config.Release.GcovServer, config.ReleaseProject.Name, config.ReleaseProject.Name)
				} else {
					command = fmt.Sprintf(`ssh root@%s "ps -ef|grep -v grep|grep gcov-server|grep calculate|grep %s$ > /dev/null || /usr/local/bin/gcov-server --calculate --project %s &"`, libs.Config.Release.GcovServer, *(config.BuildProjectBranch), *(config.BuildProjectBranch))
				}
				output, err := libs.ExecCommand(command)
				if err != nil {
					logging.ErrorLogger.Errorf(err.Error())
					logging.ErrorLogger.Errorf(output)
				}
			}
		}()
	}

	return nil
}
