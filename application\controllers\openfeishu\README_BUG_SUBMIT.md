# 锐捷Bug管理系统提交功能

这个模块提供了向锐捷Bug管理系统提交Bug信息的Go语言实现，基于原有的Java代码转换而来。

## 功能特性

- ✅ 完整的Bug信息结构体定义
- ✅ HTTP POST请求发送JSON数据
- ✅ 完善的错误处理和日志记录
- ✅ 响应解析和结果输出
- ✅ 默认Bug信息模板
- ✅ 自定义Bug提交便捷方法
- ✅ 输入验证功能
- ✅ 单元测试和示例代码

## 文件结构

```
application/controllers/openfeishu/
├── bug_submit.go          # 核心Bug提交功能
├── bug_submit_test.go     # 单元测试
├── bug_submit_example.go  # 使用示例和演示
└── README_BUG_SUBMIT.md   # 本文档
```

## 快速开始

### 1. 基本使用

```go
package main

import (
    "fmt"
    "irisAdminApi/application/controllers/openfeishu"
)

func main() {
    // 创建Bug提交器
    submitter := openfeishu.NewBugSubmitter()
    
    // 创建默认Bug信息
    bugInfo := openfeishu.NewDefaultBugInfo()
    bugInfo.Summary = "测试Bug标题"
    bugInfo.SubmitterCasUserid = "your_username"
    
    // 提交Bug
    response, err := submitter.SubmitBug(bugInfo)
    if err != nil {
        fmt.Printf("Bug提交失败: %v\n", err)
        return
    }
    
    fmt.Printf("Bug提交成功: %s\n", response.Message)
}
```

### 2. 使用便捷方法

```go
// 快速提交自定义Bug
submitter := openfeishu.NewBugSubmitter()
response, err := submitter.SubmitCustomBug(
    "登录功能异常",                    // Bug标题
    "用户无法正常登录系统",              // Bug描述
    "developer01",                   // 提交者
    "teamlead01",                    // 负责人
    "High",                         // 优先级
    "Critical",                     // 严重性
)
```

## API 参考

### 核心结构体

#### BugInfo

Bug信息结构体，包含所有必要的字段：

- `OS`: 操作系统
- `TestMethod`: 测试方法
- `Product`: 产品名称
- `TestCase`: 测试用例
- `PstlCasUserid`: PSTL
- `ChargeCasUserid`: bug负责人
- `Source`: 来源
- `TestCaseNum`: 测试用例编号
- `Summary`: bug简介
- `TestTopo`: 测试拓扑
- `TopoDesc`: 拓扑描述
- `DebugMess`: debug信息
- `VersionMess`: 版本信息
- `DeviceUnderTestConfig`: 被测设备配置
- `Repeat`: 可重复性
- `Priority`: 严重性
- `Severity`: 优先级
- `LegacyBug`: 遗留Bug
- `AffectCaseCount`: 影响用例执行数
- `TestabilitySpecial`: 可测试性专题
- `Locate`: 定位信息
- `SubmitterCasUserid`: 提交者
- `Ccusers`: 抄送者
- `PkIds`: PK IDs
- `Bugdescription`: Bug描述
- `OneKeyCollectionInfo`: 一键收集信息

#### BugSubmitResponse

Bug提交响应结构体：

- `Message`: 响应消息
- `Type`: 响应类型
- `Code`: 响应代码（可选）

### 核心方法

#### NewBugSubmitter()

创建Bug提交器实例。

#### NewDefaultBugInfo()

创建包含默认值的Bug信息实例。

#### SubmitBug(bugInfo *BugInfo)

提交Bug信息到锐捷Bug管理系统。

#### SubmitCustomBug(...)

便捷方法，快速提交自定义Bug信息。

## 字段说明

### 必填字段

- `Summary`: Bug简介/标题
- `SubmitterCasUserid`: 提交者用户名
- `ChargeCasUserid`: Bug负责人用户名
- `Priority`: 严重性 (Low, Normal, High, Critical)
- `Severity`: 优先级 (Minor, Normal, Major, Critical)

### 重要字段

- `Bugdescription`: Bug详细描述（支持HTML格式）
- `Source`: Bug来源
- `TestCase`: 相关测试用例
- `DebugMess`: Debug信息或相关链接
- `Repeat`: 可重复性描述

## 错误处理

模块提供了完善的错误处理机制：

1. **输入验证**: 检查必填字段和字段值的有效性
2. **网络错误**: 处理HTTP请求失败的情况
3. **响应解析**: 处理API响应格式错误
4. **日志记录**: 详细的错误和调试日志

## 测试

### 运行单元测试

```bash
go test ./application/controllers/openfeishu -v
```

### 运行示例代码

```go
// 在代码中调用示例函数
openfeishu.ExampleUsage()
openfeishu.DemonstrateBugInfoFields()
openfeishu.TestValidation()
```

## 配置

Bug提交API地址默认为：

```
http://bugs.ruijie.com.cn/bug_switch/service/outinterface_submitBugInfo
```

如需修改，可以在创建BugSubmitter后设置：

```go
submitter := openfeishu.NewBugSubmitter()
submitter.URL = "your_custom_api_url"
```

## 注意事项

1. **网络连接**: 确保能够访问锐捷Bug管理系统API
2. **用户权限**: 确保提交者有权限向系统提交Bug
3. **字段格式**: Bug描述支持HTML格式，可以包含链接和格式化文本
4. **调试模式**: 在Debug模式下会输出详细的请求和响应信息

## 与原Java代码的对应关系

| Java | Go |
|------|-----|
| `JSONObject json = new JSONObject()` | `BugInfo` 结构体 |
| `json.put("summary", value)` | `bugInfo.Summary = value` |
| `HttpUtils.post(url, json)` | `libs.PostJson(url, bugData, nil)` |
| `jsonObj.get("message")` | `response.Message` |

## 更新日志

### v1.0.0

- 初始版本
- 完整的Bug提交功能
- 单元测试和示例代码
- 完善的错误处理和日志记录
