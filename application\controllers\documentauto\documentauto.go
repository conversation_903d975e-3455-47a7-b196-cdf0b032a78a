package documentauto

import (
	"fmt"
	"io"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/buildfarm/dcronmakejob"
	"irisAdminApi/service/dao/documentauto/ddagbinfile"
	"irisAdminApi/service/dao/documentauto/ddagdocument"
	"irisAdminApi/service/dao/documentauto/ddagdocumentversion"
	"irisAdminApi/service/dao/documentauto/ddaghistoricalversion"
	"irisAdminApi/service/dao/documentauto/ddagserie"
	"irisAdminApi/service/dao/documentauto/ddagseriesbuildname"
	"irisAdminApi/service/dao/documentauto/ddagtask"
	"irisAdminApi/service/dao/documentauto/ddagversion"
	"irisAdminApi/service/transaction/documentauto/transdocumentauto"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"github.com/go-playground/validator/v10"
	"github.com/kataras/iris/v12"
)

// Define the maximum file size (e.g., 5 MB)
const MaxFileSize = 5 << 20 // 5 MB

// Initialize a validator instance
var validate = validator.New()

// 获取系列数据
func GetSerieData(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&ddagserie.Response{}, ctx, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

// 获取分支数据
func GetBranchs(ctx iris.Context) {
	list, err := ddagserie.GetAllBranchData()
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))

}

// 获取编译Release数据
func GetCompileReleases(ctx iris.Context) {
	branch := ctx.FormValue("branch") //分支
	start := ctx.FormValue("start")   //开始日期
	end := ctx.FormValue("end")       //结束日期
	list, err := ddagserie.GetCompileRelease(branch, start, end)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

// 获取基线版本数据
func GetBaselines(ctx iris.Context) {
	series := ctx.FormValue("series") //系列数据逗号隔开
	list, err := ddagserie.GetBaseline(series)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

// 获取 bin 文件 根据 release 和 version 获取
func GetBinFiles(ctx iris.Context) {
	release := ctx.FormValue("release")
	version := ctx.FormValue("version")
	items, err := dcronmakejob.GetBinFile(release, version)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, items, response.NoErr.Msg))
	return
}

type ReleaseDocument struct {
	Series             string `form:"series" validate:"required"`
	ReleaseType        string `form:"releaseType" validate:"required"`
	Version            string `form:"version" validate:"required"`
	Baseline           string `form:"baseline" validate:"required"`
	StartDate          string `form:"startDate" validate:"required"`
	EndDate            string `form:"endDate" validate:"required"`
	Release            string `form:"release" validate:"required"`
	BinFiles           string `form:"binFiles" validate:"required"`
	Description        string `form:"description" validate:"required,max=500"`
	UploadScreenshot   bool   `form:"uploadScreenshot"`
	UploadWordTemplate bool   `form:"uploadWordTemplate"`
	TaskID             uint   `form:"taskID"`
}

// SubmitReleaseDocTask 提交发行文档任务，异步处理下载和生成文档
func SubmitReleaseDocTask(ctx iris.Context) {
	userID, _ := dao.GetAuthId(ctx)
	request := ReleaseDocument{}

	// 解析表单数据
	if err := ctx.ReadForm(&request); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	// 验证表单数据
	if err := validate.Struct(request); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	// 生成 UUID
	uuid := libs.GetUUID()

	// 处理文件上传（截图和Word模板）
	screenshots, wordTemplates, err := handleFileUploads(ctx, request, uuid, true)
	if err != nil {
		logging.ErrorLogger.Errorf("处理文件上传出错: %v", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	// 获取系列编译名称集合
	seriesData := strings.Split(request.Series, ",")
	seriesBuildNames, err := ddagseriesbuildname.GetSeriesBuildNames(seriesData)
	if err != nil {
		logging.ErrorLogger.Errorf("获取系列编译名称出错: %v", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	// 获取编译记录名称集合
	buildRecordIDs := strings.Split(request.BinFiles, ",")
	buildRecordNames, err := dcronmakejob.GetBuildRecordIDs(buildRecordIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("获取编译记录名称出错: %v", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	// 验证 seriesBuildNames 是否全部存在于 buildRecordNames 中
	if err := validateBuildRecords(seriesBuildNames, buildRecordNames, ctx); err != nil {
		// validateBuildRecords 已经处理了响应
		logging.ErrorLogger.Errorf("验证编译记录名称出错: %v", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	// 创建系列ID与编译记录ID的映射
	seriesToBuildRecordMap := createSeriesToBuildRecordMap(seriesBuildNames, buildRecordNames)

	// 创建系列ID到编译名称的映射
	seriesIDToBuildNames := makeSeriesIDToBuildNamesMap(seriesBuildNames)

	// 创建编译名称到产品型号的映射
	buildNameToProductModel := makeBuildNameToProductModelMap(seriesBuildNames)

	// 拼接任务记录
	taskRecord := map[string]interface{}{
		"UserID":          userID,
		"SeriesIDs":       request.Series,
		"BuildRecordIDs":  request.BinFiles,
		"ReleaseVersion":  request.Release,
		"VersionNumber":   request.Version,
		"ReleaseType":     request.ReleaseType,
		"BaselineVersion": request.Baseline,
		"Description":     request.Description,
		"UUID":            uuid,
		"StartDate":       request.StartDate,
		"EndDate":         request.EndDate,
		"Status":          0, // 初始化状态为未完成
		"CreatedAt":       time.Now(),
		"UpdatedAt":       time.Now(),
	}

	// 使用事务创建任务记录和文档记录
	err = transdocumentauto.CreateTaskAndDocumentTransaction(int(userID), taskRecord, seriesIDToBuildNames, buildNameToProductModel, seriesToBuildRecordMap, screenshots, wordTemplates)
	if err != nil {
		logging.ErrorLogger.Errorf("创建任务和文档事务出错: %v", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	// 根据UUID获取任务记录信息
	taskRecordData, err := ddagtask.GetTaskByUUID(uuid)
	if err != nil {
		logging.ErrorLogger.Errorf("根据UUID获取任务记录出错: %v", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	// 异步处理下载和生成文档
	go processTaskAsync(taskRecordData.ID, uuid, request, seriesBuildNames, buildRecordNames, screenshots, wordTemplates)

	// 返回任务记录给客户端
	ctx.JSON(response.NewResponse(response.NoErr.Code, taskRecord, response.NoErr.Msg))
}

// processTaskAsync 异步处理下载和生成文档任务
func processTaskAsync(taskID uint, uuid string, request ReleaseDocument, seriesBuildNames []*ddagseriesbuildname.Response, buildRecordNames []*dcronmakejob.ListResponse, screenshots map[int]string, wordTemplates map[int]string) {
	defer func() {
		if r := recover(); r != nil {
			logging.ErrorLogger.Errorf("后台任务发生恐慌: %v", r)
			// 更新任务状态为失败
			ddagtask.UpdateTask(taskID, map[string]interface{}{
				"Status":    2, // 表示失败
				"UpdatedAt": time.Now(),
			})
		}
	}()

	// 获取文档记录
	docRecords, err := ddagdocument.GetDocumentCompositeData(taskID)
	if err != nil {
		logging.ErrorLogger.Errorf("获取文档记录出错: %v", err)
		ddagtask.UpdateTask(taskID, map[string]interface{}{
			"Status":    2,
			"UpdatedAt": time.Now(),
		})
		return
	}

	// 遍历文档记录处理
	for _, docRecord := range docRecords {
		err := processDocument(docRecord, uuid, request, seriesBuildNames, buildRecordNames)
		if err != nil {
			logging.ErrorLogger.Errorf("处理文档ID %d 出错: %v", docRecord.ID, err)
			// 发送消息文档生成失败
			to := []string{docRecords[0].Username + "@ruijie.com.cn", "<EMAIL>"}
			title := fmt.Sprintf("【发行文档自动生成系统】任务ID:%d--文档ID:%d--失败,请及时处理", taskID, docRecord.ID)
			message := fmt.Sprintf("错误信息为:%s", err.Error())
			libs.SendCardMessage(to, title, message, []string{})
			// 更新单个文档状态为失败
			ddagdocument.UpdateDocument(docRecord.ID, map[string]interface{}{
				"Status":    2,
				"UpdatedAt": time.Now(),
			})
			// 检查所有文档是否全部失败
			allFailed, err := checkAllDocumentsFailed(taskID)
			if err != nil {
				logging.ErrorLogger.Errorf("检查任务ID %d 的文档状态出错: %v", taskID, err)
				ddagtask.UpdateTask(taskID, map[string]interface{}{
					"Status":    2,
					"UpdatedAt": time.Now(),
				})
				return
			}
			if allFailed {
				// 更新任务状态为失败
				ddagtask.UpdateTask(taskID, map[string]interface{}{
					"Status":    2,
					"UpdatedAt": time.Now(),
				})
			}
			continue
		}
		// 更新单个文档状态为成功
		ddagdocument.UpdateDocument(docRecord.ID, map[string]interface{}{
			"Status":    1,
			"UpdatedAt": time.Now(),
		})
	}

	// 检查所有文档是否处理成功
	allSuccess, err := checkAllDocumentsSuccess(taskID)
	if err != nil {
		logging.ErrorLogger.Errorf("检查任务ID %d 的文档状态出错: %v", taskID, err)
		ddagtask.UpdateTask(taskID, map[string]interface{}{
			"Status":    2,
			"UpdatedAt": time.Now(),
		})
		return
	}

	if allSuccess {
		//发送消息任务完成
		to := []string{docRecords[0].Username + "@ruijie.com.cn"}
		title := fmt.Sprintf("【发行文档自动生成系统】文档生成任务ID:%d完成", taskID)
		// 根据taskID重新获取信息，提取文档记录中的文档名称并增加对应的超链接
		//<a href="https://www.baidu.com">文档名称</a>
		docRecords, err := ddagdocument.GetDocumentCompositeData(taskID)
		if err != nil {
			logging.ErrorLogger.Errorf("获取文档记录出错: %v", err)
		}
		docNames := make([]string, 0)
		// 在富文本中 word文档和excel文档超链接 显示为一组
		for _, doc := range docRecords {
			wordDocURL := fmt.Sprintf("%s/api/v1/documentauto/downloadDocument?docID=%d&docType=1", libs.Config.DocumentAuto.Server, doc.ID)
			excelDocURL := fmt.Sprintf("%s/api/v1/documentauto/downloadDocument?docID=%d&docType=2", libs.Config.DocumentAuto.Server, doc.ID)
			docNames = append(docNames, fmt.Sprintf("<a href=\"%s\">%s</a>", wordDocURL, path.Base(doc.DocumentDownloadURLWord)))
			docNames = append(docNames, fmt.Sprintf("<a href=\"%s\">%s</a>", excelDocURL, path.Base(doc.DocumentDownloadURLExcel)))
		}
		docNamesStr := strings.Join(docNames, "\n")
		message := fmt.Sprintf("文档名称:\n%s", docNamesStr)

		libs.SendCardMessage(to, title, message, []string{})
		// 更新任务状态为已完成
		ddagtask.UpdateTask(taskID, map[string]interface{}{
			"Status":    1,
			"UpdatedAt": time.Now(),
		})
	}
}

// processDocument 处理单个文档的下载和生成逻辑
func processDocument(docRecord *ddagdocument.DocumentCompositeData, uuid string, request ReleaseDocument, seriesBuildNames []*ddagseriesbuildname.Response, buildRecordNames []*dcronmakejob.ListResponse) error {
	buildRecordIDs := strings.Split(docRecord.BuildRecordIDs, ",")
	buildRecords, err := dcronmakejob.GetBuildRecordIDs(buildRecordIDs)
	if err != nil {
		return fmt.Errorf("获取编译记录出错: %v", err)
	}

	buildNameToProductModel := makeBuildNameToProductModelMap(seriesBuildNames)

	var buildSoftwareVersion string
	for _, buildRecord := range buildRecords {
		binFileURL := constructBinFileURL(buildRecord, request.ReleaseType)
		statusCode, _, bodyBytes, err := libs.Get(binFileURL, map[string]string{}, map[string]string{})
		if err != nil || statusCode != 200 {
			fmt.Println(binFileURL)
			return fmt.Errorf("获取bin文件URL出错: %v", err)
		}
		binDownloadURL, err := extractBinDownloadURL(bodyBytes, buildRecord, request.ReleaseType)
		if err != nil {
			return fmt.Errorf("提取bin文件下载URL出错: %v", err)
		}

		fileName, fileSize, md5, err := downloadBinFile(binDownloadURL)
		if err != nil {
			return fmt.Errorf("下载bin文件出错: %v", err)
		}

		softwareVersion := formatSoftwareVersion(buildRecord, request.ReleaseType)
		// 保存bin文件信息到数据库
		binFile := ddagbinfile.Response{}
		err = binFile.Create(map[string]interface{}{
			"DocumentID":      docRecord.ID,
			"SeriesID":        docRecord.SeriesID,
			"Version":         request.Version,
			"BuildRecordID":   buildRecord.Id,
			"BuildName":       buildRecord.Product,
			"BinFileName":     fileName,
			"BinFileSize":     uint(fileSize),
			"BinFileMd5":      md5,
			"BinFileURL":      binDownloadURL,
			"ProductModel":    buildNameToProductModel[buildRecord.Product],
			"SoftwareVersion": softwareVersion,
			"CPU":             buildRecord.Cpu.Cpu,
			"BaselineVersion": request.Baseline,
			"CreatedAt":       time.Now(),
			"UpdatedAt":       time.Now(),
		})
		if err != nil {
			return fmt.Errorf("保存bin文件信息出错: %v", err)
		}

		buildSoftwareVersion = strings.ReplaceAll(softwareVersion, " ", "")
	}

	// 更新文档记录中的 SoftwareVersion
	err = ddagdocument.UpdateDocument(docRecord.ID, map[string]interface{}{
		"SoftwareVersion": buildSoftwareVersion,
		"UpdatedAt":       time.Now(),
	})
	if err != nil {
		return fmt.Errorf("更新文档记录出错: %v", err)
	}

	// 生成 Word 和 Excel 文档
	err = generateDocuments(docRecord, uuid, buildSoftwareVersion, request)
	if err != nil {
		return fmt.Errorf("生成文档出错: %v", err)
	}

	return nil
}

// extractBinDownloadURL 从响应内容中提取 bin 文件下载 URL
func extractBinDownloadURL(bodyBytes []byte, buildRecord *dcronmakejob.ListResponse, releaseType string) (string, error) {
	content := string(bodyBytes)
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(content))
	if err != nil {
		return "", fmt.Errorf("解析HTML内容失败: %v", err)
	}

	branch := buildRecord.Branch
	product := strings.ToUpper(buildRecord.Product)
	branchPattern := getBranchPattern(branch, product)
	pattern := fmt.Sprintf(`%s(?:_\d+)?(?:_[\w-]+)?_install\.bin`, regexp.QuoteMeta(branchPattern))
	regex := regexp.MustCompile(pattern)
	logging.DebugLogger.Infof("使用的正则表达式模式: %s", pattern)

	var binDownloadURL string

	doc.Find("a").EachWithBreak(func(i int, s *goquery.Selection) bool {
		href, exists := s.Attr("href")
		if !exists {
			return true // 继续遍历
		}

		// 提取文件名部分
		fileName := path.Base(href)
		logging.DebugLogger.Infof("找到文件名: %s", fileName)

		// 检查文件名是否匹配正则表达式，并包含产品名称
		if regex.MatchString(fileName) && strings.Contains(fileName, product) {
			// 构建完整下载 URL
			binDownloadURL = fmt.Sprintf("%s%s", constructBinFileURL(buildRecord, releaseType), fileName)
			logging.InfoLogger.Infof("匹配到的 bin 文件下载地址: %s", binDownloadURL)
			return false // 找到第一个匹配项后终止遍历
		}
		return true // 继续遍历
	})

	if binDownloadURL == "" {
		return "", fmt.Errorf("未能构建匹配产品的bin文件下载URL")
	}

	return binDownloadURL, nil
}

// generateDocuments 生成 Word 和 Excel 文档
func generateDocuments(docComposite *ddagdocument.DocumentCompositeData, uuid string, buildSoftwareVersion string, request ReleaseDocument) error {
	replaceMap := buildReplaceMap(docComposite, buildSoftwareVersion)
	if len(replaceMap) > 0 {
		// 创建输出目录 任务UUID+文档ID
		outputDir := filepath.Join(libs.Config.DocumentAuto.SavePath, uuid, strconv.FormatUint(uint64(docComposite.ID), 10))
		err := os.MkdirAll(outputDir, 0750)
		if err != nil {
			return fmt.Errorf("创建输出目录出错: %v", err)
		}

		// 生成 Word 文档
		outputFileName, outputFilePath, err := createWordDocument(docComposite, replaceMap, outputDir, buildSoftwareVersion)
		if err != nil {
			return fmt.Errorf("生成Word文档出错: %v", err)
		}
		fmt.Println("生成Word文档成功: ", outputFileName)
		logging.InfoLogger.Infof("生成Word文档成功: %s", outputFilePath)

		// 更新文档记录中的 Word 文件路径
		err = ddagdocument.UpdateDocument(docComposite.ID, map[string]interface{}{
			"DocumentDownloadURLWord": outputFilePath,
			"UpdatedAt":               time.Now(),
		})
		if err != nil {
			return fmt.Errorf("更新文档记录中的Word路径出错: %v", err)
		}

		// 生成 Excel 文档
		versionTransferExcelFileName, versionTransferExcelFilePath, err := createExcelDocument(docComposite, replaceMap, outputDir, buildSoftwareVersion)
		if err != nil {
			return fmt.Errorf("生成Excel文档出错: %v", err)
		}
		fmt.Println("生成Excel文档成功: ", versionTransferExcelFileName)
		logging.InfoLogger.Infof("生成Excel文档成功: %s", versionTransferExcelFilePath)
		// 更新文档记录中的 Excel 文件路径
		err = ddagdocument.UpdateDocument(docComposite.ID, map[string]interface{}{
			"DocumentDownloadURLExcel": versionTransferExcelFilePath,
			"UpdatedAt":                time.Now(),
		})
		if err != nil {
			return fmt.Errorf("更新文档记录中的Excel路径出错: %v", err)
		}

		// 更新文档状态为完成
		err = ddagdocument.UpdateDocument(docComposite.ID, map[string]interface{}{
			"Status":    1,
			"UpdatedAt": time.Now(),
		})
		if err != nil {
			return fmt.Errorf("更新文档状态出错: %v", err)
		}
	}

	return nil
}

// buildReplaceMap 构建替换关键词映射表
func buildReplaceMap(docComposite *ddagdocument.DocumentCompositeData, buildSoftwareVersion string) map[string]string {
	replaceMap := make(map[string]string)
	replaceMap["{{title1}}"] = docComposite.Title1
	replaceMap["{{title2}}"] = docComposite.Title2
	replaceMap["{{softwareVersion}}"] = buildSoftwareVersion
	replaceMap["{{versionNumber}}"] = docComposite.VersionNumber
	replaceMap["{{docRev}}"] = fmt.Sprintf("V%.1f", docComposite.DocumentVersionNumber)
	replaceMap["{{excelRev}}"] = fmt.Sprintf("%.2f", docComposite.DocumentVersionNumber)
	replaceMap["{{time}}"] = time.Now().Format("2006-01-02")
	replaceMap["{{excelTime}}"] = time.Now().Format("2006/01/02")
	productModelList := strings.Split(docComposite.ProductModel, "|")
	replaceMap["{{productModel}}"] = strings.Join(productModelList, "\n")
	replaceMap["{{releaseType}}"] = docComposite.ReleaseType
	replaceMap["{{baselineVersion}}"] = strings.ReplaceAll(docComposite.BaselineVersion, "NTOS", docComposite.VersionPrefix)
	replaceMap["{{releaseReason}}"] = docComposite.ReleaseReason

	// 特殊处理系列ID为1、2、3的版本号
	if docComposite.SeriesID == 1 || docComposite.SeriesID == 2 || docComposite.SeriesID == 3 {
		replaceMap["{{releaseVersion}}"] = strings.ReplaceAll(docComposite.VersionNumber, "NTOS", docComposite.VersionPrefix)
	} else {
		replaceMap["{{releaseVersion}}"] = docComposite.VersionNumber
	}

	// 获取对应的截图
	if docComposite.ScreenshotPath != "" {
		replaceMap["{{onlineImgsrc}}"] = docComposite.ScreenshotPath
	}

	// 获取文档版本数据
	docVersionData, err := ddagdocumentversion.GetDocumentVersionByDocumentID(docComposite.SeriesID, docComposite.VersionNumber, docComposite.DocumentVersionNumber)
	if err != nil {
		logging.ErrorLogger.Errorf("获取文档版本数据出错: %v", err)
	}

	// 遍历文档版本数据
	for versionIndex, docVersion := range docVersionData {
		replaceMap[fmt.Sprintf("{{revDate%d}}", versionIndex+1)] = docVersion.CreatedAt.Format("2006-01-02")
		replaceMap[fmt.Sprintf("{{revTime%d}}", versionIndex+1)] = docVersion.CreatedAt.Format("2006/01/02")
		replaceMap[fmt.Sprintf("{{revNo%d}}", versionIndex+1)] = fmt.Sprintf("V%.1f", docVersion.VersionNumber)
		replaceMap[fmt.Sprintf("{{revDesc%d}}", versionIndex+1)] = docVersion.VersionDesc
		replaceMap[fmt.Sprintf("{{revMod%d}}", versionIndex+1)] = docVersion.VersionEditor
		replaceMap[fmt.Sprintf("{{revNb%d}}", versionIndex+1)] = fmt.Sprintf("%.2f", docVersion.VersionNumber)
		//默认送审对象
		replaceMap[fmt.Sprintf("{{revAuditor%d}}", versionIndex+1)] = "林晨1"
	}

	// 获取版本数据
	versionData, err := ddagversion.GetVersionData(docComposite.VersionNumber)
	var sort uint
	if err != nil {
		logging.ErrorLogger.Errorf("获取版本数据出错: %v", err)
		sort = 0
	} else {
		if versionData.ID > 0 {
			sort = versionData.Sort
		}
	}

	// 获取历史版本发行数据
	historicalVersionData, err := ddaghistoricalversion.GetHistoricalVersion(docComposite.SeriesID, sort)
	if err != nil {
		logging.ErrorLogger.Errorf("获取历史版本发行数据出错: %v", err)
	}

	// 遍历历史版本数据
	for versionIndex, historicalVersion := range historicalVersionData {
		if historicalVersion.HReleaseDate != "" {
			replaceMap[fmt.Sprintf("{{hreleaseDate%d}}", versionIndex+1)] = historicalVersion.HReleaseDate
		} else {
			replaceMap[fmt.Sprintf("{{hreleaseDate%d}}", versionIndex+1)] = historicalVersion.ReleaseDate
		}
		replaceMap[fmt.Sprintf("{{hreleaseVersion%d}}", versionIndex+1)] = strings.ReplaceAll(historicalVersion.VersionNumber, "NTOS", docComposite.VersionPrefix)
		replaceMap[fmt.Sprintf("{{hreleaseStatus%d}}", versionIndex+1)] = historicalVersion.Status
	}

	// 获取bin文件数据
	binFileData, err := ddagbinfile.GetBinFileByDocumentID(docComposite.ID)
	if err != nil {
		logging.ErrorLogger.Errorf("获取bin文件数据出错: %v", err)
	}

	// 遍历bin文件数据
	for binFileIndex, binFile := range binFileData {
		replaceMap[fmt.Sprintf("{{binFileName%d}}", binFileIndex+1)] = binFile.BinFileName
		replaceMap[fmt.Sprintf("{{binFileSize%d}}", binFileIndex+1)] = FormatWithComma(float64(binFile.BinFileSize), 0)
		productModelList := strings.Split(binFile.ProductModel, "|")
		replaceMap[fmt.Sprintf("{{binProductModel%d}}", binFileIndex+1)] = strings.Join(productModelList, "\n")
		replaceMap[fmt.Sprintf("{{binFileMD5%d}}", binFileIndex+1)] = binFile.BinFileMd5
		replaceMap[fmt.Sprintf("{{binSoftwareVersion%d}}", binFileIndex+1)] = binFile.SoftwareVersion
		replaceMap[fmt.Sprintf("{{binFileURL%d}}", binFileIndex+1)] = binFile.BinFileURL
	}

	return replaceMap
}

// 获取发行文档任务
func GetReleaseDocTask(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	seriesParam := ctx.FormValue("series")
	version := ctx.FormValue("version")
	baseline := ctx.FormValue("baseline")
	releaseType := ctx.FormValue("releaseType")
	var series []string
	if seriesParam != "" {
		series = strings.Split(seriesParam, ",")
	}
	Ddagtask := ddagtask.Response{}
	docRecords, err := Ddagtask.AllWithFilters(name, sort, orderBy, page, pageSize, series, version, baseline, releaseType)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, docRecords, response.NoErr.Msg))
}

// 获取文档列表
func GetDocumentLists(ctx iris.Context) {
	taskID, _ := dao.GetId(ctx)
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	docRecords, err := ddagdocument.GetDocumentList(taskID, page, pageSize, orderBy, sort)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, docRecords, response.NoErr.Msg))
}

// 下载文档
func DownloadDocument(ctx iris.Context) {
	docID := ctx.FormValue("docID")
	docType := ctx.FormValue("docType")
	docIDInt, _ := strconv.Atoi(docID)
	docTypeInt, _ := strconv.Atoi(docType)
	// 获取文档记录
	docRecord, err := ddagdocument.GetDocumentByID(uint(docIDInt))
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if docTypeInt == 1 {
		// 获取文件名去除空格
		fileName := strings.ReplaceAll(path.Base(docRecord.DocumentDownloadURLWord), " ", "")
		ctx.SendFile(docRecord.DocumentDownloadURLWord, fileName)
	} else if docTypeInt == 2 {
		fileName := strings.ReplaceAll(path.Base(docRecord.DocumentDownloadURLExcel), " ", "")
		ctx.SendFile(docRecord.DocumentDownloadURLExcel, fileName)
	}
}

// ReSubmitReleaseDocTask 重新提交发行文档任务，异步处理下载和生成文档
func ReSubmitReleaseDocTask(ctx iris.Context) {
	// 获取上一个任务的ID
	previousTaskIDStr := ctx.FormValue("taskID")
	if previousTaskIDStr == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "taskID 是必需的"))
		return
	}
	previousTaskID, err := strconv.Atoi(previousTaskIDStr)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "无效的 id"))
		return
	}

	// 获取上一个任务的详细信息
	_, err = ddagtask.GetTaskByID(uint(previousTaskID))
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未找到指定的任务"))
		return
	}

	// 获取上一个任务的文档记录
	previousDocRecords, err := ddagdocument.GetDocumentCompositeData(uint(previousTaskID))
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "无法获取上一个任务的文档记录"))
		return
	}

	// 定义一个新的 ReleaseDocument 请求
	request := ReleaseDocument{}

	// 解析表单数据
	if err := ctx.ReadForm(&request); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	// 验证表单数据
	if err := validate.Struct(request); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	// 生成新的 UUID
	uuid := libs.GetUUID()

	// 处理文件上传：如果当前请求未上传，则使用上一个任务的文件
	var screenshots, wordTemplates map[int]string
	screenshots, wordTemplates, err = handleFileUploads(ctx, request, uuid, false)
	if err != nil {
		logging.ErrorLogger.Errorf("处理文件上传出错: %v", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	// 如果没有上传截图，则使用上一个任务的截图
	if !request.UploadScreenshot {
		for _, doc := range previousDocRecords {
			screenshots[int(doc.SeriesID)] = doc.ScreenshotPath
		}
	}

	// 如果没有上传Word模板，则使用上一个任务的模板
	if !request.UploadWordTemplate {
		for _, doc := range previousDocRecords {
			wordTemplates[int(doc.SeriesID)] = doc.TemplatePath
		}
	} else {
		//如果对应系列ID的模板不存在，则使用上一个任务的模板
		for _, doc := range previousDocRecords {
			if wordTemplates[int(doc.SeriesID)] == "" {
				wordTemplates[int(doc.SeriesID)] = doc.TemplatePath
			}
		}
	}

	//使用上一个任务的文档版本号
	previousVersionNumbers := make(map[int]string)
	for _, doc := range previousDocRecords {
		previousVersionNumbers[int(doc.SeriesID)] = fmt.Sprintf("%.1f", doc.DocumentVersionNumber)
	}

	// 获取系列编译名称集合
	seriesData := strings.Split(request.Series, ",")
	seriesBuildNames, err := ddagseriesbuildname.GetSeriesBuildNames(seriesData)
	if err != nil {
		logging.ErrorLogger.Errorf("获取系列编译名称出错: %v", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	// 获取编译记录名称集合
	buildRecordIDs := strings.Split(request.BinFiles, ",")
	buildRecordNames, err := dcronmakejob.GetBuildRecordIDs(buildRecordIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("获取编译记录名称出错: %v", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	// 验证 seriesBuildNames 是否全部存在于 buildRecordNames 中
	if err := validateBuildRecords(seriesBuildNames, buildRecordNames, ctx); err != nil {
		// validateBuildRecords 已经处理了响应
		logging.ErrorLogger.Errorf("验证编译记录名称出错: %v", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	// 创建系列ID与编译记录ID的映射
	seriesToBuildRecordMap := createSeriesToBuildRecordMap(seriesBuildNames, buildRecordNames)

	// 创建系列ID到编译名称的映射
	seriesIDToBuildNames := makeSeriesIDToBuildNamesMap(seriesBuildNames)

	// 创建编译名称到产品型号的映射
	buildNameToProductModel := makeBuildNameToProductModelMap(seriesBuildNames)

	// 拼接新的任务记录
	userID, _ := dao.GetAuthId(ctx)
	newTaskRecord := map[string]interface{}{
		"UserID":          userID,
		"SeriesIDs":       request.Series,
		"BuildRecordIDs":  request.BinFiles,
		"ReleaseVersion":  request.Release,
		"VersionNumber":   request.Version,
		"ReleaseType":     request.ReleaseType,
		"BaselineVersion": request.Baseline,
		"Description":     request.Description,
		"UUID":            uuid,
		"StartDate":       request.StartDate,
		"EndDate":         request.EndDate,
		"Status":          0, // 初始化状态为未完成
		"CreatedAt":       time.Now(),
		"UpdatedAt":       time.Now(),
	}

	// 使用新的事务方法创建任务记录和文档记录
	err = transdocumentauto.CreateReSubmitTaskAndDocumentTransaction(
		int(userID),
		newTaskRecord,
		uint(previousTaskID),
		seriesIDToBuildNames,
		buildNameToProductModel,
		seriesToBuildRecordMap,
		screenshots,
		wordTemplates,
		previousVersionNumbers,
	)
	if err != nil {
		logging.ErrorLogger.Errorf("重新提交任务事务处理出错: %v", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	// 根据UUID获取任务记录信息
	taskRecordData, err := ddagtask.GetTaskByUUID(uuid)
	if err != nil {
		logging.ErrorLogger.Errorf("根据UUID获取任务记录出错: %v", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	// 异步处理下载和生成文档
	go processTaskAsync(taskRecordData.ID, uuid, request, seriesBuildNames, buildRecordNames, screenshots, wordTemplates)

	// 返回任务记录给客户端
	ctx.JSON(response.NewResponse(response.NoErr.Code, newTaskRecord, response.NoErr.Msg))
}

// 获取分支模式
func getBranchPattern(branch, product string) string {
	if strings.ToUpper(product) == "P8600-G20" {
		// "NTOS" 前缀需要被 "IDP" 替换
		return strings.Replace(branch, "NTOS", "IDP", 1)
	}
	return branch
}

func FormatWithComma(num float64, precision int) string {
	// 将浮点数格式化为指定精度的小数点字符串
	format := fmt.Sprintf("%%.%df", precision)
	numStr := fmt.Sprintf(format, num)

	// 分割整数和小数部分
	parts := strings.Split(numStr, ".")
	intPart := parts[0]
	decPart := ""
	if len(parts) > 1 {
		decPart = parts[1]
	}

	// 处理整数部分，添加千位分隔符
	var result strings.Builder
	intPartLen := len(intPart)
	for i, digit := range intPart {
		if i > 0 && (intPartLen-i)%3 == 0 {
			result.WriteString(",")
		}
		result.WriteRune(digit)
	}

	// 如果有小数部分，则加上小数点和小数部分
	if decPart != "" {
		result.WriteString(".")
		result.WriteString(decPart)
	}

	return result.String()
}

// 文件复制
func CopyFile(src, des string) (written int64, err error) {
	srcFile, err := os.Open(src)
	if err != nil {
		return 0, err
	}
	defer srcFile.Close()

	//获取源文件的权限
	fi, _ := srcFile.Stat()
	perm := fi.Mode()

	// desFile, err := os.Create(des)  //无法复制源文件的所有权限
	desFile, err := os.OpenFile(des, os.O_RDWR|os.O_CREATE|os.O_TRUNC, perm) //复制源文件的所有权限
	if err != nil {
		return 0, err
	}
	defer desFile.Close()

	return io.Copy(desFile, srcFile)
}

// 使用正则表达式从程序输出中提取产品名称
func extractProductName(output string) string {
	// 使用正则表达式以匹配特定的标记
	re := regexp.MustCompile(`产品信息开始>>>(.*?)<<<产品信息结束`)
	matches := re.FindStringSubmatch(output)
	if len(matches) > 1 {
		return matches[1] // 返回第一个匹配的组（即产品名称）
	}
	return ""
}
