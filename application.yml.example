loglevel: debug # 日志级别 info debug disable error fatal warn 等等
host: ************ # host 地址
port: 8080
maxsize: 1024  # 文件上传限制
pprof: false #  http://localhost:8086/debug/pprof/heap ...
casbin:
  prefix: blog
  path: /Users/<USER>/go/src/irisAdminApi/rbac_model.conf # go run main.go 运行必须指定路径
cache:
  driver: local # 缓存驱动 local redis
limit:
  disable: true
  limit: 2 # 每秒允许请求 1 次
  burst: 10 # 最高允许并发
admin: # 管理员账号信息，用于数据填充
  username: username
  rolename: admin
  name: name
  password: password
db:
  adapter: mysql # mysql postgres sqlite3
  conn: "root:abc.123@tcp(localhost:3306)/iris?parseTime=True&loc=Local" # postgres://root:password@localhost/iris?sslmode=disable
  prefix: blog_
redis:
  host: localhost
  port: 6379
  password:
qiniu:
  enable: false
  host:
  accesskey:
  secretkey:
  bucket:
