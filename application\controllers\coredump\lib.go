package coredump

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"time"
)

type Card struct {
	Header   Header    `json:"header"`
	Elements []Element `json:"elements"`
}

type Header struct {
	Title    Title  `json:"title"`
	Subtitle Title  `json:"subtitle"`
	Template string `json:"template"`
}

type Title struct {
	Tag     string `json:"tag"`
	Content string `json:"content"`
}

type Element struct {
	Tag     string   `json:"tag"`
	Text    Text     `json:"text,omitempty"`
	Actions []Action `json:"actions,omitempty"`
}

type Text struct {
	Content string `json:"content"`
	Tag     string `json:"tag"`
}

type Action struct {
	Tag   string            `json:"tag"`
	Text  Text              `json:"text"`
	URL   string            `json:"url"`
	Type  string            `json:"type"`
	Value map[string]string `json:"value"`
}

type CoreDumpInfo struct {
	FileName    string `json:"fileName"`
	FileSize    string `json:"fileSize"`
	CreateTime  string `json:"createTime"`
	SN          string `json:"sn"`
	SecurityUrl string `json:"securityUrl"`
	TaskId      string `json:"taskId"`
	FileId      int64  `json:"fileId"`
}
type CoredumpItem struct {
	Name string
	Path string
}

func GenSign(secret string, timestamp int64) (string, error) {
	//timestamp + key 做sha256, 再进行base64 encode
	stringToSign := fmt.Sprintf("%v", timestamp) + "\n" + secret
	var data []byte
	h := hmac.New(sha256.New, []byte(stringToSign))
	_, err := h.Write(data)
	if err != nil {
		return "", err
	}
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))
	return signature, nil
}

func SendMessage(webhookURL, secret, message string) error {
	// 获取当前时间戳
	timestamp := time.Now().Unix()
	// 生成签名
	sign, err := GenSign(secret, timestamp)
	if err != nil {
		return err
	}

	// 构建请求体
	requestBody := map[string]interface{}{
		"timestamp": timestamp,
		"sign":      sign,
		"msg_type":  "text",
		"content": map[string]string{
			"text": message,
		},
	}
	requestBodyBytes, err := json.Marshal(requestBody)
	if err != nil {
		return err
	}
	fmt.Println(string(requestBodyBytes))

	// 创建 HTTP 请求
	req, err := http.NewRequest("POST", webhookURL, bytes.NewBuffer(requestBodyBytes))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	fmt.Println("Response:", string(body))
	return nil
}

func SendCardMessage(webhookURL, secret, message, msgTitle, msgTemplate, url string) error {
	// 获取当前时间戳
	timestamp := time.Now().Unix()
	// 生成签名
	sign, err := GenSign(secret, timestamp)
	if err != nil {
		return err
	}
	// 构建消息
	card := Card{
		Header: Header{
			Title: Title{
				Tag:     "plain_text",
				Content: msgTitle,
			},
			Subtitle: Title{
				Tag:     "plain_text",
				Content: "",
			},
			Template: msgTemplate,
		},
		Elements: []Element{
			{
				Tag: "div",
				Text: Text{
					Content: message,
					Tag:     "lark_md",
				},
			},
			{
				Tag: "action",
				Actions: []Action{
					{
						Tag: "button",
						Text: Text{
							Content: "查看详情",
							Tag:     "lark_md",
						},
						URL:   url,
						Type:  "default",
						Value: map[string]string{},
					},
				},
			},
		},
	}
	// 构建请求体
	requestBody := map[string]interface{}{
		"timestamp": timestamp,
		"sign":      sign,
		"msg_type":  "interactive",
		"card":      card,
	}
	requestBodyBytes, err := json.Marshal(requestBody)
	if err != nil {
		return err
	}
	// 创建 HTTP 请求
	req, err := http.NewRequest("POST", webhookURL, bytes.NewBuffer(requestBodyBytes))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	fmt.Println("Response:", string(body))
	return nil
}

func DownloadFile(url string, filePath string) error {
	// 创建一个忽略证书验证的Transport
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	// 创建一个 HTTP 客户端
	client := &http.Client{Transport: tr}

	// 发送GET请求
	resp, err := client.Get(url)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	switch resp.StatusCode {
	case http.StatusOK:
		// 文件存在，继续下载
	case http.StatusNotFound:
		return errors.New("file not found")
	default:
		statusCode := fmt.Sprintf("%d", resp.StatusCode)
		return errors.New("download failed: server returned a non-OK status: " + statusCode)
	}

	// 创建文件以保存下载内容
	out, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer out.Close()

	// 将HTTP响应体内容写入文件
	_, err = io.Copy(out, resp.Body)
	if err != nil {
		return err
	}

	return nil
}
