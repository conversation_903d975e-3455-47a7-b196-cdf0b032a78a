package dappmergerequestdiscussioncps

import (
	"fmt"
	"reflect"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/appmergerequest"

	"github.com/pkg/errors"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "MR评审CPS表"

type AppMergeRequestDiscussionCps struct {
	appmergerequest.AppMergeRequestDiscussionCps
}

type ListResponse struct {
	AppMergeRequestDiscussionCps
}

type Request struct {
	appmergerequest.AppMergeRequestDiscussionCps
	Endpoint  string `json:"endpoint"`
	ProjectID uint   `json:"project_id"`
}

func (a *AppMergeRequestDiscussionCps) ModelName() string {
	return ModelName
}

func Model() *appmergerequest.AppMergeRequestDiscussionCps {
	return &appmergerequest.AppMergeRequestDiscussionCps{}
}

func (a *AppMergeRequestDiscussionCps) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	where := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		where = where.Where("group_name_ch like ?", fmt.Sprintf("%%%s%%", name)).
			Or("group_name_en like ?", fmt.Sprintf("%%%s%%", name)).
			Or("category_name_ch like ?", fmt.Sprintf("%%%s%%", name)).
			Or("category_name_en like ?", fmt.Sprintf("%%%s%%", name)).
			Or("description_ch like ?", fmt.Sprintf("%%%s%%", name)).
			Or("description_en like ?", fmt.Sprintf("%%%s%%", name))
		if name == "海" || name == "海外" || name == "海外库" {
			where = where.Or("oversea = 1")
		}
		if name == "大" || name == "大库" {
			where = where.Or("large = 1")
		}
		if name == "小" || name == "小库" {
			where = where.Or("small = 1")
		}
		if name == "中" || name == "中库" {
			where = where.Or("middle = 1")
		}
		if name == "默" || name == "默认" || name == "默认阻" || name == "默认阻断" || name == "阻" || name == "阻断" {
			where = where.Or("pre_def_block = 1")
		}
	}
	db = db.Where(where)
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *AppMergeRequestDiscussionCps) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *AppMergeRequestDiscussionCps) Create(object map[string]interface{}) error {
	xt := reflect.TypeOf(Model())
	columns := []string{"updated_at"}
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")

		if ok && ok2 {
			columns = append(columns, name)
		} else if ok && !ok2 {
			return errors.New("未定义json标签")
		}
	}
	db := easygorm.GetEasyGormDb().Model(Model())

	err := db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "discussion_id"}},
		DoUpdates: clause.AssignmentColumns(columns),
	}).Create(&object).Error
	if err != nil {
		return err
	}

	return nil
}

func (this *AppMergeRequestDiscussionCps) CreateV2(object interface{}) error {
	return nil
}

func (a *AppMergeRequestDiscussionCps) BatchCreate(object []map[string]interface{}, history []map[string]interface{}, commitObject []map[string]interface{}) error {
	xt := reflect.TypeOf(Model())
	columns := []string{}
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")

		if ok && ok2 {
			columns = append(columns, name)
		} else if ok && !ok2 {
			return errors.New("未定义json标签")
		}
	}

	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		if len(object) > 0 {
			err := db.Model(Model()).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "discussion_id"}},
				DoUpdates: clause.AssignmentColumns(columns),
			}).Create(&object).Error
			if err != nil {
				return errors.Wrap(err, "")
			}
		}

		if len(history) > 0 {
			err := db.Model(&appmergerequest.AppMergeRequestDiscussionCpsHistory{}).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "target_project_id"}, {Name: "merge_request_i_id"}},
				DoUpdates: clause.AssignmentColumns([]string{}),
			}).Create(&history).Error
			if err != nil {
				return errors.Wrap(err, "")
			}
		}

		if len(commitObject) > 0 {
			err := db.Model(&appmergerequest.AppMergeRequest{}).Clauses(
				clause.Insert{Modifier: "IGNORE"},
				clause.OnConflict{
					Columns:   []clause.Column{{Name: "target_project_id"}, {Name: "merge_request_i_id"}},
					DoUpdates: clause.AssignmentColumns([]string{"commit_count"}),
				},
			).Create(&commitObject).Error
			if err != nil {
				return errors.Wrap(err, "")
			}
		}

		return nil
	})
	return errors.Wrap(err, "")
}

func (a *AppMergeRequestDiscussionCps) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *AppMergeRequestDiscussionCps) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *AppMergeRequestDiscussionCps) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *AppMergeRequestDiscussionCps) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *AppMergeRequestDiscussionCps) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *AppMergeRequestDiscussionCps) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindCpsByProjectAndMergerequestIID(projectID int, mergeRequestIID string, lastFetchAt int) ([]*ListResponse, error) {
	items := []*ListResponse{}
	db := easygorm.GetEasyGormDb().Model(Model()).Where("target_project_id = ? and merge_request_i_id = ?", projectID, mergeRequestIID)
	if lastFetchAt > 0 {
		microseconds := int64(lastFetchAt) // 这里假设微秒时间戳为 1623391200123456

		// 将微秒数除以1,000,000得到秒数，将余数作为纳秒数
		seconds := microseconds / 1e6
		nanoseconds := (microseconds % 1e6) * 1e3
		_lastFetchAt := time.Unix(seconds, nanoseconds)
		db = db.Where("updated_at >= ?", _lastFetchAt)
	}
	err := db.Find(&items).Error
	return items, errors.Wrap(err, "get cps by project id and mergerequest iid error")
}

func FindCpsByProjectAndMergerequestIIDs(targetProjectID uint, mergeRequestIIDs []uint) ([]*ListResponse, error) {
	items := []*ListResponse{}
	db := easygorm.GetEasyGormDb().Model(Model()).Where("target_project_id = ? and merge_request_i_id in ?", targetProjectID, mergeRequestIIDs)

	err := db.Find(&items).Error
	return items, errors.Wrap(err, "get cps by project id and mergerequest iid error")
}

func FindCpsByProject(projectID int) ([]*ListResponse, error) {
	items := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("target_project_id = ?", projectID).Find(&items).Error
	return items, errors.Wrap(err, "get cps by project id and mergerequest iid error")
}

func DeleteCpsByDiscussionID(discussionID string) error {
	err := easygorm.GetEasyGormDb().Delete(Model(), "discussion_id = ?", discussionID).Error
	return errors.Wrap(err, "delete cps by discussion id error")
}

type CpsFeishuResponse struct {
	CpsID              uint      `json:"cps_id"`
	MrID               uint      `json:"mr_id"`
	Title              string    `json:"title"`
	CreatedAt          time.Time `json:"created_at"`
	Owner              string    `json:"owner"`
	ReleaseProject     string    `json:"release_project"`
	Requirement        string    `json:"requirement"`
	WorkPackage        string    `json:"work_package"`
	CodeQuantityAdd    uint      `json:"code_quantity_add"`
	CodeQuantityRemove uint      `json:"code_quantity_remove"`
	Object             string    `json:"object"`
	Discussion         string    `json:"discussion"`
	Reviewer           string    `json:"reviewer"`
	Category           uint      `json:"category"`
	Severity           uint      `json:"severity"`
	Confirm            uint      `json:"confirm"`
	Introduction       uint      `json:"introduction"`
	Condition          uint      `json:"condition"`
	State              uint      `json:"state"`
}

func (c *CpsFeishuResponse) GetCategory(id uint) string {
	m := map[uint]string{
		0: "未确认",
		1: "缺陷",
		2: "建议",
		3: "提问",
	}
	if val, ok := m[id]; ok {
		return val
	}
	return "未知"
}

func (c *CpsFeishuResponse) GetSeverity(id uint) string {
	m := map[uint]string{
		0: "未确认",
		1: "严重",
		2: "一般",
		3: "轻微",
	}
	if val, ok := m[id]; ok {
		return val
	}
	return "未知"
}

func (c *CpsFeishuResponse) GetConfirm(id uint) string {
	m := map[uint]string{
		0: "未确认",
		1: "接受",
		2: "拒绝",
		3: "重复",
		4: "答复",
	}
	if val, ok := m[id]; ok {
		return val
	}
	return "未知"
}

func (c *CpsFeishuResponse) GetIntroduction(id uint) string {
	m := map[uint]string{
		0:  "未确认",
		1:  "需求(遗漏)",
		2:  "需求(错误)",
		3:  "需求(不符合用户需求)",
		4:  "总体设计(遗漏)",
		5:  "总体设计(与需求不一致)",
		6:  "详细设计(遗漏)",
		7:  "详细设计(存在缺陷)",
		8:  "详细设计(与总体设计不一致)",
		9:  "编码(基础问题)",
		10: "编码(业务问题)",
		11: "编码(与设计不一致)",
		12: "配置(版本/参数/宏配置错误等)",
		13: "文档(文档/描述/文字错误)",
		14: "第三方代码",
		15: "硬件缺陷",
	}
	if val, ok := m[id]; ok {
		return val
	}
	return "未知"
}

func (c *CpsFeishuResponse) GetCondition(id uint) string {
	m := map[uint]string{
		0: "否",
		1: "是",
	}
	if val, ok := m[id]; ok {
		return val
	}
	return "未知"
}

func (c *CpsFeishuResponse) GetState(id uint) string {
	m := map[uint]string{
		0: "未确认",
		1: "开启",
		2: "关闭",
	}
	if val, ok := m[id]; ok {
		return val
	}
	return "未知"
}

func (c *AppMergeRequestDiscussionCps) GetCategory() string {
	m := map[uint]string{
		1: "缺陷",
		2: "建议",
		3: "提问",
	}
	if val, ok := m[c.Category]; ok {
		return val
	}
	return ""
}

func (c *AppMergeRequestDiscussionCps) GetSeverity() string {
	m := map[uint]string{
		1: "严重",
		2: "一般",
		3: "轻微",
	}
	if val, ok := m[c.Severity]; ok {
		return val
	}
	return ""
}

func (c *AppMergeRequestDiscussionCps) GetConfirm() string {
	m := map[uint]string{
		1: "接受",
		2: "拒绝",
		3: "重复",
		4: "答复",
		5: "误报",
	}
	if val, ok := m[c.Confirm]; ok {
		return val
	}
	return "未知"
}

func (c *AppMergeRequestDiscussionCps) GetIntroduction() string {
	m := map[uint]string{
		1:  "需求(遗漏)",
		2:  "需求(错误)",
		3:  "需求(不符合用户需求)",
		4:  "总体设计(遗漏)",
		5:  "总体设计(与需求不一致)",
		6:  "详细设计(遗漏)",
		7:  "详细设计(存在缺陷)",
		8:  "详细设计(与总体设计不一致)",
		9:  "编码(基础问题)",
		10: "编码(业务问题)",
		11: "编码(与设计不一致)",
		12: "配置(版本/参数/宏配置错误等)",
		13: "文档(文档/描述/文字错误)",
		14: "第三方代码",
		15: "硬件缺陷",
	}
	if val, ok := m[c.Introduction]; ok {
		return val
	}
	return ""
}

func (c *AppMergeRequestDiscussionCps) GetCondition() string {
	m := map[uint]string{
		0: "否",
		1: "是",
	}
	if val, ok := m[c.Condition]; ok {
		return val
	}
	return ""
}

func (c *AppMergeRequestDiscussionCps) GetState() string {
	m := map[uint]string{
		1: "开启",
		2: "关闭",
	}
	if val, ok := m[c.State]; ok {
		return val
	}
	return ""
}

func FindCpsWithoutSyncFeishu() ([]*CpsFeishuResponse, error) {
	items := []*CpsFeishuResponse{}
	selects := []string{
		`cps.id AS cps_id`,
		`mr.id AS mr_id`,
		`mr.title`,
		`mr.created_at`,
		`u2.name as owner`,
		`mr.release_project`,
		`wp.requirement`,
		`mr.work_package`,
		`mr.code_quantity_add`,
		`mr.code_quantity_remove`,
		`cps.object`,
		`cps.discussion`,
		`u1.name as reviewer`,
		`cps.category`,
		`cps.severity`,
		`cps.confirm`,
		`cps.introduction`,
		`cps.condition`,
		`cps.state`,
	}
	db := easygorm.GetEasyGormDb().
		Table("merge_request_discussion_cps cps").
		Joins("left join merge_requests as mr ON cps.target_project_id = mr.target_project_id AND cps.merge_request_i_id = mr.merge_request_i_id").
		Joins("left join merge_request_work_packages wp ON wp.release_project_id = mr.release_project_id AND wp.id = mr.work_package_id").
		Joins("LEFT JOIN users u1 ON u1.username = cps.reviewer").
		Joins("LEFT JOIN users u2 ON u2.id = mr.user_id").
		Select(selects)

	where := easygorm.GetEasyGormDb().Where("cps.target_project_id IS NOT NULL")
	// .Where("mr.status = 3")
	err := db.Where(where).Find(&items).Error
	return items, errors.Wrap(err, "get cps by project id and mergerequest iid error")
}

type CpsReviewerSummaryFeishuResponse struct {
	Name                 string  `json:"name"`
	ReleaseProject       string  `json:"release_project"`
	MrCount              uint    `json:"mr_count"`
	AvgResponseEscape    float32 `json:"avg_response_escape"`
	TotalDiscussionCount uint    `json:"total_discussion_count"`
	TotalDefect          uint    `json:"total_defect"`
	TotalSuggest         uint    `json:"total_suggest"`
	TotalQuestion        uint    `json:"total_question"`
}

func FindReviewerSummary() ([]*CpsReviewerSummaryFeishuResponse, error) {
	/*
			SELECT
			reviewer,
			u.name,
			COUNT(*) `mr_count`,
			AVG(response_escape)/60 `avg_response_escape`,
			sum(discussion_count) `total_discussion_count`,
			sum(defect) `total_defect`,
			sum(suggest) `total_suggest`,
			sum(question) `total_question`
		FROM (
			SELECT
			cps.target_project_id,
			cps.merge_request_i_id,
			cps.reviewer,
			min(TIMESTAMPDIFF(minute, mr.created_at, cps.created_at)) `response_escape`,
			COUNT(IF(cps.discussion = "" , NULL, 1)) `discussion_count`,
			COUNT(IF(cps.category = 1, 1, NULL)) `defect`,
			COUNT(IF(cps.category = 2, 1, NULL)) `suggest`,
			COUNT(IF(cps.category = 3, 1, NULL)) `question`
			-- COUNT(if(cps.state = 2, 1, NULL)) `closed`
			FROM merge_request_discussion_cps cps
			LEFT JOIN merge_requests mr ON mr.target_project_id = cps.target_project_id AND mr.merge_request_i_id = cps.merge_request_i_id AND mr.STATUS = 3
			WHERE cps.target_project_id IS NOT NULL AND cps.reviewer != 'buildfarm'
			GROUP BY target_project_id, merge_request_i_id, cps.reviewer
		) s
		LEFT JOIN users u ON u.username = s.reviewer
		GROUP BY s.reviewer
		ORDER BY total_discussion_count desc


	*/
	items := []*CpsReviewerSummaryFeishuResponse{}
	selects := []string{
		`reviewer`,
		`u.name as name`,
		`release_project`,
		`COUNT(*) as mr_count`,
		`AVG(response_escape)/60 as avg_response_escape`,
		`sum(discussion_count) as total_discussion_count`,
		`sum(defect) as total_defect`,
		`sum(suggest) as total_suggest`,
		`sum(question) as total_question`,
	}
	db := easygorm.GetEasyGormDb().
		Table(`(
			SELECT
			cps.target_project_id,
			cps.merge_request_i_id,
			cps.reviewer,
			mr.release_project,
			min(TIMESTAMPDIFF(minute, mr.created_at, cps.created_at)) as response_escape,
			COUNT(IF(cps.discussion = "" , NULL, 1)) as discussion_count,
			COUNT(IF(cps.category = 1, 1, NULL)) as defect,
			COUNT(IF(cps.category = 2, 1, NULL)) as suggest,
			COUNT(IF(cps.category = 3, 1, NULL)) as question
			FROM merge_request_discussion_cps cps
			LEFT JOIN merge_requests mr ON mr.target_project_id = cps.target_project_id AND mr.merge_request_i_id = cps.merge_request_i_id AND mr.STATUS = 3
			WHERE cps.target_project_id IS NOT NULL AND cps.reviewer != 'buildfarm'
			GROUP BY target_project_id, merge_request_i_id, cps.reviewer, mr.release_project) as s`).
		Joins("LEFT JOIN users u ON u.username = s.reviewer").
		Select(selects).
		Group("s.reviewer, s.release_project")

	err := db.Find(&items).Error
	return items, errors.Wrap(err, "get cps reviewer summary error")
}

type ProjectFeishuResponse struct {
	ReleaseProject string `json:"release_project"`
	WorkPackage    string `json:"work_package"`
}

func FindProjectSummary() ([]*ProjectFeishuResponse, error) {
	/*
			SELECT
			reviewer,
			u.name,
			COUNT(*) `mr_count`,
			AVG(response_escape)/60 `avg_response_escape`,
			sum(discussion_count) `total_discussion_count`,
			sum(defect) `total_defect`,
			sum(suggest) `total_suggest`,
			sum(question) `total_question`
		FROM (
			SELECT
			cps.target_project_id,
			cps.merge_request_i_id,
			cps.reviewer,
			min(TIMESTAMPDIFF(minute, mr.created_at, cps.created_at)) `response_escape`,
			COUNT(IF(cps.discussion = "" , NULL, 1)) `discussion_count`,
			COUNT(IF(cps.category = 1, 1, NULL)) `defect`,
			COUNT(IF(cps.category = 2, 1, NULL)) `suggest`,
			COUNT(IF(cps.category = 3, 1, NULL)) `question`
			-- COUNT(if(cps.state = 2, 1, NULL)) `closed`
			FROM merge_request_discussion_cps cps
			LEFT JOIN merge_requests mr ON mr.target_project_id = cps.target_project_id AND mr.merge_request_i_id = cps.merge_request_i_id AND mr.STATUS = 3
			WHERE cps.target_project_id IS NOT NULL AND cps.reviewer != 'buildfarm'
			GROUP BY target_project_id, merge_request_i_id, cps.reviewer
		) s
		LEFT JOIN users u ON u.username = s.reviewer
		GROUP BY s.reviewer
		ORDER BY total_discussion_count desc


	*/
	items := []*ProjectFeishuResponse{}
	selects := []string{
		`release_project`,
		`work_package`,
	}
	db := easygorm.GetEasyGormDb().
		Table(`(
			SELECT
				mr.release_project,
				mr.work_package
			FROM merge_request_discussion_cps cps
			LEFT JOIN merge_requests mr ON mr.target_project_id = cps.target_project_id AND mr.merge_request_i_id = cps.merge_request_i_id AND mr.STATUS = 3
			WHERE cps.target_project_id IS NOT NULL AND cps.reviewer != 'buildfarm'
			GROUP BY mr.release_project, mr.work_package) as s`).
		Select(selects)

	err := db.Find(&items).Error
	return items, errors.Wrap(err, "get cps reviewer summary error")
}

func BatchUpdate(dicussionIDs []string, data map[string]interface{}) error {
	return easygorm.GetEasyGormDb().Model(Model()).Where("discussion_id in (?)", dicussionIDs).Updates(data).Error
}
