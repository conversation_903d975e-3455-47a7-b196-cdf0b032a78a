package branchsearch

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/datasync/dbranchinfo"
	"irisAdminApi/service/dao/datasync/dproject"
	"strconv"
	"strings"

	"github.com/kataras/iris/v12"
)

func SearchBranch(ctx iris.Context) {

	project_name := ctx.FormValue("project_name")
	key_word := ctx.FormValue("key_word")

	if strings.Contains(project_name, "11.") {
		project_name = strings.Replace(project_name, "11.", "11_", -1)
		project_name = strings.Replace(project_name, "P", "_P", -1)
	}

	// 查找 匹配的分支或者路径
	// path := dpackagepath.Response{}
	// err := path.Find
	newBranches := []string{}
	result := []interface{}{}
	status := map[string]interface{}{}

	result1, _ := libs.ExecCommand(fmt.Sprintf("cd /mnt/sata0/data/branchcheck11.x && sh check_svn_branch.sh %s %s", project_name, key_word))
	result2, _ := libs.ExecCommand(fmt.Sprintf("cd /mnt/sata0/data/branchcheck11.x && sh check_git_branch.sh %s", key_word))

	for _, item := range strings.Split(result1, "\n") {
		if item != "" {
			_item := strings.Split(item, " ")
			newBranches = append(newBranches, strings.Replace(_item[0], "$", "", -1))
			status[strings.Replace(_item[0], "$", "", -1)] = map[string]string{"write": _item[1], "writer_username": "", "path": strings.Replace(_item[0], "$", "", -1)}
		}
	}
	for _, item := range strings.Split(result2, "\n") {
		if item != "" {
			_item := strings.Split(item, " ")
			newBranches = append(newBranches, strings.Replace(_item[0], "$", "", -1))
			status[strings.Replace(_item[0], "$", "", -1)] = map[string]string{"write": _item[1], "writer_username": "", "path": strings.Replace(_item[0], "$", "", -1)}
		}
	}
	branchInfos, _ := dbranchinfo.FindByNewBranch(newBranches)
	for _, item := range branchInfos {
		status[item.NewBranchUrl].(map[string]string)["writer_username"] = item.WriterUserName
		result = append(result, status[item.NewBranchUrl])
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func GetProjects(ctx iris.Context) {
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	pageSize = -1

	projects, err := dao.All(&dproject.Response{}, ctx, "", sort, orderBy, page, pageSize)
	if err != nil {
		logging.ErrorLogger.Error("get projects err", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, projects, response.NoErr.Msg))
	return
}
