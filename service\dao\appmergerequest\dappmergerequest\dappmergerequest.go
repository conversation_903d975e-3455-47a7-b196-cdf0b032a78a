package dappmergerequest

import (
	"fmt"
	"strings"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models"
	"irisAdminApi/application/models/appmergerequest"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "MR表单"

const (
	Running = 9
	Waiting = 0
	Success = 1
	Failed  = 2
)

type AppMergeRequest struct {
	models.ModelBase
	SourceProjectID uint   `gorm:"not null" json:"source_project_id"`
	SourceProject   string `gorm:"not null; type:varchar(200)" json:"source_project"`
	SourceBranch    string `gorm:"not null; type:varchar(200)" json:"source_branch"`

	TargetProjectID  uint   `gorm:"not null" json:"target_project_id"`
	TargetProject    string `gorm:"not null; type:varchar(200)" json:"target_project"`
	TargetBranch     string `gorm:"not null; type:varchar(200)" json:"target_branch"`
	MirrorFrom       string `gorm:"not null; type:varchar(200)"  json:"mirror_from"`
	MergeRequestID   uint   `gorm:"not null" json:"merge_request_id"`
	MergeRequestIID  uint   `gorm:"not null" json:"merge_request_Iid"`
	ReleaseProjectID uint   `gorm:"not null" json:"release_project_id"`
	ReleaseProject   string `gorm:"not null; type:varchar(200)" json:"release_project"`
	AssigneeIDs      string `gorm:"not null; type:varchar(200)" json:"assignee_ids"`
	ReviewerIDs      string `gorm:"not null; type:varchar(200)" json:"reviewer_ids"`

	WorkPackageID uint `gorm:"not null" json:"work_package_id"`
	// 以下字段写入description
	PreCheck       bool   `gorm:"not null" json:"pre_check"`
	Portable       bool   `gorm:"not null" json:"portable"`
	Type           string `gorm:"not null; type:varchar(60)" json:"type"` // new, bugfix
	BugID          string `gorm:"not null; type:varchar(200)" json:"bug_id"`
	WorkPackage    string `gorm:"not null; type:varchar(200)" json:"work_package"`
	LocalBuildPass bool   `gorm:"not null" json:"local_build_pass"`
	MFAL           uint   `gorm:"not null" json:"mfal"` // merge first audit later    //1: create  2: update
	ScreenshotFile string `gorm:"not null;" json:"screenshot_file"`

	Title             string `gorm:"not null; type:varchar(512)" json:"title"`
	OriginTitle       string `gorm:"not null; type:varchar(512)" json:"origin_title"`
	Description       string `gorm:"not null;" json:"description"`
	OriginDescription string `gorm:"not null;" json:"origin_description"`
	UserID            uint   `gorm:"not null" json:"user_id"`
	User              User   ` json:"user"`
	// 同步MR数据
	Status             int  `gorm:"not null; type: tinyint" json:"status"` //-1: 创建失败  0：创建中 1: 待评审  2：待合并   3：已合并    4：已关闭
	PipelineStatus     uint `gorm:"not null" json:"pipeline_status"`
	PhabricatorStatus  uint `gorm:"not null" json:"phabricator_status"`
	CodeQuantityAdd    uint `gorm:"not null" json:"code_quantity_add"`
	CodeQuantityRemove uint `gorm:"not null" json:"code_quantity_remove"`

	Assignees            []*User                    `gorm:"many2many:app_merge_request_assignees; foreignKey:ID; references:ID; joinForeignKey: MergeRequestID; joinReferences: UserID" json:"assignees"`
	AssigneeUsernames    []string                   `gorm:"-" json:"assignee_usernames"`
	Reviewers            []*User                    `gorm:"many2many:app_merge_request_reviewers; foreignKey:ID; references:ID; joinForeignKey: MergeRequestID; joinReferences: UserID" json:"reviewers"`
	ReviewerUsernames    []string                   `gorm:"-" json:"reviewer_usernames"`
	OverTime             bool                       `gorm:"not null;default:false" json:"over_time"`
	RelateWorkPackage    AppMergeRequestWorkPackage `gorm:"->; foreignKey:WorkPackageID" json:"relate_work_package"`
	RelateReleaseProject ReleaseProject             `gorm:"->; foreignKey:ReleaseProjectID" json:"relate_release_project"`
	Notice               *AppMergeRequestNotice     `gorm:"->; foreignKey:MergeRequestID" json:"notice"`
	Phabricator          AppMergeRequestPhabricator `gorm:"->; foreignKey:MergeRequestID" json:"Phabricator"`
	// 新增Dependencies字段
	Dependencies      []*AppMergeRequest `gorm:"many2many:app_merge_request_dependencies;foreignKey:ID;references:ID;joinForeignKey:MergeRequestID;joinReferences:DependencyID" json:"dependencies"`
	DependencyIDs     string             `gorm:"not null; type:varchar(200)" json:"dependency_ids"`
	ButtonClickStatus int                `gorm:"not null; type: tinyint" json:"button_click_status" `    // 0: 未点击  1： 已点击
	ButtonClickUser   string             `gorm:"not null; type: varchar(200)" json:"button_click_user" ` // 点击按钮人员
	MergeRequestSHA   string             `gorm:"not null; type:varchar(200)" json:"merge_request_sha"`   // merge  sha 合并唯一标记
	PmsSync           bool               `gorm:"not null; default: false" json:"pms_sync" form:"pms_sync"`
	CodeDiffSync      bool               `gorm:"not null; default: false" json:"code_diff_sync"`
	AiCheckStatus     uint               `gorm:"not null; default: 0" json:"ai_check_status"`
	CodeCheckStatus   uint               `gorm:"not null; default: 0" json:"code_check_status"`
}

type AppMergeRequestNotice struct {
	ID             uint           `gorm:"primarykey" json:"id"`
	CreatedAt      time.Time      `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt      time.Time      `gorm:"autoUpdateTime" json:"updated_at"`
	DeletedAt      gorm.DeletedAt `gorm:"index" json:"deleted_at"`
	MergeRequestID uint           `gorm:"not null;uniqueIndex" json:"merge_request_id"`
}

type ReleaseProject struct {
	models.ModelBase
	Name string `gorm:"uniqueIndex; not null; type:varchar(60)" json:"name"`
	PmID uint   `json:"pm_id"`
	Pm   User   `gorm:"->; foreignKey:PmID" json:"pm" `
}

type AppMergeRequestWorkPackage struct {
	appmergerequest.AppMergeRequestWorkPackage
	Owner          User           `gorm:"->" json:"owner"`
	Pstl           User           `gorm:"->" json:"pstl"`
	ReleaseProject ReleaseProject `gorm:"->" json:"release_project"`
}

type AppMergeRequestPhabricator struct {
	models.ModelBase
	MergeRequestID  uint   `gorm:"not null" json:"merge_request_id"`
	DifferentialUrl string `gorm:"not null; type:varchar(500)" json:"differential_url"`
}

// type MergeRequest struct {
// 	ID   uint   `json:"id"`
// 	Name string `json:"name"`
// }

type User struct {
	ID       uint   `json:"id"`
	Username string `json:"username"`
	Name     string `json:"name"`
	Intro    string `json:"intro"`
}

type Dependency struct {
	ID     uint   `json:"id"`
	Title  string `json:"title"`
	Status int    `json:"status"`
}

type ListResponse struct {
	AppMergeRequest
	Description       string `gorm:"-" json:"description"`
	OriginDescription string `gorm:"-" json:"origin_description"`
}

type Request struct {
	appmergerequest.AppMergeRequest
	DifferentialUrl string   `json:"differential_url"`
	Targets         []string `json:"targets"`
	// Assignees         []string `json:"assignees"`
	AssigneeIDs       []uint   `json:"assignee_ids"`
	AssigneeUsernames []string `gorm:"-" json:"assignee_usernames"`
	// Reviewers         []string `json:"reviewers"`
	ReviewerIDs       []uint   `json:"reviewer_ids"`
	ReviewerUsernames []string `gorm:"-" json:"reviewer_usernames"`
	DependencyIDs     []uint   `json:"dependency_ids"`
	DependencySets    []string `gorm:"-" json:"dependency_sets"`
}

func (a *AppMergeRequest) ModelName() string {
	return ModelName
}

func Model() *appmergerequest.AppMergeRequest {
	return &appmergerequest.AppMergeRequest{}
}

func (a *AppMergeRequest) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Preload("Assignees").Preload("Reviewers").Preload("Dependencies")
	where := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		where = where.Where("group_name_ch like ?", fmt.Sprintf("%%%s%%", name)).
			Or("group_name_en like ?", fmt.Sprintf("%%%s%%", name)).
			Or("category_name_ch like ?", fmt.Sprintf("%%%s%%", name)).
			Or("category_name_en like ?", fmt.Sprintf("%%%s%%", name)).
			Or("description_ch like ?", fmt.Sprintf("%%%s%%", name)).
			Or("description_en like ?", fmt.Sprintf("%%%s%%", name))
		if strings.Contains("海外库", name) {
			where = where.Or("oversea = 1")
		}
		if strings.Contains("大库", name) {
			where = where.Or("large = 1")
		}
		if strings.Contains("小库", name) {
			where = where.Or("small = 1")
		}
		if strings.Contains("中库", name) {
			where = where.Or("middle = 1")
		}
		if strings.Contains("默认阻断", name) {
			where = where.Or("pre_def_block = 1")
		}
	}
	db = db.Where(where)
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *AppMergeRequest) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *AppMergeRequest) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *AppMergeRequest) CreateV2(object interface{}) error {
	return nil
}

func (a *AppMergeRequest) BatchCreate(mrs []*AppMergeRequest) error {
	for _, mr := range mrs {
		reviewers := []*User{}

		err := easygorm.GetEasyGormDb().Model(&User{}).Where("username in ?", mr.ReviewerUsernames).Find(&reviewers).Error
		if err != nil {
			logging.ErrorLogger.Errorf("find data err ", err)
			return err
		}
		mr.Reviewers = reviewers

		assignees := []*User{}

		err = easygorm.GetEasyGormDb().Model(&User{}).Where("username in ?", mr.AssigneeUsernames).Find(&assignees).Error
		if err != nil {
			logging.ErrorLogger.Errorf("find data err ", err)
			return err
		}
		mr.Assignees = assignees

		dependencies := []*AppMergeRequest{}
		err = easygorm.GetEasyGormDb().Model(&AppMergeRequest{}).Where("id in ?", strings.Split(mr.DependencyIDs, ",")).Find(&dependencies).Error
		if err != nil {
			logging.ErrorLogger.Errorf("find data err ", err)
			return err
		}
		mr.Dependencies = dependencies

	}
	// 使用事务
	tx := easygorm.GetEasyGormDb().Begin()
	err := tx.Omit("Assignees", "Reviewers").Create(&mrs).Error
	if err != nil {
		tx.Rollback()                                          // 出错时回滚
		logging.ErrorLogger.Errorf("create data err: %v", err) // 增加更多错误信息
		return err
	}
	// 单独插入 Assignees、Reviewers 表信息
	for _, mr := range mrs {
		if len(mr.Assignees) > 0 {
			for _, assignee := range mr.Assignees {
				// 插入数据到 app_merge_request_assignees 表（事务中）
				instObject := map[string]interface{}{
					"MergeRequestID": mr.ID,
					"UserID":         assignee.ID,
				}
				if err := tx.Model(&appmergerequest.AppMergeRequestAssignee{}).Create(instObject).Error; err != nil {
					tx.Rollback()                                                                           // 出错时回滚
					logging.ErrorLogger.Errorf("create assignee err for MergeRequestID %v: %v", mr.ID, err) // 增加更多错误信息
					return err
				}
			}
		}
		if len(mr.Reviewers) > 0 {
			for _, reviewer := range mr.Reviewers {
				// 插入数据到 app_merge_request_reviewers 表（事务中）
				instObject := map[string]interface{}{
					"MergeRequestID": mr.ID,
					"UserID":         reviewer.ID,
				}
				if err := tx.Model(&appmergerequest.AppMergeRequestReviewer{}).Create(instObject).Error; err != nil {
					tx.Rollback()                                                                           // 出错时回滚
					logging.ErrorLogger.Errorf("create reviewer err for MergeRequestID %v: %v", mr.ID, err) // 增加更多错误信息
					return err
				}
			}
		}
	}
	// 提交事务
	return tx.Commit().Error
}

func (a *AppMergeRequest) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *AppMergeRequest) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *AppMergeRequest) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *AppMergeRequest) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *AppMergeRequest) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Preload("Assignees").Preload("Reviewers").Preload("Dependencies").Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *AppMergeRequest) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *AppMergeRequest) Save() error {
	err := easygorm.GetEasyGormDb().Model(u).Save(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update merge request err ", err)
		return err
	}
	return nil
}

func (u *AppMergeRequestPhabricator) Save() error {
	err := easygorm.GetEasyGormDb().Model(u).Save(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update phabricator err ", err)
		return err
	}
	return nil
}

func FindAllNotCreatedMergeRequest() ([]*ListResponse, error) {
	items := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("status <= 0 and release_project_id != 0").Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return items, err
	}
	return items, nil
}

func FindByProjetIDAndMergeRequestIID(targetProjectID, mergeRequestIID uint) (AppMergeRequest, error) {
	item := AppMergeRequest{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("target_project_id = ? and merge_request_i_id = ?", targetProjectID, mergeRequestIID).Find(&item).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return item, err
	}
	return item, nil
}

func All(userID uint, releaseProjectID, workPackageID uint, name, sort, orderBy, status string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Preload("Assignees").Preload("Reviewers").Preload("Dependencies")
	where := easygorm.GetEasyGormDb().Model(Model()).Where("release_project_id != 0")
	if len(name) > 0 {
		where = where.Where("title like ?", fmt.Sprintf("%%%s%%", name))
	}
	if len(status) > 0 {
		switch status {
		case "over_time_merged":
			where = where.Where("status = 3 and created_at < getWorkDay(updated_at, -3)")
		case "over_time_unmerged":
			where = where.Where("status in (1, 2) and created_at < getWorkDay(now(), -3)")
		default:
			where = where.Where("status = ?", status)
		}
	}
	if userID > 0 {
		where = where.Where("user_id = ?", userID)
	}
	if releaseProjectID > 0 {
		where = where.Where("release_project_id = ?", releaseProjectID)
	}
	if workPackageID > 0 {
		where = where.Where("work_package_id = ?", workPackageID)
	}
	db = db.Where(where)
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func AllV2(userID uint, releaseProjectID, workPackageID uint, name, sort, orderBy, status string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Preload("Assignees").Preload("Reviewers").Preload("Dependencies")
	where := easygorm.GetEasyGormDb().Model(Model()).Where("target_branch REGEXP ?", `^[0-9]+\.[0-9]+\.[0-9]+$`).Where("source_branch not REGEXP ?", `^develop$|^master$`)
	if len(name) > 0 {
		where = where.Where("title like ?", fmt.Sprintf("%%%s%%", name))
	}
	if len(status) > 0 {
		switch status {
		case "over_time_merged":
			where = where.Where("status = 3 and created_at < getWorkDay(updated_at, -3)")
		case "over_time_unmerged":
			where = where.Where("status in (1, 2) and created_at < getWorkDay(now(), -3)")
		default:
			where = where.Where("status = ?", status)
		}
	}
	if userID > 0 {
		where = where.Where("user_id = ?", userID)
	}
	if releaseProjectID > 0 {
		where = where.Where("release_project_id = ?", releaseProjectID)
	}
	if workPackageID > 0 {
		where = where.Where("work_package_id = ?", workPackageID)
	}
	db = db.Where(where)
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func AllV3(userID uint, releaseProjectID, workPackageID uint, name, sort, orderBy, status string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Preload("Assignees").Preload("Reviewers").Preload("Dependencies")
	where := easygorm.GetEasyGormDb().Model(Model()).Where("type =? ", "deploy")
	if len(name) > 0 {
		where = where.Where("title like ?", fmt.Sprintf("%%%s%%", name))
	}
	if len(status) > 0 {
		switch status {
		case "over_time_merged":
			where = where.Where("status = 3 and created_at < getWorkDay(updated_at, -3)")
		case "over_time_unmerged":
			where = where.Where("status in (1, 2) and created_at < getWorkDay(now(), -3)")
		default:
			where = where.Where("status = ?", status)
		}
	}
	if userID > 0 {
		where = where.Where("user_id = ?", userID)
	}
	if releaseProjectID > 0 {
		where = where.Where("release_project_id = ?", releaseProjectID)
	}
	if workPackageID > 0 {
		where = where.Where("work_package_id = ?", workPackageID)
	}

	db = db.Where(where)
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (u *AppMergeRequest) CheckExistSameOpen(sourceProjectID, targetProjectID uint, sourceBranch, targetBranch string) (bool, error) {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("source_project_id = ? and target_project_id =? and source_branch = ? and target_branch = ? and status in (1, 2)", sourceProjectID, targetProjectID, sourceBranch, targetBranch).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return false, err
	}
	if u.ID > 0 {
		return true, nil
	}
	return false, nil
}

func MergeRequestFilterOverTimeNotMerged() ([]*AppMergeRequest, error) {
	items := []*AppMergeRequest{}
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("RelateReleaseProject").Preload("RelateReleaseProject.Pm").Preload("Notice").Preload("User").Preload("Assignees").Preload("Reviewers").Preload("Dependencies").Preload("RelateWorkPackage").Preload("RelateWorkPackage.Pstl").Where("status in (1, 2) and created_at < getWorkDay(now(), -3)").Order("release_project desc, work_package desc").Find(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func MergeRequestFilterNotOverTime(status uint, condition string) ([]*AppMergeRequest, error) {
	items := []*AppMergeRequest{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("status = ? and updated_at > ? and over_time = 0", status, condition).Order("release_project desc, work_package desc").Find(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func BatchUpdate(records []map[string]interface{}) error {
	db := easygorm.GetEasyGormDb().Model(Model())
	err := db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(Model()).Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{"over_time"}),
		}).Create(&records).Error
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func GetAllDependencysByReleaseProjectID(releaseProjectID uint) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse
	db := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Preload("Assignees").Preload("Reviewers").Preload("Dependencies")
	where := easygorm.GetEasyGormDb().Model(Model()).Where("status !=? ", 4)
	where = where.Where("release_project_id = ?", releaseProjectID)
	db = db.Where(where)

	err := db.Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count}
	return list, nil
}

func FindAllCodeDiffSync() ([]*ListResponse, error) {
	items := []*ListResponse{}

	db := easygorm.GetEasyGormDb().Model(Model()).Where("status = 3 and code_diff_sync = 0 and target_project_id != 0").Order("id desc")

	err := db.Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	return items, nil
}

func FindAllCodeCheckMRs() ([]*ListResponse, error) {
	items := []*ListResponse{}

	db := easygorm.GetEasyGormDb().
		Model(Model()).
		Preload("User").
		Where("status in (1, 2) and (code_check_status in (0, 9)) and ((target_project_id in (88, 433) and target_branch = 'test-integration') or target_project_id in (437, 339))").
		Order("id desc").
		Limit(1)

	err := db.Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	return items, nil
}

func (u *AppMergeRequest) CheckCodeCheckStatus(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Select("code_check_status", "status", "button_click_status").Find(u).Error
	return err
}
