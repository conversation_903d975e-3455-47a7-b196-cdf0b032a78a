package libs

import (
	"strconv"
	"strings"

	"github.com/pkg/errors"
)

// InArrayS 如果 s 在 items 中,返回 true；否则，返回 false。
func InArrayS(items []string, s string) bool {
	for _, item := range items {
		if item == s {
			return true
		}
	}
	return false
}

func InArrayUint(items []uint, s uint) bool {
	for _, item := range items {
		if item == s {
			return true
		}
	}
	return false
}

func InArrayInt(items []int, i int) bool {
	for _, item := range items {
		if item == i {
			return true
		}
	}
	return false
}

func InArrayI(items []interface{}, i interface{}) bool {
	for _, item := range items {
		if item == i {
			return true
		}
	}
	return false
}

// 连接 unit slice 为字符串
func UintJoin(ss []uint, sep string) string {
	var rs string
	for index, item := range ss {
		itemS := strconv.FormatUint(uint64(item), 10)
		if index < len(ss)-1 {
			rs += itemS + sep
		} else {
			rs += itemS
		}
	}
	return rs
}

func ReverseSlice[T any](s []T) {
	for i, j := 0, len(s)-1; i < j; i, j = i+1, j-1 {
		s[i], s[j] = s[j], s[i]
	}
}

func StringToUintArray(s, sep string) ([]uint, error) {
	items := []uint{}
	for _, s := range strings.Split(s, sep) {
		if s != "" {
			ss, err := strconv.Atoi(s)
			if err != nil {
				return items, errors.Wrap(err, "")
			}
			items = append(items, uint(ss))
		}
	}
	return items, nil
}
