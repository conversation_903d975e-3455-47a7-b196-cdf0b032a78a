package datasync

import (
	"irisAdminApi/application/models"
)

type DfxMemoryInfo struct {
	models.ModelBase
	Name      string `gorm:"uniqueIndex:idx_name_type_unique; not null; type:varchar(200)" json:"name"`
	Type      string `gorm:"uniqueIndex:idx_name_type_unique; not null; type:varchar(60)" json:"type" update:"1"`
	Component string `gorm:"not null; type:varchar(60)" json:"component"  update:"1"`
	Owner     string `gorm:"not null; type:varchar(60)" json:"owner"  update:"1"`
}
