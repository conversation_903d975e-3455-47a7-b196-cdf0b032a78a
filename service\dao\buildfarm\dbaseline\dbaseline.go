package dbaseline

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/buildfarm"
)

const ModelName = "每日编译任务管理"

type Response struct {
	Id        uint   `json:"id"`
	UpdatedAt string `json:"updated_at"`
	CreatedAt string `json:"created_at"`
	JobId     string `json:"job_id"`
	Project   string `json:"project"`
	Repo      string `json:"repo"`
	Branch    string `json:"branch"`
	TaskType  uint   `json:"task_type"`  //编译类型，1：产品编译  2：组件编译
	BuildType string `json:"build_type"` //编译模式 debug release gcov
	Product   string `json:"product"`
	Defconfig string `json:"defconfig"`
	Target    string `json:"target"`
	Dir       string `json:"dir"`
	Status    uint   `json:"status"` // 作业状态 0：运行，1：成功， 2：失败
	Version   string `json:"version"`
}

type ListResponse struct {
	Response
}

type Request struct {
	Id        uint   `json:"id"`
	JobId     string `json:"job_id"`
	Project   string `json:"project"`
	Repo      string `json:"repo"`
	Branch    string `json:"branch"`
	TaskType  uint   `json:"task_type"` //编译类型，1：产品编译  2：组件编译
	Product   string `json:"product"`
	Defconfig string `json:"defconfig"`
	Target    string `json:"target"`
	Dir       string `json:"Dir"`
	Status    uint   `json:"status"` // 作业状态 0：运行，1：成功， 2：失败
	Version   string `json:"version"`
}

func (this *Response) ModelName() string {
	return ModelName
}

func Model() *buildfarm.Baseline {
	return &buildfarm.Baseline{}
}

func (this *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (this *Response) Update(id uint, object map[string]interface{}) error {
	err := this.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindAllCronMakeJobs(repo, branch, start, end, status, product, defconfig, target, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(repo) > 0 {
		db = db.Where("repo >= ?", repo)
	}
	if len(branch) > 0 {
		db = db.Where("branch >= ?", branch)
	}
	if len(start) > 0 {
		db = db.Where("created_at >= ?", start)
	}
	if len(start) > 0 {
		db = db.Where("created_at >= ?", start)
	}
	if len(end) > 0 {
		db = db.Where("created_at <= ?", end)
	}
	if len(status) > 0 {
		db = db.Where("status = ?", status)
	}
	if len(product) > 0 {
		db = db.Where(fmt.Sprintf("product like '%%%s%%'", product))
		// db = db.Where("status = ?", status)
	}
	if len(defconfig) > 0 {
		db = db.Where(fmt.Sprintf("defconfig like '%%%s%%'", defconfig))
		// db = db.Where("defconfig = ?", defconfig)
	}
	if len(target) > 0 {
		db = db.Where(fmt.Sprintf("target like '%%%s%%'", target))
		// db = db.Where("target = ?", target)
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}
