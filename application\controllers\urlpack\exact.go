package urlpack

import (
	"errors"
	"fmt"
	"io/fs"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"os"
	"path/filepath"

	"encoding/json"

	"github.com/kataras/iris/v12"
	"github.com/xuri/excelize/v2"
)

var ExactFileName = "url_accuracy.db"

func GetExactDir() string {
	var exactDir = libs.Config.UrlPack.ExactDir
	if libs.Config.UrlPack.ExactDir == "" {
		exactDir = filepath.Join("/tmp/exact/")
	}
	return exactDir
}

func UploadExact(ctx iris.Context) {
	exactDir := GetExactDir()
	os.MkdirAll(exactDir, 0755)
	file, fh, err := ctx.FormFile("file")

	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	defer file.Close()
	//验证文件格式是否是xlsx
	if filepath.Ext(fh.Filename) != ".xlsx" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "文件格式错误，请上传xlsx文件。"))
		return
	}

	// 保存文件到临时文件夹
	tempFilePath := filepath.Join(exactDir, "temp.xlsx")
	_, err = ctx.SaveFormFile(fh, tempFilePath)
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	// 删除临时Excel文件
	defer os.Remove(tempFilePath)

	// 打开文件
	f, err := excelize.OpenFile(tempFilePath)
	if err != nil {
		logging.ErrorLogger.Errorf("Error opening Excel file: %s", err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Failed to open Excel file."))
		return
	}
	defer f.Close()

	// 读取指定sheet
	sheetName := "NTOS-URL国内精准库"
	rows, err := f.GetRows(sheetName)
	//如果sheetName不存在，则返回错误
	if err != nil {
		logging.ErrorLogger.Errorf("Error reading sheet: %s", err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Sheet名称为"+sheetName+"的sheet不存在"))
		return
	}

	// 提取列A和B的数据
	data := make(map[string]string)
	for _, row := range rows {
		if len(row) >= 2 {
			key := row[0]
			value := row[1]
			data[key] = value
		}
	}

	// 转换为JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		logging.ErrorLogger.Errorf("Error marshalling JSON: %s", err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Failed to convert data to JSON."))
		return
	}

	// 保存JSON到文件
	err = os.WriteFile(filepath.Join(exactDir, ExactFileName), jsonData, 0644)
	if err != nil {
		logging.ErrorLogger.Errorf("Error writing JSON file: %s", err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Failed to save JSON file."))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func DownloadExact(ctx iris.Context) {
	exactDir := GetExactDir()
	_, err := os.Stat(filepath.Join(exactDir, ExactFileName))
	if errors.Is(err, fs.ErrNotExist) {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "精准库不存在，请先上传精准库。"))
		return
	}
	ctx.SendFile(filepath.Join(exactDir, ExactFileName), ExactFileName)
	return
}
