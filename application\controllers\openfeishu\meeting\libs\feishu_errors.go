package libs

import (
	"fmt"
	"strings"
	"time"
)

// FeishuAPIError 飞书API错误结构
type FeishuAPIError struct {
	Code        int    `json:"code"`
	Message     string `json:"msg"`
	RequestID   string `json:"request_id,omitempty"`
	Description string // 详细描述
	Suggestion  string // 处理建议
}

func (e *FeishuAPIError) Error() string {
	if e.Description != "" {
		return fmt.Sprintf("飞书API错误 [%d]: %s - %s", e.Code, e.Message, e.Description)
	}
	return fmt.Sprintf("飞书API错误 [%d]: %s", e.Code, e.Message)
}

// IsRetryable 判断错误是否可重试
func (e *FeishuAPIError) IsRetryable() bool {
	switch e.Code {
	case 190003, 190004, 190005, 190010: // 内部错误、频率限制
		return true
	default:
		return false
	}
}

// GetSuggestion 获取处理建议
func (e *FeishuAPIError) GetSuggestion() string {
	if e.Suggestion != "" {
		return e.Suggestion
	}
	return "请检查请求参数和权限配置"
}

// FeishuErrorHandler 飞书错误处理器
type FeishuErrorHandler struct{}

// NewFeishuErrorHandler 创建错误处理器
func NewFeishuErrorHandler() *FeishuErrorHandler {
	return &FeishuErrorHandler{}
}

// ParseAPIError 解析飞书API错误
func (h *FeishuErrorHandler) ParseAPIError(code int, message string) *FeishuAPIError {
	err := &FeishuAPIError{
		Code:    code,
		Message: message,
	}

	// 根据错误码设置详细描述和建议
	switch code {
	// 通用错误
	case 190002:
		err.Description = "请求参数无效"
		err.Suggestion = "检查请求参数的字段名称、传参类型是否正确，确认已申请相应资源权限，确认相应资源未被删除"
	case 190003:
		err.Description = "内部服务错误"
		err.Suggestion = "服务器内部错误，请稍后重试，如持续出现请联系技术支持"
	case 190004:
		err.Description = "方法频率限制"
		err.Suggestion = "请求频率过高，建议稍后再试并适当减小请求QPS"
	case 190005:
		err.Description = "应用频率限制"
		err.Suggestion = "应用请求频率过高，建议稍后再试并适当减小请求QPS"
	case 190006:
		err.Description = "应用认证错误"
		err.Suggestion = "检查应用App ID和App Secret是否正确"
	case 190007:
		err.Description = "应用机器人能力未开启"
		err.Suggestion = "确保应用开启了机器人能力"
	case 190010:
		err.Description = "当前操作被限流"
		err.Suggestion = "公用资源并发抢占失败，适当降低操作频率后重试"
	case 190011:
		err.Description = "租户加密密钥已删除"
		err.Suggestion = "加解密状态的自主密钥被删除，被该密钥加密的数据不可用"
	case 190012:
		err.Description = "租户解密密钥已删除"
		err.Suggestion = "仅解密状态的自主密钥被删除，被该密钥加密的数据不可用"
	case 190013:
		err.Description = "内容被风险控制"
		err.Suggestion = "传入的内容被风险控制，请检查内容是否合法"
	case 190014:
		err.Description = "请求参数无效"
		err.Suggestion = "请从details字段查看详细原因"

	// 日历相关错误
	case 191000:
		err.Description = "日历未找到"
		err.Suggestion = "检查并使用正确的日历ID"
	case 191001:
		err.Description = "日历ID无效"
		err.Suggestion = "检查并使用正确的日历ID格式"
	case 191002:
		err.Description = "无日历访问权限"
		err.Suggestion = "确保当前身份拥有该日历的访问权限"
	case 191003:
		err.Description = "日历已被删除"
		err.Suggestion = "检查并使用正确的日历ID"
	case 191004:
		err.Description = "日历类型错误"
		err.Suggestion = "确保日历类型为primary或shared"

	// 日程相关错误
	case 193000:
		err.Description = "日程ID无效"
		err.Suggestion = "检查并使用正确的日程ID"
	case 193001:
		err.Description = "日程未找到"
		err.Suggestion = "确保传入了正确的日程ID"
	case 193002:
		err.Description = "无日程操作权限"
		err.Suggestion = "确保有日历以及日程的编辑权限"
	case 193003:
		err.Description = "日程已被删除"
		err.Suggestion = "检查并使用正确的日程ID"
	case 193101:
		err.Description = "机器人组织者权限限制"
		err.Suggestion = "组织者是Bot时，无法指定主持人，且allow_attendees_start必须为true"
	case 193102:
		err.Description = "只有组织者可以指定主持人"
		err.Suggestion = "只有日程组织者可以指定主持人，且组织者不能是应用日历"
	case 193105:
		err.Description = "附件Token无效"
		err.Suggestion = "检查file_token参数是否正确"
	case 193106:
		err.Description = "附件大小超限"
		err.Suggestion = "附件总大小不能超过25MB"
	case 193107:
		err.Description = "无权访问附件Token"
		err.Suggestion = "检查传入的file_token是否正确"

	// 参与人相关错误
	case 194000:
		err.Description = "参与人未找到"
		err.Suggestion = "确保参与人相关参数填写正确"
	case 194001:
		err.Description = "无权限查看参与人列表"
		err.Suggestion = "检查calendar_id是否是当前日程组织者日历，或者组织者是否有查看参与人的权限"
	case 194002:
		err.Description = "无权限添加参与人"
		err.Suggestion = "检查calendar_id是否是当前日程组织者日历，或者组织者是否有邀请参与人的权限"
	case 194003:
		err.Description = "无权限删除参与人"
		err.Suggestion = "检查calendar_id是否是当前日程组织者日历"
	case 194004:
		err.Description = "参与人类型无效"
		err.Suggestion = "检查参与人类型是否填写正确"

	// 用户相关错误
	case 195100:
		err.Description = "用户已离职或不存在"
		err.Suggestion = "检查并使用正确的用户身份"
	case 195109:
		err.Description = "日程不支持创建会议群"
		err.Suggestion = "检查日历是否为当前身份的主日历且有writer权限，日程参与人是否至少为2个，且有查看参与人列表的权限"
	case 195110:
		err.Description = "日程不支持创建会议纪要"
		err.Suggestion = "检查是否有日历、日程的操作权限，以及是否有查看日程参与人列表的权限"
	case 195112:
		err.Description = "会议密码无效"
		err.Suggestion = "会议密码仅支持4-9位数字"
	case 195113:
		err.Description = "只有组织者可以设置会议密码"
		err.Suggestion = "仅日程组织者可以设置会议密码"

	// 应用信息相关错误
	case 99992402:
		err.Description = "字段验证失败"
		err.Suggestion = "检查API请求参数格式，确保必填字段完整且格式正确。对于应用信息API，需要在URL路径中指定app_id，并提供lang查询参数"

	default:
		err.Description = "未知错误"
		err.Suggestion = "请检查请求参数和权限配置，如问题持续请联系技术支持"
	}

	return err
}

// FormatErrorForLogging 格式化错误用于日志记录
func (h *FeishuErrorHandler) FormatErrorForLogging(err *FeishuAPIError, context map[string]interface{}) string {
	var parts []string

	parts = append(parts, fmt.Sprintf("错误码: %d", err.Code))
	parts = append(parts, fmt.Sprintf("错误信息: %s", err.Message))

	if err.Description != "" {
		parts = append(parts, fmt.Sprintf("详细描述: %s", err.Description))
	}

	if err.Suggestion != "" {
		parts = append(parts, fmt.Sprintf("处理建议: %s", err.Suggestion))
	}

	if err.RequestID != "" {
		parts = append(parts, fmt.Sprintf("请求ID: %s", err.RequestID))
	}

	// 添加上下文信息
	if context != nil {
		for key, value := range context {
			parts = append(parts, fmt.Sprintf("%s: %v", key, value))
		}
	}

	return strings.Join(parts, " | ")
}

// GetRetryDelay 获取重试延迟时间
func (h *FeishuErrorHandler) GetRetryDelay(err *FeishuAPIError, retryCount int) time.Duration {
	if !err.IsRetryable() {
		return 0
	}

	// 指数退避策略
	baseDelay := time.Second
	switch err.Code {
	case 190004, 190005: // 频率限制
		baseDelay = 5 * time.Second
	case 190010: // 操作限流
		baseDelay = 2 * time.Second
	default:
		baseDelay = time.Second
	}

	// 指数退避，最大30秒
	delay := baseDelay * time.Duration(1<<uint(retryCount))
	if delay > 30*time.Second {
		delay = 30 * time.Second
	}

	return delay
}
