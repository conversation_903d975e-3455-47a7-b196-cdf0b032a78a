from docxtpl import DocxTemplate
import sys
import json
import logging

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("docxtpl_replace.log"),
        logging.StreamHandler(sys.stdout)
    ]
)

def replace_with_docxtpl(template_path, output_path, context):
    try:
        logging.info(f"加载模板文档: {template_path}")
        doc = DocxTemplate(template_path)
        logging.debug(f"替换上下文: {context}")
        doc.render(context)
        doc.save(output_path)
        logging.info(f"保存替换后的文档为: {output_path}")
    except Exception as e:
        logging.error(f"替换时出错: {e}")

if __name__ == "__main__":
    if len(sys.argv) != 4:
        logging.error("用法: python replace_with_docxtpl.py <模板文件> <输出文件> <替换JSON>")
        sys.exit(1)
    
    template_file = sys.argv[1]
    output_file = sys.argv[2]
    try:
        context = json.loads(sys.argv[3])
        logging.debug(f"替换上下文: {context}")
    except json.JSONDecodeError:
        logging.error("错误: 替换参数的JSON格式无效。")
        sys.exit(1)
    
    replace_with_docxtpl(template_file, output_file, context)