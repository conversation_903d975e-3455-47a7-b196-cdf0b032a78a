package buildfarm

import (
	"bytes"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/cache"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/buildfarm/dbuildfarmjob"
	"irisAdminApi/service/dao/buildfarm/dproject"
	"irisAdminApi/service/dao/user/duser"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

func PushMailQueueV3(job *dbuildfarmjob.BuildfarmJob, status int) {
	if libs.Config.Mail.Enable {
		rc := cache.GetRedisClusterClient()
		from := "编译农场"
		to := duser.UserMap[job.UserID].Username + "@ruijie.com.cn"
		subject := fmt.Sprintf("[编译农场][普通编译][作业ID:%s][%s]", job.JobID, statusMap[status])
		body := fmt.Sprintf(`%s<br>编译日志: <a href="http://%s:%d/logs/%s.log">编译日志</a>`, subject, libs.Config.Nginx.HOST, libs.Config.Nginx.Port, job.JobID)
		if status == 1 {
			// http://*************:9090/output/6836284b1703e4433505de56022d8ca5/
			body = fmt.Sprintf(`%s<br>编译结果: <a href="http://%s:%d/output/%s/">编译结果</a>`, body, libs.Config.Nginx.HOST, libs.Config.Nginx.Port, job.JobID)
		}
		msg := strings.Join([]string{from, to, subject, body}, "|")
		_, err := rc.LPush(libs.Config.Mail.Queue, msg)
		if err != nil {
			logging.ErrorLogger.Error(err)
		}
	}
}

func updateMakeJobV3(job *dbuildfarmjob.BuildfarmJob, status int) {
	err := job.Update(job.ID, map[string]interface{}{
		"ServerID":  job.ServerID,
		"Status":    status,
		"UpdatedAt": time.Now(),
	})
	if err != nil {
		logging.ErrorLogger.Errorf("update make job err ", err)
		return
	}
	if status == 1 || status == 2 {
		PushMailQueueV3(job, status)
	}
}

func updateMakeJobSoftwareVersionAndNumberV3(job *dbuildfarmjob.BuildfarmJob) {
	archivePath := filepath.Join(libs.Config.Buildfarm.Archivepath, job.JobID)
	var packageName, systemSoftwareVersion, systemSoftwareNumber, releaseBinDir string
	var packageNameSlice, dirs []string
	dirs, err := libs.GetAllDir(archivePath, dirs)
	if err != nil {
		logging.ErrorLogger.Error("read fail", err)
		return
	}

	for _, f := range dirs {
		if strings.HasSuffix(f, "releaseID-bin") {
			releaseBinDir = f
		}
	}

	if len(releaseBinDir) > 0 {
		files, _ := os.ReadDir(releaseBinDir)
		for _, f := range files {
			if strings.Contains(f.Name(), ".bin") {
				packageName = f.Name()
				packageNameSlice = strings.Split(packageName, "_")
			}
			if f.Name() == "releaseID" {
				f, err := os.ReadFile(filepath.Join(releaseBinDir, f.Name()))
				if err != nil {
					logging.ErrorLogger.Error("read fail", err)
				}
				reg := regexp.MustCompile("\\s+")
				systemSoftwareVersion = fmt.Sprintf("%s_%s,Release(%s)", packageNameSlice[0], packageNameSlice[1], reg.ReplaceAllString(string(f), ""))
			}
			if f.Name() == "softnum" {
				f, err := os.ReadFile(filepath.Join(releaseBinDir, f.Name()))
				if err != nil {
					logging.ErrorLogger.Error("read fail", err)
				}
				reg := regexp.MustCompile("\\s+")
				systemSoftwareNumber = reg.ReplaceAllString(string(f), "")
			}
		}
	}
	err = job.Update(job.ID, map[string]interface{}{
		"UpdatedAt":       time.Now(),
		"SoftwareVersion": systemSoftwareVersion,
		"SoftwareNumber":  systemSoftwareNumber,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("update git job err ", err)
	}
}

func CheckQueueV3() {
	// 获取所有服务器状态
	// 获取所有运行作业

	logging.DebugLogger.Debugf("检查队列状态")
	queuingJobs, err := dbuildfarmjob.FindQueueJobs()
	if err != nil {
		logging.ErrorLogger.Error(err)
		return
	}
	for _, queuingJob := range queuingJobs {
		serverID := ChooseServerV3(fmt.Sprintf("%v", 1), false)
		logging.DebugLogger.Debugf("编译来源ID: %v  服务器ID: %v", queuingJob.Source, serverID)
		if serverID > 0 {
			queuingJob.ServerID = serverID
			if queuingJob.TaskType == 1 {
				go MakeProductjobV3(queuingJob)
			}
			if queuingJob.TaskType == 2 {
				go MakejobV3(queuingJob)
			}
		}
	}
}

func MakejobV3(job *dbuildfarmjob.BuildfarmJob) {
	updateMakeJobV3(job, 0)
	client, err := SSHClient(job.ServerID)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		updateMakeJobV3(job, 2)
		return
	}
	defer client.Close()

	f, _ := os.OpenFile(filepath.Join(libs.Config.Buildfarm.Logpath, job.JobID+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	defer f.Close()

	command := fmt.Sprintf(`echo 当前编译服务器： $(hostname)`)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJobV3(job, 2)
		return
	}

	command = fmt.Sprintf(`rm %s -rf && git clone -b %s %s %s`, job.GetCompileDir(), job.Branch, job.Repo, job.GetCompileDir())
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJobV3(job, 2)
		return
	}
}

func MakeProductjobV3(job *dbuildfarmjob.BuildfarmJob) {
	updateMakeJobV3(job, 0)
	client, err := SSHClient(job.ServerID)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		updateMakeJobV3(job, 2)
		return
	}
	defer client.Close()

	f, _ := os.OpenFile(filepath.Join(libs.Config.Buildfarm.Logpath, job.JobID+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	defer f.Close()

	command := fmt.Sprintf(`echo 当前编译服务器： $(hostname)`)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJobV3(job, 2)
		return
	}

	command = fmt.Sprintf(`rm %s -rf && git clone -b %s %s %s`, job.GetCompileDir(), job.Branch, job.Repo, job.GetCompileDir())
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJobV3(job, 2)
		return
	}

	BuildTypePrevTask(client, f, job.GetCompileDir(), job.Product, job.JobID, job.BuildType)
	command = fmt.Sprintf(`cd %s/prj_%s && source env-setup && make download && make %s && make && make pack-all`, job.GetCompileDir(), job.Product, job.Defconfig)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJobV3(job, 2)
		return
	}

	updateMakeJobV3(job, 1)
	if err := ArchiveOutputV2(job.ServerID, job.JobID, fmt.Sprintf("%s/prj_%s/output/images/", job.GetCompileDir(), job.Product), job.Repo, job.Branch, job.Target); err == nil {
		updateMakeJobSoftwareVersionAndNumberV3(job)
		command = fmt.Sprintf("rm -rf %s", job.GetCompileDir())
		if err := RunCommandOutFile(client, command, f); err != nil {
			logging.ErrorLogger.Errorf("run command error", err.Error())
			return
		}
	}
	BuildTypePostTask(client, f, job.JobID, job.BuildType)

}

func CreateMakeJobV3(ctx iris.Context) {
	userID, _ := dao.GetAuthId(ctx)
	request := dbuildfarmjob.Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	// project := dproject.Response{}
	// if err := project.Find(request.ProjectID); err != nil {
	// 	logging.ErrorLogger.Error(err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
	// 	return
	// }

	// if project.Id == 0 {
	// 	ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "未找到该项目"))
	// 	return
	// }

	// if project.Enable == 0 {
	// 	ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "此项目不支持编译"))
	// 	return
	// }

	// 检查是不是已经有相同作业排队或者运行中
	job := dbuildfarmjob.BuildfarmJob{}
	if err := job.FindSameJob(request.Repo, request.Branch, request.Product, request.Defconfig, request.Target, userID); err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if job.ID != 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "已有相同作业编译中或者排队中"))
		return
	}

	jobID := libs.GetUniqueID()
	if err := job.Create(map[string]interface{}{
		"JobID":     jobID,
		"ServerID":  0,
		"Source":    request.Source,
		"ProjectID": 0,
		"Project":   request.Project,
		"Repo":      request.Repo,
		"TaskType":  request.TaskType,
		"Branch":    request.Branch,
		"Product":   request.Product,
		"Defconfig": request.Defconfig,
		"Target":    request.Target,
		"BuildType": request.BuildType,
		"Status":    3,
		"UserID":    userID,
		"CreatedAt": time.Now(),
	}); err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetMakeJobsV3(ctx iris.Context) {
	// uId, err := dao.GetAuthId(ctx)
	// if err != nil {
	// 	uId = uint(0)
	// }

	userID := ctx.FormValue("user_id")

	source := ctx.FormValue("source")
	repo := ctx.FormValue("repo")
	branch := ctx.FormValue("branch")
	// server := ctx.FormValue("server")
	taskType := ctx.FormValue("task_type")
	// customized := ctx.FormValue("customized")

	start := ctx.FormValue("start")
	end := ctx.FormValue("end")
	status := ctx.FormValue("status")
	product := ctx.FormValue("product")
	defconfig := ctx.FormValue("defconfig")
	target := ctx.FormValue("target")
	buildType := ctx.FormValue("build_type")
	softnum := ctx.FormValue("softnum")
	softversion := ctx.FormValue("softversion")

	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	archive := ctx.FormValue("archive")

	// if userID == "" {
	// 	userID = fmt.Sprintf("%v", uId)
	// }

	jobs, err := dbuildfarmjob.All(userID, source, taskType, buildType, repo, branch, product, defconfig, target, start, end, archive, status, softnum, softversion, sort, orderBy, page, pageSize)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, jobs, response.NoErr.Msg))
	return
}

func CheckVersionV3(ctx iris.Context) {
	taskId := ctx.FormValue("task_id")
	if taskId == "" {
		logging.ErrorLogger.Errorf("check make job version err ")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	archivePath := filepath.Join(libs.Config.Buildfarm.Archivepath, taskId)
	var buildInfoPath string
	filepath.Walk(archivePath, func(path string, info os.FileInfo, err error) error {
		pathSlice := strings.Split(path, "/")
		if pathSlice[len(pathSlice)-1] == "build_info" {
			buildInfoPath = path
			return io.EOF
		}
		return nil
	})
	if buildInfoPath == "" {
		logging.ErrorLogger.Errorf("check make job version err ")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未找到build_info文件，无法检查！"))
		return
	}

	f, err := ioutil.ReadFile(buildInfoPath)
	if err != nil {
		logging.ErrorLogger.Errorf("check make job version err ")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "读取build_info文件失败！"))
		return
	}
	buildInfoObjects := []map[string]interface{}{}
	count := 0
	failCount := 0
	for _, item := range strings.Split(string(f), "\n") {
		_project := map[string]interface{}{}
		if item == "" {
			continue
		}
		if strings.Contains(item, "BuildTime") {
			continue
		}
		slice := strings.Split(strings.Replace(item, "git:", "git ", -1), " ")
		_project["version"] = slice[len(slice)-1]
		version := slice[len(slice)-1]
		branch := slice[2]
		project := strings.Split(strings.Split(slice[1], "/")[len(strings.Split(slice[1], "/"))-1], ".")[0]
		url := slice[1]
		latesVersion, err := GetLatestBranchVersion(project, branch)
		if err != nil {
			version = "(Unkown) " + version
			failCount++
		}
		if version == latesVersion {
			version = "(Head) " + version
			count++
		}

		buildInfoObjects = append(buildInfoObjects, map[string]interface{}{
			"version": version,
			"url":     url,
			"branch":  branch,
			"project": project,
		})
	}
	result := map[string]interface{}{}
	result["items"] = buildInfoObjects
	if count == len(buildInfoObjects) {
		result["comment"] = "当前编译为最新编译，所有分支均为最新提交！"
	} else {
		if failCount > 0 {
			result["comment"] = "部分分支检查失败，请重试。"
		} else {
			result["comment"] = "当前编译不是最新编译，部分分支不是最新提交！"
		}

	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return

}

func GetLatestBranchVersionV3(project, branch string) (string, error) {
	var version string
	for i := 0; i <= 3; i++ {
		// url := fmt.Sprintf("/projects/%s/repository/branches/%s")
		var err error
		token := libs.Config.Buildfarm.Token
		if token == "" {
			return "", errors.New("未配置Token,请联系管理员")
		}
		buildProject := dproject.Response{}

		err = buildProject.FindEx("name", project)
		if err != nil {
			continue
		}
		if buildProject.Id == 0 {
			return "", errors.New("编译农场找不到这个项目，请联系管理员")
		}
		url := fmt.Sprintf("%s/api/%s/projects/%d/repository/branches/%s?private_token=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, buildProject.GitlabId, branch, token)
		req, err := http.NewRequest("GET", url, nil)
		if err != nil {
			logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
			continue
		}
		result, err := libs.HandlerRequest(req)
		if err != nil {
			logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
			continue
		}
		logging.DebugLogger.Debug(result, url)
		if result["commit"] != nil {
			version = result["commit"].(map[string]interface{})["id"].(string)
		}
		return version, nil
	}
	return version, errors.New("检查项目版本失败，请重试")
}

func StopAndDeleteJobV3(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	job := dbuildfarmjob.BuildfarmJob{}
	err := job.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("get cron job by id err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	serverID := job.ServerID
	dir := job.GetCompileDir()

	err = job.Delete(id)
	if err != nil {
		logging.ErrorLogger.Errorf("delete record err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	client, err := SSHClient(serverID)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	defer client.Close()
	// ps -ef|grep 'bash -c cd 	/mnt/sata0/a9084b221004db27e1b8d1f4430e04eb'|grep make|awk '{print $2,$3}'
	command := fmt.Sprintf("ps -ef | grep -v grep | grep 'bash -c cd' | grep %s | awk '{print $2,$3}'", dir)

	var stdOut bytes.Buffer
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("kill job err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	pids := stdOut.String()
	if pids != "" {
		command = fmt.Sprintf("kill -9 %s", pids)
		RunCommandOutBuffer(client, command, &stdOut)
	}
	command = fmt.Sprintf(`if [ -d "%s"]; then rm %s -rf;fi`, dir, dir)
	if err := RunCommandOutBuffer(client, command, &stdOut); err == nil {
		logging.ErrorLogger.Errorf("delete dir err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}
