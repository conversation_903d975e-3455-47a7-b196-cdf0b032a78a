package firewallflextrans

import (
	"github.com/kataras/iris/v12"
)

var Party = func(party iris.Party) {
	party.Post("/jobs", CreateTranJob).Name = "创建作业"
	party.Get("/jobs", GetTranJobs).Name = "查看记录"
	party.Get("/jobs/{id:uint}", GetTranJob).Name = "查看单个记录"
	party.Get("/jobs/{id:uint}/{filename:string}", DownloadTranFile).Name = "下载加密文件"
	party.Get("/jobs/{id:uint}/log", DownloadTranLog).Name = "下载加密文件"
}
