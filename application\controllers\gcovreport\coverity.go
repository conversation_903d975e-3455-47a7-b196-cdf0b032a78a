package gcovreport

import (
	"fmt"
	"os"
	"path"
	"path/filepath"
	"strings"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/buildfarm/dproject"
	"irisAdminApi/service/dao/datasync/dcommoncomponent"

	"github.com/kataras/iris/v12"
)

func GetCoverityComponentMapRule(ctx iris.Context) {
	ret, err := CommonComponentGenerateWorker()
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, ret, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, ret, response.NoErr.Msg))
	return
}

func CommonComponentGenerateWorker() ([]map[string]interface{}, error) {
	// clone Trunk
	result := []map[string]interface{}{}
	tempPathName := libs.GetUUID()
	covTempDir := filepath.Join("/tmp", "gcov_temp", tempPathName)
	err := os.RemoveAll(covTempDir)
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		return result, err
	}

	err = os.MkdirAll(covTempDir, 0o755)
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		return result, err
	}

	defer os.RemoveAll(covTempDir)

	covRepos, err := dproject.FindAllEanbledCovProjects()
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		return result, err
	}
	tempDirs := []string{}

	// 初始化本地目录
	for _, covRepo := range covRepos {
		nameSlice := strings.Split(covRepo.Name, "/")
		dirName := nameSlice[len(nameSlice)-1]
		tempDir := filepath.Join(covTempDir, dirName)
		tempDirs = append(tempDirs, tempDir)

		command := fmt.Sprintf("git clone %s %s", covRepo.Repo, tempDir)
		output, err := libs.ExecCommand(command)
		if err != nil {
			logging.ErrorLogger.Errorf(err.Error(), command, output)
			return result, err
		}
	}

	mkDirMap := map[string][]string{}
	mkDirVersionMap := map[string]bool{}
	for _, tempDir := range tempDirs {
		command := fmt.Sprintf(`cd %s && git branch -a|grep remotes|grep -v '\->'|awk -F '/' '{print $NF}'|grep -v cherry-pick|grep -v revert|grep -v test`, tempDir)
		branchesOutput, err := libs.ExecCommand(command)
		if err != nil {
			logging.ErrorLogger.Errorf(err.Error(), command, branchesOutput)
			return result, err
		}

		for _, branch := range strings.Split(strings.TrimSpace(branchesOutput), "\n") {
			if strings.TrimSpace(branch) == "" {
				continue
			}
			command := fmt.Sprintf(`cd %s && git checkout %s`, tempDir, branch)
			mksOutput, err := libs.ExecCommand(command)
			if err != nil {
				logging.ErrorLogger.Errorf(err.Error(), command, mksOutput)
				return result, err
			}

			command = fmt.Sprintf(`find %s -name "*.mk"|awk -F '/' '{if ($NF==$(NF-1)".mk") print $0}'|sed 's@\./@@g'`, tempDir)
			mksOutput, err = libs.ExecCommand(command)
			if err != nil {
				logging.ErrorLogger.Errorf(err.Error(), command, mksOutput)
				return result, err
			}

			for _, mk := range strings.Split(strings.TrimSpace(mksOutput), "\n") {
				if strings.TrimSpace(mk) == "" {
					continue
				}
				_mk := strings.ReplaceAll(strings.TrimSpace(mk), strings.TrimSuffix(covTempDir, "/")+"/", "")

				mkSlice := strings.Split(_mk, "/")
				mkDir := strings.Join(mkSlice[0:len(mkSlice)-1], "/")
				mkDirMap[mkDir] = mkSlice
				mkContent, err := os.ReadFile(mk)
				if err != nil {
					logging.ErrorLogger.Errorf(err.Error())
					mkDirVersionMap[mkDir] = false
				}
				if strings.Contains(string(mkContent), "_VERSION =") {
					mkDirVersionMap[mkDir] = true
				} else {
					mkDirVersionMap[mkDir] = true
				}
			}
		}
	}
	commonComponents, err := dcommoncomponent.All()
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		return result, err
	}

	coverityRules := [][]string{
		{"/usr/include/c++", "compile-tool(开源-不处理)"},
		{".*/build-framework/.*", "compile_tool(开源-不处理)"},
		{".*/host/.*", "compile-tool(开源-不处理)"},
		{".*/host-.*/.*", "compile-tool(开源-不处理)"},
		{".*/base-os/.*", "baseos(开源-不处理)"},
		{".*/linux-.*/.*", "linux(开源)"},
		{".*/hyperscan-5.2.1/.*", "hyperscan(开源-不处理)"},
		{".*/libecoli-5.2.3.m1/.*", "libecoli(开源-不处理)"},
		{".*/libconsole-5.2.3.m1/.*", "libconsole(开源-不处理)"},
		{".*/geoip-1.6.12/.*", "geoip(开源-不处理)"},
		{".*/libxslt-1.1.39/.*", "libxslt(开源-不处理)"},
		{".*/vrf-5.2.3.m1/.*", "vrf(原厂-不处理)"},
		{".*/gd-2.3.0/.*", "libgd(开源-不处理)"},
		{".*/daemonctl-5.2.3.m1/.*", "daemonctl(开源-不处理)"},
		{".*/lldpd-1.0.4/.*", "lldpd(开源-不处理)"},
		{".*/sig-security-1.0.0/.*", "sig-security"},
		{".*/factory-.*/.*", "factory"},
		{".*/nginx-.*/.*", "nginx(开源)"},
		{".*/python-psycopg2-.*/.*", "python-psycopg2(开源-不处理)"},
		{".*/rtrlib-.*/.*", "原厂代码"},
		{".*/libgrpc-1.52.1/.*", "libgrpc(开源-不处理)"},
		{".*/grpc-app-5.2.3.m1/.*", "grpc-app(开源-不处理)"},
		{".*/grpcpp/.*", "grpc-app(开源-不处理)"},
		{".*/n2n/.*", "n2n"},
		{".*/fp-5.2.3.m1/fp-modules/wireguard/.*", "wireguard"},
		{".*/discovery-5.2.3.m1/.*", "discovety"},
		{".*/terminal-identify-5.2.3.m1/.*", "terminal-identify"},
		{".*/session-detect/.*", "session-detect"},
		{".*/capwap.*/.*", "capwap"},
		{".*/libcapwap.*/.*", "capwap"},
		{".*/apmg.*/.*", "apmg"},
		{".*/libapmg.*/.*", "apmg"},
		{".*/stamg.*/.*", "stamg"},
		{".*/libstamg.*/.*", "stamg"},
		{".*/wbs_mng.*/.*", "wbs_mng"},
		{".*/libwbs_mng.*", "wbs_mng"},
		{".*/wbs_ctl.*/.*", "wbs_ctl"},
		{".*/libwbs_ctl.*/.*", "wbs_ctl"},
		{".*/wbs_dev.*/.*", "wbs_dev"},
		{".*/libwbs_dev.*/.*", "wbs_dev"},
		{".*/wqos.*/.*", "wqos"},
		{".*/libwqos.*/.*", "wqos"},
		{".*/roam.*/.*", "roam"},
		{".*/libroam.*/.*", "roam"},
		{".*/wlan_intf.*/.*", "wlan_intf"},
		{".*/wvas_wids.*/.*", "wvas_wids"},
		{".*/wlan-redis_ctrl.*/.*", "wlan-redis_ctrl"},
		{".*/wlan-redis-server.*/.*", "wlan-redis-server"},
		{".*/wlib.*/.*", "wlib"},
		{".*/wlan-libutil.*/.*", "wlan-libutil"},
		{".*/wlan-lua.*/.*", "wlan-lua"},
		{".*/librgthread.*/.*", "librgthread"},
		{".*/fp-anmk.*/.*", "fp-anmk"},
		{".*/libwlan_nn.*/.*", "libwlan_nn"},
		{".*/pppoe.*/.*", "PPPoE"},
		{".*/vpdn.*/.*", "PPTP"},
		{".*/pptp.*/.*", "PPTP"},
		{".*/ppp.*/.*", "PPPD"},
	}

	for _, commonComponent := range commonComponents {
		rules := GetRulesFromMk(commonComponent, mkDirMap, mkDirVersionMap)
		if len(rules) > 0 {
			coverityRules = append(coverityRules, rules...)
		}
	}

	for _, rule := range coverityRules {
		result = append(result, map[string]interface{}{
			"componentId": map[string]string{
				"name": rule[1],
			},
			"pathPattern": rule[0],
		})
	}
	return result, nil
}

func GetRulesFromMk(commonComponent *dcommoncomponent.ListResponse, mkDirMap map[string][]string, mkDirVersionMap map[string]bool) [][]string {
	generateComponentName := func(comment, component string) string {
		if comment == "是" {
			return component + "(开源-不处理)"
		} else if comment == "否" || comment == "" {
			return component
		} else if comment != "" {
			return component + "(" + comment + ")"
		}
		return component
	}

	// 提取重复的 patternString 生成逻辑
	generatePatternString := func(base string, isVersioned bool, isFullPath bool, suffixParts ...string) string {
		if base == "sysrepo" {
			return ".*/sysrepo-5.2.3.m1/.*"
		}

		var patternString string
		if isVersioned {
			patternString = fmt.Sprintf(".*/%s-.*", base)
		} else {
			patternString = fmt.Sprintf(".*/%s", base)
		}

		if len(suffixParts) > 0 {
			patternString = path.Join(patternString, strings.Join(suffixParts, "/"))
		}

		if !isFullPath {
			patternString = fmt.Sprintf("%s/.*", patternString)
		}
		return patternString
	}

	checkIsFullPath := func(path string) bool {
		if strings.HasSuffix(path, ".h") || strings.HasSuffix(path, ".c") || strings.HasSuffix(path, ".cpp") || strings.HasSuffix(path, ".py") {
			return true
		}
		return false
	}

	fixComponentName := generateComponentName(commonComponent.Comment, commonComponent.Component)

	fullPath := commonComponent.Path
	isFullPath := checkIsFullPath(fullPath)
	// 查找路径包含多个mk的组件
	// 3种情况 1. 路径包含多个rpm包
	// 2. 路径与rpm包一致
	// 3. 一个rpm中有多个组件

	rules := [][]string{}
	for mk, mkSlice := range mkDirMap {
		if strings.HasPrefix(mk+"/", strings.TrimSuffix(fullPath, "/")+"/") {
			v, ok := mkDirVersionMap[mk]
			patternString := generatePatternString(mkSlice[len(mkSlice)-2], ok && v, false)
			rules = append(rules, []string{patternString, fixComponentName})
		}
	}

	// 查找专业组队伍建设路径与 mk路径一致的组件
	fullPathSlice := strings.Split(strings.TrimSuffix(fullPath, "/"), "/")
	for i := 2; i < len(fullPathSlice); i++ {
		fullPathParent := strings.Join(fullPathSlice[0:i], "/")
		if mkSlice, ok := mkDirMap[fullPathParent]; ok {
			if len(mkSlice) < 2 {
				// 如果 mkSlice 的长度不足 2，则跳过
				continue
			}

			if i == len(fullPathSlice)-1 {
				v, ok := mkDirVersionMap[fullPathParent]
				patternString := generatePatternString(mkSlice[len(mkSlice)-2], ok && v, isFullPath)
				rules = append(rules, []string{patternString, fixComponentName})
			} else {
				if len(fullPathSlice) > 5 {
					v, ok := mkDirVersionMap[fullPathParent]
					patternString := generatePatternString(mkSlice[len(mkSlice)-2], ok && v, isFullPath, fullPathSlice[len(fullPathSlice)-2], fullPathSlice[len(fullPathSlice)-1])
					rules = append(rules, []string{patternString, fixComponentName})
				} else {
					v, ok := mkDirVersionMap[fullPathParent]
					patternString := generatePatternString(mkSlice[len(mkSlice)-2], ok && v, isFullPath, fullPathSlice[len(fullPathSlice)-1])
					rules = append(rules, []string{patternString, fixComponentName})
				}
			}
		}
	}

	return rules
}
