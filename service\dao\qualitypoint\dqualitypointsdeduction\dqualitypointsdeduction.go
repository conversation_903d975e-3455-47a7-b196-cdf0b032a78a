package dqualitypointsdeduction

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/qualitypoint"
	"irisAdminApi/service/dao/qualitypoint/dqualityviolation"
)

const ModelName = "积分扣减记录表"

type Response struct {
	ID               uint                        `json:"id"`
	UpdatedAt        string                      `json:"updated_at"`
	CreatedAt        string                      `json:"created_at"`
	UserID           uint                        `json:"user_id"`         //用户ID
	ViolationID      uint                        `json:"violation_id"`    //事项ID
	PointsDeducted   float32                     `json:"points_deducted"` //扣减的积分值
	IssueLevel       uint                        `json:"issue_level"`     //问题等级
	ImpactDegree     uint                        `json:"impact_degree"`   //影响程度
	Year             uint                        `json:"year"`            //扣分操作的年度
	Qualityviolation *dqualityviolation.Response `gorm:"-" json:"qualityviolation"`
}

type ListResponse struct {
	Response
}

type Request struct {
	Name string `json:"name"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *qualitypoint.QualityPointsDeduction {
	return &qualitypoint.QualityPointsDeduction{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (a *Response) GetAllByUserIDAndYear(userId, year uint, name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}

	if userId > 0 {
		db = db.Where("user_id = ?", userId)
	}
	if year > 0 {
		db = db.Where("year = ?", year)
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}
	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	formatResponses(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func formatResponses(items []*ListResponse) {
	qualityViolationIds := []uint{}
	violationMap := map[uint]*dqualityviolation.Response{}
	for _, item := range items {
		qualityViolationIds = append(qualityViolationIds, item.ViolationID)
	}
	violations, err := dqualityviolation.FindInIds(qualityViolationIds)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return
	}

	for _, violation := range violations {
		violationMap[violation.ID] = &violation.Response
	}

	for _, item := range items {
		item.Qualityviolation = violationMap[item.ViolationID]
	}

}

type UserPointDeductionData struct {
	UserID               int     `json:"user_id"`
	Openid               string  `json:"openid"`
	PointsDeducted       float32 `json:"points_deducted"`
	Year                 int     `json:"year"`
	Description          string  `json:"description"`
	AssessmentItemCode   string  `json:"assessment_item_code"`
	AssessmentItemName   string  `json:"assessment_item_name"`
	IssueLevel           string  `json:"issue_level"`
	ImpactDegree         string  `json:"impact_degree"`
	DepartmentName       string  `json:"department_name"`
	ParentDepartmentName string  `json:"parent_department_name"`
}

func GetUserPointDeductionData() ([]*UserPointDeductionData, error) {
	items := []*UserPointDeductionData{}
	sql := `
  SELECT 
    qpd.user_id,
    uo.openid,
    qpd.points_deducted,
    qpd.year,
    qv.description AS description,
    qv.assessment_item_code  as assessment_item_code,
    qv.assessment_item_name as assessment_item_name,
    CASE 
        WHEN qpd.issue_level = 0 THEN '无'
        WHEN qpd.issue_level = 1 THEN '低级'
        WHEN qpd.issue_level = 2 THEN '中级'
        WHEN qpd.issue_level = 3 THEN '高级'
        WHEN qpd.issue_level = 4 THEN '不限'
        ELSE '未知'
    END AS issue_level,
    CASE 
        WHEN qpd.impact_degree = 0 THEN '无'
        WHEN qpd.impact_degree = 1 THEN '轻微'
        WHEN qpd.impact_degree = 2 THEN '一般'
        WHEN qpd.impact_degree = 3 THEN '严重'
        ELSE '未知'
    END AS impact_degree,
    d.name AS department_name,
    pd.name AS parent_department_name
FROM 
    quality_points_deductions AS qpd
LEFT JOIN 
    user_departments AS ud ON qpd.user_id = ud.user_id
LEFT JOIN 
    quality_violations AS qv ON qpd.violation_id = qv.id
LEFT JOIN 
    user_open_ids AS uo ON qpd.user_id = uo.user_id
LEFT JOIN 
   departments AS d ON ud.department_id = d.id
LEFT JOIN 
    departments AS pd ON d.parent_id = pd.id
WHERE 
    qpd.deleted_at IS NULL 
    AND qv.deleted_at IS NULL 
    AND d.deleted_at IS NULL 
    AND uo.deleted_at IS NULL 
    AND uo.openid IS NOT NULL 
    AND uo.openid != '';`
	err := easygorm.GetEasyGormDb().Table("quality_points_deductions").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}
