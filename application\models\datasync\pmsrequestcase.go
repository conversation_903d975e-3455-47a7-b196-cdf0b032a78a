package datasync

type PmsRequestCase struct {
	// models.ModelBase
	RequestID           int    `gorm:"not null; index:idx_unique, unique" json:"request_id" `
	ProjectID           int    `gorm:"not null; index:idx_unique, unique" json:"project_id" `
	ProjectCasePacketID int    `gorm:"not null; index:idx_unique, unique" json:"project_case_packet_id" `
	CaseAttribute       string `gorm:"not null; type:varchar(100)" json:"case_attribute" update:"1"`
	Disabled            bool   `gorm:"not null" json:"disabled" update:"1"`
	CasePacketName      string `gorm:"not null; type:varchar(100)" json:"case_packet_name" update:"1"`
	ChargeUserName      string `gorm:"not null; type:varchar(100)" json:"charge_user_name" update:"1"`
	// OverTimeAt          *time.Time `json:"over_time_at"`
}
