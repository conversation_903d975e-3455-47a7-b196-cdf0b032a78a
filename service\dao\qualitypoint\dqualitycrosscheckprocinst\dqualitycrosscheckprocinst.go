package dqualitycrosscheckprocinst

import (
	"encoding/json"
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/qualitypoint"
	"irisAdminApi/service/dao/qualitypoint/dqualitycrosscheck"
	"irisAdminApi/service/dao/qualitypoint/dqualitycrosscheckproctask"
	"irisAdminApi/service/dao/user/duser"
	"strings"
)

const ModelName = "共性问题排查实例"

type Response struct {
	ID    uint   `json:"id"`
	Title string `json:"title"`
	// 当前节点
	NodeID string `json:"node_id"`
	// 审批人
	// Candidate string `json:"candidate"`
	// 当前任务
	TaskID              int                                        `json:"task_id"`
	StartUserID         uint                                       `json:"start_user_id"`
	ResponsiblePersonID uint                                       `json:"responsible_person_id" ` //责任角色ID
	QualityCrossCheckID uint                                       `json:"quality_cross_check_id"`
	Status              uint                                       `json:"status"`
	Resource            string                                     `gorm:"size:10000" json:"resource,omitempty"`
	User                *duser.ApprovalResponse                    `gorm:"-" json:"user"`
	ResponsiblePerson   *duser.ApprovalResponse                    `gorm:"-" json:"responsible_person"`
	AuditManager        *duser.ApprovalResponse                    `gorm:"-" json:"audit_manager"`
	QualityCrossCheck   *dqualitycrosscheck.Response               `gorm:"-" json:"qualitycrosscheck"`
	Tasks               []*dqualitycrosscheckproctask.ListResponse `gorm:"-" json:"tasks"`
	DoneTasks           []*dqualitycrosscheckproctask.ListResponse `gorm:"-" json:"done_tasks"`

	AbstractCommonTasks *dqualitycrosscheckproctask.Response `gorm:"-" json:"abstract_common_tasks"`
}

type ListResponse struct {
	Response
}

type Request struct {
	Title string `json:"title"`
	// 当前节点
	NodeID string `json:"node_id"`
	// 审批人
	// Candidate string `json:"candidate"`
	// 当前任务
	TaskID             int    `json:"task_id"`
	StartUserID        string `json:"start_user_id"`
	QualityCrossCheckID string `json:"quality_crosscheck_id"`
	Resource           string `gorm:"size:10000" json:"resource,omitempty"`
	IsFinished         bool   `gorm:"default:false" json:"is_finished"`
}

type Node struct {
	Name       string `json:"name,omitempty"`
	NodeID     string `json:"nodeId,omitempty"`
	PrevNodeID string `json:"prevNodeId,omitempty"`
	Assignee   uint   `json:"assignee"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *qualitypoint.QualityCrossCheckProcInst {
	return &qualitypoint.QualityCrossCheckProcInst{}
}

func (a *Response) All(name, status, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	if len(status) > 0 {
		db = db.Where("status in ?", strings.Split(status, ","))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	formatResponses(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	formatResponse(u)
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindAll() ([]*Response, error) {
	var items []*Response

	if err := easygorm.GetEasyGormDb().Model(Model()).Find(&items).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return items, err
	}
	return items, nil
}

func FindInIds(ids []uint) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id in ?", ids).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	formatResponses(items)
	return items, nil
}

func DeleteByUUID(uuid string) error {
	err := easygorm.GetEasyGormDb().Unscoped().Where("uuid = ?", uuid).Delete(Model()).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func UpdateStatus(id uint, object map[string]interface{}) error {

	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func FormatResponse(items []*ListResponse) {
	userIds := []uint{}
	for _, item := range items {
		userIds = append(userIds, item.User.Id)
	}
	users, _ := duser.FindSimpleInIds(userIds)
	var userMap = make(map[uint]*duser.ApprovalResponse)
	for _, user := range users {
		userMap[user.Id] = user
	}
	for _, item := range items {
		item.User = userMap[item.User.Id]
	}
}

func createProcInst(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func AllMyAuditData(uid uint, name, status, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	if len(status) > 0 {
		db = db.Where("status in ?", strings.Split(status, ","))
	}
	if uid > 0 {
		db = db.Where("audit_manager_id = ?", uid)
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	formatResponses(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func AllMySubmissionsData(uid uint, name, status, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	if len(status) > 0 {
		db = db.Where("status in ?", strings.Split(status, ","))
	}
	if uid > 0 {
		db = db.Where("start_user_id = ?", uid)
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	formatResponses(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func AllMyResponsibleSubmissionsData(uid uint, name, status, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	if len(status) > 0 {
		db = db.Where("status in ?", strings.Split(status, ","))
	}
	if uid > 0 {
		db = db.Where("responsible_person_id = ?", uid)
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	formatResponses(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func GetPrevNodeIDs(resource string, nodeID string) ([]string, error) {
	prevNodeIDs := []string{}

	nodes := []*Node{}
	err := json.Unmarshal([]byte(resource), &nodes)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return prevNodeIDs, err
	}
	for _, node := range nodes {
		if node.NodeID == nodeID {
			prevNodeIDs = append(prevNodeIDs, node.PrevNodeID)
		}
	}
	return prevNodeIDs, nil
}

func GetNextNodeIDs(resource string, nodeID string) ([]string, error) {
	nextNodeIDs := []string{}

	nodes := []*Node{}
	err := json.Unmarshal([]byte(resource), &nodes)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return nextNodeIDs, err
	}
	for _, node := range nodes {
		if node.PrevNodeID == nodeID {
			nextNodeIDs = append(nextNodeIDs, node.NodeID)
		}
	}
	return nextNodeIDs, nil
}

func GetNode(resource string, nodeID string) (*Node, error) {
	nodes := []*Node{}

	err := json.Unmarshal([]byte(resource), &nodes)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return nil, err
	}
	for _, node := range nodes {
		if node.NodeID == nodeID {
			return node, nil
		}
	}
	return nil, nil
}

func GetNodes(resource string) ([]*Node, error) {
	nodes := []*Node{}

	err := json.Unmarshal([]byte(resource), &nodes)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return nodes, err
	}

	return nodes, nil
}

func formatResponses(items []*ListResponse) {
	qualityViolationIds := []uint{}
	procInstIDs := []uint{}
	userIds := []uint{}
	taskIds := []uint{}
	violationMap := map[uint]*dqualitycrosscheck.Response{}
	for _, item := range items {
		qualityViolationIds = append(qualityViolationIds, item.QualityCrossCheckID)
		userIds = append(userIds, item.StartUserID)
		userIds = append(userIds, item.ResponsiblePersonID)
		procInstIDs = append(procInstIDs, item.ID)
	}
	violations, err := dqualitycrosscheck.FindInIds(qualityViolationIds)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return
	}

	for _, violation := range violations {
		violationMap[violation.ID] = &violation.Response
	}

	users, _ := duser.FindSimpleInIds(userIds)
	var userMap = make(map[uint]*duser.ApprovalResponse)
	for _, user := range users {
		userMap[user.Id] = user
	}
	taskMap := map[uint][]*dqualitycrosscheckproctask.ListResponse{}
	tasks, err := dqualitycrosscheckproctask.FindInProcInstIDs(procInstIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("find tasks err ", err)
		return
	}
	for _, task := range tasks {
		taskMap[task.ProcInstID] = append(taskMap[task.Response.ProcInstID], task)
	}
	//已完成节点
	abstractCommonTasksMap := map[uint]*dqualitycrosscheckproctask.Response{}
	taskDoneMap := map[uint][]*dqualitycrosscheckproctask.ListResponse{}
	tasks2, err := dqualitycrosscheckproctask.FindInProcInstIDs2(procInstIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("find tasks err ", err)
		return
	}
	for _, task := range tasks2 {
		taskIds = append(taskIds, task.ID)
		if task.NodeID == "abstract_common" {
			abstractCommonTasksMap[task.Response.ProcInstID] = &task.Response
		}
	}
	tasks3, err := dqualitycrosscheckproctask.FindInProcInstIDs3(procInstIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("find tasks err ", err)
		return
	}
	for _, task := range tasks3 {
		taskDoneMap[task.ProcInstID] = append(taskDoneMap[task.Response.ProcInstID], task)
	}
	for _, item := range items {
		item.QualityCrossCheck = violationMap[item.QualityCrossCheckID]
		item.User = userMap[item.StartUserID]
		item.ResponsiblePerson = userMap[item.ResponsiblePersonID]
		item.Tasks = taskMap[item.ID]
		item.DoneTasks = taskDoneMap[item.ID]

		item.AbstractCommonTasks = abstractCommonTasksMap[item.ID]
	}

}

func formatResponse(item *Response) {
	qualityViolationIds := []uint{}
	procInstIDs := []uint{}
	userIds := []uint{}
	violationMap := map[uint]*dqualitycrosscheck.Response{}
	qualityViolationIds = append(qualityViolationIds, item.QualityCrossCheckID)
	userIds = append(userIds, item.StartUserID)
	userIds = append(userIds, item.ResponsiblePersonID)
	procInstIDs = append(procInstIDs, item.ID)
	taskIds := []uint{}

	violations, err := dqualitycrosscheck.FindInIds(qualityViolationIds)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return
	}

	for _, violation := range violations {
		violationMap[violation.ID] = &violation.Response
	}

	users, _ := duser.FindSimpleInIds(userIds)
	var userMap = make(map[uint]*duser.ApprovalResponse)
	for _, user := range users {
		userMap[user.Id] = user
	}
	taskMap := map[uint][]*dqualitycrosscheckproctask.ListResponse{}
	tasks, err := dqualitycrosscheckproctask.FindInProcInstIDs(procInstIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("find tasks err ", err)
		return
	}
	for _, task := range tasks {
		taskMap[task.ProcInstID] = append(taskMap[task.Response.ProcInstID], task)
	}
	//已完成节点数据
	abstractCommonTasksMap := map[uint]*dqualitycrosscheckproctask.Response{}
	taskDoneMap := map[uint][]*dqualitycrosscheckproctask.ListResponse{}
	tasks2, err := dqualitycrosscheckproctask.FindInProcInstIDs2(procInstIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("find tasks err ", err)
		return
	}
	for _, task := range tasks2 {
		taskIds = append(taskIds, task.ID)
		if task.NodeID == "abstract_common" {
			abstractCommonTasksMap[task.Response.ProcInstID] = &task.Response
		}
	}
	tasks3, err := dqualitycrosscheckproctask.FindInProcInstIDs3(procInstIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("find tasks err ", err)
		return
	}
	for _, task := range tasks3 {
		taskDoneMap[task.ProcInstID] = append(taskDoneMap[task.Response.ProcInstID], task)
	}

	item.QualityCrossCheck = violationMap[item.QualityCrossCheckID]
	item.User = userMap[item.StartUserID]
	item.ResponsiblePerson = userMap[item.ResponsiblePersonID]
	item.Tasks = taskMap[item.ID]
	item.DoneTasks = taskDoneMap[item.ID]

	item.AbstractCommonTasks = abstractCommonTasksMap[item.ID]
}

var ProductionProcInstUserMap = map[uint]uint{}
var ProductionIDProcInstIDMap = map[uint]uint{}

func UpdateFeatureProccessingCache(productionIds []uint) error {
	productionProcInsts := []*ListResponse{}
	if err := easygorm.GetEasyGormDb().Model(Model()).Where("quality_violation_id in ?", productionIds).Find(&productionProcInsts).Error; err != nil {
		return err
	}
	for _, inst := range productionProcInsts {
		ProductionProcInstUserMap[inst.QualityCrossCheckID] = inst.StartUserID
		ProductionIDProcInstIDMap[inst.QualityCrossCheckID] = inst.ID
	}
	return nil
}
