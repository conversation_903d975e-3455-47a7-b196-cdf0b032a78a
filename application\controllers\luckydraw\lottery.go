package luckydraw

import (
	"fmt"
	"irisAdminApi/application/controllers/productionrelease"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/luckydraw/dluckydrawcardconfig"
	"irisAdminApi/service/dao/luckydraw/dluckydrawlotteryrecord"
	"irisAdminApi/service/transaction/luckydraw/transluckydraw"
	"math/rand"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

type LuckyRequest struct {
	EmployeeID string `json:"employeeID"`
	Name       string `json:"name"`
	CityID     int    `json:"cityID"`
}

func GetZodiacCard(ctx iris.Context) {
	request := LuckyRequest{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	}
	if request.Name == "" {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "请填写姓名"))
		return
	}
	if request.EmployeeID == "" {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "请填写工号"))
		return
	}
	if request.CityID == 0 {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "请填写城市"))
		return
	}
	cityNames := map[int]string{
		1: "福州",
		2: "北京",
		3: "成都",
	}
	zodiacNames := map[int]string{
		1:  "鼠",
		2:  "牛",
		3:  "虎",
		4:  "兔",
		5:  "龙",
		6:  "蛇",
		7:  "马",
		8:  "羊",
		9:  "猴",
		10: "鸡",
		11: "狗",
		12: "猪",
	}

	// 检查是否已有抽奖记录
	recordCheck := dluckydrawlotteryrecord.Response{}
	err := recordCheck.CheckRecordExists(request.EmployeeID, request.Name)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	randomNumber := 0
	// 如果有记录，则返回上次的结果
	if recordCheck.Zodiac > 0 {
		randomNumber = int(recordCheck.Zodiac)
	} else {
		// 如果没有记录，进行新的抽奖
		err, randomNumber = transluckydraw.CreateDraw(request.EmployeeID, request.Name, request.CityID)
		if err != nil {
			if err.Error() != "生肖卡已分配完毕" {
				ctx.JSON(response.NewResponse(response.SystemErr.Code, "系统异常，请重新再试："+err.Error(), response.SystemErr.Msg))
				return
			}
		}
		if randomNumber == 0 {
			randomNumber = randExcept4(5, 12)
			// 创建抽奖记录
			LuckyObjects := []map[string]interface{}{}
			LuckyObjects = append(LuckyObjects, map[string]interface{}{
				"CreatedAt":  time.Now(),
				"WorkNumber": request.EmployeeID,
				"Name":       request.Name,
				"Zodiac":     randomNumber,
				"ZodiacName": zodiacNames[randomNumber],
				"City":       request.CityID,
				"CityName":   cityNames[request.CityID],
				"UpdatedAt":  time.Now(),
			})
			if err := easygorm.GetEasyGormDb().Model(dluckydrawlotteryrecord.Model()).Create(&LuckyObjects).Error; err != nil {
				ctx.JSON(response.NewResponse(response.SystemErr.Code, "系统异常，请重新再试："+err.Error(), response.SystemErr.Msg))
				return
			}

		}
	}
	randomNumberStr := strconv.Itoa(randomNumber)
	data := map[string]interface{}{"zodiacNumber": randomNumberStr}
	ctx.JSON(response.NewResponse(response.NoErr.Code, data, response.NoErr.Msg))
	return
}

func ExportRecords(ctx iris.Context) {
	cityID, _ := strconv.Atoi(ctx.FormValue("city"))
	zodiac := ctx.FormValue("zodiac")
	zodiacID := 4
	if zodiac == "all" {
		zodiacID = 0
	}
	records := dluckydrawlotteryrecord.Response{}
	items, err := records.ExportAll(cityID, zodiacID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	ExportData(ctx, records, items)
	return

}

func ResetData(ctx iris.Context) {
	password := ctx.FormValue("password")
	if password != "ruijie@123" {
		ctx.JSON(response.NewResponse(response.AuthErr.Code, "密码错误", response.AuthErr.Msg))
		return
	}
	//重置生肖配置表
	if err := easygorm.GetEasyGormDb().Model(dluckydrawcardconfig.Model()).Where("1 = 1").Update("quantity", gorm.Expr("total")).Error; err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, "系统异常，请重新再试："+err.Error(), response.SystemErr.Msg))
		return
	}
	//删除中奖记录
	if err := easygorm.GetEasyGormDb().Model(dluckydrawlotteryrecord.Model()).Where("1 = 1").Delete(dluckydrawlotteryrecord.Model()).Error; err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, "系统异常，请重新再试："+err.Error(), response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, "抽奖数据数据重置成功", response.NoErr.Msg))

}

func ExportData(ctx iris.Context, summary dluckydrawlotteryrecord.Response, items []*dluckydrawlotteryrecord.Response) {
	fileName := fmt.Sprintf("年会生肖中奖名单_%s.xlsx", time.Now().Format("20060102150405"))
	file := excelize.NewFile()
	streamWriter, err := file.NewStreamWriter("Sheet1")
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	rowNum := 1
	_, rows := summary.ExportRecord(items)
	for _, row := range rows {
		cell, _ := excelize.CoordinatesToCellName(1, rowNum)
		if err := streamWriter.SetRow(cell, row); err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
			return
		}
		rowNum++
	}
	if err := streamWriter.Flush(); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	if err := file.SaveAs(filepath.Join("/tmp", fileName)); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	defer os.Remove(filepath.Join("/tmp", fileName))
	ctx.SendFile(filepath.Join("/tmp", fileName), fileName)
	return
}

func SendToLark(ctx iris.Context) {
	// 飞书机器人的 Webhook URL
	webhookURL := "https://open.feishu.cn/open-apis/bot/v2/hook/95d8f994-57d2-467e-b2ba-8a4c1952a53e"
	// 构建消息
	message := productionrelease.Message{
		MsgType: "text",
		Content: struct {
			Text string `json:"text"`
		}{
			Text: "这是一条测试消息",
		},
	}
	// 发送消息
	err := productionrelease.SendToLark(webhookURL, message)
	if err != nil {
		panic(err)
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, "消息发送成功", response.NoErr.Msg))
}

func randExcept4(min, max int) int {
	if min > max {
		panic("min should be less than max")
	}
	src := rand.NewSource(time.Now().UnixNano())
	r := rand.New(src)
	if min <= 4 && max >= 4 { // 如果范围包括4，需要特殊处理
		for {
			num := r.Intn(max-min+1) + min
			if num != 4 {
				return num
			}
		}
	} else { // 如果范围不包括4，直接生成随机数
		return rand.Intn(max-min+1) + min
	}
}
