package coverityresult

import (
	"encoding/json"
	"fmt"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/coverityresult/dcoverityreporthistory"
	"irisAdminApi/service/dao/coverityresult/dcoverityresult"
	"irisAdminApi/service/dao/release/dreleaseprojectconfig"

	"github.com/pkg/errors"
)

func SyncCoverityResultWorker() {
	logging.InfoLogger.Infof("开始同步coverity查询结果数据")
	err := SyncEpgRule()
	if err != nil {
		logging.ErrorLogger.Errorf("同步coverity规则错误", err.Error())
	}

	// 获取项目列表，查找打开通知的项目
	enabledNoticeProjects, err := dreleaseprojectconfig.AllGcovEnabledConfig()
	if err != nil {
		logging.ErrorLogger.Error("get project config err ", err)
		return
	}

	for _, config := range enabledNoticeProjects {

		// filename := "20240815075205_NTOS1.0R9_z5100-s.json"
		// err = SyncCoverityResult(filename)
		// if err != nil {
		// 	logging.ErrorLogger.Errorf("同步coverity结果错误", err.Error(), filename)
		// }

		// filename = "20240816065925_NTOS1.0R8_z5100-s.json"
		// err = SyncCoverityResult(filename)
		// if err != nil {
		// 	logging.ErrorLogger.Errorf("同步coverity结果错误", err.Error(), filename)
		// }

		fileNamePatternStr := fmt.Sprintf(`".+_%s_.+json"`, config.ReleaseProject.Name)
		fileNamePattern := regexp.MustCompile(fileNamePatternStr)
		url := libs.Config.CoveritySync.Url
		resp, err := covResultClient.R().Get(url)
		if err != nil {
			logging.ErrorLogger.Errorf("获取文件列表错误", err.Error())
			return
		}
		var filename string
		for _, line := range strings.Split(resp.String(), "\n") {
			if fileNamePattern.MatchString(line) {
				filename = fileNamePattern.FindString(line)
				filename, err = strconv.Unquote(filename)
				if err != nil {
					logging.ErrorLogger.Errorf("获取文件名错误", err.Error(), line, fileNamePattern.FindString(line), fileNamePatternStr)
					return
				}
				// filename := fileNamePattern.FindString(line)
			}
		}
		if filename != "" {
			err = SyncCoverityResult(filename)
			if err != nil {
				logging.ErrorLogger.Errorf("获取coverity结果json错误", err.Error(), filename)
				continue
			}
		}
	}
	logging.InfoLogger.Infof("完成同步coverity查询结果数据")
}

func SyncCoverityResult(filename string) error {
	history := dcoverityreporthistory.CoverityReportHistory{}
	err := history.FindEx("filename", filename)
	if err != nil {
		return errors.Wrap(err, "")
	}
	if history.ID > 0 {
		return nil
	}

	reportHistory := map[string]interface{}{
		"Filename":  filename,
		"CreatedAt": time.Now(),
		"Status":    false,
	}

	objects := []map[string]interface{}{}
	url, err := url.JoinPath(libs.Config.CoveritySync.Url, filename)
	if err != nil {
		return errors.Wrap(err, "")
	}
	result := CovResponse{}
	resp, err := covResultClient.R().Get(url)
	if err != nil {
		return errors.Wrap(err, "")
	}
	if resp.IsErrorState() {
		return errors.New(resp.String())
	}

	jsonStr, err := strconv.Unquote(resp.String())
	if err != nil {
		// 失败时标记文件处理失败，无需重新处理
		_err := dcoverityreporthistory.BatchCreate([]map[string]interface{}{reportHistory})
		if _err != nil {
			return errors.Wrap(err, _err.Error())
		}
		return errors.Wrap(err, "")
	}

	// fmt.Println(jsonStr)
	err = json.Unmarshal([]byte(jsonStr), &result)
	if err != nil {
		return errors.Wrap(err, "")
	}

	idSlice := strings.Split(result.ID, "_")
	var project, product string
	if len(idSlice) > 1 {
		project = idSlice[0]
		product = idSlice[1]
	}

	defects := result.Defects
	for _, defect := range defects {
		for _, occurrence := range defect.Occurrences {
			objects = append(objects, map[string]interface{}{
				"Project":          project,
				"Product":          product,
				"CID":              defect.CID,
				"Mergekey":         defect.Mergekey,
				"Status":           defect.Status,
				"ComponentName":    defect.ComponentName,
				"CheckerName":      defect.CheckerName,
				"EventDescription": occurrence.EventDescription,
				"ContentsMD5":      occurrence.FiledID.ContentsMD5,
				"FilePathName":     occurrence.FiledID.FilePathName,
				"LineNumber":       occurrence.LineNumber,
				"ReportFileName":   filename,
			})
		}
	}
	// gorm batch create
	reportHistory["Status"] = true
	return dcoverityresult.BatchCreate(project, product, objects, []map[string]interface{}{reportHistory})
}
