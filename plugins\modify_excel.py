import sys
import json
import re
from openpyxl import load_workbook

def replace_text_in_cells(ws, replacements):
    """在单元格中替换占位符文本"""
    for row in ws.iter_rows():
        for cell in row:
            if cell.value and isinstance(cell.value, str):
                for old, new in replacements.items():
                    if old in cell.value:
                        cell.value = cell.value.replace(old, new)

def clear_remaining_placeholders_in_cells(ws):
    """清除所有剩余的占位符，即以 '{{' 开头，'}}' 结尾的文本"""
    placeholder_pattern = re.compile(r'{{.*?}}')
    for row in ws.iter_rows():
        for cell in row:
            if cell.value and isinstance(cell.value, str):
                new_value = placeholder_pattern.sub('', cell.value)
                if new_value != cell.value:
                    cell.value = new_value

def replace_text_in_excel(input_file, output_file, replacements):
    """加载Excel文件并替换占位符文本，然后清除所有剩余的占位符"""
    wb = load_workbook(input_file)
    for sheet in wb.worksheets:
        replace_text_in_cells(sheet, replacements)
        clear_remaining_placeholders_in_cells(sheet)
    wb.save(output_file)

if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("用法: python modify_excel.py <输入文件> <输出文件> <替换JSON>")
        sys.exit(1)

    input_file = sys.argv[1]
    output_file = sys.argv[2]
    try:
        replacements = json.loads(sys.argv[3])
    except json.JSONDecodeError:
        print("错误: 替换参数的JSON格式无效。")
        sys.exit(1)

    replace_text_in_excel(input_file, output_file, replacements)