package openfeishu

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"log"
	"regexp"
	"strings"
	"time"

	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	larkbitable "github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
	larkcontact "github.com/larksuite/oapi-sdk-go/v3/service/contact/v3"
	larkdrive "github.com/larksuite/oapi-sdk-go/v3/service/drive/v1"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	larkvc "github.com/larksuite/oapi-sdk-go/v3/service/vc/v1"
)

// 获取用户openid
func GetUserOpenIDByUserName(userName string) (string, error) {
	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)
	// 创建请求对象
	req := larkcontact.NewGetUserReqBuilder().
		UserId(userName).
		UserIdType(`user_id`).
		DepartmentIdType(`department_id`).
		Build()

	// 发起请求
	resp, err := client.Contact.User.Get(context.Background(), req)
	// 处理错误
	if err != nil {
		// logging.ErrorLogger.Errorf("get resources error", err.Error())
		return "", err
	}
	// 服务端错误处理
	if !resp.Success() {
		// logging.ErrorLogger.Errorf("get resources error", resp.Msg)
		return "", errors.New(resp.Msg)

	}
	return *resp.Data.User.OpenId, nil
}

func GetUserInfoByOpenID(openID string) (*larkcontact.GetUserResp, error) {
	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)
	// 创建请求对象
	req := larkcontact.NewGetUserReqBuilder().
		UserId(openID).
		Build()

	// 发起请求
	resp, err := client.Contact.User.Get(context.Background(), req)

	// 处理错误
	if err != nil {
		// fmt.Println(err)
		return resp, err
	}

	// 服务端错误处理
	if !resp.Success() {
		// fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		return resp, errors.New(resp.Msg)
	}

	// 业务处理
	// fmt.Println(larkcore.Prettify(resp))
	return resp, nil
}

func getUserName(openID string) (string, error) {
	userResp, err := GetUserInfoByOpenID(openID)
	if err != nil {
		return "", err
	}
	return *userResp.Data.User.Name, nil

}

// 获取文件夹下的清单
func GetFolderList(pageSize int, folderToken, pageToken string) (*larkdrive.ListFileResp, error) {
	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)
	// 创建请求对象
	reqBuilder := larkdrive.NewListFileReqBuilder()
	if pageSize > 0 {
		reqBuilder.PageSize(pageSize)
	}
	if len(pageToken) > 0 {
		reqBuilder.PageToken(pageToken)
	}
	if len(folderToken) > 0 {
		reqBuilder.FolderToken(folderToken)
	}
	req := reqBuilder.OrderBy(`CreatedTime`).Direction(`DESC`).Build()
	var resp *larkdrive.ListFileResp
	// 发起请求
	resp, err := client.Drive.File.List(context.Background(), req)
	// 处理错误
	if err != nil {
		// fmt.Println(err)
		logging.ErrorLogger.Errorf("get resources error", err.Error())
		return resp, err
	}
	// 服务端错误处理
	if !resp.Success() {
		// fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		logging.ErrorLogger.Errorf("get resources error", resp.Msg)
		return resp, errors.New(resp.Msg)
	}
	return resp, nil
}

// 创建项目文档数据表
func CreateProjectDocumentTable(tableName, BiAppToken string) (*larkbitable.CreateAppTableResp, error) {
	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)
	// 创建请求对象
	req := larkbitable.NewCreateAppTableReqBuilder().
		AppToken(BiAppToken).
		Body(larkbitable.NewCreateAppTableReqBodyBuilder().
			Table(larkbitable.NewReqTableBuilder().
				Name(tableName).
				Fields([]*larkbitable.AppTableCreateHeader{
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`文档名称`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`document_id`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`需求`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`工作包`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`负责人`).
						Type(11).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`文档评审状态`).Property(larkbitable.NewAppTableFieldPropertyBuilder().
						Options([]*larkbitable.AppTableFieldPropertyOption{
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`未确认`).
								Build(),
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`评审开始`).
								Build(),
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`评审结束`).
								Build(),
						}).
						Build()).
						Type(3).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`文档类型`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`链接`).
						Type(15).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`文档创建时间`).
						Type(5).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`评论总数`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`文档负责人`).
						Type(11).
						Build(),
				}).
				Build()).
			Build()).
		Build()

	// 发起请求
	var resp *larkbitable.CreateAppTableResp
	resp, err := client.Bitable.AppTable.Create(context.Background(), req)

	// 处理错误
	if err != nil {
		// fmt.Println(err)
		logging.ErrorLogger.Errorf("creat CreateProjectDocumentTable error", err.Error())
		return resp, err
	}

	// 服务端错误处理
	if !resp.Success() {
		// fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		logging.ErrorLogger.Errorf("creat CreateProjectDocumentTable error", resp.Msg)
		return resp, errors.New("creat CreateProjectDocumentTable error:" + resp.Msg)
	}

	// 业务处理
	// fmt.Println(larkcore.Prettify(resp))
	return resp, nil

}

// 创建评审详情数据表
func CreateProjectDocumentReviewdetailTable(tableName, BiAppToken string) (*larkbitable.CreateAppTableResp, error) {
	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)
	// 创建请求对象
	req := larkbitable.NewCreateAppTableReqBuilder().
		AppToken(BiAppToken).
		Body(larkbitable.NewCreateAppTableReqBodyBuilder().
			Table(larkbitable.NewReqTableBuilder().
				Name(tableName).
				Fields([]*larkbitable.AppTableCreateHeader{
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`评审对象`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`review_id`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`document_id`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`项目`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`需求`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`工作包`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`负责人`).
						Type(11).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`文档类型`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`划词引用内容`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`评委`).
						Type(11).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`评论内容`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`评论创建时间`).
						Type(5).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`状态`).Property(larkbitable.NewAppTableFieldPropertyBuilder().
						Options([]*larkbitable.AppTableFieldPropertyOption{
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`未解决`).
								Build(),
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`已解决`).
								Build(),
						}).
						Build()).
						Type(3).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`解决人`).
						Type(11).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`评论性质`).Property(larkbitable.NewAppTableFieldPropertyBuilder().
						Options([]*larkbitable.AppTableFieldPropertyOption{
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`未确认`).
								Build(),
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`缺陷`).
								Build(),
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`提问`).
								Build(),
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`建议`).
								Build(),
						}).
						Build()).
						Type(3).
						Build(),
				}).
				Build()).
			Build()).
		Build()

	// 发起请求
	var resp *larkbitable.CreateAppTableResp
	resp, err := client.Bitable.AppTable.Create(context.Background(), req)

	// 处理错误
	if err != nil {
		// fmt.Println(err)
		logging.ErrorLogger.Errorf("creat CreateDocumentReviewdetailTable error", err.Error())
		return resp, err
	}

	// 服务端错误处理
	if !resp.Success() {
		// fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		logging.ErrorLogger.Errorf("creat CreateDocumentReviewdetail error", resp.Msg)
		return resp, errors.New("creat CreateDocumentReviewdetailTable error:" + resp.Msg)
	}

	// 业务处理
	// fmt.Println(larkcore.Prettify(resp))
	return resp, nil

}

func BatchCreate(tableID, BiAppToken string, data []map[string]interface{}) (*larkbitable.BatchCreateAppTableRecordResp, error) {
	// 创建记录
	records := []*larkbitable.AppTableRecord{}
	for _, item := range data {
		_object := larkbitable.NewAppTableRecordBuilder().
			Fields(item).
			Build()
		records = append(records, _object)
	}

	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)
	// 创建请求对象
	req := larkbitable.NewBatchCreateAppTableRecordReqBuilder().
		AppToken(BiAppToken).
		TableId(tableID).
		Body(larkbitable.NewBatchCreateAppTableRecordReqBodyBuilder().
			Records(records).
			Build()).
		Build()
	// 发起请求
	resp, err := client.Bitable.AppTableRecord.BatchCreate(context.Background(), req)

	// 处理错误
	if err != nil {
		// fmt.Println(resp, err)
		return resp, err
	}

	// 服务端错误处理
	if !resp.Success() {
		// fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		return resp, errors.New(resp.Msg)
	}
	// 业务处理
	// fmt.Println(larkcore.Prettify(resp))
	return resp, nil
}

func BatchUpdate(tableID, BiAppToken string, data []map[string]interface{}) (*larkbitable.BatchUpdateAppTableRecordResp, error) {
	// 更新记录
	records := []*larkbitable.AppTableRecord{}
	for _, item := range data {
		//删除key值record_id的字段
		recordID := ""
		if _, exists := item["record_id"]; exists {
			recordID = item["record_id"].(string)
			delete(item, "record_id")
		}
		_object := larkbitable.NewAppTableRecordBuilder().
			Fields(item).
			RecordId(recordID).
			Build()
		records = append(records, _object)
	}

	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)
	// 创建请求对象
	req := larkbitable.NewBatchUpdateAppTableRecordReqBuilder().
		AppToken(BiAppToken).
		TableId(tableID).
		Body(larkbitable.NewBatchUpdateAppTableRecordReqBodyBuilder().
			Records(records).
			Build()).
		Build()
	// 发起请求
	resp, err := client.Bitable.AppTableRecord.BatchUpdate(context.Background(), req)

	// 处理错误
	if err != nil {
		fmt.Println(resp, err)
		return resp, err
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Println(resp.Code, resp.Msg, resp.RequestId(), records)
		return resp, errors.New(resp.Msg)
	}
	// 业务处理
	// fmt.Println(larkcore.Prettify(resp))
	return resp, nil
}

// 查询表数据
func GetTableRecord(tableID, BiAppToken, pageToken string, pageSize int, FieldNames []string) (*larkbitable.SearchAppTableRecordResp, error) {
	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)
	// 创建请求对象
	reqBuilder := larkbitable.NewSearchAppTableRecordReqBuilder().
		AppToken(BiAppToken).
		TableId(tableID)
	if pageSize > 0 {
		reqBuilder.PageSize(pageSize)
	}
	if len(pageToken) > 0 {
		reqBuilder.PageToken(pageToken)
	}
	if len(FieldNames) > 0 {
		reqBuilder.Body(larkbitable.NewSearchAppTableRecordReqBodyBuilder().
			FieldNames(FieldNames).
			Build())
	}
	req := reqBuilder.Build()

	// 发起请求
	resp, err := client.Bitable.AppTableRecord.Search(context.Background(), req)

	// 处理错误
	if err != nil {
		// fmt.Println(err)
		return resp, err
	}

	// 服务端错误处理
	if !resp.Success() {
		// fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		return resp, errors.New(resp.Msg)
	}

	// 业务处理
	// fmt.Println(larkcore.Prettify(resp))
	return resp, nil
}

// 更新表记录ID数据
func UpdateTableRecordByID(tableID, BiAppToken, recordID string, data map[string]interface{}) (*larkbitable.UpdateAppTableRecordResp, error) {
	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)
	// 创建请求对象
	req := larkbitable.NewUpdateAppTableRecordReqBuilder().
		AppToken(BiAppToken).
		TableId(tableID).
		RecordId(recordID).
		AppTableRecord(larkbitable.NewAppTableRecordBuilder().
			Fields(data).
			Build()).
		Build()

	// 发起请求
	resp, err := client.Bitable.AppTableRecord.Update(context.Background(), req)

	// 处理错误
	if err != nil {
		fmt.Println(err)
		return resp, err
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		return resp, errors.New(resp.Msg)
	}

	// 业务处理
	// fmt.Println(larkcore.Prettify(resp))
	return resp, nil
}

// 批量删除表数据
func BatchDelete(tableID, BiAppToken string, data []string) error {
	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)
	// 创建请求对象
	req := larkbitable.NewBatchDeleteAppTableRecordReqBuilder().
		AppToken(BiAppToken).
		TableId(tableID).
		Body(larkbitable.NewBatchDeleteAppTableRecordReqBodyBuilder().
			Records(data).
			Build()).
		Build()

	// 发起请求
	resp, err := client.Bitable.AppTableRecord.BatchDelete(context.Background(), req)

	// 处理错误
	if err != nil {
		return err
	}

	// 服务端错误处理
	if !resp.Success() {
		return errors.New(resp.Msg)
	}

	// 业务处理
	return nil
}

// 根据会议号获取会议编号
func GetMeetingIDByMeetingNo(meetingNo string) (string, error) {
	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)

	// 创建请求对象
	//90天前时间戳
	startTime := fmt.Sprint((time.Now().AddDate(0, 0, -90).Unix()))
	endTime := fmt.Sprint(time.Now().Unix())
	req := larkvc.NewListByNoMeetingReqBuilder().
		MeetingNo(meetingNo).
		StartTime(startTime).
		EndTime(endTime).
		PageSize(20).
		Build()

	// 发起请求
	resp, err := client.Vc.Meeting.ListByNo(context.Background(), req)

	// 处理错误
	if err != nil {
		fmt.Println(err)
		return "", err
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		return "", errors.New(resp.Msg)
	}
	// 业务处理
	fmt.Println(larkcore.Prettify(resp))
	if len(resp.Data.MeetingBriefs) == 0 {
		return "", errors.New("未找到会议")
	}
	return *resp.Data.MeetingBriefs[0].Id, nil
}

// 根据会议ID获取会议记录
func GetMeetingRecordByMeetingID(meetingNo string) (*larkvc.GetMeetingResp, error) {
	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)
	// 创建请求对象
	req := larkvc.NewGetMeetingReqBuilder().
		MeetingId(meetingNo).
		Build()

	// 发起请求
	resp, err := client.Vc.Meeting.Get(context.Background(), req)

	// 处理错误
	if err != nil {
		fmt.Println(err)
		return resp, err
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		return resp, err
	}
	// 业务处理
	fmt.Println(larkcore.Prettify(resp))
	return resp, nil
}

func GetDocAllComments(fileToken, fileType string, pageSize int) (*larkdrive.ListFileCommentResp, error) {
	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)

	// 创建请求对象 获取文档全部评论
	req := larkdrive.NewListFileCommentReqBuilder().
		FileToken(fileToken).
		FileType(fileType).
		PageSize(pageSize).
		Build()
	// 发起请求
	resp, err := client.Drive.FileComment.List(context.Background(), req)

	// 处理错误
	if err != nil {
		// fmt.Println(err)
		return resp, err
	}

	// 服务端错误处理
	if !resp.Success() {
		// fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		return resp, errors.New(resp.Msg)
	}
	// 业务处理
	// fmt.Println(larkcore.Prettify(resp))
	return resp, nil
}

func PatchFileComment(fileToken, commentId, fileType string, isSolved bool) error {
	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)
	// 创建请求对象
	req := larkdrive.NewPatchFileCommentReqBuilder().
		FileToken(fileToken).
		CommentId(commentId).
		FileType(fileType).
		Body(larkdrive.NewPatchFileCommentReqBodyBuilder().
			IsSolved(isSolved).
			Build()).
		Build()

	// 发起请求
	resp, err := client.Drive.FileComment.Patch(context.Background(), req)

	// 处理错误
	if err != nil {
		fmt.Println(err)
		return err
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		return err
	}

	// 业务处理
	fmt.Println(larkcore.Prettify(resp))
	return nil
}

// 复制飞书文件
func CopyFeishuFile(srcFileToken, targetFileName, targetFileType, targetFolderToken string) (*larkdrive.CopyFileResp, error) {
	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)
	// 创建请求对象
	req := larkdrive.NewCopyFileReqBuilder().
		FileToken(srcFileToken).
		Body(larkdrive.NewCopyFileReqBodyBuilder().
			Name(targetFileName).
			Type(targetFileType).
			FolderToken(targetFolderToken).
			Build()).
		Build()

	// 发起请求
	resp, err := client.Drive.File.Copy(context.Background(), req)

	// 处理错误
	if err != nil {
		fmt.Println(err)
		return resp, err
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		return resp, errors.New(resp.Msg)
	}

	// 业务处理
	fmt.Println(larkcore.Prettify(resp))
	return resp, nil
}

// 飞书文档增加协作者权限
func GrantPermission(fileToken, fileType, userOpenID, permission string) (*larkdrive.CreatePermissionMemberResp, error) {
	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)
	// 创建请求对象
	req := larkdrive.NewCreatePermissionMemberReqBuilder().
		Token(fileToken).
		Type(fileType).
		NeedNotification(false).
		BaseMember(larkdrive.NewBaseMemberBuilder().
			MemberType(`openid`).
			MemberId(userOpenID).
			Perm(permission).
			Type(`user`).
			Build()).
		Build()

	// 发起请求
	resp, err := client.Drive.PermissionMember.Create(context.Background(), req)
	// 处理错误
	if err != nil {
		fmt.Println(err)
		return resp, err
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		return resp, errors.New(resp.Msg)
	}

	// 业务处理
	fmt.Println(larkcore.Prettify(resp))
	return resp, nil

}

// 获取飞书文档协作者列表
func GetPermissionList(fileToken, fileType string) (*larkdrive.ListPermissionMemberResp, error) {
	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)
	// 创建请求对象
	req := larkdrive.NewListPermissionMemberReqBuilder().
		Token(fileToken).
		Type(fileType).
		Build()

	// 发起请求
	resp, err := client.Drive.PermissionMember.List(context.Background(), req)

	// 处理错误
	if err != nil {
		fmt.Println(err)
		return resp, err
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		return resp, err
	}
	// 业务处理
	fmt.Println(larkcore.Prettify(resp))
	return resp, nil
}

// 飞书文档更新协作者权限
func UpdatePermission(fileToken, fileType, userOpenID, permission string) (*larkdrive.UpdatePermissionMemberResp, error) {
	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)
	// 创建请求对象
	req := larkdrive.NewUpdatePermissionMemberReqBuilder().
		Token(fileToken).
		MemberId(userOpenID).
		Type(fileType).
		BaseMember(larkdrive.NewBaseMemberBuilder().
			MemberType(`openid`).
			Perm(permission).
			Type(`user`).
			Build()).
		Build()

	// 发起请求
	resp, err := client.Drive.PermissionMember.Update(context.Background(), req)

	// 处理错误
	if err != nil {
		// fmt.Println(err)
		logging.ErrorLogger.Errorf("UpdatePermission  error msg: %s", err)
		return resp, err
	}

	// 服务端错误处理
	if !resp.Success() {
		logging.ErrorLogger.Errorf("UpdatePermission  error msg: %s", resp.Msg)
		return resp, errors.New(resp.Msg)
	}

	// 业务处理
	// fmt.Println(larkcore.Prettify(resp))
	return resp, nil

}
func RevokeDocumentPermissions(fileToken, fileType string) error {
	fileList, err := GetPermissionList(fileToken, fileType)
	if err != nil {
		return err
	}
	if len(fileList.Data.Items) > 0 {
		for _, v := range fileList.Data.Items {
			if *v.MemberType == "openid" && *v.MemberId != "ou_ff57ac72b34fc3e982ef2ba1bb59c0b3" {
				_, err := UpdatePermission(fileToken, fileType, *v.MemberId, "view")
				if err != nil {
					return err
				}
			}
		}
	}
	return nil
}

// 批量获取评论
func BatchQueryComments(fileToken, fileType string, CommentIds []string) (*larkdrive.BatchQueryFileCommentResp, error) {
	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)
	// 创建请求对象
	req := larkdrive.NewBatchQueryFileCommentReqBuilder().
		FileToken(fileToken).
		FileType(fileType).
		Body(larkdrive.NewBatchQueryFileCommentReqBodyBuilder().
			CommentIds(CommentIds).
			Build()).
		Build()

	// 发起请求
	resp, err := client.Drive.FileComment.BatchQuery(context.Background(), req)

	// 处理错误
	if err != nil {
		// fmt.Println(err)
		return resp, err
	}
	// 服务端错误处理
	if !resp.Success() {
		fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		return resp, err
	}
	// 业务处理
	// fmt.Println(larkcore.Prettify(resp))
	return resp, nil
}

// feishu发送普通消息
func SendMessage(userOpenID, messageStr string) (*larkim.CreateMessageResp, error) {
	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)
	// 创建请求对象
	contentMap := map[string]string{
		"text": messageStr,
	}
	contentByte, err := json.Marshal(contentMap)
	if err != nil {
		return nil, err
	}
	content := string(contentByte)

	req := larkim.NewCreateMessageReqBuilder().
		ReceiveIdType(`open_id`).
		Body(larkim.NewCreateMessageReqBodyBuilder().
			ReceiveId(userOpenID).
			MsgType(`text`).
			Content(content).
			Uuid(libs.GetUUID()).
			Build()).
		Build()

	// 发起请求
	resp, err := client.Im.Message.Create(context.Background(), req)

	// 处理错误
	if err != nil {
		fmt.Println(err)
		return resp, err
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		return resp, errors.New(resp.Msg)
	}

	// // 业务处理
	// fmt.Println(larkcore.Prettify(resp))
	return resp, nil
}

type Comment struct {
	Quote       string `json:"quote"`
	Content     string `json:"content"`
	IsSolved    string `json:"is_solved"`
	CommentType string `json:"comment_type"`
}

type TemplateData struct {
	DocumentCategoryTitle string    `json:"document_category_title"`
	DocumentOwnerName     string    `json:"document_owner_name"`
	DocumentName          string    `json:"document_name"`
	DocumentURL           string    `json:"document_url"`
	ProjectName           string    `json:"project_name"`
	PMSDocID              uint      `json:"pms_doc_id"`
	JudgeEndURL           string    `json:"judge_end_url"`
	CommentList           []Comment `json:"commentList"`
}

type Data struct {
	TemplateID          string       `json:"template_id"`
	TemplateVersionName string       `json:"template_version_name"`
	TemplateVariable    TemplateData `json:"template_variable"`
}

type Template struct {
	Type string `json:"type"`
	Data Data   `json:"data"`
}

// feishu发送卡片消息
func SendCardMessage(userOpenID, messageStr string) (*larkim.CreateMessageResp, error) {
	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)
	// 创建请求对象
	req := larkim.NewCreateMessageReqBuilder().
		ReceiveIdType(`open_id`).
		Body(larkim.NewCreateMessageReqBodyBuilder().
			ReceiveId(userOpenID).
			MsgType(`interactive`).
			Content(messageStr).
			Uuid(libs.GetUUID()).
			Build()).
		Build()

	// 发起请求
	resp, err := client.Im.Message.Create(context.Background(), req)

	// 处理错误
	if err != nil {
		fmt.Println(err)
		return resp, err
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		return resp, errors.New(resp.Msg)
	}

	// 业务处理
	fmt.Println(larkcore.Prettify(resp))
	return resp, nil
}

func ExtractTextFromContent(content *larkdrive.ReplyContent) (string, string) {
	var texts []string
	var userID string
	for i, element := range content.Elements {
		if element.Type != nil && *element.Type == "text_run" && element.TextRun != nil {
			texts = append(texts, *element.TextRun.Text)
		}
		if element.Type != nil && *element.Type == "person" && element.Person != nil {
			//获取用户信息
			userName, _ := getUserName(*element.Person.UserId)
			texts = append(texts, userName)
			time.Sleep(150 * time.Millisecond)
		}
		if element.Person != nil {
			if i > 0 && content.Elements[i-1].Type != nil && *content.Elements[i-1].Type == "text_run" &&
				(strings.Contains(*content.Elements[i-1].TextRun.Text, "【缺陷】【") ||
					strings.Contains(*content.Elements[i-1].TextRun.Text, "【提问】【") ||
					strings.Contains(*content.Elements[i-1].TextRun.Text, "【建议】【")) &&
				i < len(content.Elements)-1 && content.Elements[i+1].Type != nil && *content.Elements[i+1].Type == "text_run" &&
				strings.Contains(*content.Elements[i+1].TextRun.Text, "】") {
				userID = *element.Person.UserId
			}
		}
	}
	return strings.Join(texts, ""), userID
}

func FormatReplies(replyList *larkdrive.ReplyList, docUserID string) (string, string, string) {
	var results []string
	var lastDocUserReplyContent string
	var PersonID string
	// 遍历回复列表以找到最后一个评委的回复
	for _, reply := range replyList.Replies {
		if reply.UserId != nil && reply.Content != nil && *reply.UserId == docUserID {
			lastDocUserReplyContent, _ = ExtractTextFromContent(reply.Content)
		}
	}
	// 对最后一个作者的回复进行分类
	category := ClassifyReply(lastDocUserReplyContent)
	for _, reply := range replyList.Replies {
		if reply.UserId != nil && reply.Content != nil {
			text, userID := ExtractTextFromContent(reply.Content)
			if userID != "" {
				PersonID = userID
			}
			//获取用户信息
			userName, _ := getUserName(*reply.UserId)
			results = append(results, fmt.Sprintf("%s: %s", userName, text))
			time.Sleep(150 * time.Millisecond)
		}
	}
	return strings.Join(results, "\n"), category, PersonID
}

func ClassifyReply(content string) string {
	keywordsToCategory := []string{"缺陷", "提问", "建议"}
	for _, category := range keywordsToCategory {
		if strings.Contains(strings.ToLower(content), category) {
			return category
		}
	}
	return "缺陷"
}

// 获取评委信息
func ExtractJudgeIDs(judgesInterface interface{}) ([]string, error) {
	// 用于保存id的字符串切片
	var ids []string
	judges, ok := judgesInterface.([]interface{})
	if !ok {
		return ids, errors.New("类型断言失败: judgesInterface不是[]interface{}类型")
	}
	for _, judge := range judges {
		judgeMap, ok := judge.(map[string]interface{})
		if !ok {
			fmt.Println("Element is not a map")
			continue
		}
		id, ok := judgeMap["id"].(string)
		if !ok {
			fmt.Println("id is not a string")
			continue
		}
		// 将id添加到切片中
		ids = append(ids, id)
	}
	return ids, nil
}

// 从给定的链接中提取会议号
func ExtractMeetingID(url string) (string, error) {
	// 定义正则表达式，假设会议号总是由数字组成，并且紧跟在最后一个斜杠"/"后面
	// 这里的正则表达式是：`/(\d+)$`，意味着匹配链接末尾的一串数字
	re := regexp.MustCompile(`/(\d+)$`)

	// 使用正则表达式在链接中查找匹配项
	matches := re.FindStringSubmatch(url)

	// 检查是否找到了匹配项
	if len(matches) > 1 {
		// 返回第一个括号内匹配的文本（即会议号）
		return matches[1], nil
	}

	// 如果没有找到匹配项，返回错误
	return "", fmt.Errorf("no meeting ID found in URL")
}

// 从文本字段中提取内容
func ExtractRecordTextFromContent(tableRecord *larkbitable.AppTableRecord, fieldName string) (map[string]interface{}, error) {
	var fieldMap map[string]interface{}
	fieldSlice, ok := tableRecord.Fields[fieldName].([]interface{})
	if !ok {
		err := fmt.Errorf("类型断言失败: tableRecord.Fields['%s']不是[]interface{}类型", fieldName)
		log.Println(err)
		return fieldMap, err
	}

	// Check if the slice is empty
	if len(fieldSlice) == 0 {
		err := fmt.Errorf("field '%s' is empty", fieldName)
		log.Println(err)
		return fieldMap, err
	}

	// Type assertion to map[string]interface{}
	fieldMap, ok = fieldSlice[0].(map[string]interface{})
	if !ok {
		err := fmt.Errorf("类型断言失败: %s[0]不是map[string]interface{}类型", fieldName)
		log.Println(err)
		return fieldMap, err
	}
	return fieldMap, nil
}

func parseAndConvertToTimestamp(dateStr string) (int64, error) {
	// 解析字符串为 time.Time 类型
	t, err := time.Parse(time.RFC3339, dateStr)
	if err != nil {
		return 0, err
	}

	// 将 time.Time 类型转换为 Unix 时间戳
	return t.Unix(), nil
}

func parseAndConvertToTimestampV2(dateStr string) (int64, error) {
	// 解析字符串为 time.Time 类型
	t, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return 0, err
	}

	// 将 time.Time 类型转换为 Unix 时间戳
	return t.Unix(), nil
}

func parseAndConvertToTimestampV3(dateStr string) (int64, error) {
	// 解析字符串为 time.Time 类型
	t, err := time.Parse("2006-01-02T15:04:05+07:00", dateStr)
	if err != nil {
		return 0, err
	}

	// 将 time.Time 类型转换为 Unix 时间戳
	return t.Unix(), nil
}

// contains 检查切片中是否包含指定的元素
func contains(slice []string, element string) bool {
	for _, item := range slice {
		if item == element {
			return true
		}
	}
	return false
}

func FormatWithComma(num float64, precision int) string {
	// 将浮点数格式化为指定精度的小数点字符串
	format := fmt.Sprintf("%%.%df", precision)
	numStr := fmt.Sprintf(format, num)

	// 分割整数和小数部分
	parts := strings.Split(numStr, ".")
	intPart := parts[0]
	decPart := ""
	if len(parts) > 1 {
		decPart = parts[1]
	}

	// 处理整数部分，添加千位分隔符
	var result strings.Builder
	intPartLen := len(intPart)
	for i, digit := range intPart {
		if i > 0 && (intPartLen-i)%3 == 0 {
			result.WriteString(",")
		}
		result.WriteRune(digit)
	}

	// 如果有小数部分，则加上小数点和小数部分
	if decPart != "" {
		result.WriteString(".")
		result.WriteString(decPart)
	}

	return result.String()
}

// getLastWeekRange 计算上一个自然周的起始和结束时间
func getLastWeekRange() (time.Time, time.Time) {
	now := time.Now()

	// 当前周几（0 = 周日, 1 = 周一, ..., 6 = 周六）
	weekday := int(now.Weekday())
	if weekday == 0 {
		weekday = 7 // 将周日调整为 7，方便计算
	}

	// 计算上周一的日期
	startOfLastWeek := now.AddDate(0, 0, -weekday-6)

	// 计算上周日的日期
	endOfLastWeek := now.AddDate(0, 0, -weekday)

	// 去掉时间部分，仅保留日期
	startOfLastWeek = time.Date(startOfLastWeek.Year(), startOfLastWeek.Month(), startOfLastWeek.Day(), 0, 0, 0, 0, startOfLastWeek.Location())
	endOfLastWeek = time.Date(endOfLastWeek.Year(), endOfLastWeek.Month(), endOfLastWeek.Day(), 23, 59, 59, 0, endOfLastWeek.Location())

	return startOfLastWeek, endOfLastWeek
}
