package datasync

/*
CREATE TABLE `csbu_pms_project_scale` (

	`id` VARCHAR(200) NOT NULL DEFAULT '' COLLATE 'utf8_general_ci',
	`pmsSrcProjectProductName` VARCHAR(200) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`pmsSrcProjectId` INT(11) NULL DEFAULT NULL,
	`pmsDestProjectId` INT(11) NULL DEFAULT NULL,
	`pmsSrcProjectName` VARCHAR(200) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`pmsDestProjectProductName` VARCHAR(200) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`pmsDestProjectName` VARCHAR(200) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`scale` FLOAT NULL DEFAULT NULL,
	INDEX `idx_id` (`id`) USING BTREE

)
COLLATE='utf8_general_ci'
ENGINE=InnoDB
;
*/
type CsbuPmsProjectScale struct {
	ID                        string  `gorm:"column:id" json:"id"`
	PmsSrcProjectProductName  string  `gorm:"column:pmsSrcProjectProductName" json:"pmsSrcProjectProductName"`
	PmsSrcProjectId           int     `gorm:"column:pmsSrcProjectId" json:"pmsSrcProjectId"`
	PmsSrcProjectName         string  `gorm:"column:pmsSrcProjectName" json:"pmsSrcProjectName"`
	PmsDestProjectProductName string  `gorm:"column:pmsDestProjectProductName" json:"pmsDestProjectProductName"`
	PmsDestProjectId          int     `gorm:"column:pmsDestProjectId" json:"pmsDestProjectId"`
	PmsDestProjectName        string  `gorm:"column:pmsDestProjectName" json:"pmsDestProjectName"`
	Scale                     float64 `gorm:"column:scale" json:"scale"`
}

func AllScales() ([]*CsbuPmsProjectScale, error) {
	items := []*CsbuPmsProjectScale{}
	err := DataSyncDB.db.Model(&CsbuPmsProjectScale{}).Where(`pmsDestProjectProductName != "网络安全产品事业部（CSBU）"`).Order("pmsDestProjectId DESC").Find(&items).Error
	return items, err
}
