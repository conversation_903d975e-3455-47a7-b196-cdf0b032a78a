package bugsync

import (
	"irisAdminApi/application/models"
	"time"
)

/*
"bugId":882211,
"bugBelong":"龙黎江",
"bugOwner":"龙黎江",
"bugTestCharger":"龙黎江",
"bugOs":"11.9PR82",
"bugProduct":"RG-EG5210-JP V1.00",
"workpacketName":"DHCPV6 client支持64,94选项",
"summary":"需要将动态获取配置的接口ID一起同步给tunnel",
"mainbugid":null,
"samebugid":null,
"thedate":"2022-10-12 18:47:33",
"lastupdatedate":"2022-10-19 09:41:37",
"bugState":"CHECKED",
"disabled":null
*/
type Bug struct {
	// models.ModelBase
	BugID              int       `gorm:"primarykey; autoIncrement:false" json:"bug_id"`
	BugSummary         string    `gorm:"not null; type:varchar(300)" json:"bug_summary"`
	BugOwner           string    `gorm:"not null; type:varchar(60)" json:"bug_owner"`
	BugOwnerGroup      string    `gorm:"not null; type:varchar(60)" json:"bug_owner_group"`
	BugSubmitter       string    `gorm:"not null; type:varchar(60)" json:"bug_submitter"`
	BugSubmitterGroup  string    `gorm:"not null; type:varchar(60)" json:"bug_submitter_group" `
	BugBelong          string    `gorm:"not null; type:varchar(60)" json:"bug_belong"`
	BugTestCharger     string    `gorm:"not null; type:varchar(60)" json:"bug_test_charger"`
	BugOS              string    `gorm:"not null; type:varchar(60)" json:"bug_os"`
	BugProduct         string    `gorm:"not null; type:varchar(60)" json:"bug_product"`
	BugWorkpacketName  string    `gorm:"not null; type:varchar(60)" json:"bug_workpacket_name"`
	MainBugID          int       `gorm:"not null; type:varchar(60)" json:"main_bug_id"`
	SameBugID          int       `gorm:"not null; type:varchar(60)" json:"same_bug_id"`
	BugCreatedAt       time.Time `gorm:"not null; type:varchar(60)" json:"bug_created_at"`
	BugUpdatedAt       time.Time `gorm:"not null; type:varchar(60)" json:"bug_updated_at"`
	BugState           string    `gorm:"not null; type:varchar(60)" json:"bug_state"`
	BugDisabled        bool      `gorm:"not null" json:"bug_disabled"`
	BugPriority        string    `gorm:"not null; type:varchar(60)" json:"bug_priority"`    //严重性
	BugSeverity        string    `gorm:"not null; type:varchar(60)" json:"bug_severity"`    //优先级
	BugRepro           int       `gorm:"not null" json:"bug_repro" `                        //可重复性 1必现，2有时重现 3未尝试重现 4尝试但未重现
	BugResolvedAt      time.Time `gorm:"not null; type:varchar(60)" json:"bug_resolved_at"` //解决时间
	BugSource          string    `gorm:"not null; type:varchar(60)" json:"bug_source"`      //bug来源
	MirroredProjects   string    `gorm:"type:varchar(600)" json:"mirrored_projects"`
	ResolvedProjects   string    `gorm:"type:varchar(600)" json:"resolved_projects"`
	NeedMirrorProjects string    `gorm:"type:varchar(600)" json:"need_mirror_projects"`
	MirrorStatus       uint      `gorm:"type:varchar(60)" json:"mirror_status"`  //镜像状态： 0: 未同步  1:已同步  2:部分同步
	ResolveStatus      uint      `gorm:"type:varchar(60)" json:"resolve_status"` //所有同步BUG是否已经全部解决    0: 未解决   1: 已解决
	BugCbdAt           string    `json:"bug_cbd_at"`
	BugCbtAt           string    `json:"bug_cbt_at"`
	OverTime           int       `json:"over_time"`
}

type BugProject struct {
	/*
		"projectId": 99999,                        //rgos项目ID
		"projectName": "测试项目",                //项目名称
		"pmName": null,                            //pm名称
		"projectStatus": "完成(正式发布)",        //项目状态
		"projectType": "补丁项目",                //项目类别
		"baseProject": "测试项目",                //基线项目
		"applyTime": "2014-04-01 17:46:51",        //申请时间
		"lastUpdateTime": "2014-04-24 12:30:07"    //最后更新时间
	*/
	ProjectID     int    `gorm:"primarykey; autoIncrement:false" json:"project_id"`
	ProjectName   string `gorm:"not null; type:varchar(60)" json:"project_name"`
	PMName        string `gorm:"not null; type:varchar(60)" json:"pm_name"`
	ProjectStatus string `gorm:"not null; type:varchar(60)" json:"project_status"`
	ProjectType   string `gorm:"not null; type:varchar(60)" json:"project_type"`
	BaseProject   string `gorm:"not null; type:varchar(60)" json:"base_project"`
	CreatedAt     string `gorm:"not null; type:varchar(60)" json:"created_at"`
	UpdatedAt     string `gorm:"not null; type:varchar(60)" json:"updated_at"`
}

type BugSyncRecord struct {
	models.ModelBase
	Url           string `gorm:"not null; type:varchar(300)" json:"url"`
	Body          string `gorm:"not null; type:varchar(300)" json:"body"`
	Method        string `gorm:"not null; type:varchar(60)" json:"method"`
	MinModifyDate string `gorm:"not null; type:varchar(60)" json:"min_modify_date"`
	MaxModifyDate string `gorm:"not null; type:varchar(60)" json:"max_modify_date"`
	State         string `gorm:"not null; type:varchar(60)" json:"state"`
	Message       string `json:"message"`
}

type SyncProject struct {
	models.ModelBase
	KeyWord string `gorm:"not null; type:varchar(30)" json:"key_word"`
}

type BugMirrorRecord struct {
	models.ModelBase
	MainBugID   int `gorm:"not null" json:"main_bug_id"`
	MirrorBugID int `gorm:"not null" json:"mirror_bug_id"`
}

type BugMirrorApproval struct {
	models.ModelBase
	BugID   int    `gorm:"not null" json:"bug_id"`
	BugOs   string `gorm:"not null" json:"bug_os"`
	Comment string `gorm:"not null; type:varchar(200)" json:"comment"`
	UserID  uint   `gorm:"not null" json:"user_id"`
}
