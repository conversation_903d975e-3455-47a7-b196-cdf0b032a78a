package buildfarm

import "irisAdminApi/application/models"

type Server struct {
	models.ModelBase
	Name       string `gorm:"not null; type:varchar(60)" json:"name"`
	Desc       string `gorm:"not null; type:varchar(60)" json:"desc"`
	Host       string `gorm:"not null; type:varchar(60)" json:"host"`
	Port       uint   `gorm:"not null" json:"port"`
	Username   string `gorm:"not null; type:varchar(60)" json:"username"`
	Key        bool   `gorm:"not null;default:true" json:"key"`
	Enable     bool   `gorm:"not null;default:true" json:"enable"`
	Parallel   uint   `gorm:"not null;default:2" json:"parallel"`
	Type       string `gorm:"not null; type:varchar(60)" json:"type"` //1: 普通编译 2: 每日编译
	Prometheus uint   `gorm:"not null;default:0" json:"prometheus"`
}
