package buildfarm

import (
	"bufio"
	"fmt"
	"net"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"irisAdminApi/application/controllers/ip"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/buildfarm/dbuildfarmaudit"
	"irisAdminApi/service/dao/buildfarm/dbuildfarmauditdetail"
	"irisAdminApi/service/dao/buildfarm/dbuildfarmaudithistory"
	"irisAdminApi/service/dao/buildfarm/dgitjob"
	"irisAdminApi/service/dao/buildfarm/dmakejob"

	"github.com/kataras/iris/v12"
)

func GetTaskAudits(ctx iris.Context) {
	userID, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get task audit list err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	_level := ctx.FormValue("level")
	_status := ctx.FormValue("status")
	_mine := ctx.FormValue("mine")
	var mine uint = 0

	if _mine == "1" {
		mine = userID
	}

	status, err := libs.StringToUintArray(_status, ",")
	if err != nil {
		logging.ErrorLogger.Errorf("status ato i err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	level, err := libs.StringToUintArray(_level, ",")
	if err != nil {
		logging.ErrorLogger.Errorf("status ato i err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	items, err := dbuildfarmaudit.FindTaskAudit(mine, status, level, page, pageSize, sort, orderBy)
	if err != nil {
		logging.ErrorLogger.Errorf("get task audit list err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, items, response.NoErr.Msg))
	return
}

type Ip struct {
	ID uint   `json:"id"`
	Ip string `json:"ip"`
}

func GetRemoteAddr(ctx iris.Context) string {
	addr := ctx.GetHeader("X-Real-Ip")
	if len(addr) == 0 {
		addr := strings.TrimSpace(ctx.Request().RemoteAddr)
		if addr != "" {
			// if addr has port use the net.SplitHostPort otherwise(error occurs) take as it is
			if ip, _, err := net.SplitHostPort(addr); err == nil {
				return ip
			}
		}
	} else {
		if ip, _, err := net.SplitHostPort(addr); err == nil {
			return ip
		}
	}
	return addr
}

func GetTaskAuditDetails(ctx iris.Context) {
	// var ip = Ip{}
	// addr := GetRemoteAddr(ctx)
	// if addr == "" {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	// fmt.Println(addr)

	// err := easygorm.GetEasyGormDb().Model(&fileout.AllowAuditIp{}).Where("ip = ? or ip = ?", strings.Join(strings.Split(addr, ".")[:3], ".")+".0", addr).Find(&ip).Error
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }

	// if ip.ID == 0 {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "非云办公环境，禁止访问此页面。"))
	// 	return
	// }
	permit, err := ip.CheckIP(ctx)
	if !permit {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	task_id := ctx.Params().GetString("task_id")
	level := ctx.FormValue("level")

	items, err := dbuildfarmauditdetail.FindTaskAudit(task_id)
	if err != nil {
		logging.ErrorLogger.Errorf("get task audit list err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	var list []*dbuildfarmauditdetail.ListResponse
	for _, item := range items {
		if len(item.Diff) > 0 {
			filterDiffArr := []string{}
			diffContent, err := os.ReadFile(item.Diff)
			if err != nil {
				logging.ErrorLogger.Errorf("get task audit detail err ", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
				return
			}

			scanner := bufio.NewScanner(strings.NewReader(string(diffContent)))
			var currentFile string
			diffs := map[string][]string{}
			var _diffs []string

			for scanner.Scan() {
				line := scanner.Text()
				// 检测文件名行
				// 由---行开始，防止截取时，多出---行，导致格式异常
				if strings.HasPrefix(line, "diff --git") {
					if currentFile != "" {
						diffs[currentFile] = []string{}
						diffs[currentFile] = append(diffs[currentFile], _diffs...)
						_diffs = []string{}
						currentFile = ""
					}
				} else if strings.HasPrefix(line, "+++ ") {
					parts := strings.Split(line, " ")
					if len(parts) >= 2 {
						currentFile = strings.TrimPrefix(parts[1], "b/")
					}
				}
				_diffs = append(_diffs, line)
			}
			// 补齐最后一个变更
			if currentFile != "" {
				diffs[currentFile] = []string{}
				diffs[currentFile] = append(diffs[currentFile], _diffs...)
			}

			extendDiffLevelExtMap := GetExtendDiffLevelExtMap()
			for newFile, diff := range diffs {
				_level := GetLevelByFilePath(extendDiffLevelExtMap, newFile)
				if libs.InArrayS(strings.Split(level, ","), fmt.Sprintf("%d", _level)) {
					filterDiffArr = append(filterDiffArr, strings.Join(diff, "\n"))
				}
			}

			if len(filterDiffArr) > 0 {
				item.Diff = strings.Join(filterDiffArr, "\n")
				list = append(list, item)
			}
		}
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetTaskAuditHistorys(ctx iris.Context) {
	task_id := ctx.Params().GetString("task_id")
	items, err := dbuildfarmaudithistory.FindHistoryByTaskID(task_id)
	if err != nil {
		logging.ErrorLogger.Errorf("get task audit history err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, items, response.NoErr.Msg))
	return
}

type UpdateAuditRequest struct {
	TaskID  string `json:"task_id"`
	Status  uint   `json:"status"`
	Comment string `json:"comment"`
}

func UpdateTaskAudit(ctx iris.Context) {
	userID, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get task audit list err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	request := UpdateAuditRequest{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("create project read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		logging.ErrorLogger.Errorf("create project read json err ", strings.Join(errs, ";"))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	audit := dbuildfarmaudit.BuildfarmAudit{}
	err = audit.FindEx("task_id", request.TaskID)
	if err != nil {
		logging.ErrorLogger.Errorf("get task audit list err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	if audit.ID == 0 {
		logging.ErrorLogger.Errorf("get task audit list err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if userID == audit.UserID {
		logging.ErrorLogger.Errorf("get task audit list err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "不能评审自己的个人仓编译变更。"))
		return
	}

	// details, err := dbuildfarmauditdetail.FindTaskAudit(audit.TaskID)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("get task audit detail list err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
	// 	return
	// }

	// compareDetails, err := dbuildfarmauditdetail.FindTaskAuditByJobID(audit.JobID, "")
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("get task audit compare detail list err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
	// 	return
	// }

	var gitjob dgitjob.GitJob
	var makejob dmakejob.MakeJob
	err = gitjob.FindEx("job_id", audit.JobID)
	if err != nil {
		logging.ErrorLogger.Errorf("find git job err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	err = makejob.FindEx("task_id", audit.TaskID)
	if err != nil {
		logging.ErrorLogger.Errorf("find make job err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	if request.Status == 1 {
		f, _ := os.OpenFile(filepath.Join(libs.Config.Buildfarm.Logpath, makejob.TaskID+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
		defer f.Close()

		client, err := SSHClient(gitjob.ServerID)
		if err != nil {
			logging.ErrorLogger.Errorf("ssh connect err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
			return
		}
		defer client.Close()
		ArchiveOutputWorker(true, f, client, &gitjob, &makejob, "")
	}

	err = dbuildfarmaudit.Update(audit.JobID, audit.TaskID, map[string]interface{}{"Status": request.Status, "LastAuditorID": userID, "Comment": request.Comment})
	// err = audit.Update(audit.ID, map[string]interface{}{"Status": request.Status, "LastAuditorID": userID, "Comment": request.Comment})
	if err != nil {
		logging.ErrorLogger.Errorf("update task audit err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func CheckMergeWorker() {
	res, err := dbuildfarmaudit.FindTaskAudit(uint(0), []uint{0}, []uint{1, 2, 3, 9}, 1, -1, "", "")
	if err != nil {
		logging.ErrorLogger.Errorf("get task audit list err ", err)
		return
	}
	items := res["items"].([]*dbuildfarmaudit.ListResponse)

	for _, item := range items {
		_items, err := dbuildfarmauditdetail.FindTaskAudit(item.TaskID)
		if err != nil {
			logging.ErrorLogger.Errorf("get task audit list err ", err)
			continue
		}

		ret := true

		for _, _item := range _items {
			ret, err = CheckMergeJob(_item)
			if err != nil {
				logging.ErrorLogger.Errorf("check merge err ", err)
				break
			}
			if !ret {
				break
			}
		}

		if err != nil || !ret {
			continue
		}
		// details, err := dbuildfarmauditdetail.FindTaskAudit(item.TaskID)
		// if err != nil {
		// 	logging.ErrorLogger.Errorf("get task audit detail list err ", err)
		// 	continue
		// }

		// compareDetails, err := dbuildfarmauditdetail.FindTaskAuditByJobID(item.JobID, "")
		// if err != nil {
		// 	logging.ErrorLogger.Errorf("get task audit compare detail list err ", err)
		// 	continue
		// }
		// err = dbuildfarmaudit.Update(item.JobID, item.TaskID, map[string]interface{}{"Status": 1, "LastAuditorID": 0, "Comment": "MR已合并, 自动评审"}, details, compareDetails)
		// if err != nil {
		// 	logging.ErrorLogger.Errorf("update task audit err ", err)
		// 	continue
		// }
		// err = item.Update(item.ID, map[string]interface{}{"Status": 1, "LastAuditorID": 0})
		// if err != nil {
		// 	logging.ErrorLogger.Errorf("update task audit err ", err)
		// 	continue
		// }

	}
}

func CheckMergeJob(item *dbuildfarmauditdetail.ListResponse) (bool, error) {
	workDir := filepath.Join("/tmp", item.TaskID)
	upstreamBranchSlice := strings.Split(item.UpstreamBranch, "/")
	defer os.RemoveAll(workDir)
	if item.Diff != "" {
		command := fmt.Sprintf("rm %s -rf && git clone -b %s %s %s", workDir, upstreamBranchSlice[len(upstreamBranchSlice)-1], item.UpstreamRepo, workDir)
		_, err := libs.ExecCommand(command)
		if err != nil {
			return false, err
		}
		command = fmt.Sprintf("cd %s && git log --pretty='%%H'", workDir)
		output, err := libs.ExecCommand(command)
		if err != nil {
			return false, err
		}

		for _, commitID := range strings.Split(item.CommitIDs, "\n") {
			if commitID == "" {
				continue
			}

			if !strings.Contains(output, commitID) {
				return false, nil
			}
		}
	}

	return true, nil
}
