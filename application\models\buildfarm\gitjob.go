package buildfarm

import "irisAdminApi/application/models"

type Git<PERSON>ob struct {
	models.ModelBase
	JobID      string `gorm:"not null; type:varchar(60)" json:"job_id"`
	UserID     uint   `gorm:"index;not null" json:"user_id"`
	ServerID   uint   `gorm:"index;not null" json:"server_id"`
	Project    string `gorm:"not null; type:varchar(100)" json:"project"`
	Repo       string `gorm:"not null; type:varchar(100)" json:"repo"`
	Branch     string `gorm:"not null; type:varchar(100)" json:"branch"`
	Dir        string `gorm:"not null; type:varchar(60)" json:"dir"`
	Customized uint   `gorm:"not null" json:"customized"`                //定制编译，0：生产仓库编译   1：定制编译   2: 个人仓库编译
	TaskType   uint   `gorm:"not null" json:"task_type"`                 //编译类型，1：产品编译  2：组件编译  3: 产品编译 +  coverity
	Status     uint   `gorm:"not null" json:"status"`                    // 作业状态 0：运行，1：成功， 2：失败
	Version    string `gorm:"not null; type:varchar(60)" json:"version"` // 版本hash值,初始为0
}
