package coredump

import (
	"bufio"
	"fmt"
	"io"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/buildfarm/dbuildfarmproductcpu"
	"irisAdminApi/service/dao/coredump/dcoredumpjob"
	"irisAdminApi/service/dao/coredump/dcoredumptechsupport"
	"irisAdminApi/service/dao/release/dproductmodel"
	"irisAdminApi/service/dao/sig/dtechsupportdecrypt"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

func GetCoredumpTechSupports(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dcoredumptechsupport.ListResponse{}, ctx, name, sort, orderBy, page, pageSize)
	// list, err := dproblem.AllProblems(name, sort, orderBy, page, pageSize, status, start, end, department)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetCoredumpTechSupport(ctx iris.Context) {
	info := dcoredumptechsupport.Response{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
	return
}

func CreateCoredumpTechSupport(ctx iris.Context) {
	userID, _ := dao.GetAuthId(ctx)
	req := dcoredumptechsupport.Request{}
	if err := ctx.ReadForm(&req); err != nil {
		logging.ErrorLogger.Errorf("create record read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	//先执行文件上传，文件上传成功后执行创建申请单操作
	f, fh, err := ctx.FormFile("file")
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	defer f.Close()
	fileName := strings.Replace(fh.Filename, " ", "_", -1)

	tempName := libs.GetUniqueID()
	if libs.Config.CoreDump.Upload == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置工作目录，请联系管理员"))
		return
	}
	var upload = filepath.Join(libs.Config.CoreDump.Upload, time.Now().Format("20060102"), tempName)
	err = os.MkdirAll(upload, 0750)
	os.Chmod(upload, 0750)
	//保存文件
	_, err = ctx.SaveFormFile(fh, filepath.Join(upload, fileName))
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	// 获取文件md5为文件名，用于防止重复上传相同文件
	fileMd5, err := libs.GetFileMd5(filepath.Join(upload, fileName))
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	// 检查是否已经上传过，如果已经上传，直接创建映射
	// approval := dapproval.Response{}
	// err = approval.FindByMd5(fileMd5)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }

	// if req.Product == "z8620" || req.Product == "z8680" {
	// 	product = "z8600"
	// }

	// if req.Product == "e5120" || req.Product == "e5220" {
	// 	product = "e5100"
	// }

	releaseProjectModel := dproductmodel.ReleaseProductModel{}
	err = releaseProjectModel.FindByName(req.Product)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	product := releaseProjectModel.ReleaseBuildName.Name
	err = dao.Create(&dcoredumptechsupport.Response{}, ctx, map[string]interface{}{
		"JobID":     tempName,
		"FileName":  fileName,
		"MD5":       fileMd5,
		"TempDir":   upload,
		"CreatedAt": time.Now(),
		"UpdatedAt": time.Now(),
		"UserID":    userID,
		"Product":   product,
	})

	if err != nil {
		logging.ErrorLogger.Errorf("create tech support get err ", err)
		defer os.RemoveAll(upload)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	// 执行收集堆栈动作
	techSupportFile := filepath.Join(upload, fileName)

	go InitSandBox(techSupportFile, product, tempName)
	ctx.JSON(response.NewResponse(response.NoErr.Code, req, response.NoErr.Msg))
	return
}

func InitSandBox(techSupportFile, product, tempName string) error {
	// bash gdb.sh root 127.0.0.1 9022 /tmp/tech-support-2022-09-09_11-06-39.tgz  z5100-s 20230202317009999 /mnt/sata0
	techSupport := dcoredumptechsupport.Response{}
	err := techSupport.FindEx("job_id", tempName)
	if err != nil {
		logging.ErrorLogger.Errorf("find techsupport file err ", err)
		return err
	}
	if techSupport.ID == 0 {
		logging.ErrorLogger.Errorf("techsupport file not found ")
		return fmt.Errorf("techsupport file not found ")
	}
	if libs.Config.CoreDump.Upload == "" {
		logging.ErrorLogger.Errorf("未配置工作目录 ")
		return fmt.Errorf("未配置工作目录 ")
	}
	outputDir := filepath.Join(libs.Config.CoreDump.Upload, techSupport.CreatedAt.Format("20060102"), techSupport.JobID)
	err = os.MkdirAll(outputDir, 0750)
	os.Chmod(outputDir, 0750)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		return err
	}
	logFile := filepath.Join(outputDir, fmt.Sprintf("%s_%s.log", filepath.Base(techSupport.FileName), techSupport.JobID))
	f, err := os.OpenFile(logFile, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		logging.ErrorLogger.Errorf("create coredump job err ", err)
		return err
	}
	defer f.Close()

	initGdb := filepath.Join(libs.Config.CoreDump.Plugin, "init_gdb.sh")
	command := fmt.Sprintf(`bash "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s"`, initGdb, libs.Config.CoreDump.Username, libs.Config.CoreDump.Host, libs.Config.CoreDump.Port, techSupportFile, product, tempName, libs.Config.CoreDump.RemoteWorkDir)
	output, err := libs.ExecCommand(command)
	_, err2 := f.Write([]byte(output))
	if err2 != nil {
		logging.ErrorLogger.Errorf("write log err ", err2)
		return err2
	}
	// 获取远程工作目录  配置目录+文件名(去除扩展名)+'_'+tempName+"/temp"
	fileName := strings.TrimSuffix(filepath.Base(techSupportFile), filepath.Ext(techSupportFile))
	remoteWorkDir := libs.Config.CoreDump.RemoteWorkDir + "/" + fileName + "_" + tempName + "/temp"
	// 解析产品信息
	productName := extractProductName(output)

	//更新产品名称
	if len(productName) > 0 {
		releaseProjectModel := dproductmodel.ReleaseProductModel{}
		err = releaseProjectModel.FindByName(productName)
		if err != nil {
			logging.ErrorLogger.Errorf("find product model error", err)
			return err
		}

		product = releaseProjectModel.ReleaseBuildName.Name
	}

	if err != nil {
		//回写状态为失败
		err := techSupport.Update(techSupport.ID, map[string]interface{}{"product": product, "status": 2, "updated_at": time.Now()})
		if err != nil {
			logging.ErrorLogger.Errorf("techsupport status update err ")
			return err
		}
	} else {
		err = techSupport.Update(techSupport.ID, map[string]interface{}{"product": product, "status": 1, "updated_at": time.Now(), "remote_work_dir": remoteWorkDir})
		if err != nil {
			logging.ErrorLogger.Errorf("techsupport file status udpated err ")
			return err
		}
	}
	return nil
}

func GetCoredumps(ctx iris.Context) {
	techSupport := dcoredumptechsupport.Response{}
	err := dao.Find(&techSupport, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	if techSupport.ID == 0 {
		logging.ErrorLogger.Errorf("techsupport file not found ")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未找到记录"))
		return
	}
	if techSupport.Status != 1 {
		logging.ErrorLogger.Errorf("sandbox status not ok")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": "沙箱环境未就绪"}, "未找到记录"))
		return
	}
	output, err := ListCoreDumpFiles(&techSupport)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": fmt.Sprintf("%s: %s", err.Error(), output)}, response.SystemErr.Msg))
		return
	}
	coredumps := strings.Split(strings.TrimSuffix(output, "\n"), " ")
	items := []map[string]interface{}{}
	for _, coredump := range coredumps {
		items = append(items, map[string]interface{}{
			"name": filepath.Base(coredump),
			"path": coredump,
		})
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]interface{}{"items": items}, response.NoErr.Msg))
	return
}

func GetProgram(ctx iris.Context) {
	name := ctx.FormValue("name")
	techSupport := dcoredumptechsupport.Response{}
	err := dao.Find(&techSupport, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	if techSupport.ID == 0 {
		logging.ErrorLogger.Errorf("techsupport file not found ")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未找到记录"))
		return
	}
	output, err := FindCoreDumpProgram(&techSupport, name)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": fmt.Sprintf("%s: %s", err.Error(), output)}, response.SystemErr.Msg))
		return
	}
	programs := strings.Split(strings.TrimSuffix(output, "\n"), " ")
	items := []map[string]interface{}{}
	for _, program := range programs {
		items = append(items, map[string]interface{}{
			"name": filepath.Base(program),
			"path": program,
		})
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]interface{}{"items": items}, response.NoErr.Msg))
	return
}

type BTRequest struct {
	Program  string `json:"program"`
	Coredump string `json:"coredump"`
}

func GetBT(ctx iris.Context) {
	request := BTRequest{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("create record read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	techSupport := dcoredumptechsupport.Response{}
	err := dao.Find(&techSupport, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	if techSupport.ID == 0 {
		logging.ErrorLogger.Errorf("techsupport file not found ")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, "未找到记录"))
		return
	}

	if libs.Config.CoreDump.Output == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置工作目录，请联系管理员"))
		return
	}
	var outputDir = filepath.Join(libs.Config.CoreDump.Upload, time.Now().Format("20060102"), techSupport.JobID)
	err = os.MkdirAll(outputDir, 0750)
	os.Chmod(outputDir, 0750)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	jobID := libs.GetUniqueID()
	logFile := filepath.Join(outputDir, fmt.Sprintf("%s_%s_%s.log", filepath.Base(request.Program), filepath.Base(request.Coredump), jobID))
	f, err := os.OpenFile(logFile, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		logging.ErrorLogger.Errorf("create coredump job err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	err = dao.Create(&dcoredumpjob.Response{}, ctx, map[string]interface{}{
		"JobID":         jobID,
		"TechSupportID": techSupport.ID,
		"ProcessName":   filepath.Base(request.Program),
		"ProcessPath":   request.Program,
		"FileName":      filepath.Base(request.Coredump),
		"FilePath":      request.Coredump,
		"LogFile":       logFile,
		"CreatedAt":     time.Now(),
		"UpdatedAt":     time.Now(),
		"Status":        0,
	})

	if err != nil {
		logging.ErrorLogger.Errorf("create coredump job err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	go func() {
		defer f.Close()
		job := dcoredumpjob.Response{}
		err = job.FindEx("job_id", jobID)
		if err != nil {
			logging.ErrorLogger.Errorf("create coredump job err ", err)
		}

		output, err := CollectBT(&techSupport, request.Program, request.Coredump)
		if err != nil {
			_, err = f.Write([]byte(output))
			if err != nil {
				logging.ErrorLogger.Errorf("collect bt get err ", err)
				job.Update(job.ID, map[string]interface{}{
					"status":     2,
					"updated_at": time.Now(),
				})
				return
			}
			logging.ErrorLogger.Errorf("collect bt get err ", err)
			job.Update(job.ID, map[string]interface{}{
				"status":     2,
				"updated_at": time.Now(),
			})
			return
		}
		_, err = f.Write([]byte(output))
		if err != nil {
			logging.ErrorLogger.Errorf("collect bt get err ", err)
			job.Update(job.ID, map[string]interface{}{
				"status":     2,
				"updated_at": time.Now(),
			})
			return
		}

		job.Update(job.ID, map[string]interface{}{
			"status":     1,
			"updated_at": time.Now(),
		})
	}()

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func ListCoreDumpFiles(techSupport *dcoredumptechsupport.Response) (string, error) {
	// bash gdb.sh root 127.0.0.1 9022 /tmp/tech-support-2022-09-09_11-06-39.tgz  z5100-s 20230202317009999 /mnt/sata0
	shell := filepath.Join(libs.Config.CoreDump.Plugin, "list_coredump.sh")
	command := fmt.Sprintf("bash %s %s %s %s %s", shell, libs.Config.CoreDump.Username, libs.Config.CoreDump.Host, libs.Config.CoreDump.Port, techSupport.RemoteWorkDir)
	output, err := libs.ExecCommand(command)
	if err != nil {
		return output, err
	}
	return output, err
}

func FindCoreDumpProgram(techSupport *dcoredumptechsupport.Response, name string) (string, error) {
	// bash gdb.sh root 127.0.0.1 9022 /tmp/tech-support-2022-09-09_11-06-39.tgz  z5100-s 20230202317009999 /mnt/sata0
	shell := filepath.Join(libs.Config.CoreDump.Plugin, "list_program.sh")
	command := fmt.Sprintf("bash %s %s %s %s %s %s", shell, libs.Config.CoreDump.Username, libs.Config.CoreDump.Host, libs.Config.CoreDump.Port, techSupport.RemoteWorkDir, name)

	output, err := libs.ExecCommand(command)
	if err != nil {
		return output, err
	}
	return output, err
}

func CollectBT(techSupport *dcoredumptechsupport.Response, program, coredump string) (string, error) {
	if strings.HasSuffix(coredump, ".gz") {
		shell := filepath.Join(libs.Config.CoreDump.Plugin, "unzip_coredump.sh")
		command := fmt.Sprintf("bash %s %s %s %s %s", shell, libs.Config.CoreDump.Username, libs.Config.CoreDump.Host, libs.Config.CoreDump.Port, coredump)

		output, err := libs.ExecCommand(command)
		if err != nil {
			return output, err
		}
		coredump = strings.TrimSuffix(coredump, ".gz")
	}
	shell := filepath.Join(libs.Config.CoreDump.Plugin, "remote_gdb.py")
	workDir := filepath.Join(techSupport.RemoteWorkDir, "rootfs_debug", "usr", "lib")
	cpus, err := dbuildfarmproductcpu.FindDistinctCpuByProduct(strings.Split(techSupport.Product, "|"))
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		return err.Error(), err
	}
	aarches := []string{}
	_cpus := []string{}
	for _, cpu := range cpus {
		if !libs.InArrayS(aarches, strings.Split(cpu, "-")[1]) {
			aarches = append(aarches, strings.Split(cpu, "-")[1])
			_cpus = append(_cpus, cpu)
		}
	}
	if len(aarches) == 0 {
		return "未配置该型号CPU映射关系,请联系管理员", fmt.Errorf("未配置该型号CPU映射关系,请联系管理员")
	}
	gdb := "ntos-gdb"
	if aarches[0] == "x86_64" {
		gdb = "x86_64-gdb"
	}
	// sysroot := fmt.Sprintf("%s/host/%s/%s-linux-gnu/sysroot/", techSupport.TempDir, _cpus[0], aarches[0])
	// command := fmt.Sprintf("python3 %s %s %s %s %s %s %s %s", shell, libs.Config.CoreDump.Username, libs.Config.CoreDump.Host, libs.Config.CoreDump.Port, program, coredump, gdb, sysroot)
	command := fmt.Sprintf("python3 %s %s %s %s %s %s %s %s", shell, libs.Config.CoreDump.Username, libs.Config.CoreDump.Host, libs.Config.CoreDump.Port, workDir, program, coredump, gdb)
	output, err := libs.ExecCommand(command)
	if err != nil {
		return output, err
	}

	return output, err
}

func GetLog(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	job := dcoredumpjob.Response{}
	err := job.Find(uint(id))
	if err != nil {
		logging.ErrorLogger.Errorf("get tech support err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if job.ID == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "记录不存在"))
		return
	}

	if _, err := os.Stat(job.LogFile); os.IsNotExist(err) {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "日志文件不存在"))
		return
	}

	fd, err := os.Open(filepath.Join(job.LogFile))
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	defer fd.Close()
	buff := bufio.NewReader(fd)
	var chunks []byte
	dataBuff := make([]byte, 1024)
	for {
		n, err := buff.Read(dataBuff)
		if err != nil && err != io.EOF {
			ctx.WriteString("获取文件错误！")
			return
		}
		if n == 0 {
			break
		}
		chunks = append(chunks, dataBuff[:n]...)
	}
	ctx.WriteString(string(chunks))
	return
}

func GetCoredumpJobs(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dcoredumpjob.ListResponse{}, ctx, name, sort, orderBy, page, pageSize)
	// list, err := dproblem.AllProblems(name, sort, orderBy, page, pageSize, status, start, end, department)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func TransProductToBuildname(ctx iris.Context) {
	product := ctx.FormValue("product")
	if len(product) == 0 {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	releaseProjectModel := dproductmodel.ReleaseProductModel{}
	err := releaseProjectModel.FindByName(product)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.WriteString(releaseProjectModel.ReleaseBuildName.Name)
	return
}

func GetCoredumpJobsByTechSupportID(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	where := []map[string]string{{"column": "tech_support_id", "condition": "=", "value": strconv.Itoa(int(id))}}
	list, err := dao.AllEx(&dcoredumpjob.ListResponse{}, ctx, where, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetCoredumpFiles(ctx iris.Context) {
	root := ctx.Params().Get("root")
	absPath := filepath.Join(libs.Config.CoreDump.DecryptedDir, filepath.Clean(root))
	if fileInfo, err := os.Stat(absPath); err != nil {
		if os.IsNotExist(err) {
			ctx.StatusCode(iris.StatusNotFound)
			ctx.WriteString("404 - File not found")
			return
		}
		ctx.StatusCode(iris.StatusInternalServerError)
		ctx.WriteString("500 - Internal server error")
		return
	} else if fileInfo.IsDir() {
		files, err := os.ReadDir(absPath)
		if err != nil {
			ctx.StatusCode(iris.StatusInternalServerError)
			ctx.WriteString("500 - Internal server error")
			return
		}
		parentPath := filepath.Dir(root)
		// 构建HTML内容
		htmlContent := "<html><body>"
		htmlContent += "<h1>Coredump Directory  for " + root + "</h1>"
		// 添加返回上一级入口
		if parentPath != "." && parentPath != "/" {
			htmlContent += fmt.Sprintf("<p><a href='%s/api/v1/coredump/files/%s'>⬅️返回上一级</a></p>", libs.Config.CoreDump.PathHeader, parentPath)
		}
		htmlContent += "<ul>"
		for _, file := range files {
			filePath := filepath.Join(libs.Config.CoreDump.PathHeader, "/api/v1/coredump/files/", root, file.Name())
			if file.IsDir() {
				// 在文件夹名称后添加特殊符号或文本
				// htmlContent += fmt.Sprintf("<li><a href='%s'>%s📁</a></li>", filePath, file.Name())
				htmlContent += fmt.Sprintf("<li><a href='%s'>%s/</a></li>", filePath, file.Name())
			} else {
				htmlContent += fmt.Sprintf("<li><a href='%s'>%s</a></li>", filePath, file.Name())
			}
		}
		htmlContent += "</ul>"
		htmlContent += "</body></html>"
		ctx.HTML(htmlContent)
	} else {
		ctx.SendFile(absPath, fileInfo.Name())
	}
}

func CreateCoredumpByTechsupportDecrypt(ctx iris.Context) {
	userID, _ := dao.GetAuthId(ctx)
	decryptJobID := ctx.FormValue("decrypt_job_id")
	if len(decryptJobID) == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "decrypt_job_id不能为空"))
		return
	}
	//获取一键解密作业
	decryptJob := dtechsupportdecrypt.SigTechSupportDecryptJob{}
	err := decryptJob.FindEx("job_id", decryptJobID)
	if err != nil {
		logging.ErrorLogger.Errorf("find sig job get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if decryptJob.ID == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "decrypt_job_id不存在"))
		return
	}
	//判断解密状态
	if decryptJob.Status != 1 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "该job_id为一键解密作业状态不成功，不可用"))
		return
	}
	//获取解密文件路径
	decryptFilePath := filepath.Join(decryptJob.Dir(), decryptJob.OutputFileName)
	//先将文件获取解密文件路径，复制文件到目标文件夹后执行创建申请单操作
	fileName := decryptJob.OutputFileName
	tempName := libs.GetUniqueID()
	if libs.Config.CoreDump.Upload == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置工作目录，请联系管理员"))
		return
	}
	var upload = filepath.Join(libs.Config.CoreDump.Upload, time.Now().Format("20060102"), tempName)
	err = os.MkdirAll(upload, 0750)
	os.Chmod(upload, 0750)
	//保存文件
	_, err = CopyFile(decryptFilePath, filepath.Join(upload, fileName))
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	// 获取文件md5为文件名，用于防止重复上传相同文件
	fileMd5, err := libs.GetFileMd5(filepath.Join(upload, fileName))
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	product := ""
	Dcoredumptechsupport := dcoredumptechsupport.Response{}
	err = Dcoredumptechsupport.Create(map[string]interface{}{
		"JobID":     tempName,
		"FileName":  fileName,
		"MD5":       fileMd5,
		"TempDir":   upload,
		"CreatedAt": time.Now(),
		"UpdatedAt": time.Now(),
		"UserID":    userID,
		"Product":   product,
	})

	if err != nil {
		logging.ErrorLogger.Errorf("create tech support get err ", err)
		defer os.RemoveAll(upload)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	// 执行收集堆栈动作
	techSupportFile := filepath.Join(upload, fileName)
	go InitSandBox(techSupportFile, product, tempName)
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetTechsupportLog(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	techSupport := dcoredumptechsupport.Response{}
	err := techSupport.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("get tech support log err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if techSupport.ID == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "记录不存在"))
		return
	}
	outputDir := filepath.Join(libs.Config.CoreDump.Upload, techSupport.CreatedAt.Format("20060102"), techSupport.JobID)
	logFile := filepath.Join(outputDir, fmt.Sprintf("%s_%s.log", filepath.Base(techSupport.FileName), techSupport.JobID))
	if _, err := os.Stat(logFile); os.IsNotExist(err) {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "日志文件不存在"))
		return
	}
	fd, err := os.Open(filepath.Join(logFile))
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	defer fd.Close()
	buff := bufio.NewReader(fd)
	var chunks []byte
	dataBuff := make([]byte, 1024)
	for {
		n, err := buff.Read(dataBuff)
		if err != nil && err != io.EOF {
			ctx.WriteString("获取文件错误！")
			return
		}
		if n == 0 {
			break
		}
		chunks = append(chunks, dataBuff[:n]...)
	}
	ctx.WriteString(string(chunks))
	return
}

// 文件复制
func CopyFile(src, des string) (written int64, err error) {
	srcFile, err := os.Open(src)
	if err != nil {
		return 0, err
	}
	defer srcFile.Close()

	//获取源文件的权限
	fi, _ := srcFile.Stat()
	perm := fi.Mode()

	// desFile, err := os.Create(des)  //无法复制源文件的所有权限
	desFile, err := os.OpenFile(des, os.O_RDWR|os.O_CREATE|os.O_TRUNC, perm) //复制源文件的所有权限
	if err != nil {
		return 0, err
	}
	defer desFile.Close()

	return io.Copy(desFile, srcFile)
}

// 使用正则表达式从程序输出中提取产品名称
func extractProductName(output string) string {
	// 使用正则表达式以匹配特定的标记
	re := regexp.MustCompile(`产品信息开始>>>(.*?)<<<产品信息结束`)
	matches := re.FindStringSubmatch(output)
	if len(matches) > 1 {
		return matches[1] // 返回第一个匹配的组（即产品名称）
	}
	return ""
}
