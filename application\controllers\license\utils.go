package license

import (
	"irisAdminApi/application/controllers/license/taskmanagers"
	"irisAdminApi/application/libs"
	"irisAdminApi/service/dao/user/duser"
)

func InitTaskManagers() {
	if libs.Config.License.Enable {
		taskmanagers.InitDeviceAuthTaskManager()
	}
}

func isAdminUser(userId uint) bool {
	user := &duser.User{ID: userId}
	duser.GetUserRoles(user)
	for _, role := range user.Roles {
		if role == "超级管理员" {
			return true
		}
	}
	return false
}
