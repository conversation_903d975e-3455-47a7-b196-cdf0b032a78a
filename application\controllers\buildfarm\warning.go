package buildfarm

import (
	"bufio"
	"context"
	"fmt"
	"irisAdminApi/application/controllers/datasync"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/buildfarm/dbuildfarmwarning"
	"irisAdminApi/service/dao/buildfarm/dcronmakejob"
	"irisAdminApi/service/dao/user/duser"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	larkbitable "github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
	"github.com/pkg/errors"
)

var appToken = "KZQCbsWefa5e3MsamMccOYYPnMr"
var warningTableID = "tblpZxL2Su2Q202U"
var warningPattern = regexp.MustCompile(`^\S+(:\d*)* warning:`)
var pathPattern = regexp.MustCompile(`^>>>.*Syncing from source dir`)
var bspPattern = regexp.MustCompile(`^bspdriver`)
var fpPattern = regexp.MustCompile(`^fastpath`)
var replacePattern = regexp.MustCompile(`/mnt/sata0/[a-z0-9]+/prj_[^/.]+/`)

func WarningAnalysis(makeJobID uint, jobID, project, branch, product, buildType, buildLog string) error {
	warnings := []map[string]interface{}{}

	// 打开文件
	f, err := os.Open(buildLog)
	if err != nil {
		return err
	}
	defer f.Close()
	// 读取文件并匹配正则表达式
	scanner := bufio.NewScanner(f)

	var pathWarningMap = map[string][]string{}
	var _path string
	for scanner.Scan() {
		line := scanner.Text()
		if pathPattern.MatchString(line) {
			lineSlice := strings.Split(line, " ")
			pathSlice := strings.Split(lineSlice[len(lineSlice)-1], "/")

			if len(pathSlice) > 5 {
				_path = strings.Join(pathSlice[5:], "/")
			} else {
				_path = strings.Join(pathSlice, "/")
			}

			_path = bspPattern.ReplaceAllString(_path, "bsp-driver")
			_path = fpPattern.ReplaceAllString(_path, "fast-path")
			pathWarningMap[_path] = []string{}
		}

		if len(_path) > 0 && warningPattern.MatchString(line) {
			pathWarningMap[_path] = append(pathWarningMap[_path], line)
		}
	}

	now := time.Now()

	for path := range pathWarningMap {
		for _, line := range pathWarningMap[path] {
			line = replacePattern.ReplaceAllString(line, "")
			s := strings.Split(line, " warning: ")
			pos := s[0]
			fs := strings.Split(pos, ":")
			_fn := strings.Replace(fs[0], "../", "", -1)
			fns := strings.Split(_fn, "/")
			var fn string
			if len(fns) >= 3 {
				fn = strings.Join(fns[len(fns)-3:], "/")
			} else {
				fn = strings.Join(fns, "/")
			}

			warning := map[string]interface{}{
				"CreatedAt": now,
				"UpdatedAt": now,
				"MakeJobID": makeJobID,
				"JobID":     jobID,
				"Detail":    line,
				"Position":  pos,
				"Filename":  fn,
				"SourceDir": path,
				"Project":   project,
				"Branch":    branch,
				"Product":   product,
				"BuildType": buildType,
			}
			warnings = append(warnings, warning)
		}
	}

	err = dbuildfarmwarning.BatchCreate(warnings)
	if err != nil {
		return err
	}

	return nil
}

func UpdateWarningSummaryToFeishu() error {
	users, err := duser.All()
	if err != nil {
		return errors.Wrap(err, "")
	}
	userMap := map[string]*duser.ListResponse{}

	for _, user := range users {
		userMap[user.Name] = user
	}

	feishuSummaryObjects, err := GetWarningSummaryFromFeishu()
	if err != nil {
		return errors.Wrap(err, "")
	}
	feishuSummaryMap := map[string]map[string]interface{}{}
	allSummaryMap := map[string]*dbuildfarmwarning.BuildfarmWarningSummary{}
	for _, item := range feishuSummaryObjects {
		key := item["Detail"].(string)
		feishuSummaryMap[key] = item
	}

	items, err := dbuildfarmwarning.Summary()
	if err != nil {
		return errors.Wrap(err, "")
	}
	maxCheckedAt, err := dbuildfarmwarning.MaxCheckedAt()
	if err != nil {
		return errors.Wrap(err, "")
	}
	addObjects := []map[string]interface{}{}
	updateObjects := []map[string]interface{}{}
	for _, item := range items {
		key := item.Detail
		// 跳过已存在飞书记录
		allSummaryMap[key] = item
		if _, ok := feishuSummaryMap[key]; ok {
			// 检查是否需要更新，更新字段部门、最后一次检测时间、状态
			lastCheckedAt := item.LastCheckedAt
			updateObject := map[string]interface{}{
				"record_id": feishuSummaryMap[key]["record_id"],
			}
			if u, ok := userMap[item.Owner]; ok && u.Department != nil {
				updateObject["Department"] = u.Department.Name
			}
			if maxCheckedAt.Sub(lastCheckedAt).Minutes() > 0 {
				updateObject["Status"] = "已修订"
				updateObject["LastCheckedAt"] = item.LastCheckedAt.UnixNano() / 1e6
			} else {
				updateObject["LastCheckedAt"] = item.LastCheckedAt.UnixNano() / 1e6
			}

			if feishuSummaryMap[key]["Status"].(string) == "无需修订" {
				updateObject["Status"] = "无需修订"
			}

			updateObjects = append(updateObjects, updateObject)

		} else {
			var openID []interface{}
			var department string
			if _, ok := userMap[item.Owner]; ok {
				if len(userMap[item.Owner].OpenID) > 0 {
					openID = []interface{}{map[string]interface{}{"id": userMap[item.Owner].OpenID}}
				}

				if userMap[item.Owner].Department != nil {
					department = userMap[item.Owner].Department.Name
				}
			}

			addObjects = append(addObjects, map[string]interface{}{
				"MakeJobID":       item.MakeJobID,
				"Detail":          item.Detail,
				"Position":        item.Position,
				"Filename":        item.Filename,
				"SourceDir":       item.SourceDir,
				"Project":         item.Project,
				"Branch":          item.Branch,
				"Product":         item.Product,
				"BuildType":       item.BuildType,
				"FullPath":        item.FullPath,
				"Component":       item.Component,
				"ComponentPacket": item.ComponentPacket,
				"Department":      department,
				"Owner":           openID,
				"Comment":         item.Comment,
				"Status":          "",
				"FirstCheckedAt":  item.FirstCheckedAt.UnixNano() / 1e6,
				"LastCheckedAt":   item.LastCheckedAt.UnixNano() / 1e6,
			})
		}
	}

	if len(addObjects) > 0 {
		if err := BatchCreateRecord(warningTableID, addObjects); err != nil {
			logging.ErrorLogger.Errorf("批量创建错误", err.Error())
		}
	}
	if len(updateObjects) > 0 {
		if err := BatchUpdateRecord(warningTableID, updateObjects); err != nil {
			logging.ErrorLogger.Errorf("批量更新错误", err.Error())
		}
	}

	return nil
}

func GetWarningSummaryFromFeishu() ([]map[string]interface{}, error) {
	objects := []map[string]interface{}{}
	records, err := datasync.GetTableData(appToken, warningTableID)
	if err != nil {
		return nil, errors.Wrap(err, "")
	}

	kvMap := map[string]string{
		"warning详细信息": "Detail",
		"warning位置":   "Position",
		"warning文件":   "Filename",
		"warning文件目录": "SourceDir",
		"项目":          "Project",
		"工程分支":        "Branch",
		"产品":          "Product",
		"编译类型":        "BuildType",
		"代码路径":        "FullPath",
		"组件":          "Component",
		"组件包":         "ComponentPacket",
		"是否处理":        "Comment",
		"负责人":         "Owner",
		"状态":          "Status",
		"首次检测时间":      "FirstCheckedAt",
		"最后检测时间":      "LastCheckedAt",
	}
	// createdAt := time.Now()
	// updatedAt := time.Now()

mainLoop:
	for _, record := range records {
		object := map[string]interface{}{}
		object["record_id"] = *record.RecordId
		for k, v := range kvMap {
			if value, ok := record.Fields[k]; ok {
				if value == nil {
					object[v] = ""
					if k == "warning详细信息" {
						continue mainLoop
					}
				} else if k == "负责人" {
					object[v] = datasync.GetFeishUsername(value)
				} else {
					object[v] = value
				}
			} else {
				return nil, fmt.Errorf("字段%s不存在", k)
			}
		}
		objects = append(objects, object)
	}
	return objects, nil
}

// 创建记录
func BatchCreateRecord(tableID string, data []map[string]interface{}) error {
	limit := 500
	for loop := 0; true; loop++ {
		start := loop * limit
		if start > len(data) {
			break
		}
		end := (loop + 1) * limit
		if len(data) <= (loop+1)*limit {
			end = len(data)
		}

		records := []*larkbitable.AppTableRecord{}
		for _, record := range data[start:end] {
			records = append(records, larkbitable.NewAppTableRecordBuilder().
				Fields(map[string]interface{}{
					"warning详细信息": record["Detail"],
					"warning位置":   record["Position"],
					"warning文件":   record["Filename"],
					"warning文件目录": record["SourceDir"],
					"项目":          record["Project"],
					"工程分支":        record["Branch"],
					"产品":          record["Product"],
					"编译类型":        record["BuildType"],
					"代码路径":        record["FullPath"],
					"组件":          record["Component"],
					"组件包":         record["ComponentPacket"],
					"是否处理":        record["Comment"],
					"负责人":         record["Owner"],
					"状态":          record["Status"],
					"首次检测时间":      record["FirstCheckedAt"],
					"最后检测时间":      record["LastCheckedAt"],
				}).
				Build())
		}

		body := larkbitable.NewBatchCreateAppTableRecordReqBodyBuilder().Records(records).Build()

		// 创建请求对象
		req := larkbitable.NewBatchCreateAppTableRecordReqBuilder().
			AppToken(appToken).
			TableId(tableID).
			Body(body).
			Build()

		// 发起请求
		resp, err := datasync.FeiShuClient.Bitable.AppTableRecord.BatchCreate(context.Background(), req)

		// 处理错误
		if err != nil {
			return errors.Wrap(err, "")
		}

		// 服务端错误处理
		if !resp.Success() {
			return fmt.Errorf("飞书服务端返回错误 %v %v %v", resp.Code, resp.Msg, resp.RequestId())
		}
		// 更新ID
		time.Sleep(1 * time.Second)
	}
	return nil
}

// 创建记录
func BatchUpdateRecord(tableID string, data []map[string]interface{}) error {
	limit := 500
	for loop := 0; true; loop++ {
		start := loop * limit
		if start > len(data) {
			break
		}
		end := (loop + 1) * limit
		if len(data) <= (loop+1)*limit {
			end = len(data)
		}

		records := []*larkbitable.AppTableRecord{}
		for _, record := range data[start:end] {
			f := map[string]interface{}{
				// "warning详细信息": record["Detail"],
				// "warning位置":   record["Position"],
				// "warning文件":   record["Filename"],
				// "warning文件目录": record["SourceDir"],
				// "项目":          record["Project"],
				// "工程分支":        record["Branch"],
				// "产品":          record["Product"],
				// "编译类型":        record["BuildType"],
				// "代码路径":        record["FullPath"],
				// "组件":          record["Component"],
				// "组件包":         record["ComponentPacket"],
				// "是否处理":        record["Comment"],
				// "负责人":         record["Owner"],
				// "首次检测时间": record["FirstCheckedAt"],
				// "最后检测时间": record["LastCheckedAt"],
				"状态": record["Status"],
			}

			if v, ok := record["FirstCheckedAt"]; ok {
				f["首次检测时间"] = v
			}
			if v, ok := record["LastCheckedAt"]; ok {
				f["最后检测时间"] = v
			}

			records = append(records, larkbitable.NewAppTableRecordBuilder().
				Fields(f).
				RecordId(record["record_id"].(string)).
				Build())
		}

		body := larkbitable.NewBatchUpdateAppTableRecordReqBodyBuilder().Records(records).Build()
		// 创建请求对象
		req := larkbitable.NewBatchUpdateAppTableRecordReqBuilder().
			AppToken(appToken).
			TableId(tableID).
			Body(body).
			Build()
		// 发起请求
		resp, err := datasync.FeiShuClient.Bitable.AppTableRecord.BatchUpdate(context.Background(), req)

		// 处理错误
		if err != nil {
			return errors.Wrap(err, "")
		}

		// 服务端错误处理
		if !resp.Success() {
			return fmt.Errorf("飞书服务端返回错误 %v %v %v", resp.Code, resp.Msg, resp.RequestId())
		}
		// 更新ID
		time.Sleep(1 * time.Second)
	}
	return nil
}

func UpdateWarningSummaryToFeishuWorker() {
	go func() {
		logging.DebugLogger.Debugf("开始同步warning数据")
		err := UpdateWarningSummaryToFeishu()
		if err != nil {
			logging.ErrorLogger.Errorf(err.Error())
		} else {
			logging.DebugLogger.Debugf("完成同步warning数据")
		}
	}()
}

func WarningAnalysisWorker() {
	successCronMakeJobs, err := dcronmakejob.FindLastSuccessCronMake("Trunk", "z5100", "release", time.Now())
	if err != nil {
		logging.ErrorLogger.Errorf("Warning分析，查找最近一次编译成功记录失败", err.Error())
		return
	}
	if len(successCronMakeJobs) == 0 {
		logging.ErrorLogger.Errorf("未找到最近一次编译成功记录")
		return
	}
	cronMakeJob := successCronMakeJobs[0]
	if err := WarningAnalysis(cronMakeJob.Id, cronMakeJob.JobId, cronMakeJob.Branch, cronMakeJob.Branch, cronMakeJob.Product, cronMakeJob.BuildType, filepath.Join(libs.Config.Buildfarm.Logpath, cronMakeJob.JobId+".log")); err != nil {
		logging.ErrorLogger.Errorf("Warning分析error", err.Error())
	}
}
