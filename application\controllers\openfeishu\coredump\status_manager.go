package coredump

import (
	"context"
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"time"

	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkbitable "github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
)

// StatusManager 状态管理器
type StatusManager struct {
	feishuClient *lark.Client
	debugMode    bool
	stats        *StatusUpdateStatistics
}

// StatusUpdateStatistics 状态更新统计信息
type StatusUpdateStatistics struct {
	TotalUpdates   int `json:"total_updates"`
	SuccessUpdates int `json:"success_updates"`
	FailedUpdates  int `json:"failed_updates"`
	BatchUpdates   int `json:"batch_updates"`
	RetryCount     int `json:"retry_count"`
}

// BatchUpdateResult 批量更新结果
type BatchUpdateResult struct {
	TotalRecords   int      `json:"total_records"`
	SuccessRecords int      `json:"success_records"`
	FailedRecords  int      `json:"failed_records"`
	Errors         []string `json:"errors"`
}

// NewStatusManager 创建状态管理器
func NewStatusManager(feishuClient *lark.Client) *StatusManager {
	return &StatusManager{
		feishuClient: feishuClient,
		debugMode:    libs.Config.FeiShuDoc.CoredumpDebugMode,
		stats:        &StatusUpdateStatistics{},
	}
}

// UpdateProcessingStatus 更新处理状态
func (sm *StatusManager) UpdateProcessingStatus(recordID, status, bugID, errorMsg string) error {
	if recordID == "" {
		return fmt.Errorf("记录ID不能为空")
	}

	sm.stats.TotalUpdates++

	// 检查干运行模式
	if libs.Config.FeiShuDoc.CoredumpDryRun {
		return sm.simulateStatusUpdate(recordID, "处理状态", status, map[string]interface{}{
			"bugID":    bugID,
			"errorMsg": errorMsg,
		})
	}

	// 构建更新数据
	updateData := map[string]interface{}{
		FieldProcessingStatus: status,
	}

	// 添加Bug ID（如果提供）
	if bugID != "" {
		updateData[FieldBugID] = bugID
	}

	// 添加错误信息（如果提供）
	if errorMsg != "" {
		updateData[FieldErrorMessage] = errorMsg
	}

	// 添加最后更新时间
	updateData[FieldLastUpdated] = time.Now().Format("2006-01-02 15:04:05")

	// 执行状态更新
	err := sm.updateFeishuRecordWithRetry(recordID, updateData)
	if err != nil {
		sm.stats.FailedUpdates++
		return fmt.Errorf("更新处理状态失败: %v", err)
	}

	sm.stats.SuccessUpdates++

	if sm.debugMode {
		logging.InfoLogger.Infof("处理状态更新成功 [%s]: 状态=%s, BugID=%s", recordID, status, bugID)
	}

	return nil
}

// UpdateSyncStatus 更新同步状态
func (sm *StatusManager) UpdateSyncStatus(recordID, syncStatus string) error {
	if recordID == "" {
		return fmt.Errorf("记录ID不能为空")
	}

	sm.stats.TotalUpdates++

	// 检查干运行模式
	if libs.Config.FeiShuDoc.CoredumpDryRun {
		return sm.simulateStatusUpdate(recordID, "同步状态", syncStatus, nil)
	}

	// 构建更新数据
	updateData := map[string]interface{}{
		FieldSyncStatus:  syncStatus,
		FieldLastUpdated: time.Now().Format("2006-01-02 15:04:05"),
	}

	// 执行状态更新
	err := sm.updateFeishuRecordWithRetry(recordID, updateData)
	if err != nil {
		sm.stats.FailedUpdates++
		return fmt.Errorf("更新同步状态失败: %v", err)
	}

	sm.stats.SuccessUpdates++

	if sm.debugMode {
		logging.InfoLogger.Infof("同步状态更新成功 [%s]: 状态=%s", recordID, syncStatus)
	}

	return nil
}

// BatchUpdateRecords 批量更新记录状态
func (sm *StatusManager) BatchUpdateRecords(updates []StatusUpdate) (*BatchUpdateResult, error) {
	if len(updates) == 0 {
		return &BatchUpdateResult{}, nil
	}

	sm.stats.BatchUpdates++
	result := &BatchUpdateResult{
		TotalRecords:   len(updates),
		SuccessRecords: 0,
		FailedRecords:  0,
		Errors:         make([]string, 0),
	}

	// 检查干运行模式
	if libs.Config.FeiShuDoc.CoredumpDryRun {
		return sm.simulateBatchUpdate(updates)
	}

	if sm.debugMode {
		logging.InfoLogger.Infof("开始批量更新状态: %d条记录", len(updates))
	}

	// 批量处理更新
	for i, update := range updates {
		err := sm.processStatusUpdate(update)
		if err != nil {
			result.FailedRecords++
			errorMsg := fmt.Sprintf("记录 %d (%s) 更新失败: %v", i+1, update.RecordID, err)
			result.Errors = append(result.Errors, errorMsg)

			if sm.debugMode {
				logging.ErrorLogger.Error(errorMsg)
			}
		} else {
			result.SuccessRecords++
		}

		// 添加延迟避免API频率限制
		if i < len(updates)-1 {
			time.Sleep(50 * time.Millisecond)
		}
	}

	// 更新统计信息
	sm.stats.TotalUpdates += len(updates)
	sm.stats.SuccessUpdates += result.SuccessRecords
	sm.stats.FailedUpdates += result.FailedRecords

	if sm.debugMode {
		logging.InfoLogger.Infof("批量更新完成: 成功=%d, 失败=%d", result.SuccessRecords, result.FailedRecords)
	}

	return result, nil
}

// simulateStatusUpdate 模拟状态更新（干运行模式）
func (sm *StatusManager) simulateStatusUpdate(recordID, statusType, status string, extraData map[string]interface{}) error {
	logging.InfoLogger.Infof("干运行模式：模拟更新%s [%s]: %s", statusType, recordID, status)

	if extraData != nil {
		for key, value := range extraData {
			if value != "" && value != nil {
				logging.InfoLogger.Infof("  %s: %v", key, value)
			}
		}
	}

	sm.stats.SuccessUpdates++
	return nil
}

// simulateBatchUpdate 模拟批量更新（干运行模式）
func (sm *StatusManager) simulateBatchUpdate(updates []StatusUpdate) (*BatchUpdateResult, error) {
	logging.InfoLogger.Infof("干运行模式：模拟批量更新 %d 条记录", len(updates))

	result := &BatchUpdateResult{
		TotalRecords:   len(updates),
		SuccessRecords: len(updates), // 模拟全部成功
		FailedRecords:  0,
		Errors:         make([]string, 0),
	}

	for i, update := range updates {
		status := update.ProcessingStatus
		if status == "" {
			status = update.SyncStatus
		}
		logging.InfoLogger.Infof("  记录 %d [%s]: %s", i+1, update.RecordID, status)
	}

	sm.stats.TotalUpdates += len(updates)
	sm.stats.SuccessUpdates += len(updates)

	return result, nil
}

// processStatusUpdate 处理单个状态更新
func (sm *StatusManager) processStatusUpdate(update StatusUpdate) error {
	if update.RecordID == "" {
		return fmt.Errorf("记录ID不能为空")
	}

	// 构建更新数据
	updateData := map[string]interface{}{
		FieldLastUpdated: time.Now().Format("2006-01-02 15:04:05"),
	}

	// 添加处理状态
	if update.ProcessingStatus != "" {
		updateData[FieldProcessingStatus] = update.ProcessingStatus
	}

	// 添加同步状态
	if update.SyncStatus != "" {
		updateData[FieldSyncStatus] = update.SyncStatus
	}

	// 添加Bug ID
	if update.BugID != "" {
		updateData[FieldBugID] = update.BugID
	}

	// 添加错误信息
	if update.ErrorMessage != "" {
		updateData[FieldErrorMessage] = update.ErrorMessage
	}

	// 添加自定义字段
	if len(update.UpdateFields) > 0 {
		for key, value := range update.UpdateFields {
			updateData[key] = value
		}
	}

	// 执行更新
	return sm.updateFeishuRecordWithRetry(update.RecordID, updateData)
}

// StatusUpdate 状态更新数据
type StatusUpdate struct {
	RecordID         string                 `json:"record_id"`
	ProcessingStatus string                 `json:"processing_status"`
	SyncStatus       string                 `json:"sync_status"`
	BugID            string                 `json:"bug_id"`
	ErrorMessage     string                 `json:"error_message"`
	UpdateFields     map[string]interface{} `json:"update_fields"`
}

// updateFeishuRecordWithRetry 带重试机制的飞书记录更新
func (sm *StatusManager) updateFeishuRecordWithRetry(recordID string, updateData map[string]interface{}) error {
	maxRetries := 3
	baseDelay := time.Second

	for attempt := 0; attempt <= maxRetries; attempt++ {
		if attempt > 0 {
			sm.stats.RetryCount++
			delay := time.Duration(attempt) * baseDelay
			if sm.debugMode {
				logging.InfoLogger.Infof("状态更新重试 %d/%d [%s]，等待 %v", attempt, maxRetries, recordID, delay)
			}
			time.Sleep(delay)
		}

		err := sm.updateFeishuRecord(recordID, updateData)
		if err == nil {
			return nil
		}

		if sm.debugMode {
			logging.ErrorLogger.Errorf("状态更新尝试 %d 失败 [%s]: %v", attempt+1, recordID, err)
		}

		// 最后一次尝试失败
		if attempt == maxRetries {
			return fmt.Errorf("状态更新失败，已重试 %d 次 [%s]: %v", maxRetries, recordID, err)
		}
	}

	return fmt.Errorf("状态更新失败 [%s]", recordID)
}

// updateFeishuRecord 更新飞书记录
func (sm *StatusManager) updateFeishuRecord(recordID string, updateData map[string]interface{}) error {
	config := libs.Config.FeiShuDoc

	// 使用飞书SDK直接更新记录
	req := larkbitable.NewUpdateAppTableRecordReqBuilder().
		AppToken(config.CoredumpAppToken).
		TableId(config.CoredumpTableID).
		RecordId(recordID).
		AppTableRecord(larkbitable.NewAppTableRecordBuilder().
			Fields(updateData).
			Build()).
		Build()

	// 发起更新请求
	resp, err := sm.feishuClient.Bitable.AppTableRecord.Update(context.Background(), req)
	if err != nil {
		return fmt.Errorf("飞书记录更新失败: %v", err)
	}

	// 检查响应状态
	if !resp.Success() {
		return fmt.Errorf("API错误: 错误码=%d, 错误信息=%s", resp.Code, resp.Msg)
	}

	return nil
}

// GetStatusUpdateStatistics 获取状态更新统计信息
func (sm *StatusManager) GetStatusUpdateStatistics() *StatusUpdateStatistics {
	return sm.stats
}

// ResetStatistics 重置统计信息
func (sm *StatusManager) ResetStatistics() {
	sm.stats = &StatusUpdateStatistics{}
}
