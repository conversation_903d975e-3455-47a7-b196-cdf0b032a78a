package transproblem

import (
	"errors"
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/models/kpi"
	"irisAdminApi/service/dao/kpi/dattachment"
	"irisAdminApi/service/dao/kpi/dproblem"
	"irisAdminApi/service/dao/kpi/dproblemproccess"
	"irisAdminApi/service/dao/kpi/dproblemsource"
	"irisAdminApi/service/dao/user/duser"
	"time"

	"gorm.io/gorm"
	// "reflect"
)

func UpdateProblem(uId uint, id uint, problemObject map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		// 检查是否已经是历史版本，禁止更新历史版本
		originProblem := dproblem.Response{}
		if err := tx.Model(dproblem.Model()).Where("id = ?", id).Find(&originProblem).Error; err != nil {
			return err
		}
		if originProblem.ID == 0 {
			return errors.New("未找到该问题记录")
		}

		if err := tx.Model(dproblem.Model()).Where("id = ?", id).Updates(problemObject).Error; err != nil {
			return err
		}

		newProblem := dproblem.Response{}
		if err := tx.Model(dproblem.Model()).Where("id = ?", id).Find(&newProblem).Error; err != nil {
			return err
		}

		// object := map[string]interface{}{
		// 	"UpdatedAt":       time.Now(),
		// 	"DiscoveredAt":    request.DiscoveredAt,
		// 	"Description":     request.Description,
		// 	"ProblemSourceID": request.ProblemSourceID,
		// 	"PlanCloseAt":     request.PlanCloseAt,
		// 	"UserID":          uId,
		// 	"Status":          0,
		// 	"OwnerID":         request.OwnerID,
		// }
		strings := ""
		statusMap := map[uint]string{
			0: "未闭环",
			1: "已闭环",
		}
		if problemObject["Status"].(uint) != originProblem.Status {
			if strings == "" {
				strings = fmt.Sprintf("%s %s %s\n", statusMap[originProblem.Status], "->", statusMap[problemObject["Status"].(uint)])
			} else {
				strings = fmt.Sprintf("%s%s\n", strings, fmt.Sprintf("%s %s %s", statusMap[originProblem.Status], "->", statusMap[problemObject["Status"].(uint)]))
			}
		}
		if _, ok := problemObject["Description"]; ok {
			if problemObject["Description"].(string) != originProblem.Description {
				if strings == "" {
					strings = fmt.Sprintf("%s %s %s\n", originProblem.Description, "->", problemObject["Description"].(string))
				} else {
					strings = fmt.Sprintf("%s%s\n", strings, fmt.Sprintf("%s %s %s", originProblem.Description, "->", problemObject["Description"].(string)))
				}
			}
		}
		if _, ok := problemObject["DiscoveredAt"]; ok {
			if problemObject["DiscoveredAt"].(string) != originProblem.DiscoveredAt {
				if strings == "" {
					strings = fmt.Sprintf("%s %s %s\n", originProblem.DiscoveredAt, "->", problemObject["DiscoveredAt"].(string))
				} else {
					strings = fmt.Sprintf("%s%s\n", strings, fmt.Sprintf("%s %s %s", originProblem.DiscoveredAt, "->", problemObject["DiscoveredAt"].(string)))
				}
			}
		}
		if _, ok := problemObject["PlanCloseAt"]; ok {
			if problemObject["PlanCloseAt"].(string) != originProblem.PlanCloseAt {
				if strings == "" {
					strings = fmt.Sprintf("%s %s %s\n", originProblem.PlanCloseAt, "->", problemObject["PlanCloseAt"].(string))
				} else {
					strings = fmt.Sprintf("%s%s\n", strings, fmt.Sprintf("%s %s %s", originProblem.PlanCloseAt, "->", problemObject["PlanCloseAt"].(string)))
				}
			}
		}
		if _, ok := problemObject["OwnerID"]; ok {
			if problemObject["OwnerID"].(uint) != originProblem.OwnerID {
				var users []*duser.ApprovalResponse
				if err := tx.Model(duser.Model()).Where("id in ?", []uint{originProblem.OwnerID, problemObject["OwnerID"].(uint)}).Find(&users).Error; err != nil {
					return err
				}
				var userMap = make(map[uint]*duser.ApprovalResponse)
				for _, user := range users {
					userMap[user.Id] = user
				}
				if strings == "" {
					strings = fmt.Sprintf("%s %s %s\n", userMap[originProblem.OwnerID].Name, "->", userMap[problemObject["OwnerID"].(uint)].Name)
				} else {
					strings = fmt.Sprintf("%s%s\n", strings, fmt.Sprintf("%s %s %s", userMap[originProblem.OwnerID].Name, "->", userMap[problemObject["OwnerID"].(uint)].Name))
				}
			}
		}
		if _, ok := problemObject["ProblemSourceID"]; ok {
			if problemObject["ProblemSourceID"].(uint) != originProblem.ProblemSourceID {
				var sources []*dproblemsource.Response
				if err := tx.Model(dproblemsource.Model()).Where("id in ?", []uint{originProblem.ProblemSourceID, problemObject["ProblemSourceID"].(uint)}).Find(&sources).Error; err != nil {
					return err
				}
				var sourceMap = make(map[uint]*dproblemsource.Response)
				for _, source := range sources {
					sourceMap[source.ID] = source
				}

				if strings == "" {
					strings = fmt.Sprintf("%s %s %s\n", sourceMap[originProblem.ProblemSourceID].Name, "->", sourceMap[problemObject["ProblemSourceID"].(uint)].Name)
				} else {
					strings = fmt.Sprintf("%s%s\n", strings, fmt.Sprintf("%s %s %s", sourceMap[originProblem.ProblemSourceID].Name, "->", sourceMap[problemObject["ProblemSourceID"].(uint)].Name))
				}
			}
		}
		if strings != "" {
			if err := tx.Model(dproblemproccess.Model()).Create(map[string]interface{}{
				"ProblemID":   originProblem.ID,
				"Description": strings,
				"UserID":      uId,
				"CreatedAt":   time.Now(),
			}).Error; err != nil {
				return err
			}
		}

		// 返回 nil 提交事务
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func UpdateProblemProccess(uId uint, id uint, proccessObject map[string]interface{}, fileObject map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		// 检查是否已经是历史版本，禁止更新历史版本
		originProblem := dproblem.Response{}
		if err := tx.Model(dproblem.Model()).Where("id = ?", id).Find(&originProblem).Error; err != nil {
			return err
		}
		if originProblem.ID == 0 {
			return errors.New("未找到该问题记录")
		}
		if _, ok := proccessObject["Description"]; ok {
			proccess := kpi.ProblemProccess{
				ProblemID:   originProblem.ID,
				Description: proccessObject["Description"].(string),
				UserID:      uId,
			}
			if err := tx.Model(dproblemproccess.Model()).Create(&proccess).Error; err != nil {
				return err
			}
			if fileObject != nil {
				fileObject["ProblemID"] = originProblem.ID
				fileObject["ProblemProccessID"] = proccess.ID
				if err := tx.Model(dattachment.Model()).Create(fileObject).Error; err != nil {
					return err
				}
			}
		} else {
			return errors.New("请输入进展描述")
		}
		object := map[string]interface{}{
			"UpdatedAt": time.Now(),
			"Status":    proccessObject["Status"],
		}

		// err = dao.Update(&drelease.Response{}, ctx, object)
		if err := UpdateProblem(uId, id, object); err != nil {
			return err
		}
		// if err := tx.Model(dproblem.Model()).Create(proccess).Error; err != nil {
		// 	return err
		// }
		// 返回 nil 提交事务
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}
