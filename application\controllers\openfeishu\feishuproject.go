package openfeishu

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/buildfarm/dpatchjob"
	"irisAdminApi/service/dao/datasync/dpmsproduct"
	"irisAdminApi/service/dao/feishu/dfeishuworkflowdocument"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	sdk "github.com/larksuite/project-oapi-sdk-golang"
	sdkcore "github.com/larksuite/project-oapi-sdk-golang/core"
	"github.com/larksuite/project-oapi-sdk-golang/service/attachment"
	"github.com/larksuite/project-oapi-sdk-golang/service/field"
	"github.com/larksuite/project-oapi-sdk-golang/service/project"
	"github.com/larksuite/project-oapi-sdk-golang/service/workitem"
)

type WorkItemInfo struct {
	ID                          int64
	WorkItemTypeKey             string
	ProjectName                 string
	PatchFile1                  string
	PatchName                   string
	PatchFileMD5                string
	PatchFileSize               float64
	RpmName                     string
	RpmFileMD5                  string
	RpmFileSize                 float64
	PatchType                   string
	patchFileDesc               string
	SubprocessPatchDesc         string
	PatchJobID                  string
	Patch1ApplicableModels      []string
	Patch1ApplicableVersions    []string
	Patch1HardwareVersion       interface{}
	IssuesResolvedInTestVersion string
	PatchFileName1              string
	ReleaseDate                 string
	ReleaseDate2                string
	ReleaseDescription          []interface{}
	ReleaseRegion               string
	TestReportAttachment        []interface{}
	TestReportUID               string
	TestReportPath              []string
	IssuesDescriptions          map[int]string // 存储问题描述，键为问题编号
	IssuesSources               map[int]string // 存储问题来源，键为问题编号
}

// 提取并解析字段的值到 WorkItemInfo 结构体中
func extractWorkItemInfo(item *workitem.WorkItemInfo) *WorkItemInfo {
	info := &WorkItemInfo{
		ID:                 item.ID,
		WorkItemTypeKey:    item.WorkItemTypeKey,
		IssuesDescriptions: make(map[int]string),
		IssuesSources:      make(map[int]string),
	}

	// 定义正则表达式，用于匹配并提取 bugId
	bugIdRegex := regexp.MustCompile(`bugId=(\d+)`)
	// 定义正则表达式，用于匹配并提取 jobId 的字符串
	jobIdRegex := regexp.MustCompile(`jobId=([^&]+)`)

	// 解析各个字段
	for _, field := range item.Fields {
		switch field.FieldAlias {
		case "patchFile1":
			if str, ok := field.FieldValue.(string); ok {
				info.PatchFile1 = str
				// 从URL中提取 jobId 的值
				matches := jobIdRegex.FindStringSubmatch(str)
				if len(matches) > 1 {
					info.PatchJobID = matches[1] // 提取的 jobId 值
				}
			} else {
				logging.ErrorLogger.Errorf("字段 patchFile1 的值不是字符串类型: %v", field.FieldValue)
			}
		case "patchFileName1":
			if str, ok := field.FieldValue.(string); ok {
				info.PatchFileName1 = str
			} else {
				logging.ErrorLogger.Errorf("字段 PatchFileName1 的值不是字符串类型: %v", field.FieldValue)
			}
		case "patch1ApplicableModels":
			info.Patch1ApplicableModels = parseFieldAsStringSlice(field.FieldValue)
		case "patch1ApplicableVersions":
			info.Patch1ApplicableVersions = parseFieldAsStringSlice(field.FieldValue)
		case "patch1HardwareVersion":
			info.Patch1HardwareVersion = field.FieldValue
		case "issuesResolvedInTestVersion":
			if str, ok := field.FieldValue.(string); ok {
				info.IssuesResolvedInTestVersion = str
			} else {
				logging.ErrorLogger.Errorf("字段 IssuesResolvedInTestVersion 的值不是字符串类型: %v", field.FieldValue)
			}
		case "releaseDate":
			if tsFloat, ok := field.FieldValue.(float64); ok {
				tsInt := int64(tsFloat)
				t := time.Unix(tsInt/1000, (tsInt%1000)*1000000) // 将毫秒时间戳转换为 time.Time
				info.ReleaseDate = t.Format("2006-01-02")        // 格式化时间为字符串
			} else {
				logging.ErrorLogger.Errorf("字段 ReleaseDate 的值不是有效的时间戳类型: %v", field.FieldValue)
			}
		case "releaseDescription":
			// 类型断言，检查是否是切片类型
			if descriptions, ok := field.FieldValue.([]interface{}); ok {
				info.ReleaseDescription = descriptions
			} else {
				logging.ErrorLogger.Errorf("字段 ReleaseDescription 的值不是有效的interface类型: %v", field.FieldValue)
			}
		case "testReportAttachment":
			// 类型断言，检查是否是切片类型
			if descriptions, ok := field.FieldValue.([]interface{}); ok {
				info.TestReportAttachment = descriptions
			} else {
				logging.ErrorLogger.Errorf("字段 TestReportAttachment 的值不是有效的interface类型: %v", field.FieldValue)
			}
		default:
			// 处理问题描述和来源
			if strings.HasPrefix(field.FieldAlias, "issue") && strings.Contains(field.FieldAlias, "Description") {
				num := extractIssueNumber(field.FieldAlias)
				info.IssuesDescriptions[num] = fmt.Sprintf("%v", field.FieldValue)
			} else if strings.HasPrefix(field.FieldAlias, "issue") && strings.Contains(field.FieldAlias, "Source") {
				num := extractIssueNumber(field.FieldAlias)
				// 从URL中提取 bugId 的值
				if str, ok := field.FieldValue.(string); ok {
					matches := bugIdRegex.FindStringSubmatch(str)
					if len(matches) > 1 {
						info.IssuesSources[num] = matches[1] // 提取的 bugId 值
					} else {
						logging.ErrorLogger.Errorf("字段 issue%dSource 的值不包含 bugId: %v", num, field.FieldValue)
					}
				}
			}
		}
	}

	return info
}

// 提取并解析字段的值到 WorkItemInfo 结构体中
func extractWorkItemInfos(item *workitem.WorkItemInfo) []*WorkItemInfo {
	// 创建一个切片存放多个 WorkItemInfo 实例
	var workItemInfos []*WorkItemInfo

	// 定义正则表达式，用于匹配并提取 bugId 和 jobId
	bugIdRegex := regexp.MustCompile(`bugId=(\d+)`)
	jobIdRegex := regexp.MustCompile(`jobId=([^&]+)`)

	// 查找 patchFileX 字段，X 从 1 开始递增
	for x := 1; ; x++ {
		patchFileKey := fmt.Sprintf("patchFile%d", x)
		patchFileNameKey := fmt.Sprintf("patchFileName%d", x)
		patchApplicableModelsKey := fmt.Sprintf("patch%dApplicableModels", x)
		patchApplicableVersionsKey := fmt.Sprintf("patch%dApplicableVersions", x)
		patchHardwareVersionKey := fmt.Sprintf("patch%dHardwareVersion", x)
		subprocessPatchDescKey := fmt.Sprintf("subprocessPatch%dDesc", x)
		releaseRegionKey := fmt.Sprintf("releaseRegion%d", x)

		// 查找当前的 patchFileX 字段，若无值则结束循环
		patchFile, found := getFieldValue(item, patchFileKey).(string)
		if !found || patchFile == "" {
			break
		}

		// 创建一个新的 WorkItemInfo 实例
		info := &WorkItemInfo{
			ID:                 item.ID,
			WorkItemTypeKey:    item.WorkItemTypeKey,
			IssuesDescriptions: make(map[int]string),
			IssuesSources:      make(map[int]string),
		}

		// 设置 patchFile 和 jobId
		info.PatchFile1 = patchFile
		if matches := jobIdRegex.FindStringSubmatch(patchFile); len(matches) > 1 {
			info.PatchJobID = matches[1]
		}

		// 设置 patchFileNameX
		if patchFileName, ok := getFieldValue(item, patchFileNameKey).(string); ok {
			info.PatchFileName1 = patchFileName
		}

		// 设置适用型号、适用版本和硬件版本
		info.Patch1ApplicableModels = parseFieldAsStringSlice(getFieldValue(item, patchApplicableModelsKey))
		info.Patch1ApplicableVersions = parseFieldAsStringSlice(getFieldValue(item, patchApplicableVersionsKey))
		info.Patch1HardwareVersion = getFieldValue(item, patchHardwareVersionKey)

		//版本发布时间改为当前时间
		info.ReleaseDate = time.Now().Format("2006-01-02")
		//因为格式February 14, 2025
		info.ReleaseDate2 = time.Now().Format("January 02, 2006")
		// 设置其他字段，如 issuesResolvedInTestVersion 和 releaseDate
		if resolvedVersion, ok := getFieldValue(item, "issuesResolvedInTestVersion").(string); ok {
			info.IssuesResolvedInTestVersion = resolvedVersion
		}

		// 设置 releaseRegion
		releaseRegion := extractLabelValue(getFieldValue(item, releaseRegionKey))
		if releaseRegion != "" {
			info.ReleaseRegion = releaseRegion
		} else {
			info.ReleaseRegion = "国内"
		}
		// 获取项目字段信息
		if projectName, ok := getFieldValue(item, "project").(string); ok {
			info.ProjectName = projectName
		}
		// if releaseDate, ok := getFieldValue(item, "releaseDate").(float64); ok {
		// 	info.ReleaseDate = time.Unix(int64(releaseDate)/1000, (int64(releaseDate)%1000)*1000000).Format("2006-01-02")
		// }
		if descriptions, ok := getFieldValue(item, "releaseDescription").([]interface{}); ok {
			info.ReleaseDescription = descriptions
		}
		if descriptions, ok := getFieldValue(item, "testReportAttachment").([]interface{}); ok {
			info.TestReportAttachment = descriptions
		}
		if patchDesc, ok := getFieldValue(item, subprocessPatchDescKey).(string); ok {
			info.SubprocessPatchDesc = patchDesc
		}

		// 处理问题描述和来源
		for _, field := range item.Fields {
			if strings.HasPrefix(field.FieldAlias, "issue") && strings.Contains(field.FieldAlias, "Description") {
				num := extractIssueNumber(field.FieldAlias)
				info.IssuesDescriptions[num] = fmt.Sprintf("%v", field.FieldValue)
			} else if strings.HasPrefix(field.FieldAlias, "issue") && strings.Contains(field.FieldAlias, "Source") {
				num := extractIssueNumber(field.FieldAlias)
				if source, ok := field.FieldValue.(string); ok {
					if matches := bugIdRegex.FindStringSubmatch(source); len(matches) > 1 {
						info.IssuesSources[num] = matches[1]
					}
				}
			}
		}

		// 将新生成的 WorkItemInfo 添加到切片中
		workItemInfos = append(workItemInfos, info)
	}

	return workItemInfos
}

// getFieldValue 用于根据字段名称在 item.Fields 中获取值
func getFieldValue(item *workitem.WorkItemInfo, fieldAlias string) interface{} {
	for _, field := range item.Fields {
		if field.FieldAlias == fieldAlias {
			return field.FieldValue
		}
	}
	return nil
}

// extractLabelValue 从map中提取label对应的值
func extractLabelValue(value interface{}) string {
	if value == nil {
		return ""
	}

	// 尝试将value转换为map[string]interface{}
	if mapValue, ok := value.(map[string]interface{}); ok {
		// 检查是否存在label键
		if labelValue, exists := mapValue["label"]; exists {
			// 将label值转换为字符串
			if strValue, ok := labelValue.(string); ok {
				return strValue
			}
		}
	}

	return ""
}

// 从别名中提取问题编号
func extractIssueNumber(alias string) int {
	var num int
	fmt.Sscanf(alias, "issue%dDescription", &num)
	if num == 0 {
		fmt.Sscanf(alias, "issue%dSource", &num)
	}
	return num
}

// 解析接口类型为字符串切片
func parseFieldAsStringSlice(value interface{}) []string {
	var labels []string
	if fieldValue, ok := value.([]interface{}); ok {
		for _, m := range fieldValue {
			if mMap, ok := m.(map[string]interface{}); ok {
				if label, ok := mMap["label"].(string); ok {
					labels = append(labels, label)
				}
			}
		}
	}
	return labels
}

// 获取工作项数据并返回 WorkItemInfo 数组，支持分页，每次分页请求等待300毫秒
func GetWorkItemInfos(workItemName, projectKey string) ([]WorkItemInfo, error) {
	client := sdk.NewClient("MII_655ECEC646C34003", "45D123B43E4F5E15BE6733EBD6252882")
	pageNum := int64(1)
	pageSize := int64(50)
	var allWorkItems []WorkItemInfo

	for {
		// 进行分页请求
		resp, err := client.WorkItem.Filter(context.Background(),
			workitem.NewFilterReqBuilder().
				WorkItemName(workItemName).
				ProjectKey(projectKey).
				PageSize(pageSize).
				PageNum(pageNum).
				WorkItemTypeKeys([]string{"654d85ba26c1f896d02f5bae"}).
				Build(),
			sdkcore.WithUserKey("7184385419352621084"))

		// 错误处理
		if err != nil {
			return nil, err
		}
		if !resp.Success() {
			return nil, fmt.Errorf("请求失败: %d - %s", resp.Code(), resp.ErrMsg)
		}

		// 处理符合条件的工作项数据
		for _, item := range resp.Data {
			// 检查当前节点是否符合条件
			if len(item.CurrentNodes) > 0 && item.CurrentNodes[0].ID == "state_7" {
				// 调用 extractWorkItemInfos，返回多个 WorkItemInfo 实例
				workItemInfos := extractWorkItemInfos(item)
				//保存符合条件的工作项数据 获取项目名称

				// 遍历返回的 WorkItemInfo 实例切片
				for _, info := range workItemInfos {
					// 检查 ReleaseDescription 是否为空
					if info.ReleaseDescription == nil || len(info.ReleaseDescription) == 0 {
						allWorkItems = append(allWorkItems, *info) // 追加符合条件的 WorkItemInfo
					}
				}
			}
		}

		// 判断是否有更多分页数据
		if pageNum*pageSize >= resp.Pagination.Total {
			break
		}

		// 等待300毫秒
		time.Sleep(300 * time.Millisecond)
		pageNum++ // 获取下一页
	}
	return allWorkItems, nil
}

// 改造后的方法：测试获取多个流程的工作项数据
func GetWorkItemData() {
	// 定义流程列表
	workflowNames := []string{"补丁1流程", "补丁2流程", "补丁3流程", "补丁4流程", "补丁5流程", "补丁6流程"}
	projectKey := "627ca6c56073f523bfc21d4d"
	// 遍历流程列表
	for _, workflowName := range workflowNames {
		// 请求当前流程名称的工作项数据=> 返回WorkItemInfo数组 一个workflow生成出多个补丁的WorkItemInfo
		workItems, err := GetWorkItemInfos(workflowName, projectKey)
		if err != nil {
			fmt.Printf("获取工作项数据失败 (%s): %v\n", workflowName, err)
			continue
		}
		if len(workItems) == 0 {
			fmt.Printf("没有找到符合要求的工作项 (%s)\n", workflowName)
			continue
		}

		// 处理 WorkItemInfo 数组数据
		for _, info := range workItems {
			// 根据 WorkItemInfo 进行进一步的业务逻辑处理
			patchJob := &dpatchjob.Response{}
			err := patchJob.FindPatchJob(info.PatchJobID)
			if err != nil {
				logging.ErrorLogger.Errorf("get patchJob err ", err)
				continue
			}
			fileNameWithExt := filepath.Base(patchJob.RPMFileName)
			fileExt := filepath.Ext(fileNameWithExt)
			fileName := strings.TrimSuffix(fileNameWithExt, fileExt)
			info.PatchName = fileName
			info.PatchFileName1 = fileName + ".zip"

			info.RpmName = patchJob.RPMFileName
			info.RpmFileMD5 = patchJob.RPMFileMd5
			info.RpmFileSize = float64(patchJob.RPMFileSize)

			//从数据库中获取软件版本
			if patchJob.SoftwareVersion != "" {
				info.Patch1ApplicableVersions = strings.Split(patchJob.SoftwareVersion, "|")
			}
			// 判断补丁类型
			if patchJob.PatchType == 0 {
				info.PatchType = "组件补丁"
				info.patchFileDesc = "组件补丁安装包"
			}
			if patchJob.PatchType == 1 {
				info.PatchType = "函数补丁"
				info.patchFileDesc = "函数补丁安装包"
			}

			if patchJob.PatchType == 2 {
				info.PatchType = "补丁集合包"
				info.patchFileDesc = "补丁集合包安装包"
			}

			// 下载文件并获取大小和MD5值
			// 创建临时存放文件夹
			var tempDir = libs.Config.FeiShuDoc.Temp + "feishu/" + time.Now().Format("20060102") + "/"
			err = os.MkdirAll(tempDir, 0750)
			os.Chmod(tempDir, 0750)
			if err != nil {
				logging.ErrorLogger.Errorf(fmt.Sprintf("Error while downloading: %s", err.Error()))
				continue
			}
			defer os.Remove(filepath.Join(tempDir, info.PatchFileName1)) // 最后删除临时文件
			err = downloadFile(info.PatchFile1, filepath.Join(tempDir, info.PatchFileName1))
			if err != nil {
				fmt.Println("下载文件失败:", err)
				continue
			}
			PatchFileMd5, _ := libs.GetFileMd5(filepath.Join(tempDir, info.PatchFileName1))
			PatchFileSize := getFileSize(filepath.Join(tempDir, info.PatchFileName1))
			info.PatchFileMD5 = PatchFileMd5
			info.PatchFileSize = float64(PatchFileSize)

			//获取下载测试报告附件的UID
			var testReportPaths []string
			for _, attachment := range info.TestReportAttachment {
				if attachmentMap, ok := attachment.(map[string]interface{}); ok {
					testReportUID := attachmentMap["uid"].(string)
					if len(testReportUID) > 0 {
						client := sdk.NewClient("MII_655ECEC646C34003", "45D123B43E4F5E15BE6733EBD6252882")
						workItemTypeKey := info.WorkItemTypeKey
						workItemID := info.ID
						testReportPath, err := downloadAttachmentFile(client, projectKey, workItemTypeKey, tempDir, testReportUID, workItemID)
						if err != nil {
							fmt.Println("下载文件失败:", err)
							continue
						}
						testReportPaths = append(testReportPaths, testReportPath)
					}
				}
			}
			info.TestReportPath = testReportPaths

			// 构建替换关键词映射表=>生成补丁*型号的关键词并生成文档
			replacements := createReplacements(info)
			var outputFiles []string
			if len(replacements) > 0 {
				for _, v := range replacements {
					// 在这里使用 v 进行其他操作，如保存到数据库、生成文档等
					inputFile := v["inputFile"]
					delete(v, "inputFile")
					idStr := strconv.FormatInt(info.ID, 10)
					outputDir := filepath.Join(libs.Config.FeiShuDoc.OutputPath, idStr)
					// 创建输出目录
					err := os.MkdirAll(outputDir, 0750)
					if err != nil {
						fmt.Println("Error creating output directory:", err)
						continue
					}
					//根据ReleaseRegion来命名文件名称 如果ReleaseRegion为空 或等于"海外"，则使用英文名称 如果ReleaseRegion为国内，则使用中文名称
					var outputFile string
					if info.ReleaseRegion == "海外" {
						outputFile = filepath.Join(outputDir, v["{{title1}}"]+v["{{title2}}"]+v["{{patchName}}"]+"Patch Release Notes (V1.0).docx")
					} else {
						outputFile = filepath.Join(outputDir, v["{{title1}}"]+v["{{title2}}"]+v["{{patchName}}"]+"补丁发行说明(V1.0).docx")
					}
					replacementsJSON, err := json.Marshal(v)
					if err != nil {
						fmt.Println("Error marshalling replacements:", err)
						continue
					}
					shell := filepath.Join(libs.Config.FeiShuDoc.Plugin, "modify_docx.py")
					command := fmt.Sprintf("python3 %s %s '%s' '%s'", shell, inputFile, outputFile, replacementsJSON)
					output, err := libs.ExecCommand(command)
					if err != nil {
						fmt.Println("Error executing command:", err)
					}
					fmt.Println(string(output))
					// 上传文件
					client := sdk.NewClient("MII_655ECEC646C34003", "45D123B43E4F5E15BE6733EBD6252882")
					workItemTypeKey := info.WorkItemTypeKey
					workItemID := info.ID
					fieldAlias := "releaseDescription"
					err = uploadAttachment(client, projectKey, workItemTypeKey, outputFile, fieldAlias, workItemID)
					if err != nil {
						fmt.Printf("Error uploading attachment: %v\n", err)
					}
					outputFiles = append(outputFiles, outputFile)
				}
				workflowDocuments := []map[string]interface{}{}
				//逗号分割outputFiles
				outputFilesStr := strings.Join(outputFiles, "|")
				applicableModels := strings.Join(info.Patch1ApplicableModels, "|")
				applicableVersions := strings.Join(info.Patch1ApplicableVersions, "|")

				workflowDocuments = append(workflowDocuments, map[string]interface{}{
					"WorkflowID":                info.ID,
					"ProjectKey":                projectKey,
					"WorkItemTypeKey":           info.WorkItemTypeKey,
					"ProjectName":               info.ProjectName,
					"PatchFile":                 info.PatchName,
					"PatchApplicableModels":     applicableModels,
					"PatchApplicableVersions":   applicableVersions,
					"IssuesResolvedTestVersion": info.IssuesResolvedInTestVersion,
					"DocFilePath":               outputFilesStr,
					"Status":                    0,

					"CreatedAt": time.Now(),
					"UpdatedAt": time.Now(),
				})
				//保存补丁发行说明数据到数据库中
				dfeishuworkflowdocument.CreateOrUpdateDocumentCommentData(workflowDocuments)

			}
		}
	}
}

func createReplacements(info WorkItemInfo) []map[string]string {
	// 用于存储不同分类的映射表切片
	var replacements []map[string]string
	// 定义分类切片
	var zModelsNoS, zModelsWithS, eModels, nModels, pModels []string
	// 遍历并分类处理模型
	for _, model := range info.Patch1ApplicableModels {
		switch {
		case strings.HasPrefix(model, "Z") && !strings.HasSuffix(model, "S"):
			zModelsNoS = append(zModelsNoS, "RG-WALL 1600-"+model)
		case strings.HasPrefix(model, "Z") && strings.HasSuffix(model, "S"):
			zModelsWithS = append(zModelsWithS, "RG-WALL 1600-"+model)
		case strings.HasPrefix(model, "E"):
			eModels = append(eModels, "RG-EG-"+model)
		case strings.HasPrefix(model, "N"):
			nModels = append(nModels, "RG-NBR-"+model)
		case strings.HasPrefix(model, "P"):
			pModels = append(pModels, "RG-IDP-"+model)
		}
	}

	// 创建每类的映射表并填入 `patchModel` 和 `title1`
	template1 := libs.Config.FeiShuDoc.Template + "temp1.docx"
	template2 := libs.Config.FeiShuDoc.Template + "temp2.docx"
	template3 := libs.Config.FeiShuDoc.Template + "temp3.docx"
	template4 := libs.Config.FeiShuDoc.Template + "temp4.docx"
	template5 := libs.Config.FeiShuDoc.Template + "temp5.docx"
	template6 := libs.Config.FeiShuDoc.Template + "temp6.docx"

	//提取测试报告中的图片信息
	imageInfos, err := ProcessOCR(info.TestReportPath, info.PatchName)
	if err != nil {
		fmt.Println("提取图片信息失败")
	}
	imageMap := make(map[string]map[string]string)
	for _, image := range imageInfos {
		if _, ok := imageMap[image.Category]; !ok {
			imageMap[image.Category] = make(map[string]string)
		}
		imageMap[image.Category]["LocalImport"] = image.LocalImport
		imageMap[image.Category]["OnlineUpdate"] = image.OnlineUpdate
	}

	firewallSeriesLocalImport := imageMap["FirewallSeries"]["LocalImport"]
	firewallSeriesOnlineUpdate := imageMap["FirewallSeries"]["OnlineUpdate"]
	fifthGenNBRGatewayLocalImport := imageMap["FifthGenNBRGateway"]["LocalImport"]
	fifthGenNBRGatewayOnlineUpdate := imageMap["FifthGenNBRGateway"]["OnlineUpdate"]
	nextGenSmartGatewayLocalImport := imageMap["NextGenSmartGateway"]["LocalImport"]
	nextGenSmartGatewayOnlineUpdate := imageMap["NextGenSmartGateway"]["OnlineUpdate"]
	idpSystemLocalImport := imageMap["IDPSystem"]["LocalImport"]
	idpSystemOnlineUpdate := imageMap["IDPSystem"]["OnlineUpdate"]

	if len(zModelsNoS) > 0 {
		replacements = append(replacements, createCategoryReplacement("防火墙", "RG-WALL 1600-Z系列", "新一代防火墙", template1, zModelsNoS, strings.Join(zModelsNoS, "\n"), firewallSeriesLocalImport, firewallSeriesOnlineUpdate))
	}
	if len(zModelsWithS) > 0 {
		if info.ReleaseRegion == "海外" {
			replacements = append(replacements, createCategoryReplacement("Firewall", "RG-WALL 1600-Z-S Series", "Cloud-Managed Firewall ", template6, zModelsWithS, strings.Join(zModelsWithS, "\n"), firewallSeriesLocalImport, firewallSeriesOnlineUpdate))
		} else {
			replacements = append(replacements, createCategoryReplacement("防火墙", "RG-WALL 1600-Z-S系列", "Cloud Management Firewall", template2, zModelsWithS, strings.Join(zModelsWithS, "\n"), firewallSeriesLocalImport, firewallSeriesOnlineUpdate))
		}
	}
	if len(eModels) > 0 {
		replacements = append(replacements, createCategoryReplacement("网关", "RG-EG-E系列", "下一代智能网关", template4, eModels, strings.Join(eModels, "\n"), nextGenSmartGatewayLocalImport, nextGenSmartGatewayOnlineUpdate))
	}
	if len(nModels) > 0 {
		replacements = append(replacements, createCategoryReplacement("网关", "RG-NBR-N7000系列网关", "", template3, nModels, strings.Join(nModels, "\n"), fifthGenNBRGatewayLocalImport, fifthGenNBRGatewayOnlineUpdate))
	}

	if len(pModels) > 0 {
		replacements = append(replacements, createCategoryReplacement("入侵防御检测", "RG-IDP-P系列", "入侵检测防御系统", template5, pModels, strings.Join(pModels, "\n"), idpSystemLocalImport, idpSystemOnlineUpdate))
	}

	softwareVersion := strings.Join(info.Patch1ApplicableVersions, "\n")
	// 添加通用信息
	commonReplacement := map[string]string{
		"{{patchName}}":       info.PatchName,
		"{{time}}":            info.ReleaseDate,
		"{{time2}}":           info.ReleaseDate2,
		"{{patchType}}":       info.PatchType,
		"{{softwareVersion}}": softwareVersion,
		"{{patchDesc}}":       info.IssuesResolvedInTestVersion,
		"{{patchFileName}}":   info.PatchFileName1,
		"{{patchFileDesc}}":   info.patchFileDesc,
		"{{patchFileSize}}":   FormatWithComma(info.PatchFileSize, 0),
		"{{patchFileMD5}}":    info.PatchFileMD5,
		"{{rpmName}}":         info.RpmName,
		"{{rpmFileSize}}":     FormatWithComma(info.RpmFileSize, 0),
		"{{rpmFileMD5}}":      info.RpmFileMD5,
	}

	// 将通用信息合并到每个映射表
	for i := range replacements {
		for k, v := range commonReplacement {
			replacements[i][k] = v
		}
	}
	maxIssues := 10 // 最大问题数量限制
	// 动态添加bugID和bugDesc信息
	for i := 1; i <= maxIssues; i++ {
		bugID, idExists := info.IssuesSources[i]
		bugDesc, descExists := info.IssuesDescriptions[i]
		for j := range replacements {
			if idExists {
				replacements[j][fmt.Sprintf("{{bugID%d}}", i)] = bugID
			} else {
				replacements[j][fmt.Sprintf("{{bugID%d}}", i)] = "NA" // 未填充项为空字符串
			}
			if descExists {
				replacements[j][fmt.Sprintf("{{bugDesc%d}}", i)] = bugDesc
			} else {
				replacements[j][fmt.Sprintf("{{bugDesc%d}}", i)] = ""
			}
		}
	}

	return replacements
}

// createCategoryReplacement 根据模型类型和硬件类型创建特定类别的替换映射
func createCategoryReplacement(hardType, title1, title2, inputFile string, models []string, patchModel, localImgsrc, onlineImgsrc string) map[string]string {
	replacement := make(map[string]string)
	maxEntries := 10 // 最大填充数量

	// 填充实际模型数据
	for i := 0; i < maxEntries; i++ {
		if i < len(models) {
			model := models[i]
			replacement[fmt.Sprintf("{{hardType%d}}", i+1)] = hardType
			replacement[fmt.Sprintf("{{hardModel%d}}", i+1)] = model

			// 查找模型对应的补丁版本
			productData, err := dpmsproduct.GetPmsProductVersion(model)
			if err != nil {
				logging.ErrorLogger.Errorf("get GetPmsProductVersion status err: %v", err)
				replacement[fmt.Sprintf("{{hardVersion%d}}", i+1)] = "" // 设置为空以防止因错误导致内容缺失
				continue
			}
			// 去重并提取版本
			var versionList []string
			for _, product := range productData {
				// product.Version 不能包含X字符串
				if strings.Contains(product.Version, "X") {
					continue
				}
				if !contains(versionList, product.Version) {
					versionList = append(versionList, product.Version)
				}
			}
			replacement[fmt.Sprintf("{{hardVersion%d}}", i+1)] = strings.Join(versionList, "\n")
		} else {
			// 超过models长度则置空
			replacement[fmt.Sprintf("{{hardType%d}}", i+1)] = ""
			replacement[fmt.Sprintf("{{hardModel%d}}", i+1)] = ""
			replacement[fmt.Sprintf("{{hardVersion%d}}", i+1)] = ""
		}
	}
	// 设置其余字段
	replacement["{{title1}}"] = title1
	replacement["{{title2}}"] = title2
	replacement["inputFile"] = inputFile
	replacement["{{patchModel}}"] = patchModel
	replacement["{{localImgsrc}}"] = localImgsrc
	replacement["{{onlineImgsrc}}"] = onlineImgsrc
	return replacement
}

func TestDocFile3() {
	replacements := map[string]string{
		"{{title1}}":          "新一代防火墙",
		"{{title2}}":          "RG-WALL 1600-Z系列防火墙",
		"{{patchName}}":       "HP001174",
		"{{time}}":            "2024-04-08",
		"{{patchType}}":       "组件补丁",
		"{{patchModel}}":      "RG-WALL 1600-Z3200\nRG-WALL 1600-Z3200-S\nRG-WALL 1600-Z5100\nRG-WALL 1600-Z5100-S",
		"{{softwareVersion}}": "NGFW_NTOS 1.0R6P2",
		"{{patchDesc}}":       "解决R6系列版本，安全日志页面Web页面查询慢的问题。",
		"{{bugID1}}":          "1115357",
		"{{bugDesc1}}":        "解决R6系列版本，安全日志页面Web页面查询慢的问题。",
		"{{hardType1}}":       "防火墙",
		"{{hardModel1}}":      "RG-WALL 1600-Z5100",
		"{{hardVersion1}}":    "V1.00\nV1.01\nV1.02",
		"{{hardType2}}":       "防火墙",
		"{{hardModel2}}":      "RG-WALL 1600-Z5100-S",
		"{{hardVersion2}}":    "V1.00\nV1.01\nV1.02",
		"{{hardType3}}":       "防火墙",
		"{{hardModel3}}":      "RG-WALL 1600-Z3200",
		"{{hardVersion3}}":    "V1.00\nV1.01\nV1.02\nV1.03",
		"{{hardType4}}":       "防火墙",
		"{{hardModel4}}":      "RG-WALL 1600-Z3200-S",
		"{{hardVersion4}}":    "V1.00\nV1.01\nV1.02\nV1.03",
		"{{patchFileName}}":   "HP001174.zip",
		"{{patchFileDesc}}":   "组件补丁安装包",
		"{{patchFileSize}}":   "19194578",
		"{{patchFileMD5}}":    "55deceaed524e8da3980bc25ea8818d6",
	}
	inputFile := "/tmp/temp4.docx"
	outputFile := "/tmp/test4.docx"

	replacementsJSON, err := json.Marshal(replacements)
	if err != nil {
		fmt.Println("Error marshalling replacements:", err)
		return
	}
	shell := filepath.Join(libs.Config.FeiShuDoc.Plugin, "modify_docx.py")
	command := fmt.Sprintf("python3 %s %s %s '%s'", shell, inputFile, outputFile, replacementsJSON)
	output, err := libs.ExecCommand(command)
	if err != nil {
		fmt.Println(string(output))
	}
	fmt.Println(string(output))
}

// 创建更新工作项请求
func updateWorkItemWithFields(client *sdk.Client, projectKey, workItemTypeKey string, workItemID int64, updateFields []*field.FieldValuePair) error {
	// 创建更新字段请求
	req := workitem.NewUpdateWorkItemReqBuilder().
		ProjectKey(projectKey).
		WorkItemTypeKey(workItemTypeKey).
		WorkItemID(workItemID).
		UpdateFields(updateFields).
		Build()

	// 发送请求更新字段
	resp, err := client.WorkItem.UpdateWorkItem(context.Background(), req)
	if err != nil {
		return fmt.Errorf("failed to update task field: %v", err)
	}

	fmt.Printf("Task field updated successfully: %v\n", resp)
	return nil
}

func uploadAttachment(client *sdk.Client, projectKey, workItemTypeKey, filePath, fieldAlias string, workItemID int64) error {
	// 打开文件
	fmt.Println("Opening file:", filePath)
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("failed to open file: %v", err)
	}
	defer file.Close()

	// 创建附件上传请求 NewUploadAttachmentReqBuilder
	req := attachment.NewUploadAttachmentReqBuilder().
		ProjectKey(projectKey).
		WorkItemTypeKey(workItemTypeKey).
		WorkItemID(workItemID).
		FileWithFileName(filepath.Base(filePath), file).
		FileMimeType(".docx").
		FieldAlias(fieldAlias).Build()

	// 发送附件上传请求
	resp, err := client.Attachment.UploadAttachment(context.Background(), req, sdkcore.WithUserKey("7184385419352621084"))
	if err != nil {
		return fmt.Errorf("failed to upload attachment: %v", err)
	}

	fmt.Printf("Attachment uploaded successfully: %v\n", resp)
	return nil
}

func downloadAttachmentFile(client *sdk.Client, projectKey, workItemTypeKey, saveDir, uuid string, workItemID int64) (string, error) {
	// 构建下载附件请求
	req := attachment.NewDownloadAttachmentReqBuilder().
		ProjectKey(projectKey).
		WorkItemTypeKey(workItemTypeKey).
		WorkItemID(workItemID).
		UUID(uuid).
		Build()

	// 发起下载请求
	resp, err := client.Attachment.DownloadAttachment(context.Background(), req, sdkcore.WithUserKey("7184385419352621084"))
	if err != nil {
		return "", fmt.Errorf("failed to download attachment: %v", err)
	}
	defer func() {
		if closer, ok := resp.File.(io.Closer); ok {
			closer.Close()
		}
	}()

	// 构建文件完整路径
	fullPath := filepath.Join(saveDir, resp.FileName)
	//删除文件
	os.Remove(fullPath)

	// 创建本地文件用于保存下载的内容
	outFile, err := os.Create(fullPath)
	if err != nil {
		return "", fmt.Errorf("failed to create local file %s: %v", fullPath, err)
	}
	defer outFile.Close()

	// 将下载内容写入本地文件
	if _, err := io.Copy(outFile, resp.File); err != nil {
		return "", fmt.Errorf("failed to save attachment to %s: %v", fullPath, err)
	}

	fmt.Printf("Attachment downloaded successfully to %s\n", fullPath)
	return fullPath, nil
}

// 获取空间下工作项类型
func listProjectWorkItemType(client *sdk.Client) {
	resp, err := client.Project.ListProjectWorkItemType(context.Background(), project.NewListProjectWorkItemTypeReqBuilder().
		ProjectKey("project_key").
		Build(),
		sdkcore.WithUserKey("user_key"),
	)

	//处理错误
	if err != nil {
		// 处理err
		return
	}

	// 服务端错误处理
	if !resp.Success() {
		//fmt.Println(resp.ErrCode, resp.ErrMsg, resp.RequestId())
		return
	}

	// 业务数据处理
	fmt.Println(sdkcore.Prettify(resp.Data))
}

func TestToSendMesage() {
	// 定义通知评审人员的消息模板
	reviewerMessage := fmt.Sprintf(
		`有一个新的合并请求，具体信息如下：
- 项目名称：%s
- 合并请求标题：%s
- 源分支：%s
- 目标分支：%s
- 提交人：%s
- 提交时间：%s
链接：%s
`,
		"项目名称",            // 项目名称
		"MR标题",            // 合并请求标题
		"源分支名称",           // 源分支
		"目标分支名称",          // 目标分支
		"提交人名称",           // 提交人
		"提交时间",            // 提交时间
		"GitLab合并请求页面URL", // MR链接
	)

	mailTo := []string{"<EMAIL>"}
	subject := fmt.Sprintf("[MR表单系统]代码合并请求通知 - MR: %s", "[新增][WEB服务-标签管理][安全云2.8.0]")
	err := libs.SendCardMessage(mailTo, subject, reviewerMessage, []string{})
	if err != nil {
		fmt.Println(err)
	}

}

type ImageInfo struct {
	Category     string `json:"category"`
	OnlineUpdate string `json:"OnlineUpdate"`
	LocalImport  string `json:"LocalImport"`
}

func TestOCR() {
	docPath := "/tmp/modified_temp3.docx"
	shell := filepath.Join(libs.Config.FeiShuDoc.Plugin, "process_images.py")
	command := fmt.Sprintf("python3 %s %s", shell, docPath)
	output, err := libs.ExecCommand(command)
	if err != nil {
		fmt.Println("Error executing command:", err)
	}
	fmt.Println(string(output))

	// 解析 Python 脚本的 JSON 输出
	var imageInfo []ImageInfo
	if err := json.Unmarshal([]byte(output), &imageInfo); err != nil {
		fmt.Println("Error parsing JSON:", err)
		return
	}

	// 打印分类信息
	for _, info := range imageInfo {
		fmt.Printf("Category: %s\n", info.Category)
		fmt.Printf("OnlineUpdate: %s\n", info.OnlineUpdate)
		fmt.Printf("LocalImport: %s\n", info.LocalImport)
	}
}

// ProcessOCR 处理 OCR 并返回图像信息
func ProcessOCR(docPaths []string, patchName string) ([]ImageInfo, error) {
	var allImageInfo []ImageInfo
	for _, docPath := range docPaths {
		fmt.Println("Processing:", docPath)
		fmt.Println("patchName:", patchName)
		if strings.Contains(docPath, patchName) { //查找包含补丁名称的文件去提取信息
			shell := filepath.Join(libs.Config.FeiShuDoc.Plugin, "process_images.py")
			command := fmt.Sprintf("python3 %s '%s' '%s'", shell, docPath, patchName)
			output, err := libs.ExecCommand(command)
			if err != nil {
				fmt.Println("Error executing command:", err)
				continue
			}

			var imageInfo []ImageInfo
			if err := json.Unmarshal([]byte(output), &imageInfo); err != nil {
				fmt.Println("Error parsing JSON:", err)
				continue
			}
			allImageInfo = append(allImageInfo, imageInfo...)
		}
	}
	return allImageInfo, nil
}
