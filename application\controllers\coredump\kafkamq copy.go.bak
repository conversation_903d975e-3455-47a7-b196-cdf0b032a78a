package coredump

import (
	"context"
	"encoding/json"
	"fmt"
	"irisAdminApi/application/controllers/featurerelease"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/coredump/dcoredumpjob"
	"irisAdminApi/service/dao/coredump/dcoredumptechsupport"
	"log"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/segmentio/kafka-go"
)

func TestTopic() {
	conn, err := kafka.Dial("tcp", "121.41.9.85:9093")
	if err != nil {
		panic(err.Error())
	}
	defer conn.Close()

	partitions, err := conn.ReadPartitions()
	if err != nil {
		panic(err.Error())
	}

	m := map[string]struct{}{}
	// 遍历所有分区取topic
	for _, p := range partitions {
		m[p.Topic] = struct{}{}
	}
	for k := range m {
		fmt.Println(k)
	}

}

// readByReader 通过Reader接收消息
func ReadByReader() {
	//创建Reader
	r := kafka.NewReader(kafka.ReaderConfig{
		Brokers:   []string{"10.51.134.132:9092"},
		Topic:     "coredump_info",
		Partition: 0,
		MaxBytes:  10e6, // 10MB
	})
	r.SetOffset(kafka.FirstOffset) // 设置Offset
	// 接收消息
	for {
		m, err := r.ReadMessage(context.Background())
		if err != nil {
			fmt.Println("failed to reader:", err.Error())
			break
		}
		fmt.Printf("message at offset %d: %s = %s\n", m.Offset, string(m.Key), string(m.Value))
		err = ProcessCoreDump(m)
		if err != nil {
			coreDumpInfo, _ := decodeJSON(m.Value)
			//失败消息通知
			sendNotification(coreDumpInfo, []CoredumpItem{}, false)
			fmt.Println("处理消息失败:", err.Error())
			logging.ErrorLogger.Errorf("处理消息失败: %s", err.Error())
			break
		}
	}
	// 程序退出前关闭Reader
	if err := r.Close(); err != nil {
		fmt.Println("failed to close reader:", err.Error())
		log.Fatal("failed to close reader:", err)
	}

}

// readByReader 通过Reader接收消息
func ReadByReaderGroupID() {
	// 创建一个reader，指定GroupID，从 topic-A 消费消息
	r := kafka.NewReader(kafka.ReaderConfig{
		Brokers:     []string{"10.51.134.132:9092"},
		GroupID:     "test2", // 指定消费者组id
		Topic:       "coredump_info",
		MaxBytes:    10e6,              // 10MB
		StartOffset: kafka.FirstOffset, // 从头开始读取消息
	})

	defer func() {
		if err := r.Close(); err != nil {
			log.Fatalf("failed to close reader: %v", err)
		}
	}()

	// 接收消息
	for {
		m, err := r.FetchMessage(context.Background())
		if err != nil {
			fmt.Println("读取消息失败:", err.Error())
			break
		}
		fmt.Printf("消息在 topic/partition/offset %v/%v/%v: %s = %s\n", m.Topic, m.Partition, m.Offset, string(m.Key), string(m.Value))

		// 根据消息内容下载文件

		// 手动提交消息偏移量
		// if err := r.CommitMessages(context.Background(), m); err != nil {
		// 	fmt.Println("提交消息偏移量失败:", err.Error())
		// }
	}

	// 程序退出前关闭Reader
	if err := r.Close(); err != nil {
		// log.Fatal("failed to close reader:", err)
		fmt.Println("关闭reader失败:", err.Error())
	}

}

// writeByConn 基于Conn发送消息
func WriteByConn() {
	topic := "coredump_info"
	partition := 0

	// 连接至Kafka集群的Leader节点
	conn, err := kafka.DialLeader(context.Background(), "tcp", "121.41.9.85:9093", topic, partition)
	if err != nil {
		fmt.Println("failed to dial leader:", err.Error())
	}
	defer conn.Close()
	// 设置发送消息的超时时间
	conn.SetWriteDeadline(time.Now().Add(10 * time.Second))

	// 发送消息
	_, err = conn.WriteMessages(
		kafka.Message{Value: []byte("one!")},
		kafka.Message{Value: []byte("two!")},
		kafka.Message{Value: []byte("three!")},
	)
	if err != nil {
		fmt.Println("failed to write messages:", err.Error())
	}

	// 关闭连接
	if err := conn.Close(); err != nil {
		fmt.Println("failed to close writer:", err.Error())
	}
}

func InitSandBoxBykafka(techSupportFile, product, tempName string) error {
	// bash gdb.sh root 127.0.0.1 9022 /tmp/tech-support-2022-09-09_11-06-39.tgz  z5100-s 20230202317009999 /mnt/sata0
	techSupport := dcoredumptechsupport.Response{}
	err := techSupport.FindEx("job_id", tempName)
	if err != nil {
		logging.ErrorLogger.Errorf("find techsupport file err ", err)
		return err
	}
	if techSupport.ID == 0 {
		logging.ErrorLogger.Errorf("techsupport file not found ")
		return err
	}
	if libs.Config.CoreDump.Upload == "" {
		logging.ErrorLogger.Errorf("未配置工作目录 ")
		return err
	}
	outputDir := filepath.Join(libs.Config.CoreDump.Upload, techSupport.CreatedAt.Format("20060102"), techSupport.JobID)
	err = os.MkdirAll(outputDir, 0750)
	os.Chmod(outputDir, 0750)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		return err
	}
	logFile := filepath.Join(outputDir, fmt.Sprintf("%s_%s.log", filepath.Base(techSupport.FileName), techSupport.JobID))
	f, err := os.OpenFile(logFile, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		logging.ErrorLogger.Errorf("create coredump job err ", err)
		return err
	}
	defer f.Close()

	initGdb := filepath.Join(libs.Config.CoreDump.Plugin, "init_gdb.sh")
	command := fmt.Sprintf(`bash "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s"`, initGdb, libs.Config.CoreDump.Username, libs.Config.CoreDump.Host, libs.Config.CoreDump.Port, techSupportFile, product, tempName, libs.Config.CoreDump.RemoteWorkDir)
	output, err := libs.ExecCommand(command)
	_, err2 := f.Write([]byte(output))
	if err2 != nil {
		logging.ErrorLogger.Errorf("write log err ", err)
		return err
	}
	// 获取远程工作目录  配置目录+文件名(去除扩展名)+'_'+tempName+"/temp"
	fileName := strings.TrimSuffix(filepath.Base(techSupportFile), filepath.Ext(techSupportFile))
	remoteWorkDir := libs.Config.CoreDump.RemoteWorkDir + "/" + fileName + "_" + tempName + "/temp"
	// 解析产品信息
	productName := extractProductName(output)
	//更新产品名称
	if len(productName) > 0 && product == "" {
		product = productName
	}
	if err != nil {
		//回写状态为失败
		err := techSupport.Update(techSupport.ID, map[string]interface{}{"product": product, "status": 2, "updated_at": time.Now()})
		if err != nil {
			logging.ErrorLogger.Errorf("techsupport status update err ")
			return err
		}
	} else {
		err = techSupport.Update(techSupport.ID, map[string]interface{}{"product": product, "status": 1, "updated_at": time.Now(), "remote_work_dir": remoteWorkDir})
		if err != nil {
			logging.ErrorLogger.Errorf("techsupport file status udpated err ")
			return err
		}
	}
	return nil

}

func ProcessCoreDump(m kafka.Message) error {
	items := []CoredumpItem{}
	// 解码 JSON
	coreDumpInfo, err := decodeJSON(m.Value)
	if err != nil {
		return err
	}

	// 获取下载链接
	downloadUrl, err := getDownloadUrl(coreDumpInfo)
	if err != nil {
		return err
	}

	// 创建目录
	upload, tempName, err := createDirectory()
	if err != nil {
		return err
	}

	// 下载文件到本地
	err = DownloadFile(downloadUrl, filepath.Join(upload, coreDumpInfo.FileName))
	if err != nil {
		logging.ErrorLogger.Errorf("download file err ", err)
		return err
	}

	// 获取文件 md5
	fileMd5, err := getFileMd5(filepath.Join(upload, coreDumpInfo.FileName))
	if err != nil {
		return err
	}

	// 检查文件是否存在
	exists, err := checkFileExistence(coreDumpInfo.FileId, fileMd5)
	if err != nil {
		return err
	}
	if exists {
		logging.ErrorLogger.Errorf("文件已存在")
		return nil //已存在文件跳过
	}
	// 创建记录
	product := ""
	err = createRecord(coreDumpInfo, coreDumpInfo.FileName, fileMd5, upload, tempName, product)
	if err != nil {
		return err
	}

	// 构建沙盒环境
	techSupportFile := filepath.Join(upload, coreDumpInfo.FileName)
	err = InitSandBoxBykafka(techSupportFile, product, tempName)
	if err != nil {
		return err
	}
	// 获取coreDump文件列表
	techSupport := dcoredumptechsupport.Response{}
	err = techSupport.FindByJobID(tempName)
	if err != nil {
		logging.ErrorLogger.Errorf("find techsupport file err ", err)
		return err
	}
	if techSupport.ID == 0 {
		logging.ErrorLogger.Errorf("techsupport file not found ")
		return fmt.Errorf("techsupport file not found")
	}
	if techSupport.Status != 1 {
		logging.ErrorLogger.Errorf("sandbox status not ok")
		return fmt.Errorf("sandbox status not ok")
	}

	fileName := strings.TrimSuffix(filepath.Base(techSupportFile), filepath.Ext(techSupportFile))
	workCoreDumpDir := libs.Config.CoreDump.DecryptedDir + fileName + "_" + tempName + "/temp/tech_support/coredump"
	//验证目录是否存在
	if _, err := os.Stat(workCoreDumpDir); os.IsNotExist(err) {
		logging.ErrorLogger.Errorf("workCoreDumpDir not exist")
		return err
	}

	output, err := ListCoreDumpFiles(&techSupport)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		return err
	}
	coredumps := strings.Split(strings.TrimSuffix(output, "\n"), " ")

	for _, coredump := range coredumps {
		//解压.gz文件
		if strings.HasSuffix(coredump, ".gz") {
			shell := filepath.Join(libs.Config.CoreDump.Plugin, "unzip_coredump.sh")
			command := fmt.Sprintf("bash %s %s %s %s %s", shell, libs.Config.CoreDump.Username, libs.Config.CoreDump.Host, libs.Config.CoreDump.Port, coredump)

			_, err := libs.ExecCommand(command)
			if err != nil {
				return err
			}
			coredump = strings.TrimSuffix(coredump, ".gz")
		}
		//提取执行文件路径
		exeProgramPath, err := extractExecFilePath(coredump)
		if err != nil {
			return err
		}
		items = append(items, CoredumpItem{
			Name: filepath.Base(coredump),
			Path: exeProgramPath,
		})
		//收集指定堆栈
		err = collectStack(&techSupport, coredump, exeProgramPath)
		if err != nil {
			return err
		}

	}
	//发送通知
	err = sendNotification(coreDumpInfo, items, true)
	if err != nil {
		return err
	}
	return nil
}

func decodeJSON(value []byte) (CoreDumpInfo, error) {
	var coreDumpInfo CoreDumpInfo
	err := json.Unmarshal(value, &coreDumpInfo)
	if err != nil {
		logging.ErrorLogger.Errorf("Error unmarshalling JSON: %v", err)
		return coreDumpInfo, err
	}
	return coreDumpInfo, nil
}

func getDownloadUrl(coreDumpInfo CoreDumpInfo) (string, error) {
	downloadInfo, err := featurerelease.SecCloudClient.GetDownloadUrl(coreDumpInfo.SecurityUrl+"/", coreDumpInfo.SN, strconv.FormatInt(coreDumpInfo.FileId, 10))
	if err != nil {
		logging.ErrorLogger.Errorf("get download url err ", err)
		return "", err
	}
	return downloadInfo.Data, nil
}

func createDirectory() (string, string, error) {
	tempName := libs.GetUniqueID()
	if libs.Config.CoreDump.Upload == "" {
		logging.ErrorLogger.Errorf("未配置工作目录，请联系管理员")
		return "", "", fmt.Errorf("未配置工作目录，请联系管理员")
	}
	upload := filepath.Join(libs.Config.CoreDump.Upload, time.Now().Format("20060102"), tempName)
	err := os.MkdirAll(upload, 0750)
	if err != nil {
		logging.ErrorLogger.Errorf("创建目录失败：%s", err.Error())
		return "", "", err
	}
	os.Chmod(upload, 0750)
	return upload, tempName, nil
}

func getFileMd5(filePath string) (string, error) {
	fileMd5, err := libs.GetFileMd5(filePath)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		return "", err
	}
	return fileMd5, nil
}

func checkFileExistence(fileId int64, fileMd5 string) (bool, error) {
	coredumpFind := dcoredumptechsupport.Response{}
	err := coredumpFind.FindByFileIDAndMD5(fileId, fileMd5)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		return false, err
	}
	return coredumpFind.ID > 0, nil
}

func createRecord(coreDumpInfo CoreDumpInfo, fileName, fileMd5, upload, tempName, product string) error {
	Dcoredumptechsupport := dcoredumptechsupport.Response{}
	err := Dcoredumptechsupport.Create(map[string]interface{}{
		"JobID":     tempName,
		"FileName":  fileName,
		"MD5":       fileMd5,
		"TempDir":   upload,
		"CreatedAt": time.Now(),
		"UpdatedAt": time.Now(),
		"UserID":    0,
		"Product":   product,
		"SecFileID": coreDumpInfo.FileId,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("create tech support get err ", err)
		os.RemoveAll(upload)
		return err
	}
	return nil
}

func sendNotification(coreDumpInfo CoreDumpInfo, coredumpData []CoredumpItem, ok bool) error {
	webhookURL := "https://open.feishu.cn/open-apis/bot/v2/hook/8784310a-2b6a-4030-9e16-4a8765cf0cc7"
	secret := "P3ZIiMpNxMM6QxCHirPutd"
	message := "**💻设备sn：**" + coreDumpInfo.SN + "\n**🔢文件id：**" + strconv.Itoa(int(coreDumpInfo.FileId)) +
		"\n**🔢taskId：**" + coreDumpInfo.TaskId + "\n**🗳文件名：**" + coreDumpInfo.FileName + "\n**📝文件大小：**" + coreDumpInfo.FileSize
	msgTitle := "【coredump解析成功】文件已解析成功"
	msgTemplate := "green"
	msgURL := "http://10.51.134.126:8082/coredump/#/coredump/index?fileId=" + strconv.Itoa(int(coreDumpInfo.FileId))
	if ok {
		if len(coredumpData) > 0 {
			message += "\n**💥coredump文件列表：**\n"
			for _, coredump := range coredumpData {
				message += "  - 📄**coredump文件**:" + coredump.Name + "   - ⚙️**执行路径**:" + coredump.Path + "\n"
			}
		}
	} else {
		msgTitle = "【coredump解析失败】文件解析失败"
		msgTemplate = "red"
		message += "\n**❌coredump文件解析失败**"
	}

	if err := SendCardMessage(webhookURL, secret, message, msgTitle, msgTemplate, msgURL); err != nil {
		logging.ErrorLogger.Errorf("Error sending message: %v", err)
		return err
	}
	return nil
}

func extractExecFilePath(coredump string) (string, error) {
	shell := filepath.Join(libs.Config.CoreDump.Plugin, "list_coredump_program.sh")
	command := fmt.Sprintf("bash %s %s %s %s %s", shell, libs.Config.CoreDump.Username, libs.Config.CoreDump.Host, libs.Config.CoreDump.Port, coredump)
	fmt.Println(command)
	output, err := libs.ExecCommand(command)
	if err != nil {
		return "", err
	}
	// 将输出转换为字符串并查找执行文件路径
	outputStr := string(output)
	prefix := " from '"
	startIdx := strings.Index(outputStr, prefix)
	if startIdx == -1 {
		return "", fmt.Errorf("could not find executable file path in output")
	}

	startIdx += len(prefix)
	endIdx := strings.Index(outputStr[startIdx:], "'")
	if endIdx == -1 {
		return "", fmt.Errorf("could not find end of executable file path in output")
	}
	execFilePath := outputStr[startIdx : startIdx+endIdx]
	return execFilePath, nil
}

func collectStack(techSupport *dcoredumptechsupport.Response, coredump, exeProgramPath string) error {
	if libs.Config.CoreDump.Output == "" {
		logging.ErrorLogger.Errorf("未配置工作目录，请联系管理员--collectStack")
		return fmt.Errorf("未配置工作目录，请联系管理员")
	}
	var outputDir = filepath.Join(libs.Config.CoreDump.Upload, time.Now().Format("20060102"), techSupport.JobID)
	err := os.MkdirAll(outputDir, 0750)
	os.Chmod(outputDir, 0750)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		return err
	}
	jobID := libs.GetUniqueID()
	logFile := filepath.Join(outputDir, fmt.Sprintf("%s_%s_%s.log", filepath.Base(exeProgramPath), filepath.Base(coredump), jobID))
	f, err := os.OpenFile(logFile, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		logging.ErrorLogger.Errorf("create coredump job err ", err)
		return err
	}
	Dcoredumpjob := dcoredumpjob.Response{}
	err = Dcoredumpjob.Create(map[string]interface{}{
		"JobID":         jobID,
		"TechSupportID": techSupport.ID,
		"ProcessName":   filepath.Base(exeProgramPath),
		"ProcessPath":   exeProgramPath,
		"FileName":      filepath.Base(coredump),
		"FilePath":      coredump,
		"LogFile":       logFile,
		"CreatedAt":     time.Now(),
		"UpdatedAt":     time.Now(),
		"Status":        0,
	})

	if err != nil {
		logging.ErrorLogger.Errorf("create coredump job err ", err)
		return err
	}
	go func() {
		defer f.Close()
		job := dcoredumpjob.Response{}
		err = job.FindEx("job_id", jobID)
		if err != nil {
			logging.ErrorLogger.Errorf("create coredump job err ", err)
		}
		output, err := CollectBT(techSupport, exeProgramPath, coredump)
		if err != nil {
			logging.ErrorLogger.Errorf("get user get err ", err)
			job.Update(job.ID, map[string]interface{}{
				"status":     2,
				"updated_at": time.Now(),
			})
			return
		}
		_, err = f.Write([]byte(output))
		if err != nil {
			logging.ErrorLogger.Errorf("get user get err ", err)
			job.Update(job.ID, map[string]interface{}{
				"status":     2,
				"updated_at": time.Now(),
			})
			return
		}

		job.Update(job.ID, map[string]interface{}{
			"status":     1,
			"updated_at": time.Now(),
		})
	}()
	return nil
}
