package dfeishudocumenttemplate

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models"
	"irisAdminApi/application/models/feishu"
)

const ModelName = "飞书文件模板表"

type Response struct {
	models.ModelBase
	Name                  string `gorm:"type:varchar(200)" json:"name" `
	DocumentCategoryTitle string `gorm:"type:varchar(100)" json:"document_category_title" ` //文档类型
	OwnerID               string `gorm:"type:varchar(200)" json:"owner_id" `
	ParentToken           string `gorm:"type:varchar(200)" json:"parent_token" `
	Token                 string `gorm:"type:varchar(200)" json:"token" `
	Type                  string `gorm:"type:varchar(200)" json:"type" `
	Url                   string `gorm:"type:varchar(200)" json:"url" `
}

type ListResponse struct {
	Response
}

type Request struct {
	Id uint `json:"id"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *feishu.FeishuDocumentTemplate {
	return &feishu.FeishuDocumentTemplate{}
}

func (a *Request) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Request) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Request) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (a *Request) CreateV2(object interface{}) error {
	return nil
}

func (a *Request) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (u *Request) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (u *Request) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Request) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Request) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Request) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func BatchCreate(items []map[string]interface{}) error {
	db := easygorm.GetEasyGormDb().Model(Model())
	return db.Create(items).Error
}

func DeleteAll() error {
	db := easygorm.GetEasyGormDb().Model(Model())
	err := db.Unscoped().Delete(Model(), "id>0").Error
	if err != nil {
		return err
	}
	return nil
}

// 查找项目文件夹下面的文件
func GetFolderFileDataByProjectName(projectName string) ([]*Response, error) {
	items := []*Response{}
	folderFile := &Response{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("name = ?", projectName).Find(folderFile).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find folder err ", err)
		return items, err
	}
	if folderFile.ID > 0 {
		if err := easygorm.GetEasyGormDb().Model(Model()).Where("parent_token = ?", folderFile.Token).Find(&items).Error; err != nil {
			logging.ErrorLogger.Errorf("find folder err ", err)
			return items, err
		}
	}
	return items, nil
}

func GetAll() ([]*Response, error) {
	items := []*Response{}
	if err := easygorm.GetEasyGormDb().Model(Model()).Find(&items).Error; err != nil {
		logging.ErrorLogger.Errorf("find folder err ", err)
		return items, err
	}
	return items, nil
}
