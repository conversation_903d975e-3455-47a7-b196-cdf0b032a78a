package resourcepool

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/resourcepool"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/resourcepool/dresourcepoolinterface"
	"irisAdminApi/service/dao/resourcepool/dresourcepoolresource"
	"irisAdminApi/service/dao/user/duser"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	"golang.org/x/crypto/ssh"
)

func ExecuteSSHCommand(command string) (string, error) {
	config := &ssh.ClientConfig{
		User: libs.Config.Resourcepool.Ssh.Username,
		Auth: []ssh.AuthMethod{
			ssh.Password(libs.Config.Resourcepool.Ssh.Password),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	}

	conn, err := ssh.Dial("tcp", libs.Config.Resourcepool.Ssh.Host+":"+libs.Config.Resourcepool.Ssh.Port, config)
	if err != nil {
		return "SSH连接失败", fmt.Errorf("SSH连接失败：%v", err)
	}
	defer conn.Close()

	session, err := conn.NewSession()
	if err != nil {
		return "创建SSH会话失败", fmt.Errorf("创建SSH会话失败：%v", err)
	}
	defer session.Close()

	output, err := session.CombinedOutput(command)
	if err != nil {
		return string(output), fmt.Errorf("执行SSH命令失败：%v", err)
	}

	return string(output), nil
}

func GetInterfaces(ctx iris.Context) {

	uId, _ := dao.GetAuthId(ctx)
	name := ctx.FormValue("search")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dresourcepoolinterface.All(uId, name, sort, orderBy, page, pageSize)

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetInterface(ctx iris.Context) {
	info := dresourcepoolinterface.ResourcePoolInterface{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao find resource pool interface error: %s", err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

func CreateInterface(ctx iris.Context) {

	request := dresourcepoolinterface.Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("create interface read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	validErr := libs.Validate.Struct(&request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	err := dao.Create(&dresourcepoolinterface.ResourcePoolInterface{}, ctx, map[string]interface{}{
		"CreatedAt":    time.Now(),
		"UpdatedAt":    time.Now(),
		"Name":         request.Name,
		"Interface":    request.Interface,
		"Driver":       request.Driver,
		"Mac":          request.Mac,
		"SwName":       request.SwName,
		"SwPort":       request.SwPort,
		"VlanId":       request.VlanId,
		"VlanMode":     request.VlanMode,
		"Type":         request.Type,
		"Comments":     request.Comments,
		"SwPortStatus": request.SwPortStatus,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("create interface err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, request, response.NoErr.Msg))
	return
}

func UpdateInterface(ctx iris.Context) {
	// id, err := dao.GetId(ctx)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }

	request := dresourcepoolinterface.Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(&request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	// interface_info := dresourcepoolinterface.ResourcePoolInterface{}
	// err = interface_info.Find(id)
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
	// 	return
	// }

	err := dao.Update(&dresourcepoolinterface.ResourcePoolInterface{}, ctx, map[string]interface{}{
		"UpdatedAt":    time.Now(),
		"Name":         request.Name,
		"Interface":    request.Interface,
		"Driver":       request.Driver,
		"Mac":          request.Mac,
		"SwName":       request.SwName,
		"SwPort":       request.SwPort,
		"VlanId":       request.VlanId,
		"VlanMode":     request.VlanMode,
		"Type":         request.Type,
		"Comments":     request.Comments,
		"SwPortStatus": request.SwPortStatus,
	})

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func SwitchPort(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	rinterface := dresourcepoolinterface.ResourcePoolInterface{}
	err = rinterface.Find(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	resource, err := dresourcepoolresource.FindResourceByName(rinterface.Name)
	permit, err := checkPermissionCtx(ctx, &resource)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if !permit {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, resource.Name+" 设备未占用，请先占用!"))
		return
	}

	var sw_port_status string
	sw_port_status_now := rinterface.SwPortStatus
	if sw_port_status_now == "active" {
		sw_port_status = "inactive"
	} else {
		sw_port_status = "active"
	}

	err = dao.Update(&dresourcepoolinterface.ResourcePoolInterface{}, ctx, map[string]interface{}{
		"UpdatedAt":    time.Now(),
		"SwPortStatus": sw_port_status,
	})

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func DeleteInterface(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao get delete id err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	// todo: 检查设备是否占用,禁止操作未占用设备

	cmd := libs.Config.Resourcepool.Ssh.Psw + " -d "

	var rinterface *resourcepool.ResourcePoolInterface
	if err := easygorm.GetEasyGormDb().Where("id = ?", id).Find(&rinterface).Error; err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	// todo: 检查设备是否占用,禁止操作未占用设备

	resource, err := dresourcepoolresource.FindResourceByName(rinterface.Name)
	permit, err := checkPermissionCtx(ctx, &resource)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if !permit {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, resource.Name+" 设备未占用，请先占用!"))
		return
	}

	vlanID := rinterface.VlanId
	if vlanID != "999" {
		var connections []*resourcepool.ResourcePoolInterface
		if err := easygorm.GetEasyGormDb().Where("vlan_id = ?", vlanID).Find(&connections).Error; err != nil {
			logging.ErrorLogger.Errorf("get resource pool interfaces err ", err)
			return
		}

		for _, conn := range connections {
			cname := conn.Name
			cinterface := conn.Interface
			cmd += cname + " " + cinterface + " "
		}
	}

	logging.ErrorLogger.Debugf("execute ssh command: ", cmd)

	output, err := ExecuteSSHCommand(cmd)
	if err != nil {
		logging.ErrorLogger.Errorf("execute ssh command err: ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, output))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetConByName(ctx iris.Context) {
	name := ctx.FormValue("name")
	list, err := dresourcepoolinterface.AllInterfaceByName(name)

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

type Row struct {
	Name      string `json:"name"`
	Interface string `json:"interface"`
}

type Payload struct {
	Rows     []Row  `json:"rows"`
	VlanID   string `json:"vlan_id"`
	VlanMode string `json:"vlan_mode"`
	Comments string `json:"comments"`
}

func Creatcon(ctx iris.Context) {
	userID, _ := dao.GetAuthId(ctx)
	user := duser.User{}
	err := user.Find(userID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	var payload Payload
	err = ctx.ReadJSON(&payload)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	// 判断是否占用设备
	names := []string{}
	for _, p := range payload.Rows {
		names = append(names, p.Name)
	}
	resources, err := dresourcepoolresource.FindResourceByNames(names)
	for _, resource := range resources {
		if resource.Reserved != user.Username {
			ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, resource.Name+" 设备未占用，请先占用!"))
			return
		}
	}

	cmd := libs.Config.Resourcepool.Ssh.Psw + " "

	if payload.VlanMode == "access" {
		cmd += "-l "
	} else {
		cmd += "-t " + payload.VlanID + " "
	}

	for _, row := range payload.Rows {

		name := row.Name
		interface_ := row.Interface

		cmd += name + " " + interface_ + " "

	}

	logging.ErrorLogger.Debugf("execute ssh command: ", cmd)

	output, err := ExecuteSSHCommand(cmd)
	if err != nil {
		logging.ErrorLogger.Errorf("execute ssh command err: ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, output))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}
