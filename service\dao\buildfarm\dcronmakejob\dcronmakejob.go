package dcronmakejob

import (
	"fmt"
	"strings"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/buildfarm"
	"irisAdminApi/service/dao/release/dproductmodel"

	"github.com/pkg/errors"
)

const ModelName = "每日编译任务管理"

type BuildfarmProductCpu struct {
	Product string `json:"product"`
	Cpu     string `json:"cpu"`
}

type CronTab struct {
	ID             uint   `json:"id"`
	Notice         bool   `json:"notice"`
	Recievers      string `json:"recievers"`
	WithRelease    bool   `json:"with_release"`
	WithShell      bool   `json:"with_shell"`
	Product        string `json:"product"`
	Cron           string `json:"cron"`
	ReleaseProject string `json:"release_project"`
}

type CronMakeJob struct {
	Id              uint                `json:"id"`
	UpdatedAt       time.Time           `json:"updated_at"`
	CreatedAt       time.Time           `json:"created_at"`
	StartedAt       time.Time           `json:"started_at"`
	FinishedAt      time.Time           `json:"finished_at"`
	JobId           string              `json:"job_id"`
	ServerId        uint                `json:"server_id"`
	Project         string              `json:"project"`
	Repo            string              `json:"repo"`
	Branch          string              `json:"branch"`
	TaskType        uint                `json:"task_type"` // 编译类型，1：产品编译  2：组件编译
	Product         string              `json:"product"`
	Defconfig       string              `json:"defconfig"`
	Target          string              `json:"target"`
	Dir             string              `json:"dir"`
	Status          uint                `json:"status"` // 作业状态 0：运行，1：成功， 2：失败
	Version         string              `json:"version"`
	BuildType       string              `json:"build_type"` // 编译类型： debug, performance
	SoftwareNumber  string              `json:"software_nubmer"`
	SoftwareVersion string              `json:"software_version"`
	Cpu             BuildfarmProductCpu `gorm:"->;foreignKey:Product;references:Product" json:"cpu"`
	// ProductCpu      BuildfarmProductCpu `gorm:"->;foreignKey:Product;references:Product" json:"product_cpu"`
	SmokeStatus string  `json:"smoke_status"`
	CronTabID   uint    `json:"cron_tab_id"`
	CronTab     CronTab `gorm:"->;foreignKey:CronTabID;references:ID" json:"cron_tab"`
	UseCache    bool    `gorm:"not null; default:true" json:"use_cache"`
}

type ListResponse struct {
	CronMakeJob
}

type Request struct {
	Id        uint   `json:"id"`
	JobId     string `json:"job_id"`
	ServerId  uint   `json:"server_id"`
	Project   string `json:"project"`
	Repo      string `json:"repo"`
	Branch    string `json:"branch"`
	TaskType  uint   `json:"task_type"` // 编译类型，1：产品编译  2：组件编译
	Product   string `json:"product"`
	Defconfig string `json:"defconfig"`
	Target    string `json:"target"`
	Dir       string `json:"Dir"`
	Status    uint   `json:"status"` // 作业状态 0：运行，1：成功， 2：失败
	Version   string `json:"version"`
	BuildType string `json:"build_type"` // 编译类型： debug, performance
}

func (a *CronMakeJob) ModelName() string {
	return ModelName
}

func Model() *buildfarm.CronMakeJob {
	return &buildfarm.CronMakeJob{}
}

func (a *CronMakeJob) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("CronTab").Preload("Cpu")
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	// productCpuMap := map[string]string{}
	// productCpus, err := dbuildfarmproductcpu.FindAll()
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("get product cpu map data err ", err)
	// }
	// for _, productCpu := range productCpus {
	// 	productCpuMap[productCpu.Product] = productCpu.Cpu
	// 	productCpuMap[strings.Split(productCpu.Cpu, "-")[0]] = productCpu.Cpu
	// }

	// for _, item := range items {
	// 	if item.Product != "" {
	// 		item.Cpu = productCpuMap[item.Product]
	// 	} else if item.Defconfig != "" {
	// 		item.Cpu = productCpuMap[strings.Split(item.Defconfig, "-")[0]]
	// 	}
	// }
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *CronMakeJob) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *CronMakeJob) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *CronMakeJob) CreateV2(object interface{}) error {
	return nil
}

func (a *CronMakeJob) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *CronMakeJob) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *CronMakeJob) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *CronMakeJob) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *CronMakeJob) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("CronTab").Preload("Cpu").Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	if u.Id == 0 {
		return errors.New(fmt.Sprint("record not find ", id))
	}
	return nil
}

func (u *CronMakeJob) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindAllCronMakeJobs(repo, branch, start, end, status, archive, product, defconfig, target, buildType, softnum, softversion, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var makejobs []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("CronTab").Preload("Cpu")
	if len(repo) > 0 {
		db = db.Where("repo like ?", fmt.Sprintf("%%%s%%", repo))
	}
	if len(branch) > 0 {
		db = db.Where("branch = ?", branch)
	}
	if len(start) > 0 {
		db = db.Where("created_at >= ?", start+" 00:00:00.000")
	}
	if len(end) > 0 {
		db = db.Where("created_at <= ?", end+" 23:59:59.999")
	}
	if len(status) > 0 {
		db = db.Where("status = ?", status)
	}
	if len(product) > 0 {
		db = db.Where("product in ?", strings.Split(product, ","))
		// db = db.Where("status = ?", status)
	}
	if len(defconfig) > 0 {
		db = db.Where(fmt.Sprintf("defconfig like '%%%s%%'", defconfig))
		// db = db.Where("defconfig = ?", defconfig)
	}
	if len(target) > 0 {
		db = db.Where(fmt.Sprintf("target like '%%%s%%'", target))
		// db = db.Where("target = ?", target)
	}
	if len(buildType) > 0 {
		db = db.Where("build_type in ?", strings.Split(buildType, ","))
		// db = db.Where("target = ?", target)
	}

	if len(softnum) > 0 {
		db = db.Where("software_number = ?", softnum)
		// db = db.Where("target = ?", target)
	}
	if len(softversion) > 0 {
		db = db.Where(fmt.Sprintf("software_version like '%%%s%%'", softversion))
		// db = db.Where("target = ?", target)
	}

	if len(archive) > 0 {
		db = db.Where("job_id in (?)", easygorm.GetEasyGormDb().Table("release_releases").Select(`SUBSTRING_INDEX(SUBSTRING_INDEX(build_farm_link, "/", 5), "/", -1) as job_id`))
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&makejobs).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	// productCpuMap := map[string]string{}
	// productCpus, err := dbuildfarmproductcpu.FindAll()
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("get product cpu map data err ", err)
	// }
	// for _, productCpu := range productCpus {
	// 	productCpuMap[productCpu.Product] = productCpu.Cpu
	// 	productCpuMap[strings.Split(productCpu.Cpu, "-")[0]] = productCpu.Cpu
	// }

	// for _, item := range makejobs {
	// 	if item.Product != "" {
	// 		item.Cpu = productCpuMap[item.Product]
	// 	} else if item.Defconfig != "" {
	// 		item.Cpu = productCpuMap[strings.Split(item.Defconfig, "-")[0]]
	// 	}
	// }

	list := map[string]interface{}{"items": makejobs, "total": count, "limit": pageSize}
	return list, nil
}

func FindCronMakeJobByTaskId(taskId string) (CronMakeJob, error) {
	item := CronMakeJob{}
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("CronTab").Preload("Cpu").Where("job_id = ?", taskId).Find(&item).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return item, err
	}
	return item, nil
}

func FindRuningJobs() ([]*CronMakeJob, error) {
	var runningCronMakeJobs []*CronMakeJob
	db := easygorm.GetEasyGormDb().Model(Model()).Preload("CronTab").Preload("Cpu")
	db = db.Where("status = ?", 0)

	err := db.Find(&runningCronMakeJobs).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	return runningCronMakeJobs, nil
}

type Summary struct {
	ServerId uint `json:"server_id"`
	Count    uint `json:"count"`
}

func SummaryJobByServer() ([]*Summary, error) {
	db := easygorm.GetEasyGormDb().Model(Model())
	summary := []*Summary{}
	rows, err := db.Where("status = ?", 0).Select("server_id, count(*) as total").Group("server_id").Rows()
	if err != nil {
		logging.ErrorLogger.Error(err)
		return summary, err
	}
	for rows.Next() {
		db.ScanRows(rows, &summary)
	}
	rows.Close()
	return summary, nil
}

func FindCronMakeJobInIDs(ids []uint) ([]*ListResponse, error) {
	var makejobs []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("CronTab").Preload("Cpu")

	err := db.Where("id in ?", ids).Find(&makejobs).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	// productCpuMap := map[string]string{}
	// productCpus, err := dbuildfarmproductcpu.FindAll()
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("get product cpu map data err ", err)
	// }
	// for _, productCpu := range productCpus {
	// 	productCpuMap[productCpu.Product] = productCpu.Cpu
	// 	productCpuMap[strings.Split(productCpu.Cpu, "-")[0]] = productCpu.Cpu
	// }

	// for _, item := range makejobs {
	// 	if item.Product != "" {
	// 		item.Cpu = productCpuMap[item.Product]
	// 	} else if item.Defconfig != "" {
	// 		item.Cpu = productCpuMap[strings.Split(item.Defconfig, "-")[0]]
	// 	}
	// }

	return makejobs, nil
}

func FindAllCronMakeJobsV2(repo, branch, start, end, status, archive, product, defconfig, target, buildType, softnum, softversion, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var makejobs []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("CronTab").Preload("Cpu")
	if len(repo) > 0 {
		db = db.Where("repo like ?", fmt.Sprintf("%%%s%%", repo))
	}
	if len(branch) > 0 {
		db = db.Where("branch like '%%%s%%'", branch)
	}
	if len(start) > 0 {
		db = db.Where("created_at >= ?", start+" 00:00:00.000")
	}
	if len(end) > 0 {
		db = db.Where("created_at <= ?", end+" 23:59:59.999")
	}
	if len(status) > 0 {
		db = db.Where("status = ?", status)
	}
	if len(product) > 0 {
		db = db.Where("product in ?", strings.Split(product, ","))
		// db = db.Where("status = ?", status)
	}
	if len(defconfig) > 0 {
		db = db.Where(fmt.Sprintf("defconfig like '%%%s%%'", defconfig))
		// db = db.Where("defconfig = ?", defconfig)
	}
	if len(target) > 0 {
		db = db.Where(fmt.Sprintf("target like '%%%s%%'", target))
		// db = db.Where("target = ?", target)
	}
	if len(buildType) > 0 {
		db = db.Where("build_type = ?", buildType)
		// db = db.Where("target = ?", target)
	}
	if len(softnum) > 0 {
		db = db.Where("software_number = ?", softnum)
		// db = db.Where("target = ?", target)
	}
	if len(softversion) > 0 {
		db = db.Where(fmt.Sprintf("software_version like '%%%s%%'", softversion))
		// db = db.Where("target = ?", target)
	}

	if len(archive) > 0 {
		db = db.Where("job_id in (?)", easygorm.GetEasyGormDb().Table("release_releases").Select(`SUBSTRING_INDEX(SUBSTRING_INDEX(build_farm_link, "/", 5), "/", -1) as job_id`))
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&makejobs).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": makejobs, "total": count, "limit": pageSize}
	return list, nil
}

func FindCronMakeJobsByLikeReleaseID(branch, releaseID string) ([]*ListResponse, error) {
	items := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("CronTab").Preload("Cpu").Where("branch = ? and software_version like ?", branch, fmt.Sprintf("%%%s%%", releaseID)).Find(&items).Error
	return items, errors.Wrap(err, "")
}

func FindLastSuccessCronMake(branch, product, buildType string, createdAt time.Time) ([]*ListResponse, error) {
	items := []*ListResponse{}

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("CronTab").Preload("Cpu")

	db = db.Where("created_at < ?", createdAt)

	if len(buildType) > 0 {
		db = db.Where("build_type = ?", buildType)
	}
	err := db.Where("branch = ? and product = ? and status = 1", branch, product).Limit(1).Order("created_at desc").Find(&items).Error
	return items, errors.Wrap(err, "")
}

func FindAllByCronTabID(cronTabID uint, startedAt time.Time) ([]*ListResponse, error) {
	items := []*ListResponse{}

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("CronTab").Preload("Cpu")

	db = db.Where("created_at > ?", startedAt)

	if cronTabID > 0 {
		db = db.Where("cron_tab_id = ?", cronTabID)
	}
	err := db.Order("created_at desc").Find(&items).Error
	return items, errors.Wrap(err, "")
}

func FindAllJobByProjectAndBranch(project, branch string, types []string, startedAt time.Time) ([]*ListResponse, error) {
	items := []*ListResponse{}

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("CronTab").Preload("Cpu")

	db = db.Where("created_at > ? and project = ? and branch = ? and build_type in ? and status = 1", startedAt, project, branch, types)

	err := db.Order("created_at desc").Find(&items).Error
	return items, errors.Wrap(err, "")
}

// 获取 bin 文件 根据 release 和 version 获取
func GetBinFile(softwareVersion, version string) ([]*ListResponse, error) {
	items := []*ListResponse{}
	db := easygorm.GetEasyGormDb().Model(Model()).Preload("CronTab").Preload("Cpu")
	db = db.Where("build_type = ?", "release")
	db = db.Where("status = ?", 1)
	// and project ="basesys/build-product"
	db = db.Where("project = ?", "basesys/build-product")
	// and  software_version  like  %softwareVersion%
	db = db.Where("software_version like ?", fmt.Sprintf("%%%s%%", softwareVersion))
	db = db.Where("branch = ?", version)
	err := db.Find(&items).Error
	return items, errors.Wrap(err, "")
}

// 获取编译记录ID
func GetBuildRecordIDs(buildRecordIDs []string) ([]*ListResponse, error) {
	var buildRecord []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("CronTab").Preload("Cpu").Where("id in  (?)", buildRecordIDs).Find(&buildRecord).Error
	return buildRecord, err
}

// FindCronMakeJobsByProductAndSoftware 根据产品名称、软件版本号和软件版本查询CronMakeJob信息
func FindCronMakeJobsByProductAndSoftware(product, softnum, softversion string) (*CronMakeJob, error) {
	// 构建查询
	db := easygorm.GetEasyGormDb().Model(Model()).Preload("CronTab").Preload("Cpu")

	logging.InfoLogger.Infof("查询参数: product=%s, softnum=%s, softversion=%s", product, softnum, softversion)

	// 尝试将产品名称转换为BuildName
	if len(product) > 0 {
		// 尝试从dproductmodel查找对应的BuildName
		var productModel dproductmodel.ReleaseProductModel
		err := productModel.FindByName(product)
		if err == nil && productModel.ID > 0 && len(productModel.ReleaseBuildName.Name) > 0 {
			buildName := productModel.ReleaseBuildName.Name
			logging.InfoLogger.Infof("找到产品 %s 对应的构建名称: %s", product, buildName)

			// 使用模糊查询匹配产品名称
			db = db.Where("product LIKE ?", "%"+buildName+"%")
		} else {
			// 如果没有找到对应的BuildName，直接使用product参数进行模糊查询
			logging.InfoLogger.Infof("未找到产品 %s 对应的构建名称，使用原始产品名称查询", product)
			db = db.Where("product LIKE ?", "%"+product+"%")
		}
	}

	// 添加软件版本号过滤条件（如果提供）
	if len(softnum) > 0 {
		db = db.Where("software_number LIKE ?", "%"+softnum+"%")
	}

	// 添加软件版本过滤条件（如果提供）
	if len(softversion) > 0 {
		db = db.Where("software_version LIKE ?", "%"+softversion+"%")
	}

	// 添加状态条件，优先查找成功的构建
	db = db.Where("status = ?", 1)

	// 按ID倒序排序以获取最新记录
	db = db.Order("id DESC")

	// 执行查询，只获取第一条记录
	var item CronMakeJob
	err := db.Limit(1).First(&item).Error
	// 查询没有找到记录时不返回错误
	if err != nil {
		if err.Error() == "record not found" {
			logging.InfoLogger.Infof("查询未找到匹配的记录")
			return nil, nil
		}
		logging.ErrorLogger.Errorf("查询出错: %v", err)
		return nil, err
	}
	return &item, nil
}

type ReleaseRelease struct {
	ID            uint   `json:"uint"`
	BuildFarmLink string `json:"build_farm_link"`
}

func FindCronMakeJobsToCleanIn15to60Days() ([]string, error) {
	// 获取已经发布作业ID
	result := []string{}
	rawSql := `
		select job_id from (
			select
				id,
				job_id 
			from 
				cron_make_jobs cmjs
			where cmjs.status = 1 and cmjs.created_at <= DATE_ADD(NOW(), INTERVAL - 15 DAY) and cmjs.created_at >= DATE_ADD(NOW(), INTERVAL - 60 DAY)
		) cmjs
		left join release_releases rrs on  rrs.build_farm_link like concat('%', cmjs.job_id, '%')
		where 
			rrs.id is null
			and cmjs.id not in (
				select 
					max(id) as id 
				from 
					cron_make_jobs 
				where status = 1 and created_at <= DATE_ADD(NOW(), INTERVAL - 15 DAY) and created_at >= DATE_ADD(NOW(), INTERVAL - 60 DAY)
				group by project, product, build_type, date(created_at)
			)
	`
	err := easygorm.GetEasyGormDb().Raw(rawSql).Scan(&result).Error
	if err != nil {
		return nil, err
	}
	return result, nil
}

func FindCronMakeJobsToCleanAfter60Days() ([]string, error) {
	// 获取已经发布作业ID
	result := []string{}
	rawSql := `
		select job_id from (
			select
				id,
				job_id 
			from 
				cron_make_jobs cmjs
			where cmjs.status = 1 and cmjs.created_at < DATE_ADD(NOW(), INTERVAL - 60 DAY) and cmjs.created_at >= DATE_ADD(NOW(), INTERVAL - 90 DAY)
		) cmjs
		left join release_releases rrs on  rrs.build_farm_link like concat('%', cmjs.job_id, '%')
		where 
			rrs.id is null
	`
	err := easygorm.GetEasyGormDb().Raw(rawSql).Scan(&result).Error
	if err != nil {
		return nil, err
	}
	return result, nil
}
