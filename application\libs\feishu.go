package libs

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
)

type TemplateData struct {
	Subject string `json:"subject"`
	Body    string `json:"body"`
}

type Data struct {
	TemplateID          string       `json:"template_id"`
	TemplateVersionName string       `json:"template_version_name"`
	TemplateVariable    TemplateData `json:"template_variable"`
}

type Template struct {
	Type string `json:"type"`
	Data Data   `json:"data"`
}

func SendCardMessage(mailTo []string, subject string, body string, cc []string) error {
	if len(mailTo) == 0 {
		return fmt.Errorf("mail to is empty")
	}

	template := Template{
		Type: "template",
		Data: Data{
			TemplateID:          "AAqHFrnvYs7hc",
			TemplateVersionName: "1.0.2",
			TemplateVariable: TemplateData{
				Subject: subject,
				Body:    body,
			},
		},
	}
	// 将结构体序列化为 JSON 字符串
	jsonData, err := json.MarshalIndent(template, "", "  ")
	if err != nil {
		fmt.Println("Error serializing JSON:", err)
		return err
	}
	for _, to := range mailTo {
		_, err := SendFeiShuCardMessage(to, string(jsonData))
		if err != nil {
			return err
		}
		time.Sleep(25 * time.Millisecond)
	}
	return nil
}

// feishu发送卡片消息
func SendFeiShuCardMessage(email, messageStr string) (*larkim.CreateMessageResp, error) {
	// 创建 Client
	client := lark.NewClient("********************", "52rSkFV9AEbiVbPn8arumdvhViM0BdcW")
	// 创建请求对象
	req := larkim.NewCreateMessageReqBuilder().
		ReceiveIdType(`email`).
		Body(larkim.NewCreateMessageReqBodyBuilder().
			ReceiveId(email).
			MsgType(`interactive`).
			Content(messageStr).
			Uuid(GetUUID()).
			Build()).
		Build()

	// 发起请求
	resp, err := client.Im.Message.Create(context.Background(), req)

	// 处理错误
	if err != nil {
		fmt.Println(err)
		return resp, err
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		return resp, errors.New(resp.Msg)
	}

	// 业务处理
	// fmt.Println(larkcore.Prettify(resp))
	return resp, nil
}
