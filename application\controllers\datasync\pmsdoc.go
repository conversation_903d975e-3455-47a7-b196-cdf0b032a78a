package datasync

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/datasync/dpmsdoc"
	"irisAdminApi/service/dao/release/dreleaseprojectconfig"
	"os"
	"path/filepath"
	"strings"

	"github.com/xuri/excelize/v2"
)

func RefreshItc() error {
	// 获取项目列表，查找打开通知的项目
	enabledNoticeProjects, err := dreleaseprojectconfig.AllEnabledConfig()
	if err != nil {
		logging.ErrorLogger.Error("get project config err ", err)
		return err
	}

	for _, config := range enabledNoticeProjects {
		project := config.ReleaseProject.Name
		outputPath := filepath.Join(libs.Config.DataSync.Svn.OutputPath, project)
		err := SvnCheckout(project, outputPath)
		if err != nil {
			return err
		}
		GatherItcIDFromITRWowker(project, outputPath)
	}
	return nil
}

func SvnCheckout(project, outputPath string) error {
	seUrl := fmt.Sprintf("http://svn.ruijie.net/svn3/ntos_docs/%s/report/SE", project)
	// if _, err := os.Stat(output); errors.Is(err, fs.ErrNotExist) {
	// }
	command := fmt.Sprintf("rm %s -rf && svn export --username %s --password %s --force %s %s", outputPath, libs.Config.DataSync.Svn.Username, libs.Config.DataSync.Svn.Password, seUrl, outputPath)
	output, err := libs.ExecCommand(command)
	if err != nil {
		logging.ErrorLogger.Error("svn export err ", err, seUrl, output)
		return err
	}
	return nil
}

func GatherItcIDFromITRWowker(project, outputPath string) error {
	filepath.Walk(outputPath, func(fp string, fi os.FileInfo, err error) error {
		if !fi.IsDir() {
			if strings.Contains(fi.Name(), "ITR") {
				objects := []map[string]interface{}{}
				itcIDs, err := GatherItcIDFromITRWJob(fp)
				if err != nil {
					object := map[string]interface{}{
						"ProjectName":     project,
						"DocumentName":    fi.Name(),
						"ItcID":           "",
						"RFID":            "",
						"ItcGatherStatus": false,
						"ItcGatherError":  err.Error(),
						"DeletedAt":       nil,
					}
					objects = append(objects, object)
					daoErr := dpmsdoc.DeleteByProjectAndDocument(project, fi.Name(), "")
					if daoErr != nil {
						logging.ErrorLogger.Error("delete pms doc itc record err ", project, fp)
					}
				} else {
					for _, itcID := range itcIDs {
						object := map[string]interface{}{
							"ProjectName":     project,
							"DocumentName":    fi.Name(),
							"ItcID":           itcID[0],
							"RFID":            itcID[1],
							"ItcGatherStatus": true,
							"ItcGatherError":  "",
							"DeletedAt":       nil,
						}

						objects = append(objects, object)
					}
					daoErr := dpmsdoc.DeleteByProjectAndDocument(project, fi.Name(), "0")
					if daoErr != nil {
						logging.ErrorLogger.Error("delete pms doc itc record err ", project, fp)
					}
				}

				daoErr := dpmsdoc.UpdateOrCreatePmsDocTransaction(objects)
				if daoErr != nil {
					logging.ErrorLogger.Error("create pms doc itc record err ", project, fp)
				}
			}
		}
		return nil
	})
	return nil
}

func GatherItcIDFromITRWJob(fp string) ([][]string, error) {
	itcs := [][]string{}
	f, err := excelize.OpenFile(fp)
	if err != nil {
		return itcs, err
	}
	defer f.Close()
	// 检查ITR是否符合规范
	sheetNames := f.GetSheetList()
	if len(sheetNames) != 3 {
		return itcs, fmt.Errorf("ITR文档sheet页面数量为%d个, 规范要求为3个", len(sheetNames))
	}
	checks := []string{"封面", "帮助", "内容"}
	for _, check := range checks {
		if !libs.InArrayS(sheetNames, check) {
			return itcs, fmt.Errorf("ITR文档sheet页面名称%v与模板%v不一至", sheetNames, checks)
		}
	}

	// 获取 内容sheet页 上所有列
	rows, err := f.GetRows("内容")
	if err != nil {
		return itcs, err
	}
	if len(rows) >= 2 && len(rows[0]) >= 2 && len(rows[1]) >= 3 && rows[0][1] == "测试用例编号" && rows[1][2] == "RF用例编号" {
		for _, row := range rows[2:] {
			if len(row) >= 3 {
				itcs = append(itcs, []string{row[1], row[2]})
			}
		}
	}

	if len(itcs) == 0 {
		return itcs, fmt.Errorf("未解析到ITC和RF用例编号")
	}
	// for cols.Next() {
	// 	rows, err := cols.Rows()
	// 	if err != nil {
	// 		return itcs, err
	// 	}
	// 	if rows[0] == "测试用例编号" {
	// 		// 处理用例编号
	// 		for _, row := range rows[1:] {
	// 			if len(row) > 0 {
	// 				itcs = append(itcs, row)
	// 			}
	// 		}
	// 	}
	// 	if rows[1] == "RF用例编号" {
	// 		// 处理用例编号
	// 		for _, row := range rows[2:] {
	// 			if len(row) > 0 {
	// 				itcs = append(itcs, row)
	// 			}
	// 		}
	// 	}
	// }

	return itcs, nil
}
