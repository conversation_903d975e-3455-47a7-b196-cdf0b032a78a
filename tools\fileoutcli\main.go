package main

import (
	"bufio"
	"bytes"
	"crypto/md5"
	"crypto/sha256"
	"encoding/json"
	"errors"
	"flag"
	"fmt"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strconv"

	"golang.org/x/term"
)

var host = "************:9000"

// var host = "**************:9001"

type Token struct {
	Code    int               `json:"code"`
	Data    map[string]string `json:"data"`
	Message string            `json:"message"`
}

type Profile struct {
	Code int `json:"code"`
}

type Approval struct {
	Code    int          `json:"code"`
	Data    ApprovalData `json:"data"`
	Message string       `json:"message"`
}

type ApprovalData struct {
	Id    int `json:"id"`
	Force int `json:"force"`
}

type Auditors struct {
	Code    int       `json:"code"`
	Data    []Auditor `json:"data"`
	Message string    `json:"message"`
}

type Auditor struct {
	Id       int    `json:"id"`
	Username string `json:"username"`
	Name     string `json:"name"`
}

var secret = "ThisisSecret1"

var client = &http.Client{}

func MD5(str string) (string, error) {
	data, err := ioutil.ReadFile(str)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%x", md5.Sum(data)), nil
}

func getToken(username string, password string) (string, error) {
	var token string
	fmt.Println("检测登陆状态")

	h := sha256.New()
	h.Write([]byte(username + secret + password))
	tokenFile := "/tmp/" + fmt.Sprintf("%x", h.Sum(nil))
	// tokenFile := "/tmp/"
	if _, err := os.Stat(tokenFile); os.IsNotExist(err) {
		token, err := Login(username, password, tokenFile)
		if err != nil {
			fmt.Println("使用用户名密码登陆失败", err)
			return token, err
		}
	}
	fmt.Println("发现Token文件,正在检查Token是否有效...")
	f, err := os.OpenFile(tokenFile, os.O_RDONLY, 0666)
	if err != nil {
		fmt.Println("打开Token文件异常", err)
		return token, err
	}
	defer f.Close()
	bufReader := bufio.NewReader(f)
	line, err := bufReader.ReadBytes('\n')
	token = string(line)
	err = checkToken(token)
	if err != nil {
		fmt.Println("检查Token无效，使用用户名密码登陆")
		token, err = Login(username, password, tokenFile)
		if err != nil {
			fmt.Println("使用用户名密码登陆失败", err)
			return token, err
		}
	}
	fmt.Println("Token有效，获取审批人清单")
	return token, nil
}

func Login(username string, password string, tokenFile string) (string, error) {

	url := fmt.Sprintf("http://%s/api/v1/user/login", host)
	jsonStr, _ := json.Marshal(map[string]interface{}{
		"disable_encrypt": true,
		"username":        username,
		"password":        string(password),
	})
	reqest, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	if err != nil {
		return "", err
	}
	//处理返回结果
	response, err := client.Do(reqest)
	if err != nil {
		return "", err
	}
	defer response.Body.Close()
	bodyByte, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return "", err
	}
	response.Body.Close()

	ret := Token{}
	err = json.Unmarshal(bodyByte, &ret)
	if err != nil {
		return "", err
	}
	if ret.Code == 20000 {
		f, err := os.OpenFile(tokenFile, os.O_RDWR|os.O_CREATE, 0666)
		if err != nil {
			fmt.Println("打开Token文件异常", err)
		}
		defer f.Close()
		f.WriteString(ret.Data["AccessToken"])
		return ret.Data["AccessToken"], nil
	}
	return "", errors.New(ret.Message)

}

func checkToken(token string) error {
	url := fmt.Sprintf("http://%s/api/v1/user/profile", host)
	reqest, err := http.NewRequest("GET", url, nil)

	//增加header选项
	reqest.Header.Add("Authorization", "Bearer "+token)
	if err != nil {
		return err
	}
	//处理返回结果
	response, err := client.Do(reqest)
	if err != nil {
		return err
	}
	defer response.Body.Close()
	bodyByte, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return err
	}
	response.Body.Close()
	ret := Profile{}
	err = json.Unmarshal(bodyByte, &ret)
	if err != nil {
		return err
	}
	if ret.Code == 20000 {
		return nil
	}
	return errors.New("登陆状态异常")
}

func createApproval(token string, auditorId int, filename string, comment string) error {
	url := fmt.Sprintf("http://%s/api/v1/approval", host)
	bodyBuf := &bytes.Buffer{}
	bodyWriter := multipart.NewWriter(bodyBuf)
	bodyWriter.WriteField("auditor_id", strconv.Itoa(auditorId))
	bodyWriter.WriteField("comment", comment)
	//关键的一步操作
	md5, err := MD5(filename)
	if err != nil {
		fmt.Println("md5计算失败")
		return err
	}
	bodyWriter.WriteField("md5", md5)
	fileWriter, err := bodyWriter.CreateFormFile("file", filepath.Base(filename))
	if err != nil {
		fmt.Println("error writing to buffer")
		return err
	}

	//打开文件句柄操作
	fh, err := os.Open(filename)
	if err != nil {
		fmt.Println("error opening file")
		return err
	}
	defer fh.Close()

	//iocopy
	_, err = io.Copy(fileWriter, fh)
	if err != nil {
		return err
	}

	contentType := bodyWriter.FormDataContentType()
	bodyWriter.Close()

	reqest, err := http.NewRequest("POST", url, bodyBuf)
	if err != nil {
		return err
	}
	reqest.Header.Set("Content-Type", contentType)
	reqest.Header.Add("Authorization", "Bearer "+token)
	//处理返回结果
	resp, err := client.Do(reqest)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	resp_body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	ret := Approval{}
	err = json.Unmarshal(resp_body, &ret)
	if err != nil {
		return err
	}
	if ret.Code == 20000 {
		if ret.Data.Force == 1 {
			fmt.Println("提交成功,等待审批通过")
		} else {
			fmt.Println("已自动审批通过")
		}
	}
	return errors.New(ret.Message)
}

func getAuditors(token, quick string) (int, error) {
	var auditorId int
	url := fmt.Sprintf("http://%s/api/v1/approval/auditor", host)
	reqest, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return 0, err
	}
	reqest.Header.Add("Authorization", "Bearer "+token)
	//处理返回结果
	response, err := client.Do(reqest)
	if err != nil {
		return 0, err
	}
	defer response.Body.Close()
	bodyByte, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return 0, err
	}
	response.Body.Close()

	ret := Auditors{}
	err = json.Unmarshal(bodyByte, &ret)
	if err != nil {
		return 0, err
	}
	if ret.Code == 20000 {
		auditors := ret.Data
		if len(auditors) == 1 {
			fmt.Println("已选择唯一审批人", auditors[0].Name)
			return auditors[0].Id, nil
		}
		if len(auditors) > 1 {
			if quick == "" {
				fmt.Println("审批人清单：")
				for _, auditor := range auditors {
					fmt.Println(auditor.Id, auditor.Name)
				}
				for {
					fmt.Println("请输入编号选择审批人")
					fmt.Scanln(&auditorId)
					for _, auditor := range auditors {
						if auditorId == auditor.Id {
							return auditorId, nil
						}
					}
					fmt.Println("输入错误，请重新输入")
				}
			} else {
				for _, auditor := range auditors {
					if quick == fmt.Sprintf("%d", auditor.Id) || quick == auditor.Name || quick == auditor.Username {
						return auditor.Id, nil
					}
				}
			}
		}
	}
	return 0, errors.New(ret.Message)
}

func updateApproval(token string, id int) error {
	url := fmt.Sprintf("http://%s/api/v1/approval/%d", host, id)
	jsonStr, _ := json.Marshal(map[string]interface{}{
		"force": 1,
	})
	reqest, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	if err != nil {
		return err
	}
	reqest.Header.Add("Authorization", "Bearer "+token)
	//处理返回结果
	response, err := client.Do(reqest)
	if err != nil {
		return err
	}
	defer response.Body.Close()
	bodyByte, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return err
	}
	response.Body.Close()

	ret := Approval{}
	err = json.Unmarshal(bodyByte, &ret)
	if err != nil {
		return err
	}
	if ret.Code == 20000 {
		fmt.Println("创建申请单成功")
		return nil
	}
	return errors.New("创建申请单失败")
}

// sample usage
func main() {
	// checkLogin("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2Mjg3MTk1NTMsImlhdCI6IjE0MjUzMzc2NTcwNTkxODQ2NDAifQ.7W865rsfQJPhPgaLOF5UfaFKaMnQAlWmr4UHoYJFQsQ")
	// getToken()
	var username, password, quick, filepath, comment string
	var err error
	flag.StringVar(&username, "u", "", "文件外出系统用户名")
	flag.StringVar(&password, "p", "", "文件外出系统密码")
	flag.StringVar(&quick, "a", "", "审批人,审批人ID/审批人姓名/审批人邮箱前缀")
	flag.StringVar(&filepath, "f", "", "需要申请外出的文件路径")
	flag.StringVar(&comment, "m", "", "说明")
	flag.Parse()
	if len(filepath) == 0 {
		flag.CommandLine.Usage()
		os.Exit(0)
	}
	if len(username) == 0 {
		flag.CommandLine.Usage()
		os.Exit(0)
	}
	if password == "" {
		fmt.Printf("密码: ")
		_password, err := term.ReadPassword(int(os.Stdin.Fd()))
		if err != nil {
			fmt.Println(err.Error())
			os.Exit(1)
		}
		fmt.Println("")
		password = string(_password)
	}

	token, err := getToken(username, password)
	if err != nil {
		fmt.Println(err.Error())
		os.Exit(0)
	}
	auditorId, err := getAuditors(token, quick)
	if err != nil {
		fmt.Println("获取审批人列表失败", err)
	}
	err = createApproval(token, auditorId, filepath, comment)
	if err != nil {
		fmt.Println(err.Error())
	}
}
