package buildfarm

import (
	"fmt"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/buildfarm/dcronmakejob"
	"irisAdminApi/service/dao/datasync/dbug"
	"irisAdminApi/service/dao/datasync/dpmsprojectmember"
	"irisAdminApi/service/dao/datasync/dsyncrecord"
	"irisAdminApi/service/dao/release/dreleaseprojectconfig"

	"github.com/kataras/iris/v12"
	"github.com/xuri/excelize/v2"
)

func GetCronMakeJobReport(ctx iris.Context) {
	type Request struct {
		IDs         []uint `json:"ids"`
		WithShell   bool   `json:"with_shell"`   // 是否带shell
		WithRelease bool   `json:"with_release"` // 是否在release-id目录下
	}
	request := Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(&request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": strings.Join(errs, ";")}, response.SystemErr.Msg))
		return
	}

	cronMakeJobs, err := dcronmakejob.FindCronMakeJobInIDs(request.IDs)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	results := [][]interface{}{{"项目", "产品", "下载地址", "编译类型", "文件MD5", "文件大小", "冒烟状态"}}
	for _, cronMakeJob := range cronMakeJobs {
		if cronMakeJob.Status != 1 {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("%s 编译未成功，无法导出", cronMakeJob.JobId)))
			return
		}
		if cronMakeJob.Cpu.Cpu == "" {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("%s Cpu映射未添加，请联系管理员添加", cronMakeJob.Product)))
			return
		}
		var targetPath, urlPrefix string
		if request.WithRelease {
			targetPath = filepath.Join(libs.Config.Buildfarm.Archivepath, cronMakeJob.JobId, cronMakeJob.Cpu.Cpu, "releaseID-bin")
			urlPrefix = fmt.Sprintf("http://%s:%d/output/%s/%s/releaseID-bin", libs.Config.Nginx.HOST, libs.Config.Nginx.Port, cronMakeJob.JobId, cronMakeJob.Cpu.Cpu)
		} else {
			targetPath = filepath.Join(libs.Config.Buildfarm.Archivepath, cronMakeJob.JobId, cronMakeJob.Cpu.Cpu)
			urlPrefix = fmt.Sprintf("http://%s:%d/output/%s/%s", libs.Config.Nginx.HOST, libs.Config.Nginx.Port, cronMakeJob.JobId, cronMakeJob.Cpu.Cpu)
		}

		if _, err := os.Stat(targetPath); os.IsNotExist(err) {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("%s 文件不存在", cronMakeJob.JobId)))
			return
		}

		entries, err := os.ReadDir(targetPath)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("%s 获取文件错误: %s", cronMakeJob.JobId, err.Error())))
			return
		}

		for _, entry := range entries {
			info, err := entry.Info()
			if err != nil {
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("%s 获取文件错误: %s", cronMakeJob.JobId, err.Error())))
				return
			}
			fn := info.Name()
			if strings.HasSuffix(fn, "_install.bin") && strings.Contains(fn, "NTOS") {
				if (request.WithShell && strings.Contains(fn, "shell")) || (!request.WithShell && !strings.Contains(fn, "shell")) {
					md5, err := libs.GetFileMd5(filepath.Join(targetPath, fn))
					if err != nil {
						ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("%s 获取文件错误: %s", cronMakeJob.JobId, err.Error())))
						return
					}
					results = append(results, []interface{}{
						cronMakeJob.Branch,
						cronMakeJob.Product,
						fmt.Sprintf("%s/%s", urlPrefix, fn),
						cronMakeJob.BuildType,
						md5,
						info.Size(),
						cronMakeJob.SmokeStatus,
					})
				}
			}
		}

	}
	fileName := fmt.Sprintf("每日编译_%s.xlsx", time.Now().Format("20060102150405"))
	file := excelize.NewFile()
	streamWriter, err := file.NewStreamWriter("Sheet1")
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	rowNum := 1
	for _, row := range results {
		cell, _ := excelize.CoordinatesToCellName(1, rowNum)
		if err := streamWriter.SetRow(cell, row); err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
			return
		}
		rowNum++
	}
	if err := streamWriter.Flush(); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	if err := file.SaveAs(filepath.Join("/tmp", fileName)); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	defer os.Remove(filepath.Join("/tmp", fileName))
	ctx.SendFile(filepath.Join("/tmp", fileName), url.QueryEscape(fileName))
	return
}

const TestLinkStyle = `
<style>
	table {
		border-collapse: collapse;
	}
	th {
		background-color: #007fff;
		color: white;
	}
	table, th, td {
		border: 1px solid black;
		padding: 5px;
		text-align: left;
	}
</style>
`

func SendTestLink(ctx iris.Context) {
	type Request struct {
		IDs         []uint `json:"ids"`
		WithShell   bool   `json:"with_shell"`   // 是否带shell
		WithRelease bool   `json:"with_release"` // 是否在release-id目录下
	}
	request := Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(&request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": strings.Join(errs, ";")}, response.SystemErr.Msg))
		return
	}

	withShell := request.WithShell

	cronMakeJobs, err := dcronmakejob.FindCronMakeJobInIDs(request.IDs)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	// 所有作业完成, 查询时间周期内所有CBD状态BUG
	check := map[string]bool{}
	branches := []string{}

	result := [][]interface{}{{"项目", "产品", "下载地址", "文件名", "编译类型", "文件MD5", "文件大小", "冒烟状态"}}
	for _, job := range cronMakeJobs {
		if job.Status != 1 {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("%s 编译未成功，无法导出", job.JobId)))
			return
		}
		if job.Cpu.Cpu == "" {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("%s Cpu映射未添加，请联系管理员添加", job.Product)))
			return
		}

		if !libs.InArrayS(branches, job.Branch) {
			branches = append(branches, job.Branch)
		}

		if len(branches) > 1 {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "选择了多个分支, 请确认"))
			return
		}

		var targetPath, urlPrefix string
		if request.WithRelease {
			targetPath = filepath.Join(libs.Config.Buildfarm.Archivepath, job.JobId, job.Cpu.Cpu, "releaseID-bin")
			urlPrefix = fmt.Sprintf("http://%s:%d/output/%s/%s/releaseID-bin", libs.Config.Nginx.HOST, libs.Config.Nginx.Port, job.JobId, job.Cpu.Cpu)
		} else {
			targetPath = filepath.Join(libs.Config.Buildfarm.Archivepath, job.JobId, job.Cpu.Cpu)
			urlPrefix = fmt.Sprintf("http://%s:%d/output/%s/%s", libs.Config.Nginx.HOST, libs.Config.Nginx.Port, job.JobId, job.Cpu.Cpu)
		}

		if _, err := os.Stat(targetPath); os.IsNotExist(err) {
			logging.ErrorLogger.Errorf("%s 文件不存在", job.JobId)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("%s 文件不存在", job.JobId)))
			return
		}

		entries, err := os.ReadDir(targetPath)
		if err != nil {
			logging.ErrorLogger.Errorf("%s 读取目录错误: %s", job.JobId, err.Error())
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("%s 读取目录错误: %s", job.JobId, err.Error())))
			return
		}

		for _, entry := range entries {
			info, err := entry.Info()
			if err != nil {
				logging.ErrorLogger.Errorf("%s 获取文件错误: %s", job.JobId, err.Error())
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("%s 获取文件错误: %s", job.JobId, err.Error())))
				return
			}
			fn := info.Name()
			if strings.HasSuffix(fn, "_install.bin") && strings.Contains(fn, "NTOS") {
				if (withShell && strings.Contains(fn, "shell")) || (!withShell && !strings.Contains(fn, "shell")) {
					md5, err := libs.GetFileMd5(filepath.Join(targetPath, fn))
					if err != nil {
						logging.ErrorLogger.Errorf("%s 获取文件错误: %s", job.JobId, err.Error())
						ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("%s 获取文件错误: %s", job.JobId, err.Error())))
						return
					}
					if _, ok := check[fmt.Sprintf("%s_%s", job.Product, job.BuildType)]; !ok {
						result = append(result, []interface{}{
							job.Branch,
							job.Product,
							fmt.Sprintf("%s/%s", urlPrefix, fn),
							fn,
							job.BuildType,
							md5,
							info.Size(),
							job.SmokeStatus,
						})
						check[fmt.Sprintf("%s_%s", job.Product, job.BuildType)] = true
					}
				}
			}
		}
	}

	config := dreleaseprojectconfig.ReleaseProjectConfig{}
	err = config.FindEx("build_project_branch", branches[0])
	if err != nil {
		logging.ErrorLogger.Errorf("项目配置错误: %s", err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("项目配置错误: %s", err.Error())))
		return
	}

	if config.ID == 0 {
		logging.ErrorLogger.Errorf("%s 项目配置不存在", branches[0])
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("%s 项目配置不存在", branches[0])))
		return
	}

	testLinkRoles := strings.Split(config.TestLinkRoles, ",")
	testLinkReceivers := strings.Split(config.TestLinkReceivers, ",")

	mailTo := []string{}
	mailTo = append(mailTo, testLinkReceivers...)

	if len(testLinkRoles) > 0 && len(testLinkReceivers) == 0 {
		logging.ErrorLogger.Errorf("%s 转测邮件接收角色为空", branches[0])
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("%s 转测邮件接收角色为空", branches[0])))
		return
	}

	members, err := dpmsprojectmember.FindMembersByProjectAndRole(config.ReleaseProject.Name, testLinkRoles)
	if err != nil {
		logging.ErrorLogger.Errorf("数据库错误: %s", err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("数据库错误: %s", err.Error())))
		return
	}

	for _, member := range members {
		if len(member.CasUserID) > 0 {
			mailTo = append(mailTo, member.CasUserID+"@ruijie.com.cn")
		} else {
			logging.ErrorLogger.Error("member: %s  without casUserId ", member.Username)
		}
	}

	now := time.Now()
	nowStr := now.Format("2006-01-02")
	start := now.Add(-24 * time.Hour)
	end := now
	var name string

	if nowStr >= config.TestStartAt && nowStr < config.SecondTestStartAt {
		name = "首轮测试"
	} else if nowStr >= config.SecondTestStartAt && nowStr < config.RegressTestStartAt {
		name = "次轮测试"
	} else if nowStr >= config.RegressTestStartAt {
		name = "回归测试"
	}

	if libs.Config.Mail.Enable {
		from := "编译农场"

		subject := fmt.Sprintf("关于%s%s版本更新【%s】", config.ReleaseProject.Name, name, now.Format("2006-01-02"))

		mailMsg := TestLinkStyle + `<h2>转测版本</h2>`

		mailMsg = mailMsg + `<table><tr>`
		// 	result := [][]interface{}{{"分支", "产品", "下载地址", "文件名", "编译类型", "文件MD5", "文件大小", "冒烟状态"}}
		// for _, item := range result[0] {
		// 	mailMsg = mailMsg + fmt.Sprintf(`<th style="width: 200px">%s</th>`, item)
		// }
		mailMsg += `
<th style="width: 200px">分支</th>
<th style="width: 200px">产品</th>
<th style="width: 800px">下载地址</th>
<th style="width: 200px">编译类型</th>
<th style="width: 400px">文件MD5</th>
<th style="width: 200px">文件大小</th>
<th style="width: 800px">冒烟状态</th>
		`
		mailMsg += `</tr>`

		for _, data := range result[1:] {
			// mailMsg = mailMsg + `<tr>`
			// for _, item := range data {
			// 	mailMsg = mailMsg + fmt.Sprintf(`<td>%v</td>`, item)
			// }
			mailMsg = fmt.Sprintf(`%s
				<tr>
					<td>%v</td>
					<td>%v</td>
					<td><a href="%v">%v</a></td>
					<td>%v</td>
					<td>%v</td>
					<td>%v</td>
					<td>%v</td>
				</tr>`,
				mailMsg,
				data[0],
				data[1],
				data[2],
				data[3],
				data[4],
				data[5],
				data[6],
				data[7],
			)
			// mailMsg = mailMsg + `</tr>`
		}
		mailMsg = mailMsg + `</table>`

		// 附加24小时内CBD的BUG信息
		// 增加提示，显示BUG数据同步时间
		var updatedAt string
		_url := "https://dataware.ruijie.com.cn/api/public/data-api/security_product_sw_bug/list.data"
		records, err := dsyncrecord.FindLastSuccessSyncRecord(_url)
		if err != nil {
			logging.ErrorLogger.Errorf("get last sync records", err.Error())
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("get last sync records %s", err.Error())))
			return
		}
		if len(records) > 0 {
			updatedAt = records[0].CreatedAt.Format("2006-01-02 15:04:05")
		}

		bugs, err := dbug.FindCbdBugInTime(config.ReleaseProject.Name, start, end)
		if err != nil {
			mailMsg = mailMsg + "<h2>Bug查询错误</h2>"
		} else {
			mailMsg = mailMsg + fmt.Sprintf(`<h2>Bug CBD 【%v个】。统计基于%s同步的BUG数据</h2>`, len(bugs), updatedAt)
			if len(bugs) > 0 {
				mailMsg += `<table><tr>`
				// for _, item := range []string{"BUGID", "状态", "BUG简介", "操作系统", "严重性", "优先级", "重复性", "提交者", "BUG负责人", "工作包名称", "CBD时间"} {
				// 	mailMsg = mailMsg + fmt.Sprintf(`<th style="width: 200px">%s</th>`, item)
				// }
				mailMsg += `
<th style="width: 120px">BUGID</th>
<th style="width: 150px">状态</th>
<th style="width: 800px">BUG简介</th>
<th style="width: 120px">操作系统</th>
<th style="width: 100px">严重性</th>
<th style="width: 100px">优先级</th>
<th style="width: 100px">重复性</th>
<th style="width: 200px">提交者</th>
<th style="width: 200px">BUG负责人</th>
<th style="width: 400px">工作包名称</th>
<th style="width: 400px">CBD时间</th>
				`
				mailMsg += `</tr>`
			}
			for _, bug := range bugs {
				mailMsg = fmt.Sprintf(`%s
					<tr>
					<td><a href="http://bugs.ruijie.com.cn/bug_switch/bug/main?bugId=%v">%v</a></td>
					<td>%v</td>
					<td>%v</td>
					<td>%v</td>
					<td>%v</td>
					<td>%v</td>
					<td>%v</td>
					<td>%v</td>
					<td>%v</td>
					<td>%v</td>
					<td>%v</td>
					</tr>`,
					mailMsg,
					bug.BugID,
					bug.BugID,
					bug.BugState,
					bug.BugSummary,
					bug.BugOS,
					bug.BugPriority,
					bug.BugSeverity,
					bug.BugRepro,
					bug.BugSubmitter,
					bug.BugOwner,
					bug.BugWorkpacketName,
					bug.BugCbdAt.Format("2006-01-02 15:04:05"),
				)
			}
			mailMsg = mailMsg + `</table>`
		}

		if len(mailTo) > 0 {
			err = libs.SendMailRedis(from, mailTo, subject, mailMsg, []string{})
			if err != nil {
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("send mail err %s", err.Error())))
				return
			}
		}
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}
