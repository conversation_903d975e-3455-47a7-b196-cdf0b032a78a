package dbuildfarmprojectinfo

import (
	"fmt"
	"reflect"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/buildfarm"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "编译农场编译信息表"

type BuildfarmProjectInfo struct {
	buildfarm.BuildfarmProjectInfo
}

type ListResponse struct {
	BuildfarmProjectInfo
}

type Request struct {
	BuildfarmProjectInfo
}

func (this *BuildfarmProjectInfo) ModelName() string {
	return ModelName
}

func Model() *buildfarm.BuildfarmProjectInfo {
	return &buildfarm.BuildfarmProjectInfo{}
}

func (this *BuildfarmProjectInfo) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *BuildfarmProjectInfo) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *BuildfarmProjectInfo) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *BuildfarmProjectInfo) CreateV2(object interface{}) error {
	return nil
}

func (this *BuildfarmProjectInfo) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmProjectInfo) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmProjectInfo) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmProjectInfo) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmProjectInfo) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmProjectInfo) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindDistinctByColumn(column string) ([]string, error) {
	result := []string{}
	err := easygorm.GetEasyGormDb().Model(Model()).Distinct(column).Order(column).Pluck(column, &result).Error
	return result, err
}

func BatchCreateOrUpdate(records []map[string]interface{}) error {
	xt := reflect.TypeOf(Model())
	columns := []string{}
	for i := 0; i < xt.Elem().NumField(); i++ {
		key, ok := xt.Elem().Field(i).Tag.Lookup("update")
		if ok {
			columns = append(columns, key)
		}
	}
	db := easygorm.GetEasyGormDb().Model(Model())

	err := db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "bug_id"}},
		DoUpdates: clause.AssignmentColumns(columns),
	}).Create(&records).Error
	if err != nil {
		return err
	}

	return nil
}

func UpdateOrCreateBuildFarmProjectInfoTransaction(items []map[string]interface{}) error {
	/*
	   ProjectID uint   `gorm:"index:idx_project_info, unique; not null;" json:"project_id"`
	   Branch    string `gorm:"index:idx_project_info, unique; not null; type:varchar(100)" json:"branch"`
	   Product   string `gorm:"index:idx_project_info, unique; not null; type:varchar(100)" json:"product"`
	   Defconfig string `gorm:"index:idx_project_info, unique; not null; type:varchar(100)" json:"defconfig"`
	   Target    string `gorm:"index:idx_project_info, unique; not null; type:varchar(100)" json:"target"`
	*/
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		if len(items) > 0 {
			err := tx.Model(Model()).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "project_id"}, {Name: "branch"}, {Name: "product"}, {Name: "defconfig"}, {Name: "target"}},
				DoUpdates: clause.AssignmentColumns([]string{"updated_at"}),
			}).Create(&items).Error
			if err != nil {
				return err
			}
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func CleanOverTimeProjectInfo(projectID uint, updatedAt time.Time) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), "project_id = ? and updated_at < ?", projectID, updatedAt).Error
	return err
}

func GetBranches(projectID string) ([]string, error) {
	result := []string{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("project_id = ?", projectID).Distinct("branch").Order("branch desc").Pluck("branch", &result).Error
	return result, err
}

func GetProducts(projectID, branch string) ([]string, error) {
	result := []string{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("project_id = ? and branch = ? and product != ''", projectID, branch).Distinct("product").Order("product desc").Pluck("product", &result).Error
	return result, err
}

func GetDefconfigs(projectID, branch, product string) ([]string, error) {
	result := []string{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("project_id = ? and branch = ? and product = ? and defconfig != ''", projectID, branch, product).Distinct("defconfig").Order("defconfig desc").Pluck("defconfig", &result).Error
	return result, err
}

func GetTargets(projectID, branch, product, defconfig string) ([]string, error) {
	result := []string{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("project_id = ? and branch = ? and product = ? and defconfig = ? and target != ''", projectID, branch, product, defconfig).Distinct("target").Order("target asc").Pluck("target", &result).Error
	return result, err
}

func GetFeed(projectID, branch, product, defconfig string) ([]string, error) {
	result := []string{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("project_id = ? and branch = ? and product = ? and defconfig = ? and feed != ''", projectID, branch, product, product+"_defconfig").Order("target asc").Pluck("Feed", &result).Error
	return result, err
}
