# Coredump记录自动化处理系统 - 字段设计规范

## 📋 概述

本文档详细说明了飞书多维表格中各个字段的设计规范，包括字段用途、取值范围、填写规则和系统行为。

## 📊 字段分类

### 🔵 用户填写字段（业务数据）

这些字段由用户在创建Coredump记录时填写，系统不会修改。

| 字段名称 | 字段类型 | 必填 | 说明 | 示例值 |
|---------|----------|------|------|--------|
| **SN** | 文本 | ✅ | 设备序列号 | `ABC123456789` |
| **coredump组件** | 文本 | ✅ | 出现问题的组件名称 | `网络管理模块` |
| **软件版本** | 文本 | ✅ | 软件版本号 | `v2.1.3` |
| **设备型号** | 文本 | ✅ | 设备型号 | `RG-EG210G-P` |
| **coredump收集url** | 文本 | ❌ | Coredump文件下载链接 | `http://example.com/dump/123` |
| **coredump时间** | 日期时间 | ✅ | Coredump发生时间 | `2025-01-14 10:30:00` |
| **堆栈信息** | 多行文本 | ❌ | 堆栈跟踪信息 | `Stack trace...` |
| **组件负责人** | 人员 | ❌ | 组件负责人 | `张三` |
| **进程负责人** | 人员 | ❌ | 进程负责人 | `李四` |
| **说明** | 多行文本 | ❌ | 问题描述 | `系统崩溃，需要分析` |

### 🟢 用户控制字段（同步控制）

这些字段由用户设置，用于控制系统行为。

| 字段名称 | 字段类型 | 必填 | 说明 | 取值 | 默认值 |
|---------|----------|------|------|------|--------|
| **是否需要同步Bug系统** | 单选 | ✅ | 控制是否需要同步到Bug系统 | `Y`/`N` | `N` |

**使用说明**：
- 用户将此字段设置为 `Y` 时，系统会将该记录同步到Bug系统
- 设置为 `N` 时，系统会忽略该记录
- 用户可以随时修改此字段来控制同步行为

### 🟡 系统管理字段（状态跟踪）

这些字段完全由系统自动管理，用户不应手动修改。

| 字段名称 | 字段类型 | 说明 | 取值范围 | 初始值 |
|---------|----------|------|----------|--------|
| **是否已同步bug系统** | 单选 | 同步状态标记 | `Y`/`N`/`""` | `""` |
| **处理状态** | 单选 | 当前处理状态 | `""`/`待处理`/`处理中`/`成功`/`失败` | `""` |
| **Bug系统ID** | 文本 | Bug系统中的ID | Bug ID字符串 | `""` |
| **处理时间** | 日期时间 | 开始处理的时间 | 时间戳 | `null` |
| **错误信息** | 多行文本 | 处理失败时的错误详情 | 错误描述文本 | `""` |
| **重试次数** | 数字 | 处理重试次数 | 0, 1, 2, 3... | `0` |
| **最后更新时间** | 日期时间 | 最后一次状态更新时间 | 时间戳 | `null` |

## 🔄 状态流转规则

### 处理状态流转

```
[新记录] → [处理中] → [成功]
   ""         ↓        ↓
              └─ [失败] ←┘
                  ↓
               [重试]
```

#### 状态说明

1. **新记录 ("")**
   - 用户刚创建的记录，处理状态为空
   - 满足筛选条件时会被系统处理

2. **处理中**
   - 系统正在处理该记录
   - 防止重复处理
   - 如果超时会被重置

3. **成功**
   - Bug提交成功
   - 不会被重复处理
   - `是否已同步bug系统` 同时更新为 `Y`

4. **失败**
   - Bug提交失败
   - 可以重试处理
   - 记录详细错误信息

### 同步状态流转

```
[未同步] → [已同步]
  ""/"N"      "Y"
```

## 🎯 筛选逻辑详解

### 主要筛选条件

系统会筛选满足以下**所有条件**的记录进行处理：

```sql
WHERE 是否需要同步Bug系统 = 'Y'
  AND 是否已同步bug系统 IN ('', 'N')
  AND (
    处理状态 = '' OR                    -- 新记录
    处理状态 = '待处理' OR               -- 明确标记待处理
    处理状态 = '失败' OR                 -- 失败可重试
    (处理状态 = '处理中' AND 处理时间 < NOW() - INTERVAL 1 HOUR)  -- 超时重置
  )
  AND SN != ''                          -- 必要字段不为空
  AND coredump组件 != ''
```

### 筛选逻辑说明

1. **基础筛选**
   - `是否需要同步Bug系统 = 'Y'`：用户明确要求同步
   - `是否已同步bug系统 ∈ ['', 'N']`：未同步或同步失败

2. **状态筛选**
   - `处理状态 = ''`：新记录，从未被处理
   - `处理状态 = '待处理'`：明确标记为待处理
   - `处理状态 = '失败'`：之前处理失败，可以重试
   - `处理状态 = '处理中' + 超时`：处理超时，需要重置

3. **数据完整性检查**
   - 必要字段不能为空
   - 跳过已知问题（可配置）

## ⚙️ 系统行为规范

### 处理开始时

```go
// 1. 更新状态为处理中
updateFields := map[string]interface{}{
    "处理状态": "处理中",
    "处理时间": time.Now(),
    "最后更新时间": time.Now(),
}
```

### 处理成功时

```go
// 2. 更新为成功状态
updateFields := map[string]interface{}{
    "处理状态": "成功",
    "是否已同步bug系统": "Y",
    "Bug系统ID": bugID,
    "错误信息": "",  // 清空错误信息
    "最后更新时间": time.Now(),
}
```

### 处理失败时

```go
// 3. 更新为失败状态
updateFields := map[string]interface{}{
    "处理状态": "失败",
    "错误信息": errorMessage,
    "重试次数": currentRetryCount + 1,
    "最后更新时间": time.Now(),
}
```

### 超时重置时

```go
// 4. 重置超时的处理中记录
updateFields := map[string]interface{}{
    "处理状态": "待处理",
    "错误信息": "处理超时，已重置为待处理状态",
    "最后更新时间": time.Now(),
}
```

## 🛡️ 数据完整性保护

### 字段验证规则

1. **必填字段检查**
   ```go
   if record.SN == "" || record.CoredumpComponent == "" {
       return errors.New("必填字段不能为空")
   }
   ```

2. **状态一致性检查**
   ```go
   if record.ProcessingStatus == "成功" && record.SyncStatus != "Y" {
       return errors.New("状态不一致：成功记录必须已同步")
   }
   ```

3. **时间逻辑检查**
   ```go
   if !record.ProcessingTime.IsZero() && record.ProcessingStatus == "" {
       return errors.New("时间逻辑错误：有处理时间但状态为空")
   }
   ```

### 并发控制

1. **防重复处理**
   - 通过"处理中"状态防止同一记录被多次处理
   - 超时机制防止记录永久锁定

2. **状态更新原子性**
   - 每次状态更新都是原子操作
   - 更新失败不会影响其他记录

## 📈 监控和统计

### 状态统计查询

```sql
-- 各状态记录数统计
SELECT 
    处理状态,
    COUNT(*) as 记录数
FROM coredump_table 
WHERE 是否需要同步Bug系统 = 'Y'
GROUP BY 处理状态;

-- 处理成功率
SELECT 
    COUNT(CASE WHEN 处理状态 = '成功' THEN 1 END) * 100.0 / COUNT(*) as 成功率
FROM coredump_table 
WHERE 是否需要同步Bug系统 = 'Y' 
  AND 处理状态 != '';
```

### 异常记录检查

```sql
-- 检查异常状态记录
SELECT * FROM coredump_table 
WHERE (
    -- 成功但未同步
    (处理状态 = '成功' AND 是否已同步bug系统 != 'Y') OR
    -- 有Bug ID但状态不是成功
    (Bug系统ID != '' AND 处理状态 != '成功') OR
    -- 处理中但无处理时间
    (处理状态 = '处理中' AND 处理时间 IS NULL)
);
```

## 🔧 配置参数

### 超时配置

```yaml
coredump:
  processing_timeout: 60  # 处理超时时间（分钟）
  max_retry_count: 3      # 最大重试次数
  batch_size: 50          # 批量处理大小
```

### 字段映射配置

```yaml
field_mapping:
  # 用户控制字段
  sync_required_field: "是否需要同步Bug系统"
  
  # 系统管理字段
  sync_status_field: "是否已同步bug系统"
  processing_status_field: "处理状态"
  bug_id_field: "Bug系统ID"
  processing_time_field: "处理时间"
  error_message_field: "错误信息"
  retry_count_field: "重试次数"
  last_updated_field: "最后更新时间"
```

## 📝 最佳实践

### 用户操作建议

1. **创建记录时**
   - 填写完整的必填字段
   - 根据需要设置"是否需要同步Bug系统"
   - 不要手动填写系统管理字段

2. **管理记录时**
   - 通过"是否需要同步Bug系统"字段控制同步
   - 查看"处理状态"了解处理进度
   - 查看"错误信息"排查问题

3. **问题排查时**
   - 检查必填字段是否完整
   - 确认"是否需要同步Bug系统"设置正确
   - 查看"错误信息"了解失败原因

### 系统维护建议

1. **定期检查**
   - 监控处理成功率
   - 检查异常状态记录
   - 清理过期的错误日志

2. **性能优化**
   - 合理设置批量处理大小
   - 调整超时时间参数
   - 监控API调用频率

---

**注意**: 此字段设计完全基于飞书多维表格，无需额外的数据库存储，所有状态对用户完全透明。