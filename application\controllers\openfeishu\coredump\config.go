package coredump

// 注意：配置结构已移至 application/libs/config.go 中统一管理

// 技术参数常量 - 固定的技术参数，用户通常不需要修改
const (
	DefaultPageSize   = 100 // 默认分页大小
	DefaultMaxRunTime = 30  // 默认最大运行时间(分钟)
	DefaultTimeout    = 60  // 默认API超时时间(秒)
	MaxRetryCount     = 3   // 最大重试次数
)

// 业务配置常量 - 将复杂的业务配置移到代码中管理
const (
	// 核心业务字段名称常量（与飞书多维表格字段完全匹配）
	FieldSN                   = "SN"             // 主键字段
	FieldRecordDate           = "记录日期"           // 记录创建日期
	FieldComponent            = "coredump组件"     // 组件名称
	FieldCoredumpTime         = "coredump时间"     // Coredump发生时间
	FieldDeviceModel          = "设备型号"           // 设备型号
	FieldSoftwareVersion      = "软件版本"           // 软件版本
	FieldStackInfo            = "堆栈信息"           // 堆栈跟踪信息
	FieldStackInfoEscaped     = "堆栈信息（转义）"       // 转义后的堆栈信息（公式字段）
	FieldComponentResponsible = "组件负责人"          // 组件负责人（公式字段）
	FieldProcessResponsible   = "进程负责人"          // 进程负责人（公式字段）
	FieldActualResponsible    = "实际负责人"          // 实际负责人（用户字段）
	FieldFileName             = "文件名"            // 文件名
	FieldCoredumpURL          = "coredump收集url"  // Coredump收集URL（公式字段）
	FieldIsKnownIssue         = "是否已知问题"         // 是否已知问题（单选）
	FieldProcessingResult     = "处理结果"           // 处理结果（单选）
	FieldFixVersion           = "修复版本"           // 修复版本
	FieldDescription          = "说明"             // 说明信息
	FieldComponentCategory    = "组件归属"           // 组件归属（查找字段）
	FieldCoreAlertCount       = "core告警次数"       // Core告警次数
	FieldCoredumpTypeGE4      = "coredump类型组件≥4" // Coredump类型组件≥4（公式字段）
	FieldBugOS                = "bug操作系统"        // Bug操作系统（单选）

	// 系统控制字段（已存在于飞书表格中）
	FieldSyncRequired = "是否需要同步Bug系统" // 系统控制字段（单选：Y/N）
	FieldSyncStatus   = "是否已同步bug系统"  // 同步状态字段（单选：Y/N）

	// 待添加的扩展字段（用于系统处理状态跟踪）
	FieldProcessingStatus = "处理状态"    // 处理状态字段（待添加）
	FieldBugID            = "Bug系统ID" // Bug系统ID（待添加）
	FieldProcessingTime   = "处理时间"    // 处理时间（待添加）
	FieldErrorMessage     = "错误信息"    // 错误信息（待添加）
	FieldRetryCount       = "重试次数"    // 重试次数（待添加）
	FieldLastUpdated      = "最后更新时间"  // 最后更新时间（待添加）

	// 处理状态常量
	StatusPending    = "待处理"
	StatusProcessing = "处理中"
	StatusSuccess    = "成功"
	StatusFailed     = "失败"

	// 同步状态常量
	SyncRequired     = "Y"
	SyncNotRequired  = "N"
	SyncCompleted    = "Y"
	SyncNotCompleted = "N"
)

// GetDefaultFieldNames 获取默认的字段名称列表（仅包含实际存在于飞书表格中的字段）
func GetDefaultFieldNames() []string {
	return []string{
		// 核心业务字段
		FieldSN,                   // SN
		FieldRecordDate,           // 记录日期
		FieldComponent,            // coredump组件
		FieldCoredumpTime,         // coredump时间
		FieldDeviceModel,          // 设备型号
		FieldSoftwareVersion,      // 软件版本
		FieldStackInfo,            // 堆栈信息
		FieldStackInfoEscaped,     // 堆栈信息（转义）
		FieldComponentResponsible, // 组件负责人
		FieldProcessResponsible,   // 进程负责人
		FieldActualResponsible,    // 实际负责人
		FieldFileName,             // 文件名
		FieldCoredumpURL,          // coredump收集url
		FieldIsKnownIssue,         // 是否已知问题
		FieldProcessingResult,     // 处理结果
		FieldFixVersion,           // 修复版本
		FieldDescription,          // 说明
		FieldComponentCategory,    // 组件归属
		FieldCoreAlertCount,       // core告警次数
		FieldCoredumpTypeGE4,      // coredump类型组件≥4
		FieldBugOS,                // bug操作系统

		// 系统控制字段
		FieldSyncRequired, // 是否需要同步Bug系统
		FieldSyncStatus,   // 是否已同步bug系统
	}
}

// GetCoreFieldNames 获取核心处理字段列表（用于优化查询性能）
func GetCoreFieldNames() []string {
	return []string{
		// 必需的核心字段
		FieldSN,                   // 主键
		FieldComponent,            // 组件
		FieldSoftwareVersion,      // 软件版本
		FieldDeviceModel,          // 设备型号
		FieldCoredumpTime,         // 时间
		FieldComponentResponsible, // 组件负责人
		FieldProcessResponsible,   // 进程负责人
		FieldDescription,          // 说明

		// 控制字段
		FieldSyncRequired, // 是否需要同步
		FieldSyncStatus,   // 同步状态
	}
}

// 注意：配置访问现在直接使用 libs.Config.FeiShuDoc.CoredumpAutoSync

// BugSystemConfig Bug系统配置（从环境变量或其他配置源获取）
type BugSystemConfig struct {
	BaseURL    string
	Username   string
	Password   string
	ProjectKey string
	IssueType  string
	Priority   string
}

// GetBugSystemConfig 获取Bug系统配置
func GetBugSystemConfig() *BugSystemConfig {
	// TODO: 从环境变量或其他配置源获取Bug系统配置
	// 这里先返回默认配置，后续可以从环境变量或专门的配置文件中读取
	return &BugSystemConfig{
		BaseURL:    "https://your-jira-instance.com",
		Username:   "your_username",
		Password:   "your_password",
		ProjectKey: "COREDUMP",
		IssueType:  "Bug",
		Priority:   "Medium",
	}
}
