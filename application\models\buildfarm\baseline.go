package buildfarm

import "irisAdminApi/application/models"

type Baseline struct {
	models.ModelBase
	JobId     string `gorm:"not null; type:varchar(60)" json:"job_id"`
	Dir       string `gorm:"not null; type:varchar(60)" json:"dir"`
	Project   string `gorm:"not null; type:varchar(100)" json:"project"`
	Repo      string `gorm:"not null; type:varchar(100)" json:"repo"`
	Branch    string `gorm:"not null; type:varchar(100)" json:"branch"`
	TaskType  uint   `gorm:"not null" json:"task_type"`                    //编译类型，1：产品编译  2：组件编译
	BuildType string `gorm:"not null; type:varchar(60)" json:"build_type"` //编译模式   debug release gcov
	Product   string `gorm:"not null; type:varchar(60)" json:"product"`
	Defconfig string `gorm:"not null; type:varchar(60)" json:"defconfig"`
	Target    string `gorm:"not null; type:varchar(60)" json:"target"`
	Status    uint   `gorm:"not null" json:"status"`                    // 作业状态 3：排队，0：运行， 1：成功， 2: 失败
	Version   string `gorm:"not null; type:varchar(60)" json:"version"` // 版本hash值,初始为0
}
