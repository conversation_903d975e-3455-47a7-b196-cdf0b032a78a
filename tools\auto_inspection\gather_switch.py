import utils
import sys
from typing import Dict
from netmiko import ConnectHandler
import os

job_id = sys.argv[1]
if not job_id:
    job_id = "unkown"


def connect(params: Dict, command: str):
    conn = ConnectHandler(**params)
    output = conn.send_command(command)
    return output


switches = {
    "switch1": {
        "device_type": "ruijie_os",
        "host": "***********",
        "username": "admin",
        "password": "a$rAjLwFe%",
    },
    "switch2": {
        "device_type": "ruijie_os",
        "host": "***********",
        "username": "admin",
        "password": "DEZ@9OVJQy",
    },
    "switch3": {
        "device_type": "ruijie_os",
        "host": "***********",
        "username": "admin",
        "password": "lXrQFDS8xs",
    },
}


for hostname, params in switches.items():
    ip = params.get("host")
    log_path = f"/tmp/switch_{hostname}_{ip}_{utils.get_now_str()}.log"
    with open(log_path, "w", encoding="utf8") as f:
        for rule_id, command in utils.switch_commands.items():
            name = command.get("name")
            cmd = command.get("command")
            if hostname and cmd:
                f.write(f"=============== start {rule_id} {name} =================\n")
                ret = connect(params, cmd)
                f.write(ret)
                f.write("\n")
                f.write(f"=============== end {rule_id} {name} ===================\n")
            else:
                f.write(f"=============== start {rule_id} ===================\n")
                f.write("采集配置异常，无法采集。")
                f.write("\n")
                f.write(f"=============== end {rule_id} ===================\n")
    utils.upload_file(log_path, data={"hostname": hostname, "ip": ip, "job_id": job_id})
    os.remove(log_path)
