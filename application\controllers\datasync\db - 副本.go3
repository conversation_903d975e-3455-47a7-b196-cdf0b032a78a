package datasync

import (
	"errors"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
	"gorm.io/plugin/dbresolver"
)

type DbServer struct {
	adapter string
	conn    string
	db      *gorm.DB
}

var DataSyncDB = &DbServer{
	adapter: "mysql",
	// fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8&parseTime=True&loc=Local", dbConfig.Username, dbConfig.Password, dbConfig.Host, dbConfig.Port, dbConfig.Database)
	conn: "root:fileOut@2021!@tcp(************:3306)/data_sync?charset=utf8&parseTime=True&loc=Local",
	db:   &gorm.DB{},
}

// func init() {
// 	err := DataSyncDB.Init()
// 	if err != nil {
// 		logging.ErrorLogger.Errorf(err.Error())
// 		panic(err)
// 	}
// }

// initGormDb
func (db *DbServer) Init() error {
	var err error
	var dialector gorm.Dialector
	switch db.adapter {
	case "mysql":
		dialector = mysql.Open(db.conn)
	case "postgres":
		dialector = postgres.Open(db.conn)
	case "sqlite3":
		dialector = sqlite.Open(db.conn)
	default:
		return errors.New("not supported database adapter")
	}

	db.db, err = gorm.Open(dialector, &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			TablePrefix:   "",   // 表名前缀，`User` 的表名应该是 `t_users`
			SingularTable: true, // 使用单数表名，启用该选项，此时，`User` 的表名应该是 `t_user`
		},
	})
	if err != nil {
		return err
	}

	err = db.db.Use(
		dbresolver.Register(
			dbresolver.Config{ /* xxx */ }).
			SetConnMaxIdleTime(time.Hour).
			SetConnMaxLifetime(24 * time.Hour).
			SetMaxIdleConns(100).
			SetMaxOpenConns(200),
	)
	if err != nil {
		return err
	}

	db.db.Session(&gorm.Session{FullSaveAssociations: true, AllowGlobalUpdate: false})
	return nil
}
