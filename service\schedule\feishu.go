package schedule

import (
	"sync"
	"time"

	"irisAdminApi/application/controllers/openfeishu"
	"irisAdminApi/application/controllers/openfeishu/coredump"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
)

var feishuLock sync.Mutex
var coredumpAutoSyncMutex sync.Mutex

// 获取项目数据
func RunFeiShuData() {
	feishuLock.Lock()
	defer feishuLock.Unlock()
	openfeishu.ProjectDocumentSyncWorker()
	t := time.NewTicker(1 * time.Hour)
	go func() {
		// for {
		// 	select {
		// 	case <-t.C:
		// 		openfeishu.ProjectDocumentSyncWorker()
		// 	}
		// }
		for range t.C {
			openfeishu.ProjectDocumentSyncWorker()
		}
	}()
}

// 更新项目数据
func RunUpdateFeiShuData() {
	feishuLock.Lock()
	defer feishuLock.Unlock()
	// openfeishu.ProjectDocumentUpdateWorker()
	t := time.NewTicker(70 * time.Minute)
	go func() {
		// for {
		// 	select {
		// 	case <-t.C:
		// 		openfeishu.ProjectDocumentUpdateWorker()
		// 	}
		// }
		for range t.C {
			openfeishu.ProjectDocumentUpdateWorker()
		}
	}()
}

// 更新文档评论数据
func RunFeiShuDocumentCommentData() {
	feishuLock.Lock()
	defer feishuLock.Unlock()
	openfeishu.ProjectDocumentCommentWorker()
	t := time.NewTicker(40 * time.Minute)
	go func() {
		// for {
		// 	select {
		// 	case <-t.C:
		// 		openfeishu.ProjectDocumentCommentWorker()
		// 	}
		// }
		for range t.C {
			openfeishu.ProjectDocumentCommentWorker()
		}
	}()
}

// 获取云端评审状态数据
func RunFeiShuDocumentReviewStatus() {
	feishuLock.Lock()
	defer feishuLock.Unlock()
	openfeishu.DocumentReviewStatusWorker()
	t := time.NewTicker(20 * time.Minute)
	go func() {
		// for {
		// 	select {
		// 	case <-t.C:
		// 		openfeishu.DocumentReviewStatusWorker()
		// 	}
		// }
		for range t.C {
			openfeishu.DocumentReviewStatusWorker()
		}
	}()
}

// 更新用户OpenID数据
func RunGetFeiShuOpenID() {
	feishuLock.Lock()
	defer feishuLock.Unlock()
	openfeishu.SyncFeiShuOpenID()
	t := time.NewTicker(12 * time.Hour)
	go func() {
		// for {
		// 	select {
		// 	case <-t.C:
		// 		openfeishu.SyncFeiShuOpenID()
		// 	}
		// }

		for range t.C {
			openfeishu.SyncFeiShuOpenID()
		}
	}()
}

// 提醒评委更新评审状态和评论性质
func RunFeiShuDocumentReviewStatusRemind() {
	feishuLock.Lock()
	defer feishuLock.Unlock()
	_, err := Cron.AddFunc("0 9 * * *", func() { openfeishu.NotifyReviewersForReviewTypeAndStatus() })
	if err != nil {
		logging.ErrorLogger.Error("add daily 9am NotifyReviewersForReviewTypeAndStatus cron job err", err)
	}
	_, err = Cron.AddFunc("30 9 * * *", func() { openfeishu.NotifyReviewersForReviewComment() })
	if err != nil {
		logging.ErrorLogger.Error("add daily 9:30am NotifyReviewersForReviewComment cron job err", err)
	}
}

// 获取PMS数据
func RunPMSData() {
	feishuLock.Lock()
	defer feishuLock.Unlock()
	_, err := Cron.AddFunc("10 * * * *", func() { openfeishu.SyncPMSData() })
	if err != nil {
		logging.ErrorLogger.Error("add SyncPMSData cron job err", err)
	}
}

// 补丁项目文档制作
func RunFeishuProjectData() {
	lock.Lock()
	defer lock.Unlock()
	_, err := Cron.AddFunc("58 * * * *", func() { openfeishu.GetWorkItemData() })
	if err != nil {
		logging.ErrorLogger.Error("add GetWorkItemData cron job err", err)
	}
}

// 同步品控数据
func RegisterQualityControlCron() {
	feishuLock.Lock()
	defer feishuLock.Unlock()
	_, err := Cron.AddFunc("0 8 * * *", func() {
		logging.InfoLogger.Info("开始同步品控数据到飞书")
		openfeishu.SyncQualityControlData()
		logging.InfoLogger.Info("完成同步品控数据到飞书")
	})
	if err != nil {
		logging.ErrorLogger.Error("add SyncQualityControlData cron job err", err)
	} else {
		logging.InfoLogger.Info("已注册同步品控数据到飞书的定时任务")
	}
}

// Coredump自动同步任务
func CoredumpAutoSync() {
	coredumpAutoSyncMutex.Lock()
	defer coredumpAutoSyncMutex.Unlock()

	// 获取Coredump配置（直接访问内联字段）
	config := libs.Config.FeiShuDoc

	// 检查功能是否启用
	if !config.CoredumpEnable {
		return
	}

	logging.InfoLogger.Info("开始执行Coredump记录自动同步任务")
	startTime := time.Now()

	// TODO: 创建服务实例并执行同步处理
	// 这里先记录日志，业务逻辑在后续阶段实现
	if config.CoredumpDebugMode {
		logging.InfoLogger.Info("Coredump自动同步任务运行在调试模式")
	}

	if config.CoredumpDryRun {
		logging.InfoLogger.Info("Coredump自动同步任务运行在干运行模式，不执行实际处理")
	}

	// 模拟处理时间（开发阶段）
	time.Sleep(100 * time.Millisecond)

	duration := time.Since(startTime)
	logging.InfoLogger.Infof("Coredump自动同步任务完成，耗时: %v", duration)
}

// 全局Coredump调度器实例
var globalCoredumpScheduler *coredump.CoredumpScheduler

// 注册Coredump自动同步定时任务
func RegisterCoredumpAutoSyncCron() {
	coredumpAutoSyncMutex.Lock()
	defer coredumpAutoSyncMutex.Unlock()

	// 检查Coredump自动同步是否启用
	if !libs.Config.FeiShuDoc.CoredumpEnable {
		logging.InfoLogger.Info("Coredump自动同步功能已禁用，跳过定时任务注册")
		return
	}

	// 创建Coredump服务和调度器
	service := coredump.NewCoredumpAutoSyncService()
	if service == nil {
		logging.ErrorLogger.Error("创建Coredump服务失败，跳过定时任务注册")
		return
	}

	globalCoredumpScheduler = coredump.NewCoredumpScheduler(service)

	// 启动调度器
	err := globalCoredumpScheduler.Start()
	if err != nil {
		logging.ErrorLogger.Errorf("启动Coredump定时任务失败: %v", err)
	} else {
		logging.InfoLogger.Infof("Coredump定时任务已启动，执行频率: %s", libs.Config.FeiShuDoc.CoredumpCronExpr)
	}
}

// GetCoredumpScheduler 获取全局Coredump调度器实例
func GetCoredumpScheduler() *coredump.CoredumpScheduler {
	return globalCoredumpScheduler
}
