package dpatchjob

import (
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/buildfarm"

	"github.com/pkg/errors"
)

const ModelName = "补丁任务管理"

type User struct {
	ID       uint   `json:"id"`
	Username string `json:"username"`
	Name     string `json:"name"`
}

type Response struct {
	Id                   uint        `json:"id"`
	JobId                string      `json:"job_id"`
	UpdatedAt            time.Time   `json:"updated_at"`
	CreatedAt            time.Time   `json:"created_at"`
	UserId               uint        `json:"user_id"`
	PatchFileName        string      `json:"patch_file_name"`
	PatchFileOriginName  string      `json:"patch_file_origin_name"`
	PatchFileSize        string      `json:"patch_file_size"`
	PatchFileMd5         string      `json:"patch_file_md5"`
	PatchFileDesc        string      `json:"patch_file_desc"`
	PatchFileDescEN      string      `json:"patch_file_desc_en"`
	PatchType            uint        `json:"patch_type"`         //补丁类型:0单补丁 1集合补丁
	PatchForm            uint        `json:"patch_form"`         //补丁形式：0组件补丁 1函数补丁
	PatchUpgradeType     uint        `json:"patch_upgrade_type"` //补丁升级方式 0自动升级 1手动升级 2强制升级 默认手动
	SoftwareVersion      string      `json:"software_version"`   //适配软件版本
	PatchComponentName   string      `json:"patch_component_name"`
	PatchObjectPath      string      `json:"patch_object_path" form:"patch_object_path"`
	Baseline             string      `json:"baseline"`
	AdapterModel         string      `json:"adapter_model"`
	AdapterHardware      string      `json:"adaptation_hardware"`
	Defconfig            string      `json:"defconfig"` //工具配置文件
	Status               uint        `json:"status"`    // 作业状态 0：运行，1：成功， 2：失败
	ServerId             uint        `json:"server_id"` //编译服务器id
	Dir                  string      `json:"dir"`
	LocalDir             string      `json:"Local_dir"`
	TaskId               string      `json:"task_id"`
	Version              string      `json:"version"`
	MakeStatus           int         `json:"make_status"` // 编译状态 3：排队，-1:未启动,0：运行， 1：成功， 2: 失败
	RPMFileName          string      `json:"rpm_file_name"`
	RPMFileSize          uint        `json:"rmp_file_size"`
	RPMFileMd5           string      `json:"rmp_file_md5"`
	User                 *User       `gorm:"->" json:"user"`
	Type                 string      `gorm:"not null; type:varchar(60)" json:"type"`
	Post                 string      `gorm:"not null; type:varchar(300)" json:"post"`
	CronMakeJobID        uint        `gorm:"not null" json:"cron_make_job_id"`
	MakeFrom             string      `gorm:"not null; type:varchar(60)" json:"make_from" form:"make_from"`
	Crypt                string      `gorm:"not null; type:varchar(60); default:old" json:"crypt" form:"crypt"`
	PatchVersion         int         `gorm:"default:1" json:"patch_version"`                    // 补丁编译工具版本  1: 第一代 2: 第二代
	DependencyIDs        string      `gorm:"not null; type:varchar(200)" json:"dependency_ids"` //补丁依赖项
	Dependencies         []*Response `gorm:"many2many:patch_dependencies;foreignKey:Id;references:Id;joinForeignKey:PatchJobID;joinReferences:DependencyID" json:"dependencies"`
	SerialNumber         uint        `gorm:"not null" json:"serial_number"`
	OriginSubItemIDs     string      `gorm:"not null; type:varchar(200)" json:"origin_subitem_ids" form:"origin_subitem_ids"` //原始集合补丁子项
	SubitemIDs           string      `gorm:"not null; type:varchar(200)" json:"subitem_ids" form:"subitem_ids"`               //集合补丁子项
	Subitems             []*Response `gorm:"many2many:patch_subitems;foreignKey:Id;references:Id;joinForeignKey:PatchJobID;joinReferences:SubitemID" json:"subitems"`
	EnableAutoCollection bool        `gorm:"default:false" json:"enable_auto_collection"`    //是否自动创建集合补丁
	CustomReleaseDate    *string     `json:"custom_release_date" form:"custom_release_date"` //自定义发布日期，格式YYYY-MM-DD，为空时使用created_at
}

type PatchJobListResponse struct {
	Response
}

type Request struct {
	PatchFileName        string      `json:"patch_file_name" form:"patch_file_name"`           //补丁包名称
	PatchFileSize        string      `json:"patch_file_size" form:"patch_file_size"`           //补丁包大小
	PatchFileMd5         string      `json:"patch_file_md5" form:"patch_file_md5"`             //补丁包MD5值
	PatchType            uint        `json:"patch_type" form:"patch_type"`                     //补丁类型 0：单组件补丁  1 补丁集合包 默认单组件
	SoftwareVersion      string      `json:"software_version" form:"software_version"`         // 适配软件版本
	PatchComponentName   string      `json:"patch_component_name" form:"patch_component_name"` //补丁包组件名
	PatchObjectPath      string      `json:"patch_object_path" form:"patch_object_path"`
	Baseline             string      `json:"baseline" form:"baseline"`                       //基线版本
	AdapterModel         string      `json:"adapter_model" form:"adapter_model"`             //适配型号
	AdapterHardware      string      `json:"adaptation_hardware" form:"adaptation_hardware"` //适配硬件
	PatchForm            uint        `json:"patch_form" form:"patch_form"`                   //补丁形式：0组件补丁 1函数补丁
	PatchUpgradeType     uint        `json:"patch_upgrade_type" form:"patch_upgrade_type"`   //补丁升级方式 0自动升级 1手动升级 2强制升级 默认手动
	PatchFileDesc        string      `json:"patch_file_desc" form:"patch_file_desc"`         //补丁包描述（中文描述）
	PatchFileDescEN      string      `json:"patch_file_desc_en" form:"patch_file_desc_en"`   //补丁包描述（英文描述）
	Defconfig            string      `json:"defconfig" form:"defconfig"`                     //工具配置文件
	Type                 string      `gorm:"not null; type:varchar(60)" json:"type" form:"type"`
	Post                 string      `gorm:"not null; type:varchar(1024)" json:"post" form:"post"`
	CronMakeJobID        uint        `gorm:"not null" json:"cron_make_job_id" form:"cron_make_job_id"`
	MakeFrom             string      `gorm:"not null; type:varchar(60)" json:"make_from" form:"make_from"`
	Crypt                string      `gorm:"not null; type:varchar(60); default:old" json:"crypt" form:"crypt"`
	PatchVersion         int         `gorm:"default:1" json:"patch_version"`                                          // 补丁编译工具版本  1: 第一代 2: 第二代
	DependencyIDs        string      `gorm:"not null; type:varchar(200)" json:"dependency_ids" form:"dependency_ids"` //补丁依赖项
	Dependencies         []*Response `gorm:"many2many:patch_dependencies;foreignKey:ID;references:ID;joinForeignKey:PatchJobID;joinReferences:DependencyID" json:"dependencies"`
	SubitemIDs           string      `gorm:"not null; type:varchar(200)" json:"subitem_ids" form:"subitem_ids"` //集合补丁子项
	Subitems             []*Response `gorm:"many2many:patch_subitems;foreignKey:Id;references:Id;joinForeignKey:PatchJobID;joinReferences:SubitemID" json:"subitems"`
	BuildName            string      `json:"build_name" form:"build_name"`
	EnableAutoCollection bool        `json:"enable_auto_collection" form:"enable_auto_collection"` //是否自动创建集合补丁
	SerialNumber         uint        `gorm:"not null" json:"serial_number" form:"serial_number"`
	CustomReleaseDate    string      `json:"custom_release_date" form:"custom_release_date"` //自定义发布日期，格式YYYY-MM-DD，前端传入的字符串
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *buildfarm.PatchJob {
	return &buildfarm.PatchJob{}
}

func (Response) TableName() string {
	return "patch_jobs"
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*PatchJobListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).Preload("User").Preload("Dependencies").Preload("Subitems").
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	// formatResponses(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*PatchJobListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).Preload("User").Preload("Dependencies").Preload("Subitems").
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	// formatResponses(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Preload("Dependencies").Preload("Subitems").Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

var mutexDB sync.Mutex

func (a *Response) CreateV3(patchRes Response) error {
	mutexDB.Lock()
	defer mutexDB.Unlock()
	if patchRes.SerialNumber == 0 {
		maxNumber, err := GetMaxSerialNumber(patchRes.Type, patchRes.PatchType)
		if err != nil {
			logging.ErrorLogger.Errorf("get maxid err ", err)
			return err
		}
		patchRes.SerialNumber = maxNumber + 1
	}
	//依赖项数据
	dependencies := []*Response{}
	err := easygorm.GetEasyGormDb().Model(&Response{}).Where("id in ?", strings.Split(patchRes.DependencyIDs, ",")).Find(&dependencies).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find data err ", err)
		return err
	}
	patchRes.Dependencies = dependencies
	//子项数据
	subitems := []*Response{}
	err = easygorm.GetEasyGormDb().Model(&Response{}).Where("id in ?", strings.Split(patchRes.SubitemIDs, ",")).Find(&subitems).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find data err ", err)
		return err
	}
	patchRes.Subitems = subitems

	err = easygorm.GetEasyGormDb().Create(&patchRes).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}
	return nil
}

func FindMakePatchJobs(userID, fileOriginName, start, end, makeStatus, baseline, defconfig, componentName, sort, orderBy, typeClass string, patchForm, patchType, patchVersion, isdependen, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var makepatchjobs []*PatchJobListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(userID) > 0 && userID != "0" {
		db = db.Where("user_id = ?", userID)
	}
	if len(start) > 0 {
		db = db.Where("created_at >= ?", start+" 00:00:00.000")
	}
	if len(end) > 0 {
		db = db.Where("created_at <= ?", end+" 23:59:59.999")
	}
	if len(makeStatus) > 0 {
		db = db.Where("make_status = ?", makeStatus)
	}
	if patchForm >= 0 {
		db = db.Where("patch_form = ?", patchForm)
	}
	if len(baseline) > 0 {
		db = db.Where(fmt.Sprintf("baseline like '%%%s%%'", baseline))
	}
	if len(defconfig) > 0 {
		db = db.Where(fmt.Sprintf("defconfig like '%%%s%%'", defconfig))
		// db = db.Where("defconfig = ?", defconfig)
	}
	if len(componentName) > 0 {
		db = db.Where(fmt.Sprintf("patch_component_name like '%%%s%%'", componentName))
		// db = db.Where("target = ?", target)
	}
	if len(fileOriginName) > 0 {
		db = db.Where(fmt.Sprintf("patch_file_origin_name like '%%%s%%'", fileOriginName))
		// db = db.Where("target = ?", target)
	}
	if patchType >= 0 {
		db = db.Where("patch_type = ?", patchType)
	}
	if len(typeClass) > 0 {
		if typeClass != "全部" {
			db = db.Where("type = ?", typeClass)
		}
	}

	if patchVersion > 0 {
		db = db.Where("patch_version = ?", patchVersion)
	}

	if isdependen > 0 {
		if isdependen == 0 {
			db = db.Where("dependency_ids= '' OR dependency_ids IS NULL")
		}
		if isdependen == 1 {
			db = db.Where("dependency_ids != ''")
		}
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}
	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).Preload("User").Preload("Dependencies").Preload("Subitems").
		Find(&makepatchjobs).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": makepatchjobs, "total": count, "limit": pageSize}
	return list, nil
}

func (u *Response) FindPatchJob(jobId string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Preload("Dependencies").Preload("Subitems").Where("job_id = ?", jobId).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

// func formatResponses(items []*PatchJobListResponse) {

// 	userIds := []uint{}
// 	for _, item := range items {
// 		userIds = append(userIds, item.UserId)
// 	}
// 	users, _ := duser.FindSimpleInIds(userIds)
// 	var userMap = make(map[uint]*duser.ApprovalResponse)
// 	for _, user := range users {
// 		userMap[user.Id] = user
// 	}
// 	for _, item := range items {
// 		item.User = userMap[item.UserId]
// 	}

// }

func GetPacthDependency(adapterModel, adapterHardware, softwareVersion string, patchForm, patchUpgradeType uint) ([]*Response, error) {
	var makepatchjobs []*Response
	db := easygorm.GetEasyGormDb().Model(Model())
	if adapterHardware != "" {
		db = db.Where("adapter_hardware = ?", adapterHardware)
	}
	db = db.Where("patch_form = ?", patchForm).Where("patch_version =?", 2).Where("patch_type =?", 0).Where("status =?", 1).
		Where("software_version = ?", softwareVersion).Where("adapter_model = ?", adapterModel)
	db = db.Where("patch_upgrade_type = ?", patchUpgradeType)
	err := db.Preload("User").Preload("Dependencies").Preload("Subitems").Find(&makepatchjobs).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	return makepatchjobs, nil

}

func GetMaxSerialNumber(typeModel string, patchType uint) (uint, error) {
	var maxSerialNumber uint
	db := easygorm.GetEasyGormDb().Model(Model()).Where("patch_version =?", 2).
		Where("patch_type=?", patchType).Where("type=?", typeModel)
	result := db.Select("COALESCE(MAX(serial_number), 0) as max_serial_number").Row()
	err := result.Scan(&maxSerialNumber)
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return maxSerialNumber, err
	}
	return maxSerialNumber, nil
}

func GetPatchJobList(typeModel, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var patchJobList []*Response
	var count int64
	db := easygorm.GetEasyGormDb().Model(Model()).Where("patch_version =?", 2).Where("status =?", 1).Where("make_status=?", 1).Where("type=?", typeModel)
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}
	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).Preload("User").Preload("Dependencies").Preload("Subitems").
		Find(&patchJobList).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": patchJobList, "total": count, "limit": pageSize}
	return list, nil
}

func FindAllDependencies(id uint, visited map[uint]bool) ([]*Response, error) {
	visited[id] = true // 标记当前节点为已访问
	patchJob := &Response{}
	err := patchJob.Find(id)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to find patch job with ID %d", id)
	}

	var allDependencies []*Response
	for _, dependency := range patchJob.Dependencies {
		if visited[dependency.Id] { // 如果依赖项已访问过，则跳过，避免循环依赖
			continue
		}
		// 递归查找依赖项的所有依赖
		subDependencies, err := FindAllDependencies(dependency.Id, visited)
		if err != nil {
			return nil, err
		}

		allDependencies = append(allDependencies, subDependencies...)
	}
	allDependencies = append(allDependencies, patchJob)
	return allDependencies, nil
}

func FindAllDependenciesByID(id uint) ([]*Response, error) {
	visited := make(map[uint]bool)
	return FindAllDependencies(id, visited)
}

// 获取全部单补丁数据集
func (r *Response) GetSinglePatchesFromIDs(patchIDString string) ([]*Response, error) {
	patchIDStrings := strings.Split(patchIDString, ",")
	patchIDs := make([]uint, 0, len(patchIDStrings))
	for _, strID := range patchIDStrings {
		id, err := strconv.Atoi(strID)
		if err != nil {
			return nil, errors.Wrapf(err, "invalid patch ID '%s'", strID)
		}
		patchIDs = append(patchIDs, uint(id))
	}
	return r.getSinglePatchesFromIDsInternal(patchIDs)
}

func (r *Response) getSinglePatchesFromIDsInternal(patchIDs []uint) ([]*Response, error) {
	var singlePatches []*Response

	for _, id := range patchIDs {
		patch := &Response{}
		err := patch.Find(id)
		if err != nil {
			return nil, err
		}
		if patch.PatchType == 0 {
			singlePatches = append(singlePatches, patch)
			continue
		}
		if patch.PatchType == 1 {
			subitemIDs := strings.Split(patch.SubitemIDs, ",")
			subitemIDsUint := make([]uint, 0, len(subitemIDs))
			for _, strID := range subitemIDs {
				subitemID, err := strconv.Atoi(strID)
				if err != nil {
					return nil, errors.Wrapf(err, "invalid subitem ID '%s'", strID)
				}
				subitemIDsUint = append(subitemIDsUint, uint(subitemID))
			}
			subpatches, err := r.getSinglePatchesFromIDsInternal(subitemIDsUint)
			if err != nil {
				return nil, err
			}
			singlePatches = append(singlePatches, subpatches...)
		}
	}
	return singlePatches, nil
}

func (r *Response) MergeAndDistinctAdapterModels(responses []*Response) []string {
	var adapterModels []string

	// 遍历输入的Response切片
	for _, response := range responses {
		// 如果当前Response实例的AdapterModel字段非空，则添加到adapterModels切片中
		if response.AdapterModel != "" {
			//使用|分割后的值添加到adapterModels
			subitems := strings.Split(response.AdapterModel, "|")
			for _, subitem := range subitems {
				// 仅添加非空子项subitem
				if subitem != "" {
					adapterModels = append(adapterModels, subitem)
				}
			}
		}
	}

	// 使用map进行去重，然后将结果转换回切片
	uniqueAdapterModels := make(map[string]struct{})
	for _, model := range adapterModels {
		uniqueAdapterModels[model] = struct{}{}
	}

	distinctAdapterModels := make([]string, 0, len(uniqueAdapterModels))
	for model := range uniqueAdapterModels {
		distinctAdapterModels = append(distinctAdapterModels, model)
	}

	return distinctAdapterModels
}

func (r *Response) MergeAndDistinctSoftwareVersions(responses []*Response) []string {
	var softwareVersions []string

	// 遍历输入的Response切片
	for _, response := range responses {
		// 如果当前Response实例的SoftwareVersion字段非空，则添加到softwareVersions切片中
		if response.SoftwareVersion != "" {
			//使用|分割后的值添加到softwareVersions
			subitems := strings.Split(response.SoftwareVersion, "|")
			for _, subitem := range subitems {
				// 仅添加非空子项subitem
				if subitem != "" {
					softwareVersions = append(softwareVersions, subitem)
				}
			}
		}
	}

	// 使用map进行去重，然后将结果转换回切片
	uniqueSoftwareVersions := make(map[string]struct{})
	for _, version := range softwareVersions {
		uniqueSoftwareVersions[version] = struct{}{}
	}

	distinctSoftwareVersions := make([]string, 0, len(uniqueSoftwareVersions))
	for version := range uniqueSoftwareVersions {
		distinctSoftwareVersions = append(distinctSoftwareVersions, version)
	}

	return distinctSoftwareVersions
}

func (r *Response) MergeAndDistinctAdapterHardwares(responses []*Response) []string {
	var adapterHardwares []string

	// 遍历输入的Response切片
	for _, response := range responses {
		// 如果当前Response实例的AdapterHardware字段非空，则添加到adapterHardwares切片中
		if response.AdapterHardware != "" {
			//使用|分割后的值添加到adapterHardwares
			subitems := strings.Split(response.AdapterHardware, "|")
			for _, subitem := range subitems {
				// 仅添加非空子项subitem
				if subitem != "" {
					adapterHardwares = append(adapterHardwares, subitem)
				}
			}
		}
	}

	// 使用map进行去重，然后将结果转换回切片
	uniqueAdapterHardwares := make(map[string]struct{})
	for _, model := range adapterHardwares {
		uniqueAdapterHardwares[model] = struct{}{}
	}

	distinctAdapterHardwares := make([]string, 0, len(uniqueAdapterHardwares))
	for model := range uniqueAdapterHardwares {
		distinctAdapterHardwares = append(distinctAdapterHardwares, model)
	}

	return distinctAdapterHardwares
}
