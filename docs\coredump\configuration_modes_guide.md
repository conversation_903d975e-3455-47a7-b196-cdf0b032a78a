# Coredump自动同步配置模式详细指南

## 📋 配置概览

Coredump自动同步系统提供了两个重要的运行模式配置，用于控制系统在不同开发阶段的行为：

### 核心配置项

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `coredump_enable` | bool | false | 系统启用开关 |
| `coredump_app_token` | string | "" | 飞书应用令牌 |
| `coredump_table_id` | string | "" | 飞书表格ID |
| `coredump_cron_expr` | string | "0 0 * * * *" | 定时执行表达式 |
| `coredump_debug_mode` | bool | false | 调试模式开关 |
| `coredump_dry_run` | bool | true | 干运行模式开关 |

### 固定技术参数（代码常量）

| 参数 | 值 | 说明 |
|------|-----|------|
| `DefaultPageSize` | 100 | 分页大小 |
| `DefaultMaxRunTime` | 30分钟 | 最大运行时间 |
| `DefaultTimeout` | 60秒 | API超时时间 |
| `MaxRetryCount` | 3次 | 最大重试次数 |

## 🔧 配置模式详解

### 1. 调试模式 (CoredumpDebugMode)

#### 🎯 作用和影响
- **详细日志输出**：记录每个处理步骤的详细信息
- **中间结果保存**：保存API调用的原始响应数据
- **性能指标记录**：记录每个操作的耗时和资源使用情况
- **错误详情展示**：显示完整的错误堆栈和上下文信息

#### 📊 具体行为差异

| 功能 | 调试模式关闭 | 调试模式开启 |
|------|-------------|-------------|
| **日志级别** | INFO级别 | DEBUG级别 |
| **API调用日志** | 只记录成功/失败 | 记录请求参数和响应内容 |
| **处理统计** | 基础统计 | 详细的性能指标 |
| **错误信息** | 简要错误信息 | 完整错误堆栈 |
| **中间数据** | 不保存 | 保存到临时文件 |

#### 💡 使用场景
- **开发阶段**：调试业务逻辑，排查问题
- **测试阶段**：验证数据处理流程
- **问题排查**：生产环境出现问题时临时启用

### 2. 干运行模式 (CoredumpDryRun)

#### 🎯 作用和影响
- **只读操作**：只读取飞书数据，不执行任何写入操作
- **模拟处理**：模拟完整的处理流程，但不提交Bug
- **安全验证**：验证配置和权限是否正确
- **数据预览**：查看将要处理的数据内容

#### 📊 具体行为差异

| 操作 | 干运行模式关闭 | 干运行模式开启 |
|------|---------------|---------------|
| **读取飞书数据** | ✅ 执行 | ✅ 执行 |
| **数据筛选和解析** | ✅ 执行 | ✅ 执行 |
| **Bug系统提交** | ✅ 执行 | ❌ 跳过（模拟） |
| **飞书状态更新** | ✅ 执行 | ❌ 跳过（模拟） |
| **日志记录** | 正常记录 | 标记为"模拟操作" |

#### 💡 使用场景
- **配置验证**：验证飞书连接和权限
- **数据预览**：查看将要处理的记录
- **流程测试**：测试业务逻辑而不影响实际数据
- **安全部署**：新环境部署时的安全验证

## 🔄 模式组合使用

### 组合1：开发调试 (推荐开发期间)
```yaml
coredump_debug_mode: true   # 启用详细日志
coredump_dry_run: true      # 只读取不写入
```
**适用场景**：开发期间调试业务逻辑
**效果**：详细的日志输出 + 安全的只读操作

### 组合2：生产预览
```yaml
coredump_debug_mode: false  # 关闭详细日志
coredump_dry_run: true      # 只读取不写入
```
**适用场景**：生产环境验证配置和数据
**效果**：正常日志级别 + 安全的只读操作

### 组合3：测试验证
```yaml
coredump_debug_mode: true   # 启用详细日志
coredump_dry_run: false     # 执行实际操作
```
**适用场景**：测试环境完整功能验证
**效果**：详细的日志输出 + 完整的功能执行

### 组合4：生产运行
```yaml
coredump_debug_mode: false  # 关闭详细日志
coredump_dry_run: false     # 执行实际操作
```
**适用场景**：生产环境正常运行
**效果**：正常日志级别 + 完整的功能执行

## 📈 不同开发阶段的配置建议

### 阶段1：开发期间 (Development)
```yaml
coredump_enable: false           # 手动触发测试
coredump_debug_mode: true        # 启用调试
coredump_dry_run: true          # 安全模式
coredump_cron_expr: "0 */5 * * * *"  # 5分钟执行一次（便于测试）
```

### 阶段2：集成测试 (Integration Testing)
```yaml
coredump_enable: true            # 启用定时任务
coredump_debug_mode: true        # 保持调试
coredump_dry_run: false         # 执行实际操作
coredump_cron_expr: "0 */10 * * * *"  # 10分钟执行一次
```

### 阶段3：用户验收测试 (UAT)
```yaml
coredump_enable: true            # 启用定时任务
coredump_debug_mode: false       # 关闭调试
coredump_dry_run: false         # 执行实际操作
coredump_cron_expr: "0 */30 * * * *"  # 30分钟执行一次
```

### 阶段4：生产环境 (Production)
```yaml
coredump_enable: true            # 启用定时任务
coredump_debug_mode: false       # 关闭调试
coredump_dry_run: false         # 执行实际操作
coredump_cron_expr: "0 0 * * * *"    # 每小时执行一次
```

## ⚠️ 重要注意事项

### 安全考虑
1. **生产环境首次部署**：建议先使用 `dry_run: true` 验证配置
2. **权限验证**：确保应用令牌有足够的权限访问飞书表格
3. **数据备份**：重要数据处理前建议备份

### 性能考虑
1. **调试模式影响**：调试模式会增加日志输出，可能影响性能
2. **定时频率**：生产环境建议适当降低执行频率
3. **资源监控**：监控系统资源使用情况

### 监控建议
1. **日志监控**：监控错误日志和处理统计
2. **性能监控**：监控处理时间和资源使用
3. **业务监控**：监控处理成功率和数据质量

## 🔍 故障排查

### 常见问题诊断

#### 问题1：配置不生效
**解决方案**：
1. 检查 `coredump_enable: true`
2. 验证应用令牌和表格ID
3. 启用 `debug_mode: true` 查看详细日志

#### 问题2：数据读取失败
**解决方案**：
1. 启用 `dry_run: true` 和 `debug_mode: true`
2. 检查飞书API权限
3. 验证表格ID和字段配置

#### 问题3：处理异常
**解决方案**：
1. 启用 `debug_mode: true` 获取详细错误信息
2. 检查Bug系统连接配置
3. 验证数据格式和字段映射

---

**配置优化版本**: v2.0  
**最后更新**: 2025年8月  
**适用版本**: Coredump自动同步系统 v1.0+
