package main

import (
	"context"
	"fmt"
	"time"

	influxdb2 "github.com/influxdata/influxdb-client-go/v2"
	"github.com/influxdata/influxdb-client-go/v2/api"
	"github.com/influxdata/influxdb-client-go/v2/domain"
	"github.com/spf13/cobra"
)

func RunCleanJob(cmd *cobra.Command, args []string) {
	projectName := args[0]
	fmt.Println(projectName)
	FluxTask(projectName)
}

func FluxTask(projectName string) {
	// bucket := "gcov"
	RunComponentTask(projectName, "gcov-component_packet", "gcov-component")
	RunWorkpackageTask(projectName, "gcov-workpackage")
	RunRequestTask(projectName, "gcov-request")
}

func RunComponentTask(projectName, bucketName1, bucketName2 string) {
	taskName := fmt.Sprintf("%s_%s_clean", projectName, bucketName1)
	flux := fmt.Sprintf(`

left =
    from(bucket: "gcov")
        |> range(start: -30m)
        |> filter(fn: (r) => r["_measurement"] == "gcov-file")
				|> filter(fn: (r) => r["branch"] == "%s")
        |> keep(fn: (column) => column =~ /.+/)
        |> filter(fn: (r) => exists r.component and r.component =~ /[^"]/)
        |> group(
            columns: [
                "_measurement",
                "branch",
                "softversion",
                "auto",
                "type",
                "file",
                "_field",
                "component_packet",
                "component",
                "owner",
            ],
        )
        |> last()
        |> group()
        |> duplicate(column: "_stop", as: "_time")

left
    |> filter(
        fn: (r) =>
            r["_field"] == "add" or r["_field"] == "del" or r["_field"] == "line" or r["_field"]
                ==
                "covline" or r["_field"] == "add-covline",
    )
    |> group(
        columns: [
            "_time",
            "_measurement",
            "branch",
            "softversion",
            "auto",
            "type",
            "_field",
            "component_packet",
        ],
    )
    |> sum()
    |> to(bucket: "%s")

left
    |> filter(
        fn: (r) =>
            r["_field"] == "add" or r["_field"] == "del" or r["_field"] == "line" or r["_field"]
                ==
                "covline" or r["_field"] == "add-covline",
    )
    |> group(
        columns: [
            "_time",
            "_measurement",
            "branch",
            "softversion",
            "auto",
            "type",
            "_field",
            "component_packet",
            "component",
            "owner",
        ],
    )
    |> sum()
    |> to(bucket: "%s")
	`, projectName, bucketName1, bucketName2)
	RunTask(taskName, flux)
}

func RunRequestTask(projectName, bucketName string) {
	taskName := fmt.Sprintf("%s_%s_clean", projectName, bucketName)
	flux := fmt.Sprintf(`
    from(bucket: "gcov")
        |> range(start: -30m)
        |> filter(fn: (r) => r["_measurement"] == "gcov-file")
				|> filter(fn: (r) => r["branch"] == "%s")
        |> keep(fn: (column) => column =~ /.+/)
        |> filter(fn: (r) => exists r.workpackage and r.workpackage =~ /[^"]/)
        |> group(
            columns: [
                "_measurement",
                "branch",
                "softversion",
                "auto",
                "type",
                "file",
                "_field",
                "workpackage",
                "request",
                "workpackage_manager",
            ],
        )
        |> last()
        |> group()
        |> duplicate(column: "_stop", as: "_time")

				|> filter(
						fn: (r) =>
								r["_field"] == "add" or r["_field"] == "del" or r["_field"] == "line" or r["_field"]
										==
										"covline" or r["_field"] == "add-covline",
				)
				|> group(
						columns: [
								"_time",
								"_measurement",
								"branch",
								"softversion",
								"auto",
								"type",
								"_field",
								"request",
						],
				)
				|> sum()
				|> to(bucket: "%s")
	`, projectName, bucketName)
	RunTask(taskName, flux)
}

func RunWorkpackageTask(projectName, bucketName string) {
	taskName := fmt.Sprintf("%s_%s_clean", projectName, bucketName)
	flux := fmt.Sprintf(`
    from(bucket: "gcov")
        |> range(start: -30m)
        |> filter(fn: (r) => r["_measurement"] == "gcov-file")
				|> filter(fn: (r) => r["branch"] == "%s")
        |> keep(fn: (column) => column =~ /.+/)
        |> filter(fn: (r) => exists r.workpackage and r.workpackage =~ /[^"]/)
        |> group(
            columns: [
                "_measurement",
                "branch",
                "softversion",
                "auto",
                "type",
                "file",
                "_field",
                "workpackage",
                "request",
                "workpackage_manager",
            ],
        )
        |> last()
        |> group()
        |> duplicate(column: "_stop", as: "_time")

				|> filter(
						fn: (r) =>
								r["_field"] == "add" or r["_field"] == "del" or r["_field"] == "line" or r["_field"]
										==
										"covline" or r["_field"] == "add-covline",
				)
				|> group(
						columns: [
								"_time",
								"_measurement",
								"branch",
								"softversion",
								"auto",
								"type",
								"_field",
								"workpackage",
								"request",
								"workpackage_manager",
						],
				)
				|> sum()
				|> to(bucket: "%s")
	`, projectName, bucketName)
	RunTask(taskName, flux)
}

func RunTask(taskName, flux string) {
	ctx := context.Background()
	token := "nrGwzuQZMfrAzHVPAm5e-cmognmW-AETwiIIi-fAvWAybQa4AI-JQHCIIeElft4A578sWXR5hsKM8QAyIJjJrw=="
	// Store the URL of your InfluxDB instance
	url := "http://10.51.135.15:8086"
	// Create client
	client := influxdb2.NewClientWithOptions(url, token, influxdb2.DefaultOptions().
		SetHTTPRequestTimeout(uint(1800*1000)). // Overall request timeout
		SetMaxRetries(0))

	// Get query client
	tasksAPI := client.TasksAPI()
	// Get QueryTableResult
	orgs, err := client.OrganizationsAPI().GetOrganizations(ctx)
	if err != nil {
		panic(err)
	}

	org := (*orgs)[0]
	active := domain.TaskStatusTypeActive
	inactive := domain.TaskStatusTypeInactive

	taskFilter := api.TaskFilter{Name: taskName}
	var task *domain.Task
	tasks, err := tasksAPI.FindTasks(ctx, &taskFilter)
	if err != nil {
		panic(err)
	}

	if len(tasks) == 0 {
		task, err = tasksAPI.CreateTaskWithEvery(ctx, taskName, flux, "999999h", *org.Id)
		if err != nil {
			panic(err)
		}
	} else {
		task = &(tasks[0])
	}

	task.Status = &active
	_, err = tasksAPI.UpdateTask(ctx, task)
	if err != nil {
		panic(err)
	}

	var running *domain.Run

	runs, err := tasksAPI.FindRuns(ctx, task, nil)
	if err != nil {
		panic(err)
	}

	if len(runs) == 0 || *(runs[0].Status) != "started" {
		run, err := tasksAPI.RunManually(ctx, task)
		if err != nil {
			panic(err)
		}
		running = run
	} else {
		running = &(runs[0])
	}

	time.Sleep(10 * time.Second)
	for {
		run, err := tasksAPI.GetRun(ctx, running)
		if err != nil {
			time.Sleep(10 * time.Second)
			continue
		}
		if *(run.Status) == "started" {
			time.Sleep(10 * time.Second)
			continue
		}
		break
	}

	task.Status = &inactive
	_, err = tasksAPI.UpdateTask(ctx, task)
	if err != nil {
		panic(err)
	}

	client.Close()
}
