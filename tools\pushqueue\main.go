package main

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"irisAdminApi/application/libs"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/jinzhu/configor"
)

var client = &http.Client{}
var Config = struct {
	Host  string `env:"Host" default:""`
	Queue string `env:"Queue" default:""`
}{}

type Queue struct {
	Code    int      `json:"code"`
	Data    []string `json:"data"`
	Message string   `json:"message"`
}

func InitConfig(config string) error {
	path := filepath.Join(libs.CWD(), "config.yml")
	if config != "" {
		path = config
	}

	if err := configor.Load(&Config, path); err != nil {
		return err
	}

	return nil
}

func PushQueue(msg string) (string, error) {
	url := fmt.Sprintf("%s/api/v1/queue", Config.Host)
	result := []interface{}{}

	items := strings.Split(msg, "|")
	result = append(result, map[string]interface{}{
		"key":     Config.Queue,
		"from":    items[0],
		"to":      items[1],
		"subject": items[2],
		"body":    items[3],
	})

	jsonStr, _ := json.Marshal(result)
	reqest, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	if err != nil {
		return string(jsonStr), err
	}
	//处理返回结果
	response, err := client.Do(reqest)
	if err != nil {
		return string(jsonStr), err
	}
	defer response.Body.Close()
	bodyByte, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return string(jsonStr), err
	}
	response.Body.Close()

	ret := Queue{}
	err = json.Unmarshal(bodyByte, &ret)
	if err != nil {
		return string(jsonStr), err
	}
	if ret.Code == 20000 {
		return string(jsonStr), nil
	}
	return string(jsonStr), errors.New(ret.Message)

}

func help() {
	fmt.Println("push_queue msg")
}

func main() {
	InitConfig("./config.yml")
	args := os.Args
	if len(args) < 2 || args == nil {
		help()
	}
	fmt.Println(PushQueue(args[1]))
}
