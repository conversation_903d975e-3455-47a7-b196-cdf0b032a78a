package appmergerequest

import (
	"bytes"
	"crypto/md5"
	"errors"
	"fmt"
	"irisAdminApi/application/controllers/ip"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/appmergerequest/dappmergerequest"
	"irisAdminApi/service/dao/appmergerequest/dappmergerequesthistory"
	"irisAdminApi/service/dao/appmergerequest/dappproject"
	"irisAdminApi/service/dao/appmergerequest/dappunitpackages"
	"irisAdminApi/service/dao/user/duser"
	"math/rand"
	"net"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

type Ip struct {
	ID uint   `json:"id"`
	Ip string `json:"ip"`
}

func GetRemoteAddr(ctx iris.Context) string {
	addr := ctx.GetHeader("X-Real-Ip")
	if len(addr) == 0 {
		addr := strings.TrimSpace(ctx.Request().RemoteAddr)
		if addr != "" {
			// if addr has port use the net.SplitHostPort otherwise(error occurs) take as it is
			if ip, _, err := net.SplitHostPort(addr); err == nil {
				return ip
			}
		}
	} else {
		if ip, _, err := net.SplitHostPort(addr); err == nil {
			return ip
		}
	}
	return addr
}

func GetMergeRequests(ctx iris.Context) {

	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	releaseProjectrojectID, _ := strconv.Atoi(ctx.FormValue("release_project_id"))
	workPackageID, _ := strconv.Atoi(ctx.FormValue("work_package_id"))
	owned := ctx.URLParamBoolDefault("owned", false)
	// start := ctx.FormValue("start")
	// end := ctx.FormValue("end")
	var list map[string]interface{}

	if owned {
		list, err = dappmergerequest.All(id, uint(releaseProjectrojectID), uint(workPackageID), name, sort, orderBy, status, page, pageSize)
	} else {
		list, err = dappmergerequest.All(0, uint(releaseProjectrojectID), uint(workPackageID), name, sort, orderBy, status, page, pageSize)
	}
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetMergeRequestsV2(ctx iris.Context) {

	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	releaseProjectrojectID, _ := strconv.Atoi(ctx.FormValue("release_project_id"))
	workPackageID, _ := strconv.Atoi(ctx.FormValue("work_package_id"))
	owned := ctx.URLParamBoolDefault("owned", false)
	// start := ctx.FormValue("start")
	// end := ctx.FormValue("end")
	var list map[string]interface{}

	if owned {
		list, err = dappmergerequest.AllV2(id, uint(releaseProjectrojectID), uint(workPackageID), name, sort, orderBy, status, page, pageSize)
	} else {
		list, err = dappmergerequest.AllV2(0, uint(releaseProjectrojectID), uint(workPackageID), name, sort, orderBy, status, page, pageSize)
	}

	// list, err := dproblem.AllProblems(name, sort, orderBy, page, pageSize, status, start, end, department)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetMergeRequestsV3(ctx iris.Context) {

	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	releaseProjectrojectID, _ := strconv.Atoi(ctx.FormValue("release_project_id"))
	workPackageID, _ := strconv.Atoi(ctx.FormValue("work_package_id"))
	owned := ctx.URLParamBoolDefault("owned", false)
	var list map[string]interface{}

	if owned {
		list, err = dappmergerequest.AllV3(id, uint(releaseProjectrojectID), uint(workPackageID), name, sort, orderBy, status, page, pageSize)
	} else {
		list, err = dappmergerequest.AllV3(0, uint(releaseProjectrojectID), uint(workPackageID), name, sort, orderBy, status, page, pageSize)
	}
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

/*创建项目部署MR*/
func CreateDeployMergeRequest(ctx iris.Context) {
	CreateMergeRequest(ctx)
}

func GetMergeRequest(ctx iris.Context) {

	addr := GetRemoteAddr(ctx)
	if addr == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	info := dappmergerequest.AppMergeRequest{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

func CreateMergeRequest(ctx iris.Context) {
	token, _ := dao.GetGitlabToken(ctx)
	if token == "" {
		logging.ErrorLogger.Errorf("未配置gitlab token, 请前往个人资料添加gitlab token")
		return
	}
	userId, _ := dao.GetAuthId(ctx)
	request := &dappmergerequest.Request{}
	if err := ctx.ReadJSON(request); err != nil {
		logging.ErrorLogger.Errorf("read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	/*

	   type MergeRequest struct {
	   	models.ModelBase
	   	SourceProjectID uint   `gorm:"not null" json:"source_project_id"`
	   	SourceProject   string `gorm:"not null; type:varchar(200)" json:"source_project"`
	   	SourceBranch    string `gorm:"not null; type:varchar(200)" json:"source_branch"`

	   	TargetProjectID  uint   `gorm:"not null" json:"target_project_id"`
	   	TargetProject    string `gorm:"not null; type:varchar(200)" json:"target_project"`
	   	TargetBranch     string `gorm:"not null; type:varchar(200)" json:"target_branch"`
	   	MirrorFrom       string `gorm:"not null; type:varchar(200)"  json:"mirror_from"`
	   	MergeRequestID   uint   `gorm:"not null" json:"merge_request_id"`
	   	MergeRequestIID  uint   `gorm:"not null" json:"merge_request_Iid"`
	   	ReleaseProjectID uint   `gorm:"not null" json:"release_project_id"`
	   	ReleaseProject   string `gorm:"not null; type:varchar(200)" json:"release_project"`

	   	WorkPackageID uint `gorm:"not null" json:"work_package_id"`
	   	// 以下字段写入description
	   	Type           string `gorm:"not null; type:varchar(60)" json:"type"` // new, bugfix
	   	BugID          string `gorm:"not null; type:varchar(200)" json:"bug_id"`
	   	WorkPackage    string `gorm:"not null; type:varchar(200)" json:"work_package"`
	   	LocalBuildPass bool   `gorm:"not null" json:"local_build_pass"`
	   	MFAL           uint   `gorm:"not null" json:"mfal"` //merge first audit later    //1: create  2: update

	   	Title       string `gorm:"not null; type:varchar(512)" json:"title"`
	   	Description string `gorm:"not null; type:varchar(512)" json:"description"`
	   	UserID      uint   `gorm:"not null" json:"user_id"`
	   	// 同步MR数据
	   	Status             int      `gorm:"not null" json:"status"` // -1: 创建失败  0： 待创建   1： 待评审 2：待合并   3：已合并    4：已关闭

	   	PipelineStatus     uint      `gorm:"not null" json:"pipeline_status"`
	   	SyncStatus         uint      `gorm:"not null" json:"sync_status"`
	   	SyncedAt           time.Time `json:"synced_at"`
	   	CodeQuantityAdd    uint      `gorm:"not null" json:"code_quantity_add"`
	   	CodeQuantityRemove uint      `gorm:"not null" json:"code_quantity_remove"`
	   }
	*/
	// 检查是否存在相同源分支与目标分支且未合并或者关闭的MR表单
	check := dappmergerequest.AppMergeRequest{}
	exists, err := check.CheckExistSameOpen(request.SourceProjectID, request.TargetProjectID, request.SourceBranch, request.TargetBranch)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if exists {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "存在相同源分支与目标分支且未合并或者关闭的MR表单,请确认！"))
		return
	}
	// 检查是否已经转测，转测后只允许提交bugfix
	// config := dreleaseprojectconfig.ReleaseProjectConfig{}
	// err = config.FindEx("release_project_id", fmt.Sprintf("%d", request.ReleaseProjectID))
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	// if config.TestStartAt != "" && request.Type != "bugfix" {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "转测后只允许提交bug修复"))
	// 	return
	// }

	if request.Type == "bugfix" && request.BugID == "" {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "BUGID不能为空"))
		return
	}

	for _, bugID := range strings.Split(request.BugID, ",") {
		check, err := CheckBugExists(bugID)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()+"请重试或联系管理员。"))
			return
		}
		if !check {
			ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "bug:"+bugID+" 不存在，请确认并重试。"))
			return
		}
	}

	var title string
	switch request.Type {
	case "new":
		// [新增][工作包][TITLE]
		title = fmt.Sprintf("[%s][%s][%s]", TypeMap[request.Type], request.WorkPackage, request.OriginTitle)
	case "bugfix":
		// [BUG修订][BUGID][工作包][TITLE]
		title = fmt.Sprintf("[%s][%s][%s][%s]", TypeMap[request.Type], request.BugID, request.WorkPackage, request.OriginTitle)
	case "deploy":
		// [项目部署][工作包][TITLE]
		title = fmt.Sprintf("[%s][%s][%s]", TypeMap[request.Type], request.WorkPackage, request.OriginTitle)
	default:
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "变更类型: "+response.ParamentErr.Msg))
		return
	}

	bugid := request.BugID
	if request.BugID == "" {
		bugid = "N/A"
	}

	localBuildPass := "是"
	if !request.LocalBuildPass {
		localBuildPass = "否"
	}
	preCheck := "是"
	if !request.PreCheck {
		preCheck = "否"
	}
	portable := "是"
	if !request.Portable {
		portable = "否"
	}
	// 为了格式正确，请匆修订对齐
	description := fmt.Sprintf(
		`[修订类型]：%s
[BUGID]：%s
[工作包]：%s
[移植]：%s
[本地编译]：%s
[代码检查]：%s
[Phabricator]：%s

%s
`,
		TypeMap[request.Type]+"  ",
		bugid+"  ",
		request.WorkPackage+"  ",
		portable+"  ",
		localBuildPass+"  ",
		preCheck+"  ",
		MfalMap[request.MFAL]+"  ",
		request.OriginDescription+"  ",
	)

	// 批量创建记录表
	objects := []*dappmergerequest.AppMergeRequest{
		{
			ReleaseProjectID: request.ReleaseProjectID,
			ReleaseProject:   request.ReleaseProject,
			WorkPackageID:    request.WorkPackageID,
			WorkPackage:      request.WorkPackage,

			TargetProjectID: request.TargetProjectID,
			TargetProject:   request.TargetProject,
			TargetBranch:    request.TargetBranch,
			MirrorFrom:      "",

			SourceProjectID: request.SourceProjectID,
			SourceProject:   request.SourceProject,
			SourceBranch:    request.SourceBranch,
			Type:            request.Type,
			BugID:           request.BugID,
			LocalBuildPass:  request.LocalBuildPass,
			Portable:        request.Portable,
			PreCheck:        request.PreCheck,
			MFAL:            request.MFAL,
			ScreenshotFile:  request.ScreenshotFile,

			Title:              title,
			OriginTitle:        request.OriginTitle,
			Description:        description,
			OriginDescription:  request.OriginDescription,
			UserID:             userId,
			Status:             0,
			MergeRequestID:     0,
			MergeRequestIID:    0,
			PipelineStatus:     0,
			PhabricatorStatus:  0,
			CodeQuantityAdd:    0,
			CodeQuantityRemove: 0,
			ReviewerIDs:        libs.UintJoin(request.ReviewerIDs, ","),
			ReviewerUsernames:  request.ReviewerUsernames,
			AssigneeIDs:        libs.UintJoin(request.AssigneeIDs, ","),
			AssigneeUsernames:  request.AssigneeUsernames,
			Phabricator:        dappmergerequest.AppMergeRequestPhabricator{DifferentialUrl: request.DifferentialUrl},
			DependencyIDs:      libs.UintJoin(request.DependencyIDs, ","),
		},
	}
	if len(request.Targets) > 0 {
		if libs.InArrayS(request.Targets, request.TargetBranch) {
			ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "目标分支: "+request.TargetBranch+"与选择的测试分支重复"))
			return
		}
		for _, target := range request.Targets {

			exists, err := check.CheckExistSameOpen(request.SourceProjectID, request.TargetProjectID, request.SourceBranch, target)
			if err != nil {
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
				return
			}
			if exists {
				ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "存在相同源分支与目标分支且未合并或者关闭的MR表单,请确认！"))
				return
			}
			objects = append(objects, &dappmergerequest.AppMergeRequest{
				ReleaseProjectID: request.ReleaseProjectID,
				ReleaseProject:   request.ReleaseProject,
				WorkPackageID:    request.WorkPackageID,
				WorkPackage:      request.WorkPackage,

				TargetProjectID: request.TargetProjectID,
				TargetProject:   request.TargetProject,
				TargetBranch:    target,
				MirrorFrom:      request.TargetBranch,

				SourceProjectID: request.SourceProjectID,
				SourceProject:   request.SourceProject,
				SourceBranch:    request.SourceBranch,
				Type:            request.Type,
				BugID:           request.BugID,
				LocalBuildPass:  request.LocalBuildPass,
				Portable:        request.Portable,
				PreCheck:        request.PreCheck,
				MFAL:            request.MFAL,
				ScreenshotFile:  request.ScreenshotFile,

				Title:              title,
				OriginTitle:        request.OriginTitle,
				Description:        description,
				OriginDescription:  request.OriginDescription,
				UserID:             userId,
				Status:             0,
				MergeRequestID:     0,
				MergeRequestIID:    0,
				PipelineStatus:     0,
				PhabricatorStatus:  0,
				CodeQuantityAdd:    0,
				CodeQuantityRemove: 0,
				ReviewerIDs:        libs.UintJoin(request.ReviewerIDs, ","),
				ReviewerUsernames:  request.ReviewerUsernames,
				AssigneeIDs:        libs.UintJoin(request.AssigneeIDs, ","),
				AssigneeUsernames:  request.AssigneeUsernames,
				DependencyIDs:      libs.UintJoin(request.DependencyIDs, ","),
			})
		}
	}
	//校验是否包含yang文件
	// if request.ScreenshotFile == "" {
	// 	checkRes := CheckGitlabMergeRequestWorker(ctx, objects)
	// 	if !checkRes {
	// 		ctx.JSON(response.NewResponse(300001, nil, "本次MR提交包含yang文件,请上传相关截图"))
	// 		return
	// 	}
	// }

	mr := dappmergerequest.AppMergeRequest{}
	err = mr.BatchCreate(objects)
	if err != nil {
		logging.ErrorLogger.Errorf("batch create merge request get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	go CreateGitlabMergeRequestWorker(ctx, objects)
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func UploadImage(ctx iris.Context) {
	f, fh, err := ctx.FormFile("screenshotFile")
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	defer f.Close()
	// 读取文件的前8字节
	buffer := make([]byte, 8)
	if _, err := f.Read(buffer); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Failed to read file header"))
		return
	}

	// 重置文件读取指针
	if _, err := f.Seek(0, 0); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Failed to reset file pointer"))
		return
	}

	// 检查文件类型
	if !isImageFormat(buffer) {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "截图文件格式不正确，请重新选择"))
		return
	}

	//文档文件名称
	ext := path.Ext(fh.Filename)
	rand.Seed(time.Now().UnixNano())
	randNum := fmt.Sprintf("%d", rand.Intn(9999)+1000)
	hashName := md5.Sum([]byte(time.Now().Format("2006_01_02_15_04_05_") + randNum))
	templateFileName := fmt.Sprintf("%x", hashName) + ext
	// 创建最终存放路径以及保存文件
	var tempDir = libs.Config.FileStorage.Temp + "img/" + time.Now().Format("20060102") + "/"
	err = os.MkdirAll(tempDir, 0750)
	os.Chmod(tempDir, 0750)

	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	templateFilePath := filepath.Join(tempDir, templateFileName)
	_, err = ctx.SaveFormFile(fh, templateFilePath)
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]string{"filePath": templateFilePath}, response.SystemErr.Msg))
	return
}

func GetImages(ctx iris.Context) {
	// var ip = Ip{}
	// addr := GetRemoteAddr(ctx)
	// if addr == "" {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }

	// err := easygorm.GetEasyGormDb().Model(&fileout.AllowAuditIp{}).Where("ip = ? or ip = ?", strings.Join(strings.Split(addr, ".")[:3], ".")+".0", addr).Find(&ip).Error
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	// if ip.ID == 0 {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "非云办公环境，禁止访问此页面。"))
	// 	return
	// }
	permit, err := ip.CheckIP(ctx)
	if !permit {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	id, _ := dao.GetId(ctx)
	mr := dappmergerequest.AppMergeRequest{}
	err = mr.FindEx("id", fmt.Sprintf("%v", id))
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, err.Error(), response.SystemErr.Msg))
		return
	}
	fileName := filepath.Base(mr.ScreenshotFile)
	fmt.Println(fileName, mr.ScreenshotFile)
	ctx.SendFile(filepath.Join(mr.ScreenshotFile), fileName)
	return
}

func CreateGitlabMergeRequestWorker(ctx iris.Context, mrs []*dappmergerequest.AppMergeRequest) {
	token, _ := dao.GetGitlabToken(ctx)
	if token == "" {
		logging.ErrorLogger.Errorf("未配置gitlab token, 请前往个人资料添加gitlab token")
		return
	}
	// mrs, err := dmergerequest.FindAllNotCreatedMergeRequest()
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("find not created merge request get err ", err)
	// 	return
	// }
	for _, mr := range mrs {
		err := CreateGitlabMergeRequest(token, mr)
		if err != nil {
			logging.ErrorLogger.Errorf("create merge request get err ", err)
		}
	}
}

func CreateGitlabMergeRequest(token string, mr *dappmergerequest.AppMergeRequest) error {
	id := mr.SourceProjectID
	url := fmt.Sprintf("%s/api/%s/projects/%d/merge_requests?private_token=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, id, token)
	var result MergeRequestResponse
	var errMsg interface{}

	data := map[string]interface{}{
		"title":             mr.Title,
		"description":       mr.Description,
		"source_branch":     mr.SourceBranch,
		"target_branch":     mr.TargetBranch,
		"target_project_id": mr.TargetProjectID,
		"reviewer_ids":      strings.Split(mr.ReviewerIDs, ","),
		"assignee_ids":      strings.Split(mr.AssigneeIDs, ","),
	}
	resp, err := GitlabWebClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetBody(data).Post(url)
	if err != nil {
		err := mr.Update(mr.ID, map[string]interface{}{
			"Status": -1,
		})
		if err != nil {
			return err
		}
		return err
	}
	if resp.IsSuccessState() {
		err := mr.Update(mr.ID, map[string]interface{}{
			"Status":          1,
			"MergeRequestID":  result.ID,
			"MergeRequestIID": result.IID,
		})
		if err != nil {
			return err
		}
		// 获取代码变更数据
		GetMergeRequestDiff(token, result.IID, mr)
		//消息通知
		SendMRMessage(token, result.IID, mr)
		// todo: 启动pipeline
		// pipeline由gitlab systemhook触发
		// todo: 创建phabricator
		// phabricator由gitlab systemhook触发创建
		return nil
	} else {
		err := mr.Update(mr.ID, map[string]interface{}{
			"Status": -1,
		})
		if err != nil {
			return err
		}
		return errors.New("未知错误")
	}

}

func GetMergeRequestDiff(token string, mrIID int, mr *dappmergerequest.AppMergeRequest) error {
	id := mr.TargetProjectID
	url := fmt.Sprintf("%s/api/%s/projects/%d/merge_requests/%d/changes?private_token=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, id, mrIID, token)
	var result ChangesResponse
	var errMsg MessageResponse
	resp, err := GitlabWebClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).Get(url)
	if resp.IsSuccessState() {
		remove := 0
		add := 0
		addPattern := regexp.MustCompile(`\n\+\s*\n`)
		removePattern := regexp.MustCompile(`\n\-\s*\n`)
		for _, c := range result.Changes {
			remove = remove + strings.Count(c.Diff, "\n-") - len(removePattern.FindAllStringIndex(c.Diff, -1))
			add = add + strings.Count(c.Diff, "\n+") - len(addPattern.FindAllStringIndex(c.Diff, -1))
		}
		err := mr.Update(mr.ID, map[string]interface{}{
			"code_quantity_add":    add,
			"code_quantity_remove": remove,
		})
		if err != nil {
			logging.ErrorLogger.Errorf("update merge request %d sync get err ", mr.ID, err)
		}
		his := dappmergerequesthistory.AppMergeRequestHistory{}
		err = his.CreateHistory(mr.ID, map[string]interface{}{
			"MergeRequestID":     mr.ID,
			"Status":             1,
			"CodeQuantityAdd":    add,
			"CodeQuantityRemove": remove,
			"CreatedAt":          time.Now(),
			"SyncStatus":         1,
			"PipelineStatus":     0,
			"PhabricatorStatus":  0,
		})

		if err != nil {
			logging.ErrorLogger.Errorf("create merge request history get err ", mr.ID, err)
		}
		// 获取代码变更数据
		return nil
	}
	if err != nil {
		logging.ErrorLogger.Errorf("update merge request %d sync get err ", mr.ID, err)
		return err
	}
	logging.ErrorLogger.Errorf("update merge request %d sync get err ", mr.ID, errors.New("未知错误"))
	return errors.New("未知错误")
}

func SendMRMessage(token string, mrIID int, mr *dappmergerequest.AppMergeRequest) error {
	var AssigneeTo, ReviewerTo []string
	// 检查 Assignees 是否为空
	if mr.Assignees != nil {
		for _, user := range mr.Assignees {
			AssigneeTo = append(AssigneeTo, fmt.Sprintf("%<EMAIL>", user.Username))
		}
	}
	// 检查 Reviewers 是否为空，并排除已在 Assignees 中的用户
	if mr.Reviewers != nil {
		for _, user := range mr.Reviewers {
			email := fmt.Sprintf("%<EMAIL>", user.Username)
			// 如果用户不在 AssigneeTo 列表中，才添加到 ReviewerTo
			if !contains(AssigneeTo, email) {
				ReviewerTo = append(ReviewerTo, email)
			}
		}
	}

	gitlabMRUrl := fmt.Sprintf("%s/%s/-/merge_requests/%d", libs.Config.Gitlab.Url, mr.TargetProject, mrIID)
	// 发送消息给合并者
	assigneeSubject, assigneeMessage := generateMessage(mr, gitlabMRUrl, "合并")
	err := libs.SendCardMessage(AssigneeTo, assigneeSubject, assigneeMessage, []string{})
	if err != nil {
		logging.ErrorLogger.Errorf("Failed to send message to assignees: %v", err)
		return err
	}

	// 发送消息给评审者
	reviewerSubject, reviewerMessage := generateMessage(mr, gitlabMRUrl, "评审")
	err = libs.SendCardMessage(ReviewerTo, reviewerSubject, reviewerMessage, []string{})
	if err != nil {
		logging.ErrorLogger.Errorf("Failed to send message to reviewers: %v", err)
		return err
	}
	return nil
}

// 检查字符串切片中是否包含特定元素
func contains(slice []string, item string) bool {
	for _, v := range slice {
		if v == item {
			return true
		}
	}
	return false
}

func generateMessage(mr *dappmergerequest.AppMergeRequest, gitlabMRUrl string, action string) (string, string) {
	user := duser.User{}
	user.Find(mr.UserID)
	subject := fmt.Sprintf("[MR表单系统]代码合并请求%s通知 - MR: %s", action, mr.Title)
	message := fmt.Sprintf(
		`有一个新的合并请求，等待%s，具体信息如下：<br>
- 项目名称：%s <br>
- 组件：%s <br>
- 合并请求标题：%s <br>
- 源分支：%s <br>
- 目标分支：%s <br>
- 提交人：%s  <br>
- 链接：%s
`,
		action,            // 动作（合并/评审）
		mr.ReleaseProject, // 项目名称
		mr.TargetProject,
		mr.Title,        // 合并请求标题
		mr.SourceBranch, // 源分支
		mr.TargetBranch, // 目标分支
		user.Name,       // 提交人
		gitlabMRUrl,     // MR链接
	)
	return subject, message
}

func GetReleaseProjects(ctx iris.Context) {
	items, err := dappproject.GetRunningProjects()
	if err != nil {
		logging.ErrorLogger.Errorf("get running release project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, items, response.NoErr.Msg))
	return
}

func GetUnitPackages(ctx iris.Context) {
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	var count int64
	var projects []*dappunitpackages.ListResponse

	db := easygorm.GetEasyGormDb().Model(dappunitpackages.Model())

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("db count err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&projects).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get unitpackage err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	list := map[string]interface{}{"items": projects, "total": count, "limit": pageSize}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetPersonRepositories(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	token, _ := dao.GetGitlabToken(ctx)
	if token == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置gitlab token, 请前往个人资料添加gitlab token"))
		return
	}

	url := fmt.Sprintf("%s/api/%s/projects/%d/forks?private_token=%s&owned=true", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, id, token)
	var result []*ForkResponse
	var errMsg MessageResponse
	resp, err := GitlabWebClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).Get(url)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error() + errMsg.Message}, response.SystemErr.Msg))
		return
		// return result, errors.New(fmt.Sprintf("%s, %s", err.Error(), errMsg))
	}
	if resp.IsSuccessState() {
		ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.SystemErr.Code, errMsg, "未知错误"))
	return
}

func GetTargetBranches(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, map[string]string{"err_msg": err.Error()}, response.ParamentErr.Msg))
		return
	}
	project := dappproject.Response{}
	err = project.Find(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	if ctx.FormValue("unit_package_id") == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	unitPackageID, _ := strconv.Atoi(ctx.FormValue("unit_package_id"))
	token := libs.Config.Gitlab.Token
	if token == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置gitlab token, 请联系管理员"))
		return
	}

	// /projects/:id/repository/branches

	var result []*BranchResponse
	var errMsg MessageResponse
	if len(result) == 0 {
		page := 1
		for {
			var _result []*BranchResponse
			url := fmt.Sprintf("%s/api/%s/projects/%d/repository/branches?private_token=%s&search=%s&page=%d", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, unitPackageID, token, "", page)
			resp, err := GitlabWebClient.R().SetSuccessResult(&_result).SetErrorResult(&errMsg).Get(url)
			if err != nil {
				ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error() + errMsg.Message}, response.SystemErr.Msg))
				return
			}
			if resp.IsSuccessState() {
				page++
				if len(_result) > 0 {
					result = append(result, _result...)
				} else {
					break
				}
			} else {
				ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": errMsg.Message}, response.SystemErr.Msg))
				return
			}
		}
	}

	items := []string{}

	for _, _item := range result {
		if !libs.InArrayS(items, _item.Name) {
			items = append(items, _item.Name)
		}
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, items, response.NoErr.Msg))
	return
}

func GetTestTargetBranches(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, map[string]string{"err_msg": err.Error()}, response.ParamentErr.Msg))
		return
	}
	project := dappproject.Response{}
	err = project.Find(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	if ctx.FormValue("unit_package_id") == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	unitPackageID, _ := strconv.Atoi(ctx.FormValue("unit_package_id"))
	token := libs.Config.Gitlab.Token
	if token == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置gitlab token, 请联系管理员"))
		return
	}
	var result []*BranchResponse
	var errMsg MessageResponse
	if len(result) == 0 {
		page := 1
		for {
			var _result []*BranchResponse
			url := fmt.Sprintf("%s/api/%s/projects/%d/repository/branches?private_token=%s&search=%s&page=%d", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, unitPackageID, token, "test", page)
			resp, err := GitlabWebClient.R().SetSuccessResult(&_result).SetErrorResult(&errMsg).Get(url)
			if err != nil {
				ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error() + errMsg.Message}, response.SystemErr.Msg))
				return
			}
			if resp.IsSuccessState() {
				page++
				if len(_result) > 0 {
					result = append(result, _result...)
				} else {
					break
				}
			} else {
				ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": errMsg.Message}, response.SystemErr.Msg))
				return
			}
		}
	}

	items := []string{}
	for _, _item := range result {
		if !libs.InArrayS(items, _item.Name) {
			if strings.HasPrefix(_item.Name, "test") {
				items = append(items, _item.Name)
			}
		}
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, items, response.NoErr.Msg))
	return
}

func GetBranches(ctx iris.Context) {
	search := ctx.FormValue("search")
	id, err := dao.GetId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	token, _ := dao.GetGitlabToken(ctx)
	if token == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置gitlab token, 请前往个人资料添加gitlab token"))
		return
	}

	// /projects/:id/repository/branches
	var result []*BranchResponse
	var errMsg MessageResponse
	page := 1
	for {
		var _result []*BranchResponse
		url := fmt.Sprintf("%s/api/%s/projects/%d/repository/branches?private_token=%s&search=%s&page=%d", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, id, token, search, page)
		resp, err := GitlabWebClient.R().SetSuccessResult(&_result).SetErrorResult(&errMsg).Get(url)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error() + errMsg.Message}, response.SystemErr.Msg))
			return
		}
		if resp.IsSuccessState() {
			page++
			if len(_result) > 0 {
				result = append(result, _result...)
			} else {
				break
			}
		} else {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": errMsg.Message}, response.SystemErr.Msg))
			return
		}
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func GetMembers(ctx iris.Context) {
	search := ctx.FormValue("search")
	id, err := dao.GetId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	token, _ := dao.GetGitlabToken(ctx)
	if token == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置gitlab token, 请前往个人资料添加gitlab token"))
		return
	}

	// /projects/:id/repository/branches
	var result []*MemberResponse
	var errMsg MessageResponse
	page := 1
	for {
		var _result []*MemberResponse
		url := fmt.Sprintf("%s/api/%s/projects/%d/members/all?private_token=%s&search=%s&page=%d", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, id, token, search, page)

		resp, err := GitlabWebClient.R().SetSuccessResult(&_result).SetErrorResult(&errMsg).Get(url)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error() + errMsg.Message}, response.SystemErr.Msg))
			return
		}
		if resp.IsSuccessState() {
			page++
			if len(_result) > 0 {
				result = append(result, _result...)
			} else {
				break
			}
		} else {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": errMsg.Message}, response.SystemErr.Msg))
			return
		}
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func GetPhabricatorUrls(ctx iris.Context) {
	// search := ctx.FormValue("search")
	// id, err := dao.GetId(ctx)
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
	// 	return
	// }
	token := libs.Config.MergeRequest.PhabricatorToken
	url := libs.Config.MergeRequest.PhabricatorUrl
	if token == "" || url == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置phabricator, 请联系管理员"))
		return
	}

	// /projects/:id/repository/branches
	var result DifferentialResponse
	var errMsg MessageResponse
	after := ""
	for {
		var _result DifferentialResponse
		url := fmt.Sprintf("%s/api/differential.revision.search?api.token=%s&limit=%d&after=%s", url, token, 100, after)
		resp, err := GitlabWebClient.R().SetSuccessResult(&_result).SetErrorResult(&errMsg).Get(url)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": errMsg, "err": err.Error()}, response.SystemErr.Msg))
			return
		}
		if resp.IsSuccessState() {
			if len(_result.Result.Data) > 0 {
				result.Result.Data = append(result.Result.Data, _result.Result.Data...)
			}
			if _result.Result.Cursor.After != "" {
				after = _result.Result.Cursor.After
			} else {
				break
			}
		} else {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": errMsg.Message}, response.SystemErr.Msg))
			return
		}
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, result.Result.Data, response.NoErr.Msg))
	return
}

func GetMergeRequestWebUrl(ctx iris.Context) {
	token, _ := dao.GetGitlabToken(ctx)
	if token == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置gitlab token, 请前往个人资料添加gitlab token"))
		return
	}

	id, err := dao.GetId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, map[string]string{"err_msg": err.Error()}, response.ParamentErr.Msg))
		return
	}
	mr := dappmergerequest.AppMergeRequest{}
	err = mr.Find(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, map[string]string{"err_msg": err.Error()}, response.ParamentErr.Msg))
		return
	}

	url := fmt.Sprintf("%s/api/%s/projects/%d/merge_requests/%d?private_token=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, mr.TargetProjectID, mr.MergeRequestIID, token)
	var result MergeRequestResponse
	var errMsg MessageResponse
	resp, err := GitlabWebClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).Get(url)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, map[string]string{"err_msg": err.Error()}, response.ParamentErr.Msg))
		return
	}
	if resp.IsSuccessState() {
		ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]string{"url": result.WebUrl}, response.NoErr.Msg))
		return
	} else {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": "未知错误，" + resp.Status}, response.SystemErr.Msg))
		return
	}
}

func CheckBugExists(bugID string) (bool, error) {
	resp, err := BugWebClient.R().Get(fmt.Sprintf("http://bugs.ruijie.com.cn/bug_switch/bug/bug_info?bugId=%s", bugID))
	if err != nil {
		return false, err
	}
	if strings.Contains(resp.String(), "<title>##Message##") {
		_, err := BugWebClient.R().SetQueryString("username=linjiakai&type=UsernamePassword&_eventId=submit&geolocation=&execution=b404bad9-31a1-4e09-b30a-a3ae3c266a1f_H4sIAAAAAAAAAK1aDWwcxRUen%2F8dJ%2F7BCaQobhQnIYTmzjZufjApuZzt5JJz7PjsJBykyXp37m7jvd3N7KzvjqJQhAK0FaJIkEJDgVZIDVFakJAAkVJAINFWQFUK%2FaFKJSogoKqorVpRlUr0zez%2F%2BXycDaf4srvz5r03b9689723d%2B5vqN4g6AaNZMKCjgnWwoZOZDUTzuPptKLlw7piZmQ1HFNkrNIReDJcwKJJZU2dwLpmyFQjxbVJTGRBkW%2FCUoAiSQWKf9F%2Fz57od%2B99NYRqE6hD1NRZTAyBD4uajimKJkB6xBKbJkIO5zUyE7HlR0SNYPhSFCyyOZFRkwrTCo5SSuRpk%2BJRQR9MoGbsyKToqxX5uYSRgKrAo4GNxyWKOhPHhFkhoghqJpKkjM1gQQcrbWdWKuHr2onpGfb0DCc0UVD8Wt5y9JfPHdpx119rUU0CtQjOiEFRmyUP9FAibDlcWAd7FmbPwrsFIwvP6xvffvGlFUd%2FU4tCI6hF0QRpRBDB%2FHHUTLMEG1lNkQr6dTsQ%2B7Tmm%2BC7Hf5CFLWkFWDBzW0cRydgy9l3k02KXFJUKFDUqmiw4TsFcWaKyBRtzVKqr7s6uq5%2FBP5NmxkjTEz5mMwWmwuLqvXwiJGXqZiFGyMrE22TKBiFAqxiWyWTYRUE4bCc05VwYDPi8OT5Kfr3gZ79F1pBK%2F0EQYOL4sQ80DSQ%2FekEXgQt45ZluxseVs2cf1CnqCEam4wfGAbdu7wdSMjqDJYSskFbr0we3nP0zjW1jDpfB%2FNqgHTLQpRLYsOwF7lE%2BteW14Y%2B%2BLjVkl3PTU%2FR0lkZ5xPsekQjuTJbdhnfstXw10XRSl0wDJAojQqqkME5OKfDKjsjkutGfLE7NU3BgvraavLNtx7870chVJNC9bOCYuKCXkPREoIlmYD38m3fYWAyK4t4%2B2K3n6Jmtooqna4tLxA1pmkzMj7ANOK03YiiLgN2UBajJs3CumSRBw42uJ6idlAhp6kxUJyNCQqs91pfJAMtwnBnUYWFAItwjD%2BdLOrYm39%2Fsrn7T3fUPxlCjSnUDBM0vglwrcizOJbF4kwKtRAwcW4ak1GcQI0i0Yo61ZhGEN6anK2w7%2Bso8HfGTDCpCs7B7ws6rFyHD9hJF8SBY1NEMUD%2FS0udjh3%2BJKZ%2FUH6fuu%2FCxu4Qd%2BGS4ADjzw6dvO%2FUM08PWH7ZymzrRALgmiqxCrhmmPvmEFZwBoK0ZMX3oJmjPJKtHSfarCxhwm0xDh4W09S0nDEJJzow8tqJ5CuyFEL1KdTKbDZh%2BxEsWTSMGIQewzGHu%2FyE392UOdYC60AYTgur08ImRRNnVguSoFNMKGp0r64XucoOm%2Bus2yNMxHabaL3fia%2BJROa6b8Rz3ojPdWtFW%2BkI6LHcpwdEef0gFrMQvlGL%2F%2BbGCtp4dItWqOl4MY%2Flgqy6WnX4tHKUaHAuDlXQ5nNq0pDn88tahxaHJTOmaCYk0Rb%2FTSXreHSL1glkYckUGQ9Xr0t8ekkQl4fgD%2BzoXaYq6ORQLX6%2FGAf25%2BpzaRl9DkJchoQbvP16FXoxykXr1urolgcurn6rfPoZMKhgy1HGWNzcBKdxzsPVFKUr6DqHfl1%2F76J17rC4Wb6nMW6u4it8iqexbGRNLg0SWuDucAVVfYSL1tCWFtStzaebbKiQV%2Bus%2Fw5U0IZRLFoNzr%2FsITAMzTZFk3dZ6RA4VIvWZVkGq1AYiNwm%2Fb2uVpeXDahcVj%2FP6qWPpqsKrhb1F66t37%2FyWIGk7PhX4K6Sf%2FkIvyj1ADMt5WhxwlQwQzEQmVUAi4ICmImRqOOaIotFFwkDCgiXoADD1HWNUBCv4DDj4xKPahJWHp786dMvNhz7Dy%2FcmhhPS856Xl9ZfCLAB6ozFVCaSiNJQ2MIgqOqkqKs3VfjMBzNhg1sF0vM1Mc5yl9boqLDOuxn7UfsbGonYKhkcuzISHwiOQnrvMRDR1FChCKTV7j1jVUPvCL8AKqvOKozoFTlCKzGhfAbKxmHi2YW4oa54vxvd41OXTwKhkmhBniAKUDBZrYdomWiK0pNZHOLMG6RhEMIRmjMYZrVrLQFJkBgg3WVNHHnljFCaGyC85mFx%2FV8YfVg%2BCkbdo7b0LQEuDQZOSNhlR3BgNlk3e0nUMg5XBHnigrslJqEgIPzqdaiy0j6soeVg9DSLlBsIO%2FCZmOuB%2Bi5sMMvloUyBu%2BEGuYbY7ecevPTrsYQClk9hbRMclgaD6LvIBovcLDtAHZIg%2FJ%2B0dibyubje3q3w2ki%2FgPgrriOr7gOFOuv%2BvQwjMwd5fQn527dfTjy4xCqg7pBNjxkXCfCuK1mvZyDCY7OUMbn9%2FmqBPb%2F7RRtCEc4mRHhp36TDteRUoOHdQZsunp6RqHEBIKenr7evt7egb6eHr6gu1Fgv1eX4%2BkMz8er38cr4EhrynHzCObh1z%2Fg181xOb0MZd%2Fmnh5wvEY7gMKO7CrZkZIKj5XjoqwLSjjJam%2BAItNRXVfs4aTFhlXhxpm7734k94%2B3rdpquFqu0WmDEkGkZfk25e84GY0%2FcS6EmlKoHSySwdKYSaMKwYJUZA0gQuU0zI473hpoCV3iC5d2%2B4d3qFiEB1v3l8YXKONlicuPHHAvJ7ChgytjO9SEZEfUEo3IsEWC4tVeze667AcNhmYS0XVDPVC91%2FDqvYbHAohYvSUm87QJl9emTPCqPTSaoCi8sLTI5p7R2fdDkKhhj2Yw3UUgXwBanOR3cQl8aWVG0wA9RmHFRSAyJmHbZoCED7azTZzZhTXWsXO7C%2Bxr6efofgFkJTgDiQeMIiVdl52Tgu2R8ATO4MJE6Yy7xEN7cQ1%2Bz%2FLMvvkmO544Z%2F6xlTe%2Fc%2Faf394UQiviqA2zZg9f4hiBen4P84gEWiaIwMNIAgeKM0WKts1JXracyBz%2B0cBU8LEVrhNPQFoUDGyhEKfJWx3TsiwGWTsBkAQs00qW4MYSNkQi686mgde2Q1qXrcaEI3mwesnDJZNB5jJZtQ4dPPR1KuBAOz2fFnZtUisHbq5e2NqEO2%2FQQg9wByIA6lmHX8U0MjWRgMGOnKlY0UIjzrKurV7SaOnswWArpgXO%2FqwgFr31wRMNOFPZasQ1gK3hSaHoCF%2FAMse9eWyZujkNl3sxMBlYABNnFrBoJ%2Fi4CWlU2i2okoJJmWiZxAxfthJfxHHCnC3Djbr1ENxdQ6x0WnSuCzq9L4qGqtd2aj4ugxxz8k%2F7zb%2F%2BOcSDHfMd6Uk5h3eC80ufcebObrvnictPn23mSGgZVnmHAEbYfOdMAIImtPQ5xJOvzSd8CKcF8JnPEN2xu%2FtXBza0%2FSSEmlOoDRjgOBhbNWQqz%2BIUasQWxoujeo2FmxTqsncuqngvRgwARlBo2ngwgVZJTkcyCBgdz9td%2FTYMVeIE%2FtFJ8DEAYzDuKmM7e6fjYnNGLjVVhgcgf97EtsbtYJYc2jjbaZ4ea1htMbpQQ1dU%2Fd%2Fdey5MftS1PMReJLULiqLlAfraLganYYXvNMTcF1LsrRJbxFd4c7jGacMX5us5A%2BIYeGHfje3KC5%2FwNwVLrDTBM0dhPiyAuIiXy40A%2BnZelJTPf9Qkqt8zAtE%2Fcf%2FBM%2FsfOiVaibAU%2BH12IizPNX7xd4ZympURANKu8jZ2UrPJSvrhPqddV4bae5HgAPIUWl%2BGjofEIExJoctwQVRMCdsu4clKoDY3qY7ICu99X7OIbGrNBb9fyopq0NM5UlurZxbzzwRWq1zU6OnrvRamaFcp6yCYjrjTI%2BOVGIGkLpc07p1KG5wyD2POVYrcXbewFzzvcQss64%2Ff%2BmH3m8%2FfEufVvRPEICbY%2B%2BOLCcE3t1bSWQ5aMrIxVSkGo8froCg%2FA4lqCwxb3YqmebHtqg3kyNnbrIORrJa1KIhZ7B6XihIOrf7%2By4829E6DPfagFg9fgV%2FmMIEyIuNBR1z1fnMFIlUpsHY0KIYhQQppbEqVqfcujX1C7B3q7rGpiaTuFvA1wZe0C4nCMQtudk6vbV9%2B%2F%2BzGEKq3IXOLhHVIqexNqwMjcE6QXXTow1b1elZTcVBNxBoRDe%2F97JH3H%2FqeDiVt98VH7r342FMfPvCj924%2F%2BeFtZz547vF3zz%2B1c2rXxSfPXzz%2FcCWYMJ%2FqpUh25M%2FFcyvvuvZV7tQdVsvqIOwMp8MQpzpUDTBikT0b4qOsMvA2m0EHdxEQyqHoG6hQysyLdMsUfq07o7G9R2K7o%2Fv2DSdgra08G0EeDQP4PbVuy%2BYv%2Fe9dUosa46gpC3kppkk4jupY6yXB39Gy6EqLjunTsuKYvi6rGc7%2BNAF8pZqoOVtUS3DaXs%2Bn9oeizrn1HUW8AfMOBW5QCeosXUYXuhlz8Pdbx%2F5y4c7rjev4O9Ol00XWp3IR0JI0eJJJeAeJovjngPlrRzxOBpyaNb4yogRYOOiBB6qycZ31pCaJnMlgYtuwey4Rf3c%2FqY0KUA077nKiQr5e4BrKeE%2FjvrHJI8nhybLYhp2ztpLTpAcgSgOHKE1Wq3SpgUWTOVMCz2L2Y4KtC93pcatsKnY%2Be%2Bpw82OvnwZUboeMhln3Zw3PeIGA328KvK6voaimD4JCk%2FUCIW61hj%2BqPKMWPJfNYc1D0CdXzZyGG9aMx9YchmlLJA2ilxrV9eqkXba5X9g2sHmgV%2BjdKvVd3dvbu3nL9NV90gDwqhMkiVTDZOW8nZ4CwEiCrpnfZ9JQplWqM594FKkvHXp%2FMsRdoIx4%2FmObrRuYBjezL%2BPK6ltO4Y36YgKy26q1T9r4Gx%2BPrHtAOGjl7ZH52LESsPr68juPP%2Ftg%2FsDW4%2BB2UJFhVSRFnTrUgKFEQdVUOPKKfJPgvObxfm9Rt29s33ChoBcK%2FHCt%2FD%2F0s7g%2FHCgAAA%3D%3D&captcha_code=&croypto=iQcsKZhwIJ0%3D&password=565%2FN1Pk0KmLV7J7zBV5%2BQ%3D%3D").Post("https://sid.ruijie.com.cn/login")
		if err != nil {
			return false, err
		}
		resp, err = BugWebClient.R().Get(fmt.Sprintf("http://bugs.ruijie.com.cn/bug_switch/bug/bug_info?bugId=%s", bugID))
		if err != nil {
			return false, err
		}
		if strings.Contains(resp.String(), "BUGID不存在") {
			return false, nil
		}
	} else {
		if strings.Contains(resp.String(), "BUGID不存在") {
			return false, nil
		}
	}
	return true, nil
}

func CheckGitlabMergeRequestWorker(ctx iris.Context, mrs []*dappmergerequest.AppMergeRequest) bool {
	token, _ := dao.GetGitlabToken(ctx)
	if token == "" {
		logging.ErrorLogger.Errorf("未配置gitlab token, 请前往个人资料添加gitlab token")
		return false
	}
	// 使用通道来表示Goroutines的完成
	results := make(chan bool, len(mrs))
	for _, mr := range mrs {
		go func(mr *dappmergerequest.AppMergeRequest) {
			pass, err := CodeCheckYangJob(mr)
			if err != nil {
				logging.ErrorLogger.Errorf("code check get err ", err)
				results <- false
				return
			}
			results <- pass
		}(mr)
	}

	overallResult := true
	for range mrs {
		pass := <-results
		fmt.Println("pass:", pass)
		if !pass {
			overallResult = false
		}
	}
	return overallResult
}

func GetDependencys(ctx iris.Context) {
	id, err := dao.GetId(ctx) //release_project_id
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}

	var list map[string]interface{}
	list, err = dappmergerequest.GetAllDependencysByReleaseProjectID(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

// 定义图片格式的文件头
var imageHeaders = map[string][]byte{
	"jpg":  {0xFF, 0xD8, 0xFF},
	"jpeg": {0xFF, 0xD8, 0xFF},
	"png":  {0x89, 0x50, 0x4E, 0x47},
	"gif":  {0x47, 0x49, 0x46},
}

// 检查文件头是否匹配已知的图片格式
func isImageFormat(header []byte) bool {
	for _, format := range imageHeaders {
		if bytes.HasPrefix(header, format) {
			return true
		}
	}
	return false
}
