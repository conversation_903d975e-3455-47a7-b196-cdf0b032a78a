package dcsburesresourcesplit

import (
	"encoding/json"
	"fmt"
	"reflect"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/datasync"
	"irisAdminApi/service/dao/datasync/dcsburesresourceapply"
	"irisAdminApi/service/dao/datasync/dsyncrecord"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "分摊资源计划申请表"

type ResourceSplitSyncResponse struct {
	State   string                   `json:"state"`
	Data    []*ResourceSplitResponse `json:"data"`
	Total   int                      `json:"total"`
	Message string                   `json:"message"`
}

type ResourceSplitResponse struct {
	RowNum                int                                      `json:"rownum"`
	ID                    int64                                    `json:"id,omitempty"`
	ApplyID               *int64                                   `json:"applyId,omitempty"`
	UserID                *int64                                   `json:"userId,omitempty"`
	Year                  *string                                  `json:"year,omitempty"`
	Month                 *string                                  `json:"month,omitempty"`
	Hour                  *float64                                 `json:"hour,omitempty"`
	Percent               *float64                                 `json:"percent,omitempty"`
	ProjectID             *int64                                   `json:"projectId,omitempty"`
	BeginTime             *dcsburesresourceapply.ResourceApplyTime `json:"beginTime,omitempty"`
	EndTime               *dcsburesresourceapply.ResourceApplyTime `json:"endTime,omitempty"`
	PlanHour              *float64                                 `json:"planHour,omitempty"`
	Enable                *int                                     `json:"enable,omitempty"`
	CreateDate            *dcsburesresourceapply.ResourceApplyTime `json:"createDate,omitempty"`
	ModifyDate            *dcsburesresourceapply.ResourceApplyTime `json:"modifyDate,omitempty"`
	ValidDays             *int                                     `json:"validDays,omitempty"`
	PlanManMonth          *float64                                 `json:"planManMonth,omitempty"`
	TargetDeptID          *int64                                   `json:"targetDeptId,omitempty"`
	TargetGroupID         *int64                                   `json:"targetGroupId,omitempty"`
	OneDeptCode           *string                                  `json:"oneDeptCode,omitempty"`
	TwoDeptCode           *string                                  `json:"twoDeptCode,omitempty"`
	ThreeDeptCode         *string                                  `json:"threeDeptCode,omitempty"`
	FourDeptCode          *string                                  `json:"fourDeptCode,omitempty"`
	FiveDeptCode          *string                                  `json:"fiveDeptCode,omitempty"`
	OneDeptName           *string                                  `json:"oneDeptName,omitempty"`
	TwoDeptName           *string                                  `json:"twoDeptName,omitempty"`
	ThreeDeptName         *string                                  `json:"threeDeptName,omitempty"`
	FourDeptName          *string                                  `json:"fourDeptName,omitempty"`
	FiveDeptName          *string                                  `json:"fiveDeptName,omitempty"`
	FinalDeptCode         *string                                  `json:"finalDeptCode,omitempty"`
	PmsProjectID          *int64                                   `json:"pmsProjectId,omitempty"`
	PmsProjectName        *string                                  `json:"pmsProjectName,omitempty"`
	UserName              *string                                  `json:"userName,omitempty"`
	CasUserID             *string                                  `json:"casUserId,omitempty"`
	VirtualDept           *string                                  `json:"virtualDept,omitempty"`
	BusinessDept          *string                                  `json:"businessDept,omitempty"`
	DepartmentName        *string                                  `json:"departmentName,omitempty"`
	GroupName             *string                                  `json:"groupName,omitempty"`
	IsFormalEstablishment *int64                                   `json:"isFormalEstablishment,omitempty"`
}

type CsbuResResourceSplit struct {
	datasync.CsbuResResourceSplit
}

type ListResponse struct {
	CsbuResResourceSplit
}

type Request struct {
	Id uint `json:"id"`
}

func (c *CsbuResResourceSplit) ModelName() string {
	return ModelName
}

func Model() *datasync.CsbuResResourceSplit {
	return &datasync.CsbuResResourceSplit{}
}

func (c *CsbuResResourceSplit) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *CsbuResResourceSplit) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (c *CsbuResResourceSplit) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (c *CsbuResResourceSplit) CreateV2(object interface{}) error {
	return nil
}

func (c *CsbuResResourceSplit) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (c *CsbuResResourceSplit) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(c).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (c *CsbuResResourceSplit) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(c).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (c *CsbuResResourceSplit) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (c *CsbuResResourceSplit) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(c).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (c *CsbuResResourceSplit) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(c).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func BatchCreateOrUpdate(records []map[string]interface{}) error {
	xt := reflect.TypeOf(&datasync.Bug{})
	columns := []string{}
	for i := 0; i < xt.Elem().NumField(); i++ {
		key, ok := xt.Elem().Field(i).Tag.Lookup("update")
		if ok {
			columns = append(columns, key)
		}
	}
	db := easygorm.GetEasyGormDb().Model(Model())

	err := db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "bug_id"}},
		DoUpdates: clause.AssignmentColumns(columns),
	}).Create(&records).Error
	if err != nil {
		return err
	}

	return nil
}

func UpdateOrCreateResouceSplitTransaction(items []*ResourceSplitResponse, _url string, data map[string]string, method, state, errorMsg string) error {
	objects := []map[string]interface{}{}
	ids := []int64{}
	for _, item := range items {
		ids = append(ids, item.ID)
		object := map[string]interface{}{
			"ID":                    item.ID,
			"ApplyID":               item.ApplyID,
			"UserID":                item.UserID,
			"Year":                  item.Year,
			"Month":                 item.Month,
			"Hour":                  item.Hour,
			"Percent":               item.Percent,
			"ProjectID":             item.ProjectID,
			"PlanHour":              item.PlanHour,
			"Enable":                item.Enable,
			"ValidDays":             item.ValidDays,
			"PlanManMonth":          item.PlanManMonth,
			"TargetDeptID":          item.TargetDeptID,
			"TargetGroupID":         item.TargetGroupID,
			"OneDeptCode":           item.OneDeptCode,
			"TwoDeptCode":           item.TwoDeptCode,
			"ThreeDeptCode":         item.ThreeDeptCode,
			"FourDeptCode":          item.FourDeptCode,
			"FiveDeptCode":          item.FiveDeptCode,
			"OneDeptName":           item.OneDeptName,
			"TwoDeptName":           item.TwoDeptName,
			"ThreeDeptName":         item.ThreeDeptName,
			"FourDeptName":          item.FourDeptName,
			"FiveDeptName":          item.FiveDeptName,
			"FinalDeptCode":         item.FinalDeptCode,
			"PmsProjectID":          item.PmsProjectID,
			"PmsProjectName":        item.PmsProjectName,
			"UserName":              item.UserName,
			"CasUserID":             item.CasUserID,
			"VirtualDept":           item.VirtualDept,
			"BusinessDept":          item.BusinessDept,
			"DepartmentName":        item.DepartmentName,
			"GroupName":             item.GroupName,
			"IsFormalEstablishment": item.IsFormalEstablishment,
		}

		/*
			"BeginTime":             item.BeginTime,
			"EndTime":               item.EndTime,
			"CreateDate":            item.CreateDate,
			"ModifyDate":            item.ModifyDate,
		*/

		if item.BeginTime != nil {
			object["BeginTime"] = item.BeginTime.Time
		}
		if item.EndTime != nil {
			object["EndTime"] = item.EndTime.Time
		}
		if item.CreateDate != nil {
			object["CreateDate"] = item.CreateDate.Time
		}
		if item.ModifyDate != nil {
			object["ModifyDate"] = item.ModifyDate.Time
		}

		objects = append(objects, object)
	}

	columns := []string{}

	xt := reflect.TypeOf(Model())
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")
		if ok && ok2 {
			columns = append(columns, name)
		}
	}

	body, err := json.Marshal(data)
	if err != nil {
		return err
	}
	db := easygorm.GetEasyGormDb()
	err = db.Transaction(func(tx *gorm.DB) error {
		if len(objects) > 0 {
			err := tx.Model(Model()).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "id"}},
				DoUpdates: clause.AssignmentColumns(columns),
			}).Create(&objects).Error
			if err != nil {
				return err
			}
			err = tx.Unscoped().Delete(Model(), "project_id = ? and id not in ?", objects[0]["ProjectID"], ids).Error
			if err != nil {
				return err
			}
		}

		if err := tx.Model(dsyncrecord.Model()).Create(map[string]interface{}{
			"url":             _url,
			"body":            body,
			"method":          method,
			"state":           state,
			"message":         errorMsg,
			"min_modify_date": data["minModifyDate"],
			"max_modify_date": data["maxModifyDate"],
			"created_at":      time.Now(),
		}).Error; err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

/*
func AllResourceSplit() ([]*ResourceSplit, error) {
	items := []*ResourceSplit{}
	err := DataSyncDB.db.Table(fmt.Sprintf(`(SELECT
		a.applyReason,
		s.planManMonth,
		s.planManMonth * 168 as hour,
		s.beginTime,
		s.endTime,
		a.applyUserName,
		a.pmsProjectName,
		a.workContent,
		s.userName,
		s.businessDept,
		s.departmentName,
		s.groupName,
		s.year
		FROM csbu_res_resource_split s
		LEFT JOIN csbu_res_resource_apply a ON a.id = s.applyId
		WHERE YEAR = %d) as resource_split`, time.Now().Year())).Find(&items).Error

	return items, err
}


func AllResourceSplit2() ([]*ResourceSplit2, error) {
	items := []*ResourceSplit2{}
	err := DataSyncDB.db.Table(fmt.Sprintf(`(SELECT
		s.applyId,
		a.status,
		ifnull(s.planManMonth,0) planManMonth,
		a.applyUserName,
		a.createDate,
		ifnull(a.pmsProjectName, '') pmsProjectName,
		a.targetUserGroupName,
		a.targetUserName,
		s.departmentName,
		s.groupName,
		s.userName,
		a.source,
		s.planManMonth * 168 hour,
		s.beginTime,
		s.endTime,
		ifnull(a.workContent, "") workContent,
		ifnull(a.applyReason, "") applyReason,
		IFNULL(a.verifyRemark,"") verifyRemark
		FROM csbu_res_resource_split s
		LEFT JOIN csbu_res_resource_apply a ON a.id = s.applyId
		WHERE YEAR = %d) as resource_split`, time.Now().Year())).Find(&items).Error
	return items, err
}

*/

type SplitJoinApply struct {
	ApplyID                  int64      `json:"apply_id" updated:"1"`
	UserID                   int64      `json:"user_id" updated:"1"`
	Year                     string     `json:"year" updated:"1"`
	Month                    string     `json:"month" updated:"1"`
	Hour                     float64    `json:"hour" updated:"1"`
	Percent                  float64    `json:"percent" updated:"1"`
	ProjectID                int64      `json:"project_id" updated:"1"`
	ValidDays                int        `json:"valid_days" updated:"1"`
	TargetGroupID            int64      `json:"target_group_id" updated:"1"`
	UserName                 string     `json:"user_name" updated:"1"`
	CasUserID                string     `json:"cas_user_id" updated:"1"`
	VirtualDept              string     `json:"virtual_dept" updated:"1"`
	BusinessDept             string     `json:"business_dept" updated:"1"`
	DepartmentName           string     `json:"department_name" updated:"1"`
	GroupName                string     `json:"group_name" updated:"1"`
	IsFormalEstablishment    int        `json:"is_formal_establishment" updated:"1"`
	ID                       int64      `gorm:"uniqueIndex" json:"id"`
	ApplyUserID              int64      `json:"apply_user_id" updated:"1"`
	ApplyDate                *time.Time `json:"apply_date" updated:"1"`
	ApplyReason              string     `json:"apply_reason" updated:"1"`
	Status                   string     `json:"status" updated:"1"`
	VerifyUserID             int64      `json:"verify_user_id" updated:"1"`
	VerifyDate               *time.Time `json:"verify_date" updated:"1"`
	VerifyRemark             string     `json:"verify_remark" updated:"1"`
	TargetDeptID             int64      `json:"target_dept_id" updated:"1"`
	TargetUserID             int64      `json:"target_user_id" updated:"1"`
	TargetProjectID          int64      `json:"target_project_id" updated:"1"`
	WorkContent              string     `json:"work_content" updated:"1"`
	PlanPercent              float64    `json:"plan_percent" updated:"1"`
	PlanMonth                string     `json:"plan_month" updated:"1"`
	PlanYear                 string     `json:"plan_year" updated:"1"`
	PlanManMonth             float64    `json:"plan_man_month" updated:"1"`
	BeginTime                *time.Time `json:"begin_time" updated:"1"`
	EndTime                  *time.Time `json:"end_time" updated:"1"`
	PlanHour                 float64    `json:"plan_hour" updated:"1"`
	Enable                   int        `json:"enable" updated:"1"`
	CreateDate               *time.Time `json:"create_date" updated:"1"`
	ModifyDate               *time.Time `json:"modify_date" updated:"1"`
	Source                   string     `json:"source" updated:"1"`
	ApplyUserName            string     `json:"apply_user_name" updated:"1"`
	ApplyCasUserID           string     `json:"apply_cas_user_id" updated:"1"`
	PmsProjectID             int        `json:"pms_project_id" updated:"1"`
	PmsProjectName           string     `json:"pms_project_name" updated:"1"`
	TargetUserName           string     `json:"target_user_name" updated:"1"`
	TargetCasUserID          string     `json:"target_cas_user_id" updated:"1"`
	TargetUserBusinessDept   string     `json:"target_user_business_dept" updated:"1"`
	TargetUserDepartmentName string     `json:"target_user_department_name" updated:"1"`
	TargetUserGroupName      string     `json:"target_user_group_name" updated:"1"`
	TargetDeptName           string     `json:"target_dept_name" updated:"1"`
	TargetDeptCode           string     `json:"target_dept_code" updated:"1"`
	ImportKey                string     `json:"import_key" updated:"1"`
	ImportUserID             int64      `json:"import_user_id" updated:"1"`
	ChangeReason             string     `json:"change_reason" updated:"1"`
	OneDeptCode              string     `json:"one_dept_code" updated:"1"`
	TwoDeptCode              string     `json:"two_dept_code" updated:"1"`
	ThreeDeptCode            string     `json:"three_dept_code" updated:"1"`
	FourDeptCode             string     `json:"four_dept_code" updated:"1"`
	FiveDeptCode             string     `json:"five_dept_code" updated:"1"`
	OneDeptName              string     `json:"one_dept_name" updated:"1"`
	TwoDeptName              string     `json:"two_dept_name" updated:"1"`
	ThreeDeptName            string     `json:"three_dept_name" updated:"1"`
	FourDeptName             string     `json:"four_dept_name" updated:"1"`
	FiveDeptName             string     `json:"five_dept_name" updated:"1"`
	FinalDeptCode            string     `json:"final_dept_code" updated:"1"`
}

func AllSplitJoinApply() ([]*SplitJoinApply, error) {
	var items []*SplitJoinApply
	err := easygorm.GetEasyGormDb().Table(fmt.Sprintf(`(SELECT
	a.apply_reason,
	s.plan_man_month,
	s.plan_man_month * 168 as hour,
	s.begin_time,
	s.end_time,
	a.apply_user_name,
	a.pms_project_name,
	a.work_content,
	s.user_name,
	s.business_dept,
	s.department_name,
	s.group_name
	FROM csbu_res_resource_splits s
	LEFT JOIN csbu_res_resource_applies a ON a.id = s.apply_id
	WHERE YEAR = %d) as resource_split`, time.Now().Year())).Find(&items).Error
	return items, err
}

func AllSplitJoinApply2() ([]*SplitJoinApply, error) {
	var items []*SplitJoinApply
	err := easygorm.GetEasyGormDb().Table(fmt.Sprintf(`(SELECT
	s.apply_id,
	a.status,
	ifnull(s.plan_man_month,0) plan_man_month,
	a.apply_user_name,
	a.create_date,
	ifnull(a.pms_project_name, '') pms_project_name,
	a.target_user_group_name,
	a.target_user_name,
	s.department_name,
	s.group_name,
	s.user_name,
	a.source,
	s.plan_man_month * 168 hour,
	s.begin_time,
	s.end_time,
	ifnull(a.work_content, "") work_content,
	ifnull(a.apply_reason, "") apply_reason,
	IFNULL(a.verify_remark,"") verify_remark
	FROM csbu_res_resource_splits s
	LEFT JOIN csbu_res_resource_applies a ON a.id = s.apply_id
	WHERE YEAR = %d) as resource_split`, time.Now().Year())).Find(&items).Error
	return items, err
}
