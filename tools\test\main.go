package main

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/imroc/req/v3"
	"github.com/pkg/errors"
)

var GitlabWebClient = req.C().
	SetCommonRetryCount(3).
	// Set the retry sleep interval with a commonly used algorithm: capped exponential backoff with jitter (https://aws.amazon.com/blogs/architecture/exponential-backoff-and-jitter/).
	SetCommonRetryBackoffInterval(1*time.Second, 5*time.Second).
	AddCommonRetryCondition(func(resp *req.Response, err error) bool {
		if err == nil && libs.InArrayInt([]int{200, 201, 409}, resp.StatusCode) {
			return false
		}
		return err != nil
	})

type ProjectResponse struct {
	ID                int              `json:"id"`
	SshUrlToRepo      string           `json:"ssh_url_to_repo"`
	ForkedFromProject *ProjectResponse `json:"forked_from_project"`
}

func GetForkedFromProject(repoPathID string) (string, error) {
	project := ProjectResponse{}
	url := fmt.Sprintf("%s/api/%s/projects/%s?private_token=%s", "http://10.51.135.102:8080", "v4", repoPathID, "WmQqS7_KXPRk-7XfvLhy")
	resp, err := GitlabWebClient.R().SetSuccessResult(&project).Get(url)
	if err != nil {
		return "", errors.Wrap(err, "")
	}

	if resp.IsSuccessState() {
		if project.ForkedFromProject != nil {
			return project.ForkedFromProject.SshUrlToRepo, nil
		}
		return "", nil
	}
	return "", fmt.Errorf("unkown err: %s", resp.String())
}

func GetGitlabProjectID(repo string) string {
	_repo := strings.TrimPrefix(repo, "ssh://")
	_repo = strings.TrimSuffix(_repo, ".git")
	_array := strings.Split(_repo, "/")
	return strings.Join(_array[1:], "/")
}

func CheckPersonalGit(dir string) error {
	logFile, err := os.OpenFile(filepath.Join("/tmp", "build-product"+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		logging.ErrorLogger.Errorf("create log file err ", err, "build-product")
		return err
	}

	logger := logging.NewLogger(&logging.Options{
		TimesFormat: logging.TIMESECOND,
	})

	logger.SetOutput(logFile)
	command := fmt.Sprintf("cd %s && find ./ -name .git|sed 's@/.git@@g'", dir)
	logger.Debugf("查找编译作业下所有仓库:", command)
	ret, err := libs.ExecCommand(command)
	if err != nil {
		logger.Errorf("run command error", err.Error(), command, ret)
		return err
	}
	logger.Debugf("所有仓库:", ret)

	for _, subDir := range strings.Split(ret, "\n") {
		if len(subDir) == 0 {
			continue
		}

		command := fmt.Sprintf("cd %s && cd %s && git remote -v |grep origin|grep push|grep -E 'aqyfzx.ruijie.net|10.51.135.102'|awk '{print $2}'", dir, subDir)
		logger.Debugf("查找为NTOS仓库:", command)
		subDirRet, err := libs.ExecCommand(command)
		if err != nil {
			logger.Errorf("run command error", err.Error(), command, subDirRet)
			continue
		}

		if len(strings.TrimSpace(subDirRet)) == 0 {
			continue
		}
		logger.Debugf("NTOS仓库:", subDirRet)

		repo := strings.TrimSpace(subDirRet)
		gitlabProjectPathID := GetGitlabProjectID(repo)

		// projects, err := dproject.FindByRepoPathID(gitlabProjectPathID)
		// if err != nil {
		// 	logging.ErrorLogger.Errorf("find project by repo path id err", err.Error())
		// 	continue
		// }
		// if len(projects) > 0 {
		// 	continue
		// }

		// 结果为空，git仓库非生产仓库，查找关联主仓
		forkedSshUrlToRepo, err := GetForkedFromProject(url.PathEscape(gitlabProjectPathID))
		if err != nil {
			logger.Errorf("get forked ssh url to repo error", err.Error())
			continue
		}

		if forkedSshUrlToRepo == "" {
			logger.Errorf("无关联主仓", repo)
			continue
		}

		logger.Debugf("主仓地址:", forkedSshUrlToRepo)
		command = fmt.Sprintf("cd %s && cd %s && (git remote -v |grep upstream || git remote add upstream %s) && git fetch upstream", dir, subDir, forkedSshUrlToRepo)
		logger.Debugf("增加upstream:", command)
		subDirRet, err = libs.ExecCommand(command)
		if err != nil {
			logger.Errorf("run command error", err.Error(), command, subDirRet)
			continue
		}
		logger.Debugf("增加upstream结果:", subDirRet)

		command = fmt.Sprintf("cd %s && cd %s && for i in `git log --graph --pretty=format:'%%h' --abbrev-commit|grep '^\\*'|awk '{print $NF}'|head -n 1000`;do limit=`git branch -a --contains $i|wc -l`; if [ $limit -gt 3 ]; then git branch -a --contains $i|grep upstream|tail -n 1 && break; fi; done", dir, subDir)
		logger.Debugf("查找个人仓主仓基线分支：", command)
		upstreamBranch, err := libs.ExecCommand(command)
		if err != nil {
			logger.Errorf("run command error", err.Error(), command, upstreamBranch)
			continue
		}

		upstreamBranchStr := strings.TrimSpace(upstreamBranch)
		logger.Debugf("个人仓的主仓基线分支：", upstreamBranchStr)
		command = fmt.Sprintf("cd %s && cd %s && git diff %s...`git branch|awk '{print $NF}`", dir, subDir, upstreamBranchStr)
		logger.Debugf("获取个人仓与主仓差异:", command)
		diff, err := libs.ExecCommand(command)
		if err != nil {
			logging.ErrorLogger.Errorf("run command error", err.Error(), command, diff)
			continue
		}
		logger.Debugf(diff)
	}
	return nil
}

func main() {
	CheckPersonalGit("/mnt/sata0/build-product")
	testMap := libs.KvMap[int, []int]{}
	for i := 0; i < 100; i++ {
		testMap[i/2] = append(testMap[i/2], i)
	}
	fmt.Println(testMap)
	fmt.Println(string([]byte{123, 34, 99, 111, 100, 101, 34, 58, 49, 50, 53, 52, 48, 54, 48, 44, 34, 109, 115, 103, 34, 58, 34, 84, 101, 120, 116, 70, 105, 101, 108, 100, 67, 111, 110, 118, 70, 97, 105, 108, 34, 44, 34, 101, 114, 114, 111, 114, 34, 58, 123, 34, 109, 101, 115, 115, 97, 103, 101, 34, 58, 34, 73, 110, 118, 97, 108, 105, 100, 32, 114, 101, 113, 117, 101, 115, 116, 32, 112, 97, 114, 97, 109, 101, 116, 101, 114, 58, 32, 39, 114, 101, 99, 111, 114, 100, 115, 91, 48, 93, 46, 102, 105, 101, 108, 100, 115, 46, 231, 148, 179, 232, 175, 183, 230, 151, 182, 233, 151, 180, 46, 102, 105, 101, 108, 100, 86, 97, 108, 117, 101, 46, 49, 46, 55, 52, 49, 51, 49, 48, 55, 49, 55, 101, 43, 49, 50, 46, 102, 105, 101, 108, 100, 78, 97, 109, 101, 46, 231, 148, 179, 232, 175, 183, 230, 151, 182, 233, 151, 180, 39, 46, 32, 67, 111, 114, 114, 101, 99, 116, 32, 102, 111, 114, 109, 97, 116, 32, 58, 32, 116, 104, 101, 32, 118, 97, 108, 117, 101, 32, 111, 102, 32, 39, 77, 117, 108, 116, 105, 108, 105, 110, 101, 39, 32, 109, 117, 115, 116, 32, 98, 101, 32, 97, 32, 115, 116, 114, 105, 110, 103, 46, 32, 80, 108, 101, 97, 115, 101, 32, 99, 104, 101, 99, 107, 32, 97, 110, 100, 32, 109, 111, 100, 105, 102, 121, 32, 97, 99, 99, 111, 114, 100, 105, 110, 103, 108, 121, 46, 34, 44, 34, 108, 111, 103, 95, 105, 100, 34, 58, 34, 50, 48, 50, 53, 48, 52, 48, 51, 49, 54, 50, 54, 48, 48, 67, 56, 55, 51, 54, 50, 70, 65, 49, 54, 67, 68, 53, 69, 49, 52, 68, 66, 54, 70, 34, 44, 34, 116, 114, 111, 117, 98, 108, 101, 115, 104, 111, 111, 116, 101, 114, 34, 58, 34, 230, 142, 146, 230, 159, 165, 229, 187, 186, 232, 174, 174, 230, 159, 165, 231, 156, 139, 40, 84, 114, 111, 117, 98, 108, 101, 115, 104, 111, 111, 116, 105, 110, 103, 32, 115, 117, 103, 103, 101, 115, 116, 105, 111, 110, 115, 41, 58, 32, 104, 116, 116, 112, 115, 58, 47, 47, 111, 112, 101, 110, 46, 102, 101, 105, 115, 104, 117, 46, 99, 110, 47, 115, 101, 97, 114, 99, 104, 63, 102, 114, 111, 109, 61, 111, 112, 101, 110, 97, 112, 105, 38, 108, 111, 103, 95, 105, 100, 61, 50, 48, 50, 53, 48, 52, 48, 51, 49, 54, 50, 54, 48, 48, 67, 56, 55, 51, 54, 50, 70, 65, 49, 54, 67, 68, 53, 69, 49, 52, 68, 66, 54, 70, 38, 99, 111, 100, 101, 61, 49, 50, 53, 52, 48, 54, 48, 38, 109, 101, 116, 104, 111, 100, 95, 105, 100, 61, 54, 57, 54, 53, 51, 52, 55, 50, 49, 50, 50, 56, 57, 52, 52, 49, 55, 57, 52, 34, 125, 125}))
}
