package gitlab

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/user/dgitlabtoken"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/xuri/excelize/v2"
)

func GetAllMRs(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	token := dgitlabtoken.Response{}
	err = token.FindEx("user_id", strconv.FormatUint(uint64(id), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("get token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if len(token.Token) == 0 {
		logging.ErrorLogger.Errorf("user doesn't hava token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "没有可用的gitlab token，请前往用户资料进行添加"))
		return
	}

	branch := ctx.FormValue("branch")
	scope := ctx.FormValue("scope")
	if scope == "" {
		scope = "created_by_me"
	}
	state := ctx.FormValue("state")
	if state == "" {
		state = "merged"
	}
	url := fmt.Sprintf("%s/api/%s/merge_requests?scope=%s&state=%s&private_token=%s&target_branch=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, scope, state, token.Token, branch)
	since := ctx.FormValue("since")
	until := ctx.FormValue("until")
	if since != "" {
		url = fmt.Sprintf("%s&updated_after=%s", url, since)
	}
	if until != "" {
		url = fmt.Sprintf("%s&updated_before=%s", url, until)
	}
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))

	if page > 0 {
		url = fmt.Sprintf("%s&page=%d", url, page)
	}
	if pageSize > 0 {
		url = fmt.Sprintf("%s&per_page=%d", url, pageSize)
	}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	result, err := libs.HandlerRequest(req)
	if err != nil {
		logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	for _, item := range result["items"].([]map[string]interface{}) {
		if item["title"] != nil && len([]byte(item["title"].(string))) > 512 {
			item["title"] = string([]byte(item["title"].(string)[0:512])) + "****(长度过长)"
		}
		if item["description"] != nil && len([]byte(item["description"].(string))) > 512 {
			item["description"] = string([]byte(item["description"].(string)[0:512])) + "****(长度过长)"
		}
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func GetProjectMRs(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	projectId, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	token := dgitlabtoken.Response{}
	err = token.FindEx("user_id", strconv.FormatUint(uint64(id), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("get token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if len(token.Token) == 0 {
		logging.ErrorLogger.Errorf("user doesn't hava token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "没有可用的gitlab token，请前往用户资料进行添加"))
		return
	}
	branch := ctx.FormValue("branch")
	scope := ctx.FormValue("scope")
	if scope == "" {
		scope = "created_by_me"
	}
	state := ctx.FormValue("state")
	if state == "" {
		state = "merged"
	}
	filterBy := ctx.FormValue("filter_by")
	since := ctx.FormValue("since")
	until := ctx.FormValue("until")

	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	result, err := GetMRs(token.Token, projectId, branch, scope, state, since, until, filterBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func GetMRs(token string, projectId uint, branch, scope, state, since, until, filterBy string, page, pageSize int) (map[string]interface{}, error) {
	var result map[string]interface{}
	loc, _ := time.LoadLocation("Asia/Shanghai")
	url := fmt.Sprintf("%s/api/%s/projects/%d/merge_requests?scope=%s&state=%s&private_token=%s&order_by=updated_at&target_branch=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, projectId, scope, state, token, branch)

	if since != "" {
		sinceTime, err := time.ParseInLocation("2006-01-02 15:04:05", since, loc)
		if err != nil {
			return result, err
		}
		url = fmt.Sprintf("%s&%s_after=%s", url, filterBy, sinceTime.UTC().Format("2006-01-02T15:04:05Z"))
	}

	if until != "" {
		untilTime, err := time.ParseInLocation("2006-01-02 15:04:05", until, loc)
		if err != nil {
			return result, err
		}
		url = fmt.Sprintf("%s&%s_before=%s", url, filterBy, untilTime.UTC().Format("2006-01-02T15:04:05Z"))
	}

	if page > 0 {
		url = fmt.Sprintf("%s&page=%d", url, page)
	}
	if pageSize > 0 {
		url = fmt.Sprintf("%s&per_page=%d", url, pageSize)
	}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return result, err
	}
	result, err = libs.HandlerRequest(req)
	if err != nil {
		return result, err
	}
	for _, item := range result["items"].([]map[string]interface{}) {
		if item["title"] != nil && len([]byte(item["title"].(string))) > 512 {
			item["title"] = string([]byte(item["title"].(string)[0:512])) + "****(长度过长)"
		}
		if item["description"] != nil && len([]byte(item["description"].(string))) > 512 {
			item["description"] = string([]byte(item["description"].(string)[0:512])) + "****(长度过长)"
		}
		item["created_at"] = UTC2Local("2006-01-02T15:04:05.999Z", item["created_at"].(string), loc)
		item["updated_at"] = UTC2Local("2006-01-02T15:04:05.999Z", item["updated_at"].(string), loc)
		if item["merged_at"] != nil {
			item["merged_at"] = UTC2Local("2006-01-02T15:04:05.999Z", item["merged_at"].(string), loc)
		}

	}
	return result, nil
}

func GetProjectMRNotes(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get id for get git project mr notes err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	projectId, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project id for get git project mr notes err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	mrId, err := GetMRId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get issue id for get git project mr notes err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	token := dgitlabtoken.Response{}
	err = token.FindEx("user_id", strconv.FormatUint(uint64(id), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("get token for get git project mr notes err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if len(token.Token) == 0 {
		logging.ErrorLogger.Errorf("user doesn't hava token for get git project mr notes err ", err)
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "没有可用的gitlab token，请前往用户资料进行添加"))
		return
	}
	///projects/:id/issues/:issue_iid/notes?sort=asc&order_by=updated_at
	url := fmt.Sprintf("%s/api/%s/projects/%d/merge_requests/%d/notes?private_token=%s&sort=asc&order_by=updated_at", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, projectId, mrId, token.Token)
	url = fmt.Sprintf("%s&per_page=%d", url, 100)

	var items []map[string]interface{}
	list := map[string]interface{}{}
	var page = 1
	for {
		_url := fmt.Sprintf("%s&page=%d", url, page)
		req, err := http.NewRequest("GET", _url, nil)
		if err != nil {
			logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
			continue
		}

		_result, err := libs.HandlerRequest(req)
		if err != nil {
			logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
			continue
		}
		items = append(items, _result["items"].([]map[string]interface{})...)
		list["total"] = _result["total"]
		list["limit"] = _result["limit"]
		page++
		if _result["total"].(int) <= len(items) {
			break
		}
	}
	_items := []map[string]interface{}{}
	// for _, item := range items {
	// 	switch item["type"].(type) {
	// 	case string:
	// 		if item["type"].(string) == "DiscussionNote" || item["type"].(string) == "DiffNote" {
	// 			if len([]byte(item["body"].(string))) > 512 {
	// 				item["body"] = string([]byte(item["body"].(string)[0:512])) + "****(长度过长)"
	// 			}

	// 			_items = append(_items, item)
	// 		}
	// 	}
	// }
	order := []int{}
	his := map[int]bool{}
	for i := 0; i < len(items); i++ {
		switch items[i]["position"].(type) {
		case nil:
			continue
		default:
			if _, ok := his[i]; !ok {
				his[i] = true
				order = append(order, i)
				indexs := GetNoteIndexs(items, i+1, items[i]["position"].(map[string]interface{}))
				for _, idx := range indexs {
					if _, ok := his[idx]; !ok {
						his[idx] = true
						order = append(order, idx)
					}
				}
			}
		}
	}

	for _, idx := range order {
		item := items[idx]
		switch item["type"].(type) {
		case string:
			if item["system"].(bool) == false {
				if item["type"].(string) == "DiscussionNote" || item["type"].(string) == "DiffNote" {
					if item["body"] != nil && len([]byte(item["body"].(string))) > 512 {
						item["body"] = string([]byte(item["body"].(string)[0:512])) + "****(长度过长)"
					}
					_items = append(_items, item)
				}
			}
		}
	}

	list["items"] = _items
	// req, err := http.NewRequest("GET", url, nil)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("create reuqest for get git project mr notes err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	// result, err := libs.HandlerRequest(req)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("handle reuqest for get git project mr notes err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetNoteIndexs(items []map[string]interface{}, idx int, position map[string]interface{}) []int {
	idxs := []int{}
	for i := idx; i < len(items); i++ {
		item := items[i]
		switch item["position"].(type) {
		case nil:
			continue
		default:
			if item["position"].(map[string]interface{})["new_line"] == position["new_line"] && item["position"].(map[string]interface{})["old_path"] == position["old_path"] {
				idxs = append(idxs, i)
			}
		}
	}
	return idxs
}

func GetMRId(ctx iris.Context) (uint, error) {
	id, err := ctx.Params().GetUint("mr_id")
	if err != nil {
		logging.ErrorLogger.Errorf("dao get id get err ", err)
		return 0, err
	}
	return id, nil
}

func GetGroupMRs(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	groupId, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	token := dgitlabtoken.Response{}
	err = token.FindEx("user_id", strconv.FormatUint(uint64(id), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("get token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if len(token.Token) == 0 {
		logging.ErrorLogger.Errorf("user doesn't hava token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "没有可用的gitlab token，请前往用户资料进行添加"))
		return
	}
	url := fmt.Sprintf("%s/api/%s/groups/%d/merge_requests?state=all&private_token=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, groupId, token.Token)
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	result, err := libs.HandlerRequest(req)
	if err != nil {
		logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func ExportAllMRNotes(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	projectId, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	token := dgitlabtoken.Response{}
	err = token.FindEx("user_id", strconv.FormatUint(uint64(id), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("get token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if len(token.Token) == 0 {
		logging.ErrorLogger.Errorf("user doesn't hava token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "没有可用的gitlab token，请前往用户资料进行添加"))
		return
	}
	branch := ctx.FormValue("branch")
	scope := ctx.FormValue("scope")
	if scope == "" {
		scope = "created_by_me"
	}
	state := ctx.FormValue("state")
	if state == "" {
		state = "merged"
	}
	_url := fmt.Sprintf("%s/api/%s/projects/%d/merge_requests?scope=%s&state=%s&private_token=%s&order_by=updated_at&target_branch=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, projectId, scope, state, token.Token, branch)
	_url = fmt.Sprintf("%s&per_page=%d", _url, 100)

	since := ctx.FormValue("since")
	until := ctx.FormValue("until")
	filterBy := ctx.FormValue("filter_by")
	loc, _ := time.LoadLocation("Asia/Shanghai")
	if since != "" {
		sinceTime, err := time.ParseInLocation("2006-01-02 15:04:05", since, loc)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		_url = fmt.Sprintf("%s&%s_after=%s", _url, filterBy, sinceTime.UTC().Format("2006-01-02T15:04:05Z"))
	}

	if until != "" {
		untilTime, err := time.ParseInLocation("2006-01-02 15:04:05", until, loc)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		_url = fmt.Sprintf("%s&%s_before=%s", _url, filterBy, untilTime.UTC().Format("2006-01-02T15:04:05Z"))
	}

	var items []map[string]interface{}
	list := map[string]interface{}{}
	var page = 1
	for {
		_url := fmt.Sprintf("%s&page=%d", _url, page)
		req, err := http.NewRequest("GET", _url, nil)
		if err != nil {
			logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		result, err := libs.HandlerRequest(req)
		if err != nil {
			logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}

		for _, item := range result["items"].([]map[string]interface{}) {
			mrId := int(item["iid"].(float64))
			///projects/:id/issues/:issue_iid/notes?sort=asc&order_by=updated_at
			__url := fmt.Sprintf("%s/api/%s/projects/%d/merge_requests/%d/notes?private_token=%s&sort=asc&order_by=updated_at", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, projectId, mrId, token.Token)
			__url = fmt.Sprintf("%s&per_page=%d", __url, 100)

			var _page = 1
			_items := []map[string]interface{}{}
			for {
				__url := fmt.Sprintf("%s&page=%d", __url, _page)
				req, err := http.NewRequest("GET", __url, nil)
				if err != nil {
					logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
					continue
				}
				_result, err := libs.HandlerRequest(req)
				if err != nil {
					logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
					continue
				}
				_items = append(_items, _result["items"].([]map[string]interface{})...)
				list["total"] = _result["total"]
				list["limit"] = _result["limit"]
				if _result["total"].(int) <= list["limit"].(int)*_page {
					break
				}
				_page++
			}
			__items := []map[string]interface{}{}
			order := []int{}
			his := map[int]bool{}
			for i := 0; i < len(_items); i++ {
				switch _items[i]["position"].(type) {
				case nil:
					continue
				default:
					if _, ok := his[i]; !ok {
						his[i] = true
						order = append(order, i)
						indexs := GetNoteIndexs(_items, i+1, _items[i]["position"].(map[string]interface{}))
						for _, idx := range indexs {
							if _, ok := his[idx]; !ok {
								his[idx] = true
								order = append(order, idx)
							}
						}
					}
				}
			}

			for _, idx := range order {
				item := _items[idx]
				switch item["type"].(type) {
				case string:
					if item["system"].(bool) == false {
						if item["type"].(string) == "DiscussionNote" || item["type"].(string) == "DiffNote" {
							if item["body"] != nil && len([]byte(item["body"].(string))) > 512 {
								item["body"] = string([]byte(item["body"].(string)[0:512])) + "****(长度过长)"
							}
							__items = append(__items, item)
						}
					}
				}
			}
			items = append(items, __items...)
		}
		if result["total"].(int) <= result["limit"].(int)*page {
			break
		}
		page++
	}

	list["items"] = items

	fileName := fmt.Sprintf("评审记录_%s.xlsx", time.Now().Format("20060102150405"))
	file := excelize.NewFile()
	streamWriter, err := file.NewStreamWriter("Sheet1")

	// styleID, err := file.NewStyle(&excelize.Style{Font: &excelize.Font{Color: "#777777"}})
	if err != nil {
		logging.ErrorLogger.Error(err)
	}
	header := []interface{}{"作者", "comment", "位置"}
	cell, _ := excelize.CoordinatesToCellName(1, 1)
	if err := streamWriter.SetRow(cell, header); err != nil {
		logging.ErrorLogger.Error(err)
	}
	for idx, item := range items {
		cell, _ := excelize.CoordinatesToCellName(1, 2+idx)
		position := item["position"].(map[string]interface{})["new_path"].(string)
		if item["position"].(map[string]interface{})["new_line"] != nil {
			position = fmt.Sprintf("%s:%d", position, int(item["position"].(map[string]interface{})["new_line"].(float64)))
		}

		row := []interface{}{item["author"].(map[string]interface{})["name"].(string), item["body"].(string), position}
		if err := streamWriter.SetRow(cell, row); err != nil {
			logging.ErrorLogger.Error(err)
		}
	}
	if err := streamWriter.Flush(); err != nil {
		logging.ErrorLogger.Error(err)
	}
	if err := file.SaveAs(filepath.Join("/tmp", fileName)); err != nil {
		logging.ErrorLogger.Error(err)
	}
	defer os.Remove(filepath.Join("/tmp", fileName))
	ctx.SendFile(filepath.Join("/tmp", fileName), fileName)
	return
}

func ExportAllMRs(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	projectId, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	project := ctx.FormValue("project")

	token := dgitlabtoken.Response{}
	err = token.FindEx("user_id", strconv.FormatUint(uint64(id), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("get token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if len(token.Token) == 0 {
		logging.ErrorLogger.Errorf("user doesn't hava token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "没有可用的gitlab token，请前往用户资料进行添加"))
		return
	}
	branch := ctx.FormValue("branch")
	scope := ctx.FormValue("scope")
	if scope == "" {
		scope = "created_by_me"
	}
	state := ctx.FormValue("state")
	if state == "" {
		state = "merged"
	}
	_url := fmt.Sprintf("%s/api/%s/projects/%d/merge_requests?scope=%s&state=%s&private_token=%s&order_by=updated_at&target_branch=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, projectId, scope, state, token.Token, branch)
	_url = fmt.Sprintf("%s&per_page=%d", _url, 100)

	since := ctx.FormValue("since")
	until := ctx.FormValue("until")
	filterBy := ctx.FormValue("filter_by")
	loc, _ := time.LoadLocation("Asia/Shanghai")
	if since != "" {
		sinceTime, err := time.ParseInLocation("2006-01-02 15:04:05", since, loc)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		_url = fmt.Sprintf("%s&%s_after=%s", _url, filterBy, sinceTime.UTC().Format("2006-01-02T15:04:05Z"))
	}

	if until != "" {
		untilTime, err := time.ParseInLocation("2006-01-02 15:04:05", until, loc)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		_url = fmt.Sprintf("%s&%s_before=%s", _url, filterBy, untilTime.UTC().Format("2006-01-02T15:04:05Z"))
	}

	var items []map[string]interface{}

	var page = 1
	for {
		_url := fmt.Sprintf("%s&page=%d", _url, page)
		req, err := http.NewRequest("GET", _url, nil)
		if err != nil {
			logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		result, err := libs.HandlerRequest(req)
		if err != nil {
			logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}

		for _, item := range result["items"].([]map[string]interface{}) {
			if item["title"] != nil && len([]byte(item["title"].(string))) > 512 {
				item["title"] = string([]byte(item["title"].(string)[0:512])) + "****(长度过长)"
			}
			if item["description"] != nil && len([]byte(item["description"].(string))) > 512 {
				item["description"] = string([]byte(item["description"].(string)[0:512])) + "****(长度过长)"
			}
			item["created_at"] = UTC2Local("2006-01-02T15:04:05.999Z", item["created_at"].(string), loc)
			item["updated_at"] = UTC2Local("2006-01-02T15:04:05.999Z", item["updated_at"].(string), loc)
			item["merged_at"] = UTC2Local("2006-01-02T15:04:05.999Z", item["merged_at"].(string), loc)
			items = append(items, item)
		}
		if result["total"].(int) <= result["limit"].(int)*page {
			break
		}
		page++
	}

	fileName := fmt.Sprintf("%s_%s_MR记录_%s.xlsx", project, branch, time.Now().Format("20060102150405"))
	file := excelize.NewFile()
	streamWriter, err := file.NewStreamWriter("Sheet1")

	// styleID, err := file.NewStyle(&excelize.Style{Font: &excelize.Font{Color: "#777777"}})
	if err != nil {
		logging.ErrorLogger.Error(err)
	}
	header := []interface{}{"标题", "描述", "仓库", "源分支", "目标分支", "提交人", "合并人", "创建时间", "更新时间", "合并时间", "状态"}
	cell, _ := excelize.CoordinatesToCellName(1, 1)
	if err := streamWriter.SetRow(cell, header); err != nil {
		logging.ErrorLogger.Error(err)
	}
	for idx, item := range items {
		cell, _ := excelize.CoordinatesToCellName(1, 2+idx)
		var assignee interface{}
		if item["assignee"] != nil {
			assignee = item["assignee"].(map[string]interface{})["name"]
		}
		row := []interface{}{item["title"], item["description"], strings.Split(item["references"].(map[string]interface{})["full"].(string), "!")[0], item["source_branch"], item["target_branch"], item["author"].(map[string]interface{})["name"], assignee, item["created_at"], item["updated_at"], item["merged_at"], item["state"]}
		if err := streamWriter.SetRow(cell, row); err != nil {
			logging.ErrorLogger.Error(err)
		}
	}
	if err := streamWriter.Flush(); err != nil {
		logging.ErrorLogger.Error(err)
	}
	if err := file.SaveAs(filepath.Join("/tmp", fileName)); err != nil {
		logging.ErrorLogger.Error(err)
	}
	defer os.Remove(filepath.Join("/tmp", fileName))
	ctx.SendFile(filepath.Join("/tmp", fileName), fileName)
	return
}

func UTC2Local(fmt, utcTime string, loc *time.Location) string {
	t, err := time.ParseInLocation(fmt, utcTime, loc) //t被转为本地时间的time.Time
	if err != nil {
		return utcTime
	}
	return t.Format("2006-01-02 15:04:05")
}
