package dauthtask

import (
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/license"
	"irisAdminApi/service/dao/user/duser"
	"strings"
)

const ModelName = "授权任务表"

type Response struct {
	license.LicenseAuthTask
	Username string `gorm:"-" json:"username"`
}

func (r *Response) ModelName() string {
	return ModelName
}

func Model() *license.LicenseAuthTask {
	return &license.LicenseAuthTask{}
}

func AddAuthTask(res *Response) error {
	if err := easygorm.GetEasyGormDb().Model(Model()).Create(&res.LicenseAuthTask).Error; err != nil {
		return err
	}
	return nil
}

func FindAuthTaskByDeviceSn(deviceSn string, userId uint) (*Response, error) {
	var res []*Response
	if err := easygorm.GetEasyGormDb().Model(Model()).
		Where("device_sn = ?", deviceSn).
		Where("user_id=?", userId).Find(&res).Error; err != nil {
		return nil, err
	}
	if len(res) == 0 {
		return nil, nil
	}
	return res[0], nil
}

func ListAuthTasks(page, pageSize int, deviceSn, deviceModel, materialCode string,
	authType, id, userId uint, isActive *bool, sort, orderBy, createdAt, updatedAt string) (map[string]interface{}, error) {
	var count int64
	var res []*Response

	db := easygorm.GetEasyGormDb().Model(Model())
	if id != 0 {
		db = db.Where("id =?", id)
	}
	if deviceSn != "" {
		db = db.Where("device_sn =?", deviceSn)
	}
	if deviceModel != "" {
		db = db.Where("device_model =?", deviceModel)
	}
	if materialCode != "" {
		db = db.Where("material_code =?", materialCode)
	}
	if authType != 0 {
		db = db.Where("auth_type =?", authType)
	}
	if isActive != nil {
		if *isActive {
			db = db.Where("is_active = 1")
		} else {
			db = db.Where("is_active = 0")
		}
	}
	if userId != 0 {
		db = db.Where("user_id =?", userId)
	}
	if len(createdAt) > 0 {
		array := strings.Split(createdAt, ",")
		db = db.Where("created_at between ? and ?", array[0], array[1])
	}
	if len(updatedAt) > 0 {
		array := strings.Split(updatedAt, ",")
		db = db.Where("updated_at between ? and ?", array[0], array[1])
	}
	if len(orderBy) == 0 {
		orderBy = "id"
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("count device auths get err %s", err.Error())
		return nil, err
	}
	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("list device auths get err %s", err.Error())
		return nil, err
	}
	getAuthTasksUsernames(res)
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func GetAuthTask(id, userId uint) (*Response, error) {
	var res []*Response
	db := easygorm.GetEasyGormDb().Model(Model()).
		Where("id = ?", id)
	if userId != 0 {
		db = db.Where("user_id=?", userId)
	}
	if err := db.Find(&res).Error; err != nil {
		return nil, err
	}
	if len(res) == 0 {
		return nil, nil
	}
	getAuthTaskUsername(res[0])
	return res[0], nil
}

func DeleteAuthTask(id, userId uint) error {
	res := Response{}
	res.ID = id
	db := easygorm.GetEasyGormDb().Model(Model())
	if userId != 0 {
		db = db.Where("user_id=?", userId)
	}
	if err := db.Delete(&res.LicenseAuthTask).Error; err != nil {
		return err
	} else {
		return nil
	}
}

func ListAllUnfinishedAuthTask() ([]*Response, error) {
	var res []*Response
	if err := easygorm.GetEasyGormDb().Model(Model()).
		Where("finished =?", 0).Find(&res).Error; err != nil {
		return nil, err
	} else {
		return res, nil
	}
}

func UpdateAuthTask(id uint, updates map[string]interface{}) error {
	if err := easygorm.GetEasyGormDb().Model(Model()).
		Where("id=?", id).
		Updates(updates).Error; err != nil {
		return err
	}
	return nil
}

func getAuthTaskUsername(res *Response) {
	user := &duser.User{}
	err := user.Find(res.UserID)
	if err != nil {
		return
	}

	res.Username = user.Username
}

func getAuthTasksUsernames(res []*Response) {
	for _, item := range res {
		getAuthTaskUsername(item)
	}
}
