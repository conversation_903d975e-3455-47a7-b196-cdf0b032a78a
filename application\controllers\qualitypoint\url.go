package qualitypoint

import (
	"irisAdminApi/application/middleware"

	"github.com/kataras/iris/v12"
)

var Party = func(party iris.Party) {
	party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) //登录验证
	party.Get("/category/types", GetCategoryData).Name = "获取考核大类数据"
	party.Get("/assessment/items", GetAssessmentItem).Name = "获取考核项细项表"
	party.Post("/violations", CreateViolationInst).Name = "创建事项记录"
	party.Get("/violations", GetViolationProcInsts).Name = "获取事项列表"
	party.Get("/violations/releases/{id:uint}", GetProcInst).Name = "获取事项详情"
	party.Get("/violations/{id:uint}/tasks", GetViolationProcInstTasks).Name = "获取进展详情"
	party.Get("/procinst/{id:uint}/prevnodes", GetProcPrevNodes).Name = "获取事项流程"
	party.Get("/violations/myAudit", GetMyAuditSubmissions).Name = "我审计的事项提报"
	party.Get("/violations/mySubmission", GetMySubmissions).Name = "我提报的事项"
	party.Get("/violations/myResponsibleSubmission", GetMyResponsibleSubmissions).Name = "我负责的事项"
	party.Get("/violations/myDepartmentSubmission", GetMyDepartmentSubmissions).Name = "我部门的事项"
	party.Get("/violations/commonIssuesLibrary", GetCommonIssueSubmissions).Name = "共性问题库"
	party.Post("/violations/myAudit/update", UpdateViolation).Name = "更新审计的事项"
	party.Post("/violations/discard/{id:uint}", DiscardSubmission).Name = "废弃事项记录"
	party.Get("/violations/abstractCommon", GetAbstractCommonProcTasks).Name = "抽象共性问题列表"
	party.Post("/violations/releases/abstractCommon/{id:uint}", UpdateAbstractCommonProcInst).Name = "提交抽象共性问题"
	party.Get("/violations/commonReview", GetCommonReviewProcTasks).Name = "共性问题评审列表"
	party.Post("/violations/releases/commonReview/{id:uint}", UpdateAbstractCommonProcInst).Name = "提交共性问题评审结果"
	party.Get("/violations/deductPlan", GetDeductPlanProcTasks).Name = "消分任务事项制定列表"
	party.Get("/violations/deductReview", GetDeductReviewProcTasks).Name = "消分任务事项评审列表"
	party.Get("/violations/deductEvidence", GetDeductEvidenceProcTasks).Name = "消分任务佐证列表"
	party.Get("/violations/deductExecutionReview", GetDeductExecutionReviewProcTasks).Name = "消分任务执行评审"
	party.Get("/violations/{id:uint}/evidenceMaterial", DownloadEvidenceMaterial).Name = "下载佐证材料文件"
	party.Get("/violations/repeatIssueCheck", GetRepeatIssueCheckProcTasks).Name = "复发问题判断列表"
	party.Get("/violations/adminClosure", GetAdminClosureProcTasks).Name = "积分管理员数据列表"
	party.Post("/violations/deductEvidence/{id:uint}", UpdateAbstractCommonProcInst).Name = "提交佐证材料"
	party.Get("/dashboard/getUserPointInfo", GetUserPointInfo).Name = "获取用户积分信息"
	party.Get("/dashboard/getViolationProcTasks", GetViolationProcTasks).Name = "获取用户待处理任务"
	party.Get("/dashboard/getUserPointsRecoverieDetail", GetUserPointsRecoverieDetail).Name = "获取用户消分卡列表数据信息"
	party.Get("/dashboard/getUserPointsDeductionDetail", GetUserPointsDeductionDetail).Name = "获取用户扣分列表数据信息"

	party.Get("/crossChecks", GetCrossCheckProcInsts).Name = "获取拉通排查列表"
	party.Get("/crossChecks/releases/{id:uint}", GetCrossCheckProcInstDetail).Name = "获取拉通排查详情"
	party.Get("/crossChecks/{id:uint}/tasks", GetCrosscheckProcInstTasks).Name = "获取拉通排查进展详情"
	party.Get("/crossChecks/procinst/{id:uint}/prevnodes", GetCrossCheckProcPrevNodes).Name = "获取拉通排查流程"
	party.Post("/crossChecks/releases/{id:uint}", UpdateCrossCheckProcInst).Name = "提交拉通排查"
}
