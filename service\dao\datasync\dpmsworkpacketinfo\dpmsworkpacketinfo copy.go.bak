package dpmsworkpacketinfo

import (
	"encoding/json"
	"fmt"
	"reflect"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/datasync"
	"irisAdminApi/service/dao/datasync/dsyncrecord"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "PMS用例信息表"

type PmsWorkpacketInfoSyncResponse struct {
	State   string                       `json:"state"`
	Data    []*PmsWorkpacketInfoResponse `json:"data"`
	Total   int                          `json:"total"`
	Message string                       `json:"message"`
}

type PmsWorkpacketInfoResponse struct {
	RowNum       int `json:"rownum"`
	WorkPacketID int `gorm:"not null; index:idx_unique, unique" json:"workPacketId" `
	RelationID   int `gorm:"not null; index:idx_unique, unique" json:"relationId" `

	WorkPacketName    string `gorm:"not null; type:varchar(100)" json:"workPacketName" update:"1"`
	ProjectID         int    `gorm:"not null" json:"projectId" update:"1"`
	ProjectName       string `gorm:"not null; type:varchar(100)" json:"projectName" update:"1" update:"1"`
	ProjectChangeType string `gorm:"not null; type:varchar(100)" json:"projectChangeType" update:"1"`
	Disabled          bool   `gorm:"not null" json:"disabled" update:"1"`

	RequestID          int     `gorm:"not null" json:"requestId" update:"1"`
	AddCodes           float32 `gorm:"not null" json:"addCodes" update:"1"`
	TotalCodes         float32 `gorm:"not null" json:"totalCodes" update:"1"`
	TransplantCodes    float32 `gorm:"not null" json:"transplantCodes" update:"1"`
	TransplantCodesTmp float32 `gorm:"not null" json:"transplantCodesTmp" update:"1"`
	GroupName          string  `gorm:"not null" json:"groupName" update:"1"`
	PacketManagerName  string  `gorm:"not null" json:"packetManagerName" update:"1"`
	FinishPercent      *int    `json:"finish_percent" update:"1"`

	ChangeTypeName    string  `gorm:"not null" json:"changeTypeName"`
	RelationSituation string  `json:"relationSituation"`
	PstlName          string  `json:"pstlName" `
	PlanStartDate     string  `gorm:"not null; type:varchar(100)" json:"planStartDate"`
	PlanEndDate       string  `gorm:"not null; type:varchar(100)" json:"planEndDate"`
	PlanTime          float32 `json:"planTime"`
	ActStartDate      string  `gorm:"not null; type:varchar(100)" json:"actStartDate"`
	ActEndDate        string  `gorm:"not null; type:varchar(100)" json:"actEndDate"`
	UseTime           float32 `json:"useTime"`
	KeyWord           string  `json:"keyWord"`
	DeliverSituation  string  `json:"deliverSituation"`
	StageName         string  `json:"stageName"`
}

type PmsRequest struct {
	RequestID     int    `gorm:"primarykey; autoIncrement:false" json:"request_id" `
	ProjectID     int    `gorm:"not null;" json:"project_id" update:"1"`
	ProjectName   string `gorm:"not null; type:varchar(100)" json:"project_name" update:"1"`
	RequestName   string `gorm:"not null; type:varchar(100)" json:"request_name" update:"1"`
	RequestStatus string `gorm:"not null; type:varchar(100)" json:"request_status" update:"1"`
	Disabled      bool   `json:"disabled" update:"1"`
}

type PmsWorkpacketInfo struct {
	datasync.PmsWorkpacketInfo
}

type ListResponse struct {
	PmsWorkpacketInfo
	PmsRequest PmsRequest `gorm:"foreignKey:RequestID; references:RequestID" json:"pms_request"`
}

type PmsWorkpacketInfoSummary struct {
	ProjectName       string  `json:"project_name"`
	PacketManagerName string  `json:"packet_manager_name"`
	WorkPacketName    string  `json:"work_packet_name"`
	TotalCodes        float32 `json:"total_codes"`
	FinishPercent     int     `json:"finish_percent"`
}

type Request struct {
	Id uint `json:"id"`
}

func (this *PmsWorkpacketInfo) ModelName() string {
	return ModelName
}

func Model() *datasync.PmsWorkpacketInfo {
	return &datasync.PmsWorkpacketInfo{}
}

func (this *PmsWorkpacketInfo) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *PmsWorkpacketInfo) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *PmsWorkpacketInfo) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *PmsWorkpacketInfo) CreateV2(object interface{}) error {
	return nil
}

func (this *PmsWorkpacketInfo) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *PmsWorkpacketInfo) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *PmsWorkpacketInfo) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *PmsWorkpacketInfo) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *PmsWorkpacketInfo) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *PmsWorkpacketInfo) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindAllEnabledByProjectName(projectName string) ([]*ListResponse, error) {
	items := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("PmsRequest").Where("project_name = ? and disabled = 0", projectName).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return items, err
	}
	return items, nil
}

func DeleteAll() error {
	err := easygorm.GetEasyGormDb().Unscoped().Where("1 = 1").Delete(Model()).Error
	if err != nil {
		return err
	}
	return nil
}

func UpdateOrCreatePmsWorkpacketInfoTransaction(items []*PmsWorkpacketInfoResponse, _url string, data map[string]string, method, state, errorMsg string) error {
	objects := []map[string]interface{}{}
	for _, item := range items {
		object := map[string]interface{}{
			"WorkPacketID":   item.WorkPacketID,
			"RelationID":     item.RelationID,
			"WorkPacketName": item.WorkPacketName,

			"ProjectID":          item.ProjectID,
			"ProjectName":        item.ProjectName,
			"ProjectChangeType":  item.ProjectChangeType,
			"RequestID":          item.RequestID,
			"Disabled":           item.Disabled,
			"AddCodes":           item.AddCodes,
			"TotalCodes":         item.TotalCodes,
			"TransplantCodes":    item.TransplantCodes,
			"TransplantCodesTmp": item.TransplantCodesTmp,
			"GroupName":          item.GroupName,
			"PacketManagerName":  item.PacketManagerName,
			"FinishPercent":      item.FinishPercent,
		}
		if item.ChangeTypeName != "" {
			object["ChangeTypeName"] = item.ChangeTypeName
		}
		if item.RelationSituation != "" {
			object["RelationSituation"] = item.RelationSituation
		}
		if item.PstlName != "" {
			object["PstlName"] = item.PstlName
		}
		if item.PlanTime > 0 {
			object["PlanTime"] = &item.PlanTime
		}
		if item.UseTime > 0 {
			object["UseTime"] = &item.UseTime
		}
		if len(item.PlanStartDate) > 0 {
			object["PlanStartDate"] = item.PlanStartDate
		}
		if len(item.PlanEndDate) > 0 {
			object["PlanEndDate"] = item.PlanEndDate
		}
		if len(item.ActStartDate) > 0 {
			object["ActStartDate"] = item.ActStartDate
		}
		if len(item.ActEndDate) > 0 {
			object["ActEndDate"] = item.ActEndDate
		}
		if item.KeyWord != "" {
			object["KeyWord"] = item.KeyWord
		}
		if item.DeliverSituation != "" {
			object["DeliverSituation"] = item.DeliverSituation
		}
		if item.StageName != "" {
			object["StageName"] = item.StageName
		}
		objects = append(objects, object)
	}

	columns := []string{}

	xt := reflect.TypeOf(Model())
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")
		if ok && ok2 {
			columns = append(columns, name)
		}
	}

	body, err := json.Marshal(data)
	if err != nil {
		return err
	}
	db := easygorm.GetEasyGormDb()
	err = db.Transaction(func(tx *gorm.DB) error {
		if len(objects) > 0 {
			err = tx.Model(Model()).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "work_packet_id"}, {Name: "relation_id"}},
				DoUpdates: clause.AssignmentColumns(columns),
			}).Create(&objects).Error
			if err != nil {
				return err
			}
		}

		if err := tx.Model(dsyncrecord.Model()).Create(map[string]interface{}{
			"url":             _url,
			"body":            body,
			"method":          method,
			"state":           state,
			"message":         errorMsg,
			"min_modify_date": data["minModifyDate"],
			"max_modify_date": data["maxModifyDate"],
			"created_at":      time.Now(),
		}).Error; err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func GetPmsWorkpacketInfoSummary() ([]*PmsWorkpacketInfoSummary, error) {
	items := []*PmsWorkpacketInfoSummary{}
	db := easygorm.GetEasyGormDb().
		Table("pms_workpacket_infos pwi")

	where := easygorm.GetEasyGormDb().Where("packet_manager_name != '' and disabled != 1 AND project_change_type != 'CHANGE_CANCEL'")
	err := db.Where(where).Find(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

type WorkPacketInfo struct {
	WorkPacketID         int     `json:"work_packet_id"`
	WorkPacketName       string  `json:"work_packet_name"`
	ProjectName          string  `json:"project_name"`
	AddCodes             float64 `json:"add_codes"`
	TotalCodes           float64 `json:"total_codes"`
	PortedCodes          float64 `json:"ported_codes"`
	TemporaryPortedCodes float64 `json:"temporary_ported_codes"`
	PacketManagerName    string  `json:"packet_manager_name"`
	FinishPercent        int     `json:"finish_percent"`
	GroupName            string  `json:"group_name"`
	ChangeTypeName       string  `json:"changeTypeName"`
	RelationSituation    string  `json:"relationSituation"`
	PstlName             string  `json:"pstlName" `
	PlanStartDate        string  `json:"planStartDate"`
	PlanEndDate          string  `json:"planEndDate"`
	PlanTime             float32 `json:"planTime"`
	ActStartDate         string  `json:"actStartDate"`
	ActEndDate           string  `json:"actEndDate"`
	UseTime              float32 `json:"useTime"`
	KeyWord              string  `json:"keyWord"`
	DeliverSituation     string  `json:"deliverSituation"`
	StageName            string  `json:"stageName"`
}

func GetWorkPacketInfoData(page int, pageSize int) ([]*WorkPacketInfo, error) {
	items := []*WorkPacketInfo{}
	offset := (page - 1) * pageSize
	err := easygorm.GetEasyGormDb().Table("pms_workpacket_infos wp").
		Select("wp.work_packet_id, wp.work_packet_name, wp.project_name, ROUND(SUM(wp.add_codes), 2) as add_codes, ROUND(SUM(wp.total_codes), 2) as total_codes, ROUND(SUM(wp.transplant_codes), 2) as ported_codes, ROUND(SUM(wp.transplant_codes_tmp), 2) as temporary_ported_codes, wp.packet_manager_name,wp.group_name,finish_percent,change_type_name,relation_situation,pstl_name,plan_start_date,plan_end_date,plan_time,act_start_date,act_end_date,use_time,key_word,deliver_situation,stage_name").
		Where("wp.disabled = ?", 0).Where("wp.project_change_type != ?", "CHANGE_CANCEL").
		Group("wp.work_packet_id, wp.work_packet_name, wp.project_name,wp.packet_manager_name,wp.group_name,finish_percent,change_type_name,relation_situation,pstl_name,plan_start_date,plan_end_date,plan_time,act_start_date,act_end_date,use_time,key_word,deliver_situation,stage_name").
		Offset(offset).
		Limit(pageSize).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return items, err
	}
	return items, nil
}
