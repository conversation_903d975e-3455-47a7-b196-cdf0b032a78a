package dfirewallflextrans

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/firewallflextrans"
	"path/filepath"
)

const ModelName = "配置转换作业表"

type User struct {
	ID       uint   `json:"id"`
	Name     string `json:"name"`
	Username string `json:"username"`
}

type FirewallFlexTran struct {
	firewallflextrans.FirewallFlexTran
	User *User `json:"user"`
}

type ListResponse struct {
	FirewallFlexTran
}

type Request struct {
	OutputFileName string `json:"target_name" form:"output_file_name" validate:"required"`
	WithLib        string `json:"with_lib" form:"with_lib" validate:"required"`
}

func (this *FirewallFlexTran) ModelName() string {
	return ModelName
}

func Model() *firewallflextrans.FirewallFlexTran {
	return &firewallflextrans.FirewallFlexTran{}
}

func (this *FirewallFlexTran) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Where("app = ?", libs.Config.Sig.App)
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *FirewallFlexTran) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Where("app = ?", libs.Config.Sig.App)
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *FirewallFlexTran) Create(object map[string]interface{}) error {
	object["app"] = libs.Config.Sig.App
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *FirewallFlexTran) CreateV2(object interface{}) error {
	return nil
}

func (this *FirewallFlexTran) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("app = ?", libs.Config.Sig.App).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *FirewallFlexTran) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("app = ?", libs.Config.Sig.App).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *FirewallFlexTran) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("app = ?", libs.Config.Sig.App).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *FirewallFlexTran) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Where("app = ?", libs.Config.Sig.App).Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *FirewallFlexTran) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Where("app = ?", libs.Config.Sig.App).Where("id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *FirewallFlexTran) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("app = ?", libs.Config.Sig.App).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func All(userID, job_id, name, md5, start, end, status, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Where("app = ?", libs.Config.Sig.App)
	if len(userID) > 0 && userID != "0" {
		db = db.Where("user_id = ?", userID)
	}

	if len(job_id) > 0 {
		db = db.Where("job_id = ? ", job_id)
	}

	if len(name) > 0 {
		db = db.Where("input_file_name like ? or output_file_name like ?", fmt.Sprintf("%%%s%%", name), fmt.Sprintf("%%%s%%", name))
	}

	if len(md5) > 0 {
		db = db.Where("input_md5 like ? or output_md5 like ?", fmt.Sprintf("%%%s%%", md5), fmt.Sprintf("%%%s%%", md5))
	}

	if len(start) > 0 {
		db = db.Where("created_at >= ?", start+" 00:00:00.000")
	}

	if len(end) > 0 {
		db = db.Where("created_at <= ?", end+" 23:59:59.999")
	}

	if len(status) > 0 {
		db = db.Where("status = ?", status)
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *FirewallFlexTran) Dir() string {
	return filepath.Join(libs.Config.Sig.Upload, this.CreatedAt.Format("20060102"), this.JobID)
}
