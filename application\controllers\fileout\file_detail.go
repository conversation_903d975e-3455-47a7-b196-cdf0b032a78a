package fileout

import (
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/fileout/dfiledetail"
	"irisAdminApi/service/dao/fileout/dfilestruction"

	"github.com/kataras/iris/v12"
)

func GetAllFileDetails(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get approval id err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	details, err := dfiledetail.FindDetailByApprovalId(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, details, response.NoErr.Msg))
	return
}

func GetAllFileDetailsV2(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get approval id err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	details, err := dfilestruction.FindDetailByApprovalId(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, details, response.NoErr.Msg))
	return
}
