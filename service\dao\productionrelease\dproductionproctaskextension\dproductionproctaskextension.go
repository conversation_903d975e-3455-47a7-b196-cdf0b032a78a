package dproductionproctaskextension

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/productionrelease"
)

const ModelName = "Setmac扩展项表"

type Response struct {
	productionrelease.ProductionProcTaskExtension
}

type ListResponse struct {
	Response
}

type Request struct {
	productionrelease.ProductionProcTaskExtension
}

type ToolsList struct {
	ExtensionTool string `json:"extension_tool"`
	MD5           string `json:"md5"`
}

type ExtensionGroup struct {
	TaskID        uint        `json:"task_id" form:"task_id"`
	ExtensionName string      `json:"extension_name" form:"extension_name"`
	ToolsList     []ToolsList `json:"toolsLists"` // 包含所有相关的ExtensionTool和MD5
	ExtensionDoc  string      `json:"extension_doc"`
	ProcInstID    uint        ` json:"proc_inst_id" form:"proc_inst_id"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *productionrelease.ProductionProcTaskExtension {
	return &productionrelease.ProductionProcTaskExtension{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func All() ([]*ListResponse, error) {
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	err := db.Find(&items).Error
	return items, err
}

func FindInTaskIds(taskIDs []uint) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("task_id in ? ", taskIDs).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	return items, nil
}

func FindByProcinstID(procinstID, taskID uint) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("proc_inst_id = ? and task_id=?", procinstID, taskID).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	return items, nil
}

func FindInTaskIdsV2(taskIDs []uint) ([]*ExtensionGroup, error) {
	var responses []*Response
	err := easygorm.GetEasyGormDb().Model(Model()).Where("task_id in ? ", taskIDs).Find(&responses).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err: %v", err)
		return nil, err
	}

	// 使用map来分组ExtensionName
	groups := make(map[string]*ExtensionGroup)
	for _, resp := range responses {
		// 如果组还不存在，则创建一个新的
		if group, ok := groups[resp.ExtensionName]; !ok {
			groups[resp.ExtensionName] = &ExtensionGroup{
				TaskID:        resp.TaskID,
				ExtensionName: resp.ExtensionName,
				ToolsList: []ToolsList{
					{ExtensionTool: resp.ExtensionTool, MD5: resp.MD5},
				},
				ExtensionDoc: resp.ExtensionDoc,
				ProcInstID:   resp.ProcInstID,
			}
		} else {
			// 如果组已存在，则将ToolsList添加到列表中
			group.ToolsList = append(group.ToolsList, ToolsList{ExtensionTool: resp.ExtensionTool, MD5: resp.MD5})
		}
	}

	// 将map转换为切片
	var groupedExtensions []*ExtensionGroup
	for _, group := range groups {
		groupedExtensions = append(groupedExtensions, group)
	}

	return groupedExtensions, nil
}
