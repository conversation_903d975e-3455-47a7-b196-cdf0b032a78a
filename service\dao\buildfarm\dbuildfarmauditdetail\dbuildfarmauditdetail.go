package dbuildfarmauditdetail

import (
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/buildfarm"
	"strings"

	"github.com/pkg/errors"
)

const ModelName = "编译农场个人仓编译评审详情表"

type User struct {
	ID       uint   `json:"id"`
	Name     string `json:"name"`
	Username string `json:"username"`
}

type BuildfarmAuditDetail struct {
	buildfarm.BuildfarmAuditDetail
	Auditor *User `gorm:"->;foreignKey:AuditorID;references:ID" json:"auditor"`
	User    *User `gorm:"->;foreignKey:UserID;references:ID" json:"user"`
}

type ListResponse struct {
	BuildfarmAuditDetail
}

type Request struct {
}

func (this *BuildfarmAuditDetail) ModelName() string {
	return ModelName
}

func Model() *buildfarm.BuildfarmAuditDetail {
	return &buildfarm.BuildfarmAuditDetail{}
}

func (this *BuildfarmAuditDetail) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *BuildfarmAuditDetail) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *BuildfarmAuditDetail) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *BuildfarmAuditDetail) CreateV2(object interface{}) error {
	return nil
}

func (this *BuildfarmAuditDetail) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmAuditDetail) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmAuditDetail) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmAuditDetail) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmAuditDetail) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Where("bug_id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmAuditDetail) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindTaskAudit(task_id string) ([]*ListResponse, error) {
	items := []*ListResponse{}
	// paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)

	db := easygorm.GetEasyGormDb().Model(Model()).Where("task_id = ?", task_id)
	// if len(level) > 0 {
	// 	db = db.Where("level in ?", strings.Split(level, ","))
	// }
	// db = db.Where("level >= 3")
	err := db.Preload("User").Preload("Auditor").Find(&items).Error
	if err != nil {
		return nil, errors.Wrap(err, "")
	}
	return items, nil
}

func FindTaskAuditByJobID(job_id, level string) ([]*ListResponse, error) {
	items := []*ListResponse{}
	// paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)

	db := easygorm.GetEasyGormDb().Model(Model()).Where("job_id = ?", job_id)
	if len(level) > 0 {
		db = db.Where("level in ?", strings.Split(level, ","))
	}
	err := db.Preload("User").Preload("Auditor").Find(&items).Error
	if err != nil {
		return nil, errors.Wrap(err, "")
	}
	return items, nil
}

func FindAuditByjobIDAndTaskIDAndRepo(jobID, taskID, repo string) ([]*ListResponse, error) {
	items := []*ListResponse{}
	// paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err := easygorm.GetEasyGormDb().Model(Model()).Where("job_id = ? and task_id = ? and current_repo = ?", jobID, taskID, repo).Order("id desc").Find(&items).Error
	return items, err
}
