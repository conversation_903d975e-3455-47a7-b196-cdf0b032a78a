package resourcepool

import (
	"encoding/json"
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/resourcepool/dresourcepooljob"
	"irisAdminApi/service/dao/resourcepool/dresourcepoolresource"
	"irisAdminApi/service/dao/user/duser"
	"net"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/kataras/iris/v12"
)

func GetRemoteAddr(ctx iris.Context) string {
	addr := ctx.GetHeader("X-Real-Ip")
	if len(addr) == 0 {
		addr := strings.TrimSpace(ctx.Request().RemoteAddr)
		if addr != "" {
			// if addr has port use the net.SplitHostPort otherwise(error occurs) take as it is
			if ip, _, err := net.SplitHostPort(addr); err == nil {
				return ip
			}
		}
	} else {
		if ip, _, err := net.SplitHostPort(addr); err == nil {
			return ip
		}
	}
	return addr
}

func GetResources(ctx iris.Context) {

	uId, _ := dao.GetAuthId(ctx)
	name := ctx.FormValue("search")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	make := ctx.FormValue("make")
	testbed := ctx.FormValue("testbed")

	var list map[string]interface{}
	var err error

	isAdmin := isAdminUser(uId)
	// if isAdmin {
	// 	list, err = dresourcepoolresource.All_admin(uId, name, sort, orderBy, page, pageSize)
	// } else {
	// 	list, err = dresourcepoolresource.GetAllMyResources(uId, false, name, sort, orderBy, page, pageSize)
	// }
	if isAdmin {
		list, err = dresourcepoolresource.All_admin_with_type(uId, name, sort, orderBy, page, pageSize, make, testbed)
	} else {
		list, err = dresourcepoolresource.GetAllMyResources_type(uId, name, sort, orderBy, page, pageSize, make, testbed)
	}

	// fmt.Println(list, err)

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		// ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetResourcesRuiJie(ctx iris.Context) {

	uId, _ := dao.GetAuthId(ctx)
	name := ctx.FormValue("search")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	testbed := ctx.FormValue("testbed")

	var list map[string]interface{}
	var err error

	isAdmin := isAdminUser(uId)
	if isAdmin {
		list, err = dresourcepoolresource.All_admin_with_type(uId, name, sort, orderBy, page, pageSize, "ruijie", testbed)
	} else {
		list, err = dresourcepoolresource.GetAllMyResources_type(uId, name, sort, orderBy, page, pageSize, "ruijie", testbed)
	}

	// fmt.Println(list, err)

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		// ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetResourcesRuiYi(ctx iris.Context) {

	uId, _ := dao.GetAuthId(ctx)
	name := ctx.FormValue("search")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	testbed := ctx.FormValue("testbed")

	var list map[string]interface{}
	var err error

	isAdmin := isAdminUser(uId)
	if isAdmin {
		list, err = dresourcepoolresource.All_admin_with_type(uId, name, sort, orderBy, page, pageSize, "ruijie", testbed)
	} else {
		list, err = dresourcepoolresource.GetAllMyResources_type(uId, name, sort, orderBy, page, pageSize, "ruijie", testbed)
	}

	// fmt.Println(list, err)

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		// ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetResourcesFriend(ctx iris.Context) {

	uId, _ := dao.GetAuthId(ctx)
	name := ctx.FormValue("search")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	testbed := ctx.FormValue("testbed")

	var list map[string]interface{}
	var err error

	isAdmin := isAdminUser(uId)
	if isAdmin {
		list, err = dresourcepoolresource.All_admin_with_type(uId, name, sort, orderBy, page, pageSize, "", testbed)
	} else {
		list, err = dresourcepoolresource.GetAllMyResources_type(uId, name, sort, orderBy, page, pageSize, "", testbed)
	}

	// fmt.Println(list, err)

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		// ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetMyResources(ctx iris.Context) {

	uId, _ := dao.GetAuthId(ctx)
	name := ctx.FormValue("search")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	testbed := ctx.FormValue("testbed")

	// fmt.Println(uId)
	list, err := dresourcepoolresource.GetAllMyResources(uId, true, name, sort, orderBy, page, pageSize, testbed)

	// fmt.Println(list, err)

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		// ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetAllResourcesName(ctx iris.Context) {

	list, err := dresourcepoolresource.AllName()

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetAllUsers(ctx iris.Context) {

	list, err := dresourcepoolresource.AllUsers()

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetAllMyResources(ctx iris.Context) {

	uId, _ := dao.GetAuthId(ctx)
	name := ctx.FormValue("search")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	testbed := ctx.FormValue("testbed")

	list, err := dresourcepoolresource.All(uId, name, sort, orderBy, page, pageSize, testbed)

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetResource(ctx iris.Context) {
	info := dresourcepoolresource.ResourcePoolResource{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get resource get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

func CreateResource(ctx iris.Context) {
	userId, _ := dao.GetAuthId(ctx)
	request := dresourcepoolresource.Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		fmt.Print(err)
		logging.ErrorLogger.Errorf("create resource read json err %v", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, "create resource read json err %v", response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(&request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {

		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	user := duser.User{}
	err := user.Find(userId)
	if err != nil {
		logging.ErrorLogger.Errorf("create resource get by resource id err %v", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	_, err = dresourcepoolresource.CreateResource(request)

	// err = dao.Create(&dresourcepoolresource.ResourcePoolResource{}, ctx, map[string]interface{}{
	// 	"CreatedAt":  time.Now(),
	// 	"UpdatedAt":  time.Now(),
	// 	"Name":       request.Name,
	// 	"Domain":     request.Domain,
	// 	"model":      request.Model1,
	// 	"Make":       request.Make,
	// 	"Osname":     request.Osname,
	// 	"MgtIp":      request.MgtIp,
	// 	"MgtPort":    request.MgtPort,
	// 	"ConIp":      request.ConIp,
	// 	"ConPort":    request.ConPort,
	// 	"Testbed":    request.Testbed,
	// 	"Type":       request.Type,
	// 	"Status":     request.Status,
	// 	"Rack":       request.Rack,
	// 	"Slot":       request.Slot,
	// 	"Reserved":   request.Reserved,
	// 	"StartTime":  request.StartTime,
	// 	"EndTime":    request.EndTime,
	// 	"SN":         request.SN,
	// 	"ResourceId": request.ResourceId,
	// 	"Location":   request.Location,
	// 	"Room":       request.Room,
	// 	"Comments":   request.Comments,
	// })
	if err != nil {
		logging.ErrorLogger.Errorf("create resource err", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, request, response.NoErr.Msg))
	return
}

func UpdateResource(ctx iris.Context) {
	_, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("failed to get auth id from dao: %v", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	request := dresourcepoolresource.Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("failed to read JSON request: %v", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(&request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	err = dao.Update(&dresourcepoolresource.ResourcePoolResource{}, ctx, map[string]interface{}{
		"UpdatedAt":  time.Now(),
		"Name":       request.Name,
		"Domain":     request.Domain,
		"model":      request.Model,
		"Make":       request.Make,
		"Osname":     request.Osname,
		"MgtIp":      request.MgtIp,
		"MgtPort":    request.MgtPort,
		"ConIp":      request.ConIp,
		"ConPort":    request.ConPort,
		"Testbed":    request.Testbed,
		"Type":       request.Type,
		"Status":     request.Status,
		"Rack":       request.Rack,
		"Slot":       request.Slot,
		"Reserved":   request.Reserved,
		"StartTime":  request.StartTime,
		"EndTime":    request.EndTime,
		"SN":         request.SN,
		"ResourceId": request.ResourceId,
		"Location":   request.Location,
		"Room":       request.Room,
		"Comments":   request.Comments,
	})

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

type ReservedName struct {
	Reserved string `json:"reserved"`
}

func GetNewResource(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	resource := dresourcepoolresource.ResourcePoolResource{}
	err = resource.Find(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	username, err := dresourcepoolresource.GetNowUserName(uId)

	// 判断 Reserved 字段是否为空
	if resource.Reserved == "" {
	} else if resource.Reserved == username {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "资源无需占用"))
		return
	} else {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "资源已被他人占用"))
		return
	}

	err = resource.Update(id, map[string]interface{}{
		"Reserved":  username,
		"StartTime": time.Now(),
		"EndTime":   time.Now().AddDate(0, 0, 1),
	})

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ip := GetRemoteAddr(ctx)
	go func() {
		_, _ = libs.ExecCommand(fmt.Sprintf("python3 /home/<USER>/getresource.py %s %v %s %s %s", resource.ConIp, resource.ConPort, ip, resource.MgtIp, ip))
	}()
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

type ListRequest struct {
	IDs      []uint `json:"ids"`
	Reserved string `json:"reserved"`
}

func GetNewResources(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	username, err := dresourcepoolresource.GetNowUserName(uId)
	request := ListRequest{}
	if err := ctx.ReadJSON(&request); err != nil {
		fmt.Print(err)
		logging.ErrorLogger.Errorf("create resource read json err %v", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, "create resource read json err %v", response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(&request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	resources, err := dresourcepoolresource.FindResourcesInIDs(request.IDs)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	updateObjects := []map[string]interface{}{}
	for _, resource := range resources {
		// 判断 Reserved 字段是否为空
		if resource.Reserved == "" {
		} else if resource.Reserved == username {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "资源无需占用"))
			return
		} else {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "资源已被他人占用"))
			return
		}
		updateObjects = append(updateObjects, map[string]interface{}{
			"ID":        resource.ID,
			"Name":      resource.Name,
			"Reserved":  username,
			"StartTime": time.Now(),
			"EndTime":   time.Now().AddDate(0, 0, 1),
		})
	}

	if len(updateObjects) != len(request.IDs) {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}

	err = dresourcepoolresource.BatchUpdateReserved(updateObjects)

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	// ip := GetRemoteAddr(ctx)
	// go func() {
	// 	_, _ = libs.ExecCommand(fmt.Sprintf("python3 /home/<USER>/getresource.py %s %v %s %s %s", resource.ConIp, resource.ConPort, ip, resource.MgtIp, ip))
	// }()
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func UpdateResourceFlag(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}

	request := StatusRequest{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("create approval read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(&request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	resource := dresourcepoolresource.ResourcePoolResource{}
	err = resource.Find(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	err = dao.Update(&dresourcepoolresource.ResourcePoolResource{}, ctx, map[string]interface{}{
		"UpdatedAt": time.Now(),
		"Flag":      request.Flag,
		"UserID":    uId,
	})

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func DeleteResource(ctx iris.Context) {

	err := dao.Delete(&dresourcepoolresource.ResourcePoolResource{}, ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func ReleaseResource(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	resource := dresourcepoolresource.ResourcePoolResource{}
	err = resource.Find(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	username, err := dresourcepoolresource.GetNowUserName(uId)
	isAdmin := isAdminUser(uId)
	if resource.Reserved != username && !isAdmin {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "并未占用该资源"))
		return
	}

	err = dao.Update(&dresourcepoolresource.ResourcePoolResource{}, ctx, map[string]interface{}{
		"Reserved":  "",
		"StartTime": nil,
		"EndTime":   nil,
	})

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ip := GetRemoteAddr(ctx)
	go func() {
		_, _ = libs.ExecCommand(fmt.Sprintf("python3 /home/<USER>/releaseresource.py %s %v %s %s %s", resource.ConIp, resource.ConPort, ip, resource.MgtIp, ip))
	}()

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func ReleaseResources(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	username, err := dresourcepoolresource.GetNowUserName(uId)
	isAdmin := isAdminUser(uId)

	request := ListRequest{}
	if err := ctx.ReadJSON(&request); err != nil {
		fmt.Print(err)
		logging.ErrorLogger.Errorf("create resource read json err %v", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, "create resource read json err %v", response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(&request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	resources, err := dresourcepoolresource.FindResourcesInIDs(request.IDs)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	updateObjects := []map[string]interface{}{}
	for _, resource := range resources {
		// 判断 Reserved 字段是否为空
		if resource.Reserved != username && !isAdmin {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "并未占用该资源"))
			return
		}
		updateObjects = append(updateObjects, map[string]interface{}{
			"ID":        resource.ID,
			"Name":      resource.Name,
			"Reserved":  "",
			"StartTime": nil,
			"EndTime":   nil,
		})
	}

	if len(updateObjects) != len(request.IDs) {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}

	err = dresourcepoolresource.BatchUpdateReserved(updateObjects)

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GiveResource(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	request := ReservedName{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("failed to read request JSON, error: %v", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	resource := dresourcepoolresource.ResourcePoolResource{}
	err = resource.Find(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	username, err := dresourcepoolresource.GetNowUserName(uId)
	isAdmin := isAdminUser(uId)
	if resource.Reserved != username && !isAdmin {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "非本人资源无法转移"))
		return
	}

	err = dao.Update(&dresourcepoolresource.ResourcePoolResource{}, ctx, map[string]interface{}{
		"Reserved": request.Reserved,

		"StartTime": time.Now(),
		"EndTime":   time.Now().AddDate(0, 0, 1),
	})

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GiveResources(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	username, err := dresourcepoolresource.GetNowUserName(uId)
	isAdmin := isAdminUser(uId)

	request := ListRequest{}
	if err := ctx.ReadJSON(&request); err != nil {
		fmt.Print(err)
		logging.ErrorLogger.Errorf("create resource read json err %v", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, "create resource read json err %v", response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(&request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	resources, err := dresourcepoolresource.FindResourcesInIDs(request.IDs)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	updateObjects := []map[string]interface{}{}
	for _, resource := range resources {
		// 判断 Reserved 字段是否为空
		if resource.Reserved != username && !isAdmin {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "非本人资源无法转移"))
			return
		}
		updateObjects = append(updateObjects, map[string]interface{}{
			"ID":        resource.ID,
			"Reserved":  request.Reserved,
			"Name":      resource.Name,
			"StartTime": time.Now(),
			"EndTime":   time.Now().AddDate(0, 0, 1),
		})
	}

	if len(updateObjects) != len(request.IDs) {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}

	err = dresourcepoolresource.BatchUpdateReserved(updateObjects)

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	// ip := GetRemoteAddr(ctx)
	// go func() {
	// 	_, _ = libs.ExecCommand(fmt.Sprintf("python3 /home/<USER>/getresource.py %s %v %s %s %s", resource.ConIp, resource.ConPort, ip, resource.MgtIp, ip))
	// }()

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func ReleaseResourceJober() error {
	items, err := dresourcepoolresource.AllReservedResource()
	if err != nil {
		return err
	}

	for _, item := range items {
		if time.Now().Sub(*item.EndTime) > 0 {
			err := item.Update(item.ID, map[string]interface{}{
				"Reserved":  "",
				"StartTime": nil,
				"EndTime":   nil,
			})
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func ReleaseResourceWorker() {
	err := ReleaseResourceJober()
	if err != nil {
		logging.ErrorLogger.Error("release resource get error", err)
	}
}

type SwitchTopRequest struct {
	IDs []uint `json:"ids"`
	Top string `json:"top"`
}

var switchTopLock sync.Mutex

func GetReyeeSwitchTopStatus(ctx iris.Context) {
	job := dresourcepooljob.ResourcePoolJob{}
	err := job.LastByName("切换拓扑")
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, job, response.NoErr.Msg))
	return
}

func SwitchReyeeTop(ctx iris.Context) {
	switchTopLock.Lock()
	defer switchTopLock.Unlock()

	userID, _ := dao.GetAuthId(ctx)
	user := duser.User{}
	err := user.Find(userID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	job := dresourcepooljob.ResourcePoolJob{}
	err = job.LastByName("切换拓扑")
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if job.ID > 0 && job.Status == 0 {
		if time.Now().Sub(job.CreatedAt).Minutes() < 20 {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("已经切换拓扑作业未完成，请%.1f分钟后重试", 10-time.Now().Sub(job.CreatedAt).Minutes())))
			return
		} else {
			UpdateJobStatus(&job, 2)
		}
	}

	request := SwitchTopRequest{}

	if err := ctx.ReadJSON(&request); err != nil {
		fmt.Print(err)
		logging.ErrorLogger.Errorf("create resource read json err %v", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, "create resource read json err %v", response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(&request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	// 判断是否占用设备

	resources, err := dresourcepoolresource.FindResourcesInIDs(request.IDs)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}

	for _, resource := range resources {
		if resource.Reserved != user.Username {
			ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, resource.Name+" 设备未占用，请先占用!"))
			return
		}
	}
	data, err := json.Marshal(&request)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	err = job.Create(map[string]interface{}{
		"UserID":    userID,
		"Name":      "切换拓扑",
		"Data":      data,
		"Status":    0,
		"CreatedAt": time.Now(),
	})

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	err = job.LastByName("切换拓扑")
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	go func(job *dresourcepooljob.ResourcePoolJob) {
		cmd := fmt.Sprintf("/volumes/lib/labtools/init_reyee.sh -m %s", request.Top)

		logging.DebugLogger.Debugf("execute ssh command: ", cmd)

		output, err := ExecuteSSHCommand(cmd)
		if err != nil {
			logging.ErrorLogger.Errorf("execute ssh command err: ", err, output)
			UpdateJobStatus(job, 2)
			return
		}
		logging.DebugLogger.Debugf("result", output)
		UpdateJobStatus(job, 1)
		return
	}(&job)

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func UpdateJobStatus(job *dresourcepooljob.ResourcePoolJob, status uint) {
	err := job.Update(job.ID, map[string]interface{}{
		"Status":    status,
		"UpdatedAt": time.Now(),
	})
	if err != nil {
		logging.ErrorLogger.Errorf("update job status err ", err)
	}
}

func ConsoleClear(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	resource := dresourcepoolresource.ResourcePoolResource{}
	err = resource.Find(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	username, err := dresourcepoolresource.GetNowUserName(uId)

	// 判断 Reserved 字段是否为空
	isAdmin := isAdminUser(uId)
	if resource.Reserved != username && !isAdmin {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "并未占用该资源"))
		return
	}

	cmd := fmt.Sprintf("console_clear %s %d", resource.ConIp, resource.ConPort)

	logging.DebugLogger.Debugf("execute ssh command: ", cmd)

	output, err := ExecuteSSHCommand(cmd)
	if err != nil {
		logging.ErrorLogger.Errorf("execute ssh command err: ", err, output)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}
