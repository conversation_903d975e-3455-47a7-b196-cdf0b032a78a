package performance

import (
	"irisAdminApi/application/models"
)

type Performance struct {
	models.ModelBase
	VersionID       uint   `gorm:"not null" json:"version_id" validate:"required"`
	Version         string `gorm:"not null; type:varchar(60)" json:"version"`
	Product         string `gorm:"not null; type:varchar(60)" json:"product" validate:"required"`
	Branch          string `gorm:"not null; type:varchar(60)" json:"branch" validate:"required"`
	BasicThroughput uint   `gorm:"not null" json:"basic_throughput"`
	BasicCps        uint   `gorm:"not null" json:"basic_cps"`
	BasicCc         uint   `gorm:"not null" json:"basic_cc"`
	AppidThroughput uint   `gorm:"not null" json:"appid_throughput"`
	IpsThroughput   uint   `gorm:"not null" json:"ips_throughput"`
	AvThroughput    uint   `gorm:"not null" json:"av_throughput"`
	Status          bool   `gorm:"not null" json:"status"`
	Baseline        bool   `gorm:"not null;default:false" json:"baseline"`
}

type Performance2544 struct {
	models.ModelBase
	VersionID               uint    `gorm:"not null" json:"version_id" validate:"required"`
	Version                 string  `gorm:"not null; type:varchar(60)" json:"version"`
	Product                 string  `gorm:"not null; type:varchar(60)" json:"product" validate:"required"`
	Branch                  string  `gorm:"not null; type:varchar(60)" json:"branch" validate:"required"`
	Category                string  `gorm:"not null; type:varchar(60)" json:"category" validate:"required"`
	Framesize               string  `gorm:"not null; type:varchar(60)" json:"Framesize"`
	AggRxThroughputLineRate string  `gorm:"not null; type:varchar(60)" json:"Agg Rx Throughput (% Line Rate)"`
	AggL1RxRate             float32 `gorm:"not null; type:varchar(60)" json:"Agg L1 Rx Rate(Mbps)"`
	AvgLatency              float32 `gorm:"not null" json:"Avg Latency (us)"`
	Status                  bool    `gorm:"not null" json:"status"`
	Baseline                bool    `gorm:"not null;default:false" json:"baseline"`
}
