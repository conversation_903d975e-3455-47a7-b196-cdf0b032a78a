package main

import (
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/imroc/req/v3"
)

var buildFarmServer = "http://aqyfzx.ruijie.net:9090"
var coredumpServer = "http://10.51.134.126:8082"
var client = req.C().SetTimeout(60 * time.Second) // Use C() to create a client and set with chainable client settings.
type JobResult interface {
	GetDownloadUrl(output, product, softnum, releaseID, branch string) (string, error)
}

func main() {

	var customServer, product, softnum, releaseID, branch, tech_support, output string
	var quiet bool

	var err error
	flag.StringVar(&customServer, "h", "", "自定义服务器地址http://xxx.xxx.xxx.xx:9090")
	flag.StringVar(&product, "p", "", "产品")
	flag.StringVar(&softnum, "s", "", "softnum")
	flag.StringVar(&releaseID, "r", "", "releaseID")
	flag.StringVar(&branch, "b", "", "branch")
	flag.StringVar(&tech_support, "f", "", "tech_support file")
	flag.StringVar(&output, "o", "", "output dir")
	flag.BoolVar(&quiet, "q", false, "output dir")
	flag.Parse()

	//decompress
	client = client.SetOutputDirectory(output)
	techSupportOutput := filepath.Join(output, "tech_support")
	rootfsDebugOutput := filepath.Join(output, "rootfs_debug")
	if customServer != "" {
		buildFarmServer = customServer
	}
	if !quiet {
		if _, err := os.Stat(output); !os.IsNotExist(err) {
			remove := "N"
			fmt.Printf("alread exists output dir, do you want clean it。[y/N]")
			fmt.Scanln(&remove)
			if strings.ToUpper(remove) == "Y" {
				if err := os.RemoveAll(output); err != nil {
					fmt.Println("clean output dir failed", err)
					os.Exit(1)
				}
			}
			if strings.ToUpper(remove) == "N" {
				fmt.Println("please clean output dir manual or change to a new output dir")
				os.Exit(1)
			}
			time.Sleep(5)
			if err := os.MkdirAll(output, 0755); err != nil {
				fmt.Println("create output dir failed", err)
				os.Exit(1)
			}
		}
	}

	if err := os.MkdirAll(techSupportOutput, 0755); err != nil {
		fmt.Println("create output dir failed", err)
		os.Exit(1)
	}

	if err := os.MkdirAll(rootfsDebugOutput, 0755); err != nil {
		fmt.Println("create output dir failed", err)
		os.Exit(1)
	}

	if softnum == "" {
		err := DeCompress(tech_support, techSupportOutput)
		if err != nil {
			fmt.Println("decompress tech support err", err)
			os.Exit(1)
		}
		softnum, err = GetSoftNum(techSupportOutput)
		if err != nil {
			fmt.Println("get softnum err", err)
			os.Exit(1)
		}
	}

	// branch, err = GetBranch(techSupportOutput)
	// if err != nil {
	// 	fmt.Println("get branch err", err)
	// 	os.Exit(1)
	// }

	if product == "" {
		product, err = GetProduct(techSupportOutput)
		if err != nil {
			fmt.Println("get product err", err)
			os.Exit(1)
		}

	}

	if releaseID == "" {
		releaseID, err = GetReleaseID(techSupportOutput)
		if err != nil {
			fmt.Println("get releaseid err", err)
			os.Exit(1)
		}
	}

	softversion, err := GetSoftVersion(techSupportOutput)
	if err != nil {
		fmt.Println("get softversion err", err)
		os.Exit(1)
	}

	fmt.Println(softnum, releaseID, softversion)
	fmt.Printf("softnum: %s", softnum)
	fmt.Printf("releaseID: %s", releaseID)
	fmt.Printf("softversion: %s", softversion)
	fmt.Printf("product: %s", product)
	// fmt.Printf("branch: %s", branch)

	fmt.Println(product, branch)
	fmt.Printf("产品信息开始>>>%s<<<产品信息结束\n", product)

	if product == "" || softversion == "" {
		fmt.Println("invalid softversion or product")
		os.Exit(1)
	}

	var downloadUrl string
	var jobResult JobResult
	if releaseID == "" {
		releaseAttrID := "3"
		result := archiveDReleasResult{}
		downloadUrl, err := result.GetDownloadUrl(output, product, softnum, softversion, branch, releaseAttrID)
		if err != nil {
			fmt.Println(downloadUrl, err)
			os.Exit(1)
		}
		if downloadUrl != "" && err == nil {
			err = DonwloadRootfsAndImage(output, downloadUrl, rootfsDebugOutput)
			if err != nil {
				fmt.Println(downloadUrl, err)
				os.Exit(1)
			}
		} else {
			fmt.Println("暂未查询到归档版本链接", downloadUrl, err)
			os.Exit(1)
		}
	} else {

		// 先查找每日编译
		var success bool = false
		for _, target := range []string{"cronmake", "make"} {
			// for _, target := range []string{"cronmake"} {
			switch target {
			case "cronmake":
				jobResult = &cronMakeJobResult{}
			case "make":
				jobResult = &makeJobResult{}
			}
			downloadUrl, err = jobResult.GetDownloadUrl(output, product, softnum, softversion, branch)
			if downloadUrl != "" && err == nil {
				success = true
				err := DonwloadRootfsAndImage(output, downloadUrl, rootfsDebugOutput)
				if err != nil {
					fmt.Println(downloadUrl, err)
					os.Exit(1)
				}
				break
			} else {
				fmt.Println(downloadUrl, err)
			}
		}
		if !success {
			fmt.Println("未找到匹配的编译记录")
			os.Exit(1)
		}
	}

	fmt.Println("done!")
	fmt.Println("aarch64架构请执行：")
	fmt.Println(fmt.Sprintf("请进入目录 %s， 并执行gunzip xxxxx.coredump.gz", filepath.Join(techSupportOutput, "coredump")))
	fmt.Println(fmt.Sprintf("/opt/Marvell/marvell-tools-265.0/bin/aarch64-marvell-linux-gnu-gdb %s %s", filepath.Join(rootfsDebugOutput, "/usr/bin/xxxx"), filepath.Join(techSupportOutput, "coredump/xxxxx.coredump")))
	fmt.Println("xxxx, xxxx分别代表应用及coredump程序")
	fmt.Println("编译农场下载地址", downloadUrl)

}
