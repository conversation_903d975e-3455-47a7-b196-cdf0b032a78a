package datasync

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/datasync/dresource"
	"irisAdminApi/service/dao/datasync/dsyncrecord"
)

func ResourceSyncCsbuWorker() error {
	// todo: 检查同步记录，获取同步时间，如果没有，从七天前开始，按小时同步
	modifyDateArray := []string{}
	_url := "https://dataware.ruijie.com.cn/api/public/data-api/safe_product_res_resource_detail/info.data?for=csbu"
	records, err := dsyncrecord.FindLastSuccessSyncRecord(_url)
	if err != nil {
		logging.ErrorLogger.Errorf("get last sync records", err.Error())
		return err
	}

	if len(records) > 0 {
		modifyDateArray = libs.GetDateRange(-365, records[0].MaxModifyDate)
	} else {
		modifyDateArray = []string{"2023-12-05", "2024-12-31"}
	}

	if len(modifyDateArray) < 2 {
		logging.ErrorLogger.Errorf("get modify date array error")
		return nil
	}

	page := 1
	rows := 1000

	for {
		data := map[string]string{
			"sid":           "ODc5ODc2ZTk0",
			"minModifyDate": modifyDateArray[0],
			"maxModifyDate": modifyDateArray[len(modifyDateArray)-1],
			"page":          strconv.Itoa(page),
			"rows":          strconv.Itoa(rows),
		}

		var result dresource.ResourceSyncResponse
		var errMsg dresource.ResourceSyncResponse
		resp, err := SyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(data).Get(strings.ReplaceAll(_url, "?for=csbu", ""))
		if err != nil {
			logging.ErrorLogger.Errorf("get resources error", err.Error())
			time.Sleep(30 * time.Second)
			continue
		}
		if resp.IsSuccessState() {
			if result.State == "SUCCESS" {
				err := dresource.UpdateOrCreateResourceTransaction(result.Data, _url, data, resp.Request.Method, result.State, result.Message)
				if err != nil {
					logging.ErrorLogger.Errorf("update or create resources error", err.Error())
					return err
				}
			} else {
				logging.ErrorLogger.Errorf("get resources failed", result.State, result.Message)
				time.Sleep(30 * time.Second)
				continue
			}
		} else {
			logging.ErrorLogger.Errorf("get resources unkown error")
			time.Sleep(30 * time.Second)
			continue
		}

		time.Sleep(30 * time.Second)
		if result.Total > rows*page {
			page++
		} else {
			break
		}
	}

	return nil
}

func ResourceSyncScaleWorker() error {
	// todo: 检查同步记录，获取同步时间，如果没有，从七天前开始，按小时同步
	modifyDateArray := []string{}
	_url := "https://dataware.ruijie.com.cn/api/public/data-api/safe_product_res_resource_detail/info.data?for=scale"
	records, err := dsyncrecord.FindLastSuccessSyncRecord(_url)
	if err != nil {
		logging.ErrorLogger.Errorf("get last sync records", err.Error())
		return err
	}

	if len(records) > 0 {
		modifyDateArray = libs.GetDateRange(-365, records[0].MaxModifyDate)
	} else {
		modifyDateArray = []string{"2023-01-01", "2023-12-31"}
	}

	if len(modifyDateArray) < 2 {
		logging.ErrorLogger.Errorf("get modify date array error")
		return nil
	}

	scales, err := AllScales()
	if err != nil {
		logging.ErrorLogger.Errorf("find all scales error", err.Error())
		return err
	}

	if len(scales) > 0 {

		page := 1
		rows := 1000

		for {
			data := map[string]string{
				"sid":           "ODc5ODc2ZTk0",
				"minModifyDate": modifyDateArray[0],
				"maxModifyDate": modifyDateArray[len(modifyDateArray)-1],
				"page":          strconv.Itoa(page),
				"rows":          strconv.Itoa(rows),
			}
			var result dresource.ResourceSyncResponse
			var errMsg dresource.ResourceSyncResponse
			req := SyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(data)

			for _, scale := range scales {
				req = req.SetQueryParam("pmsProjectIds[]", strconv.Itoa(scale.PmsDestProjectId))
			}

			resp, err := req.Post(strings.ReplaceAll(_url, "?for=scale", ""))
			if err != nil {
				logging.ErrorLogger.Errorf("get resources error", err.Error())
				time.Sleep(30 * time.Second)
				continue
			}
			if resp.IsSuccessState() {
				if result.State == "SUCCESS" {
					err := dresource.UpdateOrCreateResourceTransaction(result.Data, _url, data, resp.Request.Method, result.State, result.Message)
					if err != nil {
						logging.ErrorLogger.Errorf("update or create resources error", err.Error())
						return err
					}
				} else {
					logging.ErrorLogger.Errorf("get resources failed", result.State, result.Message)
					time.Sleep(30 * time.Second)
					continue
				}
			} else {
				logging.ErrorLogger.Errorf("get resources unkown error")
				time.Sleep(30 * time.Second)
				continue
			}

			time.Sleep(30 * time.Second)
			if result.Total > rows*page {
				page++
			} else {
				break
			}
		}
	}

	return nil
}

func ResourceDeleteSyncWorker() error {
	// todo: 检查同步记录，获取同步时间，如果没有，从七天前开始，按小时同步
	modifyDateArray := []string{}
	_url := "https://dataware.ruijie.com.cn/api/public/data-api/safe_product_res_resource_delete_info/list.data"
	records, err := dsyncrecord.FindLastSuccessSyncRecord(_url)
	if err != nil {
		logging.ErrorLogger.Errorf("get last sync records", err.Error())
		return err
	}

	if len(records) > 0 {
		modifyDateArray = libs.GetDateRange(-365, records[0].MaxModifyDate)
	} else {
		modifyDateArray = []string{"2023-11-12", "2024-12-31"}
	}

	if len(modifyDateArray) < 2 {
		return nil
	}

	page := 1
	rows := 1000

	for {
		data := map[string]string{
			"sid":           "ZWEwNjFjNDEw",
			"minModifyDate": modifyDateArray[0],
			"maxModifyDate": modifyDateArray[len(modifyDateArray)-1],
			"page":          strconv.Itoa(page),
			"rows":          strconv.Itoa(rows),
		}

		var result dresource.ResourceDeleteSyncResponse
		var errMsg dresource.ResourceDeleteSyncResponse
		resp, err := SyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(data).Get(_url)
		if err != nil {
			logging.ErrorLogger.Errorf("get resources delete error", err.Error())
			return err
		}
		if resp.IsSuccessState() {
			if result.State == "SUCCESS" {
				err := dresource.UpdateOrCreateResourceDeleteTransaction(result.Data, _url, data, resp.Request.Method, result.State, result.Message)
				if err != nil {
					logging.ErrorLogger.Errorf("update or create resources delete error", err.Error())
					return err
				}
			} else {
				logging.ErrorLogger.Errorf("get resources delete failed", result.State, result.Message)
				return fmt.Errorf("get resources delete failed, %s, %s", result.State, result.Message)
			}
		} else {
			logging.ErrorLogger.Errorf("get resources delete unkown error")
			return errors.New("unkown error")
		}

		time.Sleep(30 * time.Second)
		if result.Total > rows*page {
			page++
		} else {
			break
		}
	}

	return nil
}
