package user

import "irisAdminApi/application/models"

type Role struct {
	models.ModelBase

	Name        string     `gorm:"uniqueIndex;not null; type:varchar(200)" json:"name" validate:"required,gte=4,lte=50" comment:"名称"`
	DisplayName string     `gorm:"type:varchar(200)" json:"display_name" comment:"显示名称"`
	Description string     `gorm:"type:varchar(200)" json:"description" comment:"描述"`
	Perms       [][]string `gorm:"-" json:"perms" comment:"权限 name, act "`
}
