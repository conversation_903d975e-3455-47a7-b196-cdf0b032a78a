package datasync

type BugBranchInfo struct {
	/*
	   "id": 28,                                                //主键ID，可作为增量更新标识使用
	   "projectName": "测试项目",                                //项目名称
	   "componentName": "测试组件",                            //组件名称
	   "newBranchUrl": "xxxx-eg-1.1.34",                        //新分支
	   "baselineBranchUrl": "xxxx-eg-1.1.24",                    //基线分支
	   "baselineBranchVersion": "1.0",                            //基线版本号
	   "applyUserName": "测试人1",                                //分支申请人姓名
	   "createDate": "2017-07-04 19:45:31",                    //分支开通时间
	   "newBranchVersion": "2.0",                                //分支当前版本号
	   "writerUserName": "测试人2",                            //分支可写人姓名
	   "writerCasUserId": "ceshi2"                                //分支可写人邮箱前缀
	*/
	ID                    int    `gorm:"primarykey; autoIncrement:false" json:"id"`
	ProjectName           string `gorm:"not null; type:varchar(60)" json:"project_name"`
	ComponentName         string `gorm:"not null; type:varchar(300)" json:"component_name"`
	NewBranchUrl          string `gorm:"not null; type:varchar(300)" json:"new_branch_url"`
	BaselineBranchUrl     string `gorm:"not null; type:varchar(300)" json:"baseline_branch_url"`
	BaselineBranchVersion string `gorm:"not null; type:varchar(300)" json:"baseline_branch_version"`
	ApplyUserName         string `gorm:"not null; type:varchar(300)" json:"apply_user_name"`
	CreateDate            string `gorm:"not null; type:varchar(300)" json:"create_date"`
	NewBranchVersion      string `gorm:"not null; type:varchar(300)" json:"new_branch_version"`
	WriterUserName        string `gorm:"not null; type:varchar(300)" json:"writer_user_name"`
	WriterCasUserId       string `gorm:"not null; type:varchar(300)" json:"writer_cas_user_id"`
}
