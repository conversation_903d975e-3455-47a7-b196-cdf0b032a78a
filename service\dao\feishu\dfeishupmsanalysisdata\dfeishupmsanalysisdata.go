package dfeishupmsanalysisdata

import (
	"fmt"
	"reflect"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models"
	"irisAdminApi/application/models/feishu"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "评测分析数据表"

type Response struct {
	models.ModelBase
	RecordType        int      `gorm:"type:tinyint(1);not null;comment:'1:质量目标,2:项目汇总,3工作包数据'" json:"record_type"`
	BsIrDefect        *float64 `gorm:"comment:'内测前缺陷密度'" json:"bs_ir_defect" `
	StUtDefect        *float64 `gorm:"comment:'单元测试缺陷密度'" json:"st_ut_defect" `
	StIrDefect        *float64 `json:"st_ir_defect"`
	SDdDefect         *float64 `json:"s_dd_defect"`
	SSdDefect         *float64 `json:"s_sd_defect"`
	SRaDefect         *float64 `json:"s_ra_defect"`
	StIgDefect        *float64 `json:"st_ig_defect"`
	SCsDefect         *float64 `json:"s_cs_defect"`
	STdDefect         *float64 `json:"s_td_defect"`
	ProjectName       string   `gorm:"type:varchar(255)" json:"project_name" `
	ProjectID         int      `json:"project_id" `
	BsTtA             *float64 `json:"bs_tt_a" `
	WpStCount         *float64 `json:"wp_st_count" `
	StIr0             *float64 `json:"st_ir0" `
	StIr1             *float64 `json:"st_ir1" `
	CodeTransTemp     *float64 `json:"code_trans_temp" `
	StIr2             *float64 `json:"st_ir2" `
	BsIgHDefect       *float64 `json:"bs_ig_h_defect" `
	CodeTrans         *float64 `json:"code_trans" `
	SSdCount          *float64 `json:"s_sd_count" `
	CodeNew           *float64 `json:"code_new" `
	STdCount          *float64 `json:"s_td_count" `
	WpStDefect        *float64 `json:"wp_st_defect" `
	SCsRCount         *float64 `json:"s_cs_r_count" `
	SDdCount          *float64 `json:"s_dd_count" `
	StUt0             *float64 `json:"st_ut0" `
	SRaCount          *float64 `json:"s_ra_count" `
	BsIgA             *float64 `json:"bs_ig_a" `
	BsTtADefect       *float64 `json:"bs_tt_a_defect" `
	CodeTotal         *float64 `json:"code_total" `
	SCsBCount         *float64 `json:"s_cs_b_count" `
	BsIrCount         *float64 `json:"bs_ir_count" `
	BsIgADefect       *float64 `json:"bs_ig_a_defect" `
	StIg0             *float64 `json:"st_ig0" `
	BsIgH             *float64 `json:"bs_ig_h" `
	WorkPacketName    string   `gorm:"type:varchar(255)" json:"work_packet_name" `
	SRa0              *float64 `json:"s_ra0" `
	SCbDc3            *float64 `json:"s_cb_dc3" `
	SCbDc4            *float64 `json:"s_cb_dc4"  `
	SSd0              *float64 `json:"s_sd0"  `
	SSd1              *float64 `json:"s_sd1"  `
	StIr1Defect       *float64 `json:"st_ir1_defect"  `
	SCr0              *float64 `json:"s_cr0"  `
	SCr2              *float64 `json:"s_cr2"  `
	SCr1              *float64 `json:"s_cr1" `
	SCr3              *float64 `json:"s_cr3"  `
	WorkPacketID      int      `json:"work_packet_id"  `
	SDd1              *float64 `json:"s_dd1" `
	SDd0              *float64 `json:"s_dd0" `
	SCbDc0            *float64 `json:"s_cb_dc0" `
	SCbDc1            *float64 `json:"s_cb_dc1" `
	SDd2              *float64 `json:"s_dd2" `
	SCbDc2            *float64 `json:"s_cb_dc2"`
	OBs0              *float64 `json:"o_bs0" `
	STd1              *float64 `json:"s_td1" `
	STd0              *float64 `json:"s_td0" `
	WorkPacketManager string   `gorm:"type:varchar(255)" json:"work_packet_manager" `
	PSTLUserName      string   `gorm:"type:varchar(100)" json:"pstl_user_name"`
}

type ListResponse struct {
	Response
}

type Request struct {
	Id uint `json:"id"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *feishu.FeishuPmsAnalysisData {
	return &feishu.FeishuPmsAnalysisData{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (a *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (u *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (u *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func BatchCreate(items []map[string]interface{}) error {
	db := easygorm.GetEasyGormDb().Model(Model())
	return db.Create(items).Error
}

func DeleteAll() error {
	db := easygorm.GetEasyGormDb().Model(Model())
	err := db.Unscoped().Delete(Model(), "id>0").Error
	if err != nil {
		return err
	}
	// 重置id值
	alterTableSQL := fmt.Sprintf("ALTER TABLE feishu_file_lists AUTO_INCREMENT=%d", 1)
	if err := easygorm.GetEasyGormDb().Exec(alterTableSQL).Error; err != nil {
		logging.ErrorLogger.Errorf("ALTER TABLE feishu_file_lists  get err ", err)
		return err
	}
	return nil
}

func CreateOrUpdateAnalysisData(objects []map[string]interface{}) error {
	ReSetAutoIncrement()
	columns := []string{"updated_at"}
	xt := reflect.TypeOf(Model())
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")
		if ok && ok2 {
			columns = append(columns, name)
		}
	}
	projectName := ""
	workPackages := []string{}
	for _, object := range objects {
		if projectName == "" {
			projectName = object["ProjectName"].(string)
		}

		workPackages = append(workPackages, object["WorkPacketName"].(string))
	}

	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		if len(objects) > 0 {
			err := tx.Model(Model()).Where("project_name = ? and work_packet_name not in (?)", projectName, workPackages).Unscoped().Delete(Model()).Error
			if err != nil {
				return err
			}
			err = tx.Model(Model()).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "record_type"}, {Name: "project_id"}, {Name: "work_packet_id"}},
				DoUpdates: clause.AssignmentColumns(columns),
			}).Create(&objects).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func ReSetAutoIncrement() error {
	var maxID int
	err := easygorm.GetEasyGormDb().Table("feishu_pms_analysis_data").Select("MAX(id)").Scan(&maxID).Error
	if err != nil {
		return err
	}
	newAutoIncrement := maxID + 1
	alterTableSQL := fmt.Sprintf("ALTER TABLE feishu_pms_analysis_data AUTO_INCREMENT=%d", newAutoIncrement)
	if err := easygorm.GetEasyGormDb().Exec(alterTableSQL).Error; err != nil {
		logging.ErrorLogger.Errorf("ALTER TABLE feishu_pms_analysis_datas  get err ", err)
		return err
	}
	return nil
}
