package openfeishu

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/user/duser"
	"irisAdminApi/service/dao/user/duseropenid"
	"strings"
	"time"
)

// 数据处理和同步逻辑处理器
type QualityControlProcessor struct {
	config       *QualityControlConfig
	statistics   *SyncStatistics
	results      []SyncResult
	fieldMapping map[string]FieldMapping
	userCache    map[string]string // 用户名到飞书ID的缓存
}

// 创建新的处理器实例
func NewQualityControlProcessor(config *QualityControlConfig) *QualityControlProcessor {
	if config == nil {
		config = &QualityControlConfig{
			EnableClosedLoop: true,
			BatchSize:        50,
			MaxRetries:       3,
		}
	}

	return &QualityControlProcessor{
		config:       config,
		statistics:   &SyncStatistics{},
		results:      make([]SyncResult, 0),
		fieldMapping: getDefaultFieldMapping(),
		userCache:    make(map[string]string), // 初始化用户缓存
	}
}

// 获取默认字段映射配置
func getDefaultFieldMappings() []FieldMapping {
	return []FieldMapping{
		{
			FeishuField:  "品控责任人",
			QualityField: "faultUserName",
			DataType:     "string",
			Required:     true,
		},
		{
			FeishuField:  "品控问题是否关闭",
			QualityField: "pzkzStatus",
			DataType:     "string",
			DefaultValue: "未关闭",
			Required:     true,
		},
		{
			FeishuField:  "超期情况",
			QualityField: "dealDay",
			DataType:     "float",
			Required:     false,
		},
		{
			FeishuField:  "问题时效",
			QualityField: "timeDay",
			DataType:     "float",
			Required:     false,
		},
		{
			FeishuField:  "品控问题是否闭环",
			ClosedField:  "closedStatus",
			DataType:     "string",
			DefaultValue: "未闭环",
			Required:     false,
		},
		{
			FeishuField:   "是否提供解决方案",
			SolutionField: "isFinish",
			DataType:      "string",
			DefaultValue:  "否",
			Required:      false,
		},
	}
}

// 获取字段映射
func getDefaultFieldMapping() map[string]FieldMapping {
	mappings := getDefaultFieldMappings()
	result := make(map[string]FieldMapping)
	for _, mapping := range mappings {
		result[mapping.FeishuField] = mapping
	}
	return result
}

// 执行完整的数据同步流程
func (p *QualityControlProcessor) ExecuteSync() (*SyncStatistics, error) {
	logging.InfoLogger.Info("开始执行品控数据同步")

	// 记录开始时间
	startTime := time.Now()
	p.statistics.StartTime = startTime.Format("2006-01-02 15:04:05")

	defer func() {
		// 记录结束时间和耗时
		endTime := time.Now()
		p.statistics.EndTime = endTime.Format("2006-01-02 15:04:05")
		p.statistics.Duration = endTime.Sub(startTime).String()
		logging.InfoLogger.Infof("品控数据同步完成，耗时: %s", p.statistics.Duration)
	}()

	// 步骤1：获取飞书表格数据并构建索引（包含品控责任人字段状态缓存）
	logging.InfoLogger.Info("步骤1: 获取飞书表格数据并预先缓存品控责任人字段状态")
	feishuRecords, qualityResponsiblePersonStatusMap, err := GetFeishuQualityRecords()
	if err != nil {
		return nil, fmt.Errorf("获取飞书数据失败: %v", err)
	}

	p.statistics.TotalRecords = len(feishuRecords)
	logging.InfoLogger.Infof("获取到 %d 条飞书记录", p.statistics.TotalRecords)

	// 构建基于RecordID的索引（解决重复品控单号问题）
	recordIndex := BuildRecordBasedIndex(feishuRecords)

	// 步骤2：获取品控系统数据
	logging.InfoLogger.Info("步骤2: 获取品控系统数据")
	qualityNumbers := recordIndex.GetUniqueQualityNumbers()
	logging.InfoLogger.Infof("需要获取 %d 个唯一品控单号的数据", len(qualityNumbers))

	// 调用品控API获取数据
	qualityData, err := p.getQualityControlData(qualityNumbers)
	if err != nil {
		return nil, fmt.Errorf("获取品控数据失败: %v", err)
	}

	logging.InfoLogger.Infof("获取到 %d 条品控数据", len(qualityData))

	// 步骤3：获取闭环管理数据（如果启用）
	var closedLoopData map[string]map[int]*ClosedLoopResponse
	if p.config.EnableClosedLoop {
		logging.InfoLogger.Info("步骤3: 获取闭环管理数据")
		closedLoopData, err = p.getClosedLoopData(qualityNumbers)
		if err != nil {
			logging.ErrorLogger.Errorf("获取闭环数据失败: %v", err)
			// 闭环数据获取失败不影响主流程
		} else {
			logging.InfoLogger.Infof("获取到 %d 条闭环数据", len(closedLoopData))
		}
	}

	// 步骤4：数据处理和更新
	logging.InfoLogger.Info("步骤4: 开始数据处理和更新")
	err = p.processAndUpdateDataV2(recordIndex, qualityData, closedLoopData, qualityResponsiblePersonStatusMap)
	if err != nil {
		return nil, fmt.Errorf("数据处理失败: %v", err)
	}

	logging.InfoLogger.Infof("数据同步完成 - 总计: %d, 匹配: %d, 更新: %d, 跳过: %d, 错误: %d",
		p.statistics.TotalRecords,
		p.statistics.MatchedRecords,
		p.statistics.UpdatedRecords,
		p.statistics.SkippedRecords,
		p.statistics.ErrorRecords)

	return p.statistics, nil
}

// getSolutionStatus 获取解决方案状态（基于品控状态和类别的智能判断）
func (p *QualityControlProcessor) getSolutionStatus(questionID string, pzkzStatus string, pzkzTypeName string, isQualityProblemClosed string) (string, error) {
	logging.InfoLogger.Infof("开始获取品控单号 %s 的解决方案状态，品控状态: %s，品控类别: %s，问题关闭状态: %s",
		questionID, pzkzStatus, pzkzTypeName, isQualityProblemClosed)

	// 优先级1：检查品控工单状态是否为"废弃"
	if pzkzStatus == "废弃" {
		logging.InfoLogger.Infof("品控单号 %s 状态为废弃，直接设置解决方案状态为: 是", questionID)
		return "是", nil
	}

	// 优先级2：根据品控类别采用不同的判断逻辑
	switch pzkzTypeName {
	case "技术咨询":
		// 技术咨询类型：基于"品控问题是否关闭"字段状态来确定
		logging.InfoLogger.Infof("品控单号 %s 为技术咨询类型，基于问题关闭状态判断", questionID)

		if isQualityProblemClosed == "是" {
			logging.InfoLogger.Infof("品控单号 %s 技术咨询已关闭，解决方案状态: 是", questionID)
			return "是", nil
		} else {
			logging.InfoLogger.Infof("品控单号 %s 技术咨询未关闭，解决方案状态: 否", questionID)
			return "否", nil
		}

	case "问题单":
		// 问题单类型：保持现有逻辑，通过调用解决方案API获取数据
		logging.InfoLogger.Infof("品控单号 %s 为问题单类型，调用解决方案API判断", questionID)
		return p.getSolutionStatusFromAPI(questionID)

	default:
		// 其他类型：使用默认值"否"
		logging.InfoLogger.Infof("品控单号 %s 为其他类型 (%s)，使用默认值: 否", questionID, pzkzTypeName)
		return "否", nil
	}
}

// getSolutionStatusFromAPI 通过API获取解决方案状态（原有逻辑）
func (p *QualityControlProcessor) getSolutionStatusFromAPI(questionID string) (string, error) {
	logging.InfoLogger.Infof("调用解决方案API获取品控单号 %s 的状态", questionID)

	// 创建API实例
	api := NewQualityControlAPI()

	// 调用解决方案API
	solutions, err := api.GetSolutionList(questionID)
	if err != nil {
		logging.ErrorLogger.Errorf("调用解决方案API失败: %v", err)
		return "否", err
	}

	// 处理返回结果
	if len(solutions) == 0 {
		logging.InfoLogger.Infof("品控单号 %s 无解决方案数据，返回默认值: 否", questionID)
		return "否", nil
	}

	// 检查isFinish字段值
	// 根据业务逻辑，只要有一个解决方案的isFinish为1，就认为已提供解决方案
	for _, solution := range solutions {
		if solution.IsFinish == 1 {
			logging.InfoLogger.Infof("品控单号 %s 已提供解决方案 (解决方案ID: %d, isFinish: %d)",
				questionID, solution.ID, solution.IsFinish)
			return "是", nil
		}
	}

	// 所有解决方案的isFinish都为0
	logging.InfoLogger.Infof("品控单号 %s 未提供解决方案 (共 %d 条解决方案记录，isFinish均为0)",
		questionID, len(solutions))
	return "否", nil
}

// 数据处理和更新V2
func (p *QualityControlProcessor) processAndUpdateDataV2(
	recordIndex *RecordBasedIndex,
	qualityData map[string]*ProcessedQualityControlItem,
	closedLoopData map[string]map[int]*ClosedLoopResponse,
	qualityResponsiblePersonStatusMap map[string]bool) error {

	logging.InfoLogger.Info("开始处理和更新数据（基于RecordID索引）")

	// 统计重复品控单号情况
	duplicateStats := recordIndex.GetDuplicateStatistics()
	if len(duplicateStats) > 0 {
		logging.InfoLogger.Infof("🔍 发现 %d 个重复品控单号，但所有记录都将被正确处理:", len(duplicateStats))
		for qualityNumber, recordIDs := range duplicateStats {
			logging.InfoLogger.Infof("  - 品控单号 %s: %d 条记录 %v", qualityNumber, len(recordIDs), recordIDs)
		}
	}

	// 遍历每个飞书记录（基于RecordID）
	for recordID, qualityNumber := range recordIndex.RecordToQualityNumber {
		result := SyncResult{
			QualityNumber: qualityNumber,
			RecordID:      recordID,
			UpdatedFields: make([]string, 0),
		}

		// 检查是否有对应的品控数据
		qualityItem, hasQualityData := qualityData[qualityNumber]
		if !hasQualityData {
			result.Status = "skipped"
			result.Message = "未找到对应的品控数据"
			p.results = append(p.results, result)
			p.statistics.SkippedRecords++
			continue
		}
		p.statistics.MatchedRecords++

		// 构建更新数据
		updateData := make(map[string]interface{})

		// 处理品控系统数据
		err := p.mapQualityDataToFeishu(qualityItem, updateData, &result)
		if err != nil {
			result.Status = "error"
			result.Message = fmt.Sprintf("数据映射失败: %v", err)
			p.results = append(p.results, result)
			p.statistics.ErrorRecords++
			continue
		}

		// 处理闭环管理数据
		if p.config.EnableClosedLoop && closedLoopData != nil {
			if closedItems, hasClosedData := closedLoopData[qualityNumber]; hasClosedData {
				// 映射闭环数据到飞书字段（修复：使用FaultLvlName字段）
				err = p.mapClosedLoopDataToFeishu(closedItems, updateData, &result, qualityItem.FaultLvlName, qualityItem.DevFaultReasonName, qualityItem.PzkzStatus)
				if err != nil {
					logging.ErrorLogger.Errorf("品控单号 %s 闭环数据映射失败: %v", qualityNumber, err)
					// 闭环数据映射失败时设置默认值
					p.setDefaultClosedLoopStatus(updateData, &result, qualityItem.FaultLvlName, qualityItem.DevFaultReasonName)
				}
				// 记录处理情况和统计
				if len(closedItems) == 0 {
					logging.InfoLogger.Infof("品控单号 %s 无需闭环，已设置相应状态 (故障等级: %s, 研发故障原因: %s)",
						qualityNumber, qualityItem.FaultLvlName, qualityItem.DevFaultReasonName)
					p.statistics.NoClosedLoopRecords++
				} else {
					logging.InfoLogger.Infof("品控单号 %s 已处理闭环状态 (闭环数据项数: %d, 故障等级: %s, 研发故障原因: %s)",
						qualityNumber, len(closedItems), qualityItem.FaultLvlName, qualityItem.DevFaultReasonName)
					p.statistics.NeedClosedLoopRecords++
				}
			} else {
				// 没有闭环数据记录时设置默认值
				logging.InfoLogger.Infof("品控单号 %s 没有闭环数据记录，设置默认状态", qualityNumber)
				p.setDefaultClosedLoopStatus(updateData, &result, qualityItem.FaultLvlName, qualityItem.DevFaultReasonName)
			}
		} else {
			// 闭环功能未启用或获取失败时设置默认值
			logging.InfoLogger.Infof("品控单号 %s 闭环功能未启用，设置默认状态", qualityNumber)
			p.setDefaultClosedLoopStatus(updateData, &result, qualityItem.FaultLvlName, qualityItem.DevFaultReasonName)
		}

		// 执行飞书记录更新（传递预先缓存的状态映射）
		if len(updateData) > 0 {
			err = p.updateFeishuRecord(recordID, updateData, qualityResponsiblePersonStatusMap)
			if err != nil {
				result.Status = "error"
				result.Message = fmt.Sprintf("飞书更新失败: %v", err)
				p.results = append(p.results, result)
				p.statistics.ErrorRecords++
				continue
			}

			result.Status = "success"
			result.Message = "更新成功"
			p.statistics.UpdatedRecords++
		} else {
			result.Status = "skipped"
			result.Message = "无需更新"
			p.statistics.SkippedRecords++
		}

		p.results = append(p.results, result)
	}

	logging.InfoLogger.Infof("数据处理完成，处理记录数: %d", len(recordIndex.RecordToQualityNumber))
	return nil
}

// 获取品控系统数据（使用品控单列表API）
func (p *QualityControlProcessor) getQualityControlData(qualityNumbers []string) (map[string]*ProcessedQualityControlItem, error) {
	logging.InfoLogger.Infof("获取品控数据，品控单号数量: %d", len(qualityNumbers))

	// 创建品控API实例
	api := NewQualityControlAPI()

	// 使用品控单列表API获取处理后的数据
	// 由于API支持分页，我们需要批量获取所有需要的品控单
	result := make(map[string]*ProcessedQualityControlItem)

	// 分批获取品控单数据
	batchSize := 50 // 每批获取50条
	for i := 0; i < len(qualityNumbers); i += batchSize {
		end := i + batchSize
		if end > len(qualityNumbers) {
			end = len(qualityNumbers)
		}

		batch := qualityNumbers[i:end]
		logging.InfoLogger.Infof("获取第 %d-%d 条品控数据", i+1, end)

		// 对于每个品控单号，单独调用API获取数据
		for _, qualityNumber := range batch {
			// 使用品控单列表API，传入具体的ID
			processedItems, err := api.GetProcessedQualityControlList(1, 1, qualityNumber)
			if err != nil {
				logging.ErrorLogger.Errorf("获取品控单 %s 失败: %v", qualityNumber, err)
				continue
			}

			// 如果找到数据，添加到结果中
			if len(processedItems) > 0 {
				item := &processedItems[0]
				result[qualityNumber] = item
			}
		}
	}

	logging.InfoLogger.Infof("成功获取品控数据，记录数: %d", len(result))
	return result, nil
}

// 获取闭环管理数据
func (p *QualityControlProcessor) getClosedLoopData(qualityNumbers []string) (map[string]map[int]*ClosedLoopResponse, error) {
	logging.InfoLogger.Infof("获取闭环数据，品控单号数量: %d", len(qualityNumbers))

	// 首先需要获取品控数据以判断是否需要闭环
	qualityData, err := p.getQualityControlData(qualityNumbers)
	if err != nil {
		return nil, fmt.Errorf("获取品控数据失败: %v", err)
	}

	// 转换为QualityControlItem数组（从ProcessedQualityControlItem中提取原始数据）
	qualityItems := make([]QualityControlItem, 0, len(qualityData))
	for _, item := range qualityData {
		qualityItems = append(qualityItems, item.QualityControlItem)
	}

	// 创建品控API实例以获取Cookie
	api := NewQualityControlAPI()

	// 获取基础URL和Cookies（使用新的API方法）
	baseURL := api.GetBaseURL()
	cookies := api.GetCookies()

	// 验证登录状态（通过检查cookies是否存在）
	if len(cookies) == 0 {
		return nil, fmt.Errorf("品控系统未登录或Cookie已过期")
	}

	// 创建闭环管理实例
	closedLoopManager := NewClosedLoopManager(baseURL, cookies)

	// 批量获取闭环数据
	closedLoopData, err := closedLoopManager.BatchFetchClosedLoopData(qualityItems)
	if err != nil {
		return nil, fmt.Errorf("批量获取闭环数据失败: %v", err)
	}

	logging.InfoLogger.Infof("成功获取闭环数据，记录数: %d", len(closedLoopData))
	return closedLoopData, nil
}

// 映射品控数据到飞书字段（使用处理后的数据）
func (p *QualityControlProcessor) mapQualityDataToFeishu(
	qualityItem *ProcessedQualityControlItem,
	updateData map[string]interface{},
	result *SyncResult) error {

	// 品控责任人（需要检查飞书原始数据来决定是否更新）
	if mapping, exists := p.fieldMapping["品控责任人"]; exists {
		// 标记需要特殊处理品控责任人字段
		updateData[mapping.FeishuField] = qualityItem.QualityResponsiblePerson
		result.UpdatedFields = append(result.UpdatedFields, mapping.FeishuField)
		logging.InfoLogger.Infof("品控责任人字段已添加到更新数据: %s ", qualityItem.QualityResponsiblePerson)
	}

	// 品控问题是否关闭（直接使用已处理的字段）
	if mapping, exists := p.fieldMapping["品控问题是否关闭"]; exists {
		if qualityItem.IsQualityProblemClosed != "" {
			updateData[mapping.FeishuField] = qualityItem.IsQualityProblemClosed
			result.UpdatedFields = append(result.UpdatedFields, mapping.FeishuField)
		}
	}

	// 超期情况（直接使用已处理的字段）
	if mapping, exists := p.fieldMapping["超期情况"]; exists {
		if qualityItem.OverdueStatus != "" {
			updateData[mapping.FeishuField] = qualityItem.OverdueStatus
			result.UpdatedFields = append(result.UpdatedFields, mapping.FeishuField)
		}
	}

	// 问题时效（直接使用已处理的字段）
	if mapping, exists := p.fieldMapping["问题时效"]; exists {
		if qualityItem.ProblemTimeliness != "" {
			updateData[mapping.FeishuField] = qualityItem.ProblemTimeliness
			result.UpdatedFields = append(result.UpdatedFields, mapping.FeishuField)
		}
	}

	// 是否提供解决方案（基于品控状态和类别的智能判断）
	if mapping, exists := p.fieldMapping["是否提供解决方案"]; exists {
		solutionStatus, err := p.getSolutionStatus(
			fmt.Sprintf("%d", qualityItem.ID),
			qualityItem.PzkzStatus,
			qualityItem.PzkzTypeName,
			qualityItem.IsQualityProblemClosed)
		if err != nil {
			logging.ErrorLogger.Errorf("获取品控单号 %d 解决方案状态失败: %v", qualityItem.ID, err)
			// 失败时使用默认值
			solutionStatus = mapping.DefaultValue
		}
		updateData[mapping.FeishuField] = solutionStatus
		result.UpdatedFields = append(result.UpdatedFields, mapping.FeishuField)
		logging.InfoLogger.Infof("品控单号 %d 解决方案状态: %s (基于品控状态 %s 和类别 %s 的智能判断)",
			qualityItem.ID, solutionStatus, qualityItem.PzkzStatus, qualityItem.PzkzTypeName)
	}

	return nil
}

// 映射闭环管理数据到飞书字段
func (p *QualityControlProcessor) mapClosedLoopDataToFeishu(
	closedItems map[int]*ClosedLoopResponse,
	updateData map[string]interface{},
	result *SyncResult,
	faultLevel string,
	devFaultReason string,
	pzkzStatus string) error {

	// 根据故障等级、研发故障原因和品控状态判断闭环状态
	closedStatus := p.determineClosedLoopStatus(faultLevel, devFaultReason, pzkzStatus, closedItems)

	// 品控问题是否闭环
	if mapping, exists := p.fieldMapping["品控问题是否闭环"]; exists {
		// 将复杂的闭环状态映射为飞书的三个选项
		feishuStatus := p.convertToFeishuClosedStatus(closedStatus)
		updateData[mapping.FeishuField] = feishuStatus
		result.UpdatedFields = append(result.UpdatedFields, mapping.FeishuField)
		result.Message += fmt.Sprintf("; 闭环状态：%s -> %s", closedStatus, feishuStatus)
	}

	return nil
}

// 更新飞书记录（优化版本：使用预先缓存的状态映射）
func (p *QualityControlProcessor) updateFeishuRecord(recordID string, updateData map[string]interface{}, qualityResponsiblePersonStatusMap map[string]bool) error {
	// 获取配置
	qualityControlAppToken := libs.Config.FeiShuDoc.QualityControlAppToken
	qualityControlTableID := libs.Config.FeiShuDoc.QualityControlTableID

	if qualityControlAppToken == "" || qualityControlTableID == "" {
		return fmt.Errorf("品控系统配置不完整")
	}

	// 转换字段值为飞书API格式（优化：使用预先缓存的状态映射，完全消除API调用）
	processedData, err := p.processFieldValuesForFeishuOptimized(updateData, recordID, qualityResponsiblePersonStatusMap)
	if err != nil {
		logging.ErrorLogger.Errorf("字段值处理失败: %v", err)
		return fmt.Errorf("字段值处理失败: %v", err)
	}

	// 使用现有的UpdateTableRecordByID函数进行更新
	resp, err := UpdateTableRecordByID(qualityControlTableID, qualityControlAppToken, recordID, processedData)
	if err != nil {
		logging.ErrorLogger.Errorf("飞书记录更新失败: %v", err)
		return fmt.Errorf("飞书记录更新失败: %v", err)
	}

	// 检查响应状态（使用最新错误码格式化）
	if !resp.Success() {
		// 使用统一的错误格式化函数
		errorMsg := formatFeishuAPIError(resp.Code, resp.Msg, resp.RequestId())
		logging.ErrorLogger.Error(errorMsg)
		return fmt.Errorf(errorMsg)
	}

	logging.InfoLogger.Infof("飞书记录更新成功: %s，更新字段数: %d", recordID, len(processedData))
	return nil
}

// 处理字段值为飞书API格式（优化版本：使用预先缓存的状态映射，完全消除API调用）
func (p *QualityControlProcessor) processFieldValuesForFeishuOptimized(updateData map[string]interface{}, recordID string, qualityResponsiblePersonStatusMap map[string]bool) (map[string]interface{}, error) {
	processedData := make(map[string]interface{})

	for fieldName, value := range updateData {
		if fieldName == "品控责任人" {
			// 特殊处理品控责任人字段：使用预先缓存的状态映射
			processedValue, err := p.processQualityResponsiblePersonFieldOptimized(value, recordID, qualityResponsiblePersonStatusMap)
			if err != nil {
				logging.ErrorLogger.Errorf("转换字段 %s 失败: %v", fieldName, err)
				continue // 跳过错误字段，继续处理其他字段
			}
			// 如果返回nil，表示跳过此字段的更新
			if processedValue != nil {
				processedData[fieldName] = processedValue
			}
		} else {
			// 其他字段使用原有逻辑
			processedValue, err := p.convertFieldValueForFeishuOriginal(fieldName, value)
			if err != nil {
				logging.ErrorLogger.Errorf("转换字段 %s 失败: %v", fieldName, err)
				continue // 跳过错误字段，继续处理其他字段
			}
			processedData[fieldName] = processedValue
		}
	}

	return processedData, nil
}

// 处理品控责任人字段的优化逻辑（使用预先缓存的状态映射，完全消除API调用）
func (p *QualityControlProcessor) processQualityResponsiblePersonFieldOptimized(value interface{}, recordID string, qualityResponsiblePersonStatusMap map[string]bool) (interface{}, error) {
	const defaultUserID = "ou_0bb538b58f9b736d5d0bcb3d98b0fde7"

	logging.InfoLogger.Infof("优化处理品控责任人字段（使用预先缓存）: recordID=%s, value=%v", recordID, value)

	// 检查是否已经是飞书API格式
	if userArray, ok := value.([]map[string]interface{}); ok {
		logging.InfoLogger.Infof("品控责任人字段已经是正确格式，直接返回: %+v", userArray)
		return value, nil
	}

	// 修复：直接尝试用户名转换，避免不必要的状态检查
	logging.InfoLogger.Infof("开始处理品控责任人字段，待转换值: %v", value)

	// 首先尝试转换品控系统中的用户名
	result, err := p.convertUserFieldWithoutDefault(value)
	if err == nil {
		// 转换成功，使用转换后的用户ID
		return result, nil
	}

	// 用户名转换失败，此时才检查飞书原字段状态以决定处理方式
	logging.InfoLogger.Infof("用户名转换失败，检查飞书原字段状态以决定处理方式: %v", err)

	// 获取预先缓存的状态映射（仅在转换失败时使用）
	isOriginalFeishuEmpty, exists := qualityResponsiblePersonStatusMap[recordID]
	if !exists {
		logging.ErrorLogger.Errorf("记录 %s 在品控责任人状态映射中不存在", recordID)
		// 如果映射中不存在，使用默认用户ID
		logging.InfoLogger.Infof("映射中不存在记录，使用默认用户ID: %s", defaultUserID)
		return []map[string]interface{}{
			{"id": defaultUserID},
		}, nil
	}

	// 根据飞书原字段状态决定是否使用默认值
	if isOriginalFeishuEmpty {
		// 转换失败且飞书原字段为空，使用默认用户ID
		logging.InfoLogger.Infof("转换失败且飞书原字段为空，使用默认用户ID: %s", defaultUserID)
		return []map[string]interface{}{
			{"id": defaultUserID},
		}, nil
	} else {
		// 转换失败但飞书原字段不为空，跳过更新以保持原有值
		logging.InfoLogger.Infof("转换失败但飞书原字段不为空，跳过更新以保持原有值: recordID=%s", recordID)
		return nil, nil
	}
}

// 转换单个字段值为飞书API格式（用于非品控责任人字段）
func (p *QualityControlProcessor) convertFieldValueForFeishuOriginal(fieldName string, value interface{}) (interface{}, error) {
	if value == nil {
		return nil, nil
	}

	switch fieldName {
	case "品控问题是否关闭":
		// SingleSelect类型字段
		return p.convertSingleSelectField(fieldName, value)
	case "品控问题是否闭环":
		// SingleSelect类型字段
		return p.convertSingleSelectField(fieldName, value)
	case "是否提供解决方案":
		// SingleSelect类型字段
		return p.convertSingleSelectField(fieldName, value)
	case "超期情况", "问题时效":
		// Text类型字段
		return p.convertTextField(value)
	default:
		// 其他字段类型的处理
		logging.InfoLogger.Infof("处理其他类型字段: %s = %v", fieldName, value)
		return value, nil
	}
}

// 转换用户字段
func (p *QualityControlProcessor) convertUserFieldWithoutDefault(value interface{}) (interface{}, error) {
	if value == nil {
		return nil, fmt.Errorf("用户字段值为nil")
	}

	logging.InfoLogger.Infof("转换用户字段（无默认值），原始类型: %T, 原始值: %+v", value, value)

	// 严格类型检查，只接受支持的类型
	switch v := value.(type) {
	case string:
		return p.processStringUserFieldWithoutDefault(v)
	case []map[string]interface{}:
		return p.validateFeishuAPIFormat(v)
	default:
		// 严格拒绝不支持的类型
		err := fmt.Errorf("不支持的用户字段类型: %T, 期望string或[]map[string]interface{}, 实际值: %+v", v, v)
		logging.ErrorLogger.Errorf("用户字段类型检查失败: %v", err)
		// 统计类型错误
		if p.statistics != nil {
			p.statistics.TypeErrors++
		}
		return nil, err
	}
}

// 处理字符串类型的用户字段（不使用默认值）
func (p *QualityControlProcessor) processStringUserFieldWithoutDefault(strValue string) (interface{}, error) {
	if strValue == "" {
		return nil, fmt.Errorf("用户字段为空字符串")
	}

	logging.InfoLogger.Infof("处理字符串用户字段（无默认值）: %s", strValue)

	// 如果已经是用户ID格式（以ou_开头），直接返回飞书API要求的格式
	if strings.HasPrefix(strValue, "ou_") {
		result := []map[string]interface{}{
			{"id": strValue},
		}
		logging.InfoLogger.Infof("用户ID格式转换结果: %+v", result)
		return result, nil
	}

	// 如果是用户名，通过数据库查询转换为飞书用户ID（支持多种匹配策略）
	feishuUserID, err := p.getUserFeishuIDByName(strValue)
	if err != nil {
		logging.ErrorLogger.Errorf("获取用户 %s 的飞书ID失败: %v", strValue, err)
		logging.ErrorLogger.Errorf("用户名匹配失败详情: 原始用户名='%s'", strValue)
		return nil, fmt.Errorf("无法获取用户 %s 的飞书ID: %v", strValue, err)
	}

	if feishuUserID == "" {
		logging.ErrorLogger.Errorf("用户 %s 没有对应的飞书ID", strValue)
		return nil, fmt.Errorf("用户 %s 没有对应的飞书ID", strValue)
	}

	// 返回飞书API要求的用户字段格式
	result := []map[string]interface{}{
		{"id": feishuUserID},
	}
	logging.InfoLogger.Infof("用户名转换结果: %s -> %+v", strValue, result)
	return result, nil
}

// 验证飞书API格式的有效性
func (p *QualityControlProcessor) validateFeishuAPIFormat(userArray []map[string]interface{}) (interface{}, error) {
	if len(userArray) == 0 {
		return nil, fmt.Errorf("飞书API格式数组为空")
	}

	// 检查第一个元素是否包含id字段
	if _, exists := userArray[0]["id"]; !exists {
		return nil, fmt.Errorf("飞书API格式缺少id字段: %+v", userArray[0])
	}

	// 验证id字段是否为有效的飞书用户ID
	if userID, ok := userArray[0]["id"].(string); ok {
		if userID == "" {
			return nil, fmt.Errorf("飞书API格式id字段为空")
		}
		if !strings.HasPrefix(userID, "ou_") {
			logging.ErrorLogger.Errorf("飞书API格式id字段格式异常: %s", userID)
		}
		logging.InfoLogger.Infof("飞书API格式验证通过: %+v", userArray)
		return userArray, nil
	} else {
		return nil, fmt.Errorf("飞书API格式id字段类型错误: %T", userArray[0]["id"])
	}
}

// 转换单选字段
func (p *QualityControlProcessor) convertSingleSelectField(fieldName string, value interface{}) (interface{}, error) {
	strValue := fmt.Sprintf("%v", value)
	if strValue == "" {
		return nil, nil
	}

	// 根据字段名称和值返回对应的选项
	switch fieldName {
	case "品控问题是否关闭":
		switch strValue {
		case "是", "已关闭", "关闭":
			return "是", nil
		case "否", "未关闭", "开放":
			return "否", nil
		default:
			return "否", nil // 默认值
		}

	case "品控问题是否闭环":
		switch strValue {
		case "已闭环", "是":
			return "已闭环", nil
		case "未闭环", "否":
			return "未闭环", nil
		case "无需闭环":
			return "无需闭环", nil
		default:
			return "未闭环", nil // 默认值
		}

	case "是否提供解决方案":
		switch strValue {
		case "是", "1", "true", "已提供", "完成":
			return "是", nil
		case "否", "0", "false", "未提供", "未完成":
			return "否", nil
		default:
			return "否", nil // 默认值
		}
	}

	return strValue, nil
}

// 转换文本字段
func (p *QualityControlProcessor) convertTextField(value interface{}) (interface{}, error) {
	return fmt.Sprintf("%v", value), nil
}

// 获取同步结果
func (p *QualityControlProcessor) GetSyncResults() []SyncResult {
	return p.results
}

// 判断闭环状态（基于故障等级和闭环数据）
func (p *QualityControlProcessor) determineClosedLoopStatus(faultLevel, devFaultReason, pzkzStatus string, closedItems map[int]*ClosedLoopResponse) string {
	logging.InfoLogger.Infof("判断闭环状态 - 故障等级: %s, 研发故障原因: %s, 品控状态: %s", faultLevel, devFaultReason, pzkzStatus)

	// 首先检查品控工单状态，废弃的工单无需闭环
	if pzkzStatus == "废弃" {
		logging.InfoLogger.Infof("品控工单已废弃，无需闭环")
		return "无需闭环"
	}

	switch faultLevel {
	case "四级故障":
		// 四级故障：通常为轻微问题，一般无需闭环
		logging.InfoLogger.Infof("四级故障无需闭环: 研发故障原因=%s", devFaultReason)
		return "无需闭环"

	case "三级故障":
		// 三级故障：只有软件问题或硬件问题需要闭环
		if devFaultReason != "软件问题" && devFaultReason != "硬件问题" {
			logging.InfoLogger.Infof("三级故障无需闭环: 研发故障原因=%s", devFaultReason)
			return "无需闭环"
		}

		// 只有需要闭环的情况才检查闭环数据
		logging.InfoLogger.Infof("三级故障需要闭环，检查技术根因: 研发故障原因=%s", devFaultReason)
		if closedData, exists := closedItems[1]; exists && closedData != nil {
			if closedData.Data.ClosedStatus == "已闭环" {
				return "已闭环"
			}
			if closedData.Data.ClosedStatus == "无需闭环" {
				return "无需闭环"
			}
		}
		return "需要填写技术根因"

	case "二级故障":
		// 二级故障：除了客户原因和环境问题，其他都需要闭环
		if devFaultReason == "客户原因" || devFaultReason == "环境问题" {
			logging.InfoLogger.Infof("二级故障无需闭环: 研发故障原因=%s", devFaultReason)
			return "无需闭环"
		}

		// 只有需要闭环的情况才检查四个根因
		logging.InfoLogger.Infof("二级故障需要闭环，检查四个根因: 研发故障原因=%s", devFaultReason)
		return p.checkFourRootCauses(closedItems)

	case "一级故障":
		// 一级故障：都需要闭环
		logging.InfoLogger.Infof("一级故障需要闭环，检查四个根因")
		return p.checkFourRootCauses(closedItems)

	default:
		logging.ErrorLogger.Errorf("未知故障等级: %s", faultLevel)
		return "无需闭环"
	}
}

// 检查四个根因是否都已闭环
func (p *QualityControlProcessor) checkFourRootCauses(closedItems map[int]*ClosedLoopResponse) string {
	requiredTypes := []int{1, 2, 3, 4} // 四个根因类型
	closedCount := 0

	for _, rootCauseType := range requiredTypes {
		if closedData, exists := closedItems[rootCauseType]; exists && closedData != nil {
			// 检查各种状态字段
			if closedData.Data.ClosedStatus == "已闭环" || closedData.Data.ClosedStatus == "无需闭环" {
				closedCount++
			}
		}
	}

	if closedCount == len(requiredTypes) {
		return "四个根因已闭环"
	} else if closedCount > 0 {
		return fmt.Sprintf("部分根因已闭环(%d/%d)", closedCount, len(requiredTypes))
	} else {
		return "需要填写四个根因"
	}
}

// 将复杂的闭环状态转换为飞书的三个选项
func (p *QualityControlProcessor) convertToFeishuClosedStatus(closedStatus string) string {
	logging.InfoLogger.Infof("转换闭环状态: %s", closedStatus)

	switch {
	case strings.Contains(closedStatus, "无需闭环"):
		return "无需闭环"
	case strings.Contains(closedStatus, "已闭环"):
		// 包括"技术根因已闭环"、"四个根因已闭环"等
		return "已闭环"
	case strings.Contains(closedStatus, "部分根因已闭环"):
		// 部分闭环算作未闭环
		return "未闭环"
	case strings.Contains(closedStatus, "需要填写"):
		// 包括"需要填写技术根因"、"需要填写四个根因"等
		return "未闭环"
	default:
		// 默认情况，包括"未知故障等级"等
		return "未闭环"
	}
}

// 设置默认的闭环状态（当没有闭环数据时）
func (p *QualityControlProcessor) setDefaultClosedLoopStatus(updateData map[string]interface{}, result *SyncResult, faultLevel, devFaultReason string) {
	if mapping, exists := p.fieldMapping["品控问题是否闭环"]; exists {
		// 根据业务规则判断默认状态
		var defaultStatus string

		switch faultLevel {
		case "四级故障":
			// 四级故障：通常为轻微问题，一般无需闭环
			defaultStatus = "无需闭环"
		case "三级故障":
			if devFaultReason == "软件问题" || devFaultReason == "硬件问题" {
				defaultStatus = "未闭环" // 需要闭环但没有数据
			} else {
				// 业务规则：无需闭环
				defaultStatus = "无需闭环"
			}
		case "二级故障":
			if devFaultReason == "客户原因" || devFaultReason == "环境问题" {
				// 业务规则：无需闭环
				defaultStatus = "无需闭环"
			} else {
				defaultStatus = "未闭环" // 需要闭环但没有数据
			}
		case "一级故障":
			defaultStatus = "未闭环" // 需要闭环但没有数据
		default:
			defaultStatus = "未闭环" // 默认未闭环
		}

		updateData[mapping.FeishuField] = defaultStatus
		result.UpdatedFields = append(result.UpdatedFields, mapping.FeishuField)

		// 根据业务规则添加说明
		var statusExplanation string
		if defaultStatus == "已闭环" && ((faultLevel == "三级故障" && devFaultReason != "软件问题" && devFaultReason != "硬件问题") ||
			(faultLevel == "二级故障" && (devFaultReason == "客户原因" || devFaultReason == "环境问题"))) {
			statusExplanation = " (无需闭环，视为已闭环)"
		} else {
			statusExplanation = " (默认)"
		}

		result.Message += fmt.Sprintf("; 闭环状态：%s%s", defaultStatus, statusExplanation)

		logging.InfoLogger.Infof("设置默认闭环状态: %s%s (故障等级: %s, 研发故障原因: %s)",
			defaultStatus, statusExplanation, faultLevel, devFaultReason)
	}
}

// 通过用户姓名获取飞书用户ID（支持多种匹配策略和缓存）
func (p *QualityControlProcessor) getUserFeishuIDByName(userName string) (string, error) {
	if userName == "" {
		return "", fmt.Errorf("用户名不能为空")
	}

	// 检查缓存
	if cachedID, exists := p.userCache[userName]; exists {
		logging.InfoLogger.Infof("从缓存获取用户飞书ID: %s -> %s", userName, cachedID)
		return cachedID, nil
	}

	logging.InfoLogger.Infof("开始查找用户飞书ID: %s", userName)

	// 策略1: 完全匹配
	_, openID, err := p.findUserByExactMatch(userName)
	if err == nil && openID != "" {
		logging.InfoLogger.Infof("完全匹配成功: %s -> %s", userName, openID)
		p.userCache[userName] = openID // 缓存结果
		return openID, nil
	}
	logging.InfoLogger.Infof("完全匹配失败: %v", err)

	// 策略2: 前缀匹配（提取中文姓名部分）
	chineseName := p.extractChineseName(userName)
	if chineseName != userName && chineseName != "" {
		logging.InfoLogger.Infof("尝试前缀匹配，提取的中文姓名: %s", chineseName)
		_, openID, err = p.findUserByExactMatch(chineseName)
		if err == nil && openID != "" {
			logging.InfoLogger.Infof("前缀匹配成功: %s -> %s -> %s", userName, chineseName, openID)
			p.userCache[userName] = openID // 缓存结果
			return openID, nil
		}
		logging.InfoLogger.Infof("前缀匹配失败: %v", err)
	}

	// 策略3: 模糊匹配（去除特殊字符）
	cleanName := p.cleanUserName(userName)
	if cleanName != userName && cleanName != "" {
		logging.InfoLogger.Infof("尝试模糊匹配，清理后的用户名: %s", cleanName)
		_, openID, err = p.findUserByExactMatch(cleanName)
		if err == nil && openID != "" {
			logging.InfoLogger.Infof("模糊匹配成功: %s -> %s -> %s", userName, cleanName, openID)
			p.userCache[userName] = openID // 缓存结果
			return openID, nil
		}
		logging.InfoLogger.Infof("模糊匹配失败: %v", err)
	}

	// 策略4: 部分匹配（LIKE查询）
	_, openID, err = p.findUserByPartialMatch(userName)
	if err == nil && openID != "" {
		logging.InfoLogger.Infof("部分匹配成功: %s -> %s", userName, openID)
		p.userCache[userName] = openID // 缓存结果
		return openID, nil
	}
	logging.InfoLogger.Infof("部分匹配失败: %v", err)

	// 所有策略都失败，统计用户匹配失败
	if p.statistics != nil {
		p.statistics.UserMatchFailures++
	}
	return "", fmt.Errorf("无法找到用户 '%s' 的飞书ID，已尝试多种匹配策略", userName)
}

// 精确匹配用户（通过name或username字段）
func (p *QualityControlProcessor) findUserByExactMatch(userName string) (uint, string, error) {
	logging.InfoLogger.Infof("精确匹配用户: %s", userName)

	// 首先尝试通过name字段查找用户
	user := duser.User{}
	err := user.FindEx("name", userName)
	if err != nil {
		logging.InfoLogger.Infof("通过name字段查找失败: %v", err)
	}

	if user.ID == 0 {
		return 0, "", fmt.Errorf("用户 %s 不存在（ID为0）", userName)
	}

	// 查找用户对应的飞书OpenID
	userOpenID := duseropenid.Response{}
	err = userOpenID.FindEx("user_id", fmt.Sprintf("%d", user.ID))
	if err != nil {
		logging.ErrorLogger.Errorf("查找用户 %s (ID: %d) 的飞书OpenID失败: %v", userName, user.ID, err)
		return user.ID, "", fmt.Errorf("用户 %s 没有关联的飞书OpenID", userName)
	}

	if userOpenID.ID == 0 || userOpenID.Openid == "" {
		logging.ErrorLogger.Errorf("用户 %s (ID: %d) 的飞书OpenID无效: OpenID_ID=%d, Openid=%s",
			userName, user.ID, userOpenID.ID, userOpenID.Openid)
		return user.ID, "", fmt.Errorf("用户 %s 没有有效的飞书OpenID", userName)
	}

	logging.InfoLogger.Infof("精确匹配成功: %s -> UserID=%d -> OpenID=%s", userName, user.ID, userOpenID.Openid)
	return user.ID, userOpenID.Openid, nil
}

// 提取中文姓名部分（去除英文昵称）
func (p *QualityControlProcessor) extractChineseName(userName string) string {
	if userName == "" {
		return ""
	}

	// 按空格分割，取第一部分（中文姓名）
	parts := strings.Fields(userName)
	if len(parts) > 0 {
		chinesePart := strings.TrimSpace(parts[0])
		// 验证是否包含中文字符
		if p.containsChinese(chinesePart) {
			return chinesePart
		}
	}

	return userName
}

// 清理用户名（去除特殊字符和多余空格）
func (p *QualityControlProcessor) cleanUserName(userName string) string {
	if userName == "" {
		return ""
	}

	// 去除前后空格
	cleaned := strings.TrimSpace(userName)

	// 将多个连续空格替换为单个空格
	cleaned = strings.Join(strings.Fields(cleaned), " ")

	// 去除常见的特殊字符（保留中文、英文、数字、空格）
	var result strings.Builder
	for _, r := range cleaned {
		if (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') ||
			(r >= '0' && r <= '9') || r == ' ' ||
			(r >= 0x4e00 && r <= 0x9fff) { // 中文字符范围
			result.WriteRune(r)
		}
	}

	return strings.TrimSpace(result.String())
}

// 检查字符串是否包含中文字符
func (p *QualityControlProcessor) containsChinese(text string) bool {
	for _, r := range text {
		if r >= 0x4e00 && r <= 0x9fff {
			return true
		}
	}
	return false
}

// 部分匹配用户（使用LIKE查询）
func (p *QualityControlProcessor) findUserByPartialMatch(userName string) (uint, string, error) {
	logging.InfoLogger.Infof("部分匹配用户: %s", userName)

	// 提取中文姓名部分用于模糊查询
	chineseName := p.extractChineseName(userName)
	if chineseName == "" {
		logging.ErrorLogger.Errorf("无法提取有效的中文姓名: %s", userName)
		return 0, "", fmt.Errorf("无法提取有效的中文姓名: %s", userName)
	}

	logging.InfoLogger.Infof("提取的中文姓名: %s", chineseName)

	// 使用LIKE查询查找包含中文姓名的用户
	users := []duser.User{}
	db := easygorm.GetEasyGormDb().Model(&duser.User{})

	// 尝试name字段的模糊匹配
	likePattern := fmt.Sprintf("%%%s%%", chineseName)
	logging.InfoLogger.Infof("使用LIKE模式查询name字段: %s", likePattern)
	err := db.Where("name LIKE ?", likePattern).Find(&users).Error
	if err != nil {
		logging.ErrorLogger.Errorf("name字段LIKE查询失败: %v", err)
		return 0, "", fmt.Errorf("查询用户失败: %v", err)
	}

	logging.InfoLogger.Infof("name字段LIKE查询结果: 找到%d个用户", len(users))

	// 如果name字段没找到，尝试username字段
	if len(users) == 0 {
		logging.InfoLogger.Infof("使用LIKE模式查询username字段: %s", likePattern)
		err = db.Where("username LIKE ?", likePattern).Find(&users).Error
		if err != nil {
			logging.ErrorLogger.Errorf("username字段LIKE查询失败: %v", err)
			return 0, "", fmt.Errorf("查询用户失败: %v", err)
		}
		logging.InfoLogger.Infof("username字段LIKE查询结果: 找到%d个用户", len(users))
	}

	if len(users) == 0 {
		logging.ErrorLogger.Errorf("LIKE查询未找到任何用户，搜索模式: %s", likePattern)
		return 0, "", fmt.Errorf("未找到包含 '%s' 的用户", chineseName)
	}

	// 记录找到的所有用户
	for i, user := range users {
		logging.InfoLogger.Infof("找到的用户%d: ID=%d, Name=%s, Username=%s", i+1, user.ID, user.Name, user.Username)
	}

	// 如果找到多个用户，选择最匹配的（名称最短的，通常是最精确的）
	var bestUser *duser.User
	minLength := int(^uint(0) >> 1) // 最大int值

	for i := range users {
		user := &users[i]
		nameLength := len(user.Name)
		if nameLength < minLength {
			minLength = nameLength
			bestUser = user
		}
	}

	if bestUser == nil || bestUser.ID == 0 {
		return 0, "", fmt.Errorf("未找到有效用户")
	}

	logging.InfoLogger.Infof("选择最佳匹配用户: ID=%d, Name=%s", bestUser.ID, bestUser.Name)

	// 查找用户对应的飞书OpenID
	userOpenID := duseropenid.Response{}
	err = userOpenID.FindEx("user_id", fmt.Sprintf("%d", bestUser.ID))
	if err != nil {
		logging.ErrorLogger.Errorf("查找用户 %s (ID: %d) 的飞书OpenID失败: %v", bestUser.Name, bestUser.ID, err)
		return bestUser.ID, "", fmt.Errorf("用户 %s 没有关联的飞书OpenID", bestUser.Name)
	}

	if userOpenID.ID == 0 || userOpenID.Openid == "" {
		logging.ErrorLogger.Errorf("用户 %s (ID: %d) 的飞书OpenID无效: OpenID_ID=%d, Openid=%s",
			bestUser.Name, bestUser.ID, userOpenID.ID, userOpenID.Openid)
		return bestUser.ID, "", fmt.Errorf("用户 %s 没有有效的飞书OpenID", bestUser.Name)
	}

	logging.InfoLogger.Infof("部分匹配成功: %s -> %s (ID: %d) -> OpenID=%s", userName, bestUser.Name, bestUser.ID, userOpenID.Openid)
	return bestUser.ID, userOpenID.Openid, nil
}
