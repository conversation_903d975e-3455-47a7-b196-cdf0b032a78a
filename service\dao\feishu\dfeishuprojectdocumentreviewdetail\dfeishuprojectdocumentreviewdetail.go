package dfeishuprojectdocumentreviewdetail

import (
	"fmt"
	"reflect"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/feishu"
	"irisAdminApi/application/models/release"
	"irisAdminApi/service/dao/release/dproject"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "项目文档评审详情表"

type FeishuProjectDocumentReviewdetailResponse struct {
	PmsDocID                  uint64  `gorm:"type:bigint(20) unsigned;notNull" json:"pms_doc_id"`
	ProjectName               string  `gorm:"type:varchar(200);notNull" json:"project_name"`
	DocumentName              string  `gorm:"type:varchar(200);notNull" json:"document_name" `
	FileToken                 string  `gorm:"type:varchar(200);notNull" json:"file_token"`
	FileType                  string  `gorm:"type:varchar(200);notNull" json:"file_type" `
	CommentID                 uint64  `gorm:"type:bigint(20) unsigned;notNull" json:"comment_id"`
	CommentCreatedTime        uint64  `gorm:"type:bigint(20);notNull" json:"comment_created_time"`
	CommentUserID             string  `gorm:"type:varchar(100);notNull" json:"comment_user_id" `
	Quote                     string  `gorm:"type:text" json:"quote"`
	IsSolved                  bool    `gorm:"type:tinyint(1);notNull" json:"is_solved"`
	SolverUserID              string  `gorm:"type:varchar(200)" json:"solver_user_id"`
	CommentContent            string  `gorm:"type:longtext" json:"comment_content"`
	CommentType               string  `gorm:"type:varchar(200);notNull" json:"comment_type"`
	Requirement               string  `gorm:"type:varchar(200)" json:"requirement"`
	WorkPacketName            string  `gorm:"type:varchar(200)" json:"work_packet_name"`
	TotalCodes                float32 `gorm:"not null" json:"total_codes"`
	PacketManagerName         string  `gorm:"type:varchar(200)" json:"packet_manager_name"`
	PacketManagerID           string  `gorm:"type:varchar(200)" json:"packet_manager_id"`
	IsSubmit                  uint    `gorm:"not null" json:"is_submit" update:"1"`                        //入库状态 0 未入库 1 已入库
	DocumentCategoryTitle     string  `gorm:"type:varchar(100)" json:"document_category_title" update:"1"` //文档类型
	MeetingStartTime          uint64  `gorm:"type:bigint(20);notNull" json:"meeting_start_time"`           //会议开始时间
	MeetingEndTime            uint64  `gorm:"type:bigint(20);notNull" json:"meeting_end_time"`             //会议结束时间
	MeetingTopic              string  `gorm:"type:varchar(100)" json:"meeting_topic" `                     //会议主题
	MeetingNO                 string  `gorm:"type:varchar(100)" json:"meeting_no" `                        //会议号
	MeetingReviewType         string  `gorm:"type:varchar(100)" json:"meeting_review_type" `               //评审归属类型
	RecordID                  string  `gorm:"type:varchar(200)" json:"record_id" `
	CommentUserDepartmentName string  `gorm:"type:varchar(100)" json:"comment_user_department_name" ` //评委所在部门
	DocumentUrl               string  `gorm:"type:varchar(200)" json:"document_url" `
	DocumentOwnerName         string  `gorm:"type:varchar(200)" json:"document_owner_name" `
	DocumentOwnerID           string  `gorm:"type:varchar(200)" json:"document_owner_id"`
}

type UnConfirmedInformation struct {
	PmsDocID              uint64 `json:"pms_doc_id"`
	ProjectName           string `json:"project_name"`
	DocumentName          string `json:"document_name" `
	DocumentUrl           string `json:"document_url" `
	CommentUserID         string `json:"comment_user_id" `
	DocumentCategoryTitle string `json:"document_category_title"`
}

type FeishuProjectDocumentReviewdetail struct {
	feishu.FeishuProjectDocumentReviewdetail
}

type ListResponse struct {
	FeishuProjectDocumentReviewdetail
}

type Request struct {
	Id uint `json:"id"`
}

func (a *FeishuProjectDocumentReviewdetail) ModelName() string {
	return ModelName
}

func Model() *feishu.FeishuProjectDocumentReviewdetail {
	return &feishu.FeishuProjectDocumentReviewdetail{}
}

func (a *FeishuProjectDocumentReviewdetail) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (a *FeishuProjectDocumentReviewdetail) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (a *FeishuProjectDocumentReviewdetail) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (a *FeishuProjectDocumentReviewdetail) CreateV2(object interface{}) error {
	return nil
}

func (a *FeishuProjectDocumentReviewdetail) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (u *FeishuProjectDocumentReviewdetail) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (u *FeishuProjectDocumentReviewdetail) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *FeishuProjectDocumentReviewdetail) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *FeishuProjectDocumentReviewdetail) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *FeishuProjectDocumentReviewdetail) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func CreateOrUpdateProjectDocumentReviewdetailData(projectName string) error {
	ReSetAutoIncrement()
	//获取项目信息
	projects := dproject.Response{}
	err := easygorm.GetEasyGormDb().Model(&release.ReleaseProject{}).Where("name=?", projectName).Order("name desc").Find(&projects).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return err
	}
	items := []*FeishuProjectDocumentReviewdetailResponse{}
	sql := fmt.Sprintf(`
		SELECT
			fpdc.pms_doc_id,
			fpdc.project_name,
			fpdc.document_name,
			fpdc.file_token,
			fpdc.file_type,
			fpdc.comment_id,
			fpdc.comment_user_id,
			fpdc.comment_created_time,
			fpdc.quote,
			fpdc.is_solved,
			fpdc.solver_user_id,
			fpdc.comment_content,
			fpdc.comment_type,
			fpd.requirement,
			fpd.work_packet_name,
			fpd.total_codes,
			fpd.packet_manager_name,
			fpd.packet_manager_id,
			fpd.document_owner_name,
			fpd.document_owner_id,
			fpd.is_submit,
			fpd.document_category_title,
			fpd.document_url,
			d.name as comment_user_department_name
		FROM
			feishu_project_document_comments fpdc 
			left join feishu_project_documents fpd on  fpdc.pms_doc_id =fpd.pms_doc_id
			left join users u on  fpdc.comment_user_id = u.open_id 
			left join user_departments ud ON ud.user_id = u.id
			left join departments d ON d.id = ud.department_id
		WHERE
		    fpdc.project_name = "%s";`, projectName)
	err = easygorm.GetEasyGormDb().Table("feishu_project_documents").Raw(sql).Scan(&items).Error
	if err != nil {
		return err
	}

	objects := []map[string]interface{}{}
	for _, item := range items {

		object := map[string]interface{}{
			"PmsDocID":                  item.PmsDocID,
			"ProjectName":               item.ProjectName,
			"DocumentName":              item.DocumentName,
			"FileToken":                 item.FileToken,
			"FileType":                  item.FileType,
			"CommentID":                 item.CommentID,
			"CommentCreatedTime":        item.CommentCreatedTime,
			"CommentUserID":             item.CommentUserID,
			"Quote":                     item.Quote,
			"IsSolved":                  item.IsSolved,
			"SolverUserID":              item.SolverUserID,
			"CommentContent":            item.CommentContent,
			"CommentType":               item.CommentType,
			"Requirement":               item.Requirement,
			"WorkPacketName":            item.WorkPacketName,
			"TotalCodes":                item.TotalCodes,
			"PacketManagerName":         item.PacketManagerName,
			"PacketManagerID":           item.PacketManagerID,
			"IsSubmit":                  item.IsSubmit,
			"DocumentCategoryTitle":     item.DocumentCategoryTitle,
			"CommentUserDepartmentName": item.CommentUserDepartmentName,
			"DocumentUrl":               item.DocumentUrl,
			"DocumentOwnerName":         item.DocumentOwnerName,
			"DocumentOwnerID":           item.DocumentOwnerID,

			"CreatedAt": time.Now(),
			"UpdatedAt": time.Now(),
		}
		objects = append(objects, object)
	}

	columns := []string{"updated_at"}

	xt := reflect.TypeOf(Model())
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")
		if ok && ok2 {
			columns = append(columns, name)
		}
	}
	db := easygorm.GetEasyGormDb()
	err = db.Transaction(func(tx *gorm.DB) error {
		if len(objects) > 0 {
			err := tx.Model(Model()).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "pms_doc_id"}, {Name: "project_name"}, {Name: "comment_id"}},
				DoUpdates: clause.AssignmentColumns(columns),
			}).Create(&objects).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil

}

func GetInsertDataByProjectName(projectName string) ([]*FeishuProjectDocumentReviewdetail, error) {
	var items []*FeishuProjectDocumentReviewdetail
	if err := easygorm.GetEasyGormDb().Model(Model()).Where("project_name =? and record_id=''", projectName).Find(&items).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return items, err
	}
	return items, nil
}

func GetUpdateDataByProjectName(projectName string) ([]*FeishuProjectDocumentReviewdetail, error) {
	var items []*FeishuProjectDocumentReviewdetail
	if err := easygorm.GetEasyGormDb().Model(Model()).
		Where("project_name =? and record_id!=''", projectName).
		Find(&items).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return items, err
	}
	return items, nil
}

func GetDocumentReviewCommentByPMSDocID(pmsDocID uint64) ([]FeishuProjectDocumentReviewdetail, error) {
	var objects []FeishuProjectDocumentReviewdetail
	err := easygorm.GetEasyGormDb().Model(Model()).Where("pms_doc_id = ?", pmsDocID).Find(&objects).Error
	if err != nil {
		return nil, err
	}
	return objects, nil
}

func UpdateByID(id uint, object map[string]interface{}) error {
	if err := easygorm.GetEasyGormDb().Model(Model()).Where("id =?", id).Updates(object).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return err
	}
	return nil
}

func GetDocumentReviewCommentByPMSDocIDAndJudgeID(pmsDocID uint, judgeID string) ([]FeishuProjectDocumentReviewdetail, error) {
	var objects []FeishuProjectDocumentReviewdetail
	err := easygorm.GetEasyGormDb().Model(Model()).Where("pms_doc_id = ? and comment_user_id=?", pmsDocID, judgeID).Find(&objects).Error
	if err != nil {
		return nil, err
	}
	return objects, nil
}

func GetDocumentReviewCommentByCommentID(commentID string) (FeishuProjectDocumentReviewdetail, error) {
	var items FeishuProjectDocumentReviewdetail
	if err := easygorm.GetEasyGormDb().Model(Model()).
		Where("comment_id =? ", commentID).
		Find(&items).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return items, err
	}
	return items, nil
}

func FindCommentByPMSDocIDAndJudgeID(pmsDocID uint, judgeID string) (FeishuProjectDocumentReviewdetail, error) {
	var objects FeishuProjectDocumentReviewdetail
	err := easygorm.GetEasyGormDb().Model(Model()).Where("pms_doc_id = ? and comment_user_id=?", pmsDocID, judgeID).Find(&objects).Error
	if err != nil {
		return objects, err
	}
	return objects, nil
}

func ReSetAutoIncrement() error {
	var maxID int
	err := easygorm.GetEasyGormDb().Table("feishu_project_document_reviewdetails").Select("MAX(id)").Scan(&maxID).Error
	if err != nil {
		return err
	}
	newAutoIncrement := maxID + 1
	alterTableSQL := fmt.Sprintf("ALTER TABLE feishu_project_document_reviewdetails AUTO_INCREMENT=%d", newAutoIncrement)
	if err := easygorm.GetEasyGormDb().Exec(alterTableSQL).Error; err != nil {
		logging.ErrorLogger.Errorf("ALTER TABLE feishu_project_document_reviewdetails  get err ", err)
		return err
	}
	return nil
}

func GetUnConfirmedInformation() ([]*UnConfirmedInformation, error) {
	items := []*UnConfirmedInformation{}
	sql := fmt.Sprintf(`
		SELECT
		project_name,
		comment_user_id,
		document_name,
		document_url,
		pms_doc_id,
		document_category_title
	FROM
		feishu_project_document_reviewdetails fpdr
	WHERE
		comment_type = '未确认' OR is_solved = 0
	group by
		project_name,
		comment_user_id,
		document_name,
		document_url,
		pms_doc_id,
		document_category_title;`)
	err := easygorm.GetEasyGormDb().Table("feishu_project_document_reviewdetails").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func ISWorkDay() (bool, error) {
	today := time.Now().Format("2006-01-02")
	var isWorkDay int
	err := easygorm.GetEasyGormDb().Table("feishu_project_document_reviewdetails").
		Raw("SELECT is_work_day FROM workdays WHERE date = ?", today).Scan(&isWorkDay).Error
	if err != nil {
		return false, err
	}
	return isWorkDay == 1, nil
}
