package dqualityuserpoint

import (
	"fmt"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/qualitypoint"
)

const ModelName = "用户积分表"

type Response struct {
	ID              uint    `json:"id"`
	UpdatedAt       string  `json:"updated_at"`
	CreatedAt       string  `json:"created_at"`
	UserID          uint    `json:"user_id"`          //用户ID
	TotalPoints     float32 `json:"total_points"`     //当前用户的总积分，初始为12分
	Year            uint    `json:"year"`             //当前积分的年度
	PointsDeducted  float32 `json:"points_deducted"`  //当年已扣减积分（包括往年未消分的累计扣分）
	PointsRecovered float32 `json:"points_recovered"` //当年已消分积分（恢复的积分总数）
}

type ListResponse struct {
	Response
}

type Request struct {
	Name string `json:"name"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *qualitypoint.QualityUserPoint {
	return &qualitypoint.QualityUserPoint{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (a *Response) FindUserPoint(userId, year uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("user_id=? and year=?", userId, year).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func FindUserPointInfo(userId uint) (Response, error) {
	currentYear := time.Now().Year()
	var userPoint Response
	err := easygorm.GetEasyGormDb().Model(Model()).Where("user_id=? and year=?", userId, currentYear).Find(&userPoint).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return userPoint, err
	}
	return userPoint, nil
}

type UserPointData struct {
	UserID               int     `json:"user_id"`
	Openid               string  `json:"openid"`
	TotalPoints          float32 `json:"total_points"`
	PointsDeducted       float32 `json:"points_deducted"`
	PointsRecovered      float32 `json:"points_recovered"`
	Year                 int     `json:"year"`
	DepartmentName       string  `json:"department_name"`
	ParentDepartmentName string  `json:"parent_department_name"`
}

func GetUserPointData() ([]*UserPointData, error) {
	items := []*UserPointData{}
	sql := `

	SELECT 
    qp.user_id,
    uo.openid,
    qp.total_points,
    qp.points_deducted,
    qp.points_recovered,
    qp.year,
    d.name AS department_name,
    pd.name AS parent_department_name
FROM 
    quality_user_points AS qp
LEFT JOIN 
    user_departments AS ud ON qp.user_id = ud.user_id
LEFT JOIN 
    departments AS d ON ud.department_id = d.id
LEFT JOIN 
    departments AS pd ON d.parent_id = pd.id
LEFT JOIN 
    user_open_ids AS uo ON qp.user_id = uo.user_id
WHERE 
    qp.deleted_at IS NULL 
    AND (uo.openid IS NOT NULL AND uo.openid != '')
    AND (ud.deleted_at IS NULL OR ud.deleted_at IS NULL) 
    AND (d.deleted_at IS NULL OR d.deleted_at IS NULL);`
	err := easygorm.GetEasyGormDb().Table("quality_user_points").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}
