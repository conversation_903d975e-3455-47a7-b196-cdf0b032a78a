package datasync

import (
	"encoding/json"
	"errors"
	"fmt"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/datasync/dpmsprojectmember"
	"irisAdminApi/service/dao/datasync/dsyncrecord"
	"strconv"
	"time"
)

type Body struct {
	Page string `json:"page"`
	Rows string `json:"rows"`
}

func PmsProjectMemberSyncWorker() error {
	// todo: 检查同步记录，获取同步时间，如果没有，从七天前开始，按小时同步

	_url := "https://dataware.ruijie.com.cn/api/public/data-api/safe_product_line_project_member/list.data"
	records, err := dsyncrecord.FindLastSuccessSyncRecordWithoutDate(_url)
	if err != nil {
		logging.ErrorLogger.Errorf("get last sync records", err.Error())
		return err
	}

	page := 1
	rows := 1000

	if len(records) > 0 {
		body := Body{}
		err := json.Unmarshal([]byte(records[0].Body), &body)
		if err != nil {
			logging.ErrorLogger.Errorf("unmarshal body err", err.Error())
		}
		page, err = strconv.Atoi(body.Page)
		if err != nil {
			logging.ErrorLogger.Errorf("page a to i err", err.Error())
		}
		rows, err = strconv.Atoi(body.Rows)
		if err != nil {
			logging.ErrorLogger.Errorf("rows a to i err", err.Error())
		}
	}

	for {
		data := map[string]string{
			"sid":           "YWM4NDkxNDNh",
			"minModifyDate": "",
			"maxModifyDate": "",
			"page":          strconv.Itoa(page),
			"rows":          strconv.Itoa(rows),
		}
		var result dpmsprojectmember.PmsProjectMemberSyncResponse
		var errMsg dpmsprojectmember.PmsProjectMemberSyncResponse
		resp, err := SyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(data).Get(_url)
		if err != nil {
			logging.ErrorLogger.Errorf("get resources error", err.Error())
			return err
		}
		if resp.IsSuccessState() {
			if result.State == "SUCCESS" {
				err := dpmsprojectmember.UpdateOrCreatePmsProjectMemberTransaction(result.Data, _url, data, resp.Request.Method, result.State, result.Message)
				if err != nil {
					logging.ErrorLogger.Errorf("update or create resources error", err.Error())
					return err
				}
			} else {
				logging.ErrorLogger.Errorf("get resources failed", result.State, result.Message)
				return fmt.Errorf("get resources failed, %s, %s", result.State, result.Message)
			}
		} else {
			logging.ErrorLogger.Errorf("get resources unkown error")
			return errors.New("unkown error")
		}

		time.Sleep(5 * time.Second)
		if result.Total > rows*page {
			page++
		} else {
			break
		}
	}

	return nil
}
