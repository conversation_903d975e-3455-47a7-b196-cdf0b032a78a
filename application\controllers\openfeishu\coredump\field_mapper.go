package coredump

import (
	"fmt"
	"strconv"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
)

// FieldMapper 字段映射器
type FieldMapper struct {
	debugMode bool
	stats     *MappingStatistics
}

// MappingStatistics 映射统计信息
type MappingStatistics struct {
	TotalRecords    int `json:"total_records"`
	SuccessRecords  int `json:"success_records"`
	FailedRecords   int `json:"failed_records"`
	FieldErrors     int `json:"field_errors"`
	MissingFields   int `json:"missing_fields"`
	TypeConversions int `json:"type_conversions"`
}

// NewFieldMapper 创建字段映射器
func NewFieldMapper() *FieldMapper {
	return &FieldMapper{
		debugMode: libs.Config.FeiShuDoc.CoredumpDebugMode,
		stats:     &MappingStatistics{},
	}
}

// MapRecords 批量处理飞书记录，转换为CoredumpRecord对象
func (fm *FieldMapper) MapRecords(feishuRecords []map[string]interface{}) ([]*CoredumpRecord, error) {
	if len(feishuRecords) == 0 {
		return []*CoredumpRecord{}, nil
	}

	// 重置统计信息
	fm.stats = &MappingStatistics{
		TotalRecords: len(feishuRecords),
	}

	var coredumpRecords []*CoredumpRecord
	var mappingErrors []string

	if fm.debugMode {
		logging.InfoLogger.Infof("开始批量映射 %d 条飞书记录", len(feishuRecords))
	}

	for i, feishuRecord := range feishuRecords {
		record := &CoredumpRecord{}

		// 映射单条记录
		err := fm.MapFields(feishuRecord, record)
		if err != nil {
			fm.stats.FailedRecords++
			errorMsg := fmt.Sprintf("记录 %d 映射失败: %v", i+1, err)
			mappingErrors = append(mappingErrors, errorMsg)

			if fm.debugMode {
				logging.ErrorLogger.Error(errorMsg)
			}

			// 容错处理：跳过失败的记录，继续处理其他记录
			continue
		}

		// 数据质量检查
		if err := fm.validateRecord(record); err != nil {
			fm.stats.FailedRecords++
			errorMsg := fmt.Sprintf("记录 %d 质量检查失败: %v", i+1, err)
			mappingErrors = append(mappingErrors, errorMsg)

			if fm.debugMode {
				logging.ErrorLogger.Error(errorMsg)
			}

			// 容错处理：跳过质量检查失败的记录
			continue
		}

		coredumpRecords = append(coredumpRecords, record)
		fm.stats.SuccessRecords++

		if fm.debugMode && i < 3 { // 只输出前3条记录的详情
			logging.InfoLogger.Infof("记录 %d 映射成功: SN=%s, 组件=%s, 状态=%s",
				i+1, record.SN, record.Component, record.ProcessingStatus)
		}
	}

	// 输出映射统计信息
	if fm.debugMode {
		logging.InfoLogger.Infof("批量映射完成: 总计=%d, 成功=%d, 失败=%d, 字段错误=%d",
			fm.stats.TotalRecords, fm.stats.SuccessRecords, fm.stats.FailedRecords, fm.stats.FieldErrors)
	}

	// 如果有映射错误但仍有成功记录，返回成功记录和错误信息
	if len(mappingErrors) > 0 && len(coredumpRecords) > 0 {
		return coredumpRecords, fmt.Errorf("部分记录映射失败: %v", mappingErrors)
	}

	// 如果全部失败，返回错误
	if len(coredumpRecords) == 0 {
		return nil, fmt.Errorf("所有记录映射失败: %v", mappingErrors)
	}

	return coredumpRecords, nil
}

// MapFields 映射字段数据
func (fm *FieldMapper) MapFields(fields map[string]interface{}, record *CoredumpRecord) error {
	if fields == nil {
		return fmt.Errorf("字段数据不能为空")
	}

	// 映射记录ID（特殊处理）
	if recordID, ok := fields["record_id"]; ok {
		record.RecordID = fm.extractStringValue(recordID)
	}

	// 映射基础字段
	fm.mapStringField(fields, FieldSN, &record.SN, "SN")
	fm.mapStringField(fields, FieldComponent, &record.Component, "组件")
	fm.mapStringField(fields, FieldSoftwareVersion, &record.SoftwareVersion, "软件版本")
	fm.mapStringField(fields, FieldDeviceModel, &record.DeviceModel, "设备型号")
	fm.mapStringField(fields, FieldDescription, &record.Description, "描述")

	// 映射责任人字段（公式字段，可能返回用户对象数组）
	fm.mapUserField(fields, FieldComponentResponsible, &record.ComponentResponsible, "组件负责人")
	fm.mapUserField(fields, FieldProcessResponsible, &record.ProcessResponsible, "进程负责人")

	// 映射状态字段（单选字段）
	fm.mapSelectField(fields, FieldSyncRequired, &record.SyncRequired, "同步需求")
	fm.mapSelectField(fields, FieldSyncStatus, &record.SyncStatus, "同步状态")

	// 映射其他字段（如果存在）
	fm.mapStringField(fields, FieldProcessingStatus, &record.ProcessingStatus, "处理状态")
	fm.mapStringField(fields, FieldBugID, &record.BugID, "Bug ID")
	fm.mapStringField(fields, FieldErrorMessage, &record.ErrorMessage, "错误信息")

	// 映射时间字段
	fm.mapTimeField(fields, FieldCoredumpTime, &record.CoredumpTime, "Coredump时间")
	fm.mapTimeField(fields, FieldProcessingTime, &record.ProcessingTime, "处理时间")
	fm.mapTimeField(fields, FieldLastUpdated, &record.LastUpdated, "最后更新时间")

	// 映射数值字段
	fm.mapIntField(fields, FieldRetryCount, &record.RetryCount, "重试次数")

	return nil
}

// extractStringValue 提取字符串值
func (fm *FieldMapper) extractStringValue(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case []interface{}:
		if len(v) > 0 {
			if text, ok := v[0].(map[string]interface{})["text"]; ok {
				return fmt.Sprintf("%v", text)
			}
		}
	}

	return fmt.Sprintf("%v", value)
}

// extractTimeValue 提取时间值
func (fm *FieldMapper) extractTimeValue(value interface{}) (time.Time, error) {
	if value == nil {
		return time.Time{}, fmt.Errorf("时间值为空")
	}

	// TODO: 实现时间值提取逻辑
	// 这里先返回当前时间，具体实现在后续阶段完成

	return time.Now(), nil
}

// extractIntValue 提取整数值
func (fm *FieldMapper) extractIntValue(value interface{}) (int, error) {
	if value == nil {
		return 0, fmt.Errorf("整数值为空")
	}

	switch v := value.(type) {
	case int:
		return v, nil
	case float64:
		return int(v), nil
	case string:
		return strconv.Atoi(v)
	default:
		return 0, fmt.Errorf("无法转换为整数: %v", value)
	}
}

// mapStringField 映射字符串字段（带错误统计）
func (fm *FieldMapper) mapStringField(fields map[string]interface{}, fieldName string, target *string, displayName string) {
	if value, ok := fields[fieldName]; ok {
		*target = fm.extractStringValue(value)
		fm.stats.TypeConversions++

		if fm.debugMode && *target == "" {
			logging.InfoLogger.Infof("字段 %s (%s) 为空值", fieldName, displayName)
		}
	} else {
		fm.stats.MissingFields++
		if fm.debugMode {
			logging.InfoLogger.Infof("字段 %s (%s) 缺失", fieldName, displayName)
		}
	}
}

// mapTimeField 映射时间字段（带错误统计）
func (fm *FieldMapper) mapTimeField(fields map[string]interface{}, fieldName string, target *time.Time, displayName string) {
	if value, ok := fields[fieldName]; ok {
		if timeValue, err := fm.extractTimeValue(value); err == nil {
			*target = timeValue
			fm.stats.TypeConversions++
		} else {
			fm.stats.FieldErrors++
			if fm.debugMode {
				logging.ErrorLogger.Errorf("字段 %s (%s) 时间转换失败: %v", fieldName, displayName, err)
			}
		}
	} else {
		fm.stats.MissingFields++
		if fm.debugMode {
			logging.InfoLogger.Infof("字段 %s (%s) 缺失", fieldName, displayName)
		}
	}
}

// mapIntField 映射整数字段（带错误统计）
func (fm *FieldMapper) mapIntField(fields map[string]interface{}, fieldName string, target *int, displayName string) {
	if value, ok := fields[fieldName]; ok {
		if intValue, err := fm.extractIntValue(value); err == nil {
			*target = intValue
			fm.stats.TypeConversions++
		} else {
			fm.stats.FieldErrors++
			if fm.debugMode {
				logging.ErrorLogger.Errorf("字段 %s (%s) 整数转换失败: %v", fieldName, displayName, err)
			}
		}
	} else {
		fm.stats.MissingFields++
		if fm.debugMode {
			logging.InfoLogger.Infof("字段 %s (%s) 缺失", fieldName, displayName)
		}
	}
}

// validateRecord 数据质量检查
func (fm *FieldMapper) validateRecord(record *CoredumpRecord) error {
	var errors []string

	// 检查必要字段
	if record.SN == "" {
		errors = append(errors, "SN不能为空")
	}

	if record.Component == "" {
		errors = append(errors, "组件不能为空")
	}

	if record.SyncRequired == "" {
		errors = append(errors, "同步需求不能为空")
	}

	// 检查状态字段的有效性
	if record.SyncRequired != "" && record.SyncRequired != SyncRequired && record.SyncRequired != SyncNotRequired {
		errors = append(errors, fmt.Sprintf("同步需求值无效: %s", record.SyncRequired))
	}

	if record.ProcessingStatus != "" {
		validStatuses := []string{StatusPending, StatusProcessing, StatusSuccess, StatusFailed}
		isValid := false
		for _, status := range validStatuses {
			if record.ProcessingStatus == status {
				isValid = true
				break
			}
		}
		if !isValid {
			errors = append(errors, fmt.Sprintf("处理状态值无效: %s", record.ProcessingStatus))
		}
	}

	// 检查数据逻辑一致性
	if record.SyncRequired == SyncRequired && record.SyncStatus == SyncCompleted && record.BugID == "" {
		errors = append(errors, "已标记为同步完成但Bug ID为空")
	}

	if len(errors) > 0 {
		return fmt.Errorf("数据质量检查失败: %v", errors)
	}

	return nil
}

// GetMappingStatistics 获取映射统计信息
func (fm *FieldMapper) GetMappingStatistics() *MappingStatistics {
	return fm.stats
}

// mapUserField 映射用户字段（处理公式字段返回的用户对象）
func (fm *FieldMapper) mapUserField(fields map[string]interface{}, fieldName string, target *string, displayName string) {
	if value, ok := fields[fieldName]; ok {
		userValue := fm.extractUserValue(value)
		*target = userValue
		fm.stats.TypeConversions++

		if fm.debugMode && *target == "" {
			logging.InfoLogger.Infof("用户字段 %s (%s) 为空值", fieldName, displayName)
		}
	} else {
		fm.stats.MissingFields++
		if fm.debugMode {
			logging.InfoLogger.Infof("用户字段 %s (%s) 缺失", fieldName, displayName)
		}
	}
}

// mapSelectField 映射单选字段（处理单选字段的选项值）
func (fm *FieldMapper) mapSelectField(fields map[string]interface{}, fieldName string, target *string, displayName string) {
	if value, ok := fields[fieldName]; ok {
		selectValue := fm.extractSelectValue(value)
		*target = selectValue
		fm.stats.TypeConversions++

		if fm.debugMode && *target == "" {
			logging.InfoLogger.Infof("单选字段 %s (%s) 为空值", fieldName, displayName)
		}
	} else {
		fm.stats.MissingFields++
		if fm.debugMode {
			logging.InfoLogger.Infof("单选字段 %s (%s) 缺失", fieldName, displayName)
		}
	}
}

// extractUserValue 提取用户字段值（处理公式字段返回的用户对象数组）
func (fm *FieldMapper) extractUserValue(value interface{}) string {
	if value == nil {
		return ""
	}

	// 处理用户对象数组（公式字段可能返回用户数组）
	if userArray, ok := value.([]interface{}); ok {
		if len(userArray) > 0 {
			if userObj, ok := userArray[0].(map[string]interface{}); ok {
				// 尝试获取用户名称
				if name, ok := userObj["name"].(string); ok {
					return name
				}
				// 尝试获取用户ID
				if id, ok := userObj["id"].(string); ok {
					return id
				}
			}
		}
		return ""
	}

	// 处理单个用户对象
	if userObj, ok := value.(map[string]interface{}); ok {
		if name, ok := userObj["name"].(string); ok {
			return name
		}
		if id, ok := userObj["id"].(string); ok {
			return id
		}
	}

	// 处理字符串值
	return fm.extractStringValue(value)
}

// extractSelectValue 提取单选字段值
func (fm *FieldMapper) extractSelectValue(value interface{}) string {
	if value == nil {
		return ""
	}

	// 处理单选字段对象
	if selectObj, ok := value.(map[string]interface{}); ok {
		// 获取选项名称
		if name, ok := selectObj["name"].(string); ok {
			return name
		}
		// 获取选项ID
		if id, ok := selectObj["id"].(string); ok {
			return id
		}
	}

	// 处理字符串值
	return fm.extractStringValue(value)
}

// ResetStatistics 重置统计信息
func (fm *FieldMapper) ResetStatistics() {
	fm.stats = &MappingStatistics{}
}
