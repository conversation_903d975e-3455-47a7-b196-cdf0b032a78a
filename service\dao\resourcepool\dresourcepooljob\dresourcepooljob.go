package dresourcepooljob

import (
	"errors"
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/resourcepool"

	"gorm.io/gorm"
)

const ModelName = "异步作业记录"

/*
	Description     string `gorm:"not null; type:varchar(512)"`
	ProblemSourceID uint   `gorm:"not null"`
	PlanCloseAt     string `gorm:"not null; type:varchar(30)"`
	UserID          uint   `gorm:"not null"`
	Status          uint   `gorm:"not null；default:0"` //0:未闭环  1:已闭环
	OwnerID         uint   `gorm:"not null"`
*/

type ResourcePoolJob struct {
	resourcepool.ResourcePoolJob
}

type Request struct {
	resourcepool.ResourcePoolJob
}

func (a *ResourcePoolJob) ModelName() string {
	return ModelName
}

func Model() *resourcepool.ResourcePoolJob {
	return &resourcepool.ResourcePoolJob{}
}

func (a *ResourcePoolJob) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ResourcePoolJob

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("mgt_ip like ?", fmt.Sprintf("%%%s%%", name)).Or("reserved like ?", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *ResourcePoolJob) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ResourcePoolJob

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *ResourcePoolJob) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *ResourcePoolJob) CreateV2(object interface{}) error {
	return nil
}

func (a *ResourcePoolJob) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *ResourcePoolJob) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *ResourcePoolJob) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *ResourcePoolJob) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete monitor by id get  err ", err)
		return err
	}
	return nil
}

func (u *ResourcePoolJob) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *ResourcePoolJob) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func All(userId uint, name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ResourcePoolJob

	db := easygorm.GetEasyGormDb().Model(Model())
	where := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		where = where.Where("mgt_ip like ?", fmt.Sprintf("%%%s%%", name)).Or("mgt_port like ?", fmt.Sprintf("%%%s%%", name)).Or("reserved like ? ", fmt.Sprintf("%%%s%%", name)).Or("location like ?", fmt.Sprintf("%%%s%%", name)).Or("status like ?", fmt.Sprintf("%%%s%%", name))
		if name == "禁" || name == "禁用" {
			where = where.Or("flag = 0")
		}
		if name == "启" || name == "启用" {
			where = where.Or("flag = 1")
		}
	}
	if userId == 1 {
		db = db.Where(where)
	} else {
		db = db.Where("user_id = ?", userId).Where(where)
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (u *ResourcePoolJob) LastByName(name string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Last(u, "name = ?", name).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}
