debug: false
loglevel: error # 日志级别 info debug disable error fatal warn 等等
host: ************** # host 地址
port: 8003
nginx:
  host: localhost
  port: 9527
  path: /api
readtimeout: 60
writetimeout: 60
maxsize: 10240 # 文件上传限制
pprof: true #  http://localhost:8086/debug/pprof/heap ...
casbin:
  prefix:
  # path: /data/goprojects/irisAdminApi/rbac_model.conf # go run main.go 运行必须指定路径
  path: /media/sf_code/apis/rbac_model.conf #/projects/iris-admin-api/rbac_model.conf
cache:
  driver: redis # 缓存驱动 local redis
limit:
  disable: false
  limit: 20 # 每秒允许请求 1 次
  burst: 100 # 最高允许并发
admin: # 管理员账号信息，用于数据填充
  username: admin
  rolename: 超级管理员
  name: 超级管理员
  password: password
db:
  adapter: mysql # mysql postgres sqlite3 
  
  #conn: "root:abc.123@tcp(**************:3306)/test?parseTime=True&loc=Local" # postgres://root:password@localhost/iris?sslmode=disable
  conn: "aqyfzx:Aqyfzx20224!@tcp(*************:16033)/fileOut?parseTime=True&loc=Local" # postgres://root:password@localhost/iris?sslmode=disable
  prefix:
  encrypt: false
redis:
  # host: *************
  host: ***********
  port: 6379
  password: password
qiniu:
  enable: false
  host:
  accesskey:
  secretkey:
  bucket:
filestorage:
  # 临时以及最后存在路径win/linux均以/作为分隔符，以/为结尾
  temp: /tmp/temp/ # win/linux均以/作为分隔符，以/为结尾
  upload: /tmp/upload/
# objdump:
# #   enable: true
#   path: /usr/bin/objdump
# filecommand:
#   path: /usr/bin/file
rsa:
  publickey: /media/sf_code/iris-admin-api/id_rsa.pub #/home/<USER>/.ssh/id_rsa.pub
  privatekey: /media/sf_code/iris-admin-api/id_rsa  #/home/<USER>/.ssh/id_rsa

gitlab:
  url: http://************:8080
  version: v4
  # token: sFJsYX5dGsyWs_qixyby   nrBZ74SM8aqxxxKL9WPK
  token: sFJsYX5dGsyWs_qixyby
  enable: true
  isinternal: false

buildfarm:
  logpath: /tmp/  #编译日志路径
  compilepath: /tmp/  #编译项目路径
  archivepath: /tmp/  #编译输出归档路径
  user: buildfarm
  # token: TiYcqp7Lj7A6qnJ4uuwa
  token: sFJsYX5dGsyWs_qixyby
  enable: false


release:
  releasepath: /tmp/
  host: http://**************:8080/release
  dprsysid: 8a2cOLPXVq7YgdofK6FWINSTs9jyr1U0

featurerelease:
  upload: /tmp/
  url: https://*************
  biapiurl: http://bjapi.itnb.cc
  enable: false

checkccode:
  script: /root/doc2vec/check.py
  repository: /home/<USER>/repositorys/

detectionfilestorage:
  temp: /tmp/temp/ # 临时目录
  upload: /tmp/upload/detection/ #上传保存目录
  toolpath: /root/main.dist/ # 检测工具目录
  toolname: main # 检测工具名称
  outputpath: /tmp/upload/detection/output/ #检测工具输出目录
  logpath:  /tmp/upload/detection/log/ #检测工具日志
  envconfig: local

patchfilestorage:
  temp: /tmp/temp/ # 临时目录
  upload: /tmp/upload/patch/ #上传保存目录
  compilepath: /tmp/compile/  #编译项目路径
  archivepath: /tmp/compileoutput/  #编译输出路径
  gitrepo: ssh://git@**************:8022/daimingwei/patch_build.git #工具仓库地址
  branch: master #仓库分支

infoworkfilestorage:
  temp: /tmp/temp/ # 临时目录
  upload: /tmp/infowork/upload/ #上传保存目录
  sdkworksuccessfile: /media/sf_code/iris-admin-api/sdksuccessfile #SDK处理成功标识文件
  hashworksuccessfile: /media/sf_code/iris-admin-api/hashsuccessfile #Hash传输成功标识文件
  toolpath: /media/sf_code/av_tool/ # 文件处理工具目录
  toolname: iwt_format.py # hash程序名称
  toolsdkname: sdk_format.py # sdk程序名称
  sigtoolpath: /media/sf_code/av_tool/ntos_sig_tool_20220323/ #加密工具目录
  outputpath: /tmp/infowork/output/ #检测工具输出目录
  logpath:  /tmp/infowork/log/ #检测工具日志
  deviceurl: *******************************************************************/ #验证设备请求地址
  url: https://************* #安全云上传地址
  serverhost: ************** #远程服务器地址
  serveruser: linjiakai #远程服务器用户名
  serverport: 22 #远程服务器端口
  localsdkpath: /tmp/sdktemp/ #sdk文件存储路径
  localhashpath: /tmp/hashtemp/ #hash文件存储路径
  downloadhost: http://***********:9004/ #包下载Host地址
  enable: true #定时检测开关
  executewaittime: 5 #检测时间间隔(单位分钟)
  uploadretrynum: 3 #上传重试次数
  uploadretrytime: 5 #上传重试等待时间(单位/秒)
  noticemail: <EMAIL> #加工预警提醒(逗号分割)
  testurl: https://************* #安全云上传测试地址

infoworkfilewatch:
 sdkpath:  /media/sf_code/iris-admin-api/temp/ #SDK监控目录
 hashpath: /media/sf_code/iris-admin-api/hashtemp/ #Hash监控目录
 sdkworksuccessfile: /media/sf_code/iris-admin-api/sdksuccessfile #SDK传输成功标识文件
 hashworksuccessfile: /media/sf_code/iris-admin-api/hashsuccessfile #Hash传输成功标识文件
 waitingtime: 1 #等待时间(单位分钟)

productionfilestorage:
  temp: /tmp/temp/ # 临时目录
  upload: /tmp/upload/production/doctemplates/ #上传保存目录
  outputpath: /tmp/upload/production/output/ #检测工具输出目录
  logpath:  /tmp/upload/production/log/ #检测工具日志
  svnuser: suchen1
  svnpass: SuChen12345abc,.
  enable: false

accesslog:
 enable: true
 sites: superset|yangsuite|unifiedtool|grafana
 logpath: /media/sf_code/apis/

feishudoc:
 enable: false
 appid: cli_a6864542233d900e
 appsecret: f9FupAX6aw65B6FLgyvNXbEywHq7keJF
 foldertoken: LSfAfsbqGlaC6Rd0wttc8yLynRd # 目录token
 biapptoken: KZQCbsWefa5e3MsamMccOYYPnMr # 多维表格应用token
 pmsuser: suchen1
 pmspass: SuChen12345abc,.
 plugin: /media/sf_code/apis/plugins
 temp: /tmp/
 outputpath: /tmp/
 template: /tmp/

sig:
  upload: /tmp/uploads
  sigpath: /mnt/sata0/data/sigApi/ntos_sig_tool
  techsupportdecryptpath: /mnt/sata0/data/sigApi/rg_cpkg
  app: out

coredump:
  upload:  /tmp/uploads/coredump
  output:  /tmp/uploads/coredump/output
  host: 127.0.0.1
  port: 19022
  username: root
  plugin: /mnt/sata0/data/coredump/plugins
  remoteworkdir: /tmp/uploads/coredump
  decrypteddir: /tmp/uploads/coredump
  pathheader: /api