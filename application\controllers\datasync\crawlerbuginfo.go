package datasync

import (
	"encoding/json"
	"fmt"
	"io"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"net/http"
	"net/http/cookiejar"
	"net/url"
	"os"
	"regexp"
	"strings"
	"sync"
	"time"

	"irisAdminApi/service/cache"

	"github.com/PuerkitoBio/goquery"
	"github.com/go-rod/rod"
	"github.com/go-rod/rod/lib/launcher"
	"github.com/go-rod/rod/lib/proto"
	"github.com/gomodule/redigo/redis"
	"github.com/kataras/iris/v12"
)

// BugComponent BUG组件信息
type BugComponent struct {
	Name    string `json:"name"`     // 组件名称，如 NTOS-CONTRL-PLANE
	GitRepo string `json:"git_repo"` // Git仓库路径
}

// BugInfo 提取的BUG信息
type BugInfo struct {
	BugID             string         `json:"bug_id"`              // BUG ID
	BugStatus         string         `json:"bug_status"`          // BUG状态
	BugSummary        string         `json:"bug_summary"`         // BUG简介
	ProductName       string         `json:"product_name"`        // 产品名称
	OS                string         `json:"os"`                  // 操作系统
	Severity          string         `json:"severity"`            // 严重性
	Priority          string         `json:"priority"`            // 优先级
	WorkPackage       string         `json:"work_package"`        // 工作包
	RevisionImpact    string         `json:"revision_impact"`     // 修订影响面
	TestVerification  string         `json:"test_verification"`   // 测试验证点
	RootCauseAnalysis string         `json:"root_cause_analysis"` // 根因定位结论
	AnalysisProcess   string         `json:"analysis_process"`    // 分析过程
	Components        []BugComponent `json:"components"`          // BUG组件信息
}

// BugStatusMap BUG状态ID与状态名称的映射关系
var BugStatusMap = map[string]string{
	"2":  "NEW",
	"3":  "ASSIGNED",
	"5":  "RESOLVED",
	"14": "CHECKED",
	"13": "CLOSED-ByDevelopment",
	"7":  "CLOSED-ByTest",
	"23": "REQUEST-REPRODUCE",
	"24": "REQUEST-NO_TO_SOLVE",
	"25": "REQUEST-CHANGE_REQ",
	"6":  "VERIFIED",
	"16": "RENEW",
	"4":  "REOPENED",
	"8":  "DENIAL-ByDevelopment",
	"17": "DENIAL-ByTest",
	"21": "DENIAL-ByCA",
	"22": "GIVEUP",
	"9":  "DELAY",
}

// 定义常量和变量
const (
	BugLoginURL           = "https://sid.ruijie.com.cn/login?service=http:%2F%2Fbugs.ruijie.com.cn%2Fbug_switch%2Fshiro-cas"
	BugDetailURLFormat    = "http://bugs.ruijie.com.cn/bug_switch/bug/bug_info?bugId=%s"
	BugDomain             = "bugs.ruijie.com.cn"
	RedisCookieKey        = "bug_system_cookies" // Redis中存储cookies的key
	RefreshCookieInterval = 20 * time.Minute     // Cookie刷新间隔
)

// 全局cookie管理器
var bugCookieManager *BugSystemCookieManager
var managerOnce sync.Once
var refreshTicker *time.Ticker
var stopRefreshChan chan struct{}

type BugSystemCookieManager struct {
	cookieJar      *cookiejar.Jar // HTTP客户端的cookie jar
	httpClient     *http.Client   // HTTP客户端
	lastLoginTime  time.Time      // 上次登录时间
	cookieValidity time.Duration  // cookie有效期
	mutex          sync.Mutex     // 并发锁
	isLoggedIn     bool           // 是否已登录
}

// GetBugCookieManager 获取单例cookie管理器并启动刷新定时器
func GetBugCookieManager() *BugSystemCookieManager {
	managerOnce.Do(func() {
		// 创建cookie jar
		jar, err := cookiejar.New(nil)
		if err != nil {
			logging.ErrorLogger.Errorf("创建cookie jar失败: %v", err)
			return
		}

		// 创建HTTP客户端
		client := &http.Client{
			Jar: jar,
			CheckRedirect: func(req *http.Request, via []*http.Request) error {
				if strings.Contains(req.URL.String(), "sid.ruijie.com.cn/login") {
					return http.ErrUseLastResponse
				}
				return nil
			},
			Timeout: 30 * time.Second, // 添加超时设置
		}

		bugCookieManager = &BugSystemCookieManager{
			cookieJar:      jar,
			httpClient:     client,
			cookieValidity: 4 * time.Hour, // 默认cookie有效期为4小时
			isLoggedIn:     false,
		}

		// 先尝试从Redis加载cookies
		cookiesLoaded := false
		if err := bugCookieManager.LoadCookies(); err != nil {
			logging.ErrorLogger.Debugf("无法从Redis加载Cookies: %v", err)
		} else {
			cookiesLoaded = true
			logging.ErrorLogger.Debugf("从Redis成功加载Cookies")
		}

		// 如果未能加载cookies，则进行登录获取
		if !cookiesLoaded {
			if err := bugCookieManager.Login(); err != nil {
				logging.ErrorLogger.Error("初始登录失败: %v", err)
			} else {
				logging.ErrorLogger.Debugf("初始登录成功，已保存Cookie到Redis")
			}
		}

		// 启动Cookie刷新定时器
		startCookieRefresher()
	})
	return bugCookieManager
}

// startCookieRefresher 启动Cookie定时刷新器
func startCookieRefresher() {
	// 如果已经启动，先停止之前的
	if refreshTicker != nil && stopRefreshChan != nil {
		// 安全地关闭之前的定时器
		logging.ErrorLogger.Info("检测到已存在的Cookie刷新定时器，先停止")
		select {
		case stopRefreshChan <- struct{}{}:
			// 成功发送停止信号
		default:
			// 通道已满或已关闭，不阻塞
			logging.ErrorLogger.Warn("无法发送停止信号，可能通道已关闭")
		}
		refreshTicker.Stop()
	}

	// 创建新的ticker和停止通道
	refreshTicker = time.NewTicker(RefreshCookieInterval)
	stopRefreshChan = make(chan struct{})

	go func() {
		logging.ErrorLogger.Debugf("启动Cookie刷新定时器，间隔：", RefreshCookieInterval)
		for {
			select {
			case <-refreshTicker.C:
				// 定时刷新Cookie
				if err := bugCookieManager.RefreshCookies(); err != nil {
					logging.ErrorLogger.Errorf("刷新Cookie失败: %v", err)
				}
			case <-stopRefreshChan:
				refreshTicker.Stop()
				logging.ErrorLogger.Debugf("停止Cookie刷新定时器")
				return
			}
		}
	}()
}

// stopCookieRefresher 停止Cookie定时刷新器
func stopCookieRefresher() {
	if refreshTicker != nil && stopRefreshChan != nil {
		// 安全地发送停止信号
		select {
		case stopRefreshChan <- struct{}{}:
			// 成功发送停止信号
			logging.ErrorLogger.Info("已发送Cookie刷新定时器停止信号")
		default:
			// 通道已满或已关闭，不阻塞
			logging.ErrorLogger.Warn("无法发送停止信号，可能通道已关闭")
		}
		refreshTicker.Stop()
		refreshTicker = nil
		stopRefreshChan = nil
		logging.ErrorLogger.Info("已停止Cookie刷新定时器")
	}
}

// RefreshCookies 刷新Cookie，避免频繁登录
func (m *BugSystemCookieManager) RefreshCookies() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 检查是否已登录，如果未登录则直接执行登录操作
	if !m.isLoggedIn {
		logging.ErrorLogger.Info("未登录状态，执行登录获取Cookie")
		return m.Login()
	}

	// 检查是否需要刷新
	timeSinceLastLogin := time.Since(m.lastLoginTime)

	// 如果距离上次登录超过了间隔时间的80%但还没过期，主动刷新
	if timeSinceLastLogin > (m.cookieValidity*80/100) && timeSinceLastLogin < m.cookieValidity {
		logging.ErrorLogger.Info("主动刷新Cookie，距离上次登录时间：", timeSinceLastLogin)
		// 访问一个需要认证的页面来刷新Cookie
		testURL := "http://bugs.ruijie.com.cn/bug_switch/bug/main"
		resp, err := m.httpClient.Get(testURL)
		if err != nil {
			logging.ErrorLogger.Errorf("刷新Cookie请求失败: %v", err)
			return err
		}
		resp.Body.Close()

		// 检查响应状态
		if resp.StatusCode == http.StatusOK {
			// 刷新成功，更新最后登录时间
			m.lastLoginTime = time.Now()
			logging.ErrorLogger.Info("Cookie刷新成功")
			// 保存刷新后的cookies到Redis
			if err := m.SaveCookies(); err != nil {
				logging.ErrorLogger.Error("保存刷新后的cookies到Redis失败: %v", err)
				return err
			}

			return nil
		} else if resp.StatusCode == http.StatusFound && strings.Contains(resp.Header.Get("Location"), "sid.ruijie.com.cn/login") {
			// 需要重新登录
			logging.ErrorLogger.Info("Cookie已过期，需要重新登录")
			m.isLoggedIn = false
			return m.Login()
		} else {
			logging.ErrorLogger.Errorf("刷新Cookie返回意外状态码: %d", resp.StatusCode)
			return fmt.Errorf("刷新Cookie返回意外状态码: %d", resp.StatusCode)
		}
	} else if timeSinceLastLogin >= m.cookieValidity {
		// 已过期，需要重新登录
		logging.ErrorLogger.Info("Cookie已过期，执行登录")
		return m.Login()
	}

	// 无需刷新
	return nil
}

// Login 登录Bug系统并获取cookies
func (m *BugSystemCookieManager) Login() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	logging.ErrorLogger.Info("正在登录Bug系统...")

	// 初始化浏览器（仅用于登录）
	l := launcher.MustNewManaged("")
	browser := rod.New().Client(l.MustClient()).MustConnect()
	defer browser.MustClose() // 确保在函数结束时关闭浏览器

	// 设置页面
	page := setupBugPage(browser)
	defer page.MustClose()

	// 登录Bug系统
	err := logInBugSystem(page, libs.Config.DataSync.PmsUser, libs.Config.DataSync.PmsPass, BugLoginURL)
	if err != nil {
		logging.ErrorLogger.Errorf("登录Bug系统失败: %v", err)
		return err
	}

	// 获取cookies
	cookiesResult, err := proto.NetworkGetAllCookies{}.Call(page)
	if err != nil {
		logging.ErrorLogger.Errorf("获取Cookies失败: %v", err)
		return err
	}

	// 转换为http.Cookie并保存到jar中
	httpCookies := make([]*http.Cookie, 0, len(cookiesResult.Cookies))
	u, _ := url.Parse(BugDetailURLFormat)

	for _, c := range cookiesResult.Cookies {
		httpCookie := &http.Cookie{
			Name:     c.Name,
			Value:    c.Value,
			Path:     c.Path,
			Domain:   c.Domain,
			Expires:  time.Unix(int64(c.Expires), 0),
			Secure:   c.Secure,
			HttpOnly: c.HTTPOnly,
		}
		httpCookies = append(httpCookies, httpCookie)
	}
	// 设置cookies到jar
	m.cookieJar.SetCookies(u, httpCookies)
	m.lastLoginTime = time.Now()
	m.isLoggedIn = true

	// 保存cookies到Redis
	if err := m.SaveCookies(); err != nil {
		logging.ErrorLogger.Errorf("保存cookies失败: %v", err)
	}

	logging.ErrorLogger.Info("成功登录Bug系统并保存cookies")
	return nil
}

// GetBugDetail 获取Bug详情，自动处理登录和cookie过期
func (m *BugSystemCookieManager) GetBugDetail(bugID string) (BugInfo, error) {
	// 创建一个包含BugID的基本BugInfo对象，用于出错时返回
	defaultBugInfo := BugInfo{
		BugID: bugID,
	}

	// 先尝试从Redis加载最新的Cookie
	if err := m.LoadCookies(); err != nil {
		logging.ErrorLogger.Errorf("无法从Redis加载最新Cookie: %v，将使用当前Cookie或尝试登录", err)
		//尝试登录
		if err := m.Login(); err != nil {
			defaultBugInfo.BugSummary = fmt.Sprintf("登录失败: %v", err)
			return defaultBugInfo, err
		}
	} else {
		logging.ErrorLogger.Info("成功从Redis加载最新Cookie")
	}

	// 检查登录状态
	if !m.isLoggedIn || time.Since(m.lastLoginTime) > m.cookieValidity {
		logging.ErrorLogger.Info("Cookie不存在或已过期，重新登录")
		if err := m.Login(); err != nil {
			defaultBugInfo.BugSummary = fmt.Sprintf("登录失败: %v", err)
			return defaultBugInfo, err
		}
	}

	// 使用HTTP客户端直接请求Bug详情页
	bugDetailURL := fmt.Sprintf(BugDetailURLFormat, bugID)
	// 发送HTTP请求
	resp, err := m.httpClient.Get(bugDetailURL)
	if err != nil {
		errMsg := fmt.Sprintf("HTTP请求失败: %v", err)
		logging.ErrorLogger.Error(errMsg)
		defaultBugInfo.BugSummary = errMsg
		return defaultBugInfo, err
	}
	defer resp.Body.Close()

	// 检查状态码
	if resp.StatusCode != http.StatusOK {
		// 检查是否需要重新登录
		if resp.StatusCode == http.StatusFound && strings.Contains(resp.Header.Get("Location"), "sid.ruijie.com.cn/login") {
			m.isLoggedIn = false
			if err := m.Login(); err != nil {
				defaultBugInfo.BugSummary = fmt.Sprintf("重新登录失败: %v", err)
				return defaultBugInfo, err
			}

			// 重新访问Bug详情页
			return m.GetBugDetail(bugID)
		}

		errMsg := fmt.Sprintf("HTTP响应状态异常: %d", resp.StatusCode)
		defaultBugInfo.BugSummary = errMsg
		return defaultBugInfo, fmt.Errorf(errMsg)
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		errMsg := fmt.Sprintf("读取响应体失败: %v", err)
		logging.ErrorLogger.Error(errMsg)
		defaultBugInfo.BugSummary = errMsg
		return defaultBugInfo, err
	}

	// 使用goquery解析HTML
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(string(body)))
	if err != nil {
		errMsg := fmt.Sprintf("解析HTML失败: %v", err)
		logging.ErrorLogger.Error(errMsg)
		defaultBugInfo.BugSummary = errMsg
		return defaultBugInfo, err
	}

	// 检查页面是否包含Bug信息
	if doc.Find("#base").Length() == 0 {
		// 尝试检查是否需要重新登录
		if doc.Find("form[action*='login']").Length() > 0 {
			m.isLoggedIn = false
			if err := m.Login(); err != nil {
				defaultBugInfo.BugSummary = fmt.Sprintf("检测到登录表单，重新登录失败: %v", err)
				return defaultBugInfo, err
			}

			// 重新访问Bug详情页
			return m.GetBugDetail(bugID)
		}

		// 尝试提取页面标题作为bug简介
		title := doc.Find("title").Text()
		if title != "" {
			defaultBugInfo.BugSummary = title
		} else {
			defaultBugInfo.BugSummary = "页面获取不到BUG信息"
		}

		return defaultBugInfo, fmt.Errorf("页面获取不到BUG信息")
	}

	// 使用ExtractBugInfoFromHTML函数提取Bug信息
	bugInfo := ExtractBugInfoFromHTML(doc)
	bugInfo.BugID = bugID // 确保BugID正确设置

	// 获取Bug记录列表，提取分析过程
	records, err := m.GetBugRecords(bugID)
	if err != nil {
		logging.ErrorLogger.Error("获取Bug记录列表失败，无法提取分析过程: %v", err)
	} else {
		// 从记录中提取分析过程
		analysisProcess := ExtractAnalysisProcessFromRecords(records)
		if analysisProcess != "" {
			bugInfo.AnalysisProcess = analysisProcess
		} else {
			logging.ErrorLogger.Errorf("未能从Bug记录中找到分析过程内容")
		}
	}

	return bugInfo, nil
}

// BugSystemCrawler 使用Cookie管理器获取bug系统中的bug详情数据
func BugSystemCrawler(bugID string) (BugInfo, error) {
	// 使用cookie管理器来获取bug详情
	manager := GetBugCookieManager()

	// 先尝试从Redis加载最新的Cookie
	if err := manager.LoadCookies(); err != nil {
		logging.ErrorLogger.Errorf("BugSystemCrawler: 无法从Redis加载最新Cookie: %v，将使用当前Cookie或尝试登录", err)
		// 尝试登录
		if err := manager.Login(); err != nil {
			return BugInfo{}, err
		}
	} else {
		logging.ErrorLogger.Info("BugSystemCrawler: 成功从Redis加载最新Cookie")
	}

	return manager.GetBugDetail(bugID)
}

// 提取输入框的值
func extractInputValue(doc *goquery.Document, selector string) string {
	value := ""
	doc.Find(selector).Each(func(i int, s *goquery.Selection) {
		// 检查是否有值属性
		if val, exists := s.Attr("value"); exists {
			value = val
		}
	})
	return value
}

// 提取带有textbox类的输入值
func extractTextboxValue(doc *goquery.Document, id string) string {
	value := ""
	selector := fmt.Sprintf("#%s", id)
	// 尝试从textbox类型获取值
	doc.Find(selector).Each(func(i int, s *goquery.Selection) {
		if val, exists := s.Attr("value"); exists {
			value = val
		}
	})

	// 如果无法直接获取值，尝试从相关脚本中获取
	if value == "" {
		doc.Find("script").Each(func(i int, s *goquery.Selection) {
			scriptText := s.Text()
			// 使用更简单的正则表达式模式
			pattern := fmt.Sprintf(`%s"\)\.textbox\("setValue","([^"]+)"`, id)
			re := regexp.MustCompile(pattern)
			matches := re.FindStringSubmatch(scriptText)
			if len(matches) > 1 {
				value = matches[1]
			}
		})
	}

	return value
}

// 从combobox获取值
func extractComboBoxValue(doc *goquery.Document, id string) string {
	// 尝试直接从元素获取值
	value := ""
	selector := fmt.Sprintf("#%s", id)
	doc.Find(selector).Each(func(i int, s *goquery.Selection) {
		if val, exists := s.Attr("value"); exists {
			value = val
		}
	})

	// 如果直接获取失败，尝试从脚本中获取
	if value == "" {
		doc.Find("script").Each(func(i int, s *goquery.Selection) {
			scriptText := s.Text()
			// 使用更简单的正则表达式模式
			pattern := fmt.Sprintf(`%s"\)\.combobox\("setValue","([^"]+)"`, id)
			re := regexp.MustCompile(pattern)
			matches := re.FindStringSubmatch(scriptText)
			if len(matches) > 1 {
				value = matches[1]
			}

			// 尝试其他可能的设置模式
			pattern = fmt.Sprintf(`%s"\)\.commonchooser\('setValue',\{[^}]*dictText:'([^']+)'`, id)
			re = regexp.MustCompile(pattern)
			matches = re.FindStringSubmatch(scriptText)
			if len(matches) > 1 {
				value = matches[1]
			}

			// 尝试查找productchooser模式
			pattern = fmt.Sprintf(`%s"\)\.productchooser\('setValue',\{[^}]*productName:'([^']+)'`, id)
			re = regexp.MustCompile(pattern)
			matches = re.FindStringSubmatch(scriptText)
			if len(matches) > 1 {
				value = matches[1]
			}

			// 尝试查找projectchooser模式
			pattern = fmt.Sprintf(`%s"\)\.projectchooser\('setValue',\{[^}]*os:'([^']+)'`, id)
			re = regexp.MustCompile(pattern)
			matches = re.FindStringSubmatch(scriptText)
			if len(matches) > 1 {
				value = matches[1]
			}

			// 尝试查找workpacketchooser模式
			pattern = fmt.Sprintf(`%s"\)\.workpacketchooser\('setValue',\{[^}]*workPacketName\s*:\s*'([^']+)'`, id)
			re = regexp.MustCompile(pattern)
			matches = re.FindStringSubmatch(scriptText)
			if len(matches) > 1 {
				value = matches[1]
			}
		})
	}

	return value
}

// 从textarea获取值
func extractTextAreaValue(doc *goquery.Document, id string) string {
	value := ""
	selector := fmt.Sprintf("#%s", id)
	doc.Find(selector).Each(func(i int, s *goquery.Selection) {
		value = s.Text()
	})

	// 如果直接获取失败，尝试从JavaScript中提取
	if value == "" {
		doc.Find("script").Each(func(i int, s *goquery.Selection) {
			scriptText := s.Text()
			// 使用更简单的正则表达式模式
			pattern := fmt.Sprintf(`'%s'\)\.val\("([^"]+)"`, id)
			re := regexp.MustCompile(pattern)
			matches := re.FindStringSubmatch(scriptText)
			if len(matches) > 1 {
				value = matches[1]
			}
		})
	}

	return strings.TrimSpace(value)
}

// 提取easyui-textbox的值
func extractEasyUITextboxValue(doc *goquery.Document, id string) string {
	value := ""

	// 从初始化脚本中提取
	doc.Find("script").Each(func(i int, s *goquery.Selection) {
		scriptText := s.Text()
		// 使用更简单的正则表达式模式
		pattern := fmt.Sprintf(`%s"\)\.textbox\("setValue","([^"]+)"`, id)
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(scriptText)
		if len(matches) > 1 {
			value = matches[1]
		}
	})

	return value
}

// 提取BUG组件信息
func extractBugComponents(doc *goquery.Document) []BugComponent {
	var components []BugComponent

	// 查找脚本内容中的组件信息 - 只保留明确匹配真实数据的模式
	doc.Find("script").Each(func(i int, s *goquery.Selection) {
		scriptText := s.Text()

		// 使用更精确的正则表达式，只匹配实际的组件数据调用
		re := regexp.MustCompile(`var\s+\w+\s*=\s*comHtml\('([^']+)','([^']+)'\)`)
		matches := re.FindAllStringSubmatch(scriptText, -1)

		for _, match := range matches {
			if len(match) >= 3 {
				// 再次验证组件名和仓库不含特殊字符
				if !strings.Contains(match[1], "+") && !strings.Contains(match[2], "+") {
					component := BugComponent{
						Name:    match[1],
						GitRepo: match[2],
					}
					components = append(components, component)
				}
			}
		}

		// 另一种常见模式，直接使用 comHtml 调用
		re2 := regexp.MustCompile(`comHtml\('([A-Za-z0-9_\-]+)','([^']+)'\)`)
		matches2 := re2.FindAllStringSubmatch(scriptText, -1)

		for _, match := range matches2 {
			if len(match) >= 3 {
				// 验证组件名和仓库不含特殊字符
				if !strings.Contains(match[1], "+") && !strings.Contains(match[2], "+") {
					component := BugComponent{
						Name:    match[1],
						GitRepo: match[2],
					}
					// 防止重复添加
					isDuplicate := false
					for _, existingComp := range components {
						if existingComp.Name == component.Name && existingComp.GitRepo == component.GitRepo {
							isDuplicate = true
							break
						}
					}
					if !isDuplicate {
						components = append(components, component)
					}
				}
			}
		}
	})

	// 从已添加组件信息表格中提取 - 这通常是最可靠的来源
	doc.Find("#addComInfos tbody tr").Each(func(i int, s *goquery.Selection) {
		// 只处理有两个或更多单元格的行
		cells := s.Find("td")
		if cells.Length() >= 2 {
			var name, repo string

			// 获取第一个单元格的文本作为组件名
			name = strings.TrimSpace(cells.Eq(0).Text())
			// 获取第二个单元格的文本作为仓库地址
			repo = strings.TrimSpace(cells.Eq(1).Text())

			// 验证是否是有效的组件数据
			if name != "" && repo != "" &&
				!strings.Contains(name, "+") && !strings.Contains(repo, "+") &&
				!strings.Contains(name, "comInfo") && !strings.Contains(repo, "comPath") {

				component := BugComponent{
					Name:    name,
					GitRepo: repo,
				}

				// 防止重复添加
				isDuplicate := false
				for _, existingComp := range components {
					if existingComp.Name == component.Name && existingComp.GitRepo == component.GitRepo {
						isDuplicate = true
						break
					}
				}
				if !isDuplicate {
					components = append(components, component)
				}
			}
		}
	})

	// 调试信息 - 查看匹配到的原始scriptText内容
	if len(components) == 0 {
		logging.ErrorLogger.Error("没有找到任何组件信息。尝试搜索scriptText中的关键内容...")

		doc.Find("script").Each(func(i int, s *goquery.Selection) {
			scriptText := s.Text()
			if strings.Contains(scriptText, "comHtml") {
				start := strings.Index(scriptText, "comHtml")
				end := start + 200
				if end > len(scriptText) {
					end = len(scriptText)
				}
				logging.ErrorLogger.Errorf("包含comHtml的脚本片段: %s", scriptText[start:end])
			}
		})
	}

	return components
}

// ExtractBugInfoFromHTML 从HTML文档中提取BUG信息
func ExtractBugInfoFromHTML(doc *goquery.Document) BugInfo {
	// 创建BugInfo对象
	bugInfo := BugInfo{}

	// 提取页面标题，作为备选
	pageTitle := doc.Find("title").Text()
	pageTitle = strings.TrimSpace(pageTitle)

	// 提取BUG ID
	idStr := ""
	doc.Find("#base span").Each(func(i int, s *goquery.Selection) {
		text := s.Text()
		if strings.Contains(text, "BUG基本信息：") {
			idStr = strings.TrimPrefix(text, "BUG基本信息：")
		}
	})
	bugInfo.BugID = idStr

	// 提取BUG状态
	doc.Find("script").Each(func(i int, s *goquery.Selection) {
		scriptText := s.Text()
		if strings.Contains(scriptText, `$("#state").combobox("setValue",`) {
			// 使用更简单的正则表达式模式
			re := regexp.MustCompile(`state"\)\.combobox\("setValue","([^"]+)"`)
			matches := re.FindStringSubmatch(scriptText)
			if len(matches) > 1 {
				stateId := matches[1]
				// 根据状态ID查找状态名称
				if stateName, exists := BugStatusMap[stateId]; exists {
					bugInfo.BugStatus = stateName
				} else {
					bugInfo.BugStatus = stateId // 如果找不到映射，保留原始ID
				}
			}
		}
	})

	// 提取BUG简介
	bugInfo.BugSummary = extractEasyUITextboxValue(doc, "summary")

	// 如果BUG简介为空，使用页面标题作为简介
	if bugInfo.BugSummary == "" && pageTitle != "" {
		logging.ErrorLogger.Debugf("BUG简介为空，使用页面标题作为简介: %s", pageTitle)
		bugInfo.BugSummary = pageTitle
	}

	// 提取产品名称
	bugInfo.ProductName = extractComboBoxValue(doc, "product")

	// 提取操作系统
	bugInfo.OS = extractComboBoxValue(doc, "os")

	// 提取严重性
	bugInfo.Severity = extractComboBoxValue(doc, "severity")

	// 提取优先级
	bugInfo.Priority = extractComboBoxValue(doc, "priority")

	// 提取工作包
	bugInfo.WorkPackage = extractComboBoxValue(doc, "wbs")

	// 提取修订影响面
	repairInfluence := extractTextAreaValue(doc, "repairInfluence")
	if repairInfluence == "" {
		// 尝试从脚本中提取
		doc.Find("script").Each(func(i int, s *goquery.Selection) {
			scriptText := s.Text()
			if strings.Contains(scriptText, `$('#repairInfluence').val`) {
				// 使用更简单的正则表达式模式
				re := regexp.MustCompile(`repairInfluence'\)\.val\("([^"]+)"`)
				matches := re.FindStringSubmatch(scriptText)
				if len(matches) > 1 {
					repairInfluence = matches[1]
				}
			}
		})
	}
	bugInfo.RevisionImpact = repairInfluence

	// 提取测试验证点
	testVerification := extractTextAreaValue(doc, "testVerification")
	if testVerification == "" {
		// 尝试从脚本中提取
		doc.Find("script").Each(func(i int, s *goquery.Selection) {
			scriptText := s.Text()
			if strings.Contains(scriptText, `$('#testVerification').val`) {
				// 使用更简单的正则表达式模式
				re := regexp.MustCompile(`testVerification'\)\.val\("([^"]+)"`)
				matches := re.FindStringSubmatch(scriptText)
				if len(matches) > 1 {
					testVerification = matches[1]
				}
			}
		})
	}
	bugInfo.TestVerification = testVerification

	// 提取根因定位结论
	locationResult := extractTextAreaValue(doc, "locationResult")
	if locationResult == "" {
		// 尝试从脚本中提取
		doc.Find("script").Each(func(i int, s *goquery.Selection) {
			scriptText := s.Text()
			if strings.Contains(scriptText, `$('#locationResult').val`) {
				// 使用更简单的正则表达式模式
				re := regexp.MustCompile(`locationResult'\)\.val\("([^"]+)"`)
				matches := re.FindStringSubmatch(scriptText)
				if len(matches) > 1 {
					locationResult = matches[1]
				}
			}
		})
	}
	bugInfo.RootCauseAnalysis = locationResult

	// 提取BUG组件信息
	bugInfo.Components = extractBugComponents(doc)

	return bugInfo
}

// setupBugPage 设置浏览器页面
func setupBugPage(browser *rod.Browser) *rod.Page {
	page := browser.MustPage("about:blank")
	userAgent := "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
	page.MustSetUserAgent(&proto.NetworkSetUserAgentOverride{
		UserAgent:      userAgent,
		AcceptLanguage: "zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7",
	})
	return page
}

// logInBugSystem 登录Bug系统
func logInBugSystem(page *rod.Page, username, password, loginURL string) error {
	// 导航到登录页面
	page.MustNavigate(loginURL)
	page.MustWaitNavigation()
	page.MustWaitStable()

	// 检查用户名和密码是否为空
	if username == "" || password == "" {
		return fmt.Errorf("用户名或密码不能为空")
	}

	// 输入用户名和密码并点击登录按钮
	page.MustElement(`input[name="username"]`).MustInput(username)
	page.MustElement(`input[type="password"]`).MustInput(password)
	page.MustElement(`button[type="submit"]`).MustClick()

	// 等待页面加载完成
	page.MustWaitStable()

	// 检查是否登录成功
	var loginError error
	page.Race().Element("#sessionInfoDiv").MustHandle(func(e *rod.Element) {
		// 登录成功
		loginError = nil
	}).Element(".error-msg").MustHandle(func(e *rod.Element) {
		// 登录失败
		loginError = fmt.Errorf("登录失败: %s", e.MustText())
	}).MustDo()

	return loginError
}

// CrawlerBugInfo 从本地HTML文件中提取BUG信息，用于本地验证
func CrawlerBugInfo(htmlFilePath string) {
	// 如果没有提供文件路径，使用默认路径
	if htmlFilePath == "" {
		htmlFilePath = "/media/sf_code/apis/bug_info.html"
	}

	file, err := os.Open(htmlFilePath)
	if err != nil {
		logging.ErrorLogger.Error("无法打开文件: %v", err)
		return
	}
	defer file.Close()

	doc, err := goquery.NewDocumentFromReader(file)
	if err != nil {
		logging.ErrorLogger.Error("解析HTML失败: %v", err)
		return
	}

	// 使用增强的方法提取完整的BUG信息
	bugInfo := ExtractBugInfoFromHTML(doc)

	// 输出Bug信息
	outputBugInfo(bugInfo)
}

// outputBugInfo 输出Bug信息为JSON
func outputBugInfo(bugInfo BugInfo) {
	// 将BugInfo转为JSON输出
	jsonData, err := json.MarshalIndent(bugInfo, "", "  ")
	if err != nil {
		logging.ErrorLogger.Error("转换JSON失败: %v", err)
		return
	}

	fmt.Println(string(jsonData))

	// 也以简单方式打印字段和值，方便调试
	fmt.Println("\n\n提取字段数据:")
	fields := map[string]string{
		"BUG ID": bugInfo.BugID,
		"BUG状态":  bugInfo.BugStatus,
		"BUG简介":  bugInfo.BugSummary,
		"产品名称":   bugInfo.ProductName,
		"操作系统":   bugInfo.OS,
		"严重性":    bugInfo.Severity,
		"优先级":    bugInfo.Priority,
		"工作包":    bugInfo.WorkPackage,
		"修订影响面":  bugInfo.RevisionImpact,
		"测试验证点":  bugInfo.TestVerification,
		"根因定位结论": bugInfo.RootCauseAnalysis,
		"分析过程":   bugInfo.AnalysisProcess,
	}

	for name, value := range fields {
		fmt.Printf("%s: %s\n", name, value)
	}

	// 输出组件信息
	fmt.Println("\n发现BUG的组件信息:")
	if len(bugInfo.Components) == 0 {
		fmt.Println("未发现组件信息")
	} else {
		for i, component := range bugInfo.Components {
			fmt.Printf("组件 %d:\n", i+1)
			fmt.Printf("  名称: %s\n", component.Name)
			fmt.Printf("  Git仓库: %s\n", component.GitRepo)
		}
	}
}

// SaveCookies 保存cookies到Redis
func (m *BugSystemCookieManager) SaveCookies() error {
	u, _ := url.Parse(BugDetailURLFormat)
	cookies := m.cookieJar.Cookies(u)

	// 将cookies转换为可序列化的格式
	cookieData := make([]map[string]interface{}, len(cookies))
	for i, cookie := range cookies {
		cookieData[i] = map[string]interface{}{
			"name":     cookie.Name,
			"value":    cookie.Value,
			"path":     cookie.Path,
			"domain":   cookie.Domain,
			"expires":  cookie.Expires.Unix(),
			"secure":   cookie.Secure,
			"httponly": cookie.HttpOnly,
		}
	}

	// 将cookies数据转换为JSON
	jsonData, err := json.Marshal(cookieData)
	if err != nil {
		return fmt.Errorf("序列化cookies失败: %v", err)
	}

	// 使用Redis客户端存储cookies
	rc := cache.GetRedisClusterClient()
	_, err = rc.Set(RedisCookieKey, string(jsonData), 4*time.Hour) // 直接传入time.Duration类型
	if err != nil {
		return fmt.Errorf("保存cookies到Redis失败: %v", err)
	}

	logging.ErrorLogger.Info("成功保存cookies到Redis")
	return nil
}

// LoadCookies 从Redis加载cookies
func (m *BugSystemCookieManager) LoadCookies() error {
	// 如果当前已经登录并且距离上次登录时间不长，可以跳过加载
	if m.isLoggedIn && time.Since(m.lastLoginTime) < time.Minute {
		logging.ErrorLogger.Info("当前Cookie仍然有效且刚刚刷新过，跳过从Redis加载")
		return nil
	}

	// 获取Redis客户端
	rc := cache.GetRedisClusterClient()

	// 从Redis获取cookies数据
	data, err := rc.GetKey(RedisCookieKey)
	if err != nil {
		// 如果是键不存在，返回特定错误以触发重新登录
		if strings.Contains(err.Error(), "redis: nil") {
			logging.ErrorLogger.Info("Redis中不存在Cookie数据，需要重新登录获取")
			return fmt.Errorf("Redis中不存在Cookie数据")
		}
		return fmt.Errorf("从Redis获取cookies失败: %v", err)
	}

	// 将Redis返回的接口值转换为字符串
	jsonData, err := redis.String(data, err)
	if err != nil {
		return fmt.Errorf("Redis数据转换失败: %v", err)
	}

	if jsonData == "" {
		logging.ErrorLogger.Info("Redis中Cookie数据为空，需要重新登录获取")
		return fmt.Errorf("Redis中Cookie数据为空")
	}

	// 解析JSON数据
	var cookieData []map[string]interface{}
	if err := json.Unmarshal([]byte(jsonData), &cookieData); err != nil {
		return fmt.Errorf("解析cookie数据失败: %v", err)
	}

	// 检查是否有有效的Cookie数据
	if len(cookieData) == 0 {
		logging.ErrorLogger.Info("Redis中没有有效的Cookie数据，需要重新登录获取")
		return fmt.Errorf("Redis中没有有效的Cookie数据")
	}

	// 检查Redis中Cookie的最后更新时间
	var redisLastUpdateTime time.Time
	for _, data := range cookieData {
		if expires, ok := data["expires"].(float64); ok {
			cookieExpires := time.Unix(int64(expires), 0)
			// 假设Cookie的有效期是固定的，通过减去有效期得到上次更新时间
			updateTime := cookieExpires.Add(-m.cookieValidity)
			if updateTime.After(redisLastUpdateTime) {
				redisLastUpdateTime = updateTime
			}
		}
	}

	// 如果当前已登录，并且本地Cookie比Redis中的Cookie更新，则跳过加载
	if m.isLoggedIn && m.lastLoginTime.After(redisLastUpdateTime) {
		logging.ErrorLogger.Info("当前Cookie比Redis中的更新，保留当前Cookie")
		// 将当前的Cookie保存回Redis
		if err := m.SaveCookies(); err != nil {
			logging.ErrorLogger.Errorf("保存当前Cookie到Redis失败: %v", err)
		}
		return nil
	}

	// 转换为http.Cookie并添加到jar中
	u, _ := url.Parse(BugDetailURLFormat)
	cookies := make([]*http.Cookie, len(cookieData))
	for i, data := range cookieData {
		cookies[i] = &http.Cookie{
			Name:     data["name"].(string),
			Value:    data["value"].(string),
			Path:     data["path"].(string),
			Domain:   data["domain"].(string),
			Expires:  time.Unix(int64(data["expires"].(float64)), 0),
			Secure:   data["secure"].(bool),
			HttpOnly: data["httponly"].(bool),
		}
	}

	// 设置cookies到jar
	m.cookieJar.SetCookies(u, cookies)
	m.lastLoginTime = time.Now()
	m.isLoggedIn = true

	logging.ErrorLogger.Info("成功从Redis加载cookies")
	return nil
}

// GetBugInfoAPI 提供HTTP接口用于查询bug详情
// 支持单个bugID或多个逗号分隔的bugID
func GetBugInfoAPI(ctx iris.Context) {
	// 获取请求参数
	bugIDParam := ctx.URLParam("bugID")
	if bugIDParam == "" {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "缺少必要参数bugID"))
		return
	}

	// 解析bugID参数（支持单个ID或逗号分隔的多个ID）
	bugIDs := strings.Split(bugIDParam, ",")

	// 去重处理
	uniqueBugIDs := make([]string, 0, len(bugIDs))
	uniqueMap := make(map[string]bool)
	for _, id := range bugIDs {
		id = strings.TrimSpace(id)
		if id != "" && !uniqueMap[id] {
			uniqueMap[id] = true
			uniqueBugIDs = append(uniqueBugIDs, id)
		}
	}

	// 初始化结果数组
	results := make([]BugInfo, 0, len(uniqueBugIDs))
	failedIDs := make([]string, 0)

	// 获取cookie管理器（单例）
	manager := GetBugCookieManager()

	// 无论是单个还是多个BUG ID，都使用统一的处理逻辑
	for _, bugID := range uniqueBugIDs {
		// 直接使用manager而不是通过BugSystemCrawler重新获取
		bugInfo, err := manager.GetBugDetail(bugID)
		if err != nil {
			logging.ErrorLogger.Errorf("获取BUG %s 信息失败: %v", bugID, err)
			failedIDs = append(failedIDs, bugID)

			// 即使出错，如果bugInfo中已有BugID和BugSummary，也添加到结果中
			if bugInfo.BugID != "" {
				logging.ErrorLogger.Infof("添加出错但有基本信息的BugInfo: %s - %s", bugInfo.BugID, bugInfo.BugSummary)
				results = append(results, bugInfo)
			}
			continue
		}
		results = append(results, bugInfo)
	}

	// 构建响应信息
	var responseMsg string
	if len(failedIDs) > 0 {
		responseMsg = fmt.Sprintf("部分BUG获取失败: %s", strings.Join(failedIDs, ","))
		ctx.JSON(response.NewResponse(response.NoErr.Code, results, responseMsg))
	} else {
		ctx.JSON(response.NewResponse(response.NoErr.Code, results, response.NoErr.Msg))
	}
}

// 获取Bug记录列表响应结构
type BugRecordResponse struct {
	Rows  []BugRecord `json:"rows"`
	Total int         `json:"total"`
}

// Bug记录结构
type BugRecord struct {
	BugID       int       `json:"bugid"`
	TheDate     time.Time `json:"thedate"`
	Submiter    int       `json:"submiter"`
	ID          int       `json:"ID"`
	UserName    string    `json:"userName"`
	Info        string    `json:"info"`
	ConsumeTime int       `json:"consumetime"`
}

// GetBugRecords 获取Bug记录列表
func (m *BugSystemCookieManager) GetBugRecords(bugID string) ([]BugRecord, error) {
	// 先尝试从Redis加载最新的Cookie
	if err := m.LoadCookies(); err != nil {
		logging.ErrorLogger.Errorf("获取Bug记录前无法从Redis加载最新Cookie: %v，将使用当前Cookie或尝试登录", err)
		// 尝试登录
		if err := m.Login(); err != nil {
			return nil, fmt.Errorf("登录失败: %v", err)
		}
	} else {
		logging.ErrorLogger.Info("获取Bug记录前成功从Redis加载最新Cookie")
	}

	// 确保已登录
	if !m.isLoggedIn || time.Since(m.lastLoginTime) > m.cookieValidity {
		logging.ErrorLogger.Info("获取Bug记录前检查登录状态，Cookie不存在或已过期，重新登录")
		if err := m.Login(); err != nil {
			return nil, fmt.Errorf("登录失败: %v", err)
		}
	}

	// 构建POST请求URL和参数
	requestURL := "http://bugs.ruijie.com.cn/bug_switch/bug/bug_record/load_record_list"
	data := url.Values{}
	data.Set("bugId", bugID)

	// 创建请求
	req, err := http.NewRequest("POST", requestURL, strings.NewReader(data.Encode()))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

	// 发送请求
	resp, err := m.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		// 处理重定向到登录页面的情况
		if resp.StatusCode == http.StatusFound && strings.Contains(resp.Header.Get("Location"), "sid.ruijie.com.cn/login") {
			logging.ErrorLogger.Info("获取Bug记录时Cookie已失效，重定向到登录页，尝试重新登录")
			m.isLoggedIn = false
			if err := m.Login(); err != nil {
				return nil, fmt.Errorf("重新登录失败: %v", err)
			}
			// 重新获取Bug记录
			return m.GetBugRecords(bugID)
		}
		return nil, fmt.Errorf("请求返回错误状态码: %d", resp.StatusCode)
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析JSON响应
	var recordResponse BugRecordResponse
	if err := json.Unmarshal(body, &recordResponse); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return recordResponse.Rows, nil
}

// ExtractAnalysisProcessFromRecords 从Bug记录中提取分析过程
func ExtractAnalysisProcessFromRecords(records []BugRecord) string {
	for _, record := range records {
		// 在info字段中查找分析过程
		if strings.Contains(record.Info, "分析过程:") || strings.Contains(record.Info, "分析过程：") {
			// 提取分析过程部分
			startIndex := -1
			if strings.Contains(record.Info, "分析过程:") {
				startIndex = strings.Index(record.Info, "分析过程:") + len("分析过程:")
			} else if strings.Contains(record.Info, "分析过程：") {
				startIndex = strings.Index(record.Info, "分析过程：") + len("分析过程：")
			}

			if startIndex == -1 {
				continue
			}

			// 查找解决方案的位置作为结束位置
			endIndex := -1
			if strings.Contains(record.Info[startIndex:], "解决方案:") {
				endIndex = startIndex + strings.Index(record.Info[startIndex:], "解决方案:")
			} else if strings.Contains(record.Info[startIndex:], "解决方案：") {
				endIndex = startIndex + strings.Index(record.Info[startIndex:], "解决方案：")
			} else if strings.Contains(record.Info[startIndex:], "<br/>解决方案") {
				endIndex = startIndex + strings.Index(record.Info[startIndex:], "<br/>解决方案")
			}

			if endIndex == -1 {
				// 如果没有找到解决方案，可能是最后一个字段，直接返回剩余内容
				return strings.TrimSpace(record.Info[startIndex:])
			}

			// 提取并返回分析过程内容
			analysisProcess := record.Info[startIndex:endIndex]
			// 清理HTML标签和特殊字符
			analysisProcess = strings.ReplaceAll(analysisProcess, "<br/>", "\n")
			analysisProcess = strings.ReplaceAll(analysisProcess, "&nbsp;", " ")
			return strings.TrimSpace(analysisProcess)
		}
	}

	return ""
}

// 添加Cookie健康检查函数
func checkCookieHealth() {
	// 每30分钟执行一次健康检查
	healthTicker := time.NewTicker(30 * time.Minute)
	stopHealthChan := make(chan struct{})

	go func() {
		for {
			select {
			case <-healthTicker.C:
				// 获取Cookie管理器
				manager := GetBugCookieManager()

				// 检查Cookie是否有效
				if !manager.isLoggedIn {
					logging.ErrorLogger.Warn("健康检查: 未登录状态，尝试登录")
					if err := manager.Login(); err != nil {
						logging.ErrorLogger.Error("健康检查: 登录失败: %v", err)
					}
					continue
				}

				// 验证Cookie有效性
				testURL := "http://bugs.ruijie.com.cn/bug_switch/bug/main"
				resp, err := manager.httpClient.Get(testURL)
				if err != nil {
					logging.ErrorLogger.Error("健康检查: 请求失败: %v", err)
					continue
				}

				// 关闭响应体
				resp.Body.Close()

				// 检查响应状态
				if resp.StatusCode != http.StatusOK {
					if resp.StatusCode == http.StatusFound && strings.Contains(resp.Header.Get("Location"), "sid.ruijie.com.cn/login") {
						logging.ErrorLogger.Error("健康检查: Cookie已失效，需要重新登录")
						manager.isLoggedIn = false
						if err := manager.Login(); err != nil {
							logging.ErrorLogger.Error("健康检查: 重新登录失败: %v", err)
						} else {
							// 登录成功后，确保保存到Redis
							if err := manager.SaveCookies(); err != nil {
								logging.ErrorLogger.Error("健康检查: 保存cookies到Redis失败: %v", err)
							} else {
								logging.ErrorLogger.Info("健康检查: 成功保存cookies到Redis")
							}
						}
					} else {
						logging.ErrorLogger.Error("健康检查: 意外的响应状态: %d", resp.StatusCode)
					}
				} else {
					logging.ErrorLogger.Info("健康检查: Cookie有效")
					// 即使Cookie有效，也主动刷新并保存
					if err := manager.SaveCookies(); err != nil {
						logging.ErrorLogger.Error("健康检查: 保存cookies到Redis失败: %v", err)
					} else {
						logging.ErrorLogger.Info("健康检查: 刷新并保存有效的cookies到Redis")
					}
				}
			case <-stopHealthChan:
				healthTicker.Stop()
				logging.ErrorLogger.Info("停止Cookie健康检查")
				return
			}
		}
	}()
}

// 添加系统预热函数
func WarmupBugSystem() {
	// 确保已获取Cookie
	manager := GetBugCookieManager()
	testURL := "http://bugs.ruijie.com.cn/bug_switch/bug/main"
	resp, err := manager.httpClient.Get(testURL)
	if err != nil {
		logging.ErrorLogger.Error("预热: 请求失败: %v", err)
		return
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode == http.StatusFound && strings.Contains(resp.Header.Get("Location"), "sid.ruijie.com.cn/login") {
			logging.ErrorLogger.Warn("预热: Cookie已失效，需要重新登录")
			manager.isLoggedIn = false
			if err := manager.Login(); err != nil {
				logging.ErrorLogger.Error("预热: 重新登录失败: %v", err)
				return
			}

			// 确保登录后保存cookies到Redis
			if err := manager.SaveCookies(); err != nil {
				logging.ErrorLogger.Error("预热: 保存cookies到Redis失败: %v", err)
			} else {
				logging.ErrorLogger.Info("预热: 已成功保存新的cookies到Redis")
			}

			// 再次验证
			resp, err := manager.httpClient.Get(testURL)
			if err != nil {
				logging.ErrorLogger.Errorf("预热: 二次验证请求失败: %v", err)
				return
			}
			defer resp.Body.Close()

			if resp.StatusCode != http.StatusOK {
				logging.ErrorLogger.Error("预热: 登录后仍无法访问系统，状态码: %d", resp.StatusCode)
				return
			}
		} else {
			logging.ErrorLogger.Error("预热: 意外的响应状态: %d", resp.StatusCode)
			return
		}
	} else {
		// 即使成功访问，也确保cookies保存到Redis
		if err := manager.SaveCookies(); err != nil {
			logging.ErrorLogger.Error("预热: 保存当前有效cookies到Redis失败: %v", err)
		} else {
			logging.ErrorLogger.Info("预热: 已成功保存当前有效cookies到Redis")
		}
	}

	// 尝试获取一个示例BUG，确保系统完全可用
	testBugID := "1353463" // 使用一个可能存在的BUG ID
	_, err = manager.GetBugDetail(testBugID)
	if err != nil {
		logging.ErrorLogger.Error("预热: 获取测试BUG详情失败: %v，这可能是正常的，如果该BUG不存在", err)
	} else {
		logging.ErrorLogger.Info("预热: 成功获取测试BUG详情")
	}
	// 启动健康检查
	go checkCookieHealth()
}
