package gitlab

import (
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/user/dgitlabtoken"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

func GetUserToken(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	token := dgitlabtoken.Response{}
	err = token.FindEx("user_id", strconv.FormatUint(uint64(id), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("get token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if token.Id == 0 {
		token := map[string]string{
			"token": "",
		}
		ctx.JSON(response.NewResponse(response.NoErr.Code, token, response.NoErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, token, response.NoErr.Msg))
	return
}

func CreateUserToken(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	tokenReq := &dgitlabtoken.GitlabTokenReq{}
	if err := ctx.ReadJSON(tokenReq); err != nil {
		logging.ErrorLogger.Errorf("create user token read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	tokenReq.UserId = id
	validErr := libs.Validate.Struct(*tokenReq)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	token := dgitlabtoken.Response{}
	err = token.FindEx("user_id", strconv.FormatUint(uint64(id), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("get token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if token.Id == 0 {
		err = dao.Create(&dgitlabtoken.Response{}, ctx, map[string]interface{}{
			"UserId":    id,
			"Token":     tokenReq.Token,
			"CreatedAt": time.Now(),
			"UpdatedAt": time.Now(),
		})
		if err != nil {
			logging.ErrorLogger.Errorf("create user token get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
	} else {
		err = token.Update(token.Id, map[string]interface{}{
			"UserId":    id,
			"Token":     tokenReq.Token,
			"UpdatedAt": time.Now(),
		})
		if err != nil {
			logging.ErrorLogger.Errorf("create user token get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, tokenReq, response.NoErr.Msg))
	return
}
