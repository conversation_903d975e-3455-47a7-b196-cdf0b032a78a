package dappmergerequestbugsummary

import (
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/appmergerequest"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm/clause"
)

const ModelName = "BUG统计"

type Bug struct {
	BugID         int    `gorm:"primarykey" json:"bug_id"`
	BugSummary    string `gorm:"not null; type:varchar(300)" json:"bug_summary" update:"1"`
	BugOwner      string `gorm:"not null; type:varchar(60)" json:"bug_owner" update:"1"`
	BugOwnerGroup string `gorm:"not null; type:varchar(60)" json:"bug_owner_group" update:"1"`
	BugSubmitter  string `gorm:"not null; type:varchar(60)" json:"bug_submitter" update:"1"`

	BugBelong           string `gorm:"not null; type:varchar(60)" json:"bug_belong" update:"1"`
	BugTestCharger      string `gorm:"not null; type:varchar(60)" json:"bug_test_charger" update:"1"`
	BugTestChargerGroup string `gorm:"not null; type:varchar(60)" json:"bug_test_charger_group" update:"1"`
	BugPstlName         string `gorm:"not null; type:varchar(60)" json:"bug_pstl_name" update:"1"`
	BugGroupName        string `gorm:"not null; type:varchar(60)" json:"bug_group_name" update:"1"`
	BugOS               string `gorm:"not null; type:varchar(60)" json:"bug_os" update:"1"`
	BugProduct          string `gorm:"not null; type:varchar(60)" json:"bug_product" update:"1"`
	BugWorkpacketName   string `gorm:"not null; type:varchar(60)" json:"bug_workpacket_name" update:"1"`
	MainBugID           int    `gorm:"not null" json:"main_bug_id" update:"1"`
	SameBugID           int    `gorm:"not null" json:"same_bug_id" update:"1"`

	BugDisabled bool `gorm:"not null" json:"bug_disabled" update:"1"`

	BugCreatedAt      *time.Time `json:"bug_created_at"`
	BugUpdatedAt      *time.Time `json:"bug_updated_at"`
	BugState          string     `json:"bug_state"`
	BugPriority       string     `gorm:"not null; type:varchar(60)" json:"priority"`
	BugSeverity       string     `grom:"not null; type:varchar(60)" json:"severity"`
	BugRepro          int        `gorm:"not null" json:"repro"` //可重复性 1必现，2有时重现 3未尝试重现 4尝试但未重现
	BugCbdAt          *time.Time `json:"bug_cbd_at"`
	BugCbtAt          *time.Time `json:"bug_cbt_at"`
	BugCreatedAtFix   string     `gorm:"->" json:"bug_created_at_fix"`
	BugSubmitterGroup string     `json:"bug_submitter_group"`

	ApprovalType string `gorm:"->" json:"approval_type"`
	OverTime     int    `json:"over_time"`
	TestStartAt  string `gorm:"->" json:"test_start_at"`
}

type BugSummary struct {
	// BugOs string `json:"bug_os" xlsx:"项目" idx:"1"`
	// BugWorkPacketName  string  `json:"bug_workpacket_name" xlsx:"工作包" idx:"3"`
	Total                  uint    `json:"total" xlsx:"合计提交" idx:"2"`
	NotCbdPercent          float32 `json:"not_cbd_percent" xlsx:"所有BUG未决率" idx:"2"`
	NotCbdCount            uint    `json:"not_cbd_count" xlsx:"未决BUG" idx:"3"`
	CbdCount               uint    `json:"cbd_count" xlsx:"已决BUG" idx:"3"`
	Request                uint    `json:"request" xlsx:"REQUEST" idx:"4"`
	NewAssigned            uint    `json:"new_assigned" xlsx:"New+Assigned" idx:"5"`
	CheckedResolved        uint    `json:"checked_resolved" xlsx:"Checked+Resolved" idx:"6"`
	OvertimeBlockingByTest uint    `json:"overtime_blocking_by_test" xlsx:"测试提Blocking超期未决" idx:"7"`
	OvertimeCriticalByTest uint    `json:"overtime_critical_by_test" xlsx:"测试提Critical超期未决" idx:"8"`
	OvertimeMajorByTest    uint    `json:"overtime_major_by_test" xlsx:"测试提Major超期未决" idx:"9"`
	OvertimeNormalByTest   uint    `json:"overtime_normal_by_test" xlsx:"测试提Normal超期未决" idx:"10"`
	OvertimeByDev          uint    `json:"overtime_by_dev" xlsx:"开发提超期未决" idx:"11"`

	BlockingNotCbd uint `json:"blocking_not_cbd" xlsx:"Blocking未决" idx:"12"`
	CriticalNotCbd uint `json:"critical_not_cbd" xlsx:"Ctritical未决" idx:"13"`
	MajorNotCbd    uint `json:"major_not_cbd" xlsx:"Major未决" idx:"14"`
}

type BugOwnerDelayTop struct {
	BugOs         string `json:"bug_os" xlsx:"项目" idx:"1"`
	BugOwner      string `json:"bug_owner" xlsx:"Bug负责人" idx:"2"`
	BugOwnerGroup string `json:"bug_owner_group" xlsx:"所属工作组" idx:"3"`
	Count         uint   `json:"count" xlsx:"超时BUG数" idx:"4"`
	Total         uint   `json:"total" xlsx:"超时时长" idx:"5"`
	Round         uint   `json:"round" xlsx:"延期次数" idx:"6"`
}

type BugOwnerGroupDelayTop struct {
	BugOs         string `json:"bug_os" xlsx:"项目" idx:"1"`
	BugOwnerGroup string `json:"bug_owner_group" xlsx:"工作组" idx:"2"`
	Count         uint   `json:"count" xlsx:"超时BUG数" idx:"3"`
	Total         uint   `json:"total" xlsx:"超时时长" idx:"4"`
	Round         uint   `json:"round" xlsx:"延期次数" idx:"5"`
}

type WorkDay struct {
	Date      string `json:"date"`
	IsWorkDay bool   `json:"is_work_day"`
}

func (a *BugSummary) ModelName() string {
	return ModelName
}

func Model() *appmergerequest.AppMergeRequest {
	return &appmergerequest.AppMergeRequest{}
}

func (b *Bug) Save() error {
	err := easygorm.GetEasyGormDb().Model(b).Save(b).Error
	if err != nil {
		return err
	}
	return nil
}

var LastUpdateOverTime *time.Time

func UpdateOverTime() error {
	now := time.Now()
	// if LastUpdateOverTime != nil && now.Sub(*LastUpdateOverTime).Minutes() <= 10 {
	// 	return nil
	// }

	workdays := []*WorkDay{}
	err := easygorm.GetEasyGormDb().
		Table("workdays").
		Where(`is_work_day = false`).
		Find(&workdays).Error
	if err != nil {
		return err
	}
	workDayMap := map[string]bool{}
	for _, workday := range workdays {
		workDayMap[workday.Date] = workday.IsWorkDay
	}

	bugs := []*Bug{}
	updateBugs := []*Bug{}
	selects := []string{
		"b.bug_id as bug_id",
		"b.bug_created_at as bug_created_at",
		"b.bug_updated_at as bug_updated_at",
		"b.bug_submitter_group as bug_submitter_group",
		"b.bug_state as bug_state",
		"b.bug_priority as bug_priority",
		"b.bug_severity as bug_severity",
		"b.bug_repro as bug_repro",
		"b.bug_cbd_at as bug_cbd_at",
		"b.bug_cbt_at as bug_cbt_at",
		`IF(a.approval_type ='延期',
			IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_severity = 'Blocking' AND b.bug_repro=1 and main_bug_id = 0,
				DATE_ADD(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), INTERVAL + 1*a.round DAY),
				IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_priority = 'critical' AND b.bug_repro=1 and main_bug_id = 0,
					getWorkDay(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), 3*a.round),
					IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_priority = 'major' AND b.bug_repro=1 and main_bug_id = 0,
						getWorkDay(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), 5*a.round),
						getWorkDay(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), 7*a.round)
					)
				)
			),
		 	null
		) as bug_created_at_fix`,
		"a.approval_type as approval_type",
		"c.test_start_at as test_start_at",
		// `IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_severity = 'Blocking' AND b.bug_repro=1,
		// 	STR_TO_DATE(getWorkDay(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), IF(a.approval_type ='延期', 3*a.round, 3)), "YYYY-MM-DD"),
		// 	IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_severity = 'critical' AND b.bug_repro=1,
		// 		STR_TO_DATE(getWorkDay(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), IF(a.approval_type ='延期', 3*a.round, 3)), "YYYY-MM-DD"),
		// 		IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_severity = 'major' AND b.bug_repro=1,
		// 			STR_TO_DATE(getWorkDay(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), IF(a.approval_type ='延期', 5*a.round, 5)), "YYYY-MM-DD"),
		// 			DATE_ADD(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), INTERVAL + IF(a.approval_type ='延期', 7*a.round, 7) DAY)
		// 		)
		// 	)
		// ) as over_time_at`,
	}
	/*
				getWorkDay(IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at), IF(a.approval_type ='延期', 3*a.round, 3)),
			DATE_ADD(IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at), IF(a.approval_type ='延期', INTERVAL + 7*a.round DAY, INTERVAL + 7 DAY))

		IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_severity = 'critical' AND b.bug_repro=1,
			getWorkDay(IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at), IF(a.approval_type ='延期', 3*a.round, 3)),
			IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_severity = 'major' AND b.bug_repro=1,
				getWorkDay(IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at), IF(a.approval_type ='延期', 5*a.round, 5)),
				DATE_ADD(IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at), IF(a.approval_type ='延期', INTERVAL + 7*a.round DAY, INTERVAL + 7 DAY))
			)
		)
	*/
	// `COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' AND b.bug_severity = 'Blocking' AND b.bug_repro=1 AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -3*a.round)) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -3))), b.bug_id, NULL)) AS  overtime_blocking_by_test`,
	// `COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' AND b.bug_priority = 'critical' AND b.bug_repro=1  AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -3*a.round)) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -3))), b.bug_id, NULL)) AS  overtime_critical_by_test`,
	// `COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' AND b.bug_priority = 'major' AND b.bug_repro=1  AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -5*a.round)) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -5))), b.bug_id, NULL)) AS  overtime_major_by_test`,
	// `COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' AND b.bug_priority = 'Normal' AND b.bug_repro=1  AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) <  DATE_ADD(NOW(), INTERVAL -7*a.round DAY)) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) <  DATE_ADD(NOW(), INTERVAL -7 DAY))), b.bug_id, NULL)) AS  overtime_normal_by_test`,
	// `COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group NOT LIKE '%测试%' AND b.bug_repro=1 AND ((a.approval_type='延期' AND  IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < DATE_ADD(NOW(), INTERVAL -7*a.round DAY)) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < DATE_ADD(NOW(), INTERVAL -7 DAY))), b.bug_id, NULL)) AS  overtime_by_dev`,

	err = easygorm.GetEasyGormDb().
		Table("bugs as b").
		Joins("left join (select max(`round`) as `round`, max(created_at) as created_at, bug_id, `status`, approval_type from bug_approvals GROUP BY bug_id, `status`, approval_type) a on b.bug_id = a.bug_id and a.`status` = 1").
		Joins("left join (select r.id as id, r.name as name, c.test_start_at as test_start_at from release_projects r left join release_project_configs c on r.id = c.release_project_id) c on c.name = b.bug_os").
		Select(selects).
		Where(`b.bug_repro = 1`).
		Find(&bugs).Error

	if err != nil {
		return err
	}

	for _, bug := range bugs {
		var start, end time.Time

		if bug.ApprovalType == "延期" && bug.BugCreatedAtFix != "" {
			bugCreatedAtFix, _ := time.Parse("2006-01-02", bug.BugCreatedAtFix)
			start = bugCreatedAtFix
		} else {
			start = *bug.BugCreatedAt
		}

		if bug.TestStartAt != "" {
			test_start_at, _ := time.Parse("2006-01-02", bug.TestStartAt)
			if start.Compare(test_start_at) <= 0 {
				start = test_start_at
			}
		}

		if strings.HasPrefix(bug.BugState, "CLOSED") {
			if bug.BugCbdAt == nil {
				end = *bug.BugUpdatedAt
			} else {
				end = *bug.BugCbdAt
			}

		} else if strings.HasPrefix(bug.BugState, "DENIAL") || bug.BugState == "DELAY" || bug.BugState == "GIVEUP" {
			end = *bug.BugUpdatedAt
		} else {
			end = time.Now()
		}

		// overTimeAt := start.AddDate(0, 0, limit)
		// bug.OverTimeAt = &overTimeAt
		days := 0
		workDays := 0
		for i := start; i.Compare(end) < 0; i = i.AddDate(0, 0, 1) {
			_, ok := workDayMap[i.Format("2006-01-02")]
			if !ok {
				workDays++
			}
			days++
		}

		limit := 7

		if bug.BugSeverity == "Blocking" && strings.Contains(bug.BugSubmitterGroup, "测试") && bug.MainBugID == 0 && bug.BugRepro == 1 {
			limit = 1
		} else if bug.BugPriority == "critical" && strings.Contains(bug.BugSubmitterGroup, "测试") && bug.MainBugID == 0 && bug.BugRepro == 1 {
			limit = 3
		} else if bug.BugPriority == "major" && strings.Contains(bug.BugSubmitterGroup, "测试") && bug.MainBugID == 0 && bug.BugRepro == 1 {
			limit = 5
		}

		if workDays > limit {
			if limit == 1 {
				bug.OverTime = days - limit - 1 // -1用于修正超期时间提前1天
			} else {
				bug.OverTime = workDays - limit - 1 // -1用于修正超期时间提前1天
			}
		} else {
			bug.OverTime = 0
		}
		// debug用于验证数据准确性
		// if bug.OverTime > 0 {
		// 	fmt.Println(bug.ApprovalType, bug.MainBugID, bug.BugPriority, bug.BugSeverity, bug.BugSubmitterGroup, limit, days, workDays, bug.OverTime, start, end, bug.TestStartAt, bug.BugCreatedAtFix, bug.BugID)
		// }
		updateBugs = append(updateBugs, bug)
	}

	for i := 0; i < len(updateBugs); i += 1000 {
		end := i + 1000
		if end > len(updateBugs) {
			end = len(updateBugs)
		}
		err := easygorm.GetEasyGormDb().Model(&Bug{}).Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "bug_id"}},
			DoUpdates: clause.AssignmentColumns([]string{"over_time"}),
		}).Create(updateBugs[i:end]).Error
		if err != nil {
			return err
		}
	}
	LastUpdateOverTime = &now
	logging.DebugLogger.Debug("update bug over time")
	return nil
}

func GetBugOwnerDelayTop(bugOS, bugOwnerGroup string, order, by string, filterZero bool) ([]*BugOwnerDelayTop, error) {
	// err := UpdateOverTime()
	// if err != nil {
	// 	return nil, err
	// }
	items := []*BugOwnerDelayTop{}
	filter_bug_owner_groups := []string{
		"研发一部控制组",
		"研发一部管理组",
		"研发一部平台组",
		"研发二部安全组",
		"研发二部转发组",
		"研发三部安全组",
		"研发三部业务组",
		"研发三部转发组",
		"研发三部平台组",
		"研发五部WEB组",
		"研发五部应软开发1组",
		"研发五部应软开发2组",
		"安全架构部架构组",
		"战略方案组",
	}

	selects := []string{
		"bug_os",
		"bug_owner",
		"bug_owner_group",
		"count(if(over_time > 0, over_time, null)) as count",
		"sum(over_time)as total",
		"sum(a.round) as round",
	}

	db := easygorm.GetEasyGormDb().
		Table("bugs as b").
		Joins("left join (select max(`round`) as `round`, max(created_at) as created_at, bug_id, `status`, approval_type from bug_approvals GROUP BY bug_id, `status`, approval_type) a on b.bug_id = a.bug_id and a.`status` = 1").
		Select(selects).
		Group("b.bug_os, b.bug_owner, b.bug_owner_group").Order(fmt.Sprintf("%s %s", by, order))
	if filterZero {
		db = db.Having("total > 0")
	}
	where := easygorm.GetEasyGormDb().Where("b.bug_os like '安全云管理平台%'").Where("b.bug_owner_group in ?", filter_bug_owner_groups)
	if len(bugOS) > 0 {
		where = where.Where("b.bug_os = ?", bugOS)
	}
	if len(bugOwnerGroup) > 0 {
		where = where.Where("b.bug_owner_group in ?", strings.Split(bugOwnerGroup, ","))
	}
	err := db.Where(where).Find(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func GetBugOwnerGroupDelayTop(bugOS string, bugOwnerGroup, order, by string, filterZero bool) ([]*BugOwnerGroupDelayTop, error) {
	// err := UpdateOverTime()
	// if err != nil {
	// 	return nil, err
	// }
	items := []*BugOwnerGroupDelayTop{}
	filter_bug_owner_groups := []string{
		"研发一部控制组",
		"研发一部管理组",
		"研发一部平台组",
		"研发二部安全组",
		"研发二部转发组",
		"研发三部安全组",
		"研发三部业务组",
		"研发三部转发组",
		"研发三部平台组",
		"研发五部WEB组",
		"研发五部应软开发1组",
		"研发五部应软开发2组",
		"安全架构部架构组",
		"战略方案组",
	}

	selects := []string{
		"bug_os",
		"bug_owner_group",
		"count(if(over_time > 0, over_time, null)) as count",
		"sum(over_time)as total",
		"sum(a.round) as round",
	}

	db := easygorm.GetEasyGormDb().
		Table("bugs as b").
		Joins("left join (select max(`round`) as `round`, max(created_at) as created_at, bug_id, `status`, approval_type from bug_approvals GROUP BY bug_id, `status`, approval_type) a on b.bug_id = a.bug_id and a.`status` = 1").
		Select(selects).
		Group("b.bug_os, b.bug_owner_group").Order(fmt.Sprintf("%s %s", by, order))
	if filterZero {
		db = db.Having("total > 0")
	}
	where := easygorm.GetEasyGormDb().Where("b.bug_os like '安全云管理平台%'").Where("b.bug_owner_group in ?", filter_bug_owner_groups)
	if len(bugOS) > 0 {
		where = where.Where("b.bug_os = ?", bugOS)
	}
	if len(bugOwnerGroup) > 0 {
		where = where.Where("b.bug_owner_group in ?", strings.Split(bugOwnerGroup, ","))
	}
	err := db.Where(where).Find(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func (s *BugSummary) All(bugOS string, pstlID uint, order, by string) error {
	filter_bug_owner_groups := []string{
		"研发一部控制组",
		"研发一部管理组",
		"研发一部平台组",
		"研发二部安全组",
		"研发二部转发组",
		"研发三部安全组",
		"研发三部业务组",
		"研发三部转发组",
		"研发三部平台组",
		"研发五部WEB组",
		"研发五部应软开发1组",
		"研发五部应软开发2组",
		"安全架构部架构组",
		"战略方案组",
	}

	selects := []string{
		"b.bug_os",
		"COUNT(*) AS total",
		`Round(COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP"), b.bug_id, NULL))/COUNT(*) *100, 2) AS not_cbd_percent`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP"), b.bug_id, NULL)) AS not_cbd_count`,
		`COUNT(IF(b.bug_state LIKE 'CLOSED%' OR b.bug_state LIKE 'DENIAL%' OR b.bug_state IN ("DELAY", "GIVEUP"), b.bug_id, NULL)) AS cbd_count`,
		`COUNT(IF(b.bug_state LIKE 'REQUEST%', b.bug_id, NULL)) AS request`,
		`COUNT(IF(b.bug_state IN ('NEW', 'ASSIGNED'), b.bug_id, NULL)) AS new_assigned`,
		`COUNT(IF(b.bug_state IN ('CHECKED', 'RESOLVED'), b.bug_id, NULL)) AS checked_resolved`,

		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' and b.main_bug_id = 0 AND b.bug_severity = 'Blocking' AND b.bug_repro=1 AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < DATE_ADD(NOW(), INTERVAL -1*(a.round+1) DAY)) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < DATE_ADD(NOW(), INTERVAL -1 DAY))), b.bug_id, NULL)) AS  overtime_blocking_by_test`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' and b.main_bug_id = 0 AND b.bug_priority = 'critical' AND b.bug_repro=1  AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -3*(a.round+1))) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -3))), b.bug_id, NULL)) AS  overtime_critical_by_test`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' and b.main_bug_id = 0 AND b.bug_priority = 'major' AND b.bug_repro=1  AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -5*(a.round+1))) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -5))), b.bug_id, NULL)) AS  overtime_major_by_test`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' and b.main_bug_id = 0 AND (b.bug_severity = 'Normal' || b.bug_priority = 'normal') AND b.bug_repro=1  AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) <  getWorkDay(now(), -7*(a.round+1))) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) <  getWorkDay(now(), -7))), b.bug_id, NULL)) AS  overtime_normal_by_test`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and (b.bug_submitter_group NOT LIKE '%测试%' or b.main_bug_id != 0) AND b.bug_repro=1 AND ((a.approval_type='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -7*(a.round+1))) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -7))), b.bug_id, NULL)) AS  overtime_by_dev`,

		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_severity = 'Blocking', b.bug_id, NULL)) AS  blocking_not_cbd`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_priority = 'critical', b.bug_id, NULL)) AS  critical_not_cbd`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_priority = 'major', b.bug_id, NULL)) AS  major_not_cbd`,
	}

	db := easygorm.GetEasyGormDb().
		Table("bugs as b").
		Joins("left join (select max(`round`) as `round`, bug_id, `status`, approval_type from bug_approvals GROUP BY bug_id, `status`, approval_type) a on b.bug_id = a.bug_id and a.`status` = 1").
		Joins("left join (select r.id as id, r.name as name, c.test_start_at as test_start_at from release_projects r left join release_project_configs c on r.id = c.release_project_id) c on c.name = b.bug_os").
		Select(selects).
		Group("b.bug_os").
		Order(fmt.Sprintf("%s %s", by, order)).
		Having("not_cbd_count > 0")
	where := easygorm.GetEasyGormDb().Where("b.bug_os like '安全云管理平台%'").Where("b.bug_owner_group in ?", filter_bug_owner_groups)
	if len(bugOS) > 0 {
		where = where.Where("b.bug_os = ?", bugOS)
	}

	err := db.Where(where).Find(&s).Error
	if err != nil {
		return err
	}
	return nil
}

func (s *BugSummary) ExportBugSummary(items []*BugSummary) ([]interface{}, [][]interface{}) {
	xt := reflect.TypeOf(s)
	// xv := reflect.ValueOf(s)

	rows := [][]interface{}{}
	headers := []interface{}{}
	for i := 0; i < xt.Elem().NumField(); i++ {
		head, ok := xt.Elem().Field(i).Tag.Lookup("xlsx")
		if ok {
			headers = append(headers, head)
		}
	}
	rows = append(rows, headers)

	for _, e := range items {
		cells := []interface{}{}
		xv := reflect.ValueOf(e)
		for i := 0; i < xv.Elem().NumField(); i++ {
			_, ok := xt.Elem().Field(i).Tag.Lookup("xlsx")
			if ok {
				cells = append(cells, xv.Elem().Field(i).Interface())
			}

		}
		rows = append(rows, cells)
	}
	return headers, rows
}

type Column struct {
	Idx   int    `json:"idx"`
	Key   string `json:"key"`
	Label string `json:"label"`
}

func (s *BugSummary) GetColumns() ([]*Column, error) {
	xt := reflect.TypeOf(s)

	columns := []*Column{}

	for i := 0; i < xt.Elem().NumField(); i++ {
		key, ok1 := xt.Elem().Field(i).Tag.Lookup("json")
		label, ok2 := xt.Elem().Field(i).Tag.Lookup("xlsx")
		_idx, ok3 := xt.Elem().Field(i).Tag.Lookup("idx")

		if ok1 && ok2 && ok3 {
			idx, err := strconv.Atoi(_idx)
			if err != nil {
				return columns, err
			}
			columns = append(columns, &Column{
				Idx:   idx,
				Key:   key,
				Label: label,
			})
		}
	}
	sort.SliceStable(columns, func(i, j int) bool {
		if columns[i].Idx < columns[j].Idx {
			return true
		}
		return false
	})
	return columns, nil
}

func (s *BugSummary) Struct2Slice() ([]map[string]interface{}, error) {
	xt := reflect.TypeOf(s)
	v := reflect.ValueOf(*s)
	result := []map[string]interface{}{}
	for i := 0; i < xt.Elem().NumField(); i++ {
		key, ok1 := xt.Elem().Field(i).Tag.Lookup("json")
		label, ok2 := xt.Elem().Field(i).Tag.Lookup("xlsx")
		_idx, ok3 := xt.Elem().Field(i).Tag.Lookup("idx")

		if ok1 && ok2 && ok3 {
			idx, err := strconv.Atoi(_idx)
			if err != nil {
				return result, err
			}
			result = append(result, map[string]interface{}{
				"idx":   idx,
				"key":   key,
				"label": label,
				"value": v.Field(i).Interface(),
			})
		}
	}
	sort.SliceStable(result, func(i, j int) bool {
		if result[i]["idx"].(int) < result[j]["idx"].(int) {
			return true
		}
		return false
	})
	return result, nil
}
