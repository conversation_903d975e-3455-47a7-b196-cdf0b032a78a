package openfeishu

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/cache"
	"net/http"
	"net/http/cookiejar"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-rod/rod"
	"github.com/go-rod/rod/lib/launcher"
	"github.com/go-rod/rod/lib/proto"
	"github.com/gomodule/redigo/redis"
)

// 定义常量
const (
	QualityControlLoginURL       = "https://sid.ruijie.com.cn/login?service=http:%2F%2Fyfzl.ruijie.com.cn%2Fshiro-cas"
	QualityControlDomain         = "yfzl.ruijie.com.cn"
	QualityIndexURL              = "http://yfzl.ruijie.com.cn/quality/index"
	QualityControlRedisCookieKey = "quality_control_cookies" // Redis中存储cookies的key
	CookieValidityDuration       = 6 * time.Hour             // Cookie有效期
)

// 品控系统Cookie管理器
type QualityControlCookieManager struct {
	cookieJar      *cookiejar.Jar // HTTP客户端的cookie jar
	httpClient     *http.Client   // HTTP客户端
	lastLoginTime  time.Time      // 上次登录时间
	cookieValidity time.Duration  // cookie有效期
	mutex          sync.Mutex     // 并发锁
	isLoggedIn     bool           // 是否已登录
	baseURL        string         // 品控系统基础URL
}

// 全局Cookie管理器
var qualityControlCookieManager *QualityControlCookieManager
var managerOnce sync.Once

// 品控系统API调用模块（简化版）
type QualityControlAPI struct {
	manager *QualityControlCookieManager
}

// ProcessedQualityControlItem 处理后的品控数据项
type ProcessedQualityControlItem struct {
	QualityControlItem              // 嵌入原始数据
	QualityResponsiblePerson string `json:"quality_responsible_person"` // 品控责任人
	IsQualityProblemClosed   string `json:"is_quality_problem_closed"`  // 品控问题是否关闭
	OverdueStatus            string `json:"overdue_status"`             // 超期情况
	ProblemTimeliness        string `json:"problem_timeliness"`         // 问题时效
}

// QualityControlListResponse 品控单列表响应结构
type QualityControlListResponse struct {
	Rows  []QualityControlItem `json:"rows"`  // 品控单列表
	Total int                  `json:"total"` // 总数
}

// GetQualityControlCookieManager 获取单例Cookie管理器
func GetQualityControlCookieManager() *QualityControlCookieManager {
	managerOnce.Do(func() {
		// 使用panic恢复机制保护初始化过程
		defer func() {
			if r := recover(); r != nil {
				logging.ErrorLogger.Errorf("初始化Cookie管理器时发生panic: %v", r)
				// 创建一个基本的管理器实例，避免nil指针
				if qualityControlCookieManager == nil {
					jar, _ := cookiejar.New(nil)
					qualityControlCookieManager = &QualityControlCookieManager{
						cookieJar:      jar,
						httpClient:     &http.Client{Timeout: 30 * time.Second},
						cookieValidity: CookieValidityDuration,
						isLoggedIn:     false,
						baseURL:        libs.Config.FeiShuDoc.QualityControlURL,
					}
				}
			}
		}()

		// 创建cookie jar
		jar, err := cookiejar.New(nil)
		if err != nil {
			logging.ErrorLogger.Errorf("创建cookie jar失败: %v", err)
			return
		}

		// 创建HTTP客户端
		client := &http.Client{
			Jar: jar,
			CheckRedirect: func(req *http.Request, via []*http.Request) error {
				if strings.Contains(req.URL.String(), "sid.ruijie.com.cn/login") {
					return http.ErrUseLastResponse
				}
				return nil
			},
			Timeout: 30 * time.Second,
		}

		qualityControlCookieManager = &QualityControlCookieManager{
			cookieJar:      jar,
			httpClient:     client,
			cookieValidity: CookieValidityDuration,
			isLoggedIn:     false,
			baseURL:        libs.Config.FeiShuDoc.QualityControlURL,
		}

		// 先尝试从Redis加载cookies
		cookiesLoaded := false
		if err := qualityControlCookieManager.LoadCookies(); err != nil {
			logging.InfoLogger.Infof("无法从Redis加载Cookies: %v", err)
		} else {
			cookiesLoaded = true
			logging.InfoLogger.Info("从Redis成功加载Cookies")
		}

		// 如果未能加载cookies，则进行登录获取（带重试机制）
		if !cookiesLoaded {
			if err := qualityControlCookieManager.loginWithRetry(3); err != nil {
				logging.ErrorLogger.Errorf("初始登录失败（已重试3次）: %v", err)
			} else {
				logging.InfoLogger.Info("初始登录成功，已保存Cookie到Redis")
			}
		}
	})
	return qualityControlCookieManager
}

// loginWithRetry 带重试机制的登录方法
func (m *QualityControlCookieManager) loginWithRetry(maxRetries int) error {
	var lastErr error

	for i := 0; i < maxRetries; i++ {
		if i > 0 {
			// 重试前等待一段时间
			waitTime := time.Duration(i*2) * time.Second
			logging.InfoLogger.Infof("登录失败，%v后进行第%d次重试...", waitTime, i+1)
			time.Sleep(waitTime)
		}

		lastErr = m.Login()
		if lastErr == nil {
			logging.InfoLogger.Infof("登录成功（第%d次尝试）", i+1)
			return nil
		}

		logging.ErrorLogger.Errorf("第%d次登录尝试失败: %v", i+1, lastErr)
	}

	return fmt.Errorf("登录失败，已重试%d次，最后错误: %v", maxRetries, lastErr)
}

// 创建新的品控系统API实例
func NewQualityControlAPI() *QualityControlAPI {
	return &QualityControlAPI{
		manager: GetQualityControlCookieManager(),
	}
}

// GetBaseURL 获取基础URL（公有方法）
func (api *QualityControlAPI) GetBaseURL() string {
	return api.manager.baseURL
}

// GetCookies 获取cookies（公有方法）
func (api *QualityControlAPI) GetCookies() []*http.Cookie {
	u, err := url.Parse(api.manager.baseURL)
	if err != nil {
		logging.ErrorLogger.Errorf("解析基础URL失败: %v", err)
		return nil
	}
	return api.manager.cookieJar.Cookies(u)
}

// Login 登录品控系统
func (m *QualityControlCookieManager) Login() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	logging.InfoLogger.Info("正在登录品控系统...")

	// 设置登录超时时间
	loginTimeout := 60 * time.Second
	ctx, cancel := context.WithTimeout(context.Background(), loginTimeout)
	defer cancel()

	// 使用panic恢复机制
	var loginErr error
	func() {
		defer func() {
			if r := recover(); r != nil {
				logging.ErrorLogger.Errorf("登录过程中发生panic: %v", r)
				loginErr = fmt.Errorf("登录过程中发生panic: %v", r)
			}
		}()

		// 初始化浏览器（仅用于登录）
		l, err := launcher.NewManaged("")
		if err != nil {
			loginErr = fmt.Errorf("创建launcher失败: %v", err)
			return
		}

		client, err := l.Client()
		if err != nil {
			loginErr = fmt.Errorf("获取launcher客户端失败: %v", err)
			return
		}

		browser := rod.New().Client(client).Context(ctx)

		// 使用WithPanic来自定义panic处理
		browser = browser.WithPanic(func(v interface{}) {
			panic(fmt.Errorf("浏览器操作失败: %v", v))
		})

		connectErr := browser.Connect()
		if connectErr != nil {
			loginErr = fmt.Errorf("连接浏览器失败: %v", connectErr)
			return
		}

		defer func() {
			// 安全关闭浏览器
			if browser != nil {
				if err := browser.Close(); err != nil {
					logging.ErrorLogger.Errorf("关闭浏览器失败: %v", err)
				}
			}
		}()

		// 设置页面
		page := setupQualityControlPage(browser)
		if page == nil {
			loginErr = fmt.Errorf("创建页面失败")
			return
		}
		defer func() {
			// 安全关闭页面
			if page != nil {
				if err := page.Close(); err != nil {
					logging.ErrorLogger.Errorf("关闭页面失败: %v", err)
				}
			}
		}()

		// 登录品控系统
		loginErr = loginQualityControlSystemWithTimeout(page, libs.Config.FeiShuDoc.PmsUser, libs.Config.FeiShuDoc.PmsPass, QualityControlLoginURL, 20*time.Second)
		if loginErr != nil {
			logging.ErrorLogger.Errorf("登录品控系统失败: %v", loginErr)
			return
		}

		// 获取cookies
		loginErr = m.extractAndSaveCookies(page)
	}()

	return loginErr
}

// extractAndSaveCookies 提取并保存cookies的辅助方法
func (m *QualityControlCookieManager) extractAndSaveCookies(page *rod.Page) error {
	// 获取cookies
	cookiesResult, err := proto.NetworkGetAllCookies{}.Call(page)
	if err != nil {
		logging.ErrorLogger.Errorf("获取Cookies失败: %v", err)
		return err
	}

	// 转换为http.Cookie并保存到jar中
	httpCookies := make([]*http.Cookie, 0, len(cookiesResult.Cookies))
	u, err := url.Parse(QualityIndexURL)
	if err != nil {
		logging.ErrorLogger.Errorf("解析基础URL失败: %v", err)
		return fmt.Errorf("解析基础URL失败: %v", err)
	}

	for _, c := range cookiesResult.Cookies {
		httpCookie := &http.Cookie{
			Name:     c.Name,
			Value:    c.Value,
			Path:     c.Path,
			Domain:   c.Domain,
			Expires:  time.Unix(int64(c.Expires), 0),
			Secure:   c.Secure,
			HttpOnly: c.HTTPOnly,
		}
		httpCookies = append(httpCookies, httpCookie)
	}

	// 设置cookies到jar
	m.cookieJar.SetCookies(u, httpCookies)
	m.lastLoginTime = time.Now()
	m.isLoggedIn = true

	// 保存cookies到Redis
	if err := m.SaveCookies(); err != nil {
		logging.ErrorLogger.Errorf("保存cookies失败: %v", err)
	}

	logging.InfoLogger.Info("成功登录品控系统并保存cookies")
	return nil
}

// SaveCookies 保存cookies到Redis
func (m *QualityControlCookieManager) SaveCookies() error {
	u, err := url.Parse(m.baseURL)
	if err != nil {
		return fmt.Errorf("解析基础URL失败: %v", err)
	}
	cookies := m.cookieJar.Cookies(u)

	// 将cookies转换为可序列化的格式
	cookieData := make([]map[string]interface{}, len(cookies))
	for i, cookie := range cookies {
		cookieData[i] = map[string]interface{}{
			"name":     cookie.Name,
			"value":    cookie.Value,
			"path":     cookie.Path,
			"domain":   cookie.Domain,
			"expires":  cookie.Expires.Unix(),
			"secure":   cookie.Secure,
			"httponly": cookie.HttpOnly,
		}
	}

	// 将cookies数据转换为JSON
	jsonData, err := json.Marshal(cookieData)
	if err != nil {
		return fmt.Errorf("序列化cookies失败: %v", err)
	}

	// 获取Redis客户端并保存
	rc := cache.GetRedisClusterClient()
	_, err = rc.Set(QualityControlRedisCookieKey, string(jsonData), CookieValidityDuration)
	if err != nil {
		return fmt.Errorf("保存cookies到Redis失败: %v", err)
	}

	logging.InfoLogger.Infof("成功保存 %d 个cookies到Redis", len(cookies))
	return nil
}

// LoadCookies 从Redis加载cookies
func (m *QualityControlCookieManager) LoadCookies() error {
	// 如果当前已经登录并且距离上次登录时间不长，可以跳过加载
	if m.isLoggedIn && time.Since(m.lastLoginTime) < time.Minute {
		logging.InfoLogger.Info("当前Cookie仍然有效且刚刚刷新过，跳过从Redis加载")
		return nil
	}

	// 获取Redis客户端
	rc := cache.GetRedisClusterClient()

	// 从Redis获取cookies数据
	data, err := rc.GetKey(QualityControlRedisCookieKey)
	if err != nil {
		// 如果是键不存在，返回特定错误以触发重新登录
		if strings.Contains(err.Error(), "redis: nil") {
			logging.InfoLogger.Info("Redis中不存在Cookie数据，需要重新登录获取")
			return fmt.Errorf("Redis中不存在Cookie数据")
		}
		return fmt.Errorf("从Redis获取cookies失败: %v", err)
	}

	// 将Redis返回的接口值转换为字符串
	jsonData, err := redis.String(data, nil)
	if err != nil {
		return fmt.Errorf("Redis数据转换失败: %v", err)
	}

	// 解析JSON数据
	var cookieData []map[string]interface{}
	if err := json.Unmarshal([]byte(jsonData), &cookieData); err != nil {
		return fmt.Errorf("反序列化cookies失败: %v", err)
	}

	// 转换为http.Cookie并添加到jar中
	u, err := url.Parse(m.baseURL)
	if err != nil {
		return fmt.Errorf("解析基础URL失败: %v", err)
	}
	cookies := make([]*http.Cookie, len(cookieData))
	for i, data := range cookieData {
		// 安全的类型断言，带错误检查
		name, ok := data["name"].(string)
		if !ok {
			logging.ErrorLogger.Errorf("Cookie[%d] name字段类型断言失败", i)
			continue
		}
		value, ok := data["value"].(string)
		if !ok {
			logging.ErrorLogger.Errorf("Cookie[%d] value字段类型断言失败", i)
			continue
		}
		path, ok := data["path"].(string)
		if !ok {
			path = "/" // 默认路径
		}
		domain, ok := data["domain"].(string)
		if !ok {
			domain = "" // 默认域名
		}
		expires, ok := data["expires"].(float64)
		if !ok {
			expires = 0 // 默认过期时间
		}
		secure, ok := data["secure"].(bool)
		if !ok {
			secure = false // 默认不安全
		}
		httponly, ok := data["httponly"].(bool)
		if !ok {
			httponly = false // 默认非HttpOnly
		}

		cookies[i] = &http.Cookie{
			Name:     name,
			Value:    value,
			Path:     path,
			Domain:   domain,
			Expires:  time.Unix(int64(expires), 0),
			Secure:   secure,
			HttpOnly: httponly,
		}
	}

	// 设置cookies到jar
	m.cookieJar.SetCookies(u, cookies)
	m.lastLoginTime = time.Now()
	m.isLoggedIn = true

	logging.InfoLogger.Infof("成功从Redis加载 %d 个cookies", len(cookies))
	return nil
}

// setupQualityControlPage 设置品控系统页面
func setupQualityControlPage(browser *rod.Browser) *rod.Page {
	// 使用非Must版本创建页面，避免panic
	page, err := browser.Page(proto.TargetCreateTarget{URL: "about:blank"})
	if err != nil {
		logging.ErrorLogger.Errorf("创建页面失败: %v", err)
		// 如果非Must版本失败，尝试创建空白页面
		page, err = browser.Page(proto.TargetCreateTarget{URL: ""})
		if err != nil {
			logging.ErrorLogger.Errorf("创建空白页面也失败: %v", err)
			return nil
		}
	}

	// 设置用户代理
	err = page.SetUserAgent(&proto.NetworkSetUserAgentOverride{
		UserAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
	})
	if err != nil {
		logging.ErrorLogger.Errorf("设置用户代理失败: %v", err)
		// 继续执行，不因为这个错误而失败
	}

	// 设置页面视口
	err = page.SetViewport(&proto.EmulationSetDeviceMetricsOverride{
		Width:             1920,
		Height:            1080,
		DeviceScaleFactor: 1,
		Mobile:            false,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("设置页面视口失败: %v", err)
		// 继续执行，不因为这个错误而失败
	}

	return page
}

// loginQualityControlSystemWithTimeout 带超时的登录方法
func loginQualityControlSystemWithTimeout(page *rod.Page, username, password, loginURL string, timeout time.Duration) error {
	logging.InfoLogger.Infof("开始登录品控系统，URL: %s，超时时间: %v", loginURL, timeout)

	// 设置页面超时
	page = page.Timeout(timeout)

	// 导航到登录页面
	err := page.Navigate(loginURL)
	if err != nil {
		return fmt.Errorf("导航到登录页面失败: %v", err)
	}

	// 等待页面加载
	err = page.WaitLoad()
	if err != nil {
		return fmt.Errorf("等待页面加载失败: %v", err)
	}

	logging.InfoLogger.Info("页面加载完成，开始查找登录元素...")

	// 调试：获取页面标题和部分内容
	pageInfo, err := page.Info()
	if err != nil {
		logging.ErrorLogger.Errorf("获取页面信息失败: %v", err)
	} else {
		logging.InfoLogger.Infof("页面标题: %s", pageInfo.Title)
	}

	// 调试：检查页面是否包含登录表单
	hasForm, _, _ := page.Has("form")
	logging.InfoLogger.Infof("页面是否包含表单: %v", hasForm)

	// 安全地查找和操作登录元素
	logging.InfoLogger.Info("查找用户名输入框...")

	// 等待用户名输入框出现
	usernameInput, err := page.Element(`input[name="username"]`)
	if err != nil {
		return fmt.Errorf("找不到用户名输入框: %v", err)
	}

	// 等待密码输入框出现
	passwordInput, err := page.Element(`input[type="password"]`)
	if err != nil {
		return fmt.Errorf("找不到密码输入框: %v", err)
	}

	// 等待提交按钮出现
	submitButton, err := page.Element(`button[type="submit"]`)
	if err != nil {
		return fmt.Errorf("找不到提交按钮: %v", err)
	}

	// 输入用户名和密码并点击登录按钮
	err = usernameInput.Input(username)
	if err != nil {
		return fmt.Errorf("输入用户名失败: %v", err)
	}

	err = passwordInput.Input(password)
	if err != nil {
		return fmt.Errorf("输入密码失败: %v", err)
	}

	err = submitButton.Click(proto.InputMouseButtonLeft, 1)
	if err != nil {
		return fmt.Errorf("点击登录按钮失败: %v", err)
	}

	logging.InfoLogger.Info("登录按钮点击完成，等待登录结果...")

	// 等待页面稳定
	err = page.WaitStable(3 * time.Second)
	if err != nil {
		logging.ErrorLogger.Errorf("等待页面稳定失败: %v", err)
		// 不直接返回错误，继续尝试检查登录结果
	}

	// 使用更安全的方式检查登录结果
	return checkLoginResult(page, timeout)
}

// checkLoginResult 检查登录结果的辅助函数
func checkLoginResult(page *rod.Page, timeout time.Duration) error {
	logging.InfoLogger.Info("开始检查登录结果...")

	// 创建一个带超时的context用于Race操作
	ctx, cancel := context.WithTimeout(context.Background(), timeout/2) // 使用一半的超时时间
	defer cancel()

	// 使用非Must版本的Race操作，避免panic
	race := page.Context(ctx).Race()

	var loginError error
	var raceErr error

	// 添加成功登录的检查
	race.Element("#sessionInfoDiv").Handle(func(e *rod.Element) error {
		logging.InfoLogger.Info("检测到登录成功标识")
		loginError = nil
		return nil
	})

	// 添加登录失败的检查
	race.Element(".error-msg").Handle(func(e *rod.Element) error {
		text, err := e.Text()
		if err != nil {
			logging.ErrorLogger.Errorf("获取错误信息失败: %v", err)
			loginError = fmt.Errorf("登录失败，但无法获取具体错误信息")
		} else {
			logging.ErrorLogger.Errorf("登录失败: %s", text)
			loginError = fmt.Errorf("登录失败: %s", text)
		}
		return nil
	})

	// 添加页面跳转检查（某些系统登录成功后会跳转）
	race.Element("body").Handle(func(e *rod.Element) error {
		// 检查当前URL是否已经跳转
		currentURL, err := page.Eval(`window.location.href`)
		if err == nil && currentURL.Value.Str() != "" {
			urlStr := currentURL.Value.Str()
			if !strings.Contains(urlStr, "login") {
				logging.InfoLogger.Infof("检测到URL变化，可能登录成功: %s", urlStr)
				loginError = nil
			}
		}
		return nil
	})

	// 执行Race操作
	_, raceErr = race.Do()

	// 处理Race操作的结果
	if raceErr != nil {
		if errors.Is(raceErr, context.DeadlineExceeded) {
			logging.ErrorLogger.Error("登录结果检查超时")
			return fmt.Errorf("登录结果检查超时，可能网络较慢或页面响应异常")
		}
		logging.ErrorLogger.Errorf("检查登录结果时发生错误: %v", raceErr)
		return fmt.Errorf("检查登录结果失败: %v", raceErr)
	}

	// 如果没有明确的成功或失败标识，尝试额外的检查
	if loginError == nil {
		// 进行额外的验证，检查是否真的登录成功
		if err := validateLoginSuccess(page); err != nil {
			logging.ErrorLogger.Errorf("登录验证失败: %v", err)
			return err
		}
		logging.InfoLogger.Info("登录成功验证通过")
	}

	return loginError
}

// validateLoginSuccess 验证登录是否真的成功
func validateLoginSuccess(page *rod.Page) error {
	// 检查当前URL是否还在登录页面
	currentURL, err := page.Eval(`window.location.href`)
	if err != nil {
		logging.ErrorLogger.Errorf("获取当前URL失败: %v", err)
		return nil // 不因为这个错误而失败
	}

	urlStr := currentURL.Value.String()
	if strings.Contains(urlStr, "login") {
		return fmt.Errorf("仍在登录页面，登录可能失败")
	}

	// 检查页面是否包含登录后的特征元素
	hasSessionInfo, _, _ := page.Has("#sessionInfoDiv")
	hasUserInfo, _, _ := page.Has(".user-info")
	hasLogoutBtn, _, _ := page.Has("a[href*='logout']")

	if hasSessionInfo || hasUserInfo || hasLogoutBtn {
		return nil // 找到登录后的特征，认为登录成功
	}

	logging.InfoLogger.Info("未找到明确的登录成功标识，但也未发现登录失败，假设登录成功")
	return nil
}

// // loginQualityControlSystem 保留原有方法以保持兼容性
// func loginQualityControlSystem(page *rod.Page, username, password, loginURL string) error {
// 	return loginQualityControlSystemWithTimeout(page, username, password, loginURL, 20*time.Second)
// }

// ClearAllCache 清除所有缓存
func (api *QualityControlAPI) ClearAllCache() error {
	logging.InfoLogger.Info("清除所有品控系统缓存...")

	// 重置Cookie管理器状态
	api.manager.isLoggedIn = false
	api.manager.lastLoginTime = time.Time{}

	// 清除Redis缓存
	redisClient := cache.GetRedisClusterClient()
	if redisClient != nil {
		_, err := redisClient.Do("DEL", QualityControlRedisCookieKey)
		if err != nil {
			logging.ErrorLogger.Errorf("清除Redis缓存失败: %v", err)
			return fmt.Errorf("清除Redis缓存失败: %v", err)
		}
		logging.InfoLogger.Info("Redis缓存已清除")
	}

	logging.InfoLogger.Info("所有缓存已清除")
	return nil
}

// ProcessQualityControlData 处理品控数据，添加格式化字段
func (api *QualityControlAPI) ProcessQualityControlData(items []QualityControlItem) []ProcessedQualityControlItem {
	processedItems := make([]ProcessedQualityControlItem, 0, len(items))

	for _, item := range items {
		processed := ProcessedQualityControlItem{
			QualityControlItem: item,
			// 品控责任人 - 使用故障责任人
			QualityResponsiblePerson: item.FaultUserName,
			// 品控问题是否关闭 - 根据状态判断
			IsQualityProblemClosed: api.formatQualityProblemStatus(item.PzkzStatus),
			// 超期情况
			OverdueStatus: api.formatDealDay(item.DealDay, item),
			// 问题时效
			ProblemTimeliness: api.formatTimeDay(item.TimeDay, item),
		}
		processedItems = append(processedItems, processed)
	}
	return processedItems
}

// formatQualityProblemStatus 格式化品控问题关闭状态
func (api *QualityControlAPI) formatQualityProblemStatus(pzkzStatus string) string {
	if pzkzStatus == "问题关闭" || pzkzStatus == "废弃" {
		return "是"
	}
	return "否"
}

// formatTimeDay 格式化超期情况
func (api *QualityControlAPI) formatTimeDay(timeDay float64, item QualityControlItem) string {
	// 构建详细信息标题
	title := fmt.Sprintf("初始故障等级：%s（时效：%.1f天）\n",
		item.FaultLvlBaseName,
		parseFloat(item.FaultLvlBaseTime)/24)
	title += fmt.Sprintf("当前故障等级：%s（时效：%.1f天）\n",
		item.FaultLvlName,
		parseFloat(item.FaultLvlTime)/24)
	title += fmt.Sprintf("品控创建时间：%s\n",
		formatTime(item.CreateDate))

	if item.ExtendHour > 0 {
		title += fmt.Sprintf("品控挂起时长：%.1f天\n", float64(item.ExtendHour)/24)
	}

	// 返回格式化的天数显示
	return fmt.Sprintf("%.1f天", timeDay)
}

// formatDealDay 格式化问题时效
func (api *QualityControlAPI) formatDealDay(dealDay float64, item QualityControlItem) string {
	// 如果状态为废弃，返回"-"
	if item.PzkzStatus == "废弃" {
		return "-"
	}

	// 如果dealDay > 0，表示还有剩余时间或提前解决
	if dealDay > 0 {
		if item.SolutionDate != "" || item.IsFinish == 1 {
			return fmt.Sprintf("提前解决(%.1f天)", dealDay)
		} else {
			return fmt.Sprintf("剩余%.1f天", dealDay)
		}
	}

	// dealDay <= 0，表示超期
	return fmt.Sprintf("超期%.1f天", -dealDay)
}

// parseFloat 解析字符串为浮点数的辅助函数
func parseFloat(s string) float64 {
	if s == "" {
		return 0
	}
	f, err := strconv.ParseFloat(s, 64)
	if err != nil {
		logging.ErrorLogger.Errorf("解析浮点数失败: %s, 错误: %v", s, err)
		return 0
	}
	return f
}

// formatTime 格式化时间的辅助函数
func formatTime(timeStr string) string {
	if timeStr == "" {
		return ""
	}

	// 尝试解析时间字符串
	t, err := time.Parse("2006-01-02T15:04:05.000Z07:00", timeStr)
	if err != nil {
		// 尝试其他格式
		t, err = time.Parse("2006-01-02T15:04:05Z07:00", timeStr)
		if err != nil {
			logging.ErrorLogger.Errorf("解析时间失败: %s, 错误: %v", timeStr, err)
			return timeStr
		}
	}

	return t.Format("2006-01-02 15:04:05")
}

// GetQualityControlList 获取品控单列表数据
func (api *QualityControlAPI) GetQualityControlList(page, rows int, id string) (*QualityControlListResponse, error) {
	// 确保Cookie管理器已初始化并登录
	if !api.manager.isLoggedIn {
		logging.InfoLogger.Info("Cookie管理器未登录，开始登录...")
		err := api.manager.Login()
		if err != nil {
			return nil, fmt.Errorf("登录失败: %v", err)
		}
	}

	// 检查Cookie是否过期，如果过期则重新登录
	if time.Since(api.manager.lastLoginTime) > api.manager.cookieValidity {
		logging.InfoLogger.Info("Cookie已过期，重新登录...")
		err := api.manager.Login()
		if err != nil {
			return nil, fmt.Errorf("重新登录失败: %v", err)
		}
	}

	// 构建API请求URL
	apiURL := fmt.Sprintf("%s/pzkz/load_pzkz_list", api.manager.baseURL)

	// 准备请求数据
	data := url.Values{}
	data.Set("disabled", "0")
	data.Set("isPzkz", "1")
	data.Set("page", fmt.Sprintf("%d", page))
	data.Set("rows", fmt.Sprintf("%d", rows))
	if id != "" {
		data.Set("id", id)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", apiURL, strings.NewReader(data.Encode()))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	// 使用Cookie管理器的HTTP客户端发送请求
	resp, err := api.manager.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP请求失败，状态码: %d", resp.StatusCode)
	}

	// 检查是否返回HTML页面（表示需要重新登录）
	bodyStr := string(body)
	if strings.Contains(bodyStr, "<html") || strings.Contains(bodyStr, "<!DOCTYPE") {
		return nil, fmt.Errorf("API返回HTML页面而不是JSON数据，需要重新登录")
	}

	// 解析JSON响应
	var response QualityControlListResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析JSON响应失败: %v", err)
	}
	return &response, nil
}

// GetProcessedQualityControlList 获取并处理品控单列表数据
func (api *QualityControlAPI) GetProcessedQualityControlList(page, rows int, id string) ([]ProcessedQualityControlItem, error) {
	// 获取原始品控单列表数据
	response, err := api.GetQualityControlList(page, rows, id)
	if err != nil {
		return nil, fmt.Errorf("获取品控单列表失败: %v", err)
	}
	// 处理品控数据，添加格式化字段
	processedItems := api.ProcessQualityControlData(response.Rows)
	return processedItems, nil
}

// GetSolutionList 获取解决方案列表
func (api *QualityControlAPI) GetSolutionList(questionID string) ([]SolutionResponse, error) {
	logging.InfoLogger.Infof("开始获取品控单号 %s 的解决方案数据", questionID)

	// 构建请求URL
	baseURL := "http://yfzl.ruijie.com.cn/solution/load_solution_list"
	params := url.Values{}
	params.Add("questionId", questionID)
	params.Add("questionType", "PzkzInfo")

	requestURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())

	// 创建HTTP请求
	req, err := http.NewRequest("POST", requestURL, nil)
	if err != nil {
		logging.ErrorLogger.Errorf("创建解决方案API请求失败: %v", err)
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	// 添加认证Cookie（与品控系统API相同的认证机制）
	if api.manager != nil && api.manager.cookieJar != nil {
		cookies := api.manager.cookieJar.Cookies(req.URL)
		for _, cookie := range cookies {
			req.AddCookie(cookie)
		}
	}

	// 发送请求
	client := &http.Client{
		Timeout: 30 * time.Second,
		Jar:     api.manager.cookieJar,
	}

	resp, err := client.Do(req)
	if err != nil {
		logging.ErrorLogger.Errorf("解决方案API请求失败: %v", err)
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logging.ErrorLogger.Errorf("读取解决方案API响应失败: %v", err)
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		logging.ErrorLogger.Errorf("解决方案API返回错误状态码: %d, 响应: %s", resp.StatusCode, string(body))
		return nil, fmt.Errorf("API返回错误状态码: %d", resp.StatusCode)
	}

	// 解析JSON响应
	var solutions []SolutionResponse
	if err := json.Unmarshal(body, &solutions); err != nil {
		logging.ErrorLogger.Errorf("解析解决方案API响应失败: %v, 响应内容: %s", err, string(body))
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}
	return solutions, nil
}
