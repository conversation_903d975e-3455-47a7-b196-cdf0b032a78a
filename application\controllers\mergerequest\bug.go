package mergerequest

import (
	"fmt"
	"strconv"
	"strings"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	dbugmirror "irisAdminApi/service/dao/bugmirror/dbug"
	"irisAdminApi/service/dao/datasync/dbug"
	"irisAdminApi/service/dao/release/dproject"
	"irisAdminApi/service/dao/user/duser"

	"github.com/kataras/iris/v12"
)

func GetBugs(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValueDefault("orderBy", "bug_created_at")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	bugOs := ctx.FormValue("bug_os")
	bugOwner := ctx.FormValue("bug_owner")
	bugOwnerGroup := ctx.FormValue("bug_owner_group")
	// owned := ctx.URLParamBoolDefault("owned", false)

	var list map[string]interface{}

	list, err := dbug.All(bugOwner, bugOs, bugOwnerGroup, name, sort, orderBy, status, page, pageSize)
	// list, err := dproblem.AllProblems(name, sort, orderBy, page, pageSize, status, start, end, department)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetBug(ctx iris.Context) {
	info := dbug.Bug{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

func GetRunningProjects(ctx iris.Context) {
	result, err := dproject.GetRunningProjects()
	if err != nil {
		logging.ErrorLogger.Errorf("find running projects err ", err.Error())
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func GetMirrorProjects(ctx iris.Context) {
	result, err := dproject.GetBugMirrorProjects()
	if err != nil {
		logging.ErrorLogger.Errorf("find running projects err ", err.Error())
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

type BugMirrorRequest struct {
	BugOS string `json:"bug_os"`
}

type BugMirrorResponse struct {
	/*
		{
			"type": "success",
			"content": "镜像成功",
			"id": 1035980
		}
	*/
	Type    string `json:"type"`
	Content string `json:"content"`
	ID      int    `json:"id"`
}

func CreateBugMirror(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	userID, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	user := duser.User{}
	err = user.Find(userID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if user.ID == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "用户不存在"))
		return
	}

	request := BugMirrorRequest{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	url := fmt.Sprintf("http://bugs.ruijie.com.cn/bug_switch/interface/bug_mirror?bugId=%v&osName=%s&submitUser=%s", id, request.BugOS, user.Username)
	ret := BugMirrorResponse{}
	resp, err := GitlabWebClient.R().SetSuccessResult(&ret).Post(url)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	resultMsg := ret.Content
	logging.DebugLogger.Debugf("Bug镜像 user_id:%v url: %s, 响应: %s", userID, url, resp.String())
	if strings.Contains(resp.String(), "镜像成功") {
		err = dbugmirror.MirrorCreate(userID, int(id), ret.ID, request.BugOS)
		if err != nil {
			resultMsg = resultMsg + "\n" + err.Error()
		}
		ctx.JSON(response.NewResponse(response.NoErr.Code, nil, resultMsg))
		return
	}

	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	return
}

type BugMirrorApprovalRequest struct {
	BugID   int    `gorm:"not null" json:"bug_id"`
	BugOS   string `gorm:"not null" json:"bug_os"`
	Comment string `gorm:"not null; type:varchar(200)" json:"comment"`
}

type BatchBugMirrorApprovalRequest struct {
	BugIDs  []int    `gorm:"not null" json:"bug_ids"`
	BugOSes []string `gorm:"not null" json:"bug_oses"`
	Comment string   `gorm:"not null; type:varchar(200)" json:"comment"`
}

func IgnoreBugMirror(ctx iris.Context) {
	// id, err := dao.GetId(ctx)
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
	// 	return
	// }
	userID, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	request := BatchBugMirrorApprovalRequest{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	objects := []map[string]interface{}{}
	for _, bugOs := range request.BugOSes {
		for _, bugID := range request.BugIDs {
			object := map[string]interface{}{
				"bug_os":  bugOs,
				"bug_id":  bugID,
				"user_id": userID,
				"comment": request.Comment,
			}
			objects = append(objects, object)
		}
	}

	if len(objects) > 0 {
		bugMirrorApproval := dbugmirror.BugMirrorApproval{}
		err := bugMirrorApproval.BatchCreate(objects)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func CancelIgnoreBugMirror(ctx iris.Context) {
	userID, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	request := BugMirrorApprovalRequest{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	if len(request.BugOS) > 0 && request.BugID > 0 {
		object := map[string]interface{}{
			"bug_os":  request.BugOS,
			"bug_id":  request.BugID,
			"user_id": userID,
			"comment": request.Comment,
		}
		bugMirrorApproval := dbugmirror.BugMirrorApproval{}
		err := bugMirrorApproval.Delete(object)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetBugMirrorApprovalHistory(ctx iris.Context) {
	// userID, err := dao.GetAuthId(ctx)
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
	// 	return
	// }

	request := BugMirrorApprovalRequest{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	if len(request.BugOS) > 0 && request.BugID > 0 {
		bugMirrorApprovalHistory := dbugmirror.BugMirrorApprovalHistory{}
		items, err := bugMirrorApprovalHistory.Find(request.BugID, request.BugOS)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		ctx.JSON(response.NewResponse(response.NoErr.Code, items, response.NoErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
	return
}
