package mergerequest

import (
	"fmt"
	"regexp"
	"strings"

	"github.com/pkg/errors"
)

// Block represents a diff block with its header and lines
type Block struct {
	Header           string
	OriginalStart    int
	ModifiedStart    int
	OriginLines      []string
	ModifiedLines    []string
	ModifiedLineNums []int
}

// parseDiff parses the diff content into a list of Blocks
func parseDiff(diffContent string) ([]Block, map[int]int) {
	var blocks []Block
	var currentBlock Block

	lineMap := map[int]int{}

	lines := strings.Split(diffContent, "\n")

	for _, line := range lines {
		if strings.HasPrefix(line, "@@") {
			if currentBlock.Header != "" {
				blocks = append(blocks, currentBlock)
				currentBlock = Block{}
			}
			// Extract original and modified start lines
			parts := strings.Fields(line)
			if len(parts) >= 2 {
				fmt.Sscanf(parts[1], "-%d,", &currentBlock.OriginalStart)
				fmt.Sscanf(parts[2], "+%d,", &currentBlock.ModifiedStart)

				if currentBlock.Header == "" {
					currentBlock.Header = line
				}
			}
		} else {
			if currentBlock.Header != "" {
				if strings.HasPrefix(line, "+") {
					currentBlock.ModifiedLines = append(currentBlock.ModifiedLines, line)
					currentBlock.ModifiedLineNums = append(currentBlock.ModifiedLineNums, len(currentBlock.ModifiedLines)-1+currentBlock.ModifiedStart)
				} else if strings.HasPrefix(line, "-") {
					currentBlock.OriginLines = append(currentBlock.OriginLines, line)
				} else {
					currentBlock.ModifiedLines = append(currentBlock.ModifiedLines, line)
					currentBlock.OriginLines = append(currentBlock.OriginLines, line)
					lineMap[len(currentBlock.ModifiedLines)-1+currentBlock.ModifiedStart] = len(currentBlock.OriginLines) - 1 + currentBlock.OriginalStart
				}
			}
		}
	}
	if currentBlock.Header != "" {
		blocks = append(blocks, currentBlock)
	}
	return blocks, lineMap
}

var lineNumPattern = regexp.MustCompile(`^<\d+> `)

func CheckIfCodeChanged(lines []string, diffLineNums []int) bool {
	var start, end int

	if lineNumPattern.MatchString(lines[0]) {
		_, err := fmt.Sscanf(lines[0], "<%d> ", &start)
		if err != nil {
			return false
		}
	}

	if lineNumPattern.MatchString(lines[len(lines)-1]) {
		_, err := fmt.Sscanf(lines[len(lines)-1], "<%d> ", &end)
		if err != nil {
			return false
		}
	}

	for _, diffLineNum := range diffLineNums {
		if diffLineNum >= start && diffLineNum <= end {
			return true
		}
	}
	return false
}

func CodeSplit(ext, code string) ([]string, error) {
	url := "http://127.0.0.1:5000/api/split"
	headers := map[string]string{
		"Content-Type": "application/json",
	}

	chunks := []string{}
	payload := map[string]string{
		"ext":  ext,
		"code": code,
	}

	resp, err := BugWebClient.R().SetHeaders(headers).SetBody(payload).SetSuccessResult(&chunks).Post(url)
	if err != nil {
		return chunks, err
	}

	if resp.IsSuccessState() {
		return chunks, nil
	}

	return nil, errors.New(fmt.Sprintf("Unknow error, %s", resp.String()))
}

func CodeAnalisysTest(code string) ([]*Suggestion, error) {
	suggestions := AnalisysResponse{}
	url := "http://58.22.100.210:7002/analyze"
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	payload := map[string]string{
		"code": code,
	}

	resp, err := BugWebClient.R().SetHeaders(headers).SetBody(payload).SetSuccessResult(&suggestions).Post(url)
	if err != nil {
		return suggestions.Suggestions, err
	}

	if resp.IsSuccessState() {
		return suggestions.Suggestions, nil
	}

	return nil, errors.New(fmt.Sprintf("Unknow error, %s", resp.String()))
}

func CodeAnalisys(ext, chunk string) ([]*Suggestion, error) {
	suggestions := []*Suggestion{}
	url := "http://127.0.0.1:5000/api/analyze"
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	payload := map[string]string{
		"ext":   ext,
		"chunk": chunk,
	}

	resp, err := BugWebClient.R().SetHeaders(headers).SetBody(payload).SetSuccessResult(&suggestions).Post(url)
	if err != nil {
		return suggestions, err
	}

	if resp.IsSuccessState() {
		return suggestions, nil
	}

	return nil, errors.New(fmt.Sprintf("Unknow error, %s", resp.String()))
}
