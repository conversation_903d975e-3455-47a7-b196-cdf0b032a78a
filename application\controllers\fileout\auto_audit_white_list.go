package fileout

import (
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/fileout/dautoauditfile"
	"irisAdminApi/service/dao/fileout/dautoauditwhitelist"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

func GetAllAutoAuditWhiteLists(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dautoauditwhitelist.AutoAuditWhiteList{}, ctx, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func DeleteAutoAuditWhiteList(ctx iris.Context) {
	err := dao.Delete(&dautoauditwhitelist.AutoAuditWhiteList{}, ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func CreateAutoAuditWhiteList(ctx iris.Context) {
	req := dautoauditwhitelist.Request{}
	if err := ctx.ReadJSON(&req); err != nil {
		logging.ErrorLogger.Errorf("create autoauditfile read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(req)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	err := dao.Create(&dautoauditfile.Response{}, ctx, map[string]interface{}{
		"CreatedAt": time.Now(),
		"UserID":    req.UserID,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, req, response.NoErr.Msg))
	return
}

func GetAllBlackUsers(ctx iris.Context) {
	whitelist := dautoauditwhitelist.AutoAuditWhiteList{}
	users, err := whitelist.FindAllBlackUsers()
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, users, response.NoErr.Msg))
	return
}
