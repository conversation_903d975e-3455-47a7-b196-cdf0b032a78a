insert into common_components (component_packet, component, path) values ("boot", "uboot", "basesys/u-boot/uboot-trunk");
insert into common_components (component_packet, component, path) values ("boot", "bios", "basesys/bios");
insert into common_components (component_packet, component, path) values ("boot", "bootloader", "bsp-driver/package/utils/bootloader/");
insert into common_components (component_packet, component, path) values ("dev-driver", "ddr-bus", "basesys/u-boot/uboot-trunk/");
insert into common_components (component_packet, component, path) values ("dev-driver", "cpu-arch", "basesys/kernel/kernel-xx/arch");
insert into common_components (component_packet, component, path) values ("dev-driver", "pcie-bus", "basesys/kernel/kernel-xx/driver/pci/");
insert into common_components (component_packet, component, path) values ("dev-driver", "usb-bus", "basesys/kernel/kernel-xx/driver/usb/");
insert into common_components (component_packet, component, path) values ("dev-driver", "i2c-bus", "basesys/kernel/kernel-xx/driver/i2c/");
insert into common_components (component_packet, component, path) values ("dev-driver", "spi-bus", "basesys/kernel/kernel-xx/driver/spi/");
insert into common_components (component_packet, component, path) values ("dev-driver", "local-bus", "basesys/kernel/kernel-xx/driver/");
insert into common_components (component_packet, component, path) values ("dev-driver", "sata", "basesys/kernel/kernel-xx/driver/scsi/");
insert into common_components (component_packet, component, path) values ("dev-driver", "mmc", "basesys/kernel/kernel-xx/driver/mmc/");
insert into common_components (component_packet, component, path) values ("dev-driver", "flash", "basesys/kernel/kernel-xx/driver/mtd/");
insert into common_components (component_packet, component, path) values ("dev-driver", "uart", "basesys/kernel/kernel-xx/driver/tty/serial");
insert into common_components (component_packet, component, path) values ("dev-driver", "switch", "bsp-driver/package/switch-drv");
insert into common_components (component_packet, component, path) values ("dev-driver", "net-driver", "basesys/kernel/kernel-xx/driver/net");
insert into common_components (component_packet, component, path) values ("kernel", "kernel", "basesys/kernel/kernel-xx/");
insert into common_components (component_packet, component, path) values ("dpdk", "dpdk", "bsp-driver/package/dpdk");
insert into common_components (component_packet, component, path) values ("product", "cpld-upgrade", "bsp-driver/package/firmware-upgrade");
insert into common_components (component_packet, component, path) values ("product", "dual_boot", "basesys/u-boot/uboot-trunk/");
insert into common_components (component_packet, component, path) values ("product", "panic-oops", "bsp-driver/package/utils/sysdiag/");
insert into common_components (component_packet, component, path) values ("product", "libenv", "bsp-driver/package/libs/libenv   ");
insert into common_components (component_packet, component, path) values ("product", "libenv", "basesys/u-boot/uboot-trunk/cmd/simpleui/");
insert into common_components (component_packet, component, path) values ("product", "uart-hotkey", "bsp-driver/package/utils/sysdiag/src/proc_info");
insert into common_components (component_packet, component, path) values ("fda", "fda", "bsp-driver/package/fda");
insert into common_components (component_packet, component, path) values ("fdc", "diag-framework", "mgmt/package/yams/srcs/yams/service/sys_diag.py");
insert into common_components (component_packet, component, path) values ("fdc", "config-analyse", "mgmt/package/yams/srcs/yams/util/config_analyse/");
insert into common_components (component_packet, component, path) values ("fdc", "dev-selftest", "mgmt/package/yams/srcs/yams/service/sys_diag.py");
insert into common_components (component_packet, component, path) values ("fdc", "hw-selftest", "bsp-driver/package/utils/factory");
insert into common_components (component_packet, component, path) values ("fdc", "pkt-trace", "fast-path/package/fp/src/fp-modules/pkt-trace/");
insert into common_components (component_packet, component, path) values ("net-diag", "tcpdump", "baseos/package/utils/tcpdump");
insert into common_components (component_packet, component, path) values ("unwind_stack", "unwind_stack", "bsp-driver/package/libs/unwind_stack");
insert into common_components (component_packet, component, path) values ("dmc", "dmc-framework", "bsp-driver/package/dmc");
insert into common_components (component_packet, component, path) values ("dmc", "plugin-oneclick", "bsp-driver/package/dmc");
insert into common_components (component_packet, component, path) values ("dmc", "plugin-collector", "bsp-driver/package/dmc");
insert into common_components (component_packet, component, path) values ("license", "license", "bsp-driver/package/license");
insert into common_components (component_packet, component, path) values ("upgrade", "system-upgrade", "bsp-driver/package/utils/upgrade");
insert into common_components (component_packet, component, path) values ("upgrade", "patch-manager", "bsp-driver/package/patch-mng");
insert into common_components (component_packet, component, path) values ("upgrade", "libunwind", "bsp-driver/package/utils/libunwind");
insert into common_components (component_packet, component, path) values ("upgrade", "hotpatch-manager", "bsp-driver/package/utils/hotpatch-manager");
insert into common_components (component_packet, component, path) values ("build-framework", "build-framework", "basesys/build-framwork");
insert into common_components (component_packet, component, path) values ("cloud-service", "LOG2CLOUD", "mgmt/package/collectd/srcs/src/log2cloud");
insert into common_components (component_packet, component, path) values ("cloud-service", "online-upgrade", "bsp-driver/package/online-upgrade");
insert into common_components (component_packet, component, path) values ("cloud-service", "cloud", "bsp-driver/package/misc/ntos-cloud");
insert into common_components (component_packet, component, path) values ("factory", "FACTEST", "bsp-driver/package/utils/factory");
insert into common_components (component_packet, component, path) values ("dev-cap", "dev-cap", "bsp-driver/package/libs/devcap");
insert into common_components (component_packet, component, path) values ("dm", "smart-monitor", "bsp-driver/package/utils/dm/src/driver/dev_monitor");
insert into common_components (component_packet, component, path) values ("dm", "system-manager", "bsp-driver/package/utils/dm/src/");
insert into common_components (component_packet, component, path) values ("dm", "optical-manager", "bsp-driver/package/utils/dm/src/driver/sfp");
insert into common_components (component_packet, component, path) values ("dm", "storage-manger", "bsp-driver/package/utils/dm/src/");
insert into common_components (component_packet, component, path) values ("dm", "device-manger", "bsp-driver/package/utils/dm/src/driver");
insert into common_components (component_packet, component, path) values ("jemalloc", "jemalloc", "bsp-driver/package/libs/jemalloc");
insert into common_components (component_packet, component, path) values ("HA-RRGP ", "rrgp", "fast-path/package/ha");
insert into common_components (component_packet, component, path) values ("HA-DataPlane", "rto-sync", "fast-path/package/fp/src/fp-modules/ha");
insert into common_components (component_packet, component, path) values ("HA-MNG", "ISSU", "fast-path/package/ha");
insert into common_components (component_packet, component, path) values ("HA-MNG", "config-sync", "mgmt/package/redis-server");
insert into common_components (component_packet, component, path) values ("HA-MNG", "NFS", "base-os/package/libs/libtirpc");
insert into common_components (component_packet, component, path) values ("HA-MNG", "NFS", "base-os/package/utils/rpcbind");
insert into common_components (component_packet, component, path) values ("HA-MNG", "NFS", "base-os/package/utils/nfs-utils");
insert into common_components (component_packet, component, path) values ("vrrp", "vrrp", "fast-path/package/frr/src/vrrpd");
insert into common_components (component_packet, component, path) values ("vrrp", "vrrp", "mgmt/package/yams/srcs/yams/service/vrrp/");
insert into common_components (component_packet, component, path) values ("ism framework", "ism framework", "fast-path/package/ism");
insert into common_components (component_packet, component, path) values ("lag interface", "lag interface", "fast-path/package/fp/src/fp-modules/lag/");
insert into common_components (component_packet, component, path) values ("fwd(转发引擎)", "fwd-v4(V4引擎)", "fast-path/package/fp/src/fp-modules/ip/");
insert into common_components (component_packet, component, path) values ("fwd(转发引擎)", "fwd-v6(V6引擎)", "fast-path/package/fp/src/fp-modules/ip6/");
insert into common_components (component_packet, component, path) values ("fwd(转发引擎)", "fpn-mem(FP内存管理)", "fast-path/package/fpn-sdk/src/fpn-mempool-dynamic.c");
insert into common_components (component_packet, component, path) values ("fwd(转发引擎)", "fp-scripts(fastpath启动脚本)", "fast-path/package/fp/src/fastpath/arch/dpdk/scripts/");
insert into common_components (component_packet, component, path) values ("fwd(转发引擎)", "fp-debug", "fast-path/package/fp/src/fpdebug/");
insert into common_components (component_packet, component, path) values ("fwd(转发引擎)", "fp-debug", "fast-path/package/fp/src/fp-modules/fp-debug/");
insert into common_components (component_packet, component, path) values ("fwd(转发引擎)", "pkt-trace", "fast-path/package/fp/src/fp-modules/pkt-trace/");
insert into common_components (component_packet, component, path) values ("fwd(转发引擎)", "fp-plugins", "fast-path/package/fp/src/fp-plugins/");
insert into common_components (component_packet, component, path) values ("mode-mng(模式管理)", "fp-monitor(旁路)", "fast-path/package/fp/src/fp-modules/monitor");
insert into common_components (component_packet, component, path) values ("mode-mng(模式管理)", "fp-bridge(桥)", "fast-path/package/fp/src/fp-modules/bridge");
insert into common_components (component_packet, component, path) values ("mode-mng(模式管理)", "fp-route(路由)", "fast-path/package/fp/src/fastpath/fp-ether.c");
insert into common_components (component_packet, component, path) values ("fp-sdk", "fpn-sdk", "fast-path/package/fpn-sdk/");
insert into common_components (component_packet, component, path) values ("fp-sdk", "libfp_shm", "fast-path/package/fp/src/libfp_shm/");
insert into common_components (component_packet, component, path) values ("fp-sdk", "libfpnetfpc", "fast-path/package/fp/src/libnetfpc/");
insert into common_components (component_packet, component, path) values ("fp-sdk", "libfpnetfpc", "fast-path/package/fp/src/fastpath/fp-netfpc/");
insert into common_components (component_packet, component, path) values ("fp-sdk", "libfpnetfpc", "fast-path/package/libnetfpc/");
insert into common_components (component_packet, component, path) values ("fp-sdk", "libfps", "fast-path/package/fp/src/fps/");
insert into common_components (component_packet, component, path) values ("fp-sdk", "libfp-app-ring", "fast-path/package/fp/src/libfp-app-ring/");
insert into common_components (component_packet, component, path) values ("linux-fp-sync", "cmgrd", "fast-path/package/linux-fp-sync/");
insert into common_components (component_packet, component, path) values ("linux-fp-sync", "fpmd", "fast-path/package/fp/src/fpm/");
insert into common_components (component_packet, component, path) values ("linux-fp-sync", "hitflagsd", "fast-path/package/fp/src/hitflagsd/");
insert into common_components (component_packet, component, path) values ("linux-fp-sync", "fptun", "fast-path/package/fp/src/fptun/");
insert into common_components (component_packet, component, path) values ("linux-fp-sync", "libfpvi", "fast-path/package/fpn-sdk/src/fpvi/");
insert into common_components (component_packet, component, path) values ("linux-fp-sync", "libfpvi", "fast-path/package/fp/src/libfpvi_hwinfo/");
insert into common_components (component_packet, component, path) values ("npf(流平台)", "npf-mng", "fast-path/package/fp/src/fp-modules/npf/");
insert into common_components (component_packet, component, path) values ("npf(流平台)", "plugin", "fast-path/package/fp/src/plugin_infra/");
insert into common_components (component_packet, component, path) values ("npf(流平台)", "tcp-reass(TCP重组)", "fast-path/package/fp/src/fp-modules/npf/dataplane/npf_tcp_reass.c");
insert into common_components (component_packet, component, path) values ("npf(流平台)", "tcp-state(TCP状态检查)", "fast-path/package/fp/src/fp-modules/npf/dataplane/npf_state_tcp.c");
insert into common_components (component_packet, component, path) values ("L4-service(4层业务)", "flow-audit(流量审计)", "fast-path/package/fp/src/fp-modules/flow_audit/");
insert into common_components (component_packet, component, path) values ("L4-service(4层业务)", "flow-audit(流量审计)", "mgmt/package/flow_audit/");
insert into common_components (component_packet, component, path) values ("L4-service(4层业务)", "flow-control(流控)", "fast-path/package/fp/src/fp-modules/flow_control/");
insert into common_components (component_packet, component, path) values ("L4-service(4层业务)", "track(链路探测)", "mgmt/package/track-rns/");
insert into common_components (component_packet, component, path) values ("L4-service(4层业务)", "dns-sniff(DNS嗅探)", "fast-path/package/fp/src/fp-modules/dns-sniff/");
insert into common_components (component_packet, component, path) values ("NAT", "NAT", "fast-path/package/fp/src/fp-modules/cgnat/common/");
insert into common_components (component_packet, component, path) values ("NAT", "NAT", "fast-path/package/fp/src/fp-modules/cgnat/dataplane/");
insert into common_components (component_packet, component, path) values ("NAT", "NAT", "fast-path/package/fp/src/fp-modules/npf/dataplane/npf_ctl.c");
insert into common_components (component_packet, component, path) values ("NAT", "NAT", "fast-path/package/fp/src/fp-modules/npf/dataplane/npf_nat.c");
insert into common_components (component_packet, component, path) values ("NAT", "NAT", "fast-path/package/fp/src/fp-modules/npf/dataplane/npf_nat_dynamic.c");
insert into common_components (component_packet, component, path) values ("NAT", "NAT", "fast-path/package/fp/src/fp-modules/npf/dataplane/npf_nat_static.c");
insert into common_components (component_packet, component, path) values ("NAT", "NAT66", "fast-path/package/fp/src/fp-modules/cgnat/common/");
insert into common_components (component_packet, component, path) values ("NAT", "NAT66", "fast-path/package/fp/src/fp-modules/cgnat/dataplane/");
insert into common_components (component_packet, component, path) values ("NAT", "NAT66", "fast-path/package/fp/src/fp-modules/npf/dataplane/npf_ctl.c");
insert into common_components (component_packet, component, path) values ("NAT", "NAT66", "fast-path/package/fp/src/fp-modules/npf/dataplane/npf_nat.c");
insert into common_components (component_packet, component, path) values ("NAT", "NAT66", "fast-path/package/fp/src/fp-modules/npf/dataplane/npf_nat_dynamic.c");
insert into common_components (component_packet, component, path) values ("NAT", "NAT66", "fast-path/package/fp/src/fp-modules/npf/dataplane/npf_nat_static.c");
insert into common_components (component_packet, component, path) values ("NAT", "NAT64", "fast-path/package/fp/src/fp-modules/cgnat/dataplane/");
insert into common_components (component_packet, component, path) values ("NAT", "NAT64", "fast-path/package/fp/src/fp-modules/npf/dataplane/npf_ctl.c");
insert into common_components (component_packet, component, path) values ("NAT", "NAT64", "fast-path/package/fp/src/fp-modules/npf/dataplane/npf_nat.c");
insert into common_components (component_packet, component, path) values ("NAT", "NAT64", "fast-path/package/fp/src/fp-modules/npf/dataplane/npf_nat_dynamic.c");
insert into common_components (component_packet, component, path) values ("NAT", "NAT64", "fast-path/package/fp/src/fp-modules/npf/dataplane/npf_nat_static.c");
insert into common_components (component_packet, component, path) values ("NAT", "NAT64", "fast-path/package/fp/src/fp-modules/npf/dataplane/npf_napt64c.c");
insert into common_components (component_packet, component, path) values ("ALG", "ALG", "fast-path/package/fp/src/fp-modules/npf/dataplane/npf_alg.c");
insert into common_components (component_packet, component, path) values ("ALG", "ALG", "fast-path/package/fp/src/fp-modules/npf/dataplane/npf_alg_ftp.c");
insert into common_components (component_packet, component, path) values ("ALG", "ALG", "fast-path/package/fp/src/fp-modules/npf/dataplane/npf_alg_tftp.c");
insert into common_components (component_packet, component, path) values ("ALG", "ALG", "fast-path/package/fp/src/fp-modules/npf/dataplane/npf_alg_sip.c");
insert into common_components (component_packet, component, path) values ("ALG", "ALG", "fast-path/package/fp/src/fp-modules/npf/dataplane/npf_alg_dns_udp.c");
insert into common_components (component_packet, component, path) values ("ALG", "ALG", "fast-path/package/fp/src/fp-modules/npf/dataplane/npf_conn_expect.c");
insert into common_components (component_packet, component, path) values ("GRE/ERSPAN", "GRE/ERSPAN", "fast-path/package/fp/src/fp-modules/gre/");
insert into common_components (component_packet, component, path) values ("GRE/ERSPAN", "GRE/ERSPAN", "mgmt/package/yams/srcs/yams/service/gre.py");
insert into common_components (component_packet, component, path) values ("PPPoE", "PPPoE", "fast-path/package/pppoe-client/");
insert into common_components (component_packet, component, path) values ("SSLVPN", "SSL", "fast-path/package/sslvpn-server/src/openssl-1.1.1v/");
insert into common_components (component_packet, component, path) values ("SSLVPN", "VGW", "fast-path/package/sslvpn-server/");
insert into common_components (component_packet, component, path) values ("SSLVPN", "VGW", "fast-path/package/sslvpn-server/src/nginx/");
insert into common_components (component_packet, component, path) values ("SSLVPN", "VGW", "fast-path/package/sslvpn-server/src/sslvpn/server/src/");
insert into common_components (component_packet, component, path) values ("SSLVPN", "VGW", "mgmt/package/yams/srcs/yams/service/sslvpn/");
insert into common_components (component_packet, component, path) values ("SSLVPN", "AA", "fast-path/package/sslvpn-server/src/sslvpn/server/src/");
insert into common_components (component_packet, component, path) values ("SSLVPN", "AA", "mgmt/package/yams/srcs/yams/service/sslvpn/sslvpn_author.py");
insert into common_components (component_packet, component, path) values ("SSLVPN", "AA", "mgmt/package/yams/srcs/yams/service/sslvpn/sslvpn_hardid.py");
insert into common_components (component_packet, component, path) values ("SSLVPN", "TUNNEL", "fast-path/package/sslvpn-server/src/sslvpn/server/src/iptunnel/");
insert into common_components (component_packet, component, path) values ("SSLVPN", "TUNNEL", "fast-path/package/fp/src/fp-modules/sslvpn/");
insert into common_components (component_packet, component, path) values ("SSLVPN", "TUNNEL", "mgmt/package/yams/srcs/yams/service/sslvpn/sslvpn_iptun.py");
insert into common_components (component_packet, component, path) values ("IPSEC", "IKEv1", "fast-path/package/ike/");
insert into common_components (component_packet, component, path) values ("IPSEC", "IKEv1", "fast-path/package/ike/src/bin/ikev1/");
insert into common_components (component_packet, component, path) values ("IPSEC", "IKEv2", "fast-path/package/ike/src/bin/ikev2/");
insert into common_components (component_packet, component, path) values ("IPSEC", "VTI", "fast-path/package/fp/src/fp-modules/vti/");
insert into common_components (component_packet, component, path) values ("IPSEC", "VTI", "mgmt/package/yams/srcs/yams/service/vti.py");
insert into common_components (component_packet, component, path) values ("IPSEC", "OAM", "fast-path/package/ike/src/oam/");
insert into common_components (component_packet, component, path) values ("IPSEC", "OAM", "fast-path/package/ike/src/ike-cli/");
insert into common_components (component_packet, component, path) values ("IPSEC", "OAM", "mgmt/package/yams/srcs/yams/service/ike.py");
insert into common_components (component_packet, component, path) values ("IPSEC", "LIB", "fast-path/package/ike/src/libs/");
insert into common_components (component_packet, component, path) values ("IPSEC", "IPSEC TUNNEL", "fast-path/package/fp/src/fp-modules/ipsec");
insert into common_components (component_packet, component, path) values ("security(安全防护)", "black-white-list(黑白名单)", "fast-path/package/fp/src/fp-modules/npf/dataplane/sec-defend/sd-bwlist-v6.c");
insert into common_components (component_packet, component, path) values ("security(安全防护)", "black-white-list(黑白名单)", "fast-path/package/fp/src/fp-modules/npf/dataplane/sec-defend/sd-bwlist-common.c");
insert into common_components (component_packet, component, path) values ("security(安全防护)", "black-white-list(黑白名单)", "fast-path/package/fp/src/fp-modules/npf/dataplane/sec-defend/sd-bwlist.c");
insert into common_components (component_packet, component, path) values ("security(安全防护)", "DDOS", "fast-path/package/fp/src/fp-modules/npf/dataplane/sec-defend/");
insert into common_components (component_packet, component, path) values ("security(安全防护)", "session-limit(会话抑制)", "fast-path/package/fp/src/fp-modules/session-limit");
insert into common_components (component_packet, component, path) values ("security(安全防护)", "ipmac-filter(IP+MAC绑定过滤)", "fast-path/package/fp/src/fp-modules/ip-mac/");
insert into common_components (component_packet, component, path) values ("security(安全防护)", "ipmac-filter(IP+MAC绑定过滤)", "mgmt/package/yams/srcs/yams/service/ip_mac/");
insert into common_components (component_packet, component, path) values ("usr-stack(用户态协议栈)", "tcp-usr-stack(TCP协议栈)", "fast-path/package/fp/src/fp-modules/tcp-udp");
insert into common_components (component_packet, component, path) values ("usr-stack(用户态协议栈)", "tcp-usr-stack(TCP协议栈)", "fast-path/package/fp/src/fp-modules/tcp-udp6");
insert into common_components (component_packet, component, path) values ("usr-stack(用户态协议栈)", "udp-usr-stack(UDP协议栈)", "fast-path/package/fp/src/fp-modules/tcp-udp");
insert into common_components (component_packet, component, path) values ("usr-stack(用户态协议栈)", "udp-usr-stack(UDP协议栈)", "fast-path/package/fp/src/fp-modules/tcp-udp6");
insert into common_components (component_packet, component, path) values ("usr-stack(用户态协议栈)", "ssl-usr-stack(SSL协议栈)", "fast-path/package/fp/src/fp-modules/tls");
insert into common_components (component_packet, component, path) values ("net-measure(度量)", "net-measure(度量)", "mgmt/package/yams/srcs/yams/service/network_measure");
insert into common_components (component_packet, component, path) values ("net-measure(度量)", "net-measure(度量)", "mgmt/package/yams/srcs/libyams/srcs/libnetwork_measure");
insert into common_components (component_packet, component, path) values ("net-measure(度量)", "net-measure(度量)", "fast-path/package/fp/src/fp-modules/network_measure");
insert into common_components (component_packet, component, path) values ("app-parser", "app-parser", "fast-path/package/fp/src/fp-modules/app_parser");
insert into common_components (component_packet, component, path) values ("app-parser", "待拆分，按照不同协议、框架拆分", "fast-path/package/fp/src/fp-modules/app_parser");
insert into common_components (component_packet, component, path) values ("l7-pub", "file-extract", "fast-path/package/fp/src/fp-modules/file_extract");
insert into common_components (component_packet, component, path) values ("l7-pub", "reputation-center", "fast-path/package/fp/src/fp-modules/reputation_center");
insert into common_components (component_packet, component, path) values ("l7-pub", "reputation-center", "mgmt/package/yams/srcs/yams/service/reputation_center");
insert into common_components (component_packet, component, path) values ("sig-update", "sig-update", "fast-path/package/fp/src/fp-modules/sig-update");
insert into common_components (component_packet, component, path) values ("ssl-proxy", "ssl-proxy", "fast-path/package/fp/src/fp-modules/ssl_proxy");
insert into common_components (component_packet, component, path) values ("ssl-proxy", "ssl-proxy", "mgmt/package/yams/srcs/yams/service/ssl_proxy");
insert into common_components (component_packet, component, path) values ("appid", "appid", "fast-path/package/fp/src/fp-modules/app_identify");
insert into common_components (component_packet, component, path) values ("appid", "appid", "mgmt/package/yams/srcs/yams/service/appid.py");
insert into common_components (component_packet, component, path) values ("appid", "appid", "mgmt/package/yams/srcs/yams/service/appid_api.py");
insert into common_components (component_packet, component, path) values ("appid", "appid", "mgmt/package/yams/srcs/yams/service/appid_pub.py");
insert into common_components (component_packet, component, path) values ("content-audit", "content-audit", "fast-path/package/fp/src/fp-modules/content_audit");
insert into common_components (component_packet, component, path) values ("content-audit", "content-audit", "mgmt/package/yams/srcs/yams/service/content_audit");
insert into common_components (component_packet, component, path) values ("content-ctrl", "url-filter", "fast-path/package/fp/src/fp-modules/url_filter");
insert into common_components (component_packet, component, path) values ("content-ctrl", "url-filter", "mgmt/package/yams/srcs/yams/service/url_filter");
insert into common_components (component_packet, component, path) values ("content-ctrl", "content-filter", "fast-path/package/fp/src/fp-modules/content_filter");
insert into common_components (component_packet, component, path) values ("content-ctrl", "content-filter", "mgmt/package/yams/srcs/yams/service/content_filter");
insert into common_components (component_packet, component, path) values ("content-ctrl", "file-filter", "fast-path/package/fp/src/fp-modules/file_filter");
insert into common_components (component_packet, component, path) values ("content-ctrl", "file-filter", "mgmt/package/yams/srcs/yams/service/file_filter");
insert into common_components (component_packet, component, path) values ("l7-security", "pm", "fast-path/package/fp/src/fp-modules/pm");
insert into common_components (component_packet, component, path) values ("l7-security", "ips", "fast-path/package/fp/src/fp-modules/ips");
insert into common_components (component_packet, component, path) values ("l7-security", "ips", "mgmt/package/yams/srcs/yams/service/ips");
insert into common_components (component_packet, component, path) values ("l7-security", "web-sec", "fast-path/package/fp/src/fp-modules/web_security");
insert into common_components (component_packet, component, path) values ("l7-security", "web-sec", "mgmt/package/yams/srcs/yams/service/websec");
insert into common_components (component_packet, component, path) values ("l7-security", "ti", "fast-path/package/fp/src/fp-modules/ti");
insert into common_components (component_packet, component, path) values ("l7-security", "ti", "mgmt/package/yams/srcs/yams/service/ti");
insert into common_components (component_packet, component, path) values ("l7-security", "av", "fast-path/package/fp/src/fp-modules/av");
insert into common_components (component_packet, component, path) values ("l7-security", "av", "mgmt/package/yams/srcs/yams/service/av");
insert into common_components (component_packet, component, path) values ("security-policy(安全策略)", "security-policy(安全策略)", "mgmt/package/yams/srcs/yams/service/security_policy/");
insert into common_components (component_packet, component, path) values ("security-policy(安全策略)", "security-policy(安全策略)", "mgmt/package/libyams/src/libpolicy/");
insert into common_components (component_packet, component, path) values ("security-policy(安全策略)", "security-policy(安全策略)", "fast-path/package/fp/src/fp-modules/security_policy/");
insert into common_components (component_packet, component, path) values ("discovery(自发现)", "discovery(自发现)", "mgmt/package/yams/srcs/yams/service/discovery.py");
insert into common_components (component_packet, component, path) values ("discovery(自发现)", "discovery(自发现)", "mgmt/package/discovery");
insert into common_components (component_packet, component, path) values ("auth（系统账号）", "auth（系统账号）", "mgmt/package/yams/srcs/yams/service/auth.py");
insert into common_components (component_packet, component, path) values ("traffic-analy(流量学习)", "traffic-analy(流量学习)", "mgmt/package/yams/srcs/yams/service/traffic_analy.py");
insert into common_components (component_packet, component, path) values ("traffic-analy(流量学习)", "traffic-analy(流量学习)", "mgmt/package/traffic-analy/");
insert into common_components (component_packet, component, path) values ("service-object(服务对象)", "service-object(服务对象)", "mgmt/package/yams/srcs/yams/service/service_obj");
insert into common_components (component_packet, component, path) values ("service-object(服务对象)", "service-object(服务对象)", "mgmt/package/libyams/src/libservice/");
insert into common_components (component_packet, component, path) values ("service-object(服务对象)", "service-object(服务对象)", "fast-path/package/fp/src/fp-modules/service_object/");
insert into common_components (component_packet, component, path) values ("network-object(地址对象)", "network-object(地址对象)", "mgmt/package/yams/srcs/yams/service/network_obj/");
insert into common_components (component_packet, component, path) values ("network-object(地址对象)", "network-object(地址对象)", "mgmt/package/libyams/src/libnetwork_obj/");
insert into common_components (component_packet, component, path) values ("network-object(地址对象)", "network-object(地址对象)", "fast-path/package/fp/src/fp-modules/network-object/");
insert into common_components (component_packet, component, path) values ("security-zone(域对象)", "security-zone(域对象)", "mgmt/package/yams/srcs/yams/service/security_zone/");
insert into common_components (component_packet, component, path) values ("security-zone(域对象)", "security-zone(域对象)", "mgmt/package/libyams/src/libsecurity_zone/");
insert into common_components (component_packet, component, path) values ("security-zone(域对象)", "security-zone(域对象)", "fast-path/package/fp/src/fp-modules/security_zone/");
insert into common_components (component_packet, component, path) values ("time-range(时间对象)", "time-range(时间对象)", "mgmt/package/yams/srcs/yams/service/time_range/");
insert into common_components (component_packet, component, path) values ("time-range(时间对象)", "time-range(时间对象)", "mgmt/package/libyams/src/libtime_range/");
insert into common_components (component_packet, component, path) values ("user-managerment", "user-managerment", "mgmt/package/yams/srcs/yams/service/user_manage/");
insert into common_components (component_packet, component, path) values ("user-managerment", "user-managerment", "mgmt/package/libyams/src/libusr_mng");
insert into common_components (component_packet, component, path) values ("user-managerment", "user-managerment", "fast-path/package/fp/src/fp-modules/user_mng");
insert into common_components (component_packet, component, path) values ("radius", "radius", "mgmt/package/yams/srcs/yams/service/rdsd.py");
insert into common_components (component_packet, component, path) values ("radius", "radius", "mgmt/package/radius/");
insert into common_components (component_packet, component, path) values ("aaa", "aaa", "mgmt/package/yams/srcs/yams/service/aaa.py");
insert into common_components (component_packet, component, path) values ("aaa", "aaa", "mgmt/package/yams/srcs/yams/service/aaad.py");
insert into common_components (component_packet, component, path) values ("aaa", "aaa", "mgmt/package/aaa/");
insert into common_components (component_packet, component, path) values ("ldap", "ldap", "mgmt/package/yams/srcs/yams/service/ldapd/");
insert into common_components (component_packet, component, path) values ("ldap", "ldap", "mgmt/package/ldap/");
insert into common_components (component_packet, component, path) values ("webauth", "webauth", "mgmt/package/yams/srcs/yams/service/webauth/");
insert into common_components (component_packet, component, path) values ("webauth", "webauth", "mgmt/package/libyams/src/libwebauth/");
insert into common_components (component_packet, component, path) values ("webauth", "webauth", "fast-path/package/fp/src/fp-modules/webauth");
insert into common_components (component_packet, component, path) values ("pbr(策略路由)", "PBR：Policy-base route", "mgmt/package/yams/srcs/yams/service/pbr/");
insert into common_components (component_packet, component, path) values ("pbr(策略路由)", "PBR：Policy-base route", "mgmt/package/libyams/src/libyams_pbr/");
insert into common_components (component_packet, component, path) values ("pbr(策略路由)", "PBR：Policy-base route", "fast-path/package/fp/src/fp-modules/pbr");
insert into common_components (component_packet, component, path) values ("app-route(应用路由)", "app-route", "mgmt/package/yams/srcs/yams/service/pbr/");
insert into common_components (component_packet, component, path) values ("app-route(应用路由)", "app-route", "mgmt/package/libyams/src/libyams_pbr/");
insert into common_components (component_packet, component, path) values ("app-route(应用路由)", "app-route", "fast-path/package/fp/src/fp-modules/pbr");
insert into common_components (component_packet, component, path) values ("rpl(源进源出)", "rpl(源进源出)", "fast-path/package/fp/src/fp-modules/ip/common/fp-l3.c");
insert into common_components (component_packet, component, path) values ("RIP", "rip", "fast-path/package/frr/src/ripd");
insert into common_components (component_packet, component, path) values ("RIPng", "ripng", "fast-path/package/frr/src/ripngd");
insert into common_components (component_packet, component, path) values ("OSPFv2", "ospfv2", "fast-path/package/frr/src/ospfd");
insert into common_components (component_packet, component, path) values ("OSPFv3", "ospfv3", "fast-path/package/frr/src/ospf6d");
insert into common_components (component_packet, component, path) values ("BGP(含BGP4+)", "bgp", "fast-path/package/frr/src/bgpd");
insert into common_components (component_packet, component, path) values ("路由管理", "zebra_route_mgmt", "fast-path/package/frr/src/zebra");
insert into common_components (component_packet, component, path) values ("路由重分发管理", "zebra_route_redistribute", "fast-path/package/frr/src/zebra");
insert into common_components (component_packet, component, path) values ("数据面IPv4路由管理", "fp_route_ipv4", "fast-path/package/fp/src/fp-modules/ip/common");
insert into common_components (component_packet, component, path) values ("数据面IPv6路由管理", "fp_route_ipv6", "fast-path/package/fp/src/fp-modules/ip/common");
insert into common_components (component_packet, component, path) values ("MLLB", "mllb", "mgmt/package/yams/srcs/yams/service/mllb/");
insert into common_components (component_packet, component, path) values ("MLLB", "mllb", "mgmt/package/libyams/src/libmllb/");
insert into common_components (component_packet, component, path) values ("MLLB", "mllb", "fast-path/package/fp/src/fp-modules/mllb");
insert into common_components (component_packet, component, path) values ("snmp", "netsnmp", "mgmt/package/netsnmp");
insert into common_components (component_packet, component, path) values ("cli", "nc-cli", "mgmt/package/nc-cli");
insert into common_components (component_packet, component, path) values ("cli", "libedit", "base-os/package/libs/libedit");
insert into common_components (component_packet, component, path) values ("cli", "libecoli", "mgmt/package/libecoli");
insert into common_components (component_packet, component, path) values ("netconf", "libnetconf2", "mgmt/package/libnetconf2");
insert into common_components (component_packet, component, path) values ("netconf", "web_bg(ncclient)", "mgmt/package/web_bg");
insert into common_components (component_packet, component, path) values ("netconf", "netopeer2", "mgmt/package/netopeer2");
insert into common_components (component_packet, component, path) values ("yams", "yams", "mgmt/package/yams");
insert into common_components (component_packet, component, path) values ("yams", "tidal-forest", "mgmt/package/tidal-forest");
insert into common_components (component_packet, component, path) values ("libyang", "libyang1", "mgmt/package/libyang1");
insert into common_components (component_packet, component, path) values ("libyang", "libyang2", "mgmt/package/libyang");
insert into common_components (component_packet, component, path) values ("libyang", "python-libyang", "mgmt/package/python-libyang");
insert into common_components (component_packet, component, path) values ("libyang", "yang-tools", "mgmt/package/yang-tools");
insert into common_components (component_packet, component, path) values ("sysrepo", "sysrepo", "mgmt/package/sysrepo");
insert into common_components (component_packet, component, path) values ("sysrepo", "python-sysrepo", "mgmt/package/python-sysrepo");
insert into common_components (component_packet, component, path) values ("sysrepo", "sysrepo-plugins", "mgmt/package/sysrepo-plugins");
insert into common_components (component_packet, component, path) values ("sysrepo", "libsysrepo-proxy", "mgmt/package/libsysrepo-proxy");
insert into common_components (component_packet, component, path) values ("redis", "redis-server", "mgmt/package/redis-server");
insert into common_components (component_packet, component, path) values ("redis", "libredis-client", "mgmt/package/libredis-client");
insert into common_components (component_packet, component, path) values ("syslog", "rsyslog", "base-os/package/system/rsyslog");
insert into common_components (component_packet, component, path) values ("syslog", "fp-syslog", "fast-path/package/fp/src/fp-modules/syslog");
insert into common_components (component_packet, component, path) values ("sqlite", "sqlite", "base-os/package/libs/sqlite");
insert into common_components (component_packet, component, path) values ("postgresql", "postgresql", "mgmt/package/postgresql");
insert into common_components (component_packet, component, path) values ("postgresql", "psql", "mgmt/package/postgresql");
insert into common_components (component_packet, component, path) values ("log-collect", "collectd", "mgmt/package/collectd");
insert into common_components (component_packet, component, path) values ("log-collect", "libcollect", "mgmt/package/libcollect");
insert into common_components (component_packet, component, path) values ("log-collect", "log2cloud", "mgmt/package/collectd");
insert into common_components (component_packet, component, path) values ("dhcp", "dhcp-server", "mgmt/package/dhcp/srcs/server");
insert into common_components (component_packet, component, path) values ("dhcp", "dhcp-client", "mgmt/package/dhcp/srcs/client");
insert into common_components (component_packet, component, path) values ("dhcp", "dhcp-relay", "mgmt/package/dhcp/srcs/relay");
insert into common_components (component_packet, component, path) values ("nginx", "nginx", "mgmt/package/nginx");
insert into common_components (component_packet, component, path) values ("ddns", "ddns", "mgmt/package/ddns");
insert into common_components (component_packet, component, path) values ("dns", "dns-client", "mgmt/package/dns-client");
insert into common_components (component_packet, component, path) values ("dns", "libdns-client", "mgmt/package/libdns-client");
insert into common_components (component_packet, component, path) values ("dns", "dns-server", "mgmt/package/bind");
insert into common_components (component_packet, component, path) values ("dns", "dns-proxy", "mgmt/package/bind");
insert into common_components (component_packet, component, path) values ("ntp", "chrony", "base-os/package/utils/chrony");
insert into common_components (component_packet, component, path) values ("libtrusted-host", "libtrusted-host", "mgmt/package/libtrusted-host");
insert into common_components (component_packet, component, path) values ("ssh", "libssh", "base-os/package/libssh");
insert into common_components (component_packet, component, path) values ("ssh", "openssh", "mgmt/package/openssh");
