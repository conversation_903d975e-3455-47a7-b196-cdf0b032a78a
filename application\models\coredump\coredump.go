package coredump

import (
	"irisAdminApi/application/models"
)

type CoredumpTechSupport struct {
	models.ModelBase
	JobID         string `gorm:"not null; type:varchar(100)" json:"job_id" form:"job_id"`
	FileName      string `gorm:"not null; type:varchar(100)" json:"file_name" form:"file_name"`
	Product       string `gorm:"not null; type:varchar(100)" json:"product" form:"product"`
	MD5           string `gorm:"not null; type:varchar(100)" json:"md5" form:"md5"`
	TempDir       string `gorm:"not null; type:varchar(300)" json:"temp_dir" form:"temp_dir"`
	UserID        uint   `gorm:"not null" json:"user_id"`
	Status        uint   `gorm:"not null; default:0" json:"status"` // 0：创建沙箱中  1：创建沙箱完成 2：创建沙箱失败
	RemoteWorkDir string `gorm:"not null; type:varchar(300); default:''" json:"remote_work_dir" form:"remote_work_dir"`
	SecFileID     string `gorm:"not null; type:varchar(100)" json:"sec_file_id" form:"sec_file_id"`
}

type CoredumpJob struct {
	models.ModelBase
	JobID         string `gorm:"not null; type:varchar(100)" json:"job_id"`
	TechSupportID uint   `gorm:"not null" json:"tech_support_id"`
	ProcessName   string `gorm:"not null; type:varchar(100)" json:"process_name"`
	ProcessPath   string `gorm:"not null; type:varchar(300)" json:"process_path"`
	FileName      string `gorm:"not null; type:varchar(100)" json:"file_name"`
	FilePath      string `gorm:"not null; type:varchar(300)" json:"file_path"`
	LogFile       string `gorm:"not null; type:varchar(300)" json:"log_file"`
	Status        uint   `gorm:"not null; default:0" json:"status"`
}
