package qualitypoint

import (
	"encoding/json"
	"fmt"
	"irisAdminApi/application/controllers/openfeishu"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/qualitypoint/dqualityassessmentitem"
	"irisAdminApi/service/dao/qualitypoint/dqualitycategorie"
	"irisAdminApi/service/dao/qualitypoint/dqualitycrosscheckprocdef"
	"irisAdminApi/service/dao/qualitypoint/dqualitydepartment"
	"irisAdminApi/service/dao/qualitypoint/dqualitygroup"
	"irisAdminApi/service/dao/qualitypoint/dqualitypointproc"
	"irisAdminApi/service/dao/qualitypoint/dqualitypointprocdef"
	"irisAdminApi/service/dao/qualitypoint/dqualitypointprocinst"
	"irisAdminApi/service/dao/qualitypoint/dqualitypointproctask"
	"irisAdminApi/service/dao/qualitypoint/dqualitypointsdeduction"
	"irisAdminApi/service/dao/qualitypoint/dqualitypointsrecoverie"
	"irisAdminApi/service/dao/qualitypoint/dqualityuserpoint"
	"irisAdminApi/service/dao/qualitypoint/dqualityviolation"
	"irisAdminApi/service/dao/user/ddepartment"
	"irisAdminApi/service/dao/user/duser"
	"irisAdminApi/service/transaction/qualitypoint/transqualitycrosscheck"
	"irisAdminApi/service/transaction/qualitypoint/transqualitypoint"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

func GetCategoryData(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dqualitycategorie.Response{}, ctx, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetAssessmentItem(ctx iris.Context) {
	categoryID := ctx.FormValue("categoryID")
	directionType := ctx.FormValue("directionType")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	where := []map[string]string{{"column": "category_id", "condition": "=", "value": categoryID},
		{"column": "direction", "condition": "=", "value": directionType}}

	list, err := dao.AllEx(&dqualityassessmentitem.Response{}, ctx, where, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func CreateViolationInst(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx) //事项提报人user_id
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	uuid := libs.GetUUID()
	request := &dqualityviolation.Request{}
	if err := ctx.ReadForm(request); err != nil && !iris.IsErrPath(err) {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	//根据考核项编码获取考核事项信息
	assessmentItem := dqualityassessmentitem.Response{}
	err = assessmentItem.Find(request.AssessmentItemID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	categorie := dqualitycategorie.Response{}
	err = categorie.Find(assessmentItem.CategoryID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	//根据事项责任人id 获取事项责任人信息  所在专业组 所在部门  部门经理ID 专业组组长ID
	//获取事项责任人信息
	profile := &duser.User{}
	err = profile.Profile(request.ResponsiblePersonID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	groupName := profile.Department.Name
	groupID := profile.Department.ID
	departmentName, departmentID := getDepartmentInfo(profile.Department)
	// 获取所在专业组组长和部门经理信息
	groupInfo := dqualitygroup.Response{}
	err = groupInfo.FindByName(groupName)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	groupLeaderID := groupInfo.GroupLeaderID
	departmentInfo := dqualitydepartment.Response{}
	err = departmentInfo.FindByName(departmentName)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	departmentManagerID := departmentInfo.DepartmentManagerID
	//判断是否事项类型 事项类型 2：正向 1：负向
	if request.Direction == 2 {
		//增加消分卡
		//根据规则判断是否增加积分
		//消息通知相关人员
		//事项关闭
		err = transqualitypoint.CreatePositiveViolationTransaction(id, map[string]interface{}{
			"Direction":           request.Direction,
			"IssueLevel":          assessmentItem.IssueLevel,
			"ImpactDegree":        assessmentItem.ImpactDegree,
			"CategoryID":          assessmentItem.CategoryID,
			"AssessmentItemCode":  assessmentItem.Code,
			"AssessmentItemID":    assessmentItem.ID,
			"AssessmentItemName":  assessmentItem.Name,
			"Description":         request.Description,
			"AuditTime":           request.AuditTime,
			"DeductionPoints":     request.ScoreImpact,
			"ResponsiblePersonID": request.ResponsiblePersonID,

			"AuditManagerID":      categorie.AuditManagerID,
			"UserID":              id,
			"DepartmentManagerID": departmentManagerID,
			"TeamLeaderID":        groupLeaderID,
			"Department":          departmentName,
			"Team":                groupName,
			"DepartmentID":        departmentID,
			"TeamID":              groupID,
			"IsRecurrent":         0,
			"CreatedAt":           time.Now(),
			"UpdatedAt":           time.Now(),
			"Uuid":                uuid,
		})
		if err != nil {
			logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
		return
	} else if request.Direction == 1 {
		//获取负向事项流程JSON信息
		defs, err := dqualitypointprocdef.GetNegativeImpactDefs()
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		var qualityManagerID, scoreAdminID int
		qualityManagerID, err = strconv.Atoi(libs.Config.QualityPoint.QualityManagerID)
		if err != nil {
			// 处理转换失败的情况
			logging.ErrorLogger.Errorf("Error while strconv.Atoi ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		scoreAdminID, err = strconv.Atoi(libs.Config.QualityPoint.ScoreAdminID)
		if err != nil {
			// 处理转换失败的情况
			logging.ErrorLogger.Errorf("Error while strconv.Atoi ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		// 解析 JSON 数据
		var nodes []Node
		err = json.Unmarshal([]byte(defs.Resource), &nodes)
		if err != nil {
			logging.ErrorLogger.Errorf("Error while unmarshal json ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		//更新流程节点人员信息,生成流程信息文件
		nodeAssignments := map[string]int{
			"start":                   int(id),
			"abstract_common":         int(request.ResponsiblePersonID),
			"common_review_1":         int(departmentManagerID),
			"common_review_2":         qualityManagerID,
			"repeat_issue_check":      qualityManagerID,
			"deduct_plan":             int(request.ResponsiblePersonID),
			"deduct_review":           qualityManagerID,
			"deduct_evidence":         int(request.ResponsiblePersonID),
			"deduct_execution_review": qualityManagerID,
			"admin_closure":           scoreAdminID,
		}
		// 遍历映射表并调用 modifyAssignee 函数
		for nodeName, userID := range nodeAssignments {
			modifyAssignee(nodes, nodeName, userID)
		}
		// node信息序列化为 JSON
		nodeJSON, err := json.Marshal(nodes)
		if err != nil {
			logging.ErrorLogger.Errorf("Error while marshal json ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		}
		//创建流程节点
		err = transqualitypoint.CreateNegativeImpactViolationTransaction(id, map[string]interface{}{
			"Direction":           request.Direction,
			"IssueLevel":          assessmentItem.IssueLevel,
			"ImpactDegree":        assessmentItem.ImpactDegree,
			"CategoryID":          assessmentItem.CategoryID,
			"AssessmentItemCode":  assessmentItem.Code,
			"AssessmentItemID":    assessmentItem.ID,
			"AssessmentItemName":  assessmentItem.Name,
			"Description":         request.Description,
			"AuditTime":           request.AuditTime,
			"ScoreImpact":         request.ScoreImpact,
			"ResponsiblePersonID": request.ResponsiblePersonID,

			"AuditManagerID":      categorie.AuditManagerID,
			"UserID":              id,
			"DepartmentManagerID": departmentManagerID,
			"TeamLeaderID":        groupLeaderID,
			"Department":          departmentName,
			"Team":                groupName,
			"DepartmentID":        departmentID,
			"TeamID":              groupID,
			"IsRecurrent":         0,
			"CreatedAt":           time.Now(),
			"UpdatedAt":           time.Now(),
			"Uuid":                uuid,
			"Resource":            string(nodeJSON),
		})
		if err != nil {
			logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
		return
	}

}

func GetViolationProcInsts(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")

	procInst := dqualitypointprocinst.Response{}
	list, err := procInst.All(name, status, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetProcInst(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	info := dqualitypointprocinst.Response{}
	err := info.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

func GetViolationProcInstTasks(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")

	list, err := dqualitypointproc.AllTasksByProcInst(id, name, sort, orderBy, page, pageSize, status, start, end)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetProcPrevNodes(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	nodeID := ctx.FormValue("nodeId")
	procInst := dqualitypointprocinst.Response{}
	err := procInst.Find(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	resource := procInst.Resource
	if resource == "" {
		ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
		return
	}
	nodes, err := dqualitypointprocdef.GetNodes(resource)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	result, err := dqualitypointprocdef.GetBeforeNodes(nodes, nodeID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

// 我审计的事项提报
func GetMyAuditSubmissions(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	list, err := dqualitypointprocinst.AllMyAuditData(id, name, status, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

// 我提报的事项
func GetMySubmissions(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	list, err := dqualitypointprocinst.AllMySubmissionsData(id, name, status, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return

}

// 我部门的事项提报
func GetMyDepartmentSubmissions(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	//判断用户是否为小组组长
	groupInfo := dqualitygroup.Response{}
	err = groupInfo.FindEx("group_leader_id", strconv.FormatUint(uint64(id), 10))
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	list := map[string]interface{}{}
	if groupInfo.ID == 0 {
		//判断用户是否为部门管理员
		departmentInfo := dqualitydepartment.Response{}
		err = departmentInfo.FindEx("department_manager_id", strconv.FormatUint(uint64(id), 10))
		fmt.Println("departmentInfo", departmentInfo)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		if departmentInfo.ID == 0 {
			ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
			return
		} else {
			list, err = dqualityviolation.AllMyDepartmentSubmissionsData(id, 2, name, status, sort, orderBy, page, pageSize)
			if err != nil {
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
				return
			}
		}
	} else {
		list, err = dqualityviolation.AllMyDepartmentSubmissionsData(id, 1, name, status, sort, orderBy, page, pageSize)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

// 共性问题库
func GetCommonIssueSubmissions(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	list, err := dqualityviolation.AllCommonIssueSubmissionsData(id, name, status, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return

}

// 更新事项信息
func UpdateViolation(ctx iris.Context) {
	request := &dqualityviolation.Request{}
	if err := ctx.ReadForm(request); err != nil && !iris.IsErrPath(err) {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	//查找该事项信息
	violation := dqualityviolation.Response{}
	err := violation.Find(request.ID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if violation.ID == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "更新失败,未找到该事项"))
		return
	}

	if violation.Status == 1 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "该事项已完结，不可进行操作"))
		return
	}

	//根据考核项编码获取考核事项信息
	assessmentItem := dqualityassessmentitem.Response{}
	err = assessmentItem.Find(request.AssessmentItemID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	categorie := dqualitycategorie.Response{}
	err = categorie.Find(assessmentItem.CategoryID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	//根据事项责任人id 获取事项责任人信息  所在专业组 所在部门  部门经理ID 专业组组长ID
	//获取事项责任人信息
	profile := &duser.User{}
	err = profile.Profile(request.ResponsiblePersonID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	groupName := profile.Department.Name
	groupID := profile.Department.ID
	departmentName, departmentID := getDepartmentInfo(profile.Department)
	// 获取所在专业组组长和部门经理信息
	groupInfo := dqualitygroup.Response{}
	err = groupInfo.FindByName(groupName)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	groupLeaderID := groupInfo.GroupLeaderID
	departmentInfo := dqualitydepartment.Response{}
	err = departmentInfo.FindByName(departmentName)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	departmentManagerID := departmentInfo.DepartmentManagerID
	//判断是否事项类型 事项类型 2：正向 1：负向
	if request.Direction == 2 {
		//增加消分卡
		//根据规则判断是否增加积分
		//消息通知相关人员
		//事项关闭
		err = transqualitypoint.UpdatePositiveViolationTransaction(violation.ID, map[string]interface{}{
			"Direction":           request.Direction,
			"IssueLevel":          assessmentItem.IssueLevel,
			"ImpactDegree":        assessmentItem.ImpactDegree,
			"CategoryID":          assessmentItem.CategoryID,
			"AssessmentItemCode":  assessmentItem.Code,
			"AssessmentItemID":    assessmentItem.ID,
			"AssessmentItemName":  assessmentItem.Name,
			"Description":         request.Description,
			"AuditTime":           request.AuditTime,
			"ScoreImpact":         request.ScoreImpact,
			"ResponsiblePersonID": request.ResponsiblePersonID,

			"AuditManagerID":      categorie.AuditManagerID,
			"DepartmentManagerID": departmentManagerID,
			"TeamLeaderID":        groupLeaderID,
			"Department":          departmentName,
			"Team":                groupName,
			"DepartmentID":        departmentID,
			"TeamID":              groupID,
			"UpdatedAt":           time.Now(),
		})
		if err != nil {
			logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
		return
	} else if request.Direction == 1 {
		//获取负向事项流程JSON信息
		procinst := dqualitypointprocinst.Response{}
		err := procinst.Find(violation.ID)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		var qualityManagerID, scoreAdminID int
		qualityManagerID, err = strconv.Atoi(libs.Config.QualityPoint.QualityManagerID)
		if err != nil {
			// 处理转换失败的情况
			logging.ErrorLogger.Errorf("Error while strconv.Atoi ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		scoreAdminID, err = strconv.Atoi(libs.Config.QualityPoint.ScoreAdminID)
		if err != nil {
			// 处理转换失败的情况
			logging.ErrorLogger.Errorf("Error while strconv.Atoi ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		// 解析 JSON 数据
		var nodes []Node
		err = json.Unmarshal([]byte(procinst.Resource), &nodes)
		if err != nil {
			logging.ErrorLogger.Errorf("Error while unmarshal json ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		//更新流程节点人员信息,生成流程信息文件
		nodeAssignments := map[string]int{
			"abstract_common":         int(request.ResponsiblePersonID),
			"common_review_1":         int(departmentManagerID),
			"common_review_2":         qualityManagerID,
			"repeat_issue_check":      qualityManagerID,
			"deduct_plan":             int(request.ResponsiblePersonID),
			"deduct_review":           qualityManagerID,
			"deduct_evidence":         int(request.ResponsiblePersonID),
			"deduct_execution_review": qualityManagerID,
			"admin_closure":           scoreAdminID,
		}
		// 遍历映射表并调用 modifyAssignee 函数
		for nodeName, userID := range nodeAssignments {
			modifyAssignee(nodes, nodeName, userID)
		}
		// node信息序列化为 JSON
		nodeJSON, err := json.Marshal(nodes)
		if err != nil {
			logging.ErrorLogger.Errorf("Error while marshal json ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		}
		//创建流程节点
		err = transqualitypoint.UpdateNegativeImpactViolationTransaction(violation.ID, map[string]interface{}{
			"Direction":           request.Direction,
			"IssueLevel":          assessmentItem.IssueLevel,
			"ImpactDegree":        assessmentItem.ImpactDegree,
			"CategoryID":          assessmentItem.CategoryID,
			"AssessmentItemCode":  assessmentItem.Code,
			"AssessmentItemID":    assessmentItem.ID,
			"AssessmentItemName":  assessmentItem.Name,
			"Description":         request.Description,
			"AuditTime":           request.AuditTime,
			"ScoreImpact":         request.ScoreImpact,
			"ResponsiblePersonID": request.ResponsiblePersonID,

			"AuditManagerID":      categorie.AuditManagerID,
			"DepartmentManagerID": departmentManagerID,
			"TeamLeaderID":        groupLeaderID,
			"Department":          departmentName,
			"Team":                groupName,
			"DepartmentID":        departmentID,
			"TeamID":              groupID,
			"UpdatedAt":           time.Now(),
			"Resource":            string(nodeJSON),
		})
		if err != nil {
			logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
		return
	}
}

// 我负责的事项
func GetMyResponsibleSubmissions(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	list, err := dqualitypointprocinst.AllMyResponsibleSubmissionsData(id, name, status, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return

}

// 废弃事项
func DiscardSubmission(ctx iris.Context) {
	//查找该事项信息
	id, _ := dao.GetId(ctx)
	violation := dqualityviolation.Response{}
	err := violation.Find(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if violation.ID == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未找到该事项"))
		return
	}
	//判断是否事项类型 事项类型 2：正向 1：负向
	if violation.Direction == 2 {
		err = transqualitypoint.DiscardPositiveViolationTransaction(id)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
		return

	} else if violation.Direction == 1 {
		err = transqualitypoint.DiscardNegativeImpactViolationTransaction(id)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
		return
	}

}

// 获取抽象共性问题数据
func GetAbstractCommonProcTasks(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")
	nodeID := "abstract_common"

	list, err := dqualitypointproc.AllTasksByAssigneeAndTasksNode(uId, nodeID, name, sort, orderBy, page, pageSize, status, start, end)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

type TaskRequest struct {
	Comment                   string  `form:"comment"`
	Status                    uint    `form:"status"`
	NodeID                    string  `form:"nodeId"`
	NextNodeID                string  `form:"nextNodeID"`
	TaskID                    uint    `form:"taskId"`
	UserID                    uint    `form:"userId"`
	IssueDescription          string  `json:"issue_description" form:"issue_description"`
	IsCommonIssue             uint    `json:"is_common_issue" form:"is_common_issue"`
	CommonReviewComments      string  `form:"common_review_comments"`
	ReoccurrenceType          uint    `form:"reoccurrence_type"`
	DeductTaskDescription     string  `form:"deduct_task_description"`
	DeductCompletionTime      string  `form:"deduct_completion_time"`
	DeductEvidenceDescription string  `form:"deduct_evidence_description"`
	DeductEvidenceMaterial    string  `form:"deduct_evidence_material"`
	DeductionPoints           float32 `form:"deduction_points"`
	IsRecurring               uint    `json:"is_recurring" form:"is_recurring"`
}

func UpdateAbstractCommonProcInst(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	procInst := dqualitypointprocinst.Response{}

	err = procInst.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	request := TaskRequest{}
	formValues := ctx.FormValues()
	for key, values := range formValues {
		if len(values) > 0 {
			value := values[0]
			switch key {
			case "status":
				fmt.Sscanf(value, "%d", &request.Status)
			case "nodeId":
				request.NodeID = value
			case "nextNodeID":
				request.NextNodeID = value
			case "userId":
				uintValue, _ := strconv.ParseUint(value, 10, 0)
				request.UserID = uint(uintValue)
			case "taskId":
				uintValue, _ := strconv.ParseUint(value, 10, 0)
				request.TaskID = uint(uintValue)
			case "comment":
				request.Comment = value
			case "issue_description":
				request.IssueDescription = value
			case "is_common_issue":
				fmt.Sscanf(value, "%d", &request.IsCommonIssue)
			case "common_review_comments":
				request.CommonReviewComments = value
			case "reoccurrence_type":
				fmt.Sscanf(value, "%d", &request.ReoccurrenceType)
			case "deduct_task_description":
				request.DeductTaskDescription = value
			case "deduct_completion_time":
				request.DeductCompletionTime = value
			case "deduct_evidence_description":
				request.DeductEvidenceDescription = value
			case "is_recurring":
				fmt.Sscanf(value, "%d", &request.IsRecurring)
			case "deduction_points":
				fmt.Sscanf(value, "%f", &request.DeductionPoints)
			}
		}
	}

	violationObject := map[string]interface{}{
		"UpdatedAt": time.Now(),
	}

	fileName := ""
	logging.DebugLogger.Debugf("update", request.NodeID, request.Status)

	taskObject := map[string]interface{}{
		"NodeID":     request.NodeID,
		"UpdatedAt":  time.Now(),
		"Status":     request.Status,
		"NextNodeID": request.NextNodeID,
		"Comment":    request.Comment,
		"Attachment": fileName,
		"UserID":     request.UserID,
	}
	if request.NodeID == "abstract_common" && request.Status == 1 {
		taskObject["IssueDescription"] = request.IssueDescription
		violationObject["IssueDescription"] = request.IssueDescription
	}
	if request.NodeID == "common_review_1" && request.Status == 1 {
		taskObject["IsCommonIssue"] = request.IsCommonIssue
		taskObject["CommonReviewComments"] = request.CommonReviewComments
	}
	if request.NodeID == "common_review_2" && request.Status == 1 {
		taskObject["IsCommonIssue"] = request.IsCommonIssue
		taskObject["CommonReviewComments"] = request.CommonReviewComments
		violationObject["IsCommonIssue"] = request.IsCommonIssue               //存入事项主表数据
		violationObject["CommonReviewComments"] = request.CommonReviewComments //存入事项主表数据
		//非共性问题，删除流程节点
		if request.IsCommonIssue == 0 {
			// 解析 JSON 数据
			var nodes []Node
			err = json.Unmarshal([]byte(procInst.Resource), &nodes)
			if err != nil {
				logging.ErrorLogger.Errorf("Error while unmarshal json ", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
				return
			}
			// 删除某个节点并调整后续节点
			nodes = deleteNode(nodes, "repeat_issue_check")
			// 序列化为 JSON
			outputJSON, err := json.Marshal(nodes)
			if err != nil {
				logging.ErrorLogger.Errorf("Error marshalling JSON: %v", err)
			}
			//更新节点信息数据
			procInstUp := dqualitypointprocinst.Response{}
			err = procInstUp.Update(procInst.ID, map[string]interface{}{
				"Resource": string(outputJSON),
			})
			if err != nil {
				logging.ErrorLogger.Errorf("Error while Update Resource json ", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "更新节点信息失败"))
				return
			}

		} else if request.IsCommonIssue == 1 {
			//消息通知其它部门经理进行拉通排查进入拉通排查流程
			//获取全部部门数据信息，排查本部分id
			departmentObj := dqualitydepartment.Response{}
			departmentList, err := departmentObj.GetNotifiedDataByDepartmentId(procInst.Qualityviolation.DepartmentID)
			if err != nil {
				logging.ErrorLogger.Errorf("Error while GetNotifiedDataByDepartmentId json ", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "获取部门数据失败"))
				return
			}
			//获取负向事项流程JSON信息
			defs, err := dqualitycrosscheckprocdef.GetNegativeImpactDefs()
			if err != nil {
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
				return
			}
			// 解析 JSON 数据
			var nodes []Node
			err = json.Unmarshal([]byte(defs.Resource), &nodes)
			if err != nil {
				logging.ErrorLogger.Errorf("Error while unmarshal json ", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
				return
			}

			//循环创建拉通排查流程
			for _, department := range departmentList {
				//更新流程节点人员信息,生成流程信息文件
				newNodes := nodes
				nodeAssignments := map[string]int{
					"start":       int(uId),
					"cross_check": int(department.DepartmentManagerID),
				}
				// 遍历映射表并调用 modifyAssignee 函数
				for nodeName, userID := range nodeAssignments {
					modifyAssignee(newNodes, nodeName, userID)
				}
				// node信息序列化为 JSON
				nodeJSON, err := json.Marshal(newNodes)
				if err != nil {
					logging.ErrorLogger.Errorf("Error while marshal json ", err)
					ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
				}
				//创建拉通排查流程
				uuid := libs.GetUUID()
				err = transqualitycrosscheck.CreateCrossCheckTransaction(uId, map[string]interface{}{
					"QualityViolationID":   procInst.QualityViolationID,
					"AuditTime":            procInst.Qualityviolation.AuditTime,
					"Description":          procInst.Qualityviolation.Description,
					"CommonReviewComments": procInst.Qualityviolation.CommonReviewComments,
					"Department":           procInst.Qualityviolation.Department,
					"DepartmentID":         procInst.Qualityviolation.DepartmentID,
					"Team":                 procInst.Qualityviolation.Team,
					"TeamID":               procInst.Qualityviolation.TeamID,
					"DepartmentManagerID":  procInst.Qualityviolation.DepartmentManagerID,
					"ResponsiblePersonID":  procInst.Qualityviolation.ResponsiblePersonID,
					"IssueDescription":     procInst.Qualityviolation.IssueDescription,
					"CreatedAt":            time.Now(),
					"UpdatedAt":            time.Now(),
					"Uuid":                 uuid,
					"Resource":             string(nodeJSON),
				})
				if err != nil {
					logging.ErrorLogger.Errorf("Error while CreateCrossCheckTransaction json ", err)
					ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "创建拉通排查流程失败"))
					return
				}
			}

		}

	}
	//复发问题判断处理
	if request.NodeID == "repeat_issue_check" && request.Status == 1 {
		taskObject["ReoccurrenceType"] = request.ReoccurrenceType
		taskObject["IsRecurrent"] = request.IsRecurring
		violationObject["ReoccurrenceType"] = request.ReoccurrenceType //存入事项主表数据
		violationObject["IsRecurrent"] = request.IsRecurring

		if request.IsRecurring == 1 {
			if err := deductPoints(int(procInst.QualityViolationID)); err != nil {
				logAndRespondError(ctx, "Error while DeductDoublePointsTransaction", err)
				return
			}

			qualityManagerID, scoreAdminID, err := getManagerIDs()
			if err != nil {
				logAndRespondError(ctx, "Error while getting manager IDs", err)
				return
			}

			nodes, err := getProcessNodes()
			if err != nil {
				logAndRespondError(ctx, "Error while getting process nodes", err)
				return
			}

			switch request.ReoccurrenceType {
			case 1:
				handleReoccurrence(nodes, procInst, ctx, uId, qualityManagerID, scoreAdminID, true, true)
			case 2:
				handleReoccurrence(nodes, procInst, ctx, uId, qualityManagerID, scoreAdminID, true, false)
			case 3:
				handleReoccurrence(nodes, procInst, ctx, uId, qualityManagerID, scoreAdminID, false, true)
			}
		}
	}

	//制定消分任务事项计划
	if request.NodeID == "deduct_plan" && request.Status == 1 {
		taskObject["DeductTaskDescription"] = request.DeductTaskDescription
		taskObject["DeductCompletionTime"] = request.DeductCompletionTime
	}
	//消分事项评审
	if request.NodeID == "deduct_review" && request.Status == 1 {
		violationObject["DeductTaskDescription"] = procInst.DeductPlanTasks.DeductTaskDescription //存入事项主表数据
		violationObject["DeductCompletionTime"] = procInst.DeductPlanTasks.DeductCompletionTime   //存入事项主表数据
	}
	//消分任务佐证
	if request.NodeID == "deduct_evidence" && request.Status == 1 {
		f, fh, err := ctx.FormFile("file")

		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		defer f.Close()
		//构造文件名称
		fileName = fh.Filename
		taskObject["DeductEvidenceMaterial"] = fileName
		// 创建最终存放路径以及保存文件
		var upload = filepath.Join(libs.Config.QualityPoint.Upload, procInst.Qualityviolation.CreatedAt.Format("20060102"), procInst.Qualityviolation.Uuid)
		err = os.MkdirAll(upload, 0750)
		os.Chmod(upload, 0750)

		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		//创建申请单
		tempFn := filepath.Join(upload, fileName)
		_, err = ctx.SaveFormFile(fh, tempFn)
		if err != nil {
			logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		taskObject["DeductEvidenceDescription"] = request.DeductEvidenceDescription
	}

	//消分任务执行评审
	if request.NodeID == "deduct_execution_review" && request.Status == 1 {
		taskObject["DeductionPoints"] = request.DeductionPoints
		violationObject["DeductionPoints"] = request.DeductionPoints
		violationObject["DeductEvidenceDescription"] = procInst.DeductEvidenceTasks.DeductEvidenceDescription //存入事项主表数据
		violationObject["DeductEvidenceMaterial"] = procInst.DeductEvidenceTasks.DeductEvidenceMaterial       //存入事项主表数据
	}
	//积分管理核对事项闭环
	if request.NodeID == "admin_closure" && request.Status == 1 {
		//积分管理员闭环关闭事项,处理消分事项
		if procInst.DeductExecutionReviewTasks == nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "消分任务执行评审未执行"))
			return
		}
		err := transqualitypoint.ProcessPointDeductionTransaction(procInst.ID, procInst.DeductExecutionReviewTasks.DeductionPoints)
		if err != nil {
			logging.ErrorLogger.Errorf("Error while PointDeduction ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
	}

	err = transqualitypoint.UpdateViolationTransaction(uId, id, request.TaskID, violationObject, taskObject)
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

// 共性问题评审数据
func GetCommonReviewProcTasks(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")
	nodeID := "common_review_1"

	list, err := dqualitypointproc.AllCommonReviewTasksByAssignee(uId, nodeID, name, sort, orderBy, page, pageSize, status, start, end)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

// 复发问题判断数据获取
func GetRepeatIssueCheckProcTasks(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")
	nodeID := "repeat_issue_check"

	list, err := dqualitypointproc.AllTasksByAssigneeAndTasksNode(uId, nodeID, name, sort, orderBy, page, pageSize, status, start, end)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

// 消分任务事项制定数据
func GetDeductPlanProcTasks(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")
	nodeID := "deduct_plan"

	list, err := dqualitypointproc.AllTasksByAssigneeAndTasksNode(uId, nodeID, name, sort, orderBy, page, pageSize, status, start, end)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

// 消分任务事项评审数据
func GetDeductReviewProcTasks(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")
	nodeID := "deduct_review"

	list, err := dqualitypointproc.AllTasksByAssigneeAndTasksNode(uId, nodeID, name, sort, orderBy, page, pageSize, status, start, end)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

// 消分任务佐证
func GetDeductEvidenceProcTasks(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")
	nodeID := "deduct_evidence"

	list, err := dqualitypointproc.AllTasksByAssigneeAndTasksNode(uId, nodeID, name, sort, orderBy, page, pageSize, status, start, end)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

// 消分任务执行评审
func GetDeductExecutionReviewProcTasks(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")
	nodeID := "deduct_execution_review"

	list, err := dqualitypointproc.AllTasksByAssigneeAndTasksNode(uId, nodeID, name, sort, orderBy, page, pageSize, status, start, end)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

// 下载佐证材料文件
func DownloadEvidenceMaterial(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	procInst := dqualitypointprocinst.Response{}

	err := procInst.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	attachment, err := dqualitypointproctask.FindEvidenceMaterialByProc(id, "deduct_evidence")
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if attachment == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "没有找到文件"))
		return
	}
	var upload = filepath.Join(libs.Config.QualityPoint.Upload, procInst.Qualityviolation.CreatedAt.Format("20060102"), procInst.Qualityviolation.Uuid)
	ctx.SendFile(filepath.Join(upload, attachment), attachment)
	return
}

// 积分管理员数据获取
func GetAdminClosureProcTasks(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")
	nodeID := "admin_closure"

	list, err := dqualitypointproc.AllTasksByAssigneeAndTasksNode(uId, nodeID, name, sort, orderBy, page, pageSize, status, start, end)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

// 获取用户积分信息
func GetUserPointInfo(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	user, err := dqualityuserpoint.FindUserPointInfo(uId)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, user, response.NoErr.Msg))
}

// 获取用户待处理任务
func GetViolationProcTasks(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")

	list, err := dqualitypointproc.AllTasksByAssigneeV2(uId, name, sort, orderBy, page, pageSize, status, start, end)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

// 获取用户消分卡列表数据信息
func GetUserPointsRecoverieDetail(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	currentYear := time.Now().Year()
	pointsRecoverie := dqualitypointsrecoverie.Response{}
	list, err := pointsRecoverie.GetAllByUserIDAndYear(uId, uint(currentYear), name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return

}

//获取用户扣分详细信息

func GetUserPointsDeductionDetail(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	currentYear := time.Now().Year()
	pointsDeduction := dqualitypointsdeduction.Response{}
	list, err := pointsDeduction.GetAllByUserIDAndYear(uId, uint(currentYear), name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

// 推送用户积分数据到多维表格
func SyncQualityPointUserData() {
	tableID := "tblmiAT2Bqw8MbOu"
	BiAppToken := "WqJrb8aXZayupbsIEzzcbj6BnRc"
	//批量删除线上数据
	openfeishu.DeleteTableRecordDataV2(tableID, []string{"年度"}, BiAppToken)
	//获取本地数据
	items, err := dqualityuserpoint.GetUserPointData()
	if err != nil {
		logging.ErrorLogger.Errorf("GetbugDatas error:%s", err.Error())
		return
	}
	//批量同步到线上
	//分批处理新增数据
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			//将新增数据按照批次插入飞书数据表,每批次500条
			for _, reviewdetail := range batchData {
				rec := map[string]interface{}{
					"年度":    reviewdetail.Year,
					"剩余总积分": reviewdetail.TotalPoints,
					"已扣减积分": reviewdetail.PointsDeducted,
					"已消分积分": reviewdetail.PointsRecovered,
				}
				//专业组
				if len(reviewdetail.DepartmentName) > 0 {
					rec["专业组"] = reviewdetail.DepartmentName
				}
				if len(reviewdetail.ParentDepartmentName) > 0 {
					rec["二级部门"] = reviewdetail.ParentDepartmentName
				}
				if len(reviewdetail.Openid) > 0 {
					rec["人员"] = []interface{}{map[string]interface{}{"id": reviewdetail.Openid}}
				}

				records = append(records, rec)
			}
			tableRecordResp, err := openfeishu.BatchCreate(tableID, BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("GetDocumentTypeWorkPacket batchCreate error:%s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

// 推送用户消分卡数据到多维表格
func SyncQualityPointsRecoverieData() {
	tableID := "tbl6Giw0bIxCWb0i"
	BiAppToken := "WqJrb8aXZayupbsIEzzcbj6BnRc"
	//批量删除线上数据
	openfeishu.DeleteTableRecordDataV2(tableID, []string{"年度"}, BiAppToken)
	//获取本地数据
	items, err := dqualitypointsrecoverie.GetUserPointRecoverieData()
	if err != nil {
		logging.ErrorLogger.Errorf("GetbugDatas error:%s", err.Error())
		return
	}
	//批量同步到线上
	//分批处理新增数据
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			//将新增数据按照批次插入飞书数据表,每批次500条
			for _, reviewdetail := range batchData {
				rec := map[string]interface{}{
					"年度":   reviewdetail.Year,
					"事项":   reviewdetail.Description,
					"消分积分": reviewdetail.PointsRecovered,
				}
				if len(reviewdetail.EffectiveDate) > 0 {
					EffectiveDateTimestamp, _ := parseAndConvertToTimestampV3(reviewdetail.EffectiveDate)
					fmt.Println(EffectiveDateTimestamp, reviewdetail.EffectiveDate)
					rec["生效时间"] = EffectiveDateTimestamp
				}
				if len(reviewdetail.DeductTaskDescription) > 0 {
					rec["消分任务描述"] = reviewdetail.DeductTaskDescription
				}
				if len(reviewdetail.DeductEvidenceDescription) > 0 {
					rec["消分任务佐证说明"] = reviewdetail.DeductEvidenceDescription
				}
				//专业组
				if len(reviewdetail.DepartmentName) > 0 {
					rec["专业组"] = reviewdetail.DepartmentName
				}
				if len(reviewdetail.ParentDepartmentName) > 0 {
					rec["二级部门"] = reviewdetail.ParentDepartmentName
				}
				if len(reviewdetail.Openid) > 0 {
					rec["人员"] = []interface{}{map[string]interface{}{"id": reviewdetail.Openid}}
				}

				records = append(records, rec)
			}
			tableRecordResp, err := openfeishu.BatchCreate(tableID, BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("GetDocumentTypeWorkPacket batchCreate error:%s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

// 推送用户扣分数据到多维表格
func SyncQualityPointsDeductionData() {
	tableID := "tbl1RC8UYJJXwnCo"
	BiAppToken := "WqJrb8aXZayupbsIEzzcbj6BnRc"
	//批量删除线上数据
	openfeishu.DeleteTableRecordDataV2(tableID, []string{"年度"}, BiAppToken)
	//获取本地数据
	items, err := dqualitypointsdeduction.GetUserPointDeductionData()
	if err != nil {
		logging.ErrorLogger.Errorf("GetbugDatas error:%s", err.Error())
		return
	}
	//批量同步到线上
	//分批处理新增数据
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			//将新增数据按照批次插入飞书数据表,每批次500条
			for _, reviewdetail := range batchData {
				rec := map[string]interface{}{
					"年度":    reviewdetail.Year,
					"事项":    reviewdetail.Description,
					"扣减积分":  reviewdetail.PointsDeducted,
					"考核项编号": reviewdetail.AssessmentItemCode,
					"考核项名称": reviewdetail.AssessmentItemName,
					"问题等级":  reviewdetail.IssueLevel,
					"影响程度":  reviewdetail.ImpactDegree,
				}
				//专业组
				if len(reviewdetail.DepartmentName) > 0 {
					rec["专业组"] = reviewdetail.DepartmentName
				}
				if len(reviewdetail.ParentDepartmentName) > 0 {
					rec["二级部门"] = reviewdetail.ParentDepartmentName
				}
				if len(reviewdetail.Openid) > 0 {
					rec["人员"] = []interface{}{map[string]interface{}{"id": reviewdetail.Openid}}
				}

				records = append(records, rec)
			}
			tableRecordResp, err := openfeishu.BatchCreate(tableID, BiAppToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("GetDocumentTypeWorkPacket batchCreate error:%s", err.Error())
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

func getDepartmentInfo(department *ddepartment.Department) (string, uint) {
	var name string
	var id uint
	name = department.Name
	id = department.ID

	// 获取所在部门并判断是否以“组”结尾
	if strings.HasSuffix(name, "组") {
		department, err := ddepartment.FindById(department.ParentID)
		if err != nil {
			logging.ErrorLogger.Errorf("find parent department err: %v", err)
			return "", 0
		}
		name = department.Name
		id = department.ID
	}

	return name, id
}

type Node struct {
	Name       string `json:"name"`
	NodeID     string `json:"nodeId"`
	PrevNodeID string `json:"prevNodeId"`
	Assignee   int    `json:"assignee"`
}

// 修改节点的 Assignee 值
func modifyAssignee(nodes []Node, nodeID string, newAssignee int) {
	for i, node := range nodes {
		if node.NodeID == nodeID {
			nodes[i].Assignee = newAssignee
		}
	}
}

// 删除节点并调整后续节点的前置节点
func deleteNode(nodes []Node, nodeID string) []Node {
	var updatedNodes []Node
	var prevNodeID string

	// 找出要删除节点的 PrevNodeID
	for _, node := range nodes {
		if node.NodeID == nodeID {
			prevNodeID = node.PrevNodeID
			break
		}
	}

	// 更新所有后续节点的 PrevNodeID
	for _, node := range nodes {
		if node.NodeID != nodeID {
			if node.PrevNodeID == nodeID {
				node.PrevNodeID = prevNodeID
			}
			updatedNodes = append(updatedNodes, node)
		}
	}

	return updatedNodes
}

func deductPoints(violationID int) error {
	return transqualitypoint.DeductDoublePointsTransaction(uint(violationID))
}

func getManagerIDs() (int, int, error) {
	qualityManagerID, err := strconv.Atoi(libs.Config.QualityPoint.QualityManagerID)
	if err != nil {
		return 0, 0, err
	}
	scoreAdminID, err := strconv.Atoi(libs.Config.QualityPoint.ScoreAdminID)
	if err != nil {
		return 0, 0, err
	}
	return qualityManagerID, scoreAdminID, nil
}

func getProcessNodes() ([]Node, error) {
	defs, err := dqualitypointprocdef.GetHandleJointLiabilityDefs()
	if err != nil {
		return nil, err
	}

	var nodes []Node
	err = json.Unmarshal([]byte(defs.Resource), &nodes)
	return nodes, err
}

func handleReoccurrence(nodes []Node, procInst dqualitypointprocinst.Response, ctx iris.Context, uId uint, qualityManagerID, scoreAdminID int, includeTeamLeader, includeDepartmentManager bool) {
	if includeTeamLeader {
		if err := createTransaction(nodes, procInst, ctx, uId, qualityManagerID, scoreAdminID, true); err != nil {
			logAndRespondError(ctx, "Error while creating team leader transaction", err)
			return
		}
	}

	if includeDepartmentManager {
		if err := createTransaction(nodes, procInst, ctx, uId, qualityManagerID, scoreAdminID, false); err != nil {
			logAndRespondError(ctx, "Error while creating department manager transaction", err)
			return
		}
	}
}

func createTransaction(nodes []Node, procInst dqualitypointprocinst.Response, ctx iris.Context, uId uint, qualityManagerID, scoreAdminID int, isTeamLeader bool) error {
	nodeAssignments := getNodeAssignments(procInst, qualityManagerID, scoreAdminID, isTeamLeader)
	modifiedNodes := modifyNodes(nodes, nodeAssignments)

	resourceJSON, err := json.Marshal(modifiedNodes)
	if err != nil {
		return err
	}

	return transqualitypoint.CreateNegativeImpactViolationTransaction(uId, getTransactionData(procInst, isTeamLeader, string(resourceJSON), uId))
}

func getNodeAssignments(procInst dqualitypointprocinst.Response, qualityManagerID, scoreAdminID int, isTeamLeader bool) map[string]int {
	if isTeamLeader {
		return map[string]int{
			"deduct_plan":             int(procInst.Qualityviolation.TeamLeaderID),
			"deduct_review":           qualityManagerID,
			"deduct_evidence":         int(procInst.Qualityviolation.TeamLeaderID),
			"deduct_execution_review": qualityManagerID,
			"admin_closure":           scoreAdminID,
		}
	}
	return map[string]int{
		"deduct_plan":             int(procInst.Qualityviolation.DepartmentManagerID),
		"deduct_review":           qualityManagerID,
		"deduct_evidence":         int(procInst.Qualityviolation.DepartmentManagerID),
		"deduct_execution_review": qualityManagerID,
		"admin_closure":           scoreAdminID,
	}
}

func modifyNodes(nodes []Node, assignments map[string]int) []Node {
	for nodeName, userID := range assignments {
		modifyAssignee(nodes, nodeName, userID)
	}
	return nodes
}

func getTransactionData(procInst dqualitypointprocinst.Response, isTeamLeader bool, resourceJSON string, uId uint) map[string]interface{} {
	scoreImpact := procInst.Qualityviolation.ScoreImpact
	responsiblePersonID := procInst.Qualityviolation.TeamLeaderID
	if !isTeamLeader {
		scoreImpact /= 3
		responsiblePersonID = procInst.Qualityviolation.DepartmentManagerID
	} else {
		scoreImpact /= 2
	}

	return map[string]interface{}{
		"Direction":           3,
		"IssueLevel":          procInst.Qualityviolation.IssueLevel,
		"ImpactDegree":        procInst.Qualityviolation.ImpactDegree,
		"CategoryID":          procInst.Qualityviolation.CategoryID,
		"AssessmentItemCode":  procInst.Qualityviolation.AssessmentItemCode,
		"AssessmentItemID":    procInst.Qualityviolation.AssessmentItemID,
		"AssessmentItemName":  procInst.Qualityviolation.AssessmentItemName,
		"Description":         "【连带责任复发问题】【" + procInst.Qualityviolation.Description + "】",
		"AuditTime":           procInst.Qualityviolation.AuditTime,
		"ScoreImpact":         scoreImpact,
		"ResponsiblePersonID": responsiblePersonID,
		"AuditManagerID":      procInst.Qualityviolation.AuditManagerID,
		"UserID":              uId,
		"DepartmentManagerID": procInst.Qualityviolation.DepartmentManagerID,
		"TeamLeaderID":        procInst.Qualityviolation.TeamLeaderID,
		"Department":          procInst.Qualityviolation.Department,
		"Team":                procInst.Qualityviolation.Team,
		"DepartmentID":        procInst.Qualityviolation.DepartmentID,
		"TeamID":              procInst.Qualityviolation.TeamID,
		"IsRecurrent":         0,
		"CreatedAt":           time.Now(),
		"UpdatedAt":           time.Now(),
		"Uuid":                libs.GetUUID(),
		"Resource":            resourceJSON,
	}
}

func logAndRespondError(ctx iris.Context, message string, err error) {
	logging.ErrorLogger.Errorf(message, err)
	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
}

func parseAndConvertToTimestampV3(dateStr string) (int64, error) {
	// 解析字符串为 time.Time 类型
	t, err := time.Parse(time.RFC3339, dateStr)
	if err != nil {
		return 0, err
	}

	// 将 time.Time 类型转换为 Unix 时间戳
	return t.UnixMilli(), nil
}
