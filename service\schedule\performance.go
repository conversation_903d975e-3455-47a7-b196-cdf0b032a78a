package schedule

import (
	"irisAdminApi/application/controllers/dfx"
	"irisAdminApi/application/controllers/performance"
	"irisAdminApi/application/logging"
)

func PerformanceSchedule() {
	// performance.SendWeekReport()
	// performance.SendMail()
	// performance.SendWeekReport()
	// dfx.SendWeekReport()

	_, err := Cron.AddFunc("0 9 * * *", func() { performance.SendMail() })
	if err != nil {
		logging.ErrorLogger.Error("add send performance daily check cron err ", err)
	}

	_, err = Cron.AddFunc("0 9 * * 5", func() { performance.SendWeekReport() })
	if err != nil {
		logging.ErrorLogger.Error("add send performance week report cron err ", err)
	}

	_, err = Cron.AddFunc("0 9 * * 5", func() { dfx.SendWeekReport() })
	if err != nil {
		logging.ErrorLogger.Error("add send performance week report cron err ", err)
	}
}
