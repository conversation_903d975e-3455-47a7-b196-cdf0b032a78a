package dresource

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strings"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/datasync"
	"irisAdminApi/service/dao/datasync/dsyncrecord"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "资源表"

type ResourceSyncResponse struct {
	State   string              `json:"state"`
	Data    []*ResourceResponse `json:"data"`
	Total   int                 `json:"total"`
	Message string              `json:"message"`
}

type ResourceDeleteSyncResponse struct {
	State   string                    `json:"state"`
	Data    []*ResourceDeleteResponse `json:"data"`
	Total   int                       `json:"total"`
	Message string                    `json:"message"`
}

type ResourceDeleteResponse struct {
	RowNum     int    `json:"rownum"`
	ID         int    `json:"id"`
	SummaryID  int    `gorm:"primarykey; autoIncrement:false" json:"summaryId" `
	DeleteDate string `gorm:"not null; type:varchar(100)" json:"deleteDate" update:"1"`
}

type ResourceResponse struct {
	RowNum         int     `json:"rownum"`
	ResourceID     int     `gorm:"primarykey; autoIncrement:false" json:"id" `
	DivisionName   string  `gorm:"not null; type:varchar(100)" json:"divisionName" update:"1"`
	ProjectName    string  `gorm:"not null; type:varchar(200)" json:"projectName" update:"1"`
	DepartmentName string  `gorm:"not null; type:varchar(100)" json:"departmentName" update:"1"`
	GroupName      string  `gorm:"not null; type:varchar(100)" json:"groupName" update:"1"`
	UserName       string  `gorm:"not null; type:varchar(100)" json:"userName" update:"1"`
	WorkClassName  string  `gorm:"not null; type:varchar(200)" json:"workClassName" update:"1"`
	StageName      string  `gorm:"not null; type:varchar(200)" json:"stageName" update:"1"`
	ActivityName   string  `gorm:"not null; type:varchar(200)" json:"activityName" update:"1"`
	TaskName       string  `gorm:"not null; type:varchar(200)" json:"taskName" update:"1"`
	ProduceValue   string  `gorm:"not null; type:varchar(60)" json:"produceValue" update:"1"`
	ReportDate     string  `gorm:"not null; type:varchar(60)" json:"reportDate" update:"1"`
	Year           int     `gorm:"not null" json:"year" update:"1"`
	Month          int     `gorm:"not null" json:"month" update:"1"`
	WorkTime       float32 `gorm:"not null" json:"workTime" update:"1"`
	AddTime        float32 `gorm:"not null" json:"addTime" update:"1"`
	TotalTime      float32 `gorm:"not null" json:"totalTime" update:"1"`
	Remarks        string  `gorm:"not null; type:varchar(2000)" json:"remarks" update:"1"`
	TemplateID     int     `json:"templateId" update:"1"`
	WorkpacketName string  `json:"workpacketName" update:"1"`
}

type ResourceSummary struct {
	ProjectName string  `json:"project_name"`
	UserName    string  `json:"user_name"`
	GroupName   string  `json:"group_name"`
	WorkTime    float32 `json:"work_time"`
}

type Resource struct {
	datasync.Resource
}

type ListResponse struct {
	Resource
}

type Request struct {
	Id uint `json:"id"`
}

func (this *Resource) ModelName() string {
	return ModelName
}

func Model() *datasync.Resource {
	return &datasync.Resource{}
}

func (this *Resource) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *Resource) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *Resource) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Resource) CreateV2(object interface{}) error {
	return nil
}

func (this *Resource) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *Resource) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *Resource) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *Resource) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *Resource) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *Resource) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func BatchCreateOrUpdate(records []map[string]interface{}) error {
	xt := reflect.TypeOf(&datasync.Bug{})
	columns := []string{}
	for i := 0; i < xt.Elem().NumField(); i++ {
		key, ok := xt.Elem().Field(i).Tag.Lookup("update")
		if ok {
			columns = append(columns, key)
		}
	}
	db := easygorm.GetEasyGormDb().Model(Model())

	err := db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "bug_id"}},
		DoUpdates: clause.AssignmentColumns(columns),
	}).Create(&records).Error
	if err != nil {
		return err
	}

	return nil
}

func TransStageName(stageName string) string {
	stageMap := map[string]string{
		"启动": "1-启动阶段",
		"计划": "1-启动阶段",
		"模块": "2-执行阶段",
		"集成": "3-集成阶段",
		"内部": "4-内测阶段",
		"发布": "5-发布阶段",
	}
	var _stageName string
	stageSlice := strings.Split(stageName, "")
	if len(stageSlice) > 2 {
		_stageName = strings.Join(stageSlice[0:2], "")
	} else {
		_stageName = stageName
	}
	if v, ok := stageMap[_stageName]; ok {
		return v
	} else {
		return stageName
	}
}

func UpdateOrCreateResourceTransaction(items []*ResourceResponse, _url string, data map[string]string, method, state, errorMsg string) error {
	objects := []map[string]interface{}{}
	for _, item := range items {
		object := map[string]interface{}{
			"ResourceID":     item.ResourceID,
			"DivisionName":   item.DivisionName,
			"ProjectName":    item.ProjectName,
			"DepartmentName": item.DepartmentName,
			"GroupName":      item.GroupName,
			"UserName":       item.UserName,
			"WorkClassName":  item.WorkClassName,
			"StageName":      item.StageName,
			"ActivityName":   item.ActivityName,
			"TaskName":       item.TaskName,
			"ProduceValue":   item.ProduceValue,
			"ReportDate":     item.ReportDate,
			"Year":           item.Year,
			"Month":          item.Month,
			"WorkTime":       item.WorkTime,
			"AddTime":        item.AddTime,
			"TotalTime":      item.TotalTime,
			"Remarks":        item.Remarks,
			"StageNameClean": TransStageName(item.StageName),
			"TemplateID":     item.TemplateID,
			"WorkpacketName": item.WorkpacketName,
		}

		objects = append(objects, object)
	}

	columns := []string{}

	xt := reflect.TypeOf(Model())
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")
		if ok && ok2 {
			columns = append(columns, name)
		}
	}

	body, err := json.Marshal(data)
	if err != nil {
		return err
	}
	db := easygorm.GetEasyGormDb()
	err = db.Transaction(func(tx *gorm.DB) error {
		if len(objects) > 0 {
			err := tx.Model(Model()).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "resource_id"}},
				DoUpdates: clause.AssignmentColumns(columns),
			}).Create(&objects).Error
			if err != nil {
				return err
			}
		}

		if err := tx.Model(dsyncrecord.Model()).Create(map[string]interface{}{
			"url":             _url,
			"body":            body,
			"method":          method,
			"state":           state,
			"message":         errorMsg,
			"min_modify_date": data["minModifyDate"],
			"max_modify_date": data["maxModifyDate"],
			"created_at":      time.Now(),
		}).Error; err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func UpdateOrCreateResourceDeleteTransaction(items []*ResourceDeleteResponse, _url string, data map[string]string, method, state, errorMsg string) error {
	ids := []int{}
	for _, item := range items {
		ids = append(ids, item.SummaryID)
	}
	body, err := json.Marshal(data)
	if err != nil {
		return err
	}
	db := easygorm.GetEasyGormDb()
	err = db.Transaction(func(tx *gorm.DB) error {
		if len(ids) > 0 {
			err := tx.Model(Model()).Delete("resource_id in ?", ids).Error
			if err != nil {
				return err
			}
		}

		if err := tx.Model(dsyncrecord.Model()).Create(map[string]interface{}{
			"url":             _url,
			"body":            body,
			"method":          method,
			"state":           state,
			"message":         errorMsg,
			"min_modify_date": data["minModifyDate"],
			"max_modify_date": data["maxModifyDate"],
			"created_at":      time.Now(),
		}).Error; err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func GetLastWeekResourceSummary(by, order string) ([]*ResourceSummary, error) {
	now := time.Now()

	// 计算上周一
	startOfWeek := now.AddDate(0, 0, -int(now.Weekday())-6) // 上周一
	// 计算上周天
	endOfWeek := now.AddDate(0, 0, -int(now.Weekday())) // 上周天

	items := []*ResourceSummary{}
	selects := []string{
		"if(project_name != '' and project_name is not null, project_name, '其他') project_name",
		"user_name",
		"group_name",
		"sum(work_time) work_time",
	}

	db := easygorm.GetEasyGormDb().
		Table("resources").
		Select(selects).
		Group("project_name, user_name, group_name").
		Order(fmt.Sprintf("%s %s", by, order))

	where := easygorm.GetEasyGormDb().Where("report_date >= ? and report_date <= ?", startOfWeek.Format("2006-01-02"), endOfWeek.Format("2006-01-02"))
	err := db.Where(where).Find(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

type ResourceDetail struct {
	UserName       string  `json:"user_name"`
	DivisionName   string  `json:"division_name"`
	DepartmentName string  `json:"department_name"`
	GroupName      string  `json:"group_name"`
	ProjectName    string  `json:"project_name"`
	WorkClassName  string  `json:"work_class_name"`
	StageName      string  `json:"stage_name"`
	ActivityName   string  `json:"activity_name"`
	TaskName       string  `json:"task_name"`
	ProduceValue   string  `json:"produce_value"`
	ReportDate     string  `json:"report_date"`
	Year           int     `json:"year"`
	Month          int     `json:"month"`
	TemplateID     int     `json:"template_id"`
	WorkTime       float32 `json:"work_time"`
	AddTime        float32 `json:"add_time"`
	TotalTime      float32 `json:"total_time"`
	Remarks        string  `json:"remarks"`
	StageNameClean string  `json:"stage_name_clean"`
}

func GetResourceDetailData(star, end string) ([]*ResourceDetail, error) {
	items := []*ResourceDetail{}
	sql := fmt.Sprintf(`SELECT
	user_name,
	division_name,
	department_name,
	group_name,
	project_name,
	work_class_name,
	stage_name,
	activity_name,
	task_name,
	produce_value,
	report_date,
	YEAR as 'year' ,
	MONTH as 'month',
	template_id,
	work_time,
	add_time,
	total_time,
	remarks,
	stage_name_clean
FROM
	resources
WHERE
	report_date >= '%s'
	AND report_date <= '%s'
	`, star, end)
	err := easygorm.GetEasyGormDb().Table("resources").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}
