package ddetection

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/featurerelease"
	"irisAdminApi/service/dao/user/duser"
)

const ModelName = "规则库ips检测任务"

type Response struct {
	ID uint `json:"id"`

	FileName           string `json:"file_name"`             //文件名
	FileSize           uint   `json:"file_size"`             //文件大小
	FileMd5            string `json:"file_md5"`              //文件Md5值
	FileType           uint   `json:"file_type"`             //文件类型
	FileOriginName     string `json:"file_origin_name" `     //原始文件名称
	FileUnzipDirectory string `json:"file_unzip_directory" ` //文件解压目录

	ResultJsonFile    string                  `json:"result_json_file"`     //检测结果json文件路径
	ResultLogFile     string                  `json:"result_log_file"`      //检测结果日志文件路径
	ResultLogFileName string                  `json:"result_log_file_name"` //检测结果日志文件文件名
	UserID            uint                    `json:"user_id"`              //上传用户ID
	Status            uint                    `json:"status"`               // 检测任务状态  0:运行，1：成功， 2：失败
	DetectionStatus   uint                    `json:"detection_status"`     // 规则库检测状态  0:运行，1：通过， 2：不通过
	UpdatedAt         string                  `json:"updated_at"`           //创建时间
	CreatedAt         string                  `json:"created_at"`           //更新时间
	User              *duser.ApprovalResponse `gorm:"-" json:"user"`
}

type ListResponse struct {
	Response
}

type Request struct {
	FileName           string `json:"file_name" form:"file_name"`
	FileSize           uint   `json:"file_size" form:"file_size"`
	FileMd5            string `json:"file_md5" form:"file_md5"`
	FileType           uint   `json:"file_type" form:"file_type"`
	ResultJsonFile     string `json:"result_json_file" form:"result_json_file"`
	ResultLogFile      string `json:"result_log_file" form:"result_log_file"`
	FileOriginName     string `json:"file_origin_name" form:"file_origin_name"`
	FileUnzipDirectory string `json:"file_unzip_directory" form:"file_unzip_directory"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *featurerelease.DetectionList {
	return &featurerelease.DetectionList{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	formatResponses(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func AllDetection(uid uint, name, sort, orderBy string, page, pageSize int, status, start, end string) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	// db = db.Where("user_id = ?", uid)

	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	if len(status) > 0 {
		// if status == "0" {
		// 	db = db.Where("status = ?", status)
		// } else {
		// 	db = db.Where("status > 0")
		// }
		db = db.Where("status = ?", status)
	}
	if len(start) > 0 {
		db = db.Where("updated_at >= ?", start)
	}
	if len(end) > 0 {
		db = db.Where("updated_at <= ?", end)
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	formatResponses(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindAll() ([]*Response, error) {
	var items []*Response

	if err := easygorm.GetEasyGormDb().Model(Model()).Find(&items).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return items, err
	}
	return items, nil
}

func FindInIds(ids []uint) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id in ?", ids).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	return items, nil
}

func UpdateStatus(id uint, object map[string]interface{}) error {

	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func formatResponses(items []*ListResponse) {

	userIds := []uint{}
	for _, item := range items {
		userIds = append(userIds, item.UserID)
	}
	users, _ := duser.FindSimpleInIds(userIds)
	var userMap = make(map[uint]*duser.ApprovalResponse)
	for _, user := range users {
		userMap[user.Id] = user
	}
	for _, item := range items {
		item.User = userMap[item.UserID]
	}

}
