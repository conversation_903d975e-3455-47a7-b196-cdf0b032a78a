package models

import (
	"time"
)

// ServiceConfig 共享服务配置结构体
type ServiceConfig struct {
	AppID               string `json:"app_id"`
	AppSecret           string `json:"app_secret"`
	BiAppToken          string `json:"bi_app_token"`
	MeetingAppToken     string `json:"meeting_app_token"`
	MeetingTableID      string `json:"meeting_table_id"`
	DefaultCalendarID   string `json:"default_calendar_id"`
	DefaultTimezone     string `json:"default_timezone"`
	AutoNotifyAttendees bool   `json:"auto_notify_attendees"`
}

// FeiShuConfig 飞书配置接口（用于类型转换）
type FeiShuConfig interface {
	GetAppID() string
	GetAppSecret() string
	GetBiAppToken() string
	GetMeetingAppToken() string
	GetMeetingTableID() string
	GetDefaultCalendarID() string
	GetDefaultTimezone() string
	GetAutoNotifyAttendees() bool
}

// NewServiceConfig 创建服务配置实例
func NewServiceConfig(appID, appSecret, biAppToken, meetingAppToken, meetingTableID, defaultCalendarID, defaultTimezone string, autoNotifyAttendees bool) *ServiceConfig {
	return &ServiceConfig{
		AppID:               appID,
		AppSecret:           appSecret,
		BiAppToken:          biAppToken,
		MeetingAppToken:     meetingAppToken,
		MeetingTableID:      meetingTableID,
		DefaultCalendarID:   defaultCalendarID,
		DefaultTimezone:     defaultTimezone,
		AutoNotifyAttendees: autoNotifyAttendees,
	}
}

// MeetingRecord 会议记录数据结构
type MeetingRecord struct {
	RecordID string                 `json:"record_id"`
	Fields   map[string]interface{} `json:"fields"`
	RawData  map[string]interface{} `json:"raw_data"`
}

// StandardMeetingData 标准化会议数据结构
type StandardMeetingData struct {
	RecordID              string    `json:"record_id"`
	Title                 string    `json:"title"`
	StartTime             time.Time `json:"start_time"`
	EndTime               time.Time `json:"end_time"`
	Location              string    `json:"location"`
	Description           string    `json:"description"`
	AttendeeNames         string    `json:"attendee_names"` // 参会人员名称字符串（用|分割）
	AttendeeOpenIDs       []string  `json:"attendee_open_ids"`
	AddAttendeeOpenIDs    []string  `json:"add_attendee_open_ids"`    // 需要添加的参会人员OpenID列表
	RemoveAttendeeOpenIDs []string  `json:"remove_attendee_open_ids"` // 需要删除的参会人员OpenID列表
	Status                string    `json:"status"`
	EventID               string    `json:"event_id"`      // 日程ID（用于更新和删除操作）
	EventURL              string    `json:"event_url"`     // 日程链接
	ErrorMessage          string    `json:"error_message"` // 错误信息
	IsAllDay              bool      `json:"is_all_day"`
	Timezone              string    `json:"timezone"`

	// 新增操作控制字段（使用中文）
	OperationType   string `json:"操作类型"` // "创建"、"更新"、"删除"
	OperationStatus string `json:"操作状态"` // "待处理"、"处理中"、"已完成"、"失败"
}

// CalendarEvent 日历事件结构
type CalendarEvent struct {
	EventID     string    `json:"event_id"`
	CalendarID  string    `json:"calendar_id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	StartTime   time.Time `json:"start_time"`
	EndTime     time.Time `json:"end_time"`
	Location    string    `json:"location"`
	Attendees   []string  `json:"attendees"`
	IsAllDay    bool      `json:"is_all_day"`
	Timezone    string    `json:"timezone"`
	EventURL    string    `json:"event_url"`
}

// ProcessResult 处理结果结构（日程ID回填增强）
type ProcessResult struct {
	RecordID     string    `json:"record_id"`
	Status       string    `json:"status"`        // "success" | "failed"
	EventID      string    `json:"event_id"`      // 日程ID回填增强：飞书日历事件ID
	EventURL     string    `json:"event_url"`     // 日程ID回填增强：飞书日程链接
	ErrorMessage string    `json:"error_message"` // 错误信息
	ProcessTime  time.Time `json:"process_time"`  // 处理时间
	RetryCount   int       `json:"retry_count"`   // 重试次数

	// 扩展字段 - 日程管理功能
	OperationType    string     `json:"operation_type"`    // "create" | "update" | "cancel"
	CancelStatus     string     `json:"cancel_status"`     // "active" | "cancelled"
	CancelledAt      *time.Time `json:"cancelled_at"`      // 取消时间
	CancelReason     string     `json:"cancel_reason"`     // 取消原因
	ChangedFields    []string   `json:"changed_fields"`    // 变更字段列表
	NotificationSent bool       `json:"notification_sent"` // 是否已发送通知
	Version          int        `json:"version"`           // 版本号

	// 实际参会人员信息（用于回填多维表格）
	ActualAttendeeOpenIDs []string `json:"actual_attendee_open_ids"` // 实际参会人员OpenID列表
	ActualAttendeeCount   int      `json:"actual_attendee_count"`    // 实际参会人员数量
}

// BatchProcessResult 批量处理结果
type BatchProcessResult struct {
	TotalCount   int             `json:"total_count"`
	SuccessCount int             `json:"success_count"`
	FailedCount  int             `json:"failed_count"`
	Results      []ProcessResult `json:"results"`
	StartTime    time.Time       `json:"start_time"`
	EndTime      time.Time       `json:"end_time"`
	Duration     time.Duration   `json:"duration"`
}

// EventUpdates 日程更新数据结构
type EventUpdates struct {
	Title           *string    `json:"title,omitempty"`
	Description     *string    `json:"description,omitempty"`
	StartTime       *time.Time `json:"start_time,omitempty"`
	EndTime         *time.Time `json:"end_time,omitempty"`
	Location        *string    `json:"location,omitempty"`
	AttendeeOpenIDs []string   `json:"attendee_open_ids,omitempty"` // 目标参会人员状态（用于状态同步）
	AddAttendees    []string   `json:"add_attendees,omitempty"`     // 增量添加参会人员（兼容性）
	RemoveAttendees []string   `json:"remove_attendees,omitempty"`  // 增量删除参会人员（兼容性）
	Timezone        *string    `json:"timezone,omitempty"`
}

// OperationLog 操作日志结构
type OperationLog struct {
	ID            string                 `json:"id"`
	EventID       string                 `json:"event_id"`
	RecordID      string                 `json:"record_id"`
	OperationType string                 `json:"operation_type"`
	UserID        string                 `json:"user_id"`
	Changes       map[string]interface{} `json:"changes"`
	Result        string                 `json:"result"`
	ErrorMessage  string                 `json:"error_message"`
	Timestamp     time.Time              `json:"timestamp"`
	Duration      int64                  `json:"duration_ms"`
}

// FieldMapping 字段映射配置
type FieldMapping struct {
	BitableFieldName  string      `json:"bitable_field_name"`
	StandardFieldName string      `json:"standard_field_name"`
	FieldType         string      `json:"field_type"`
	Required          bool        `json:"required"`
	DefaultValue      interface{} `json:"default_value"`
}

// CreateEventRequest 创建事件请求结构
type CreateEventRequest struct {
	Summary          string          `json:"summary"`
	Description      string          `json:"description"`
	StartTime        EventTime       `json:"start_time"`
	EndTime          EventTime       `json:"end_time"`
	Location         *EventLocation  `json:"location,omitempty"`
	NeedNotification bool            `json:"need_notification"`
	Attendees        []EventAttendee `json:"attendees,omitempty"`
	Timezone         string          `json:"timezone"`
	VChat            *VChatInfo      `json:"vchat,omitempty"` // 新增：视频会议信息
}

// EventTime 事件时间结构
type EventTime struct {
	Timestamp string `json:"timestamp,omitempty"` // 秒级时间戳
	Date      string `json:"date,omitempty"`      // 全天事件日期 YYYY-MM-DD
	Timezone  string `json:"timezone,omitempty"`  // 时区
}

// EventLocation 事件地点结构
type EventLocation struct {
	Name      string  `json:"name,omitempty"`
	Address   string  `json:"address,omitempty"`
	Latitude  float64 `json:"latitude,omitempty"`
	Longitude float64 `json:"longitude,omitempty"`
}

// EventAttendee 事件参会人员结构
type EventAttendee struct {
	Type       string `json:"type"`        // "user"
	UserID     string `json:"user_id"`     // open_id
	IsOptional bool   `json:"is_optional"` // 是否可选参会
}

// VChatInfo 视频会议信息结构
type VChatInfo struct {
	VCType          string           `json:"vc_type"`                    // 视频会议类型: "vc", "third_party", "no_meeting"
	IconType        string           `json:"icon_type,omitempty"`        // 第三方视频会议icon类型
	Description     string           `json:"description,omitempty"`      // 第三方视频会议文案
	MeetingURL      string           `json:"meeting_url,omitempty"`      // 视频会议URL
	MeetingSettings *MeetingSettings `json:"meeting_settings,omitempty"` // 飞书视频会议设置
}

// MeetingSettings 飞书视频会议设置结构
type MeetingSettings struct {
	OwnerID               string   `json:"owner_id,omitempty"`                // 会议owner的用户ID
	JoinMeetingPermission string   `json:"join_meeting_permission,omitempty"` // 入会范围
	AssignHosts           []string `json:"assign_hosts,omitempty"`            // 主持人用户ID列表
	AutoRecord            bool     `json:"auto_record"`                       // 是否开启自动录制
	OpenLobby             bool     `json:"open_lobby"`                        // 是否开启等候室
	AllowAttendeesStart   bool     `json:"allow_attendees_start"`             // 是否允许参与者发起会议
}

// AddAttendeesRequest 添加参会人员请求结构
type AddAttendeesRequest struct {
	Attendees        []EventAttendee `json:"attendees"`
	NeedNotification bool            `json:"need_notification"`
}

// UpdateRecordRequest 更新记录请求结构
type UpdateRecordRequest struct {
	RecordID string                 `json:"record_id"`
	Fields   map[string]interface{} `json:"fields"`
}

// BatchUpdateRequest 批量更新请求结构
type BatchUpdateRequest struct {
	Records []UpdateRecordRequest `json:"records"`
}

// APIResponse 通用API响应结构
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// ErrorInfo 错误信息结构
type ErrorInfo struct {
	Code      string    `json:"code"`
	Message   string    `json:"message"`
	Details   string    `json:"details"`
	Timestamp time.Time `json:"timestamp"`
}

// RetryConfig 重试配置
type RetryConfig struct {
	MaxRetries    int           `json:"max_retries"`
	BaseDelay     time.Duration `json:"base_delay"`
	MaxDelay      time.Duration `json:"max_delay"`
	BackoffFactor float64       `json:"backoff_factor"`
}

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	RequestsPerSecond int           `json:"requests_per_second"`
	BurstSize         int           `json:"burst_size"`
	Timeout           time.Duration `json:"timeout"`
}

// ValidationRule 验证规则
type ValidationRule struct {
	FieldName       string   `json:"field_name"`
	Required        bool     `json:"required"`
	MinLength       int      `json:"min_length"`
	MaxLength       int      `json:"max_length"`
	Pattern         string   `json:"pattern"`
	AllowedValues   []string `json:"allowed_values"`
	CustomValidator string   `json:"custom_validator"`
}

// MeetingConfig 会议配置结构（扩展现有配置）
type MeetingConfig struct {
	// 基础配置
	Enabled           bool   `json:"enabled"`
	MeetingTableID    string `json:"meeting_table_id"`
	DefaultCalendarID string `json:"default_calendar_id"`
	DefaultTimezone   string `json:"default_timezone"`

	// 通知配置
	AutoNotifyAttendees bool `json:"auto_notify_attendees"`

	// 处理配置
	BatchSize      int           `json:"batch_size"`
	ProcessTimeout time.Duration `json:"process_timeout"`
	MaxConcurrency int           `json:"max_concurrency"`

	// 重试和限流配置
	RetryConfig     RetryConfig     `json:"retry_config"`
	RateLimitConfig RateLimitConfig `json:"rate_limit_config"`

	// 字段映射配置
	FieldMappings []FieldMapping `json:"field_mappings"`

	// 验证规则
	ValidationRules []ValidationRule `json:"validation_rules"`
}

// CancelEventRequest 取消日程请求结构
type CancelEventRequest struct {
	EventID         string `json:"event_id" validate:"required"`
	CancelReason    string `json:"cancel_reason" validate:"required,max=500"`
	NotifyAttendees bool   `json:"notify_attendees"`
	UserID          string `json:"user_id"`
}

// CancelEventResponse 取消日程响应结构
type CancelEventResponse struct {
	Success          bool      `json:"success"`
	EventID          string    `json:"event_id"`
	CancelledAt      time.Time `json:"cancelled_at"`
	NotificationSent bool      `json:"notification_sent"`
	Message          string    `json:"message"`
}

// UpdateEventRequest 更新日程请求结构
type UpdateEventRequest struct {
	EventID         string       `json:"event_id" validate:"required"`
	Updates         EventUpdates `json:"updates" validate:"required"`
	NotifyAttendees bool         `json:"notify_attendees"`
	UserID          string       `json:"user_id"`
	CheckConflicts  bool         `json:"check_conflicts"`
}

// UpdateEventResponse 更新日程响应结构
type UpdateEventResponse struct {
	Success          bool      `json:"success"`
	EventID          string    `json:"event_id"`
	UpdatedAt        time.Time `json:"updated_at"`
	ChangedFields    []string  `json:"changed_fields"`
	NotificationSent bool      `json:"notification_sent"`
	Message          string    `json:"message"`
}

// GetEventResponse 获取日程响应结构
type GetEventResponse struct {
	Success bool           `json:"success"`
	Event   *CalendarEvent `json:"event,omitempty"`
	Message string         `json:"message"`
}

// Constants 常量定义
const (
	// 处理状态
	StatusPending    = "pending"
	StatusProcessing = "processing"
	StatusSuccess    = "success"
	StatusFailed     = "failed"
	StatusSkipped    = "skipped" // 跳过处理（如参会人员为空）

	// 操作类型
	OperationTypeCreate = "create"
	OperationTypeUpdate = "update"
	OperationTypeCancel = "cancel"

	// 取消状态
	CancelStatusActive    = "active"
	CancelStatusCancelled = "cancelled"

	// 字段类型
	FieldTypeText         = "text"
	FieldTypeDateTime     = "datetime"
	FieldTypeUser         = "user"
	FieldTypeSingleSelect = "single_select"
	FieldTypeMultiSelect  = "multi_select"

	// 默认配置值
	DefaultBatchSize      = 50
	DefaultProcessTimeout = 5 * time.Minute
	DefaultTimezone       = "Asia/Shanghai"
	DefaultCalendarID     = "<EMAIL>"

	// Redis键前缀
	RedisKeyPrefix     = "meeting:"
	RedisRequestPrefix = "meeting:request:"
	RedisStatusPrefix  = "meeting:status:"
	RedisLockPrefix    = "meeting:lock:"

	// 错误码
	ErrCodeInvalidRequest  = "INVALID_REQUEST"
	ErrCodeConfigError     = "CONFIG_ERROR"
	ErrCodeAPIError        = "API_ERROR"
	ErrCodeProcessingError = "PROCESSING_ERROR"
)

// OperationTypeMap 操作类型中英文映射
var OperationTypeMap = map[string]string{
	OperationTypeCreate: "创建",
	OperationTypeUpdate: "更新",
	OperationTypeCancel: "删除",
}

// GetOperationTypeChinese 获取操作类型的中文描述
func GetOperationTypeChinese(operationType string) string {
	if chinese, exists := OperationTypeMap[operationType]; exists {
		return chinese
	}
	// 如果已经是中文，直接返回
	if operationType == "创建" || operationType == "更新" || operationType == "删除" {
		return operationType
	}
	return operationType // 返回原值
}

// GetOperationTypeEnglish 获取操作类型的英文常量
func GetOperationTypeEnglish(operationType string) string {
	// 反向映射
	reverseMap := map[string]string{
		"创建": OperationTypeCreate,
		"更新": OperationTypeUpdate,
		"删除": OperationTypeCancel,
	}
	if english, exists := reverseMap[operationType]; exists {
		return english
	}
	// 如果已经是英文常量，直接返回
	if operationType == OperationTypeCreate || operationType == OperationTypeUpdate || operationType == OperationTypeCancel {
		return operationType
	}
	return operationType // 返回原值
}
