package gcovreport

import "irisAdminApi/application/models"

type CommonNtosFileComponent struct {
	models.ModelBase
	FullPath        string `gorm:"not null; type:varchar(300)" json:"full_path"`
	File            string `gorm:"not null; type:varchar(200)" json:"file"`
	Path            string `gorm:"not null; type:varchar(300)" json:"path"`
	ComponentPacket string `gorm:"not null; type:varchar(200)" json:"component_packet"`
	Component       string `gorm:"not null; type:varchar(200)" json:"component"`
}

type CommonComponent struct {
	models.ModelBase
	Path            string `gorm:"not null; type:varchar(500)" json:"path"`
	ComponentPacket string `gorm:"not null; type:varchar(200)" json:"component_packet"`
	Component       string `gorm:"not null; type:varchar(200)" json:"component"`
	Owner           string `gorm:"type:varchar(50)" json:"owner"`
	Comment         string `gorm:"type:varchar(50)" json:"comment"`
}
