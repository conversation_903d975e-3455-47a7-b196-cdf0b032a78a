package buildfarm

import "irisAdminApi/application/models"

type Project struct {
	models.ModelBase
	Name         string `gorm:"uniqueIndex; not null; type:varchar(100)" json:"name"`
	GitlabId     uint   `gorm:"not null" json:"gitlab_id"`
	Repo         string `gorm:"uniqueIndex; not null; type:varchar(100)" json:"repo"`
	TaskType     uint   `gorm:"not null" json:"task_type"`                         //编译类型，1：产品编译  2：组件编译
	Enable       uint   `gorm:"not null" json:"enable"`                            // 启用 0：禁用， 1：启用
	PatchEnable  uint   `gorm:"not null" json:"patch_enable"`                      // 启用 0：禁用， 1：启用
	PatchDirName string `gorm:"not null; type:varchar(100)" json:"patch_dir_name"` // patch命令执行的最上级目录名，即在pro_xxxx目录中的目录名
	Owner        uint   `gorm:"not null"`
	EnableCov    bool   `gorm:"not null;default:false" json:"enable_cov"`
}

type BuildfarmProjectInfo struct {
	models.ModelBase
	ProjectID uint   `gorm:"index:idx_project_info, unique; not null;" json:"project_id"`
	Branch    string `gorm:"index:idx_project_info, unique; not null; type:varchar(100)" json:"branch"`
	Product   string `gorm:"index:idx_project_info, unique; not null; type:varchar(100)" json:"product"`
	Defconfig string `gorm:"index:idx_project_info, unique; not null; type:varchar(100)" json:"defconfig"`
	Target    string `gorm:"index:idx_project_info, unique; not null; type:varchar(100)" json:"target"`
	Feed      string `json:"feed"`
}

type BuildfarmJob struct {
	models.ModelBase
	JobID     string `gorm:"not null; type:varchar(60)" json:"job_id"`
	ProjectID uint   `gorm:"not null" json:"project_id"`
	ServerID  uint   `gorm:"index;not null" json:"server_id"`
	Source    uint   `gorm:"not null" json:"source"` // 作业来源： 1. 普通编译  2. 每日编译  3.生测编译  4.商业代码检查
	Project   string `gorm:"not null; type:varchar(100)" json:"project"`
	Repo      string `gorm:"not null; type:varchar(100)" json:"repo"`
	Branch    string `gorm:"not null; type:varchar(100)" json:"branch"`

	TaskType uint `gorm:"not null" json:"task_type"` //任务类型，1：产品编译  2：组件编译

	Product   string `gorm:"not null; type:varchar(60)" json:"product"`
	Defconfig string `gorm:"not null; type:varchar(60)" json:"defconfig"`
	Target    string `gorm:"not null; type:varchar(60)" json:"target"`

	Status    uint   `gorm:"not null" json:"status"`                       // 作业状态 3：排队，0：运行， 1：成功， 2: 失败
	BuildType string `gorm:"not null; type:varchar(60)" json:"build_type"` // 编译类型： debug, performance, factory
	UserID    uint   `gorm:"not null" json:"user_id"`

	SoftwareNumber  string `gorm:"type:varchar(60)" json:"software_nubmer"`
	SoftwareVersion string `gorm:"type:varchar(60)" json:"software_version"`
	SmokeStatus     string `gorm:"type:varchar(2000)" json:"smoke_status"`
}

/*
    步骤：
	0. 未开始
	1. git clone    进行中/成功/失败
	2. make download   进行中/成功/失败
	3. 定制/debug/release/patch等前置动作   进行中/成功/失败
	4. make defconfig/make install-devel  进行中/成功/失败
	5. make target  进行中/成功/失败
	6. make pack-all 进行中/成功/失败
	7. 归档 进行中/成功/失败
	8. 收集信息  进行中/成功/失败
*/

// type Defconfig struct {
// 	models.ModelBase
// 	ProjectID uint   `gorm:"not null" json:"project_id"`
// 	Branch    string `gorm:"uniqueIndex; not null; type:varchar(100)" json:"branch"`
// 	Name      string `gorm:"uniqueIndex; not null; type:varchar(100)" json:"name"`
// 	Target    string `gorm:"uniqueIndex; not null; type:varchar(100)" json:"target"`
// }
