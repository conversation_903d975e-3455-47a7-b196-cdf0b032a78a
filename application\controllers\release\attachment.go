package release

import (
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/release/dattachment"
	"net/url"
	"path/filepath"

	"github.com/kataras/iris/v12"
)

func Download(ctx iris.Context) {
	id, _ := dao.GetId(ctx)

	attachment := dattachment.Response{}
	err := easygorm.GetEasyGormDb().Model(dattachment.Model()).Where("release_project_id = ? AND type = 1", id).Find(&attachment).Error
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	fp := filepath.Join(libs.Config.FileStorage.Upload, "sow", attachment.Name)
	ctx.SendFile(fp, url.QueryEscape(attachment.OriginName))
	return

}
