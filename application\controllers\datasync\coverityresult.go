package datasync

var RgciInstance *Rgci

type Rgci struct {
	Authorization string
}

func (rgci *Rgci) CheckLogin() (bool, error) {
	return true, nil
}

/*
select * from (SELECT cr.* FROM fileOut.coverity_results AS cr
WHERE project = 'NTOS1.0R11' and checker_name in (select checker_name from fileOut.coverity_rules cr2 where epg_level = 'epg')
ORDER BY cr.id DESC) cr
left join (select case when comment="是" then concat(component, "(开源)") when comment="否" then component when comment != '' then concat(component, "(", comment, ")") end component, owner
from (select component, owner, comment from common_components cc  group by component, owner, comment) cc) cc on cc.component = cr.component_name
*/
