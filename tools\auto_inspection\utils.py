import datetime
import http.client
import mimetypes
import os


def upload_file(filename, data=None):
    # 连接到服务器
    conn = http.client.HTTPConnection("************", 9090)

    # 读取文件内容
    with open(filename, "rb") as file:
        file_content = file.read()

    # 获取文件的MIME类型
    mime_type, _ = mimetypes.guess_type(filename)
    if mime_type is None:
        mime_type = "application/octet-stream"

    # 构建multipart/form-data的内容
    boundary = "----WebKitFormBoundaryrGKCBY7qhFd3TrwA"
    file_data = "--" + boundary + "\r\n"
    file_data += 'Content-Disposition: form-data; name="file"; filename="%s"\r\n' % os.path.basename(filename)
    file_data += "Content-Type: %s\r\n" % mime_type
    file_data += "\r\n"
    file_data += file_content.decode("utf-8")
    file_data += "\r\n"

    if data and isinstance(data, dict):
        for k, v in data.items():
            file_data += "--" + boundary + "\r\n"
            file_data += 'Content-Disposition: form-data; name="%s"\r\n' % k
            file_data += "\r\n"
            file_data += v
            file_data += "\r\n"

    file_data += "--" + boundary + "--"
    print(file_data)
    # 构建请求头
    headers = {"Content-Type": "multipart/form-data; boundary=" + boundary}

    # 构建请求体
    body = file_data.encode("utf-8")

    # 发送请求
    conn.request("POST", "/inspection-api/api/v1/inspection/upload", body, headers)

    # 接收响应
    response = conn.getresponse()
    data = response.read()

    # 打印响应内容
    print(data.decode("utf8"))

    # 关闭连接
    conn.close()


def get_now_str():
    return datetime.datetime.now().strftime("%Y%m%d%H%M%S")


common_commands = {
    "01": {
        "name": "检查categraf是否安装",
        "command": "ls /opt/categraf",
    },
    "02": {
        "name": "检查categraf进程是否运行",
        "command": "ps -ef | grep '/opt/categraf/categraf'",
    },
    "03": {
        "name": "检查与nightingale(监控服务器)连通性",
        "command": "nc -zv ************ 17000 -w 3 || nc -zv ************* 17000 -w 3",
    },
    "04": {
        "name": "检查可登录帐号",
        "command": "cat /etc/passwd|grep -v nologin|grep -v '/bin/false'",
    },
    "05": {
        "name": "检查可登录帐号上次修改密码时间",
        "command": "cat /etc/passwd|grep -v nologin|grep -v '/bin/false'|awk -F ':' '{print $1}'|xargs -I {} chage -l {}",
    },
    "06": {
        "name": "硬件告警日志",
        "command": "ipmitool sel elist",
    },
    "07": {
        "name": "内核错误日志",
        "command": "dmesg | grep -i -E 'error|warning'",
    },
    "08": {
        "name": "系统错误日志",
        "command": "cat /var/log/syslog | grep -i error || cat /var/log/message | grep -i error",
    },
    "09": {
        "name": "挂载点信息",
        "command": "df -h|grep -v tmpfs|grep -v /dev/loop|grep -v overlay",
    },
    "10": {
        "name": "smartctl检查",
        "command": """for i in `lsblk|grep disk|awk '{print $1}'`;do echo "/dev/$i:"  `smartctl -H /dev/$i|grep 'test result'`;done""",
    },
    "11": {
        "name": "megacli_RAID检查",
        "command": """dmesg|grep MegaRAID && megacli -cfgdsply -aALL|grep -E 'DISK GROUP:|State'|grep -v Foreign""",
    },
    "12": {
        "name": "megacli_磁盘检查",
        "command": """dmesg|grep MegaRAID && megacli -cfgdsply -aALL|grep -i -E 'Physical Disk:|Slot Number:|Device Id:|Firmware state'""",
    },
}

backup_commands = {"************": "cat /var/log/backup/rsync_"}

switch_commands = {
    "01": {"name": "硬件状态", "command": "show power"},
    "02": {"name": "日志", "command": "show log"},
}
