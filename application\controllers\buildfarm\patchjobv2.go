package buildfarm

import (
	"bufio"
	"bytes"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/buildfarm"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/buildfarm/dbuildfarmproductcpu"
	"irisAdminApi/service/dao/buildfarm/dcronmakejob"
	"irisAdminApi/service/dao/buildfarm/dmakejob"
	"irisAdminApi/service/dao/buildfarm/dpatchdefconfig"
	"irisAdminApi/service/dao/buildfarm/dpatchjob"
	"irisAdminApi/service/dao/buildfarm/dserver"
	"irisAdminApi/service/dao/buildfarm/dsoftversions"
	"math/rand"
	"net/url"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gabriel-vasile/mimetype"
	"github.com/kataras/iris/v12"
)

func GetPatchJobsV2(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx) //获取用户id
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	// name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	userId := fmt.Sprintf("%d", id) //用户id
	where := []map[string]string{{"column": "user_id", "condition": "=", "value": userId}, {"column": "patch_version", "condition": "=", "value": "2"}}

	list, err := dao.AllEx(&dpatchjob.PatchJobListResponse{}, ctx, where, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func CreatePatchJobV2(ctx iris.Context) {
	//校验用户权限
	uID, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	request := &dpatchjob.Request{}
	if err := ctx.ReadForm(request); err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		logging.ErrorLogger.Errorf("create patchjob read json err ", strings.Join(errs, ";"))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	cpus, err := dbuildfarmproductcpu.FindDistinctCpuByProduct(strings.Split(request.BuildName, "|"))
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	aarches := []string{}
	for _, cpu := range cpus {
		if !libs.InArrayS(aarches, strings.Split(cpu, "-")[1]) {
			aarches = append(aarches, strings.Split(cpu, "-")[1])
		}
	}
	if len(aarches) == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "适配产品型号不存在，联系管理员进行添加"))
		return
	}
	if len(aarches) > 1 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "适配产品型号不能同时包含多种架构，如：aarch64、x86_64"))
		return
	}
	//编译服务器选择
	serverId := ChoosePatchServer()
	if serverId == 0 {
		logging.ErrorLogger.Errorf("create patchjob serverID err ")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "没用可用的服务器"))
		return
	}
	//todo: url还是手动上传
	tempName := libs.GetUUID()
	var fileName, tempCompileDir, upload string
	upload = filepath.Join(libs.Config.PatchFileStorage.Upload, "tar", time.Now().Format("20060102"))
	err = os.MkdirAll(upload, 0o750)
	os.Chmod(upload, 0o750)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	//编译目录生成
	parentPath := libs.Config.PatchFileStorage.CompilePath
	tempCompileDir = filepath.Join(parentPath, tempName)
	tempDir := libs.Config.PatchFileStorage.Temp + "patch/" + time.Now().Format("20060102") + "/"
	err = os.MkdirAll(tempDir, 0o750)
	os.Chmod(tempDir, 0o750)

	if request.Type == "生产" {
		var jobID string
		switch request.MakeFrom {
		case "每日编译":
			cronMakeJob := dcronmakejob.CronMakeJob{}
			err := cronMakeJob.Find(request.CronMakeJobID)
			if err != nil {
				logging.ErrorLogger.Errorf("get cron job by id err ", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
				return
			}

			if cronMakeJob.Id == 0 {
				ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "未到找记录"))
				return
			}
			jobID = cronMakeJob.JobId
		case "发布归档":
			cronMakeJob := dcronmakejob.CronMakeJob{}
			err := cronMakeJob.Find(request.CronMakeJobID)
			if err != nil {
				logging.ErrorLogger.Errorf("get cron job by id err ", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
				return
			}

			if cronMakeJob.Id == 0 {
				ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "未到找记录"))
				return
			}
			jobID = cronMakeJob.JobId
		case "定制编译":
			makeJob := dmakejob.MakeJob{}
			err := makeJob.Find(request.CronMakeJobID)
			if err != nil {
				logging.ErrorLogger.Errorf("get make job by id err ", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
				return
			}

			if makeJob.ID == 0 {
				ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "未到找记录"))
				return
			}
			jobID = makeJob.TaskID
		default:
			return
		}

		//通过url下载文件，并生成post.txt
		fileName = tempName + ".tar"
		fp := filepath.Join(upload, fileName)
		tmp := tempDir + tempName + "/"
		err = os.MkdirAll(tmp, 0o750)
		os.Chmod(tmp, 0o750)
		defer os.RemoveAll(tmp)
		f, err := os.OpenFile(filepath.Join(tmp, "post.txt"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
			return
		}
		defer f.Close()
		// 智能标准化文本内容，处理前端JavaScript预处理转义和行结束符问题
		normalizedContent := normalizeTextContent(request.Post)
		_, err = f.WriteString(normalizedContent)
		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
			return
		}
		for _, packageName := range strings.Split(request.PatchFileName, "|") {
			output, err := libs.ExecCommand(fmt.Sprintf(`find %s -name "%s"|xargs -I {} cp {} %s`, filepath.Join(libs.Config.Buildfarm.Archivepath, jobID), packageName, tmp))
			if err != nil {
				logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
				ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": output}, response.SystemErr.Msg))
				return
			}
		}
		output, err := libs.ExecCommand(fmt.Sprintf(`cd %s && tar cvf %s ./`, tmp, fp))
		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": output}, response.SystemErr.Msg))
			return
		}
		fi, err := os.Stat(fp)
		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": output}, response.SystemErr.Msg))
			return
		}
		request.PatchFileSize = strconv.FormatInt(fi.Size(), 10)
		request.PatchFileMd5, err = libs.GetFileMd5(fp)
		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": output}, response.SystemErr.Msg))
			return
		}

	} else if request.Type == "测试" {
		//接收上传文件
		f, fh, err := ctx.FormFile("file") //获取文件数据
		defer f.Close()
		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		//验证后缀名是否符合要求
		ext := path.Ext(fh.Filename)
		var AllowExtMap map[string]bool = map[string]bool{
			".tar": true,
			".zip": true,
		}

		//上传文件放入临时目录下
		//构造文件名称

		fileName = tempName + ext

		_, err = ctx.SaveFormFile(fh, filepath.Join(tempDir, fileName)) //保存文件到临时目录中
		defer os.Remove(filepath.Join(tempDir, fileName))               //最后删除临时文件
		if err != nil {
			logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}

		//检查文件类型与文件扩展为TAR格式 ZIP格式
		mime, err := mimetype.DetectFile(filepath.Join(tempDir, fileName))
		if err != nil {
			logging.ErrorLogger.Errorf("Error while Check File Type ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "无法判断文件类型"))
			return
		}
		if _, ok := AllowExtMap[mime.Extension()]; !ok {
			logging.ErrorLogger.Errorf("Error while Check File Type ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "只允许上传tar|zip压缩的文件"))
			return
		}

		if mime.Extension() == ".zip" {
			//解压压缩包文件到指定目录
			fileUnzipDirectory := tempDir + tempName + "/"
			err = unzip(fileUnzipDirectory, filepath.Join(tempDir, fileName))
			defer os.RemoveAll(fileUnzipDirectory)
			if err != nil {
				logging.ErrorLogger.Errorf("Error while Unzip File", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "压缩包存在问题，请重新打包后重试。"))
				return
			}
			//将目录打包成tar包
			tarfile := upload + "/" + tempName + ".tar"
			fileName = tempName + ".tar"
			err := tarFolder(fileUnzipDirectory, tarfile)
			if err != nil {
				logging.ErrorLogger.Errorf("Error while tar Directory  ", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "压缩包存在问题,打包tar失败，请重新打包后重试。"))
				return
			}
		} else {
			//创建最终存放路径以及保存文件
			tempFn := filepath.Join(upload, fileName) //路径拼接
			_, err = ctx.SaveFormFile(fh, tempFn)
			if err != nil {
				logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
				return
			}
		}
	}

	//创建任务单
	objects := dpatchjob.Response{
		JobId:                tempName,
		UserId:               uID,
		CreatedAt:            time.Now(),
		UpdatedAt:            time.Now(),
		PatchFileName:        fileName,
		PatchFileSize:        request.PatchFileSize,
		PatchFileMd5:         request.PatchFileMd5,
		PatchType:            request.PatchType,
		PatchForm:            request.PatchForm,
		PatchUpgradeType:     request.PatchUpgradeType,
		PatchFileOriginName:  request.PatchFileName,
		PatchObjectPath:      request.PatchObjectPath,
		SoftwareVersion:      request.SoftwareVersion,
		PatchComponentName:   request.PatchComponentName,
		Baseline:             request.Baseline,
		AdapterModel:         request.AdapterModel,
		AdapterHardware:      request.AdapterHardware,
		PatchFileDesc:        request.PatchFileDesc,
		PatchFileDescEN:      request.PatchFileDescEN,
		Dir:                  tempCompileDir,
		LocalDir:             upload,
		Version:              "",
		TaskId:               "",
		ServerId:             serverId,
		Status:               0,
		Type:                 request.Type,
		Post:                 request.Post,
		CronMakeJobID:        request.CronMakeJobID,
		DependencyIDs:        request.DependencyIDs,
		MakeFrom:             request.MakeFrom,
		MakeStatus:           -1,
		Defconfig:            aarches[0] + "_defconfig",
		Crypt:                "new",
		PatchVersion:         2,
		EnableAutoCollection: request.EnableAutoCollection,
		SerialNumber:         request.SerialNumber,
	}

	DaoPatchjob := dpatchjob.Response{}
	err = DaoPatchjob.CreateV3(objects)
	if err != nil {
		defer os.Remove(filepath.Join(upload, fileName)) //删除上传文件
		logging.ErrorLogger.Errorf("create detection Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	patchJob := &dpatchjob.Response{}
	err = easygorm.GetEasyGormDb().Model(&buildfarm.PatchJob{}).Where("job_id = ?", tempName).Find(&patchJob).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create detectionJob get err ", err)
	}

	//执行编译任务前置工作（拉取编译工具 拷贝文件到远程服务器）
	if patchJob.PatchForm == 1 && patchJob.Type == "生产" {
		go FunctionPatchJobV2(ctx, patchJob)
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, patchJob, response.NoErr.Msg))
	return
}

type PatchDependencyReq struct {
	AdapterModel     string `json:"adapter_model" form:"adapter_model"`             //适配型号
	AdapterHardware  string `json:"adaptation_hardware" form:"adaptation_hardware"` //适配硬件
	PatchForm        uint   `json:"patch_form" form:"patch_form"`                   //补丁形式：0组件补丁 1函数补丁
	PatchUpgradeType uint   `json:"patch_upgrade_type" form:"patch_upgrade_type"`   //补丁升级方式 0自动升级 1手动升级 2强制升级 默认手动
	SoftwareVersion  string `json:"software_version" form:"software_version"`       // 适配软件版本
}

// 获取依赖补丁数据
func GetDependencyData(ctx iris.Context) {
	PatchDependencyReq := &PatchDependencyReq{}
	if err := ctx.ReadForm(PatchDependencyReq); err != nil {
		logging.ErrorLogger.Errorf("read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*PatchDependencyReq)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	// 查找相同的升级方式、软件版本、硬件版本、适配型号
	depenPacthJobs, err := dpatchjob.GetPacthDependency(PatchDependencyReq.AdapterModel, PatchDependencyReq.AdapterHardware, PatchDependencyReq.SoftwareVersion, PatchDependencyReq.PatchForm, PatchDependencyReq.PatchUpgradeType)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, depenPacthJobs, response.NoErr.Msg))
	return

}

func GetPatchJobV2(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	patchjob := &dpatchjob.Response{}
	err := easygorm.GetEasyGormDb().Model(&buildfarm.PatchJob{}).Where("id = ?", id).Find(&patchjob).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get job status err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, patchjob, response.NoErr.Msg))
	return
}

func CreatePatchMakeJobV2(ctx iris.Context) {
	id, _ := dao.GetId(ctx)

	patchJob := &dpatchjob.Response{}
	err := patchJob.Find(id)

	if err != nil {
		logging.ErrorLogger.Errorf("get patchjob err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if patchJob.Id == 0 {
		logging.ErrorLogger.Errorf("get patchjob err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	PatchMakejobReq := &PatchMakejobReq{}
	if err := ctx.ReadForm(PatchMakejobReq); err != nil {
		logging.ErrorLogger.Errorf("read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*PatchMakejobReq)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	//检查当前作业是否已经存在排队中的编译申请
	// patchmakejob := &dpatchjob.Response{}
	// err = easygorm.GetEasyGormDb().Model(&buildfarm.PatchJob{}).Where("job_id = ? and (make_status = 3 or make_status = 0)", dpatchJob.JobId).Find(&patchmakejob).Error
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("create job get err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
	// 	return
	// }

	if patchJob.MakeStatus == 0 || patchJob.MakeStatus == 1 {
		logging.ErrorLogger.Errorf("create job get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "当前作业存在未完成编译任务"))
		return
	}
	rand.Seed(time.Now().UnixNano())
	randNum := fmt.Sprintf("%d", rand.Intn(9999)+1000)
	hashName := md5.Sum([]byte(time.Now().Format("2006_01_02_15_04_05_") + randNum))
	tempName := fmt.Sprintf("%x", hashName)
	//更新编译任务信息
	// defconfig := PatchMakejobReq.Defconfig
	// if patchJob.Defconfig == "" && defconfig == "" && (patchJob.PatchForm == 0 || patchJob.Type == "测试") {
	// 	defconfig = "aarch64_defconfig"
	// } else {
	// 	defconfig = patchJob.Defconfig
	// }
	data := map[string]interface{}{
		"UpdatedAt": time.Now(),
		"TaskId":    tempName,
		// "Defconfig":     defconfig,
		// "PatchFileOriginName": PatchMakejobReq.PatchFileOriginName,
		"MakeStatus": 0,
	}

	if PatchMakejobReq.Defconfig != "" {
		data["Defconfig"] = patchJob.Defconfig
	}

	if PatchMakejobReq.PatchFileOriginName != "" {
		data["PatchFileOriginName"] = PatchMakejobReq.PatchFileOriginName
	}

	err2 := patchJob.Update(patchJob.Id, data)

	if err2 != nil {
		logging.ErrorLogger.Errorf("update patch job err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	//开始编译
	MakePatchjobV2(patchJob)

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func UnitpackagePatchJobV2(patchjob *dpatchjob.Response) {
	// token := libs.Config.Buildfarm.Token
	client, err := SSHClient(patchjob.ServerId)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		updatePatchJobV2(patchjob, 2, "")
		return
	}
	//拉取仓库代码到指定目录
	defer client.Close()
	f, err := os.OpenFile(filepath.Join(libs.Config.PatchFileStorage.Temp, "patchjob_"+patchjob.JobId+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		updatePatchJobV2(patchjob, 2, "")
		return
	}
	defer f.Close()
	// repo_url := strings.Replace(libs.Config.PatchFileStorage.Gitrepo, "http://", fmt.Sprintf("http://oauth2:%s@", token), -1)
	repo_url := libs.Config.PatchFileStorage.Gitrepo
	command := fmt.Sprintf("git clone %s %s -v && cd %s && git checkout %s", repo_url, patchjob.Dir, patchjob.Dir, libs.Config.PatchFileStorage.NewBranch)
	f.WriteString("执行拉取项目操作，并切换分支\r\n")

	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updatePatchJobV2(patchjob, 2, "")
		return
	}

	command = fmt.Sprintf("cd %s && git rev-parse HEAD", patchjob.Dir)
	var stdOut bytes.Buffer
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updatePatchJobV2(patchjob, 2, "")
		return
	}

	version := strings.Replace(stdOut.String(), "\n", "", -1)

	if len(version) == 40 {
		updatePatchJobV2(patchjob, 1, version)
		f.WriteString(fmt.Sprintf("当前版本为：%s\r\n", version))
	} else {
		updatePatchJobV2(patchjob, 1, "")
		logging.ErrorLogger.Errorf("获取版本号异常", patchjob.JobId)
		f.WriteString(fmt.Sprintf("获取版本号异常：%s\r\n", version))
	}

	f.WriteString(fmt.Sprintf("拉取项目任务完成\r\n"))
	// 拷贝tar文件到远程服务器上
	server := &dserver.Response{}
	err = server.Find(patchjob.ServerId)
	tempPatchFile := patchjob.LocalDir + "/" + patchjob.PatchFileName
	if err != nil {
		logging.ErrorLogger.Errorf("create patchjob find server get err ", err)
	}
	//scp  -o stricthostkeychecking=no   /opt/soft/demo.tar root@************:/opt/soft/scptest
	command2 := fmt.Sprintf("scp -o stricthostkeychecking=no -P %s  %s %s@%s:%s", strconv.Itoa(int(server.Port)), tempPatchFile, server.Username, server.Host, patchjob.Dir)
	//记录日志
	f.WriteString(fmt.Sprintf("执行文件同步操作，执行命令如下:\r\n%s\r\n", command2))
	cmd := exec.Command("scp", "-o", "stricthostkeychecking=no", "-P", strconv.Itoa(int(server.Port)), tempPatchFile, fmt.Sprintf("%s@%s:%s", server.Username, server.Host, patchjob.Dir))
	var out bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &stderr
	err = cmd.Run()
	outStr, errStr := string(out.Bytes()), string(stderr.Bytes())
	if err != nil {
		logging.ErrorLogger.Errorf("scp command error", err.Error())
		updatePatchJobV2(patchjob, 2, "")
		return
	}
	f.WriteString(fmt.Sprintf("返回结果:\r\n%s\r\n", outStr))
	f.WriteString(fmt.Sprintf("错误信息:\r\n%s\r\n", errStr))

}

func FunctionPatchJobV2(ctx iris.Context, patchjob *dpatchjob.Response) {
	// token := libs.Config.Buildfarm.Token

	cronMakeJob := dcronmakejob.CronMakeJob{}
	err := cronMakeJob.Find(patchjob.CronMakeJobID)
	if err != nil {
		updatePatchJobV2(patchjob, 2, "")
		logging.ErrorLogger.Error("PatchJob: Find CronMakeJob Error", patchjob.Id, patchjob.CronMakeJobID, err)
		return
	}

	if cronMakeJob.Id == 0 {
		updatePatchJobV2(patchjob, 2, "")
		logging.ErrorLogger.Error("PatchJob: CronMakeJob Not Found", patchjob.Id, patchjob.CronMakeJobID)
		return
	}

	client, err := SSHClient(patchjob.ServerId)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		updatePatchJobV2(patchjob, 2, "")
		return
	}
	//拉取仓库代码到指定目录

	defer client.Close()
	f, err := os.OpenFile(filepath.Join(libs.Config.PatchFileStorage.Temp, "patchjob_"+patchjob.JobId+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		updatePatchJobV2(patchjob, 2, "")
		return
	}
	defer f.Close()
	// repo_url := strings.Replace(libs.Config.PatchFileStorage.Gitrepo, "http://", fmt.Sprintf("http://oauth2:%s@", token), -1)
	repo_url := libs.Config.PatchFileStorage.HotpatchRepo
	command := fmt.Sprintf("git clone -b %s %s %s -v", cronMakeJob.Branch, repo_url, patchjob.Dir)
	f.WriteString("执行拉取项目操作，并切换分支\r\n")

	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updatePatchJobV2(patchjob, 2, "")
		return
	}

	command = fmt.Sprintf("cd %s && git rev-parse HEAD", patchjob.Dir)
	var stdOut bytes.Buffer
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updatePatchJobV2(patchjob, 2, "")
		return
	}

	version := strings.Replace(stdOut.String(), "\n", "", -1)

	if len(version) == 40 {
		updatePatchJobV2(patchjob, 1, version)
		f.WriteString(fmt.Sprintf("当前版本为：%s\r\n", version))
	} else {
		updatePatchJobV2(patchjob, 1, "")
		logging.ErrorLogger.Errorf("获取版本号异常", patchjob.JobId)
		f.WriteString(fmt.Sprintf("获取版本号异常：%s\r\n", version))
	}

	f.WriteString(fmt.Sprintf("拉取项目任务完成\r\n"))
	// 拷贝tar文件到远程服务器上

}

func updatePatchJobV2(patch *dpatchjob.Response, status int, version string) {
	err := patch.Update(patch.Id, map[string]interface{}{
		"Status":    status,
		"UpdatedAt": time.Now(),
		"Version":   version,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("update patch job err ", err)
	}
}

func updateMakePatchJobV2(patch *dpatchjob.Response, makeStatus int, version string) {
	err := patch.Update(patch.Id, map[string]interface{}{
		"MakeStatus": makeStatus,
		"UpdatedAt":  time.Now(),
		"Version":    version,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("update patch job err ", err)
	}
}

func lsUnitPackagePatchDefConfigJobV2(patchjob *dpatchjob.Response) (map[string]interface{}, error) {
	defconfig := make(map[string]interface{})

	client, err := SSHClient(patchjob.ServerId)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		return defconfig, err
	}
	defer client.Close()
	f, err := os.OpenFile(filepath.Join(libs.Config.PatchFileStorage.Temp, "patchjob_"+patchjob.JobId+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		return defconfig, err
	}
	defer f.Close()
	command := fmt.Sprintf(`cd %s && make download`, patchjob.Dir)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("make download", err.Error())
		return defconfig, err
	}
	var stdOut bytes.Buffer
	command = fmt.Sprintf(`cd %s && make list-defconfigs|grep -v "="|grep defconfig|grep '-'|awk '{print $1}'`, patchjob.Dir)
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("get defconfig list", err.Error())
		return defconfig, err
	}

	// defconfig = strings.Split(stdOut.String(), "\n")[:len(strings.Split(stdOut.String(), "\n"))-1]
	// 通过ch返回运行状态为运行

	//获取配置数据
	patchDefconfigDatas, dfErr := dpatchdefconfig.FindAll()
	if dfErr != nil {
		return defconfig, dfErr
	}
	defconfig["items"] = patchDefconfigDatas

	return defconfig, nil
}

func lsFunctionPatchDefConfigJobV2(patchjob *dpatchjob.Response) (map[string]interface{}, error) {
	defconfig := make(map[string]interface{})

	client, err := SSHClient(patchjob.ServerId)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		return defconfig, err
	}
	defer client.Close()
	f, err := os.OpenFile(filepath.Join(libs.Config.PatchFileStorage.Temp, "patchjob_"+patchjob.JobId+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		return defconfig, err
	}
	defer f.Close()

	var stdOut bytes.Buffer
	command := fmt.Sprintf(`cd %s && ls ./package/ |grep -E '^HP'`, patchjob.Dir)

	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("get defconfig list", err.Error())
		return defconfig, err
	}

	defconfig["items"] = strings.Split(stdOut.String(), "\n")[:len(strings.Split(stdOut.String(), "\n"))-1]
	// defconfig = strings.Split(stdOut.String(), "\n")[:len(strings.Split(stdOut.String(), "\n"))-1]
	// 通过ch返回运行状态为运行

	return defconfig, nil
}

func GetPatchDefConfigsV2(ctx iris.Context) {
	id, _ := dao.GetId(ctx)

	patchjob := &dpatchjob.Response{}
	err := patchjob.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("get gitjob err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if patchjob.Id == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	var list map[string]interface{}
	if patchjob.PatchForm == 0 {
		list, err = lsUnitPackagePatchDefConfigJobV2(patchjob)
		if err != nil {
			logging.ErrorLogger.Errorf("create user get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, list, response.SystemErr.Msg))
			return
		}
	} else if patchjob.PatchForm == 1 {
		list, err = lsFunctionPatchDefConfigJobV2(patchjob)
		if err != nil {
			logging.ErrorLogger.Errorf("create user get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, list, response.SystemErr.Msg))
			return
		}
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func MakePatchjobV2(patchjob *dpatchjob.Response) {
	if patchjob.PatchForm == 0 || patchjob.Type == "测试" {
		UnitpackagePatchJobV2(patchjob)
		go MakeUnitPackagePatchjobV2(patchjob)
	} else if patchjob.PatchForm == 1 && patchjob.Type == "生产" {
		go MakeFunctionPatchjobV2(patchjob)
	}
}

func MakeFunctionPatchjobV2(patchjob *dpatchjob.Response) {
	cronMakeJob := dcronmakejob.CronMakeJob{}
	err := cronMakeJob.Find(patchjob.CronMakeJobID)
	if err != nil {
		updateMakePatchJobV2(patchjob, 2, "")
		logging.ErrorLogger.Error("PatchJob: Find CronMakeJob Error", patchjob.Id, patchjob.CronMakeJobID, err)
		return
	}

	if cronMakeJob.Id == 0 {
		updateMakePatchJobV2(patchjob, 2, "")
		logging.ErrorLogger.Error("PatchJob: CronMakeJob Not Found", patchjob.Id, patchjob.CronMakeJobID)
		return
	}

	version := ""
	updateMakePatchJobV2(patchjob, 0, version)
	client, err := SSHClient(patchjob.ServerId)
	if err != nil {
		logging.ErrorLogger.Errorf("MakePatchjob get err ", err)
		updateMakePatchJobV2(patchjob, 2, version)
		return
	}

	defer client.Close()
	f, err := os.OpenFile(filepath.Join(libs.Config.PatchFileStorage.Temp, "Make_patchjob_"+patchjob.TaskId+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
	if err != nil {
		logging.ErrorLogger.Errorf("MakePatchjob get err ", err)
		updateMakePatchJobV2(patchjob, 2, version)
		return
	}
	defer f.Close()
	command := fmt.Sprintf("cd %s && git rev-parse HEAD", patchjob.Dir)
	var stdOut bytes.Buffer
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakePatchJobV2(patchjob, 2, version)
		return
	}

	getVersion := strings.Replace(stdOut.String(), "\n", "", -1)
	if len(getVersion) == 40 {
		version = getVersion
		updateMakePatchJobV2(patchjob, 0, version)
	} else {
		logging.ErrorLogger.Errorf("获取版本号异常", patchjob.Id)
		updateMakePatchJobV2(patchjob, 2, version)
		return
	}
	f.WriteString(fmt.Sprintf("当前版本为：%s\r\n", version))
	// http://10.51.135.15:9090/output/fcd3421edb5ad19c2da588a18954b0e5/armada7k-aarch64/armada7k-aarch64-image.tar.bz2
	imageUrl := fmt.Sprintf("http://10.51.135.15:9090/output/%s/%s/%s-image.tar.bz2", cronMakeJob.JobId, cronMakeJob.Cpu.Cpu, cronMakeJob.Cpu.Cpu)
	command = fmt.Sprintf("cd %s && make download && make install-devel IMG=%s", patchjob.Dir, imageUrl)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakePatchJobV2(patchjob, 2, version)
		return
	}
	command = fmt.Sprintf(`cd %s && echo 'BR2_PACKAGE_PRODUCT="%s"' >> output/.config && make %s`, patchjob.Dir, cronMakeJob.Product, patchjob.PatchFileOriginName)
	f.WriteString(fmt.Sprintf("\r\n%s\r\n", command))
	//执行最后编译命令
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakePatchJobV2(patchjob, 2, version)
		return
	}

	// 打包HPxxxx.rpm
	command = fmt.Sprintf(`cd %s && fp=$(find ./ -name %s*.rpm); tar -cvf %s.tar -C $(dirname $fp) $(basename $fp)`, patchjob.Dir, patchjob.PatchFileOriginName, patchjob.PatchFileOriginName)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakePatchJobV2(patchjob, 2, version)
		return
	}

	// 拉取补丁仓库
	repo_url := libs.Config.PatchFileStorage.Gitrepo
	command = fmt.Sprintf("cd %s && git clone -b %s %s -v", patchjob.Dir, libs.Config.PatchFileStorage.NewBranch, repo_url)
	f.WriteString("执行拉取项目操作，并切换分支\r\n")

	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakePatchJobV2(patchjob, 2, "")
		return
	}

	// 拼接命令字符串
	patchOrginal := fmt.Sprintf("%s/%s.tar", patchjob.Dir, patchjob.PatchFileOriginName) //补丁原始包
	patchOBJModel := patchjob.AdapterModel                                               //适配产品,用|分割
	patchOBJHardware := patchjob.AdapterHardware                                         //适配硬件
	patchOBJComponentName := patchjob.PatchComponentName                                 //组件补丁名
	patchOBJPath := patchjob.PatchObjectPath                                             //补丁对象路径
	patchOBJForm := patchjob.PatchForm                                                   //补丁形式：0组件补丁 1函数补丁
	patchUpgradeType := patchjob.PatchUpgradeType                                        //补丁升级方式 0自动升级 1手动升级 2强制升级 默认手动
	patchOBJSW := patchjob.SoftwareVersion                                               //适配软件版本
	patchBase := patchjob.Baseline                                                       //补丁基线版本
	patchSerialNumber := patchjob.SerialNumber                                           //补丁版本
	patchFileDesc := patchjob.PatchFileDesc                                              //补丁描述
	patchFileDescEN := patchjob.PatchFileDescEN                                          //补丁英文描述

	//提取依赖项
	dependencies := make([]string, 0)
	for _, dependency := range patchjob.Dependencies {
		//补丁匹配需求将依赖项的后缀.sig 替换为.pat
		NewRPMFileName := strings.Replace(dependency.RPMFileName, ".sig", ".pat", -1)
		dependencies = append(dependencies, NewRPMFileName)
	}
	patchDepends := strings.Join(dependencies, "|")
	command = fmt.Sprintf(`cd %s/patch_build && make download && make %s && make build-sigpatchpkg PATCH_ORGINAL="%s" PATCH_OBJ_MODEL="%s" PATCH_OBJ_HW="%s" PATCH_OBJ_CMPNT="%s" PATCH_OBJ_FORM=%d PATCH_UPGRADE_TYPE=%d PATCH_OBJ_SW="%s" PATCH_BASE="%s" PATCH_VERSION=%d PATCH_DESC="%s" PATCH_DESC_EN="%s" PATCH_OBJ_PATH="%s" DEPENDS="%s"`,
		patchjob.Dir, patchjob.Defconfig, patchOrginal, patchOBJModel, patchOBJHardware, patchOBJComponentName, patchOBJForm, patchUpgradeType, patchOBJSW, patchBase, patchSerialNumber, patchFileDesc, patchFileDescEN, patchOBJPath, patchDepends)
	//执行最后编译命令
	f.WriteString(fmt.Sprintf("\r\n%s\r\n", command))
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakePatchJobV2(patchjob, 2, version)
		return
	}

	//更新状态
	updateMakePatchJobV2(patchjob, 1, version)
	if err := ArchiveCompileOutputV2(patchjob); err == nil {
		//保存文件信息（文件大小、MD5值）生成update.json文件
		//保存RMP文件名称
		rmpFilePath, findErr := findSIGFilePath(patchjob)
		if findErr != nil {
			logging.ErrorLogger.Errorf("rmpFile get err ", findErr)
			return
		}
		rmpFileName := strings.ReplaceAll(rmpFilePath, patchjob.Dir+"/patch_build/output/", "")
		daoErr := patchjob.Update(patchjob.Id, map[string]interface{}{
			"RPMFileName": rmpFileName,
			"UpdatedAt":   time.Now(),
		})
		if daoErr != nil {
			logging.ErrorLogger.Errorf("save rpm update patch job err ", daoErr)
		}
		if err := createConfigFileV2(patchjob, rmpFileName); err == nil {
			//删除远程服务器编译目录
			command = fmt.Sprintf("rm -rf %s", patchjob.Dir)
			if err := RunCommandOutFile(client, command, f); err != nil {
				logging.ErrorLogger.Errorf("run command error", err.Error())
				return
			}
		}
	}

}

func MakeUnitPackagePatchjobV2(patchjob *dpatchjob.Response) {

	version := ""
	updateMakePatchJobV2(patchjob, 0, version)
	client, err := SSHClient(patchjob.ServerId)
	if err != nil {
		logging.ErrorLogger.Errorf("MakePatchjob get err ", err)
		updateMakePatchJobV2(patchjob, 2, version)
		return
	}

	defer client.Close()
	f, err := os.OpenFile(filepath.Join(libs.Config.PatchFileStorage.Temp, "Make_patchjob_"+patchjob.TaskId+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
	if err != nil {
		logging.ErrorLogger.Errorf("MakePatchjob get err ", err)
		updateMakePatchJobV2(patchjob, 2, version)
		return
	}
	defer f.Close()
	command := fmt.Sprintf("cd %s && git rev-parse HEAD", patchjob.Dir)
	var stdOut bytes.Buffer
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakePatchJobV2(patchjob, 2, version)
		return
	}

	getVersion := strings.Replace(stdOut.String(), "\n", "", -1)
	if len(getVersion) == 40 {
		version = getVersion
		updateMakePatchJobV2(patchjob, 0, version)
	} else {
		logging.ErrorLogger.Errorf("获取版本号异常", patchjob.Id)
		updateMakePatchJobV2(patchjob, 2, version)
		return
	}
	f.WriteString(fmt.Sprintf("当前版本为：%s\r\n", version))

	command = fmt.Sprintf("cd %s && make download && make %s", patchjob.Dir, patchjob.Defconfig)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakePatchJobV2(patchjob, 2, version)
		return
	}
	//拼接命令字符串
	patchOrginal := patchjob.Dir + "/" + patchjob.PatchFileName //补丁原始包
	patchOBJModel := patchjob.AdapterModel                      //适配产品,用|分割
	patchOBJHardware := patchjob.AdapterHardware                //适配硬件
	patchOBJComponentName := patchjob.PatchComponentName        //组件补丁名
	patchOBJPath := patchjob.PatchObjectPath                    //补丁对象路径
	patchOBJForm := patchjob.PatchForm                          //补丁形式：0组件补丁 1函数补丁
	patchUpgradeType := patchjob.PatchUpgradeType               //补丁升级方式 0自动升级 1手动升级 2强制升级 默认手动
	patchOBJSW := patchjob.SoftwareVersion                      //适配软件版本
	patchBase := patchjob.Baseline                              //补丁基线版本
	patchSerialNumber := patchjob.SerialNumber                  //补丁版本
	patchFileDesc := patchjob.PatchFileDesc                     //补丁描述
	patchFileDescEN := patchjob.PatchFileDescEN                 //补丁描述
	//提取依赖项
	dependencies := make([]string, 0)
	for _, dependency := range patchjob.Dependencies {
		//补丁匹配需求将依赖项的后缀.sig 替换为.pat
		NewRPMFileName := strings.Replace(dependency.RPMFileName, ".sig", ".pat", -1)
		dependencies = append(dependencies, NewRPMFileName)
	}
	patchDepends := strings.Join(dependencies, "|")

	command = fmt.Sprintf(`cd %s && make build-sigpatchpkg PATCH_ORGINAL="%s" PATCH_OBJ_MODEL="%s" PATCH_OBJ_HW="%s" PATCH_OBJ_CMPNT="%s" PATCH_OBJ_FORM=%d PATCH_UPGRADE_TYPE=%d PATCH_OBJ_SW="%s" PATCH_BASE="%s" PATCH_VERSION=%d PATCH_DESC="%s" PATCH_DESC_EN="%s" PATCH_OBJ_PATH="%s" DEPENDS="%s" `,
		patchjob.Dir, patchOrginal, patchOBJModel, patchOBJHardware, patchOBJComponentName, patchOBJForm, patchUpgradeType, patchOBJSW, patchBase, patchSerialNumber, patchFileDesc, patchFileDescEN, patchOBJPath, patchDepends)
	f.WriteString(fmt.Sprintf("\r\n%s\r\n", command))
	//执行最后编译命令
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", command, err.Error())
		updateMakePatchJobV2(patchjob, 2, version)
		return
	}
	//更新状态
	updateMakePatchJobV2(patchjob, 1, version)
	if err := ArchiveCompileOutputV2(patchjob); err == nil {
		//保存文件信息（文件大小、MD5值）生成update.json文件
		//保存SIG文件名称
		rmpFilePath, findErr := findSIGFilePath(patchjob)
		if findErr != nil {
			logging.ErrorLogger.Errorf("rmpFile get err ", findErr)
			return
		}
		rmpFileName := strings.ReplaceAll(rmpFilePath, patchjob.Dir+"/output/", "")
		daoErr := patchjob.Update(patchjob.Id, map[string]interface{}{
			"RPMFileName": rmpFileName,
			"UpdatedAt":   time.Now(),
		})
		if daoErr != nil {
			logging.ErrorLogger.Errorf("save rpm update patch job err ", daoErr)
		}
		if err := createConfigFileV2(patchjob, rmpFileName); err == nil {
			//删除远程服务器编译目录
			command = fmt.Sprintf("rm -rf %s", patchjob.Dir)
			if err := RunCommandOutFile(client, command, f); err != nil {
				logging.ErrorLogger.Errorf("run command error", err.Error())
				return
			}

			// 检查是否需要自动创建并编译集合补丁
			if patchjob.PatchType == 0 {
				// 判断是否启用了自动集合补丁编译
				if patchjob.EnableAutoCollection {
					logging.InfoLogger.Infof("单补丁编译完成，准备创建集合补丁任务: %s", patchjob.JobId)
					go createCollectionPatchFromSingle(patchjob)
				}
			}
		}
	}
}

// 根据单补丁自动创建集合补丁任务
func createCollectionPatchFromSingle(patchjob *dpatchjob.Response) {
	tempName := libs.GetUUID()
	upload := filepath.Join(libs.Config.PatchFileStorage.Upload, "tar", time.Now().Format("20060102"))
	err := os.MkdirAll(upload, 0o750)
	os.Chmod(upload, 0o750)
	if err != nil {
		logging.ErrorLogger.Errorf("创建集合补丁自动编译目录失败: %s", err.Error())
		return
	}

	// 编译目录生成
	parentPath := libs.Config.PatchFileStorage.CompilePath
	tempCompileDir := filepath.Join(parentPath, tempName)

	// 获取单补丁数据作为集合补丁的子项
	subItemIDs := fmt.Sprintf("%d", patchjob.Id)

	// 准备集合补丁的文件
	fileSize, fileMd5, err := prepareCollectionPatchFiles(patchjob, upload, tempName)
	if err != nil {
		logging.ErrorLogger.Errorf("准备集合补丁文件失败: %s", err.Error())
		return
	}

	fileName := tempName + ".tar"

	// 创建集合补丁任务
	collectionPatch := dpatchjob.Response{
		JobId:                tempName,
		UserId:               patchjob.UserId,
		CreatedAt:            time.Now(),
		UpdatedAt:            time.Now(),
		PatchFileName:        fileName,
		PatchFileSize:        fileSize,
		PatchFileMd5:         fileMd5,
		PatchType:            1, // 集合补丁
		PatchForm:            patchjob.PatchForm,
		PatchUpgradeType:     patchjob.PatchUpgradeType,
		PatchFileOriginName:  patchjob.RPMFileName,
		PatchObjectPath:      patchjob.PatchObjectPath,
		SoftwareVersion:      patchjob.SoftwareVersion,
		PatchComponentName:   patchjob.PatchComponentName,
		Baseline:             patchjob.Baseline,
		AdapterModel:         patchjob.AdapterModel,
		AdapterHardware:      patchjob.AdapterHardware,
		PatchFileDesc:        patchjob.PatchFileDesc,   //fmt.Sprintf("自动创建的集合补丁，包含单补丁ID: %d", patchjob.Id)
		PatchFileDescEN:      patchjob.PatchFileDescEN, //fmt.Sprintf("Auto generated collection patch includes single patch ID: %d", patchjob.Id),
		Dir:                  tempCompileDir,
		LocalDir:             upload,
		Version:              "",
		TaskId:               "",
		ServerId:             patchjob.ServerId,
		Status:               0,
		Type:                 patchjob.Type, // 保持与源补丁相同的环境类型（生产或测试）
		Post:                 patchjob.Post,
		CronMakeJobID:        patchjob.CronMakeJobID,
		DependencyIDs:        patchjob.DependencyIDs,
		OriginSubItemIDs:     subItemIDs,
		SubitemIDs:           subItemIDs,
		MakeFrom:             patchjob.MakeFrom,
		MakeStatus:           -1,
		Crypt:                "new",
		PatchVersion:         2,
		EnableAutoCollection: false, // 禁用集合补丁的自动创建功能，防止无限循环
	}

	// 保存集合补丁任务
	DaoPatchjob := dpatchjob.Response{}
	err = DaoPatchjob.CreateV3(collectionPatch)
	if err != nil {
		logging.ErrorLogger.Errorf("创建集合补丁任务失败: %s", err.Error())
		return
	}

	// 查询新创建的补丁任务以获取ID
	newPatchJob := &dpatchjob.Response{}
	err = easygorm.GetEasyGormDb().Model(&buildfarm.PatchJob{}).Where("job_id = ?", tempName).Find(&newPatchJob).Error
	if err != nil {
		logging.ErrorLogger.Errorf("查询新创建的集合补丁任务失败: %s", err.Error())
		return
	}

	// 自动启动编译任务
	logging.InfoLogger.Infof("创建集合补丁任务成功，准备启动编译: %s", tempName)
	// 生成随机任务ID
	rand.Seed(time.Now().UnixNano())
	randNum := fmt.Sprintf("%d", rand.Intn(9999)+1000)
	hashName := md5.Sum([]byte(time.Now().Format("2006_01_02_15_04_05_") + randNum))
	taskId := fmt.Sprintf("%x", hashName)

	// 更新任务状态
	err = newPatchJob.Update(newPatchJob.Id, map[string]interface{}{
		"UpdatedAt":  time.Now(),
		"TaskId":     taskId,
		"MakeStatus": 0,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("更新集合补丁任务状态失败: %s", err.Error())
		return
	}

	// 执行集合补丁编译流程
	CollectionPackagePatchJob(newPatchJob)
	go MakeCollectionPackagePatchjob(newPatchJob)
}

// 准备集合补丁文件
func prepareCollectionPatchFiles(patchjob *dpatchjob.Response, upload, tempName string) (string, string, error) {
	tempDir := libs.Config.PatchFileStorage.Temp + "patch/" + time.Now().Format("20060102") + "/"
	err := os.MkdirAll(tempDir, 0o750)
	os.Chmod(tempDir, 0o750)
	if err != nil {
		return "", "", err
	}

	// 创建临时目录用于存放单补丁文件
	tmp := tempDir + tempName + "/"
	err = os.MkdirAll(tmp, 0o750)
	os.Chmod(tmp, 0o750)
	if err != nil {
		return "", "", err
	}
	defer os.RemoveAll(tmp)

	// 复制单补丁RPM文件到临时目录
	patchFilePath := filepath.Join(libs.Config.PatchFileStorage.ArchivePath, patchjob.TaskId, patchjob.RPMFileName)
	if _, err := os.Stat(patchFilePath); os.IsNotExist(err) {
		return "", "", fmt.Errorf("单补丁文件不存在: %s", patchFilePath)
	}

	// 复制文件到临时目录
	destFile := filepath.Join(tmp, patchjob.RPMFileName)
	err = copyFile(patchFilePath, destFile)
	if err != nil {
		return "", "", fmt.Errorf("复制单补丁文件失败: %s", err.Error())
	}

	// 创建tar包
	tarFile := filepath.Join(upload, tempName+".tar")
	cmd := exec.Command("tar", "-cf", tarFile, "-C", tmp, ".")
	var stderr bytes.Buffer
	cmd.Stderr = &stderr
	if err := cmd.Run(); err != nil {
		return "", "", fmt.Errorf("创建tar包失败: %s, %s", err.Error(), stderr.String())
	}

	// 获取文件大小和MD5
	fi, err := os.Stat(tarFile)
	if err != nil {
		return "", "", fmt.Errorf("获取文件信息失败: %s", err.Error())
	}
	fileSize := strconv.FormatInt(fi.Size(), 10)
	fileMd5, err := libs.GetFileMd5(tarFile)
	if err != nil {
		return "", "", fmt.Errorf("计算MD5失败: %s", err.Error())
	}

	return fileSize, fileMd5, nil
}

// 文件复制工具函数
func copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	if err != nil {
		return err
	}

	err = destFile.Sync()
	if err != nil {
		return err
	}

	// 复制源文件的权限
	sourceInfo, err := os.Stat(src)
	if err != nil {
		return err
	}
	return os.Chmod(dst, sourceInfo.Mode())
}

// 拷贝远程编译服务器RPM文件到本地
func ArchiveCompileOutputV2(patchjob *dpatchjob.Response) error {
	server := &dserver.Response{}
	err := server.Find(patchjob.ServerId)
	if err != nil {
		logging.ErrorLogger.Errorf("create gitjob find server get err ", err)
	}
	archivePath := filepath.Join(libs.Config.PatchFileStorage.ArchivePath, patchjob.TaskId)
	err = os.MkdirAll(archivePath, 0o755)
	os.Chmod(archivePath, 0o755)

	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("mkdir before scp error: %s", err.Error()))
		return err
	}
	rmpFilePath, findErr := findSIGFilePath(patchjob)
	if findErr != nil {
		logging.ErrorLogger.Errorf("rmpFile get err ", findErr)
		return findErr
	}
	//编译输出RPM文件拷贝
	cmd2 := exec.Command("scp", "-o", "stricthostkeychecking=no", "-P", strconv.Itoa(int(server.Port)), fmt.Sprintf("%s@%s:/%s", server.Username, server.Host, rmpFilePath), archivePath)
	var out2 bytes.Buffer
	var stderr2 bytes.Buffer
	cmd2.Stdout = &out2
	cmd2.Stderr = &stderr2
	err = cmd2.Run()
	if err != nil {
		logging.ErrorLogger.Errorf("scp get err ", err, stderr2.String())
		return err
	}
	return nil
}

// 获取远程服务器上SIG包路径
func findSIGFilePath(patchjob *dpatchjob.Response) (string, error) {
	RPMFilePath := ""
	client, err := SSHClient(patchjob.ServerId)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		return RPMFilePath, err
	}
	defer client.Close()
	f, err := os.OpenFile(filepath.Join(libs.Config.PatchFileStorage.Temp, "patchjob_"+patchjob.JobId+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		return RPMFilePath, err
	}
	defer f.Close()
	var stdOut bytes.Buffer

	command := fmt.Sprintf(`find %s -name "*.sig" |awk '{print $1}'`, patchjob.Dir)
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("get defconfig list", err.Error())
		return RPMFilePath, err
	}
	RPMFilePath = strings.Join(strings.Split(stdOut.String(), "\n")[:len(strings.Split(stdOut.String(), "\n"))-1], "")
	f.WriteString(fmt.Sprintf("SIG包路径:\r\n%s\r\n", RPMFilePath))
	return RPMFilePath, nil
}

// 获取远程服务器上MIX包路径
func findMIXFilePath(patchjob *dpatchjob.Response) (string, error) {
	RPMFilePath := ""
	client, err := SSHClient(patchjob.ServerId)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		return RPMFilePath, err
	}
	defer client.Close()
	f, err := os.OpenFile(filepath.Join(libs.Config.PatchFileStorage.Temp, "patchjob_"+patchjob.JobId+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		return RPMFilePath, err
	}
	defer f.Close()
	var stdOut bytes.Buffer

	command := fmt.Sprintf(`find %s -name "*.MIX" |awk '{print $1}'`, patchjob.Dir)
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("get defconfig list", err.Error())
		return RPMFilePath, err
	}
	RPMFilePath = strings.Join(strings.Split(stdOut.String(), "\n")[:len(strings.Split(stdOut.String(), "\n"))-1], "")
	f.WriteString(fmt.Sprintf("MIX包路径:\r\n%s\r\n", RPMFilePath))
	return RPMFilePath, nil
}

// 拷贝远程编译服务器MIX文件到本地
func ArchiveCompileOutputV3(patchjob *dpatchjob.Response) error {
	server := &dserver.Response{}
	err := server.Find(patchjob.ServerId)
	if err != nil {
		logging.ErrorLogger.Errorf("create gitjob find server get err ", err)
	}
	archivePath := filepath.Join(libs.Config.PatchFileStorage.ArchivePath, patchjob.TaskId)
	err = os.MkdirAll(archivePath, 0o755)
	os.Chmod(archivePath, 0o755)

	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("mkdir before scp error: %s", err.Error()))
		return err
	}
	mixFilePath, findErr := findMIXFilePath(patchjob)
	if findErr != nil {
		logging.ErrorLogger.Errorf("mixFile get err ", findErr)
		return findErr
	}
	//编译输出RPM文件拷贝
	cmd2 := exec.Command("scp", "-o", "stricthostkeychecking=no", "-P", strconv.Itoa(int(server.Port)), fmt.Sprintf("%s@%s:/%s", server.Username, server.Host, mixFilePath), archivePath)
	var out2 bytes.Buffer
	var stderr2 bytes.Buffer
	cmd2.Stdout = &out2
	cmd2.Stderr = &stderr2
	err = cmd2.Run()
	if err != nil {
		logging.ErrorLogger.Errorf("scp get err ", err, stderr2.String())
		return err
	}
	return nil
}

// 获取补丁编译列表
// func GetMakePatchJobsV2(ctx iris.Context) {
// 	fileOriginName := ctx.FormValue("file_origin_name") //补丁包原名
// 	defconfig := ctx.FormValue("defconfig")             //配置
// 	start := ctx.FormValue("start")                     //开始日期
// 	end := ctx.FormValue("end")                         //结束日期
// 	makeStatus := ctx.FormValue("make_status")          //编译状态 3：排队，-1:未启动,0：运行， 1：成功， 2: 失败
// 	baseline := ctx.FormValue("baseline")               //基线版本
// 	componentName := ctx.FormValue("component_name")    //补丁组件名
// 	patchForm := ctx.FormValue("patch_form")            //补丁形式

// 	page, _ := strconv.Atoi(ctx.FormValue("page"))
// 	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
// 	orderBy := ctx.FormValue("orderBy")
// 	sort := ctx.FormValue("sort")

// 	makejobs, err := dpatchjob.FindMakePatchJobs(fileOriginName, start, end, makeStatus, baseline, defconfig, componentName, patchForm, sort, orderBy, page, pageSize)

// 	if err != nil {
// 		logging.ErrorLogger.Errorf("create user get err ", err)
// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
// 		return
// 	}

// 	ctx.JSON(response.NewResponse(response.NoErr.Code, makejobs, response.NoErr.Msg))
// 	return
// }

func GetMakePatchJobV2(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	job := dpatchjob.Response{}
	err := job.Find(id)

	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, job, response.NoErr.Msg))
	return
}

// 查看编译日志
func ShowPatchLogV2(ctx iris.Context) {
	jobId := ctx.FormValue("jobId")
	if jobId = strings.ReplaceAll(jobId, " ", ""); jobId == "" {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "作业id参数错误"))
		return
	}
	patchJob := &dpatchjob.Response{}
	err := patchJob.FindPatchJob(jobId)
	if err != nil {
		logging.ErrorLogger.Errorf("get patchJob err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if patchJob.Id == 0 {
		logging.ErrorLogger.Errorf("get patchJob err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "任务不存在"))
		return
	}
	if _, err := os.Stat(filepath.Join(libs.Config.PatchFileStorage.Temp, "Make_patchjob_"+patchJob.TaskId+".log")); os.IsNotExist(err) {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "日志文件不存在"))
		return
	}
	fd, err := os.Open(filepath.Join(libs.Config.PatchFileStorage.Temp, "Make_patchjob_"+patchJob.TaskId+".log"))
	defer fd.Close()
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	buff := bufio.NewReader(fd)
	var chunks []byte
	dataBuff := make([]byte, 1024)
	for {
		n, err := buff.Read(dataBuff)
		if err != nil && err != io.EOF {
			ctx.WriteString("获取文件错误！")
			return
		}
		if 0 == n {
			break
		}
		chunks = append(chunks, dataBuff[:n]...)
	}
	ctx.WriteString(string(chunks))
	return
}

// 下载RPM包文件
func DownloadRPMFileV2(ctx iris.Context) {
	jobId := ctx.FormValue("jobId")
	if jobId = strings.ReplaceAll(jobId, " ", ""); jobId == "" {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "作业id参数错误"))
		return
	}
	patchJob := &dpatchjob.Response{}
	err := patchJob.FindPatchJob(jobId)
	if err != nil {
		logging.ErrorLogger.Errorf("get patchJob err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if patchJob.Id == 0 {
		logging.ErrorLogger.Errorf("get patchJob err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "任务不存在"))
		return
	}
	archivePath := libs.Config.PatchFileStorage.ArchivePath + "/" + patchJob.TaskId
	ZipFile := strings.TrimSuffix(patchJob.RPMFileName, ".pat") + ".zip"
	if _, err := os.Stat(filepath.Join(archivePath, ZipFile)); os.IsNotExist(err) {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "文件不存在"))
		return
	}
	downloadFile := filepath.Join(archivePath + "/" + ZipFile)
	ctx.SendFile(downloadFile, url.QueryEscape(ZipFile))
	return
}

// 获取产品软件版本
func GetSoftVersionsV2(ctx iris.Context) {
	_, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dsoftversions.ListResponse{}, ctx, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

// 获取产品软件版本
func GetReleaseSoftVersionsV2(ctx iris.Context) {
	_, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dsoftversions.ListResponse{}, ctx, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

// 获取新版补丁数据列表 包含集合包和单补丁
func GetNewPatchList(ctx iris.Context) {
	_, err := dao.GetAuthId(ctx) //获取用户id
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	typeModel := ctx.FormValue("type")
	PatchJobList, err := dpatchjob.GetPatchJobList(typeModel, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, PatchJobList, response.NoErr.Msg))
	return
}

// 创建集合补丁编译任务
func CreateCollectionPatchJob(ctx iris.Context) {
	//校验用户权限
	uID, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	request := &dpatchjob.Request{}
	if err := ctx.ReadForm(request); err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		logging.ErrorLogger.Errorf("create patchjob read json err ", strings.Join(errs, ";"))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	// 验证自定义发布日期格式
	if err := validateDateFormat(request.CustomReleaseDate); err != nil {
		logging.ErrorLogger.Errorf("create collection patch job date validation err: %s", err.Error())
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, err.Error()))
		return
	}

	//编译服务器选择
	serverId := ChoosePatchServer()
	if serverId == 0 {
		logging.ErrorLogger.Errorf("create patchjob serverID err ")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "没用可用的服务器"))
		return
	}
	//todo: url还是手动上传
	tempName := libs.GetUUID()
	var fileName, tempCompileDir, upload string
	upload = filepath.Join(libs.Config.PatchFileStorage.Upload, "tar", time.Now().Format("20060102"))
	err = os.MkdirAll(upload, 0o750)
	os.Chmod(upload, 0o750)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	//编译目录生成
	parentPath := libs.Config.PatchFileStorage.CompilePath
	tempCompileDir = filepath.Join(parentPath, tempName)
	tempDir := libs.Config.PatchFileStorage.Temp + "patch/" + time.Now().Format("20060102") + "/"
	err = os.MkdirAll(tempDir, 0o750)
	os.Chmod(tempDir, 0o750)

	//获取全部的单补丁数据集
	Dpatchjob := &dpatchjob.Response{}
	patchSubitemList, err := Dpatchjob.GetSinglePatchesFromIDs(request.SubitemIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("get SinglePatchesFromIDs by SubitemIDs err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	//使用单补丁数据集进行拓扑排序
	graph, inDegree := buildGraph(patchSubitemList)
	tSortResult := topologicalSort(graph, inDegree)
	originSubItemIDs := request.SubitemIDs                                     //获取原始的子项ID
	sortedRPMFileNames := getSortedRPMFileNames(tSortResult, patchSubitemList) //获取排序后的RPM文件名

	//使用单补丁数据提取适配产品型号、软件版本信息
	adapterModels := Dpatchjob.MergeAndDistinctAdapterModels(patchSubitemList)       //获取适配产品型号
	softwareVersions := Dpatchjob.MergeAndDistinctSoftwareVersions(patchSubitemList) //获取软件版本
	adapterHardwares := Dpatchjob.MergeAndDistinctAdapterHardwares(patchSubitemList) //获取适配硬件
	//复制全部单补丁文件到临时目录
	fileSize, fileMd5, err := PreparePatchArchive(patchSubitemList, upload, tempName, tempDir)
	if err != nil {
		logging.ErrorLogger.Errorf("Error while preparing patch archive: %s", err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	fileName = tempName + ".tar"
	request.PatchFileSize = fileSize
	request.PatchFileMd5 = fileMd5
	request.AdapterModel = strings.Join(adapterModels, "|")
	request.AdapterHardware = strings.Join(adapterHardwares, "|")
	request.SoftwareVersion = strings.Join(softwareVersions, "|")
	request.PatchFileName = strings.Join(sortedRPMFileNames, "|")
	request.SubitemIDs = strings.Join(tSortResult, ",")
	//创建任务单
	objects := dpatchjob.Response{
		JobId:               tempName,
		UserId:              uID,
		CreatedAt:           time.Now(),
		UpdatedAt:           time.Now(),
		PatchFileName:       fileName,
		PatchFileSize:       request.PatchFileSize,
		PatchFileMd5:        request.PatchFileMd5,
		PatchType:           request.PatchType,
		PatchFileOriginName: request.PatchFileName,
		SoftwareVersion:     request.SoftwareVersion,
		PatchComponentName:  request.PatchComponentName,
		Baseline:            request.Baseline,
		AdapterModel:        request.AdapterModel,
		AdapterHardware:     request.AdapterHardware,
		PatchFileDesc:       request.PatchFileDesc,
		PatchFileDescEN:     request.PatchFileDescEN,
		Dir:                 tempCompileDir,
		LocalDir:            upload,
		Version:             "",
		TaskId:              "",
		ServerId:            serverId,
		Status:              0,
		Type:                request.Type,
		Post:                request.Post,
		CronMakeJobID:       request.CronMakeJobID,
		DependencyIDs:       request.DependencyIDs,
		OriginSubItemIDs:    originSubItemIDs,
		SubitemIDs:          request.SubitemIDs,
		MakeFrom:            request.MakeFrom,
		MakeStatus:          -1,
		Crypt:               "new",
		PatchVersion:        2,
		SerialNumber:        request.SerialNumber,
	}

	// 处理自定义发布日期字段
	if request.CustomReleaseDate != "" {
		objects.CustomReleaseDate = &request.CustomReleaseDate
		logging.InfoLogger.Info("create collection patch job with custom release date: %s", request.CustomReleaseDate)
	} else {
		objects.CustomReleaseDate = nil
	}
	DaoPatchjob := dpatchjob.Response{}
	err = DaoPatchjob.CreateV3(objects)
	if err != nil {
		defer os.Remove(filepath.Join(upload, fileName)) //删除上传文件
		logging.ErrorLogger.Errorf("create detection Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	patchJob := &dpatchjob.Response{}
	err = easygorm.GetEasyGormDb().Model(&buildfarm.PatchJob{}).Where("job_id = ?", tempName).Find(&patchJob).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create detectionJob get err ", err)
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, patchJob, response.NoErr.Msg))
	return
}

func CreateCollectionPatchMakeJob(ctx iris.Context) {
	id, _ := dao.GetId(ctx)

	patchJob := &dpatchjob.Response{}
	err := patchJob.Find(id)

	if err != nil {
		logging.ErrorLogger.Errorf("get patchjob err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if patchJob.Id == 0 {
		logging.ErrorLogger.Errorf("get patchjob err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if patchJob.MakeStatus == 0 || patchJob.MakeStatus == 1 {
		logging.ErrorLogger.Errorf("create job get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "当前作业存在未完成编译任务"))
		return
	}
	rand.Seed(time.Now().UnixNano())
	randNum := fmt.Sprintf("%d", rand.Intn(9999)+1000)
	hashName := md5.Sum([]byte(time.Now().Format("2006_01_02_15_04_05_") + randNum))
	tempName := fmt.Sprintf("%x", hashName)
	data := map[string]interface{}{
		"UpdatedAt":  time.Now(),
		"TaskId":     tempName,
		"MakeStatus": 0,
	}
	err2 := patchJob.Update(patchJob.Id, data)
	if err2 != nil {
		logging.ErrorLogger.Errorf("update patch job err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	//开始编译
	CollectionPackagePatchJob(patchJob)
	go MakeCollectionPackagePatchjob(patchJob)
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func CollectionPackagePatchJob(patchjob *dpatchjob.Response) {
	client, err := SSHClient(patchjob.ServerId)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		updatePatchJobV2(patchjob, 2, "")
		return
	}
	//拉取仓库代码到指定目录
	defer client.Close()
	f, err := os.OpenFile(filepath.Join(libs.Config.PatchFileStorage.Temp, "patchjob_"+patchjob.JobId+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		updatePatchJobV2(patchjob, 2, "")
		return
	}
	defer f.Close()
	// repo_url := strings.Replace(libs.Config.PatchFileStorage.Gitrepo, "http://", fmt.Sprintf("http://oauth2:%s@", token), -1)
	repo_url := libs.Config.PatchFileStorage.Gitrepo
	command := fmt.Sprintf("git clone %s %s -v && cd %s && git checkout %s", repo_url, patchjob.Dir, patchjob.Dir, libs.Config.PatchFileStorage.NewBranch)
	f.WriteString("执行拉取项目操作，并切换分支\r\n")

	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updatePatchJobV2(patchjob, 2, "")
		return
	}

	command = fmt.Sprintf("cd %s && git rev-parse HEAD", patchjob.Dir)
	var stdOut bytes.Buffer
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updatePatchJobV2(patchjob, 2, "")
		return
	}

	version := strings.Replace(stdOut.String(), "\n", "", -1)

	if len(version) == 40 {
		updatePatchJobV2(patchjob, 1, version)
		f.WriteString(fmt.Sprintf("当前版本为：%s\r\n", version))
	} else {
		updatePatchJobV2(patchjob, 1, "")
		logging.ErrorLogger.Errorf("获取版本号异常", patchjob.JobId)
		f.WriteString(fmt.Sprintf("获取版本号异常：%s\r\n", version))
	}

	f.WriteString(fmt.Sprintf("拉取项目任务完成\r\n"))
	// 拷贝tar文件到远程服务器上
	server := &dserver.Response{}
	err = server.Find(patchjob.ServerId)
	tempPatchFile := patchjob.LocalDir + "/" + patchjob.PatchFileName
	if err != nil {
		logging.ErrorLogger.Errorf("create patchjob find server get err ", err)
	}
	//scp  -o stricthostkeychecking=no   /opt/soft/demo.tar root@************:/opt/soft/scptest
	command2 := fmt.Sprintf("scp -o stricthostkeychecking=no -P %s  %s %s@%s:%s", strconv.Itoa(int(server.Port)), tempPatchFile, server.Username, server.Host, patchjob.Dir)
	//记录日志
	f.WriteString(fmt.Sprintf("执行文件同步操作，执行命令如下:\r\n%s\r\n", command2))
	cmd := exec.Command("scp", "-o", "stricthostkeychecking=no", "-P", strconv.Itoa(int(server.Port)), tempPatchFile, fmt.Sprintf("%s@%s:%s", server.Username, server.Host, patchjob.Dir))
	var out bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &stderr
	err = cmd.Run()
	outStr, errStr := string(out.Bytes()), string(stderr.Bytes())
	if err != nil {
		logging.ErrorLogger.Errorf("scp command error", err.Error())
		updatePatchJobV2(patchjob, 2, "")
		return
	}
	f.WriteString(fmt.Sprintf("返回结果:\r\n%s\r\n", outStr))
	f.WriteString(fmt.Sprintf("错误信息:\r\n%s\r\n", errStr))
	// 新增：解压补丁文件到指定目录的子目录中
	unzipDir := path.Join(patchjob.Dir, "unzipped_patch") // 指定解压目录
	command3 := fmt.Sprintf("mkdir -p %s && tar -xvf %s/%s -C %s", unzipDir, patchjob.Dir, patchjob.PatchFileName, unzipDir)
	f.WriteString(fmt.Sprintf("执行解压操作，命令如下:\r\n%s\r\n", command3))
	if err := RunCommandOutBuffer(client, command3, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("run command error (unzip): %s", err.Error())
		updatePatchJobV2(patchjob, 2, "")
		return
	}
	f.WriteString(fmt.Sprintf("解压操作完成，输出如下:\r\n%s\r\n", stdOut.String()))
	f.WriteString(fmt.Sprintf("拉取项目及文件同步任务完成\r\n"))
}

func MakeCollectionPackagePatchjob(patchjob *dpatchjob.Response) {

	version := ""
	updateMakePatchJobV2(patchjob, 0, version)
	client, err := SSHClient(patchjob.ServerId)
	if err != nil {
		logging.ErrorLogger.Errorf("MakePatchjob get err ", err)
		updateMakePatchJobV2(patchjob, 2, version)
		return
	}

	defer client.Close()
	f, err := os.OpenFile(filepath.Join(libs.Config.PatchFileStorage.Temp, "Make_patchjob_"+patchjob.TaskId+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
	if err != nil {
		logging.ErrorLogger.Errorf("MakePatchjob get err ", err)
		updateMakePatchJobV2(patchjob, 2, version)
		return
	}
	defer f.Close()
	command := fmt.Sprintf("cd %s && git rev-parse HEAD", patchjob.Dir)
	var stdOut bytes.Buffer
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakePatchJobV2(patchjob, 2, version)
		return
	}

	getVersion := strings.Replace(stdOut.String(), "\n", "", -1)
	if len(getVersion) == 40 {
		version = getVersion
		updateMakePatchJobV2(patchjob, 0, version)
	} else {
		logging.ErrorLogger.Errorf("获取版本号异常", patchjob.Id)
		updateMakePatchJobV2(patchjob, 2, version)
		return
	}
	f.WriteString(fmt.Sprintf("当前版本为：%s\r\n", version))

	command = fmt.Sprintf("cd %s && make download && make aarch64_defconfig", patchjob.Dir)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakePatchJobV2(patchjob, 2, version)
		return
	}
	//拼接命令字符串
	patchFilePath := filepath.Join(patchjob.Dir, "unzipped_patch") + "/" //补丁文件路径
	patchFileNameList := patchjob.PatchFileOriginName                    //补丁原始包
	patchSerialNumber := patchjob.SerialNumber                           //补丁版本号
	command = fmt.Sprintf(`cd %s && make build-mixpatchpkg	FILEPATH="%s" FILELIST="%s" MIX_VERSION="%d"`, patchjob.Dir, patchFilePath, patchFileNameList, patchSerialNumber)
	f.WriteString(fmt.Sprintf("\r\n%s\r\n", command))
	//执行最后编译命令
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", command, err.Error())
		updateMakePatchJobV2(patchjob, 2, version)
		return
	}
	//更新状态
	updateMakePatchJobV2(patchjob, 1, version)
	if err := ArchiveCompileOutputV3(patchjob); err == nil {
		//保存文件信息（文件大小、MD5值）生成update.json文件
		//保存.MIX文件名称
		rmpFilePath, findErr := findMIXFilePath(patchjob)
		if findErr != nil {
			logging.ErrorLogger.Errorf("rmpFile get err ", findErr)
			return
		}
		rmpFileName := strings.ReplaceAll(rmpFilePath, patchjob.Dir+"/output/", "")
		daoErr := patchjob.Update(patchjob.Id, map[string]interface{}{
			"RPMFileName": rmpFileName,
			"UpdatedAt":   time.Now(),
		})
		if daoErr != nil {
			logging.ErrorLogger.Errorf("save rpm update patch job err ", daoErr)
		}
		if err := createConfigFileV3(patchjob, rmpFileName); err == nil {
			//删除远程服务器编译目录
			command = fmt.Sprintf("rm -rf %s", patchjob.Dir)
			if err := RunCommandOutFile(client, command, f); err != nil {
				logging.ErrorLogger.Errorf("run command error", err.Error())
				return
			}
		}
	}
}

// 获取单补丁依赖项
func FetchPatchDependencies(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	allDependencies, err := dpatchjob.FindAllDependenciesByID(id)
	if err != nil {
		logging.ErrorLogger.Errorf("get gitjob err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	var dependencyIDs []int
	for _, dependency := range allDependencies {
		dependencyIDs = append(dependencyIDs, int(dependency.Id))
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, dependencyIDs, response.NoErr.Msg))
	return
}

// 创建update.json配置文件
func createConfigFileV2(patchjob *dpatchjob.Response, rmpFileName string) error {

	type Primary struct {
		FileName    string `json:"file_name"`
		FileSize    uint   `json:"file_size"`
		FileMd5     string `json:"file_md5"`
		ReleaseDate string `json:"release_date"`
		FileDesc    string `json:"file_desc"`
		FileType    uint   `json:"file_type"`
	}
	type Extend struct {
		Version string `json:"version"`
		// VersionDesc   string   `json:"version_desc"`
		PatchForm     uint     `json:"patch_form"`
		PatchType     uint     `json:"patch_type"`
		UpgradeType   uint     `json:"upgrade_type"`
		ProductModels []string `json:"product_models"`
		SoftVersions  []string `json:"soft_versions"`
		HardVersions  []string `json:"hard_versions"`
		Component     string   `json:"component"`
		PatchObjPath  string   `json:"patch_obj_path"`
	}
	type UpdateConfig struct {
		Primary Primary `json:"primary"`
		Extend  Extend  `json:"extend"`
	}

	archivePath := filepath.Join(libs.Config.PatchFileStorage.ArchivePath, patchjob.TaskId) //保存路径
	//文件信息保存
	rpmFilePath := filepath.Join(archivePath, rmpFileName)
	fileInfo, err := os.Stat(rpmFilePath)
	if err != nil {
		logging.ErrorLogger.Errorf("file err ", err)
	}
	md5, _ := libs.GetFileMd5(filepath.Join(rpmFilePath)) //MD5值
	daoErr := patchjob.Update(patchjob.Id, map[string]interface{}{
		"RPMFileSize": fileInfo.Size(),
		"RPMFileMd5":  md5,
	})
	if daoErr != nil {
		logging.ErrorLogger.Errorf("save rpm md5 update patch job err ", daoErr)
	}
	primary := Primary{
		FileName:    rmpFileName,
		FileSize:    uint(fileInfo.Size()),
		FileMd5:     md5,
		ReleaseDate: patchjob.CreatedAt.Format("2006-01-02"),
		FileDesc:    strings.Replace(patchjob.PatchFileDesc, `\n`, "\n", -1),
		FileType:    1,
	}

	if patchjob.AdapterHardware == "all" {
		patchjob.AdapterHardware = ""
	}
	extend := Extend{
		Version: fmt.Sprintf("%d", patchjob.Id),
		// VersionDesc:   strings.Replace(patchjob.PatchFileDesc, `\n`, "\n", -1),
		PatchForm:     patchjob.PatchForm,
		PatchType:     patchjob.PatchType,
		UpgradeType:   patchjob.PatchUpgradeType,
		ProductModels: strings.Split(patchjob.AdapterModel, "|"),
		SoftVersions:  strings.Split(patchjob.SoftwareVersion, "|"),
		HardVersions:  strings.Split(patchjob.AdapterHardware, "|"),
		Component:     strings.ReplaceAll(patchjob.PatchComponentName, "|", ","),
		PatchObjPath:  patchjob.PatchObjectPath,
	}

	updateConfig := UpdateConfig{
		Primary: primary,
		Extend:  extend,
	}
	//创建json文件
	filePtr, err := os.Create(filepath.Join(archivePath, "update.json"))
	if err != nil {
		logging.ErrorLogger.Errorf("Create file failed", err.Error())
		return err
	}
	defer filePtr.Close()
	//创建Json编码器
	encoder := json.NewEncoder(filePtr)
	err = encoder.Encode(updateConfig)
	encoder.SetEscapeHTML(false) // 设置为false以避免转义HTML字符
	if err != nil {
		return err
	}
	//将rmp包和update.json打包成zip 文件
	//要压缩成一个zip的多个文件的路径
	jsonFilePath := filepath.Join(archivePath, "update.json")
	files := []string{filepath.Join(archivePath, rmpFileName), jsonFilePath}
	ZipFileName := strings.TrimSuffix(rmpFileName, ".sig")
	//设置输出的zip的路径
	ZipFilePath := filepath.Join(archivePath, ZipFileName+".zip")
	if err := ZipFiles(ZipFilePath, files); err != nil {
		logging.ErrorLogger.Errorf("Create ZipFile failed", err.Error())
		return err
	}
	return nil
}

func GetCronMakeJobsV2(ctx iris.Context) {
	// id, err := dao.GetAuthId(ctx)
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	repo := ctx.FormValue("repo")
	branch := ctx.FormValue("branch")
	// server := ctx.FormValue("server")

	start := ctx.FormValue("start")
	end := ctx.FormValue("end")
	status := ctx.FormValue("status") // 已发布归档，状态为3
	product := ctx.FormValue("product")

	defconfig := ctx.FormValue("defconfig")
	target := ctx.FormValue("target")
	build_type := ctx.FormValue("build_type")
	softnum := ctx.FormValue("softnum")
	softversion := ctx.FormValue("softversion")

	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	archive := ctx.FormValue("archive")

	makejobs, err := dcronmakejob.FindAllCronMakeJobsV2(repo, branch, start, end, status, archive, product, defconfig, target, build_type, softnum, softversion, sort, orderBy, page, pageSize)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, makejobs, response.NoErr.Msg))
	return
}

// 标准化文本内容，处理转义字符问题
func normalizeTextContent(input string) string {
	if input == "" {
		return input
	}
	result := input
	// 处理前端标准转义
	result = strings.ReplaceAll(result, "\\r\\n", "\r\n") // 前端转义的 \r\n
	result = strings.ReplaceAll(result, "\\n", "\n")      // 前端转义的 \n
	result = strings.ReplaceAll(result, "\\t", "\t")      // 前端转义的 \t
	return result
}
