package coredump

import (
	"context"
	"fmt"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"

	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkbitable "github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
)

// CoredumpAutoSyncService Coredump自动同步服务
type CoredumpAutoSyncService struct {
	feishuClient  *lark.Client
	filterBuilder *FilterBuilder
	fieldMapper   *FieldMapper
	statusManager *StatusManager
	bugSubmitter  *BugSubmitter
}

// NewCoredumpAutoSyncService 创建Coredump自动同步服务
func NewCoredumpAutoSyncService() *CoredumpAutoSyncService {
	// 验证配置
	if err := validateCoredumpConfig(); err != nil {
		logging.ErrorLogger.Errorf("Coredump配置验证失败: %v", err)
		return nil
	}

	// 创建飞书客户端
	feishuClient := getFeishuClient()
	if feishuClient == nil {
		logging.ErrorLogger.Error("创建飞书客户端失败")
		return nil
	}

	// 验证飞书连接
	if err := validateFeishuConnection(feishuClient); err != nil {
		logging.ErrorLogger.Errorf("飞书连接验证失败: %v", err)
		return nil
	}

	logging.InfoLogger.Info("Coredump自动同步服务初始化成功")

	return &CoredumpAutoSyncService{
		feishuClient:  feishuClient,
		filterBuilder: NewFilterBuilder(),
		fieldMapper:   NewFieldMapper(),
		statusManager: NewStatusManager(feishuClient),
		bugSubmitter:  NewBugSubmitter(),
	}
}

// ProcessResult 处理结果
type ProcessResult struct {
	TaskID          string        `json:"task_id"`
	StartTime       time.Time     `json:"start_time"`
	EndTime         time.Time     `json:"end_time"`
	Duration        time.Duration `json:"duration"`
	TotalRecords    int           `json:"total_records"`
	FilteredRecords int           `json:"filtered_records"`
	SuccessRecords  int           `json:"success_records"`
	FailedRecords   int           `json:"failed_records"`
	Errors          []string      `json:"errors"`
}

// ProcessCoredumpRecords 处理Coredump记录（完整业务流程）
func (s *CoredumpAutoSyncService) ProcessCoredumpRecords() (*ProcessResult, error) {
	taskID := fmt.Sprintf("coredump_sync_%d", time.Now().Unix())
	logging.InfoLogger.Infof("[%s] 开始执行Coredump记录自动化处理", taskID)

	result := &ProcessResult{
		TaskID:    taskID,
		StartTime: time.Now(),
		Errors:    make([]string, 0),
	}

	// 检查干运行模式
	if libs.Config.FeiShuDoc.CoredumpDryRun {
		logging.InfoLogger.Info("干运行模式：模拟完整业务流程")
		return s.simulateCompleteProcess(taskID, result)
	}

	defer func() {
		result.EndTime = time.Now()
		result.Duration = result.EndTime.Sub(result.StartTime)
		logging.InfoLogger.Infof("[%s] 处理完成: 总计=%d, 筛选后=%d, 成功=%d, 失败=%d, 耗时=%v",
			taskID, result.TotalRecords, result.FilteredRecords, result.SuccessRecords, result.FailedRecords, result.Duration)
	}()

	// 步骤1：获取飞书记录（使用服务端筛选优化）
	logging.InfoLogger.Infof("[%s] 步骤1: 获取飞书Coredump记录", taskID)
	feishuRecords, err := s.fetchFeishuRecords()
	if err != nil {
		return nil, fmt.Errorf("获取飞书记录失败: %v", err)
	}

	result.TotalRecords = len(feishuRecords)
	result.FilteredRecords = len(feishuRecords) // 已通过服务端筛选
	logging.InfoLogger.Infof("[%s] 获取到 %d 条筛选后的记录", taskID, result.FilteredRecords)

	if result.FilteredRecords == 0 {
		logging.InfoLogger.Infof("[%s] 无需要处理的记录", taskID)
		return result, nil
	}

	// 步骤2：字段映射和数据解析
	logging.InfoLogger.Infof("[%s] 步骤2: 字段映射和数据解析", taskID)
	coredumpRecords, err := s.fieldMapper.MapRecords(feishuRecords)
	if err != nil {
		return nil, fmt.Errorf("字段映射失败: %v", err)
	}

	logging.InfoLogger.Infof("[%s] 成功映射 %d 条记录", taskID, len(coredumpRecords))

	// 步骤3：处理每条记录（Bug提交和状态更新）
	logging.InfoLogger.Infof("[%s] 步骤3: 处理Coredump记录", taskID)
	err = s.processCoredumpRecords(taskID, coredumpRecords, result)
	if err != nil {
		return nil, fmt.Errorf("记录处理失败: %v", err)
	}

	return result, nil
}

// validateCoredumpConfig 验证Coredump配置
func validateCoredumpConfig() error {
	config := libs.Config.FeiShuDoc

	// 检查基础配置
	if config.AppID == "" {
		return fmt.Errorf("飞书AppID未配置")
	}

	if config.AppSecret == "" {
		return fmt.Errorf("飞书AppSecret未配置")
	}

	// 检查Coredump专用配置
	if config.CoredumpAppToken == "" {
		return fmt.Errorf("Coredump应用令牌未配置")
	}

	if config.CoredumpTableID == "" {
		return fmt.Errorf("Coredump表格ID未配置")
	}

	logging.InfoLogger.Info("Coredump配置验证通过")
	return nil
}

// getFeishuClient 创建飞书客户端
func getFeishuClient() *lark.Client {
	config := libs.Config.FeiShuDoc

	// 创建飞书客户端（复用现有模式）
	client := lark.NewClient(config.AppID, config.AppSecret)
	if client == nil {
		logging.ErrorLogger.Error("创建飞书客户端失败")
		return nil
	}

	logging.InfoLogger.Info("飞书客户端创建成功")
	return client
}

// validateFeishuConnection 验证飞书连接
func validateFeishuConnection(client *lark.Client) error {
	config := libs.Config.FeiShuDoc

	// 构建简单的查询请求来验证连接
	req := larkbitable.NewListAppTableFieldReqBuilder().
		AppToken(config.CoredumpAppToken).
		TableId(config.CoredumpTableID).
		Build()

	// 执行带重试的API调用
	err := executeWithRetry(func() error {
		resp, err := client.Bitable.V1.AppTableField.List(context.Background(), req)
		if err != nil {
			return fmt.Errorf("网络错误: %v", err)
		}

		if !resp.Success() {
			return fmt.Errorf("API错误: 错误码=%d, 错误信息=%s", resp.Code, resp.Msg)
		}

		return nil
	})

	if err != nil {
		return fmt.Errorf("飞书连接验证失败: %v", err)
	}

	logging.InfoLogger.Info("飞书连接验证成功")
	return nil
}

// executeWithRetry 执行带重试的操作（复用现有重试机制）
func executeWithRetry(operation func() error) error {
	maxRetries := 3
	baseDelay := time.Second

	var lastErr error
	for attempt := 0; attempt <= maxRetries; attempt++ {
		err := operation()
		if err == nil {
			return nil // 成功，直接返回
		}

		lastErr = err

		// 如果是最后一次尝试，不再重试
		if attempt == maxRetries {
			break
		}

		// 检查是否是需要重试的错误
		if shouldRetry(err) {
			delay := baseDelay * time.Duration(1<<attempt) // 指数退避
			logging.InfoLogger.Infof("操作失败，%v后重试 (尝试 %d/%d): %v", delay, attempt+1, maxRetries+1, err)
			time.Sleep(delay)
			continue
		}

		// 不需要重试的错误，直接返回
		break
	}

	return fmt.Errorf("操作失败，已重试%d次: %v", maxRetries, lastErr)
}

// shouldRetry 判断是否应该重试（基于现有错误码规范）
func shouldRetry(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()

	// 检查常见的需要重试的错误码
	retryableCodes := []string{
		"1254290", // 请求过快，稍后重试
		"1254291", // 写冲突
		"1254607", // 数据未就绪，请稍后重试
		"1255040", // 请求超时
	}

	for _, code := range retryableCodes {
		if fmt.Sprintf("错误码=%s", code) == errStr ||
			fmt.Sprintf("Code=%s", code) == errStr {
			return true
		}
	}

	// 网络错误通常也需要重试
	if fmt.Sprintf("网络错误") == errStr {
		return true
	}

	return false
}

// fetchFeishuRecords 获取飞书Coredump记录（支持分页查询和服务端筛选）
func (s *CoredumpAutoSyncService) fetchFeishuRecords() ([]map[string]interface{}, error) {
	config := libs.Config.FeiShuDoc

	// 检查干运行模式
	if config.CoredumpDryRun {
		logging.InfoLogger.Info("干运行模式：模拟获取飞书记录")
		return s.simulateFetchRecords()
	}

	logging.InfoLogger.Info("开始获取飞书Coredump记录（使用服务端筛选优化）")

	var allRecords []map[string]interface{}
	pageToken := ""
	pageSize := int64(100) // 批次大小设为100条记录（考虑Coredump记录复杂度）
	totalFetched := 0

	// 获取筛选条件和字段列表
	filter := s.filterBuilder.BuildOptimizedFilter()
	requiredFields := s.filterBuilder.GetRequiredFields()

	// 验证筛选条件
	if err := s.filterBuilder.ValidateFilter(filter); err != nil {
		return nil, fmt.Errorf("筛选条件验证失败: %v", err)
	}

	// 调试模式输出筛选条件
	if config.CoredumpDebugMode {
		s.filterBuilder.DebugFilter(filter)
		logging.InfoLogger.Infof("使用字段列表: %v", requiredFields)
	}

	// 分页查询循环
	for {
		records, nextPageToken, hasMore, err := s.queryFeishuRecordsPage(pageToken, pageSize, filter, requiredFields)
		if err != nil {
			return nil, fmt.Errorf("分页查询失败 (页面令牌: %s): %v", pageToken, err)
		}

		// 添加到结果集
		allRecords = append(allRecords, records...)
		totalFetched += len(records)

		logging.InfoLogger.Infof("已获取 %d 条记录，本页 %d 条", totalFetched, len(records))

		// 检查是否还有更多页面
		if !hasMore {
			break
		}

		pageToken = nextPageToken

		// 添加延迟避免API频率限制
		time.Sleep(100 * time.Millisecond)
	}

	// 输出统计信息
	stats := s.filterBuilder.GetFilterStatistics()
	logging.InfoLogger.Infof("获取完成: 总记录数=%d, 筛选统计=%+v", totalFetched, stats)

	return allRecords, nil
}

// queryFeishuRecordsPage 分页查询飞书记录（复用现有重试机制）
func (s *CoredumpAutoSyncService) queryFeishuRecordsPage(pageToken string, pageSize int64, filter map[string]interface{}, fieldNames []string) ([]map[string]interface{}, string, bool, error) {
	config := libs.Config.FeiShuDoc

	// 构建查询请求
	reqBuilder := larkbitable.NewSearchAppTableRecordReqBuilder().
		AppToken(config.CoredumpAppToken).
		TableId(config.CoredumpTableID)

	// 设置分页参数
	if pageSize > 0 {
		reqBuilder = reqBuilder.PageSize(int(pageSize))
	}

	if pageToken != "" {
		reqBuilder = reqBuilder.PageToken(pageToken)
	}

	// 构建请求体（集成筛选条件和字段选择）
	bodyBuilder := larkbitable.NewSearchAppTableRecordReqBodyBuilder()

	// 设置字段名称（减少数据传输量）
	if len(fieldNames) > 0 {
		bodyBuilder = bodyBuilder.FieldNames(fieldNames)
	}

	// TODO: 设置筛选条件（服务端筛选优化）
	// 当前版本先使用字段选择优化，筛选条件在后续版本中实现
	// if filter != nil {
	//     bodyBuilder = bodyBuilder.Filter(filter)
	// }

	reqBuilder = reqBuilder.Body(bodyBuilder.Build())
	req := reqBuilder.Build()

	// 带重试的API调用
	var records []map[string]interface{}
	var nextPageToken string
	var hasMore bool

	err := executeWithRetry(func() error {
		// 发起请求
		resp, err := s.feishuClient.Bitable.V1.AppTableRecord.Search(context.Background(), req)
		if err != nil {
			return fmt.Errorf("网络错误: %v", err)
		}

		if !resp.Success() {
			return fmt.Errorf("API错误: 错误码=%d, 错误信息=%s", resp.Code, resp.Msg)
		}

		// 解析响应数据
		records = make([]map[string]interface{}, 0)
		if resp.Data != nil && resp.Data.Items != nil {
			for _, item := range resp.Data.Items {
				if item.Fields != nil {
					// 添加记录ID到字段中
					record := make(map[string]interface{})
					for k, v := range item.Fields {
						record[k] = v
					}
					if item.RecordId != nil {
						record["record_id"] = *item.RecordId
					}
					records = append(records, record)
				}
			}
		}

		// 获取分页信息
		if resp.Data != nil {
			if resp.Data.PageToken != nil {
				nextPageToken = *resp.Data.PageToken
			}
			if resp.Data.HasMore != nil {
				hasMore = *resp.Data.HasMore
			}
		}

		return nil
	})

	if err != nil {
		return nil, "", false, err
	}

	// 调试模式输出查询详情
	if config.CoredumpDebugMode {
		logging.InfoLogger.Infof("分页查询结果: 页面令牌=%s, 记录数=%d, 还有更多=%v", pageToken, len(records), hasMore)
	}

	return records, nextPageToken, hasMore, nil
}

// simulateFetchRecords 模拟获取飞书记录（干运行模式）
func (s *CoredumpAutoSyncService) simulateFetchRecords() ([]map[string]interface{}, error) {
	logging.InfoLogger.Info("干运行模式：生成模拟Coredump记录")

	// 生成模拟记录
	mockRecords := []map[string]interface{}{
		{
			"record_id":           "rec001",
			FieldSN:               "SN001",
			FieldComponent:        "组件A",
			FieldSoftwareVersion:  "v1.0.0",
			FieldDeviceModel:      "设备型号A",
			FieldDescription:      "模拟Coredump记录1",
			FieldSyncRequired:     SyncRequired,
			FieldSyncStatus:       SyncNotCompleted,
			FieldProcessingStatus: StatusPending,
		},
		{
			"record_id":           "rec002",
			FieldSN:               "SN002",
			FieldComponent:        "组件B",
			FieldSoftwareVersion:  "v1.1.0",
			FieldDeviceModel:      "设备型号B",
			FieldDescription:      "模拟Coredump记录2",
			FieldSyncRequired:     SyncRequired,
			FieldSyncStatus:       "",
			FieldProcessingStatus: StatusFailed,
		},
		{
			"record_id":           "rec003",
			FieldSN:               "SN003",
			FieldComponent:        "组件C",
			FieldSoftwareVersion:  "v2.0.0",
			FieldDeviceModel:      "设备型号C",
			FieldDescription:      "模拟Coredump记录3",
			FieldSyncRequired:     SyncRequired,
			FieldSyncStatus:       SyncNotCompleted,
			FieldProcessingStatus: "",
		},
	}

	logging.InfoLogger.Infof("干运行模式：生成了 %d 条模拟记录", len(mockRecords))

	// 调试模式输出模拟记录详情
	if libs.Config.FeiShuDoc.CoredumpDebugMode {
		for i, record := range mockRecords {
			logging.InfoLogger.Infof("模拟记录 %d: %+v", i+1, record)
		}
	}

	return mockRecords, nil
}

// simulateCompleteProcess 模拟完整业务流程（干运行模式）
func (s *CoredumpAutoSyncService) simulateCompleteProcess(taskID string, result *ProcessResult) (*ProcessResult, error) {
	logging.InfoLogger.Infof("[%s] 干运行模式：模拟完整业务流程", taskID)

	// 模拟步骤1：获取飞书记录
	logging.InfoLogger.Infof("[%s] 模拟步骤1: 获取飞书记录", taskID)
	mockRecords, err := s.simulateFetchRecords()
	if err != nil {
		return nil, err
	}

	result.TotalRecords = len(mockRecords)
	result.FilteredRecords = len(mockRecords)

	// 模拟步骤2：字段映射
	logging.InfoLogger.Infof("[%s] 模拟步骤2: 字段映射和数据解析", taskID)
	coredumpRecords, err := s.fieldMapper.MapRecords(mockRecords)
	if err != nil {
		return nil, fmt.Errorf("模拟字段映射失败: %v", err)
	}

	// 模拟步骤3：处理记录
	logging.InfoLogger.Infof("[%s] 模拟步骤3: 处理Coredump记录", taskID)
	for i, record := range coredumpRecords {
		logging.InfoLogger.Infof("[%s] 模拟处理记录 %d: SN=%s, 组件=%s", taskID, i+1, record.SN, record.Component)

		// 模拟Bug提交
		if record.SyncRequired == SyncRequired {
			logging.InfoLogger.Infof("[%s] 模拟Bug提交: %s", taskID, record.SN)
			result.SuccessRecords++
		}
	}

	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)

	logging.InfoLogger.Infof("[%s] 干运行模式完成: 总计=%d, 成功=%d", taskID, result.TotalRecords, result.SuccessRecords)
	return result, nil
}

// processCoredumpRecords 处理Coredump记录（核心业务逻辑）
func (s *CoredumpAutoSyncService) processCoredumpRecords(taskID string, records []*CoredumpRecord, result *ProcessResult) error {
	if len(records) == 0 {
		return nil
	}

	logging.InfoLogger.Infof("[%s] 开始处理 %d 条Coredump记录", taskID, len(records))

	// 批量状态更新列表
	var statusUpdates []StatusUpdate

	for i, record := range records {
		logging.InfoLogger.Infof("[%s] 处理记录 %d/%d: SN=%s, 组件=%s", taskID, i+1, len(records), record.SN, record.Component)

		// 检查是否需要同步到Bug系统
		if record.SyncRequired != SyncRequired {
			logging.InfoLogger.Infof("[%s] 记录 %s 无需同步Bug系统，跳过", taskID, record.SN)
			continue
		}

		// 处理单条记录
		err := s.processSingleRecord(taskID, record, &statusUpdates, result)
		if err != nil {
			logging.ErrorLogger.Errorf("[%s] 记录 %s 处理失败: %v", taskID, record.SN, err)
			result.FailedRecords++
			result.Errors = append(result.Errors, fmt.Sprintf("记录 %s 处理失败: %v", record.SN, err))
		} else {
			result.SuccessRecords++
		}

		// 添加延迟避免API频率限制
		if i < len(records)-1 {
			time.Sleep(100 * time.Millisecond)
		}
	}

	// 批量更新状态
	if len(statusUpdates) > 0 {
		logging.InfoLogger.Infof("[%s] 批量更新 %d 条记录状态", taskID, len(statusUpdates))
		batchResult, err := s.statusManager.BatchUpdateRecords(statusUpdates)
		if err != nil {
			logging.ErrorLogger.Errorf("[%s] 批量状态更新失败: %v", taskID, err)
			// 不返回错误，因为主要处理已完成
		} else {
			logging.InfoLogger.Infof("[%s] 批量状态更新完成: 成功=%d, 失败=%d",
				taskID, batchResult.SuccessRecords, batchResult.FailedRecords)
		}
	}

	return nil
}

// processSingleRecord 处理单条Coredump记录
func (s *CoredumpAutoSyncService) processSingleRecord(taskID string, record *CoredumpRecord, statusUpdates *[]StatusUpdate, result *ProcessResult) error {
	// 构建Bug数据
	bugData := s.buildBugDataFromRecord(record)

	// 提交Bug到Bug系统
	logging.InfoLogger.Infof("[%s] 提交Bug: SN=%s, 标题=%s", taskID, record.SN, bugData.Summary)
	bugID, err := s.bugSubmitter.SubmitBug(bugData)
	if err != nil {
		// Bug提交失败，更新状态为失败
		*statusUpdates = append(*statusUpdates, StatusUpdate{
			RecordID:         record.RecordID,
			ProcessingStatus: StatusFailed,
			ErrorMessage:     fmt.Sprintf("Bug提交失败: %v", err),
		})
		return fmt.Errorf("Bug提交失败: %v", err)
	}

	logging.InfoLogger.Infof("[%s] Bug提交成功: SN=%s, BugID=%s", taskID, record.SN, bugID)

	// Bug提交成功，更新状态
	*statusUpdates = append(*statusUpdates, StatusUpdate{
		RecordID:         record.RecordID,
		ProcessingStatus: StatusSuccess,
		SyncStatus:       SyncCompleted,
		BugID:            bugID,
	})

	return nil
}

// buildBugDataFromRecord 从Coredump记录构建Bug数据
func (s *CoredumpAutoSyncService) buildBugDataFromRecord(record *CoredumpRecord) *BugData {
	return &BugData{
		Summary:     fmt.Sprintf("Coredump问题: %s - %s", record.Component, record.SN),
		Description: s.formatCoredumpDescription(record),
		IssueType:   "Bug",
		Priority:    "中",
		ProjectKey:  "COREDUMP",
		Reporter:    record.ComponentResponsible,
		Assignee:    record.ProcessResponsible,
		Components:  record.Component,
		Labels:      []string{"coredump", "自动化", record.Component},
		CustomFields: map[string]interface{}{
			"sn":               record.SN,
			"software_version": record.SoftwareVersion,
			"device_model":     record.DeviceModel,
			"coredump_time":    record.CoredumpTime.Format("2006-01-02 15:04:05"),
		},
	}
}

// formatCoredumpDescription 格式化Coredump描述
func (s *CoredumpAutoSyncService) formatCoredumpDescription(record *CoredumpRecord) string {
	description := fmt.Sprintf("系统检测到Coredump问题，详细信息如下：\n\n")
	description += fmt.Sprintf("SN号: %s\n", record.SN)
	description += fmt.Sprintf("组件: %s\n", record.Component)
	description += fmt.Sprintf("软件版本: %s\n", record.SoftwareVersion)
	description += fmt.Sprintf("设备型号: %s\n", record.DeviceModel)

	if !record.CoredumpTime.IsZero() {
		description += fmt.Sprintf("Coredump时间: %s\n", record.CoredumpTime.Format("2006-01-02 15:04:05"))
	}

	if record.Description != "" {
		description += fmt.Sprintf("问题描述: %s\n", record.Description)
	}

	if record.ComponentResponsible != "" {
		description += fmt.Sprintf("组件负责人: %s\n", record.ComponentResponsible)
	}

	if record.ProcessResponsible != "" {
		description += fmt.Sprintf("进程负责人: %s\n", record.ProcessResponsible)
	}

	description += "\n此Bug由Coredump记录自动化处理系统自动创建。"

	return description
}
