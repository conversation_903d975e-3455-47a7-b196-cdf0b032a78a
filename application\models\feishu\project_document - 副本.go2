package feishu

import "irisAdminApi/application/models"

type FeishuProjectDocument struct {
	models.ModelBase
	PMSDocID              uint    `gorm:"not null" json:"pms_doc_id" update:"1"`
	ProjectName           string  `gorm:"not null; type:varchar(200)" json:"project_name" update:"1"`
	DocumentName          string  `gorm:"type:varchar(200)" json:"document_name" update:"1"`
	DocumentType          string  `gorm:"type:varchar(200)" json:"document_type" update:"1"`
	DocumentUrl           string  `gorm:"type:varchar(200)" json:"document_url" `
	DocumentOwnerName     string  `gorm:"type:varchar(200)" json:"document_owner_name" update:"1"`
	DocumentOwnerID       string  `gorm:"type:varchar(200)" json:"document_owner_id" update:"1"`
	DocumentCommentNum    uint    `gorm:"not null" json:"document_comment_num" `
	DocumentCreatedTime   uint    `gorm:"not null" json:"document_created_time" `
	DocumentReviewStatus  string  `gorm:"type:varchar(100)" json:"document_review_status" `
	FileName              string  `gorm:"type:varchar(200)" json:"file_name" update:"1"`
	FileToken             string  `gorm:"type:varchar(200)" json:"file_token" `
	FileType              string  `gorm:"type:varchar(200)" json:"file_type" `
	Requirement           string  `gorm:"type:varchar(200)" json:"requirement" update:"1"`
	WorkPacketName        string  `gorm:"type:varchar(200)" json:"work_packet_name"  update:"1"`
	TotalCodes            float32 `gorm:"not null" json:"total_codes" update:"1"`
	PacketManagerName     string  `gorm:"type:varchar(200)" json:"packet_manager_name" update:"1"`
	PacketManagerID       string  `gorm:"type:varchar(200)" json:"packet_manager_id" update:"1"`
	RecordID              string  `gorm:"type:varchar(200)" json:"record_id" `
	DocumentSubmitStatus  string  `gorm:"type:varchar(100)" json:"document_submit_status" update:"1" `
	ReviewStatus          string  `gorm:"type:varchar(100)" json:"review_status" update:"1"`
	IsSubmit              uint    `gorm:"not null" json:"is_submit" update:"1"` //入库状态 0 未入库 1 已入库
	IsInstorage           uint    `gorm:"not null" json:"is_instorage" update:"1"`
	DocumentStatus        string  `gorm:"type:varchar(100)" json:"document_status"  update:"1"`
	ComponentPacketName   string  `gorm:"type:varchar(100)" json:"component_packet_name" update:"1"`
	DocumentCategoryTitle string  `gorm:"type:varchar(100)" json:"document_category_title" update:"1"` //文档类型
	RequiredJudge         string  `gorm:"type:varchar(100)" json:"required_judge" `                    //必选评委
	OptionalJudge         string  `gorm:"type:varchar(100)" json:"optional_judge" `                    //可选评委
	JudgeStartTime        uint64  `gorm:"type:bigint(20);notNull" json:"judge_start_time"`             //评审开始时间
	JudgeEndTime          uint64  `gorm:"type:bigint(20);notNull" json:"judge_end_time"`               //评审结束时间
	MeetingStartTime      uint64  `gorm:"type:bigint(20);notNull" json:"meeting_start_time"`           //会议开始时间
	MeetingEndTime        uint64  `gorm:"type:bigint(20);notNull" json:"meeting_end_time"`             //会议结束时间
	MeetingUrl            string  `gorm:"type:varchar(100)" json:"meeting_url" `                       //会议链接
	MeetingTopic          string  `gorm:"type:varchar(100)" json:"meeting_topic" `                     //会议主题
	MeetingID             string  `gorm:"type:varchar(100)" json:"meeting_id" `                        //会议id
	MeetingNO             string  `gorm:"type:varchar(100)" json:"meeting_no" `                        //会议号
	ExpectedReviewEndTime uint64  `gorm:"type:bigint(20);notNull" json:"expected_review_end_time"`     //期望评审结束时间
	AddCodes              float32 `gorm:"not null" json:"add_codes" update:"1"`                        //新增代码
	PortedCodes           float32 `gorm:"not null" json:"ported_codes" update:"1"`                     // 移植代码
	TemporaryPortedCodes  float32 `gorm:"not null" json:"temporary_ported_codes" update:"1"`           //移植代码(临时)
	DepartmentName        string  `gorm:"type:varchar(100)" json:"department_name" update:"1"`         //文档作者所在专业组
	RequestID             uint    `gorm:"not null" json:"request_id" update:"1"`                       //需求ID
	WorkPacketID          uint    `gorm:"not null" json:"work_packet_id" update:"1"`                   //工作ID
	PgttmUserName         string  `gorm:"type:varchar(200)" json:"pgttm_user_name" update:"1"`         //pgttm 用户
	PgttmUserID           string  `gorm:"type:varchar(200)" json:"pgttm_user_id" update:"1"`           //pgttmid
	PstlUserName          string  `gorm:"type:varchar(200)" json:"pstl_user_name" `                    //pstl 用户
	PstlUserID            string  `gorm:"type:varchar(200)" json:"pstl_user_id"`                       //pstl id
}

type FeishuFileList struct {
	models.ModelBase
	FileCreatedTime  uint   `gorm:"not null" json:"file_created_time" `
	FileModifiedTime uint   `gorm:"not null" json:"file_modified_time" `
	Name             string `gorm:"type:varchar(200)" json:"name" `
	OwnerID          string `gorm:"type:varchar(200)" json:"owner_id" `
	ParentToken      string `gorm:"type:varchar(200)" json:"parent_token" `
	Token            string `gorm:"type:varchar(200)" json:"token" `
	Type             string `gorm:"type:varchar(200)" json:"type" `
	Url              string `gorm:"type:varchar(200)" json:"url" `
}

type FeishuReviewConfirmation struct {
	models.ModelBase
	DocID           uint64 `gorm:"type:bigint(20);notNull" json:"doc_id" `
	PmsDocID        uint64 `gorm:"type:bigint(20);notNull" json:"pms_doc_id" `
	ProjectName     string `gorm:"type:varchar(200);notNull" json:"project_name"`
	DocumentName    string `gorm:"type:varchar(200);notNull" json:"document_name" `
	JudgeID         string `gorm:"type:varchar(200)" json:"judge_id" `
	RecordID        string `gorm:"type:varchar(200)" json:"record_id" `
	Confirmation    string `gorm:"type:varchar(200)" json:"confirmation" `
	DocumentUrl     string `gorm:"type:varchar(200)" json:"document_url" `
	DocumentOwnerID string `gorm:"type:varchar(200)" json:"document_owner_id"`
}

type FeishuBitableList struct {
	models.ModelBase
	Bitable_app_token string `gorm:"type:varchar(200)" json:"bitable_app_token" `
	Name              string `gorm:"type:varchar(200)" json:"name" `
	TableID           string `gorm:"type:varchar(200)" json:"table_id" `
	ProjectName       string `gorm:"type:varchar(200)" json:"project_name" `
}

type FeishuProjectList struct {
	models.ModelBase
	ProjectName string `gorm:"type:varchar(200)" json:"project_name" `
	Status      uint   `gorm:"not null,default:1" json:"status"`
}

type FeishuProjectDocumentComment struct {
	models.ModelBase
	PmsDocID           uint64 `gorm:"type:bigint(20) unsigned;notNull" json:"pms_doc_id" update:"1"`
	ProjectName        string `gorm:"type:varchar(200);notNull" json:"project_name" update:"1"`
	DocumentName       string `gorm:"type:varchar(200);notNull" json:"document_name" update:"1"`
	FileToken          string `gorm:"type:varchar(200);notNull" json:"file_token" update:"1"`
	FileType           string `gorm:"type:varchar(200);notNull" json:"file_type" update:"1"`
	CommentID          uint64 `gorm:"type:bigint(20) unsigned;notNull" json:"comment_id"`
	CommentCreatedTime uint64 `gorm:"type:bigint(20);notNull" json:"comment_created_time" update:"1"`
	CommentUpdateTime  uint64 `gorm:"type:bigint(20);notNull" json:"comment_update_time" update:"1"`
	CommentUserID      string `gorm:"type:varchar(100);notNull" json:"comment_user_id" update:"1"`
	Quote              string `gorm:"type:text" json:"quote" update:"1"`
	IsSolved           bool   `gorm:"type:tinyint(1);notNull" json:"is_solved" update:"1"`
	SolvedTime         uint64 `gorm:"type:bigint(20)" json:"solved_time" update:"1"`
	SolverUserID       string `gorm:"type:varchar(200);notNull" json:"solver_user_id" update:"1"`
	CommentContent     string `gorm:"type:longtext" json:"comment_content" update:"1"`
	CommentType        string `gorm:"type:varchar(200);notNull" json:"comment_type" update:"1"`
}

type FeishuProjectDocumentReviewdetail struct {
	models.ModelBase
	PmsDocID                  uint64  `gorm:"type:bigint(20)" json:"pms_doc_id" update:"1"`
	ProjectName               string  `gorm:"type:varchar(200)" json:"project_name" update:"1"`
	DocumentName              string  `gorm:"type:varchar(200)" json:"document_name" update:"1"`
	FileToken                 string  `gorm:"type:varchar(200);notNull" json:"file_token" update:"1"`
	FileType                  string  `gorm:"type:varchar(200)" json:"file_type" update:"1"`
	CommentID                 uint64  `gorm:"type:bigint(20) unsigned" json:"comment_id"`
	CommentUserID             string  `gorm:"type:varchar(100)" json:"comment_user_id" update:"1"`
	Quote                     string  `gorm:"type:text" json:"quote" update:"1"`
	IsSolved                  bool    `gorm:"type:tinyint(1)" json:"is_solved" update:"1"`
	SolverUserID              string  `gorm:"type:varchar(200)" json:"solver_user_id" update:"1"`
	CommentContent            string  `gorm:"type:longtext" json:"comment_content" update:"1"`
	CommentCreatedTime        uint64  `gorm:"type:bigint(20);notNull" json:"comment_created_time" update:"1"`
	CommentType               string  `gorm:"type:varchar(200)" json:"comment_type" update:"1"`
	WorkPacketName            string  `gorm:"type:varchar(200)" json:"work_packet_name"  update:"1"`
	TotalCodes                float32 `gorm:"not null" json:"total_codes" update:"1"`
	PacketManagerName         string  `gorm:"type:varchar(200)" json:"packet_manager_name" update:"1"`
	PacketManagerID           string  `gorm:"type:varchar(200)" json:"packet_manager_id" update:"1"`
	RecordID                  string  `gorm:"type:varchar(200)" json:"record_id" `
	Requirement               string  `gorm:"type:varchar(200)" json:"requirement" update:"1"`
	IsSubmit                  uint    `gorm:"not null" json:"is_submit" update:"1"`                             //入库状态 0 未入库 1 已入库
	DocumentCategoryTitle     string  `gorm:"type:varchar(100)" json:"document_category_title" update:"1"`      //文档类型
	MeetingStartTime          uint64  `gorm:"type:bigint(20);notNull" json:"meeting_start_time"`                //会议开始时间
	MeetingEndTime            uint64  `gorm:"type:bigint(20);notNull" json:"meeting_end_time"`                  //会议结束时间
	MeetingTopic              string  `gorm:"type:varchar(100)" json:"meeting_topic" `                          //会议主题
	MeetingNO                 string  `gorm:"type:varchar(100)" json:"meeting_no" `                             //会议号
	MeetingReviewType         string  `gorm:"type:varchar(100)" json:"meeting_review_type" `                    //评审归属类型
	CommentUserDepartmentName string  `gorm:"type:varchar(100)" json:"comment_user_department_name" update:"1"` //评委所在部门
	DocumentUrl               string  `gorm:"type:varchar(200)" json:"document_url"  update:"1"`
	DocumentOwnerName         string  `gorm:"type:varchar(200)" json:"document_owner_name" update:"1"`
	DocumentOwnerID           string  `gorm:"type:varchar(200)" json:"document_owner_id" update:"1"`
}

type FeishuDocumentTemplate struct {
	models.ModelBase
	Name                  string `gorm:"type:varchar(200)" json:"name" `
	DocumentCategoryTitle string `gorm:"type:varchar(100)" json:"document_category_title" ` //文档类型
	OwnerID               string `gorm:"type:varchar(200)" json:"owner_id" `
	ParentToken           string `gorm:"type:varchar(200)" json:"parent_token" `
	Token                 string `gorm:"type:varchar(200)" json:"token" `
	Type                  string `gorm:"type:varchar(200)" json:"type" `
	Url                   string `gorm:"type:varchar(200)" json:"url" `
}

type FeishuPmsAnalysisData struct {
	models.ModelBase
	RecordType        int      `gorm:"type:tinyint(1);not null;" json:"record_type" update:"1"` // 1:质量目标,2:项目汇总,3工作包数据
	WpStCount         *float64 `json:"wp_st_count" update:"1"`                                  //项目周期缺陷个数=内测前QN + 内测QN
	WpStDefect        *float64 `json:"wp_st_defect" update:"1"`                                 //项目周期缺陷密度=项目周期缺陷个数/总计代码量
	BsIrDefect        *float64 `gorm:"comment:'内测前缺陷密度'" json:"bs_ir_defect" update:"1"`        //内测前缺陷密度=（需求分析、系统设计、总体设计、详细设计、编码、单元测试、集成测试[测试组(准入测试)除外]）QN之和   ρ = QN / 总计代码量
	BsIrCount         *float64 `json:"bs_ir_count" update:"1"`                                  //内测前缺陷个数=（需求分析、系统设计、总体设计、详细设计、编码、单元测试、集成测试[测试组(准入测试)除外]）QN之和
	StUtDefect        *float64 `gorm:"comment:'单元测试缺陷密度'" json:"st_ut_defect" update:"1"`       // 单元测试缺陷密度=开发单元测试BUG数/总计代码量
	StUt0             *float64 `json:"st_ut0" update:"1"`                                       //开发单元测试BUG数
	StIrDefect        *float64 `json:"st_ir_defect" update:"1"`                                 // 内测缺陷密度=（本项目内测BUG个数+非本项目内测BUG个数)/ 总计代码量
	StIr0             *float64 `json:"st_ir0" update:"1"`                                       //内测缺陷个数=（本项目内测BUG个数+非本项目内测BUG个数）
	StIr1             *float64 `json:"st_ir1" update:"1"`                                       //本项目内测BUG个数
	StIr2             *float64 `json:"st_ir2" update:"1"`                                       //非本项目内测BUG个数
	SDdDefect         *float64 `json:"s_dd_defect" update:"1"`                                  //详细设计缺陷密度=详细设计缺陷个数/ 总计代码量
	SDdCount          *float64 `json:"s_dd_count" update:"1"`                                   //评审类型：LLD、MIB、SYSLOG、YANG、LDD  详细设计缺陷个数=需求分析问题数 + 总体设计问题数 + 详细设计问题数
	SSdDefect         *float64 `json:"s_sd_defect" update:"1"`                                  //系统设计缺陷密度=(需求分析问题数 + 总体设计问题数)/ 总计代码量
	SSdCount          *float64 `json:"s_sd_count" update:"1"`                                   //评审类型：SFS  系统设计缺陷个数=需求分析问题数 + 总体设计问题数
	SRaDefect         *float64 `json:"s_ra_defect" update:"1"`                                  //需求分析缺陷密度=需求分析问题数/ 总计代码量
	SRaCount          *float64 `json:"s_ra_count" update:"1"`                                   // 评审类型：PRD、SRS、SCG、CREF、SPEC  需求分析问题数
	SRa0              *float64 `json:"s_ra0" update:"1"`                                        // 评审类型：PRD、SRS、SCG、CREF、SPEC  需求分析问题数
	StIgDefect        *float64 `json:"st_ig_defect" update:"1"`                                 //集成测试缺陷密度=开发集成测试BUG数/ 总计代码量
	StIg0             *float64 `json:"st_ig0" update:"1"`                                       //开发集成测试BUG数
	SCsDefect         *float64 `json:"s_cs_defect" update:"1"`                                  //编码缺陷密度= （编码缺陷个数+软件BUG个数） / 总计代码量
	SCsCount          *float64 `json:"s_cs_count"  update:"1"`                                  //编码缺陷总数=编码缺陷个数+软件BUG个数
	SCsRCount         *float64 `json:"s_cs_r_count" update:"1"`                                 //编码缺陷个数=(需求分析问题数 + 总体设计问题数 + 详细设计问题数 + 编码问题数)
	SCsBCount         *float64 `json:"s_cs_b_count" update:"1"`                                 //软件BUG个数
	CodeTotal         *float64 `json:"code_total" update:"1"`                                   //总计代码量
	CodeNew           *float64 `json:"code_new" update:"1"`                                     //新增代码量
	CodeTrans         *float64 `json:"code_trans" update:"1"`                                   //移植(正式)代码量
	CodeTransTemp     *float64 `json:"code_trans_temp" update:"1"`                              //移植(临时)代码量
	STdDefect         *float64 `json:"s_td_defect" update:"1"`                                  //总体设计缺陷密度=(需求分析问题数 + 总体设计问题数)/ 总计代码量
	STdCount          *float64 `json:"s_td_count" update:"1"`                                   //评审类型：HLD、C-SPEC-D 总体缺陷个数=需求分析问题数+总体设计问题数
	STd1              *float64 `json:"s_td1" update:"1"`                                        //评审类型：HLD总体设计问题数
	STd0              *float64 `json:"s_td0" update:"1"`                                        //评审类型：C-SPEC-D需求分析问题数
	ProjectName       string   `gorm:"type:varchar(255)" json:"project_name" update:"1"`
	ProjectID         int      `json:"project_id" update:"1"`
	BsTtA             *float64 `json:"bs_tt_a" update:"1"`
	BsIgHDefect       *float64 `json:"bs_ig_h_defect" update:"1"`
	BsIgA             *float64 `json:"bs_ig_a" update:"1"`
	BsTtADefect       *float64 `json:"bs_tt_a_defect" update:"1"`
	BsIgADefect       *float64 `json:"bs_ig_a_defect" update:"1"`
	BsIgH             *float64 `json:"bs_ig_h" update:"1"`
	WorkPacketName    string   `gorm:"type:varchar(255)" json:"work_packet_name" update:"1"`
	SSd0              *float64 `json:"s_sd0"  update:"1"` //系统设计->需求分析问题数(评审类型：HLD、C-SPEC-D)
	SSd1              *float64 `json:"s_sd1"  update:"1"` //系统设计->总体设计问题数(评审类型：HLD、C-SPEC-D)
	StIr1Defect       *float64 `json:"st_ir1_defect"  update:"1"`
	SCr0              *float64 `json:"s_cr0"  update:"1"` //编码缺陷个数->需求分析问题数
	SCr1              *float64 `json:"s_cr1" update:"1"`  //编码缺陷个数->总体设计问题数
	SCr2              *float64 `json:"s_cr2"  update:"1"` //编码缺陷个数->详细设计问题数
	SCr3              *float64 `json:"s_cr3"  update:"1"` //编码缺陷个数->编码问题数
	WorkPacketID      int      `json:"work_packet_id"  update:"1"`
	SDd1              *float64 `json:"s_dd1" update:"1"`     //详细设计->总体设计问题数（评审类型：LLD、MIB、SYSLOG、YANG、LDD）
	SDd0              *float64 `json:"s_dd0" update:"1"`     //详细设计->需求分析问题数（评审类型：LLD、MIB、SYSLOG、YANG、LDD）
	SDd2              *float64 `json:"s_dd2" update:"1"`     //详细设计->详细设计问题数（评审类型：LLD、MIB、SYSLOG、YANG、LDD）
	SCbDc0            *float64 `json:"s_cb_dc0" update:"1"`  //软件BUG数->指标问题  BUG来源分类：开发代码审查
	SCbDc1            *float64 `json:"s_cb_dc1" update:"1"`  //软件BUG数->总体设计
	SCbDc2            *float64 `json:"s_cb_dc2" update:"1"`  //软件BUG数->详细设计
	SCbDc3            *float64 `json:"s_cb_dc3" update:"1"`  //软件BUG数->编码阶段
	SCbDc4            *float64 `json:"s_cb_dc4"  update:"1"` //软件BUG数->未指定引入状态
	OBs0              *float64 `json:"o_bs0" update:"1"`
	WorkPacketManager string   `gorm:"type:varchar(255)" json:"work_packet_manager" update:"1"` //工作包负责人
	PSTLUserName      string   `gorm:"type:varchar(100)" json:"pstl_user_name"  update:"1"`
}

type FeishuWorkflowDocument struct {
	models.ModelBase
	WorkflowID                string `gorm:"type:varchar(200)" json:"workflow_id" `
	ProjectKey                string `gorm:"type:varchar(100)" json:"project_key" `
	WorkItemTypeKey           string `gorm:"type:varchar(200)" json:"work_item_type_key" `
	ProjectName               string `gorm:"type:varchar(200)" json:"project_name" `
	PatchFile                 string `gorm:"type:varchar(200)" json:"patch_file" `
	Status                    uint   `gorm:"not null" json:"status" `
	PatchApplicableModels     string `gorm:"type:varchar(200)" json:"patch_applicable_models" `
	PatchApplicableVersions   string `gorm:"type:varchar(200)" json:"patch_applicable_versions" `
	IssuesResolvedTestVersion string `gorm:"type:varchar(200)" json:"issues_resolved_test_version" `
	DocFilePath               string `gorm:"type:varchar(200)" json:"doc_file_path" `
}
