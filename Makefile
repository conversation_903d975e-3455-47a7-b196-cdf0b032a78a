BUILD_VERSION   := v1.1.1
BUILD_TIME      := $(shell date "+%F %T")
BUILD_NAME      := app_$(shell date "+%Y%m%d%H" )
COMMIT_SHA1     := $(shell git rev-parse HEAD )

TARGET = "buildFarmApi"

LDFLAGS := "-X 'main.BuildVersion=${BUILD_VERSION}' -X 'main.BuildTime=${BUILD_TIME}' -X 'main.BuildName=${BUILD_NAME}' -X 'main.CommitID=${COMMIT_SHA1}'"

.PHONY: help

all:
	rm ./output/* -rf
	go build -ldflags $(LDFLAGS) -o ./output/$(TARGET) main.go

ntos_gdb_init:
	go build -o ./output/ntos_gdb_init tools/ntos_gdb_init/*.go

noticer:
	go build -o ./output/noticer tools/noticer/*.go