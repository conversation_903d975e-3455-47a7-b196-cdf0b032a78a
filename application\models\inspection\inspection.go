package inspection

import "irisAdminApi/application/models"

type InspectionJob struct {
	models.ModelBase
	JobID  string `gorm:"not null; type:varchar(60)" json:"job_id" form:"job_id"`
	Status uint   `gorm:"not null; default:0" json:"status"`
}

type InspectionLog struct {
	models.ModelBase
	JobID    string `gorm:"not null; type:varchar(60)" json:"job_id" form:"job_id"`
	Hostname string `gorm:"not null; type:varchar(60)" json:"hostname" form:"hostname"`
	Ip       string `gorm:"not null; type:varchar(60)" json:"ip" form:"ip"`
	Log      string `gorm:"not null; type:varchar(300)" json:"log" form:"log"`
}
