<?xml version='1.0' encoding='UTF-8'?>
<config xmlns="urn:ruijie:ntos">
  <vrf>
    <name>main</name>
    <aaa xmlns="urn:ruijie:ntos:params:xml:ns:yang:aaad">
      <enabled>true</enabled>
      <domain-enabled>true</domain-enabled>
      <domain>
        <name>default</name>
        <authentication>
          <sslvpn>
            <method>default</method>
            <enabled>true</enabled>
          </sslvpn>
          <webauth>
            <method>default</method>
            <enabled>true</enabled>
          </webauth>
        </authentication>
        <enabled>true</enabled>
        <username-format>
          <without-domain/>
        </username-format>
        <auto-create-group>true</auto-create-group>
      </domain>
      <authentication>
        <sslvpn>
          <name>default</name>
          <subs/>
          <auth-order>only-subs</auth-order>
        </sslvpn>
        <webauth>
          <name>default</name>
          <subs/>
          <auth-order>only-subs</auth-order>
        </webauth>
      </authentication>
      <accounting>
        <update>
          <periodic>5</periodic>
          <enabled>false</enabled>
        </update>
      </accounting>
    </aaa>
    <anti-virus xmlns="urn:ruijie:ntos:params:xml:ns:yang:anti-virus">
    <template><name>default</name><description>Profile for default</description><scan-mode>deep</scan-mode><protocols><protocol><name>FTP</name><direction>both</direction></protocol><protocol><name>HTTP</name><direction>both</direction></protocol><protocol><name>IMAP</name><direction>to-client</direction></protocol><protocol><name>NFS</name><direction>both</direction></protocol><protocol><name>POP3</name><direction>to-client</direction></protocol><protocol><name>SMB</name><direction>both</direction></protocol><protocol><name>SMTP</name><direction>to-server</direction></protocol></protocols><file-type-sets><file-type><suffix>swf</suffix></file-type><file-type><suffix>a</suffix></file-type><file-type><suffix>class</suffix></file-type><file-type><suffix>com</suffix></file-type><file-type><suffix>dex</suffix></file-type><file-type><suffix>dll</suffix></file-type><file-type><suffix>elf</suffix></file-type><file-type><suffix>exe</suffix></file-type><file-type><suffix>macho</suffix></file-type><file-type><suffix>msi</suffix></file-type><file-type><suffix>ocx</suffix></file-type><file-type><suffix>pascal</suffix></file-type><file-type><suffix>reg</suffix></file-type><file-type><suffix>rpm</suffix></file-type><file-type><suffix>doc</suffix></file-type><file-type><suffix>docx</suffix></file-type><file-type><suffix>dot</suffix></file-type><file-type><suffix>dotx</suffix></file-type><file-type><suffix>eml</suffix></file-type><file-type><suffix>fla</suffix></file-type><file-type><suffix>pdf</suffix></file-type><file-type><suffix>potx</suffix></file-type><file-type><suffix>ppt</suffix></file-type><file-type><suffix>pptx</suffix></file-type><file-type><suffix>rtf</suffix></file-type><file-type><suffix>vsd</suffix></file-type><file-type><suffix>xls</suffix></file-type><file-type><suffix>xlsx</suffix></file-type><file-type><suffix>xltx</suffix></file-type><file-type><suffix>7z</suffix></file-type><file-type><suffix>bz2</suffix></file-type><file-type><suffix>cab</suffix></file-type><file-type><suffix>cpio</suffix></file-type><file-type><suffix>gz</suffix></file-type><file-type><suffix>jar</suffix></file-type><file-type><suffix>rar</suffix></file-type><file-type><suffix>xz</suffix></file-type><file-type><suffix>zip</suffix></file-type><file-type><suffix>bash</suffix></file-type><file-type><suffix>bat</suffix></file-type><file-type><suffix>pl</suffix></file-type><file-type><suffix>py</suffix></file-type><file-type><suffix>sh</suffix></file-type><file-type><suffix>apk</suffix></file-type></file-type-sets></template></anti-virus>
    <app-parse-mgmt xmlns="urn:ruijie:ntos:params:xml:ns:yang:app-parse-mgmt">
      <overload-protection>
        <enabled>true</enabled>
        <action>bypass</action>
      </overload-protection>
      <http>
        <decompress-length>2048</decompress-length>
      </http>
    </app-parse-mgmt>
    <appid xmlns="urn:ruijie:ntos:params:xml:ns:yang:appid">
      <mode>dynamic-identify</mode>
    </appid>
    <arp xmlns="urn:ruijie:ntos:params:xml:ns:yang:arp">
      <proxy-enabled>false</proxy-enabled>
      <gratuitous-send>
        <enabled>false</enabled>
        <interval>30</interval>
      </gratuitous-send>
    </arp>
    <bridge xmlns="urn:ruijie:ntos:params:xml:ns:yang:bridge">
      <fdb>
        <aging>300</aging>
      </fdb>
    </bridge>
    <collab-disposal xmlns="urn:ruijie:ntos:params:xml:ns:yang:collab-disposal">
      <enabled>false</enabled>
      <identity-system>none</identity-system>
    </collab-disposal>
    <content-audit xmlns="urn:ruijie:ntos:params:xml:ns:yang:content-audit">
      <policy>
        <name>any</name>
        <enabled>true</enabled>
        <action>
          <audit-all/>
        </action>
        <time-range>any</time-range>
      </policy>
      <default-policy>
        <name>default_recommended</name>
        <enabled>true</enabled>
      </default-policy>
      <default-policy>
        <name>default_all</name>
        <enabled>true</enabled>
      </default-policy>
    </content-audit>
    <keyword-group xmlns="urn:ruijie:ntos:params:xml:ns:yang:content-filter">
      <group>
        <name>test</name>
        <description>test</description>
        <keyword>test</keyword>
      </group>
    </keyword-group>
    <content-filter xmlns="urn:ruijie:ntos:params:xml:ns:yang:content-filter">
      <profile>
        <name>content_filter</name>
        <rule>
          <name>1</name>
          <protocols>any</protocols>
          <direction>both</direction>
          <action>block</action>
          <keyword-group>test</keyword-group>
        </rule>
      </profile>
    </content-filter>
    <dhcp xmlns="urn:ruijie:ntos:params:xml:ns:yang:dhcp">
      <server>
        <enabled>true</enabled>
        <default-lease-time>43200</default-lease-time>
        <max-lease-time>86400</max-lease-time>
        <subnet>
          <prefix>**********/24</prefix>
          <interface>Ge0/1.77</interface>
          <description>**********/24</description>
          <default-gateway>**********</default-gateway>
          <range>
            <start-ip>**********</start-ip>
            <end-ip>************</end-ip>
          </range>
          <ping-check>true</ping-check>
          <default-lease-time>28800</default-lease-time>
          <max-lease-time>28800</max-lease-time>
          <lease-id-format>hex</lease-id-format>
          <warning-high-threshold>90</warning-high-threshold>
          <warning-low-threshold>80</warning-low-threshold>
          <dhcp-options>
            <domain-name-server>***************</domain-name-server>
          </dhcp-options>
        </subnet>
      </server>
      <relay>
        <enabled>true</enabled>
        <handle-option>append</handle-option>
        <drop-unmatched>false</drop-unmatched>
        <hop-count>10</hop-count>
        <max-size>576</max-size>
      </relay>
      <snooping xmlns="urn:ruijie:ntos:params:xml:ns:yang:dhcp-snooping">
        <enabled>false</enabled>
        <mode>normal</mode>
        <loose-forward>false</loose-forward>
        <bootp-bind>false</bootp-bind>
        <giaddr-check>false</giaddr-check>
        <mac-verify>false</mac-verify>
        <unicast>false</unicast>
        <db-write-interval>0</db-write-interval>
      </snooping>
    </dhcp>
    <dns xmlns="urn:ruijie:ntos:params:xml:ns:yang:dns">
      <server>
        <address>***********</address>
      </server>
      <proxy>
        <enabled>false</enabled>
      </proxy>
    </dns>
    <dns-client xmlns="urn:ruijie:ntos:params:xml:ns:yang:dns-client">
      <ip-domain-lookup>
        <enabled>true</enabled>
      </ip-domain-lookup>
    </dns-client>
    <dns-security xmlns="urn:ruijie:ntos:params:xml:ns:yang:dns-security">
      <dns-filter>
        <dns-status>false</dns-status>
      </dns-filter>
      <dns-attack-defense>
        <attack-defense>
          <protocol>true</protocol>
          <security-vul>true</security-vul>
        </attack-defense>
        <flood-defense>
          <enable>true</enable>
        </flood-defense>
      </dns-attack-defense>
      <dns-security-cloud>
        <pdns>true</pdns>
        <online-protect>true</online-protect>
      </dns-security-cloud>
    </dns-security>
    <dns-transparent-proxy xmlns="urn:ruijie:ntos:params:xml:ns:yang:dns-transparent-proxy">
      <enabled>true</enabled>
      <mode>mllb</mode>
    </dns-transparent-proxy>
    <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
      <enabled>true</enabled>
      <channel>
        <name>vti</name>
        <shared>false</shared>
        <config-source>manual</config-source>
        <priority>4</priority>
        <fine-grained>false</fine-grained>
        <bandwidth>
          <whole>
            <maximum-bandwidth>
              <upstream>10000000</upstream>
              <downstream>10000000</downstream>
            </maximum-bandwidth>
            <guaranteed-bandwidth>
              <upstream>10000000</upstream>
              <downstream>10000000</downstream>
            </guaranteed-bandwidth>
          </whole>
          <per-ip-or-user>per-ip</per-ip-or-user>
          <per-ip>
            <manual>
              <maximum-bandwidth>
                <upstream>100000</upstream>
                <downstream>100000</downstream>
              </maximum-bandwidth>
            </manual>
          </per-ip>
        </bandwidth>
      </channel>
      <channel>
        <name>route</name>
        <shared>false</shared>
        <config-source>manual</config-source>
        <priority>1</priority>
        <fine-grained>false</fine-grained>
        <bandwidth>
          <whole>
            <maximum-bandwidth>
              <upstream>10000000</upstream>
              <downstream>10000000</downstream>
            </maximum-bandwidth>
            <guaranteed-bandwidth>
              <upstream>10000000</upstream>
              <downstream>10000000</downstream>
            </guaranteed-bandwidth>
          </whole>
          <per-ip-or-user>per-ip</per-ip-or-user>
          <per-ip>
            <manual>
              <maximum-bandwidth>
                <upstream>100000</upstream>
                <downstream>100000</downstream>
              </maximum-bandwidth>
            </manual>
          </per-ip>
        </bandwidth>
      </channel>
      <policy>
        <name>ispec</name>
        <enabled>true</enabled>
        <config-source>manual</config-source>
        <dest-network>
          <name>10.1.1-4</name>
        </dest-network>
        <time-range>any</time-range>
        <action>
          <permit/>
        </action>
      </policy>
      <policy>
        <name>1</name>
        <enabled>true</enabled>
        <config-source>manual</config-source>
        <parent>ispec</parent>
        <dest-network>
          <name>********</name>
        </dest-network>
        <time-range>any</time-range>
        <action>
          <limit>
            <channel>vti</channel>
          </limit>
        </action>
      </policy>
      <policy>
        <name>2</name>
        <enabled>true</enabled>
        <config-source>manual</config-source>
        <parent>ispec</parent>
        <dest-network>
          <name>********</name>
        </dest-network>
        <time-range>any</time-range>
        <action>
          <limit>
            <channel>vti</channel>
          </limit>
        </action>
      </policy>
      <policy>
        <name>3</name>
        <enabled>true</enabled>
        <config-source>manual</config-source>
        <parent>ispec</parent>
        <dest-network>
          <name>********</name>
        </dest-network>
        <time-range>any</time-range>
        <action>
          <limit>
            <channel>vti</channel>
          </limit>
        </action>
      </policy>
      <policy>
        <name>4</name>
        <enabled>true</enabled>
        <config-source>manual</config-source>
        <parent>ispec</parent>
        <dest-network>
          <name>********</name>
        </dest-network>
        <time-range>any</time-range>
        <action>
          <limit>
            <channel>vti</channel>
          </limit>
        </action>
      </policy>
      <policy>
        <name>route</name>
        <enabled>true</enabled>
        <config-source>manual</config-source>
        <dest-network>
          <name>10.1_and_10.2</name>
        </dest-network>
        <time-range>any</time-range>
        <action>
          <limit>
            <channel>route</channel>
          </limit>
        </action>
      </policy>
    </flow-control>
    <ha xmlns="urn:ruijie:ntos:params:xml:ns:yang:ha">
      <group>
        <id>0</id>
        <description>group0</description>
        <priority>100</priority>
        <monitor>
          <monitor-name>HA0-TrackIF</monitor-name>
        </monitor>
        <monitor>
          <monitor-name>HA0-TrackIP</monitor-name>
        </monitor>
      </group>
      <link>
        <link>ha</link>
        <local-addr>************</local-addr>
        <peer-addr>************</peer-addr>
      </link>
      <mgmt-link>
        <link>MGMT</link>
      </mgmt-link>
      <mode>A-P</mode>
      <heart-interval>1000</heart-interval>
      <heart-alert-count>3</heart-alert-count>
      <gra-arp-count>5</gra-arp-count>
      <vmac-prefix>00:00:3e</vmac-prefix>
      <preempt>false</preempt>
      <preempt-delay>60</preempt-delay>
      <session-sync>true</session-sync>
      <neigh-sync>true</neigh-sync>
      <switch-link-time>1</switch-link-time>
      <auth-type>none</auth-type>
      <enabled>true</enabled>
      <log-level>
        <group>on</group>
        <adv>off</adv>
        <dbus>on</dbus>
        <monitor>off</monitor>
      </log-level>
    </ha>
    <ike xmlns="urn:ruijie:ntos:params:xml:ns:yang:ike">
      <proposal>
        <name>default</name>
        <prf>sha-256</prf>
        <life-seconds>86400</life-seconds>
        <encrypt-alg>des des3 aes-128 aes-192 aes-256</encrypt-alg>
        <hash-alg>md5 sha</hash-alg>
        <dh-group>group1 group2 group5</dh-group>
        <auth-mode>preshared-key</auth-mode>
      </proposal>
      <proposal>
        <name>branch3-core1-TenGe0_0.902</name>
        <prf>sha-256</prf>
        <life-seconds>86400</life-seconds>
        <encrypt-alg>aes-128</encrypt-alg>
        <hash-alg>sha</hash-alg>
        <dh-group>group5</dh-group>
        <auth-mode>preshared-key</auth-mode>
      </proposal>
      <proposal>
        <name>branch3-core1-TenGe0_0.903</name>
        <prf>sha-256</prf>
        <life-seconds>86400</life-seconds>
        <encrypt-alg>aes-128</encrypt-alg>
        <hash-alg>sha</hash-alg>
        <dh-group>group5</dh-group>
        <auth-mode>preshared-key</auth-mode>
      </proposal>
      <proposal>
        <name>branch3-core1-TenGe0_0.904</name>
        <prf>sha-256</prf>
        <life-seconds>86400</life-seconds>
        <encrypt-alg>aes-128</encrypt-alg>
        <hash-alg>sha</hash-alg>
        <dh-group>group5</dh-group>
        <auth-mode>preshared-key</auth-mode>
      </proposal>
      <proposal>
        <name>branch3-core1-TenGe0_0.901</name>
        <prf>sha-256</prf>
        <life-seconds>86400</life-seconds>
        <encrypt-alg>aes-128</encrypt-alg>
        <hash-alg>sha</hash-alg>
        <dh-group>group5</dh-group>
        <auth-mode>preshared-key</auth-mode>
      </proposal>
      <key>
        <type>pre-share</type>
        <ipv4-address>
          <ipv4-address>********</ipv4-address>
          <key>=*-#!$_2g+awtqJWZWf+C6zl4RLQ==</key>
        </ipv4-address>
        <ipv4-address>
          <ipv4-address>********</ipv4-address>
          <key>=*-#!$_2g+awtqJWZWf+C6zl4RLQ==</key>
        </ipv4-address>
        <ipv4-address>
          <ipv4-address>********</ipv4-address>
          <key>=*-#!$_2g+awtqJWZWf+C6zl4RLQ==</key>
        </ipv4-address>
        <ipv4-address>
          <ipv4-address>********</ipv4-address>
          <key>=*-#!$_2g+awtqJWZWf+C6zl4RLQ==</key>
        </ipv4-address>
      </key>
      <profile>
        <name>default</name>
      </profile>
      <profile>
        <name>temporary</name>
      </profile>
      <profile>
        <name>branch3-core1-TenGe0_0.902</name>
        <default-psk>=*-#!$_2g+awtqJWZWf+C6zl4RLQ==</default-psk>
      </profile>
      <profile>
        <name>branch3-core1-TenGe0_0.903</name>
        <default-psk>=*-#!$_2g+awtqJWZWf+C6zl4RLQ==</default-psk>
      </profile>
      <profile>
        <name>branch3-core1-TenGe0_0.904</name>
        <default-psk>=*-#!$_2g+awtqJWZWf+C6zl4RLQ==</default-psk>
      </profile>
      <profile>
        <name>branch3-core1-TenGe0_0.901</name>
        <default-psk>=*-#!$_2g+awtqJWZWf+C6zl4RLQ==</default-psk>
        <peer-id>
          <ipv4-address>
            <ipv4-address>********</ipv4-address>
          </ipv4-address>
        </peer-id>
      </profile>
      <dpd>
        <interval>30</interval>
        <retry-interval>5</retry-interval>
      </dpd>
      <nat-traversal>
        <enabled>true</enabled>
      </nat-traversal>
      <nat>
        <keepalive>20</keepalive>
      </nat>
    </ike>
    <interface xmlns="urn:ruijie:ntos:params:xml:ns:yang:interface">
      <snmp>
        <if-usage-compute-interval>30</if-usage-compute-interval>
        <if-global-notify-enable>false</if-global-notify-enable>
      </snmp>
      <physical>
        <name>Ge0/15</name>
        <mtu>1500</mtu>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <ethernet>
          <mac-address>00:d0:f9:22:36:a9</mac-address>
        </ethernet>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>MGMT</name>
        <mtu>1500</mtu>
        <promiscuous>false</promiscuous>
        <description/>
        <enabled>true</enabled>
        <wanlan>lan</wanlan>
        <working-mode>route</working-mode>
        <ipv4>
          <address>
            <ip>************/24</ip>
          </address>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
          <nd>
            <suppress-ra>true</suppress-ra>
          </nd>
          <dhcp>
            <enabled>false</enabled>
            <dad-wait-time>1</dad-wait-time>
            <prefix-request>false</prefix-request>
            <information-request>false</information-request>
          </dhcp>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
          <ipv6>
            <autoconfiguration>false</autoconfiguration>
            <accept-router-advert>never</accept-router-advert>
          </ipv6>
        </network-stack>
        <reverse-path>false</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/3</name>
        <mtu>1500</mtu>
        <promiscuous>false</promiscuous>
        <description/>
        <enabled>true</enabled>
        <wanlan>wan</wanlan>
        <working-mode>route</working-mode>
        <ha-group>0</ha-group>
        <ipv4>
          <address>
            <ip>********/24</ip>
          </address>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/4</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ha-group>0</ha-group>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/5</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ha-group>0</ha-group>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/6</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ha-group>0</ha-group>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/7</name>
        <mtu>1500</mtu>
        <promiscuous>false</promiscuous>
        <description/>
        <enabled>true</enabled>
        <wanlan>lan</wanlan>
        <working-mode>route</working-mode>
        <ipv4>
          <enabled>true</enabled>
          <pppoe>
            <connection>
              <tunnel>5</tunnel>
              <enabled>true</enabled>
              <user>test</user>
              <password>=*-#!$8WZHIA4FEa0=</password>
              <gateway>true</gateway>
              <timeout>5</timeout>
              <retries>3</retries>
              <ppp>
                <negotiation-timeout>3</negotiation-timeout>
                <lcp-echo-interval>10</lcp-echo-interval>
                <lcp-echo-retries>10</lcp-echo-retries>
              </ppp>
              <ppp-mtu>64</ppp-mtu>
              <reverse-path>true</reverse-path>
              <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
                <enabled>false</enabled>
              </flow-control>
              <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
                <https>false</https>
                <ping>false</ping>
                <ssh>false</ssh>
              </access-control>
            </connection>
          </pppoe>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/8</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ha-group>0</ha-group>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/9</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ha-group>0</ha-group>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/10</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ha-group>0</ha-group>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/11</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ha-group>0</ha-group>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/12</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ha-group>0</ha-group>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/13</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ha-group>0</ha-group>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>TenGe0/0</name>
        <mtu>1500</mtu>
        <promiscuous>false</promiscuous>
        <description/>
        <enabled>true</enabled>
        <wanlan>wan</wanlan>
        <working-mode>route</working-mode>
        <ha-group>0</ha-group>
        <ipv4>
          <address>
            <ip>********/24</ip>
            <nexthop>**********</nexthop>
          </address>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <upload-bandwidth>
          <upload-bandwidth-value>10000000</upload-bandwidth-value>
          <upload-bandwidth-unit>kbps</upload-bandwidth-unit>
        </upload-bandwidth>
        <download-bandwidth>
          <download-bandwidth-value>10000000</download-bandwidth-value>
          <download-bandwidth-unit>kbps</download-bandwidth-unit>
        </download-bandwidth>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>true</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </physical>
      <physical>
        <name>TenGe0/1</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ha-group>0</ha-group>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>TenGe0/2</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ha-group>0</ha-group>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>TenGe0/3</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ha-group>0</ha-group>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>TenGe0/4</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ha-group>0</ha-group>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>TenGe0/5</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ha-group>0</ha-group>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>TenGe0/6</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ha-group>0</ha-group>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>TenGe0/7</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ha-group>0</ha-group>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>TenGe0/8</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ha-group>0</ha-group>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>TenGe0/9</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ha-group>0</ha-group>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/14</name>
        <mtu>1500</mtu>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <ethernet>
          <mac-address>00:d0:f9:22:36:a9</mac-address>
        </ethernet>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <lag xmlns="urn:ruijie:ntos:params:xml:ns:yang:lag">
        <name>ha</name>
        <mtu>1500</mtu>
        <enabled>true</enabled>
        <ethernet>
          <mac-address>00:d0:f9:22:36:a9</mac-address>
        </ethernet>
        <wanlan>lan</wanlan>
        <working-mode>route</working-mode>
        <ipv4>
          <address>
            <ip>************/24</ip>
          </address>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <mode>xor</mode>
        <xmit-hash-policy>layer2+3</xmit-hash-policy>
        <up-link-monitoring>0</up-link-monitoring>
        <mii-link-monitoring>100</mii-link-monitoring>
        <link-interface>
          <slave>Ge0/14</slave>
        </link-interface>
        <link-interface>
          <slave>Ge0/15</slave>
        </link-interface>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
      </lag>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>TenGe0/0.901</name>
        <mtu>1500</mtu>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <address>
            <ip>********/24</ip>
            <nexthop>********54</nexthop>
          </address>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <address>
            <ip>77:1::1/64</ip>
          </address>
          <address>
            <ip>77:1:1::1/64</ip>
          </address>
          <enabled>true</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <vlan-id>901</vlan-id>
        <link-interface>TenGe0/0</link-interface>
        <protocol>802.1q</protocol>
        <ha-group>0</ha-group>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <isp-config xmlns="urn:ruijie:ntos:params:xml:ns:yang:isp">
          <isp-route>
            <name>CNC</name>
          </isp-route>
        </isp-config>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>TenGe0/0.902</name>
        <mtu>1500</mtu>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <address>
            <ip>********/24</ip>
            <nexthop>********54</nexthop>
          </address>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <address>
            <ip>77:2::1/64</ip>
          </address>
          <address>
            <ip>77:1:2::1/64</ip>
          </address>
          <enabled>true</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <vlan-id>902</vlan-id>
        <link-interface>TenGe0/0</link-interface>
        <protocol>802.1q</protocol>
        <ha-group>0</ha-group>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <isp-config xmlns="urn:ruijie:ntos:params:xml:ns:yang:isp">
          <isp-route>
            <name>CNII</name>
          </isp-route>
        </isp-config>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>TenGe0/0.903</name>
        <mtu>1500</mtu>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <address>
            <ip>********/24</ip>
            <nexthop>********54</nexthop>
          </address>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <address>
            <ip>77:3::1/64</ip>
          </address>
          <address>
            <ip>77:1:3::1/64</ip>
          </address>
          <enabled>true</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <vlan-id>903</vlan-id>
        <link-interface>TenGe0/0</link-interface>
        <protocol>802.1q</protocol>
        <ha-group>0</ha-group>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <isp-config xmlns="urn:ruijie:ntos:params:xml:ns:yang:isp">
          <isp-route>
            <name>CERNET</name>
          </isp-route>
        </isp-config>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>TenGe0/0.904</name>
        <mtu>1500</mtu>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <address>
            <ip>********/24</ip>
            <nexthop>********54</nexthop>
          </address>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <address>
            <ip>77:4::1/64</ip>
          </address>
          <address>
            <ip>77:1:4::1/64</ip>
          </address>
          <enabled>true</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <vlan-id>904</vlan-id>
        <link-interface>TenGe0/0</link-interface>
        <protocol>802.1q</protocol>
        <ha-group>0</ha-group>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <isp-config xmlns="urn:ruijie:ntos:params:xml:ns:yang:isp">
          <isp-route>
            <name>CMCC</name>
          </isp-route>
        </isp-config>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>TenGe0/0.905</name>
        <mtu>1500</mtu>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <address>
            <ip>********/24</ip>
            <nexthop>********54</nexthop>
          </address>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <address>
            <ip>77:5::1/64</ip>
          </address>
          <address>
            <ip>77:1:5::1/64</ip>
          </address>
          <enabled>true</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <vlan-id>905</vlan-id>
        <link-interface>TenGe0/0</link-interface>
        <protocol>802.1q</protocol>
        <ha-group>0</ha-group>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <isp-config xmlns="urn:ruijie:ntos:params:xml:ns:yang:isp">
          <isp-route>
            <name>BEIJINGTELETRON</name>
          </isp-route>
        </isp-config>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>TenGe0/0.906</name>
        <mtu>1500</mtu>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <address>
            <ip>********/24</ip>
            <nexthop>********54</nexthop>
          </address>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <address>
            <ip>77:6::1/64</ip>
          </address>
          <address>
            <ip>77:1:6::1/64</ip>
          </address>
          <enabled>true</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <vlan-id>906</vlan-id>
        <link-interface>TenGe0/0</link-interface>
        <protocol>802.1q</protocol>
        <ha-group>0</ha-group>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <isp-config xmlns="urn:ruijie:ntos:params:xml:ns:yang:isp">
          <isp-route>
            <name>CNC</name>
          </isp-route>
        </isp-config>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>TenGe0/0.907</name>
        <mtu>1500</mtu>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <address>
            <ip>********/24</ip>
            <nexthop>**********</nexthop>
          </address>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <address>
            <ip>77:7::1/64</ip>
          </address>
          <address>
            <ip>77:1:7::1/64</ip>
          </address>
          <enabled>true</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <vlan-id>907</vlan-id>
        <link-interface>TenGe0/0</link-interface>
        <protocol>802.1q</protocol>
        <ha-group>0</ha-group>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <isp-config xmlns="urn:ruijie:ntos:params:xml:ns:yang:isp">
          <isp-route>
            <name>CNII</name>
          </isp-route>
        </isp-config>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>TenGe0/0.908</name>
        <mtu>1500</mtu>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <address>
            <ip>********/24</ip>
            <nexthop>********54</nexthop>
          </address>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <address>
            <ip>77:8::1/64</ip>
          </address>
          <address>
            <ip>77:1:8::1/64</ip>
          </address>
          <enabled>true</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <vlan-id>908</vlan-id>
        <link-interface>TenGe0/0</link-interface>
        <protocol>802.1q</protocol>
        <ha-group>0</ha-group>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <isp-config xmlns="urn:ruijie:ntos:params:xml:ns:yang:isp">
          <isp-route>
            <name>CERNET</name>
          </isp-route>
        </isp-config>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>TenGe0/0.909</name>
        <mtu>1500</mtu>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <address>
            <ip>********/24</ip>
            <nexthop>********54</nexthop>
          </address>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <address>
            <ip>77:9::1/64</ip>
          </address>
          <address>
            <ip>77:1:9::1/64</ip>
          </address>
          <enabled>true</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <vlan-id>909</vlan-id>
        <link-interface>TenGe0/0</link-interface>
        <protocol>802.1q</protocol>
        <ha-group>0</ha-group>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <isp-config xmlns="urn:ruijie:ntos:params:xml:ns:yang:isp">
          <isp-route>
            <name>CMCC</name>
          </isp-route>
        </isp-config>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>TenGe0/0.910</name>
        <mtu>1500</mtu>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <address>
            <ip>*********/24</ip>
            <nexthop>*********54</nexthop>
          </address>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <address>
            <ip>77:10::1/64</ip>
          </address>
          <address>
            <ip>77:1:10::1/64</ip>
          </address>
          <enabled>true</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <vlan-id>910</vlan-id>
        <link-interface>TenGe0/0</link-interface>
        <protocol>802.1q</protocol>
        <ha-group>0</ha-group>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <isp-config xmlns="urn:ruijie:ntos:params:xml:ns:yang:isp">
          <isp-route>
            <name>BEIJINGTELETRON</name>
          </isp-route>
        </isp-config>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/0.71</name>
        <mtu>1500</mtu>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <address>
            <ip>**********/24</ip>
            <nexthop>************</nexthop>
          </address>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <vlan-id>71</vlan-id>
        <link-interface>Ge0/0</link-interface>
        <protocol>802.1q</protocol>
        <ha-group>0</ha-group>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/1.77</name>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <address>
            <ip>**********/24</ip>
          </address>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <vlan-id>77</vlan-id>
        <link-interface>Ge0/1</link-interface>
        <protocol>802.1q</protocol>
        <ha-group>0</ha-group>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
      </vlan>
      <vti xmlns="urn:ruijie:ntos:params:xml:ns:yang:vti">
        <name>vti31</name>
        <description>by tunnel wizard branch3-core1-TenGe0_0.901</description>
        <enabled>false</enabled>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <reverse-path>true</reverse-path>
        <local>*******</local>
        <is-template>true</is-template>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </vti>
      <vti xmlns="urn:ruijie:ntos:params:xml:ns:yang:vti">
        <name>vti32</name>
        <description>by tunnel wizard branch3-core1-TenGe0_0.902</description>
        <enabled>false</enabled>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <reverse-path>true</reverse-path>
        <local>*******</local>
        <is-template>true</is-template>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </vti>
      <vti xmlns="urn:ruijie:ntos:params:xml:ns:yang:vti">
        <name>vti33</name>
        <description>by tunnel wizard branch3-core1-TenGe0_0.903</description>
        <enabled>false</enabled>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <reverse-path>true</reverse-path>
        <local>*******</local>
        <is-template>true</is-template>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </vti>
      <vti xmlns="urn:ruijie:ntos:params:xml:ns:yang:vti">
        <name>vti34</name>
        <description>by tunnel wizard branch3-core1-TenGe0_0.904</description>
        <enabled>false</enabled>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <reverse-path>true</reverse-path>
        <local>*******</local>
        <is-template>true</is-template>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </vti>
    <physical><name>Ge0/0</name><mtu>1500</mtu><promiscuous>false</promiscuous><description></description><enabled>true</enabled><wanlan>lan</wanlan><working-mode>route</working-mode><ns3:access-control xmlns:ns3="urn:ruijie:ntos:params:xml:ns:yang:local-defend"><https>true</https><ping>true</ping><ssh>true</ssh></ns3:access-control><ipv4><enabled>true</enabled><address><ip>*******/32</ip><nexthop>0.0.0.0</nexthop></address></ipv4></physical><physical><name>Ge0/1</name><mtu>1500</mtu><promiscuous>false</promiscuous><description></description><enabled>true</enabled><wanlan>lan</wanlan><working-mode>route</working-mode><ns4:access-control xmlns:ns4="urn:ruijie:ntos:params:xml:ns:yang:local-defend"><https>true</https><ping>true</ping><ssh>true</ssh></ns4:access-control><ipv4><enabled>true</enabled><address><ip>*************/24</ip><nexthop>0.0.0.0</nexthop></address></ipv4></physical><physical><name>Ge0/2</name><mtu>1500</mtu><promiscuous>false</promiscuous><description></description><enabled>true</enabled><wanlan>lan</wanlan><working-mode>route</working-mode><ns5:access-control xmlns:ns5="urn:ruijie:ntos:params:xml:ns:yang:local-defend"><https>true</https><ping>true</ping><ssh>true</ssh></ns5:access-control><ipv4><enabled>true</enabled><address><ip>*******/24</ip><nexthop>0.0.0.0</nexthop></address></ipv4></physical></interface>
    <ips-config xmlns="urn:ruijie:ntos:params:xml:ns:yang:intrusion-prevention">
    <template><name>default</name><description>Prevent critical attacks."</description><filter><name>test-filter</name><target>both</target><severity>informational</severity><severity>low</severity><severity>medium</severity><severity>high</severity><protocol><all-protocol>true</all-protocol></protocol><category><all-category>true</all-category></category></filter></template><template><name>sniffer-profile</name><description>Monitor IPS attacks."</description><filter><name>test-filter</name><target>both</target><severity>low</severity><severity>medium</severity><severity>high</severity><protocol><all-protocol>true</all-protocol></protocol><category><all-category>true</all-category></category></filter></template><template><name>wifi-default</name><description>Default configuration for offloading WiFi traffic."</description><filter><name>test-filter</name><target>both</target><severity>low</severity><severity>medium</severity><severity>high</severity><protocol><all-protocol>true</all-protocol></protocol><category><all-category>true</all-category></category></filter></template><template><name>all_default</name><description>All predefined signatures with default setting."</description><filter><name>test-filter</name><target>both</target><protocol><all-protocol>true</all-protocol></protocol><category><all-category>true</all-category></category></filter></template><template><name>all_default_pass</name><description>All predefined signatures with PASS action."</description><filter><name>test-filter</name><target>both</target><protocol><all-protocol>true</all-protocol></protocol><category><all-category>true</all-category></category></filter></template><template><name>protect_http_server</name><description>Protect against HTTP server-side vulnerabilities."</description><filter><name>test-filter</name><target>both</target><protocol><all-protocol>true</all-protocol></protocol><category><all-category>true</all-category></category></filter></template><template><name>protect_email_server</name><description>Protect against email server-side vulnerabilities."</description><filter><name>test-filter</name><target>both</target><protocol><all-protocol>true</all-protocol></protocol><category><all-category>true</all-category></category></filter></template><template><name>protect_client</name><description>Protect against client-side vulnerabilities."</description><filter><name>test-filter</name><target>both</target><protocol><all-protocol>true</all-protocol></protocol><category><all-category>true</all-category></category></filter></template><template><name>high_security</name><description>Blocks all Critical/High/Medium and some Low severity vulnerabilities"</description><filter><name>test-filter</name><target>both</target><severity>low</severity><severity>medium</severity><severity>high</severity><severity>informational</severity><protocol><all-protocol>true</all-protocol></protocol><category><all-category>true</all-category></category></filter></template></ips-config>
    <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
      <ipv4>
        <enabled>false</enabled>
        <no-match-action-drop>false</no-match-action-drop>
      </ipv4>
      <ipv6>
        <enabled>false</enabled>
        <no-match-action-drop>false</no-match-action-drop>
      </ipv6>
    </ip-mac-bind>
    <track xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-track">
      <enabled>true</enabled>
      <rule>
        <name>track-if-001</name>
        <enabled>true</enabled>
        <delay>
          <up>0</up>
          <down>0</down>
        </delay>
        <least-activenum>1</least-activenum>
        <track-threshold>1</track-threshold>
        <config-source>TRACK</config-source>
        <state-detection>LEAST-ACTIVENUM</state-detection>
        <type>interface</type>
        <thread-weight>255</thread-weight>
        <track-interface>
          <ifname>Ge0/1</ifname>
        </track-interface>
      </rule>
      <rule>
        <name>track-if-002</name>
        <enabled>true</enabled>
        <delay>
          <up>0</up>
          <down>0</down>
        </delay>
        <least-activenum>1</least-activenum>
        <track-threshold>1</track-threshold>
        <config-source>TRACK</config-source>
        <state-detection>LEAST-ACTIVENUM</state-detection>
        <type>interface</type>
        <thread-weight>255</thread-weight>
        <track-interface>
          <ifname>Ge0/2</ifname>
        </track-interface>
      </rule>
      <rule>
        <name>track-if-003</name>
        <enabled>true</enabled>
        <delay>
          <up>0</up>
          <down>0</down>
        </delay>
        <least-activenum>1</least-activenum>
        <track-threshold>1</track-threshold>
        <config-source>TRACK</config-source>
        <state-detection>LEAST-ACTIVENUM</state-detection>
        <type>interface</type>
        <thread-weight>255</thread-weight>
        <track-interface>
          <ifname>Ge0/3</ifname>
        </track-interface>
      </rule>
      <rule>
        <name>HA0-TrackIF</name>
        <enabled>true</enabled>
        <delay>
          <up>0</up>
          <down>0</down>
        </delay>
        <least-activenum>1</least-activenum>
        <track-threshold>1</track-threshold>
        <config-source>TRACK</config-source>
        <state-detection>LEAST-ACTIVENUM</state-detection>
        <type>interface</type>
        <thread-weight>255</thread-weight>
        <track-interface>
          <ifname>Ge0/0</ifname>
        </track-interface>
        <track-interface>
          <ifname>Ge0/3</ifname>
        </track-interface>
      </rule>
      <rule>
        <name>HA0-TrackIP</name>
        <enabled>true</enabled>
        <delay>
          <up>0</up>
          <down>0</down>
        </delay>
        <least-activenum>1</least-activenum>
        <track-threshold>100</track-threshold>
        <config-source>HA</config-source>
        <state-detection>WEIGHT-THRESHOLD</state-detection>
        <type>rns</type>
        <thread-weight>255</thread-weight>
      </rule>
    </track>
    <ipsec xmlns="urn:ruijie:ntos:params:xml:ns:yang:ipsec">
      <profile>
        <name>branch3-core1-TenGe0_0.902</name>
        <enabled>true</enabled>
        <description>by tunnel wizard branch3-core1-TenGe0_0.902</description>
        <create-time>1723447942</create-time>
        <version>ikev1 ikev2</version>
        <exchange-mode>main</exchange-mode>
        <autoup>true</autoup>
        <local>
          <interface>
            <interface>TenGe0/0.902</interface>
          </interface>
        </local>
        <proxyid>branch3-core1-TenGe0_0.902</proxyid>
        <ike-profile>branch3-core1-TenGe0_0.902</ike-profile>
        <ike-proposal>branch3-core1-TenGe0_0.902</ike-proposal>
        <ipsec-proposal>branch3-core1-TenGe0_0.902</ipsec-proposal>
        <life-seconds>3600</life-seconds>
        <reverse-route>
          <enabled>false</enabled>
          <distance>5</distance>
        </reverse-route>
        <fragmentation-mtu>1400</fragmentation-mtu>
        <peer-address>
          <peer-address>********</peer-address>
        </peer-address>
        <type>static</type>
        <local-identity>
          <ipv4-address/>
        </local-identity>
        <check-id>false</check-id>
        <dpd>
          <interval>30</interval>
          <retry-interval>5</retry-interval>
          <type>periodic</type>
        </dpd>
        <tunnel-interface>vti32</tunnel-interface>
        <is-template>true</is-template>
      </profile>
      <profile>
        <name>branch3-core1-TenGe0_0.903</name>
        <enabled>true</enabled>
        <description>by tunnel wizard branch3-core1-TenGe0_0.903</description>
        <create-time>1723448039</create-time>
        <version>ikev1 ikev2</version>
        <exchange-mode>main</exchange-mode>
        <autoup>true</autoup>
        <local>
          <interface>
            <interface>TenGe0/0.903</interface>
          </interface>
        </local>
        <proxyid>branch3-core1-TenGe0_0.903</proxyid>
        <ike-profile>branch3-core1-TenGe0_0.903</ike-profile>
        <ike-proposal>branch3-core1-TenGe0_0.903</ike-proposal>
        <ipsec-proposal>branch3-core1-TenGe0_0.903</ipsec-proposal>
        <life-seconds>3600</life-seconds>
        <reverse-route>
          <enabled>false</enabled>
          <distance>5</distance>
        </reverse-route>
        <fragmentation-mtu>1400</fragmentation-mtu>
        <peer-address>
          <peer-address>********</peer-address>
        </peer-address>
        <type>static</type>
        <local-identity>
          <ipv4-address/>
        </local-identity>
        <check-id>false</check-id>
        <dpd>
          <interval>30</interval>
          <retry-interval>5</retry-interval>
          <type>periodic</type>
        </dpd>
        <tunnel-interface>vti33</tunnel-interface>
        <is-template>true</is-template>
      </profile>
      <profile>
        <name>branch3-core1-TenGe0_0.904</name>
        <enabled>true</enabled>
        <description>by tunnel wizard branch3-core1-TenGe0_0.904</description>
        <create-time>1723617589</create-time>
        <version>ikev1 ikev2</version>
        <exchange-mode>main</exchange-mode>
        <autoup>true</autoup>
        <local>
          <interface>
            <interface>TenGe0/0.904</interface>
          </interface>
        </local>
        <proxyid>branch3-core1-TenGe0_0.904</proxyid>
        <ike-profile>branch3-core1-TenGe0_0.904</ike-profile>
        <ike-proposal>branch3-core1-TenGe0_0.904</ike-proposal>
        <ipsec-proposal>branch3-core1-TenGe0_0.904</ipsec-proposal>
        <life-seconds>3600</life-seconds>
        <reverse-route>
          <enabled>false</enabled>
          <distance>5</distance>
        </reverse-route>
        <fragmentation-mtu>1400</fragmentation-mtu>
        <peer-address>
          <peer-address>********</peer-address>
        </peer-address>
        <type>static</type>
        <local-identity>
          <ipv4-address/>
        </local-identity>
        <check-id>false</check-id>
        <dpd>
          <interval>30</interval>
          <retry-interval>5</retry-interval>
          <type>periodic</type>
        </dpd>
        <tunnel-interface>vti34</tunnel-interface>
        <is-template>true</is-template>
      </profile>
      <profile>
        <name>branch3-core1-TenGe0_0.901</name>
        <enabled>true</enabled>
        <description>by tunnel wizard branch3-core1-TenGe0_0.901</description>
        <create-time>1712053993</create-time>
        <version>ikev1</version>
        <exchange-mode>main</exchange-mode>
        <autoup>true</autoup>
        <local>
          <interface>
            <interface>TenGe0/0.901</interface>
          </interface>
        </local>
        <proxyid>branch3-core1-TenGe0_0.901</proxyid>
        <ike-profile>branch3-core1-TenGe0_0.901</ike-profile>
        <ike-proposal>branch3-core1-TenGe0_0.901</ike-proposal>
        <ipsec-proposal>branch3-core1-TenGe0_0.901</ipsec-proposal>
        <life-seconds>3600</life-seconds>
        <reverse-route>
          <enabled>false</enabled>
          <distance>5</distance>
        </reverse-route>
        <fragmentation-mtu>1400</fragmentation-mtu>
        <peer-address>
          <peer-address>********</peer-address>
        </peer-address>
        <type>static</type>
        <local-identity>
          <ipv4-address/>
        </local-identity>
        <check-id>false</check-id>
        <dpd>
          <interval>30</interval>
          <retry-interval>5</retry-interval>
          <type>idle</type>
        </dpd>
        <tunnel-interface>vti31</tunnel-interface>
        <is-template>true</is-template>
      </profile>
      <proposal>
        <name>branch3-core1-TenGe0_0.902</name>
        <protocol>esp</protocol>
        <encap-mode>tunnel</encap-mode>
        <esn>true</esn>
        <esp-encrypt-alg>aes-128</esp-encrypt-alg>
        <esp-auth-alg>sha</esp-auth-alg>
      </proposal>
      <proposal>
        <name>branch3-core1-TenGe0_0.903</name>
        <protocol>esp</protocol>
        <encap-mode>tunnel</encap-mode>
        <esn>true</esn>
        <esp-encrypt-alg>aes-128</esp-encrypt-alg>
        <esp-auth-alg>sha</esp-auth-alg>
      </proposal>
      <proposal>
        <name>branch3-core1-TenGe0_0.904</name>
        <protocol>esp</protocol>
        <encap-mode>tunnel</encap-mode>
        <esn>true</esn>
        <esp-encrypt-alg>aes-128</esp-encrypt-alg>
        <esp-auth-alg>sha</esp-auth-alg>
      </proposal>
      <proposal>
        <name>branch3-core1-TenGe0_0.901</name>
        <protocol>esp</protocol>
        <encap-mode>tunnel</encap-mode>
        <esn>true</esn>
        <esp-encrypt-alg>aes-128</esp-encrypt-alg>
        <esp-auth-alg>sha</esp-auth-alg>
      </proposal>
      <proxyid>
        <proxyid-name>branch3-core1-TenGe0_0.902</proxyid-name>
        <ip>
          <local>********/24</local>
          <remote>********/24</remote>
        </ip>
        <ip>
          <local>********/24</local>
          <remote>********/24</remote>
        </ip>
        <ip>
          <local>********/24</local>
          <remote>********/24</remote>
        </ip>
        <ip>
          <local>********/24</local>
          <remote>********/24</remote>
        </ip>
      </proxyid>
      <proxyid>
        <proxyid-name>branch3-core1-TenGe0_0.903</proxyid-name>
        <ip>
          <local>********/24</local>
          <remote>********/24</remote>
        </ip>
        <ip>
          <local>********/24</local>
          <remote>********/24</remote>
        </ip>
        <ip>
          <local>********/24</local>
          <remote>********/24</remote>
        </ip>
        <ip>
          <local>********/24</local>
          <remote>********/24</remote>
        </ip>
      </proxyid>
      <proxyid>
        <proxyid-name>branch3-core1-TenGe0_0.904</proxyid-name>
        <ip>
          <local>********/24</local>
          <remote>********/24</remote>
        </ip>
        <ip>
          <local>********/24</local>
          <remote>********/24</remote>
        </ip>
        <ip>
          <local>********/24</local>
          <remote>********/24</remote>
        </ip>
        <ip>
          <local>********/24</local>
          <remote>********/24</remote>
        </ip>
      </proxyid>
      <proxyid>
        <proxyid-name>branch3-core1-TenGe0_0.901</proxyid-name>
        <ip>
          <local>********/24</local>
          <remote>********/24</remote>
        </ip>
        <ip>
          <local>********/24</local>
          <remote>********/24</remote>
        </ip>
        <ip>
          <local>********/24</local>
          <remote>********/24</remote>
        </ip>
        <ip>
          <local>********/24</local>
          <remote>********/24</remote>
        </ip>
      </proxyid>
      <anti-replay>
        <check>true</check>
        <window-size>64</window-size>
      </anti-replay>
      <df-bit>clear</df-bit>
      <prefrag>true</prefrag>
      <inbound-sp>
        <check>true</check>
      </inbound-sp>
      <spd-hash-bits>
        <src-bits>16</src-bits>
        <dst-bits>16</dst-bits>
      </spd-hash-bits>
      <hardware-crypto-offload>true</hardware-crypto-offload>
    </ipsec>
    <isp xmlns="urn:ruijie:ntos:params:xml:ns:yang:isp">
      <distance>10</distance>
    </isp>
    <local-defend xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
      <policy>
        <name>deny_all</name>
        <enabled>true</enabled>
        <action>deny</action>
        <limit>false</limit>
        <pps>600</pps>
        <description/>
      </policy>
      <policy>
        <name>limit_local</name>
        <enabled>true</enabled>
        <action>permit</action>
        <limit>true</limit>
        <pps>1500</pps>
        <description/>
      </policy>
    </local-defend>
    <logging xmlns="urn:ruijie:ntos:params:xml:ns:yang:logging">
      <syslog>
        <enabled>true</enabled>
        <fp-syslog>
          <enabled>true</enabled>
        </fp-syslog>
        <remote-server>
          <host>***********</host>
          <protocol>udp</protocol>
          <port>514</port>
          <syslog-protocol>rfc5424</syslog-protocol>
          <log-timestamp>utc</log-timestamp>
          <log-filter>
            <facility>local7</facility>
            <level>
              <equal>warning</equal>
            </level>
          </log-filter>
          <log-type-filter>
            <log-type>flow</log-type>
          </log-type-filter>
          <log-type-filter>
            <log-type>securityLog</log-type>
          </log-type-filter>
          <log-type-filter>
            <log-type>urlFilterLog</log-type>
          </log-type-filter>
          <log-type-filter>
            <log-type>caUrlLog</log-type>
          </log-type-filter>
          <log-type-filter>
            <log-type>caWebSearchLog</log-type>
          </log-type-filter>
          <log-type-filter>
            <log-type>caImLog</log-type>
          </log-type-filter>
          <log-type-filter>
            <log-type>caMailLog</log-type>
          </log-type-filter>
          <log-type-filter>
            <log-type>caBbsMblogLog</log-type>
          </log-type-filter>
          <log-type-filter>
            <log-type>caFileLog</log-type>
          </log-type-filter>
          <log-type-filter>
            <log-type>systemLog</log-type>
          </log-type-filter>
          <log-charset>utf-8</log-charset>
        </remote-server>
      </syslog>
    </logging>
    <misn xmlns="urn:ruijie:ntos:params:xml:ns:yang:misn">
      <enabled>true</enabled>
    </misn>
    <mllb xmlns="urn:ruijie:ntos:params:xml:ns:yang:mllb">
      <arithmetic>
        <src-dst-ip-hash/>
      </arithmetic>
      <wan-interface>
        <name>TenGe0/0.910</name>
        <enabled>true</enabled>
        <weight>1000000</weight>
        <priority>1</priority>
        <max-conn>2000</max-conn>
        <conn-overload-threshold>90</conn-overload-threshold>
        <conn-recover-threshold>80</conn-recover-threshold>
        <upload-bandwidth>
          <upload-bandwidth-unit>mbps</upload-bandwidth-unit>
          <upload-bandwidth-threshold>80</upload-bandwidth-threshold>
        </upload-bandwidth>
        <download-bandwidth>
          <download-bandwidth-unit>mbps</download-bandwidth-unit>
          <download-bandwidth-threshold>80</download-bandwidth-threshold>
        </download-bandwidth>
      </wan-interface>
      <advanced-options>
        <refresh-session>false</refresh-session>
        <cache-timeout>300</cache-timeout>
        <cache-once>256</cache-once>
        <cache-disable>false</cache-disable>
        <alarm-threshold>90</alarm-threshold>
      </advanced-options>
      <all-if-switch>false</all-if-switch>
    </mllb>
    <port-mapping xmlns="urn:ruijie:ntos:params:xml:ns:yang:nat">
      <enabled>true</enabled>
    </port-mapping>
    <nat xmlns="urn:ruijie:ntos:params:xml:ns:yang:nat">
      <alg>ftp sip-tcp sip-udp tftp dns-udp</alg>
      <sip-port-check>
        <enabled>true</enabled>
      </sip-port-check>
    <enabled>true</enabled><pool><name>jok234</name><address>100.0.0.0-*********</address></pool><enabled>true</enabled><pool><name>kkey</name><address>*************-**************</address></pool><enabled>true</enabled><pool><name>nat-pool</name><address>************-*************</address></pool><rule><name>vpn_To_71_70_Master_local</name><desc>VPN: To_71_70_Master (Created by VPN wizard)</desc><rule_en>true</rule_en><static-snat44><match><source-network><name>To_71_70_Master_local</name></source-network><dest-network><name>To_71_70_Master_remote</name></dest-network><service><name>ALL</name></service></match><translate-to><output-address/><no-pat>false</no-pat></translate-to></static-snat44></rule><rule><name>vpn_To_71_70_Master_remote</name><desc>VPN: To_71_70_Master (Created by VPN wizard)</desc><rule_en>true</rule_en><static-snat44><match><source-network><name>To_71_70_Master_remote</name></source-network><dest-network><name>To_71_70_Master_local</name></dest-network><service><name>ALL</name></service></match><translate-to><output-address/><no-pat>false</no-pat></translate-to></static-snat44></rule><rule><name>vpn_To_72_70_Backup_local</name><desc>VPN: To_72_70_Backup (Created by VPN wizard)</desc><rule_en>true</rule_en><static-snat44><match><source-network><name>To_72_70_Backup_local</name></source-network><dest-network><name>To_72_70_Backup_remote</name></dest-network><service><name>ALL</name></service></match><translate-to><output-address/><no-pat>false</no-pat></translate-to></static-snat44></rule><rule><name>vpn_To_72_70_Backup_remote</name><desc>VPN: To_72_70_Backup (Created by VPN wizard)</desc><rule_en>true</rule_en><static-snat44><match><source-network><name>To_72_70_Backup_remote</name></source-network><dest-network><name>To_72_70_Backup_local</name></dest-network><service><name>ALL</name></service></match><translate-to><output-address/><no-pat>false</no-pat></translate-to></static-snat44></rule><rule><name>vpn_test1_local</name><desc>VPN: test1 (Created by VPN wizard)</desc><rule_en>true</rule_en><static-snat44><match><source-network><name>test1_local</name></source-network><dest-network><name>test1_remote</name></dest-network><service><name>ALL</name></service></match><translate-to><output-address/><no-pat>false</no-pat></translate-to></static-snat44></rule><rule><name>vpn_test1_remote</name><desc>VPN: test1 (Created by VPN wizard)</desc><rule_en>true</rule_en><static-snat44><match><source-network><name>test1_remote</name></source-network><dest-network><name>test1_local</name></dest-network><service><name>ALL</name></service></match><translate-to><output-address/><no-pat>false</no-pat></translate-to></static-snat44></rule><rule><name>vpn_to-HW_local</name><desc>VPN: to-HW (Created by VPN wizard)</desc><rule_en>true</rule_en><static-snat44><match><source-network><name>to-HW_local</name></source-network><dest-network><name>to-HW_remote</name></dest-network><service><name>ALL</name></service></match><translate-to><output-address/><no-pat>false</no-pat></translate-to></static-snat44></rule><rule><name>vpn_to-HW_remote</name><desc>VPN: to-HW (Created by VPN wizard)</desc><rule_en>true</rule_en><static-snat44><match><source-network><name>to-HW_remote</name></source-network><dest-network><name>to-HW_local</name></dest-network><service><name>ALL</name></service></match><translate-to><output-address/><no-pat>false</no-pat></translate-to></static-snat44></rule><rule><name>vpn_tst_local</name><desc>VPN: tst (Created by VPN wizard)</desc><rule_en>true</rule_en><static-snat44><match><source-network><name>tst_local</name></source-network><dest-network><name>tst_remote</name></dest-network><service><name>ALL</name></service></match><translate-to><output-address/><no-pat>false</no-pat></translate-to></static-snat44></rule><rule><name>vpn_tst_remote</name><desc>VPN: tst (Created by VPN wizard)</desc><rule_en>true</rule_en><static-snat44><match><source-network><name>tst_remote</name></source-network><dest-network><name>tst_local</name></dest-network><service><name>ALL</name></service></match><translate-to><output-address/><no-pat>false</no-pat></translate-to></static-snat44></rule><rule><name>GBB</name><desc></desc><rule_en>true</rule_en><static-snat44><match><source-network><name>111_local_subnet_1</name></source-network><dest-network><name>Site_to_Site_remote_subnet_1</name></dest-network><service><name>ALL_ICMP6</name></service></match><translate-to><output-address/><no-pat>false</no-pat></translate-to></static-snat44></rule><rule><name>nat_rule</name><desc>test-123123</desc><rule_en>false</rule_en><static-snat44><match><source-network><name>nat_sip</name></source-network><service><name>ALL</name></service></match><translate-to><output-address/><no-pat>false</no-pat></translate-to></static-snat44></rule><rule><name>dnat_rule</name><desc>dnat-webserver</desc><rule_en>true</rule_en><static-dnat44><match><source-network><name>all</name></source-network><dest-network><name>webserver</name></dest-network><service><name>HTTP</name></service></match><translate-to><output-address/><no-pat>false</no-pat></translate-to></static-dnat44></rule></nat>
    <netconf-server xmlns="urn:ruijie:ntos:params:xml:ns:yang:netconf-server">
      <enabled>true</enabled>
      <idle-timeout>3600</idle-timeout>
    </netconf-server>
    <network-device xmlns="urn:ruijie:ntos:params:xml:ns:yang:network-device">
      <mac-snooping>
        <enabled>true</enabled>
      </mac-snooping>
      <location-snooping>
        <enabled>false</enabled>
      </location-snooping>
      <frequency>600</frequency>
      <server>
        <host>************</host>
        <enabled>true</enabled>
        <device-name>锐捷交换机68_LAN-76-01</device-name>
        <device-type>L3</device-type>
        <manufacturer>RUIJIE</manufacturer>
        <static-info>
          <location/>
        </static-info>
        <snmp-snooping-params>
          <access-params>
            <version>v2c</version>
            <community>=*-#!$_2g+awtqJWZWf+C6zl4RLQ==</community>
            <protocol>udp</protocol>
            <port>161</port>
            <auth-method>sha</auth-method>
            <priv-protocol>aes</priv-protocol>
          </access-params>
        </snmp-snooping-params>
      </server>
      <server>
        <host>************</host>
        <enabled>true</enabled>
        <device-name>锐捷交换机57_LAN-71-01</device-name>
        <device-type>L3</device-type>
        <manufacturer>RUIJIE</manufacturer>
        <static-info>
          <location/>
        </static-info>
        <snmp-snooping-params>
          <access-params>
            <version>v2c</version>
            <community>=*-#!$eMz1ny9gpH8=</community>
            <protocol>udp</protocol>
            <port>161</port>
            <auth-method>sha</auth-method>
            <priv-protocol>aes</priv-protocol>
          </access-params>
        </snmp-snooping-params>
      </server>
    </network-device>
    <network-measure xmlns="urn:ruijie:ntos:params:xml:ns:yang:network-measure">
      <enabled>true</enabled>
      <message-send-enabled>false</message-send-enabled>
      <service>
        <dhcp>
          <enabled>true</enabled>
        </dhcp>
        <dns>
          <enabled>true</enabled>
          <monitor-threshold>1000</monitor-threshold>
        </dns>
        <nat>
          <enabled>true</enabled>
        </nat>
        <ipsec>
          <enabled>true</enabled>
        </ipsec>
        <sslvpn>
          <enabled>true</enabled>
        </sslvpn>
      </service>
      <warning-config>
        <link>
          <high-load>
            <enabled>true</enabled>
            <duration>120</duration>
            <threshold>95</threshold>
          </high-load>
          <suspect-disconnected>
            <enabled>true</enabled>
            <duration>300</duration>
            <threshold>10000</threshold>
          </suspect-disconnected>
          <change-to-down>
            <enabled>true</enabled>
          </change-to-down>
        </link>
        <app>
          <change-to-poor>
            <enabled>true</enabled>
          </change-to-poor>
        </app>
        <user>
          <wan-detect-failed>
            <enabled>true</enabled>
          </wan-detect-failed>
          <lan-detect-failed>
            <enabled>true</enabled>
          </lan-detect-failed>
          <change-to-poor>
            <enabled>true</enabled>
          </change-to-poor>
        </user>
        <dhcp-server>
          <ip-conflict>
            <enabled>true</enabled>
          </ip-conflict>
          <high-load>
            <enabled>true</enabled>
            <threshold>95</threshold>
          </high-load>
        </dhcp-server>
        <dns>
          <unuseable>
            <enabled>true</enabled>
          </unuseable>
        </dns>
        <nat>
          <hit-fail>
            <enabled>false</enabled>
          </hit-fail>
          <hit-miss>
            <enabled>false</enabled>
          </hit-miss>
        </nat>
        <ipsec>
          <disconnected>
            <enabled>true</enabled>
            <duration>120</duration>
          </disconnected>
        </ipsec>
        <sslvpn>
          <lost>
            <enabled>true</enabled>
            <threshold>10</threshold>
          </lost>
          <license>
            <enabled>true</enabled>
            <threshold>20</threshold>
          </license>
        </sslvpn>
      </warning-config>
      <flow-limit>
        <enabled>true</enabled>
        <up>80</up>
        <down>80</down>
      </flow-limit>
    </network-measure>
    <network-obj xmlns="urn:ruijie:ntos:params:xml:ns:yang:network-obj">
    <address-set><name>SSLVPN_TUNNEL_ADDR1</name><ip-set><ip-address>**************-**************</ip-address></ip-set></address-set><address-group><name>111_local</name><address-set><name>111_local_subnet_1</name></address-set></address-group><address-group><name>111_remote</name><address-set><name>111_remote_subnet_1</name></address-set></address-group><address-group><name>To_71_70_Master_local</name><address-set><name>To_71_70_Master_local_subnet_1</name></address-set></address-group><address-group><name>To_71_70_Master_remote</name><address-set><name>To_71_70_Master_remote_subnet_1</name></address-set></address-group><address-group><name>To_72_70_Backup_local</name><address-set><name>To_72_70_Backup_local_subnet_1</name></address-set></address-group><address-group><name>To_72_70_Backup_remote</name><address-set><name>To_72_70_Backup_remote_subnet_1</name></address-set></address-group><address-group><name>to42_local</name><address-set><name>to42_local_subnet_1</name></address-set></address-group><address-group><name>to42_remote</name><address-set><name>to42_remote_subnet_1</name></address-set></address-group><address-group><name>to37_local</name><address-set><name>to37_local_subnet_1</name></address-set></address-group><address-group><name>to37_remote</name><address-set><name>to37_remote_subnet_1</name></address-set></address-group><address-group><name>new42_local</name><address-set><name>new42_local_subnet_1</name></address-set></address-group><address-group><name>new42_remote</name><address-set><name>new42_remote_subnet_1</name></address-set></address-group><address-group><name>tu1_local</name><address-set><name>tu1_local_subnet_1</name></address-set></address-group><address-group><name>tu1_remote</name><address-set><name>tu1_remote_subnet_1</name></address-set></address-group><address-group><name>Site_to_Site_local</name><address-set><name>Site_to_Site_local_subnet_1</name></address-set></address-group><address-group><name>Site_to_Site_remote</name><address-set><name>Site_to_Site_remote_subnet_1</name></address-set></address-group><address-group><name>test1_local</name><address-set><name>test1_local_subnet_1</name></address-set></address-group><address-group><name>test1_remote</name><address-set><name>test1_remote_subnet_1</name></address-set></address-group><address-group><name>to-HW_local</name><address-set><name>to-HW_local_subnet_1</name></address-set></address-group><address-group><name>to-HW_remote</name><address-set><name>to-HW_remote_subnet_1</name></address-set></address-group><address-group><name>tst_local</name><address-set><name>tst_local_subnet_1</name></address-set></address-group><address-group><name>tst_remote</name><address-set><name>tst_remote_subnet_1</name></address-set></address-group></network-obj>
    <nfp xmlns="urn:ruijie:ntos:params:xml:ns:yang:nfp">
      <session>
        <state-inspection>
          <tcp>true</tcp>
          <icmp>true</icmp>
        </state-inspection>
      </session>
      <tunnel-inspection>
        <bridge>
          <VLAN>true</VLAN>
          <PPPoE>false</PPPoE>
          <GRE>false</GRE>
          <L2TPv2>false</L2TPv2>
        </bridge>
      </tunnel-inspection>
    </nfp>
    <ntp xmlns="urn:ruijie:ntos:params:xml:ns:yang:ntp">
      <enabled>true</enabled>
      <server>
        <address>ntp.ntsc.ac.cn</address>
        <version>4</version>
        <association-type>SERVER</association-type>
        <iburst>false</iburst>
        <prefer>false</prefer>
      </server>
      <server>
        <address>ntp1.aliyun.com</address>
        <version>4</version>
        <association-type>SERVER</association-type>
        <iburst>false</iburst>
        <prefer>false</prefer>
      </server>
    </ntp>
    <pbr xmlns="urn:ruijie:ntos:params:xml:ns:yang:pbr">
      <policy>
        <name>G20_路由</name>
        <group-name>def-group</group-name>
        <enabled>true</enabled>
        <type>multi</type>
        <dest-network>
          <name>10.1_and_10.2</name>
        </dest-network>
        <time-range>any</time-range>
        <action>permit</action>
        <out-if>
          <name>TenGe0/0.905</name>
          <weight>1000000</weight>
          <upload-bandwidth-threshold>80</upload-bandwidth-threshold>
          <download-bandwidth-threshold>80</download-bandwidth-threshold>
          <priority>1</priority>
          <max-conn>10000</max-conn>
          <next-hop>********</next-hop>
          <is-master>false</is-master>
          <intf-type>interface</intf-type>
        </out-if>
        <out-if>
          <name>TenGe0/0.906</name>
          <weight>1000000</weight>
          <upload-bandwidth-threshold>80</upload-bandwidth-threshold>
          <download-bandwidth-threshold>80</download-bandwidth-threshold>
          <priority>1</priority>
          <max-conn>10000</max-conn>
          <next-hop>********</next-hop>
          <is-master>false</is-master>
          <intf-type>interface</intf-type>
        </out-if>
        <out-if>
          <name>TenGe0/0.907</name>
          <weight>1000000</weight>
          <upload-bandwidth-threshold>80</upload-bandwidth-threshold>
          <download-bandwidth-threshold>80</download-bandwidth-threshold>
          <priority>1</priority>
          <max-conn>10000</max-conn>
          <next-hop>77.7.0.2</next-hop>
          <is-master>false</is-master>
          <intf-type>interface</intf-type>
        </out-if>
        <out-if>
          <name>TenGe0/0.908</name>
          <weight>1000000</weight>
          <upload-bandwidth-threshold>80</upload-bandwidth-threshold>
          <download-bandwidth-threshold>80</download-bandwidth-threshold>
          <priority>1</priority>
          <max-conn>10000</max-conn>
          <next-hop>********</next-hop>
          <is-master>false</is-master>
          <intf-type>interface</intf-type>
        </out-if>
        <next-hop-list>
          <name>********</name>
        </next-hop-list>
        <mode>12</mode>
        <bandwidth-type>down-bandwidth</bandwidth-type>
        <config-source>manual</config-source>
        <sla-type>auto</sla-type>
        <sla-template>predefine</sla-template>
      </policy>
      <policy>
        <name>IPv6</name>
        <group-name>def-group</group-name>
        <enabled>true</enabled>
        <type>multi</type>
        <source-zone>
          <name>branch3net1v68</name>
        </source-zone>
        <dest-network>
          <name>10：1</name>
        </dest-network>
        <time-range>any</time-range>
        <action>permit</action>
        <out-if>
          <name>TenGe0/0.901</name>
          <weight>1000000</weight>
          <upload-bandwidth-threshold>80</upload-bandwidth-threshold>
          <download-bandwidth-threshold>80</download-bandwidth-threshold>
          <priority>1</priority>
          <max-conn>10000</max-conn>
          <next-hop6>77:1::2</next-hop6>
          <is-master>false</is-master>
          <intf-type>interface</intf-type>
        </out-if>
        <out-if>
          <name>TenGe0/0.902</name>
          <weight>1000000</weight>
          <upload-bandwidth-threshold>80</upload-bandwidth-threshold>
          <download-bandwidth-threshold>80</download-bandwidth-threshold>
          <priority>1</priority>
          <max-conn>10000</max-conn>
          <next-hop6>77:2::2</next-hop6>
          <is-master>false</is-master>
          <intf-type>interface</intf-type>
        </out-if>
        <out-if>
          <name>TenGe0/0.903</name>
          <weight>1000000</weight>
          <upload-bandwidth-threshold>80</upload-bandwidth-threshold>
          <download-bandwidth-threshold>80</download-bandwidth-threshold>
          <priority>1</priority>
          <max-conn>10000</max-conn>
          <next-hop6>77:3::2</next-hop6>
          <is-master>false</is-master>
          <intf-type>interface</intf-type>
        </out-if>
        <out-if>
          <name>TenGe0/0.904</name>
          <weight>1000000</weight>
          <upload-bandwidth-threshold>80</upload-bandwidth-threshold>
          <download-bandwidth-threshold>80</download-bandwidth-threshold>
          <priority>1</priority>
          <max-conn>10000</max-conn>
          <next-hop6>77:4::2</next-hop6>
          <is-master>false</is-master>
          <intf-type>interface</intf-type>
        </out-if>
        <out-if>
          <name>TenGe0/0.905</name>
          <weight>1000000</weight>
          <upload-bandwidth-threshold>80</upload-bandwidth-threshold>
          <download-bandwidth-threshold>80</download-bandwidth-threshold>
          <priority>1</priority>
          <max-conn>10000</max-conn>
          <next-hop6>77:5::2</next-hop6>
          <is-master>false</is-master>
          <intf-type>interface</intf-type>
        </out-if>
        <out-if>
          <name>TenGe0/0.906</name>
          <weight>1000000</weight>
          <upload-bandwidth-threshold>80</upload-bandwidth-threshold>
          <download-bandwidth-threshold>80</download-bandwidth-threshold>
          <priority>1</priority>
          <max-conn>10000</max-conn>
          <next-hop6>77:6::2</next-hop6>
          <is-master>false</is-master>
          <intf-type>interface</intf-type>
        </out-if>
        <out-if>
          <name>TenGe0/0.907</name>
          <weight>1000000</weight>
          <upload-bandwidth-threshold>80</upload-bandwidth-threshold>
          <download-bandwidth-threshold>80</download-bandwidth-threshold>
          <priority>1</priority>
          <max-conn>10000</max-conn>
          <next-hop6>77:7::2</next-hop6>
          <is-master>false</is-master>
          <intf-type>interface</intf-type>
        </out-if>
        <out-if>
          <name>TenGe0/0.908</name>
          <weight>1000000</weight>
          <upload-bandwidth-threshold>80</upload-bandwidth-threshold>
          <download-bandwidth-threshold>80</download-bandwidth-threshold>
          <priority>1</priority>
          <max-conn>10000</max-conn>
          <next-hop6>77:8::2</next-hop6>
          <is-master>false</is-master>
          <intf-type>interface</intf-type>
        </out-if>
        <mode>1</mode>
        <bandwidth-type>down-bandwidth</bandwidth-type>
        <config-source>manual</config-source>
        <sla-type>auto</sla-type>
        <sla-template>predefine</sla-template>
      </policy>
    </pbr>
    <wba-portal xmlns="urn:ruijie:ntos:params:xml:ns:yang:portal">
      <enabled>false</enabled>
      <port>8081</port>
      <ssl-enabled>false</ssl-enabled>
      <redirection-mode>no-redirection</redirection-mode>
    </wba-portal>
    <pppoe xmlns="urn:ruijie:ntos:params:xml:ns:yang:pppoe">
      <multi-dial>
        <enabled>false</enabled>
      </multi-dial>
    </pppoe>
    <replacement-messages xmlns="urn:ruijie:ntos:params:xml:ns:yang:replacement-messages">
      <management>
        <rm-enable-status>true</rm-enable-status>
        <cache-enable-status>true</cache-enable-status>
        <message>
          <message-id>1</message-id>
          <message-type>usually</message-type>
          <information>
            <alarm-title>网页访问被阻断</alarm-title>
            <alarm-description>根据网络控制策略，您没有权限访问该网页。如需访问，请联系网络管理员。</alarm-description>
          </information>
        </message>
        <message>
          <message-id>2</message-id>
          <message-type>usually</message-type>
          <information>
            <alarm-title>防病毒阻断</alarm-title>
            <alarm-description>您请求的页面含有病毒，已被自动阻断。请联系网络管理员协助解决。</alarm-description>
          </information>
        </message>
        <message>
          <message-id>3</message-id>
          <message-type>usually</message-type>
          <information>
            <alarm-title>您已禁止访问网络</alarm-title>
            <alarm-description>您的设备因存在安全风险或其他管理原因，已经被禁止访问网络，请自查是否存在中毒或其他安全风险，如有疑问请联系管理人员。</alarm-description>
          </information>
        </message>
      </management>
    </replacement-messages>
    <reputation-center xmlns="urn:ruijie:ntos:params:xml:ns:yang:reputation-center">
      <enabled>true</enabled>
    </reputation-center>
    <routing xmlns="urn:ruijie:ntos:params:xml:ns:yang:routing">
      <static>
        <ipv4-route>
          <destination>0.0.0.0/0</destination>
          <next-hop>
            <next-hop>***********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>***********/24</destination>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>77.7.0.2</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>*********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>***********/24</destination>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>77.7.0.2</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>*********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>***********/24</destination>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>77.7.0.2</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>*********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>***********/24</destination>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>77.7.0.2</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>*********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>***********/16</destination>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>77.7.0.2</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>*********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>********/16</destination>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>77.7.0.2</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>*********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>********/16</destination>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>77.7.0.2</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>*********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>********/16</destination>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>77.7.0.2</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>*********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>********/16</destination>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>77.7.0.2</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>*********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>********/16</destination>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>77.7.0.2</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>*********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>********/24</destination>
          <next-hop>
            <next-hop>vti31</next-hop>
            <distance>5</distance>
            <enable>true</enable>
            <descr>by tunnel wizard branch3-core1-TenGe0_0.901</descr>
          </next-hop>
          <next-hop>
            <next-hop>blackhole</next-hop>
            <distance>254</distance>
            <enable>true</enable>
            <descr>by tunnel wizard branch3-core1-TenGe0_0.904</descr>
          </next-hop>
          <next-hop>
            <next-hop>vti32</next-hop>
            <distance>5</distance>
            <enable>true</enable>
            <descr>by tunnel wizard branch3-core1-TenGe0_0.902</descr>
          </next-hop>
          <next-hop>
            <next-hop>vti33</next-hop>
            <distance>5</distance>
            <enable>true</enable>
            <descr>by tunnel wizard branch3-core1-TenGe0_0.903</descr>
          </next-hop>
          <next-hop>
            <next-hop>vti34</next-hop>
            <distance>5</distance>
            <enable>true</enable>
            <descr>by tunnel wizard branch3-core1-TenGe0_0.904</descr>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>********/24</destination>
          <next-hop>
            <next-hop>vti31</next-hop>
            <distance>5</distance>
            <enable>true</enable>
            <descr>by tunnel wizard branch3-core1-TenGe0_0.901</descr>
          </next-hop>
          <next-hop>
            <next-hop>blackhole</next-hop>
            <distance>254</distance>
            <enable>true</enable>
            <descr>by tunnel wizard branch3-core1-TenGe0_0.904</descr>
          </next-hop>
          <next-hop>
            <next-hop>vti32</next-hop>
            <distance>5</distance>
            <enable>true</enable>
            <descr>by tunnel wizard branch3-core1-TenGe0_0.902</descr>
          </next-hop>
          <next-hop>
            <next-hop>vti33</next-hop>
            <distance>5</distance>
            <enable>true</enable>
            <descr>by tunnel wizard branch3-core1-TenGe0_0.903</descr>
          </next-hop>
          <next-hop>
            <next-hop>vti34</next-hop>
            <distance>5</distance>
            <enable>true</enable>
            <descr>by tunnel wizard branch3-core1-TenGe0_0.904</descr>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>********/24</destination>
          <next-hop>
            <next-hop>vti31</next-hop>
            <distance>5</distance>
            <enable>true</enable>
            <descr>by tunnel wizard branch3-core1-TenGe0_0.901</descr>
          </next-hop>
          <next-hop>
            <next-hop>blackhole</next-hop>
            <distance>254</distance>
            <enable>true</enable>
            <descr>by tunnel wizard branch3-core1-TenGe0_0.904</descr>
          </next-hop>
          <next-hop>
            <next-hop>vti32</next-hop>
            <distance>5</distance>
            <enable>true</enable>
            <descr>by tunnel wizard branch3-core1-TenGe0_0.902</descr>
          </next-hop>
          <next-hop>
            <next-hop>vti33</next-hop>
            <distance>5</distance>
            <enable>true</enable>
            <descr>by tunnel wizard branch3-core1-TenGe0_0.903</descr>
          </next-hop>
          <next-hop>
            <next-hop>vti34</next-hop>
            <distance>5</distance>
            <enable>true</enable>
            <descr>by tunnel wizard branch3-core1-TenGe0_0.904</descr>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>********/24</destination>
          <next-hop>
            <next-hop>vti31</next-hop>
            <distance>5</distance>
            <enable>true</enable>
            <descr>by tunnel wizard branch3-core1-TenGe0_0.901</descr>
          </next-hop>
          <next-hop>
            <next-hop>blackhole</next-hop>
            <distance>254</distance>
            <enable>true</enable>
            <descr>by tunnel wizard branch3-core1-TenGe0_0.904</descr>
          </next-hop>
          <next-hop>
            <next-hop>vti32</next-hop>
            <distance>5</distance>
            <enable>true</enable>
            <descr>by tunnel wizard branch3-core1-TenGe0_0.902</descr>
          </next-hop>
          <next-hop>
            <next-hop>vti33</next-hop>
            <distance>5</distance>
            <enable>true</enable>
            <descr>by tunnel wizard branch3-core1-TenGe0_0.903</descr>
          </next-hop>
          <next-hop>
            <next-hop>vti34</next-hop>
            <distance>5</distance>
            <enable>true</enable>
            <descr>by tunnel wizard branch3-core1-TenGe0_0.904</descr>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>********/24</destination>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>********/24</destination>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
          <next-hop>
            <next-hop>********</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>*********/16</destination>
          <next-hop>
            <next-hop>************%Ge0/0.71</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
        </ipv4-route>
      </static>
      <interface>
        <name>TenGe0/0.901</name>
        <ip>
          <ospf xmlns="urn:ruijie:ntos:params:xml:ns:yang:ospf">
            <area>0.0.0.0</area>
            <authentication>null</authentication>
            <cost>1</cost>
            <dead-interval>
              <seconds>3</seconds>
            </dead-interval>
            <hello-interval>1</hello-interval>
            <mtu-ignore>false</mtu-ignore>
            <passive>false</passive>
            <priority>1</priority>
            <retransmit-interval>5</retransmit-interval>
            <transmit-delay>1</transmit-delay>
            <network>broadcast</network>
          </ospf>
        </ip>
      </interface>
      <interface>
        <name>TenGe0/0.902</name>
        <ip>
          <ospf xmlns="urn:ruijie:ntos:params:xml:ns:yang:ospf">
            <area>0.0.0.0</area>
            <authentication>null</authentication>
            <cost>1</cost>
            <dead-interval>
              <seconds>3</seconds>
            </dead-interval>
            <hello-interval>1</hello-interval>
            <mtu-ignore>false</mtu-ignore>
            <passive>false</passive>
            <priority>1</priority>
            <retransmit-interval>5</retransmit-interval>
            <transmit-delay>1</transmit-delay>
            <network>broadcast</network>
          </ospf>
        </ip>
      </interface>
      <interface>
        <name>Ge0/14</name>
      </interface>
      <interface>
        <name>TenGe0/0.905</name>
        <ip>
          <ospf xmlns="urn:ruijie:ntos:params:xml:ns:yang:ospf">
            <area>0.0.0.0</area>
            <authentication>null</authentication>
            <cost>1</cost>
            <dead-interval>
              <seconds>40</seconds>
            </dead-interval>
            <hello-interval>10</hello-interval>
            <mtu-ignore>false</mtu-ignore>
            <passive>false</passive>
            <priority>1</priority>
            <retransmit-interval>5</retransmit-interval>
            <transmit-delay>1</transmit-delay>
            <network>broadcast</network>
          </ospf>
        </ip>
      </interface>
      <interface>
        <name>TenGe0/0.906</name>
        <ip>
          <ospf xmlns="urn:ruijie:ntos:params:xml:ns:yang:ospf">
            <area>0.0.0.0</area>
            <authentication>null</authentication>
            <cost>1</cost>
            <dead-interval>
              <seconds>40</seconds>
            </dead-interval>
            <hello-interval>10</hello-interval>
            <mtu-ignore>false</mtu-ignore>
            <passive>false</passive>
            <priority>1</priority>
            <retransmit-interval>5</retransmit-interval>
            <transmit-delay>1</transmit-delay>
            <network>broadcast</network>
          </ospf>
        </ip>
      </interface>
      <interface>
        <name>Ge0/15</name>
      </interface>
      <ospf xmlns="urn:ruijie:ntos:params:xml:ns:yang:ospf">
        <router-id>************</router-id>
        <abr-type>cisco</abr-type>
        <write-multiplier>20</write-multiplier>
        <area>
          <area-id>0.0.0.0</area-id>
        </area>
        <area>
          <area-id>*******</area-id>
          <nssa>
            <summary>true</summary>
            <translate>candidate</translate>
          </nssa>
          <default-cost>1</default-cost>
        </area>
        <auto-cost>100000</auto-cost>
        <opaque-lsa>false</opaque-lsa>
        <compatible-rfc1583>false</compatible-rfc1583>
        <refresh-timer>10</refresh-timer>
        <redistribute>
          <protocol>static</protocol>
          <metric>20</metric>
          <metric-type>2</metric-type>
        </redistribute>
        <redistribute>
          <protocol>connected</protocol>
          <metric>20</metric>
          <metric-type>2</metric-type>
        </redistribute>
      </ospf>
    </routing>
    <security-defend xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-defend">
      <basic-protocol-control-enabled>false</basic-protocol-control-enabled>
      <defend-zone>
        <name>source_ddos</name>
        <enabled>true</enabled>
        <description/>
        <tcp>
          <syns-in>
            <host>
              <enabled>true</enabled>
              <threshold>10</threshold>
              <timeout>300</timeout>
            </host>
          </syns-in>
          <dst-syns-in>
            <host>
              <enabled>false</enabled>
              <threshold>10000</threshold>
              <timeout>300</timeout>
            </host>
          </dst-syns-in>
        </tcp>
        <udp>
          <src-in>
            <host>
              <enabled>true</enabled>
              <threshold>10</threshold>
              <timeout>300</timeout>
            </host>
          </src-in>
          <dst-in>
            <host>
              <enabled>false</enabled>
              <threshold>10000</threshold>
              <timeout>300</timeout>
            </host>
          </dst-in>
        </udp>
        <icmp>
          <src-in>
            <host>
              <enabled>true</enabled>
              <threshold>10</threshold>
              <timeout>300</timeout>
            </host>
          </src-in>
          <dst-in>
            <host>
              <enabled>false</enabled>
              <threshold>10000</threshold>
              <timeout>300</timeout>
            </host>
          </dst-in>
        </icmp>
        <icmpv6>
          <src-in>
            <host>
              <enabled>true</enabled>
              <threshold>10</threshold>
              <timeout>300</timeout>
            </host>
          </src-in>
          <dst-in>
            <host>
              <enabled>false</enabled>
              <threshold>10000</threshold>
              <timeout>300</timeout>
            </host>
          </dst-in>
        </icmpv6>
        <other-protocol>
          <src-in>
            <host>
              <enabled>false</enabled>
              <threshold>2000</threshold>
              <timeout>300</timeout>
            </host>
          </src-in>
          <dst-in>
            <host>
              <enabled>false</enabled>
              <threshold>10000</threshold>
              <timeout>300</timeout>
            </host>
          </dst-in>
        </other-protocol>
        <scan-ip>
          <enabled>false</enabled>
          <threshold>100</threshold>
          <timeout>300</timeout>
        </scan-ip>
        <scan-port>
          <enabled>false</enabled>
          <threshold>100</threshold>
          <timeout>300</timeout>
        </scan-port>
        <policy-action>
          <notify>true</notify>
          <block>false</block>
          <limit>false</limit>
        </policy-action>
        <proto-attack>
          <teardrop>true</teardrop>
          <src-route>true</src-route>
          <route-record>true</route-record>
          <smurf>true</smurf>
          <redirect>true</redirect>
          <icmp-unreach>true</icmp-unreach>
          <land>true</land>
          <winnuke>true</winnuke>
          <fraggle>true</fraggle>
          <large-icmp>
            <enabled>true</enabled>
            <len>1500</len>
          </large-icmp>
          <ipv6-extend-header>
            <ah>false</ah>
            <destination>false</destination>
            <esp>false</esp>
            <fragment>false</fragment>
            <hop-by-hop>false</hop-by-hop>
            <none>false</none>
            <route>false</route>
          </ipv6-extend-header>
        </proto-attack>
        <traffic-monitor>
          <tcp>false</tcp>
          <udp>false</udp>
          <icmp>false</icmp>
          <icmpv6>false</icmpv6>
          <ip>false</ip>
          <http>false</http>
        </traffic-monitor>
      </defend-zone>
      <dst-defend-zone>
        <name>dest_ddos</name>
        <enabled>true</enabled>
        <description/>
        <tcp>
          <dst-syns-in>
            <host>
              <enabled>true</enabled>
              <threshold>10</threshold>
              <timeout>300</timeout>
            </host>
          </dst-syns-in>
        </tcp>
        <udp>
          <dst-in>
            <host>
              <enabled>true</enabled>
              <threshold>10</threshold>
              <timeout>300</timeout>
            </host>
          </dst-in>
        </udp>
        <icmp>
          <dst-in>
            <host>
              <enabled>true</enabled>
              <threshold>10</threshold>
              <timeout>300</timeout>
            </host>
          </dst-in>
        </icmp>
        <icmpv6>
          <dst-in>
            <host>
              <enabled>true</enabled>
              <threshold>10</threshold>
              <timeout>300</timeout>
            </host>
          </dst-in>
        </icmpv6>
        <other-protocol>
          <dst-in>
            <host>
              <enabled>false</enabled>
              <threshold>10000</threshold>
              <timeout>300</timeout>
            </host>
          </dst-in>
        </other-protocol>
        <policy-action>
          <notify>true</notify>
          <block>false</block>
          <limit>false</limit>
        </policy-action>
        <proto-attack>
          <teardrop>true</teardrop>
          <src-route>true</src-route>
          <route-record>true</route-record>
          <smurf>true</smurf>
          <redirect>true</redirect>
          <icmp-unreach>true</icmp-unreach>
          <land>true</land>
          <winnuke>true</winnuke>
          <fraggle>true</fraggle>
          <large-icmp>
            <enabled>true</enabled>
            <len>1500</len>
          </large-icmp>
          <ipv6-extend-header>
            <ah>false</ah>
            <destination>false</destination>
            <esp>false</esp>
            <fragment>false</fragment>
            <hop-by-hop>false</hop-by-hop>
            <none>false</none>
            <route>false</route>
          </ipv6-extend-header>
        </proto-attack>
        <traffic-monitor>
          <tcp>false</tcp>
          <udp>false</udp>
          <icmp>false</icmp>
          <icmpv6>false</icmpv6>
          <ip>false</ip>
          <http>false</http>
        </traffic-monitor>
      </dst-defend-zone>
      <blacklist>
        <object>*******</object>
        <way>srcip</way>
        <src-type>manual</src-type>
        <description>IPv4黑名单</description>
        <enabled>true</enabled>
      </blacklist>
      <ipv6-blacklist>
        <object>1::1</object>
        <way>srcip</way>
        <src-type>manual</src-type>
        <description>IPv6黑名单</description>
        <enabled>true</enabled>
      </ipv6-blacklist>
      <whitelist>
        <object>*******</object>
        <way>srcip</way>
        <src-type>manual</src-type>
        <description>IPv4白名单</description>
        <enabled>true</enabled>
      </whitelist>
      <ipv6-whitelist>
        <object>1::</object>
        <way>srcip</way>
        <src-type>manual</src-type>
        <description>IPv6白名单</description>
        <enabled>true</enabled>
      </ipv6-whitelist>
      <enabled>false</enabled>
    </security-defend>
    <security-policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
      <policy>
        <name>IPsec_branch3-core1-TenGe0_0.904_in</name>
        <enabled>true</enabled>
        <description>by tunnel wizard branch3-core1-TenGe0_0.904</description>
        <group-name>def-group</group-name>
        <source-zone>
          <name>branch3-core1-TenGe0_0.904</name>
        </source-zone>
        <source-network>
          <name>branch3-core1-TenGe0_0.904_remote</name>
        </source-network>
        <dest-network>
          <name>branch3-core1-TenGe0_0.904_local</name>
        </dest-network>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>auto</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>IPsec_branch3-core1-TenGe0_0.904_out</name>
        <enabled>true</enabled>
        <description>by tunnel wizard branch3-core1-TenGe0_0.904</description>
        <group-name>def-group</group-name>
        <dest-zone>
          <name>branch3-core1-TenGe0_0.904</name>
        </dest-zone>
        <source-network>
          <name>branch3-core1-TenGe0_0.904_local</name>
        </source-network>
        <dest-network>
          <name>branch3-core1-TenGe0_0.904_remote</name>
        </dest-network>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>auto</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>IPsec_branch3-core1-TenGe0_0.903_in</name>
        <enabled>true</enabled>
        <description>by tunnel wizard branch3-core1-TenGe0_0.903</description>
        <group-name>def-group</group-name>
        <source-zone>
          <name>branch3-core1-TenGe0_0.903</name>
        </source-zone>
        <source-network>
          <name>branch3-core1-TenGe0_0.903_remote</name>
        </source-network>
        <dest-network>
          <name>branch3-core1-TenGe0_0.903_local</name>
        </dest-network>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>auto</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>IPsec_branch3-core1-TenGe0_0.903_out</name>
        <enabled>true</enabled>
        <description>by tunnel wizard branch3-core1-TenGe0_0.903</description>
        <group-name>def-group</group-name>
        <dest-zone>
          <name>branch3-core1-TenGe0_0.903</name>
        </dest-zone>
        <source-network>
          <name>branch3-core1-TenGe0_0.903_local</name>
        </source-network>
        <dest-network>
          <name>branch3-core1-TenGe0_0.903_remote</name>
        </dest-network>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>auto</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>IPsec_branch3-core1-TenGe0_0.902_in</name>
        <enabled>true</enabled>
        <description>by tunnel wizard branch3-core1-TenGe0_0.902</description>
        <group-name>def-group</group-name>
        <source-zone>
          <name>branch3-core1-TenGe0_0.902</name>
        </source-zone>
        <source-network>
          <name>branch3-core1-TenGe0_0.902_remote</name>
        </source-network>
        <dest-network>
          <name>branch3-core1-TenGe0_0.902_local</name>
        </dest-network>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>auto</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>IPsec_branch3-core1-TenGe0_0.902_out</name>
        <enabled>true</enabled>
        <description>by tunnel wizard branch3-core1-TenGe0_0.902</description>
        <group-name>def-group</group-name>
        <dest-zone>
          <name>branch3-core1-TenGe0_0.902</name>
        </dest-zone>
        <source-network>
          <name>branch3-core1-TenGe0_0.902_local</name>
        </source-network>
        <dest-network>
          <name>branch3-core1-TenGe0_0.902_remote</name>
        </dest-network>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>auto</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>branch3-core1-TenGe0_0.901_in</name>
        <enabled>true</enabled>
        <description>by tunnel wizard branch3-core1-TenGe0_0.901</description>
        <group-name>def-group</group-name>
        <source-zone>
          <name>branch3-core1-TenGe0_0.901</name>
        </source-zone>
        <source-network>
          <name>branch3-core1-TenGe0_0.901_remote</name>
        </source-network>
        <dest-network>
          <name>branch3-core1-TenGe0_0.901_local</name>
        </dest-network>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>branch3-core1-TenGe0_0.901_out</name>
        <enabled>true</enabled>
        <description>by tunnel wizard branch3-core1-TenGe0_0.901</description>
        <group-name>def-group</group-name>
        <dest-zone>
          <name>branch3-core1-TenGe0_0.901</name>
        </dest-zone>
        <source-network>
          <name>branch3-core1-TenGe0_0.901_local</name>
        </source-network>
        <dest-network>
          <name>branch3-core1-TenGe0_0.901_remote</name>
        </dest-network>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <ips>default-use-signature-action</ips>
        <av>default-block</av>
        <url>URL_filter</url>
        <websec>default-sig-action</websec>
        <content-filter>content_filter</content-filter>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr150_172.216.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr150_172.216.0.0_16</name>
        </dest-network>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <url>URL_filter</url>
        <websec>default-sig-action</websec>
        <content-filter>content_filter</content-filter>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr200_172.217.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr200_172.217.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr650_172.218.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr650_172.218.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr700_172.219.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr700_172.219.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr150_172.216.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr150_172.216.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr200_172.217.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr200_172.217.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr650_172.218.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr650_172.218.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr700_172.219.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr700_172.219.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr250_***********_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr250_***********_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr300_192.168.100.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr300_192.168.100.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr350_192.168.150.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr350_192.168.150.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr400_192.168.200.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr400_192.168.200.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr68_68.0.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr68_68.0.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr69_69.0.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr69_69.0.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr71_********_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr71_********_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr250_***********_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr250_***********_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr300_192.168.100.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr300_192.168.100.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr350_192.168.150.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr350_192.168.150.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr400_192.168.200.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr400_192.168.200.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr68_68.0.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr68_68.0.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr69_69.0.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr69_69.0.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr71_********_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr71_********_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr450_********_24_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr450_********_24</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr500_********_24_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr500_********_24</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr550_********_24_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr550_********_24</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr600_********_24_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr600_********_24</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr72_72.0.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr72_72.0.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr450_********_24_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr450_********_24</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr500_********_24_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr500_********_24</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr550_********_24_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr550_********_24</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr600_********_24_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr600_********_24</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr72_72.0.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr72_72.0.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>permit_any</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <ips>default-use-signature-action</ips>
        <av>default-block</av>
        <url>URL_filter</url>
        <websec>default-sig-action</websec>
        <content-filter>content_filter</content-filter>
      </policy>
      <policy>
        <name>permit_all</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <ips>client-alert</ips>
        <av>branch3_deep-alert</av>
      </policy>
    </security-policy>
    <security-zone xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-zone">
      <zone>
        <name>trust</name>
        <description>Trust Zone.</description>
        <interface>
          <name>Ge0/7</name>
        </interface>
        <interface>
          <name>Ge0/2</name>
        </interface>
        <interface>
          <name>Ge0/3</name>
        </interface>
        <interface>
          <name>Ge0/0.71</name>
        </interface>
        <interface>
          <name>Ge0/0</name>
        </interface>
      </zone>
      <zone>
        <name>untrust</name>
        <description>Untrust Zone.</description>
        <interface>
          <name>Ge0/1</name>
        </interface>
        <interface>
          <name>Ge0/1.77</name>
        </interface>
      </zone>
      <zone>
        <name>DMZ</name>
        <description>Demilitarized Zone.</description>
        <priority>50</priority>
      </zone>
      <zone>
        <name>branch3wan77</name>
        <interface>
          <name>TenGe0/0</name>
        </interface>
      </zone>
      <zone>
        <name>branch3net1v68</name>
      </zone>
      <zone>
        <name>branch3net2v69</name>
      </zone>
      <zone>
        <name>branch3wan901</name>
        <interface>
          <name>TenGe0/0.901</name>
        </interface>
      </zone>
      <zone>
        <name>branch3wan902</name>
        <interface>
          <name>TenGe0/0.902</name>
        </interface>
      </zone>
      <zone>
        <name>branch3wan903</name>
        <interface>
          <name>TenGe0/0.903</name>
        </interface>
      </zone>
      <zone>
        <name>branch3wan904</name>
        <interface>
          <name>TenGe0/0.904</name>
        </interface>
      </zone>
      <zone>
        <name>branch3wan905</name>
        <interface>
          <name>TenGe0/0.905</name>
        </interface>
      </zone>
      <zone>
        <name>branch3wan906</name>
        <interface>
          <name>TenGe0/0.906</name>
        </interface>
      </zone>
      <zone>
        <name>branch3wan907</name>
        <interface>
          <name>TenGe0/0.907</name>
        </interface>
      </zone>
      <zone>
        <name>branch3wan908</name>
        <interface>
          <name>TenGe0/0.908</name>
        </interface>
      </zone>
      <zone>
        <name>branch3wan909</name>
        <interface>
          <name>TenGe0/0.909</name>
        </interface>
      </zone>
      <zone>
        <name>branch3wan910</name>
        <interface>
          <name>TenGe0/0.910</name>
        </interface>
      </zone>
      <zone>
        <name>branch3-core1-TenGe0_0.901</name>
        <description>by tunnel wizard branch3-core1-TenGe0_0.901</description>
        <interface>
          <name>vti31</name>
        </interface>
      </zone>
      <zone>
        <name>ha</name>
      </zone>
      <zone>
        <name>branch3-core1-TenGe0_0.902</name>
        <description>by tunnel wizard branch3-core1-TenGe0_0.902</description>
        <interface>
          <name>vti32</name>
        </interface>
      </zone>
      <zone>
        <name>branch3-core1-TenGe0_0.903</name>
        <description>by tunnel wizard branch3-core1-TenGe0_0.903</description>
        <interface>
          <name>vti33</name>
        </interface>
      </zone>
      <zone>
        <name>branch3-core1-TenGe0_0.904</name>
        <description>by tunnel wizard branch3-core1-TenGe0_0.904</description>
        <interface>
          <name>vti34</name>
        </interface>
      </zone>
      <zone>
        <name>monitor</name>
        <interface>
          <name>Ge0/13</name>
        </interface>
      </zone>
    </security-zone>
    <service-obj xmlns="urn:ruijie:ntos:params:xml:ns:yang:service-obj">
      <service-set>
        <name>service_22_TCP</name>
        <tcp>
          <source-port>1-65535</source-port>
          <dest-port>22</dest-port>
        </tcp>
      </service-set>
    <service-set><name>ALL</name></service-set><service-set><name>ALL_TCP</name><tcp><dest-port>1-65535</dest-port></tcp></service-set><service-set><name>ALL_UDP</name><udp><dest-port>1-65535</dest-port></udp></service-set><service-set><name>ALL_ICMP</name></service-set><service-set><name>ALL_ICMP6</name></service-set><service-set><name>GRE</name></service-set><service-set><name>AH</name></service-set><service-set><name>ESP</name></service-set><service-set><name>AOL</name><tcp><dest-port>5190-5194</dest-port></tcp></service-set><service-set><name>BGP</name><tcp><dest-port>179</dest-port></tcp></service-set><service-set><name>DHCP</name><udp><dest-port>67-68</dest-port></udp></service-set><service-set><name>DNS</name><tcp><dest-port>53</dest-port></tcp><udp><dest-port>53</dest-port></udp></service-set><service-set><name>FINGER</name><tcp><dest-port>79</dest-port></tcp></service-set><service-set><name>FTP</name><tcp><dest-port>21</dest-port></tcp></service-set><service-set><name>FTP_GET</name><tcp><dest-port>21</dest-port></tcp></service-set><service-set><name>FTP_PUT</name><tcp><dest-port>21</dest-port></tcp></service-set><service-set><name>GOPHER</name><tcp><dest-port>70</dest-port></tcp></service-set><service-set><name>H323</name><tcp><dest-port>1503</dest-port></tcp><udp><dest-port>1719</dest-port></udp></service-set><service-set><name>HTTP</name><tcp><dest-port>80</dest-port></tcp></service-set><service-set><name>HTTPS</name><tcp><dest-port>443</dest-port></tcp></service-set><service-set><name>IKE</name><udp><dest-port>4500</dest-port></udp></service-set><service-set><name>IMAP</name><tcp><dest-port>143</dest-port></tcp></service-set><service-set><name>IMAPS</name><tcp><dest-port>993</dest-port></tcp></service-set><service-set><name>Internet-Locator-Service</name><tcp><dest-port>389</dest-port></tcp></service-set><service-set><name>IRC</name><tcp><dest-port>6660-6669</dest-port></tcp></service-set><service-set><name>L2TP</name><tcp><dest-port>1701</dest-port></tcp><udp><dest-port>1701</dest-port></udp></service-set><service-set><name>LDAP</name><tcp><dest-port>389</dest-port></tcp></service-set><service-set><name>NetMeeting</name><tcp><dest-port>1720</dest-port></tcp></service-set><service-set><name>NFS</name><tcp><dest-port>2049</dest-port></tcp><udp><dest-port>2049</dest-port></udp></service-set><service-set><name>NNTP</name><tcp><dest-port>119</dest-port></tcp></service-set><service-set><name>NTP</name><tcp><dest-port>123</dest-port></tcp><udp><dest-port>123</dest-port></udp></service-set><service-set><name>OSPF</name></service-set><service-set><name>PC-Anywhere</name><tcp><dest-port>5631</dest-port></tcp><udp><dest-port>5632</dest-port></udp></service-set><service-set><name>PING</name></service-set><service-set><name>TIMESTAMP</name></service-set><service-set><name>INFO_REQUEST</name></service-set><service-set><name>INFO_ADDRESS</name></service-set><service-set><name>ONC-RPC</name><tcp><dest-port>111</dest-port></tcp><udp><dest-port>111</dest-port></udp></service-set><service-set><name>DCE-RPC</name><tcp><dest-port>135</dest-port></tcp><udp><dest-port>135</dest-port></udp></service-set><service-set><name>POP3</name><tcp><dest-port>110</dest-port></tcp></service-set><service-set><name>POP3S</name><tcp><dest-port>995</dest-port></tcp></service-set><service-set><name>PPTP</name><tcp><dest-port>1723</dest-port></tcp></service-set><service-set><name>QUAKE</name><udp><dest-port>27960</dest-port></udp></service-set><service-set><name>RAUDIO</name><udp><dest-port>7070</dest-port></udp></service-set><service-set><name>REXEC</name><tcp><dest-port>512</dest-port></tcp></service-set><service-set><name>RIP</name><udp><dest-port>520</dest-port></udp></service-set><service-set><name>RLOGIN</name><tcp><dest-port>513:512-1023</dest-port></tcp></service-set><service-set><name>RSH</name><tcp><dest-port>514:512-1023</dest-port></tcp></service-set><service-set><name>SCCP</name><tcp><dest-port>2000</dest-port></tcp></service-set><service-set><name>SIP</name><tcp><dest-port>5060</dest-port></tcp><udp><dest-port>5060</dest-port></udp></service-set><service-set><name>SIP-MSNmessenger</name><tcp><dest-port>1863</dest-port></tcp></service-set><service-set><name>SAMBA</name><tcp><dest-port>139</dest-port></tcp></service-set><service-set><name>SMTP</name><tcp><dest-port>25</dest-port></tcp></service-set><service-set><name>SMTPS</name><tcp><dest-port>465</dest-port></tcp></service-set><service-set><name>SNMP</name><tcp><dest-port>161-162</dest-port></tcp><udp><dest-port>161-162</dest-port></udp></service-set><service-set><name>SSH</name><tcp><dest-port>22</dest-port></tcp></service-set><service-set><name>SYSLOG</name><udp><dest-port>514</dest-port></udp></service-set><service-set><name>TALK</name><udp><dest-port>517-518</dest-port></udp></service-set><service-set><name>TELNET</name><tcp><dest-port>23</dest-port></tcp></service-set><service-set><name>TFTP</name><udp><dest-port>69</dest-port></udp></service-set><service-set><name>MGCP</name><udp><dest-port>2727</dest-port></udp></service-set><service-set><name>UUCP</name><tcp><dest-port>540</dest-port></tcp></service-set><service-set><name>VDOLIVE</name><tcp><dest-port>7000-7010</dest-port></tcp></service-set><service-set><name>WAIS</name><tcp><dest-port>210</dest-port></tcp></service-set><service-set><name>WINFRAME</name><tcp><dest-port>2598</dest-port></tcp></service-set><service-set><name>X-WINDOWS</name><tcp><dest-port>6000-6063</dest-port></tcp></service-set><service-set><name>PING6</name></service-set><service-set><name>MS-SQL</name><tcp><dest-port>1434</dest-port></tcp></service-set><service-set><name>MYSQL</name><tcp><dest-port>3306</dest-port></tcp></service-set><service-set><name>RDP</name><tcp><dest-port>3389</dest-port></tcp></service-set><service-set><name>VNC</name><tcp><dest-port>5900</dest-port></tcp></service-set><service-set><name>DHCP6</name><udp><dest-port>547</dest-port></udp></service-set><service-set><name>SQUID</name><tcp><dest-port>3128</dest-port></tcp></service-set><service-set><name>SOCKS</name><tcp><dest-port>1080</dest-port></tcp><udp><dest-port>1080</dest-port></udp></service-set><service-set><name>WINS</name><tcp><dest-port>1512</dest-port></tcp><udp><dest-port>1512</dest-port></udp></service-set><service-set><name>RADIUS</name><udp><dest-port>1813</dest-port></udp></service-set><service-set><name>RADIUS-OLD</name><udp><dest-port>1646</dest-port></udp></service-set><service-set><name>CVSPSERVER</name><tcp><dest-port>2401</dest-port></tcp><udp><dest-port>2401</dest-port></udp></service-set><service-set><name>AFS3</name><tcp><dest-port>7000-7009</dest-port></tcp><udp><dest-port>7000-7009</dest-port></udp></service-set><service-set><name>TRACEROUTE</name><udp><dest-port>33434-33535</dest-port></udp></service-set><service-set><name>RTSP</name><tcp><dest-port>8554</dest-port></tcp><udp><dest-port>554</dest-port></udp></service-set><service-set><name>MMS</name><tcp><dest-port>1755</dest-port></tcp><udp><dest-port>1024-5000</dest-port></udp></service-set><service-set><name>KERBEROS</name><tcp><dest-port>464</dest-port></tcp><udp><dest-port>464</dest-port></udp></service-set><service-set><name>LDAP_UDP</name><udp><dest-port>389</dest-port></udp></service-set><service-set><name>SMB</name><tcp><dest-port>445</dest-port></tcp></service-set><service-set><name>NONE</name><tcp><dest-port>0</dest-port></tcp></service-set><service-set><name>webproxy</name><tcp><dest-port>0-65535:0-65535</dest-port></tcp></service-set><service-group><name>Email Access</name><service-set><name>DNS</name></service-set><service-set><name>IMAP</name></service-set><service-set><name>IMAPS</name></service-set><service-set><name>POP3</name></service-set><service-set><name>POP3S</name></service-set><service-set><name>SMTP</name></service-set><service-set><name>SMTPS</name></service-set></service-group><service-group><name>Web Access</name><service-set><name>DNS</name></service-set><service-set><name>HTTP</name></service-set><service-set><name>HTTPS</name></service-set></service-group><service-group><name>Windows AD</name><service-set><name>DCE-RPC</name></service-set><service-set><name>DNS</name></service-set><service-set><name>KERBEROS</name></service-set><service-set><name>LDAP</name></service-set><service-set><name>LDAP_UDP</name></service-set><service-set><name>SAMBA</name></service-set><service-set><name>SMB</name></service-set></service-group><service-group><name>Exchange Server</name><service-set><name>DCE-RPC</name></service-set><service-set><name>DNS</name></service-set><service-set><name>HTTPS</name></service-set></service-group></service-obj>
    <session-limit xmlns="urn:ruijie:ntos:params:xml:ns:yang:session-limit">
      <pps-limit>
        <enabled>false</enabled>
        <global-pps>10000</global-pps>
        <ip-addr>
          <address>********</address>
          <pps>100</pps>
        </ip-addr>
      </pps-limit>
      <sps-limit>
        <enabled>false</enabled>
        <global-sps>10000</global-sps>
        <ip-addr>
          <address>********</address>
          <sps>100</sps>
        </ip-addr>
      </sps-limit>
      <total-session>
        <enabled>false</enabled>
        <policy>
          <name>test1</name>
          <enabled>true</enabled>
          <description/>
          <time-range>any</time-range>
          <session-number>10000</session-number>
          <per-ip-number>1000</per-ip-number>
          <action>alert</action>
        </policy>
      </total-session>
    </session-limit>
    <sim-status xmlns="urn:ruijie:ntos:params:xml:ns:yang:sim-security-policy">
      <action>free</action>
    </sim-status>
    <sim-security-policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:sim-security-policy">
      <policy>
        <name>addr70_********_24_to_addr150_172.216.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr150_172.216.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr200_172.217.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr200_172.217.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr650_172.218.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr650_172.218.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr700_172.219.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr700_172.219.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr150_172.216.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr150_172.216.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr200_172.217.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr200_172.217.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr650_172.218.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr650_172.218.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr700_172.219.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr700_172.219.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr250_***********_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr250_***********_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr300_192.168.100.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr300_192.168.100.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr350_192.168.150.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr350_192.168.150.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr400_192.168.200.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr400_192.168.200.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr68_68.0.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr68_68.0.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr69_69.0.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr69_69.0.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr71_********_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr71_********_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr250_***********_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr250_***********_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr300_192.168.100.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr300_192.168.100.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr350_192.168.150.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr350_192.168.150.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr400_192.168.200.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr400_192.168.200.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr68_68.0.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr68_68.0.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr69_69.0.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr69_69.0.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr71_********_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr71_********_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr450_********_24_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr450_********_24</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr500_********_24_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr500_********_24</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr550_********_24_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr550_********_24</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr600_********_24_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr600_********_24</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr70_********_24_to_addr72_72.0.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr70_********_24</name>
        </source-network>
        <dest-network>
          <name>addr72_72.0.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr450_********_24_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr450_********_24</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr500_********_24_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr500_********_24</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr550_********_24_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr550_********_24</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr600_********_24_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr600_********_24</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>addr76_********_24_to_addr72_72.0.0.0_16_any_any_ips_disable_av_disable_url_disable_permit</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-network>
          <name>addr76_********_24</name>
        </source-network>
        <dest-network>
          <name>addr72_72.0.0.0_16</name>
        </dest-network>
        <service>
          <name>any</name>
        </service>
        <app>
          <name>any</name>
        </app>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>permit_any</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
    </sim-security-policy>
    <snmp xmlns="urn:ruijie:ntos:params:xml:ns:yang:snmp">
      <enabled>false</enabled>
      <listen>
        <protocols>udp</protocols>
        <port>161</port>
      </listen>
      <static-info>
        <location/>
        <contact/>
        <name/>
      </static-info>
      <view>
        <name>sysinfo</name>
        <subtree>
          <oid>1</oid>
          <included>true</included>
        </subtree>
      </view>
      <community>
        <name>=*-#!$eMz1ny9gpH8=</name>
        <authorization>read-write</authorization>
        <source>0.0.0.0/0</source>
        <view>sysinfo</view>
      </community>
      <community>
        <name>=*-#!$Eg+unmAs8K8=</name>
        <authorization>read-only</authorization>
        <source>0.0.0.0/0</source>
        <view>sysinfo</view>
      </community>
      <traps>
        <destination>
          <host>************</host>
          <port>162</port>
          <protocol>udp</protocol>
          <notification-type>TRAP2</notification-type>
          <community>=*-#!$eMz1ny9gpH8=</community>
        </destination>
      </traps>
    </snmp>
    <snmp-client xmlns="urn:ruijie:ntos:params:xml:ns:yang:snmp-client">
      <enabled>true</enabled>
    </snmp-client>
    <ssh-server xmlns="urn:ruijie:ntos:params:xml:ns:yang:ssh-server">
      <enabled>true</enabled>
      <port>22</port>
      <deny-count>3</deny-count>
      <unlock-time>60</unlock-time>
    </ssh-server>
    <ssl-proxy xmlns="urn:ruijie:ntos:params:xml:ns:yang:ssl-proxy">
      <policy>
        <name>any</name>
        <enabled>true</enabled>
        <action>decrypt</action>
        <ssl-proxy-profile>default</ssl-proxy-profile>
      </policy>
      <profile>
        <name>default</name>
        <description>Default Template, Traffic proxy for Internet access of users.</description>
        <outbound/>
      </profile>
      <ca-cert>
        <trust-cert>default_ca</trust-cert>
      </ca-cert>
    </ssl-proxy>
    <network-stack xmlns="urn:ruijie:ntos:params:xml:ns:yang:system">
      <ipv4>
        <arp-ignore>check-interface-and-subnet</arp-ignore>
      </ipv4>
    </network-stack>
    <threat-intelligence xmlns="urn:ruijie:ntos:params:xml:ns:yang:threat-intelligence">
      <management>
        <enable-status>true</enable-status>
        <enable-ai>false</enable-ai>
        <security-zone>
          <auto>true</auto>
        </security-zone>
        <ti-source-group>
          <source>
            <name>Tencent</name>
            <enabled>true</enabled>
            <status>0</status>
            <source-type>2</source-type>
            <capabilities>
              <capability>
                <name>in</name>
              </capability>
            </capabilities>
            <channels>
              <channel-tree>
                <name>default</name>
                <status>true</status>
              </channel-tree>
            </channels>
            <ti-types>
              <type-action-map>
                <name>1</name>
                <action>block</action>
                <status>true</status>
              </type-action-map>
              <type-action-map>
                <name>2</name>
                <action>block</action>
                <status>true</status>
              </type-action-map>
              <type-action-map>
                <name>3</name>
                <action>block</action>
                <status>true</status>
              </type-action-map>
              <type-action-map>
                <name>4</name>
                <action>block</action>
                <status>true</status>
              </type-action-map>
              <type-action-map>
                <name>5</name>
                <action>block</action>
                <status>true</status>
              </type-action-map>
              <type-action-map>
                <name>6</name>
                <action>block</action>
                <status>true</status>
              </type-action-map>
              <type-action-map>
                <name>7</name>
                <action>block</action>
                <status>true</status>
              </type-action-map>
              <type-action-map>
                <name>8</name>
                <action>block</action>
                <status>true</status>
              </type-action-map>
              <type-action-map>
                <name>9</name>
                <action>block</action>
                <status>true</status>
              </type-action-map>
              <type-action-map>
                <name>10</name>
                <action>block</action>
                <status>true</status>
              </type-action-map>
              <type-action-map>
                <name>19</name>
                <action>block</action>
                <status>true</status>
              </type-action-map>
              <type-action-map>
                <name>20</name>
                <action>block</action>
                <status>true</status>
              </type-action-map>
            </ti-types>
          </source>
          <source>
            <name>Anheng</name>
            <enabled>true</enabled>
            <status>6</status>
            <source-type>4</source-type>
            <capabilities>
              <capability>
                <name>in</name>
              </capability>
            </capabilities>
            <channels>
              <channel-tree>
                <name>default</name>
                <status>true</status>
              </channel-tree>
            </channels>
            <ti-types>
              <type-action-map>
                <name>1</name>
                <action>block</action>
                <status>true</status>
              </type-action-map>
              <type-action-map>
                <name>2</name>
                <action>block</action>
                <status>true</status>
              </type-action-map>
              <type-action-map>
                <name>3</name>
                <action>block</action>
                <status>true</status>
              </type-action-map>
              <type-action-map>
                <name>4</name>
                <action>block</action>
                <status>true</status>
              </type-action-map>
              <type-action-map>
                <name>5</name>
                <action>block</action>
                <status>true</status>
              </type-action-map>
              <type-action-map>
                <name>6</name>
                <action>block</action>
                <status>true</status>
              </type-action-map>
              <type-action-map>
                <name>7</name>
                <action>block</action>
                <status>true</status>
              </type-action-map>
              <type-action-map>
                <name>8</name>
                <action>block</action>
                <status>true</status>
              </type-action-map>
              <type-action-map>
                <name>9</name>
                <action>block</action>
                <status>true</status>
              </type-action-map>
              <type-action-map>
                <name>10</name>
                <action>block</action>
                <status>true</status>
              </type-action-map>
            </ti-types>
          </source>
        </ti-source-group>
      </management>
      <custom>
        <ti-groups>
          <name>test1</name>
          <enable>true</enable>
        </ti-groups>
      </custom>
    </threat-intelligence>
    <time-range xmlns="urn:ruijie:ntos:params:xml:ns:yang:time-range">
      <range>
        <name>any</name>
        <description>Time range of all the time.</description>
        <period>
          <start>00:00:00</start>
          <end>23:59:59</end>
          <weekday>
            <key>sun</key>
          </weekday>
          <weekday>
            <key>mon</key>
          </weekday>
          <weekday>
            <key>tue</key>
          </weekday>
          <weekday>
            <key>wed</key>
          </weekday>
          <weekday>
            <key>thu</key>
          </weekday>
          <weekday>
            <key>fri</key>
          </weekday>
          <weekday>
            <key>sat</key>
          </weekday>
        </period>
      </range>
    </time-range>
    <traffic-analy xmlns="urn:ruijie:ntos:params:xml:ns:yang:traffic-analy">
      <enabled>true</enabled>
      <learning-address>
        <ipv4-address>********/24</ipv4-address>
      </learning-address>
      <learning-address>
        <ipv4-address>********/24</ipv4-address>
      </learning-address>
    </traffic-analy>
    <upnp-proxy xmlns="urn:ruijie:ntos:params:xml:ns:yang:upnp-proxy">
      <enabled>false</enabled>
      <bind-rule>ip</bind-rule>
      <advance>
        <automatic-enrollment>
          <enabled>false</enabled>
          <registration-time>1440</registration-time>
          <logout-check-period>3</logout-check-period>
        </automatic-enrollment>
        <terminal-authorization>
          <enabled>false</enabled>
        </terminal-authorization>
        <linkage-service>
          <enabled>false</enabled>
        </linkage-service>
        <offline-detect>
          <time-range>60</time-range>
          <flow-rate>0</flow-rate>
        </offline-detect>
        <scheduled-offline>
          <enabled>false</enabled>
          <time>00:00</time>
        </scheduled-offline>
        <quick-response-code-valid-time>
          <time>480</time>
        </quick-response-code-valid-time>
      </advance>
      <reserve>
        <single-ip-process>
          <interval-time>5</interval-time>
          <max-package>40</max-package>
        </single-ip-process>
        <unicast>
          <enabled>false</enabled>
        </unicast>
        <web-url-compatible>
          <enabled>false</enabled>
        </web-url-compatible>
        <map-cover-mode>
          <enabled>false</enabled>
        </map-cover-mode>
        <server-capacity>1</server-capacity>
      </reserve>
    </upnp-proxy>
    <url-filter xmlns="urn:ruijie:ntos:params:xml:ns:yang:url-filter">
      <name>URL_filter</name>
      <unknown-category-action>alert</unknown-category-action>
    </url-filter>
    <user-management xmlns="urn:ruijie:ntos:params:xml:ns:yang:user-management">
      <user>
        <name>gc1001</name>
        <aaa-domain>default</aaa-domain>
        <enabled>true</enabled>
        <password>=*-#!$_2g+awtqJWZWf+C6zl4RLQ==</password>
        <parent-group-path>/default</parent-group-path>
        <ip-mac-binding>
          <no-binding/>
        </ip-mac-binding>
        <expiration-time>
          <never-expire/>
        </expiration-time>
      </user>
      <user>
        <name>gc1002</name>
        <aaa-domain>default</aaa-domain>
        <enabled>true</enabled>
        <password>=*-#!$_2g+awtqJWZWf+C6zl4RLQ==</password>
        <parent-group-path>/default</parent-group-path>
        <ip-mac-binding>
          <no-binding/>
        </ip-mac-binding>
        <expiration-time>
          <never-expire/>
        </expiration-time>
      </user>
      <user>
        <name>gc1003</name>
        <aaa-domain>default</aaa-domain>
        <enabled>true</enabled>
        <password>=*-#!$_2g+awtqJWZWf+C6zl4RLQ==</password>
        <parent-group-path>/default</parent-group-path>
        <ip-mac-binding>
          <no-binding/>
        </ip-mac-binding>
        <expiration-time>
          <never-expire/>
        </expiration-time>
      </user>
      <user>
        <name>gc1004</name>
        <aaa-domain>default</aaa-domain>
        <enabled>true</enabled>
        <password>=*-#!$_2g+awtqJWZWf+C6zl4RLQ==</password>
        <parent-group-path>/default</parent-group-path>
        <ip-mac-binding>
          <no-binding/>
        </ip-mac-binding>
        <expiration-time>
          <never-expire/>
        </expiration-time>
      </user>
      <user>
        <name>gc1005</name>
        <aaa-domain>default</aaa-domain>
        <enabled>true</enabled>
        <password>=*-#!$_2g+awtqJWZWf+C6zl4RLQ==</password>
        <parent-group-path>/default</parent-group-path>
        <ip-mac-binding>
          <no-binding/>
        </ip-mac-binding>
        <expiration-time>
          <never-expire/>
        </expiration-time>
      </user>
      <user>
        <name>gc1006</name>
        <aaa-domain>default</aaa-domain>
        <enabled>true</enabled>
        <password>=*-#!$_2g+awtqJWZWf+C6zl4RLQ==</password>
        <parent-group-path>/default</parent-group-path>
        <ip-mac-binding>
          <no-binding/>
        </ip-mac-binding>
        <expiration-time>
          <never-expire/>
        </expiration-time>
      </user>
      <user>
        <name>gc1007</name>
        <aaa-domain>default</aaa-domain>
        <enabled>true</enabled>
        <password>=*-#!$_2g+awtqJWZWf+C6zl4RLQ==</password>
        <parent-group-path>/default</parent-group-path>
        <ip-mac-binding>
          <no-binding/>
        </ip-mac-binding>
        <expiration-time>
          <never-expire/>
        </expiration-time>
      </user>
      <user>
        <name>gc1008</name>
        <aaa-domain>default</aaa-domain>
        <enabled>true</enabled>
        <password>=*-#!$_2g+awtqJWZWf+C6zl4RLQ==</password>
        <parent-group-path>/default</parent-group-path>
        <ip-mac-binding>
          <no-binding/>
        </ip-mac-binding>
        <expiration-time>
          <never-expire/>
        </expiration-time>
      </user>
      <user>
        <name>gc1009</name>
        <aaa-domain>default</aaa-domain>
        <enabled>true</enabled>
        <password>=*-#!$_2g+awtqJWZWf+C6zl4RLQ==</password>
        <parent-group-path>/default</parent-group-path>
        <ip-mac-binding>
          <no-binding/>
        </ip-mac-binding>
        <expiration-time>
          <never-expire/>
        </expiration-time>
      </user>
      <user>
        <name>gc1010</name>
        <aaa-domain>default</aaa-domain>
        <enabled>true</enabled>
        <password>=*-#!$_2g+awtqJWZWf+C6zl4RLQ==</password>
        <parent-group-path>/default</parent-group-path>
        <ip-mac-binding>
          <no-binding/>
        </ip-mac-binding>
        <expiration-time>
          <never-expire/>
        </expiration-time>
      </user>
    </user-management>
    <websec-config xmlns="urn:ruijie:ntos:params:xml:ns:yang:web-security">
      <profile>
        <name>default-sig-action</name>
        <template-name>default</template-name>
        <action>sig-action</action>
      </profile>
    </websec-config>
    <webauth xmlns="urn:ruijie:ntos:params:xml:ns:yang:webauth">
      <authentication-options>
        <portal-authentication>
          <portal-group>
            <name>cportal</name>
            <protocol>portal</protocol>
          </portal-group>
        </portal-authentication>
      </authentication-options>
      <link-sam>
        <enabled>true</enabled>
        <listening-port>2009</listening-port>
      </link-sam>
      <single-sign-on>
        <ad>
          <method>plugin</method>
        </ad>
      </single-sign-on>
    </webauth>
  </vrf>
  <system xmlns="urn:ruijie:ntos:params:xml:ns:yang:system">
    <hostname>z86g20-02</hostname>
    <devicename>RG-WALL</devicename>
    <cp-mask>default</cp-mask>
    <nfp>
      <autoperf>
        <enabled>true</enabled>
      </autoperf>
    </nfp>
    <network-stack>
      <bridge>
        <call-ipv4-filtering>false</call-ipv4-filtering>
        <call-ipv6-filtering>false</call-ipv6-filtering>
      </bridge>
      <icmp>
        <rate-limit-icmp>1000</rate-limit-icmp>
        <rate-mask-icmp>destination-unreachable source-quench time-exceeded parameter-problem</rate-mask-icmp>
      </icmp>
      <ipv4>
        <forwarding>true</forwarding>
        <send-redirects>true</send-redirects>
        <accept-redirects>false</accept-redirects>
        <accept-source-route>false</accept-source-route>
        <arp-announce>any</arp-announce>
        <arp-filter>false</arp-filter>
        <arp-ignore>any</arp-ignore>
        <log-invalid-addresses>false</log-invalid-addresses>
      </ipv4>
      <ipv6>
        <forwarding>true</forwarding>
        <autoconfiguration>true</autoconfiguration>
        <accept-router-advert>never</accept-router-advert>
        <accept-redirects>false</accept-redirects>
        <accept-source-route>false</accept-source-route>
        <router-solicitations>-1</router-solicitations>
        <use-temporary-addresses>never</use-temporary-addresses>
      </ipv6>
    </network-stack>
    <timezone>Asia/Shanghai</timezone>
    <scheduled-restart>
      <enabled>false</enabled>
      <hour>3</hour>
      <minute>0</minute>
      <once>false</once>
    </scheduled-restart>
    <anti-virus-file-exception xmlns="urn:ruijie:ntos:params:xml:ns:yang:anti-virus">
      <enabled>true</enabled>
    </anti-virus-file-exception>
    <auth xmlns="urn:ruijie:ntos:params:xml:ns:yang:system:auth">
      <user>
        <name>admin</name>
        <role>admin</role>
        <password>=*-#!$R8dJseVj53p1AAQyrq1s6oNkqql3diMYVZCqmzUfdSOEoVX1HWd5NRjruTxfzHdc3oq2rsUgS0WTzurLZ5ylyQ==</password>
        <network-password>=*-#!$_2g+awtqJWZWf+C6zl4RLQ==</network-password>
      </user>
      <user>
        <name>securityadmin</name>
        <role>admin</role>
        <lock>true</lock>
      </user>
      <user>
        <name>useradmin</name>
        <role>admin</role>
        <lock>true</lock>
      </user>
      <user>
        <name>auditadmin</name>
        <role>admin</role>
        <lock>true</lock>
      </user>
    </auth>
    <wis-service xmlns="urn:ruijie:ntos:params:xml:ns:yang:cloud-service">
      <enabled>true</enabled>
    </wis-service>
    <macc-service xmlns="urn:ruijie:ntos:params:xml:ns:yang:cloud-service">
      <enabled>true</enabled>
    </macc-service>
    <security-cloud-service xmlns="urn:ruijie:ntos:params:xml:ns:yang:cloud-service">
      <enabled>true</enabled>
    </security-cloud-service>
    <log2cloud xmlns="urn:ruijie:ntos:params:xml:ns:yang:cloud-service">
      <upload-interval>5</upload-interval>
    </log2cloud>
    <collect xmlns="urn:ruijie:ntos:params:xml:ns:yang:system:collect">
      <alarm-threshold>
        <disk-db-threshold>90</disk-db-threshold>
      </alarm-threshold>
      <enabled>true</enabled>
      <max-records>3072</max-records>
      <record-interval>300</record-interval>
      <memory-storage-threshold>90</memory-storage-threshold>
      <statistics-enabled>false</statistics-enabled>
      <record-stats-enabled>true</record-stats-enabled>
      <flow-log-enabled>true</flow-log-enabled>
      <log-language>Chinese</log-language>
    </collect>
    <dataplane xmlns="urn:ruijie:ntos:params:xml:ns:yang:dataplane-dsa">
      <hash>
        <type>hash-default</type>
        <bind>none</bind>
      </hash>
    </dataplane>
    <flow-audit xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-audit">
      <flowrate>
        <enable>false</enable>
      </flowrate>
      <session>
        <enable>false</enable>
      </session>
      <flowtotal>
        <enabled>false</enabled>
      </flowtotal>
      <flowspeed>
        <enabled>false</enabled>
      </flowspeed>
      <hard-disk-quota>20</hard-disk-quota>
      <refresh-time>30</refresh-time>
    </flow-audit>
    <local-defend xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
      <enabled>true</enabled>
      <host-hardening>
        <enabled>true</enabled>
        <injection-prevention>
          <enabled>false</enabled>
          <action>block</action>
        </injection-prevention>
      </host-hardening>
      <arp-monitor>
        <enabled>false</enabled>
        <scan-threshold>200</scan-threshold>
      </arp-monitor>
      <rate-limit>
        <arp>
          <req-token>5</req-token>
          <res-token>1</res-token>
          <req-threshold>100</req-threshold>
          <res-threshold>100</res-threshold>
        </arp>
      </rate-limit>
    </local-defend>
    <memory xmlns="urn:ruijie:ntos:params:xml:ns:yang:system:mem">
      <warning-threshold>90</warning-threshold>
      <critical-threshold>90</critical-threshold>
    </memory>
    <srpds xmlns="urn:ruijie:ntos:params:xml:ns:yang:srpds">
      <nosync-instance>/ntos:config/vrf[name='main']/ntos-interface:interface/physical[name='MGMT']</nosync-instance>
      <nosync-instance>/ntos:config/vrf[name='main']/ntos-ip-track:track/rule[name='HA0-TrackIP']</nosync-instance>
      <nosync-instance>/ntos:config/vrf[name='main']/ntos-interface:interface/ntos-lag:lag[name='ha']</nosync-instance>
    </srpds>
    <usr-exp-plan xmlns="urn:ruijie:ntos:params:xml:ns:yang:user-experience-plan">
      <no-prompt>false</no-prompt>
      <enabled>true</enabled>
      <log-category>
        <security-log>true</security-log>
        <device-log>true</device-log>
        <engine-log>true</engine-log>
      </log-category>
    </usr-exp-plan>
    <web-server xmlns="urn:ruijie:ntos:params:xml:ns:yang:system:web-server">
      <enabled>true</enabled>
      <port>443</port>
      <http-enabled>true</http-enabled>
      <smart-http-enabled>false</smart-http-enabled>
    </web-server>
  </system>
<ns6:sslvpn xmlns:ns6="urn:ruijie:ntos:params:xml:ns:yang:sslvpn"><ns6:gateway><name>dd</name><type>exclusive</type><address/><short-message><enabled>false</enabled><smsg-everyday-peruser-max>0</smsg-everyday-peruser-max><smsg-extend-functions></smsg-extend-functions><smsg-code-policy><validate-times>5</validate-times><max-retries>5</max-retries></smsg-code-policy></short-message><dns-order>gateway-first</dns-order><protocol>tls1.2</protocol><local-cert>ruijie</local-cert><max-concur-users>10</max-concur-users><auth-zone>default</auth-zone><login-limit><by-user><enabled>true</enabled><user-max-retries>5</user-max-retries><user-lock-secs>5</user-lock-secs></by-user><by-ip><enabled>true</enabled><ip-max-retries>5</ip-max-retries><ip-lock-secs>5</ip-lock-secs></by-ip></login-limit><client-policy><policy>any</policy></client-policy><session-timeout>30</session-timeout><soft-keyboard-enabled>false</soft-keyboard-enabled><img-verify><enabled>false</enabled></img-verify><hardid-verify><enabled>false</enabled><max-num>3</max-num><auto-approval>true</auto-approval><auto-approve-pub-term>false</auto-approve-pub-term><hardid-self-unbind>false</hardid-self-unbind></hardid-verify><iptunnel><timeout-idle>30</timeout-idle><keep-alive><interval>30</interval><dead-time>180</dead-time></keep-alive><route-mode>split</route-mode><sslvpn-line>false</sslvpn-line><ip-pool/></iptunnel><resource><iptun-resgrp><name>ff</name><host><name>ff</name><value>*******</value><proto>any</proto><start-port>1</start-port><end-port>65535</end-port></host></iptun-resgrp></resource><authorize-policy><name>default</name><group><name>/default</name></group><iptun-resgrp>ff</iptun-resgrp></authorize-policy><enabled>true</enabled></ns6:gateway></ns6:sslvpn></config>
