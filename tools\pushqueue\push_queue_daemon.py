
#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File    :   testmanager.py
@Time    :   2022/06/10 11:07:05
<AUTHOR>   <PERSON><PERSON><PERSON>
@Version :   1.0
@Contact :   <EMAIL>
'''

# here put the import lib
import random, time, queue
from multiprocessing.managers import BaseManager
import pickle

CACHE = 'cache.dat'
data = {}
try:
    with open(CACHE, 'rb') as f:
        data = pickle.load(f)
except Exception as e:
    print(e)

class Cache:
    def set_data(self, key, value, method='add'):
        if method == 'add':
            if key and value:
                data[key] = value
        else:
            if key:
                del data[key]
        with open(CACHE, 'wb') as f:
            pickle.dump(data, f)
        return data

    def get_data(self):
        return data

# 继承
class QueueManager(BaseManager):
    pass

if __name__ == '__main__':
    # 把俩Queue注册到网络上
    QueueManager.register('cache', Cache)

    # 绑定端口号
    manager = QueueManager(address=('127.0.0.1', 6001), authkey=b'ok')
    # server = manager.get_server()
    # server.serve_forever()
    # 启动Queue
    manager.start()
    print('here')
    # # 获取通过网络访问的queue对象
    cache = manager.cache()

    while True:
        r = cache.get_data()
        print('Result:%s' % r)
        for key in r.keys():
            if time.time() - r[key]['last_modified'] > 10:
                cache.set_data(key, '', method='del')
        time.sleep(10)
