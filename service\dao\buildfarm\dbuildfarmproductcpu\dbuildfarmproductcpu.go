package dbuildfarmproductcpu

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/buildfarm"
)

const ModelName = "产品CPU映射表"

type BuildfarmProductCpu struct {
	buildfarm.BuildfarmProductCpu
}

type ListResponse struct {
	BuildfarmProductCpu
}

type Request struct {
}

func (a *BuildfarmProductCpu) ModelName() string {
	return ModelName
}

func Model() *buildfarm.BuildfarmProductCpu {
	return &buildfarm.BuildfarmProductCpu{}
}

func (a *BuildfarmProductCpu) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *BuildfarmProductCpu) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *BuildfarmProductCpu) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	return err
}

func (this *BuildfarmProductCpu) CreateV2(object interface{}) error {
	return nil
}

func (a *BuildfarmProductCpu) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *BuildfarmProductCpu) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *BuildfarmProductCpu) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *BuildfarmProductCpu) FindEx(p1, p2 interface{}, a ...interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(p1, p2, a).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindAll() ([]*BuildfarmProductCpu, error) {
	var list []*BuildfarmProductCpu
	err := easygorm.GetEasyGormDb().Model(Model()).Find(&list).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return nil, err
	}
	return list, nil
}

func FindDistinctCpuByProduct(products []string) ([]string, error) {
	var cpus []string
	err := easygorm.GetEasyGormDb().Model(Model()).Distinct("cpu").Where("product in ?", products).Find(&cpus).Error
	return cpus, err
}

func (u *BuildfarmProductCpu) FindByProductAndCpu(product, cpu string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("product = ? and cpu = ?", product, cpu).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}
