#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
import email.header
from email import message_from_string
import re

mail = '''Date: Mon, 30 May 2022 08:54:23 +0000
From: =?UTF-8?B?5p2O6Zye?= <gitlab@**************>
Reply-To: GitLab <noreply@**************>
To: z<PERSON><EMAIL>
Message-ID: <merge_request_3688@**************>
Subject: =?UTF-8?Q?fast-path_|?=
 =?UTF-8?Q?_=E6=B5=81=E7=8A=B6=E6=80=81=E6=9B=B4=E6=96=B0=E6=8E=A5=E5=8F=A3=EF=BC=8Cssl=E4=BB=A3=E7=90=86makefile/=E4=BF=AE=E8=AE=A2bug?=
 =?UTF-8?Q?_=28!807=29?=
Mime-Version: 1.0
Content-Type: multipart/alternative;
 boundary="--==_mimepart_6294863f3b566_22e1edda686211cd";
 charset=UTF-8
Content-Transfer-Encoding: 7bit
X-GitLab-Project: fast-path
X-GitLab-Project-Id: 35
*********************: dataplane/fast-path
List-Id: dataplane/fast-path <35.fast-path.dataplane.**************>
List-Unsubscribe: <http://**************:8080/-/sent_notifications/ded1ea5886de09fd2083ecd05375d3bb/unsubscribe?force=true>
X-GitLab-MergeRequest-ID: 3688
X-GitLab-MergeRequest-IID: 807
X-GitLab-NotificationReason: 
X-GitLab-Reply-Key: ded1ea5886de09fd2083ecd05375d3bb
Auto-Submitted: auto-generated
X-Auto-Response-Suppress: All


----==_mimepart_6294863f3b566_22e1edda686211cd
Content-Type: text/plain;
 charset=UTF-8
Content-Transfer-Encoding: base64

DQoNCuadjumcniBjcmVhdGVkIGEgbWVyZ2UgcmVxdWVzdDogaHR0cDovLzE3
************************************************************
X3JlcXVlc3RzLzgwNw0KDQpQcm9qZWN0OkJyYW5jaGVzOiBsaXhpYTEvZmFz
dC1wYXRoOnRydW5rLW1lcmdlIHRvIGRhdGFwbGFuZS9mYXN0LXBhdGg6VHJ1
bmsNCkF1dGhvcjogICAg5p2O6ZyeDQpBc3NpZ25lZTogemhhb3Rhbw0KDQoN
Cua1geW5s+WPsOaPkOS+m+eahOa1geeKtuaAgeabtOaWsOaOpeWPow0Kc3Ns
5Luj55CG5ZGo6L6555qE5Luj56CB77yabWFrZWZpbGUv5ou36LSd5paH5Lu2
L1RMU+WNj+iuruino+aekOmAmuWRii9hcHAtcGFyc2Vy5qOA5p+l5qCH5b+X
5L2NDQrnvJbnoIFidWfkv67orqINCg0KLS0gDQpWaWV3IGl0IG9uIEdpdExh
************************************************************
cGF0aC8tL21lcmdlX3JlcXVlc3RzLzgwNw0KWW91J3JlIHJlY2VpdmluZyB0
aGlzIGVtYWlsIGJlY2F1c2Ugb2YgeW91ciBhY2NvdW50IG9uIDE3Mi4xOC4x
NDEuMTAyLg0KDQoNCg==

----==_mimepart_6294863f3b566_22e1edda686211cd
Content-Type: text/html;
 charset=UTF-8
Content-Transfer-Encoding: quoted-printable

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www=
.w3.org/TR/REC-html40/loose.dtd">
<html lang=3D"en">
<head>
<meta content=3D"text/html; charset=3DUS-ASCII" http-equiv=3D"Content-Typ=
e">
<title>
GitLab
</title>


<style>img {
max-width: 100%; height: auto;
}
</style>
</head>
<body>
<div class=3D"content">

<p class=3D"details" style=3D"font-style: italic; color: #666;">
<a href=3D"http://**************:8080/lixia1">&#26446;&#38686;</a> create=
d a merge request:
</p>
<p>
</p>
<div class=3D"branch">
Project:Branches: lixia1/fast-path:trunk-merge to dataplane/fast-path:Tru=
nk
</div>
<div class=3D"author">
Author: &#26446;&#38686;
</div>
<div class=3D"assignee">
Assignee: zhaotao
</div>
<div class=3D"approvers">

</div>

<div>
<p dir=3D"auto">&#27969;&#24179;&#21488;&#25552;&#20379;&#30340;&#27969;&=
#29366;&#24577;&#26356;&#26032;&#25509;&#21475;
ssl&#20195;&#29702;&#21608;&#36793;&#30340;&#20195;&#30721;&#65306;makefi=
le/&#25335;&#36125;&#25991;&#20214;/TLS&#21327;&#35758;&#35299;&#26512;&#=
36890;&#21578;/app-parser&#26816;&#26597;&#26631;&#24535;&#20301;
&#32534;&#30721;bug&#20462;&#35746;</p>
</div>

</div>
<div class=3D"footer" style=3D"margin-top: 10px;">
<p style=3D"font-size: small; color: #666;">
&#8212;
<br>
<a href=3D"http://**************:8080/dataplane/fast-path/-/merge_request=
s/807">View it on GitLab</a>.
<br>
You're receiving this email because of your account on **************.
If you'd like to receive fewer emails, you can
<a href=3D"http://**************:8080/-/sent_notifications/ded1ea5886de09=
fd2083ecd05375d3bb/unsubscribe">unsubscribe</a>
from this thread or
adjust your notification settings.
<script type=3D"application/ld+json">{"@context":"http://schema.org","@ty=
pe":"EmailMessage","action":{"@type":"ViewAction","name":"View Merge requ=
est","url":"http://**************:8080/dataplane/fast-path/-/merge_reques=
ts/807"}}</script>


</p>
</div>
</body>
</html>

----==_mimepart_6294863f3b566_22e1edda686211cd--'''
mail = re.sub(r'\r(?!\n)', '\r\n', mail)

def decode_mime_words(s):
    return u''.join(
        word.decode(encoding or 'utf8') if isinstance(word, bytes) else word
        for word, encoding in email.header.decode_header(s))

_from = '<EMAIL>'


def parser_mail_data(mail):
    msg = message_from_string(mail)
    sub = msg.get('subject').split("|")
    sub = "".join(['[{}]'.format(i.strip()) for i in sub])
    sub = '[gitlab]' + sub
    _from = '<EMAIL>'
    to = msg.get('to')
    if to not in ['<EMAIL>']:
        to = '<EMAIL>'
    body = parseEmail(msg)
    print(f'{_from}|{to}|{sub}|{body}')

def parseEmail(e):
    # 解析邮件/信体
    # 循环信件中的每一个mime的数据块
    body = ''
    for part in e.walk():
        # 这里要判断是否是multipart，是的话，里面的数据是一个message 列表
        if not part.is_multipart():
            contenttype = part.get_content_type()
            # 如果是附件，这里就会取出附件的文件名
            if contenttype == 'text/plain':
                print(part.get('Content-Transfer-Encoding'))
                if part.get('Content-Transfer-Encoding') == 'base64':
                    body = part.get_payload(decode=True).decode('utf-8')
    _body = body.split('\n')
    __body = []
    for i in _body:
        if i and not i.startswith('>') and ('http://**************:8080' in i or len(i) < 200) and i != '\\r':
            __body.append(i)
    result = []
    # if len(__body) > 22:
    #     result = result + __body[:10]
    #     result = result + __body[-10:]
    # else:
    #     result = __body
    if len(__body[0]) > 51:
        result.append('{}......(已隐藏后续内容，详情请登陆gitlab)'.format(__body[0][:51]))
    else:
        result.append('{}'.format(__body[0]))
    result = result + __body[-2:]

    body = "".join(['<p>{}</p>'.format(i) for i in result])

    return body


parser_mail_data(mail)