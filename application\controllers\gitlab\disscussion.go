package gitlab

import (
	"errors"
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"strings"

	"github.com/kataras/iris/v12"
)

type MergeRequestResponse struct {
	ID          uint   `json:"id"`
	IID         uint   `json:"iid"`
	Title       string `json:"title"`
	Description string `json:"description"`
	ProjectID   uint   `json:"project_id"`
	Author      struct {
		ID       uint   `json:"id"`
		Name     string `json:"name"`
		Username string `json:"username"`
	} `json:"author"`
	Assignees []struct {
		ID       uint   `json:"id"`
		Name     string `json:"name"`
		Username string `json:"username"`
	} `json:"assignees"`
	Reviewers []struct {
		ID       uint   `json:"id"`
		Name     string `json:"name"`
		Username string `json:"username"`
	} `json:"reviewers"`
	WebUrl         string   `json:"web_url"`
	DiffRefs       DiffRefs `json:"diff_refs"`
	MergeCommitSha *string  `json:"merge_commit_sha"`
	TargetBranch   string   `json:"target_branch"`
}

type DiscussionRequest struct {
	Body     string   `json:"body"`
	Position Position `json:"position"`
}

type DiscussionResponse struct {
	Notes []DiscussionRequest `json:"notes"`
}

type DiffRefs struct {
	BaseSha  string `json:"base_sha"`
	HeadSha  string `json:"head_sha"`
	StartSha string `json:"start_sha"`
}

type Position struct {
	DiffRefs
	PositionType string `json:"position_type"`
	NewPath      string `json:"new_path"`
	NewLine      uint   `json:"new_line"`
	LineRange    struct {
		Start struct {
			LineCode uint   `json:"line_code"`
			Type     string `json:"type"`
		} `json:"start"`
		End struct {
			LineCode uint   `json:"line_code"`
			Type     string `json:"type"`
		} `json:"end"`
	} `json:"line_range;omitempty"`
}

func MergeRequestDiscussions(ctx iris.Context) {
	projectId := ctx.Params().GetString("project_id")
	mergeRequestIID := ctx.Params().GetString("merge_request_iid")
	if projectId == "" || mergeRequestIID == "" {
		logging.ErrorLogger.Errorf("params error")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	mr, err := MergeRequest(projectId, mergeRequestIID)
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if mr == nil {
		logging.ErrorLogger.Errorf("未找到mr")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	request := DiscussionRequest{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("create project read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(&request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		logging.ErrorLogger.Errorf("create project read json err ", strings.Join(errs, ";"))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	request.Position.BaseSha = mr.DiffRefs.BaseSha
	request.Position.HeadSha = mr.DiffRefs.HeadSha
	request.Position.StartSha = mr.DiffRefs.StartSha

	if err := mr.CreateMergeRequsetDiscussion(&request); err != nil {
		logging.ErrorLogger.Errorf("create merge request discussion err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func MergeRequest(projectId, mergeRequestIID string) (*MergeRequestResponse, error) {
	mergeRequestResponse := MergeRequestResponse{}
	var errMsg interface{}
	resp, err := GitlabClient.R().SetSuccessResult(&mergeRequestResponse).SetErrorResult(&errMsg).Get(fmt.Sprintf("projects/%s/merge_requests/%s", projectId, mergeRequestIID))
	if err != nil {
		logging.ErrorLogger.Errorf("get pipeline error", err.Error())
		return &mergeRequestResponse, err
	}
	if resp.IsSuccessState() {
		return &mergeRequestResponse, nil
	}
	return nil, fmt.Errorf("%v", errMsg)
}

func GetMergeRequset(ctx iris.Context) {
	projectId := ctx.Params().GetString("project_id")
	mergeRequestIID := ctx.Params().GetString("merge_request_iid")
	if projectId == "" || mergeRequestIID == "" {
		logging.ErrorLogger.Errorf("params error")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	mr, err := MergeRequest(projectId, mergeRequestIID)
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if mr == nil {
		logging.ErrorLogger.Errorf("未找到mr")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, mr, response.NoErr.Msg))
	return
}

func (r *MergeRequestResponse) CreateMergeRequsetDiscussion(body *DiscussionRequest) error {
	var errMsg interface{}
	discussions, err := r.GetMergeRequsetDiscussions()
	if err != nil {
		return errors.New(fmt.Sprintf("Unknow error, %s", err.Error()))
	}
	for _, discussion := range discussions {
		for _, note := range discussion.Notes {
			if note.Position == body.Position {
				return nil
			}
		}

	}
	resp, err := GitlabClient.R().SetErrorResult(&errMsg).SetBody(body).Post(fmt.Sprintf("projects/%d/merge_requests/%d/discussions", r.ProjectID, r.IID))
	if err != nil {
		logging.ErrorLogger.Errorf("get pipeline error", err.Error())
		return err
	}
	if resp.IsError() {
		return errors.New(fmt.Sprintf("Unknow error, %s", errMsg))
	}
	return nil
}

func (r *MergeRequestResponse) GetMergeRequsetDiscussions() ([]*DiscussionResponse, error) {
	var errMsg interface{}
	var result []*DiscussionResponse
	resp, err := GitlabClient.R().SetErrorResult(&errMsg).SetSuccessResult(&result).Get(fmt.Sprintf("projects/%d/merge_requests/%d/discussions", r.ProjectID, r.IID))
	if err != nil {
		logging.ErrorLogger.Errorf("get pipeline error", err.Error())
		return result, err
	}
	if resp.IsError() {
		return result, errors.New(fmt.Sprintf("Unknow error, %s", errMsg))
	}
	return result, nil
}

/*
{
  "id": 155016530,
  "iid": 133,
  "project_id": 15513260,
  "title": "Manual job rules",
  "description": "",
  "state": "opened",
  "created_at": "2022-05-13T07:26:38.402Z",
  "updated_at": "2022-05-14T03:38:31.354Z",
  "merged_by": null, // Deprecated and will be removed in API v5, use `merge_user` instead
  "merge_user": null,
  "merged_at": null,
  "closed_by": null,
  "closed_at": null,
  "target_branch": "master",
  "source_branch": "manual-job-rules",
  "user_notes_count": 0,
  "upvotes": 0,
  "downvotes": 0,
  "author": {
    "id": 4155490,
    "username": "marcel.amirault",
    "name": "Marcel Amirault",
    "state": "active",
    "avatar_url": "https://gitlab.com/uploads/-/system/user/avatar/4155490/avatar.png",
    "web_url": "https://gitlab.com/marcel.amirault"
  },
  "assignees": [],
  "assignee": null,
  "reviewers": [],
  "source_project_id": 15513260,
  "target_project_id": 15513260,
  "labels": [],
  "draft": false,
  "work_in_progress": false,
  "milestone": null,
  "merge_when_pipeline_succeeds": false,
  "merge_status": "can_be_merged",
  "sha": "e82eb4a098e32c796079ca3915e07487fc4db24c",
  "merge_commit_sha": null,
  "squash_commit_sha": null,
  "discussion_locked": null,
  "should_remove_source_branch": null,
  "force_remove_source_branch": true,
  "reference": "!133",
  "references": {
    "short": "!133",
    "relative": "!133",
    "full": "marcel.amirault/test-project!133"
  },
  "web_url": "https://gitlab.com/marcel.amirault/test-project/-/merge_requests/133",
  "time_stats": {
    "time_estimate": 0,
    "total_time_spent": 0,
    "human_time_estimate": null,
    "human_total_time_spent": null
  },
  "squash": false,
  "task_completion_status": {
    "count": 0,
    "completed_count": 0
  },
  "has_conflicts": false,
  "blocking_discussions_resolved": true,
  "approvals_before_merge": null,
  "subscribed": true,
  "changes_count": "1",
  "latest_build_started_at": "2022-05-13T09:46:50.032Z",
  "latest_build_finished_at": null,
  "first_deployed_to_production_at": null,
  "pipeline": { // Old parameter, use `head_pipeline` instead.
    "id": 538317940,
    "iid": 1877,
    "project_id": 15513260,
    "sha": "1604b0c46c395822e4e9478777f8e54ac99fe5b9",
    "ref": "refs/merge-requests/133/merge",
    "status": "failed",
    "source": "merge_request_event",
    "created_at": "2022-05-13T09:46:39.560Z",
    "updated_at": "2022-05-13T09:47:20.706Z",
    "web_url": "https://gitlab.com/marcel.amirault/test-project/-/pipelines/538317940"
  },
  "head_pipeline": {
    "id": 538317940,
    "iid": 1877,
    "project_id": 15513260,
    "sha": "1604b0c46c395822e4e9478777f8e54ac99fe5b9",
    "ref": "refs/merge-requests/133/merge",
    "status": "failed",
    "source": "merge_request_event",
    "created_at": "2022-05-13T09:46:39.560Z",
    "updated_at": "2022-05-13T09:47:20.706Z",
    "web_url": "https://gitlab.com/marcel.amirault/test-project/-/pipelines/538317940",
    "before_sha": "1604b0c46c395822e4e9478777f8e54ac99fe5b9",
    "tag": false,
    "yaml_errors": null,
    "user": {
      "id": 4155490,
      "username": "marcel.amirault",
      "name": "Marcel Amirault",
      "state": "active",
      "avatar_url": "https://gitlab.com/uploads/-/system/user/avatar/4155490/avatar.png",
      "web_url": "https://gitlab.com/marcel.amirault"
    },
    "started_at": "2022-05-13T09:46:50.032Z",
    "finished_at": "2022-05-13T09:47:20.697Z",
    "committed_at": null,
    "duration": 30,
    "queued_duration": 10,
    "coverage": null,
    "detailed_status": {
      "icon": "status_failed",
      "text": "failed",
      "label": "failed",
      "group": "failed",
      "tooltip": "failed",
      "has_details": true,
      "details_path": "/marcel.amirault/test-project/-/pipelines/538317940",
      "illustration": null,
      "favicon": "/assets/ci_favicons/favicon_status_failed-41304d7f7e3828808b0c26771f0309e55296819a9beea3ea9fbf6689d9857c12.png"
    }
  },
  "diff_refs": {
    "base_sha": "1162f719d711319a2efb2a35566f3bfdadee8bab",
    "head_sha": "e82eb4a098e32c796079ca3915e07487fc4db24c",
    "start_sha": "1162f719d711319a2efb2a35566f3bfdadee8bab"
  },
  "merge_error": null,
  "first_contribution": false,
  "user": {
    "can_merge": true
  }
}
*/
