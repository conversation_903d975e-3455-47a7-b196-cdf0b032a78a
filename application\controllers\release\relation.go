package release

import (
	"fmt"
	"sort"
	"strings"

	"irisAdminApi/application/libs/response"
	"irisAdminApi/service/dao/release/dbranch"
	"irisAdminApi/service/dao/release/dproject"

	"github.com/kataras/iris/v12"
)

// TreeNode 表示树的节点
type TreeNode struct {
	Value    string
	Children []*TreeNode
}

// AddChild 向节点添加子节点
func (n *TreeNode) AddChild(child *TreeNode) {
	n.Children = append(n.Children, child)
}

// Print 打印树的结构
func (n *TreeNode) Print(level int) {
	fmt.Printf("%s%s\n", getIndent(level), n.Value)
	for _, child := range n.Children {
		child.Print(level + 1)
	}
}

// Print 打印树的结构
func (n *TreeNode) BuildMapData(nodes, edges *[]map[string]interface{}) {
	start := len(*nodes) - 1

	for _, child := range n.Children {
		*nodes = append(*nodes, map[string]interface{}{
			"id":       len(*nodes),
			"nodeName": child.Value,
			"shape":    "rect",
		})

		*edges = append(*edges, map[string]interface{}{
			"start": start,
			"end":   len(*nodes) - 1,
			"label": "",
		})
		child.BuildMapData(nodes, edges)
	}
}

// getIndent 根据层级返回缩进
func getIndent(level int) string {
	return strings.Repeat("  ", level) // 每层缩进两个空格
}

// BuildTree 根据输入数据构建树
func BuildTree(data map[string]string) []*TreeNode {
	trees := []*TreeNode{}
	nodes := make(map[string]*TreeNode)

	// 创建所有节点
	for current, base := range data {
		if _, exists := nodes[current]; !exists {
			nodes[current] = &TreeNode{Value: current}
		}
		if _, exists := nodes[base]; !exists {
			nodes[base] = &TreeNode{Value: base}
		}
	}

	// 构建树形结构
	for current, base := range data {
		nodes[base].AddChild(nodes[current])
	}

	// 返回根节点（假设根节点是没有基线的节点）
	for _, node := range nodes {
		if _, exists := data[node.Value]; !exists {
			trees = append(trees, node)
		}
	}
	return trees
}

func GetProjectTrees(ctx iris.Context) {
	project := ctx.FormValue("project")
	projects, err := dproject.FindAllProjcts()
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	// 解析数据
	dataMap := make(map[string]string)

	for _, project := range projects {
		if project.BaseReleaseProject != nil {
			dataMap[project.Name] = project.BaseReleaseProject.Name
		}
	}

	// 构建树
	trees := BuildTree(dataMap)
	result := map[string]interface{}{"nodes": []map[string]interface{}{}, "edges": []map[string]interface{}{}}
	// 打印树结构

	for _, root := range trees {
		nodes := []map[string]interface{}{
			{
				"id":       int(0),
				"nodeName": root.Value,
				"shape":    "rect",
			},
		}
		edges := []map[string]interface{}{}
		root.BuildMapData(&nodes, &edges)

		for _, node := range nodes {
			if node["nodeName"].(string) == project {
				result["nodes"] = nodes
				result["edges"] = edges
				ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
				return
			}
		}
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func GetProjectTreesV2(ctx iris.Context) {
	project := ctx.FormValue("project")
	projects, err := dproject.FindAllProjcts()
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	// 构建项目到基线的映射及所有项目集合
	dataMap := make(map[string]string)
	allProjects := make(map[string]bool) // 记录所有项目名称
	for _, p := range projects {
		allProjects[p.Name] = true
		if p.BaseReleaseProject != nil {
			dataMap[p.Name] = p.BaseReleaseProject.Name
		}
	}

	// 验证目标项目是否存在
	if !allProjects[project] {
		ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]interface{}{
			"nodes": []map[string]interface{}{},
			"edges": []map[string]interface{}{},
		}, response.NoErr.Msg))
		return
	}

	// 收集从目标到根节点的路径
	var path []string
	current := project
	for {
		path = append(path, current)
		parent, exists := dataMap[current]
		if !exists {
			break // 到达根节点
		}
		current = parent
	}
	// 反转路径为根到目标
	for i, j := 0, len(path)-1; i < j; i, j = i+1, j-1 {
		path[i], path[j] = path[j], path[i]
	}

	// 构建节点和边
	nodes := make([]map[string]interface{}, 0, len(path))
	edges := make([]map[string]interface{}, 0, len(path)-1)
	for idx, name := range path {
		nodes = append(nodes, map[string]interface{}{
			"id":       idx,
			"nodeName": name,
			"shape":    "rect",
		})
		if idx > 0 {
			edges = append(edges, map[string]interface{}{
				"start": idx - 1,
				"end":   idx,
				"label": "",
			})
		}
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]interface{}{
		"nodes": nodes,
		"edges": edges,
	}, response.NoErr.Msg))
}

func GetProjectTreesV3(ctx iris.Context) {
	// 解析多项目参数（假设用逗号分隔）
	projectsParam := ctx.FormValue("project")
	projectNames := strings.Split(projectsParam, ",")
	if len(projectNames) == 0 {
		ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]interface{}{
			"nodes": []map[string]interface{}{},
			"edges": []map[string]interface{}{},
		}, response.NoErr.Msg))
		return
	}

	// 获取所有项目数据
	projects, err := dproject.FindAllProjcts()
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	// 构建项目关系映射
	dataMap := make(map[string]string)
	allProjects := make(map[string]bool)
	for _, p := range projects {
		allProjects[p.Name] = true
		if p.BaseReleaseProject != nil {
			dataMap[p.Name] = p.BaseReleaseProject.Name
		}
	}

	// 验证项目有效性
	var invalidProjects []string
	for _, name := range projectNames {
		if !allProjects[name] {
			invalidProjects = append(invalidProjects, name)
		}
	}
	if len(invalidProjects) > 0 {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil,
			fmt.Sprintf("无效项目: %s", strings.Join(invalidProjects, ","))))
		return
	}

	// 收集所有相关节点和边
	nodeSet := make(map[string]bool)
	edgeSet := make(map[string]struct{})

	for _, project := range projectNames {
		current := project
		for {
			// 添加当前节点
			nodeSet[current] = true

			// 查找父节点
			parent, exists := dataMap[current]
			if !exists {
				break
			}

			// 添加父子关系边
			edgeKey := fmt.Sprintf("%s->%s", parent, current)
			edgeSet[edgeKey] = struct{}{}

			current = parent
		}
	}

	// 分离根节点和非根节点（优化显示顺序）
	var roots, nonRoots []string
	for node := range nodeSet {
		if _, exists := dataMap[node]; !exists {
			roots = append(roots, node)
		} else {
			nonRoots = append(nonRoots, node)
		}
	}

	// 排序确保ID稳定性
	sort.Strings(roots)
	sort.Strings(nonRoots)
	nodesList := append(roots, nonRoots...)

	// 创建ID映射
	idMap := make(map[string]int)
	for i, name := range nodesList {
		idMap[name] = i
	}

	// 构建响应数据
	nodes := make([]map[string]interface{}, 0, len(nodesList))
	edges := make([]map[string]interface{}, 0, len(edgeSet))

	// 生成节点
	for i, name := range nodesList {
		nodes = append(nodes, map[string]interface{}{
			"id":       i,
			"nodeName": name,
			"shape":    "rect",
		})
	}

	// 生成边
	for edge := range edgeSet {
		parts := strings.Split(edge, "->")
		source := idMap[parts[0]]
		target := idMap[parts[1]]
		edges = append(edges, map[string]interface{}{
			"start": source,
			"end":   target,
			"label": "",
		})
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]interface{}{
		"nodes": nodes,
		"edges": edges,
	}, response.NoErr.Msg))
}

func GetBranchTrees(ctx iris.Context) {
	unitpackage := ctx.FormValue("unitpackage")
	branch := ctx.FormValue("branch")
	branches, err := dbranch.FindBranchesByUnitpackage(unitpackage)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	// 解析数据
	dataMap := make(map[string]string)
	for _, branch := range branches {
		if branch.BaseBranch != "" {
			dataMap[branch.NewBranch] = branch.BaseBranch
		}
	}

	// 构建树
	trees := BuildTree(dataMap)
	result := map[string]interface{}{"nodes": []map[string]interface{}{}, "edges": []map[string]interface{}{}}
	// 打印树结构

	for _, root := range trees {
		nodes := []map[string]interface{}{
			{
				"id":       int(0),
				"nodeName": root.Value,
				"shape":    "rect",
			},
		}
		edges := []map[string]interface{}{}
		root.BuildMapData(&nodes, &edges)
		for _, node := range nodes {
			if node["nodeName"].(string) == branch {
				result["nodes"] = nodes
				result["edges"] = edges
				ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
				return
			}
		}
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func GetBranchTreesV2(ctx iris.Context) {
	unitpackage := ctx.FormValue("unitpackage")
	branch := ctx.FormValue("branch")
	branches, err := dbranch.FindBranchesByUnitpackage(unitpackage)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	// 构建项目到基线的映射及所有项目集合
	dataMap := make(map[string]string)
	allBranches := make(map[string]bool) // 记录所有项目名称
	for _, b := range branches {
		allBranches[b.NewBranch] = true
		if b.BaseBranch != "" {
			dataMap[b.NewBranch] = b.BaseBranch
		}
	}

	// 验证目标项目是否存在
	if !allBranches[branch] {
		ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]interface{}{
			"nodes": []map[string]interface{}{},
			"edges": []map[string]interface{}{},
		}, response.NoErr.Msg))
		return
	}

	// 收集从目标到根节点的路径
	var path []string
	current := branch
	for {
		path = append(path, current)
		parent, exists := dataMap[current]
		if !exists {
			break // 到达根节点
		}
		current = parent
	}
	// 反转路径为根到目标
	for i, j := 0, len(path)-1; i < j; i, j = i+1, j-1 {
		path[i], path[j] = path[j], path[i]
	}

	// 构建节点和边
	nodes := make([]map[string]interface{}, 0, len(path))
	edges := make([]map[string]interface{}, 0, len(path)-1)
	for idx, name := range path {
		nodes = append(nodes, map[string]interface{}{
			"id":       idx,
			"nodeName": name,
			"shape":    "rect",
		})
		if idx > 0 {
			edges = append(edges, map[string]interface{}{
				"start": idx - 1,
				"end":   idx,
				"label": "",
			})
		}
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]interface{}{
		"nodes": nodes,
		"edges": edges,
	}, response.NoErr.Msg))
}

func GetBranchTreesV3(ctx iris.Context) {
	unitpackage := ctx.FormValue("unitpackage")
	branchsParam := ctx.FormValue("branch")
	branchNames := strings.Split(branchsParam, ",")
	if len(branchNames) == 0 {
		ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]interface{}{
			"nodes": []map[string]interface{}{},
			"edges": []map[string]interface{}{},
		}, response.NoErr.Msg))
		return
	}

	branches, err := dbranch.FindBranchesByUnitpackage(unitpackage)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	// 构建项目关系映射
	dataMap := make(map[string]string)
	allBranches := make(map[string]bool)
	for _, b := range branches {
		allBranches[b.NewBranch] = true
		if b.BaseBranch != "" {
			dataMap[b.NewBranch] = b.BaseBranch
		}
	}

	// 验证项目有效性
	var invalidBranches []string
	for _, name := range branchNames {
		if !allBranches[name] {
			invalidBranches = append(invalidBranches, name)
		}
	}
	if len(invalidBranches) > 0 {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil,
			fmt.Sprintf("无效分支: %s", strings.Join(invalidBranches, ","))))
		return
	}

	// 收集所有相关节点和边
	nodeSet := make(map[string]bool)
	edgeSet := make(map[string]struct{})

	for _, branch := range branchNames {
		current := branch
		for {
			// 添加当前节点
			nodeSet[current] = true

			// 查找父节点
			parent, exists := dataMap[current]
			if !exists {
				break
			}

			// 添加父子关系边
			edgeKey := fmt.Sprintf("%s->%s", parent, current)
			edgeSet[edgeKey] = struct{}{}

			current = parent
		}
	}

	// 分离根节点和非根节点（优化显示顺序）
	var roots, nonRoots []string
	for node := range nodeSet {
		if _, exists := dataMap[node]; !exists {
			roots = append(roots, node)
		} else {
			nonRoots = append(nonRoots, node)
		}
	}

	// 排序确保ID稳定性
	sort.Strings(roots)
	sort.Strings(nonRoots)
	nodesList := append(roots, nonRoots...)

	// 创建ID映射
	idMap := make(map[string]int)
	for i, name := range nodesList {
		idMap[name] = i
	}

	// 构建响应数据
	nodes := make([]map[string]interface{}, 0, len(nodesList))
	edges := make([]map[string]interface{}, 0, len(edgeSet))

	// 生成节点
	for i, name := range nodesList {
		nodes = append(nodes, map[string]interface{}{
			"id":       i,
			"nodeName": name,
			"shape":    "rect",
		})
	}

	// 生成边
	for edge := range edgeSet {
		parts := strings.Split(edge, "->")
		source := idMap[parts[0]]
		target := idMap[parts[1]]
		edges = append(edges, map[string]interface{}{
			"start": source,
			"end":   target,
			"label": "",
		})
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]interface{}{
		"nodes": nodes,
		"edges": edges,
	}, response.NoErr.Msg))
}
