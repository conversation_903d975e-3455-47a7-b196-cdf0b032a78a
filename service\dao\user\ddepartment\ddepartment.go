package ddepartment

import (
	"errors"
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/user"
)

const ModelName = "用户组管理"

type Department struct {
	ID        uint   `json:"id"`
	Name      string `json:"name"`
	ParentID  uint   `json:"parent_id"`
	Link      string `json:"link"`
	UpdatedAt string `json:"updated_at"`
	CreatedAt string `json:"created_at"`
}

type ListResponse struct {
	Department
}

type DepartmentReq struct {
	Name     string `json:"name" `
	ParentID uint   `json:"parent_id"`
	Link     string `json:"link"`
}

func (u *Department) ModelName() string {
	return ModelName
}

func Model() *user.Department {
	return &user.Department{}
}

func (this *Department) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var groups []*ListResponse
	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&groups).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": groups, "total": count, "limit": pageSize}
	return list, nil
}

func (this *Department) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var groups []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&groups).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": groups, "total": count, "limit": pageSize}
	return list, nil
}

func (this *Department) FindByUserName(username string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("name = ?", username).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user by username ", username, " err ", err)
		return err
	}
	return nil
}

func (this *Department) Create(object map[string]interface{}) error {
	if link, ok := object["Link"].(string); ok {
		err := this.FindEx("link", link)
		if err != nil {
			logging.ErrorLogger.Errorf("create user find by username get err ", err)
			return err
		}

		if this.ID > 0 {
			return errors.New(fmt.Sprintf("已经存在%s， 请确认", link))
		}
	}

	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Department) CreateV2(object interface{}) error {
	return nil
}

func (this *Department) Update(id uint, object map[string]interface{}) error {
	err := this.Find(id)
	if err != nil {
		return err
	}

	if name, ok := object["Name"].(string); ok {
		err := this.FindByUserName(name)
		if err != nil {
			logging.ErrorLogger.Errorf("create user find by name get err ", err)
			return err
		}

		if this.ID > 0 && this.ID != id {
			return errors.New(fmt.Sprintf("name %s is being used", name))
		}
	}
	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).UpdateColumns(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *Department) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *Department) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *Department) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func FindById(id uint) (Department, error) {
	var department Department
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(&department).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return department, err
	}
	return department, nil
}

func FindAll() ([]*Department, error) {
	var departments []*Department
	err := easygorm.GetEasyGormDb().Model(Model()).Find(&departments).Order("link asc").Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return departments, err
	}
	return departments, nil
}
