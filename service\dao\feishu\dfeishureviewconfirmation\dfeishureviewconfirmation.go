package dfeishureviewconfirmation

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models"
	"irisAdminApi/application/models/feishu"
)

const ModelName = "飞书评委评审结束确认表"

type Response struct {
	models.ModelBase
	DocID           uint64 `gorm:"type:bigint(20);notNull" json:"doc_id" `
	PmsDocID        uint64 `gorm:"type:bigint(20);notNull" json:"pms_doc_id" `
	ProjectName     string `gorm:"type:varchar(200);notNull" json:"project_name"`
	DocumentName    string `gorm:"type:varchar(200);notNull" json:"document_name" `
	JudgeID         string `gorm:"type:varchar(200)" json:"judge_id" `
	RecordID        string `gorm:"type:varchar(200)" json:"record_id" `
	Confirmation    string `gorm:"type:varchar(200)" json:"confirmation" `
	DocumentUrl     string `gorm:"type:varchar(200)" json:"document_url" `
	DocumentOwnerID string `gorm:"type:varchar(200)" json:"document_owner_id"`
}

type ConfirmationPMSDoc struct {
	PmsDocID    uint64 `gorm:"type:bigint(20)" json:"pms_doc_id" `
	ProjectName string `gorm:"type:varchar(200)" json:"project_name"`
}

type ListResponse struct {
	Response
}

type Request struct {
	Id uint `json:"id"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *feishu.FeishuReviewConfirmation {
	return &feishu.FeishuReviewConfirmation{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (a *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) CreateV3(object *Response) error {

	db := easygorm.GetEasyGormDb().Model(Model())
	err := db.Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (u *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (u *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindByNameAndProjectName(name, projectName string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("name = ? and project_name=?", name, projectName).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func FindByProjectNameAndDocID(projectName, judgeID string, PmsDocID uint) (Response, error) {
	var res Response
	err := easygorm.GetEasyGormDb().Model(Model()).Where("project_name = ? and pms_doc_id=? and judge_id=?", projectName, PmsDocID, judgeID).Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return res, err
	}
	return res, nil
}

func GetDocumentAllConfirmation() ([]*ConfirmationPMSDoc, error) {
	items := []*ConfirmationPMSDoc{}
	sql := fmt.Sprintf(`
	SELECT pms_doc_id,project_name  from
	(SELECT *
		FROM feishu_review_confirmations AS outer_tbl
		WHERE confirmation = '确认'
		AND NOT EXISTS (
			SELECT 1
			FROM feishu_review_confirmations AS inner_tbl
			WHERE inner_tbl.pms_doc_id = outer_tbl.pms_doc_id
			AND inner_tbl.project_name = outer_tbl.project_name
			AND inner_tbl.confirmation <> '确认'
		)) as confirmationData
	group  by pms_doc_id,project_name
`)
	err := easygorm.GetEasyGormDb().Table("feishu_review_confirmations").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func UpdateByProjectNameAndDocID(projectName string, PmsDocID uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("project_name = ? and pms_doc_id=? ", projectName, PmsDocID).
		Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func GetConfirmationByProjectNameAndDocID(projectName string, PmsDocID uint) ([]Response, error) {
	var res []Response
	err := easygorm.GetEasyGormDb().Model(Model()).Where("project_name = ? and pms_doc_id=? ", projectName, PmsDocID).
		Find(res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return res, err
	}
	return res, nil
}

func GetUnConfirmationData() ([]Response, error) {
	var res []Response
	err := easygorm.GetEasyGormDb().Model(Model()).Where("confirmation <> ?", "确认").Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return res, err
	}
	return res, nil
}
