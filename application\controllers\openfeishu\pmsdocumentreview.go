package openfeishu

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/http/cookiejar"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/feishu/dfeishupmsmilestonedata"
	"irisAdminApi/service/dao/feishu/dfeishupmsprojectlistdata"
	"irisAdminApi/service/dao/feishu/dfeishupmsreviewdata"

	"github.com/go-rod/rod"
	"github.com/go-rod/rod/lib/launcher"
	"github.com/go-rod/rod/lib/proto"
)

// 配置URL
const (
	reviewUrl = "http://pms.ruijie.com.cn/review/load_review_list.jhtml?isReview=1&reviewType=&entityId="
)

func GetPMSReviewData(projectNames []string) {
	l := launcher.MustNewManaged("") // 远程调用docker容器内浏览器
	browser := rod.New().Client(l.MustClient()).MustConnect()
	// browser := rod.New().MustConnect()//连接本地浏览器
	defer browser.MustClose()
	// 初始化浏览器
	page := setupPage(browser)
	// 登录PMS系统
	err := logIn(page, libs.Config.FeiShuDoc.PmsUser, libs.Config.FeiShuDoc.PmsPass, PMSLoginURL)
	if err != nil {
		fmt.Println("登录PMS系统失败：", err)
		logging.ErrorLogger.Errorf("登录PMS系统失败：", err)
		return
	}
	// 获取评审数据
	projectListData, err := dfeishupmsprojectlistdata.AllProjectListData()
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		return
	}

	projectNameIdMap := map[string]int{}
	projectIdNameMap := map[int]string{}
	for _, item := range projectListData {
		projectNameIdMap[item.ProjectName] = item.ProjectID
		projectIdNameMap[item.ProjectID] = item.ProjectName
	}

	for _, projectName := range projectNames {
		if projectID, ok := projectNameIdMap[projectName]; ok {
			reviewData := fetchReveiwData(page, projectID)
			err = processReviewListData(reviewData)
			if err != nil {
				logging.ErrorLogger.Errorf("处理评审数据失败", err)
				continue
			}
			fmt.Println("处理评审数据成功")
			time.Sleep(5 * time.Second)
		}
	}
	// SyncFeiShuMilestoneDatasV4(projectIdNameMap)
}

func fetchReveiwData(page *rod.Page, projectID int) []*ReviewData {
	var data []*ReviewData
	page.Race().Element("#sessionInfoDiv").MustHandle(func(e *rod.Element) {
		cookies, err := proto.NetworkGetAllCookies{}.Call(page)
		if err != nil {
			logging.ErrorLogger.Errorf("获取Cookie失败：", err)
			return
		}
		jar, _ := cookiejar.New(nil)
		client := &http.Client{
			Jar: jar,
		}
		storeCookiesInJar(jar, cookies)
		data = fetchReviewFromURL(client, reviewUrl, projectID)
	}).Element(".error-msg").MustHandle(func(e *rod.Element) {
		log.Println(e.MustText())
	}).MustDo()
	return data
}

func fetchReviewFromURL(client *http.Client, _url string, projectId int) []*ReviewData {
	reviews := []*ReviewData{}
	// page=14&rows=50&sort=projectName&order=desc&productLineId=26&projectName=
	// url = fmt.Sprintf("%s&page=1&rows=2000&sort=projectName&order=desc&productLineId=26&projectName=", url)
	page := 1
	for {
		_url = fmt.Sprintf("%s&projectId=%d&page=%d&rows=1000", _url, projectId, page)
		req, _ := http.NewRequest("POST", _url, nil)

		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
		resp, err := client.Do(req)
		if err != nil {
			logging.ErrorLogger.Errorf("发送请求失败：", err)
			return nil // 返回空的AnalysisData
		}
		defer resp.Body.Close()
		body, _ := io.ReadAll(resp.Body)
		if resp.StatusCode == 200 {
			ret := parseReviewDataResponseBody(body)
			reviews = append(reviews, ret.Rows...)
			if len(reviews) >= ret.Total {
				break
			}
		} else {
			logging.ErrorLogger.Errorf(_url, string(body))
		}
		page++
	}

	return reviews
}

func parseReviewDataResponseBody(body []byte) ReviewListData {
	var data ReviewListData
	if err := json.Unmarshal(body, &data); err != nil {
		logging.ErrorLogger.Errorf(err.Error())
	}
	return data
}

/*
"acceptContent": "接受",
"acceptStatus": "ACCEPT",
"applicantUserName": "林亮",
"chargeUserCasUserId": "linl",
"chargeUserName": "林亮",
"closeDate": 1741844554450,
"closeStatus": true,
"createDate": 1741844554343,
"disabled": false,
"documentCategoryName": "FRD",
"documentChargeName": "林亮",
"entityId": 1210196,
"entityName": "SBG_SDWAN-FRD.docx",
"firstSubmit": false,
"flawType": "编码(基础问题)",
"formCode": "RF20250313239",
"id": 1049158,
"isValidReview": 1,
"modifyDate": 1741844554450,
"projectCalcAttribute": "特定客户新增需求的临时版本(PS)",
"projectId": 110866
"projectName": "NTOS1.0R10P6S1",
"questionType": "缺陷",
"reviewContent": "FRD是以模块为单位，那里可以写当前项目ID，或 模块名",
"reviewDescription": "<p><img src=\"http://resource.ruijie.com.cn:8080/pms_image/ueditor/jsp/upload/image/20250313/1741844546514090501.png\" title=\"1741844546514090501.png\" alt=\"image.png\"/></p>",
"reviewFormId": 857244,
"reviewObjectId": 976974,
"reviewStatus": "PASS",
"reviewSubject": "【SBG_SDWAN-FRD.docx】评审",
"reviewType": 11,
"reviewUserCasUserId": "weify",
"reviewUserDisabled": false,
"reviewUserName": "魏逢一",
"severityLevel": "一般",
"source": "线下意见",
*/

func processReviewListData(reviewData []*ReviewData) error {
	if len(reviewData) > 0 {
		items := []map[string]interface{}{}
		for _, item := range reviewData {
			// 将dataType1导入数据库
			data := map[string]interface{}{
				"CreatedAt":            time.Now(),
				"UpdatedAt":            time.Now(),
				"AcceptContent":        item.AcceptContent,
				"AcceptStatus":         item.AcceptStatus,
				"ApplicantUserName":    item.ApplicantUserName,
				"ChargeUserCasUserID":  item.ChargeUserCasUserID,
				"ChargeUserName":       item.ChargeUserName,
				"CloseDate":            item.CloseDate,
				"CloseStatus":          item.CloseStatus,
				"CreateDate":           item.CreateDate,
				"Disabled":             item.Disabled,
				"DocumentCategoryName": item.DocumentCategoryName,
				"DocumentChargeName":   item.DocumentChargeName,
				"EntityID":             item.EntityID,
				"EntityName":           item.EntityName,
				"FirstSubmit":          item.FirstSubmit,
				"FlawType":             item.FlawType,
				"FormCode":             item.FormCode,
				"ID":                   item.ID,
				"IsValidReview":        item.IsValidReview,
				"ModifyDate":           item.ModifyDate,
				"ProjectCalcAttribute": item.ProjectCalcAttribute,
				"ProjectID":            item.ProjectID,
				"ProjectName":          item.ProjectName,
				"QuestionType":         item.QuestionType,
				"ReviewContent":        item.ReviewContent,
				"ReviewDescription":    item.ReviewDescription,
				"ReviewFormID":         item.ReviewFormID,
				"ReviewObjectID":       item.ReviewObjectID,
				"ReviewStatus":         item.ReviewStatus,
				"ReviewSubject":        item.ReviewSubject,
				"ReviewType":           item.ReviewType,
				"ReviewUserCasUserID":  item.ReviewUserCasUserID,
				"ReviewUserDisabled":   item.ReviewUserDisabled,
				"ReviewUserName":       item.ReviewUserName,
				"SeverityLevel":        item.SeverityLevel,
				"Source":               item.Source,
			}
			items = append(items, data)
		}
		err := dfeishupmsreviewdata.CreateOrUpdateReviewData(items)
		if err != nil {
			return err
		}

	}
	return nil
}

func SyncFeiShuReviewDatasV4(projectIdNameMap map[int]string) {
	appToken := "ZfoVbWxqTaI09Rs3Ml1cRzRCnuf"
	tableID := "tbl6PbcP7pIRhbCG"

	// 获取本地数据

	milestoneData, err := dfeishupmsmilestonedata.All()
	if err != nil {
		logging.ErrorLogger.Errorf("GetAnalysisDatas error:%s", err.Error())
		return
	}

	projectIdDataMap := map[int][]*dfeishupmsmilestonedata.ListResponse{}

	for _, item := range milestoneData {
		if _, ok := projectIdDataMap[item.ProjectID]; ok {
			projectIdDataMap[item.ProjectID] = append(projectIdDataMap[item.ProjectID], item)
		} else {
			projectIdDataMap[item.ProjectID] = []*dfeishupmsmilestonedata.ListResponse{item}
		}
	}
	records := []map[string]interface{}{}
	for projectId, items := range projectIdDataMap {
		rec := map[string]interface{}{
			"项目": projectIdNameMap[projectId],
		}
		for _, item := range items {
			SetMileStoneColumnAndValue(&rec, item.MilestoneName, item.Disabled, item.PlanDate, item.ActDate, item.Status, item.ReviewStatus)
		}
		records = append(records, rec)
		// 将数据按照批次插入飞书数据表,每批次500条
	}

	// 批量删除线上数据
	DeleteTableRecordDataV4(appToken, tableID, []string{"项目"})
	page := 1
	pageSize := 500

	for {
		start := (page - 1) * pageSize
		end := page * pageSize
		if end > len(records) {
			end = len(records)
		}

		tableRecordResp, err := BatchCreate(tableID, appToken, records[start:end])
		if err != nil {
			logging.ErrorLogger.Errorf("GetAnalysisDatas batchCreate error:%s", err.Error(), string(tableRecordResp.RawBody))
		}

		// 检查是否还有更多数据

		time.Sleep(300 * time.Millisecond)
		if page*pageSize > len(records) {
			break
		}
		page++
	}
}
