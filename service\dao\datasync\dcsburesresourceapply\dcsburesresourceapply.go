package dcsburesresourceapply

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strings"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/datasync"
	"irisAdminApi/service/dao/datasync/dsyncrecord"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "分摊资源计划申请表"

type ResourceApplySyncResponse struct {
	State   string                   `json:"state"`
	Data    []*ResourceApplyResponse `json:"data"`
	Total   int                      `json:"total"`
	Message string                   `json:"message"`
}

type ResourceApplyTime struct {
	time.Time
}

func (rt *ResourceApplyTime) UnmarshalJSON(data []byte) error {
	value := strings.Trim(string(data), `\"`) // 去除两端的引号
	value = strings.Trim(string(value), `"`)
	if value == "" || value == "null" {
		return nil
	}

	var err error
	rt.Time, err = time.ParseInLocation("2006-01-02 15:04:05", value, time.Local)
	if err != nil {
		return err
	}
	return nil
}

type ResourceApplyResponse struct {
	RowNum                   int                `json:"rownum"`
	ID                       int64              `json:"id"`
	ApplyUserID              *int64             `json:"applyUserId"`
	ApplyDate                *ResourceApplyTime `json:"applyDate"`
	ApplyReason              *string            `json:"applyReason"`
	Status                   *string            `json:"status"`
	VerifyUserID             *int64             `json:"verifyUserId"`
	VerifyDate               *ResourceApplyTime `json:"verifyDate"`
	VerifyRemark             *string            `json:"verifyRemark"`
	TargetDeptID             *int64             `json:"targetDeptId"`
	TargetUserID             *int64             `json:"targetUserId"`
	TargetProjectID          *int64             `json:"targetProjectId"`
	WorkContent              *string            `json:"workContent"`
	PlanPercent              *float64           `json:"planPercent"`
	PlanMonth                *string            `json:"planMonth"`
	PlanYear                 *string            `json:"planYear"`
	PlanManMonth             *float64           `json:"planManMonth"`
	BeginTime                *ResourceApplyTime `json:"beginTime"`
	EndTime                  *ResourceApplyTime `json:"endTime"`
	PlanHour                 *float64           `json:"planHour"`
	Enable                   *int               `json:"enable"`
	CreateDate               *ResourceApplyTime `json:"createDate"`
	ModifyDate               *ResourceApplyTime `json:"modifyDate"`
	Source                   *string            `json:"source"`
	ImportKey                *string            `json:"importKey"`
	ImportUserID             *int64             `json:"importUserId"`
	ChangeReason             *string            `json:"changeReason"`
	OneDeptCode              *string            `json:"oneDeptCode"`
	TwoDeptCode              *string            `json:"twoDeptCode"`
	ThreeDeptCode            *string            `json:"threeDeptCode"`
	FourDeptCode             *string            `json:"fourDeptCode"`
	FiveDeptCode             *string            `json:"fiveDeptCode"`
	OneDeptName              *string            `json:"oneDeptName"`
	TwoDeptName              *string            `json:"twoDeptName"`
	ThreeDeptName            *string            `json:"threeDeptName"`
	FourDeptName             *string            `json:"fourDeptName"`
	FiveDeptName             *string            `json:"fiveDeptName"`
	FinalDeptCode            *string            `json:"finalDeptCode"`
	ApplyUserName            *string            `json:"applyUserName"`
	ApplyCasUserID           *string            `json:"applyCasUserId"`
	PmsProjectID             *int               `json:"pmsProjectId"`
	PmsProjectName           *string            `json:"pmsProjectName"`
	TargetUserName           *string            `json:"targetUserName"`
	TargetCasUserID          *string            `json:"targetCasUserId"`
	TargetUserBusinessDept   *string            `json:"targetUserBusinessDept"`
	TargetUserDepartmentName *string            `json:"targetUserDepartmentName"`
	TargetUserGroupName      *string            `json:"targetUserGroupName"`
	TargetDeptName           *string            `json:"targetDeptName"`
	TargetDeptCode           *string            `json:"targetDeptCode"`
}

type CsbuResResourceApply struct {
	datasync.CsbuResResourceApply
}

type ListResponse struct {
	CsbuResResourceApply
}

type Request struct {
	Id uint `json:"id"`
}

func (c *CsbuResResourceApply) ModelName() string {
	return ModelName
}

func Model() *datasync.CsbuResResourceApply {
	return &datasync.CsbuResResourceApply{}
}

func (c *CsbuResResourceApply) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *CsbuResResourceApply) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (c *CsbuResResourceApply) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (c *CsbuResResourceApply) CreateV2(object interface{}) error {
	return nil
}

func (c *CsbuResResourceApply) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (c *CsbuResResourceApply) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(c).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (c *CsbuResResourceApply) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(c).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (c *CsbuResResourceApply) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (c *CsbuResResourceApply) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(c).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (c *CsbuResResourceApply) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(c).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func BatchCreateOrUpdate(records []map[string]interface{}) error {
	xt := reflect.TypeOf(&datasync.Bug{})
	columns := []string{}
	for i := 0; i < xt.Elem().NumField(); i++ {
		key, ok := xt.Elem().Field(i).Tag.Lookup("update")
		if ok {
			columns = append(columns, key)
		}
	}
	db := easygorm.GetEasyGormDb().Model(Model())

	err := db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "bug_id"}},
		DoUpdates: clause.AssignmentColumns(columns),
	}).Create(&records).Error
	if err != nil {
		return err
	}

	return nil
}

func UpdateOrCreateResouceApplyTransaction(items []*ResourceApplyResponse, _url string, data map[string]string, method, state, errorMsg string) error {
	objects := []map[string]interface{}{}
	ids := []int64{}
	for _, item := range items {
		ids = append(ids, item.ID)
		object := map[string]interface{}{
			"ID":           item.ID,
			"ApplyUserID":  item.ApplyUserID,
			"ApplyDate":    item.ApplyDate.Time,
			"ApplyReason":  item.ApplyReason,
			"Status":       item.Status,
			"VerifyUserID": item.VerifyUserID,

			"VerifyRemark":             item.VerifyRemark,
			"TargetDeptID":             item.TargetDeptID,
			"TargetUserID":             item.TargetUserID,
			"TargetProjectID":          item.TargetProjectID,
			"WorkContent":              item.WorkContent,
			"PlanPercent":              item.PlanPercent,
			"PlanMonth":                item.PlanMonth,
			"PlanYear":                 item.PlanYear,
			"PlanManMonth":             item.PlanManMonth,
			"PlanHour":                 item.PlanHour,
			"Enable":                   item.Enable,
			"Source":                   item.Source,
			"ImportKey":                item.ImportKey,
			"ImportUserID":             item.ImportUserID,
			"ChangeReason":             item.ChangeReason,
			"OneDeptCode":              item.OneDeptCode,
			"TwoDeptCode":              item.TwoDeptCode,
			"ThreeDeptCode":            item.ThreeDeptCode,
			"FourDeptCode":             item.FourDeptCode,
			"FiveDeptCode":             item.FiveDeptCode,
			"OneDeptName":              item.OneDeptName,
			"TwoDeptName":              item.TwoDeptName,
			"ThreeDeptName":            item.ThreeDeptName,
			"FourDeptName":             item.FourDeptName,
			"FiveDeptName":             item.FiveDeptName,
			"FinalDeptCode":            item.FinalDeptCode,
			"ApplyUserName":            item.ApplyUserName,
			"ApplyCasUserID":           item.ApplyCasUserID,
			"PmsProjectID":             item.PmsProjectID,
			"PmsProjectName":           item.PmsProjectName,
			"TargetUserName":           item.TargetUserName,
			"TargetCasUserID":          item.TargetCasUserID,
			"TargetUserBusinessDept":   item.TargetUserBusinessDept,
			"TargetUserDepartmentName": item.TargetUserDepartmentName,
			"TargetUserGroupName":      item.TargetUserGroupName,
			"TargetDeptName":           item.TargetDeptName,
			"TargetDeptCode":           item.TargetDeptCode,
		}

		// "VerifyDate":               item.VerifyDate.Time,
		// "BeginTime":                item.BeginTime.Time,
		// "EndTime":                  item.EndTime.Time,
		// "CreateDate":               item.CreateDate.Time,
		// "ModifyDate":               item.ModifyDate.Time,

		if item.VerifyDate != nil {
			object["VerifyDate"] = item.VerifyDate.Time
		}
		if item.BeginTime != nil {
			object["BeginTime"] = item.BeginTime.Time
		}
		if item.EndTime != nil {
			object["EndTime"] = item.EndTime.Time
		}
		if item.CreateDate != nil {
			object["CreateDate"] = item.CreateDate.Time
		}
		if item.ModifyDate != nil {
			object["ModifyDate"] = item.ModifyDate.Time
		}

		objects = append(objects, object)
	}

	columns := []string{}

	xt := reflect.TypeOf(Model())
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")
		if ok && ok2 {
			columns = append(columns, name)
		}
	}

	body, err := json.Marshal(data)
	if err != nil {
		return err
	}
	db := easygorm.GetEasyGormDb()
	err = db.Transaction(func(tx *gorm.DB) error {
		if len(objects) > 0 {
			err := tx.Model(Model()).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "id"}},
				DoUpdates: clause.AssignmentColumns(columns),
			}).Create(&objects).Error
			if err != nil {
				return err
			}
			err = tx.Unscoped().Delete(Model(), "target_project_id = ? and id not in ?", objects[0]["TargetProjectID"], ids).Error
			if err != nil {
				return err
			}
		}

		if err := tx.Model(dsyncrecord.Model()).Create(map[string]interface{}{
			"url":             _url,
			"body":            body,
			"method":          method,
			"state":           state,
			"message":         errorMsg,
			"min_modify_date": data["minModifyDate"],
			"max_modify_date": data["maxModifyDate"],
			"created_at":      time.Now(),
		}).Error; err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}
