package featurerelease

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/featurerelease/dfeatureseccloud"
	"strings"
	"time"

	"github.com/imroc/req/v3"
)

type SecCloudConfig struct {
	WebClient *req.Client
}

var SecCloudClient = SecCloudConfig{}

func InitClient() {
	SecCloudClient.WebClient = req.C().
		SetCommonRetryCount(5).
		// Set the retry sleep interval with a commonly used algorithm: capped exponential backoff with jitter (https://aws.amazon.com/blogs/architecture/exponential-backoff-and-jitter/).
		SetCommonRetryBackoffInterval(1*time.Second, 5*time.Second).
		AddCommonRetryCondition(func(resp *req.Response, err error) bool {
			if err != nil {
				return false
			}
			commonResponse := CommonResponse{}
			unmarshalErr := json.Unmarshal([]byte(resp.String()), &commonResponse)
			if unmarshalErr != nil {
				return false
			}
			return commonResponse.Code != 0
		}).EnableInsecureSkipVerify()

	if libs.Config.Debug {
		SecCloudClient.WebClient.DevMode()
	}
}

/*
	{
	    "code": 0,
	    "msg": null,
	    "data": {
	        "accessToken": "order::10002::cd2f0669-b7ff-468c-8c54-422f95d0f35c",
	        "refreshToken": "order::10002::WLZk39ITTiBe6eC22woft4p2Ml63OhFFMCxE9v6anplxUnOGM1X6lc0_s24jfnfX-CgtB8FgMK9GZJrBBnZVqBCtiURFD7yCunb3mv2TkeIwyWmOgYJsK04nzdNsjgxP",
	        "expires": 1686034755191,
	        "username": "order",
	        "menus": [],
	        "roles": [
	            "ROLE_-2"
	        ]
	    }
	}
*/

type CommonResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"string"`
}

type AuthResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"string"`
	Data struct {
		AccessToken  string `json:"accessToken"`
		RefreshToken string `json:"refreshToken"`
		Expires      int    `json:"expires"`
		Username     string `json:"username"`
	} `json:"data"`
}

type SoftVersionsResponse struct {
	Code int      `json:"code"`
	Msg  string   `json:"msg"`
	Data []string `json:"data"`
}

type ProductModelsResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data []struct {
		ID           string `json:"id"`
		ProductModel string `json:"productModel"`
	} `json:"data"`
}

type AddFeatureVersionsResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data string `json:"data"`
}

type QueryFeatureVersionsResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"string"`
	Data struct {
		Recoreds []struct {
			ID          string `json:"id"`
			FileMd5     string `json:"fileMd5"`
			PackageType string `json:"packageType"`
			ReleaseDate string `json:"releaseDate"`
		} `json:"records"`
	} `json:"data"`
}

type DownloadUrlResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data string `json:"data"`
}

type GrayRuleData struct {
	ID               string `json:"id"`
	GrayName         string `json:"grayName"`
	GrayUpdateDate   string `json:"grayUpdateDate"`
	GrayDays         int    `json:"grayDays"`
	GrayTimes        int    `json:"grayTimes"`
	GrayDesc         string `json:"grayDesc"`
	GrayExpireStatus int    `json:"grayExpireStatus"`
	UpdateTime       string `json:"updateTime"`
	CreateTime       string `json:"createTime"`
}

type QueryGrayRulesResponse struct {
	Code int            `json:"code"`
	Msg  string         `json:"msg"`
	Data []GrayRuleData `json:"data"`
}

type AddGrayRuleResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data string `json:"data"`
}

type GrayRule struct {
	GrayName         string `json:"grayName"`
	GrayUpdateDate   string `json:"grayUpdateDate"`
	GrayDays         int    `json:"grayDays"`
	GrayTimes        int    `json:"grayTimes"`
	GrayDesc         string `json:"grayDesc"`
	GrayExpireStatus int    `json:"grayExpireStatus"`
}

func (sc *SecCloudConfig) Auth(url, username, password string) (AuthResponse, error) {
	url = fmt.Sprintf("%s/%s", strings.TrimSuffix(url, "/"), "auth/oauth2/token")
	var result AuthResponse
	var errMsg AuthResponse
	authorization := getBasicAuthorization(username, password)
	resp, err := sc.WebClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetHeader("Authorization", authorization).SetBody(map[string]string{
		"scope":     "server",
		"grantType": "clientCredentials",
		// "privacyAgreementTime": "E3uKkhqgGx5JAWw6ySuspZ7YidmbVYlibK4OvN1s1GU=",
	}).Post(url)
	if resp.IsSuccessState() {
		return result, nil
	}
	return errMsg, err
}

func (sc *SecCloudConfig) GetSoftVersions(url string) (SoftVersionsResponse, error) {

	var result SoftVersionsResponse
	var errMsg SoftVersionsResponse
	authorization, err := refreshJwtToken(url)
	if err != nil {
		return errMsg, err
	}
	url = fmt.Sprintf("%s/%s", strings.TrimSuffix(url, "/"), "version/featureManager/queryAllSoftVersion")

	resp, err := sc.WebClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetHeader("Authorization", "Bearer "+authorization).Get(url)
	if resp.IsSuccessState() {
		return result, nil
	}
	return errMsg, err
}

func (sc *SecCloudConfig) GetProductModels(url string) (ProductModelsResponse, error) {

	var result ProductModelsResponse
	var errMsg ProductModelsResponse
	authorization, err := refreshJwtToken(url)
	if err != nil {
		return errMsg, err
	}
	url = fmt.Sprintf("%s/%s", strings.TrimSuffix(url, "/"), "authorization/productModel/productModel")

	resp, err := sc.WebClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetHeader("Authorization", "Bearer "+authorization).Get(url)
	if resp.IsSuccessState() {
		return result, nil
	}
	return errMsg, err
}

func (sc *SecCloudConfig) AddFeatureVersions(url, fp string, grayId ...string) (AddFeatureVersionsResponse, error) {
	var result AddFeatureVersionsResponse
	var errMsg AddFeatureVersionsResponse
	authorization, err := refreshJwtToken(url)
	if err != nil {
		return errMsg, err
	}
	url = fmt.Sprintf("%s/%s", strings.TrimSuffix(url, "/"), "version/featureManager/addVersion")

	//增加灰度发布ID
	formData := map[string]string{}
	if len(grayId) > 0 && grayId[0] != "" {
		formData["grayId"] = grayId[0]
	}

	req := sc.WebClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetHeader("Authorization", "Bearer "+authorization).SetFile("featureFile", fp)
	if len(formData) > 0 {
		req = req.SetFormData(formData)
	}

	resp, err := req.Post(url)
	if err != nil {
		return errMsg, err
	}

	if resp.IsSuccessState() {
		if result.Code == 0 {
			return result, nil
		} else {
			return result, errors.New(result.Msg)
		}
	}
	return errMsg, err
}

func (sc *SecCloudConfig) QueryFeatureVersions(url, fn, ft, version string) (QueryFeatureVersionsResponse, error) {
	var result QueryFeatureVersionsResponse
	var errMsg QueryFeatureVersionsResponse
	authorization, err := refreshJwtToken(url)
	if err != nil {
		return errMsg, err
	}
	url = fmt.Sprintf("%s/%s", strings.TrimSuffix(url, "/"), "version/featureManager/queryVersions")

	resp, err := sc.WebClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetHeader("Authorization", "Bearer "+authorization).SetQueryParams(map[string]string{
		"featureType": ft,
		"searchParam": fn,
		"version":     version,
	}).Get(url)
	if err != nil {
		return errMsg, err
	}

	if resp.IsSuccessState() {
		return result, nil
	}
	return errMsg, err
}

func (sc *SecCloudConfig) GetDownloadUrl(url, sn, fileId string) (DownloadUrlResponse, error) {

	var result DownloadUrlResponse
	var errMsg DownloadUrlResponse
	authorization, err := refreshJwtToken(url)
	if err != nil {
		return errMsg, err
	}
	url = fmt.Sprintf("%s/%s", strings.TrimSuffix(url, "/"), "device/devInfo/collect/downloadUrl")

	resp, err := sc.WebClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetHeader("Authorization", "Bearer "+authorization).SetQueryParams(map[string]string{
		"sn":     sn,
		"fileId": fileId,
	}).Get(url)
	if resp.IsSuccessState() {
		return result, nil
	}
	return errMsg, err
}

func (sc *SecCloudConfig) QueryGrayRules(url string) (QueryGrayRulesResponse, error) {
	var result QueryGrayRulesResponse
	var errMsg QueryGrayRulesResponse
	authorization, err := refreshJwtToken(url)
	if err != nil {
		return errMsg, err
	}
	url = fmt.Sprintf("%s/%s", strings.TrimSuffix(url, "/"), "version/featureManager/gray/rules")

	resp, err := sc.WebClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetHeader("Authorization", "Bearer "+authorization).Get(url)
	if err != nil {
		return errMsg, err
	}
	if resp.IsSuccessState() {
		return result, nil
	}
	return errMsg, err
}

func (sc *SecCloudConfig) AddGrayRule(url, grayName, grayUpdateDate string, grayDays, grayTimes int, grayDesc string, grayExpireStatus int) (AddGrayRuleResponse, error) {
	var result AddGrayRuleResponse
	var errMsg AddGrayRuleResponse

	authorization, err := refreshJwtToken(url)
	if err != nil {
		return errMsg, err
	}

	grayRule := GrayRule{
		GrayName:         grayName,
		GrayUpdateDate:   grayUpdateDate,
		GrayDays:         grayDays,
		GrayTimes:        grayTimes,
		GrayDesc:         grayDesc,
		GrayExpireStatus: grayExpireStatus,
	}

	url = fmt.Sprintf("%s/%s", strings.TrimSuffix(url, "/"), "version/featureManager/gray/save")

	jsonData, err := json.Marshal(grayRule)
	if err != nil {
		return errMsg, err
	}

	resp, err := sc.WebClient.R().
		SetSuccessResult(&result).
		SetErrorResult(&errMsg).
		SetHeader("Authorization", "Bearer "+authorization).
		SetHeader("Content-Type", "application/json").
		SetBody(jsonData).
		Post(url)
	fmt.Println(resp)
	if err != nil {
		return errMsg, err
	}

	if resp.IsSuccessState() {
		if result.Code == 0 {
			return result, nil
		} else {
			return result, errors.New(result.Msg)
		}
	}
	return errMsg, err
}

func getBasicAuthorization(username, password string) string {
	return "Basic " + base64.StdEncoding.EncodeToString(
		[]byte(username+":"+password))
}

func refreshJwtToken(url string) (string, error) {
	secCloud := dfeatureseccloud.Response{}
	err := secCloud.FindEx("url", url)
	if err != nil {
		return "", err
	}
	if secCloud.ID == 0 {
		return "", errors.New("%s 安全云配置不存在")
	}
	accessToken := secCloud.AccessToken
	if int64(secCloud.Expires) < time.Now().UnixNano()/1e6 {
		auth, err := SecCloudClient.Auth(url, secCloud.Username, secCloud.Password)
		if err != nil {
			secCloud.Update(secCloud.ID, map[string]interface{}{
				"AccessToken":  "",
				"Expires":      0,
				"RefreshToken": "",
			})
			logging.ErrorLogger.Errorf("login seccloud %s failed", url)
			return "", err
		}
		secCloud.Update(secCloud.ID, map[string]interface{}{
			"AccessToken":  auth.Data.AccessToken,
			"Expires":      auth.Data.Expires,
			"RefreshToken": auth.Data.RefreshToken,
		})
		return auth.Data.AccessToken, nil
	}
	return accessToken, nil
}
