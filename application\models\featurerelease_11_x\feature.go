package featurerelease_11_x

import "irisAdminApi/application/models"

type Feature_11_X struct {
	models.ModelBase
	FileName           string `gorm:"not null; type:varchar(200)"`
	FileSize           uint   `gorm:"not null"`
	FileMd5            string `gorm:"not null; type:varchar(200)"`
	Version            string `gorm:"not null; type:varchar(60)"`
	ProductModels      string
	SoftVersions       string
	FeatureType        string `gorm:"not null; type:varchar(60)"`
	FeatureVersions    string
	FeatureBaseVersion string `gorm:"type:varchar(60)"`
	FileType           uint   `gorm:"not null"`
	ReleaseDate        string `gorm:"not null; type:varchar(60)"`
	VersionDesc        string `gorm:"not null"`
	Sign               string
	Uuid               string `gorm:"not null; type:varchar(60)"`
	Status             uint   `gorm:"not null,default:0"` //0:失败 1:成功
	UpdateToMain       bool   `gorm:"not null,default:false"`
}

type FeatureProcDef_11_X struct {
	models.ModelBase
	Name    string `json:"name,omitempty"`
	Version int    `json:"version,omitempty"`
	// 流程定义json字符串
	Resource string `gorm:"size:10000" json:"resource,omitempty"`
}

type FeatureProcInst_11_X struct {
	models.ModelBase
	// 流程定义ID
	// ProcDefID int `json:"proc_def_id"`
	// title 标题
	Title string `json:"title"`
	// 当前节点
	NodeID string `json:"node_id"`
	// 审批人
	// Candidate string `json:"candidate"`
	// 当前任务
	TaskID      uint   `json:"task_id"`
	StartUserID uint   `json:"start_user_id"`
	FeatureID   uint   `json:"feature_id"`
	Status      uint   `gorm:"not null,default:0"` //0:进行中 1:完成 2：失败
	Resource    string `gorm:"size:10000" json:"resource,omitempty"`
}

type FeatureProcTask_11_X struct {
	models.ModelBase
	// 当前执行流所在的节点
	NodeName   string `json:"nodeName"`
	PrevNodeID string `json:"prevNodeId"`
	NodeID     string `json:"nodeId"`
	// Step   int    `json:"step"`
	// 流程实例id
	ProcInstID uint `json:"procInstID"`
	Assignee   uint `json:"assignee"`
	// 还未审批的用户数，等于0代表会签已经全部审批结束，默认值为1
	// MemberCount   int8 `json:"memberCount" gorm:"default:1"`
	// UnCompleteNum int8 `json:"unCompleteNum" gorm:"default:1"`
	//审批通过数
	// AgreeNum int8 `json:"agreeNum"`
	// and 为会签，or为或签，默认为or
	// ActType    string `json:"actType" gorm:"default:'or'"`
	Comment    string `json:"comment"`
	Status     uint   `gorm:"not null,default:0"` //0:进行中 1:通过 2：转派 3:回退
	Flag       bool   `gorm:"not null,default:true"`
	Attachment string `gorm:"type:varchar(300)"`
}
