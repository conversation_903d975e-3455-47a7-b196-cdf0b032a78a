package kpi

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/kpi/dproblem"
	"irisAdminApi/service/dao/kpi/dproblemproccess"
	"irisAdminApi/service/dao/kpi/dproblemsource"
	"irisAdminApi/service/dao/user/duserdepartment"
	"irisAdminApi/service/transaction/kpi/transproblem"
	"net/url"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/xuri/excelize/v2"
)

func GetProblems(ctx iris.Context) {
	// id, err := dao.GetAuthId(ctx)
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")
	department := ctx.FormValue("department")

	// list, err := dao.All(&dproblem.ListResponse{}, ctx, name, sort, orderBy, page, pageSize)
	list, err := dproblem.AllProblems(name, sort, orderBy, page, pageSize, status, start, end, department)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetProblem(ctx iris.Context) {
	info := dproblem.Response{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

func CreateProblem(ctx iris.Context) {
	// program, err := buildfarmProject.FindProduct()
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("create release project get err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未找到工程文件，请联系管理员"))
	// 	return
	// }

	userId, _ := dao.GetAuthId(ctx)
	request := &dproblem.Request{}
	if err := ctx.ReadJSON(request); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	// delta, _ := time.ParseDuration("24*60h")
	userDepartment, err := duserdepartment.FindByUserId(request.OwnerID)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	err = dao.Create(&dproblem.Response{}, ctx, map[string]interface{}{
		"CreatedAt":       time.Now(),
		"Description":     request.Description,
		"ProblemSourceID": request.ProblemSourceID,
		"DiscoveredAt":    request.DiscoveredAt,
		"PlanCloseAt":     request.PlanCloseAt,
		"UserID":          userId,
		"Status":          0,
		"OwnerID":         request.OwnerID,
		"DepartmentID":    userDepartment.DepartmentID,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, request, response.NoErr.Msg))
	return
}

func CreateProblemProccess(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	userId, _ := dao.GetAuthId(ctx)
	request := &dproblemproccess.Request{}
	if err := ctx.ReadJSON(request); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	// delta, _ := time.ParseDuration("24*60h")
	err := dao.Create(&dproblemproccess.Response{}, ctx, map[string]interface{}{
		"CreatedAt":   time.Now(),
		"Description": request.Description,
		"ProblemID":   id,
		"UserID":      userId,
		"Status":      request.Status,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if request.Status == 1 {
		object := map[string]interface{}{
			"UpdatedAt": time.Now(),
			"Status":    request.Status,
		}

		// err = dao.Update(&drelease.Response{}, ctx, object)
		err = transproblem.UpdateProblem(userId, id, object)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, request, response.NoErr.Msg))
	return
}

func GetProblemProccessses(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dproblemproccess.AllByProblemID(id, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func UpdateProblem(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	id, _ := dao.GetId(ctx)
	request := &dproblem.Request{}
	if err := ctx.ReadJSON(request); err != nil {
		logging.ErrorLogger.Errorf("create approval read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	object := map[string]interface{}{
		"UpdatedAt":       time.Now(),
		"DiscoveredAt":    request.DiscoveredAt,
		"Description":     request.Description,
		"ProblemSourceID": request.ProblemSourceID,
		"PlanCloseAt":     request.PlanCloseAt,
		"UserID":          uId,
		"Status":          uint(0),
		"OwnerID":         request.OwnerID,
	}

	// err = dao.Update(&drelease.Response{}, ctx, object)
	err = transproblem.UpdateProblem(uId, id, object)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetProblemSources(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dproblemsource.Response{}, ctx, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetMyProblem(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")

	list, err := dproblem.AllByOwnerID(uId, name, sort, orderBy, page, pageSize, status, start, end)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetMySubmitProblem(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")

	list, err := dproblem.AllByUserID(uId, name, sort, orderBy, page, pageSize, status, start, end)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func CreateProblemProccessV2(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	userId, _ := dao.GetAuthId(ctx)
	request := &dproblemproccess.Request{}
	if err := ctx.ReadForm(request); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	filename, filepath, err := saveFile(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "附件保存异常，请重试，如果多次出现，请联系管理员。"))
		return
	}
	var fileObject map[string]interface{}
	if filename != "" && filepath != "" {
		fileObject = map[string]interface{}{
			"OriginName": filename,
			"Name":       filepath,
			"Type":       1,
		}
	}

	// err = dao.Create(&dproblemproccess.Response{}, ctx)
	err = transproblem.UpdateProblemProccess(userId, id, map[string]interface{}{
		"CreatedAt":   time.Now(),
		"Description": request.Description,
		"ProblemID":   id,
		"UserID":      userId,
		"Status":      uint(request.Status),
	}, fileObject)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, request, response.NoErr.Msg))
	return
}

func saveFile(ctx iris.Context) (string, string, error) {
	fileType := "problem"
	f, fh, err := ctx.FormFile("file")
	if err != nil {
		return "", "", nil
	}
	defer f.Close()
	ext := path.Ext(fh.Filename)

	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		return "", "", err
	}

	var uploadDir = path.Join(libs.Config.FileStorage.Upload, fileType, time.Now().Format("20060102"))
	err = os.MkdirAll(uploadDir, 0750)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		return "", "", err
	}
	os.Chmod(uploadDir, 0750)

	//构造文件名称

	fileName := libs.GetUUID() + ext

	tempFn := filepath.Join(uploadDir, fileName)
	_, err = ctx.SaveFormFile(fh, tempFn)
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		return "", "", err
	}
	return fh.Filename, strings.Replace(tempFn, path.Join(libs.Config.FileStorage.Upload, fileType), "", -1), nil
}

func ExportProblem(ctx iris.Context) {
	name := ctx.FormValue("name")
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")
	group := ctx.FormValue("group")

	// list, err := dao.All(&dproblem.ListResponse{}, ctx, name, sort, orderBy, page, pageSize)
	list, err := dproblem.AllProblems(name, sort, orderBy, 1, -1, status, start, end, group)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	fileName := fmt.Sprintf("问题记录_%s.xlsx", time.Now().Format("20060102150405"))
	file := excelize.NewFile()
	streamWriter, err := file.NewStreamWriter("Sheet1")

	// styleID, err := file.NewStyle(&excelize.Style{Font: &excelize.Font{Color: "#777777"}})
	if err != nil {
		logging.ErrorLogger.Error(err)
	}
	header := []interface{}{"描述", "责任人", "提交时间", "记录人", "状态"}
	cell, _ := excelize.CoordinatesToCellName(1, 1)
	if err := streamWriter.SetRow(cell, header); err != nil {
		logging.ErrorLogger.Error(err)
	}
	for idx, item := range list["items"].([]*dproblem.ListResponse) {
		cell, _ := excelize.CoordinatesToCellName(1, 2+idx)
		status := "未闭环"
		if item.Status == 1 {
			status = "已闭环"
		}
		row := []interface{}{item.Description, item.Owner.Name, item.DiscoveredAt, item.User.Name, status}
		if err := streamWriter.SetRow(cell, row); err != nil {
			logging.ErrorLogger.Error(err)
		}
	}
	if err := streamWriter.Flush(); err != nil {
		logging.ErrorLogger.Error(err)
	}
	if err := file.SaveAs(filepath.Join("/tmp", fileName)); err != nil {
		logging.ErrorLogger.Error(err)
	}
	ctx.SendFile(filepath.Join("/tmp", fileName), url.QueryEscape(fileName))
	return
}

func DeleteProblem(ctx iris.Context) {
	userId, _ := dao.GetAuthId(ctx)
	info := dproblem.Response{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if info.UserID != userId {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "仅允许删除自己创建的问题"))
	}
	err = dao.Delete(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}
