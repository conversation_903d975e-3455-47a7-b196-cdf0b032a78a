package gitlab

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/user/dgitlabtoken"
	"net/http"
	"strconv"

	"github.com/kataras/iris/v12"
)

func GetUserGoups(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	token := dgitlabtoken.Response{}
	err = token.FindEx("user_id", strconv.FormatUint(uint64(id), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("get token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if len(token.Token) == 0 {
		logging.ErrorLogger.Errorf("user doesn't hava token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "没有可用的gitlab token，请前往用户资料进行添加"))
		return
	}
	url := fmt.Sprintf("%s/api/%s/groups?private_token=%s&simple=true", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, token.Token)

	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))

	if page > 0 {
		url = fmt.Sprintf("%s&page=%d", url, page)
	}
	if pageSize > 0 {
		url = fmt.Sprintf("%s&per_page=%d", url, pageSize)
	}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	result, err := libs.HandlerRequest(req)
	if err != nil {
		logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func GetGroupIssues(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	groupId, err := dao.GetId(ctx)
	token := dgitlabtoken.Response{}
	err = token.FindEx("user_id", strconv.FormatUint(uint64(id), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("get token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if len(token.Token) == 0 {
		logging.ErrorLogger.Errorf("user doesn't hava token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "没有可用的gitlab token，请前往用户资料进行添加"))
		return
	}
	url := fmt.Sprintf("%s/api/%s/groups/%d/issues?private_token=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, groupId, token.Token)

	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))

	if page > 0 {
		url = fmt.Sprintf("%s&page=%d", url, page)
	}
	if pageSize > 0 {
		url = fmt.Sprintf("%s&per_page=%d", url, pageSize)
	}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	result, err := libs.HandlerRequest(req)
	if err != nil {
		logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	for _, item := range result["items"].([]map[string]interface{}) {
		if len([]byte(item["title"].(string))) > 512 {
			item["title"] = string([]byte(item["title"].(string)[0:512])) + "****(长度过长)"
		}
		if len([]byte(item["description"].(string))) > 512 {
			item["description"] = string([]byte(item["description"].(string)[0:512])) + "****(长度过长)"
		}
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}
