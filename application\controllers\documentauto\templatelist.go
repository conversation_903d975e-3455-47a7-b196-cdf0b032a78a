package documentauto

import (
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/documentauto/ddagtemplate"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

// 获取模板列表
func GetTemplateLists(ctx iris.Context) {
	//系列ID
	seriesID := ctx.FormValue("seriesID")
	seriesIDInt, _ := strconv.Atoi(seriesID)
	//版本号
	version := ctx.FormValue("version")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	docRecords, err := ddagtemplate.GetTemplateList(seriesIDInt, version, page, pageSize, orderBy, sort)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, docRecords, response.NoErr.Msg))
}

// 创建新模板
func CreateNewTemplate(ctx iris.Context) {
	//系列ID
	seriesID := ctx.FormValue("seriesID")
	seriesIDInt, _ := strconv.Atoi(seriesID)
	//版本号
	version := ctx.FormValue("version")
	//模板文件保存到本地
	wordTemplates, info, err := ctx.FormFile("template")
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	defer wordTemplates.Close()
	//验证文件大小和类型
	if err := validateFile(info, []string{".docx"}, libs.Config.DocumentAuto.MaxFileSize); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	//生成UUID
	uuid := libs.GetUUID()
	//保存到本地
	saveDir := filepath.Join(libs.Config.DocumentAuto.WordTemplatePath, time.Now().Format("20060102"), uuid)
	err = os.MkdirAll(saveDir, 0750)
	if err != nil {
		logging.ErrorLogger.Errorf("创建保存目录出错: %v", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "创建保存目录时出错"))
		return
	}

	//保存文件 文件名格式：原始文件名去掉后缀(系列ID-版本号).docx
	// fileName := fmt.Sprintf("%s(%s-%s).docx", strings.TrimSuffix(filepath.Base(info.Filename), filepath.Ext(info.Filename)), seriesID, version)
	fileName := info.Filename
	filePath, err := saveFile(wordTemplates, saveDir, fileName)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	//保存到数据库
	err = ddagtemplate.CreateTemplate(seriesIDInt, version, filePath)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, "创建新模板成功"))
}

// 下载模板
func DownloadTemplate(ctx iris.Context) {
	//模板ID
	templateID, _ := dao.GetId(ctx)
	//获取模板
	template, err := ddagtemplate.GetTemplateByID(templateID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	//下载模板
	templatePath := template.TemplatePath
	fileName := strings.ReplaceAll(path.Base(templatePath), " ", "")
	ctx.SendFile(templatePath, fileName)

}
