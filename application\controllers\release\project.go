package release

import (
	"crypto/md5"
	"fmt"
	"irisAdminApi/application/controllers/mergerequest"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/release/dbranch"
	"irisAdminApi/service/dao/release/dproject"
	"irisAdminApi/service/dao/release/dstatus"
	"irisAdminApi/service/transaction/release/transrelease"
	"math/rand"
	"os"
	"path"
	"path/filepath"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

func GetProjects(ctx iris.Context) {
	// id, err := dao.GetAuthId(ctx)
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dproject.ListResponse{}, ctx, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetProject(ctx iris.Context) {
	info := dproject.Response{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
	return
}

func CreateProject(ctx iris.Context) {
	userId, _ := dao.GetAuthId(ctx)
	request := &dproject.Request{}
	if err := ctx.ReadForm(request); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	filename, filepath, err := saveFile(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "附件保存异常，请重试，如果多次出现，请联系管理员。"))
		return
	}
	project := dproject.Response{}
	project.FindEx("name", request.Name)
	if project.ID > 0 {
		logging.ErrorLogger.Errorf("create release project get err ", err)
		ctx.JSON(response.NewResponse(response.DuplicateErr.Code, nil, "已存在相同名称项目"))
		return
	}

	project.FindEx("soft_version", request.SoftVersion)
	if project.ID > 0 {
		logging.ErrorLogger.Errorf("create release project get err ", err)
		ctx.JSON(response.NewResponse(response.DuplicateErr.Code, nil, "已存在相同软件版本号项目"))
		return
	}
	object := map[string]interface{}{
		"UserID":    userId,
		"CreatedAt": time.Now(),
	}
	t := reflect.TypeOf(*request)
	v := reflect.ValueOf(*request)
	for k := 0; k < t.NumField(); k++ {
		if t.Field(k).Type == reflect.TypeOf([]uint{}) {
			continue
		}
		object[t.Field(k).Name] = v.Field(k).Interface()
	}
	// PMO强制为李斌。暂时使用硬编码控制
	object["PmoID"] = "41"

	status := dstatus.Response{}
	err = status.FindEx("name", "申请中")
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "查找项目状态异常，请重试。"))
		return
	}

	if object["StatusID"] != status.ID {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "请设置状态为 申请中"))
		return
	}

	err = dao.Create(&dproject.Response{}, ctx, object)
	if err != nil {
		logging.ErrorLogger.Errorf("create release project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if err := project.FindEx("name", request.Name); err != nil {
		logging.ErrorLogger.Errorf("create release project ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if err := dproject.AppendAttatchment(project.ID, filename, filepath); err != nil {
		project.Delete(project.ID)
		logging.ErrorLogger.Errorf("create release project ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if err := dproject.AppendDemandSrcs(project.ID, request.DemandSrcIds); err != nil {
		project.Delete(project.ID)
		logging.ErrorLogger.Errorf("create release project ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if err := dproject.AppendNotices(project.ID, request.NoticeIds); err != nil {
		project.Delete(project.ID)
		logging.ErrorLogger.Errorf("create release project ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if err := dproject.AppendWrites(project.ID, request.WriteIds); err != nil {
		project.Delete(project.ID)
		logging.ErrorLogger.Errorf("create release project ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, project, response.NoErr.Msg))
	return
}

func saveFile(ctx iris.Context) (string, string, error) {
	f, fh, err := ctx.FormFile("file")
	if err != nil {
		return "", "", nil
	}
	defer f.Close()
	ext := path.Ext(fh.Filename)

	var uploadDir = path.Join(libs.Config.FileStorage.Upload, "sow", time.Now().Format("20060102"))
	err = os.MkdirAll(uploadDir, 0750)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		return "", "", err
	}
	os.Chmod(uploadDir, 0750)

	//构造文件名称
	rand.Seed(time.Now().UnixNano())
	randNum := fmt.Sprintf("%d", rand.Intn(9999)+1000)
	hashName := md5.Sum([]byte(time.Now().Format("2006_01_02_15_04_05_") + randNum))
	fileName := fmt.Sprintf("%x", hashName) + ext

	tempFn := filepath.Join(uploadDir, fileName)
	_, err = ctx.SaveFormFile(fh, tempFn)
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		return "", "", err
	}
	return fh.Filename, strings.Replace(tempFn, path.Join(libs.Config.FileStorage.Upload, "sow"), "", -1), nil
}

func UpdateProject(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}

	project := dproject.Response{}
	err = dao.Find(&project, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	var userIds = []uint{1, project.UserID, project.PmID, project.PmoID, project.CmaID}

	for _, write := range project.Writes {
		userIds = append(userIds, write.Id)
	}

	if !libs.InArrayUint(userIds, uId) {
		logging.ErrorLogger.Errorf("create approval read json err ", err)
		ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, "仅允许申请人、PM、CMA修改"))
		return
	}

	request := &dproject.Request{}
	if err := ctx.ReadForm(request); err != nil {
		logging.ErrorLogger.Errorf("create approval read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	if request.StatusID != project.StatusID {
		if project.CmaID != uId {
			logging.ErrorLogger.Errorf("create user read json err ", err)
			ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, "仅允许CMA修改项目状态"))
			return
		}
	}

	object := map[string]interface{}{
		"UpdatedAt": time.Now(),
	}
	t := reflect.TypeOf(*request)
	v := reflect.ValueOf(*request)
	for k := 0; k < t.NumField(); k++ {
		if t.Field(k).Type == reflect.TypeOf([]uint{}) {
			continue
		}

		object[t.Field(k).Name] = v.Field(k).Interface()
	}

	err = dao.Update(&dproject.Response{}, ctx, object)

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	filename, filepath, err := saveFile(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "附件保存异常，请重试，如果多次出现，请联系管理员。"))
		return
	}

	if err := dproject.AppendAttatchment(project.ID, filename, filepath); err != nil {
		logging.ErrorLogger.Errorf("create release project ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if err := dproject.AppendDemandSrcs(project.ID, request.DemandSrcIds); err != nil {
		logging.ErrorLogger.Errorf("create release project ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if err := dproject.AppendNotices(project.ID, request.NoticeIds); err != nil {
		logging.ErrorLogger.Errorf("create release project ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if err := dproject.AppendWrites(project.ID, request.WriteIds); err != nil {
		logging.ErrorLogger.Errorf("create release project ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetAuditedProjects(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dproject.FindAuditedProject(id, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetAuditProjects(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dproject.FindAuditProject(id, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetMyProjects(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dproject.FindMyProject(id, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetMrControlStatusAll(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	project := dproject.Response{}
	err = dao.Find(&project, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	// fmt.Println(uId)
	var userIds = []uint{1, project.PmID}
	if !libs.InArrayUint(userIds, uId) {
		logging.ErrorLogger.Errorf("create approval read json err ", err)
		ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, "无权限进行操作,仅允许PM"))
		return
	}

	projectID, _ := dao.GetId(ctx)
	//获取项目对应分支数据
	branches, err := dbranch.FindBranchByProjectIDAndStatusV2(projectID, 1)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	//获取项目对应工作包数据
	// workPackages, err := dmergerequestworkpackage.FindAllByReleaseProjectID(projectID)
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	result := map[string]interface{}{}
	result["branches"] = branches
	// result["workPackages"] = workPackages
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func ProjecMrControlStatuOpen(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	project := dproject.Response{}
	err = dao.Find(&project, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	// fmt.Println(uId)
	var userIds = []uint{1, project.PmID}
	if !libs.InArrayUint(userIds, uId) {
		logging.ErrorLogger.Errorf("create approval read json err ", err)
		ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, "无权限进行操作,仅允许PM"))
		return
	}
	projectID, _ := dao.GetId(ctx)
	err = transrelease.UpdateProjecMrControlStatuTransaction(projectID, 0)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
	}
	go mergerequest.ProcessUnmergedMRsByProjectID(projectID, 0, "")
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func ProjectMrControlStatuClose(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	project := dproject.Response{}
	err = dao.Find(&project, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	// fmt.Println(uId)
	var userIds = []uint{1, project.PmID}
	if !libs.InArrayUint(userIds, uId) {
		logging.ErrorLogger.Errorf("create approval read json err ", err)
		ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, "无权限进行操作,仅允许PM"))
		return
	}
	projectID, _ := dao.GetId(ctx)
	err = transrelease.UpdateProjecMrControlStatuTransaction(projectID, 1)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func ProjectMrControlStatuControl(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	project := dproject.Response{}
	err = dao.Find(&project, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	// fmt.Println(uId)
	var userIds = []uint{1, project.PmID}
	if !libs.InArrayUint(userIds, uId) {
		logging.ErrorLogger.Errorf("create approval read json err ", err)
		ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, "无权限进行操作,仅允许PM"))
		return
	}
	projectID, _ := dao.GetId(ctx)
	err = transrelease.UpdateProjecMrControlStatuTransaction(projectID, 2)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}
