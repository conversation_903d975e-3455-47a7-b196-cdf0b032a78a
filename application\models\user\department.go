package user

import "irisAdminApi/application/models"

type Department struct {
	models.ModelBase

	Name     string `gorm:"not null; type:varchar(200)" json:"name" validate:"required,gte=4,lte=50" comment:"名称"`
	ParentID uint   `json:"parent_id"`
	Link     string `gorm:"uniqueIndex;not null; type:varchar(200)" json:"Link" validate:"required,gte=4,lte=50" comment:"层级链，以/分隔"`
}

type UserDepartment struct {
	models.ModelBase

	UserID       uint `gorm:"primaryKey;autoIncrement:false" json:"user_id"`
	DepartmentID uint `gorm:"primaryKey;autoIncrement:false" json:"department_id"`
}
