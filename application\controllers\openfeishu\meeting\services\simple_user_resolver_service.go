package services

import (
	"fmt"
	"path/filepath"
	"strings"

	"irisAdminApi/application/controllers/openfeishu/meeting/libs"
	"irisAdminApi/application/controllers/openfeishu/meeting/models"
	"irisAdminApi/application/logging"
)

// SimpleUserResolverService 简化的用户解析服务
type SimpleUserResolverService struct {
	resolver *libs.SimpleUserResolver
}

// NewSimpleUserResolverService 创建简化的用户解析服务
func NewSimpleUserResolverService(configPath string) (*SimpleUserResolverService, error) {
	// 如果没有提供配置路径，使用默认路径
	if configPath == "" {
		configPath = filepath.Join("application", "controllers", "openfeishu", "meeting", "config", "user_mappings.yaml")
	}

	// 创建简化的用户解析器
	resolver, err := libs.NewSimpleUserResolver(configPath)
	if err != nil {
		return nil, fmt.Errorf("创建用户解析器失败: %v", err)
	}

	return &SimpleUserResolverService{
		resolver: resolver,
	}, nil
}

// ProcessAttendeesForMeeting 为会议处理参会人员
func (s *SimpleUserResolverService) ProcessAttendeesForMeeting(data *models.StandardMeetingData) error {
	if data == nil {
		return fmt.Errorf("会议数据不能为空")
	}

	// 如果没有参会人员名称，直接返回
	if data.AttendeeNames == "" {
		logging.DebugLogger.Debugf("没有参会人员需要处理, record_id: %s", data.RecordID)
		return nil
	}

	logging.InfoLogger.Infof("开始处理参会人员, record_id: %s, attendee_names: %s",
		data.RecordID, data.AttendeeNames)

	// 解析参会人员（直接使用resolver的方法）
	openIDs, failedNames, err := s.resolver.ParseAndResolveAttendees(data.AttendeeNames)
	if err != nil {
		return fmt.Errorf("解析参会人员失败: %v", err)
	}

	// 设置解析结果
	data.AttendeeOpenIDs = openIDs

	// 记录解析结果（使用统一的解析方法获取总数）
	allNames := s.parseNames(data.AttendeeNames)
	totalCount := len(allNames)
	logging.InfoLogger.Infof("参会人员处理完成, record_id: %s, total_names: %d, resolved_count: %d, failed_count: %d, failed_names: %v",
		data.RecordID, totalCount, len(openIDs), len(failedNames), failedNames)

	// 如果有失败的名称，记录到错误信息中
	if len(failedNames) > 0 {
		if data.ErrorMessage != "" {
			data.ErrorMessage += "; "
		}
		data.ErrorMessage += fmt.Sprintf("无法解析的参会人员: %v", failedNames)
	}

	return nil
}

// ResolveUserByName 根据名称解析单个用户
func (s *SimpleUserResolverService) ResolveUserByName(name string) *libs.SimpleResolveResult {
	return s.resolver.ResolveUser(name)
}

// ResolveBatchUsers 批量解析用户
func (s *SimpleUserResolverService) ResolveBatchUsers(names []string) map[string]*libs.SimpleResolveResult {
	return s.resolver.ResolveBatch(names)
}

// ValidateAttendeeNames 验证参会人员名称格式
func (s *SimpleUserResolverService) ValidateAttendeeNames(attendeeNames string) error {
	if attendeeNames == "" {
		return nil // 空值是允许的
	}

	names := s.parseNames(attendeeNames)
	if len(names) == 0 {
		return fmt.Errorf("参会人员名称格式无效")
	}

	// 检查每个名称的有效性
	for _, name := range names {
		if len(name) == 0 {
			return fmt.Errorf("参会人员名称不能为空")
		}
		if len(name) > 50 {
			return fmt.Errorf("参会人员名称过长: %s", name)
		}
	}

	// 检查总数量限制
	if len(names) > 100 {
		return fmt.Errorf("参会人员数量不能超过100人，当前: %d", len(names))
	}

	return nil
}

// AttendeeResolvePreview 参会人员解析预览
type AttendeeResolvePreview struct {
	TotalCount    int            `json:"total_count"`
	ResolvedCount int            `json:"resolved_count"`
	FailedCount   int            `json:"failed_count"`
	ResolvedUsers []ResolvedUser `json:"resolved_users"`
	FailedNames   []string       `json:"failed_names"`
}

// ResolvedUser 已解析的用户
type ResolvedUser struct {
	Name   string `json:"name"`
	OpenID string `json:"open_id"`
	Source string `json:"source"`
}

// PreviewResolveResult 预览解析结果
func (s *SimpleUserResolverService) PreviewResolveResult(attendeeNames string) (*AttendeeResolvePreview, error) {
	if attendeeNames == "" {
		return &AttendeeResolvePreview{}, nil
	}

	// 解析参会人员名称（使用统一的解析方法）
	validNames := s.parseNames(attendeeNames)
	results := s.resolver.ResolveBatch(validNames)

	preview := &AttendeeResolvePreview{
		TotalCount:    len(validNames),
		ResolvedCount: 0,
		FailedCount:   0,
		ResolvedUsers: make([]ResolvedUser, 0),
		FailedNames:   make([]string, 0),
	}

	for _, name := range validNames {
		if result, exists := results[name]; exists && result.OpenID != "" {
			preview.ResolvedCount++
			preview.ResolvedUsers = append(preview.ResolvedUsers, ResolvedUser{
				Name:   name,
				OpenID: result.OpenID,
				Source: result.Source,
			})
		} else {
			preview.FailedCount++
			preview.FailedNames = append(preview.FailedNames, name)
		}
	}

	return preview, nil
}

// parseNames 解析名称字符串（内部辅助方法）
func (s *SimpleUserResolverService) parseNames(attendeeNames string) []string {
	if attendeeNames == "" {
		return nil
	}

	// 预处理：去除前后空格
	attendeeNames = strings.TrimSpace(attendeeNames)
	if attendeeNames == "" {
		return nil
	}

	// 使用 | 分割并处理边界情况
	validNames := make([]string, 0)
	parts := strings.Split(attendeeNames, "|")

	for _, part := range parts {
		// 去除每个部分的前后空格
		trimmed := strings.TrimSpace(part)
		if trimmed != "" {
			// 进一步清理：去除内部多余空格，保持名称的完整性
			cleaned := strings.Join(strings.Fields(trimmed), " ")
			if cleaned != "" {
				validNames = append(validNames, cleaned)
			}
		}
	}

	return validNames
}

// TestResolverConnection 测试解析器连接
func (s *SimpleUserResolverService) TestResolverConnection() error {
	return s.resolver.TestConnection()
}
