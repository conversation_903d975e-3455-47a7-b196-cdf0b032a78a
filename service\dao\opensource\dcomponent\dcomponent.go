package dcomponent

import (
	"strings"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/opensource"
)

const ModelName = "开源组件表"

type User struct {
	ID       uint   `json:"id"`
	Name     string `json:"name"`
	Username string `json:"username"`
}

type OpenSourceComponent struct {
	opensource.OpenSourceComponent
	ResponsibleUsers   []*User `gorm:"many2many:open_source_component_permissions; foreignKey:ID; references:ID; joinForeignKey: ComponentID; joinReferences: UserID" json:"responsible_users"`
	ResponsibleLeaders []*User `gorm:"many2many:open_source_component_permissions; foreignKey:ID; references:ID; joinForeignKey: ComponentID; joinReferences: UserID" json:"responsible_leaders"`
}

type ListResponse struct {
	OpenSourceComponent
}

type Request struct {
	Id uint `json:"id"`
}

func (r *OpenSourceComponent) ModelName() string {
	return ModelName
}

func Model() *opensource.OpenSourceComponent {
	return &opensource.OpenSourceComponent{}
}

func (r *OpenSourceComponent) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update opensource component get err %s", err)
		return err
	}
	return nil
}

// ListComponents 根据条件分页查询开源组件列表;
func ListComponents(page, pageSize int, name, version, product, productVersion string,
	isEnable, isExternalServe *bool, sort, orderBy, createdAt, updatedAt string, userId uint) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("ResponsibleUsers", "id in (select user_id from open_source_component_permissions where type = 1)").Preload("ResponsibleLeaders", "id in (select user_id from open_source_component_permissions where type =3)")
	if userId != 0 {
		db = db.Joins("inner join open_source_component_permissions on "+
			"open_source_components.id=open_source_component_permissions.component_id and "+
			"open_source_component_permissions.deleted_at is null").
			Where("open_source_component_permissions.user_id=?", userId)
	}

	if len(name) != 0 {
		db = db.Where("open_source_components.name like ?", "%"+name+"%")
	}
	if len(version) != 0 {
		db = db.Where("open_source_components.version like ?", "%"+version+"%")
	}
	if len(product) != 0 {
		db = db.Where("open_source_components.product like ?", "%"+product+"%")
	}
	if len(productVersion) != 0 {
		// db = db.Where("open_source_components.product_version in ?", easygorm.GetEasyGormDb().Model(Model()).Distinct("product_version").Where("product_version like ?", "%"+productVersion+"%"))
		db = db.Where("open_source_components.product_version like ?", "%"+productVersion+"%")
	}
	if isEnable != nil {
		if *isEnable {
			db = db.Where("open_source_components.enable = 1")
		} else {
			db = db.Where("open_source_components.enable = 0")
		}
	}
	if isExternalServe != nil {
		if *isExternalServe {
			db = db.Where("open_source_components.is_external_serve = 1")
		} else {
			db = db.Where("open_source_components.is_external_serve = 0")
		}
	}

	if len(createdAt) > 0 {
		array := strings.Split(createdAt, ",")
		db = db.Where("open_source_components.created_at between ? and ?", array[0], array[1])
	}
	if len(updatedAt) > 0 {
		array := strings.Split(updatedAt, ",")
		db = db.Where("open_source_components.updated_at between ? and ?", array[0], array[1])
	}
	if len(orderBy) == 0 {
		orderBy = "open_source_components.id"
	}
	err := db.Select("count(distinct(open_source_components.id))").Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("count opensource component get err %s", err.Error())
		return nil, err
	}
	if userId != 0 {
		db = db.Select("distinct open_source_components.*")
	} else {
		db = db.Select("*")
	}
	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("list opensource component get err %s", err.Error())
		return nil, err
	}

	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func FindById(id uint) (*OpenSourceComponent, error) {
	var res []*OpenSourceComponent

	err := easygorm.GetEasyGormDb().Model(Model()).Preload("ResponsibleUsers", "id in (select user_id from open_source_component_permissions where type = 1)").Preload("ResponsibleLeaders", "id in (select user_id from open_source_component_permissions where type =3)").
		Where("id=?", id).Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find opensource component by id get err %s", err.Error())
		return nil, err
	}
	if len(res) == 0 {
		return nil, nil
	}
	return res[0], nil
}

// func getComponentsUsers(components []*ListResponse) {
// 	for _, component := range components {
// 		_ = GetComponentUsers(&component.OpenSourceComponent)
// 	}
// }

// func GetComponentUsers(res *OpenSourceComponent) error {
// 	var responsibleUsers []*OpenSourceComponent
// 	var responsibleLeaders []*OpenSourceComponent

// 	permissions, err := dcomponentpermission.ListPermissionsByComponentId(res.ID)
// 	if err != nil {
// 		return err
// 	}
// 	for _, per := range permissions {
// 		if per.Type == opensource.ResponsibleUserPermissionType {
// 			responsibleUsers = append(responsibleUsers, &UserInfo{UserId: per.UserID, Username: per.Username})
// 		} else if per.Type == opensource.ResponsibleLeaderPermissionType {
// 			responsibleLeaders = append(responsibleLeaders, &UserInfo{UserId: per.UserID, Username: per.Username})
// 		}
// 	}
// 	res.ResponsibleUsers = responsibleUsers
// 	res.ResponsibleLeaders = responsibleLeaders
// 	return nil
// }

func ListAllComponents() ([]*OpenSourceComponent, error) {
	var res []*OpenSourceComponent

	if err := easygorm.GetEasyGormDb().Model(Model()).Select("id").Find(&res).Error; err != nil {
		logging.ErrorLogger.Errorf("list all opensource component get err: %s", err.Error())
		return nil, err
	} else {
		return res, nil
	}
}
