package ddagdocument

import (
	"encoding/json"
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/documentauto"
)

const ModelName = "文档数据表"

type Response struct {
	documentauto.DagDocument
}

type ListResponse struct {
	Response
}

type Request struct {
	TaskID                   uint    `gorm:"not null" json:"task_id"`
	SeriesID                 uint    `gorm:"not null" json:"series_id"`
	VersionNumber            string  `gorm:"not null; type:varchar(300)" json:"version_number"`
	BuildRecordIDs           string  `gorm:"not null; type:varchar(300)" json:"build_record_ids"`
	BuildName                string  `gorm:"not null; type:varchar(300)" json:"build_name"`
	ProductModel             string  `gorm:"not null; type:varchar(300)" json:"product_model"`
	SoftwareVersion          string  `gorm:"not null; type:varchar(300)" json:"software_version"`
	ReleaseType              string  `gorm:"not null; type:varchar(300)" json:"release_type"`
	BaselineVersion          string  `gorm:"not null; type:varchar(300)" json:"baseline_version"`
	ScreenshotPath           string  `gorm:"not null; type:varchar(300)" json:"screenshot_path"`
	TemplatePath             string  `gorm:"not null; type:varchar(300)" json:"template_path"`
	DocumentDownloadURLWord  string  `gorm:"not null; type:varchar(300)" json:"document_download_url_word"`
	DocumentDownloadURLExcel string  `gorm:"not null; type:varchar(300)" json:"document_download_url_excel"`
	DocumentVersionNumber    float64 `gorm:"not null" json:"document_version_number"`
	Status                   uint    `gorm:"not null; default:0" json:"status"`
	UserID                   uint    `gorm:"not null" json:"user_id"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *documentauto.DagDocument {
	return &documentauto.DagDocument{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (a *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func GetDocumentByTaskID(taskID uint) ([]*Response, error) {
	var docResponse []*Response
	err := easygorm.GetEasyGormDb().Model(Model()).Where("task_id = ?", taskID).Find(&docResponse).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get document by task id err ", err)
		return nil, err
	}
	return docResponse, nil
}

func UpdateDocument(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update document err ", err)
		return err
	}
	return nil
}

// 文档复合数据包含文档数据和系列信息数据
type DocumentCompositeData struct {
	ID                       uint    `json:"id"`
	TaskID                   uint    `json:"task_id"`
	SeriesID                 uint    `json:"series_id"`
	VersionNumber            string  `json:"version_number"`
	BuildName                string  `json:"build_name"`
	ProductModel             string  `json:"product_model"`
	SoftwareVersion          string  `json:"software_version"`
	ReleaseType              string  `json:"release_type"`
	BaselineVersion          string  `json:"baseline_version"`
	BuildRecordIDs           string  `json:"build_record_ids"`
	ScreenshotPath           string  `json:"screenshot_path"`
	TemplatePath             string  `json:"template_path"`
	DocumentDownloadURLWord  string  `json:"document_download_url_word"`
	DocumentDownloadURLExcel string  `json:"document_download_url_excel"`
	DocumentVersionNumber    float64 `json:"document_version_number"`
	Status                   uint    `json:"status"`
	UserID                   uint    `json:"user_id"`
	SeriesName               string  `json:"series_name"`
	Title1                   string  `json:"title1"`
	Title2                   string  `json:"title2"`
	VersionPrefix            string  `json:"version_prefix"`
	ReleaseReason            string  `json:"release_reason"`
	DocName                  string  `json:"doc_name"`
	DocType                  uint    `json:"doc_type"`
	Username                 string  `json:"username"`
}

func (d DocumentCompositeData) MarshalJSON() ([]byte, error) {
	type Alias DocumentCompositeData
	return json.Marshal(&struct {
		DocumentVersionNumber string `json:"document_version_number"`
		*Alias
	}{
		DocumentVersionNumber: fmt.Sprintf("%.1f", d.DocumentVersionNumber),
		Alias:                 (*Alias)(&d),
	})
}

// 获取文档复合数据
func GetDocumentCompositeData(taskID uint) ([]*DocumentCompositeData, error) {
	items := []*DocumentCompositeData{}
	sql := fmt.Sprintf(`
SELECT 
	dd.id,
	dd.task_id,
	dd.series_id,
	dd.version_number,
	dd.build_record_ids,
	dd.build_name,
	dd.product_model,
	dd.software_version,
	dd.release_type,
	dd.baseline_version,
	dd.screenshot_path,
	dd.template_path,
	dd.document_download_url_word,
	dd.document_download_url_excel,
	dd.document_version_number,
	dd.status,
	dd.user_id,
	ds.name as series_name,
	ds.title1,
	ds.title2,
	ds.version_prefix,
	dd.release_reason,
	ds.doc_name,
	ds.doc_type,
	u.username
FROM
	dag_documents dd
LEFT JOIN dag_series as ds ON
	dd.series_id = ds.id
LEFT JOIN users as u ON
	dd.user_id = u.id
	WHERE
	dd.task_id = %d;
		`, taskID)
	err := easygorm.GetEasyGormDb().Table("dag_documents").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

// 根据文档ID获取文档信息
func GetDocumentByID(id uint) (*Response, error) {
	var item *Response
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(&item).Error
	return item, err
}

// 获取文档列表
func GetDocumentList(taskID uint, page, pageSize int, orderBy, sort string) (map[string]interface{}, error) {
	var count int64
	items := []*DocumentCompositeData{}
	//dag_documents as dd
	db := easygorm.GetEasyGormDb().Table("dag_documents AS dd").
		Select("dd.*, ds.name as series_name, u.username").
		Joins("LEFT JOIN dag_series as ds ON dd.series_id = ds.id").
		Joins("LEFT JOIN users as u ON dd.user_id = u.id")
	if taskID != 0 {
		db = db.Where("dd.task_id = ?", taskID)
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}
	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}
