package dvulnerabilityhistory

import (
	"strings"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/opensource"
	"irisAdminApi/service/dao/opensource/dvulnerability"
	"irisAdminApi/service/dao/user/duser"
)

const ModelName = "漏洞处理记录表"

type OpenSourceVulnerabilityHistory struct {
	opensource.OpenSourceVulnerabilityHistory
	Vulnerablility    dvulnerability.OpenSourceVulnerability `gorm:"->" json:"vulnerablility"`
	DelegatedUsername string                                 `gorm:"-" json:"delegated_username"`
}

type ListResponse struct {
	OpenSourceVulnerabilityHistory
}

type Request struct {
	Id uint `json:"id"`
}

func (this *OpenSourceVulnerabilityHistory) ModelName() string {
	return ModelName
}

func Model() *opensource.OpenSourceVulnerabilityHistory {
	return &opensource.OpenSourceVulnerabilityHistory{}
}

func ListVulnerabilityHistories(page, pageSize int, componentName, componentVersion, productName, productVersion string, vulnerabilityId,
	status uint, sort, orderBy, createdAt, updatedAt string, userId uint, processUserId uint) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("Vulnerablility")
	if userId != 0 {
		db = db.Joins("inner join open_source_vulnerability_permissions on "+
			"open_source_vulnerability_permissions.vulnerability_id=open_source_vulnerability_histories.vulnerability_id and "+
			"open_source_vulnerability_permissions.deleted_at is null").
			Where("open_source_vulnerability_permissions.user_id=?", userId)
	}

	if processUserId != 0 {
		db = db.Where("open_source_vulnerability_histories.user_id=?", processUserId)
	}
	if len(componentName) != 0 {
		db = db.Where("open_source_vulnerability_histories.component_name=?", componentName)
	}
	if len(componentVersion) != 0 {
		db = db.Where("open_source_vulnerability_histories.component_version=?", componentVersion)
	}
	if len(productName) != 0 {
		db = db.Where("open_source_vulnerability_histories.product_name=?", productName)
	}
	if len(productVersion) != 0 {
		db = db.Where("open_source_vulnerability_histories.product_version=?", productVersion)
	}
	if vulnerabilityId != 0 {
		db = db.Where("open_source_vulnerability_histories.vulnerability_id=?", vulnerabilityId)
	}
	if status != 0 {
		db = db.Where("open_source_vulnerability_histories.status=?", status)
	}
	if len(createdAt) > 0 {
		array := strings.Split(createdAt, ",")
		db = db.Where("open_source_vulnerability_histories.created_at between ? and ?", array[0], array[1])
	}
	if len(updatedAt) > 0 {
		array := strings.Split(updatedAt, ",")
		db = db.Where("open_source_vulnerability_histories.updated_at between ? and ?", array[0], array[1])
	}
	if len(orderBy) == 0 {
		orderBy = "open_source_vulnerability_histories.created_at"
	}
	err := db.Select("count(distinct(open_source_vulnerability_histories.id))").Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("count opensource vulnerability histories get err %s", err.Error())
		return nil, err
	}
	if userId != 0 {
		db = db.Select("distinct open_source_vulnerability_histories.*")
	} else {
		db = db.Select("*")
	}
	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("list vulnerability histories get err %s", err.Error())
		return nil, err
	}
	getVulnerabilityHistoriesDelegatedUsername(res)
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func GetVulnerabilityHistoryById(id, vulnerabilityId uint) (*OpenSourceVulnerabilityHistory, error) {
	var res []*OpenSourceVulnerabilityHistory
	err := easygorm.GetEasyGormDb().Model(Model()).
		Where("id=?", id).
		Where("vulnerability_id=?", vulnerabilityId).
		Find(res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get vulnerability history by history-id get err %s", err.Error())
		return nil, err
	} else if len(res) == 0 {
		return nil, nil
	}
	getVulnerabilityHistoryDelegatedUsername(res[0])
	return res[0], nil
}

func getVulnerabilityHistoriesDelegatedUsername(listRes []*ListResponse) {
	for _, res := range listRes {
		getVulnerabilityHistoryDelegatedUsername(&res.OpenSourceVulnerabilityHistory)
	}
}

func getVulnerabilityHistoryDelegatedUsername(res *OpenSourceVulnerabilityHistory) {
	if res.DelegatedUser != 0 {
		user := &duser.User{}
		err := user.Find(res.DelegatedUser)
		if err != nil {
			return
		}
		res.DelegatedUsername = user.Username
	}
}
