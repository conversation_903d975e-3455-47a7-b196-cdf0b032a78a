package libs

import (
	"fmt"
	"strings"

	"irisAdminApi/application/logging"
)

// SimpleUserResolver 简化的用户解析器
type SimpleUserResolver struct {
	userMappings map[string]string // 用户名到OpenID的映射
	normalizer   *NameNormalizer   // 名称标准化器
	legacyQuery  *LegacyUserQuery  // 数据库查询
}

// SimpleResolveResult 简化的解析结果
type SimpleResolveResult struct {
	OpenID string `json:"open_id"`
	Source string `json:"source"`
	Error  string `json:"error,omitempty"`
}

// NewSimpleUserResolver 创建简化的用户解析器
func NewSimpleUserResolver(configPath string) (*SimpleUserResolver, error) {
	// 加载配置文件中的用户映射
	userMappings := make(map[string]string)

	if configPath != "" {
		configLoader := NewConfigLoader(configPath)
		config, err := configLoader.LoadConfig()
		if err != nil {
			logging.ErrorLogger.Errorf("加载配置文件失败: %v", err)
		} else {
			// 构建用户映射表
			for _, user := range config.UserMappings.CommonUsers {
				userMappings[user.Name] = user.OpenID
				// 添加别名映射
				for _, alias := range user.Aliases {
					userMappings[alias] = user.OpenID
				}
			}
		}
	}

	// 创建名称标准化器
	normalizer := NewNameNormalizer()

	// 创建数据库查询
	legacyQuery := NewLegacyUserQuery()

	return &SimpleUserResolver{
		userMappings: userMappings,
		normalizer:   normalizer,
		legacyQuery:  legacyQuery,
	}, nil
}

// ResolveUser 解析单个用户
func (r *SimpleUserResolver) ResolveUser(name string) *SimpleResolveResult {
	if name == "" {
		return &SimpleResolveResult{
			Error: "用户名不能为空",
		}
	}

	// 标准化名称
	normalizedName := r.normalizer.Normalize(name)
	if normalizedName == "" {
		return &SimpleResolveResult{
			Error: "名称标准化失败",
		}
	}

	// 1. 首先查找配置文件映射
	if openID, exists := r.userMappings[normalizedName]; exists {
		return &SimpleResolveResult{
			OpenID: openID,
			Source: "config",
		}
	}

	// 2. 查找原始名称
	if openID, exists := r.userMappings[name]; exists {
		return &SimpleResolveResult{
			OpenID: openID,
			Source: "config",
		}
	}

	// 3. 使用数据库查询
	if r.legacyQuery != nil {
		if openID := r.legacyQuery.GetOpenIDByName(normalizedName); openID != "" {
			return &SimpleResolveResult{
				OpenID: openID,
				Source: "database",
			}
		}
	}

	// 所有方法都失败
	return &SimpleResolveResult{
		Error: fmt.Sprintf("无法找到用户: %s", name),
	}
}

// ResolveBatch 批量解析用户
func (r *SimpleUserResolver) ResolveBatch(names []string) map[string]*SimpleResolveResult {
	results := make(map[string]*SimpleResolveResult)

	for _, name := range names {
		results[name] = r.ResolveUser(name)
	}

	return results
}

// ParseAndResolveAttendees 解析参会人员字符串
func (r *SimpleUserResolver) ParseAndResolveAttendees(attendeesStr string) ([]string, []string, error) {
	if attendeesStr == "" {
		return nil, nil, nil
	}

	// 解析人员名称（使用|分割）
	names := r.parseAttendeeNames(attendeesStr)
	if len(names) == 0 {
		return nil, nil, fmt.Errorf("未找到有效的参会人员名称")
	}

	// 批量解析
	results := r.ResolveBatch(names)

	var openIDs []string
	var failedNames []string

	for _, name := range names {
		if result, exists := results[name]; exists && result.OpenID != "" {
			openIDs = append(openIDs, result.OpenID)
		} else {
			failedNames = append(failedNames, name)
		}
	}

	logging.InfoLogger.Infof("参会人员解析完成: 总数=%d, 成功=%d, 失败=%d",
		len(names), len(openIDs), len(failedNames))

	return openIDs, failedNames, nil
}

// parseAttendeeNames 解析参会人员名称字符串
func (r *SimpleUserResolver) parseAttendeeNames(attendeesStr string) []string {
	if attendeesStr == "" {
		return nil
	}

	// 预处理：去除前后空格
	attendeesStr = strings.TrimSpace(attendeesStr)
	if attendeesStr == "" {
		return nil
	}

	// 使用 | 分割并处理边界情况
	names := make([]string, 0)
	parts := strings.Split(attendeesStr, "|")

	for _, part := range parts {
		// 去除每个部分的前后空格
		trimmed := strings.TrimSpace(part)
		if trimmed != "" {
			// 进一步清理：去除内部多余空格
			cleaned := strings.Join(strings.Fields(trimmed), " ")
			if cleaned != "" {
				names = append(names, cleaned)
			}
		}
	}

	return names
}

// AddUserMapping 添加用户映射（运行时添加）
func (r *SimpleUserResolver) AddUserMapping(name, openID string) {
	if name != "" && openID != "" {
		r.userMappings[name] = openID

		// 同时添加标准化后的名称
		normalizedName := r.normalizer.Normalize(name)
		if normalizedName != "" && normalizedName != name {
			r.userMappings[normalizedName] = openID
		}
	}
}

// TestConnection 测试连接
func (r *SimpleUserResolver) TestConnection() error {
	// 测试解析一个已知用户
	result := r.ResolveUser("Rock")
	if result.Error != "" {
		logging.ErrorLogger.Errorf("测试解析失败: %s", result.Error)
	} else {
		logging.InfoLogger.Infof("测试解析成功: Rock -> %s", result.OpenID)
	}
	return nil
}
