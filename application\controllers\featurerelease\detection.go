package featurerelease

import (
	"archive/zip"
	"bytes"
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/featurerelease"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/featurerelease/ddetection"
	"math/rand"
	"net/url"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gabriel-vasile/mimetype"
	"github.com/kataras/iris/v12"
)

//创建规则检测记录
func CreateDetection(ctx iris.Context) {
	//校验用户权限
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	request := &ddetection.Request{}
	if err := ctx.ReadForm(request);err != nil{
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	}
   //接收上传文件
	f, fh, err := ctx.FormFile("file") //获取文件数据
	defer f.Close()
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	//验证后缀名是否符合要求
	ext := path.Ext(fh.Filename)
	var AllowExtMap map[string]bool = map[string]bool{
		".zip": true,
	}
	var tempDir = libs.Config.DetectionFileStorage.Temp + "detection/" + time.Now().Format("20060102") + "/"
	err = os.MkdirAll(tempDir, 0750)
	os.Chmod(tempDir, 0750)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	//上传文件放入临时目录（detection）下
	// fileOriginName := fh.Filename

	//构造文件名称
	rand.Seed(time.Now().UnixNano())
	randNum := fmt.Sprintf("%d", rand.Intn(9999)+1000)
	hashName := md5.Sum([]byte(time.Now().Format("2006_01_02_15_04_05_") + randNum))
	fileName := fmt.Sprintf("%x", hashName) + ext

	_, err = ctx.SaveFormFile(fh, filepath.Join(tempDir, fileName))//保存文件到临时目录中
	defer os.Remove(filepath.Join(tempDir, fileName)) //最后删除临时文件
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	//通过MD5值 判断检测文件是否存在
	md5 := request.FileMd5
	check := ddetection.Response{}
	err = check.FindEx("file_md5", md5)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if check.ID > 0 {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "已经存在相同文件检测记录"))
		return
	}
	//检查文件类型与文件扩展为ZIP格式
	mime, err := mimetype.DetectFile(filepath.Join(tempDir, fileName))
	if err != nil {
		logging.ErrorLogger.Errorf("Error while Check File Type ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "无法判断文件类型"))
		return
	}
	if _, ok := AllowExtMap[mime.Extension()]; !ok {
		logging.ErrorLogger.Errorf("Error while Check File Type ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "只允许上传ZIP压缩的文件"))
		return
	}
   //解压压缩包文件到指定目录
   fileUnzipDirectory := tempDir + fmt.Sprintf("%x", hashName) + "/"
   err = unzip(fileUnzipDirectory, filepath.Join(tempDir, fileName))
//    defer os.RemoveAll(tempDir + fmt.Sprintf("%x", hashName) + "/")
   if err != nil {
	   logging.ErrorLogger.Errorf("Error while Unzip File", err)
	   ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "压缩包存在问题，请重新打包后重试。"))
	   return
   }

	//创建最终存放路径以及保存文件
	var upload = filepath.Join(libs.Config.DetectionFileStorage.Upload,"zip", time.Now().Format("20060102"))
	err = os.MkdirAll(upload, 0750)
	os.Chmod(upload, 0750)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	tempFn := filepath.Join(upload, fileName)//路径拼接
	_, err = ctx.SaveFormFile(fh, tempFn)
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

    //创建检测任务单
	err = dao.Create(&ddetection.Response{}, ctx, map[string]interface{}{
		"UserID":                 id,
		"FileName":               fileName,
		"FileSize":               request.FileSize,
		"FileMd5":                request.FileMd5,
		"FileType":               request.FileType,
		"FileOriginName":         request.FileName,
		"FileUnzipDirectory":     fileUnzipDirectory,
		"CreatedAt":              time.Now(),
		"UpdatedAt":              time.Now(),
		"Status":                    0,
	})
	if err != nil {
		defer os.Remove(filepath.Join(upload, fileName)) //删除上传文件
		defer os.RemoveAll(tempDir + fmt.Sprintf("%x", hashName) + "/") //删除解压目录
		logging.ErrorLogger.Errorf("create detection Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	detectionJob := &ddetection.Response{}
	err = easygorm.GetEasyGormDb().Model(&featurerelease.DetectionList{}).Where("file_md5 = ?", request.FileMd5).Find(&detectionJob).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create detectionJob get err ", err)
	}
	//执行检测任务
	err = DetectionJob(ctx, detectionJob)
	if err != nil {
		logging.ErrorLogger.Errorf("create detectionJob err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetDetectionList(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&ddetection.ListResponse{}, ctx, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}


func GetDetectionTodoList(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := "0"
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")

	list, err := ddetection.AllDetection(uId, name, sort, orderBy, page, pageSize, status, start, end)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetDetectionDoneList(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := "1"
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")

	list, err := ddetection.AllDetection(uId, name, sort, orderBy, page, pageSize, status, start, end)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}


func GetDetectionDetail(ctx iris.Context) {
    //获取检测结果
	info := ddetection.ListResponse{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get ddetection err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	type DetailResponse struct {
		DetectionData    *ddetection.ListResponse             `json:"detection_data"`
		ResultJsonData    interface{}                          `json:"result_json_data"`
	}
	detectionDetail := DetailResponse{}
	detectionDetail.DetectionData = &info
	//获取json报告数据路径
	filePath := info.ResultJsonFile
	//读取Json数据内容
	jsonFile,err := os.Open(filePath)
	if err != nil{
		logging.ErrorLogger.Errorf("OpenJsonFile err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	//关闭文件
	defer jsonFile.Close()
	byteValue,_ := ioutil.ReadAll(jsonFile)
	var jsonData = map[string]interface{}{}
	// var jsonData interface{}
	err = json.Unmarshal(byteValue, &jsonData) //反序列json数据
	if err != nil{
		logging.ErrorLogger.Errorf("json.Unmarshal err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	detectionDetail.ResultJsonData = jsonData
	ctx.JSON(response.NewResponse(response.NoErr.Code, detectionDetail, response.NoErr.Msg))
}


func DownloadDetectionLogFile(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	detectionListData := ddetection.ListResponse{}
	err := detectionListData.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	var logFile = filepath.Join(detectionListData.ResultLogFile)
	ctx.SendFile(logFile, url.QueryEscape(detectionListData.ResultLogFileName))
	return
}

func GetDetectionTodoInfo(ctx iris.Context) {

	//获取工具目录路径
	var toolPath string
	if libs.Config.DetectionFileStorage.EnvConfig == "local"{
		toolPath = "/media/sf_code/iris-admin-api/" //工具目录
	} else{
		toolPath = libs.Config.DetectionFileStorage.ToolPath //工具目录
	}
	filePath := toolPath +"index.md"
	//读取md数据内容
	mdFile,err := os.Open(filePath)
	if err != nil{
		logging.ErrorLogger.Errorf("OpenMDFile err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	//关闭文件
	defer mdFile.Close()
	byteValue,_ := ioutil.ReadAll(mdFile)
	mdString := string(byteValue)
	ctx.JSON(response.NewResponse(response.NoErr.Code, mdString, response.NoErr.Msg))
}


func DetectionJob(ctx iris.Context, detectionjob *ddetection.Response) error {

	go func() {
		f, err := os.OpenFile(filepath.Join(libs.Config.DetectionFileStorage.LogPath, fmt.Sprintf("%d", detectionjob.ID) + ".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
		if err != nil {
			logging.ErrorLogger.Errorf("create user get err ", err)
		}
		defer f.Close()
		//拼接本地执行命令
		toolPath := libs.Config.DetectionFileStorage.ToolPath //工具目录
		absolutePathTool := toolPath +"./" + libs.Config.DetectionFileStorage.ToolName //工具绝对路径
		inputPath := detectionjob.FileUnzipDirectory //输入目录
		outputPath := libs.Config.DetectionFileStorage.OutputPath   //输出目录
		outputPathFile := libs.Config.DetectionFileStorage.OutputPath + strings.TrimSuffix(detectionjob.FileName,".zip")
		command := fmt.Sprintf("%s  --input=%s  --output=%s  --output_filename=%s\r\n", absolutePathTool,inputPath, outputPath,outputPathFile)
		//记录日志
		f.WriteString(fmt.Sprintf("执行规则库检测操作，执行命令如下:\r\n%s", command))
		//执行本地命令,接收命令执行结果
		execStatus := false
		if libs.Config.DetectionFileStorage.EnvConfig == "local"{ //本地环境无工具，使用代替方案
			cmd := exec.Command("ls")
			var stdout, stderr bytes.Buffer
			cmd.Stdout = &stdout  // 标准输出
			cmd.Stderr = &stderr  // 标准错误
			cmdErr := cmd.Run()
			outStr, _ := string(stdout.Bytes()), string(stderr.Bytes())
			// fmt.Printf("out:n%sn   err:n%s\r\n", outStr, errStr)
			if cmdErr != nil {
				logging.ErrorLogger.Errorf("cmd.Run() failed err", cmdErr)
				fmt.Printf("cmd.Run() failed err %sn",cmdErr)
			}
			f.WriteString(fmt.Sprintf("执行规则库检测操作，返回结果如下:\r\n%s",outStr))
			execStatus = strings.Contains(outStr,"application.yml.example")
		} else{
			cmd := exec.Command(absolutePathTool,fmt.Sprintf("--input=%s",inputPath),fmt.Sprintf("--output=%s",outputPath),fmt.Sprintf("--output_filename=%s",outputPathFile))
			var stdout, stderr bytes.Buffer
			cmd.Stdout = &stdout  // 标准输出
			cmd.Stderr = &stderr  // 标准错误
			cmdErr := cmd.Run()
			outStr, _ := string(stdout.Bytes()), string(stderr.Bytes())
			// fmt.Printf("out:n%sn   err:n%sn", outStr, errStr)
			// fmt.Println(outStr)
			// fmt.Println(errStr)
			if cmdErr != nil {
				logging.ErrorLogger.Errorf("cmd.Run() failed err", cmdErr)
			}
			f.WriteString(fmt.Sprintf("执行规则库检测操作，返回结果如下:\r\n%s",outStr))
			execStatus = strings.Contains(outStr,"Filter Chain rules perform analysis done")
		}
		//将结果更新到表中
		if execStatus {
			err := detectionjob.Update(detectionjob.ID, map[string]interface{}{
				"Status":    1,
				"ResultJsonFile" :  outputPathFile + ".json",
				"ResultLogFile" :  outputPathFile + ".log",
				"ResultLogFileName" :  strings.TrimSuffix(detectionjob.FileName,".zip") + ".log",
				"UpdatedAt": time.Now(),
			})
			if err != nil {
				logging.ErrorLogger.Errorf("update detection job err ", err)
			}
			//检测状态更新
			resultDetectionDeal(detectionjob.FileMd5)
		}else{
			err :=detectionjob.Update(detectionjob.ID, map[string]interface{}{
				"Status":    2,
				"UpdatedAt": time.Now(),
			})
			if err != nil {
				logging.ErrorLogger.Errorf("update detection job err ", err)
			}
		}
		
	}()
	return nil

}

func unzip(pathname, fpath string) error {
	err := os.MkdirAll(pathname, 0750)
	os.Chmod(pathname, 0750)
	if err != nil {
		return err
	}
	r, err := zip.OpenReader(fpath)
	if err != nil {
		return errors.New("系统存在问题，请联系开发！")
	}
	defer r.Close()
	for _, k := range r.Reader.File {
		if k.FileInfo().IsDir() {
			err := os.MkdirAll(pathname+k.Name, 0750)
			os.Chmod(pathname, 0750)
			if err != nil {
				return errors.New("系统存在问题，请联系开发！")
			}
			continue
		}

		r, err := k.Open()
		if err != nil {
			return errors.New("请勿使用加密压缩包或压缩包已损坏，请检查")
		}
		defer r.Close()
		NewFile, err := os.Create(pathname + k.Name)
		if err != nil {
			return err
		}
		io.Copy(NewFile, r)
		NewFile.Close()
	}
	return nil
}

//检测脚本运行结果
func resultDetectionDeal(FileMd5 string){
	detectionJob := &ddetection.Response{}
	err := easygorm.GetEasyGormDb().Model(&featurerelease.DetectionList{}).Where("file_md5 = ?",FileMd5).Find(&detectionJob).Error
	if err != nil {
		logging.ErrorLogger.Errorf("detectionJob get err ", err)
	}
	//获取json报告数据路径
	filePath := detectionJob.ResultJsonFile
	//读取Json数据内容
	jsonFile,err := os.Open(filePath)
	if err != nil{
		logging.ErrorLogger.Errorf("OpenJsonFile err ", err)
		return
	}
	//关闭文件
	defer jsonFile.Close()
	byteValue,_ := ioutil.ReadAll(jsonFile)
	var jsonData = map[string]interface{}{}
	// var jsonData interface{}
	err = json.Unmarshal(byteValue, &jsonData) //反序列json数据
	detectionStatus := true
	for _, v := range jsonData["filters"].([]interface{}) {
		if v.(map[string]interface{})["level"] =="Error"{
			if len(v.(map[string]interface{})["hit_rules"].([]interface{})) >0{
				detectionStatus = false
				break
			}
		}
    }
	if detectionStatus{
		err := detectionJob.Update(detectionJob.ID, map[string]interface{}{
			"DetectionStatus":    1,
			"UpdatedAt": time.Now(),
		})
		if err != nil {
			logging.ErrorLogger.Errorf("update detection job err ", err)
		}
	}else{
		err :=detectionJob.Update(detectionJob.ID, map[string]interface{}{
			"DetectionStatus":    2,
			"UpdatedAt": time.Now(),
		})
		if err != nil {
			logging.ErrorLogger.Errorf("update detection job err ", err)
		}
	}
}
