package coredump

import "time"

// CoredumpRecord Coredump记录
type CoredumpRecord struct {
	RecordID             string    `json:"record_id"`
	SN                   string    `json:"sn"`
	Component            string    `json:"component"`
	SoftwareVersion      string    `json:"software_version"`
	DeviceModel          string    `json:"device_model"`
	CoredumpTime         time.Time `json:"coredump_time"`
	ComponentResponsible string    `json:"component_responsible"`
	ProcessResponsible   string    `json:"process_responsible"`
	Description          string    `json:"description"`
	SyncRequired         string    `json:"sync_required"`
	SyncStatus           string    `json:"sync_status"`
	ProcessingStatus     string    `json:"processing_status"`
	BugID                string    `json:"bug_id"`
	ProcessingTime       time.Time `json:"processing_time"`
	ErrorMessage         string    `json:"error_message"`
	RetryCount           int       `json:"retry_count"`
	LastUpdated          time.Time `json:"last_updated"`
}

// BugData Bug提交数据
type BugData struct {
	Summary      string                 `json:"summary"`
	Description  string                 `json:"description"`
	IssueType    string                 `json:"issue_type"`
	Priority     string                 `json:"priority"`
	ProjectKey   string                 `json:"project_key"`
	Reporter     string                 `json:"reporter"`
	Assignee     string                 `json:"assignee"`
	Components   string                 `json:"components"`
	Labels       []string               `json:"labels"`
	CustomFields map[string]interface{} `json:"custom_fields"`
}

// ProcessingStatus 处理状态枚举
type ProcessingStatus string

// SyncStatus 同步状态枚举
type SyncStatus string

// 注意：状态常量已在config.go中定义，避免重复声明
