package datasync

import (
	"strings"
	"sync"

	"irisAdminApi/application/controllers/openfeishu"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"

	"github.com/kataras/iris/v12"
)

var BugSyncLock sync.Mutex

func StartBugSync(ctx iris.Context) {
	if BugSyncLock.TryLock() {
		defer BugSyncLock.Unlock()
		err := BugSyncManualWorker(1)
		if err != nil {
			ctx.StatusCode(500)
			ctx.WriteString(err.Error())
			return
		}

		err = BugLogSyncWorker(1)
		if err != nil {
			ctx.StatusCode(500)
			ctx.WriteString(err.Error())
			return
		}
	} else {
		ctx.StatusCode(500)
		ctx.WriteString("sync is running")
		return
	}

	ctx.StatusCode(200)
}

type UpdateBugCafIDRequest struct {
	CasID     string `json:"cas_id"`
	CafID     string `json:"caf_id"`
	BugIDList string `json:"bug_id_list"`
}

func UpdateBugCafID(ctx iris.Context) {
	req := UpdateBugCafIDRequest{}
	if err := ctx.ReadJSON(&req); err != nil {
		logging.ErrorLogger.Errorf("create gitjob read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(&req)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		logging.ErrorLogger.Errorf("create gitjob read json err ", strings.Join(errs, ";"))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	url := "http://bugs.ruijie.com.cn/bug_switch/interface/bugInfoIF"
	resp, err := SyncClient.R().SetFormData(map[string]string{
		"casId":     req.CasID,
		"cafId":     req.CafID,
		"bugIdList": req.BugIDList,
		"type":      "updateCafId",
	}).Post(url)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if resp.IsErrorState() {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, resp.String()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, resp.String()))
	return
}

func StartSyncFeishuBaseData(ctx iris.Context) {
	ResourceCleanWorker()
	openfeishu.SyncAllOriginalResourceDataV4()
	// go SyncFeisuBaseData()
	// openfeishu.GetPMSProjectListData()
	// projectNames := openfeishu.SyncAllSoftProjectDataV4()

	// ctx.WriteString(strings.Join(projectNames, ","))
	// openfeishu.GetPMSReviewData([]string{"12.6RPJ20"})
	ctx.StatusCode(200)
	return
}
