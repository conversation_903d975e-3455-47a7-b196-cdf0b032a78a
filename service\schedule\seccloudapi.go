package schedule

import (
	"irisAdminApi/application/controllers/featurerelease"
	"time"
)

func RunRefreshSoftVersions() {
	t := time.NewTicker(12 * time.Hour)
	featurerelease.RefreshSoftVersionsFromSecCloud()
	go func() {
		// for {
		// 	select {
		// 	case <-t.C:
		// 		featurerelease.RefreshSoftVersionsFromSecCloud()
		// 	}
		// }
		for range t.C {
			featurerelease.RefreshSoftVersionsFromSecCloud()
		}
	}()
}
