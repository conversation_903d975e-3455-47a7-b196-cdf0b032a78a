package openfeishu

import (
	"encoding/json"
)

// 定义第一种数据结构
type DataType1 struct {
	BsIrDefect  float64 `json:"bs_ir_defect"`
	StUtDefect  float64 `json:"st_ut_defect"`
	StIrDefect  float64 `json:"st_ir_defect"`
	SDdDefect   float64 `json:"s_dd_defect"`
	SSdDefect   float64 `json:"s_sd_defect"`
	SRaDefect   float64 `json:"s_ra_defect"`
	StIgDefect  float64 `json:"st_ig_defect"`
	SCsDefect   float64 `json:"s_cs_defect"`
	IsGoalRow   bool    `json:"isGoalRow"`
	STdDefect   float64 `json:"s_td_defect"`
	ProjectName string  `json:"projectName"`
	ProjectId   uint    `json:"projectId"`
}

// 定义第二种数据结构
type DataType2 struct {
	IsStatRow     bool    `json:"isStatRow"`
	BsTtA         float64 `json:"bs_tt_a"`
	WpStCount     float64 `json:"wp_st_count"`
	StIr0         float64 `json:"st_ir0"`
	SDdDefect     float64 `json:"s_dd_defect"`
	StIr1         float64 `json:"st_ir1"`
	CodeTransTemp float64 `json:"codeTransTemp"`
	StIr2         float64 `json:"st_ir2"`
	BsIgHDefect   float64 `json:"bs_ig_h_defect"`
	CodeTrans     float64 `json:"codeTrans"`
	SSdCount      float64 `json:"s_sd_count"`
	CodeNew       float64 `json:"codeNew"`
	STdCount      float64 `json:"s_td_count"`
	WpStDefect    float64 `json:"wp_st_defect"`
	SCsRCount     float64 `json:"s_cs_r_count"`
	SDdCount      float64 `json:"s_dd_count"`
	SSdDefect     float64 `json:"s_sd_defect"`
	STdDefect     float64 `json:"s_td_defect"`
	StUt0         float64 `json:"st_ut0"`
	StUtDefect    float64 `json:"st_ut_defect"`
	SRaCount      float64 `json:"s_ra_count"`
	BsIgA         float64 `json:"bs_ig_a"`
	BsTtADefect   float64 `json:"bs_tt_a_defect"`
	CodeTotal     float64 `json:"codeTotal"`
	SCsBCount     float64 `json:"s_cs_b_count"`
	SRaDefect     float64 `json:"s_ra_defect"`
	SCsCount      float64 `json:"s_cs_count"`
	BsIrDefect    float64 `json:"bs_ir_defect"`
	BsIrCount     float64 `json:"bs_ir_count"`
	StIrDefect    float64 `json:"st_ir_defect"`
	BsIgADefect   float64 `json:"bs_ig_a_defect"`
	StIg0         float64 `json:"st_ig0"`
	BsIgH         float64 `json:"bs_ig_h"`
	StIgDefect    float64 `json:"st_ig_defect"`
	SCsDefect     float64 `json:"s_cs_defect"`
	ProjectName   string  `json:"projectName"`
	ProjectId     uint    `json:"projectId"`
}

// 定义其余数据的结构
type DataTypeRest struct {
	StIr0             float64 `json:"st_ir0"`
	CodeTransTemp     float64 `json:"codeTransTemp"`
	StIr1             float64 `json:"st_ir1"`
	BsIgHDefect       float64 `json:"bs_ig_h_defect"`
	StIr2             float64 `json:"st_ir2"`
	WorkPacketName    string  `json:"workPacketName"`
	CodeTrans         float64 `json:"codeTrans"`
	SRa0              float64 `json:"s_ra0"`
	SCbDc3            float64 `json:"s_cb_dc3"`
	SCbDc4            float64 `json:"s_cb_dc4"`
	STdDefect         float64 `json:"s_td_defect"`
	BsTtADefect       float64 `json:"bs_tt_a_defect"`
	SRaDefect         float64 `json:"s_ra_defect"`
	SCsBCount         float64 `json:"s_cs_b_count"`
	SSd0              float64 `json:"s_sd0"`
	StIrDefect        float64 `json:"st_ir_defect"`
	BsIrCount         float64 `json:"bs_ir_count"`
	SSd1              float64 `json:"s_sd1"`
	BsIgADefect       float64 `json:"bs_ig_a_defect"`
	StIg0             float64 `json:"st_ig0"`
	StIr1Defect       float64 `json:"st_ir1_defect"`
	SCsDefect         float64 `json:"s_cs_defect"`
	ProjectName       string  `json:"projectName"`
	ProjectId         uint    `json:"projectId"`
	BsTtA             float64 `json:"bs_tt_a"`
	WpStCount         float64 `json:"wp_st_count"`
	SDdDefect         float64 `json:"s_dd_defect"`
	CodeNew           float64 `json:"codeNew"`
	SSdCount          float64 `json:"s_sd_count"`
	STdCount          float64 `json:"s_td_count"`
	SCr0              float64 `json:"s_cr0"`
	WpStDefect        float64 `json:"wp_st_defect"`
	SCr2              float64 `json:"s_cr2"`
	SCsRCount         float64 `json:"s_cs_r_count"`
	SCr1              float64 `json:"s_cr1"`
	SSdDefect         float64 `json:"s_sd_defect"`
	SDdCount          float64 `json:"s_dd_count"`
	SCr3              float64 `json:"s_cr3"`
	WorkPacketId      int     `json:"workPacketId"`
	StUt0             float64 `json:"st_ut0"`
	StUtDefect        float64 `json:"st_ut_defect"`
	CodeTotal         float64 `json:"codeTotal"`
	BsIgA             float64 `json:"bs_ig_a"`
	SRaCount          float64 `json:"s_ra_count"`
	SDd1              float64 `json:"s_dd1"`
	SDd0              float64 `json:"s_dd0"`
	SCbDc0            float64 `json:"s_cb_dc0"`
	SCbDc1            float64 `json:"s_cb_dc1"`
	SDd2              float64 `json:"s_dd2"`
	SCbDc2            float64 `json:"s_cb_dc2"`
	OBs0              float64 `json:"o_bs0"`
	SCsCount          float64 `json:"s_cs_count"`
	BsIrDefect        float64 `json:"bs_ir_defect"`
	STd1              float64 `json:"s_td1"`
	STd0              float64 `json:"s_td0"`
	WorkPacketManager string  `json:"workPacketManager"`
	BsIgH             float64 `json:"bs_ig_h"`
	StIgDefect        float64 `json:"st_ig_defect"`
	PSTLUserName      string  `json:"pstlUserName"`
}

// 定义一个通用的结构来解析数据
type AnalysisData struct {
	Data []json.RawMessage `json:"data"`
}

// 定义一个通用的结构来解析数据
type MilestoneData struct {
	ID                   int      `json:"id"`
	ProjectID            int      `json:"projectId" update:"1"`
	Orders               int      `json:"orders" update:"1"`
	MileStoneValue       string   `gorm:"type:varchar(255)" json:"mileStoneValue" update:"1"`
	MilestoneName        string   `gorm:"type:varchar(255)" json:"milestoneName" update:"1"`
	LatestReviewFormCode string   `gorm:"type:varchar(255)" json:"latestReviewForm_code" update:"1"`
	ReviewStatus         string   `gorm:"type:varchar(255)" json:"reviewStatus" update:"1"`
	CreateDate           uint64   `json:"createDate" update:"1"`
	ModifyDate           uint64   `json:"modifyDate" update:"1"`
	PlanDate             uint64   `json:"planDate" update:"1"`
	ActDate              uint64   `json:"actDate" update:"1"`
	Disabled             bool     `json:"disabled" update:"1"`
	ChangeNum            *float64 `json:"changeNum" update:"1"`
	Status               string   `gorm:"type:varchar(255)" json:"status" update:"1"`
	FinishedTaskNums     *float64 `json:"finishedTaskNums" update:"1"`
	Progress             *float64 `json:"progress" update:"1"`
	TaskNums             *float64 `json:"taskNums" update:"1"`
}

type ProjectData struct {
	ProjectName   string `json:"projectName"`
	ProjectID     int    `json:"projectId"`
	ProjectStatus string `json:"projectStatus"`
}

type ProjectListData struct {
	Rows  []*ProjectData `json:"rows"`
	Total int            `json:"total"`
}

/*
	{

                "acceptContent": "接受",
                "acceptStatus": "ACCEPT",
                "applicantUserName": "林亮",
                "chargeUserCasUserId": "linl",
                "chargeUserId": 6307,
                "chargeUserName": "林亮",
                "closeDate": 1741844554450,
                "closeStatus": true,
                "createDate": 1741844554343,
                "disabled": false,
                "documentCategoryName": "FRD",
                "documentChargeName": "林亮",
                "documentChargeUserId": 6307,
                "entityId": 1210196,
                "entityName": "SBG_SDWAN-FRD.docx",
                "firstSubmit": false,
                "flawType": "编码(基础问题)",
                "formCode": "RF20250313239",
                "id": 1049158,
                "isValidReview": 1,
                "modifyDate": 1741844554450,
                "projectCalcAttribute": "特定客户新增需求的临时版本(PS)",
                "projectId": 110866
                "projectName": "NTOS1.0R10P6S1",
                "questionType": "缺陷",
                "reviewContent": "FRD是以模块为单位，那里可以写当前项目ID，或 模块名",
                "reviewDescription": "<p><img src=\"http://resource.ruijie.com.cn:8080/pms_image/ueditor/jsp/upload/image/20250313/1741844546514090501.png\" title=\"1741844546514090501.png\" alt=\"image.png\"/></p>",
                "reviewFormId": 857244,
                "reviewObjectId": 976974,
                "reviewStatus": "PASS",
                "reviewSubject": "【SBG_SDWAN-FRD.docx】评审",
                "reviewType": 11,
                "reviewUserCasUserId": "weify",
                "reviewUserDisabled": false,
                "reviewUserId": 724,
                "reviewUserName": "魏逢一",
                "rowNumber": 1,
                "severityLevel": "一般",
                "source": "线下意见",

	},
*/
type ReviewListData struct {
	Rows  []*ReviewData `json:"rows"`
	Total int           `json:"total"`
}

type ReviewData struct {
	AcceptContent        string `json:"acceptContent"`
	AcceptStatus         string `json:"acceptStatus"`
	ApplicantUserName    string `json:"applicantUserName"`
	ChargeUserCasUserID  string `json:"chargeUserCasUserId"`
	ChargeUserName       string `json:"chargeUserName"`
	CloseDate            uint64 `json:"closeDate"`
	CloseStatus          bool   `json:"closeStatus"`
	CreateDate           uint64 `json:"createDate"`
	Disabled             bool   `json:"disabled"`
	DocumentCategoryName string `json:"documentCategoryName"`
	DocumentChargeName   string `json:"documentChargeName"`
	EntityID             int    `json:"entityId"`
	EntityName           string `json:"entityName"`
	FlawType             string `json:"flawType"`
	FirstSubmit          bool   `json:"firstSubmit"`
	FormCode             string `json:"formCode"`
	ID                   int    `json:"id"`
	IsValidReview        int    `json:"isValidReview"`
	ModifyDate           uint64 `json:"modifyDate"`
	ProjectCalcAttribute string `json:"projectCalcAttribute"`
	ProjectID            int    `json:"projectId"`
	ProjectName          string `json:"projectName"`
	QuestionType         string `json:"questionType"`
	ReviewContent        string `json:"reviewContent"`
	ReviewDescription    string `json:"reviewDescription"`
	ReviewFormID         int    `json:"reviewFormId"`
	ReviewObjectID       int    `json:"reviewObjectId"`
	ReviewStatus         string `json:"reviewStatus"`
	ReviewSubject        string `json:"reviewSubject"`
	ReviewType           int    `json:"reviewType"`
	ReviewUserCasUserID  string `json:"reviewUserCasUserId"`
	ReviewUserDisabled   bool   `json:"reviewUserDisabled"`
	ReviewUserID         int    `json:"reviewUserId"`
	ReviewUserName       string `json:"reviewUserName"`
	RowNumber            int    `json:"rowNumber"`
	SeverityLevel        string `json:"severityLevel"`
	Source               string `json:"source"`
}
