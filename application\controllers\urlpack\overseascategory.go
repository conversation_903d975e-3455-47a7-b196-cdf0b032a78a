package urlpack

import (
	"archive/zip"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strconv"
	"strings"

	"github.com/kataras/iris/v12"
	"github.com/xuri/excelize/v2"
)

func ImportOverseasCategoryAndCreateConfFiles(ctx iris.Context) {
	file, _, err := ctx.FormFile("file")

	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	defer file.Close()

	f, err := excelize.OpenReader(file)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	defer f.Close()

	// 获取目标目录
	outputDir := libs.Config.UrlPack.OverseasDir

	// 确保目录存在
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error creating output directory: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "创建输出目录失败，请重试。"+err.Error()))
		return
	}

	// 创建日志文件
	logFile, err := os.OpenFile(filepath.Join(outputDir, "generate.log"), os.O_RDWR|os.O_CREATE, 0666)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error creating log file: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "创建日志文件失败，请重试。"+err.Error()))
		return
	}
	defer logFile.Close()

	// 处理"NTOS-URL海外"工作表
	categorys, err := processNTOSURLSheet(f, logFile)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	// 直接使用categorys生成配置文件到指定目录
	err = createConfigFilesFromMap(categorys, outputDir, logFile)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error generating config files: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "生成配置文件失败，请重试。"+err.Error()))
		return
	}

	// 处理"BC-自研分类映射表"工作表
	err = processBCMappingSheet(f, outputDir, logFile)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	// 返回成功信息
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, "配置文件已成功生成"))
	return
}

// 处理"NTOS-URL海外"工作表
func processNTOSURLSheet(f *excelize.File, logFile *os.File) ([]map[string]interface{}, error) {
	// 查找名为"NTOS-URL海外"的工作表
	sheetName := ""
	for _, sheet := range f.GetSheetList() {
		if sheet == "NTOS-URL海外" {
			sheetName = sheet
			break
		}
	}

	if sheetName == "" {
		return nil, fmt.Errorf("导入的分类文件不存在'NTOS-URL海外'表格")
	}

	// 获取工作表上所有单元格
	rows, err := f.GetRows(sheetName)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		return nil, fmt.Errorf("处理excel失败，请重试。%s", err.Error())
	}
	if len(rows) < 2 {
		return nil, fmt.Errorf("导入的分类文件没有有效内容")
	}

	templateHeaders := []string{"分组编号", "分组中文名", "分组英文名", "分类编号", "分类中文名", "分类英文名", "中文描述", "英文描述", "海外大库", "海外小库", "大库", "小库", "预定义阻断类"}
	for i := range templateHeaders {
		if rows[0][i] != templateHeaders[i] {
			return nil, fmt.Errorf("导入的分类文件不是有效的文件,表头字段(%s),系统表头字段(%s),请检查", rows[0][i], templateHeaders[i])
		}
	}

	length := len(rows[0])
	var categorys []map[string]interface{}
	regexStr := "[`" + `~!#%^$&*+|{};:"/\\<>?]`
	pattern := regexp.MustCompile(regexStr)

	// 创建一个集合来存储所有的分类英文名
	categoryEnNames := make(map[string]bool)

	for rowNum := range rows[1:] {
		rowData := []string{}
		for col := 0; col < length; col++ {
			cell := getCellRef(col, rowNum+1)
			value, _ := f.GetCellValue(sheetName, cell)
			rowData = append(rowData, value)
		}
		if len(pattern.FindStringIndex(rowData[6])) > 0 {
			errStr := []string{}
			for _, idx := range pattern.FindStringIndex(rowData[6]) {
				errStr = append(errStr, rowData[idx])
			}
			return nil, fmt.Errorf("导入数据失败, %s分类的中文描述包含以下字符%s", rowData[4], strings.Join(errStr, ","))
		} else if len(pattern.FindStringIndex(rowData[7])) > 0 {
			errStr := []string{}
			for _, idx := range pattern.FindStringIndex(rowData[6]) {
				errStr = append(errStr, rowData[idx])
			}
			return nil, fmt.Errorf("导入数据失败，%s分类的英文描述包含以下字符%s", rowData[4], strings.Join(errStr, ","))
		}

		// 将分类英文名添加到集合中
		categoryEnNames[rowData[5]] = true

		category := map[string]interface{}{
			"GroupID":        rowData[0],
			"GroupNameCH":    rowData[1],
			"GroupNameEN":    rowData[2],
			"CategoryID":     rowData[3],
			"CategoryNameCH": rowData[4],
			"CategoryNameEN": rowData[5],
			"DescriptionCH":  rowData[6],
			"DescriptionEN":  rowData[7],
			"OverseaLarge":   CheckBool(rowData[8]),
			"OverseaSmall":   CheckBool(rowData[9]),
			"Large":          CheckBool(rowData[10]),
			"Small":          CheckBool(rowData[11]),
			"PreDefBlock":    CheckBool(rowData[12]),
		}

		categorys = append(categorys, category)
	}

	// 验证BC映射表中的自研分类英文名
	bcSheetName := "BC-自研分类映射表"
	//验证BC映射表是否存在
	if _, err := f.GetRows(bcSheetName); err != nil {
		return nil, fmt.Errorf("导入的分类文件不存在'BC-自研分类映射表'表格")
	}

	if bcRows, err := f.GetRows(bcSheetName); err == nil && len(bcRows) > 1 {
		for i, row := range bcRows[1:] {
			if len(row) >= 3 {
				selfCategoryEN := strings.TrimSpace(row[2])
				if selfCategoryEN != "" && !categoryEnNames[selfCategoryEN] {
					return nil, fmt.Errorf("BC映射表第%d行的自研分类英文名'%s'在NTOS-URL海外表的分类英文名中不存在", i+2, selfCategoryEN)
				}
			}
		}
	}

	return categorys, nil
}

// 处理"BC-自研分类映射表"工作表
func processBCMappingSheet(f *excelize.File, outputDir string, logFile *os.File) error {
	// 查找名为"BC-自研分类映射表"的工作表
	sheetName := ""
	for _, sheet := range f.GetSheetList() {
		if sheet == "BC-自研分类映射表" {
			sheetName = sheet
			break
		}
	}

	if sheetName == "" {
		logFile.WriteString("警告：导入的分类文件不存在'BC-自研分类映射表'表格，跳过生成category_map.json\n")
		return fmt.Errorf("导入的分类文件不存在'BC-自研分类映射表'表格")
	}

	// 获取工作表上所有单元格
	rows, err := f.GetRows(sheetName)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while processing BC mapping: %s", err.Error()))
		return fmt.Errorf("处理BC映射表失败，请重试。%s", err.Error())
	}
	if len(rows) < 2 {
		return fmt.Errorf("'BC-自研分类映射表'没有有效内容")
	}

	// 验证表头
	expectedHeaders := []string{"BC分类名", "BC分类ID", "自研分类英文名", "自研分类中文名", "自研分类ID"}
	if len(rows[0]) < len(expectedHeaders) {
		return fmt.Errorf("BC-自研分类映射表的列数不足")
	}

	for i, header := range expectedHeaders {
		if rows[0][i] != header {
			return fmt.Errorf("BC-自研分类映射表的表头不符合要求，期望：%s，实际：%s", header, rows[0][i])
		}
	}

	// 创建映射
	categoryMap := make(map[string]string)
	for _, row := range rows[1:] {
		if len(row) < 3 {
			continue // 跳过不完整的行
		}

		bcCategoryID := strings.TrimSpace(row[1])
		selfCategoryEN := strings.TrimSpace(row[2])

		if bcCategoryID != "" && selfCategoryEN != "" {
			categoryMap[bcCategoryID] = selfCategoryEN
		}
	}
	// 手动构建有序JSON
	var buf bytes.Buffer
	buf.WriteString("{\n")

	// 提取所有键并排序
	keys := []string{}
	for k := range categoryMap {
		keys = append(keys, k)
	}

	// 将键分为数字键和非数字键
	var numericKeys []int
	var nonNumericKeys []string

	for _, k := range keys {
		if n, err := strconv.Atoi(k); err == nil {
			numericKeys = append(numericKeys, n)
		} else {
			nonNumericKeys = append(nonNumericKeys, k)
		}
	}

	// 排序
	sort.Ints(numericKeys)
	sort.Strings(nonNumericKeys)

	// 先处理数字键
	firstItem := true
	for _, n := range numericKeys {
		k := strconv.Itoa(n)
		if !firstItem {
			buf.WriteString(",\n")
		}
		firstItem = false

		// 格式化带引号的键和值
		fmt.Fprintf(&buf, "  %q: %q", k, categoryMap[k])
	}

	// 再处理非数字键
	for _, k := range nonNumericKeys {
		if !firstItem {
			buf.WriteString(",\n")
		}
		firstItem = false

		fmt.Fprintf(&buf, "  %q: %q", k, categoryMap[k])
	}

	buf.WriteString("\n}")
	//存在category_map.json 先删除
	if _, err := os.Stat(filepath.Join(outputDir, "category_map.json")); err == nil {
		os.Remove(filepath.Join(outputDir, "category_map.json"))
	}
	// 写入文件
	err = os.WriteFile(filepath.Join(outputDir, "category_map.json"), buf.Bytes(), 0644)
	if err != nil {
		return fmt.Errorf("写入category_map.json失败：%s", err.Error())
	}
	return nil
}

// 直接从map创建配置文件
func createConfigFilesFromMap(categories []map[string]interface{}, dst string, f *os.File) error {
	// 创建predef_profile.conf
	predefProfileConf := []predefProfileConf{
		{
			Name:        "default",
			Desc:        "Default profile. Block unsavory.",
			DescCH:      "默认模板。阻断非法与不良。",
			Subcategory: []*predefProfileConfSubcategory{},
		},
	}

	urlCategoryConfMap := map[int]*UrlCategoryConf{}

	// 按分组编号排序
	for _, category := range categories {
		groupID, _ := strconv.Atoi(category["GroupID"].(string))
		categoryID, _ := strconv.Atoi(category["CategoryID"].(string))

		if _, ok := urlCategoryConfMap[groupID]; ok {
			subCategory := Subcategory{
				Name:   category["CategoryNameEN"].(string),
				NameCH: category["CategoryNameCH"].(string),
				ID:     groupID*512 + categoryID,
				Desc:   category["DescriptionEN"].(string),
				DescCH: category["DescriptionCH"].(string),
			}
			urlCategoryConfMap[groupID].Subcategory = append(urlCategoryConfMap[groupID].Subcategory, &subCategory)
		} else {
			subCategory := Subcategory{
				Name:   category["CategoryNameEN"].(string),
				NameCH: category["CategoryNameCH"].(string),
				ID:     groupID*512 + categoryID,
				Desc:   category["DescriptionEN"].(string),
				DescCH: category["DescriptionCH"].(string),
			}
			urlCategoryConfMap[groupID] = &UrlCategoryConf{
				Name:        category["GroupNameEN"].(string),
				NameCH:      category["GroupNameCH"].(string),
				ID:          groupID,
				Subcategory: []*Subcategory{&subCategory},
			}
		}

		if category["PreDefBlock"].(bool) {
			subcategory := predefProfileConfSubcategory{
				Name:   category["CategoryNameEN"].(string),
				Action: "block",
			}
			predefProfileConf[0].Subcategory = append(predefProfileConf[0].Subcategory, &subcategory)
		}
	}

	// 生成url_category.conf
	urlCategoryConf := []*UrlCategoryConf{}
	for k := range urlCategoryConfMap {
		urlCategoryConf = append(urlCategoryConf, urlCategoryConfMap[k])
	}
	sort.Slice(urlCategoryConf, func(i, j int) bool {
		return urlCategoryConf[i].ID < urlCategoryConf[j].ID
	})

	jsonByte, err := json.Marshal(urlCategoryConf)
	if err != nil {
		f.WriteString("marshal url category conf err" + err.Error() + "\n")
		return err
	}
	jsonStr := strings.ReplaceAll(string(jsonByte), `\n`, "") // 直接替换
	//存在url_category.conf 先删除
	if _, err := os.Stat(filepath.Join(dst, "url_category.conf")); err == nil {
		os.Remove(filepath.Join(dst, "url_category.conf"))
	}

	conf, err := os.OpenFile(filepath.Join(dst, "url_category.conf"), os.O_RDWR|os.O_CREATE, 0666)
	if err != nil {
		f.WriteString("create url category conf err" + err.Error() + "\n")
		logging.ErrorLogger.Errorf("create url category conf err", err)
		return err
	}
	conf.WriteString(jsonStr)
	conf.Close()

	//存在predef_profile.conf 先删除
	if _, err := os.Stat(filepath.Join(dst, "predef_profile.conf")); err == nil {
		os.Remove(filepath.Join(dst, "predef_profile.conf"))
	}

	// 生成predef_profile.conf
	conf, err = os.OpenFile(filepath.Join(dst, "predef_profile.conf"), os.O_RDWR|os.O_CREATE, 0666)
	if err != nil {
		logging.ErrorLogger.Errorf("create logfile err", err)
		return err
	}

	jsonByte, err = json.Marshal(predefProfileConf)
	if err != nil {
		return err
	}

	conf.WriteString(string(jsonByte))
	conf.Close()
	return nil
}

func GetOverseasCategory(ctx iris.Context) {
	overseasDir := libs.Config.UrlPack.OverseasDir
	zipFileName := "overseas_category.zip"
	//如果文件存在，则删除
	if _, err := os.Stat(filepath.Join(overseasDir, zipFileName)); err == nil {
		os.Remove(filepath.Join(overseasDir, zipFileName))
	}
	//将url_category.conf、predef_profile.conf、category_map.json 打包成zip文件
	if err := ZipFilesV2(filepath.Join(overseasDir, zipFileName), []string{filepath.Join(overseasDir, "url_category.conf"), filepath.Join(overseasDir, "predef_profile.conf"), filepath.Join(overseasDir, "category_map.json")}); err != nil {
		logging.ErrorLogger.Errorf("ZipFilesV2 err", err)
		return
	}
	ctx.SendFile(filepath.Join(overseasDir, zipFileName), zipFileName)
	return

}

func ZipFilesV2(filename string, files []string) error {
	//创建输出文件目录
	newZipFile, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer newZipFile.Close()
	//创建空的zip档案，准备写入
	zipWriter := zip.NewWriter(newZipFile)
	defer zipWriter.Close()
	// Add files to zip
	for _, file := range files {
		if err = AddFileToZip(zipWriter, file); err != nil {
			return err
		}
	}
	return nil
}

func AddFileToZip(zipWriter *zip.Writer, fileName string) error {
	if len(fileName) < 1 {
		return fmt.Errorf("将要压缩的文件列表不能为空")
	}
	//打开要压缩的文件
	fileToZip, err := os.Open(fileName)
	if err != nil {
		return err
	}
	defer fileToZip.Close()
	//获取文件的描述
	info, err := fileToZip.Stat()
	if err != nil {
		return err
	}
	//FileInfoHeader返回一个根据fi填写了部分字段的Header，可以理解成是将fileinfo转换成zip格式的文件信息
	header, err := zip.FileInfoHeader(info)
	if err != nil {
		return err
	}
	header.Name = filepath.Base(fileName)
	if info.IsDir() {
		header.Name += "/"
	}
	/*
	   预定义压缩算法。
	   archive/zip包中预定义的有两种压缩方式
	   Store   unit16 = 0  //仅存储文件
	   Deflate unit16 = 8  //压缩文件
	*/
	header.Method = zip.Deflate
	//创建压缩包头部信息
	writer, err := zipWriter.CreateHeader(header)
	if err != nil {
		return err
	}
	//将源复制到目标，将fileToZip 写入writer 是按默认的缓冲区32k循环操作的
	_, err = io.Copy(writer, fileToZip)
	return err
}
