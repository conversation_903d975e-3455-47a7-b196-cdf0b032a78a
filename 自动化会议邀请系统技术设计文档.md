# 自动化会议邀请系统技术设计文档

## 文档信息

- **项目名称**: 自动化会议邀请系统
- **版本**: v1.0
- **创建日期**: 2025-01-11
- **最后更新**: 2025-01-11
- **文档状态**: 设计阶段

---

## 1. 系统概述

### 1.1 项目背景和目标

#### 项目背景

当前组织内部会议安排主要依赖手工操作，存在以下问题：

- 会议信息分散在多个系统中，缺乏统一管理
- 手动创建会议邀请效率低下，容易出错
- 参会人员通知不及时，影响会议效果
- 缺乏自动化流程，增加管理成本

#### 项目目标

1. **自动化流程**: 实现从多维表格到日历邀请的全自动化流程
2. **提升效率**: 减少90%的手动操作时间
3. **降低错误**: 通过数据验证和自动化处理减少人为错误
4. **统一管理**: 建立统一的会议数据管理平台
5. **实时同步**: 确保会议信息的实时性和一致性

### 1.2 技术栈选择说明

#### 后端技术栈

- **编程语言**: Go 1.19+
  - 选择理由: 现有系统基于Go，具备高并发性能和丰富的生态
- **Web框架**: Iris
  - 选择理由: 现有系统使用，具备完整的中间件支持
- **数据库**: MySQL 8.0 + GORM
  - 选择理由: 现有基础设施，支持事务和复杂查询
- **缓存**: Redis 6.0+
  - 选择理由: 现有基础设施，用于状态缓存和分布式锁
- **定时任务**: Cron调度器
  - 选择理由: 现有系统集成，支持复杂的调度规则

#### 第三方集成

- **飞书SDK**: 官方Go SDK (github.com/larksuite/oapi-sdk-go)
  - 多维表格API: 数据读取和状态更新
  - 日历API: 会议邀请创建和管理
  - 消息API: 通知和提醒功能
  - 用户API: 人员信息查询和验证

### 1.3 系统边界和约束条件

#### 系统边界

**包含功能**:

- 飞书多维表格数据读取和解析
- 会议数据验证和格式转换
- 飞书日历事件自动创建
- 参会人员自动邀请和通知
- 处理状态跟踪和错误处理
- 管理员监控和报告功能

**不包含功能**:

- 会议内容录制和存储
- 第三方日历系统集成
- 会议室硬件设备控制
- 会议费用统计和分析

#### 约束条件

**技术约束**:

- 必须基于现有技术栈进行开发
- 需要兼容现有的认证和权限体系
- 数据库表结构需要向后兼容
- API调用频率受飞书平台限制

**业务约束**:

- 会议时间不能早于当前时间
- 单次处理的会议数量不超过500个
- 会议持续时间不超过8小时
- 参会人员数量不超过100人

**性能约束**:

- 单次同步任务完成时间不超过10分钟
- API响应时间不超过5秒
- 系统可用性要求99.5%以上
- 支持并发处理多个会议创建请求

---

## 2. 架构设计

### 2.1 系统整体架构

系统采用分层架构设计，包含以下层次：

```
┌─────────────────────────────────────────────────────────────┐
│                        表现层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   定时调度器     │  │   手动触发API   │  │   Webhook接口   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                        业务层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  会议数据读取   │  │  数据验证转换   │  │  会议创建服务   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   通知服务      │  │   状态管理      │  │   错误处理      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                        集成层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ 飞书多维表格API │  │  飞书日历API    │  │  飞书消息API    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                        数据层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   MySQL数据库   │  │   Redis缓存     │  │   日志文件      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 模块划分和职责说明

#### 核心模块

**1. 会议数据读取模块 (MeetingDataReader)**

- 职责: 从飞书多维表格读取会议数据
- 功能: 分页查询、数据过滤、增量检测
- 接口: `ReadMeetingData(filter MeetingFilter) ([]MeetingData, error)`

**2. 数据验证转换模块 (MeetingDataProcessor)**

- 职责: 验证和转换会议数据格式
- 功能: 数据校验、格式转换、字段映射
- 接口: `ProcessMeetingData(raw RawMeetingData) (ProcessedMeetingData, error)`

**3. 会议创建服务模块 (MeetingCreator)**

- 职责: 创建飞书日历会议邀请
- 功能: 日历事件创建、参会人员邀请、会议室预订
- 接口: `CreateMeeting(meeting ProcessedMeetingData) (CalendarEvent, error)`

**4. 通知服务模块 (NotificationService)**

- 职责: 发送会议通知和状态更新
- 功能: 消息推送、邮件通知、状态同步
- 接口: `SendNotification(notification NotificationData) error`

**5. 状态管理模块 (StatusManager)**

- 职责: 管理会议处理状态和进度跟踪
- 功能: 状态更新、进度监控、重试管理
- 接口: `UpdateStatus(recordID string, status ProcessingStatus) error`

### 2.3 数据流设计

系统数据流遵循以下路径：

```
多维表格数据 → 数据读取 → 数据验证 → 格式转换 → 会议创建 → 状态更新 → 通知发送
     ↓            ↓         ↓         ↓         ↓         ↓         ↓
   原始数据    → 结构化数据 → 验证数据 → 标准数据 → 日历事件 → 处理记录 → 通知消息
```

### 2.4 接口设计规范

#### 内部服务接口规范

**接口命名规范**:

- 使用动词+名词的形式: `CreateMeeting`, `ReadMeetingData`
- 使用驼峰命名法
- 接口名称要清晰表达功能意图

**参数设计规范**:

- 使用结构体传递复杂参数
- 必填参数放在前面，可选参数使用Options模式
- 所有接口都要返回error类型

**响应格式规范**:

```go
type APIResponse struct {
    Code    int         `json:"code"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
    TraceID string      `json:"trace_id"`
}
```

**错误处理规范**:

- 使用自定义错误类型
- 错误信息要包含足够的上下文
- 支持错误链追踪

---

## 3. 数据库设计

### 3.1 会议记录表结构设计

#### 主表: meeting_records

```sql
CREATE TABLE `meeting_records` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `bitable_record_id` varchar(100) NOT NULL COMMENT '多维表格记录ID',
  `calendar_event_id` varchar(100) DEFAULT NULL COMMENT '日历事件ID',
  `meeting_title` varchar(200) NOT NULL COMMENT '会议标题',
  `meeting_description` text COMMENT '会议描述',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `location` varchar(200) DEFAULT NULL COMMENT '会议地点',
  `organizer_id` varchar(100) NOT NULL COMMENT '组织者ID',
  `organizer_name` varchar(100) NOT NULL COMMENT '组织者姓名',
  `status` enum('pending','processing','success','failed','skipped') NOT NULL DEFAULT 'pending' COMMENT '处理状态',
  `error_message` text COMMENT '错误信息',
  `retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
  `processed_at` datetime DEFAULT NULL COMMENT '处理时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bitable_record` (`bitable_record_id`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_organizer` (`organizer_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会议记录表';
```

#### 参会人员表: meeting_attendees

```sql
CREATE TABLE `meeting_attendees` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `meeting_record_id` bigint(20) unsigned NOT NULL COMMENT '会议记录ID',
  `attendee_id` varchar(100) NOT NULL COMMENT '参会人员ID',
  `attendee_name` varchar(100) NOT NULL COMMENT '参会人员姓名',
  `attendee_email` varchar(200) DEFAULT NULL COMMENT '参会人员邮箱',
  `role` enum('required','optional') NOT NULL DEFAULT 'required' COMMENT '参会角色',
  `response_status` enum('pending','accepted','declined','tentative') DEFAULT 'pending' COMMENT '响应状态',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_meeting_record` (`meeting_record_id`),
  KEY `idx_attendee` (`attendee_id`),
  FOREIGN KEY (`meeting_record_id`) REFERENCES `meeting_records` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会议参会人员表';
```

#### 处理日志表: meeting_process_logs

```sql
CREATE TABLE `meeting_process_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `meeting_record_id` bigint(20) unsigned NOT NULL COMMENT '会议记录ID',
  `action` varchar(50) NOT NULL COMMENT '操作类型',
  `status` varchar(20) NOT NULL COMMENT '操作状态',
  `message` text COMMENT '操作信息',
  `details` json COMMENT '详细信息',
  `duration_ms` int(11) DEFAULT NULL COMMENT '执行时长(毫秒)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_meeting_record` (`meeting_record_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`meeting_record_id`) REFERENCES `meeting_records` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会议处理日志表';
```

### 3.2 索引设计和性能优化

#### 索引策略

**主要查询场景**:

1. 按状态查询待处理会议: `status = 'pending'`
2. 按时间范围查询会议: `start_time BETWEEN ? AND ?`
3. 按组织者查询会议: `organizer_id = ?`
4. 按多维表格记录ID查询: `bitable_record_id = ?`

**索引设计**:

```sql
-- 复合索引：状态+创建时间（用于定时任务查询）
CREATE INDEX idx_status_created ON meeting_records(status, created_at);

-- 复合索引：开始时间+状态（用于时间范围查询）
CREATE INDEX idx_start_time_status ON meeting_records(start_time, status);

-- 复合索引：组织者+状态（用于用户查询）
CREATE INDEX idx_organizer_status ON meeting_records(organizer_id, status);
```

**性能优化策略**:

1. **分页查询优化**: 使用游标分页替代OFFSET分页
2. **批量操作**: 使用批量插入和更新减少数据库连接
3. **读写分离**: 查询操作使用只读副本
4. **连接池优化**: 合理配置数据库连接池大小

### 3.3 数据迁移策略

#### 迁移脚本结构

```sql
-- V1.0.0__create_meeting_tables.sql
-- 创建会议相关表结构

-- V1.0.1__add_meeting_indexes.sql
-- 添加性能优化索引

-- V1.0.2__insert_default_data.sql
-- 插入默认配置数据
```

#### 数据迁移流程

1. **备份现有数据**: 执行迁移前完整备份
2. **创建新表结构**: 执行DDL脚本创建表
3. **数据导入**: 如有历史数据需要导入
4. **索引创建**: 创建性能优化索引
5. **验证数据**: 验证数据完整性和一致性

---

## 4. API设计

### 4.1 飞书日历API集成方案

#### 日历事件创建接口

```go
// CalendarEventRequest 创建日历事件请求
type CalendarEventRequest struct {
    CalendarID string                 `json:"calendar_id"`
    Summary    string                 `json:"summary"`
    StartTime  *CalendarEventTime     `json:"start_time"`
    EndTime    *CalendarEventTime     `json:"end_time"`
    Description string                `json:"description,omitempty"`
    Location   *CalendarEventLocation `json:"location,omitempty"`
    Attendees  []*CalendarAttendee    `json:"attendees,omitempty"`
    Reminders  []*CalendarReminder    `json:"reminders,omitempty"`
}

// CalendarEventTime 日历事件时间
type CalendarEventTime struct {
    Timestamp string `json:"timestamp"`
    Timezone  string `json:"timezone"`
}

// CalendarEventLocation 日历事件地点
type CalendarEventLocation struct {
    Name      string  `json:"name"`
    Address   string  `json:"address,omitempty"`
    Latitude  float64 `json:"latitude,omitempty"`
    Longitude float64 `json:"longitude,omitempty"`
}

// CalendarAttendee 参会人员
type CalendarAttendee struct {
    UserID   string `json:"user_id"`
    Required bool   `json:"required"`
}

// CalendarReminder 会议提醒
type CalendarReminder struct {
    Minutes int `json:"minutes"`
}
```

#### 日历API服务实现

```go
// CalendarService 日历服务接口
type CalendarService interface {
    CreateEvent(ctx context.Context, req *CalendarEventRequest) (*CalendarEventResponse, error)
    UpdateEvent(ctx context.Context, eventID string, req *CalendarEventRequest) (*CalendarEventResponse, error)
    DeleteEvent(ctx context.Context, eventID string) error
    GetEvent(ctx context.Context, eventID string) (*CalendarEventResponse, error)
}

// CalendarServiceImpl 日历服务实现
type CalendarServiceImpl struct {
    client *lark.Client
    config *CalendarConfig
}

func (s *CalendarServiceImpl) CreateEvent(ctx context.Context, req *CalendarEventRequest) (*CalendarEventResponse, error) {
    // 构建飞书日历API请求
    larkReq := larkcalendar.NewCreateCalendarEventReqBuilder().
        CalendarId(req.CalendarID).
        Body(larkcalendar.NewCreateCalendarEventReqBodyBuilder().
            Summary(req.Summary).
            StartTime(larkcalendar.NewTimeInfoBuilder().
                Timestamp(req.StartTime.Timestamp).
                Timezone(req.StartTime.Timezone).
                Build()).
            EndTime(larkcalendar.NewTimeInfoBuilder().
                Timestamp(req.EndTime.Timestamp).
                Timezone(req.EndTime.Timezone).
                Build()).
            Description(req.Description).
            Build()).
        Build()

    // 发起API调用
    resp, err := s.client.Calendar.CalendarEvent.Create(ctx, larkReq)
    if err != nil {
        return nil, fmt.Errorf("创建日历事件失败: %w", err)
    }

    if !resp.Success() {
        return nil, fmt.Errorf("飞书API返回错误: %s", resp.Msg)
    }

    return &CalendarEventResponse{
        EventID: *resp.Data.CalendarEvent.EventId,
        Status:  "created",
    }, nil
}
```

### 4.2 多维表格数据读取接口

#### 数据读取服务接口

```go
// MeetingDataReader 会议数据读取接口
type MeetingDataReader interface {
    ReadPendingMeetings(ctx context.Context, filter *MeetingFilter) ([]*RawMeetingData, error)
    ReadMeetingByID(ctx context.Context, recordID string) (*RawMeetingData, error)
    UpdateMeetingStatus(ctx context.Context, recordID string, status string) error
}

// MeetingFilter 会议数据过滤条件
type MeetingFilter struct {
    Status     []string  `json:"status,omitempty"`
    StartTime  *time.Time `json:"start_time,omitempty"`
    EndTime    *time.Time `json:"end_time,omitempty"`
    Organizer  string    `json:"organizer,omitempty"`
    PageSize   int       `json:"page_size"`
    PageToken  string    `json:"page_token,omitempty"`
}

// RawMeetingData 原始会议数据
type RawMeetingData struct {
    RecordID    string                 `json:"record_id"`
    Fields      map[string]interface{} `json:"fields"`
    CreatedTime int64                  `json:"created_time"`
    UpdatedTime int64                  `json:"updated_time"`
}
```

#### 数据读取实现

```go
// BitableReaderImpl 多维表格读取实现
type BitableReaderImpl struct {
    client   *lark.Client
    appToken string
    tableID  string
}

func (r *BitableReaderImpl) ReadPendingMeetings(ctx context.Context, filter *MeetingFilter) ([]*RawMeetingData, error) {
    // 构建查询条件
    reqBuilder := larkbitable.NewSearchAppTableRecordReqBuilder().
        AppToken(r.appToken).
        TableId(r.tableID).
        PageSize(filter.PageSize)

    if filter.PageToken != "" {
        reqBuilder.PageToken(filter.PageToken)
    }

    // 构建过滤条件
    if len(filter.Status) > 0 {
        bodyBuilder := larkbitable.NewSearchAppTableRecordReqBodyBuilder()
        filterBuilder := larkbitable.NewFilterInfoBuilder()

        conditions := make([]*larkbitable.FilterCondition, 0)
        for _, status := range filter.Status {
            condition := larkbitable.NewFilterConditionBuilder().
                FieldName("处理状态").
                Operator("is").
                Value([]string{status}).
                Build()
            conditions = append(conditions, condition)
        }

        filterBuilder.Conjunction("or").Conditions(conditions)
        bodyBuilder.Filter(filterBuilder.Build())
        reqBuilder.Body(bodyBuilder.Build())
    }

    req := reqBuilder.Build()

    // 发起API调用
    resp, err := r.client.Bitable.AppTableRecord.Search(ctx, req)
    if err != nil {
        return nil, fmt.Errorf("查询多维表格数据失败: %w", err)
    }

    if !resp.Success() {
        return nil, fmt.Errorf("飞书API返回错误: %s", resp.Msg)
    }

    // 转换数据格式
    meetings := make([]*RawMeetingData, 0, len(resp.Data.Items))
    for _, item := range resp.Data.Items {
        meeting := &RawMeetingData{
            RecordID:    *item.RecordId,
            Fields:      item.Fields,
            CreatedTime: *item.CreatedTime,
            UpdatedTime: *item.LastModifiedTime,
        }
        meetings = append(meetings, meeting)
    }

    return meetings, nil
}
```

### 4.3 内部服务接口定义

#### 会议处理服务接口

```go
// MeetingProcessService 会议处理服务接口
type MeetingProcessService interface {
    ProcessMeetings(ctx context.Context, filter *MeetingFilter) (*ProcessResult, error)
    ProcessSingleMeeting(ctx context.Context, recordID string) (*MeetingProcessResult, error)
    GetProcessStatus(ctx context.Context, recordID string) (*ProcessStatus, error)
    RetryFailedMeetings(ctx context.Context, maxRetries int) (*ProcessResult, error)
}

// ProcessResult 处理结果
type ProcessResult struct {
    TotalCount    int                      `json:"total_count"`
    SuccessCount  int                      `json:"success_count"`
    FailedCount   int                      `json:"failed_count"`
    SkippedCount  int                      `json:"skipped_count"`
    Results       []*MeetingProcessResult  `json:"results"`
    Duration      time.Duration            `json:"duration"`
}

// MeetingProcessResult 单个会议处理结果
type MeetingProcessResult struct {
    RecordID      string    `json:"record_id"`
    EventID       string    `json:"event_id,omitempty"`
    Status        string    `json:"status"`
    ErrorMessage  string    `json:"error_message,omitempty"`
    ProcessedAt   time.Time `json:"processed_at"`
}

// ProcessStatus 处理状态
type ProcessStatus struct {
    RecordID     string    `json:"record_id"`
    Status       string    `json:"status"`
    RetryCount   int       `json:"retry_count"`
    LastError    string    `json:"last_error,omitempty"`
    CreatedAt    time.Time `json:"created_at"`
    UpdatedAt    time.Time `json:"updated_at"`
}
```

### 4.4 错误码和响应格式规范

#### 错误码定义

```go
const (
    // 成功
    CodeSuccess = 0

    // 客户端错误 (4xx)
    CodeBadRequest     = 400
    CodeUnauthorized   = 401
    CodeForbidden      = 403
    CodeNotFound       = 404
    CodeConflict       = 409
    CodeValidationFail = 422

    // 服务端错误 (5xx)
    CodeInternalError  = 500
    CodeServiceUnavail = 503
    CodeTimeout        = 504

    // 业务错误 (6xx)
    CodeMeetingExists     = 600
    CodeInvalidTimeRange  = 601
    CodeAttendeeNotFound  = 602
    CodeCalendarNotFound  = 603
    CodeFeishuAPIError    = 604
)

// ErrorCode 错误码映射
var ErrorCodeMap = map[int]string{
    CodeSuccess:           "成功",
    CodeBadRequest:        "请求参数错误",
    CodeUnauthorized:      "未授权访问",
    CodeForbidden:         "访问被禁止",
    CodeNotFound:          "资源不存在",
    CodeConflict:          "资源冲突",
    CodeValidationFail:    "数据验证失败",
    CodeInternalError:     "内部服务错误",
    CodeServiceUnavail:    "服务不可用",
    CodeTimeout:           "请求超时",
    CodeMeetingExists:     "会议已存在",
    CodeInvalidTimeRange:  "无效的时间范围",
    CodeAttendeeNotFound:  "参会人员不存在",
    CodeCalendarNotFound:  "日历不存在",
    CodeFeishuAPIError:    "飞书API调用失败",
}
```

#### 统一响应格式

```go
// Response 统一响应格式
type Response struct {
    Code      int         `json:"code"`
    Message   string      `json:"message"`
    Data      interface{} `json:"data,omitempty"`
    TraceID   string      `json:"trace_id"`
    Timestamp int64       `json:"timestamp"`
}

// SuccessResponse 成功响应
func SuccessResponse(data interface{}) *Response {
    return &Response{
        Code:      CodeSuccess,
        Message:   "成功",
        Data:      data,
        TraceID:   generateTraceID(),
        Timestamp: time.Now().Unix(),
    }
}

// ErrorResponse 错误响应
func ErrorResponse(code int, message string) *Response {
    if message == "" {
        message = ErrorCodeMap[code]
    }
    return &Response{
        Code:      code,
        Message:   message,
        TraceID:   generateTraceID(),
        Timestamp: time.Now().Unix(),
    }
}
```

---

## 5. 业务逻辑设计

### 5.1 核心业务流程详细说明

#### 会议数据处理流程

**阶段1: 数据读取和预处理**

1. 定时任务触发数据同步
2. 从多维表格读取待处理会议数据
3. 按状态过滤（pending状态）
4. 数据去重和排序（按创建时间升序）

**阶段2: 数据验证和转换**

1. 必填字段验证（标题、时间、组织者）
2. 数据格式验证（时间格式、邮箱格式）
3. 业务规则验证（时间合理性、人员有效性）
4. 数据格式转换（时间戳转换、人员ID映射）

**阶段3: 会议创建和通知**

1. 检查会议是否已存在（防重复）
2. 调用飞书日历API创建事件
3. 添加参会人员和设置提醒
4. 发送会议通知给参会人员
5. 更新多维表格处理状态

### 5.2 数据验证规则

#### 基础数据验证

```go
// MeetingValidator 会议数据验证器
type MeetingValidator struct {
    userService UserService
    timeZone    *time.Location
}

// ValidationRule 验证规则
type ValidationRule struct {
    Field       string
    Required    bool
    Validator   func(interface{}) error
    ErrorCode   int
    ErrorMsg    string
}

// 验证规则定义
var MeetingValidationRules = []ValidationRule{
    {
        Field:     "meeting_title",
        Required:  true,
        Validator: validateTitle,
        ErrorCode: CodeValidationFail,
        ErrorMsg:  "会议标题不能为空且长度不超过200字符",
    },
    {
        Field:     "start_time",
        Required:  true,
        Validator: validateStartTime,
        ErrorCode: CodeInvalidTimeRange,
        ErrorMsg:  "开始时间不能早于当前时间",
    },
    {
        Field:     "end_time",
        Required:  true,
        Validator: validateEndTime,
        ErrorCode: CodeInvalidTimeRange,
        ErrorMsg:  "结束时间必须晚于开始时间",
    },
    {
        Field:     "organizer",
        Required:  true,
        Validator: validateOrganizer,
        ErrorCode: CodeAttendeeNotFound,
        ErrorMsg:  "组织者信息无效",
    },
}

// 验证函数实现
func validateTitle(value interface{}) error {
    title, ok := value.(string)
    if !ok {
        return errors.New("标题必须是字符串类型")
    }
    if len(strings.TrimSpace(title)) == 0 {
        return errors.New("标题不能为空")
    }
    if len(title) > 200 {
        return errors.New("标题长度不能超过200字符")
    }
    return nil
}

func validateStartTime(value interface{}) error {
    timestamp, ok := value.(float64)
    if !ok {
        return errors.New("开始时间格式错误")
    }

    startTime := time.Unix(int64(timestamp/1000), 0)
    if startTime.Before(time.Now().Add(-5 * time.Minute)) {
        return errors.New("开始时间不能早于当前时间")
    }
    return nil
}

func validateEndTime(value interface{}) error {
    // 需要结合开始时间进行验证
    // 实现逻辑...
    return nil
}
```

#### 业务规则验证

```go
// BusinessRuleValidator 业务规则验证器
type BusinessRuleValidator struct {
    maxDuration     time.Duration // 最大会议时长
    maxAttendees    int           // 最大参会人数
    advanceNotice   time.Duration // 最小提前通知时间
    workingHours    WorkingHours  // 工作时间配置
}

// WorkingHours 工作时间配置
type WorkingHours struct {
    StartHour int // 开始小时 (0-23)
    EndHour   int // 结束小时 (0-23)
    Weekdays  []time.Weekday // 工作日
}

// ValidateBusinessRules 验证业务规则
func (v *BusinessRuleValidator) ValidateBusinessRules(meeting *ProcessedMeetingData) error {
    // 验证会议时长
    duration := meeting.EndTime.Sub(meeting.StartTime)
    if duration > v.maxDuration {
        return fmt.Errorf("会议时长不能超过%v", v.maxDuration)
    }

    // 验证参会人数
    if len(meeting.Attendees) > v.maxAttendees {
        return fmt.Errorf("参会人数不能超过%d人", v.maxAttendees)
    }

    // 验证提前通知时间
    noticeTime := meeting.StartTime.Sub(time.Now())
    if noticeTime < v.advanceNotice {
        return fmt.Errorf("会议开始时间距离现在至少需要%v", v.advanceNotice)
    }

    // 验证工作时间
    if !v.isWorkingTime(meeting.StartTime) {
        return errors.New("会议时间必须在工作时间内")
    }

    return nil
}

func (v *BusinessRuleValidator) isWorkingTime(t time.Time) bool {
    // 检查是否为工作日
    isWorkday := false
    for _, weekday := range v.workingHours.Weekdays {
        if t.Weekday() == weekday {
            isWorkday = true
            break
        }
    }
    if !isWorkday {
        return false
    }

    // 检查是否在工作时间内
    hour := t.Hour()
    return hour >= v.workingHours.StartHour && hour < v.workingHours.EndHour
}
```

### 5.3 状态转换机制

#### 状态定义和转换规则

```go
// ProcessingStatus 处理状态
type ProcessingStatus string

const (
    StatusPending    ProcessingStatus = "pending"    // 待处理
    StatusProcessing ProcessingStatus = "processing" // 处理中
    StatusSuccess    ProcessingStatus = "success"    // 成功
    StatusFailed     ProcessingStatus = "failed"     // 失败
    StatusSkipped    ProcessingStatus = "skipped"    // 跳过
    StatusRetrying   ProcessingStatus = "retrying"   // 重试中
)

// StatusTransition 状态转换规则
type StatusTransition struct {
    From      ProcessingStatus
    To        ProcessingStatus
    Condition string
    Action    func(context.Context, *MeetingRecord) error
}

// 状态转换规则定义
var StatusTransitions = []StatusTransition{
    {
        From:      StatusPending,
        To:        StatusProcessing,
        Condition: "开始处理",
        Action:    markAsProcessing,
    },
    {
        From:      StatusProcessing,
        To:        StatusSuccess,
        Condition: "处理成功",
        Action:    markAsSuccess,
    },
    {
        From:      StatusProcessing,
        To:        StatusFailed,
        Condition: "处理失败",
        Action:    markAsFailed,
    },
    {
        From:      StatusFailed,
        To:        StatusRetrying,
        Condition: "重试处理",
        Action:    markAsRetrying,
    },
    {
        From:      StatusRetrying,
        To:        StatusSuccess,
        Condition: "重试成功",
        Action:    markAsSuccess,
    },
    {
        From:      StatusRetrying,
        To:        StatusFailed,
        Condition: "重试失败",
        Action:    markAsFailed,
    },
}

// StatusManager 状态管理器
type StatusManager struct {
    db     *gorm.DB
    cache  *redis.Client
    logger *logging.Logger
}

// TransitionStatus 状态转换
func (sm *StatusManager) TransitionStatus(ctx context.Context, recordID string, to ProcessingStatus, message string) error {
    // 获取当前状态
    current, err := sm.GetCurrentStatus(ctx, recordID)
    if err != nil {
        return fmt.Errorf("获取当前状态失败: %w", err)
    }

    // 验证状态转换是否合法
    if !sm.isValidTransition(current.Status, to) {
        return fmt.Errorf("无效的状态转换: %s -> %s", current.Status, to)
    }

    // 执行状态转换
    return sm.updateStatus(ctx, recordID, to, message)
}

func (sm *StatusManager) isValidTransition(from, to ProcessingStatus) bool {
    for _, transition := range StatusTransitions {
        if transition.From == from && transition.To == to {
            return true
        }
    }
    return false
}
```

### 5.4 异常处理策略

#### 错误分类和处理策略

```go
// ErrorType 错误类型
type ErrorType string

const (
    ErrorTypeValidation ErrorType = "validation" // 数据验证错误
    ErrorTypeAPI        ErrorType = "api"        // API调用错误
    ErrorTypeNetwork    ErrorType = "network"    // 网络错误
    ErrorTypeAuth       ErrorType = "auth"       // 认证错误
    ErrorTypeSystem     ErrorType = "system"     // 系统错误
    ErrorTypeBusiness   ErrorType = "business"   // 业务逻辑错误
)

// ErrorHandlingStrategy 错误处理策略
type ErrorHandlingStrategy struct {
    ErrorType     ErrorType     `json:"error_type"`
    MaxRetries    int           `json:"max_retries"`
    RetryDelay    time.Duration `json:"retry_delay"`
    BackoffFactor float64       `json:"backoff_factor"`
    Recoverable   bool          `json:"recoverable"`
    NotifyAdmin   bool          `json:"notify_admin"`
}

// 错误处理策略配置
var ErrorStrategies = map[ErrorType]ErrorHandlingStrategy{
    ErrorTypeValidation: {
        ErrorType:     ErrorTypeValidation,
        MaxRetries:    0, // 验证错误不重试
        Recoverable:   false,
        NotifyAdmin:   false,
    },
    ErrorTypeAPI: {
        ErrorType:     ErrorTypeAPI,
        MaxRetries:    3,
        RetryDelay:    time.Second * 5,
        BackoffFactor: 2.0,
        Recoverable:   true,
        NotifyAdmin:   true,
    },
    ErrorTypeNetwork: {
        ErrorType:     ErrorTypeNetwork,
        MaxRetries:    5,
        RetryDelay:    time.Second * 2,
        BackoffFactor: 1.5,
        Recoverable:   true,
        NotifyAdmin:   false,
    },
    ErrorTypeAuth: {
        ErrorType:     ErrorTypeAuth,
        MaxRetries:    1,
        RetryDelay:    time.Second * 10,
        BackoffFactor: 1.0,
        Recoverable:   true,
        NotifyAdmin:   true,
    },
    ErrorTypeSystem: {
        ErrorType:     ErrorTypeSystem,
        MaxRetries:    2,
        RetryDelay:    time.Second * 30,
        BackoffFactor: 2.0,
        Recoverable:   true,
        NotifyAdmin:   true,
    },
}

// ErrorHandler 错误处理器
type ErrorHandler struct {
    strategies map[ErrorType]ErrorHandlingStrategy
    notifier   NotificationService
    logger     *logging.Logger
}

// HandleError 处理错误
func (eh *ErrorHandler) HandleError(ctx context.Context, err error, recordID string) error {
    // 分析错误类型
    errorType := eh.analyzeErrorType(err)
    strategy := eh.strategies[errorType]

    // 记录错误日志
    eh.logger.Errorf("处理会议记录 %s 时发生错误: %v (类型: %s)", recordID, err, errorType)

    // 如果需要通知管理员
    if strategy.NotifyAdmin {
        go eh.notifyAdmin(ctx, err, recordID, errorType)
    }

    // 如果错误可恢复，返回重试信息
    if strategy.Recoverable && strategy.MaxRetries > 0 {
        return &RecoverableError{
            OriginalError: err,
            ErrorType:     errorType,
            Strategy:      strategy,
        }
    }

    return err
}

// RecoverableError 可恢复错误
type RecoverableError struct {
    OriginalError error
    ErrorType     ErrorType
    Strategy      ErrorHandlingStrategy
}

func (e *RecoverableError) Error() string {
    return e.OriginalError.Error()
}

func (e *RecoverableError) IsRecoverable() bool {
    return e.Strategy.Recoverable
}

func (e *RecoverableError) GetRetryDelay(attempt int) time.Duration {
    delay := e.Strategy.RetryDelay
    for i := 1; i < attempt; i++ {
        delay = time.Duration(float64(delay) * e.Strategy.BackoffFactor)
    }
    return delay
}
```

---

## 6. 配置管理

### 6.1 新增配置项定义

#### 会议系统配置结构

```go
// MeetingConfig 会议系统配置
type MeetingConfig struct {
    Enable bool `yaml:"enable" json:"enable"` // 是否启用会议系统

    // 飞书配置
    Feishu FeishuMeetingConfig `yaml:"feishu" json:"feishu"`

    // 数据源配置
    DataSource DataSourceConfig `yaml:"datasource" json:"datasource"`

    // 调度配置
    Schedule ScheduleConfig `yaml:"schedule" json:"schedule"`

    // 验证配置
    Validation ValidationConfig `yaml:"validation" json:"validation"`

    // 通知配置
    Notification NotificationConfig `yaml:"notification" json:"notification"`

    // 性能配置
    Performance PerformanceConfig `yaml:"performance" json:"performance"`
}

// FeishuMeetingConfig 飞书会议配置
type FeishuMeetingConfig struct {
    AppID                string `yaml:"app_id" json:"app_id"`
    AppSecret            string `yaml:"app_secret" json:"app_secret"`
    MeetingAppToken      string `yaml:"meeting_app_token" json:"meeting_app_token"`      // 会议多维表格应用Token
    MeetingTableID       string `yaml:"meeting_table_id" json:"meeting_table_id"`       // 会议数据表ID
    DefaultCalendarID    string `yaml:"default_calendar_id" json:"default_calendar_id"` // 默认日历ID
    DefaultTimezone      string `yaml:"default_timezone" json:"default_timezone"`       // 默认时区
    RateLimitPerSecond   int    `yaml:"rate_limit_per_second" json:"rate_limit_per_second"` // API调用频率限制
}

// DataSourceConfig 数据源配置
type DataSourceConfig struct {
    BatchSize        int           `yaml:"batch_size" json:"batch_size"`               // 批量处理大小
    QueryTimeout     time.Duration `yaml:"query_timeout" json:"query_timeout"`         // 查询超时时间
    MaxRecords       int           `yaml:"max_records" json:"max_records"`             // 单次最大处理记录数
    IncrementalField string        `yaml:"incremental_field" json:"incremental_field"` // 增量同步字段
}

// ScheduleConfig 调度配置
type ScheduleConfig struct {
    FullSyncCron        string        `yaml:"full_sync_cron" json:"full_sync_cron"`               // 全量同步Cron表达式
    IncrementalSyncCron string        `yaml:"incremental_sync_cron" json:"incremental_sync_cron"` // 增量同步Cron表达式
    StatusCheckCron     string        `yaml:"status_check_cron" json:"status_check_cron"`         // 状态检查Cron表达式
    CleanupCron         string        `yaml:"cleanup_cron" json:"cleanup_cron"`                   // 清理任务Cron表达式
    TaskTimeout         time.Duration `yaml:"task_timeout" json:"task_timeout"`                   // 任务超时时间
    MaxConcurrentTasks  int           `yaml:"max_concurrent_tasks" json:"max_concurrent_tasks"`   // 最大并发任务数
}

// ValidationConfig 验证配置
type ValidationConfig struct {
    MaxMeetingDuration  time.Duration `yaml:"max_meeting_duration" json:"max_meeting_duration"`   // 最大会议时长
    MaxAttendees        int           `yaml:"max_attendees" json:"max_attendees"`                 // 最大参会人数
    MinAdvanceNotice    time.Duration `yaml:"min_advance_notice" json:"min_advance_notice"`       // 最小提前通知时间
    WorkingHours        WorkingHours  `yaml:"working_hours" json:"working_hours"`                 // 工作时间
    RequiredFields      []string      `yaml:"required_fields" json:"required_fields"`             // 必填字段
    AllowWeekendMeeting bool          `yaml:"allow_weekend_meeting" json:"allow_weekend_meeting"` // 是否允许周末会议
}

// NotificationConfig 通知配置
type NotificationConfig struct {
    EnableEmail         bool     `yaml:"enable_email" json:"enable_email"`                 // 是否启用邮件通知
    EnableFeishuMessage bool     `yaml:"enable_feishu_message" json:"enable_feishu_message"` // 是否启用飞书消息通知
    AdminEmails         []string `yaml:"admin_emails" json:"admin_emails"`                 // 管理员邮箱列表
    AdminOpenIDs        []string `yaml:"admin_open_ids" json:"admin_open_ids"`             // 管理员飞书OpenID列表
    NotifyOnSuccess     bool     `yaml:"notify_on_success" json:"notify_on_success"`       // 成功时是否通知
    NotifyOnFailure     bool     `yaml:"notify_on_failure" json:"notify_on_failure"`       // 失败时是否通知
}

// PerformanceConfig 性能配置
type PerformanceConfig struct {
    WorkerPoolSize      int           `yaml:"worker_pool_size" json:"worker_pool_size"`           // 工作协程池大小
    TaskQueueSize       int           `yaml:"task_queue_size" json:"task_queue_size"`             // 任务队列大小
    CacheExpiration     time.Duration `yaml:"cache_expiration" json:"cache_expiration"`           // 缓存过期时间
    DatabaseMaxIdle     int           `yaml:"database_max_idle" json:"database_max_idle"`         // 数据库最大空闲连接
    DatabaseMaxOpen     int           `yaml:"database_max_open" json:"database_max_open"`         // 数据库最大打开连接
    DatabaseMaxLifetime time.Duration `yaml:"database_max_lifetime" json:"database_max_lifetime"` // 数据库连接最大生命周期
}
```

### 6.2 环境配置说明

#### application.yml 配置示例

```yaml
# 会议系统配置
meeting:
  enable: true

  # 飞书配置
  feishu:
    app_id: "cli_a6864542233d900e"
    app_secret: "f9FupAX6aw65B6FLgyvNXbEywHq7keJF"
    meeting_app_token: "KZQCbsWefa5e3MsamMccOYYPnMr"
    meeting_table_id: "tblbTRhZXzVkwjYx"
    default_calendar_id: "primary"
    default_timezone: "Asia/Shanghai"
    rate_limit_per_second: 10

  # 数据源配置
  datasource:
    batch_size: 50
    query_timeout: 30s
    max_records: 500
    incremental_field: "updated_at"

  # 调度配置
  schedule:
    full_sync_cron: "0 8 * * *"           # 每天早上8点全量同步
    incremental_sync_cron: "*/30 8-18 * * 1-5"  # 工作时间每30分钟增量同步
    status_check_cron: "*/5 * * * *"      # 每5分钟检查状态
    cleanup_cron: "0 2 * * *"             # 每天凌晨2点清理
    task_timeout: 10m
    max_concurrent_tasks: 5

  # 验证配置
  validation:
    max_meeting_duration: 8h
    max_attendees: 100
    min_advance_notice: 30m
    allow_weekend_meeting: false
    required_fields:
      - "meeting_title"
      - "start_time"
      - "end_time"
      - "organizer"
    working_hours:
      start_hour: 9
      end_hour: 18
      weekdays: [1, 2, 3, 4, 5]  # 周一到周五

  # 通知配置
  notification:
    enable_email: true
    enable_feishu_message: true
    admin_emails:
      - "<EMAIL>"
    admin_open_ids:
      - "ou_844642732286f2ae9e7c8885d9757696"
    notify_on_success: false
    notify_on_failure: true

  # 性能配置
  performance:
    worker_pool_size: 10
    task_queue_size: 100
    cache_expiration: 1h
    database_max_idle: 10
    database_max_open: 100
    database_max_lifetime: 1h
```

#### 环境变量配置

```bash
# 开发环境
export MEETING_ENABLE=true
export MEETING_FEISHU_APP_ID="cli_a6864542233d900e"
export MEETING_FEISHU_APP_SECRET="f9FupAX6aw65B6FLgyvNXbEywHq7keJF"
export MEETING_FEISHU_MEETING_APP_TOKEN="KZQCbsWefa5e3MsamMccOYYPnMr"
export MEETING_FEISHU_MEETING_TABLE_ID="tblbTRhZXzVkwjYx"

# 生产环境
export MEETING_ENABLE=true
export MEETING_FEISHU_APP_ID="${PROD_FEISHU_APP_ID}"
export MEETING_FEISHU_APP_SECRET="${PROD_FEISHU_APP_SECRET}"
export MEETING_FEISHU_MEETING_APP_TOKEN="${PROD_MEETING_APP_TOKEN}"
export MEETING_FEISHU_MEETING_TABLE_ID="${PROD_MEETING_TABLE_ID}"
export MEETING_NOTIFICATION_ADMIN_EMAILS="<EMAIL>,<EMAIL>"
```

### 6.3 敏感信息管理

#### 配置加密策略

```go
// ConfigEncryption 配置加密管理
type ConfigEncryption struct {
    privateKey []byte
    publicKey  []byte
}

// EncryptSensitiveConfig 加密敏感配置
func (ce *ConfigEncryption) EncryptSensitiveConfig(config *MeetingConfig) error {
    // 加密AppSecret
    encryptedSecret, err := ce.encrypt(config.Feishu.AppSecret)
    if err != nil {
        return fmt.Errorf("加密AppSecret失败: %w", err)
    }
    config.Feishu.AppSecret = encryptedSecret

    // 加密其他敏感信息
    // ...

    return nil
}

// DecryptSensitiveConfig 解密敏感配置
func (ce *ConfigEncryption) DecryptSensitiveConfig(config *MeetingConfig) error {
    // 解密AppSecret
    decryptedSecret, err := ce.decrypt(config.Feishu.AppSecret)
    if err != nil {
        return fmt.Errorf("解密AppSecret失败: %w", err)
    }
    config.Feishu.AppSecret = decryptedSecret

    return nil
}

func (ce *ConfigEncryption) encrypt(data string) (string, error) {
    // 使用RSA加密实现
    // ...
    return "", nil
}

func (ce *ConfigEncryption) decrypt(encryptedData string) (string, error) {
    // 使用RSA解密实现
    // ...
    return "", nil
}
```

#### 配置热更新机制

```go
// ConfigManager 配置管理器
type ConfigManager struct {
    config     *MeetingConfig
    configPath string
    mutex      sync.RWMutex
    watchers   []ConfigWatcher
}

// ConfigWatcher 配置变更监听器
type ConfigWatcher interface {
    OnConfigChanged(oldConfig, newConfig *MeetingConfig) error
}

// WatchConfigChanges 监听配置变更
func (cm *ConfigManager) WatchConfigChanges() error {
    watcher, err := fsnotify.NewWatcher()
    if err != nil {
        return err
    }
    defer watcher.Close()

    err = watcher.Add(cm.configPath)
    if err != nil {
        return err
    }

    for {
        select {
        case event := <-watcher.Events:
            if event.Op&fsnotify.Write == fsnotify.Write {
                if err := cm.reloadConfig(); err != nil {
                    logging.ErrorLogger.Errorf("重新加载配置失败: %v", err)
                }
            }
        case err := <-watcher.Errors:
            logging.ErrorLogger.Errorf("配置文件监听错误: %v", err)
        }
    }
}

// ReloadConfig 重新加载配置
func (cm *ConfigManager) reloadConfig() error {
    cm.mutex.Lock()
    defer cm.mutex.Unlock()

    oldConfig := cm.config
    newConfig, err := cm.loadConfigFromFile()
    if err != nil {
        return err
    }

    // 通知所有监听器
    for _, watcher := range cm.watchers {
        if err := watcher.OnConfigChanged(oldConfig, newConfig); err != nil {
            logging.ErrorLogger.Errorf("配置变更通知失败: %v", err)
        }
    }

    cm.config = newConfig
    return nil
}
```

---

## 7. 部署和运维

### 7.1 部署架构说明

#### 部署拓扑图

```
                    ┌─────────────────┐
                    │   Load Balancer │
                    │    (Nginx)      │
                    └─────────┬───────┘
                              │
                    ┌─────────┴───────┐
                    │                 │
            ┌───────▼────────┐ ┌──────▼────────┐
            │  App Server 1  │ │  App Server 2 │
            │   (Meeting)    │ │   (Meeting)   │
            └───────┬────────┘ └──────┬────────┘
                    │                 │
                    └─────────┬───────┘
                              │
                    ┌─────────▼───────┐
                    │                 │
            ┌───────▼────────┐ ┌──────▼────────┐
            │  MySQL Master  │ │  Redis Cluster│
            │   (Primary)    │ │   (Cache)     │
            └───────┬────────┘ └───────────────┘
                    │
            ┌───────▼────────┐
            │  MySQL Slave   │
            │  (Read Only)   │
            └────────────────┘
```

#### 容器化部署配置

**Dockerfile**

```dockerfile
FROM golang:1.19-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o meeting-service main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

COPY --from=builder /app/meeting-service .
COPY --from=builder /app/application.yml .
COPY --from=builder /app/rbac_model.conf .

EXPOSE 9001
CMD ["./meeting-service"]
```

**docker-compose.yml**

```yaml
version: '3.8'

services:
  meeting-service:
    build: .
    ports:
      - "9001:9001"
    environment:
      - DB_HOST=mysql
      - REDIS_HOST=redis
      - MEETING_ENABLE=true
    depends_on:
      - mysql
      - redis
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    restart: unless-stopped

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: abc.123
      MYSQL_DATABASE: iris
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    restart: unless-stopped

  redis:
    image: redis:6.0-alpine
    ports:
      - "6379:6379"
    command: redis-server --requirepass fileOut@2021!
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - meeting-service
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
```

### 7.2 监控指标定义

#### 业务监控指标

```go
// MeetingMetrics 会议系统监控指标
type MeetingMetrics struct {
    // 处理量指标
    TotalMeetingsProcessed   prometheus.Counter   // 总处理会议数
    SuccessfulMeetings       prometheus.Counter   // 成功创建会议数
    FailedMeetings           prometheus.Counter   // 失败会议数
    SkippedMeetings          prometheus.Counter   // 跳过会议数

    // 性能指标
    ProcessingDuration       prometheus.Histogram // 处理耗时分布
    APICallDuration          prometheus.Histogram // API调用耗时
    DatabaseQueryDuration    prometheus.Histogram // 数据库查询耗时

    // 错误指标
    ValidationErrors         prometheus.Counter   // 验证错误数
    APIErrors                prometheus.Counter   // API错误数
    NetworkErrors            prometheus.Counter   // 网络错误数

    // 资源指标
    ActiveWorkers            prometheus.Gauge     // 活跃工作协程数
    QueueLength              prometheus.Gauge     // 队列长度
    CacheHitRate             prometheus.Gauge     // 缓存命中率
}

// 指标初始化
func InitMeetingMetrics() *MeetingMetrics {
    return &MeetingMetrics{
        TotalMeetingsProcessed: prometheus.NewCounter(prometheus.CounterOpts{
            Name: "meeting_total_processed",
            Help: "Total number of meetings processed",
        }),
        SuccessfulMeetings: prometheus.NewCounter(prometheus.CounterOpts{
            Name: "meeting_successful_total",
            Help: "Total number of successfully created meetings",
        }),
        ProcessingDuration: prometheus.NewHistogram(prometheus.HistogramOpts{
            Name:    "meeting_processing_duration_seconds",
            Help:    "Time spent processing meetings",
            Buckets: prometheus.DefBuckets,
        }),
        // ... 其他指标初始化
    }
}
```

#### 系统监控指标

```yaml
# Prometheus 配置
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "meeting_rules.yml"

scrape_configs:
  - job_name: 'meeting-service'
    static_configs:
      - targets: ['localhost:9001']
    metrics_path: '/metrics'
    scrape_interval: 10s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

#### 告警规则配置

```yaml
# meeting_rules.yml
groups:
  - name: meeting.rules
    rules:
      # 会议处理失败率告警
      - alert: HighMeetingFailureRate
        expr: rate(meeting_failed_total[5m]) / rate(meeting_total_processed[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "会议处理失败率过高"
          description: "过去5分钟会议处理失败率超过10%"

      # API调用延迟告警
      - alert: HighAPILatency
        expr: histogram_quantile(0.95, rate(meeting_api_duration_seconds_bucket[5m])) > 5
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "API调用延迟过高"
          description: "95%的API调用延迟超过5秒"

      # 队列积压告警
      - alert: HighQueueLength
        expr: meeting_queue_length > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "任务队列积压严重"
          description: "任务队列长度超过100，可能存在处理瓶颈"
```

### 7.3 日志记录规范

#### 日志级别和格式

```go
// LogLevel 日志级别
type LogLevel string

const (
    LogLevelDebug LogLevel = "DEBUG"
    LogLevelInfo  LogLevel = "INFO"
    LogLevelWarn  LogLevel = "WARN"
    LogLevelError LogLevel = "ERROR"
    LogLevelFatal LogLevel = "FATAL"
)

// LogEntry 日志条目
type LogEntry struct {
    Timestamp   time.Time              `json:"timestamp"`
    Level       LogLevel               `json:"level"`
    Message     string                 `json:"message"`
    Module      string                 `json:"module"`
    TraceID     string                 `json:"trace_id,omitempty"`
    RecordID    string                 `json:"record_id,omitempty"`
    Duration    time.Duration          `json:"duration,omitempty"`
    Error       string                 `json:"error,omitempty"`
    Fields      map[string]interface{} `json:"fields,omitempty"`
}

// MeetingLogger 会议系统日志记录器
type MeetingLogger struct {
    logger *logrus.Logger
    module string
}

// 日志记录方法
func (ml *MeetingLogger) LogMeetingProcessStart(recordID string, meetingTitle string) {
    ml.logger.WithFields(logrus.Fields{
        "module":        ml.module,
        "record_id":     recordID,
        "meeting_title": meetingTitle,
        "action":        "process_start",
    }).Info("开始处理会议记录")
}

func (ml *MeetingLogger) LogMeetingProcessSuccess(recordID string, eventID string, duration time.Duration) {
    ml.logger.WithFields(logrus.Fields{
        "module":    ml.module,
        "record_id": recordID,
        "event_id":  eventID,
        "duration":  duration.Milliseconds(),
        "action":    "process_success",
    }).Info("会议处理成功")
}

func (ml *MeetingLogger) LogMeetingProcessError(recordID string, err error, duration time.Duration) {
    ml.logger.WithFields(logrus.Fields{
        "module":    ml.module,
        "record_id": recordID,
        "error":     err.Error(),
        "duration":  duration.Milliseconds(),
        "action":    "process_error",
    }).Error("会议处理失败")
}
```

#### 日志配置

```yaml
# logrus 配置
logging:
  level: info
  format: json
  output:
    - type: file
      path: /app/logs/meeting.log
      max_size: 100MB
      max_backups: 10
      max_age: 30
      compress: true
    - type: stdout
      format: text

  # 模块日志配置
  modules:
    meeting:
      level: debug
      fields:
        - record_id
        - event_id
        - duration
    feishu_api:
      level: info
      fields:
        - api_name
        - response_code
        - duration
```

### 7.4 性能优化建议

#### 数据库优化

```sql
-- 索引优化建议
-- 1. 复合索引优化查询性能
CREATE INDEX idx_meeting_status_time ON meeting_records(status, created_at);
CREATE INDEX idx_meeting_organizer_time ON meeting_records(organizer_id, start_time);

-- 2. 分区表优化大数据量查询
ALTER TABLE meeting_records PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 3. 定期清理历史数据
DELETE FROM meeting_process_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
```

#### 缓存优化策略

```go
// CacheStrategy 缓存策略
type CacheStrategy struct {
    // 用户信息缓存
    UserInfoTTL time.Duration // 用户信息缓存时间

    // 会议状态缓存
    MeetingStatusTTL time.Duration // 会议状态缓存时间

    // API响应缓存
    APIResponseTTL time.Duration // API响应缓存时间
}

// CacheManager 缓存管理器
type CacheManager struct {
    redis    *redis.Client
    strategy CacheStrategy
}

// 缓存用户信息
func (cm *CacheManager) CacheUserInfo(userID string, userInfo *UserInfo) error {
    key := fmt.Sprintf("user:info:%s", userID)
    data, _ := json.Marshal(userInfo)
    return cm.redis.Set(context.Background(), key, data, cm.strategy.UserInfoTTL).Err()
}

// 获取缓存的用户信息
func (cm *CacheManager) GetCachedUserInfo(userID string) (*UserInfo, error) {
    key := fmt.Sprintf("user:info:%s", userID)
    data, err := cm.redis.Get(context.Background(), key).Result()
    if err != nil {
        return nil, err
    }

    var userInfo UserInfo
    err = json.Unmarshal([]byte(data), &userInfo)
    return &userInfo, err
}
```

#### 并发处理优化

```go
// WorkerPool 工作协程池
type WorkerPool struct {
    workerCount int
    taskQueue   chan *MeetingTask
    wg          sync.WaitGroup
    ctx         context.Context
    cancel      context.CancelFunc
}

// MeetingTask 会议处理任务
type MeetingTask struct {
    RecordID string
    Data     *RawMeetingData
    Callback func(*MeetingProcessResult)
}

// NewWorkerPool 创建工作协程池
func NewWorkerPool(workerCount, queueSize int) *WorkerPool {
    ctx, cancel := context.WithCancel(context.Background())
    return &WorkerPool{
        workerCount: workerCount,
        taskQueue:   make(chan *MeetingTask, queueSize),
        ctx:         ctx,
        cancel:      cancel,
    }
}

// Start 启动工作协程池
func (wp *WorkerPool) Start() {
    for i := 0; i < wp.workerCount; i++ {
        wp.wg.Add(1)
        go wp.worker(i)
    }
}

// worker 工作协程
func (wp *WorkerPool) worker(id int) {
    defer wp.wg.Done()

    for {
        select {
        case task := <-wp.taskQueue:
            result := wp.processTask(task)
            if task.Callback != nil {
                task.Callback(result)
            }
        case <-wp.ctx.Done():
            return
        }
    }
}

// SubmitTask 提交任务
func (wp *WorkerPool) SubmitTask(task *MeetingTask) error {
    select {
    case wp.taskQueue <- task:
        return nil
    case <-wp.ctx.Done():
        return errors.New("工作协程池已关闭")
    default:
        return errors.New("任务队列已满")
    }
}
```

---

## 8. 测试策略

### 8.1 单元测试计划

#### 测试覆盖范围

```go
// 测试用例结构
type TestCase struct {
    Name        string
    Input       interface{}
    Expected    interface{}
    ExpectedErr error
    Setup       func()
    Teardown    func()
}

// 数据验证模块测试
func TestMeetingValidator(t *testing.T) {
    testCases := []TestCase{
        {
            Name: "有效会议数据验证",
            Input: &RawMeetingData{
                Fields: map[string]interface{}{
                    "meeting_title": "项目评审会议",
                    "start_time":    time.Now().Add(time.Hour).Unix() * 1000,
                    "end_time":      time.Now().Add(2 * time.Hour).Unix() * 1000,
                    "organizer":     "ou_844642732286f2ae9e7c8885d9757696",
                },
            },
            Expected:    true,
            ExpectedErr: nil,
        },
        {
            Name: "缺少必填字段",
            Input: &RawMeetingData{
                Fields: map[string]interface{}{
                    "start_time": time.Now().Add(time.Hour).Unix() * 1000,
                    "end_time":   time.Now().Add(2 * time.Hour).Unix() * 1000,
                },
            },
            Expected:    false,
            ExpectedErr: errors.New("会议标题不能为空"),
        },
        {
            Name: "无效时间范围",
            Input: &RawMeetingData{
                Fields: map[string]interface{}{
                    "meeting_title": "测试会议",
                    "start_time":    time.Now().Add(2 * time.Hour).Unix() * 1000,
                    "end_time":      time.Now().Add(time.Hour).Unix() * 1000,
                    "organizer":     "ou_844642732286f2ae9e7c8885d9757696",
                },
            },
            Expected:    false,
            ExpectedErr: errors.New("结束时间必须晚于开始时间"),
        },
    }

    validator := NewMeetingValidator()

    for _, tc := range testCases {
        t.Run(tc.Name, func(t *testing.T) {
            if tc.Setup != nil {
                tc.Setup()
            }
            defer func() {
                if tc.Teardown != nil {
                    tc.Teardown()
                }
            }()

            result, err := validator.Validate(tc.Input.(*RawMeetingData))

            assert.Equal(t, tc.Expected, result)
            if tc.ExpectedErr != nil {
                assert.Error(t, err)
                assert.Contains(t, err.Error(), tc.ExpectedErr.Error())
            } else {
                assert.NoError(t, err)
            }
        })
    }
}

// API调用模块测试
func TestCalendarService(t *testing.T) {
    // Mock飞书API客户端
    mockClient := &MockFeishuClient{}
    service := &CalendarServiceImpl{
        client: mockClient,
        config: &CalendarConfig{},
    }

    t.Run("成功创建日历事件", func(t *testing.T) {
        // 设置Mock期望
        mockClient.On("CreateEvent", mock.Anything).Return(&CalendarEventResponse{
            EventID: "event_123",
            Status:  "created",
        }, nil)

        req := &CalendarEventRequest{
            CalendarID: "primary",
            Summary:    "测试会议",
            StartTime: &CalendarEventTime{
                Timestamp: "1640995200",
                Timezone:  "Asia/Shanghai",
            },
            EndTime: &CalendarEventTime{
                Timestamp: "1640998800",
                Timezone:  "Asia/Shanghai",
            },
        }

        resp, err := service.CreateEvent(context.Background(), req)

        assert.NoError(t, err)
        assert.Equal(t, "event_123", resp.EventID)
        assert.Equal(t, "created", resp.Status)
        mockClient.AssertExpectations(t)
    })
}
```

#### 测试工具和框架

```go
// 测试依赖
import (
    "testing"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
    "github.com/stretchr/testify/suite"
    "github.com/DATA-DOG/go-sqlmock"
    "github.com/go-redis/redismock/v8"
)

// 测试套件
type MeetingServiceTestSuite struct {
    suite.Suite
    db       *sql.DB
    dbMock   sqlmock.Sqlmock
    redis    *redis.Client
    redisMock redismock.ClientMock
    service  *MeetingService
}

func (suite *MeetingServiceTestSuite) SetupTest() {
    // 设置数据库Mock
    db, mock, err := sqlmock.New()
    suite.Require().NoError(err)
    suite.db = db
    suite.dbMock = mock

    // 设置Redis Mock
    redis, redisMock := redismock.NewClientMock()
    suite.redis = redis
    suite.redisMock = redisMock

    // 初始化服务
    suite.service = NewMeetingService(db, redis)
}

func (suite *MeetingServiceTestSuite) TearDownTest() {
    suite.db.Close()
    suite.redis.Close()
}

func TestMeetingServiceTestSuite(t *testing.T) {
    suite.Run(t, new(MeetingServiceTestSuite))
}
```

### 8.2 集成测试方案

#### 测试环境配置

```yaml
# docker-compose.test.yml
version: '3.8'

services:
  meeting-service-test:
    build:
      context: .
      dockerfile: Dockerfile.test
    environment:
      - DB_HOST=mysql-test
      - REDIS_HOST=redis-test
      - MEETING_ENABLE=true
      - FEISHU_APP_ID=test_app_id
      - FEISHU_APP_SECRET=test_app_secret
    depends_on:
      - mysql-test
      - redis-test
    volumes:
      - ./test/data:/app/test/data

  mysql-test:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: test123
      MYSQL_DATABASE: iris_test
    ports:
      - "3307:3306"
    volumes:
      - ./test/sql:/docker-entrypoint-initdb.d

  redis-test:
    image: redis:6.0-alpine
    ports:
      - "6380:6379"
    command: redis-server --requirepass test123
```

#### 集成测试用例

```go
// 端到端测试
func TestMeetingProcessingE2E(t *testing.T) {
    // 准备测试数据
    testData := &RawMeetingData{
        RecordID: "test_record_001",
        Fields: map[string]interface{}{
            "meeting_title": "集成测试会议",
            "start_time":    time.Now().Add(time.Hour).Unix() * 1000,
            "end_time":      time.Now().Add(2 * time.Hour).Unix() * 1000,
            "organizer":     "ou_test_organizer",
            "attendees":     []string{"ou_test_attendee1", "ou_test_attendee2"},
            "location":      "会议室A",
            "description":   "这是一个集成测试会议",
        },
    }

    // 执行完整流程
    processor := NewMeetingProcessor()
    result, err := processor.ProcessSingleMeeting(context.Background(), testData.RecordID)

    // 验证结果
    assert.NoError(t, err)
    assert.Equal(t, "success", result.Status)
    assert.NotEmpty(t, result.EventID)

    // 验证数据库记录
    var record MeetingRecord
    err = db.Where("bitable_record_id = ?", testData.RecordID).First(&record).Error
    assert.NoError(t, err)
    assert.Equal(t, "success", record.Status)
    assert.Equal(t, result.EventID, record.CalendarEventID)
}

// API集成测试
func TestFeishuAPIIntegration(t *testing.T) {
    if testing.Short() {
        t.Skip("跳过API集成测试")
    }

    // 使用真实的飞书API进行测试
    client := lark.NewClient(testAppID, testAppSecret)
    service := &CalendarServiceImpl{
        client: client,
        config: &CalendarConfig{},
    }

    // 创建测试事件
    req := &CalendarEventRequest{
        CalendarID: testCalendarID,
        Summary:    "API集成测试",
        StartTime: &CalendarEventTime{
            Timestamp: strconv.FormatInt(time.Now().Add(time.Hour).Unix(), 10),
            Timezone:  "Asia/Shanghai",
        },
        EndTime: &CalendarEventTime{
            Timestamp: strconv.FormatInt(time.Now().Add(2*time.Hour).Unix(), 10),
            Timezone:  "Asia/Shanghai",
        },
    }

    resp, err := service.CreateEvent(context.Background(), req)
    assert.NoError(t, err)
    assert.NotEmpty(t, resp.EventID)

    // 清理测试数据
    defer func() {
        err := service.DeleteEvent(context.Background(), resp.EventID)
        assert.NoError(t, err)
    }()
}
```

### 8.3 性能测试指标

#### 性能测试场景

```go
// 性能测试配置
type PerformanceTestConfig struct {
    ConcurrentUsers    int           // 并发用户数
    TestDuration       time.Duration // 测试持续时间
    RampUpTime         time.Duration // 压力递增时间
    MeetingsPerSecond  int           // 每秒处理会议数
    MaxResponseTime    time.Duration // 最大响应时间
    SuccessRate        float64       // 成功率要求
}

// 负载测试
func TestMeetingProcessingLoad(t *testing.T) {
    config := PerformanceTestConfig{
        ConcurrentUsers:   50,
        TestDuration:      5 * time.Minute,
        RampUpTime:        30 * time.Second,
        MeetingsPerSecond: 10,
        MaxResponseTime:   5 * time.Second,
        SuccessRate:       0.95,
    }

    // 创建测试数据
    testMeetings := generateTestMeetings(1000)

    // 执行负载测试
    results := runLoadTest(config, testMeetings)

    // 验证性能指标
    assert.True(t, results.SuccessRate >= config.SuccessRate,
        "成功率 %.2f%% 低于要求 %.2f%%", results.SuccessRate*100, config.SuccessRate*100)
    assert.True(t, results.AverageResponseTime <= config.MaxResponseTime,
        "平均响应时间 %v 超过要求 %v", results.AverageResponseTime, config.MaxResponseTime)
    assert.True(t, results.P95ResponseTime <= config.MaxResponseTime*2,
        "95%响应时间 %v 超过要求 %v", results.P95ResponseTime, config.MaxResponseTime*2)
}

// 压力测试结果
type LoadTestResult struct {
    TotalRequests       int           // 总请求数
    SuccessfulRequests  int           // 成功请求数
    FailedRequests      int           // 失败请求数
    SuccessRate         float64       // 成功率
    AverageResponseTime time.Duration // 平均响应时间
    P95ResponseTime     time.Duration // 95%响应时间
    P99ResponseTime     time.Duration // 99%响应时间
    ThroughputPerSecond float64       // 每秒吞吐量
    ErrorDistribution   map[string]int // 错误分布
}
```

#### 性能基准测试

```go
// 基准测试
func BenchmarkMeetingValidation(b *testing.B) {
    validator := NewMeetingValidator()
    testData := generateValidMeetingData()

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        _, err := validator.Validate(testData)
        if err != nil {
            b.Fatal(err)
        }
    }
}

func BenchmarkMeetingProcessing(b *testing.B) {
    processor := NewMeetingProcessor()
    testData := generateTestMeetings(b.N)

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        _, err := processor.ProcessSingleMeeting(context.Background(), testData[i].RecordID)
        if err != nil {
            b.Fatal(err)
        }
    }
}

// 内存使用测试
func TestMemoryUsage(t *testing.T) {
    var m1, m2 runtime.MemStats
    runtime.GC()
    runtime.ReadMemStats(&m1)

    // 执行大量会议处理
    processor := NewMeetingProcessor()
    for i := 0; i < 1000; i++ {
        testData := generateValidMeetingData()
        processor.ProcessSingleMeeting(context.Background(), testData.RecordID)
    }

    runtime.GC()
    runtime.ReadMemStats(&m2)

    memoryUsed := m2.Alloc - m1.Alloc
    t.Logf("内存使用量: %d bytes", memoryUsed)

    // 验证内存使用不超过阈值
    maxMemoryUsage := uint64(100 * 1024 * 1024) // 100MB
    assert.True(t, memoryUsed < maxMemoryUsage,
        "内存使用量 %d 超过阈值 %d", memoryUsed, maxMemoryUsage)
}
```

---

## 总结

本技术设计文档详细描述了自动化会议邀请系统的完整设计方案，涵盖了从系统架构到具体实现的各个方面。文档的主要特点：

1. **完整性**: 覆盖了系统设计的所有关键方面
2. **可执行性**: 提供了具体的代码示例和配置文件
3. **可维护性**: 考虑了系统的扩展性和维护需求
4. **可靠性**: 包含了完整的错误处理和监控方案

该设计方案基于现有代码库的深入分析，充分利用了现有的技术基础设施，确保了系统的稳定性和可靠性。通过模块化的设计和完善的测试策略，为后续的代码实现提供了明确的技术指导。
