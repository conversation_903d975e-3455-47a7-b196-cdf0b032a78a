username=$1
host=$2
port=$3
tech_support_file_path=$4
tech_support_file_name=`basename $tech_support_file_path`
filename=$(echo $tech_support_file_name | cut -d . -f1)
product=$5
tempname=$6
work_dir=$7
output="$work_dir/${filename}_${tempname}"

ssh_prefix="ssh $username@$host -p $port"

$ssh_prefix rm $output -rf
$ssh_prefix mkdir -p $output
scp -P $port $tech_support_file_path $username@$host:$output > /dev/null
if [ $? -eq 1 ];then exit 1;fi

command_params=""
if [ ! -z "$product" ]; then
    command_params="-p $product"
fi

command="ntos_gdb_init -f $output/$tech_support_file_name -o $output/temp $command_params -h http://************:9090 -q"
result=`$ssh_prefix $command`
if [ $? -eq 0 ];then
    echo $result
else
	echo $result
	exit 1
fi

