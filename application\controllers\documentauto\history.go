package documentauto

import (
	"irisAdminApi/application/libs/response"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/documentauto/ddaghistoricalversion"
	"strconv"
	"time"

	"github.com/kataras/iris/v12"
)

func GetHistoryLists(ctx iris.Context) {
	name := ctx.FormValue("name")
	seriesID, _ := strconv.Atoi(ctx.FormValue("series_id"))
	versionID, _ := strconv.Atoi(ctx.FormValue("version_id"))
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	historyList, err := ddaghistoricalversion.GetHistoryList(name, seriesID, versionID, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, historyList, response.NoErr.Msg))
}

func CreateHistoryRelease(ctx iris.Context) {
	request := ddaghistoricalversion.Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	historyRelease := ddaghistoricalversion.Response{}
	err := historyRelease.Create(
		map[string]interface{}{
			"SeriesID":    request.SeriesID,
			"VersionID":   request.VersionID,
			"ReleaseDate": request.ReleaseDate,
			"CreatedAt":   time.Now(),
			"UpdatedAt":   time.Now(),
		},
	)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
}

func UpdateHistoryRelease(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	request := ddaghistoricalversion.Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	historyRelease := ddaghistoricalversion.Response{}
	err := historyRelease.Update(id, map[string]interface{}{
		"SeriesID":    request.SeriesID,
		"VersionID":   request.VersionID,
		"ReleaseDate": request.ReleaseDate,
		"UpdatedAt":   time.Now(),
	})
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
}

func DeleteHistoryRelease(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	historyRelease := ddaghistoricalversion.Response{}
	err := historyRelease.Delete(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
}
