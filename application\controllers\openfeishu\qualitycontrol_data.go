package openfeishu

// 飞书表格查询响应结构
type FeishuTableResponse struct {
	Code int `json:"code"`
	Data struct {
		HasMore   bool              `json:"has_more"`
		Items     []FeishuTableItem `json:"items"`
		PageToken string            `json:"page_token"`
		Total     int               `json:"total"`
	} `json:"data"`
	Msg string `json:"msg"`
}

type FeishuTableItem struct {
	Fields   map[string]interface{} `json:"fields"`
	RecordID string                 `json:"record_id"`
}

// 品控单号索引结构
type QualityNumberIndex struct {
	QualityNumber string `json:"quality_number"`
	RecordID      string `json:"record_id"`
}

// 品控API响应结构
type QualityControlResponse struct {
	Rows  []QualityControlItem `json:"rows"`
	Total int                  `json:"total"`
}

type QualityControlItem struct {
	// 基础信息
	ID         int    `json:"id"`         // 品控单号
	Title      string `json:"title"`      // 标题
	CreateDate string `json:"createDate"` // 创建时间
	ModifyDate string `json:"modifyDate"` // 修改时间

	// 责任人信息
	FaultUserName   string `json:"faultUserName"`   // 故障责任人
	QualityUserName string `json:"qualityUserName"` // 品控责任人
	SubmitterName   string `json:"submitterName"`   // 提交人

	// 状态信息
	PzkzStatus     string `json:"pzkzStatus"`     // 品控状态
	PzkzTypeName   string `json:"pzkzTypeName"`   // 品控类别（技术咨询、问题单等）
	IsFinish       int    `json:"isFinish"`       // 是否完成 (0/1)
	SolutionFinish int    `json:"solutionFinish"` // 解决方案是否完成 (0/1)

	// 时间相关
	TimeDay      float64 `json:"timeDay"`      // 超期天数
	DealDay      float64 `json:"dealDay"`      // 处理天数
	SolutionDate string  `json:"solutionDate"` // 解决方案时间
	FinishDate   string  `json:"finishDate"`   // 完成时间

	// 故障等级
	FaultLvlBaseName string `json:"faultLvlBaseName"` // 基础故障等级名称
	FaultLvlName     string `json:"faultLvlName"`     // 当前故障等级名称
	FaultLvlTime     string `json:"faultLvlTime"`     // 故障等级时间（小时）
	FaultLvlBaseTime string `json:"faultLvlBaseTime"` // 基础故障等级时间（小时）
	FaultLvl         string `json:"faultLvl"`         // 故障等级代码
	FaultLvlBase     int    `json:"faultLvlBase"`     // 基础故障等级代码

	// 故障原因
	DevFaultReasonName string `json:"devFaultReasonName"` // 研发故障原因
	DevFaultReason     int    `json:"devFaultReason"`     // 研发故障原因代码
	CsmFaultReasonName string `json:"csmFaultReasonName"` // CSM故障原因
	CsmFaultReason     int    `json:"csmFaultReason"`     // CSM故障原因代码

	// 产品信息
	ProductModel    string `json:"productModel"`    // 产品型号
	ProductLine     string `json:"productLine"`     // 产品线
	ProductSeries   string `json:"productSeries"`   // 产品系列
	Version         string `json:"version"`         // 版本
	SoftWareVersion string `json:"softWareVersion"` // 软件版本

	// 客户信息
	CustomerName      string `json:"customerName"`      // 客户名称
	FinalCustomerName string `json:"finalCustomerName"` // 最终客户名称

	// 其他字段
	ExtendHour   int    `json:"extendHour"`   // 延期小时
	CsmId        string `json:"csmId"`        // CSM ID
	BugId        string `json:"bugId"`        // Bug ID
	Conclusion   string `json:"conclusion"`   // 结论
	SolutionDesc string `json:"solutionDesc"` // 解决方案描述
}

// 闭环管理API响应结构
type ClosedLoopResponse struct {
	Type string `json:"type"`
	Data struct {
		ID                          int    `json:"id"`
		EntityID                    int    `json:"entityId"`
		EntityType                  string `json:"entityType"`
		RootCauseDescription        string `json:"rootCauseDescription"`
		RootCauseType               int    `json:"rootCauseType"`
		PreventMeasure              string `json:"preventMeasure"`
		PreventUserId               int    `json:"preventUserId"`
		PreventUserName             string `json:"preventUserName"`
		PreventUserGroupLeaderId    int    `json:"preventUserGroupLeaderId"`
		PreventUserGroupLeaderName  string `json:"preventUserGroupLeaderName"`
		PreventUserDeptLeaderId     int    `json:"preventUserDeptLeaderId"`
		PreventUserDeptLeaderName   string `json:"preventUserDeptLeaderName"`
		PlanClosedDate              string `json:"planClosedDate"`
		ActClosedDate               string `json:"actClosedDate"`
		ClosedStatus                string `json:"closedStatus"`
		ClosedLevel                 string `json:"closedLevel"`
		ClosedEvidence              string `json:"closedEvidence"`
		DeliverableName             string `json:"deliverableName"`
		RootCauseCheck              string `json:"rootCauseCheck"`
		PreventMeasureCheck         string `json:"preventMeasureCheck"`
		PreventMeasureValidityCheck string `json:"preventMeasureValidityCheck"`
		CreateDate                  string `json:"createDate"`
		ModifyDate                  string `json:"modifyDate"`
		AnalysisStatus              string `json:"analysisStatus"`
	} `json:"data"`
	Content string `json:"content"`
}

// 飞书更新数据结构
type FeishuQualityUpdateRecord struct {
	QualityResponsible string `json:"品控责任人"`
	IsClosed           string `json:"品控问题是否关闭"`
	OverdueStatus      string `json:"超期情况"`
	ProblemEfficiency  string `json:"问题时效"`
	IsClosedLoop       string `json:"品控问题是否闭环"`
}

// 品控数据同步配置
type QualityControlConfig struct {
	EnableClosedLoop bool `json:"enable_closed_loop"` // 是否启用闭环管理
	BatchSize        int  `json:"batch_size"`         // 批处理大小
	MaxRetries       int  `json:"max_retries"`        // 最大重试次数
}

// 同步统计结构
type SyncStatistics struct {
	TotalRecords          int    `json:"total_records"`            // 飞书表格总记录数
	MatchedRecords        int    `json:"matched_records"`          // 成功匹配的记录数
	UpdatedRecords        int    `json:"updated_records"`          // 成功更新的记录数
	SkippedRecords        int    `json:"skipped_records"`          // 跳过的记录数
	ErrorRecords          int    `json:"error_records"`            // 错误记录数
	TypeErrors            int    `json:"type_errors"`              // 数据类型错误数
	UserMatchFailures     int    `json:"user_match_failures"`      // 用户匹配失败数
	NoClosedLoopRecords   int    `json:"no_closed_loop_records"`   // 无需闭环的记录数
	NeedClosedLoopRecords int    `json:"need_closed_loop_records"` // 需要闭环的记录数
	StartTime             string `json:"start_time"`               // 同步开始时间
	EndTime               string `json:"end_time"`                 // 同步结束时间
	Duration              string `json:"duration"`                 // 同步耗时
}

// 同步结果详情
type SyncResult struct {
	QualityNumber string   `json:"quality_number"` // 品控单号
	RecordID      string   `json:"record_id"`      // 飞书记录ID
	Status        string   `json:"status"`         // 同步状态：success, error, skipped
	Message       string   `json:"message"`        // 详细信息
	UpdatedFields []string `json:"updated_fields"` // 更新的字段列表
}

// 数据映射配置
type FieldMapping struct {
	FeishuField   string `json:"feishu_field"`   // 飞书字段名
	QualityField  string `json:"quality_field"`  // 品控系统字段名
	ClosedField   string `json:"closed_field"`   // 闭环管理字段名
	SolutionField string `json:"solution_field"` // 解决方案字段名
	DataType      string `json:"data_type"`      // 数据类型
	DefaultValue  string `json:"default_value"`  // 默认值
	Required      bool   `json:"required"`       // 是否必填
}

// 同步配置
type SyncConfig struct {
	BatchSize        int            `json:"batch_size"`         // 批处理大小
	RetryCount       int            `json:"retry_count"`        // 重试次数
	RetryDelay       int            `json:"retry_delay"`        // 重试延迟(秒)
	TimeoutSeconds   int            `json:"timeout_seconds"`    // 超时时间
	FieldMappings    []FieldMapping `json:"field_mappings"`     // 字段映射配置
	EnableClosedLoop bool           `json:"enable_closed_loop"` // 是否启用闭环管理
}

// SolutionResponse 解决方案API响应结构体
type SolutionResponse struct {
	DealDay          int           `json:"dealDay"`
	AccessoryList    []interface{} `json:"accessoryList"`
	QuestionID       int           `json:"questionId"`
	ProgressDate     string        `json:"progressDate"`
	ExcellentCase    int           `json:"excellentCase"`
	PlanFinishDay    string        `json:"planFinishDay"`
	IsDown           string        `json:"isDown"`
	SolutionTypeName string        `json:"solutionTypeName"`
	Disabled         int           `json:"disabled"`
	ID               int           `json:"id"`
	SolutionType     int           `json:"solutionType"`
	Email            string        `json:"email"`
	CreateDate       string        `json:"createDate"`
	SolutionDesc     string        `json:"solutionDesc"`
	ModifyDate       string        `json:"modifyDate"`
	EntityType       string        `json:"entityType"`
	DownProgress     string        `json:"downProgress"`
	NeedWatch        string        `json:"needWatch"`
	ProgressList     []interface{} `json:"progressList"`
	IsFinish         int           `json:"isFinish"` // 关键字段：0=否，1=是
	UserName         string        `json:"userName"`
	Version          string        `json:"version"`
	RejectTimes      int           `json:"rejectTimes"`
	Prescription     int           `json:"prescription"`
	Progress         string        `json:"progress"`
	DayNum           int           `json:"dayNum"`
	ReviewState      int           `json:"reviewState"`
	QuestionType     string        `json:"questionType"`
	ChargeUserID     int           `json:"chargeUserId"`
}
