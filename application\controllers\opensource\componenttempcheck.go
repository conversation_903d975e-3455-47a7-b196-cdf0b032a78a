package opensource

import (
	"fmt"
	"strconv"

	"github.com/kataras/iris/v12"

	"irisAdminApi/application/controllers/opensource/taskmanagers"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/opensource"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/opensource/dcomponenttempcheck"
)

type CreateComponentTempCheckReq struct {
	ComponentName    string `json:"component_name"`
	ComponentVersion string `json:"component_version"`
}

func CreateComponentTempCheck(ctx iris.Context) {
	authUserId, _ := dao.GetAuthId(ctx)
	req := &CreateComponentTempCheckReq{}
	if err := ctx.ReadJSON(req); err != nil {
		logging.ErrorLogger.Errorf("create component temp check read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if req.ComponentName == "" {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "组件名参数<component_name>不能为空"))
		return
	}
	if req.ComponentVersion == "" {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "组件版本参数<component_version>不能为空"))
		return
	}

	tempCheckRes := &dcomponenttempcheck.Response{}
	tempCheckRes.ComponentName = req.ComponentName
	tempCheckRes.ComponentVersion = req.ComponentVersion
	tempCheckRes.Status = opensource.ComponentTempCheckStatusProcessing
	tempCheckRes.UserID = authUserId

	err := dcomponenttempcheck.CreateComponentTempCheck(tempCheckRes)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, tempCheckRes, response.NoErr.Msg))
	if taskmanagers.TempSyncCVETaskManager != nil {
		taskmanagers.TempSyncCVETaskManager.AddComponentTempSyncTask(tempCheckRes)
	}
	return
}

func GetComponentTempCheck(ctx iris.Context) {
	tempCheckId, _ := ctx.Params().GetUint("id")
	res, err := dcomponenttempcheck.GetComponentTempCheck(tempCheckId)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if res == nil {
		ctx.JSON(response.NewResponse(response.DataEmptyErr.Code, nil, "预查记录不存在"))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, res, response.NoErr.Msg))
	return
}

func ListComponentTempChecks(ctx iris.Context) {
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	componentName := ctx.FormValue("component_name")
	componentVersion := ctx.FormValue("component_version")
	status, _ := strconv.Atoi(ctx.FormValue("status"))
	userId, _ := strconv.Atoi(ctx.FormValue("user_id"))
	sort := ctx.FormValue("sort")
	orderBy := ctx.FormValue("orderBy")
	createdAt := ctx.FormValue("created_at")
	updatedAt := ctx.FormValue("updated_at")

	list, err := dcomponenttempcheck.ListComponentTempCheck(page, pageSize, componentName,
		componentVersion, uint(status), sort, orderBy, createdAt, updatedAt, uint(userId))
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func DownloadComponentTempCheckResult(ctx iris.Context) {
	tempCheckId, _ := ctx.Params().GetUint("id")
	res, err := dcomponenttempcheck.GetComponentTempCheck(tempCheckId)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if res == nil {
		ctx.JSON(response.NewResponse(response.DataEmptyErr.Code, nil, "预查记录不存在"))
		return
	}

	if res.Status != opensource.ComponentTempCheckStatusSucceeded {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "预查还处理中或预查失败"))
		return
	}
	ctx.SendFile(res.ResultFilename, fmt.Sprintf("%d.pdf", res.ID))
}
