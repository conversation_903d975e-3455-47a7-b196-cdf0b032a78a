package dproductdata

import (
	"fmt"
	"reflect"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/release"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "产品数据" //(来源BIAPI)

type Response struct {
	ID              uint   `json:"id"`
	ProductId       string `json:"product_id"`       //产品ID
	ItemNum         string `json:"item_num"`         //编号
	ItemDesc        string `json:"item_desc"`        //描述
	ItemAttType     string `json:"item_att_type"`    //类型
	ProductLine     string `json:"product_line" `    //产品线
	ProductFamily   string `json:"product_family"`   //产品系列
	ProductCategory string `json:"product_category"` //产品类别
	ProductSeries   string `json:"product_series" `  //产品系列号
	ProductModel    string `json:"product_model" `   //产品型号
}

type ListResponse struct {
	Response
}

type Request struct {
	ProductId       string `json:"product_id"        form:"product_id"`       //产品ID
	ItemNum         string `json:"item_num"          form:"item_num"`         //编号
	ItemDesc        string `json:"item_desc"         form:"item_desc"`        //描述
	ItemAttType     string `json:"item_att_type"     form:"item_att_type"`    //类型
	ProductLine     string `json:"product_line"      form:"product_line"`     //产品线
	ProductFamily   string `json:"product_family"    form:"product_family"`   //产品系列
	ProductCategory string `json:"product_category"  form:"product_category"` //产品类别
	ProductSeries   string `json:"product_series"    form:"product_series"`   //产品系列号
	ProductModel    string `json:"product_model"     form:"product_model"`    //产品型号
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *release.ProductData {
	return &release.ProductData{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func AllProductBIDatas(uid uint, name, sort, orderBy string, page, pageSize int, status, start, end string) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	// db = db.Where("user_id = ?", uid)

	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	if len(status) > 0 {
		// if status == "0" {
		// 	db = db.Where("status = ?", status)
		// } else {
		// 	db = db.Where("status > 0")
		// }
		db = db.Where("status = ?", status)
	}
	if len(start) > 0 {
		db = db.Where("updated_at >= ?", start)
	}
	if len(end) > 0 {
		db = db.Where("updated_at <= ?", end)
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindAll() ([]*Response, error) {
	var items []*Response

	if err := easygorm.GetEasyGormDb().Model(Model()).Find(&items).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return items, err
	}
	return items, nil
}

func FindInIds(ids []uint) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id in ?", ids).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	return items, nil
}

func UpdateStatus(id uint, object map[string]interface{}) error {

	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func FindProductDatas(col, value string) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Select("project_model").Distinct("product_model").Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	return items, nil
}

func BatchCreate(objects []map[string]interface{}) error {
	columns := []string{"updated_at"}

	xt := reflect.TypeOf(Model())
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")
		if ok && ok2 {
			columns = append(columns, name)
		}
	}

	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		if len(objects) > 0 {
			err := tx.Model(Model()).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "product_id"}},
				DoUpdates: clause.AssignmentColumns(columns),
			}).Create(&objects).Error
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return err
	}
	return nil
}
