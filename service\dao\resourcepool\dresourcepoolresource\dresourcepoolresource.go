package dresourcepoolresource

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/resourcepool"
	"strings"
	"time"

	"gorm.io/gorm/clause"
)

const ModelName = "资源管理"

/*
	Description     string `gorm:"not null; type:varchar(512)"`
	ProblemSourceID uint   `gorm:"not null"`
	PlanCloseAt     string `gorm:"not null; type:varchar(30)"`
	UserID          uint   `gorm:"not null"`
	Status          uint   `gorm:"not null；default:0"` //0:未闭环  1:已闭环
	OwnerID         uint   `gorm:"not null"`
*/

type ResourcePoolResource struct {
	resourcepool.ResourcePoolResource
}

type ListResponse struct {
	ResourcePoolResource
}

type Request struct {
	resourcepool.ResourcePoolResource
}

func (a *ResourcePoolResource) ModelName() string {
	return ModelName
}

func Model() *resourcepool.ResourcePoolResource {
	return &resourcepool.ResourcePoolResource{}
}

func (a *ResourcePoolResource) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ResourcePoolResource

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("mgt_ip like ?", fmt.Sprintf("%%%s%%", name)).Or("reserved like ?", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *ResourcePoolResource) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ResourcePoolResource

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *ResourcePoolResource) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *ResourcePoolResource) CreateV2(object interface{}) error {
	return nil
}

func (a *ResourcePoolResource) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update resource error: %v", err)
		return err
	}
	return nil
}

func (a *ResourcePoolResource) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find resource by user_id error: %v", err)
		return err
	}
	return nil
}

func (a *ResourcePoolResource) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find resource by auditor_id error: %v", err)
		return err
	}
	return nil
}

func (a *ResourcePoolResource) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete resource by id [%d] failed: %v", id, err)
		return fmt.Errorf("failed to delete resource: %v", err)
	}
	return nil
}

func (u *ResourcePoolResource) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find resource pool info err: %v", err)
		return err
	}
	return nil
}

func (u *ResourcePoolResource) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

type ResourcePoolResourceWithInterfaces struct {
	*ResourcePoolResource
	Interfaces []*resourcepool.ResourcePoolInterface
}

func All_admin(userId uint, name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ResourcePoolResource
	var res []*ResourcePoolResourceWithInterfaces

	db := easygorm.GetEasyGormDb().Model(Model())
	where := easygorm.GetEasyGormDb().Model(Model())

	if len(libs.Config.Resourcepool.Testbed) > 0 {
		db = db.Where("testbed in (?)", strings.Split(libs.Config.Resourcepool.Testbed, ","))
	}

	if len(name) > 0 {
		where = where.Where("name like ?", fmt.Sprintf("%%%s%%", name)).
			Or("domain like ?", fmt.Sprintf("%%%s%%", name)).
			Or("model like ?", fmt.Sprintf("%%%s%%", name)).
			Or("make like ?", fmt.Sprintf("%%%s%%", name)).
			Or("osname like ?", fmt.Sprintf("%%%s%%", name)).
			Or("mgt_ip like ?", fmt.Sprintf("%%%s%%", name)).
			Or("mgt_port like ?", fmt.Sprintf("%%%s%%", name)).
			Or("con_ip like ?", fmt.Sprintf("%%%s%%", name)).
			Or("con_port like ?", fmt.Sprintf("%%%s%%", name)).
			Or("testbed like ?", fmt.Sprintf("%%%s%%", name)).
			Or("type like ?", fmt.Sprintf("%%%s%%", name)).
			Or("status like ?", fmt.Sprintf("%%%s%%", name)).
			Or("rack like ?", fmt.Sprintf("%%%s%%", name)).
			Or("slot like ?", fmt.Sprintf("%%%s%%", name)).
			Or("reserved like ?", fmt.Sprintf("%%%s%%", name)).
			Or("start_time like ?", fmt.Sprintf("%%%s%%", name)).
			Or("end_time like ?", fmt.Sprintf("%%%s%%", name)).
			Or("sn like ?", fmt.Sprintf("%%%s%%", name)).
			Or("resource_id like ?", fmt.Sprintf("%%%s%%", name)).
			Or("location like ?", fmt.Sprintf("%%%s%%", name)).
			Or("room like ?", fmt.Sprintf("%%%s%%", name)).
			Or("comments like ?", fmt.Sprintf("%%%s%%", name))
	}
	// if userId != 1 {
	// 	db = db.Where("user_id = ?", userId).Where(where)
	// } else {
	// 	db = db.Where(where)
	// }
	db = db.Where(where)

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	sort = "ASC"
	orderBy = "id"
	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	for _, item := range items {
		rname := item.Name
		var rinterfaces []*resourcepool.ResourcePoolInterface
		if err := easygorm.GetEasyGormDb().Where("name = ?", rname).Find(&rinterfaces).Error; err != nil {
			logging.ErrorLogger.Errorf("get resource pool interfaces err ", err)
			return nil, err
		}
		for _, iface := range rinterfaces {
			vlanID := iface.VlanId
			if vlanID == "999" {
				continue
			}
			var connections []*resourcepool.ResourcePoolInterface
			if err := easygorm.GetEasyGormDb().Where("vlan_id = ? AND name <> ?", vlanID, rname).Find(&connections).Error; err != nil {
				logging.ErrorLogger.Errorf("get resource pool interfaces err ", err)
				return nil, err
			}

			for _, conn := range connections {
				cname := conn.Name
				cinterface := conn.Interface
				iface.Connections += cname + " " + cinterface + " "
			}
		}
		itemWithInterfaces := &ResourcePoolResourceWithInterfaces{
			ResourcePoolResource: item,
			Interfaces:           rinterfaces,
		}
		res = append(res, itemWithInterfaces)
	}

	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func All_admin_with_type(userId uint, name, sort, orderBy string, page, pageSize int, ruijie, testbed string) (map[string]interface{}, error) {
	var count int64
	var items []*ResourcePoolResource
	var res []*ResourcePoolResourceWithInterfaces

	db := easygorm.GetEasyGormDb().Model(Model())
	where := easygorm.GetEasyGormDb().Model(Model())
	if len(libs.Config.Resourcepool.Testbed) > 0 {
		db = db.Where("testbed in (?)", strings.Split(libs.Config.Resourcepool.Testbed, ","))
	}

	if len(testbed) > 0 {
		db = db.Where("testbed = ?", testbed)
	}

	if len(name) > 0 {
		where = where.Where("name like ?", fmt.Sprintf("%%%s%%", name)).
			Or("domain like ?", fmt.Sprintf("%%%s%%", name)).
			Or("model like ?", fmt.Sprintf("%%%s%%", name)).
			Or("make like ?", fmt.Sprintf("%%%s%%", name)).
			Or("osname like ?", fmt.Sprintf("%%%s%%", name)).
			Or("mgt_ip like ?", fmt.Sprintf("%%%s%%", name)).
			Or("mgt_port like ?", fmt.Sprintf("%%%s%%", name)).
			Or("con_ip like ?", fmt.Sprintf("%%%s%%", name)).
			Or("con_port like ?", fmt.Sprintf("%%%s%%", name)).
			Or("testbed like ?", fmt.Sprintf("%%%s%%", name)).
			Or("type like ?", fmt.Sprintf("%%%s%%", name)).
			Or("status like ?", fmt.Sprintf("%%%s%%", name)).
			Or("rack like ?", fmt.Sprintf("%%%s%%", name)).
			Or("slot like ?", fmt.Sprintf("%%%s%%", name)).
			Or("reserved like ?", fmt.Sprintf("%%%s%%", name)).
			Or("start_time like ?", fmt.Sprintf("%%%s%%", name)).
			Or("end_time like ?", fmt.Sprintf("%%%s%%", name)).
			Or("sn like ?", fmt.Sprintf("%%%s%%", name)).
			Or("resource_id like ?", fmt.Sprintf("%%%s%%", name)).
			Or("location like ?", fmt.Sprintf("%%%s%%", name)).
			Or("room like ?", fmt.Sprintf("%%%s%%", name)).
			Or("comments like ?", fmt.Sprintf("%%%s%%", name))
	}
	switch ruijie {
	case "ruijie":
		where = where.Where("make = 'Ruijie'")
	case "":
	default:
		where = where.Where("make <> 'Ruijie'")
	}
	// if ruijie == "ruijie" {
	// 	where = where.Where("make = 'Ruijie'")
	// } else {
	// 	where = where.Where("make <> 'Ruijie'")
	// }

	db = db.Where(where)

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	sort = "ASC"
	orderBy = "id"
	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	for _, item := range items {
		rname := item.Name
		var rinterfaces []*resourcepool.ResourcePoolInterface
		if err := easygorm.GetEasyGormDb().Where("name = ?", rname).Find(&rinterfaces).Error; err != nil {
			logging.ErrorLogger.Errorf("get resource pool interfaces err ", err)
			return nil, err
		}
		for _, iface := range rinterfaces {
			vlanID := iface.VlanId
			if vlanID == "999" {
				continue
			}
			var connections []*resourcepool.ResourcePoolInterface
			if err := easygorm.GetEasyGormDb().Where("vlan_id = ? AND name <> ?", vlanID, rname).Find(&connections).Error; err != nil {
				logging.ErrorLogger.Errorf("get resource pool interfaces err ", err)
				return nil, err
			}

			for _, conn := range connections {
				cname := conn.Name
				cinterface := conn.Interface
				iface.Connections += cname + " " + cinterface + " "
			}
		}
		itemWithInterfaces := &ResourcePoolResourceWithInterfaces{
			ResourcePoolResource: item,
			Interfaces:           rinterfaces,
		}
		res = append(res, itemWithInterfaces)
	}

	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func All(userId uint, name, sort, orderBy string, page, pageSize int, testbed string) (map[string]interface{}, error) {
	var count int64
	var items []*ResourcePoolResource
	var res []*ResourcePoolResourceWithInterfaces

	db := easygorm.GetEasyGormDb().Model(Model())
	where := easygorm.GetEasyGormDb().Model(Model())
	if len(libs.Config.Resourcepool.Testbed) > 0 {
		db = db.Where("testbed in (?)", strings.Split(libs.Config.Resourcepool.Testbed, ","))
	}
	if len(testbed) > 0 {
		db = db.Where("testbed = ?", testbed)
	}
	if len(name) > 0 {
		where = where.Where("name like ?", fmt.Sprintf("%%%s%%", name)).
			Or("domain like ?", fmt.Sprintf("%%%s%%", name)).
			Or("model like ?", fmt.Sprintf("%%%s%%", name)).
			Or("make like ?", fmt.Sprintf("%%%s%%", name)).
			Or("osname like ?", fmt.Sprintf("%%%s%%", name)).
			Or("mgt_ip like ?", fmt.Sprintf("%%%s%%", name)).
			Or("mgt_port like ?", fmt.Sprintf("%%%s%%", name)).
			Or("con_ip like ?", fmt.Sprintf("%%%s%%", name)).
			Or("con_port like ?", fmt.Sprintf("%%%s%%", name)).
			Or("testbed like ?", fmt.Sprintf("%%%s%%", name)).
			Or("type like ?", fmt.Sprintf("%%%s%%", name)).
			Or("status like ?", fmt.Sprintf("%%%s%%", name)).
			Or("rack like ?", fmt.Sprintf("%%%s%%", name)).
			Or("slot like ?", fmt.Sprintf("%%%s%%", name)).
			Or("reserved like ?", fmt.Sprintf("%%%s%%", name)).
			Or("start_time like ?", fmt.Sprintf("%%%s%%", name)).
			Or("end_time like ?", fmt.Sprintf("%%%s%%", name)).
			Or("sn like ?", fmt.Sprintf("%%%s%%", name)).
			Or("resource_id like ?", fmt.Sprintf("%%%s%%", name)).
			Or("location like ?", fmt.Sprintf("%%%s%%", name)).
			Or("room like ?", fmt.Sprintf("%%%s%%", name)).
			Or("comments like ?", fmt.Sprintf("%%%s%%", name))
	}

	db = db.Where(where)

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	sort = "ASC"
	orderBy = "id"

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	for _, item := range items {
		rname := item.Name
		var rinterfaces []*resourcepool.ResourcePoolInterface
		if err := easygorm.GetEasyGormDb().Where("name = ?", rname).Find(&rinterfaces).Error; err != nil {
			logging.ErrorLogger.Errorf("get resource pool interfaces err ", err)
			return nil, err
		}
		for _, iface := range rinterfaces {
			vlanID := iface.VlanId
			if vlanID == "999" {
				continue
			}
			var connections []*resourcepool.ResourcePoolInterface
			if err := easygorm.GetEasyGormDb().Where("vlan_id = ? AND name <> ?", vlanID, rname).Find(&connections).Error; err != nil {
				logging.ErrorLogger.Errorf("get resource pool interfaces err ", err)
				return nil, err
			}

			for _, conn := range connections {
				cname := conn.Name
				cinterface := conn.Interface
				iface.Connections += cname + " " + cinterface + " "
			}
		}
		itemWithInterfaces := &ResourcePoolResourceWithInterfaces{
			ResourcePoolResource: item,
			Interfaces:           rinterfaces,
		}
		res = append(res, itemWithInterfaces)
	}

	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}
func GetNowUserName(userId uint) (string, error) {
	var username string
	if err := easygorm.GetEasyGormDb().Raw("SELECT username FROM users WHERE id = ?", userId).Scan(&username).Error; err != nil {
		logging.ErrorLogger.Errorf("get user name err ", err)
		return "", err
	}
	return username, nil
}

func GetAllMyResources(userId uint, served bool, name, sort, orderBy string, page, pageSize int, testbed string) (map[string]interface{}, error) {
	var count int64
	var items []*ResourcePoolResource
	var username string
	var res []*ResourcePoolResourceWithInterfaces

	if err := easygorm.GetEasyGormDb().Raw("SELECT username FROM users WHERE id = ?", userId).Scan(&username).Error; err != nil {
		logging.ErrorLogger.Errorf("get user name err ", err)
		return nil, err
	}

	db := easygorm.GetEasyGormDb().Model(Model())
	where := easygorm.GetEasyGormDb().Model(Model())

	if len(libs.Config.Resourcepool.Testbed) > 0 {
		db = db.Where("testbed in (?)", strings.Split(libs.Config.Resourcepool.Testbed, ","))
	}

	if len(testbed) > 0 {
		db = db.Where("testbed = ?", testbed)
	}

	if len(name) > 0 {
		where = where.Where("name like ?", fmt.Sprintf("%%%s%%", name)).
			Or("domain like ?", fmt.Sprintf("%%%s%%", name)).
			Or("model like ?", fmt.Sprintf("%%%s%%", name)).
			Or("make like ?", fmt.Sprintf("%%%s%%", name)).
			Or("osname like ?", fmt.Sprintf("%%%s%%", name)).
			Or("mgt_ip like ?", fmt.Sprintf("%%%s%%", name)).
			Or("mgt_port like ?", fmt.Sprintf("%%%s%%", name)).
			Or("con_ip like ?", fmt.Sprintf("%%%s%%", name)).
			Or("con_port like ?", fmt.Sprintf("%%%s%%", name)).
			Or("testbed like ?", fmt.Sprintf("%%%s%%", name)).
			Or("type like ?", fmt.Sprintf("%%%s%%", name)).
			Or("status like ?", fmt.Sprintf("%%%s%%", name)).
			Or("rack like ?", fmt.Sprintf("%%%s%%", name)).
			Or("slot like ?", fmt.Sprintf("%%%s%%", name)).
			Or("reserved like ?", fmt.Sprintf("%%%s%%", name)).
			Or("start_time like ?", fmt.Sprintf("%%%s%%", name)).
			Or("end_time like ?", fmt.Sprintf("%%%s%%", name)).
			Or("sn like ?", fmt.Sprintf("%%%s%%", name)).
			Or("resource_id like ?", fmt.Sprintf("%%%s%%", name)).
			Or("location like ?", fmt.Sprintf("%%%s%%", name)).
			Or("room like ?", fmt.Sprintf("%%%s%%", name)).
			Or("comments like ?", fmt.Sprintf("%%%s%%", name))
	}
	if served {
		where = where.Where(`status = "active" AND reserved = ?`, username)
	} else {
		where = where.Where(`status = "active" AND (reserved = ? OR reserved = "" OR reserved is NULL)`, username)
	}

	db = db.Where(where)

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	sort = "desc"
	orderBy = "reserved"

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	for _, item := range items {
		rname := item.Name
		var rinterfaces []*resourcepool.ResourcePoolInterface
		if err := easygorm.GetEasyGormDb().Where("name = ?", rname).Find(&rinterfaces).Error; err != nil {
			logging.ErrorLogger.Errorf("get resource pool interfaces err ", err)
			return nil, err
		}
		for _, iface := range rinterfaces {
			vlanID := iface.VlanId
			if vlanID == "999" {
				continue
			}
			var connections []*resourcepool.ResourcePoolInterface
			if err := easygorm.GetEasyGormDb().Where("vlan_id = ? AND name <> ?", vlanID, rname).Find(&connections).Error; err != nil {
				logging.ErrorLogger.Errorf("get resource pool interfaces err ", err)
				return nil, err
			}

			for _, conn := range connections {
				cname := conn.Name
				cinterface := conn.Interface
				iface.Connections += cname + " " + cinterface + " "
			}
		}
		itemWithInterfaces := &ResourcePoolResourceWithInterfaces{
			ResourcePoolResource: item,
			Interfaces:           rinterfaces,
		}
		res = append(res, itemWithInterfaces)
	}

	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func GetAllMyResources_type(userId uint, name, sort, orderBy string, page, pageSize int, ruijie, testbed string) (map[string]interface{}, error) {
	var count int64
	var items []*ResourcePoolResource
	var username string
	var res []*ResourcePoolResourceWithInterfaces

	if err := easygorm.GetEasyGormDb().Raw("SELECT username FROM users WHERE id = ?", userId).Scan(&username).Error; err != nil {
		logging.ErrorLogger.Errorf("get user name err ", err)
		return nil, err
	}

	db := easygorm.GetEasyGormDb().Model(Model())
	where := easygorm.GetEasyGormDb().Model(Model())
	if len(libs.Config.Resourcepool.Testbed) > 0 {
		db = db.Where("testbed in (?)", strings.Split(libs.Config.Resourcepool.Testbed, ","))
	}

	if len(testbed) > 0 {
		db = db.Where("testbed = ?", testbed)
	}

	if len(name) > 0 {
		where = where.Where("name like ?", fmt.Sprintf("%%%s%%", name)).
			Or("domain like ?", fmt.Sprintf("%%%s%%", name)).
			Or("model like ?", fmt.Sprintf("%%%s%%", name)).
			Or("make like ?", fmt.Sprintf("%%%s%%", name)).
			Or("osname like ?", fmt.Sprintf("%%%s%%", name)).
			Or("mgt_ip like ?", fmt.Sprintf("%%%s%%", name)).
			Or("mgt_port like ?", fmt.Sprintf("%%%s%%", name)).
			Or("con_ip like ?", fmt.Sprintf("%%%s%%", name)).
			Or("con_port like ?", fmt.Sprintf("%%%s%%", name)).
			Or("testbed like ?", fmt.Sprintf("%%%s%%", name)).
			Or("type like ?", fmt.Sprintf("%%%s%%", name)).
			Or("status like ?", fmt.Sprintf("%%%s%%", name)).
			Or("rack like ?", fmt.Sprintf("%%%s%%", name)).
			Or("slot like ?", fmt.Sprintf("%%%s%%", name)).
			Or("reserved like ?", fmt.Sprintf("%%%s%%", name)).
			Or("start_time like ?", fmt.Sprintf("%%%s%%", name)).
			Or("end_time like ?", fmt.Sprintf("%%%s%%", name)).
			Or("sn like ?", fmt.Sprintf("%%%s%%", name)).
			Or("resource_id like ?", fmt.Sprintf("%%%s%%", name)).
			Or("location like ?", fmt.Sprintf("%%%s%%", name)).
			Or("room like ?", fmt.Sprintf("%%%s%%", name)).
			Or("comments like ?", fmt.Sprintf("%%%s%%", name))
	}

	switch ruijie {
	case "ruijie":
		where = where.Where("make = 'Ruijie' AND status = ? AND (reserved = ? OR reserved = ? OR reserved IS NULL)", "active", username, "")
	case "":
		where = where.Where("status = ? AND (reserved = ? OR reserved = ? OR reserved IS NULL)", "active", username, "")
	default:
		where = where.Where("make <> 'Ruijie' AND status = ? AND (reserved = ? OR reserved = ? OR reserved IS NULL)", "active", username, "")
	}
	// if ruijie == "ruijie" {
	// 	where = where.Where("make = 'Ruijie' AND status = ? AND (reserved = ? OR reserved = ? OR reserved IS NULL)", "active", username, "")
	// } else {
	// 	where = where.Where("make <> 'Ruijie' AND status = ? AND (reserved = ? OR reserved = ? OR reserved IS NULL)", "active", username, "")
	// }

	db = db.Where(where)

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	sort = "DESC"
	orderBy = "id"

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	for _, item := range items {
		rname := item.Name
		var rinterfaces []*resourcepool.ResourcePoolInterface
		if err := easygorm.GetEasyGormDb().Where("name = ?", rname).Find(&rinterfaces).Error; err != nil {
			logging.ErrorLogger.Errorf("get resource pool interfaces err ", err)
			return nil, err
		}
		for _, iface := range rinterfaces {
			vlanID := iface.VlanId
			if vlanID == "999" {
				continue
			}
			var connections []*resourcepool.ResourcePoolInterface
			if err := easygorm.GetEasyGormDb().Where("vlan_id = ? AND name <> ?", vlanID, rname).Find(&connections).Error; err != nil {
				logging.ErrorLogger.Errorf("get resource pool interfaces err ", err)
				return nil, err
			}

			for _, conn := range connections {
				cname := conn.Name
				cinterface := conn.Interface
				iface.Connections += cname + " " + cinterface + " "
			}
		}
		itemWithInterfaces := &ResourcePoolResourceWithInterfaces{
			ResourcePoolResource: item,
			Interfaces:           rinterfaces,
		}
		res = append(res, itemWithInterfaces)
	}

	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func AllName() (map[string]interface{}, error) {

	rows, err := easygorm.GetEasyGormDb().Raw("SELECT DISTINCT name FROM resource_pool_resources").Rows()
	if err != nil {
		logging.ErrorLogger.Errorf("get resource name err ", err)
		return nil, err
	}
	var names []string
	for rows.Next() {
		var name string
		if err := rows.Scan(&name); err != nil {
			logging.ErrorLogger.Errorf("get resource name err ", err)
			return nil, err
		}
		names = append(names, name)
	}

	list := map[string]interface{}{"names": names}
	return list, nil
}

func AllUsers() (map[string]interface{}, error) {

	rows, err := easygorm.GetEasyGormDb().Raw("SELECT DISTINCT username FROM users").Rows()
	if err != nil {
		logging.ErrorLogger.Errorf("get resource users err ", err)
		return nil, err
	}
	var names []string
	for rows.Next() {
		var name string
		if err := rows.Scan(&name); err != nil {
			logging.ErrorLogger.Errorf("get resource name err ", err)
			return nil, err
		}
		names = append(names, name)
	}

	list := map[string]interface{}{"users": names}
	return list, nil
}

func CreateResource(request Request) (map[string]interface{}, error) {

	if request.Status == "" {
		request.Status = "active"
	}

	var MgtIp interface{}
	if request.MgtIp == "" {
		MgtIp = nil
	} else {
		MgtIp = request.MgtIp
	}

	// var StartTime interface{}
	// if request.StartTime.Format("2006-01-02T15:04:05Z") == "2000-01-01T00:00:00Z" {
	// 	StartTime = nil
	// } else {
	// 	StartTime = request.StartTime
	// }

	// var EndTime interface{}
	// if request.EndTime.Format("2006-01-02T15:04:05Z") == "2000-01-01T00:00:00Z" {
	// 	EndTime = nil
	// } else {
	// 	EndTime = request.EndTime
	// }
	_, err := easygorm.GetEasyGormDb().Raw(
		"INSERT INTO resource_pool_resources "+
			"(name, domain, model, make, osname, mgt_ip, mgt_port, con_ip, con_port, testbed, type, status, rack, slot, reserved, "+
			"start_time, end_time, sn, resource_id, location, room, comments, created_at, updated_at) "+
			"VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
		request.Name,
		request.Domain,
		request.Model,
		request.Make,
		request.Osname,
		MgtIp,
		request.MgtPort,
		request.ConIp,
		request.ConPort,
		request.Testbed,
		request.Type,
		request.Status,
		request.Rack,
		request.Slot,
		request.Reserved,
		request.StartTime,
		request.EndTime,
		request.SN,
		request.ResourceId,
		request.Location,
		request.Room,
		request.Comments,
		time.Now(),
		time.Now()).Rows()

	if err != nil {
		logging.ErrorLogger.Errorf("create resource err ", err)

		return nil, err
	}

	return nil, nil
}

func FindResourceByNames(names []string) ([]*ResourcePoolResource, error) {
	result := []*ResourcePoolResource{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("name in ?", names).Find(&result).Error
	if err != nil {
		return result, err
	}
	return result, nil
}

func FindResourceByName(name string) (ResourcePoolResource, error) {
	result := ResourcePoolResource{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("name = ?", name).Find(&result).Error
	if err != nil {
		return result, err
	}
	return result, nil
}

func AllReservedResource() ([]*ListResponse, error) {
	items := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).
		Where("reserved != '' or reserved is not null").
		Where("start_time != '' or start is not null").
		Where("end_time != '' or end is not null").
		Find(&items).
		Error

	return items, err
}

func FindResourcesInIDs(ids []uint) ([]*ListResponse, error) {
	items := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).
		Where("id in ?", ids).
		Find(&items).
		Error
	return items, err
}

func BatchUpdateReserved(objects []map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Model(Model()).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "id"}},
		DoUpdates: clause.AssignmentColumns([]string{"reserved", "start_time", "end_time"}),
	}).Create(&objects).Error
	return err
}
