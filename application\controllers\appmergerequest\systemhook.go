package appmergerequest

import (
	"bufio"
	"bytes"
	"fmt"
	"os/exec"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/appmergerequest/dappmergerequest"
	"irisAdminApi/service/dao/buildfarm/dproject"
	"irisAdminApi/service/dao/datasync/dcommonntosfileworkpackage"
	"irisAdminApi/service/dao/mergerequest/dmergerequest"
	"irisAdminApi/service/dao/mergerequest/dmergerequesthistory"

	"github.com/imroc/req/v3"
	"github.com/kataras/iris/v12"
	"github.com/pkg/errors"
)

var GitlabClient *req.Client

func InitGitLabClient() {
	GitlabClient = req.C().
		SetCommonRetryCount(5).
		// Set the retry sleep interval with a commonly used algorithm: capped exponential backoff with jitter (https://aws.amazon.com/blogs/architecture/exponential-backoff-and-jitter/).
		SetCommonRetryBackoffInterval(1*time.Second, 5*time.Second).
		SetCommonQueryParam("private_token", libs.Config.Gitlab.Token).
		SetBaseURL(fmt.Sprintf("%s/api/%s/", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version)).
		AddCommonRetryCondition(func(resp *req.Response, err error) bool {
			return err != nil
		})
	// if libs.Config.Debug {
	// 	GitlabClient.DevMode()
	// }
}

type Request struct {
	ObjectKind string `json:"object_kind"`
	EventType  string `json:"event_type"`
	Project    struct {
		ID int `json:"id"`
	} `json:"project"`
	ObjectAttributes struct {
		ID         int    `json:"id"`
		IID        int    `json:"iid"`
		CreatedAt  string `json:"created_at"`
		UpdatedAt  string `json:"updated_at"`
		State      string `json:"state"`
		Action     string `json:"action"`
		LastCommit struct {
			ID string `json:"id"`
		} `json:"last_commit"`
		Description     string `json:"description"`
		SourceProjectID int    `json:"source_project_id"`
		SourceBranch    string `json:"source_branch"`
		TargetProjectID int    `json:"target_project_id"`
		TargetBranch    string `json:"target_branch"`
		Source          struct {
			GitSSHUrl         string `json:"git_ssh_url"`
			PathWithNamespace string `json:"path_with_namespace"`
		} `json:"source"`
		Target struct {
			GitSSHUrl         string `json:"git_ssh_url"`
			PathWithNamespace string `json:"path_with_namespace"`
		} `json:"target"`
		Url string `json:"url"`
	} `json:"object_attributes"`
}

var verifyToken string = "Aqyfzx@gitlab2022"

func SystemHook(ctx iris.Context) {
	token := ctx.GetHeader("X-Gitlab-Token")
	event := ctx.GetHeader("X-Gitlab-Event")
	if verifyToken != token {
		logging.ErrorLogger.Errorf("token not match!")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if event != "System Hook" {
		logging.ErrorLogger.Errorf("X-Gitlab-Event not match System Hook!")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	request := Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("create project read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(&request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		logging.ErrorLogger.Errorf("create project read json err ", strings.Join(errs, ";"))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	switch request.EventType {
	case "merge_request":
		// sync merge request
		var mr dappmergerequest.AppMergeRequest
		var err error
		for i := 0; i < 3; i++ {
			mr, err = dappmergerequest.FindByProjetIDAndMergeRequestIID(uint(request.Project.ID), uint(request.ObjectAttributes.IID))
			if err != nil {
				logging.ErrorLogger.Errorf("find merge request error ", err.Error())
				continue
			}
			if mr.ID > 0 {
				break
			}
			time.Sleep(time.Second * 1)
		}

		if err != nil {
			logging.ErrorLogger.Errorf("find merge request error ", err.Error())
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}

		if mr.ID == 0 {
			now := time.Now()
			err := mr.Create(map[string]interface{}{
				"ReleaseProjectID": 0,
				"ReleaseProject":   "",
				"WorkPackageID":    0,
				"WorkPackage":      "",
				"MirrorFrom":       "",
				"Type":             "",
				"BugID":            "",
				"LocalBuildPass":   false,
				"Portable":         false,
				"PreCheck":         false,
				"MFAL":             0,
				"ScreenshotFile":   "",

				"Title":              "",
				"OriginTitle":        "",
				"Description":        "",
				"OriginDescription":  "",
				"UserID":             0,
				"PipelineStatus":     0,
				"PhabricatorStatus":  0,
				"CodeQuantityAdd":    0,
				"CodeQuantityRemove": 0,
				"ReviewerIDs":        "",
				"AssigneeIDs":        "",
				"DependencyIDs":      "",
				"SourceProject":      request.ObjectAttributes.Source.PathWithNamespace,
				"SourceProjectID":    request.ObjectAttributes.SourceProjectID,
				"SourceBranch":       request.ObjectAttributes.SourceBranch,
				"TargetProject":      request.ObjectAttributes.Target.PathWithNamespace,
				"TargetProjectID":    request.ObjectAttributes.TargetProjectID,
				"TargetBranch":       request.ObjectAttributes.TargetBranch,
				"CreatedAt":          now,
				"UpdatedAt":          now,
				"MergeRequestID":     request.ObjectAttributes.ID,
				"MergeRequestIID":    request.ObjectAttributes.IID,
				"Status":             1,
				"CodeCheckStatus":    0,
			})
			if err != nil {
				logging.ErrorLogger.Errorf("find merge request error ", err.Error())
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
				return
			}
			mr, err = dappmergerequest.FindByProjetIDAndMergeRequestIID(uint(request.Project.ID), uint(request.ObjectAttributes.IID))
			if err != nil {
				logging.ErrorLogger.Errorf("find merge request error ", err.Error())
				return
			}
		}

		if err := request.SyncMergeRequest(&mr); err != nil {
			logging.ErrorLogger.Errorf("sync merge request err ", err.Error())
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}

		if err := request.StartMergeRequestPipeline(&mr); err != nil {
			logging.ErrorLogger.Errorf("start merge request pipeline err ", err.Error())
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}

		ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
		return

	default:
		ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
		return
	}
}

func (r *Request) SyncMergeRequest(mr *dappmergerequest.AppMergeRequest) error {
	// stateMap := map[string]uint{
	// 	"opened": 1,
	// 	"merged": 3,
	// 	"closed": 4,
	// }
	actionMap := map[string]uint{
		"update":   0,
		"reopen":   1,
		"open":     1,
		"approved": 2,
		"merge":    3,
		"close":    4,
	}

	pipelineStatusMap := map[string]uint{
		"not_config": 3,
		"failed":     2,
		"success":    1,
		"running":    0,
	}

	mr.OverTime = false

	if int(actionMap[r.ObjectAttributes.Action]) > mr.Status || (mr.Status == 4 && actionMap[r.ObjectAttributes.Action] == 1) {
		mr.Status = int(actionMap[r.ObjectAttributes.Action])
	}

	var pipelineStatus string
	pipeline := r.GetMergeRequsetPipelines()
	if pipeline != nil {
		pipelineStatus = pipeline.Status
	} else {
		pipelineStatus = "not_config"
	}
	mr.PipelineStatus = pipelineStatusMap[pipelineStatus]

	// 判定是否超时
	if int(actionMap[r.ObjectAttributes.Action]) == 3 {
		if libs.WorkDayCount(mr.CreatedAt, time.Now()) > 3 {
			mr.OverTime = true
		}
	}

	if err := mr.Update(mr.ID, map[string]interface{}{
		"Status":         mr.Status,
		"PipelineStatus": mr.PipelineStatus,
		"OverTime":       mr.OverTime,
	}); err != nil {
		logging.ErrorLogger.Errorf("update merge request error ", err.Error())
	}

	return nil
}

func (r *Request) StartMergeRequestPipeline(mr *dappmergerequest.AppMergeRequest) error {
	if r.ObjectAttributes.Action == "close" {
		pipeline := r.GetMergeRequsetPipelines()
		if pipeline != nil && pipeline.ID != 0 {
			r.CancelMergeRequsetPipeline(pipeline.ID)
		}
	}

	if r.ObjectAttributes.State == "opened" && r.ObjectAttributes.Action == "update" {
		// update code check status
		// update ai check status
		mr.CodeCheckStatus = 0
		mr.AiCheckStatus = 0
		if err := mr.Update(mr.ID, map[string]interface{}{
			"CodeCheckStatus": mr.CodeCheckStatus,
			"AiCheckStatus":   mr.AiCheckStatus,
		}); err != nil {
			logging.ErrorLogger.Errorf("update merge request error ", err.Error())
		}
	}

	if r.ObjectAttributes.State == "opened" && (r.ObjectAttributes.Action == "open" || r.ObjectAttributes.Action == "update" || r.ObjectAttributes.Action == "reopen") {
		// 增加白名单，不进行pipeline检查
		if !libs.InArrayInt([]int{33, 34}, r.Project.ID) {
			// 检查是否已经运行Pipeline, 检查Pipeline是否已过期
			time.Sleep(3 * time.Second)
			pipeline := r.GetMergeRequsetPipelines()
			if pipeline == nil || pipeline.Sha != r.ObjectAttributes.LastCommit.ID {
				if pipeline != nil && pipeline.ID != 0 {
					r.CancelMergeRequsetPipeline(pipeline.ID)
				}
				r.CreateMergeRequsetPipeline()
			}
		} else {
			logging.DebugLogger.Debugf("skip pipeline, %d %s", r.Project.ID, r.ObjectAttributes.Url)
		}
	}

	return nil
}

type Pipeline struct {
	ID        int    `json:"id"`
	Sha       string `json:"sha"`
	Status    string `json:"status"`
	CreatedAt string `json:"created_at"`
	UpdatedAt string `json:"updated_at"`
}

func (r *Request) GetMergeRequestDiff(mr *dmergerequest.MergeRequest, hisData map[string]interface{}) error {
	id := mr.TargetProjectID
	// url := fmt.Sprintf("%s/api/%s/projects/%d/merge_requests/%d/changes", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, id, r.ObjectAttributes.IID)
	// var result ChangesResponse
	// var errMsg MessageResponse
	// // resp, err := GitlabClient.R().SetQueryParam("access_raw_diffs", "true").SetSuccessResult(&result).SetErrorResult(&errMsg).Get(url)
	// resp, err := GitlabClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).Get(url)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("get merge request change get err ", err)
	// 	return err
	// }

	project := dproject.Response{}
	err := project.FindEx("gitlab_id", fmt.Sprintf("%v", mr.TargetProjectID))
	if err != nil {
		return err
	}

	mergerequestDetail, err := MergeRequest(fmt.Sprintf("%v", id), fmt.Sprintf("%v", r.ObjectAttributes.IID))
	if err != nil {
		return err
	}
	mergeCommitSha := mergerequestDetail.MergeCommitSha
	if mergeCommitSha != nil && *mergeCommitSha != "" {
		targetBranch := mergerequestDetail.TargetBranch
		repo := project.Repo

		tempDir := filepath.Join("/tmp", *mergeCommitSha)
		command := fmt.Sprintf("git clone -b %s %s %s", targetBranch, repo, tempDir)
		output, err := libs.ExecCommand(command)
		if err != nil {
			return errors.Wrap(err, output)
		}

		command = fmt.Sprintf("cd %s && git show -m %s", tempDir, *mergeCommitSha)
		diff, err := libs.ExecCommand(command)
		if err != nil {
			return errors.Wrap(err, diff)
		}

		remove := 0
		add := 0
		addPattern := regexp.MustCompile(`\n\+\s*\n`)
		removePattern := regexp.MustCompile(`\n\-\s*\n`)
		commonNtosFileWorkPackageObjects := []map[string]interface{}{}

		repoNameArr := strings.Split(mr.TargetProject, "/")
		repoName := repoNameArr[len(repoNameArr)-1]

		remove = remove + strings.Count(diff, "\n-") - len(removePattern.FindAllStringIndex(diff, -1))
		add = add + strings.Count(diff, "\n+") - len(addPattern.FindAllStringIndex(diff, -1))

		// 使用 strings.NewReader 创建一个读取器
		reader := strings.NewReader(diff)

		// 使用 bufio.NewScanner 创建一个扫描器
		scanner := bufio.NewScanner(reader)

		// 按行读取字符串
		for scanner.Scan() {
			line := scanner.Text() // 获取当前行的内容
			prefix := "+++ b"
			if strings.HasPrefix(line, prefix) {
				fullPath := strings.ReplaceAll(line, prefix, repoName)
				commonNtosFileWorkPackageObjects = append(commonNtosFileWorkPackageObjects, map[string]interface{}{
					"FullPath":       fullPath,
					"WorkPackage":    mr.WorkPackage,
					"TargetProject":  mr.TargetProject,
					"TargetBranch":   mr.TargetBranch,
					"ReleaseProject": mr.ReleaseProject,
					"MergeRequestID": mr.ID,
				})
			}
		}

		// 检查扫描过程中是否有错误
		if err := scanner.Err(); err != nil {
			logging.ErrorLogger.Errorf("scan diff error ", err.Error())
		}

		mr.CodeQuantityAdd = uint(add)
		mr.CodeQuantityRemove = uint(remove)
		if err := mr.Update(mr.ID, map[string]interface{}{
			"CodeQuantityAdd":    add,
			"CodeQuantityRemove": remove,
			"SyncStatus":         1,
		}); err != nil {
			logging.ErrorLogger.Errorf("update merge request error ", err.Error())
		}

		hisData["CodeQuantityAdd"] = add
		hisData["CodeQuantityRemove"] = remove
		hisData["SyncStatus"] = 1

		his := dmergerequesthistory.MergeRequestHistory{}
		err = his.CreateHistory(mr.ID, hisData)
		if err != nil {
			logging.ErrorLogger.Errorf("create merge request history get err ", mr.ID, err)
		}

		err = dcommonntosfileworkpackage.BatchCreate(commonNtosFileWorkPackageObjects)
		if err != nil {
			logging.ErrorLogger.Errorf("create common ntos file workpackage get err ", mr.ID, err)
		}

		// 获取代码变更数据
		return nil
	}

	logging.ErrorLogger.Errorf("update merge request %d sync get err ", mr.ID, errors.New("未知错误"))
	return errors.New("未知错误")
}

func (r *Request) GetMergeRequsetPipelines() *Pipeline {
	results := []*Pipeline{}
	var errMsg interface{}
	resp, err := GitlabClient.R().SetSuccessResult(&results).SetErrorResult(&errMsg).Get(fmt.Sprintf("projects/%d/merge_requests/%d/pipelines", r.Project.ID, r.ObjectAttributes.IID))
	if err != nil {
		logging.ErrorLogger.Errorf("get pipeline error", err.Error())
		return nil
	}
	if resp.IsSuccessState() {
		if len(results) > 0 {
			pipeline := r.GetProjectPipeline(results[0].ID)
			return pipeline
		}
	}
	return nil
}

func (r *Request) GetProjectPipeline(pipelineID int) *Pipeline {
	result := &Pipeline{}
	var errMsg interface{}
	resp, err := GitlabClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).Get(fmt.Sprintf("projects/%d/pipelines/%d", r.Project.ID, pipelineID))
	if err != nil {
		logging.ErrorLogger.Errorf("get pipeline error", err.Error())
		return nil
	}
	if resp.IsSuccessState() {
		return result
	}
	return nil
}

func (r *Request) CreateMergeRequsetPipeline() error {
	var errMsg interface{}
	resp, err := GitlabClient.R().SetErrorResult(&errMsg).Post(fmt.Sprintf("projects/%d/merge_requests/%d/pipelines", r.Project.ID, r.ObjectAttributes.IID))
	if err != nil {
		logging.ErrorLogger.Errorf("get pipeline error", err.Error())
		return err
	}
	if resp.IsError() {
		return errors.New(fmt.Sprintf("Unknow error, %s", errMsg))
	}
	return nil
}

func (r *Request) CancelMergeRequsetPipeline(pipelineID int) error {
	var errMsg interface{}
	resp, err := GitlabClient.R().SetErrorResult(&errMsg).Post(fmt.Sprintf("projects/%d/pipelines/%d/cancel", r.Project.ID, pipelineID))
	if err != nil {
		logging.ErrorLogger.Errorf("get pipeline error", err.Error())
		return err
	}
	if resp.IsError() {
		return errors.New(fmt.Sprintf("Unknow error, %s", errMsg))
	}
	return nil
}

func (r *Request) CreateMergeRequsetNote(comment string) error {
	var errMsg interface{}
	resp, err := GitlabClient.R().SetErrorResult(&errMsg).SetBody(map[string]string{"body": comment}).Post(fmt.Sprintf("projects/%d/merge_requests/%d/notes", r.Project.ID, r.ObjectAttributes.IID))
	if err != nil {
		logging.ErrorLogger.Errorf("create merge request notes error ", err.Error())
		return err
	}
	if resp.IsError() {
		return errors.New(fmt.Sprintf("Unknow error, %s", errMsg))
	}
	return nil
}

func (r *Request) RunScript(action string) error {
	command := ""
	mr, err := MergeRequest(strconv.FormatInt(int64(r.Project.ID), 10), strconv.FormatInt(int64(r.ObjectAttributes.IID), 10))
	if err != nil {
		return err
	}
	if mr != nil {
		reviewers := []string{}
		for _, reviewer := range mr.Reviewers {
			reviewers = append(reviewers, reviewer.Username)
		}
		author := mr.Author.Username
		// 正则匹配 <MFAL>关键字
		reg1 := regexp.MustCompile(`<Phabricator>[\r\s: ,，;\n]*[12][\r\s:,，;\n]+http://[\S]+D[0-9]+|<Phabricator>[\r\s:,，;\n]*[12]`)
		ret1 := reg1.FindAllString(mr.Description, -1)
		if len(ret1) > 0 {
			reg := regexp.MustCompile(`[\s:,，;\/]+`)
			ret := reg.Split(strings.Replace(ret1[0], "<Phabricator>", "", -1), -1)
			if action == "open" {
				if ret[0] == "1" {
					command = fmt.Sprintf(`sh creatediff.sh "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s"`, "create", r.ObjectAttributes.Source.GitSSHUrl, r.ObjectAttributes.SourceBranch, mr.Title, mr.DiffRefs.BaseSha, mr.DiffRefs.HeadSha, strings.Join(reviewers, ","), author, r.ObjectAttributes.Url)
					output, _ := ExecCommand(command)
					r.CreateMergeRequsetNote(output)
					return nil
				}
				if ret[0] == "2" {
					command = fmt.Sprintf(`sh creatediff.sh "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s"`, "update", r.ObjectAttributes.Source.GitSSHUrl, r.ObjectAttributes.SourceBranch, mr.Title, mr.DiffRefs.BaseSha, mr.DiffRefs.HeadSha, strings.Join(reviewers, ","), author, r.ObjectAttributes.Url, ret[len(ret)-1])
					output, _ := ExecCommand(command)
					r.CreateMergeRequsetNote(output)
					return nil
				}
			} else if action == "update" {
				if ret[0] == "1" {
					discussions, err := mr.GetMergeRequsetDiscussions()
					if err != nil {
						r.CreateMergeRequsetNote("更新phabricator diff失败: 查找关联Diff ID失败, " + err.Error())
					}
					diffId := ""
					reg := regexp.MustCompile(`Revision URI:[\s]+http://[\S]+D[0-9]+`)
					diffIdReg := regexp.MustCompile(`\/D[0-9]+`)
					for _, discussion := range discussions {
						for _, note := range discussion.Notes {
							res := reg.FindAllString(note.Body, -1)
							if len(res) > 0 {
								res := diffIdReg.FindAllString(res[0], -1)
								if len(res) > 0 {
									diffId = strings.Replace(res[0], "/", "", -1)
								}
							}
							if diffId != "" {
								break
							}
						}
						if diffId != "" {
							break
						}
					}
					if diffId == "" {
						command = fmt.Sprintf(`sh creatediff.sh "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s"`, "create", r.ObjectAttributes.Source.GitSSHUrl, r.ObjectAttributes.SourceBranch, mr.Title, mr.DiffRefs.BaseSha, mr.DiffRefs.HeadSha, strings.Join(reviewers, ","), author, r.ObjectAttributes.Url, diffId)
					} else {
						command = fmt.Sprintf(`sh creatediff.sh "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s"`, "update", r.ObjectAttributes.Source.GitSSHUrl, r.ObjectAttributes.SourceBranch, mr.Title, mr.DiffRefs.BaseSha, mr.DiffRefs.HeadSha, strings.Join(reviewers, ","), author, r.ObjectAttributes.Url, diffId)
					}
					output, _ := ExecCommand(command)
					r.CreateMergeRequsetNote(output)
					return nil
				}
				if ret[0] == "2" {
					command = fmt.Sprintf(`sh creatediff.sh "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s"`, "update", r.ObjectAttributes.Source.GitSSHUrl, r.ObjectAttributes.SourceBranch, mr.Title, mr.DiffRefs.BaseSha, mr.DiffRefs.HeadSha, strings.Join(reviewers, ","), author, r.ObjectAttributes.Url, ret[len(ret)-1])
					output, _ := ExecCommand(command)
					r.CreateMergeRequsetNote(output)
					return nil
				}
			} else if action == "close" {
				if ret[0] == "1" {
					discussions, err := mr.GetMergeRequsetDiscussions()
					if err != nil {
						r.CreateMergeRequsetNote("更新phabricator diff失败: 查找关联Diff ID失败, " + err.Error())
					}
					diffId := ""
					reg := regexp.MustCompile(`Revision URI:[\s]+http://[\S]+D[0-9]+`)
					diffIdReg := regexp.MustCompile(`\/D[0-9]+`)
					for _, discussion := range discussions {
						for _, note := range discussion.Notes {
							res := reg.FindAllString(note.Body, -1)
							if len(res) > 0 {
								res := diffIdReg.FindAllString(res[0], -1)
								if len(res) > 0 {
									diffId = strings.Replace(res[0], "/", "", -1)
								}
							}
							if diffId != "" {
								break
							}
						}
						if diffId != "" {
							break
						}
					}
					command = fmt.Sprintf(`sh creatediff.sh "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s"`, "close", r.ObjectAttributes.Source.GitSSHUrl, r.ObjectAttributes.SourceBranch, mr.Title, mr.DiffRefs.BaseSha, mr.DiffRefs.HeadSha, strings.Join(reviewers, ","), author, r.ObjectAttributes.Url, diffId)
					output, _ := ExecCommand(command)
					r.CreateMergeRequsetNote(output)
					return nil
				}
			}
		}
	}
	return nil
}

func (r *Request) RunScriptV2(action string) error {
	// 获取MR表单信息
	_mr, err := dmergerequest.FindByProjetIDAndMergeRequestIID(uint(r.Project.ID), uint(r.ObjectAttributes.IID))
	if err != nil {
		logging.ErrorLogger.Errorf("find merge request error ", err.Error())
		return err
	}
	if _mr.ID == 0 {
		logging.ErrorLogger.Errorf("merge request not found, project id: %d, merge request iid: %d ", r.Project.ID, r.ObjectAttributes.IID)
		return err
	}
	command := ""
	mr, err := MergeRequest(strconv.FormatInt(int64(r.Project.ID), 10), strconv.FormatInt(int64(r.ObjectAttributes.IID), 10))
	if err != nil {
		return err
	}
	if mr != nil {
		reviewers := []string{}
		for _, reviewer := range mr.Reviewers {
			reviewers = append(reviewers, reviewer.Username)
		}
		author := mr.Author.Username
		// 正则匹配 <MFAL>关键字
		// Phabricator动作

		if action == "open" {
			if _mr.MFAL == 1 {
				command = fmt.Sprintf(`sh creatediff.sh "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s"`, "create", r.ObjectAttributes.Source.GitSSHUrl, r.ObjectAttributes.SourceBranch, mr.Title, mr.DiffRefs.BaseSha, mr.DiffRefs.HeadSha, strings.Join(reviewers, ","), author, r.ObjectAttributes.Url)
				output, _ := ExecCommand(command)
				r.CreateMergeRequsetNote(output)
				// 获取output中的ID更新至数据库
				url := ""
				reg := regexp.MustCompile(`Revision URI:[\s]+http://[\S]+D[0-9]+`)
				res := reg.FindAllString(output, -1)

				if len(res) > 0 {
					url = strings.Replace(res[0], "Revision URI:", "", -1)
				}

				_mr.Phabricator.DifferentialUrl = url
				err := _mr.Phabricator.Save()
				if err != nil {
					logging.ErrorLogger.Errorf("update phabricator error: %s, mr id: %d, url: %s", err.Error(), _mr.ID, url)
					return err
				}
				return nil
			}
			if _mr.MFAL == 2 {
				diffID := ""
				diffIdReg := regexp.MustCompile(`\/D[0-9]+`)
				res := diffIdReg.FindAllString(_mr.Phabricator.DifferentialUrl, -1)
				if len(res) > 0 {
					diffID = strings.Replace(res[0], "/", "", -1)
				}
				command = fmt.Sprintf(`sh creatediff.sh "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s"`, "update", r.ObjectAttributes.Source.GitSSHUrl, r.ObjectAttributes.SourceBranch, mr.Title, mr.DiffRefs.BaseSha, mr.DiffRefs.HeadSha, strings.Join(reviewers, ","), author, r.ObjectAttributes.Url, diffID)
				output, _ := ExecCommand(command)
				r.CreateMergeRequsetNote(output)
				return nil
			}
		} else if action == "update" {
			if _mr.MFAL == 1 || _mr.MFAL == 2 {
				diffID := ""
				diffIdReg := regexp.MustCompile(`\/D[0-9]+`)
				res := diffIdReg.FindAllString(_mr.Phabricator.DifferentialUrl, -1)
				if len(res) > 0 {
					diffID = strings.Replace(res[0], "/", "", -1)
				}
				command = fmt.Sprintf(`sh creatediff.sh "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s"`, "update", r.ObjectAttributes.Source.GitSSHUrl, r.ObjectAttributes.SourceBranch, mr.Title, mr.DiffRefs.BaseSha, mr.DiffRefs.HeadSha, strings.Join(reviewers, ","), author, r.ObjectAttributes.Url, diffID)
				output, _ := ExecCommand(command)
				r.CreateMergeRequsetNote(output)
				return nil
			}
		} else if action == "close" {
			if _mr.MFAL == 1 || _mr.MFAL == 2 {
				diffID := ""
				diffIdReg := regexp.MustCompile(`\/D[0-9]+`)
				res := diffIdReg.FindAllString(_mr.Phabricator.DifferentialUrl, -1)
				if len(res) > 0 {
					diffID = strings.Replace(res[0], "/", "", -1)
				}
				command = fmt.Sprintf(`sh creatediff.sh "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s" "%s"`, "close", r.ObjectAttributes.Source.GitSSHUrl, r.ObjectAttributes.SourceBranch, mr.Title, mr.DiffRefs.BaseSha, mr.DiffRefs.HeadSha, strings.Join(reviewers, ","), author, r.ObjectAttributes.Url, diffID)
				output, _ := ExecCommand(command)
				r.CreateMergeRequsetNote(output)
				return nil
			}
		}

	}
	return nil
}

func ExecCommand(command string) (string, error) {
	if command == "" {
		return "command is nil", errors.New("command is nil")
	}
	logging.DebugLogger.Debugf(command)
	cmd := exec.Command("bash", "-c", command)
	var stdout bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr
	err := cmd.Run()
	if err != nil {
		logging.ErrorLogger.Errorf("run script err, ", stdout.String(), stderr.String())
		return stdout.String() + "\n" + stderr.String(), err
	}
	logging.DebugLogger.Debugf("run script success, ", stdout.String())
	return stdout.String(), nil
}
