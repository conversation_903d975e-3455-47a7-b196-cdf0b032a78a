from lxml import etree
import argparse
import sys

def parse_firewall_address(lines):
    """
    解析 `config firewall address` 块，提取地址配置信息。
    """
    inside_address_block = False
    current_address = None
    addresses = []

    for line in lines:
        line = line.strip()

        if line.startswith("config firewall address"):
            inside_address_block = True
            continue

        if inside_address_block:
            if line.startswith("end"):
                if current_address:
                    addresses.append(current_address)
                break

            elif line.startswith("edit"):
                if current_address:
                    addresses.append(current_address)
                current_address = {
                    "name": line.split('"')[1],
                    "type": None,
                    "start_ip": None,
                    "end_ip": None,
                    "subnet": None,
                    "mask": None,
                }

            elif line.startswith("set type"):
                if current_address:
                    current_address["type"] = line.split(" ")[-1]

            elif line.startswith("set start-ip"):
                if current_address:
                    current_address["start_ip"] = line.split(" ")[-1]

            elif line.startswith("set end-ip"):
                if current_address:
                    current_address["end_ip"] = line.split(" ")[-1]

            elif line.startswith("set subnet"):
                if current_address:
                    parts = line.split(" ")
                    current_address["subnet"] = parts[-2]
                    current_address["mask"] = parts[-1]

    valid_addresses = []
    for address in addresses:
        if address["type"] == "iprange" and address["start_ip"] and address["end_ip"]:
            valid_addresses.append(address)
        elif address["subnet"] and address["mask"]:
            valid_addresses.append(address)

    return valid_addresses


def parse_firewall_addrgrp(lines):
    """
    解析 `config firewall addrgrp` 块，提取服务组信息。
    """
    inside_service_group = False
    current_group = None
    address_groups = []

    for line in lines:
        line = line.strip()

        if line.startswith("config firewall addrgrp"):
            inside_service_group = True
            continue

        if inside_service_group:
            if line.startswith("end"):
                if current_group:
                    address_groups.append(current_group)
                break

            elif line.startswith("edit"):
                if current_group:
                    address_groups.append(current_group)
                current_group = {"name": line.split('"')[1], "members": []}

            elif line.startswith("set member"):
                members = line.split('"')[1::2]
                if current_group:
                    current_group["members"].extend(members)

    return address_groups

def parse_service_group(lines):
    """
    解析 `config firewall service group` 块，提取服务组信息。
    """
    inside_service_group = False
    current_group = None
    service_groups = []

    for line in lines:
        line = line.strip()

        if line.startswith("config firewall service group"):
            inside_service_group = True
            continue

        if inside_service_group:
            if line.startswith("end"):
                if current_group:
                    service_groups.append(current_group)
                break

            elif line.startswith("edit"):
                if current_group:
                    service_groups.append(current_group)
                current_group = {"name": line.split('"')[1], "members": []}

            elif line.startswith("set member"):
                members = line.split('"')[1::2]
                if current_group:
                    current_group["members"].extend(members)

    return service_groups


def parse_fortinet_policy(lines):
    """
    解析 `config firewall policy` 配置块，提取策略信息。
    """
    policies = []
    inside_policy_block = False
    current_policy = None

    for line in lines:
        line = line.strip()

        if line.startswith("config firewall policy"):
            inside_policy_block = True
            continue

        if inside_policy_block:
            if line.startswith("end"):
                if current_policy:
                    policies.append(current_policy)
                break

            elif line.startswith("edit"):
                if current_policy:
                    policies.append(current_policy)
                current_policy = {
                    "name": None,
                    "srcaddr": None,
                    "dstaddr": None,
                    "service": None,
                    "status": None,
                    "fixedport": None,
                    "comments": None,
                }

            elif line.startswith("set name"):
                if current_policy:
                    current_policy["name"] = line.split('"')[1]

            elif line.startswith("set srcaddr"):
                if current_policy:
                    current_policy["srcaddr"] = line.split('"')[1]

            elif line.startswith("set dstaddr"):
                if current_policy:
                    current_policy["dstaddr"] = line.split('"')[1]

            elif line.startswith("set service"):
                if current_policy:
                    current_policy["service"] = line.split('"')[1]

            elif line.startswith("set status"):
                if current_policy:
                    current_policy["status"] = line.split(" ")[-1]

            elif line.startswith("set fixedport"):
                if current_policy:
                    current_policy["fixedport"] = line.split(" ")[-1]

            elif line.startswith("set comments"):
                if current_policy:
                    current_policy["comments"] = line.split('"')[1]

    return policies


def generate_ruijie_nat_config(root, policies, ippools):
    """
    在锐捷 XML 中动态添加 NAT 配置信息。
    """
    nat_node = root.find(".//{urn:ruijie:ntos:params:xml:ns:yang:nat}nat")
    if nat_node is None:
        raise ValueError("无法找到 <nat> 节点，请检查模板文件")
    
    #添加IP Pool
    for ippool in ippools:
        enabled_node = etree.SubElement(nat_node,"enabled")
        enabled_node.text = "true"
        pool_node = etree.SubElement(nat_node, "pool")
        etree.SubElement(pool_node, "name").text = ippool["name"]
        address_node = etree.SubElement(pool_node, "address")
        address_node.text = f"{ippool['startip']}-{ippool['endip']}"

    for policy in policies:
        rule = etree.SubElement(nat_node, "rule")

        etree.SubElement(rule, "name").text = policy["name"] or ""
        etree.SubElement(rule, "desc").text = policy["comments"] or ""

        rule_en = etree.SubElement(rule, "rule_en")
        rule_en.text = "true" if policy["status"] != "disable" else "false"

        static_snat44 = etree.SubElement(rule, "static-snat44")
        match = etree.SubElement(static_snat44, "match")

        source_network = etree.SubElement(match, "source-network")
        etree.SubElement(source_network, "name").text = policy["srcaddr"]

        if policy["dstaddr"] and policy["dstaddr"].lower() != "all":
            dest_network = etree.SubElement(match, "dest-network")
            etree.SubElement(dest_network, "name").text = policy["dstaddr"]

        service = etree.SubElement(match, "service")
        etree.SubElement(service, "name").text = policy["service"] or "any"

        translate_to = etree.SubElement(static_snat44, "translate-to")
        etree.SubElement(translate_to, "output-address")

        no_pat = etree.SubElement(translate_to, "no-pat")
        no_pat.text = "true" if policy["fixedport"] == "enable" else "false"

def generate_ruijie_firewall_addresses(root, firewall_addresses):
    # 提取命名空间映射
    nsmap = root.nsmap
    network_obj_ns = "urn:ruijie:ntos:params:xml:ns:yang:network-obj"

    # 查找 <network-obj> 节点
    network_obj_node = root.find(f".//{{{network_obj_ns}}}network-obj")
    if network_obj_node is None:
        raise ValueError("无法找到 <network-obj> 节点，请检查模板文件")

# 添加地址配置信息
    for address in firewall_addresses:
        if not address.get("name") or not address.get("type"):
            continue
        if address["type"] == "iprange" and (not address.get("start_ip") or not address.get("end_ip")):
            continue
        if address["type"] == "subnet" and (not address.get("subnet") or not address.get("mask")):
            continue

        # 创建 <address-set> 节点
        address_set_node = etree.Element("address-set", nsmap=network_obj_node.nsmap)

        # 添加 <name> 节点
        name_node = etree.SubElement(address_set_node, "name")
        name_node.text = address["name"]

        # 添加 <ip-set> 节点及其内容
        ip_set_node = etree.SubElement(address_set_node, "ip-set")
        ip_address_node = etree.SubElement(ip_set_node, "ip-address")
        if address["type"] == "iprange":
            ip_address_node.text = f"{address['start_ip']}-{address['end_ip']}"
        elif address["type"] == "subnet":
            ip_address_node.text = f"{address['subnet']}/{address['mask']}"

        # 将 <address-set> 节点添加到 <network-obj> 节点
        network_obj_node.append(address_set_node)

def parse_anti_virus(lines):
    """
    解析 `config antivirus profile` 配置块，提取防病毒配置信息。
    """
    inside_av_block = False
    current_av_profile = None
    av_profiles = []

    for line in lines:
        line = line.strip()

        if line.startswith("config antivirus profile"):
            inside_av_block = True
            continue

        if inside_av_block:
            if line.startswith("end"):
                if current_av_profile:
                    av_profiles.append(current_av_profile)
                break

            elif line.startswith("edit"):
                if current_av_profile:
                    av_profiles.append(current_av_profile)
                current_av_profile = {
                    "name": line.split('"')[1],
                    "protocols": [],
                    "file_types": []
                }

            elif line.startswith("set scan-mode"):
                if current_av_profile:
                    current_av_profile["scan_mode"] = line.split()[-1]

            elif line.startswith("set protocols"):
                if current_av_profile:
                    protocols = line.split('"')[1::2]
                    current_av_profile["protocols"].extend(protocols)

            elif line.startswith("set file-types"):
                if current_av_profile:
                    file_types = line.split('"')[1::2]
                    current_av_profile["file_types"].extend(file_types)

    return av_profiles


antivirus_config = {
    "protocols": [
        {"name": "FTP", "direction": "both"},
        {"name": "HTTP", "direction": "both"},
        {"name": "IMAP", "direction": "to-client"},
        {"name": "NFS", "direction": "both"},
        {"name": "POP3", "direction": "to-client"},
        {"name": "SMB", "direction": "both"},
        {"name": "SMTP", "direction": "to-server"},
    ],
    "file_types": [
        "swf", "a", "class", "com", "dex", "dll", "elf", "exe", "macho", "msi",
        "ocx", "pascal", "reg", "rpm", "doc", "docx", "dot", "dotx", "eml", "fla",
        "pdf", "potx", "ppt", "pptx", "rtf", "vsd", "xls", "xlsx", "xltx", "7z",
        "bz2", "cab", "cpio", "gz", "jar", "rar", "xz", "zip", "bash", "bat",
        "pl", "py", "sh", "apk"
    ]
}
def generate_ruijie_av_config(root, av_profiles):
    """
    在锐捷 XML 中动态添加防病毒配置信息。
    """
    av_node = root.find(".//{urn:ruijie:ntos:params:xml:ns:yang:anti-virus}anti-virus")
    if av_node is None:
        raise ValueError("无法找到 <anti-virus> 节点，请检查模板文件")

    for profile in av_profiles:
        template = etree.SubElement(av_node, "template")

       # 填充基本信息
        etree.SubElement(template, "name").text = profile["name"]
        etree.SubElement(template, "description").text = f"Profile for {profile['name']}"
        etree.SubElement(template, "scan-mode").text = profile.get("scan_mode", "deep")
       # 填充协议部分
        protocols_node = etree.SubElement(template, "protocols")
       #如果profile["protocols"]为空 则默认为所有协议
        if not profile["protocols"]:
            for protocol in antivirus_config.get("protocols", []):
                protocol_node = etree.SubElement(protocols_node, "protocol")
                etree.SubElement(protocol_node, "name").text = protocol["name"]
                etree.SubElement(protocol_node, "direction").text = protocol["direction"]
        else:
            for protocol in profile["protocols"]:
                protocol_node = etree.SubElement(protocols_node, "protocol")
                etree.SubElement(protocol_node, "name").text = protocol
                etree.SubElement(protocol_node, "direction").text = "both"  # 默认值

        file_type_sets_node = etree.SubElement(template, "file-type-sets")
        for file_type in antivirus_config.get("file_types", []):
            file_type_node = etree.SubElement(file_type_sets_node, "file-type")
            etree.SubElement(file_type_node, "suffix").text = file_type
def parse_service_custom(lines):
    """
    解析 `config firewall service custom` 块，提取自定义服务配置信息。
    """
    inside_service_custom = False
    current_service = None
    services = []

    for line in lines:
        line = line.strip()

        if line.startswith("config firewall service custom"):
            inside_service_custom = True
            continue

        if inside_service_custom:
            if line.startswith("end"):
                if current_service:
                    services.append(current_service)
                break

            elif line.startswith("edit"):
                if current_service:
                    services.append(current_service)
                current_service = {
                    "name": line.split('"')[1],
                    "tcp_ports": [],
                    "udp_ports": [],
                }

            elif line.startswith("set tcp-portrange"):
                if current_service:
                    ports = line.split()[-1]
                    current_service["tcp_ports"].extend(ports.split())

            elif line.startswith("set udp-portrange"):
                if current_service:
                    ports = line.split()[-1]
                    current_service["udp_ports"].extend(ports.split())

    return services

def parse_firewall_ippool(lines):
    """
    解析 `config firewall ippool` 块，提取 IP 池信息。
    """
    inside_ippool_block = False
    current_ippool = None
    ippools = []

    for line in lines:
        line = line.strip()

        if line.startswith("config firewall ippool"):
            inside_ippool_block = True
            continue

        if inside_ippool_block:
            if line.startswith("end"):
                if current_ippool:
                    ippools.append(current_ippool)
                break

            elif line.startswith("edit"):
                if current_ippool:
                    ippools.append(current_ippool)
                current_ippool = {"name": line.split('"')[1], "startip": None, "endip": None}

            elif line.startswith("set startip"):
                if current_ippool:
                    current_ippool["startip"] = line.split()[-1]

            elif line.startswith("set endip"):
                if current_ippool:
                    current_ippool["endip"] = line.split()[-1]

    return ippools

def generate_ruijie_service_config(root, services):
    """
    在锐捷 XML 中动态添加服务配置。
    """
    service_obj_node = root.find(".//{urn:ruijie:ntos:params:xml:ns:yang:service-obj}service-obj")
    if service_obj_node is None:
        raise ValueError("无法找到 <service-obj> 节点，请检查模板文件")

    for service in services:
        service_set = etree.SubElement(service_obj_node, "service-set")

        etree.SubElement(service_set, "name").text = service["name"]

        if service["tcp_ports"]:
            tcp_node = etree.SubElement(service_set, "tcp")
            etree.SubElement(tcp_node, "dest-port").text = ",".join(
                ["-".join(port.split()) if " " in port else port for port in service["tcp_ports"]]
            )

        if service["udp_ports"]:
            udp_node = etree.SubElement(service_set, "udp")
            etree.SubElement(udp_node, "dest-port").text = ",".join(
                ["-".join(port.split()) if " " in port else port for port in service["udp_ports"]]
            )
def generate_ruijie_service_groups(root, service_groups):
    """
    在锐捷 XML 中动态添加服务组配置信息。
    """
    service_obj_node = root.find(".//{urn:ruijie:ntos:params:xml:ns:yang:service-obj}service-obj")
    if service_obj_node is None:
        raise ValueError("无法找到 <service-obj> 节点，请检查模板文件")

    for group in service_groups:
        service_group = etree.SubElement(service_obj_node, "service-group")
        etree.SubElement(service_group, "name").text = group["name"]

        for member in group["members"]:
            service_set = etree.SubElement(service_group, "service-set")
            etree.SubElement(service_set, "name").text = member

def generate_ruijie_address_groups(root, address_groups):
    """
    在锐捷 XML 中动态添加IP地址组配置信息。
    """
    service_obj_node = root.find(".//{urn:ruijie:ntos:params:xml:ns:yang:network-obj}network-obj")
    if service_obj_node is None:
        raise ValueError("无法找到 <network-obj> 节点，请检查模板文件")

    for group in address_groups:
        service_group = etree.SubElement(service_obj_node, "address-group")
        etree.SubElement(service_group, "name").text = group["name"]

        for member in group["members"]:
            service_set = etree.SubElement(service_group, "address-set")
            etree.SubElement(service_set, "name").text = member


def parse_ips_sensor(lines):
    """
    解析 `config ips sensor` 配置块，提取 IPS 传感器的名称、注释和严重性信息。
    支持多层嵌套结构，并增强对异常格式的兼容性。
    """
    sensors = []  # 存储所有传感器信息
    current_sensor = None  # 当前正在处理的传感器
    inside_ips_sensor = False  # 是否在 `config ips sensor` 块中
    nested_blocks = []  # 用于记录嵌套层次

    lines_iterator = iter(lines)

    for line in lines_iterator:
        line = line.strip()

        # 开始解析 `config ips sensor`
        if line.startswith("config ips sensor"):
            inside_ips_sensor = True
            continue

        # 退出 `config ips sensor`
        if inside_ips_sensor and line == "end" and not nested_blocks:
            if current_sensor:
                sensors.append(current_sensor)
            break

        # 进入嵌套块时，记录块名称
        if line.startswith("config") or line.startswith("edit"):
            nested_blocks.append(line)
            # 解析顶层 `edit` 块
            if len(nested_blocks) == 1 and line.startswith("edit"):
                # 尝试提取名称
                try:
                    sensor_name = line.split('"')[1]
                except IndexError:
                    sensor_name = line.split()[1] if len(line.split()) > 1 else "unknown"

                if current_sensor:
                    sensors.append(current_sensor)
                current_sensor = {"name": sensor_name, "comment": "", "severity": []}
            continue

        # 退出嵌套块时，清理层级
        if line == "end" or line == "next":
            if nested_blocks:
                nested_blocks.pop()
                if not nested_blocks and current_sensor:
                    sensors.append(current_sensor)
                    current_sensor = None
            continue

        # 提取注释
        if line.startswith("set comment") and current_sensor:
            current_sensor["comment"] = line.split('"', 1)[1] if '"' in line else ""

        # 提取严重性级别
        if line.startswith("set severity") and current_sensor:
            severities = line.split()[2:]  # 提取严重性级别
            current_sensor["severity"].extend(severities)

        # 进入 `config entries` 块
        if line.startswith("config entries") and current_sensor:
            # 专门处理 `config entries` 中的严重性
            for entry_line in lines_iterator:
                entry_line = entry_line.strip()
                if entry_line.startswith("end"):
                    break
                if entry_line.startswith("set severity"):
                    severities = entry_line.split()[2:]
                    current_sensor["severity"].extend(severities)

    return sensors





def generate_ruijie_ips_config(root, ips_sensors):
    """
    在锐捷 XML 中动态添加 IPS 配置信息。
    """
    ips_node = root.find(".//{urn:ruijie:ntos:params:xml:ns:yang:intrusion-prevention}ips-config")
    if ips_node is None:
        raise ValueError("无法找到 <ips-config> 节点，请检查模板文件")

    severity_mapping = {
        "critical": "high",
        "high": "medium",
        "medium": "low",
        "low": "informational",
        "informational": "informational"
    }

    for sensor in ips_sensors:
        template = etree.SubElement(ips_node, "template")

        # 填充基本信息
        etree.SubElement(template, "name").text = sensor["name"]
        etree.SubElement(template, "description").text = sensor["comment"]

        # 填充过滤器
        filter_node = etree.SubElement(template, "filter")
        etree.SubElement(filter_node, "name").text = "test-filter"
        etree.SubElement(filter_node, "target").text = "both"

        # 映射并填充严重性等级
        for severity in sensor["severity"]:
            mapped_severity = severity_mapping.get(severity.lower())
            if mapped_severity:
                etree.SubElement(filter_node, "severity").text = mapped_severity

        # 填充协议和类别
        protocol_node = etree.SubElement(filter_node, "protocol")
        etree.SubElement(protocol_node, "all-protocol").text = "true"

        category_node = etree.SubElement(filter_node, "category")
        etree.SubElement(category_node, "all-category").text = "true"


def update_ruijie_template(template_file, output_file, firewall_addresses, address_groups, policies, av_profiles,services,service_groups,ippools,ips_sensors):
    """
    更新锐捷模板 XML 文件，动态添加解析后的飞塔配置信息。
    """
    parser = etree.XMLParser(remove_blank_text=False)
    tree = etree.parse(template_file, parser)
    root = tree.getroot()

    # IP地址配置
    generate_ruijie_firewall_addresses(root, firewall_addresses)
    # IP地址组配置
    generate_ruijie_address_groups(root, address_groups)
    # 添加 NAT 配置
    generate_ruijie_nat_config(root, policies,ippools)
    # 添加防病毒配置
    generate_ruijie_av_config(root, av_profiles)
    # 添加服务配置
    generate_ruijie_service_config(root, services)
    # 添加服务组配置
    generate_ruijie_service_groups(root, service_groups)
     # IPS 配置
    generate_ruijie_ips_config(root, ips_sensors)

    tree.write(output_file, pretty_print=True, xml_declaration=True, encoding="UTF-8")


def process_fortinet_to_ruijie(fortinet_file, ruijie_template, output_file):
    """
    主函数：解析飞塔配置文件并更新锐捷模板。
    """
    with open(fortinet_file, "r", encoding="utf-8") as f:
        lines = f.readlines()

    firewall_addresses = parse_firewall_address(lines)
    address_groups = parse_firewall_addrgrp(lines)
    policies = parse_fortinet_policy(lines)
    av_profiles = parse_anti_virus(lines)
    services = parse_service_custom(lines)
    service_groups = parse_service_group(lines)
    ippools = parse_firewall_ippool(lines)
    ips_sensors = parse_ips_sensor(lines)
    print(ips_sensors)
    update_ruijie_template(ruijie_template, output_file, firewall_addresses, address_groups, policies, av_profiles,services,service_groups,ippools,ips_sensors)




def main():
    # 示例运行
    # fortinet_config_path = "E:\\fortinet_config.conf"
    # ruijie_template_path = "E:\\Z8620-startup-R10.xml"
    # output_path = "updated_ruijie_template.xml"
    # process_fortinet_to_ruijie(fortinet_config_path, ruijie_template_path, output_path)

    parser = argparse.ArgumentParser(description="飞塔到锐捷配置转换脚本")
    parser.add_argument("fortinetConfigPath", help="飞塔配置文件路径")
    parser.add_argument("ruijieTemplatePath", help="锐捷XML模板路径")
    parser.add_argument("outputPath", help="输出文件路径")
    
    args = parser.parse_args()
    
    fortinet_config_path = args.fortinetConfigPath
    ruijie_template_path = args.ruijieTemplatePath
    output_path = args.outputPath
    
    try:
        process_fortinet_to_ruijie(fortinet_config_path, ruijie_template_path, output_path)
        print("配置转换成功。输出文件位于:", output_path)
        sys.exit(0)
    except Exception as e:
        print(f"配置转换失败: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()