import sys
import json
from docx import Document

def merge_text_across_runs(paragraph):
    """组合跨运行的文本，返回完整的段落文本。"""
    return ''.join(run.text for run in paragraph.runs)

def apply_replacements(text, replacements):
    """对文本应用替换规则。"""
    for old_text, new_text in replacements.items():
        text = text.replace(old_text, new_text)
    return text

def replace_text_across_runs(paragraph, replacements):
    """在段落中逐个运行替换文本，处理跨运行的占位符替换。"""
    full_text = merge_text_across_runs(paragraph)
    new_text = apply_replacements(full_text, replacements)

    # 更新运行文本
    if new_text != full_text:
        paragraph.clear()  # 清除段落内容
        paragraph.add_run(new_text)  # 重新添加完整的文本

def replace_text_in_paragraphs(paragraphs, replacements):
    """替换一组段落中的文本。"""
    for para in paragraphs:
        replace_text_across_runs(para, replacements)

def replace_text_in_tables(tables, replacements):
    """替换所有表格中的文本。"""
    for table in tables:
        for row in table.rows:
            for cell in row.cells:
                replace_text_in_paragraphs(cell.paragraphs, replacements)

def replace_text_in_headers_footers(headers_footers, replacements):
    """替换页眉和页脚中的文本。"""
    for header_footer in headers_footers:
        replace_text_in_paragraphs(header_footer.paragraphs, replacements)

def replace_text_in_docx(input_file, output_file, replacements):
    """在 Word 文档中替换文本。"""
    doc = Document(input_file)
    replace_text_in_paragraphs(doc.paragraphs, replacements)
    replace_text_in_tables(doc.tables, replacements)
    for section in doc.sections:
        replace_text_in_headers_footers([section.header, section.footer], replacements)
    doc.save(output_file)

if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("用法: python modify_docx.py <输入文件> <输出文件> <替换JSON>")
        sys.exit(1)

    input_file = sys.argv[1]
    output_file = sys.argv[2]
    try:
        replacements = json.loads(sys.argv[3])
    except json.JSONDecodeError:
        print("错误: 替换参数的JSON格式无效。")
        sys.exit(1)

    replace_text_in_docx(input_file, output_file, replacements)
