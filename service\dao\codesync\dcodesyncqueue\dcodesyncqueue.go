package dcodesyncqueue

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/codesync"
	"irisAdminApi/service/dao/user/duser"

	"gorm.io/gorm"
)

const ModelName = "代码同步队列"

type User struct {
	ID             uint                      `json:"id"`
	Name           string                    `json:"name"`
	Username       string                    `json:"username"`
	CodeSyncQueues []*codesync.CodeSyncQueue `gorm:"many2many:code_sync_queue_users;joinForeignKey:UserID;" json:"code_sync_queues"`
}

type CodeSyncQueue struct {
	codesync.CodeSyncQueue
	Users []User `gorm:"many2many:code_sync_queue_users;joinForeignKey:CodeSyncQueueID" json:"users"`
}

type ListResponse struct {
	codesync.CodeSyncQueue
	Users []User `gorm:"many2many:code_sync_queue_users;joinForeignKey:CodeSyncQueueID" json:"users"`
}

type Request struct {
	codesync.CodeSyncQueue
}

func (a *CodeSyncQueue) ModelName() string {
	return ModelName
}

func Model() *codesync.CodeSyncQueue {
	return &codesync.CodeSyncQueue{}
}

func (a *CodeSyncQueue) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).Preload("Users").
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *CodeSyncQueue) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *CodeSyncQueue) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (a *CodeSyncQueue) CreateV2(object interface{}) error {
	return nil
}

func (a *CodeSyncQueue) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}

	return nil
}

func (a *CodeSyncQueue) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *CodeSyncQueue) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *CodeSyncQueue) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *CodeSyncQueue) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *CodeSyncQueue) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *CodeSyncQueue) FindAllQueue() ([]*ListResponse, error) {
	items := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find code sync queue err ", err)
		return items, err
	}
	return items, nil
}

func Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (u *CodeSyncQueue) AppendUsers(id uint, usernames []string) error {
	userIDs := []uint{}

	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(&User{}).Where("username in ?", usernames).Select("id").Pluck("id", &userIDs).Error
		if err != nil {
			return err
		}
		objects := []map[string]interface{}{}
		for _, userID := range userIDs {
			objects = append(objects, map[string]interface{}{
				"cdoe_sync_queue_id": id,
				"user_id":            userID,
			})
		}
		if err := tx.Table("code_sync_queue_users").Create(objects).Error; err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (u *CodeSyncQueue) ReplaceUsers(usernames []string) error {
	userIDs := []uint{}

	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(&User{}).Where("username in ?", usernames).Select("id").Pluck("id", &userIDs).Error
		if err != nil {
			return err
		}
		if err := tx.Unscoped().Table("code_sync_queue_users").Where("code_sync_queue_id = ?", u.ID).Delete(&codesync.CodeSyncQueueUser{}).Error; err != nil {
			return err
		}
		objects := []map[string]interface{}{}
		for _, userID := range userIDs {
			objects = append(objects, map[string]interface{}{
				"code_sync_queue_id": u.ID,
				"user_id":            userID,
			})
		}
		if err := tx.Table("code_sync_queue_users").Create(objects).Error; err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (a *CodeSyncQueue) UserQueuesBak(userID uint, status, name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*User
	var countErr error
	err := easygorm.GetEasyGormDb().Where("id = ?", userID).Preload("CodeSyncQueues", func(db *gorm.DB) *gorm.DB {
		if len(status) > 0 {
			db = db.Where("status = ?", status)
		}
		paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
		return db.Scopes(paginateScope)
	}).Find(&items).Error
	fmt.Println(countErr, err)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *CodeSyncQueue) UserQueues(assigneeIDs []uint, authorIDs []uint, status []uint, repo, sourceBranch, targetBranch, sourceMR, targetMR, start, end, name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*CodeSyncQueue
	queueIDs := []uint{}
	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	db := easygorm.GetEasyGormDb()
	if len(assigneeIDs) > 0 {
		err := easygorm.GetEasyGormDb().Table("code_sync_queue_users").Where("user_id in ?", assigneeIDs).Pluck("code_sync_queue_id", &queueIDs).Error
		if err != nil {
			logging.ErrorLogger.Errorf("get user queues err", err)
			return nil, err
		}
		db = db.Where("id in ?", queueIDs)
	}
	if len(authorIDs) > 0 {
		usernames := []string{}
		for _, authorID := range authorIDs {
			usernames = append(usernames, duser.UserMap[authorID].Username)
		}
		db = db.Where("source_merge_request_username in ?", usernames)
	}
	if len(status) > 0 {
		db = db.Where("status in ?", status)
	}
	if len(repo) > 0 {
		db = db.Where("repository_path like ?", fmt.Sprintf("%%%s%%", repo))
	}
	if len(sourceBranch) > 0 {
		db = db.Where("source_branch like ?", fmt.Sprintf("%%%s%%", sourceBranch))
	}
	if len(targetBranch) > 0 {
		db = db.Where("target_branch like ?", fmt.Sprintf("%%%s%%", targetBranch))
	}
	if len(sourceMR) > 0 {
		db = db.Where("source_web_url = ?", sourceMR)
	}
	if len(targetMR) > 0 {
		db = db.Where("target_web_url = ?", targetMR)
	}
	if len(start) > 0 {
		db = db.Where("updated_at >= ?", start+" 00:00:00")
	}
	if len(end) > 0 {
		db = db.Where("updated_at <= ?", end+" 23:59:59")
	}
	err := db.Table("code_sync_queues").Count(&count).Scopes(paginateScope).Preload("Users").Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get user queues err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (u *CodeSyncQueue) FindQueue(sourceMergeRequestID int, sourceBranch, targetBranch string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("source_merge_request_id = ? and source_branch = ? and target_branch = ?", sourceMergeRequestID, sourceBranch, targetBranch).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *CodeSyncQueue) FindQueueByTargetMRID(targetMergeRequestID int) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("target_merge_request_id = ?", targetMergeRequestID).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}
