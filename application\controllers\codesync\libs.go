package codesync

import (
	"fmt"
	"net/http"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"

	"github.com/kataras/iris/v12"
)

func GetBuildProjects(ctx iris.Context) {
	token := libs.Config.CodeSync.Token
	if len(token) == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置gitlab token, 请前往个人资料添加gitlab token"))
		return
	}
	url := fmt.Sprintf("%s/api/%s/projects?private_token=%s&simple=true", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, token)

	// page, _ := strconv.Atoi(ctx.FormValue("page"))
	// pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))

	url = fmt.Sprintf("%s&per_page=%d", url, 100)

	var items []map[string]interface{}
	list := map[string]interface{}{}
	page := 1
	for i := 0; i <= 10; i++ {
		_url := fmt.Sprintf("%s&page=%d", url, page)
		req, err := http.NewRequest("GET", _url, nil)
		if err != nil {
			logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
			continue
		}

		_result, err := libs.HandlerRequest(req)
		if err != nil {
			logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
			continue
		}
		items = append(items, _result["items"].([]map[string]interface{})...)
		list["total"] = _result["total"]
		list["limit"] = _result["limit"]
		page++
		if _result["total"].(int) <= len(items) {
			break
		}
	}
	list["items"] = items
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}
