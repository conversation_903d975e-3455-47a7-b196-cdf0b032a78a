package release

import (
	"fmt"
	"net/http"
	"reflect"
	"strconv"
	"strings"
	"time"

	"irisAdminApi/application/controllers/mergerequest"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	buildfarmProject "irisAdminApi/service/dao/buildfarm/dproject"
	"irisAdminApi/service/dao/mergerequest/dmergerequestworkpackage"
	"irisAdminApi/service/dao/release/dbranch"
	"irisAdminApi/service/dao/release/dbranchaudit"
	"irisAdminApi/service/dao/release/dbranchinfo"
	"irisAdminApi/service/dao/release/dproject"
	"irisAdminApi/service/dao/release/dstatus"
	"irisAdminApi/service/dao/user/duser"
	"irisAdminApi/service/transaction/release/transrelease"

	"github.com/kataras/iris/v12"
	"github.com/pkg/errors"
)

func GetBranches(ctx iris.Context) {
	// id, err := dao.GetAuthId(ctx)
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	name := ctx.FormValue("name")
	projectID := ctx.FormValue("project_id")
	unitPackage := ctx.FormValue("unit_package")
	newBranch := ctx.FormValue("new_branch")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dbranch.All(name, projectID, unitPackage, newBranch, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	token := libs.Config.Buildfarm.Token
	if len(token) == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置gitlab token,请联系管理员"))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetBaseBranch(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	baseBranch, err := dbranch.GetBaseBranch(id, name)
	if err != nil {
		logging.ErrorLogger.Errorf("get base branch get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]interface{}{"base_branch": baseBranch}, response.NoErr.Msg))
	return
}

func GetBranch(ctx iris.Context) {
	info := dbranch.ReleaseBranch{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

func CreateBranch(ctx iris.Context) {
	userId, _ := dao.GetAuthId(ctx)
	request := &dbranch.Request{}
	if err := ctx.ReadJSON(request); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	project := dproject.Response{}
	if err := project.Find(request.ReleaseProjectID); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "该项目不存在！"))
		return
	}

	status := dstatus.Response{}
	err := status.FindEx("name", "申请中")
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "查找项目状态异常，请重试。"))
		return
	}

	if project.StatusID == status.ID {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "该项目状态为申请中，无法申请分支，请联系CMA处理"))
		return
	}

	if len(request.BaseBranch) == 0 && len(request.BaseVerison) == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "基线分支与基线版本号不能同时为空"))
		return
	}

	if !libs.InArrayUint(request.AuditorIds, project.PmID) {
		request.AuditorIds = append(request.AuditorIds, project.PmID)
	}
	if !libs.InArrayUint(request.AuditorIds, project.PmoID) {
		request.AuditorIds = append(request.AuditorIds, project.PmoID)
	}
	if !libs.InArrayUint(request.AuditorIds, project.CmaID) {
		request.AuditorIds = append(request.AuditorIds, project.CmaID)
	}

	buildFarmProject := buildfarmProject.Response{}
	if err := buildFarmProject.FindEx("gitlab_id", strconv.FormatUint(uint64(request.UnitPackage), 10)); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "查找gitlab仓库负责人失败"))
		return
	}

	if !libs.InArrayUint(request.AuditorIds, buildFarmProject.Owner) {
		request.AuditorIds = append(request.AuditorIds, buildFarmProject.Owner)
	}

	uuid := libs.GetUUID()
	object := map[string]interface{}{
		"UserID":    userId,
		"CreatedAt": time.Now(),
		"Uuid":      uuid,
	}
	t := reflect.TypeOf(*request)
	v := reflect.ValueOf(*request)
	for k := 0; k < t.NumField(); k++ {
		if t.Field(k).Type == reflect.TypeOf([]uint{}) {
			continue
		}
		object[t.Field(k).Name] = v.Field(k).Interface()
	}
	if err := dbranch.CheckBranchExists(request.UnitPackage, request.NewBranch); err != nil {
		logging.ErrorLogger.Errorf("create release project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	err = dao.Create(&dbranch.ReleaseBranch{}, ctx, object)
	if err != nil {
		logging.ErrorLogger.Errorf("create release project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	branch := dbranch.ReleaseBranch{}
	if err := branch.FindEx("uuid", uuid); err != nil {
		logging.ErrorLogger.Errorf("create release project ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if err := dbranch.AppendWrites(branch.ID, request.WriteIds); err != nil {
		logging.ErrorLogger.Errorf("create release project ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if err := dbranch.AppendNotices(branch.ID, request.NoticeIds); err != nil {
		branch.Delete(branch.ID)
		logging.ErrorLogger.Errorf("create release project ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if err := dbranch.AppendAuditors(branch.ID, request.AuditorIds); err != nil {
		branch.Delete(branch.ID)
		logging.ErrorLogger.Errorf("create release project ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	go func() {
		_package := buildfarmProject.Response{}
		err := _package.FindEx("gitlab_id", fmt.Sprintf("%v", branch.UnitPackage))
		if err != nil {
			logging.ErrorLogger.Error(err)
		}
		mailTo := []string{}
		for _, auditorID := range request.AuditorIds {
			to := duser.UserMap[auditorID].Username + "@ruijie.com.cn"
			mailTo = append(mailTo, to)
		}
		subject := fmt.Sprintf("【版本发布系统】【项目：%s】【组件：%s】【新分支：%s】【待评审】", project.Name, _package.Name, branch.NewBranch)
		body := fmt.Sprintf(`<p>项目：%s</p>
		<p>组件：%s</p>
		<p>基线分支：%s</p>
		<p>基线版本：%s(基线版本号为空时,将使用评审通过时基线分支最新的版本号)</p>
		<p>新分支: %s</p>
		<p>分支描述: %s</p>`,
			project.Name,
			_package.Name,
			branch.BaseBranch,
			branch.BaseVerison,
			branch.NewBranch,
			branch.BranchComment)
		libs.SendMailRedis("版本发布系统", mailTo, subject, body, []string{"<EMAIL>"})
	}()

	ctx.JSON(response.NewResponse(response.NoErr.Code, request, response.NoErr.Msg))
	return
}

func UpdateBranch(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}

	branch := dbranch.ReleaseBranch{}
	err = dao.Find(&branch, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	userIds := branch.Auditors
	if !libs.InArrayUint(userIds, uId) {
		logging.ErrorLogger.Errorf("create approval read json err ", err)
		ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, response.PermitErr.Msg))
		return
	}

	request := &dbranch.Request{}
	if err := ctx.ReadJSON(request); err != nil {
		logging.ErrorLogger.Errorf("create approval read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	object := map[string]interface{}{
		"UpdatedAt": time.Now(),
		"Status":    request.Status,
	}
	// t := reflect.TypeOf(*request)
	// v := reflect.ValueOf(*request)
	// for k := 0; k < t.NumField(); k++ {
	// 	if t.Field(k).Type == reflect.TypeOf([]uint{}) {
	// 		continue
	// 	}

	// 	object[t.Field(k).Name] = v.Field(k).Interface()
	// }

	err = dao.Update(&dbranch.ReleaseBranch{}, ctx, object)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	// if err := dbranch.AppendNotices(branch.ID, request.NoticeIds); err != nil {
	// 	logging.ErrorLogger.Errorf("create release project ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
	// 	return
	// }

	// if err := dbranch.AppendAuditors(branch.ID, request.AuditorIds); err != nil {
	// 	logging.ErrorLogger.Errorf("create release project ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
	// 	return
	// }
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func CreateBranchAudit(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}

	branch := dbranch.ReleaseBranch{}
	err = dao.Find(&branch, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	userIds := branch.Auditors
	if !libs.InArrayUint(userIds, uId) {
		logging.ErrorLogger.Errorf("create approval read json err ", err)
		ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, "无审核权限"))
		return
	}

	if branch.Status != 0 {
		logging.ErrorLogger.Errorf("create approval read json err ", err)
		ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, "已经评审结束。"))
		return
	}

	request := &dbranchaudit.Request{}
	if err := ctx.ReadJSON(request); err != nil {
		logging.ErrorLogger.Errorf("create branch audit read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	project := dproject.Response{}
	err = project.Find(branch.ReleaseProjectID)
	if err != nil {
		logging.ErrorLogger.Errorf("find project err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	audits, err := dbranchaudit.FindAll(branch.ID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	auditObjects := []map[string]interface{}{}
	auditObject := map[string]interface{}{
		"UserID":          uId,
		"UpdatedAt":       time.Now(),
		"Comment":         request.Comment,
		"Status":          request.Status,
		"ReleaseBranchID": branch.ID,
	}

	auditObjects = append(auditObjects, auditObject)
	pms := []uint{project.PmoID, project.PmID}
	if libs.InArrayUint(pms, uId) {
		for _, _id := range pms {
			if _id != uId {
				auditObject := map[string]interface{}{
					"UserID":          _id,
					"UpdatedAt":       time.Now(),
					"Comment":         "无需处理",
					"Status":          request.Status,
					"ReleaseBranchID": branch.ID,
				}
				auditObjects = append(auditObjects, auditObject)
			}
		}
	}

	// UpdateFlag := 0
	Pass := 0
	NotPass := 0
	// PmPass := 0
	// PmoPass := 0

	for _, audit := range audits {
		if audit.Status == 1 {
			// PM和PMO只要有一个审批就可以了
			Pass++
		}

		if audit.Status == 2 {
			NotPass++
		}
	}

	if request.Status == 1 {
		Pass = Pass + len(auditObjects)
	}
	if request.Status == 2 {
		NotPass = NotPass + len(auditObjects)
	}

	if NotPass > 0 {
		object := map[string]interface{}{
			"Status": 2,
		}
		err = dao.Update(&dbranch.ReleaseBranch{}, ctx, object)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		go func() {
			_package := buildfarmProject.Response{}
			err := _package.FindEx("gitlab_id", fmt.Sprintf("%v", branch.UnitPackage))
			if err != nil {
				logging.ErrorLogger.Error(err)
			}

			mailTo := []string{}
			for _, audit := range audits {
				to := duser.UserMap[audit.ID].Username + "@ruijie.com.cn"
				mailTo = append(mailTo, to)
			}

			subject := fmt.Sprintf("【版本发布系统】【项目：%s】【组件：%s】【新分支：%s】【评审取消】", project.Name, _package.Name, branch.NewBranch)
			body := fmt.Sprintf(`<p>项目：%s</p>
			<p>组件：%s</p>
			<p>基线分支：%s</p>
			<p>基线版本：%s(基线版本号为空时,将使用评审通过时基线分支最新的版本号)</p>
			<p>新分支: %s</p>
			<p>分支描述: %s</p>`,
				project.Name,
				_package.Name,
				branch.BaseBranch,
				branch.BaseVerison,
				branch.NewBranch,
				branch.BranchComment)
			libs.SendMailRedis("版本发布系统", mailTo, subject, body, []string{"<EMAIL>"})
		}()

	} else if NotPass == 0 && Pass == len(branch.Auditors) {
		object := map[string]interface{}{
			"Status": 1,
		}

		commitID, err := CreateGitLabBranch(branch.UnitPackage, branch.NewBranch, branch.BaseBranch, branch.BaseVerison)
		if err != nil {
			logging.ErrorLogger.Errorf("get project get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		} else {
			object["AutoCreate"] = 1
			object["BaseVerison"] = commitID
		}
		go func() {
			_package := buildfarmProject.Response{}
			err := _package.FindEx("gitlab_id", fmt.Sprintf("%v", branch.UnitPackage))
			if err != nil {
				logging.ErrorLogger.Error(err)
			}

			mailTo := []string{}
			for _, audit := range audits {
				to := duser.UserMap[audit.ID].Username + "@ruijie.com.cn"
				mailTo = append(mailTo, to)
			}

			subject := fmt.Sprintf("【版本发布系统】【项目：%s】【组件：%s】【新分支：%s】【评审通过】", project.Name, _package.Name, branch.NewBranch)
			body := fmt.Sprintf(`<p>项目：%s</p>
			<p>组件：%s</p>
			<p>基线分支：%s</p>
			<p>基线版本：%s(基线版本号为空时,将使用评审通过时基线分支最新的版本号)</p>
			<p>新分支: %s</p>
			<p>分支描述: %s</p>`,
				project.Name,
				_package.Name,
				branch.BaseBranch,
				commitID,
				branch.NewBranch,
				branch.BranchComment)
			libs.SendMailRedis("版本发布系统", mailTo, subject, body, []string{"<EMAIL>"})
		}()

		err = dao.Update(&dbranch.ReleaseBranch{}, ctx, object)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}

		err = dproject.AppendBranchInfo(branch.ReleaseProjectID, branch.NewBranch, branch.BaseVerison, branch.BaseBranch, branch.BaseVerison, "分支创建于"+time.Now().Format("2006-01-02"))
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
	}

	err = dbranchaudit.BatchCreate(auditObjects)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

type BranchIDs struct {
	ProjectID uint   `json:"project_id"`
	Handle    string `json:"handle"`
}

func BatchBrachClose(ctx iris.Context) {
	token := libs.Config.Buildfarm.Token
	if len(token) == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置gitlab token,请联系管理员"))
		return
	}

	projectID, _ := dao.GetId(ctx)
	branches, err := dbranch.FindBranchByProjectIDAndStatus(projectID, 1)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	success := 0
	for _, branch := range branches {
		err = CloseGitBranch(branch.UnitPackage, token, branch.NewBranch)
		if err == nil {
			success++
		}
	}

	if success == len(branches) {
		ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未全部成功，请重试"))
	return
}

func BatchBrachOpen(ctx iris.Context) {
	token := libs.Config.Buildfarm.Token
	if len(token) == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置gitlab token,请联系管理员"))
		return
	}
	projectID, _ := dao.GetId(ctx)
	branches, err := dbranch.FindBranchByProjectIDAndStatus(projectID, 1)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	success := 0
	for _, branch := range branches {
		err = OpenGitBranch(branch.UnitPackage, token, branch.NewBranch)
		if err == nil {
			success++
		}
	}

	if success == len(branches) {
		ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未全部成功，请重试"))
	return
}

func BatchBrachStatus(ctx iris.Context) {
	token := libs.Config.Buildfarm.Token
	if len(token) == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置gitlab token,请联系管理员"))
		return
	}
	projectID, _ := dao.GetId(ctx)
	branches, err := dbranch.FindBranchByProjectIDAndStatus(projectID, 1)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	open := 0
	close := 0
	for _, branch := range branches {
		status, err := CheckGitBranch(branch.UnitPackage, token, branch.NewBranch)
		if err == nil {
			if status {
				open++
			} else {
				close++
			}
		}
	}
	result := map[string]interface{}{}
	if open == len(branches) {
		result["status"] = "open"
	} else if close == len(branches) {
		result["status"] = "close"
	} else if open+close == len(branches) {
		result["status"] = "partial"
	} else {
		result["status"] = "error"
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func HandleBranchPermition(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}

	branch := dbranch.ReleaseBranch{}
	err = dao.Find(&branch, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if !libs.InArrayUint([]uint{branch.ReleaseProject.CmaID, branch.ReleaseProject.PmID, branch.ReleaseProject.PmoID, 1}, uId) {
		ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, "无权限进行操作"))
		return
	}
	handle := ctx.FormValue("handle")

	token := libs.Config.Buildfarm.Token
	if len(token) == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置gitlab token,请联系管理员"))
		return
	}
	switch handle {
	case "open":
		err = OpenGitBranch(branch.UnitPackage, token, branch.NewBranch)
	case "close":
		err = CloseGitBranch(branch.UnitPackage, token, branch.NewBranch)
	case "check":
		status, err := CheckGitBranch(branch.UnitPackage, token, branch.NewBranch)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]interface{}{"status": status}, response.NoErr.Msg))
		return
	default:
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未提供操作"))
		return
	}
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetAuditedBranches(ctx iris.Context) {
	uid, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	project := ctx.FormValue("project")
	unit_package := ctx.FormValue("unit_package")
	list, err := dbranch.FindAllAudited(uid, name, status, project, unit_package, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetAuditBranches(ctx iris.Context) {
	uid, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dbranch.FindAllAuditByUserID(uid, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetMyBranches(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dbranch.FindMyBranch(id, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetBranchAudits(ctx iris.Context) {
	// id, err := dao.GetAuthId(ctx)
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	id, err := dao.GetId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	list, err := dbranchaudit.FindAllByBranchId(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func UpdateProjectInfo(projectID uint) {
	url := fmt.Sprintf("http://10.51.135.15:9090/buildfarm-api/api/v1/manage/farm/projects/%d/updateinfo", projectID)
	resp, err := ReleaseClient.R().Get(url)
	if err != nil {
		logging.ErrorLogger.Errorf("", err, url, resp.String())
		return
	}
	logging.DebugLogger.Debugf("", url, resp.String())
}

func CreateGitLabBranch(unitpackage uint, branch, baseBranch, ref string) (string, error) {
	commitID := ""
	_project := buildfarmProject.Response{}
	err := _project.FindEx("gitlab_id", strconv.FormatUint(uint64(unitpackage), 10))
	if err != nil {
		return commitID, errors.New("编译农场未找到该组件信息，请联系管理员")
	}
	token := libs.Config.Buildfarm.Token
	if len(token) == 0 {
		return commitID, errors.New("未配置gitlab token,请联系管理员")
	}
	gitlabId := _project.GitlabId

	if len(ref) == 0 {
		commitID, err = CreateGitBranch(gitlabId, token, branch, baseBranch)
		if err != nil {
			return commitID, err
		}
	} else {
		commitID, err = CreateGitBranch(gitlabId, token, branch, ref)
		if err != nil {
			return commitID, err
		}
	}

	// 检查是否Trunk分支开出

	err = OpenGitBranch(gitlabId, token, branch)
	if err != nil {
		return commitID, err
	}
	return commitID, nil
}

type CreateBranchResponse struct {
	Commit struct {
		ID string `json:"id"`
	} `json:"commit"`
	Name string `json:"name"`
}

type ErrorResponse struct {
	Message string `json:"string"`
}

func CreateGitBranch(gitlabId uint, token, branch, ref string) (string, error) {
	result := CreateBranchResponse{}
	errMsg := ""
	for i := 0; i <= 5; i++ {
		// /api/v4/projects/5/repository/branches?branch=newbranch&ref=master
		url := fmt.Sprintf("%s/api/%s/projects/%d/repository/branches?private_token=%s&branch=%s&ref=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, gitlabId, token, branch, ref)
		logging.DebugLogger.Debug("url is", url)
		// req, err := http.NewRequest("POST", url, nil)
		// if err != nil {
		// 	logging.ErrorLogger.Errorf("create reuqest for get git projects err %s %s", err, url)
		// 	err = errors.New("自动创建分支请求失败，请联系管理员")
		// }
		// result, err := libs.HandlerRequest(req)
		// if err != nil {
		// 	logging.ErrorLogger.Errorf("handle reuqest for get git projects err %s %s", err, url)
		// 	err = errors.New("自动创建分支返回失败，请联系管理员")
		// }
		// if result["name"] == branch {
		// 	return nil
		// }
		// if value, ok := result["message"]; ok {
		// 	return errors.New(fmt.Sprintf("自动创建分支失败，请联系管理员 %s", value))
		// }
		resp, err := ReleaseClient.R().SetSuccessResult(&result).Post(url)
		errMsg = resp.String()
		if err != nil {
			logging.ErrorLogger.Error(resp.String(), err)
			return "", errors.Wrap(err, errMsg)
		}

		logging.DebugLogger.Debug("result ", resp.String(), result)
		if result.Name == branch {
			return result.Commit.ID, nil
		}

		time.Sleep(3 * time.Second)
	}
	return "", errors.New(errMsg + "自动创建分支失败，请联系管理员")
}

func OpenGitBranch(projectId uint, token, branch string) error {
	for i := 0; i <= 3; i++ {
		// /api/v4/projects/5/protected_branches?name=*-stable&push_access_level=30&merge_access_level=30&unprotect_access_level=40
		url := fmt.Sprintf("%s/api/%s/projects/%d/protected_branches/%s?private_token=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, projectId, branch, token)
		req, err := http.NewRequest("DELETE", url, nil)
		if err != nil {
			logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
			err = errors.New("初始化分支保护请求失败，请联系管理员")
			continue
		}
		result, err := libs.HandlerRequest(req)
		if err != nil {
			logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
			err = errors.New("初始化分支保护返回失败，请联系管理员")
			continue
		}
		url = fmt.Sprintf("%s/api/%s/projects/%d/protected_branches?private_token=%s&name=%s&push_access_level=40&merge_access_level=40", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, projectId, token, branch)
		req, err = http.NewRequest("POST", url, nil)
		if err != nil {
			logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
			err = errors.New("打开分支请求失败，请联系管理员")
			continue
		}
		result, err = libs.HandlerRequest(req)
		if err != nil {
			logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
			err = errors.New("打开分支返回失败，请联系管理员")
			continue
		}
		if result["name"] == branch {
			if result["push_access_levels"].([]interface{})[0].(map[string]interface{})["access_level"].(float64) == float64(40) && result["merge_access_levels"].([]interface{})[0].(map[string]interface{})["access_level"].(float64) == float64(40) {
				return nil
			}
		}
	}
	return errors.New("打开分支失败，请联系管理员")
}

func CloseGitBranch(projectId uint, token, branch string) error {
	for i := 0; i <= 3; i++ {
		// /api/v4/projects/5/protected_branches?name=*-stable&push_access_level=30&merge_access_level=30&unprotect_access_level=40"
		url := fmt.Sprintf("%s/api/%s/projects/%d/protected_branches/%s?private_token=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, projectId, branch, token)
		req, err := http.NewRequest("DELETE", url, nil)
		if err != nil {
			logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
			err = errors.New("初始化分支保护请求失败，请联系管理员")
			continue
		}
		result, err := libs.HandlerRequest(req)
		if err != nil {
			logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
			err = errors.New("初始化分支保护返回失败，请联系管理员")
			continue
		}
		url = fmt.Sprintf("%s/api/%s/projects/%d/protected_branches?private_token=%s&name=%s&push_access_level=0&merge_access_level=0", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, projectId, token, branch)
		req, err = http.NewRequest("POST", url, nil)
		if err != nil {
			logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
			err = errors.New("关闭分支权限请求失败，请联系管理员")
			continue
		}
		result, err = libs.HandlerRequest(req)
		if err != nil {
			logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
			err = errors.New("关闭分支权限返回失败，请联系管理员")
			continue
		}
		if result["name"] == branch {
			if result["push_access_levels"].([]interface{})[0].(map[string]interface{})["access_level"].(float64) == float64(0) && result["merge_access_levels"].([]interface{})[0].(map[string]interface{})["access_level"].(float64) == float64(0) {
				return nil
			}
		}
	}
	return errors.New("关闭分支权限失败，请联系管理员")
}

func CheckGitBranch(projectId uint, token, branch string) (bool, error) {
	for i := 0; i <= 3; i++ {
		// /projects/:id/protected_branches/:name
		url := fmt.Sprintf("%s/api/%s/projects/%d/protected_branches/%s?private_token=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, projectId, branch, token)
		req, err := http.NewRequest("GET", url, nil)
		if err != nil {
			logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
			err = errors.New("检查分支状态请求失败，请联系管理员")
			continue
		}
		result, err := libs.HandlerRequest(req)
		if err != nil {
			logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
			err = errors.New("检查分支状态返回失败，请联系管理员")
			continue
		}
		if result["name"] == branch {
			if result["push_access_levels"].([]interface{})[0].(map[string]interface{})["access_level"].(float64) == float64(0) && result["merge_access_levels"].([]interface{})[0].(map[string]interface{})["access_level"].(float64) == float64(0) {
				return false, err
			} else {
				return true, err
			}
		}
	}
	return false, errors.New("检查分支状态失败，请联系管理员")
}

func GetBranchInfo(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	info, err := dbranchinfo.FindBranchInfoByProjectId(id)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

type Branch struct {
	ID        uint   `json:"id"`
	Name      string `json:"name"`
	Branch    string `json:"branch"`
	Project   string `json:"project"`
	ProjectID uint   `json:"project_id"`
}

func GetReleaseBranches(ctx iris.Context) {
	_items, err := dbranch.FindReleaseBranches()
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	var items []Branch
	for _, _item := range _items {
		if _item.ReleaseProject.Status != nil {
			items = append(items, Branch{
				ID:        _item.ID,
				Name:      _item.NewBranch + "(" + _item.ReleaseProject.Status.Name + ")",
				Branch:    _item.NewBranch,
				Project:   _item.ReleaseProject.Name,
				ProjectID: _item.ReleaseProject.ID,
			})
		}
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, items, response.NoErr.Msg))
	return
}

func GetNewBranches(ctx iris.Context) {
	// id, err := dao.GetAuthId(ctx)
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	id, _ := dao.GetId(ctx)
	name := ctx.FormValue("name")

	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	if len(orderBy) == 0 {
		orderBy = "id"
	}

	if len(sort) == 0 {
		sort = "desc"
	}

	list, err := dbranch.FindAllAuditedPassedByProjectID(id, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func AuditBranchByPM(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}

	branch := dbranch.ReleaseBranch{}
	err = dao.Find(&branch, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if !libs.InArrayUint([]uint{branch.ReleaseProject.PmID, branch.ReleaseProject.PmoID, 1, branch.ReleaseProject.CmaID}, uId) {
		// if uId != branch.ReleaseProject.PmID && !libs.InArrayS(duser.UserMap[uId].Roles, "超级管理员") {
		logging.ErrorLogger.Errorf("create approval read json err ", err)
		ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, "只允许PM,CMA一键通过"))
		return
	}

	if branch.Status != 0 {
		logging.ErrorLogger.Errorf("create approval read json err ", err)
		ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, "已经评审结束。"))
		return
	}

	request := &dbranchaudit.Request{}
	if err := ctx.ReadJSON(request); err != nil {
		logging.ErrorLogger.Errorf("create approval read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	audits, err := dbranchaudit.FindAll(branch.ID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	object := map[string]interface{}{
		"UserID":          uId,
		"UpdatedAt":       time.Now(),
		"Comment":         request.Comment,
		"Status":          request.Status,
		"ReleaseBranchID": branch.ID,
	}

	UpdateFlag := 0

	for _, audit := range audits {
		if audit.UserID == uId {
			_audit := dbranchaudit.Response{}
			err = _audit.Update(audit.ID, object)
			if err != nil {
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
				return
			}
			UpdateFlag = 1
		}
	}

	if UpdateFlag == 0 {
		err = dao.Create(&dbranchaudit.Response{}, ctx, object)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
	}

	object = map[string]interface{}{
		"Status": request.Status,
	}

	if request.Status == 1 {
		err = dao.Update(&dbranch.ReleaseBranch{}, ctx, object)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		err = dproject.AppendBranchInfo(branch.ReleaseProjectID, branch.NewBranch, branch.BaseVerison, branch.BaseBranch, branch.BaseVerison, "分支创建于"+time.Now().Format("2006-01-02"))
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		commitID, err := CreateGitLabBranch(branch.UnitPackage, branch.NewBranch, branch.BaseBranch, branch.BaseVerison)
		if err != nil {
			logging.ErrorLogger.Errorf("get project get err ", err)
			object["AutoCreate"] = 2
		} else {
			object["AutoCreate"] = 1
			object["BaseVerison"] = commitID
		}
	}

	err = dao.Update(&dbranch.ReleaseBranch{}, ctx, object)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetBranchesMrControlStatus(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}

	branch := dbranch.ReleaseBranch{}
	err = dao.Find(&branch, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if branch.ID <= 0 {
		ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, "分支数据不存在"))
		return
	}
	if !libs.InArrayUint([]uint{branch.ReleaseProject.PmID, 1}, uId) {
		ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, "无权限进行操作"))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, branch, response.NoErr.Msg))
}

func HandleBranchMrControlStatus(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}

	branch := dbranch.ReleaseBranch{}
	err = dao.Find(&branch, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	// fmt.Println(uId)
	if !libs.InArrayUint([]uint{branch.ReleaseProject.PmID, 1}, uId) {
		ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, "无权限进行操作"))
		return
	}
	branchID, _ := dao.GetId(ctx)

	handle := ctx.FormValue("handle")
	switch handle {
	case "open":
		err = transrelease.UpdateBranchMrControlStatuTransaction(branchID, 0)
		go mergerequest.ProcessUnmergedMRsByProjectID(branch.ReleaseProject.ID, 0, branch.NewBranch)
	case "close":
		err = transrelease.UpdateBranchMrControlStatuTransaction(branchID, 1)
	case "control":
		err = transrelease.UpdateBranchMrControlStatuTransaction(branchID, 2)
	default:
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未提供操作"))
		return
	}
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetMRWorkPackages(ctx iris.Context) {
	// id, err := dao.GetId(ctx)
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
	// 	return
	// }
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	projectID, _ := strconv.Atoi(ctx.FormValue("project"))
	name := ctx.FormValue("name")

	ret, err := dmergerequestworkpackage.FindByReleaseProjectIDV2(projectID, page, pageSize, sort, orderBy, name)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, ret, response.NoErr.Msg))
	return
}

func GetWorkPackageMrControlStatus(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}

	workpackage := dmergerequestworkpackage.MergeRequestWorkPackage{}
	err = dao.Find(&workpackage, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if workpackage.ID <= 0 {
		ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, "工作包数据不存在"))
		return
	}
	project := dproject.Response{}
	if err := project.Find(workpackage.ReleaseProjectID); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "该项目不存在！"))
		return
	}
	if !libs.InArrayUint([]uint{project.PmID, 1}, uId) {
		ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, "无权限进行操作"))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, workpackage, response.NoErr.Msg))
}

func HandleWorkPackageMrControlStatus(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}

	workpackage := dmergerequestworkpackage.MergeRequestWorkPackage{}
	err = dao.Find(&workpackage, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	project := dproject.Response{}
	if err := project.Find(workpackage.ReleaseProjectID); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "该项目不存在！"))
		return
	}
	if !libs.InArrayUint([]uint{project.PmID, 1}, uId) {
		ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, "无权限进行操作"))
		return
	}
	workpackageID, _ := dao.GetId(ctx)

	handle := ctx.FormValue("handle")
	switch handle {
	case "open":
		err = transrelease.UpdateWorkPackageMrControlStatuTransaction(workpackageID, 0)
		go mergerequest.ProcessUnmergedMRsByProjectID(workpackage.ReleaseProjectID, workpackageID, "")
	case "close":
		err = transrelease.UpdateWorkPackageMrControlStatuTransaction(workpackageID, 1)
	case "control":
		err = transrelease.UpdateWorkPackageMrControlStatuTransaction(workpackageID, 2)
	default:
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未提供操作"))
		return
	}
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}
