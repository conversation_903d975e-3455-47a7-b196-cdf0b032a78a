package buildfarm

import (
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/buildfarm/dbaseline"
	"strconv"

	"github.com/kataras/iris/v12"
)

func GetBaselines(ctx iris.Context) {
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))

	var items []*dbaseline.ListResponse
	var err error
	db := easygorm.GetEasyGormDb().Model(dbaseline.Model())

	if pageSize == 0 {
		err = db.Order("id desc").Limit(1000).Find(&items).Error
	} else if pageSize == -1 {
		err = db.Order("id desc").Find(&items).Error
	} else {
		err = db.Order("id desc").Limit(pageSize).Find(&items).Error
	}

	if err != nil {
		logging.ErrorLogger.Errorf("create filedetail get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	list := map[string]interface{}{"items": items}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}
