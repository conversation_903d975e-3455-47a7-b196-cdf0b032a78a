package ddagtask

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models"
	"irisAdminApi/application/models/documentauto"
)

const ModelName = "任务数据表"

type Response struct {
	models.ModelBase
	SeriesIDs       string      `gorm:"not null; type:varchar(300)" json:"series_ids"`
	BuildRecordIDs  string      `gorm:"not null; type:varchar(300)" json:"build_record_ids"`
	ReleaseVersion  string      `gorm:"not null; type:varchar(300)" json:"release_version"`
	VersionNumber   string      `gorm:"not null; type:varchar(300)" json:"version_number"`
	ReleaseType     string      `gorm:"not null; type:varchar(300)" json:"release_type"`
	BaselineVersion string      `gorm:"not null; type:varchar(300)" json:"baseline_version"`
	Description     string      `gorm:"not null; type:varchar(300)" json:"description"`
	UserID          uint        `gorm:"not null" json:"user_id"`
	Status          uint        `gorm:"not null; default:0" json:"status"`
	UUID            string      `gorm:"not null; type:varchar(300)" json:"uuid"`
	StartDate       string      `gorm:"not null; type:varchar(300)" json:"start_date"`
	EndDate         string      `gorm:"not null; type:varchar(300)" json:"end_date"`
	Series          []*DagSerie `gorm:"many2many:dag_task_series; foreignKey:ID; references:ID; joinForeignKey: TaskID; joinReferences: SeriesID" json:"series"`
}

// 定义表名
func (a *Response) TableName() string {
	return "dag_tasks"
}

type ListResponse struct {
	Response
}

type Request struct {
	SeriesIDs       string `json:"series_ids"`       //系列id
	BuildRecordIDs  string `json:"build_record_ids"` // 编译记录id
	ReleaseVersion  string `json:"release_version"`  // Release 版本号
	VersionNumber   string `json:"version_number"`   // 版本号
	ReleaseType     string `json:"release_type"`     // 版本类型
	BaselineVersion string `json:"baseline_version"` // 基线版本
}

type DagSerie struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *documentauto.DagTask {
	return &documentauto.DagTask{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("Series")
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllWithFilters(name, sort, orderBy string, page, pageSize int, series []string, version, baseline, releaseType string) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("Series")

	if len(name) > 0 {
		db = db.Where("name LIKE ?", fmt.Sprintf("%%%s%%", name))
	}
	if len(series) > 0 {
		db = db.Joins("JOIN dag_task_series ON dag_task_series.task_id = dag_tasks.id").
			Where("dag_task_series.series_id IN (?)", series)
	}
	if len(version) > 0 {
		db = db.Where("version_number = ?", version)
	}
	if len(baseline) > 0 {
		db = db.Where("baseline_version = ?", baseline)
	}
	if len(releaseType) > 0 {
		db = db.Where("release_type = ?", releaseType)
	}
	db = db.Distinct()
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count error: %v", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data error: %v", err)
		return nil, err
	}

	list := map[string]interface{}{
		"items":       items,
		"total":       count,
		"limit":       pageSize,
		"series":      series,
		"version":     version,
		"baseline":    baseline,
		"releaseType": releaseType,
	}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (a *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func GetTaskByUUID(uuid string) (Response, error) {
	var taskResponse Response
	err := easygorm.GetEasyGormDb().Model(Model()).Where("uuid = ?", uuid).Find(&taskResponse).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get task by uuid err ", err)
		return taskResponse, err
	}
	return taskResponse, nil
}

func UpdateTask(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update task err ", err)
		return err
	}
	return nil
}

func GetReleaseDocTask(name string, page, pageSize int, orderBy, sort string) ([]Response, error) {
	var tasks []Response
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("DagSerie").Where("name = ?", name).Find(&tasks).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get release doc task err ", err)
		return nil, err
	}
	return tasks, nil
}

func GetTaskByID(id uint) (Response, error) {
	var task Response
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(&task).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get task by id err ", err)
		return task, err
	}
	return task, nil
}
