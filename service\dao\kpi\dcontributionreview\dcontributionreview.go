package dcontributionreview

import (
	"errors"
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/kpi"
	"irisAdminApi/service/dao/kpi/dcontribution"
	"irisAdminApi/service/dao/user/duser"
)

const ModelName = "贡献评审管理"

/*
	ContributionID uint `gorm:"not null"`
	ReviewerID     uint `gorm:"not null"`
	Judge          uint `gorm:"not null；default:0"` //0:评审中  1:通过 2:不通过
*/

type Response struct {
	ID             uint                    `json:"id"`
	UpdatedAt      string                  `json:"updated_at"`
	CreatedAt      string                  `json:"created_at"`
	ContributionID uint                    `json:"contribution_id"`
	ReviewerID     uint                    `json:"reviewer_id"`
	Judge          uint                    `json:"judge"`
	Comment        string                  `json:"comment"`
	Contributiton  *dcontribution.Response `gorm:"-" json:"contribution"`
	Reviewer       *duser.ApprovalResponse `gorm:"-" json:"reviewer,omitempty"`
}

type ListResponse struct {
	Response
}

type Request struct {
	Judge   uint   `json:"judge"`
	Comment string `json:"comment"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *kpi.ContributionReview {
	return &kpi.ContributionReview{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}
	if a.ReviewerID != object["ReviewerID"].(uint) {
		return errors.New("非当前用户的评审记录，禁止修改")
	}
	GetContribution(a)
	if a.Contributiton.Status != 0 {
		return errors.New("该贡献记录已评审结束，禁止修改评审记录")
	}
	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func BatchCreate(objects []map[string]interface{}) error {
	if len(objects) > 0 {
		err := easygorm.GetEasyGormDb().Model(Model()).Create(objects).Error
		if err != nil {
			logging.ErrorLogger.Errorf("create data err ", err)
			return err
		}
	}

	return nil
}

func FindByContributionID(id uint) ([]*ListResponse, error) {
	items := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("contribution_id = ?", id).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return items, err
	}
	GetUsers(items)
	return items, nil
}

func FindInContributionID(ids []uint) ([]*ListResponse, error) {
	items := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("contribution_id in ?", ids).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return items, err
	}
	GetUsers(items)
	return items, nil
}

func GetContribution(item *Response) error {
	contribution := dcontribution.Response{}
	err := contribution.Find(item.ContributionID)
	if err != nil {
		return err
	}
	item.Contributiton = &contribution
	return nil
}

func GetContributions(items []*ListResponse) error {
	contributionIds := []uint{}
	for _, item := range items {
		contributionIds = append(contributionIds, item.ContributionID)
	}

	contributions, err := dcontribution.FindInIds(contributionIds)
	if err != nil {
		return err
	}
	contributionMap := map[uint]*dcontribution.Response{}
	for _, contribution := range contributions {
		contributionMap[contribution.ID] = &contribution.Response
	}
	for _, item := range items {
		item.Contributiton = contributionMap[item.ContributionID]
	}

	return nil
}

func GetUsers(items []*ListResponse) error {
	userIds := []uint{}
	for _, item := range items {
		userIds = append(userIds, item.ReviewerID)
	}
	users, _ := duser.FindSimpleInIds(userIds)
	var userMap = make(map[uint]*duser.ApprovalResponse)
	for _, user := range users {
		userMap[user.Id] = user
	}
	for _, item := range items {
		item.Reviewer = userMap[item.ReviewerID]
	}
	return nil
}

// func FindContributionInIDs(ids uint) (map[string]interface{}, error) {
// 	var count int64
// 	var items []*ContributionListResponse

// 	db := easygorm.GetEasyGormDb().Model(dcontribution.Model())
// 	if len(name) > 0 {
// 		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
// 	}
// 	err := db.Count(&count).Error
// 	if err != nil {
// 		logging.ErrorLogger.Errorf("get list count err ", err)
// 		return nil, err
// 	}

// 	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
// 	err = db.Scopes(paginateScope).
// 		Find(&items).Error
// 	if err != nil {
// 		logging.ErrorLogger.Errorf("get list data err ", err)
// 		return nil, err
// 	}
// 	fmt.Println(items)
// 	contributionIDReviewsMap := map[uint][]*ListResponse{}
// 	contributionIDs := []uint{}
// 	for _, item := range items {
// 		contributionIDs = append(contributionIDs, item.ID)
// 	}
// 	reviews, err := FindInContributionID(contributionIDs)
// 	if err != nil {
// 		logging.ErrorLogger.Errorf("get list data err ", err)
// 	}
// 	for _, review := range reviews {
// 		if _, ok := contributionIDReviewsMap[review.ContributionID]; ok {
// 			contributionIDReviewsMap[review.ContributionID] = append(contributionIDReviewsMap[review.ContributionID], review)
// 		} else {
// 			contributionIDReviewsMap[review.ContributionID] = []*ListResponse{}
// 			contributionIDReviewsMap[review.ContributionID] = append(contributionIDReviewsMap[review.ContributionID], review)
// 		}
// 	}
// 	for _, item := range items {
// 		item.Reviews = contributionIDReviewsMap[item.ID]
// 	}
// 	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
// 	fmt.Println(list)
// 	return list, nil
// }
