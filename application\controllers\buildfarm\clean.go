package buildfarm

import (
	"fmt"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/buildfarm/dcronmakejob"
	"irisAdminApi/service/dao/buildfarm/dmakejob"
)

func CleanWorker() {
	// 两周以上的版本，每天保留最后一个版本
	// 两个月以上的版本，不保留
	logging.DebugLogger.Debug("清理编译农场归档开始")
	jobIDs, err := dcronmakejob.FindCronMakeJobsToCleanIn15to60Days()
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
	}
	fmt.Println(len(jobIDs))
	Clean(jobIDs)
	jobIDs, err = dcronmakejob.FindCronMakeJobsToCleanAfter60Days()
	if err != nil {
		logging.ErrorLogger.Errorf(err.<PERSON><PERSON>r())
	}
	fmt.Println(len(jobIDs))
	Clean(jobIDs)
	taskIDs, err := dmakejob.FindMakeJobsToClean()
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
	}
	fmt.Println(len(taskIDs))
	Clean(taskIDs)
	logging.DebugLogger.Debug("清理编译农场归档结束")
}

func Clean(ids []string) {
	for _, id := range ids {
		command := fmt.Sprintf(`cd %s && find ./%s -type f|grep -E '\.tar|\.bin|\.zip|\.rpm'|xargs -i rm {} -rf`, libs.Config.Buildfarm.Archivepath, id)
		output, err := libs.ExecCommand(command)
		if err != nil {
			logging.ErrorLogger.Errorf(err.Error())
		}
		logging.InfoLogger.Info(output)
	}
}
