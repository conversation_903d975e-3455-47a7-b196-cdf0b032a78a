package dqualitypointsrecoverie

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/qualitypoint"
	"irisAdminApi/service/dao/qualitypoint/dqualityviolation"
)

const ModelName = "积分消分记录表"

type Response struct {
	ID               uint                        `json:"id"`
	UpdatedAt        string                      `json:"updated_at"`
	CreatedAt        string                      `json:"created_at"`
	UserID           uint                        `gorm:"not null" json:"user_id" form:"user_id"`                   //用户ID
	ViolationID      uint                        `gorm:"not null" json:"violation_id" form:"violation_id"`         //事项ID
	PointsRecovered  float32                     `gorm:"not null" json:"points_recovered" form:"points_recovered"` //消分的积分值
	Year             uint                        `gorm:"not null" json:"year" form:"year"`                         //恢复操作的年度
	EffectiveDate    string                      `gorm:"not null" json:"effective_date" form:"effective_date"`     //消分生效日期
	UsageState       uint                        `gorm:"not null" json:"usage_state" form:"usage_state"`           //使用状态
	UsagePoint       float32                     `gorm:"not null" json:"usage_point" form:"usage_point"`           //已使用分数
	Qualityviolation *dqualityviolation.Response `gorm:"-" json:"qualityviolation"`
}

type ListResponse struct {
	Response
}

type Request struct {
	Name string `json:"name"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *qualitypoint.QualityPointsRecoverie {
	return &qualitypoint.QualityPointsRecoverie{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (a *Response) GetAllByUserIDAndYear(userId, year uint, name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}

	if userId > 0 {
		db = db.Where("user_id = ?", userId)
	}
	if year > 0 {
		db = db.Where("year = ?", year)
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}
	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	formatResponses(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func formatResponses(items []*ListResponse) {
	qualityViolationIds := []uint{}
	violationMap := map[uint]*dqualityviolation.Response{}
	for _, item := range items {
		qualityViolationIds = append(qualityViolationIds, item.ViolationID)
	}
	violations, err := dqualityviolation.FindInIds(qualityViolationIds)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return
	}

	for _, violation := range violations {
		violationMap[violation.ID] = &violation.Response
	}

	for _, item := range items {
		item.Qualityviolation = violationMap[item.ViolationID]
	}

}

type UserPointRecoverieData struct {
	UserID                    int     `json:"user_id"`
	Openid                    string  `json:"openid"`
	PointsRecovered           float32 `json:"points_recovered"`
	Year                      int     `json:"year"`
	Description               string  `json:"description"`
	EffectiveDate             string  `json:"effective_date"`
	UsageState                int     `json:"usage_state"`
	UsagePoint                float32 `json:"usage_point"`
	IssueLevel                string  `json:"issue_level"`
	DeductTaskDescription     string  `json:"deduct_task_description"`
	DeductEvidenceDescription string  `json:"deduct_evidence_description"`
	DepartmentName            string  `json:"department_name"`
	ParentDepartmentName      string  `json:"parent_department_name"`
}

func GetUserPointRecoverieData() ([]*UserPointRecoverieData, error) {
	items := []*UserPointRecoverieData{}
	sql := `
SELECT 
    qpr.user_id,
    uo.openid,
    qpr.points_recovered,
    qpr.year,
    qpr.effective_date,
    qpr.usage_state,
    qpr.usage_point,
    qv.description AS description,
    qv.deduct_task_description as deduct_task_description,
    qv.deduct_evidence_description as deduct_evidence_description,
    d.name AS department_name,
    pd.name AS parent_department_name
FROM 
    quality_points_recoveries AS qpr
LEFT JOIN 
    user_departments AS ud ON qpr.user_id = ud.user_id
LEFT JOIN 
    quality_violations AS qv ON qpr.violation_id = qv.id
LEFT JOIN 
    user_open_ids AS uo ON qpr.user_id = uo.user_id
LEFT JOIN 
   departments AS d ON ud.department_id = d.id
LEFT JOIN 
    departments AS pd ON d.parent_id = pd.id
WHERE 
    qpr.deleted_at IS NULL 
    AND qv.deleted_at IS NULL 
    AND ud.deleted_at IS NULL
    AND d.deleted_at IS NULL 
    AND uo.deleted_at IS NULL
    AND uo.openid IS NOT NULL 
    AND uo.openid != '';`
	err := easygorm.GetEasyGormDb().Table("quality_points_recoveries").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}
