package dproblem

import (
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/kpi"
	"irisAdminApi/service/dao/kpi/dproblemproccess"
	"irisAdminApi/service/dao/user/duser"
	"strings"
)

const ModelName = "问题管理"

/*
	Description     string `gorm:"not null; type:varchar(512)"`
	ProblemSourceID uint   `gorm:"not null"`
	PlanCloseAt     string `gorm:"not null; type:varchar(30)"`
	UserID          uint   `gorm:"not null"`
	Status          uint   `gorm:"not null；default:0"` //0:未闭环  1:已闭环
	OwnerID         uint   `gorm:"not null"`
*/

type Response struct {
	ID              uint                    `json:"id"`
	UpdatedAt       string                  `json:"updated_at"`
	CreatedAt       string                  `json:"created_at"`
	DiscoveredAt    string                  `json:"discovered_at"`
	PlanCloseAt     string                  `json:"plan_close_at"`
	Description     string                  `json:"description"`
	ProblemSourceID uint                    `json:"problem_source_id"`
	UserID          uint                    `json:"user_id"`
	DepartmentID    uint                    `json:"department_id"`
	OwnerID         uint                    `json:"owner_id"`
	Status          uint                    `json:"status"`
	User            *duser.ApprovalResponse `gorm:"-" json:"user,omitempty"`
	Owner           *duser.ApprovalResponse `gorm:"-" json:"owner,omitempty"`
}

type ListResponse struct {
	Response
}

type Request struct {
	DiscoveredAt    string `json:"discovered_at"`
	PlanCloseAt     string `json:"plan_close_at"`
	Description     string `json:"description"`
	ProblemSourceID uint   `json:"problem_source_id"`
	OwnerID         uint   `json:"owner_id"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *kpi.Problem {
	return &kpi.Problem{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	GetUsers(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	err = easygorm.GetEasyGormDb().Unscoped().Delete(dproblemproccess.Model(), "problem_id = ?", id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func GetUsers(items []*ListResponse) error {
	userIds := []uint{}
	for _, item := range items {
		userIds = append(userIds, item.UserID)
		userIds = append(userIds, item.OwnerID)
	}
	users, _ := duser.FindSimpleInIds(userIds)
	var userMap = make(map[uint]*duser.ApprovalResponse)
	for _, user := range users {
		userMap[user.Id] = user
	}
	for _, item := range items {
		item.Owner = userMap[item.OwnerID]
		item.User = userMap[item.UserID]
	}
	return nil
}

func AllByUserID(uid uint, name, sort, orderBy string, page, pageSize int, status, start, end string) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	db = db.Where("user_id = ?", uid)

	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	if len(status) > 0 {
		db = db.Where("status = ?", status)
	}
	if len(start) > 0 {
		db = db.Where("discovered_at >= ?", start)
	}
	if len(end) > 0 {
		db = db.Where("discovered_at <= ?", end)
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	GetUsers(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func AllByOwnerID(uid uint, name, sort, orderBy string, page, pageSize int, status, start, end string) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	db = db.Where("owner_id = ?", uid)

	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	if len(status) > 0 {
		db = db.Where("status = ?", status)
	}
	if len(start) > 0 {
		db = db.Where("discovered_at >= ?", start)
	}
	if len(end) > 0 {
		db = db.Where("discovered_at <= ?", end)
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	GetUsers(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func AllProblems(name, sort, orderBy string, page, pageSize int, status, start, end, department string) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	if len(status) > 0 {
		db = db.Where("status = ?", status)
	}
	if len(start) > 0 {
		db = db.Where("discovered_at >= ?", start)
	}
	if len(end) > 0 {
		db = db.Where("discovered_at <= ?", end)
	}
	if len(department) > 0 {
		departmentIds := strings.Split(department, ",")
		db = db.Where("department_id in ?", departmentIds)
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	GetUsers(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}
