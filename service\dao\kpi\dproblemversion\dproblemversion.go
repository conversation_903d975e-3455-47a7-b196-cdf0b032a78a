package dcontributionversion

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/kpi"
	"irisAdminApi/service/dao/kpi/dcontribution"
)

const ModelName = "问题版本管理"

/*
	Description        string `gorm:"not null; type:varchar(512)"`
	ContributedAt      string `gorm:"not null; type:varchar(30)"`
	ContributionTypeID uint   `gorm:"not null"`
	UserID             uint   `gorm:"not null"`
	Status             uint   `gorm:"not null；default:0"` //0:评审中  1:通过 2:不通过
*/

type Response struct {
	ID              uint                    `json:"id"`
	UpdatedAt       string                  `json:"updated_at"`
	CreatedAt       string                  `json:"created_at"`
	ContributionID  uint                    `json:"contribution_id"`
	ContributionIDs string                  `json:"contribution_ids"`
	Contributiton   *dcontribution.Response `gorm:"-" json:"contribution"`
}

type ListResponse struct {
	Response
}

type Request struct {
	Description     string `json:"description"`
	ContributedAt   string `json:"contributed_at"`
	ContributionID  uint   `json:"contribution_id"`
	ContributionIDs string `json:"contribution_ids"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *kpi.ContributionVersion {
	return &kpi.ContributionVersion{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	GetContributions(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func All() ([]*ListResponse, error) {
	var items []*ListResponse

	err := easygorm.GetEasyGormDb().Model(Model()).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	return items, nil
}

func GetContribution(item *Response) error {
	contribution := dcontribution.Response{}
	err := contribution.Find(item.ContributionID)
	if err != nil {
		return err
	}
	item.Contributiton = &contribution
	return nil
}

func GetContributions(items []*ListResponse) error {
	contributionIds := []uint{}
	for _, item := range items {
		contributionIds = append(contributionIds, item.ContributionID)
	}

	contributions, err := dcontribution.FindInIds(contributionIds)
	if err != nil {
		return err
	}
	contributionMap := map[uint]*dcontribution.Response{}
	for _, contribution := range contributions {
		contributionMap[contribution.ID] = &contribution.Response
	}
	for _, item := range items {
		item.Contributiton = contributionMap[item.ContributionID]
	}
	return nil
}
