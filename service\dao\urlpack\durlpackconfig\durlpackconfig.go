package durlpackconfig

import (
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/urlpack"
)

const ModelName = "URL库作业表"

type UrlPackConfig struct {
	urlpack.UrlPackConfig
}

type ListResponse struct {
	UrlPackConfig
}

type Request struct {
	urlpack.UrlPackJob
	OnlyFull bool `json:"only_full"`
}

func (a *UrlPackConfig) ModelName() string {
	return ModelName
}

func Model() *urlpack.UrlPackConfig {
	return &urlpack.UrlPackConfig{}
}

func (a *UrlPackConfig) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse
	var typeMap = map[string]string{
		"海":       "oversea",
		"海外":      "oversea",
		"海外库":     "oversea",
		"大":       "large",
		"大库":      "large",
		"中":       "middle",
		"中库":      "middle",
		"小":       "small",
		"小库":      "small",
		"oversea": "oversea",
		"middle":  "middle",
		"small":   "small",
	}
	db := easygorm.GetEasyGormDb().Model(Model())
	where := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		where = where.Where("type = ?", typeMap[name]).
			Or("version like ?", fmt.Sprintf("%%%s%%", name)).
			Or("base_version like ?", fmt.Sprintf("%%%s%%", name))
	}

	db = db.Where(where)
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *UrlPackConfig) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *UrlPackConfig) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *UrlPackConfig) CreateV2(object interface{}) error {
	return nil
}

func (a *UrlPackConfig) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *UrlPackConfig) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *UrlPackConfig) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *UrlPackConfig) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *UrlPackConfig) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *UrlPackConfig) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *UrlPackConfig) FindByVersion(version string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("version  = ? and status = 1", version).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *UrlPackConfig) Last(t string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("type = ? and status = 1", t).Order("id desc").Limit(1).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find last job err ", err)
		return err
	}
	return nil
}

func (a *UrlPackConfig) AllByNameAndVersionAndBaseVersion(name, version, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse
	var typeMap = map[string]string{
		"海":       "oversea",
		"海外":      "oversea",
		"海外库":     "oversea",
		"大":       "large",
		"大库":      "large",
		"中":       "middle",
		"中库":      "middle",
		"小":       "small",
		"小库":      "small",
		"oversea": "oversea",
		"middle":  "middle",
		"small":   "small",
	}
	db := easygorm.GetEasyGormDb().Model(Model())
	where := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		where = where.Where("type = ?", typeMap[name]).
			Or("version like ?", fmt.Sprintf("%%%s%%", name)).
			Or("base_version like ?", fmt.Sprintf("%%%s%%", name))
	}
	if len(version) > 0 {
		where = where.Or("version like ?", fmt.Sprintf("%%%s%%", version))
	}

	db = db.Where(where)
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func GetAllMap() (map[string]*ListResponse, error) {
	items := []*ListResponse{}
	itemMap := map[string]*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Order("id desc").Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find last job err ", err)
		return nil, err
	}
	for _, item := range items {
		itemMap[item.Type] = item
	}
	return itemMap, nil
}
