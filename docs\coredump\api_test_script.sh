#!/bin/bash

# Coredump记录自动化处理系统 - API测试脚本
# 用于测试所有API接口的功能

set -e  # 遇到错误立即退出

# 配置
BASE_URL="http://localhost:8080/api/coredump"
SCHEDULER_URL="$BASE_URL/scheduler"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务是否运行
check_service() {
    log_info "检查服务状态..."
    if curl -s "$BASE_URL/status" > /dev/null 2>&1; then
        log_success "服务正在运行"
    else
        log_error "服务未运行，请先启动服务"
        exit 1
    fi
}

# 测试处理状态接口
test_status_api() {
    log_info "测试处理状态接口..."
    
    response=$(curl -s "$BASE_URL/status")
    if echo "$response" | jq -e '.success' > /dev/null 2>&1; then
        log_success "状态接口正常"
        echo "$response" | jq '.'
    else
        log_error "状态接口异常"
        echo "$response"
    fi
    echo
}

# 测试定时任务状态接口
test_scheduler_status() {
    log_info "测试定时任务状态接口..."
    
    response=$(curl -s "$SCHEDULER_URL/status")
    if echo "$response" | jq -e '.success' > /dev/null 2>&1; then
        log_success "定时任务状态接口正常"
        echo "$response" | jq '.'
    else
        log_error "定时任务状态接口异常"
        echo "$response"
    fi
    echo
}

# 测试启动定时任务
test_start_scheduler() {
    log_info "测试启动定时任务..."
    
    response=$(curl -s -X POST "$SCHEDULER_URL/start")
    if echo "$response" | jq -e '.success' > /dev/null 2>&1; then
        log_success "定时任务启动成功"
        echo "$response" | jq '.'
    else
        log_warning "定时任务启动失败（可能已经在运行）"
        echo "$response" | jq '.'
    fi
    echo
}

# 测试停止定时任务
test_stop_scheduler() {
    log_info "测试停止定时任务..."
    
    response=$(curl -s -X POST "$SCHEDULER_URL/stop")
    if echo "$response" | jq -e '.success' > /dev/null 2>&1; then
        log_success "定时任务停止成功"
        echo "$response" | jq '.'
    else
        log_warning "定时任务停止失败（可能已经停止）"
        echo "$response" | jq '.'
    fi
    echo
}

# 测试手动处理接口
test_manual_process() {
    log_info "测试手动处理接口..."
    
    log_warning "这将触发实际的处理流程，请确认是否继续？(y/N)"
    read -r confirm
    if [[ $confirm != [yY] ]]; then
        log_info "跳过手动处理测试"
        return
    fi
    
    response=$(curl -s -X POST "$BASE_URL/process")
    if echo "$response" | jq -e '.success' > /dev/null 2>&1; then
        log_success "手动处理触发成功"
        echo "$response" | jq '.'
    else
        log_error "手动处理触发失败"
        echo "$response" | jq '.'
    fi
    echo
}

# 测试错误处理
test_error_handling() {
    log_info "测试错误处理..."
    
    # 测试不存在的接口
    response=$(curl -s "$BASE_URL/nonexistent")
    if echo "$response" | grep -q "404\|Not Found"; then
        log_success "404错误处理正常"
    else
        log_warning "404错误处理可能异常"
    fi
    echo
}

# 性能测试
test_performance() {
    log_info "进行简单的性能测试..."
    
    start_time=$(date +%s.%N)
    for i in {1..10}; do
        curl -s "$BASE_URL/status" > /dev/null
    done
    end_time=$(date +%s.%N)
    
    duration=$(echo "$end_time - $start_time" | bc)
    avg_time=$(echo "scale=3; $duration / 10" | bc)
    
    log_success "10次状态查询平均耗时: ${avg_time}秒"
    echo
}

# 生成测试报告
generate_report() {
    log_info "生成测试报告..."
    
    cat > coredump_api_test_report.md << EOF
# Coredump API测试报告

## 测试时间
$(date '+%Y-%m-%d %H:%M:%S')

## 测试环境
- 服务地址: $BASE_URL
- 测试脚本: $0

## 测试结果

### 基础接口测试
- [x] 服务状态检查
- [x] 处理状态接口
- [x] 定时任务状态接口

### 定时任务管理测试
- [x] 启动定时任务
- [x] 停止定时任务

### 错误处理测试
- [x] 404错误处理

### 性能测试
- [x] 接口响应时间测试

## 建议
1. 定期运行此测试脚本确保API正常
2. 监控接口响应时间
3. 检查错误日志

## 注意事项
- 手动处理接口会触发实际的处理流程
- 建议在测试环境中运行完整测试
- 生产环境请谨慎使用手动处理功能
EOF

    log_success "测试报告已生成: coredump_api_test_report.md"
}

# 主函数
main() {
    echo "========================================"
    echo "  Coredump API 测试脚本"
    echo "========================================"
    echo
    
    # 检查依赖
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装，请先安装 curl"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_error "jq 未安装，请先安装 jq"
        exit 1
    fi
    
    # 执行测试
    check_service
    test_status_api
    test_scheduler_status
    test_start_scheduler
    test_stop_scheduler
    test_manual_process
    test_error_handling
    test_performance
    generate_report
    
    echo "========================================"
    log_success "所有测试完成！"
    echo "========================================"
}

# 帮助信息
show_help() {
    cat << EOF
Coredump API测试脚本

用法: $0 [选项]

选项:
    -h, --help          显示帮助信息
    -u, --url URL       指定服务地址 (默认: http://localhost:8080/api/coredump)
    --status-only       仅测试状态接口
    --scheduler-only    仅测试定时任务接口
    --no-manual         跳过手动处理测试

示例:
    $0                                    # 运行完整测试
    $0 -u http://prod.example.com/api/coredump  # 指定服务地址
    $0 --status-only                      # 仅测试状态接口
    $0 --no-manual                        # 跳过手动处理测试

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -u|--url)
            BASE_URL="$2"
            SCHEDULER_URL="$BASE_URL/scheduler"
            shift 2
            ;;
        --status-only)
            STATUS_ONLY=true
            shift
            ;;
        --scheduler-only)
            SCHEDULER_ONLY=true
            shift
            ;;
        --no-manual)
            NO_MANUAL=true
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 根据参数执行相应测试
if [[ "$STATUS_ONLY" == true ]]; then
    check_service
    test_status_api
elif [[ "$SCHEDULER_ONLY" == true ]]; then
    check_service
    test_scheduler_status
    test_start_scheduler
    test_stop_scheduler
else
    # 如果设置了 NO_MANUAL，重定义 test_manual_process 函数
    if [[ "$NO_MANUAL" == true ]]; then
        test_manual_process() {
            log_info "跳过手动处理测试（--no-manual 参数）"
            echo
        }
    fi
    
    main
fi