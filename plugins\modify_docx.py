import sys
import json
import re
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT

# 定义 placeholder_pattern 全局变量
placeholder_pattern = re.compile(r'{{.*?}}')

def contains_picture(run):
    """检查运行（Run）是否包含图片"""
    return 'graphic' in run._element.xml

def insert_image(paragraph, image_path):
    """在指定段落中插入图片（仅在路径有效时）"""
    if image_path:  # 检查路径是否非空
        run = paragraph.add_run()
        run.add_picture(image_path, width=Inches(5.9055))  # 根据需求调整图片宽度
        paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT  # 设置段落左对齐

def replace_placeholders(paragraph, replacements):
    """
    替换段落中的占位符，同时保留原先占位符的样式。
    """
    try:
        # 获取所有运行及其文本
        runs = paragraph.runs
        full_text = ''.join(run.text for run in runs)
        
        for placeholder, replacement in replacements.items():
            # 跳过已处理的图片占位符
            if placeholder in ["{{localImgsrc}}", "{{onlineImgsrc}}"]:
                continue

            # 查找所有占位符的位置
            for match in re.finditer(re.escape(placeholder), full_text):
                start, end = match.start(), match.end()

                # 记录替换的起始和结束运行索引及位置
                current_pos = 0
                start_run = None
                end_run = None
                start_index = None
                end_index = None

                for idx, run in enumerate(runs):
                    run_text = run.text
                    run_start = current_pos
                    run_end = current_pos + len(run_text)

                    if run_start <= start < run_end:
                        start_run = idx
                        start_index = start - run_start

                    if run_start < end <= run_end:
                        end_run = idx
                        end_index = end - run_start
                        break

                    current_pos += len(run_text)

                if start_run is not None and end_run is not None:
                    if start_run == end_run:
                        run = runs[start_run]
                        new_text = run.text[:start_index] + replacement + run.text[end_index:]
                        run.text = new_text
                    else:
                        # 处理起始运行
                        first_run = runs[start_run]
                        first_run.text = first_run.text[:start_index] + replacement

                        # 清空中间运行的文本
                        for i in range(start_run + 1, end_run):
                            runs[i].text = ''

                        # 处理结束运行
                        last_run = runs[end_run]
                        last_run.text = last_run.text[end_index:]
        
        # 处理图片占位符
        full_text = ''.join(run.text for run in runs)
        for key in ["{{localImgsrc}}", "{{onlineImgsrc}}"]:
            if key in full_text and key in replacements:
                # 清除包含图片占位符的所有运行文本
                for run in runs:
                    if key in run.text:
                        run.text = ''
                # 插入图片
                insert_image(paragraph, replacements[key])
                
    except Exception as e:
        print(f"Error processing paragraph: {e}")

def clear_all_placeholders(paragraph):
    """
    清除段落中所有符合 {{...}} 格式的占位符。
    处理跨越多run的占位符。
    """
    try:
        runs = paragraph.runs
        full_text = ''.join(run.text for run in runs)
        matches = list(placeholder_pattern.finditer(full_text))
        
        for match in reversed(matches):  # 反向处理以避免索引问题
            start, end = match.span()
            replacement = ''

            # 找到每个占位符的位置对应的运行和索引
            current_pos = 0
            start_run = None
            end_run = None
            start_index = None
            end_index = None

            for idx, run in enumerate(runs):
                run_text = run.text
                run_start = current_pos
                run_end = current_pos + len(run_text)

                if run_start <= start < run_end:
                    start_run = idx
                    start_index = start - run_start

                if run_start < end <= run_end:
                    end_run = idx
                    end_index = end - run_start
                    break

                current_pos += len(run_text)

            if start_run is not None and end_run is not None:
                if start_run == end_run:
                    run = runs[start_run]
                    run.text = run.text[:start_index] + replacement + run.text[end_index:]
                else:
                    # 处理起始运行
                    first_run = runs[start_run]
                    first_run.text = first_run.text[:start_index] + replacement

                    # 清空中间运行的文本
                    for i in range(start_run + 1, end_run):
                        runs[i].text = ''

                    # 处理结束运行
                    last_run = runs[end_run]
                    last_run.text = last_run.text[end_index:]
    except Exception as e:
        print(f"Error clearing placeholders: {e}")

def clear_remaining_placeholders(paragraphs):
    """清除所有剩余的占位符，即以 '{{' 开头，'}}' 结尾的文本"""
    for para in paragraphs:
        clear_all_placeholders(para)

def replace_text_in_paragraphs(paragraphs, replacements):
    """遍历段落，进行占位符替换"""
    for para in paragraphs:
        replace_placeholders(para, replacements)

def replace_text_in_tables(tables, replacements):
    """遍历表格中的单元格内容，进行占位符替换"""
    for table in tables:
        for row in table.rows:
            for cell in row.cells:
                replace_text_in_paragraphs(cell.paragraphs, replacements)
                replace_text_in_tables(cell.tables, replacements)  # 递归处理嵌套表格

def replace_text_in_headers_footers(headers_footers, replacements):
    """遍历页眉和页脚中的内容，进行占位符替换"""
    for header_footer in headers_footers:
        replace_text_in_paragraphs(header_footer.paragraphs, replacements)
        replace_text_in_tables(header_footer.tables, replacements)  # 处理页眉/页脚中的表格

def replace_text_in_docx(input_file, output_file, replacements):
    doc = Document(input_file)
    # 处理段落和标题的文本替换
    replace_text_in_paragraphs(doc.paragraphs, replacements)

    # 处理表格中的文本替换
    replace_text_in_tables(doc.tables, replacements)

    # 处理页眉和页脚中的文本替换
    for section in doc.sections:
        headers = [
            section.header,
            section.first_page_header,
            section.even_page_header
        ]
        footers = [
            section.footer,
            section.first_page_footer,
            section.even_page_footer
        ]
        replace_text_in_headers_footers(headers + footers, replacements)

    # 清除所有剩余的占位符
    clear_remaining_placeholders(doc.paragraphs)
    clear_remaining_placeholders_in_tables(doc.tables, placeholder_pattern)
    
    # 保存修改后的文档
    doc.save(output_file)

def clear_remaining_placeholders_in_tables(tables, placeholder_pattern):
    """递归清除表格中的剩余占位符"""
    for table in tables:
        for row in table.rows:
            for cell in row.cells:
                clear_remaining_placeholders(cell.paragraphs)
                clear_remaining_placeholders_in_tables(cell.tables, placeholder_pattern)

if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("用法: python modify_docx.py <输入文件> <输出文件> <替换JSON>")
        sys.exit(1)

    input_file = sys.argv[1]
    output_file = sys.argv[2]
    try:
        replacements = json.loads(sys.argv[3])
    except json.JSONDecodeError:
        print("错误: 替换参数的JSON格式无效。")
        sys.exit(1)

    replace_text_in_docx(input_file, output_file, replacements)