package buildfarm

import (
	"irisAdminApi/application/middleware"

	"github.com/kataras/iris/v12"
)

var Party = func(party iris.Party) {
	party.Get("/patch/downloadrpmfile", DownloadRPMFile).Name = "下载补丁编译的RPM文件"
	party.Get("/patch/downloadrawrpmfile", DownloadRawRPMFile).Name = "直接下载补丁编译的原始RPM文件"
	party.Get("/patch/showpatchlog", ShowPatchLog).Name = "查看补丁编译日志文件"
	party.Get("/patchquery", GetMakePatchJobs).Name = "补丁编译查询"
	party.Get("/patchquery/{id:uint}", GetMakePatchJob).Name = "补丁编译查询"
	party.Get("/cronmake", GetCronMakeJobs).Name = "查询每日编译任务"
	party.Get("/make", GetMakeJobs).Name = "查询编译任务"
	party.Post("/cronmake/report", GetCronMakeJobReport).Name = "获取每日编译报告"
	party.Post("/cronmake/sendmail", SendTestLink).Name = "发送转测试邮件"
	party.Get("/cronmake/{id:uint}/packages", GetCronMakeJobPackages).Name = "获取每日编译Package"
	party.Get("/make/{id:uint}/packages", GetMakeJobPackages).Name = "获取普通编译package"
	party.Get("/make/{id:uint}/smoke", GetMakeJobSmokeStatus).Name = "获取编译任务冒烟状态"
	party.Get("/cronmake/{id:uint}/smoke", GetCronMakeJobSmokeStatus).Name = "获取每日编译冒烟状态"

	party.Get("/branches", GetBranchesV3).Name = "获取编译项目分支"
	party.Get("/products", GetProductsV3).Name = "获取编译产品型号"
	party.Get("/defconfigs", GetDefconfigsV3).Name = "获取编译Defconfig"
	party.Get("/targets", GetTargetsV3).Name = "获取编译Target"
	party.Get("/feed", GetFeedsConfigDefaultV3).Name = "获取编译Feed"
	//party.Use之前不需要鉴权

	party.Get("/makejobs", GetMakeJobsV3).Name = "查询生测编译任务"
	party.PartyFunc("/archives", func(party iris.Party) {
		party.Get("/{root:path}", GetArchiveFiles).Name = "下载归档文件"
	})

	party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) //登录验证

	party.Get("/audits", GetTaskAudits).Name = "获取待评审作业"
	party.Get("/audits/{task_id:string}/details", GetTaskAuditDetails).Name = "获取待评审作业详情"
	party.Get("/audits/{task_id:string}/historys", GetTaskAuditHistorys).Name = "获取待评审历史"
	party.Post("/audits/audit", UpdateTaskAudit).Name = "更新评审作业详情"

	party.Post("/make/{id:uint}/smoke", UpdateMakeJobSmokeStatus).Name = "更新编译任务冒烟状态"
	party.Post("/cronmake/{id:uint}/smoke", UpdateCronMakeJobSmokeStatus).Name = "更新每日编译冒烟状态"
	party.Post("/server/add", CreateServer).Name = "添加编译服务器"
	party.Post("/server/test", TestConnection).Name = "测试服务器连接"
	party.Get("/server", GetServers).Name = "获取编译服务器"
	party.Get("/server/status", GetServerStatus).Name = "获取编译服务器"
	party.Post("/gitjob", CreateGitJob).Name = "创建任务"
	party.Post("/makejobs", CreateMakeJobV3).Name = "创建编译任务"
	party.Get("/gitjob", GetGitJobs).Name = "查看任务"
	party.Get("/queue", GetQueueJobs).Name = "查看队列"
	party.Get("/gitjob/{id:uint}", GetGitjob).Name = "查看git任务状态"
	// party.Get("/gitjob/{id:uint}/log", Log).Name = "查看日志"
	party.Post("/gitjob/{id:uint}/restart", RestartJob).Name = "重新运行git任务"
	party.Get("/gitjob/{id:uint}/products", GetProducts).Name = "获取products列表"
	party.Get("/gitjob/{id:uint}/defconfigs", GetDefConfigs).Name = "获取defconfigs列表"
	party.Post("/gitjob/{id:uint}/install-devel", InstallDevel).Name = "执行install-devel"
	party.Get("/gitjob/{id:uint}/targets", GetMakeTarget).Name = "获取make targets列表"
	party.Post("/gitjob/{id:uint}/stop", StopAndDeleteJob).Name = "停止并删除作业"
	party.Post("/gitjob/{id:uint}/make", CreateMakeJobV2).Name = "运行编译任务"
	// party.Post("/gitjob/{id:uint}/incrementmake", CreateIncrementMakeJobV2).Name = "运行增量编译任务"
	party.Get("/gitjob/{id:uint}/feedsconfigdefault", GetFeedsConfigDefault).Name = "获取编译仓库"
	// party.Post("/gitjob/make", CreateMakeJobV2).Name = "运行编译任务"
	party.Get("/project", GetEnableProjects).Name = "获取项目列表"
	party.Get("/git/project", GetBuildProjects).Name = "获取GIT项目列表"
	party.Get("/git/userproject", GetUserProjects).Name = "获取用户项目列表"
	party.Get("/git/project/{id:uint}/branch", GetProjectBranches).Name = "获取项目分支列表"
	party.Get("/baseline", GetBaselines).Name = "获取基线列表"
	party.Get("/checkversion", CheckVersion).Name = "检查分支版本"
	party.Get("/patch", GetPatchJobs).Name = "补丁编译列表"
	party.Post("/patch", CreatePatchJob).Name = "创建补丁编译"
	party.Get("/patch/{id:uint}", GetPatchJob).Name = "查看补丁编译任务"
	party.Get("/patch/{id:uint}/defconfigs", GetPatchDefConfigs).Name = "获取补丁编译defconfigs列表"
	party.Post("/patch/{id:uint}/make", CreatePatchMakeJob).Name = "运行补丁编译任务"

	party.Post("/patch/getdependens", GetDependencyData).Name = "获取依赖补丁数据"
	party.Get("/patch/getpatchjobs", GetPatchJobsV2).Name = "补丁编译列表(v2)"
	party.Post("/patch/createpatch", CreatePatchJobV2).Name = "创建补丁编译(V2)"
	party.Post("/patch/{id:uint}/makeV2", CreatePatchMakeJobV2).Name = "运行补丁编译任务(V2)"
	party.Get("/patch/getnewpatchjobs", GetNewPatchList).Name = "获取补丁数据(单补丁+集合补丁)"
	party.Get("/patch/{id:uint}/fetchpatchdependencies", FetchPatchDependencies).Name = "获取单补丁依赖项ID"
	party.Post("/patch/createcollectionpatch", CreateCollectionPatchJob).Name = "创建集合补丁编译"
	party.Post("/patch/{id:uint}/makecollectionpatch", CreateCollectionPatchMakeJob).Name = "运行集合补丁编译任务"
	party.Get("/patch/cronmake", GetCronMakeJobsV2).Name = "查询每日编译任务"

	party.Get("/softversions", GetSoftVersions).Name = "获取产品软件版本"
	party.Post("/coverity/{id:uint}/start", StartCoverityMakeProductjobQueue).Name = "允许商业代码检查"

	party.Post("/cpld/jobs", CreateCpldBuildJob).Name = "创建CPLD打包作业"
	party.Get("/cpld/jobs", GetCpldBuildJobs).Name = "获取CPLD打包作业列表"
	party.Get("/cpld/jobs/{id:uint}", GetCpldBuildJob).Name = "获取CPLD打包作业详情"
	party.Get("/cpld/jobs/{id:uint}/{filename:string}", DownloadCpldFile).Name = "获取CPLD固件包"
	party.Get("/cpld/jobs/{id:uint}/log", DownloadCpldBuildLog).Name = "获取CPLD打包作业日志"
}
