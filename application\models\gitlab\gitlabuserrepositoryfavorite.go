package gitlab

import "irisAdminApi/application/models"

type UserRepositoryFavorite struct {
	models.ModelBase
	UserId           uint   `gorm:"not null; type:varchar(60)" json:"user_id"`
	RepositoryId     uint   `gorm:"not null" json:"repository_id"`
	RepositoryName   string `gorm:"not null; type:varchar(60)" json:"repository_name"`
	RepositoryBranch string `gorm:"not null; type:varchar(60)" json:"repository_branch"`
}
