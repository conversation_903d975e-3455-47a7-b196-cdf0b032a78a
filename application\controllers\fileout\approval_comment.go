package fileout

import (
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/fileout/dapprovalcomment"

	"github.com/kataras/iris/v12"
)

func GetApprovalComments(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get approval id err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	details, err := dapprovalcomment.FindCommentsByApprovalId(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, details, response.NoErr.Msg))
	return
}
