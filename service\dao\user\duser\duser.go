package duser

import (
	"errors"
	"fmt"
	"sort"
	"strings"

	"strconv"

	"irisAdminApi/service/dao/user/ddepartment"
	"irisAdminApi/service/dao/user/drole"
	"irisAdminApi/service/dao/user/duserdepartment"
	"irisAdminApi/service/dao/user/dusergroup"
	"irisAdminApi/service/dao/user/duserwhitelist"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/user"
)

const ModelName = "用户管理"

type User struct {
	ID           uint                         `json:"id"`
	Name         string                       `json:"name"`
	Username     string                       `json:"username"`
	Intro        string                       `json:"introduction"`
	Avatar       string                       `json:"avatar"`
	UpdatedAt    string                       `json:"updated_at"`
	CreatedAt    string                       `json:"created_at"`
	Roles        []string                     `gorm:"-" json:"roles"`
	Department   *ddepartment.Department      `gorm:"-" json:"department"`
	DepartmentID uint                         `gorm:"-" json:"department_id"`
	Enable       bool                         `json:"enable"`
	OpenID       string                       `json:"open_id"`
	WhiteList    duserwhitelist.UserWhiteList `gorm:"->" json:"white_list"`
}

type UserPassword struct {
	Password string
}

type ListResponse struct {
	User
	Roles []string `gorm:"-" json:"roles"`
}

type ApprovalResponse struct {
	Id   uint   `json:"id"`
	Name string `json:"name"`
}

type UserNameResponse struct {
	Id       uint   `json:"id"`
	Username string `json:"username"`
}

type UserReq struct {
	Name         string   `json:"name" `
	OldPassword  string   `json:"oldpassword"`
	Username     string   `json:"username"`
	Password     string   `json:"password"`
	Intro        string   `json:"introduction"`
	Avatar       string   `json:"avatar"`
	Roles        []string `json:"roles"`
	DepartmentID string   `json:"department_id"`
	Enable       bool     `json:"enable"`
}

func (u *User) ModelName() string {
	return ModelName
}

func Model() *user.User {
	return &user.User{}
}

func (u *User) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var users []*ListResponse
	db := easygorm.GetEasyGormDb().Model(Model()).Preload("WhiteList")
	if len(name) > 0 {
		db = db.Where("name like ? or username like ?", fmt.Sprintf("%%%s%%", name), fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&users).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	// 查询用户角色
	getRoles(users)
	// 查询用户组
	getUsersGroupAndDepartment(users)
	list := map[string]interface{}{"items": users, "total": count, "limit": pageSize}
	return list, nil
}

func (u *User) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var users []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("WhiteList")
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&users).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	// 查询用户角色
	getRoles(users)
	getUsersGroupAndDepartment(users)
	list := map[string]interface{}{"items": users, "total": count, "limit": pageSize}
	return list, nil
}

func getRoles(users []*ListResponse) {
	var roleIds []string
	userRoleIds := make(map[uint][]string)
	for _, user := range users {
		userRoleId := easygorm.GetRolesForUser(user.ID)
		userRoleIds[user.ID] = userRoleId
		for _, item := range userRoleId {
			roleIds = append(roleIds, strings.TrimPrefix(item, "role::"))
		}
	}

	roles, err := drole.FindInId(roleIds)
	if err != nil {
		logging.ErrorLogger.Errorf("get role get err ", err)
	}

	for _, user := range users {
		user.Roles = []string{}
		for _, role := range roles {
			// sRoleId := strconv.FormatInt(int64(role.Id), 10)
			if libs.InArrayS(userRoleIds[user.ID], fmt.Sprintf("%v", role.ID)) {
				user.Roles = append(user.Roles, role.Name)
			}
			if libs.InArrayS(userRoleIds[user.ID], fmt.Sprintf("role::%v", role.ID)) {
				user.Roles = append(user.Roles, role.Name)
			}
		}
	}
}

func getUsersGroupAndDepartment(users []*ListResponse) {

	departmentMap := map[uint]*ddepartment.Department{}
	userDepartmentMap := map[uint]uint{}
	departments, err := ddepartment.FindAll()
	if err != nil {
		logging.ErrorLogger.Errorf("get role get err ", err)
	}

	for _, department := range departments {
		departmentMap[department.ID] = department
	}

	userIDs := []uint{}
	for _, user := range users {
		if !libs.InArrayUint(userIDs, user.ID) {
			userIDs = append(userIDs, user.ID)
		}
	}
	userDepartments, err := duserdepartment.FindInUserIds(userIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("get role get err ", err)
	}
	for _, userDepartment := range userDepartments {
		userDepartmentMap[userDepartment.UserID] = userDepartment.DepartmentID
	}

	for _, user := range users {
		for _, userDepartment := range userDepartments {
			if userDepartment.UserID == user.ID {
				user.DepartmentID = userDepartmentMap[user.ID]
				user.Department = departmentMap[userDepartmentMap[user.ID]]
				break
			}
		}
	}

}

func (u *User) FindByUserName(username string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("WhiteList").Where("username = ?", username).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user by username ", username, " err ", err)
		return err
	}
	return nil
}

func (u *User) Create(object map[string]interface{}) error {
	var username, group string
	var roles []string
	if uname, ok := object["Username"].(string); ok {
		username = object["Username"].(string)
		err := u.FindByUserName(uname)
		if err != nil {
			logging.ErrorLogger.Errorf("create user find by username get err ", err)
			return err
		}

		if u.ID > 0 {
			return errors.New(fmt.Sprintf("username %s is being used", username))
		}
	}

	if r, ok := object["Roles"].([]string); ok {
		roles = r
	}
	if g, ok := object["DepartmentID"].(string); ok {
		group = g
	}
	if g, ok := object["DepartmentID"].(uint); ok {
		group = strconv.FormatUint(uint64(g), 10)
	}
	delete(object, "Roles")
	delete(object, "DepartmentID")
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	err = u.FindByUserName(username)
	if err != nil {
		logging.ErrorLogger.Errorf("create user find by username get err ", err)
		return err
	}

	if u.ID > 0 {
		UpdateRoleForUserById(u.ID, roles)
		UpdateDepartment(u.ID, group)
	}

	return nil
}

func (this *User) CreateV2(object interface{}) error {
	return nil
}

func (u *User) Update(id uint, object map[string]interface{}) error {
	err := u.Find(id)
	if err != nil {
		return err
	}
	// if u.Username == "username" {
	// 	return errors.New("不能编辑管理员")
	// }
	if username, ok := object["Username"].(string); ok {
		err := u.FindByUserName(username)
		if err != nil {
			logging.ErrorLogger.Errorf("create user find by username get err ", err)
			return err
		}

		if u.ID > 0 && u.ID != id {
			return errors.New(fmt.Sprintf("username %s is being used", username))
		}
	}
	var department string
	if g, ok := object["DepartmentID"].(string); ok {
		department = g
	}
	if g, ok := object["DepartmentID"].(uint); ok {
		department = strconv.FormatUint(uint64(g), 10)
	}
	delete(object, "DepartmentID")
	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).UpdateColumns(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	if department != "" {
		if err := UpdateDepartment(u.ID, department); err != nil {
			logging.ErrorLogger.Errorf("update group  get err ", err)
			return err
		}
	}
	return nil
}

func CheckAuditor(uid uint) bool {
	userRoleId := easygorm.GetRolesForUser(uid)
	roles, err := drole.FindInId(userRoleId)
	if err != nil {
		logging.ErrorLogger.Errorf("get role get err ", err)
	}
	for _, role := range roles {
		if role.Name == "审计员" {
			return true
		}
	}
	return false
}

func (u *User) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("WhiteList").Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (u *User) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("WhiteList").Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (u *ApprovalResponse) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (u *ApprovalResponse) FindByAuthId(id uint, uid uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (u *User) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

// AddRoleForUser add roles for user
func AddRoleForUser(user *user.User) error {
	if len(user.RoleIds) == 0 {
		return nil
	}

	var err error
	var roleIds []string
	var oldRoleIds []string

	userId := strconv.FormatUint(uint64(user.ID), 10)
	oldRoleIds, err = easygorm.GetEasyGormEnforcer().GetRolesForUser(userId)
	if err != nil {
		logging.ErrorLogger.Errorf("add role to user,del role  err: %+v\n", err)
		return err
	}

	for _, roleId := range user.RoleIds {
		roleId := strconv.FormatUint(uint64(roleId), 10)
		if len(oldRoleIds) > 0 && libs.InArrayS(oldRoleIds, roleId) {
			continue
		}

		roleIds = append(roleIds, roleId)
	}

	if _, err := easygorm.GetEasyGormEnforcer().AddRolesForUser(userId, roleIds); err != nil {
		logging.ErrorLogger.Errorf("add role to user role failed: %+v\n", err)
		return err
	}
	easygorm.GetEasyGormEnforcer().LoadPolicy()
	return nil
}

func (u *User) Profile(id uint) error {
	u.Find(id)
	GetUserRoles(u)
	getUserGroupAndDepartment(u)
	return u.Find(id)
}

func GetUserRoles(u *User) error {
	userRoleIds := easygorm.GetRolesForUser(u.ID)
	roleIds := []string{}
	for _, item := range userRoleIds {
		roleIds = append(roleIds, strings.TrimPrefix(item, "role::"))
	}
	roles, err := drole.FindInId(roleIds)
	if err != nil {
		logging.ErrorLogger.Errorf("get role get err ", err)
		return err
	}
	u.Roles = []string{}
	for _, role := range roles {
		u.Roles = append(u.Roles, role.Name)
	}
	return nil
}

func getUserGroupAndDepartment(user *User) {
	userDepartment, err := duserdepartment.FindByUserId(user.ID)
	if err != nil {
		logging.ErrorLogger.Errorf("get role get err ", err)
	}

	department, err := ddepartment.FindById(userDepartment.DepartmentID)
	user.Department = &department
}

// AddRoleForUser add roles for user
func UpdateRoleForUserById(uid uint, roles []string) error {
	if len(roles) == 0 {
		return nil
	}
	var err error
	var roleIds []string
	var oldRoleIds []string

	userId := strconv.FormatUint(uint64(uid), 10)

	oldRoleIds, err = easygorm.GetEasyGormEnforcer().GetRolesForUser(userId)
	if err != nil {
		logging.ErrorLogger.Errorf("add role to user,del role  err: %+v\n", err)
		return err
	}

	// 需要添加的新的role
	for _, roleId := range roles {
		if len(oldRoleIds) > 0 && libs.InArrayS(oldRoleIds, roleId) {
			continue
		}
		roleIds = append(roleIds, roleId)
	}

	// 删除旧的role
	if len(oldRoleIds) > 0 {
		for _, oldRoleId := range oldRoleIds {
			if libs.InArrayS(roles, oldRoleId) {
				continue
			}
			if _, err = easygorm.GetEasyGormEnforcer().DeleteRoleForUser(userId, oldRoleId); err != nil {
				logging.ErrorLogger.Errorf("add role to user,del role  err: %+v\n", err)
				return err
			}
		}
	}

	if _, err = easygorm.GetEasyGormEnforcer().AddRolesForUser(userId, roleIds); err != nil {
		logging.ErrorLogger.Errorf("add role to user role failed: %+v\n", err)
		return err
	}

	// 刷新权限表
	if err = easygorm.GetEasyGormEnforcer().LoadPolicy(); err != nil {
		logging.ErrorLogger.Errorf("add role to user role failed: %+v\n", err)
		return err
	}
	return nil

}

func UpdateGroup(uid uint, gid string) error {
	object := map[string]interface{}{
		"UserId":  strconv.FormatUint(uint64(uid), 10),
		"GroupId": gid,
	}
	userGroup := dusergroup.Response{}
	if err := dusergroup.DeleteByUserID(uid); err != nil {
		logging.ErrorLogger.Errorf("delete user group relation err ", err)
		return err

	}
	if err := userGroup.Create(object); err != nil {
		logging.ErrorLogger.Errorf("create user group relation err ", err)
		return err
	}
	return nil
}

func UpdateDepartment(uid uint, gid string) error {
	object := map[string]interface{}{
		"UserID":       strconv.FormatUint(uint64(uid), 10),
		"DepartmentID": gid,
	}
	userDepartment := duserdepartment.UserDepartment{}
	if err := duserdepartment.DeleteByUserID(uid); err != nil {
		logging.ErrorLogger.Errorf("delete user group relation err ", err)
		return err

	}

	if err := userDepartment.Create(object); err != nil {
		logging.ErrorLogger.Errorf("create user group relation err ", err)
		return err
	}
	return nil
}

func FindInId(ids []string) ([]*ListResponse, error) {
	var users []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id in ?", ids).Find(&users).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return nil, err
	}
	getRoles(users)
	getUsersGroupAndDepartment(users)
	return users, nil
}

func FindSimpleInId(ids []string) ([]*ApprovalResponse, error) {
	var users []*ApprovalResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id in ?", ids).Find(&users).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return nil, err
	}
	return users, nil
}

func FindInIds(ids []uint) ([]*User, error) {
	var users []*User
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("WhiteList").Where("id in ?", ids).Find(&users).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return nil, err
	}
	return users, nil
}

func FindSimpleInIds(ids []uint) ([]*ApprovalResponse, error) {
	var users []*ApprovalResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id in ?", ids).Find(&users).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return nil, err
	}
	return users, nil
}

func FindAllByRoleId(roleId uint) ([]*ListResponse, error) {
	userIds := easygorm.GetUsersForRoleV2(roleId)
	users, err := FindInId(userIds)
	if err != nil {
		return nil, err
	}
	getRoles(users)
	getUsersGroupAndDepartment(users)
	return users, nil
}

func FindInUintIds(ids []uint) ([]*ListResponse, error) {
	var users []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("WhiteList").Where("id in ?", ids).Find(&users).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return nil, err
	}
	getRoles(users)
	getUsersGroupAndDepartment(users)
	return users, nil
}

func FindAuditors(userId uint) ([]*ListResponse, error) {
	var defautAuditors, auditors, managers []*ListResponse
	users := UserMap
	for _, a := range users {
		if libs.InArrayS(a.Roles, "默认审核员") {
			if a.ID != userId {
				defautAuditors = append(defautAuditors, a)
			}
		}
	}

	for _, user := range users {
		if libs.InArrayS(user.Roles, "组长") {
			managers = append(managers, user)
			if user.ID == userId {
				sort.Slice(defautAuditors, func(i, j int) bool {
					return defautAuditors[i].ID > defautAuditors[j].ID
				})
				return defautAuditors, nil
			}
		}
	}

	userDepartment, err := duserdepartment.FindByUserId(userId)
	if err != nil {
		logging.ErrorLogger.Errorf("find user department get  err ", err)
		return auditors, err
	}
	department, err := ddepartment.FindById(userDepartment.DepartmentID)
	linkArray := strings.Split(department.Link, "/")

	// 寻找当前部门是否存在组长角色
	for _, m := range managers {
		if m.DepartmentID == userDepartment.DepartmentID {
			auditors = append(auditors, m)
		}
	}

	if len(auditors) == 0 && len(linkArray) > 1 {
		// 寻找上一级部门是否存在组长角色

		err := department.FindEx("link", strings.Join(linkArray[0:len(linkArray)-1], "/"))
		if err != nil {
			logging.ErrorLogger.Errorf("find user up level department get  err ", err)
		} else {

			for _, m := range managers {
				if m.DepartmentID == department.ID {
					auditors = append(auditors, m)
				}
			}
		}
	}

	if len(auditors) == 0 {
		auditors = defautAuditors
	}
	sort.Slice(auditors, func(i, j int) bool {
		return auditors[i].ID > auditors[j].ID
	})
	return auditors, nil
}

func FindReviewers(userId uint) ([]*ListResponse, error) {
	var reviewers, managers []*ListResponse
	users := UserMap

	for _, user := range users {
		if libs.InArrayS(user.Roles, "组长") && user.ID != userId {
			managers = append(managers, user)
		}
	}

	userDepartment, err := duserdepartment.FindByUserId(userId)
	if err != nil {
		logging.ErrorLogger.Errorf("find user department get  err ", err)
		return reviewers, err
	}
	department, err := ddepartment.FindById(userDepartment.DepartmentID)
	linkArray := strings.Split(department.Link, "/")

	// 寻找当前部门是否存在组长角色
	for _, m := range managers {
		if m.DepartmentID == userDepartment.DepartmentID {
			reviewers = append(reviewers, m)
		}
	}

	if len(reviewers) == 0 {
		// 寻找上一级部门是否存在组长角色
		for i := 1; i < 3; i++ {
			err := department.FindEx("link", strings.Join(linkArray[:len(linkArray)-i], "/"))
			if err != nil {
				logging.ErrorLogger.Errorf("find user up level department get  err ", err)
				continue
			} else {
				for _, m := range managers {
					if m.DepartmentID == department.ID {
						reviewers = append(reviewers, m)
					}
				}
			}
		}
	}

	return reviewers, nil
}

var UserMap map[uint]*ListResponse
var UserChange int64
var UserChan chan int

func UpdateUserMapCache() error {
	logging.DebugLogger.Debug("update user map cache")
	UserMap = map[uint]*ListResponse{}
	Users := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(&user.User{}).Find(&Users).Error
	if err != nil {
		return err
	}
	getRoles(Users)
	getUsersGroupAndDepartment(Users)
	for _, User := range Users {
		UserMap[User.ID] = User
	}

	return nil
}

func FindAllAuditors(userId uint) ([]*ListResponse, error) {
	his := []uint{}
	var defautAuditors []*ListResponse
	users := UserMap
	for _, a := range users {
		if libs.InArrayS(a.Roles, "默认审核员") || libs.InArrayS(a.Roles, "组长") {
			if a.ID != userId && !libs.InArrayUint(his, a.ID) {
				his = append(his, a.ID)
				defautAuditors = append(defautAuditors, a)
			}
		}
	}

	return defautAuditors, nil
}

func FindInNames(names []string) ([]*ListResponse, error) {
	var users []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("username in ? or name in ?", names, names).Find(&users).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return nil, err
	}
	getUsersGroupAndDepartment(users)
	return users, nil
}

func All() ([]*ListResponse, error) {
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("WhiteList")
	err := db.Find(&items).Error
	getUsersGroupAndDepartment(items)
	return items, err
}
