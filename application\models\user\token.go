package user

import "irisAdminApi/application/models"

type UserToken struct {
	models.ModelBase
	UserID uint   `gorm:"uniqueIndex; not null; type:varchar(60)" json:"user_id"`
	Token  string `gorm:"not null; type:varchar(60)" json:"token"`
}

type UserOpenID struct {
	models.ModelBase
	UserID uint   `gorm:"uniqueIndex; not null; type:varchar(60)" json:"user_id"`
	Openid string `gorm:"not null; type:varchar(100)" json:"openid"`
}
