package buildfarm

import (
	"irisAdminApi/application/models"

	"gorm.io/gorm"
)

type MakeJob struct {
	models.ModelBase
	JobID             string `gorm:"not null; type:varchar(60)" json:"job_id"`
	ServerID          uint   `gorm:"index;not null" json:"server_id"`
	TaskID            string `gorm:"not null; type:varchar(60)" json:"task_id"`
	TaskType          uint   `gorm:"not null" json:"task_type"` //编译类型，1：产品编译  2：组件编译  3: 产品编译+coverity
	Product           string `gorm:"not null; type:varchar(60)" json:"product"`
	Defconfig         string `gorm:"not null; type:varchar(60)" json:"defconfig"`
	Target            string `gorm:"not null; type:varchar(60)" json:"target"`
	Baseline          string `gorm:"not null; type:varchar(200)" json:"baseline"`
	Status            uint   `gorm:"not null" json:"status"`                    // 作业状态 3：排队，0：运行， 1：成功， 2: 失败， 4: 待审批   5: 拒绝
	Version           string `gorm:"not null; type:varchar(60)" json:"version"` // 版本hash值,初始为0
	SoftwareNumber    string `gorm:"type:varchar(60)" json:"software_nubmer"`
	SoftwareVersion   string `gorm:"type:varchar(60)" json:"software_version"`
	FeedsConfigCustom string `json:"feeds_config_custom"`
	PatchEnable       bool   `gorm:"not null;default:false" json:"patch_enable"`
	BuildType         string `gorm:"not null; type:varchar(60)" json:"build_type"` // 编译类型： debug, performance
	BuildOspkg        bool   `gorm:"not null;default:false" json:"build_ospkg"`
	DirClean          bool   `gorm:"not null;default:false" json:"dir_clean"`
	UserID            uint   `gorm:"not null" json:"user_id"`
	SmokeStatus       string `gorm:"type:varchar(500)" json:"smoke_status"`
	KernelRepo        string `gorm:"not null; type:varchar(200)" json:"kernel_repo"`
	KernelBranch      string `gorm:"not null; type:varchar(60)" json:"kernel_branch"`
	Comment           string `gorm:"type:varchar(100)" json:"comment"`
}

func (this *MakeJob) BeforeCreate(tx *gorm.DB) (err error) {
	if this.TaskType != 3 {
		return nil
	}
	return tx.Model(this).Delete("user_id = ? and product = ? and task_type = 3 and status = 3", this.UserID, this.Product).Error
}
