package featurerelease

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/buildfarm/dsoftversions"
	"irisAdminApi/service/dao/featurerelease/dfeatureseccloud"
	"net/http"

	"github.com/kataras/iris/v12"
)

func GetProductModelsFromSecCloud(ctx iris.Context) {

	// if libs.Config.FeatureRelease.Url == "" {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置云平台地址，请联系管理员"))
	// 	return
	// }
	url := ctx.FormValue("url")
	if url == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "请先选择要发布的安全云"))
		return
	}
	url = fmt.Sprintf("%s/api/featureManager/queryAllSoftVersion", url)
	req, err := http.NewRequest("POST", url, nil)
	req.Header.Add("iamfeign", "1")

	if err != nil {
		logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	result, err := libs.HandlerRequest(req)
	if err != nil {
		logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if result["success"].(bool) {
		ctx.JSON(response.NewResponse(response.NoErr.Code, result["result"], response.NoErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("未知错误: %s", result)))
	return
}

func GetSoftVersionsFromSecCloudV2(ctx iris.Context) {

	// if libs.Config.FeatureRelease.Url == "" {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置云平台地址，请联系管理员"))
	// 	return
	// }
	url := ctx.FormValue("url")
	if url == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "请先选择要发布的安全云"))
		return
	}

	softVersions, err := SecCloudClient.GetSoftVersions(url)
	if err != nil {
		logging.ErrorLogger.Errorf("get softversion err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, softVersions.Data, response.NoErr.Msg))
	return
}

func GetProductModelsFromSecCloudV2(ctx iris.Context) {

	// if libs.Config.FeatureRelease.Url == "" {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置云平台地址，请联系管理员"))
	// 	return
	// }
	url := ctx.FormValue("url")
	if url == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "请先选择要发布的安全云"))
		return
	}

	productModels, err := SecCloudClient.GetProductModels(url)
	if err != nil {
		logging.ErrorLogger.Errorf("get product model err ", url, err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, productModels.Data, response.NoErr.Msg))
	return
}

func RefreshSoftVersionsFromSecCloud() {
	secClouds, err := dfeatureseccloud.All()
	if err != nil {
		logging.ErrorLogger.Errorf("get secclouds err", err)
	}
	softVersions := []map[string]interface{}{}
	his := []string{}
	for _, secCloud := range secClouds {
		softVersionsResponse, err := SecCloudClient.GetSoftVersions(secCloud.Url)
		if err != nil {
			logging.ErrorLogger.Errorf("get seccloud softversion err", err, secCloud.Url)
			continue
		}
		for _, item := range softVersionsResponse.Data {
			if !libs.InArrayS(his, item+secCloud.Type) {
				softVersions = append(softVersions, map[string]interface{}{
					"SoftVersion": item,
					"Type":        secCloud.Type,
				})
				his = append(his, item+secCloud.Type)
			}
		}
	}

	err = dsoftversions.BatchCreate(softVersions)
	if err != nil {
		logging.ErrorLogger.Errorf("batch create  soft versions err", err)
	}
}

func RefreshProductModelsFromSecCloud() {
	secClouds, err := dfeatureseccloud.All()
	if err != nil {
		logging.ErrorLogger.Errorf("get secclouds err", err)
	}
	softVersions := []map[string]interface{}{}
	his := []string{}
	for _, secCloud := range secClouds {
		softVersionsResponse, err := SecCloudClient.GetSoftVersions(secCloud.Url)
		if err != nil {
			logging.ErrorLogger.Errorf("get seccloud softversion err", err, secCloud.Url)
			continue
		}
		for _, item := range softVersionsResponse.Data {
			if !libs.InArrayS(his, item+secCloud.Type) {
				softVersions = append(softVersions, map[string]interface{}{
					"SoftVersion": item,
					"Type":        secCloud.Type,
				})
				his = append(his, item+secCloud.Type)
			}
		}
	}

	err = dsoftversions.BatchCreate(softVersions)
	if err != nil {
		logging.ErrorLogger.Errorf("batch create  soft versions err", err)
	}
}
