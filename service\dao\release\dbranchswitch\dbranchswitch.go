package dbranchswitch

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/release"
	"irisAdminApi/service/dao/release/dproject"
	"irisAdminApi/service/dao/user/duser"
)

const ModelName = "分支切换记录表"

type Response struct {
	ID               uint                    `json:"id"`
	ReleaseProjectID uint                    `json:"release_project_id"`
	UserID           uint                    `json:"user_id"`
	AuditorID        uint                    `json:"auditor_id"`
	Content          string                  `json:"content"`
	Comment          string                  `json:"comment"`
	Status           uint                    `json:"status"`
	Auditor          *duser.ApprovalResponse `gorm:"-" json:"auditor,omitempty"`
	User             *duser.ApprovalResponse `gorm:"-" json:"user,omitempty"`
	ReleaseProject   *dproject.Response      `gorm:"-" json:"project"`
	UpdatedAt        string                  `json:"updated_at"`
	CreatedAt        string                  `json:"created_at"`
}

type ListResponse struct {
	Response
}

type Request struct {
	ReleaseProjectID uint   `json:"release_project_id"`
	UserID           uint   `json:"user_id"`
	AuditorID        uint   `json:"auditor_id"`
	Content          string `json:"content"`
	Comment          string `json:"comment"`
	Status           uint   `json:"status"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *release.ReleaseBranchSwitch {
	return &release.ReleaseBranchSwitch{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	getUsers(items)
	getProjects(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).UpdateColumns(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func getUsers(items []*ListResponse) {
	for _, item := range items {
		item.User = &duser.ApprovalResponse{
			Id:   duser.UserMap[item.UserID].ID,
			Name: duser.UserMap[item.UserID].Name,
		}
		item.Auditor = &duser.ApprovalResponse{
			Id:   duser.UserMap[item.AuditorID].ID,
			Name: duser.UserMap[item.AuditorID].Name,
		}
	}
}

func getProjects(items []*ListResponse) {
	projectIds := []uint{}
	for _, item := range items {
		projectIds = append(projectIds, item.ReleaseProjectID)
	}

	projects, _ := dproject.FindInIds(projectIds)
	projectMap := make(map[uint]*dproject.Response)
	for _, project := range projects {
		projectMap[project.ID] = project
	}
	for _, item := range items {
		item.ReleaseProject = projectMap[item.ReleaseProjectID]
	}
}
