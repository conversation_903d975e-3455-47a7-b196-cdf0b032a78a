package datasync

type PmsBuMember struct {
	/*
	   "projectId": 1961,                    //rgos项目ID
	   "path": "路径",                        //组件路径
	   "pathType": "代码"                    //组件类型
	*/
	ID                  int    `gorm:"index;not null" json:"ID"`
	UserCn              string `gorm:"not null; type:varchar(60)" json:"user_cn" update:"1"`
	UserEn              string `gorm:"not null; type:varchar(60)" json:"user_en" update:"1"`
	Email               string `gorm:"not null; type:varchar(200)" json:"email" update:"1"`
	EmployeeID          string `gorm:"not null; type:varchar(60)" json:"employee_id" update:"1"`
	UserStatus          int    `json:"user_status" update:"1"` //不为2的才是有效的数据
	OneDepartmentName   string `gorm:"not null; type:varchar(200)" json:"one_department_name" update:"1"`
	TwoDepartmentName   string `gorm:"not null; type:varchar(200)" json:"two_department_name" update:"1"`
	ThreeDepartmentName string `gorm:"not null; type:varchar(200)" json:"three_department_name" update:"1"`
	FourDepartmentName  string `gorm:"not null; type:varchar(200)" json:"four_department_name" update:"1"`
	FiveDepartmentName  string `gorm:"not null; type:varchar(200)" json:"five_department_name" update:"1"`
}
