package dappmergerequestbugworkgroupsummary

import (
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/models/appmergerequest"
	"reflect"
	"sort"
	"strconv"
)

const ModelName = "BUG统计"

/*
SELECT
bug_owner_group,
COUNT(*) AS total,
Round(COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP"), bug_id, NULL))/COUNT(*) *100, 2) AS not_cbd_percent,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVE<PERSON>"), bug_id, NULL)) AS not_cbd,
COUNT(IF(bug_workpacket_name ='', bug_id, NULL)) AS unrelate_work_packet,
COUNT(IF(bug_state IN ('CHECKED', 'RESOLVED'), bug_id, NULL)) AS CHECKED_RESOLVED,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_submitter_group LIKE '%测试%' AND bug_priority = 'blocking' AND bug_repro=1 AND bug_created_at < getWorkDay(bug_created_at, -3), bug_id, NULL)) AS  over_time_blocking_by_test,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_submitter_group LIKE '%测试%' AND bug_priority = 'critical' AND bug_repro=1 AND bug_created_at < getWorkDay(bug_created_at, -3), bug_id, NULL)) AS  over_time_critical_by_test,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_submitter_group LIKE '%测试%' AND bug_priority = 'major' AND bug_repro=1 AND bug_created_at <getWorkDay(bug_created_at, -5), bug_id, NULL)) AS  over_time_major_by_test,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_submitter_group LIKE '%测试%' AND bug_priority = 'normal' AND bug_repro=1 AND bug_created_at <  DATE_ADD(NOW(), INTERVAL -7 DAY), bug_id, NULL)) AS  over_time_normal_by_test,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_submitter_group NOT LIKE '%测试%' AND bug_repro=1 AND bug_created_at < DATE_ADD(NOW(), INTERVAL -7 DAY), bug_id, NULL)) AS  over_time_by_dev,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_priority = 'blocking', bug_id, NULL)) AS  blocking_not_cbd,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_priority = 'critical', bug_id, NULL)) AS  critical_not_cbd,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_priority = 'major', bug_id, NULL)) AS  major_not_cbd,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_priority = 'normal', bug_id, NULL)) AS  normal_not_cbd,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_priority = 'trivial', bug_id, NULL)) AS  trivial_not_cbd,
COUNT(IF(bug_submitter_group LIKE '%测试%', bug_id, NULL)) AS  total_by_test,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_submitter_group LIKE '%测试%', bug_id, NULL)) AS  not_cbd_by_test,
ROUND(COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_submitter_group LIKE '%测试%', bug_id, NULL))/COUNT(IF(bug_submitter_group LIKE '%测试%', bug_id, NULL))*100,2) AS not_cbd_by_test_percent,
COUNT(IF(bug_submitter_group not LIKE '%测试%', bug_id, NULL)) AS  total_by_dev,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_submitter_group not LIKE '%测试%', bug_id, NULL)) AS  not_cbd_by_dev,
Round(COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_submitter_group not LIKE '%测试%', bug_id, NULL))/COUNT(IF(bug_submitter_group not LIKE '%测试%', bug_id, NULL))*100, 2) AS not_cbd_by_dev_percent
FROM bugs
WHERE bug_os = 'NTOS1.0R6'
GROUP BY bug_owner_group
*/

type BugGroupSummary struct {
	BugOs         string `json:"bug_os" xlsx:"项目" idx:"1"`
	BugOwnerGroup string `json:"bug_owner_group" xlsx:"所属工作组" idx:"2"`
	// BugWorkPacketName  string  `json:"bug_workpacket_name" xlsx:"工作包" idx:"3"`
	Total                  uint    `json:"total" xlsx:"合计提交" idx:"3"`
	NotCbdPercent          float32 `json:"not_cbd_percent" xlsx:"所有BUG未决率" idx:"2"`
	NotCbdCount            uint    `json:"not_cbd_count" xlsx:"未CBD" idx:"3"`
	NoWorkpacket           uint    `json:"no_workpacket" xlsx:"未关联工作包" idx:"4"`
	CheckedResolved        uint    `json:"checked_resolved" xlsx:"CHECKED+RESOLVED" idx:"5"`
	OvertimeBlockingByTest uint    `json:"overtime_blocking_by_test" xlsx:"测试提Blocking超期未决" idx:"6"`
	OvertimeCriticalByTest uint    `json:"overtime_critical_by_test" xlsx:"测试提Critical超期未决" idx:"7"`
	OvertimeMajorByTest    uint    `json:"overtime_major_by_test" xlsx:"测试提Major超期未决" idx:"8"`
	OvertimeNormalByTest   uint    `json:"overtime_normal_by_test" xlsx:"测试提Normal超期未决" idx:"9"`
	OvertimeByDev          uint    `json:"overtime_by_dev" xlsx:"开发提超期未决" idx:"10"`
	BlockingNotCbd         uint    `json:"blocking_not_cbd" xlsx:"Blocking未决" idx:"11"`
	CriticalNotCbd         uint    `json:"critical_not_cbd" xlsx:"Ctritical未决" idx:"12"`
	MajorNotCbd            uint    `json:"major_not_cbd" xlsx:"Major未决" idx:"13"`
	NormalNotCbd           uint    `json:"normal_not_cbd" xlsx:"Normal未决" idx:"14"`
	TrivialNotCbd          uint    `json:"trivial_not_cbd" xlsx:"Trivial未决" idx:"15"`
	TotalByTest            uint    `json:"total_by_test" xlsx:"测试提交bug数" idx:"16"`
	NotCbdCountByTest      uint    `json:"not_cbd_count_by_test" xlsx:"测试提交未CBD" idx:"17"`
	NotCbdPercentByTest    float32 `json:"not_cbd_percent_by_test" xlsx:"测试提交未决率" idx:"18"`
	TotalByDev             uint    `json:"total_by_dev" xlsx:"开发提交bug数" idx:"19"`
	NotCbdCountByDev       uint    `json:"not_cbd_count_by_dev" xlsx:"开发提交未CBD" idx:"20"`
	NotCbdPercentByDev     float32 `json:"not_cbd_percent_by_dev" xlsx:"开发提交未决率" idx:"21"`
}

func (a *BugGroupSummary) ModelName() string {
	return ModelName
}

func Model() *appmergerequest.AppMergeRequest {
	return &appmergerequest.AppMergeRequest{}
}

func (s *BugGroupSummary) All(bugOS, bugOwnerGroup string, pstlID uint, order, by string) ([]*BugGroupSummary, error) {
	items := []*BugGroupSummary{}
	filter_bug_owner_groups := []string{
		"研发一部控制组",
		"研发一部管理组",
		"研发一部平台组",
		"研发二部安全组",
		"研发二部转发组",
		"研发三部安全组",
		"研发三部业务组",
		"研发三部转发组",
		"研发三部平台组",
		"研发五部WEB组",
		"研发五部应软开发1组",
		"研发五部应软开发2组",
		"安全架构部架构组",
		"战略方案组",
	}
	selects := []string{
		"b.bug_os",
		"b.bug_owner_group",
		"COUNT(*) AS total",
		`Round(COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP"), b.bug_id, NULL))/COUNT(*) *100, 2) AS not_cbd_percent`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP"), b.bug_id, NULL)) AS not_cbd_count`,
		`COUNT(IF(b.bug_workpacket_name ='', b.bug_id, NULL)) AS no_workpacket`,
		`COUNT(IF(b.bug_state IN ('CHECKED', 'RESOLVED'), b.bug_id, NULL)) AS checked_resolved`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' and b.main_bug_id = 0 AND b.bug_severity = 'Blocking' AND b.bug_repro=1 AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < DATE_ADD(NOW(), INTERVAL -1*(a.round+1) DAY)) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < DATE_ADD(NOW(), INTERVAL -1 DAY))), b.bug_id, NULL)) AS  overtime_blocking_by_test`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' and b.main_bug_id = 0 AND b.bug_priority = 'critical' AND b.bug_repro=1  AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -3*(a.round+1))) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -3))), b.bug_id, NULL)) AS  overtime_critical_by_test`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' and b.main_bug_id = 0 AND b.bug_priority = 'major' AND b.bug_repro=1  AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -5*(a.round+1))) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -5))), b.bug_id, NULL)) AS  overtime_major_by_test`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' and b.main_bug_id = 0 AND (b.bug_severity = 'Normal' or b.bug_priority = 'normal') AND b.bug_repro=1  AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) <  getWorkDay(now(), -7*(a.round+1))) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) <  getWorkDay(now(), -7))), b.bug_id, NULL)) AS  overtime_normal_by_test`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and (b.bug_submitter_group NOT LIKE '%测试%' or b.main_bug_id != 0) AND b.bug_repro=1 AND ((a.approval_type='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -7*(a.round+1))) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -7))), b.bug_id, NULL)) AS  overtime_by_dev`,

		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_severity = 'Blocking', b.bug_id, NULL)) AS  blocking_not_cbd`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_priority = 'critical', b.bug_id, NULL)) AS  critical_not_cbd`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_priority = 'major', b.bug_id, NULL)) AS  major_not_cbd`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_priority = 'normal', b.bug_id, NULL)) AS  normal_not_cbd`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_priority = 'trivial', b.bug_id, NULL)) AS  trivial_not_cbd`,
		`COUNT(IF(b.bug_submitter_group LIKE '%测试%', b.bug_id, NULL)) AS  total_by_test`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%', b.bug_id, NULL)) AS  not_cbd_count_by_test`,
		`ROUND(COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%', b.bug_id, NULL))/COUNT(IF(b.bug_submitter_group LIKE '%测试%', b.bug_id, NULL))*100,2) AS not_cbd_percent_by_test`,
		`COUNT(IF(b.bug_submitter_group not LIKE '%测试%', b.bug_id, NULL)) AS  total_by_dev`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group not LIKE '%测试%', b.bug_id, NULL)) AS  not_cbd_count_by_dev`,
		`Round(COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group not LIKE '%测试%', b.bug_id, NULL))/COUNT(IF(b.bug_submitter_group not LIKE '%测试%', b.bug_id, NULL))*100, 2) AS not_cbd_percent_by_dev`,
	}

	db := easygorm.GetEasyGormDb().
		Table("bugs as b").
		Joins("left join (select max(`round`) as `round`, max(created_at) as created_at, bug_id, `status`, approval_type from bug_approvals GROUP BY bug_id, `status`, approval_type) a on b.bug_id = a.bug_id and a.`status` = 1").
		Joins("left join (select r.id as id, r.name as name, c.test_start_at as test_start_at from release_projects r left join release_project_configs c on r.id = c.release_project_id) c on c.name = b.bug_os").
		Select(selects).Group("b.bug_os, b.bug_owner_group").
		Order(fmt.Sprintf("%s %s", by, order)).
		Having("not_cbd_count > 0")
	where := easygorm.GetEasyGormDb().Where("b.bug_os like '安全云管理平台%'").Where("b.bug_owner_group in ?", filter_bug_owner_groups)
	if len(bugOS) > 0 {
		where = where.Where("b.bug_os = ?", bugOS)
	}
	if len(bugOwnerGroup) > 0 {
		where = where.Where("b.bug_owner_group = ?", bugOwnerGroup)
	}

	err := db.Where(where).Find(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func (s *BugGroupSummary) ExportBugSummary(items []*BugGroupSummary) ([]interface{}, [][]interface{}) {
	xt := reflect.TypeOf(s)
	// xv := reflect.ValueOf(s)

	rows := [][]interface{}{}
	headers := []interface{}{}
	for i := 0; i < xt.Elem().NumField(); i++ {
		head, ok := xt.Elem().Field(i).Tag.Lookup("xlsx")
		if ok {
			headers = append(headers, head)
		}
	}
	rows = append(rows, headers)

	for _, e := range items {
		cells := []interface{}{}
		xv := reflect.ValueOf(e)
		for i := 0; i < xv.Elem().NumField(); i++ {
			_, ok := xt.Elem().Field(i).Tag.Lookup("xlsx")
			if ok {
				cells = append(cells, xv.Elem().Field(i).Interface())
			}

		}
		rows = append(rows, cells)
	}
	return headers, rows
}

type Column struct {
	Idx   int    `json:"idx"`
	Key   string `json:"key"`
	Label string `json:"label"`
}

func (s *BugGroupSummary) GetColumns() ([]*Column, error) {
	xt := reflect.TypeOf(s)

	columns := []*Column{}

	for i := 0; i < xt.Elem().NumField(); i++ {
		key, ok1 := xt.Elem().Field(i).Tag.Lookup("json")
		label, ok2 := xt.Elem().Field(i).Tag.Lookup("xlsx")
		_idx, ok3 := xt.Elem().Field(i).Tag.Lookup("idx")

		if ok1 && ok2 && ok3 {
			idx, err := strconv.Atoi(_idx)
			if err != nil {
				return columns, err
			}
			columns = append(columns, &Column{
				Idx:   idx,
				Key:   key,
				Label: label,
			})
		}
	}
	sort.SliceStable(columns, func(i, j int) bool {
		if columns[i].Idx < columns[j].Idx {
			return true
		}
		return false
	})
	return columns, nil
}
