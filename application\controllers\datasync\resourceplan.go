package datasync

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	"irisAdminApi/application/controllers/openfeishu"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/datasync/dcsburesresourceapply"
	"irisAdminApi/service/dao/datasync/dcsburesresourcesplit"
)

/*
CREATE TABLE `csbu_res_resource_apply` (
	`id` BIGINT(20) NULL DEFAULT NULL,
	`applyUserId` BIGINT(20) NULL DEFAULT NULL,
	`applyDate` DATETIME NULL DEFAULT NULL,
	`applyReason` MEDIUMTEXT NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`status` VARCHAR(2) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`verifyUserId` BIGINT(20) NULL DEFAULT NULL,
	`verifyDate` DATETIME NULL DEFAULT NULL,
	`verifyRemark` MEDIUMTEXT NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`targetDeptId` BIGINT(20) NULL DEFAULT NULL,
	`targetUserId` BIGINT(20) NULL DEFAULT NULL,
	`targetProjectId` BIGINT(20) NULL DEFAULT NULL,
	`workContent` MEDIUMTEXT NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`planPercent` DOUBLE NULL DEFAULT NULL,
	`planMonth` VARCHAR(2) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`planYear` VARCHAR(10) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`planManMonth` DOUBLE NULL DEFAULT NULL,
	`beginTime` DATETIME NULL DEFAULT NULL,
	`endTime` DATETIME NULL DEFAULT NULL,
	`planHour` DOUBLE NULL DEFAULT NULL,
	`enable` INT(11) NULL DEFAULT NULL,
	`createDate` DATETIME NULL DEFAULT NULL,
	`modifyDate` DATETIME NULL DEFAULT NULL,
	`source` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`applyUserName` VARCHAR(100) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`applyCasUserId` VARCHAR(100) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`pmsProjectId` INT(11) NULL DEFAULT NULL,
	`pmsProjectName` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`targetUserName` VARCHAR(100) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`targetCasUserId` VARCHAR(100) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`targetUserBusinessDept` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`targetUserDepartmentName` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`targetUserGroupName` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`targetDeptName` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`targetDeptCode` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`importKey` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`importUserId` BIGINT(20) NULL DEFAULT NULL,
	`changeReason` TEXT NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`oneDeptCode` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`twoDeptCode` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`threeDeptCode` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`fourDeptCode` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`fiveDeptCode` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`oneDeptName` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`twoDeptName` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`threeDeptName` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`fourDeptName` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`fiveDeptName` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`finalDeptCode` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	INDEX `idx_csbu_res_resource_apply_lookup` (`id`) USING BTREE
)
COLLATE='utf8_general_ci'
ENGINE=InnoDB
;

*/

/*
SELECT
if (a.applyReason IS NULL, '', a.applyReason),
if (s.planManMonth IS NULL, 0, s.planManMonth),
if (s.hour IS NULL, 0, s.hour),
s.beginTime,
s.endTime,
a.applyUserName,
if (a.pmsProjectName IS NULL, '', a.pmsProjectName),
if(a.workContent IS NULL, '', a.workContent),
s.userName,
s.businessDept,
s.departmentName,
s.groupName,
s.year
FROM csbu_res_resource_split s
LEFT JOIN csbu_res_resource_apply a ON a.id = s.applyId
WHERE YEAR = 2025
*/

type ResourceSplit struct {
	ApplyReason    string    `gorm:"column:applyReason" json:"applyReason"`
	PlanManMonth   float64   `gorm:"column:planManMonth" json:"planManMonth"`
	Hour           float64   `gorm:"column:hour" json:"hour"`
	BeginTime      time.Time `gorm:"column:beginTime" json:"beginTime"`
	EndTime        time.Time `gorm:"column:endTime" json:"endTime"`
	ApplyUserName  string    `gorm:"column:applyUserName" json:"applyUserName"`
	PmsProjectName string    `gorm:"column:pmsProjectName" json:"pmsProjectName"`
	WorkContent    string    `gorm:"column:workContent" json:"workContent"`
	UserName       string    `gorm:"column:userName" json:"userName"`
	BusinessDept   string    `gorm:"column:businessDept" json:"businessDept"`
	DepartmentName string    `gorm:"column:departmentName" json:"departmentName"`
	GroupName      string    `gorm:"column:groupName" json:"groupName"`
	Year           int       `gorm:"column:year" json:"year"`
}

type ResourceSplit2 struct {
	ApplyID             int       `gorm:"column:applyId" json:"applyId"`
	Status              string    `gorm:"column:status" json:"status"`
	PlanManMonth        float64   `gorm:"column:planManMonth" json:"planManMonth"`
	ApplyUserName       string    `gorm:"column:applyUserName" json:"applyUserName"`
	CreateDate          time.Time `gorm:"column:createDate" json:"createDate"`
	PmsProjectName      string    `gorm:"column:pmsProjectName" json:"pmsProjectName"`
	TargetUserGroupName string    `gorm:"column:targetUserGroupName" json:"targetUserGroupName"`
	TargetUserName      string    `gorm:"column:targetUserName" json:"targetUserName"`

	DepartmentName string  `gorm:"column:departmentName" json:"departmentName"`
	GroupName      string  `gorm:"column:groupName" json:"groupName"`
	UserName       string  `gorm:"column:userName" json:"userName"`
	Source         string  `gorm:"column:source" json:"source"`
	Hour           float64 `gorm:"column:hour" json:"hour"`

	BeginTime time.Time `gorm:"column:beginTime" json:"beginTime"`
	EndTime   time.Time `gorm:"column:endTime" json:"endTime"`

	WorkContent  string `gorm:"column:workContent" json:"workContent"`
	ApplyReason  string `gorm:"column:applyReason" json:"applyReason"`
	VerifyRemark string `gorm:"column:verifyRemark" json:"verifyRemark"`
}

func AllResourceSplit() ([]*ResourceSplit, error) {
	items := []*ResourceSplit{}
	err := DataSyncDB.db.Table(fmt.Sprintf(`(SELECT 
		a.applyReason,
		s.planManMonth,
		s.planManMonth * 168 as hour,
		s.beginTime,
		s.endTime,
		a.applyUserName,
		a.pmsProjectName,
		a.workContent,
		s.userName,
		s.businessDept,
		s.departmentName,
		s.groupName
		FROM csbu_res_resource_split s 
		LEFT JOIN csbu_res_resource_apply a ON a.id = s.applyId
		WHERE YEAR = %d) as resource_split`, time.Now().Year())).Find(&items).Error

	return items, err
}

func SyncAllResourceSplitToFeishu() {
	appToken := "Lv14bYFRya1MlDsb5i3csaehnBg"
	tableID := "tblJQwy3xg1NQnp1"

	items, err := AllResourceSplit()
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
	}

	items2, err := dcsburesresourcesplit.AllSplitJoinApply()
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
	}

	// 批量删除线上数据
	openfeishu.DeleteTableRecordDataV4(appToken, tableID, []string{"申请理由"})
	// 获取本地数据

	// 批量同步到线上
	now := time.Now()
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, item := range batchData {
				rec := map[string]interface{}{
					"申请理由":   item.ApplyReason,
					"计划人月":   item.PlanManMonth,
					"计划工时":   item.Hour,
					"开始时间":   item.BeginTime.UnixMilli(),
					"结束时间":   item.EndTime.UnixMilli(),
					"申请人":    item.ApplyUserName,
					"申请项目":   item.PmsProjectName,
					"工作内容":   item.WorkContent,
					"目标人员":   item.UserName,
					"事业部":    item.BusinessDept,
					"部门":     item.DepartmentName,
					"专业组":    item.GroupName,
					"数据过期时间": now.Add(12 * time.Hour).UnixMilli(),
				}

				records = append(records, rec)
			}

			tableRecordResp, err := openfeishu.BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("批量创建记录失败: %s", err.Error(), string(tableRecordResp.RawBody))
				continue
			}
		}
	}

	for i := 0; i < len(items2); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items2) {
			endIndex = len(items2)
		}
		batchData := items2[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, item := range batchData {
				rec := map[string]interface{}{
					"申请理由":   item.ApplyReason,
					"计划人月":   item.PlanManMonth,
					"计划工时":   item.Hour,
					"开始时间":   item.BeginTime.UnixMilli(),
					"结束时间":   item.EndTime.UnixMilli(),
					"申请人":    item.ApplyUserName,
					"申请项目":   item.PmsProjectName,
					"工作内容":   item.WorkContent,
					"目标人员":   item.UserName,
					"事业部":    item.BusinessDept,
					"部门":     item.DepartmentName,
					"专业组":    item.GroupName,
					"数据过期时间": now.Add(12 * time.Hour).UnixMilli(),
				}
				records = append(records, rec)
			}

			tableRecordResp, err := openfeishu.BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("批量创建记录失败: %s", err.Error(), string(tableRecordResp.RawBody))
				continue
			}
		}
	}
}

func AllResourceSplit2() ([]*ResourceSplit2, error) {
	items := []*ResourceSplit2{}
	err := DataSyncDB.db.Table(fmt.Sprintf(`(SELECT 
		s.applyId,
		a.status,
		ifnull(s.planManMonth,0) planManMonth,
		a.applyUserName,
		a.createDate,
		ifnull(a.pmsProjectName, '') pmsProjectName,
		a.targetUserGroupName,
		a.targetUserName,
		s.departmentName,
		s.groupName,
		s.userName,
		a.source,
		s.planManMonth * 168 hour,
		s.beginTime,
		s.endTime,
		ifnull(a.workContent, "") workContent,
		ifnull(a.applyReason, "") applyReason,
		IFNULL(a.verifyRemark,"") verifyRemark
		FROM csbu_res_resource_split s 
		LEFT JOIN csbu_res_resource_apply a ON a.id = s.applyId
		WHERE YEAR = %d) as resource_split`, time.Now().Year())).Find(&items).Error
	return items, err
}

func SyncAllResourceSplitToFeishu2() {
	appToken := "Hi7rbhcSxaAEvnsm7HrcQDHkn1e"
	tableID := "tblZ37ZhPHt3HTNu"

	items, err := AllResourceSplit2()
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
	}

	items2, err := dcsburesresourcesplit.AllSplitJoinApply2()
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
	}
	// 批量删除线上数据
	openfeishu.DeleteTableRecordDataV4(appToken, tableID, []string{"申请ID"})
	// 获取本地数据

	// 批量同步到线上
	now := time.Now()
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, item := range batchData {
				rec := map[string]interface{}{
					"申请ID":   item.ApplyID,
					"审核状态":   item.Status,
					"计划人月":   item.PlanManMonth,
					"申请人":    item.ApplyUserName,
					"申请时间":   item.CreateDate.UnixMilli(),
					"申请项目":   item.PmsProjectName,
					"申请目标部门": item.TargetUserGroupName,
					"申请目标人员": item.TargetUserName,
					"分配部门":   item.DepartmentName,
					"分配专业组":  item.GroupName,
					"分配人员":   item.UserName,
					"分配人月":   item.PlanManMonth,
					"申请来源":   item.Source,
					"总计工时":   item.Hour,
					"开始时间":   item.BeginTime.UnixMilli(),
					"结束时间":   item.EndTime.UnixMilli(),

					"工作内容":   item.WorkContent,
					"申请理由":   item.ApplyReason,
					"审核意见":   item.VerifyRemark,
					"数据过期时间": now.Add(12 * time.Hour).UnixMilli(),
				}
				records = append(records, rec)
			}

			tableRecordResp, err := openfeishu.BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("批量创建记录失败: %s", err.Error(), tableRecordResp.RawBody)
				continue
			}
		}
	}

	for i := 0; i < len(items2); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items2) {
			endIndex = len(items2)
		}
		batchData := items2[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, item := range batchData {
				rec := map[string]interface{}{
					"申请ID":   item.ApplyID,
					"审核状态":   item.Status,
					"计划人月":   item.PlanManMonth,
					"申请人":    item.ApplyUserName,
					"申请时间":   item.CreateDate.UnixMilli(),
					"申请项目":   item.PmsProjectName,
					"申请目标部门": item.TargetUserGroupName,
					"申请目标人员": item.TargetUserName,
					"分配部门":   item.DepartmentName,
					"分配专业组":  item.GroupName,
					"分配人员":   item.UserName,
					"分配人月":   item.PlanManMonth,
					"申请来源":   item.Source,
					"总计工时":   item.Hour,
					"开始时间":   item.BeginTime.UnixMilli(),
					"结束时间":   item.EndTime.UnixMilli(),

					"工作内容":   item.WorkContent,
					"申请理由":   item.ApplyReason,
					"审核意见":   item.VerifyRemark,
					"数据过期时间": now.Add(12 * time.Hour).UnixMilli(),
				}
				records = append(records, rec)
			}

			tableRecordResp, err := openfeishu.BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("批量创建记录失败: %s", err.Error(), tableRecordResp.RawBody)
				continue
			}
		}
	}
}

func ResourceApplySyncWorker() error {
	// todo: 检查同步记录，获取同步时间，如果没有，从七天前开始，按小时同步
	_url := "https://dataware.ruijie.com.cn/api/public/data-api/csbu_scale_project_res_plan_apply/list.data"
	yearMonth := time.Now().Format("2006-01")
	minBeginTime := fmt.Sprintf("%s-01 00:00:00", yearMonth)
	minBeginTime = "2025-01-01 00:00:00"

	scales, err := AllScales()
	if err != nil {
		logging.ErrorLogger.Errorf("find all scales error", err.Error())
		return err
	}

	if len(scales) > 0 {
		for _, scale := range scales {
			fmt.Println(scale.ID, scale.PmsDestProjectName, scale)
			page := 1
			rows := 1000

			for {
				data := map[string]string{
					"sid":             "OTJlMWFmZWMz",
					"minBeginTime":    minBeginTime,
					"page":            strconv.Itoa(page),
					"rows":            strconv.Itoa(rows),
					"pmsProjectIds[]": strconv.Itoa(scale.PmsDestProjectId),
				}

				var result dcsburesresourceapply.ResourceApplySyncResponse
				// var errMsg dcsburesresourceapply.ResourceApplySyncResponse
				resp, err := SyncClient.R().SetQueryParams(data).Get(_url)
				if err != nil {
					logging.ErrorLogger.Errorf("get resource apply error", err.Error())
					return err
				}

				if resp.IsSuccessState() {
					err = json.Unmarshal(resp.Bytes(), &result)
					if err != nil {
						logging.ErrorLogger.Errorf("unmarshal resource apply error", err.Error())
						return err
					}
					if result.State == "SUCCESS" {
						err := dcsburesresourceapply.UpdateOrCreateResouceApplyTransaction(result.Data, _url, data, resp.Request.Method, result.State, result.Message)
						if err != nil {
							logging.ErrorLogger.Errorf("update or create resources error", err.Error())
							return err
						}
					} else {
						logging.ErrorLogger.Errorf("get resources failed", result.State, result.Message)
						return fmt.Errorf("get resources failed, %s, %s", result.State, result.Message)
					}
				} else {
					logging.ErrorLogger.Errorf("get resources unkown error")
					return errors.New("unkown error")
				}

				time.Sleep(5 * time.Second)
				if result.Total > rows*page {
					page++
				} else {
					break
				}
			}
		}
	}

	return nil
}

func ResourceSplitSyncWorker() error {
	// todo: 检查同步记录，获取同步时间，如果没有，从七天前开始，按小时同步
	_url := "https://dataware.ruijie.com.cn/api/public/data-api/csbu_scale_project_res_plan_split/list.data"
	yearMonth := time.Now().Format("2006-01")
	minBeginTime := fmt.Sprintf("%s-01 00:00:00", yearMonth)
	minBeginTime = "2025-01-01 00:00:00"

	scales, err := AllScales()
	if err != nil {
		logging.ErrorLogger.Errorf("find all scales error", err.Error())
		return err
	}

	if len(scales) > 0 {
		for _, scale := range scales {
			fmt.Println(scale.ID, scale.PmsDestProjectName, scale)
			page := 1
			rows := 1000

			for {
				data := map[string]string{
					"sid":             "YWQ2NDMw",
					"minBeginTime":    minBeginTime,
					"page":            strconv.Itoa(page),
					"rows":            strconv.Itoa(rows),
					"pmsProjectIds[]": strconv.Itoa(scale.PmsDestProjectId),
				}

				var result dcsburesresourcesplit.ResourceSplitSyncResponse
				var errMsg dcsburesresourcesplit.ResourceSplitSyncResponse
				resp, err := SyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(data).Get(_url)
				if err != nil {
					logging.ErrorLogger.Errorf("get resouce split error", err.Error())
					return err
				}

				if resp.IsSuccessState() {
					if result.State == "SUCCESS" {
						err := dcsburesresourcesplit.UpdateOrCreateResouceSplitTransaction(result.Data, _url, data, resp.Request.Method, result.State, result.Message)
						if err != nil {
							logging.ErrorLogger.Errorf("update or create resources error", err.Error())
							return err
						}
					} else {
						logging.ErrorLogger.Errorf("get resources failed", result.State, result.Message)
						return fmt.Errorf("get resources failed, %s, %s", result.State, result.Message)
					}
				} else {
					logging.ErrorLogger.Errorf("get resources unkown error")
					return errors.New("unkown error")
				}

				time.Sleep(5 * time.Second)
				if result.Total > rows*page {
					page++
				} else {
					break
				}
			}
		}
	}

	return nil
}
