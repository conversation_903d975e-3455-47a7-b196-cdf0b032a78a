package dfeatureprocdef_11_x

import (
	"encoding/json"
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/featurerelease_11_x"
	// "irisAdminApi/service/dao/user/duser"
)

const ModelName = "规则库流程定义"

type Response struct {
	ID uint `json:"id"`
	// 当前执行流所在的节点
	Name    string `json:"name,omitempty"`
	Version int    `json:"version,omitempty"`
	// 流程定义json字符串
	Resource string `gorm:"size:10000" json:"resource,omitempty"`
}

type ListResponse struct {
	Response
}

type Request struct {
	// 当前执行流所在的节点
	Name    string `json:"name,omitempty"`
	Version int    `json:"version,omitempty"`
	// 流程定义json字符串
	Resource string `gorm:"size:10000" json:"resource,omitempty"`
}

/*
[
	{
		"name": "创建规则库",
		"nodeId": "start",
		"prevNodeId": ""
	},
	{
		"name": "测试验证",
		"nodeId": "test_check",
		"prevNodeId": "start",
		"assignee": 1
	},
	{
		"name": "试点验证",
		"nodeId": "experiment",
		"prevNodeId": "start",
		"assignee": 1
	},
	{
		"name": "功能开发",
		"nodeId": "feature_dev",
		"prevNodeId": "start",
		"assignee": 1
	},
	{
		"name": "获取签名",
		"nodeId": "get_sign",
		"prevNodeId": "test_check",
		"assignee": 0
	},
	{
		"name": "QA审计",
		"nodeId": "qa_audit",
		"prevNodeId": "feature_dev",
		"assignee": 1
	},
	{
		"name": "QA审计",
		"nodeId": "qa_audit",
		"prevNodeId": "test_check",
		"assignee": 1
	},
	{
		"name": "QA审计",
		"nodeId": "qa_audit",
		"prevNodeId": "experiment",
		"assignee": 1
	},
	{
		"name": "CMA发布",
		"nodeId": "cmd_release",
		"prevNodeId": "qa_audit",
		"assignee": 1
	}
]
*/

type Node struct {
	Name       string `json:"name,omitempty"`
	NodeID     string `json:"nodeId,omitempty"`
	PrevNodeID string `json:"prevNodeId,omitempty"`
	Assignee   uint   `json:"assignee"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *featurerelease_11_x.FeatureProcDef_11_X {
	return &featurerelease_11_x.FeatureProcDef_11_X{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func GetPrevNodeIDs(resource, nodeID string) ([]string, error) {
	prevNodeIDs := []string{}
	nodes := []*Node{}
	err := json.Unmarshal([]byte(resource), &nodes)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return prevNodeIDs, err
	}
	for _, node := range nodes {
		if node.NodeID == nodeID {
			if node.PrevNodeID != "" {
				prevNodeIDs = append(prevNodeIDs, node.PrevNodeID)
			}
		}
	}
	return prevNodeIDs, nil
}

func GetNextNodeIDs(resource, nodeID string) ([]string, error) {
	nextNodeIDs := []string{}
	nodes := []*Node{}
	err := json.Unmarshal([]byte(resource), &nodes)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return nextNodeIDs, err
	}
	for _, node := range nodes {
		if node.PrevNodeID == nodeID {
			nextNodeIDs = append(nextNodeIDs, node.NodeID)
		}
	}
	return nextNodeIDs, nil
}

func GetNodes(resource string) ([]*Node, error) {
	nodes := []*Node{}

	err := json.Unmarshal([]byte(resource), &nodes)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return nodes, err
	}

	return nodes, nil
}

func GetResource(name string) (string, error) {
	define := Response{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("name = ?", name).Find(&define).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return "", err
	}
	if define.ID == 0 {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return "", err
	}
	resource := define.Resource
	return resource, nil
}

func GetPrevNodes(resource, nodeID string) ([]*Node, error) {
	nodes := []*Node{}
	prevNodeIDs, err := GetPrevNodeIDs(resource, nodeID)
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nodes, err
	}
	for _, prevNodeID := range prevNodeIDs {
		node, _ := GetNode(resource, prevNodeID)
		if node != nil {
			nodes = append(nodes, node)
		}
	}
	return nodes, nil
}

func GetNode(resource string, nodeID string) (*Node, error) {
	nodes, _ := GetNodes(resource)
	for _, node := range nodes {
		if node.NodeID == nodeID {
			return node, nil
		}
	}
	return nil, nil
}

func GetBeforeNodes(nodes []*Node, nodeID string) ([]*Node, error) {
	result := []*Node{}
	beforeNodeIDs := []string{}
	checkNodeID := nodeID
	for _, node := range nodes {
		if node.NodeID == checkNodeID {
			if node.PrevNodeID != "" {
				if !libs.InArrayS(beforeNodeIDs, node.PrevNodeID) {
					ret, _ := GetBeforeNodes(nodes, node.PrevNodeID)
					for _, r := range ret {
						if !libs.InArrayS(beforeNodeIDs, r.NodeID) {
							beforeNodeIDs = append(beforeNodeIDs, r.NodeID)
						}
					}
					beforeNodeIDs = append(beforeNodeIDs, node.PrevNodeID)
				}
			}
		}
	}
	for _, id := range beforeNodeIDs {
		for _, node := range nodes {
			if node.NodeID == id {
				result = append(result, node)
			}
		}
	}
	return result, nil
}

func GetAfterNodes(nodes []*Node, nodeID string) ([]*Node, error) {
	result := []*Node{}
	afterNodeIDs := []string{}
	checkNodeID := nodeID
	for _, node := range nodes {
		if node.PrevNodeID == checkNodeID {
			if !libs.InArrayS(afterNodeIDs, node.NodeID) {
				ret, _ := GetAfterNodes(nodes, node.NodeID)
				for _, r := range ret {
					if !libs.InArrayS(afterNodeIDs, r.NodeID) {
						afterNodeIDs = append(afterNodeIDs, r.NodeID)
					}
				}
				afterNodeIDs = append(afterNodeIDs, node.NodeID)
			}
		}
	}
	for _, id := range afterNodeIDs {
		for _, node := range nodes {
			if node.NodeID == id {
				result = append(result, node)
			}
		}
	}
	return result, nil
}
