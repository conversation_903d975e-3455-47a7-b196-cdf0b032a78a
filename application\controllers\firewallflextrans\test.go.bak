// 定义命名空间常量
const (
	NamespaceSystem     = "urn:ruijie:ntos"
	NamespaceNetworkObj = "urn:ruijie:ntos:params:xml:ns:yang:network-obj"
	NamespaceNat        = "urn:ruijie:ntos:params:xml:ns:yang:nat"
	NamespaceServiceObj = "urn:ruijie:ntos:params:xml:ns:yang:service-obj"
	NamespaceAV         = "urn:ruijie:ntos:params:xml:ns:yang:anti-virus"
	NamespaceIPS        = "urn:ruijie:ntos:params:xml:ns:yang:intrusion-prevention"
)

// 定义结构体以匹配 XML 结构
type Config struct {
    XMLName xml.Name `xml:"config"`
    VRF     VRF      `xml:"vrf"`
}

type VRF struct {
    NetworkObj NetworkObj `xml:"network-obj"`
}

type NetworkObj struct {
	XMLName xml.Name `xml:"network-obj"`
	Item    string   `xml:"item"`
}

// FirewallAddress 定义防火墙地址结构
type FirewallAddress struct {
	Name    string
	Type    string
	StartIP string
	EndIP   string
	Subnet  string
	Mask    string
}

// FirewallAddrGroup 定义地址组结构
type FirewallAddrGroup struct {
	Name    string
	Members []string
}

// ServiceGroup 定义服务组结构
type ServiceGroup struct {
	Name    string
	Members []string
}

// Policy 定义防火墙策略结构
type Policy struct {
	Name      string
	SrcAddr   string
	DstAddr   string
	Service   string
	Status    string
	FixedPort string
	Comments  string
}

// AVProfile 定义防病毒配置结构
type AVProfile struct {
	Name        string
	Description string
	ScanMode    string
	Protocols   []Protocol
	FileTypes   []string
}

// Protocol 定义协议结构
type Protocol struct {
	Name      string
	Direction string
}

// Service 定义服务结构
type Service struct {
	Name     string
	TCPPorts []string
	UDPPorts []string
}

// IPPool 定义IP池结构
type IPPool struct {
	Name    string
	StartIP string
	EndIP   string
}

// IPSensor 定义IPS传感器结构
type IPSensor struct {
	Name     string
	Comment  string
	Severity []string
}

// 提取引号中的字符串
func extractQuotedString(line string) string {
	parts := strings.Split(line, "\"")
	if len(parts) >= 2 {
		return parts[1]
	}
	return ""
}

// 提取多组引号中的字符串
func extractMultipleQuotedStrings(line string) []string {
	var results []string
	parts := strings.Split(line, "\"")
	for i := 1; i < len(parts); i += 2 {
		results = append(results, parts[i])
	}
	return results
}

func parseFirewallAddress(lines []string) []FirewallAddress {
	var addresses []FirewallAddress
	var currentAddress *FirewallAddress
	insideAddressBlock := false

	for _, line := range lines {
		line = strings.TrimSpace(line)

		if strings.HasPrefix(line, "config firewall address") {
			insideAddressBlock = true
			continue
		}

		if insideAddressBlock {
			if strings.HasPrefix(line, "end") {
				if currentAddress != nil {
					addresses = append(addresses, *currentAddress)
				}
				insideAddressBlock = false
				continue
			} else if strings.HasPrefix(line, "edit") {
				if currentAddress != nil {
					addresses = append(addresses, *currentAddress)
				}
				name := extractQuotedString(line)
				currentAddress = &FirewallAddress{
					Name:    name,
					Type:    "",
					StartIP: "",
					EndIP:   "",
					Subnet:  "",
					Mask:    "",
				}
			} else if strings.HasPrefix(line, "set type") {
				if currentAddress != nil {
					currentAddress.Type = strings.Fields(line)[len(strings.Fields(line))-1]
				}
			} else if strings.HasPrefix(line, "set start-ip") {
				if currentAddress != nil {
					fields := strings.Fields(line)
					if len(fields) > 0 {
						currentAddress.StartIP = fields[len(fields)-1]
					}
				}
			} else if strings.HasPrefix(line, "set end-ip") {
				if currentAddress != nil {
					fields := strings.Fields(line)
					if len(fields) > 0 {
						currentAddress.EndIP = fields[len(fields)-1]
					}
				}
			} else if strings.HasPrefix(line, "set subnet") {
				if currentAddress != nil {
					parts := strings.Fields(line)
					if len(parts) >= 2 {
						currentAddress.Subnet = parts[len(parts)-2]
						currentAddress.Mask = parts[len(parts)-1]
					}
				}
			}
		}
	}

	var validAddresses []FirewallAddress
	for _, addr := range addresses {
		if (addr.Type == "iprange" && addr.StartIP != "" && addr.EndIP != "") ||
			(addr.Subnet != "" && addr.Mask != "") {
			validAddresses = append(validAddresses, addr)
		}
	}

	return validAddresses
}

func parseFirewallAddrGrp(lines []string) []FirewallAddrGroup {
	var groups []FirewallAddrGroup
	var currentGroup *FirewallAddrGroup
	insideGroup := false

	for _, line := range lines {
		line = strings.TrimSpace(line)

		if strings.HasPrefix(line, "config firewall addrgrp") {
			insideGroup = true
			continue
		}

		if insideGroup {
			if strings.HasPrefix(line, "end") {
				if currentGroup != nil {
					groups = append(groups, *currentGroup)
				}
				insideGroup = false
				continue
			} else if strings.HasPrefix(line, "edit") {
				if currentGroup != nil {
					groups = append(groups, *currentGroup)
				}
				name := extractQuotedString(line)
				currentGroup = &FirewallAddrGroup{
					Name:    name,
					Members: []string{},
				}
			} else if strings.HasPrefix(line, "set member") {
				members := extractMultipleQuotedStrings(line)
				if currentGroup != nil {
					currentGroup.Members = append(currentGroup.Members, members...)
				}
			}
		}
	}

	return groups
}

func parseAntiVirus(lines []string) []AVProfile {
	var profiles []AVProfile
	var currentProfile *AVProfile
	insideAVBlock := false

	for _, line := range lines {
		line = strings.TrimSpace(line)

		if strings.HasPrefix(line, "config antivirus profile") {
			insideAVBlock = true
			continue
		}

		if insideAVBlock {
			if strings.HasPrefix(line, "end") {
				if currentProfile != nil {
					profiles = append(profiles, *currentProfile)
				}
				break
			} else if strings.HasPrefix(line, "edit") {
				if currentProfile != nil {
					profiles = append(profiles, *currentProfile)
				}
				name := extractQuotedString(line)
				currentProfile = &AVProfile{
					Name:        name,
					Description: fmt.Sprintf("Profile for %s", name),
					ScanMode:    "",
					Protocols:   []Protocol{},
					FileTypes:   []string{},
				}
			} else if strings.HasPrefix(line, "set scan-mode") {
				if currentProfile != nil {
					fields := strings.Fields(line)
					if len(fields) > 0 {
						currentProfile.ScanMode = fields[len(fields)-1]
					}
				}
			} else if strings.HasPrefix(line, "set protocols") {
				if currentProfile != nil {
					protocols := extractMultipleQuotedStrings(line)
					for _, p := range protocols {
						currentProfile.Protocols = append(currentProfile.Protocols, Protocol{
							Name:      p,
							Direction: "both",
						})
					}
				}
			} else if strings.HasPrefix(line, "set file-types") {
				if currentProfile != nil {
					fileTypes := extractMultipleQuotedStrings(line)
					currentProfile.FileTypes = append(currentProfile.FileTypes, fileTypes...)
				}
			}
		}
	}

	return profiles
}

func parseServiceGroup(lines []string) []ServiceGroup {
	var serviceGroups []ServiceGroup
	var currentGroup *ServiceGroup
	insideGroup := false

	for _, line := range lines {
		line = strings.TrimSpace(line)

		if strings.HasPrefix(line, "config firewall service group") {
			insideGroup = true
			continue
		}

		if insideGroup {
			if strings.HasPrefix(line, "end") {
				if currentGroup != nil {
					serviceGroups = append(serviceGroups, *currentGroup)
				}
				insideGroup = false
				continue
			} else if strings.HasPrefix(line, "edit") {
				if currentGroup != nil {
					serviceGroups = append(serviceGroups, *currentGroup)
				}
				name := extractQuotedString(line)
				currentGroup = &ServiceGroup{
					Name:    name,
					Members: []string{},
				}
			} else if strings.HasPrefix(line, "set member") {
				members := extractMultipleQuotedStrings(line)
				if currentGroup != nil {
					currentGroup.Members = append(currentGroup.Members, members...)
				}
			}
		}
	}

	return serviceGroups
}

func parseFortinetPolicy(lines []string) []Policy {
	var policies []Policy
	var currentPolicy *Policy
	insidePolicyBlock := false

	for _, line := range lines {
		line = strings.TrimSpace(line)

		if strings.HasPrefix(line, "config firewall policy") {
			insidePolicyBlock = true
			continue
		}

		if insidePolicyBlock {
			if strings.HasPrefix(line, "end") {
				if currentPolicy != nil {
					policies = append(policies, *currentPolicy)
				}
				insidePolicyBlock = false
				continue
			} else if strings.HasPrefix(line, "edit") {
				if currentPolicy != nil {
					policies = append(policies, *currentPolicy)
				}
				currentPolicy = &Policy{
					Name:      "",
					SrcAddr:   "",
					DstAddr:   "",
					Service:   "",
					Status:    "",
					FixedPort: "",
					Comments:  "",
				}
			} else if strings.HasPrefix(line, "set name") {
				if currentPolicy != nil {
					currentPolicy.Name = extractQuotedString(line)
				}
			} else if strings.HasPrefix(line, "set srcaddr") {
				if currentPolicy != nil {
					currentPolicy.SrcAddr = extractMultipleQuotedStrings(line)[0]
				}
			} else if strings.HasPrefix(line, "set dstaddr") {
				if currentPolicy != nil {
					currentPolicy.DstAddr = extractMultipleQuotedStrings(line)[0]
				}
			} else if strings.HasPrefix(line, "set service") {
				if currentPolicy != nil {
					currentPolicy.Service = extractMultipleQuotedStrings(line)[0]
				}
			} else if strings.HasPrefix(line, "set status") {
				if currentPolicy != nil {
					currentPolicy.Status = strings.Fields(line)[len(strings.Fields(line))-1]
				}
			} else if strings.HasPrefix(line, "set fixedport") {
				if currentPolicy != nil {
					currentPolicy.FixedPort = strings.Fields(line)[len(strings.Fields(line))-1]
				}
			} else if strings.HasPrefix(line, "set comments") {
				if currentPolicy != nil {
					currentPolicy.Comments = extractQuotedString(line)
				}
			}
		}
	}

	return policies
}

func parseFirewallIPPool(lines []string) []IPPool {
	var ippools []IPPool
	var currentIPPool *IPPool
	insideIPPoolBlock := false

	for _, line := range lines {
		line = strings.TrimSpace(line)

		if strings.HasPrefix(line, "config firewall ippool") {
			insideIPPoolBlock = true
			continue
		}

		if insideIPPoolBlock {
			if strings.HasPrefix(line, "end") {
				if currentIPPool != nil {
					ippools = append(ippools, *currentIPPool)
				}
				insideIPPoolBlock = false
				continue
			} else if strings.HasPrefix(line, "edit") {
				if currentIPPool != nil {
					ippools = append(ippools, *currentIPPool)
				}
				name := extractQuotedString(line)
				currentIPPool = &IPPool{
					Name:    name,
					StartIP: "",
					EndIP:   "",
				}
			} else if strings.HasPrefix(line, "set startip") {
				fields := strings.Fields(line)
				if len(fields) > 0 && currentIPPool != nil {
					currentIPPool.StartIP = fields[len(fields)-1]
				}
			} else if strings.HasPrefix(line, "set endip") {
				fields := strings.Fields(line)
				if len(fields) > 0 && currentIPPool != nil {
					currentIPPool.EndIP = fields[len(fields)-1]
				}
			}
		}
	}

	return ippools
}

func parseIPSSensor(lines []string) []IPSensor {
	var sensors []IPSensor
	var currentSensor *IPSensor
	insideIPSSensor := false
	nestedBlocks := []string{}

	linesIterator := make([]string, len(lines))
	copy(linesIterator, lines)

	for i := 0; i < len(linesIterator); i++ {
		line := strings.TrimSpace(linesIterator[i])

		if strings.HasPrefix(line, "config ips sensor") {
			insideIPSSensor = true
			continue
		}

		if insideIPSSensor {
			if line == "end" && len(nestedBlocks) == 0 {
				if currentSensor != nil {
					sensors = append(sensors, *currentSensor)
				}
				break
			}

			if strings.HasPrefix(line, "config") || strings.HasPrefix(line, "edit") {
				nestedBlocks = append(nestedBlocks, line)
				if len(nestedBlocks) == 1 && strings.HasPrefix(line, "edit") {
					name := extractQuotedString(line)
					if currentSensor != nil {
						sensors = append(sensors, *currentSensor)
					}
					currentSensor = &IPSensor{
						Name:     name,
						Comment:  "",
						Severity: []string{},
					}
				}
				continue
			}

			if line == "end" || line == "next" {
				if len(nestedBlocks) > 0 {
					nestedBlocks = nestedBlocks[:len(nestedBlocks)-1]
					if len(nestedBlocks) == 0 && currentSensor != nil {
						sensors = append(sensors, *currentSensor)
						currentSensor = nil
					}
				}
				continue
			}

			if strings.HasPrefix(line, "set comment") && currentSensor != nil {
				currentSensor.Comment = extractQuotedString(line)
			}

			if strings.HasPrefix(line, "set severity") && currentSensor != nil {
				severities := strings.Fields(line)[2:]
				currentSensor.Severity = append(currentSensor.Severity, severities...)
			}

			if strings.HasPrefix(line, "config entries") && currentSensor != nil {
				for j := i + 1; j < len(linesIterator); j++ {
					entryLine := strings.TrimSpace(linesIterator[j])
					if entryLine == "end" {
						i = j
						break
					}
					if strings.HasPrefix(entryLine, "set severity") {
						severities := strings.Fields(entryLine)[2:]
						currentSensor.Severity = append(currentSensor.Severity, severities...)
					}
				}
			}
		}
	}

	return sensors
}

func parseServiceCustom(lines []string) []Service {
	var services []Service
	var currentService *Service
	insideServiceCustom := false

	for _, line := range lines {
		line = strings.TrimSpace(line)

		if strings.HasPrefix(line, "config firewall service custom") {
			insideServiceCustom = true
			continue
		}

		if insideServiceCustom {
			if strings.HasPrefix(line, "end") {
				if currentService != nil {
					services = append(services, *currentService)
				}
				break
			} else if strings.HasPrefix(line, "edit") {
				if currentService != nil {
					services = append(services, *currentService)
				}
				name := extractQuotedString(line)
				currentService = &Service{
					Name:     name,
					TCPPorts: []string{},
					UDPPorts: []string{},
				}
			} else if strings.HasPrefix(line, "set tcp-portrange") {
				if currentService != nil {
					fields := strings.Fields(line)
					if len(fields) > 0 {
						ports := fields[len(fields)-1]
						currentService.TCPPorts = append(currentService.TCPPorts, strings.Split(ports, ",")...)
					}
				}
			} else if strings.HasPrefix(line, "set udp-portrange") {
				if currentService != nil {
					fields := strings.Fields(line)
					if len(fields) > 0 {
						ports := fields[len(fields)-1]
						currentService.UDPPorts = append(currentService.UDPPorts, strings.Split(ports, ",")...)
					}
				}
			}
		}
	}

	return services
}

func generateRuijieNatConfig(root *etree.Element, policies []Policy, ippools []IPPool) error {
	natNode := root.FindElement(".//{" + NamespaceNat + "}nat")
	if natNode == nil {
		return fmt.Errorf("无法找到 <nat> 节点，请检查模板文件")
	}

	for _, ippool := range ippools {
		enabledNode := natNode.CreateElement("enabled")
		enabledNode.SetText("true")

		poolNode := natNode.CreateElement("pool")
		nameNode := poolNode.CreateElement("name")
		nameNode.SetText(ippool.Name)

		addressNode := poolNode.CreateElement("address")
		addressNode.SetText(fmt.Sprintf("%s-%s", ippool.StartIP, ippool.EndIP))
	}

	for _, policy := range policies {
		rule := natNode.CreateElement("rule")
		nameNode := rule.CreateElement("name")
		nameNode.SetText(policy.Name)

		descNode := rule.CreateElement("desc")
		descNode.SetText(policy.Comments)

		ruleEn := rule.CreateElement("rule_en")
		if policy.Status != "disable" {
			ruleEn.SetText("true")
		} else {
			ruleEn.SetText("false")
		}

		staticSNAT44 := rule.CreateElement("static-snat44")
		match := staticSNAT44.CreateElement("match")

		sourceNetwork := match.CreateElement("source-network")
		sourceName := sourceNetwork.CreateElement("name")
		sourceName.SetText(policy.SrcAddr)

		if policy.DstAddr != "" && strings.ToLower(policy.DstAddr) != "all" {
			destNetwork := match.CreateElement("dest-network")
			destName := destNetwork.CreateElement("name")
			destName.SetText(policy.DstAddr)
		}

		service := match.CreateElement("service")
		serviceName := service.CreateElement("name")
		serviceName.SetText(policy.Service)

		translateTo := staticSNAT44.CreateElement("translate-to")
		translateTo.CreateElement("output-address")
		noPAT := translateTo.CreateElement("no-pat")
		if policy.FixedPort == "enable" {
			noPAT.SetText("true")
		} else {
			noPAT.SetText("false")
		}
	}

	return nil
}

func generateRuijieFirewallAddresses(root *etree.Element, firewallAddresses []FirewallAddress) error {
	networkObjNode := root.FindElement(".//{" + NamespaceNetworkObj + "}network-obj")
	if networkObjNode == nil {
		return fmt.Errorf("无法找到 <network-obj> 节点，请检查模板文件")
	}

	for _, address := range firewallAddresses {
		if address.Name == "" || address.Type == "" {
			continue
		}
		if address.Type == "iprange" && (address.StartIP == "" || address.EndIP == "") {
			continue
		}
		if address.Type == "subnet" && (address.Subnet == "" || address.Mask == "") {
			continue
		}

		addressSetNode := networkObjNode.CreateElement("address-set")
		nameNode := addressSetNode.CreateElement("name")
		nameNode.SetText(address.Name)

		ipSetNode := addressSetNode.CreateElement("ip-set")
		ipAddressNode := ipSetNode.CreateElement("ip-address")
		if address.Type == "iprange" {
			ipAddressNode.SetText(fmt.Sprintf("%s-%s", address.StartIP, address.EndIP))
		} else if address.Type == "subnet" {
			ipAddressNode.SetText(fmt.Sprintf("%s/%s", address.Subnet, address.Mask))
		}
	}

	return nil
}

func generateRuijieServiceConfig(root *etree.Element, services []Service) error {
	serviceObjNode := root.FindElement(".//{" + NamespaceServiceObj + "}service-obj")
	if serviceObjNode == nil {
		return fmt.Errorf("无法找到 <service-obj> 节点，请检查模板文件")
	}

	for _, service := range services {
		serviceSet := serviceObjNode.CreateElement("service-set")
		nameNode := serviceSet.CreateElement("name")
		nameNode.SetText(service.Name)

		if len(service.TCPPorts) > 0 {
			tcpNode := serviceSet.CreateElement("tcp")
			destPort := strings.Join(service.TCPPorts, ",")
			destPortNode := tcpNode.CreateElement("dest-port")
			destPortNode.SetText(destPort)
		}

		if len(service.UDPPorts) > 0 {
			udpNode := serviceSet.CreateElement("udp")
			destPort := strings.Join(service.UDPPorts, ",")
			destPortNode := udpNode.CreateElement("dest-port")
			destPortNode.SetText(destPort)
		}
	}

	return nil
}

func generateRuijieServiceGroups(root *etree.Element, serviceGroups []ServiceGroup) error {
	serviceObjNode := root.FindElement(".//{" + NamespaceServiceObj + "}service-obj")
	if serviceObjNode == nil {
		return fmt.Errorf("无法找到 <service-obj> 节点，请检查模板文件")
	}

	for _, group := range serviceGroups {
		serviceGroup := serviceObjNode.CreateElement("service-group")
		nameNode := serviceGroup.CreateElement("name")
		nameNode.SetText(group.Name)

		for _, member := range group.Members {
			serviceSet := serviceGroup.CreateElement("service-set")
			memberName := serviceSet.CreateElement("name")
			memberName.SetText(member)
		}
	}

	return nil
}

func generateRuijieAddressGroups(root *etree.Element, addressGroups []FirewallAddrGroup) error {
	networkObjNode := root.FindElement(".//{" + NamespaceNetworkObj + "}network-obj")
	if networkObjNode == nil {
		return fmt.Errorf("无法找到 <network-obj> 节点，请检查模板文件")
	}

	for _, group := range addressGroups {
		addressGroup := networkObjNode.CreateElement("address-group")
		nameNode := addressGroup.CreateElement("name")
		nameNode.SetText(group.Name)

		for _, member := range group.Members {
			addressSet := addressGroup.CreateElement("address-set")
			memberName := addressSet.CreateElement("name")
			memberName.SetText(member)
		}
	}

	return nil
}

func generateRuijieAVConfig(root *etree.Element, avProfiles []AVProfile) error {
	avNode := root.FindElement(".//{" + NamespaceAV + "}anti-virus")
	if avNode == nil {
		return fmt.Errorf("无法找到 <anti-virus> 节点，请检查模板文件")
	}

	for _, profile := range avProfiles {
		template := avNode.CreateElement("template")

		nameNode := template.CreateElement("name")
		nameNode.SetText(profile.Name)

		descNode := template.CreateElement("description")
		descNode.SetText(profile.Description)

		scanModeNode := template.CreateElement("scan-mode")
		scanModeNode.SetText(profile.ScanMode)

		protocolsNode := template.CreateElement("protocols")
		if len(profile.Protocols) == 0 {
			// 默认所有协议
			defaultProtocols := []Protocol{
				{"FTP", "both"}, {"HTTP", "both"}, {"IMAP", "to-client"},
				{"NFS", "both"}, {"POP3", "to-client"}, {"SMB", "both"},
				{"SMTP", "to-server"},
			}
			for _, proto := range defaultProtocols {
				protocolNode := protocolsNode.CreateElement("protocol")
				protocolName := protocolNode.CreateElement("name")
				protocolName.SetText(proto.Name)
				directionNode := protocolNode.CreateElement("direction")
				directionNode.SetText(proto.Direction)
			}
		} else {
			for _, proto := range profile.Protocols {
				protocolNode := protocolsNode.CreateElement("protocol")
				protocolName := protocolNode.CreateElement("name")
				protocolName.SetText(proto.Name)
				directionNode := protocolNode.CreateElement("direction")
				directionNode.SetText(proto.Direction)
			}
		}

		fileTypeSetsNode := template.CreateElement("file-type-sets")
		for _, ft := range profile.FileTypes {
			fileTypeNode := fileTypeSetsNode.CreateElement("file-type")
			suffixNode := fileTypeNode.CreateElement("suffix")
			suffixNode.SetText(ft)
		}
	}

	return nil
}

func generateRuijieIPSConfig(root *etree.Element, ipsSensors []IPSensor) error {
	ipsNode := root.FindElement(".//{" + NamespaceIPS + "}ips-config")
	if ipsNode == nil {
		return fmt.Errorf("无法找到 <ips-config> 节点，请检查模板文件")
	}

	severityMapping := map[string]string{
		"critical":      "high",
		"high":          "medium",
		"medium":        "low",
		"low":           "informational",
		"informational": "informational",
	}

	for _, sensor := range ipsSensors {
		template := ipsNode.CreateElement("template")

		nameNode := template.CreateElement("name")
		nameNode.SetText(sensor.Name)

		descNode := template.CreateElement("description")
		descNode.SetText(sensor.Comment)

		filterNode := template.CreateElement("filter")
		filterName := filterNode.CreateElement("name")
		filterName.SetText("test-filter")

		targetNode := filterNode.CreateElement("target")
		targetNode.SetText("both")

		for _, severity := range sensor.Severity {
			if mapped, ok := severityMapping[strings.ToLower(severity)]; ok {
				severityNode := filterNode.CreateElement("severity")
				severityNode.SetText(mapped)
			}
		}

		protocolNode := filterNode.CreateElement("protocol")
		allProtocolNode := protocolNode.CreateElement("all-protocol")
		allProtocolNode.SetText("true")

		categoryNode := filterNode.CreateElement("category")
		allCategoryNode := categoryNode.CreateElement("all-category")
		allCategoryNode.SetText("true")
	}

	return nil
}

func updateRuijieTemplate(templateFile, outputFile string, firewallAddresses []FirewallAddress, addressGroups []FirewallAddrGroup, policies []Policy, avProfiles []AVProfile, services []Service, serviceGroups []ServiceGroup, ippools []IPPool, ipsSensors []IPSensor) error {
	doc := etree.NewDocument()
	if err := doc.ReadFromFile(templateFile); err != nil {
		return err
	}
	root := doc.Root()

	// IP地址配置
	if err := generateRuijieFirewallAddresses(root, firewallAddresses); err != nil {
		return err
	}

	// 地址组配置
	if err := generateRuijieAddressGroups(root, addressGroups); err != nil {
		return err
	}

	// NAT 配置
	if err := generateRuijieNatConfig(root, policies, ippools); err != nil {
		return err
	}

	// 防病毒配置
	if err := generateRuijieAVConfig(root, avProfiles); err != nil {
		return err
	}

	// 服务配置
	if err := generateRuijieServiceConfig(root, services); err != nil {
		return err
	}

	// 服务组配置
	if err := generateRuijieServiceGroups(root, serviceGroups); err != nil {
		return err
	}

	// IPS 配置
	if err := generateRuijieIPSConfig(root, ipsSensors); err != nil {
		return err
	}

	// 写入输出文件
	return doc.WriteToFile(outputFile)
}

func processFortinetToRuijie(fortinetFile, ruijieTemplate, outputFile string) error {
	file, err := os.Open(fortinetFile)
	if err != nil {
		return err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	var lines []string
	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}

	if err := scanner.Err(); err != nil {
		return err
	}

	firewallAddresses := parseFirewallAddress(lines)
	addressGroups := parseFirewallAddrGrp(lines)
	policies := parseFortinetPolicy(lines)
	avProfiles := parseAntiVirus(lines)
	services := parseServiceCustom(lines)
	serviceGroups := parseServiceGroup(lines)
	ippools := parseFirewallIPPool(lines)
	ipsSensors := parseIPSSensor(lines)

	fmt.Println(ipsSensors)

	return updateRuijieTemplate(ruijieTemplate, outputFile, firewallAddresses, addressGroups, policies, avProfiles, services, serviceGroups, ippools, ipsSensors)
}

// 打印所有元素及其命名空间（用于调试）
func printElementNamespaces(el *etree.Element, indent string) {
	fmt.Printf("%sElement: {%s}%s\n", indent, el.Space, el.Tag)
	for _, child := range el.ChildElements() {
		printElementNamespaces(child, indent+"  ")
	}
}

// 查找带命名空间的元素
func findElementWithNamespace(root *etree.Element, namespaceURI, tag string) *etree.Element {
	xpath := fmt.Sprintf(".//{%s}%s", namespaceURI, tag)
	return root.FindElement(xpath)
}

func FileTest() {
	// fortinetConfigPath := "/tmp/fortinet_config.conf"
	//ruijieTemplatePath := "/tmp/Z8620-startup-R10.xml"
	// outputPath := "/tmp/updated_ruijie_template.xml"

	// if err := processFortinetToRuijie(fortinetConfigPath, ruijieTemplatePath, outputPath); err != nil {
	// 	fmt.Printf("转换失败: %v\n", err)
	// } else {
	// 	fmt.Println("转换成功，输出文件为:", outputPath)
	// }
	ruijieTemplatePath := "/tmp/test.xml"
	// 读取 XML 文件
	data, err := ioutil.ReadFile(ruijieTemplatePath)
	if err != nil {
		log.Fatalf("无法读取XML文件: %v", err)
	}

	var config Config

	// 自定义解码，处理命名空间
	decoder := xml.NewDecoder(strings.NewReader(string(data)))
	decoder.CharsetReader = charsetReader

	if err := decoder.Decode(&config); err != nil {
		log.Fatalf("XML解析失败: %v", err)
	}

	// 打印解析结果
	fmt.Printf("根节点: {%s}%s\n", config.XMLName.Space, config.XMLName.Local)
	fmt.Printf("NetworkObj节点: {%s}%s\n", config.VRF.NetworkObj.XMLName.Space, config.VRF.NetworkObj.XMLName.Local)
	fmt.Printf("Item值: %s\n", config.VRF.NetworkObj.Item)

}

// charsetReader 处理 XML 编码
func charsetReader(charset string, input io.Reader) (io.Reader, error) {
	// 可以根据需要处理不同的字符集
	return input, nil
}
