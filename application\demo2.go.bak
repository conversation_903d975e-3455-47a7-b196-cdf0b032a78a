package main

import (
	"bufio"
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"
)

// 定义日志条目结构体
type LogEntry struct {
	Time       time.Time
	ClientIP   string
	Method     string
	URL        string
	Status     int
	BytesSent  int
	UserAgent  string
	RefererURL string
	SiteName   string // 新增站点名称字段
}

// Nginx日志格式正则表达式
var logPattern = regexp.MustCompile(`^(\d+.\d+.\d+.\d+)\s-\s-\s\[(.+)\]\s"(\w+)\s(.+)\s\w+/\d+.\d+"\s(\d+)\s(\d+)\s"(.+)"\s"(.+)"`)

func main() {
	// 解析命令行参数
	logDir := flag.String("logdir", "/var/log/nginx", "Nginx日志目录")
	outputDir := flag.String("output", "./logs", "输出日志目录")
	flag.Parse()

	// 创建输出目录
	err := os.MkdirAll(*outputDir, 0755)
	if err != nil {
		log.Fatalf("无法创建输出目录: %v", err)
	}

	// 获取日志文件列表
	files, err := filepath.Glob(filepath.Join(*logDir, "*access.log*"))
	if err != nil {
		log.Fatalf("无法获取日志文件列表: %v", err)
	}

	// 使用wait group等待所有goroutine完成
	var wg sync.WaitGroup
	wg.Add(len(files))

	// 为每个日志文件创建一个goroutine
	for _, file := range files {
		go processLogFile(file, *outputDir, &wg)
	}

	// 等待所有goroutine完成
	wg.Wait()
}

func processLogFile(logFile, outputDir string, wg *sync.WaitGroup) {
	defer wg.Done()

	// 获取站点名称
	siteName := getSiteNameFromFilename(logFile)

	// 打开日志文件
	file, err := os.Open(logFile)
	if err != nil {
		log.Printf("无法打开日志文件 %s: %v", logFile, err)
		return
	}
	defer file.Close()

	// 创建bufio.Scanner
	scanner := bufio.NewScanner(file)

	// 逐行读取并解析日志
	for scanner.Scan() {
		line := scanner.Text()
		entry, err := parseLogLine(line, siteName)
		if err != nil {
			log.Printf("无法解析日志行: %s, 错误: %v", line, err)
			continue
		}

		// 根据日期创建输出文件
		outputFile, err := getOutputFile(outputDir, entry.Time)
		if err != nil {
			log.Printf("无法创建输出文件: %v", err)
			continue
		}
		defer outputFile.Close()

		// 将解析后的日志条目写入输出文件
		outputLine := fmt.Sprintf("%s %s %s %s %d %d %s %s %s\n",
			entry.Time.Format(time.RFC3339),
			entry.ClientIP,
			entry.Method,
			entry.URL,
			entry.Status,
			entry.BytesSent,
			entry.UserAgent,
			entry.RefererURL,
			entry.SiteName)
		_, err = outputFile.WriteString(outputLine)
		if err != nil {
			log.Printf("无法写入输出文件: %v", err)
		}
	}

	if err := scanner.Err(); err != nil {
		log.Printf("扫描日志文件时发生错误: %v", err)
	}
}

func parseLogLine(line, siteName string) (*LogEntry, error) {
	matches := logPattern.FindStringSubmatch(line)
	if len(matches) != 9 {
		return nil, fmt.Errorf("无效的日志行格式: %s", line)
	}

	timeStr := matches[2]
	t, err := time.Parse("02/Jan/2006:15:04:05 -0700", timeStr)
	if err != nil {
		return nil, fmt.Errorf("无法解析时间字符串: %s, 错误: %v", timeStr, err)
	}

	status, err := parseIntOrDefault(matches[5], 0)
	if err != nil {
		return nil, fmt.Errorf("无法解析状态码: %s, 错误: %v", matches[5], err)
	}

	bytesSent, err := parseIntOrDefault(matches[6], 0)
	if err != nil {
		return nil, fmt.Errorf("无法解析字节数: %s, 错误: %v", matches[6], err)
	}

	return &LogEntry{
		Time:       t,
		ClientIP:   matches[1],
		Method:     matches[3],
		URL:        matches[4],
		Status:     status,
		BytesSent:  bytesSent,
		UserAgent:  matches[7],
		RefererURL: matches[8],
		SiteName:   siteName,
	}, nil
}

func getSiteNameFromFilename(filename string) string {
	dir, file := filepath.Split(filename)
	siteDir := filepath.Base(dir)
	if siteDir != "" {
		return siteDir
	}
	parts := strings.Split(file, ".")
	if len(parts) > 0 {
		return parts[0]
	}
	return ""
}

func getOutputFile(outputDir string, t time.Time) (*os.File, error) {
	dateStr := t.Format("2006-01-02")
	outputFile, err := os.OpenFile(filepath.Join(outputDir, fmt.Sprintf("access_%s.log", dateStr)), os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	return outputFile, err
}

func parseIntOrDefault(s string, defaultValue int) (int, error) {
	if s == "" {
		return defaultValue, nil
	}
	return strconv.Atoi(s)
}
