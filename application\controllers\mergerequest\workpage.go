package mergerequest

import (
	"fmt"
	"strconv"
	"strings"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/datasync/dpmsworkpacketinfo"
	"irisAdminApi/service/dao/mergerequest/dmergerequestworkpackage"
	"irisAdminApi/service/dao/release/dproject"

	"github.com/kataras/iris/v12"
	"github.com/xuri/excelize/v2"
)

// 获取单元格的引用
func getCellRef(col, row int) string {
	return fmt.Sprintf("%c%d", 'A'+col, row+1)
}

func CheckBool(v string) bool {
	return v == "1"
}

func ImportWorkPackage(ctx iris.Context) {
	releaseProjectID, err := dao.GetId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	file, _, err := ctx.FormFile("file")
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	defer file.Close()
	f, err := excelize.OpenReader(file)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	defer f.Close()

	sheetName := f.GetSheetName(0)

	if sheetName == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "导入的工作包文件不存在表格"))
		return
	}

	// 获取 Sheet1 上所有单元格
	rows, err := f.GetRows(sheetName)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "处理excel失败，请重试。"+err.Error()))
		return
	}
	if len(rows) < 2 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "导入的工作包文件没有有效内容"))
		return
	}

	templateHeaders1 := []string{"工作包", "负责人", "新增代码量", "移植代码量\n(正式)", "移植代码量\n(临时)", "总计代码量", "所属专业组", "项目需求", "PSTL"}
	templateHeaders2 := []string{"工作包", "负责人", "新增代码量", "移植代码量(正式)", "移植代码量(临时)", "总计代码量", "所属专业组", "项目需求", "PSTL"}

	for _, r := range rows[0] {
		if !libs.InArrayS(templateHeaders1, r) && !libs.InArrayS(templateHeaders2, r) {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "导入的工作包文件不是有效的文件"))
			return
		}
	}
	/*
		Name                        string `gorm:"not null; type:varchar(60)" json:"name"`
		ReleaseProjectID            uint   `gorm:"not null" json:"release_project_id"`
		CodeQuantity                uint   `gorm:"not null" json:"code_quantity"`
		PortedCodeQuantity          uint   `gorm:"not null" json:"ported_code_quantity"`
		TemporaryPortedCodeQuantity uint   `gorm:"not null" json:"temporary_ported_code_quantity"`
		TotalCodeQuantity           uint   `gorm:"not null" json:"total_code_quantity"`
		OwnerID                     uint   `grom:"not null" josn:"owner_id"`
	*/
	length := len(rows[0])
	var items []map[string]interface{}
	// var workGroups libs.KvMap[string, bool]
	for rowNum := range rows[1:] {
		rowData := []string{}
		var codeQuantity, portedCodeQuantity, temporaryPortedCodeQuantity, totalCodeQuantity float64
		for col := 0; col < length; col++ {
			cell := getCellRef(col, rowNum+1)
			value, _ := f.GetCellValue(sheetName, cell)
			rowData = append(rowData, value)
		}
		codeQuantity, err := strconv.ParseFloat(rowData[2], 32)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, "导入的工作包文件不是有效的文件"))
			return
		}
		portedCodeQuantity, err = strconv.ParseFloat(rowData[3], 32)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, "导入的工作包文件不是有效的文件"))
			return
		}
		temporaryPortedCodeQuantity, err = strconv.ParseFloat(rowData[4], 32)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, "导入的工作包文件不是有效的文件"))
			return
		}
		totalCodeQuantity, err = strconv.ParseFloat(rowData[5], 32)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, "导入的工作包文件不是有效的文件"))
			return
		}
		item := map[string]interface{}{
			"Name":                        rowData[0],
			"CodeQuantity":                codeQuantity,
			"PortedCodeQuantity":          portedCodeQuantity,
			"TotalCodeQuantity":           totalCodeQuantity,
			"TemporaryPortedCodeQuantity": temporaryPortedCodeQuantity,
			"User":                        rowData[1],
			"WorkGroup":                   rowData[6],
			"Requirement":                 rowData[7],
			"PSTL":                        strings.Replace(rowData[8], "@", "", -1),
		}
		// workGroups[rowData[6]] = true
		items = append(items, item)
	}

	if err := dmergerequestworkpackage.ImportWorkPackage(releaseProjectID, items); err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "导入数据失败，"+err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetIndex(templateHeaders1, templateHeaders2 []string, name string) int {
	for idx, h := range templateHeaders1 {
		if h == name {
			return idx
		}
	}
	for idx, h := range templateHeaders2 {
		if h == name {
			return idx
		}
	}
	return -1
}

func GetWorkPackages(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	ret, err := dmergerequestworkpackage.FindByReleaseProjectID(id, page, pageSize, sort, orderBy)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, ret, response.NoErr.Msg))
	return
}

func GetDistinctColumns(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	if len(name) == 0 {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	ret, err := dmergerequestworkpackage.GetDistinctColumns(id, name)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, ret, response.NoErr.Msg))
	return
}

func SyncWorkPackage() error {
	releaseProjects, err := dproject.FindAll()
	if err != nil {
		return err
	}
	for _, releaseProject := range releaseProjects {
		items := []map[string]interface{}{}
		workPackages, err := dpmsworkpacketinfo.FindAllEnabledByProjectName(releaseProject.Name)
		if err != nil {
			return err
		}

		for _, workPackage := range workPackages {
			var requestName string
			if workPackage.PmsRequest != nil {
				requestName = workPackage.PmsRequest.RequestName
			}

			item := map[string]interface{}{
				"Name":                        workPackage.WorkPacketName,
				"CodeQuantity":                workPackage.AddCodes,
				"PortedCodeQuantity":          workPackage.TransplantCodes,
				"TotalCodeQuantity":           workPackage.TotalCodes,
				"TemporaryPortedCodeQuantity": workPackage.TransplantCodesTmp,
				"User":                        workPackage.PacketManagerName,
				"WorkGroup":                   workPackage.GroupName,
				"Requirement":                 requestName,
				"PSTL":                        "",
			}
			// workGroups[rowData[6]] = true
			items = append(items, item)
		}
		if len(items) > 0 {
			if err := dmergerequestworkpackage.SyncWorkPackageFromPms(releaseProject.ID, items); err != nil {
				return err
			}
		}
	}
	return nil
}
