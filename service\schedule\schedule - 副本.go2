package schedule

import (
	"irisAdminApi/application/controllers/coverityresult"
	"irisAdminApi/application/controllers/release"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
)

func RunSchedule() {
	go UpdateUserCache()
	if libs.Config.Buildfarm.Enable {
		RunCheckQueue()
		RunCrontab()
	}

	if libs.Config.FileOut.Enable {
		go UpdateFileCache()
	}

	if libs.Config.FeatureRelease.Enable {
		RunRefreshSoftVersions()
	}

	if libs.Config.Feature.Enable {
		go ChecTaskskOverTime()
		go ChecFeaturekOverTime()
	}

	if libs.Config.BugSync.Enable {
		go RunBugSync()
	}

	if libs.Config.DataSync.Enable {
		go RunSync()
	}

	// if libs.Config.Debug {
	// 	logging.DebugLogger.Debugf("开始计算BUG超期时间")
	// 	dmergerequestbugsummary.UpdateOverTime()
	// 	logging.DebugLogger.Debugf("完成计算BUG超期时间")
	// }

	if libs.Config.CodeSync.Enable {
		go RunCodeSync()
	}
	if libs.Config.MergeRequest.Enable {
		go MergeRequestSechdule()
	}
	if libs.Config.Performance.Enable {
		go PerformanceSchedule()
	}
	if libs.Config.ProductionFileStorage.Enable {
		go CheckTaskskOverTime()
		go CheckProductionkOverTime()
	}
	// buildfarm.UpdateProjectInfo()
	if libs.Config.Resourcepool.Enable {
		go ResourcePoolSchedule()
	}
	if libs.Config.AccessLog.Enable {
		go RunAnalyzeLogs()   //日志文件分析
		go RunAccessLogData() //日志数据存储
	}
	if libs.Config.FeiShuDoc.Enable {
		go RunFeiShuData()                       //项目文档数据获取
		go RunUpdateFeiShuData()                 //更新项目数据
		go RunFeiShuDocumentCommentData()        //更新文档评论数据
		go RunFeiShuDocumentReviewStatus()       //获取云端评审状态数据
		go RunFeiShuDocumentReviewStatusRemind() //评审状态提醒
		go RunGetFeiShuOpenID()                  //更新用户OpenID数据
		go RunPMSData()                          //PMS数据
		go RunFeishuProjectData()                //补丁项目数据
	}

	if libs.Config.CoreDump.Enable {
		go CoreDumpKafka() //监听kafka消息
	}

	if libs.Config.CoveritySync.Enable {
		// coverityresult.SyncCoverityResultWorker()
		_, err := Cron.AddFunc("0 8 * * *", func() { coverityresult.SyncCoverityResultWorker() })
		if err != nil {
			logging.ErrorLogger.Error("add coverityresult cron err ", err)
		}
	}

	if libs.Config.Release.Enable {
		go release.ArchiveWorker()
		go release.RestartWorker()
		// release.SendTestLinkWorker()
		_, err := Cron.AddFunc("30 9 * * *", func() { release.SendTestLinkWorker() })
		if err != nil {
			logging.ErrorLogger.Error("add coverityresult cron err ", err)
		}
	}

	if libs.Config.QualityPoint.Enable {
		go RunQualityPointData()
	}

	// firewallflextrans.FileTest()
	// openfeishu.SyncLastWeekOriginalResourceData()
	// openfeishu.SyncLastWeekResourceDetailsData()
	// openfeishu.SyncActualCodeOutputData()
	// openfeishu.SyncAllUserTaskData()
	// openfeishu.SyncAllUserMRWorkpacketData()
	// qualitypoint.SyncQualityPointData()
	// openfeishu.TestToSendMesage()
	// openfeishu.TestOCR()
	// openfeishu.GetWorkItemData()
	// openfeishu.SyncAllBugDataV2("tbl9WQZVpeBXKPF1", "KmVRbOUQEa1MyIsDGGQcULohnMb")
	// openfeishu.TestUploadFile()
	// qualitypoint.SyncQualityPointUserData()
	// qualitypoint.SyncQualityPointsDeductionData()
	// qualitypoint.SyncQualityPointsRecoverieData()
}
