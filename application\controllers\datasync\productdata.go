package datasync

import (
	"encoding/json"
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/datasync/dproductdata"
	"strings"
)

type SyncProductDataFromBIApiResponse struct {
	Status int    `json:"status"`
	Err    string `json:"err"`
	Count  int    `json:"count"`
	Data   string `json:"data"`
}

/*
ID              uint   `json:"id"`
ProductId       string `json:"product_id"`       //产品ID
ItemNum         string `json:"item_num"`         //编号
ItemDesc        string `json:"item_desc"`        //描述
ItemAttType     string `json:"item_att_type"`    //类型
ProductLine     string `json:"product_line" `    //产品线
ProductFamily   string `json:"product_family"`   //产品系列
ProductCategory string `json:"product_category"` //产品类别
ProductSeries   string `json:"product_series" `  //产品系列号
ProductModel    string `json:"product_model" `   //产品型号
*/
type ProductData struct {
	ProductID       string `json:"ID"`
	ItemNum         string `json:"ITEM_NUM"`
	ItemDesc        string `json:"ITEM_DESC"`
	ProductLine     string `json:"PRODUCT_LINE"`
	ProductFamily   string `json:"PRODUCT_FAMILY"`
	ProductCategory string `json:"PRODUCT_CATEGORY"`
	ProductSeries   string `json:"PRODUCT_SERIES"`
	ItemAttType     string `json:"ITEM_ATT_TYPE"`
	ProductModel    string `json:"PRODUCT_MODEL"`
}

func SyncProductDataFromBIApi() {
	if libs.Config.FeatureRelease.BIApiUrl == "" {
		return
	}
	url := libs.Config.FeatureRelease.BIApiUrl + "/dprapi/BIAPI/GetReportData"
	headers := map[string]string{
		"sysid": "0ea45d606c6434cb26ea8b9688f0ea48",
	}
	page := 1
	pageSize := 1000

	for {
		data := map[string]string{
			"sysid":     "0ea45d606c6434cb26ea8b9688f0ea48",
			"tid":       "TL201",
			"where":     "PRODUCT_BUSINESS_CODE = '000029' and PRODUCT_CATEGORY='主机'",
			"PageSize":  fmt.Sprintf("%d", pageSize),
			"PageIndex": fmt.Sprintf("%d", page),
		}
		_result := SyncProductDataFromBIApiResponse{}
		resp, err := SyncClient.R().SetSuccessResult(&_result).SetBody(&data).SetHeaders(headers).Post(url)
		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("get ProductData error: %s", err.Error()))
			return
		}
		if resp.IsErrorState() {
			logging.ErrorLogger.Errorf(fmt.Sprintf("get ProductData error: %s", resp.String()))
			return
		}
		productDataString := strings.ReplaceAll(_result.Data, `\`, "")
		productData := []*ProductData{}
		err = json.Unmarshal([]byte(productDataString), &productData)
		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("unmarshal ProductData error: %s", err.Error()))
			return
		}

		objects := []map[string]interface{}{}
		for _, item := range productData {
			objects = append(objects, map[string]interface{}{
				"ProductId":       item.ProductID,
				"ItemNum":         item.ItemNum,
				"ItemDesc":        item.ItemDesc,
				"ProductLine":     item.ProductLine,
				"ProductFamily":   item.ProductFamily,
				"ProductCategory": item.ProductCategory,
				"ProductSeries":   item.ProductSeries,
				"ItemAttType":     item.ItemAttType,
				"ProductModel":    item.ProductModel,
			})
		}

		err = dproductdata.BatchCreate(objects)
		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("batch created ProductData error: %s", err.Error()))
			return
		}

		if page*pageSize >= _result.Count {
			break
		}
		page++
	}

	// _, result, err := libs.RespDataDeal(libs.PostJson(url, jsonStr, headers))
	// if err != nil {
	// 	return
	// }
	// //存入数据库中
	// if result["status"] != nil && result["status"].(float64) == 200 && result["count"].(float64) > 0 {
	// 	productDataList := []map[string]interface{}{}
	// 	err = json.Unmarshal([]byte(result["data"].(string)), &productDataList)
	// 	if err != nil {
	// 		return
	// 	}
	// 	for _, v := range productDataList {
	// 		if v["PRODUCT_MODEL"] != nil {
	// check := dproductdata.Response{}
	// 			err = check.FindEx("product_model", v["PRODUCT_MODEL"].(string))
	// 			if err != nil {
	// 				logging.ErrorLogger.Errorf(fmt.Sprintf("Error while find ProductData: %s", err.Error()))
	// 				return
	// 			}
	// 			if check.ID <= 0 {
	// 				productDataCreate := dproductdata.Response{}
	// 				err = productDataCreate.Create(map[string]interface{}{
	// 					"CreatedAt":       time.Now(),
	// 					"UpdatedAt":       time.Now(),
	// 					"ProductId":       v["ID"].(string),
	// 					"ItemNum":         v["ITEM_NUM"].(string),         //编号
	// 					"ItemDesc":        v["ITEM_DESC"].(string),        //描述
	// 					"ItemAttType":     v["ITEM_ATT_TYPE"].(string),    //类型
	// 					"ProductLine":     v["PRODUCT_LINE"].(string),     //产品线
	// 					"ProductFamily":   v["PRODUCT_FAMILY"].(string),   //产品系列
	// 					"ProductCategory": v["PRODUCT_CATEGORY"].(string), //产品类别
	// 					"ProductSeries":   v["PRODUCT_SERIES"].(string),   //产品系列号
	// 					"ProductModel":    v["PRODUCT_MODEL"].(string),    //产品型号
	// 				})
	// 				if err != nil {
	// 					logging.ErrorLogger.Errorf("create productData Error ", err)
	// 					return
	// 				}
	// 			}
	// 		}
	// 	}
	// }
}
