package mergerequest

import (
	"fmt"
	"time"

	"irisAdminApi/application/libs"

	"github.com/imroc/req/v3"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	"github.com/xanzy/go-gitlab"
)

var GitlabWebClient *req.Client

var BugWebClient *req.Client

var GoGitlabClient *gitlab.Client

var GitlabAIClient *req.Client

var CodeSyncClient *req.Client

func InitClient() {
	GitlabAIClient = req.C().
		SetCommonRetryCount(5).
		// Set the retry sleep interval with a commonly used algorithm: capped exponential backoff with jitter (https://aws.amazon.com/blogs/architecture/exponential-backoff-and-jitter/).
		SetCommonRetryBackoffInterval(1*time.Second, 5*time.Second).
		SetCommonQueryParam("private_token", libs.Config.Gitlab.AiToken).
		SetBaseURL(fmt.Sprintf("%s/api/%s/", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version)).
		AddCommonRetryCondition(func(resp *req.Response, err error) bool {
			return err != nil
		})
	GitlabWebClient = req.C().
		SetCommonRetryCount(3).
		// Set the retry sleep interval with a commonly used algorithm: capped exponential backoff with jitter (https://aws.amazon.com/blogs/architecture/exponential-backoff-and-jitter/).
		SetCommonRetryBackoffInterval(1*time.Second, 5*time.Second).
		AddCommonRetryCondition(func(resp *req.Response, err error) bool {
			if err == nil && libs.InArrayInt([]int{200, 201, 409}, resp.StatusCode) {
				return false
			}
			return err != nil
		})

	BugWebClient = req.C().
		SetCommonRetryCount(3).
		// Set the retry sleep interval with a commonly used algorithm: capped exponential backoff with jitter (https://aws.amazon.com/blogs/architecture/exponential-backoff-and-jitter/).
		SetCommonRetryBackoffInterval(1*time.Second, 5*time.Second).
		AddCommonRetryCondition(func(resp *req.Response, err error) bool {
			return err != nil
		}).
		SetCookieJar(nil)

	var err error
	GoGitlabClient, err = gitlab.NewClient(libs.Config.Gitlab.Token, gitlab.WithBaseURL(libs.Config.Gitlab.Url+"/api/v4"))
	if err != nil {
		panic(err)
	}

	CodeSyncClient = req.C().
		SetCommonRetryCount(3).
		// Set the retry sleep interval with a commonly used algorithm: capped exponential backoff with jitter (https://aws.amazon.com/blogs/architecture/exponential-backoff-and-jitter/).
		SetCommonRetryBackoffInterval(1*time.Second, 5*time.Second).
		AddCommonRetryCondition(func(resp *req.Response, err error) bool {
			return err != nil
		})
	if libs.Config.Debug {
		CodeSyncClient.DevMode()
	}
	// if libs.Config.Debug {
	// 	GitlabWebClient.DevMode()
	// 	BugWebClient.DevMode()
	// }
}

var GitlabApis = map[string]string{
	"fork_list":             "%s/api/%s/projects/%s/forks",
	"fork_create":           "%s/api/%s/projects/%s/fork",
	"project_merge_request": "%s/api/%s/projects/%s/merge_requests",
	"merge_request_commit":  "%s/api/%s/projects/%s/merge_requests/%s/commits",
}

type MergeRequestResponse struct {
	ID              int            `json:"id"`
	IID             int            `json:"iid"`
	ProjectID       int            `json:"project_id"`
	SourceProjectID int            `json:"source_project_id"`
	SourceBranch    string         `json:"source_branch"`
	TargetProjectID int            `json:"target_project_id"`
	TargetBranch    string         `json:"target_branch"`
	WebUrl          string         `json:"web_url"`
	Assignees       []UserResponse `json:"assignees"`
	Reviewers       []UserResponse `json:"reviewers"`
	Title           string         `json:"title"`
	Description     string         `json:"description"`
	State           string         `json:"state"`
	Author          UserResponse   `json:"author"`
	MergedAt        string         `json:"merged_at"`
	CreatedAt       string         `json:"created_at"`
	UpdatedAt       string         `json:"updated_at"`
	DiffRefs        DiffRefs       `json:"diff_refs"`
}

type UserResponse struct {
	ID       int    `json:"id"`
	Username string `json:"username"`
}

type CommitsResponse struct {
	ID         string    `json:"id"`
	ShortID    string    `json:"short_id"`
	Message    string    `json:"message"`
	CreatedAt  time.Time `json:"created_at"`
	AuthorName string    `json:"author_name"`
}

type ForkResponse struct {
	ID                int          `json:"id"`
	SshUrlToRepo      string       `json:"ssh_url_to_repo"`
	ImportStatus      string       `json:"import_status"`
	PathWithNamespace string       `json:"path_with_namespace"`
	Owner             UserResponse `json:"owner"`
}

type BranchResponse struct {
	Name   string          `json:"name"`
	Commit CommitsResponse `json:"commit"`
}

type MergeRequestListResponse []*MergeRequestListResponse

type ChangeResponse struct {
	NewPath string `json:"new_path"`
	Diff    string `json:"diff"`
}

type ChangesResponse struct {
	Changes []ChangeResponse `json:"changes"`
}

type MessageResponse struct {
	Message string `json:"message"`
}

type MemberResponse struct {
	ID          int    `json:"id"`
	Username    string `json:"username"`
	Name        string `json:"name"`
	AccessLevel uint   `json:"access_level"`
	State       string `json:"state"`
}

type DiscussionRequest struct {
	Body     string   `json:"body"`
	Author   Author   `json:"author"`
	Position Position `json:"position"`
	Resolved *bool    `json:"resolved,omitempty"`
}

type DiscussionResponse struct {
	ID             string              `json:"id"`
	IndividualNote bool                `json:"individual_note"`
	Notes          []DiscussionRequest `json:"notes"`
}

type DiffRefs struct {
	BaseSha  string `json:"base_sha"`
	HeadSha  string `json:"head_sha"`
	StartSha string `json:"start_sha"`
}

type Position struct {
	DiffRefs
	PositionType string `json:"position_type"`
	NewPath      string `json:"new_path"`
	OldPath      string `json:"old_path"`
	NewLine      uint   `json:"new_line,omitempty"`
	OldLine      uint   `json:"old_line,omitempty"`
	LineRange    struct {
		Start struct {
			LineCode string `json:"line_code"`
			Type     string `json:"type"`
		} `json:"start"`
		End struct {
			LineCode string `json:"line_code"`
			Type     string `json:"type"`
		} `json:"end"`
	} `json:"line_range,omitempty"`
}

type DifferentialResponse struct {
	Result struct {
		Data []struct {
			ID     int `json:"id"`
			Fields struct {
				Uri string `json:"uri"`
			} `json:"fields"`
		} `json:"data"`
		Cursor struct {
			Limit  string `json:"limit"`
			After  string `json:"after"`
			Before string `json:"before"`
		} `json:"cursor"`
	} `json:"result"`
}

var TypeMap = map[string]string{
	"new":    "新增",
	"bugfix": "BUG修订",
}

var MfalMap = map[uint]string{
	0: "否",
	1: "是",
}

var FeiShuClient = lark.NewClient("********************", "f9FupAX6aw65B6FLgyvNXbEywHq7keJF")

func init() {
}
