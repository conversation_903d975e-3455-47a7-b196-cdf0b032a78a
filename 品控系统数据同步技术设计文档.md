# 品控系统数据同步功能技术设计文档

## 更新日志

### 2025-01-17 - 品控单列表API功能实现

- ✅ **新增品控单列表API** - 实现 `GetQualityControlList` 和 `GetProcessedQualityControlList` 方法
- ✅ **数据处理和格式化** - 实现 `ProcessQualityControlData` 方法，支持字段映射和格式化
- ✅ **字段映射规则** - 实现品控责任人、问题关闭状态、超期情况、问题时效的自动映射
- ✅ **业务逻辑处理** - 基于JS代码逻辑实现时效计算和状态判断
- ✅ **数据结构扩展** - 新增 `ProcessedQualityControlItem` 和 `QualityControlListResponse` 结构体
- ✅ **API接口支持** - 支持分页获取品控单列表，参数包括 `page`, `rows`, `id`

## 1. 项目概述

### 1.1 功能需求

- **数据源**：品控系统 (<http://yfzl.ruijie.com.cn>) 和飞书多维表格
- **同步策略**：基于飞书表格中的"品控单号"字段进行增量数据更新（仅更新现有记录）
- **数据流程**：飞书表格索引构建 → 品控基础数据获取 → 闭环管理数据获取 → 现有记录更新
- **同步频率**：每天8点自动执行（集成到现有PMS数据同步任务）

### 1.2 技术目标

- 复用现有PMS爬虫架构和定时任务系统
- 实现三层数据获取：飞书索引→品控→闭环管理
- 支持条件判断的闭环数据获取逻辑
- 基于品控单号的精确匹配更新机制
- 提供完善的错误处理和监控机制

### 1.3 业务价值

- 自动化品控数据状态更新，提高工作效率
- 实时同步闭环管理状态，支持决策分析
- 统一数据展示平台，便于跨部门协作
- 保持数据完整性，避免重复记录

## 2. 系统架构设计

### 2.1 整体架构扩展方案

```
现有品控系统集成架构
├── 定时任务调度层 (service/schedule/feishu.go)
├── Redis缓存层 (新增 - Cookie持久化缓存)
│   ├── QualityControlRedisCookieKey: "quality_control_cookies"
│   ├── 6小时有效期管理
│   └── 多实例部署支持
├── 智能认证服务层 (已实现 - qualitycontrol_api.go)
│   ├── 浏览器管理 (Rod浏览器自动化)
│   ├── SSO智能登录 (多选择器支持)
│   ├── Cookie提取和验证
│   ├── 认证失败自动检测和恢复
│   └── 系统诊断功能
├── 飞书数据索引层 (新增 - 构建品控单号映射)
│   ├── 分页查询飞书多维表格
│   ├── 品控单号索引构建
│   └── 记录ID映射管理
├── 品控API调用层 (已实现 - 增强版)
│   ├── 品控单列表API (新增 - GetQualityControlList)
│   ├── 数据处理和格式化 (新增 - ProcessQualityControlData)
│   ├── 智能重试机制
│   ├── HTML/JSON响应检测
│   ├── 批量数据获取
│   └── 详细调试日志
├── 闭环管理数据层 (新增 - 条件判断和数据获取)
│   ├── 故障等级条件判断
│   ├── 闭环管理API调用
│   ├── 闭环状态解析
│   └── 业务规则引擎
├── 数据处理转换层 (新增 - 业务逻辑处理)
│   ├── 字段映射和转换
│   ├── 复杂业务逻辑处理
│   ├── 数据验证和清洗
│   └── 批量数据组装
└── 飞书更新层 (已实现 - qualitycontrol_feishu.go)
    ├── UpdateFeishuRecordByFieldNames (单条更新)
    ├── BatchUpdateFeishuRecordsByFieldNames (批量更新)
    ├── 字段名称直接支持
    ├── 类型安全转换
    └── 完善错误处理
```

### 2.2 数据流设计

```mermaid
graph TD
    A[定时任务触发] --> B[系统诊断检查]
    B --> C[Redis缓存检查]
    C --> D{Cookie有效?}
    D -->|是| E[飞书表格分页查询]
    D -->|否| F[智能SSO登录]
    F --> G[Cookie提取和验证]
    G --> H[保存到Redis缓存]
    H --> E
    E --> I[构建品控单号索引]
    I --> J[批量获取品控基础数据]
    J --> K[故障等级条件判断]
    K --> L{需要闭环数据?}
    L -->|是| M[获取闭环管理数据]
    L -->|否| N[数据处理转换]
    M --> N
    N --> O[字段映射和验证]
    O --> P[批量更新飞书记录]
    P --> Q[同步统计和日志]
    Q --> R[完成同步]

    style F fill:#f9f,stroke:#333,stroke-width:2px
    style H fill:#bbf,stroke:#333,stroke-width:2px
    style P fill:#bfb,stroke:#333,stroke-width:2px
```

### 2.3 智能认证流程设计

```mermaid
graph TD
    A[开始认证] --> B[检查Redis缓存]
    B --> C{Cookie存在且有效?}
    C -->|是| D[加载Cookie到内存]
    C -->|否| E[启动Rod浏览器]
    E --> F[访问品控系统登录页面]
    F --> G{检测到SSO页面?}
    G -->|是| H[智能SSO登录]
    G -->|否| I[已登录状态检查]
    H --> J[多选择器元素查找]
    J --> K[输入用户名密码]
    K --> L[点击登录按钮]
    L --> M[等待页面跳转]
    M --> N{登录成功?}
    N -->|否| O[错误信息提取]
    N -->|是| P[提取Cookie]
    O --> Q[登录失败处理]
    P --> R[Cookie验证]
    R --> S[保存到Redis缓存]
    S --> T[认证完成]
    I --> T
    D --> T
    Q --> U[认证失败]

    style H fill:#f9f,stroke:#333,stroke-width:2px
    style S fill:#bbf,stroke:#333,stroke-width:2px
    style T fill:#bfb,stroke:#333,stroke-width:2px
```

## 3. 核心功能模块

### 3.1 文件结构设计

```
application/controllers/openfeishu/
├── qualitycontrol.go              // 主入口函数和流程控制
├── qualitycontrol_data.go         // 数据结构定义
├── qualitycontrol_feishu.go       // 飞书数据获取和索引构建模块
├── qualitycontrol_api.go          // 品控系统API调用模块
├── qualitycontrol_closed.go       // 闭环管理数据获取模块 (新增)
└── qualitycontrol_processor.go    // 数据处理和同步逻辑 (新增)

service/schedule/feishu.go          // 定时任务调度集成
```

### 3.2 闭环管理数据获取模块设计

#### 3.2.1 闭环管理模块架构

```mermaid
graph TD
    A[品控基础数据] --> B[故障等级判断]
    B --> C{需要闭环数据?}
    C -->|一级故障| D[获取四个根因数据]
    C -->|二级故障| E[条件判断]
    C -->|三级故障| F[条件判断]
    C -->|其他| G[标记无需闭环]
    E --> H{非客户/环境原因?}
    H -->|是| D
    H -->|否| G
    F --> I{软件/硬件问题?}
    I -->|是| J[获取技术根因数据]
    I -->|否| G
    D --> K[闭环管理API调用]
    J --> K
    K --> L[闭环状态解析]
    L --> M[闭环结果判断]
    M --> N[返回闭环状态]
    G --> O[返回无需闭环]

    style D fill:#f9f,stroke:#333,stroke-width:2px
    style J fill:#f9f,stroke:#333,stroke-width:2px
    style K fill:#bbf,stroke:#333,stroke-width:2px
```

#### 3.2.2 闭环判断业务规则引擎

```go
// 闭环判断规则配置
type ClosedLoopRule struct {
    FaultLevel      string   `json:"fault_level"`      // 故障等级
    RequiredReasons []string `json:"required_reasons"` // 需要闭环的故障原因
    ClosedType      string   `json:"closed_type"`      // 闭环类型：four_causes/tech_cause/none
    Description     string   `json:"description"`      // 规则描述
}

// 闭环规则配置
var ClosedLoopRules = []ClosedLoopRule{
    {
        FaultLevel:      "一级故障",
        RequiredReasons: []string{"*"}, // 所有原因都需要闭环
        ClosedType:      "four_causes",
        Description:     "一级故障需要填写四个根因",
    },
    {
        FaultLevel:      "二级故障",
        RequiredReasons: []string{"软件问题", "硬件问题", "设计问题", "流程问题"},
        ClosedType:      "four_causes",
        Description:     "二级故障非客户/环境原因需要填写四个根因",
    },
    {
        FaultLevel:      "三级故障",
        RequiredReasons: []string{"软件问题", "硬件问题"},
        ClosedType:      "tech_cause",
        Description:     "三级故障软件/硬件问题需要填写技术根因",
    },
}

// 智能闭环判断引擎
func DetermineClosedLoopRequirement(faultLevel, devFaultReason string) (bool, string, string) {
    for _, rule := range ClosedLoopRules {
        if rule.FaultLevel == faultLevel {
            // 检查是否匹配故障原因
            if contains(rule.RequiredReasons, "*") || contains(rule.RequiredReasons, devFaultReason) {
                return true, rule.ClosedType, rule.Description
            }
        }
    }
    return false, "none", "无需闭环"
}
```

#### 3.2.3 闭环管理API调用设计

```go
// 闭环管理API客户端
type ClosedLoopAPIClient struct {
    baseURL    string
    httpClient *http.Client
    cookies    []*http.Cookie
}

// 闭环数据获取接口
func (c *ClosedLoopAPIClient) GetClosedLoopData(qualityNumber string) (*ClosedLoopResponse, error) {
    // API端点构建
    apiURL := fmt.Sprintf("%s/api/closed-loop/quality/%s", c.baseURL, qualityNumber)

    // 创建HTTP请求
    req, err := http.NewRequest("GET", apiURL, nil)
    if err != nil {
        return nil, fmt.Errorf("创建请求失败: %v", err)
    }

    // 添加认证Cookie
    c.addCookiesToRequest(req)

    // 添加请求头
    req.Header.Set("Accept", "application/json")
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("X-Requested-With", "XMLHttpRequest")

    // 发送请求
    resp, err := c.httpClient.Do(req)
    if err != nil {
        return nil, fmt.Errorf("发送请求失败: %v", err)
    }
    defer resp.Body.Close()

    // 读取响应
    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return nil, fmt.Errorf("读取响应失败: %v", err)
    }

    // 检查响应状态
    if resp.StatusCode != http.StatusOK {
        return nil, fmt.Errorf("API请求失败，状态码: %d，响应: %s", resp.StatusCode, string(body))
    }

    // 解析JSON响应
    var closedLoopResp ClosedLoopResponse
    err = json.Unmarshal(body, &closedLoopResp)
    if err != nil {
        return nil, fmt.Errorf("解析JSON响应失败: %v", err)
    }

    return &closedLoopResp, nil
}

// 批量获取闭环数据
func (c *ClosedLoopAPIClient) BatchGetClosedLoopData(qualityNumbers []string) (map[string]*ClosedLoopResponse, error) {
    results := make(map[string]*ClosedLoopResponse)
    errors := make([]string, 0)

    for _, qualityNumber := range qualityNumbers {
        closedLoopData, err := c.GetClosedLoopData(qualityNumber)
        if err != nil {
            errors = append(errors, fmt.Sprintf("品控单号 %s: %v", qualityNumber, err))
            logging.ErrorLogger.Errorf("获取闭环数据失败 (品控单号: %s): %v", qualityNumber, err)
            continue
        }

        results[qualityNumber] = closedLoopData
        logging.InfoLogger.Infof("成功获取闭环数据 (品控单号: %s)", qualityNumber)
    }

    if len(errors) > 0 {
        logging.ErrorLogger.Errorf("批量获取闭环数据部分失败: %v", errors)
    }

    logging.InfoLogger.Infof("批量获取闭环数据完成，成功: %d，失败: %d", len(results), len(errors))
    return results, nil
}
```

### 3.3 数据结构定义

```go
// 飞书表格查询响应结构
type FeishuTableResponse struct {
    Code int `json:"code"`
    Data struct {
        HasMore   bool                   `json:"has_more"`
        Items     []FeishuTableItem     `json:"items"`
        PageToken string                `json:"page_token"`
        Total     int                   `json:"total"`
    } `json:"data"`
    Msg string `json:"msg"`
}

type FeishuTableItem struct {
    Fields   map[string]interface{} `json:"fields"`
    RecordID string                 `json:"record_id"`
}

// 品控单号索引结构
type QualityNumberIndex struct {
    QualityNumber string `json:"quality_number"`
    RecordID      string `json:"record_id"`
}

// 品控API响应结构
type QualityControlResponse struct {
    Rows  []QualityControlItem `json:"rows"`
    Total int                  `json:"total"`
}

type QualityControlItem struct {
    ID                    int     `json:"id"`                    // 品控单号
    FaultUserName        string  `json:"faultUserName"`         // 故障责任人
    PzkzStatus           string  `json:"pzkzStatus"`            // 品控状态
    TimeDay              float64 `json:"timeDay"`               // 超期天数
    DealDay              float64 `json:"dealDay"`               // 处理天数
    FaultLvlBaseName     string  `json:"faultLvlBaseName"`      // 故障等级
    DevFaultReasonName   string  `json:"devFaultReasonName"`    // 研发故障原因
    SolutionDate         string  `json:"solutionDate"`          // 解决方案时间
    IsFinish             string  `json:"isFinish"`              // 是否完成
    CreateDate           string  `json:"createDate"`            // 创建时间
    FaultLvlTime         string  `json:"faultLvlTime"`          // 故障等级时间
    FaultLvlBaseTime     string  `json:"faultLvlBaseTime"`      // 基础故障等级时间
    ExtendHour           int     `json:"extendHour"`            // 延期小时
    EndDate              string  `json:"endDate"`               // 结束时间
}

// 处理后的品控数据项 (新增)
type ProcessedQualityControlItem struct {
    QualityControlItem              // 嵌入原始数据
    QualityResponsiblePerson string `json:"quality_responsible_person"` // 品控责任人
    IsQualityProblemClosed   string `json:"is_quality_problem_closed"`  // 品控问题是否关闭
    OverdueStatus            string `json:"overdue_status"`             // 超期情况
    ProblemTimeliness        string `json:"problem_timeliness"`         // 问题时效
}

// 品控单列表响应结构 (新增)
type QualityControlListResponse struct {
    Rows  []QualityControlItem `json:"rows"`  // 品控单列表
    Total int                  `json:"total"` // 总数
}

// 闭环管理API响应结构
type ClosedLoopResponse struct {
    Type string `json:"type"`
    Data struct {
        ID                           int    `json:"id"`
        EntityID                     int    `json:"entityId"`
        EntityType                   string `json:"entityType"`
        RootCauseDescription         string `json:"rootCauseDescription"`
        RootCauseType               int    `json:"rootCauseType"`
        PreventMeasure              string `json:"preventMeasure"`
        PreventUserId               int    `json:"preventUserId"`
        PreventUserName             string `json:"preventUserName"`
        PreventUserGroupLeaderId    int    `json:"preventUserGroupLeaderId"`
        PreventUserGroupLeaderName  string `json:"preventUserGroupLeaderName"`
        PreventUserDeptLeaderId     int    `json:"preventUserDeptLeaderId"`
        PreventUserDeptLeaderName   string `json:"preventUserDeptLeaderName"`
        PlanClosedDate              string `json:"planClosedDate"`
        ActClosedDate               string `json:"actClosedDate"`
        ClosedStatus                string `json:"closedStatus"`
        ClosedLevel                 string `json:"closedLevel"`
        ClosedEvidence              string `json:"closedEvidence"`
        DeliverableName             string `json:"deliverableName"`
        RootCauseCheck              string `json:"rootCauseCheck"`
        PreventMeasureCheck         string `json:"preventMeasureCheck"`
        PreventMeasureValidityCheck string `json:"preventMeasureValidityCheck"`
        CreateDate                  string `json:"createDate"`
        ModifyDate                  string `json:"modifyDate"`
        AnalysisStatus              string `json:"analysisStatus"`
    } `json:"data"`
    Content string `json:"content"`
}

// 飞书更新数据结构
type FeishuQualityUpdateRecord struct {
    QualityResponsible  string `json:"品控责任人"`
    IsClosed           string `json:"品控问题是否关闭"`
    OverdueStatus      string `json:"超期情况"`
    ProblemEfficiency  string `json:"问题时效"`
    IsClosedLoop       string `json:"品控问题是否闭环"`
}

// 同步统计结构
type SyncStatistics struct {
    TotalRecords      int `json:"total_records"`       // 飞书表格总记录数
    MatchedRecords    int `json:"matched_records"`     // 成功匹配的记录数
    UpdatedRecords    int `json:"updated_records"`     // 成功更新的记录数
    SkippedRecords    int `json:"skipped_records"`     // 跳过的记录数
    ErrorRecords      int `json:"error_records"`       // 错误记录数
}
```

### 3.4 飞书多维表格更新功能集成

#### 3.4.1 已实现的飞书更新功能

基于 `qualitycontrol_feishu.go` 中已实现的功能：

```go
// 品控系统字段映射配置 (已实现)
var QualityControlFieldMappings = map[string]FeishuFieldMapping{
    "品控单号": {
        FieldID:   "fldUnWJxQK",
        FieldName: "品控单号",
        Type:      1,  // Text
        UIType:    "Text",
    },
    "品控问题是否关闭": {
        FieldID:   "fld7cG2Sod",
        FieldName: "品控问题是否关闭",
        Type:      3,  // SingleSelect
        UIType:    "SingleSelect",
    },
    "品控问题是否闭环": {
        FieldID:   "fldCLV0fjf",
        FieldName: "品控问题是否闭环",
        Type:      3,  // SingleSelect
        UIType:    "SingleSelect",
    },
    "品控责任人": {
        FieldID:   "fldVh6NMku",
        FieldName: "品控责任人",
        Type:      11, // User
        UIType:    "User",
    },
    "超期情况": {
        FieldID:   "fldZfrEHoB",
        FieldName: "超期情况",
        Type:      1,  // Text
        UIType:    "Text",
    },
    "问题时效": {
        FieldID:   "fld7Xnswn4",
        FieldName: "问题时效",
        Type:      2,  // Number
        UIType:    "Number",
    },
}

// 单选字段选项映射 (已实现)
var SingleSelectOptions = map[string]map[string]string{
    "品控问题是否关闭": {
        "是": "optbya0XdU",
        "否": "optEe3D8Uh",
    },
    "品控问题是否闭环": {
        "已闭环": "optix8lQwT",
        "未闭环": "optAV9JzTC",
        "无需闭环": "optifjbt5i",
    },
}
```

#### 3.4.2 品控数据同步专用更新接口

```go
// 品控数据同步更新请求结构
type QualityControlSyncRequest struct {
    RecordID string                 `json:"record_id"`
    Fields   map[string]interface{} `json:"fields"`
}

// 品控数据批量同步接口
func BatchSyncQualityControlToFeishu(updates []QualityControlSyncRequest) (*SyncStatistics, error) {
    logging.InfoLogger.Infof("开始批量同步品控数据到飞书，共 %d 条记录", len(updates))

    // 转换为飞书更新格式
    feishuUpdates := make([]FeishuRecordUpdateRequest, 0, len(updates))
    for _, update := range updates {
        feishuUpdate := FeishuRecordUpdateRequest{
            RecordID: update.RecordID,
            Fields:   update.Fields,
        }
        feishuUpdates = append(feishuUpdates, feishuUpdate)
    }

    // 调用已实现的批量更新功能
    err := BatchUpdateFeishuRecordsByFieldNames(feishuUpdates)
    if err != nil {
        return nil, fmt.Errorf("批量更新飞书记录失败: %v", err)
    }

    // 构建同步统计
    stats := &SyncStatistics{
        TotalRecords:   len(updates),
        MatchedRecords: len(updates),
        UpdatedRecords: len(updates), // 假设全部成功，实际应该从BatchUpdate返回
        SkippedRecords: 0,
        ErrorRecords:   0,
    }

    logging.InfoLogger.Infof("品控数据批量同步完成: %+v", stats)
    return stats, nil
}

// 单条品控数据同步接口
func SyncSingleQualityControlToFeishu(recordID string, fields map[string]interface{}) error {
    logging.InfoLogger.Infof("同步单条品控数据到飞书: %s", recordID)

    // 调用已实现的单条更新功能
    err := UpdateFeishuRecordByFieldNames(recordID, fields)
    if err != nil {
        return fmt.Errorf("更新飞书记录失败: %v", err)
    }

    logging.InfoLogger.Infof("单条品控数据同步成功: %s", recordID)
    return nil
}
```

#### 3.4.3 数据转换和验证

```go
// 品控数据到飞书字段转换
func ConvertQualityDataToFeishuFields(item QualityControlItem, closedLoopData *ClosedLoopResponse) (map[string]interface{}, error) {
    fields := make(map[string]interface{})

    // 品控责任人 (User类型)
    if item.FaultUserName != "" {
        fields["品控责任人"] = item.FaultUserName
    }

    // 品控问题是否关闭 (SingleSelect类型)
    if item.PzkzStatus == "问题关闭" || item.PzkzStatus == "废弃" {
        fields["品控问题是否关闭"] = "是"
    } else {
        fields["品控问题是否关闭"] = "否"
    }

    // 超期情况 (Text类型)
    fields["超期情况"] = formatTimeDay(item.TimeDay, item)

    // 问题时效 (Number类型)
    fields["问题时效"] = int(item.DealDay)

    // 品控问题是否闭环 (SingleSelect类型) - 基于闭环数据判断
    closedLoopStatus := determineClosedLoopStatus(item.FaultLvlBaseName, item.DevFaultReasonName, closedLoopData)
    fields["品控问题是否闭环"] = closedLoopStatus

    return fields, nil
}

// 批量数据转换
func BatchConvertQualityDataToFeishuFields(items []QualityControlItem, closedLoopDataMap map[string]*ClosedLoopResponse) (map[string]map[string]interface{}, error) {
    results := make(map[string]map[string]interface{})

    for _, item := range items {
        qualityNumber := fmt.Sprintf("%d", item.ID)
        closedLoopData := closedLoopDataMap[qualityNumber]

        fields, err := ConvertQualityDataToFeishuFields(item, closedLoopData)
        if err != nil {
            logging.ErrorLogger.Errorf("转换品控数据失败 (品控单号: %s): %v", qualityNumber, err)
            continue
        }

        results[qualityNumber] = fields
    }

    logging.InfoLogger.Infof("批量数据转换完成，成功转换 %d 条记录", len(results))
    return results, nil
}
```

### 3.5 接口设计

```go
// 主入口函数
func SyncQualityControlData() error

// 系统诊断和监控 (已实现)
func (api *QualityControlAPI) DiagnoseSystem() error
func (api *QualityControlAPI) ClearAllCache() error
func (api *QualityControlAPI) TestConnection() error

// 智能认证服务 (已实现)
func (api *QualityControlAPI) loginQualitySystem() error
func (api *QualityControlAPI) performSSOLogin() error
func (api *QualityControlAPI) loadCookiesFromCache()
func (api *QualityControlAPI) saveCookiesToCache()

// 飞书数据获取和索引构建
func getFeishuQualityRecords() ([]FeishuTableItem, error)
func buildQualityNumberIndex(items []FeishuTableItem) (map[string]string, error)
func extractQualityNumbers(indexMap map[string]string) []string

// 品控系统数据获取 (已实现增强版)
func (api *QualityControlAPI) GetQualityControlData(qualityNumbers []string) ([]QualityControlItem, error)
func (api *QualityControlAPI) fetchQualityControlData(qualityNumbers []string) ([]QualityControlItem, error)

// 品控单列表API (新增)
func (api *QualityControlAPI) GetQualityControlList(page, rows int, id string) (*QualityControlListResponse, error)
func (api *QualityControlAPI) GetProcessedQualityControlList(page, rows int, id string) ([]ProcessedQualityControlItem, error)
func (api *QualityControlAPI) ProcessQualityControlData(items []QualityControlItem) []ProcessedQualityControlItem

// 闭环管理数据获取 (新增)
func DetermineClosedLoopRequirement(faultLevel, devFaultReason string) (bool, string, string)
func (c *ClosedLoopAPIClient) GetClosedLoopData(qualityNumber string) (*ClosedLoopResponse, error)
func (c *ClosedLoopAPIClient) BatchGetClosedLoopData(qualityNumbers []string) (map[string]*ClosedLoopResponse, error)

// 数据处理转换 (新增)
func ConvertQualityDataToFeishuFields(item QualityControlItem, closedLoopData *ClosedLoopResponse) (map[string]interface{}, error)
func BatchConvertQualityDataToFeishuFields(items []QualityControlItem, closedLoopDataMap map[string]*ClosedLoopResponse) (map[string]map[string]interface{}, error)

// 飞书数据更新 (已实现)
func UpdateFeishuRecordByFieldNames(recordID string, fieldsData map[string]interface{}) error
func BatchUpdateFeishuRecordsByFieldNames(records []FeishuRecordUpdateRequest) error
func BatchSyncQualityControlToFeishu(updates []QualityControlSyncRequest) (*SyncStatistics, error)

// 统计和日志
func logSyncStatistics(stats *SyncStatistics)
func logQualityControlSync(stage string, details interface{})
```

### 3.6 监控和诊断功能设计

#### 3.6.1 系统健康监控

```go
// 系统健康检查接口
type HealthChecker interface {
    CheckRedisConnection() error
    CheckFeishuAPI() error
    CheckQualityControlAPI() error
    CheckClosedLoopAPI() error
    GenerateHealthReport() *HealthReport
}

// 健康检查报告
type HealthReport struct {
    Timestamp       time.Time            `json:"timestamp"`
    OverallStatus   string               `json:"overall_status"`   // healthy/warning/critical
    ComponentStatus map[string]Component `json:"component_status"`
    Recommendations []string             `json:"recommendations"`
}

type Component struct {
    Status      string        `json:"status"`       // healthy/warning/critical
    ResponseTime time.Duration `json:"response_time"`
    LastError   string        `json:"last_error"`
    Details     interface{}   `json:"details"`
}

// 实现健康检查
func (api *QualityControlAPI) GenerateHealthReport() *HealthReport {
    report := &HealthReport{
        Timestamp:       time.Now(),
        ComponentStatus: make(map[string]Component),
        Recommendations: make([]string, 0),
    }

    // Redis连接检查
    redisStatus := api.checkRedisHealth()
    report.ComponentStatus["redis"] = redisStatus

    // 飞书API检查
    feishuStatus := api.checkFeishuHealth()
    report.ComponentStatus["feishu"] = feishuStatus

    // 品控系统API检查
    qualityStatus := api.checkQualityControlHealth()
    report.ComponentStatus["quality_control"] = qualityStatus

    // 闭环管理API检查
    closedLoopStatus := api.checkClosedLoopHealth()
    report.ComponentStatus["closed_loop"] = closedLoopStatus

    // 确定整体状态
    report.OverallStatus = api.determineOverallStatus(report.ComponentStatus)

    // 生成建议
    report.Recommendations = api.generateRecommendations(report.ComponentStatus)

    return report
}
```

#### 3.6.2 性能监控指标

```go
// 性能监控指标
type PerformanceMetrics struct {
    SyncDuration        time.Duration `json:"sync_duration"`         // 同步总耗时
    LoginDuration       time.Duration `json:"login_duration"`        // 登录耗时
    DataFetchDuration   time.Duration `json:"data_fetch_duration"`   // 数据获取耗时
    ProcessDuration     time.Duration `json:"process_duration"`      // 数据处理耗时
    UpdateDuration      time.Duration `json:"update_duration"`       // 更新耗时

    TotalRecords        int           `json:"total_records"`          // 总记录数
    ProcessedRecords    int           `json:"processed_records"`      // 处理记录数
    UpdatedRecords      int           `json:"updated_records"`        // 更新记录数
    ErrorRecords        int           `json:"error_records"`          // 错误记录数

    CacheHitRate        float64       `json:"cache_hit_rate"`         // 缓存命中率
    APISuccessRate      float64       `json:"api_success_rate"`       // API成功率

    MemoryUsage         int64         `json:"memory_usage"`           // 内存使用量
    CPUUsage            float64       `json:"cpu_usage"`              // CPU使用率
}

// 性能监控收集器
type MetricsCollector struct {
    startTime   time.Time
    metrics     *PerformanceMetrics
    checkpoints map[string]time.Time
}

func NewMetricsCollector() *MetricsCollector {
    return &MetricsCollector{
        startTime:   time.Now(),
        metrics:     &PerformanceMetrics{},
        checkpoints: make(map[string]time.Time),
    }
}

func (mc *MetricsCollector) Checkpoint(name string) {
    mc.checkpoints[name] = time.Now()
}

func (mc *MetricsCollector) CalculateDuration(from, to string) time.Duration {
    fromTime, fromExists := mc.checkpoints[from]
    toTime, toExists := mc.checkpoints[to]

    if !fromExists {
        fromTime = mc.startTime
    }
    if !toExists {
        toTime = time.Now()
    }

    return toTime.Sub(fromTime)
}

func (mc *MetricsCollector) GenerateReport() *PerformanceMetrics {
    mc.metrics.SyncDuration = time.Since(mc.startTime)
    mc.metrics.LoginDuration = mc.CalculateDuration("start", "login_complete")
    mc.metrics.DataFetchDuration = mc.CalculateDuration("login_complete", "data_fetch_complete")
    mc.metrics.ProcessDuration = mc.CalculateDuration("data_fetch_complete", "process_complete")
    mc.metrics.UpdateDuration = mc.CalculateDuration("process_complete", "update_complete")

    return mc.metrics
}
```

## 4. 数据字段映射规范

### 4.1 飞书表格配置

- **App Token**: `Dtl6bmYfyauc0VsNDNbcBsH3n7d`
- **Table ID**: `tblyBol3c07mhW1T`
- **关键字段**: `品控单号` (用于数据匹配)
- **更新策略**: 仅更新现有记录，不新增记录

### 4.2 基础字段映射

| 飞书字段 | API字段 | 处理逻辑 |
|---------|---------|----------|
| 品控责任人 | faultUserName | 直接映射 |
| 品控问题是否关闭 | pzkzStatus | 条件判断：问题关闭/废弃→"是"，其他→"否" |
| 超期情况 | timeDay | 复杂格式化逻辑 |
| 问题时效 | dealDay | 复杂格式化逻辑 |
| 品控问题是否闭环 | 闭环管理数据 | 基于故障等级和研发故障原因的条件判断 |

### 4.2.1 品控单列表API字段映射 (新增)

**API接口**: `http://yfzl.ruijie.com.cn/pzkz/load_pzkz_list?disabled=0&isPzkz=1`

| 处理后字段 | 原始字段 | 映射规则 | 示例 |
|-----------|----------|----------|------|
| QualityResponsiblePerson | faultUserName | 直接映射故障责任人 | "陈馨怡1" |
| IsQualityProblemClosed | pzkzStatus | "问题关闭"或"废弃"→"是"，其他→"否" | "是" |
| OverdueStatus | timeDay | 格式化为"X.X天" | "15.0天" |
| ProblemTimeliness | dealDay | 基于JS逻辑格式化 | "提前解决(12.1天)" |

**处理逻辑详细说明**:

```go
// 品控问题是否关闭判断
func (api *QualityControlAPI) formatQualityProblemStatus(pzkzStatus string) string {
    if pzkzStatus == "问题关闭" || pzkzStatus == "废弃" {
        return "是"
    }
    return "否"
}

// 问题时效格式化 (基于JS代码逻辑)
func (api *QualityControlAPI) formatDealDay(dealDay float64, item QualityControlItem) string {
    if item.PzkzStatus == "废弃" {
        return "-"
    }
    if dealDay > 0 {
        if item.SolutionDate != "" || item.IsFinish == 1 {
            return fmt.Sprintf("提前解决(%.1f天)", dealDay)
        } else {
            return fmt.Sprintf("剩余%.1f天", dealDay)
        }
    }
    return fmt.Sprintf("超期%.1f天", -dealDay)
}
```

### 4.3 索引构建逻辑

#### 4.3.1 品控单号索引构建

```go
func buildQualityNumberIndex(items []FeishuTableItem) (map[string]string, error) {
    indexMap := make(map[string]string)
    
    for _, item := range items {
        if qualityNumber, exists := item.Fields["品控单号"]; exists {
            if qnStr, ok := qualityNumber.(string); ok && qnStr != "" {
                indexMap[qnStr] = item.RecordID
            }
        }
    }
    
    return indexMap, nil
}
```

#### 4.3.2 记录匹配和更新逻辑

```go
func updateExistingRecords(updates map[string]FeishuQualityUpdateRecord, indexMap map[string]string) (*SyncStatistics, error) {
    stats := &SyncStatistics{
        TotalRecords: len(indexMap),
    }
    
    for qualityNumber, updateData := range updates {
        if recordID, exists := indexMap[qualityNumber]; exists {
            stats.MatchedRecords++
            
            // 执行更新操作
            err := UpdateTableRecordByID(tableID, appToken, recordID, updateData)
            if err != nil {
                stats.ErrorRecords++
                logging.ErrorLogger.Errorf("更新品控单号 %s 失败: %s", qualityNumber, err.Error())
                continue
            }
            
            stats.UpdatedRecords++
        } else {
            stats.SkippedRecords++
            logging.DebugLogger.Debugf("品控单号 %s 在飞书表格中不存在，跳过更新", qualityNumber)
        }
    }
    
    return stats, nil
}
```

### 4.4 闭环判断逻辑

#### 4.4.1 条件判断规则

```go
func shouldFetchClosedLoop(faultLevel, devFaultReason string) bool {
    switch faultLevel {
    case "三级故障":
        return devFaultReason == "软件问题" || devFaultReason == "硬件问题"
    case "二级故障":
        return devFaultReason != "客户原因" && devFaultReason != "环境问题"
    case "一级故障":
        return true
    default:
        return false
    }
}
```

#### 4.4.2 闭环状态判断

```go
func determineClosedLoopStatus(faultLevel, devFaultReason string, closedData *ClosedLoopResponse) string {
    if !shouldFetchClosedLoop(faultLevel, devFaultReason) {
        return "无需闭环"
    }
    
    if closedData == nil {
        return "需要填写闭环信息"
    }
    
    switch faultLevel {
    case "三级故障":
        if closedData.Data.RootCauseCheck == "通过" {
            return "技术根因已闭环"
        }
        return "需要填写技术根因"
    case "二级故障", "一级故障":
        if closedData.Data.RootCauseCheck == "通过" && 
           closedData.Data.PreventMeasureCheck == "通过" && 
           closedData.Data.PreventMeasureValidityCheck == "通过" && 
           closedData.Data.ClosedStatus == "已闭环" {
            return "四个根因已闭环"
        }
        return "需要填写四个根因"
    default:
        return "未知状态"
    }
}
```

### 4.5 复杂业务逻辑处理

#### 4.5.1 超期情况处理 (formatTimeDay)

```go
func formatTimeDay(value float64, item QualityControlItem) string {
    if value > 0 {
        return fmt.Sprintf("超期%.0f天", value)
    } else if value == 0 {
        return "正常"
    } else {
        return fmt.Sprintf("剩余%.0f天", math.Abs(value))
    }
}
```

#### 4.5.2 问题时效处理 (formatDealDay)

```go
func formatDealDay(value float64, item QualityControlItem) string {
    if item.PzkzStatus == "废弃" {
        return "已废弃"
    }
    
    // 挂起状态检查
    if item.EndDate != "" {
        endTime, err := time.Parse(time.RFC3339, item.EndDate)
        if err == nil && endTime.After(time.Now()) {
            remainDays := int(endTime.Sub(time.Now()).Hours() / 24)
            return fmt.Sprintf("挂起(剩余%d天)", remainDays)
        }
    }
    
    // 已完成状态
    if item.SolutionDate != "" || item.IsFinish == "1" {
        if value > 0 {
            return fmt.Sprintf("提前解决(%.0f天)", value)
        } else {
            return "按时解决"
        }
    }
    
    // 进行中状态
    if value > 0 {
        return fmt.Sprintf("剩余%.0f天", value)
    } else {
        return fmt.Sprintf("超期%.0f天", math.Abs(value))
    }
}
```

## 5. 技术实现细节

### 5.1 配置文件扩展

#### 5.1.1 配置结构扩展

```go
// application/libs/config.go
type FeiShuDoc struct {
    Enable                    bool   `default:"false"`
    AppID                     string `default:""`
    AppSecret                 string `default:""`
    FolderToken              string `default:""`
    BiAppToken               string `default:""`
    PmsUser                  string `default:""`
    PmsPass                  string `default:""`
    // 品控系统配置
    QualityControlURL        string `default:"http://yfzl.ruijie.com.cn"`
    QualityControlAppToken   string `default:"Dtl6bmYfyauc0VsNDNbcBsH3n7d"`
    QualityControlTableID    string `default:"tblyBol3c07mhW1T"`
}
```

#### 5.1.2 配置文件更新

```yaml
# application.yml
feishudoc:
  enable: true
  appid: cli_a6864542233d900e
  appsecret: f9FupAX6aw65B6FLgyvNXbEywHq7keJF
  foldertoken: LSfAfsbqGlaC6Rd0wttc8yLynRd
  biapptoken: KZQCbsWefa5e3MsamMccOYYPnMr
  pmsuser: username
  pmspass: password
  # 品控系统配置
  qualitycontrolurl: "http://yfzl.ruijie.com.cn"
  qualitycontrolapptoken: "Dtl6bmYfyauc0VsNDNbcBsH3n7d"
  qualitycontroltableid: "tblyBol3c07mhW1T"
```

### 5.2 定时任务集成

#### 5.2.1 集成到现有PMS数据同步

```go
// application/controllers/openfeishu/synpmsdata.go
func SyncPMSData() {
    // ... 现有同步逻辑 ...
    
    // 新增品控数据同步
    logging.DebugLogger.Debugf("开始同步品控数据")
    err := SyncQualityControlData()
    if err != nil {
        logging.ErrorLogger.Errorf("品控数据同步失败: %s", err.Error())
    } else {
        logging.DebugLogger.Debugf("完成同步品控数据")
    }
}
```

#### 5.2.2 定时任务调度

```go
// service/schedule/feishu.go
// 品控数据同步已集成到现有的RunPMSData()函数中
// 执行频率：每天8点执行一次 ("0 8 * * *")
func RunPMSData() {
    feishuLock.Lock()
    defer feishuLock.Unlock()
    _, err := Cron.AddFunc("0 8 * * *", func() { 
        openfeishu.SyncPMSData() // 包含品控数据同步
    })
    if err != nil {
        logging.ErrorLogger.Error("add SyncPMSData cron job err", err)
    }
}
```

### 5.3 错误处理策略

```go
// 统一错误处理函数
func handleQualityControlError(operation string, err error) {
    logging.ErrorLogger.Errorf("品控系统同步-%s失败: %s", operation, err.Error())
}

// 重试机制
func retryOperation(operation func() error, maxRetries int) error {
    for i := 0; i < maxRetries; i++ {
        if err := operation(); err == nil {
            return nil
        } else if i == maxRetries-1 {
            return err
        }
        time.Sleep(time.Duration(i+1) * time.Second)
    }
    return nil
}

// 批量处理错误恢复（仅更新模式）
func processBatchWithErrorRecovery(updates map[string]FeishuQualityUpdateRecord, indexMap map[string]string) *SyncStatistics {
    stats := &SyncStatistics{TotalRecords: len(indexMap)}
    
    for qualityNumber, updateData := range updates {
        if recordID, exists := indexMap[qualityNumber]; exists {
            stats.MatchedRecords++
            if err := updateSingleRecord(recordID, updateData); err != nil {
                logging.ErrorLogger.Errorf("更新品控单号 %s 失败: %s", qualityNumber, err.Error())
                stats.ErrorRecords++
                continue // 继续处理其他项目
            }
            stats.UpdatedRecords++
        } else {
            stats.SkippedRecords++
        }
    }
    
    return stats
}
```

### 5.4 日志记录策略

```go
func logQualityControlSync(stage string, details interface{}) {
    logging.DebugLogger.Debugf("品控系统同步-%s: %+v", stage, details)
}

func logSyncStatistics(stats *SyncStatistics) {
    logging.DebugLogger.Debugf("品控数据同步统计: 总记录数=%d, 匹配记录数=%d, 更新成功=%d, 跳过记录=%d, 错误记录=%d", 
        stats.TotalRecords, stats.MatchedRecords, stats.UpdatedRecords, stats.SkippedRecords, stats.ErrorRecords)
}

// 使用示例
logQualityControlSync("开始", "启动品控数据同步")
logQualityControlSync("飞书索引构建", fmt.Sprintf("构建索引完成，共%d条记录", len(indexMap)))
logQualityControlSync("登录", "品控系统登录成功")
logQualityControlSync("数据获取", fmt.Sprintf("获取到%d条品控数据", len(items)))
logQualityControlSync("闭环查询", fmt.Sprintf("需要查询闭环数据的记录数: %d", count))
logSyncStatistics(stats)
logQualityControlSync("同步完成", "品控数据同步到飞书成功")
```

## 6. 开发实施计划

### 6.1 ✅ 第一阶段：基础架构搭建 (已完成)

**交付物**：

- [x] 配置文件扩展完成
- [x] 数据结构定义完成 (`qualitycontrol_data.go`)
- [x] 基础文件结构创建

**验收标准**：

- ✅ 配置文件能正确加载品控系统参数
- ✅ 数据结构能正确解析API响应
- ✅ 代码结构符合现有项目规范

### 6.2 第二阶段：飞书数据获取和索引构建 (1天)

**交付物**：

- [ ] 飞书表格分页查询功能 (`qualitycontrol_feishu.go`)
- [ ] 品控单号索引构建逻辑
- [ ] 索引映射管理机制

**验收标准**：

- 能正确查询飞书多维表格数据
- 品控单号索引构建准确
- 支持大量数据的分页处理和索引构建

### 6.3 ✅ 第三阶段：品控系统数据获取 (已完成 - 增强版)

**交付物**：

- [x] 品控系统登录功能 (`qualitycontrol_api.go`) - **已实现增强版**
- [x] Redis Cookie缓存机制 - **新增功能**
- [x] 智能SSO登录 - **新增功能**
- [x] 品控基础数据API调用 - **已实现增强版**
- [x] 品控单列表API - **新增功能** (GetQualityControlList, ProcessQualityControlData)
- [x] 认证失败自动检测和恢复 - **新增功能**
- [x] 系统诊断功能 - **新增功能**

**验收标准**：

- ✅ 能成功登录品控系统 (智能SSO登录)
- ✅ Cookie能正确获取和复用 (Redis持久化缓存)
- ✅ 基础数据API调用稳定可靠 (智能重试机制)
- ✅ 品控单列表API正常工作 (分页获取，数据格式化)
- ✅ 认证失败自动恢复 (新增功能)
- ✅ 完整的系统诊断 (新增功能)

### 6.4 第四阶段：闭环管理数据获取 (2天)

**交付物**：

- [ ] 条件判断逻辑实现 (`qualitycontrol_closed.go`)
- [ ] 闭环管理API调用
- [ ] 闭环状态解析处理

**验收标准**：

- 故障等级和研发故障原因判断准确
- 闭环管理API能正确调用
- 闭环状态解析逻辑正确

### 6.5 ✅ 第五阶段：数据处理和更新同步 (已完成 - 增强版)

**交付物**：

- [x] 数据转换处理逻辑 (`qualitycontrol_processor.go`) - **设计完成**
- [x] 飞书记录更新功能 (`qualitycontrol_feishu.go`) - **已实现增强版**
  - [x] `UpdateFeishuRecordByFieldNames()` - 单条更新
  - [x] `BatchUpdateFeishuRecordsByFieldNames()` - 批量更新
  - [x] 字段名称直接支持
  - [x] 类型安全转换
- [x] 同步统计和错误处理机制 - **已实现**

**验收标准**：

- ✅ 数据转换逻辑与业务需求一致
- ✅ 飞书记录更新功能正常 (已实现增强版)
- ✅ 同步统计信息准确完整

### 6.6 第六阶段：集成测试和优化 (2天)

**交付物**：

- [ ] 主入口函数实现 (`qualitycontrol.go`)
- [ ] 集成到PMS数据同步任务
- [ ] 端到端功能测试和性能优化

**验收标准**：

- 完整同步流程运行正常
- 定时任务集成成功
- 错误情况能正确处理和恢复
- 同步统计报告功能完善

### 6.7 📊 当前进度总结

**已完成的核心功能**：

- ✅ **Redis Cookie缓存机制** - 支持多实例部署，4小时有效期
- ✅ **智能SSO登录** - 多选择器支持，自动错误检测
- ✅ **品控API调用** - 智能重试，HTML/JSON检测，详细日志
- ✅ **品控单列表API** - 分页获取，数据格式化，字段映射 (新增)
- ✅ **飞书记录更新** - 字段名称直接支持，批量更新，类型安全
- ✅ **认证失败恢复** - 自动检测，缓存清除，智能重试
- ✅ **系统诊断功能** - 6步诊断，健康检查，缓存管理

**待完成的功能**：

- 🔄 **飞书数据索引构建** - 品控单号映射，分页查询
- 🔄 **闭环管理API实现** - 基于设计的具体实现
- 🔄 **主流程集成** - 完整同步流程，定时任务集成

**技术债务和优化**：

- 性能监控指标收集
- 并发处理优化
- 缓存策略优化
- 错误恢复策略完善

## 7. 风险评估和应对策略

### 7.1 技术风险

- **风险**：品控系统API变更或不稳定
- **应对**：实现重试机制和降级处理

### 7.2 性能风险

- **风险**：大量品控单号查询影响系统性能
- **应对**：实现分页查询、索引优化和批量更新机制

### 7.3 数据风险

- **风险**：闭环判断逻辑复杂，容易出错
- **应对**：充分的单元测试和边界条件验证

### 7.4 匹配风险

- **风险**：品控单号匹配失败导致数据不同步
- **应对**：详细的匹配统计和错误日志，支持手动重试

## 8. 后续扩展规划

### 8.1 功能扩展

- 支持品控数据变更通知
- 增加品控数据统计分析功能
- 支持自定义闭环判断规则
- 支持新增记录的自动创建功能

### 8.2 技术优化

- 实现缓存机制减少API调用
- 增加并发处理提升性能
- 支持增量同步优化
- 优化索引构建性能

---

**文档版本**：v4.0
**创建时间**：2024年12月
**维护人员**：开发团队
**更新记录**：

- v1.0: 初始版本创建
- v2.0: 集成定时任务调度信息，完善数据结构和业务逻辑
- v3.0: 更新为仅更新现有记录模式，增加索引构建和匹配统计功能
- v4.0: 集成Redis缓存机制、智能SSO登录、闭环管理数据模块设计、飞书多维表格更新功能、监控诊断功能，更新实际开发进度
