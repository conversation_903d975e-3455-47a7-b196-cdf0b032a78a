package controllers

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"irisAdminApi/application/controllers/openfeishu/meeting/libs"
	"irisAdminApi/application/controllers/openfeishu/meeting/models"
	"irisAdminApi/application/controllers/openfeishu/meeting/services"
	applibs "irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/cache"

	"github.com/kataras/iris/v12"
)

// MeetingController 会议控制器
type MeetingController struct {
	meetingService  *services.MeetingService
	bitableService  *services.BitableService
	calendarService *services.CalendarService
	redisHelper     *libs.RedisHelper
	metrics         *libs.MetricsCollector
	isConfigured    bool // 标记控制器是否正确配置
}

// NewMeetingController 创建会议控制器实例
func NewMeetingController() *MeetingController {
	// 复用现有配置管理
	feishuConfig := applibs.Config.FeiShuDoc

	// 使用共享配置结构体
	serviceConfig := models.NewServiceConfig(
		feishuConfig.AppID,
		feishuConfig.AppSecret,
		feishuConfig.BiAppToken,
		feishuConfig.MeetingAppToken,
		feishuConfig.MeetingTableID,
		feishuConfig.DefaultCalendarID,
		feishuConfig.DefaultTimezone,
		feishuConfig.AutoNotifyAttendees,
	)

	// 初始化服务
	bitableService := services.NewBitableService(serviceConfig)
	calendarService := services.NewCalendarService(serviceConfig)

	// 初始化简化的用户解析服务
	userResolverService, err := services.NewSimpleUserResolverService("")
	if err != nil {
		logging.ErrorLogger.Errorf("初始化用户解析服务失败: %v", err)
		// 继续运行，但用户解析功能将不可用
		userResolverService = nil
	}

	meetingService := services.NewMeetingService(bitableService, calendarService, userResolverService)

	// 初始化辅助工具
	redisHelper := libs.NewRedisHelper()
	metrics := libs.GetGlobalMetrics()

	return &MeetingController{
		meetingService:  meetingService,
		bitableService:  bitableService,
		calendarService: calendarService,
		redisHelper:     redisHelper,
		metrics:         metrics,
		isConfigured:    true, // 标记为已正确配置
	}
}

// successResponse 统一成功响应方法
func (mc *MeetingController) successResponse(ctx iris.Context, message string, data interface{}) {
	ctx.JSON(iris.Map{
		"code":    200,
		"message": message,
		"data":    data,
	})
}

// errorResponse 统一错误响应方法
func (mc *MeetingController) errorResponse(ctx iris.Context, code int, message string) {
	ctx.JSON(iris.Map{
		"code":    code,
		"message": message,
		"data":    nil,
	})
}

// badRequestResponse 统一400错误响应方法
func (mc *MeetingController) badRequestResponse(ctx iris.Context, message string) {
	mc.errorResponse(ctx, http.StatusBadRequest, message)
}

// internalErrorResponse 统一500错误响应方法
func (mc *MeetingController) internalErrorResponse(ctx iris.Context, message string) {
	mc.errorResponse(ctx, http.StatusInternalServerError, message)
}

// HealthCheck 会议服务健康检查
func (mc *MeetingController) HealthCheck(ctx iris.Context) {
	status := "healthy"
	message := "会议服务运行正常"
	code := 200

	if !mc.isConfigured {
		status = "configuration_error"
		message = "会议服务配置错误"
		code = 500
	}

	if code == 200 {
		mc.successResponse(ctx, message, iris.Map{
			"service": "meeting",
			"status":  status,
		})
	} else {
		mc.errorResponse(ctx, code, message)
	}
}

// checkConfiguration 检查配置状态，如果未配置则返回错误响应
func (mc *MeetingController) checkConfiguration(ctx iris.Context) bool {
	if !mc.isConfigured {
		mc.internalErrorResponse(ctx, "会议服务未正确配置，请检查飞书配置")
		return false
	}
	return true
}

// ProcessMeetings 处理会议请求 - GET请求触发
// GET /api/v1/meeting/process
func (c *MeetingController) ProcessMeetings(ctx iris.Context) {
	// 检查配置状态
	if !c.checkConfiguration(ctx) {
		return
	}

	// 记录请求开始
	logging.InfoLogger.Info("收到会议处理请求")
	startTime := time.Now()

	// 生成请求ID用于幂等性控制
	requestID := fmt.Sprintf("meeting_process_%d", time.Now().UnixNano())

	// 检查重复请求（复用现有Redis连接池）
	if isDuplicate, err := c.checkDuplicateRequest(requestID); err != nil {
		logging.ErrorLogger.Errorf("检查重复请求失败: %v", err)
		c.metrics.GetMetrics().RecordError("redis_error", "duplicate_check")
		c.internalErrorResponse(ctx, "系统错误")
		return
	} else if isDuplicate {
		logging.InfoLogger.Infof("重复请求，请求ID: %s", requestID)
		c.successResponse(ctx, "请求正在处理中，请勿重复提交", nil)
		return
	}

	// 设置请求处理标记
	if err := c.setRequestProcessing(requestID); err != nil {
		logging.ErrorLogger.Errorf("设置请求处理标记失败: %v", err)
	}

	// 异步处理会议数据
	go func() {
		// 开始处理监控
		c.metrics.StartProcessing()

		defer func() {
			// 结束处理监控
			c.metrics.EndProcessing()

			if r := recover(); r != nil {
				logging.ErrorLogger.Errorf("会议处理发生panic: %v", r)
				c.metrics.GetMetrics().RecordError("panic_error", "meeting_processing")
			}
			// 清理请求标记
			c.clearRequestProcessing(requestID)
		}()

		// 调用核心业务服务
		result, err := c.meetingService.ProcessMeetings(context.Background())
		if err != nil {
			logging.ErrorLogger.Errorf("会议处理失败: %v", err)
			c.metrics.GetMetrics().RecordError("processing_error", "meeting_service")
			return
		}

		// 记录处理结果和监控指标
		duration := time.Since(startTime)
		c.metrics.RecordBatchResult(result.TotalCount, result.SuccessCount, result.FailedCount, duration)

		logging.InfoLogger.Infof("会议处理完成，耗时: %v, 成功: %d, 失败: %d",
			duration, result.SuccessCount, result.FailedCount)
	}()

	// 立即返回响应（异步处理）
	c.successResponse(ctx, "会议处理请求已接收，正在后台处理", iris.Map{
		"request_id": requestID,
		"status":     "processing",
	})
}

// GetProcessStatus 获取处理状态
// GET /api/v1/meeting/status/:request_id
func (c *MeetingController) GetProcessStatus(ctx iris.Context) {
	// 检查配置状态
	if !c.checkConfiguration(ctx) {
		return
	}

	requestID := ctx.Params().Get("request_id")
	if requestID == "" {
		c.badRequestResponse(ctx, "请求ID不能为空")
		return
	}

	// 从Redis获取处理状态
	status, err := c.getRequestStatus(requestID)
	if err != nil {
		logging.ErrorLogger.Errorf("获取请求状态失败: %v", err)
		c.internalErrorResponse(ctx, "获取状态失败")
		return
	}

	c.successResponse(ctx, "获取状态成功", iris.Map{
		"request_id": requestID,
		"status":     status,
	})
}

// checkDuplicateRequest 检查重复请求（复用现有Redis连接池）
func (c *MeetingController) checkDuplicateRequest(requestID string) (bool, error) {
	rc := cache.GetRedisClusterClient()
	defer rc.Close()

	key := fmt.Sprintf("meeting:request:%s", requestID)
	exists := rc.Exists(key)
	return exists, nil
}

// setRequestProcessing 设置请求处理标记
func (c *MeetingController) setRequestProcessing(requestID string) error {
	rc := cache.GetRedisClusterClient()
	defer rc.Close()

	key := fmt.Sprintf("meeting:request:%s", requestID)
	// 设置5分钟过期时间
	_, err := rc.Set(key, "processing", 5*time.Minute)
	return err
}

// clearRequestProcessing 清理请求处理标记
func (c *MeetingController) clearRequestProcessing(requestID string) error {
	rc := cache.GetRedisClusterClient()
	defer rc.Close()

	key := fmt.Sprintf("meeting:request:%s", requestID)
	_, err := rc.Del(key)
	return err
}

// getRequestStatus 获取请求状态
func (c *MeetingController) getRequestStatus(requestID string) (string, error) {
	rc := cache.GetRedisClusterClient()
	defer rc.Close()

	key := fmt.Sprintf("meeting:request:%s", requestID)
	status, err := rc.GetKey(key)
	if err != nil {
		return "not_found", err
	}
	if status == nil {
		return "completed", nil
	}
	return fmt.Sprintf("%v", status), nil
}

// CancelEvent 取消日程
// POST /api/v1/openfeishu/meeting/cancel
func (c *MeetingController) CancelEvent(ctx iris.Context) {
	// 检查配置状态
	if !c.checkConfiguration(ctx) {
		return
	}

	// 解析请求参数
	var request models.CancelEventRequest
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("解析取消日程请求失败: %v", err)
		c.badRequestResponse(ctx, "请求参数格式错误")
		return
	}

	// 参数验证
	if request.EventID == "" {
		c.badRequestResponse(ctx, "事件ID不能为空")
		return
	}

	if request.CancelReason == "" {
		c.badRequestResponse(ctx, "取消原因不能为空")
		return
	}

	if len(request.CancelReason) > 500 {
		c.badRequestResponse(ctx, "取消原因长度不能超过500字符")
		return
	}

	logging.InfoLogger.Infof("收到取消日程请求: EventID=%s, Reason=%s", request.EventID, request.CancelReason)

	// 记录操作开始时间
	startTime := time.Now()

	// 调用日历服务取消日程
	err := c.calendarService.CancelEvent(ctx.Request().Context(), request.EventID, request.CancelReason)

	// 计算操作耗时
	duration := time.Since(startTime).Milliseconds()

	if err != nil {
		logging.ErrorLogger.Errorf("取消日程失败: %v", err)
		c.metrics.GetMetrics().RecordError("cancel_event_error", "calendar_service")

		// 记录失败日志
		cancelLog := c.meetingService.CreateCancelLog(request.EventID, request.UserID, request.CancelReason, "failed", duration)
		cancelLog.ErrorMessage = err.Error()
		c.meetingService.LogOperation(ctx.Request().Context(), cancelLog)

		c.internalErrorResponse(ctx, fmt.Sprintf("取消日程失败: %v", err))
		return
	}

	// 记录成功日志
	cancelLog := c.meetingService.CreateCancelLog(request.EventID, request.UserID, request.CancelReason, "success", duration)
	c.meetingService.LogOperation(ctx.Request().Context(), cancelLog)

	// 构建处理结果
	now := time.Now()
	processResult := &models.ProcessResult{
		RecordID:         request.EventID, // 使用EventID作为RecordID
		Status:           models.StatusSuccess,
		EventID:          request.EventID,
		OperationType:    models.OperationTypeCancel,
		CancelStatus:     models.CancelStatusCancelled,
		CancelledAt:      &now,
		CancelReason:     request.CancelReason,
		ProcessTime:      now,
		NotificationSent: request.NotifyAttendees,
	}

	// 更新多维表格状态（如果有对应的记录）
	if err := c.bitableService.UpdateRecordStatus(ctx.Request().Context(), request.EventID, processResult); err != nil {
		logging.ErrorLogger.Errorf("更新多维表格状态失败: %v", err)
		// 不影响主流程，只记录错误
	}

	// 构建响应
	response := models.CancelEventResponse{
		Success:          true,
		EventID:          request.EventID,
		CancelledAt:      now,
		NotificationSent: request.NotifyAttendees,
		Message:          "日程取消成功",
	}

	logging.InfoLogger.Infof("成功取消日程: EventID=%s", request.EventID)

	c.successResponse(ctx, "取消日程成功", response)
}

// UpdateEvent 更新日程
// POST /api/v1/openfeishu/meeting/update
func (c *MeetingController) UpdateEvent(ctx iris.Context) {
	// 检查配置状态
	if !c.checkConfiguration(ctx) {
		return
	}

	// 解析请求参数
	var request models.UpdateEventRequest
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("解析更新日程请求失败: %v", err)
		c.badRequestResponse(ctx, "请求参数格式错误")
		return
	}

	// 参数验证
	if request.EventID == "" {
		c.badRequestResponse(ctx, "事件ID不能为空")
		return
	}

	// 验证更新内容不能为空
	if c.isEmptyUpdates(&request.Updates) {
		c.badRequestResponse(ctx, "更新内容不能为空")
		return
	}

	logging.InfoLogger.Infof("收到更新日程请求: EventID=%s", request.EventID)

	// 冲突检测功能暂未实现
	if request.CheckConflicts {
		logging.InfoLogger.Info("冲突检测功能暂未实现，跳过检测")
	}

	// 记录操作开始时间
	startTime := time.Now()

	// 调用日历服务更新日程
	_, err := c.calendarService.UpdateEvent(ctx.Request().Context(), request.EventID, &request.Updates)

	// 计算操作耗时
	duration := time.Since(startTime).Milliseconds()

	// 记录变更字段
	changedFields := c.getChangedFields(&request.Updates)

	if err != nil {
		logging.ErrorLogger.Errorf("更新日程失败: %v", err)
		c.metrics.GetMetrics().RecordError("update_event_error", "calendar_service")

		// 记录失败日志
		updateLog := c.meetingService.CreateUpdateLog(request.EventID, request.UserID, changedFields, "failed", duration)
		updateLog.ErrorMessage = err.Error()
		c.meetingService.LogOperation(ctx.Request().Context(), updateLog)

		c.internalErrorResponse(ctx, fmt.Sprintf("更新日程失败: %v", err))
		return
	}

	// 记录成功日志
	updateLog := c.meetingService.CreateUpdateLog(request.EventID, request.UserID, changedFields, "success", duration)
	c.meetingService.LogOperation(ctx.Request().Context(), updateLog)

	// 构建处理结果
	now := time.Now()
	processResult := &models.ProcessResult{
		RecordID:         request.EventID,
		Status:           models.StatusSuccess,
		EventID:          request.EventID,
		OperationType:    models.OperationTypeUpdate,
		ChangedFields:    changedFields,
		ProcessTime:      now,
		NotificationSent: request.NotifyAttendees,
		Version:          1, // 简单版本控制，后续可扩展
	}

	// 更新多维表格状态（如果有对应的记录）
	if err := c.bitableService.UpdateRecordStatus(ctx.Request().Context(), request.EventID, processResult); err != nil {
		logging.ErrorLogger.Errorf("更新多维表格状态失败: %v", err)
		// 不影响主流程，只记录错误
	}

	// 构建响应
	response := models.UpdateEventResponse{
		Success:          true,
		EventID:          request.EventID,
		UpdatedAt:        now,
		ChangedFields:    changedFields,
		NotificationSent: request.NotifyAttendees,
		Message:          "日程更新成功",
	}

	logging.InfoLogger.Infof("成功更新日程: EventID=%s, 变更字段: %v", request.EventID, changedFields)

	c.successResponse(ctx, "更新日程成功", response)
}

// isEmptyUpdates 检查更新内容是否为空
func (c *MeetingController) isEmptyUpdates(updates *models.EventUpdates) bool {
	if updates == nil {
		return true
	}

	// 检查所有字段是否都为空
	return updates.Title == nil &&
		updates.Description == nil &&
		updates.StartTime == nil &&
		updates.EndTime == nil &&
		updates.Location == nil &&
		updates.Timezone == nil &&
		len(updates.AttendeeOpenIDs) == 0 &&
		len(updates.AddAttendees) == 0 &&
		len(updates.RemoveAttendees) == 0
}

// getChangedFields 获取变更字段列表
func (c *MeetingController) getChangedFields(updates *models.EventUpdates) []string {
	var changedFields []string

	if updates.Title != nil {
		changedFields = append(changedFields, "title")
	}
	if updates.Description != nil {
		changedFields = append(changedFields, "description")
	}
	if updates.StartTime != nil {
		changedFields = append(changedFields, "start_time")
	}
	if updates.EndTime != nil {
		changedFields = append(changedFields, "end_time")
	}
	if updates.Location != nil {
		changedFields = append(changedFields, "location")
	}
	if updates.Timezone != nil {
		changedFields = append(changedFields, "timezone")
	}
	if len(updates.AttendeeOpenIDs) > 0 {
		changedFields = append(changedFields, "attendee_sync")
	}
	if len(updates.AddAttendees) > 0 {
		changedFields = append(changedFields, "add_attendees")
	}
	if len(updates.RemoveAttendees) > 0 {
		changedFields = append(changedFields, "remove_attendees")
	}

	return changedFields
}

// GetEvent 获取日程详情
// GET /api/v1/openfeishu/meeting/event/:event_id
func (c *MeetingController) GetEvent(ctx iris.Context) {
	// 检查配置状态
	if !c.checkConfiguration(ctx) {
		return
	}

	eventID := ctx.Params().Get("event_id")
	if eventID == "" {
		c.badRequestResponse(ctx, "事件ID不能为空")
		return
	}

	logging.InfoLogger.Infof("收到获取日程详情请求: EventID=%s", eventID)

	// TODO: 实现CalendarService.GetEvent方法
	event := &models.CalendarEvent{
		EventID:     eventID,
		Title:       "日程详情",
		Description: "通过事件ID获取的日程信息",
	}

	response := models.GetEventResponse{
		Success: true,
		Event:   event,
		Message: "获取日程详情成功",
	}

	c.successResponse(ctx, "获取日程详情成功", response)
}
