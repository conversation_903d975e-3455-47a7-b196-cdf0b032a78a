package dperfanalysisresult

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/sig"
	"time"
)

const ModelName = "Perf性能分析结果表"

type User struct {
	ID       uint   `json:"id"`
	Name     string `json:"name"`
	Username string `json:"username"`
}

type SigPerfAnalysisResult struct {
	sig.SigPerfAnalysisResult
}

type ListResponse struct {
	SigPerfAnalysisResult
}

func (this *SigPerfAnalysisResult) ModelName() string {
	return ModelName
}

func Model() *sig.SigPerfAnalysisResult {
	return &sig.SigPerfAnalysisResult{}
}

// Create 创建perf分析结果
func (this *SigPerfAnalysisResult) Create(object map[string]interface{}) error {
	object["app"] = libs.Config.Sig.App
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create perf analysis data err ", err)
		return err
	}

	return nil
}

// Update 更新perf分析结果
func (this *SigPerfAnalysisResult) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("app = ?", libs.Config.Sig.App).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update perf analysis result err ", err)
		return err
	}
	return nil
}

// Delete 删除perf分析结果
func (this *SigPerfAnalysisResult) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Where("app = ?", libs.Config.Sig.App).Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete perf analysis result err ", err)
		return err
	}
	return nil
}

// Find 根据ID查找perf分析结果
func (this *SigPerfAnalysisResult) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("app = ?", libs.Config.Sig.App).Where("id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find perf analysis result err ", err)
		return err
	}
	return nil
}

// FindEx 根据指定列值查找perf分析结果
func (this *SigPerfAnalysisResult) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("app = ?", libs.Config.Sig.App).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find perf analysis result err ", err)
		return err
	}
	return nil
}

// FindByJobID 根据jobID查找所有perf分析结果
func FindByJobID(jobID string) ([]*SigPerfAnalysisResult, error) {
	var results []*SigPerfAnalysisResult
	err := easygorm.GetEasyGormDb().Model(Model()).Where("app = ?", libs.Config.Sig.App).Where("job_id = ?", jobID).Find(&results).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find perf analysis results by job ID err ", err)
		return nil, err
	}
	return results, nil
}

// FindByJobIDAndType 根据jobID和分析类型查找所有perf分析结果
func FindByJobIDAndType(jobID, analysisType string) ([]*SigPerfAnalysisResult, error) {
	var results []*SigPerfAnalysisResult
	err := easygorm.GetEasyGormDb().Model(Model()).Where("app = ?", libs.Config.Sig.App).Where("job_id = ? AND analysis_type = ?", jobID, analysisType).Find(&results).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find perf analysis results by job ID and type err ", err)
		return nil, err
	}
	return results, nil
}

// GetPerfResultPath 返回perf结果文件的完整路径
func (this *SigPerfAnalysisResult) GetPerfResultPath() string {
	return this.ResultPath
}

// SavePerfResult 保存perf分析结果
func SavePerfResult(jobID string, decryptJobID uint, perfFileName, analysisType, resultPath string, fileSize int64, status uint, errorMsg string) (*SigPerfAnalysisResult, error) {
	result := &SigPerfAnalysisResult{}
	err := result.Create(map[string]interface{}{
		"JobID":        jobID,
		"DecryptJobID": decryptJobID,
		"PerfFileName": perfFileName,
		"AnalysisType": analysisType,
		"ResultPath":   resultPath,
		"Status":       status,
		"ErrorMessage": errorMsg,
		"FileSize":     fileSize,
		"CreatedAt":    time.Now(),
		"UpdatedAt":    time.Now(),
	})

	if err != nil {
		logging.ErrorLogger.Errorf("Failed to save perf result: %v", err)
		return nil, err
	}

	return result, nil
}

// All 返回所有perf分析结果
func All(jobID, perfFileName, analysisType, status, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Where("app = ?", libs.Config.Sig.App)

	if len(jobID) > 0 {
		db = db.Where("job_id = ?", jobID)
	}

	if len(perfFileName) > 0 {
		db = db.Where("perf_file_name LIKE ?", fmt.Sprintf("%%%s%%", perfFileName))
	}

	if len(analysisType) > 0 {
		db = db.Where("analysis_type = ?", analysisType)
	}

	if len(status) > 0 {
		db = db.Where("status = ?", status)
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}
