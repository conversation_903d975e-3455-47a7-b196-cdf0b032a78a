package dfeishuprojectdocument

import (
	"fmt"
	"path/filepath"
	"reflect"
	"strings"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/feishu"
	"irisAdminApi/application/models/release"
	"irisAdminApi/service/dao/release/dproject"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "项目文档表"

type PmsProjectDocumentResponse struct {
	PMSDocID              uint    `gorm:"not null" json:"pms_doc_id"`
	ProjectName           string  `gorm:"not null; type:varchar(200)" json:"project_name" `
	DocumentName          string  `gorm:"type:varchar(200)" json:"document_name" `
	DocumentType          string  `gorm:"type:varchar(200)" json:"document_type" `
	DocumentUrl           string  `gorm:"type:varchar(200)" json:"document_url" `
	DocumentOwnerName     string  `gorm:"type:varchar(200)" json:"document_owner_name" `
	DocumentOwnerID       string  `gorm:"type:varchar(200)" json:"document_owner_id"`
	DocumentCommentNum    uint    `gorm:"not null" json:"document_comment_num"`
	DocumentCreatedTime   uint    `gorm:"not null" json:"document_created_time"`
	DocumentReviewStatus  string  `gorm:"type:varchar(100)" json:"document_review_status" `
	FileName              string  `gorm:"type:varchar(200)" json:"file_name" `
	FileToken             string  `gorm:"type:varchar(200)" json:"file_token" `
	FileType              string  `gorm:"type:varchar(200)" json:"file_type" `
	Requirement           string  `gorm:"type:varchar(200)" json:"requirement"`
	WorkPacketName        string  `gorm:"type:varchar(200)" json:"work_packet_name" `
	PacketManagerName     string  `gorm:"type:varchar(200)" json:"packet_manager_name" `
	PacketManagerID       string  `gorm:"type:varchar(200)" json:"packet_manager_id" `
	TotalCodes            float32 `gorm:"not null" json:"total_codes" `
	RecordID              string  `gorm:"type:varchar(200)" json:"record_id" `
	RecordCreatedTime     uint    `gorm:"not null" json:"record_created_time" `
	DocumentSubmitStatus  string  `gorm:"type:varchar(100)" json:"document_submit_status" update:"1" `
	ReviewStatus          string  `gorm:"type:varchar(100)" json:"review_status" update:"1"`
	IsSubmit              uint    `gorm:"not null" json:"is_submit" update:"1"` //入库状态 0 未入库 1 已入库
	IsInstorage           uint    `gorm:"not null" json:"is_instorage" update:"1"`
	DocumentStatus        string  `gorm:"type:varchar(100)" json:"document_status"  update:"1"`
	ComponentPacketName   string  `gorm:"type:varchar(100)" json:"component_packet_name" update:"1"`
	DocumentCategoryTitle string  `gorm:"type:varchar(100)" json:"document_category_title" update:"1"` //文档类型
	RequiredJudge         string  `gorm:"type:varchar(100)" json:"required_judge" `                    //必选评委
	OptionalJudge         string  `gorm:"type:varchar(100)" json:"optional_judge" `                    //可选评委
	JudgeStartTime        uint64  `gorm:"type:bigint(20);notNull" json:"judge_start_time"`             //评审开始时间
	JudgeEndTime          uint64  `gorm:"type:bigint(20);notNull" json:"judge_end_time"`               //评审结束时间
	MeetingStartTime      uint64  `gorm:"type:bigint(20);notNull" json:"meeting_start_time"`           //会议开始时间
	MeetingEndTime        uint64  `gorm:"type:bigint(20);notNull" json:"meeting_end_time"`             //会议结束时间
	MeetingUrl            string  `gorm:"type:varchar(100)" json:"meeting_url" `                       //会议链接
	MeetingTopic          string  `gorm:"type:varchar(100)" json:"meeting_topic" `                     //会议主题
	MeetingID             string  `gorm:"type:varchar(100)" json:"meeting_id" `                        //会议id
	MeetingNO             string  `gorm:"type:varchar(100)" json:"meeting_no" `                        //会议号
	ExpectedReviewEndTime uint64  `gorm:"type:bigint(20);notNull" json:"expected_review_end_time"`     //期望评审结束时间
	AddCodes              float32 `gorm:"not null" json:"add_codes" update:"1"`                        //新增代码
	PortedCodes           float32 `gorm:"not null" json:"ported_codes" update:"1"`                     // 移植代码
	TemporaryPortedCodes  float32 `gorm:"not null" json:"temporary_ported_codes" update:"1"`           //移植代码(临时)
	DepartmentName        string  `gorm:"type:varchar(100)" json:"department_name" update:"1"`         //文档作者所在专业组
	RequestID             uint    `gorm:"not null" json:"request_id" update:"1"`                       //需求ID
	WorkPacketID          uint    `gorm:"not null" json:"work_packet_id" update:"1"`                   //工作ID
	PgttmUserName         string  `gorm:"type:varchar(200)" json:"pgttm_user_name" update:"1"`         //pgttm 用户
	PgttmUserID           string  `gorm:"type:varchar(200)" json:"pgttm_user_id" update:"1"`           //pgttmid
}

type DocumentJudge struct {
	ProjectName string `json:"project_name" `
	OpenID      string `json:"open_id" `
	UserName    string `json:"user_name" `
	Name        string `json:"name" `
}

type DocumentANDJudge struct {
	ProjectName  string `json:"project_name" `
	DocumentName string `json:"document_name" `
	OpenID       string `json:"open_id" `
	UserName     string `json:"user_name" `
	Name         string `json:"name" `
}

type DocumentRequest struct {
	Requirement string  `json:"requirement" `
	RequestID   string  `json:"request_id" `
	ProjectName string  `json:"project_name" `
	PgttmUserID string  `json:"pgttm_user_id" `
	TotalCodes  float64 `json:"total_codes" `
}

type DocumentTypeRequest struct {
	Requirement           string `json:"requirement" `
	ProjectName           string `json:"project_name" `
	RequestID             string `json:"request_id" `
	DocumentCategoryTitle string `json:"document_category_title" `
}

type DocumentWorkPacket struct {
	WorkPacketName    string  `json:"work_packet_name" `
	ProjectName       string  `json:"project_name" `
	PacketManagerName string  `json:"packet_manager_name" `
	PacketManagerID   string  `json:"packet_manager_id" `
	TotalCodes        float64 `json:"total_codes" `
}

type DocumentTypeWorkPacket struct {
	WorkPacketName        string `json:"work_packet_name" `
	ProjectName           string `json:"project_name" `
	DocumentCategoryTitle string `json:"document_category_title" `
}

type DocumentPSTL struct {
	ProjectName    string `json:"project_name" `
	DepartmentName string `json:"department_name" `
	OpenID         string `json:"open_id" `
	UserName       string `json:"user_name" `
	Name           string `json:"name" `
}

type FeishuProjectDocument struct {
	feishu.FeishuProjectDocument
}

type ListResponse struct {
	FeishuProjectDocument
}

type Request struct {
	Id uint `json:"id"`
}

func (a *FeishuProjectDocument) ModelName() string {
	return ModelName
}

func Model() *feishu.FeishuProjectDocument {
	return &feishu.FeishuProjectDocument{}
}

func (a *FeishuProjectDocument) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (a *FeishuProjectDocument) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (a *FeishuProjectDocument) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (a *FeishuProjectDocument) CreateV2(object interface{}) error {
	return nil
}

func (a *FeishuProjectDocument) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *FeishuProjectDocument) UpdateByPMSDocID(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("pms_doc_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (u *FeishuProjectDocument) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (u *FeishuProjectDocument) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *FeishuProjectDocument) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *FeishuProjectDocument) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *FeishuProjectDocument) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func GetInsertDataByProjectName(projectName string) ([]*FeishuProjectDocument, error) {
	var items []*FeishuProjectDocument
	if err := easygorm.GetEasyGormDb().Model(Model()).Where("project_name =? and record_id=''", projectName).Find(&items).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return items, err
	}
	return items, nil
}

func GetUpdateDataByProjectName(projectName string) ([]*FeishuProjectDocument, error) {
	var items []*FeishuProjectDocument
	if err := easygorm.GetEasyGormDb().Model(Model()).
		Where("project_name =? and record_id!=''", projectName).
		Find(&items).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return items, err
	}
	return items, nil
}

func GetPMSProjectDocumentData(projectName string) error {
	//获取项目信息
	ReSetAutoIncrement()
	projects := dproject.Response{}
	err := easygorm.GetEasyGormDb().Model(&release.ReleaseProject{}).Where("name=?", projectName).Order("name desc").Find(&projects).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return err
	}
	items := []*PmsProjectDocumentResponse{}
	sql := fmt.Sprintf(`
SELECT
    docdata.pms_doc_id,
    docdata.project_name,
    docdata.document_name,
    docdata.requirement,
    docdata.request_id,
    docdata.document_submit_status,
    docdata.review_status,
    docdata.is_submit,
    docdata.is_instorage,
    docdata.document_status,
    docdata.document_category_title,
    docdata.component_packet_name,
    docdata.document_owner_name,
    docdata.document_owner_id,
    docdata.work_packet_name,
    docdata.work_packet_id,
    docdata.add_codes,
    docdata.ported_codes,
    docdata.temporary_ported_codes,
    docdata.total_codes,
    docdata.packet_manager_name,
    docdata.packet_manager_id,
    docdata.pgttm_user_name,
    u.open_id as pgttm_user_id,
    docdata.department_name
FROM (
    SELECT
        ppd.id as pms_doc_id,
        ppd.project_name,
        ppd.document_name,
        COALESCE(NULLIF(COALESCE(mrwp.requirement, pr.request_name), ''), '') as requirement,
        COALESCE(NULLIF(COALESCE(mrwp.request_id, pr.request_id), ''), 0) as request_id,
        ppd.document_submit_status,
        ppd.review_status,
        ppd.is_submit,
        ppd.is_instorage,
        ppd.document_status,
        ppd.document_category_title,
        ppd.component_packet_name,
        ppd.charge_user_name as document_owner_name,
        u.open_id as document_owner_id,
        COALESCE(NULLIF(ppd.work_packet_name, ''), '项目级文档') AS work_packet_name,
        COALESCE(NULLIF(mrwp.work_packet_id, ''), 0) AS work_packet_id,
        COALESCE(ROUND(mrwp.add_codes, 2), 0) AS add_codes,
        COALESCE(ROUND(mrwp.ported_codes, 2), 0) AS ported_codes,
        COALESCE(ROUND(mrwp.temporary_ported_codes, 2), 0) AS temporary_ported_codes,
        COALESCE(ROUND(mrwp.total_codes, 2), 0) AS total_codes,
        COALESCE(mrwp.packet_manager_name, ppd.charge_user_name) as packet_manager_name,
        COALESCE(mrwp.packet_manager_id, u.open_id) as packet_manager_id,
        COALESCE(mrwp.pgttm_user_name, pr.pgttm_user_name) as pgttm_user_name,
        d.name as department_name
    FROM
        pms_project_documents ppd
    LEFT JOIN pms_document_request_relations pdrr ON ppd.id = pdrr.request_document_id AND pdrr.disabled = 0
    LEFT JOIN pms_requests pr ON pr.request_id = pdrr.project_request_id AND pr.disabled = 0
    LEFT JOIN (
        SELECT
            wp.work_packet_id,
            wp.work_packet_name,
            pr.project_name,
            wp.request_id,
            ROUND(SUM(wp.add_codes), 2) as add_codes,
            ROUND(SUM(wp.total_codes), 2) as total_codes,
            ROUND(SUM(wp.transplant_codes), 2) as ported_codes,
            ROUND(SUM(wp.transplant_codes_tmp), 2) as temporary_ported_codes,
            wp.packet_manager_name,
            u.open_id as packet_manager_id,
            pr.request_name as requirement,
            pr.pgttm_user_name
        FROM
            pms_workpacket_infos wp
        LEFT JOIN users u ON wp.packet_manager_name = u.name
        LEFT JOIN pms_requests pr ON wp.request_id = pr.request_id AND pr.disabled = 0
        WHERE
            wp.project_name = "%s"
            AND wp.disabled = 0
        GROUP BY
            wp.work_packet_id,
            wp.work_packet_name,
            wp.request_id,
            wp.packet_manager_name,
            u.open_id,
            pr.request_name,
            pr.pgttm_user_name
    ) mrwp ON ppd.work_packet_name = mrwp.work_packet_name
    LEFT JOIN users u ON ppd.charge_user_name = u.name
    LEFT JOIN user_departments ud ON ud.user_id = u.id
    LEFT JOIN departments d ON d.id = ud.department_id
    WHERE
        ppd.project_name = "%s"
        AND ppd.document_category_title IN (
            "CREF", "ITR", "ITC", "SRS", "HLD", "FRD", "LLD", ".yang", "TWP", "ITP", "MIB",
            "PRD", "PSS", "SFS", "Syslog", "STP", "TP"
        )
        AND ppd.disabled = 0
) docdata
LEFT JOIN users u ON docdata.pgttm_user_name = u.name;`, projectName, projectName)

	err = easygorm.GetEasyGormDb().Table("pms_project_documents").Raw(sql).Scan(&items).Error
	if err != nil {
		return err
	}

	objects := []map[string]interface{}{}
	for _, item := range items {

		documentName := item.DocumentName
		extension := filepath.Ext(documentName)

		var fileName, documentType string
		if extension == "" {
			// 当没有扩展名时
			fileName = documentName
			documentType = "docx"
		} else {
			// 正常处理有扩展名的情况
			fileName = strings.TrimSuffix(filepath.Base(documentName), extension)
			documentType = strings.TrimPrefix(extension, ".")
		}
		object := map[string]interface{}{
			"PMSDocID":              item.PMSDocID,
			"ProjectName":           item.ProjectName,
			"DocumentName":          item.DocumentName,
			"DocumentSubmitStatus":  item.DocumentSubmitStatus,
			"ReviewStatus":          item.ReviewStatus,
			"IsSubmit":              item.IsSubmit,
			"IsInstorage":           item.IsInstorage,
			"DocumentStatus":        item.DocumentStatus,
			"ComponentPacketName":   item.ComponentPacketName,
			"DocumentCategoryTitle": item.DocumentCategoryTitle,

			"FileName":             fileName,
			"DocumentType":         documentType,
			"WorkPacketName":       item.WorkPacketName,
			"PacketManagerName":    item.PacketManagerName,
			"DocumentOwnerName":    item.DocumentOwnerName,
			"DocumentOwnerID":      item.DocumentOwnerID,
			"PacketManagerID":      item.PacketManagerID,
			"Requirement":          item.Requirement,
			"TotalCodes":           item.TotalCodes,
			"AddCodes":             item.AddCodes,
			"PortedCodes":          item.PortedCodes,
			"TemporaryPortedCodes": item.TemporaryPortedCodes,
			"DepartmentName":       item.DepartmentName,
			"RequestID":            item.RequestID,
			"WorkPacketID":         item.WorkPacketID,
			"PgttmUserName":        item.PacketManagerName,
			"PgttmUserID":          item.PgttmUserID,

			"CreatedAt": time.Now(),
			"UpdatedAt": time.Now(),
		}

		objects = append(objects, object)
	}

	columns := []string{"updated_at"}

	xt := reflect.TypeOf(Model())
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")
		if ok && ok2 {
			columns = append(columns, name)
		}
	}
	db := easygorm.GetEasyGormDb()
	err = db.Transaction(func(tx *gorm.DB) error {
		if len(objects) > 0 {
			err := tx.Model(Model()).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "pms_doc_id"}, {Name: "project_name"}, {Name: "request_id"}, {Name: "work_packet_id"}},
				DoUpdates: clause.AssignmentColumns(columns),
			}).Create(&objects).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil

}

func GetPMSProjectDocumentDisabledData(projectName string) ([]*FeishuProjectDocument, error) {
	// //获取项目信息
	// ReSetAutoIncrement()
	projects := dproject.Response{}
	items := []*PmsProjectDocumentResponse{}
	var docItems []*FeishuProjectDocument
	err := easygorm.GetEasyGormDb().Model(&release.ReleaseProject{}).Where("name=?", projectName).Order("name desc").Find(&projects).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return docItems, err
	}
	sql := fmt.Sprintf(`
			SELECT
			ppd.id as pms_doc_id,
			project_name,
			document_name,
			document_submit_status,
			review_status,
			is_submit,
			is_instorage,
			document_status,
			document_category_title,
			component_packet_name,
			mrwp.requirement as requirement,
			u.open_id as document_owner_name,
			COALESCE(NULLIF(ppd.work_packet_name, ''), '项目级文档') AS work_packet_name,
			COALESCE(ROUND(mrwp.code_quantity,2), 0) AS add_codes,
			COALESCE(ROUND(mrwp.ported_code_quantity,2), 0) AS ported_codes,
			COALESCE(ROUND(mrwp.temporary_ported_code_quantity,2), 0) AS temporary_ported_codes,
			COALESCE(ROUND(mrwp.total_code_quantity,2), 0) AS total_codes,
			COALESCE(mrwp.uname,charge_user_name) as packet_manager_name,
			COALESCE(mrwp.open_id,u.open_id) as packet_manager_id,
			d.name as department_name
		FROM
			pms_project_documents ppd
		left join (
			select
		     mrwp2.name,requirement,pstl_id,code_quantity, ported_code_quantity,temporary_ported_code_quantity,total_code_quantity,u.open_id ,u.name as uname
			from
				merge_request_work_packages  mrwp2
			left JOIN users u on  mrwp2.owner_id=u.id 
			WHERE
				release_project_id = "%d") mrwp 
		on ppd.work_packet_name = mrwp.name
		left JOIN users u on
			ppd.charge_user_name = u.name
	    LEFT JOIN user_departments ud ON
	        ud.user_id = u.id
		LEFT JOIN departments d ON
	         d.id = ud.department_id
		WHERE
			ppd.project_name = "%s" AND ppd.document_category_title IN(
	"CREF","ITR","ITC","SRS","HLD","FRD","LLD",".yang","TWP","ITP","MIB",
	"PRD","PSS","SFS","Syslog","STP","TP") and ppd.disabled =1;`, projects.ID, projectName)

	err = easygorm.GetEasyGormDb().Table("pms_project_documents").Raw(sql).Scan(&items).Error
	if err != nil {
		return docItems, err
	}

	for _, item := range items {
		doc, err := GetDocumentByPMSDocIDAndProjectName(uint64(item.PMSDocID), projectName)
		if err != nil {
			continue
		}
		if doc.ID > 0 {
			docItems = append(docItems, &doc)
		}
	}
	return docItems, err
}

func UpdateDocumentDataByProjectName(projectName, fileName string, object map[string]interface{}) error {
	if err := easygorm.GetEasyGormDb().Model(Model()).Where("project_name =? and file_name=?", projectName, fileName).Updates(object).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return err
	}
	return nil
}

func UpdateByRecordID(recordID string, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("record_id = ?", recordID).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func GetDocumentReviewByProjectName(projectName string) ([]*FeishuProjectDocument, error) {
	var items []*FeishuProjectDocument
	if err := easygorm.GetEasyGormDb().Model(Model()).
		Where("project_name =? and document_review_status ='评审开始' and record_id!=''", projectName).
		Find(&items).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return items, err
	}
	return items, nil
}

func GetDocumentDataByFileName(projectName, fileName string) (FeishuProjectDocument, error) {
	var document FeishuProjectDocument
	if err := easygorm.GetEasyGormDb().Model(Model()).
		Where("project_name =? and file_name=?", projectName, fileName).Find(&document).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return document, err
	}
	if document.ID == 0 {
		if err := easygorm.GetEasyGormDb().Model(Model()).
			Where("project_name =? and document_name=?", projectName, fileName).Find(&document).Error; err != nil {
			logging.ErrorLogger.Errorf("get all status err ", err)
			return document, err
		}
	}
	return document, nil
}

func UpdateDocumentDataByID(id uint, object map[string]interface{}) error {
	if err := easygorm.GetEasyGormDb().Model(Model()).Where("id =?", id).Updates(object).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return err
	}
	return nil
}

func GetDocumentReviewByRecordID(recordID string) (FeishuProjectDocument, error) {
	var document FeishuProjectDocument
	if err := easygorm.GetEasyGormDb().Model(Model()).
		Where("record_id=?", recordID).
		Find(&document).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return document, err
	}
	return document, nil
}

func GetDocumentAllJudge() ([]*DocumentJudge, error) {
	items := []*DocumentJudge{}
	sql := fmt.Sprintf(`
	SELECT 	project_name,
	reviewData.open_id,
	u.username as user_name,u.name  from 
	(SELECT project_name, 
		SUBSTRING_INDEX(SUBSTRING_INDEX(t.required_judge, '|', numbers.n), '|', -1) AS open_id
	FROM feishu_project_documents t
	CROSS JOIN (
		SELECT 1 n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10  UNION ALL SELECT 11 UNION ALL SELECT 12 UNION ALL SELECT 13 UNION ALL SELECT 14 UNION ALL SELECT 15 UNION ALL SELECT 16 UNION ALL SELECT 17 UNION ALL SELECT 18 UNION ALL SELECT 19 UNION ALL SELECT 20
	) numbers
	WHERE n <= 1 + LENGTH(t.required_judge) - LENGTH(REPLACE(t.required_judge, '|', ''))
	AND SUBSTRING_INDEX(SUBSTRING_INDEX(t.required_judge, '|', numbers.n), '|', -1) <> ''
	UNION
	SELECT project_name, 
		SUBSTRING_INDEX(SUBSTRING_INDEX(t.optional_judge, '|', numbers.n), '|', -1) AS open_id
	FROM feishu_project_documents t
	CROSS JOIN (
		SELECT 1 n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10  UNION ALL SELECT 11 UNION ALL SELECT 12 UNION ALL SELECT 13 UNION ALL SELECT 14 UNION ALL SELECT 15 UNION ALL SELECT 16 UNION ALL SELECT 17 UNION ALL SELECT 18 UNION ALL SELECT 19 UNION ALL SELECT 20
	) numbers
	WHERE n <= 1 + LENGTH(t.optional_judge) - LENGTH(REPLACE(t.optional_judge, '|', ''))
	AND SUBSTRING_INDEX(SUBSTRING_INDEX(t.optional_judge, '|', numbers.n), '|', -1) <> '' )as reviewData 
	left JOIN users u on
		reviewData.open_id = u.open_id 
	GROUP BY project_name,open_id,u.username;`)
	err := easygorm.GetEasyGormDb().Table("pms_project_documents").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil

}

func GetDocumentAllJudgeAndDOC() ([]*DocumentANDJudge, error) {
	items := []*DocumentANDJudge{}
	sql := fmt.Sprintf(`
	SELECT 	project_name,
	reviewData.open_id,
	reviewData.document_name,
	u.username as user_name,u.name  from 
	(SELECT project_name, document_name,
		SUBSTRING_INDEX(SUBSTRING_INDEX(t.required_judge, '|', numbers.n), '|', -1) AS open_id
	FROM feishu_project_documents t
	CROSS JOIN (
		SELECT 1 n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10  UNION ALL SELECT 11 UNION ALL SELECT 12 UNION ALL SELECT 13 UNION ALL SELECT 14 UNION ALL SELECT 15 UNION ALL SELECT 16 UNION ALL SELECT 17 UNION ALL SELECT 18 UNION ALL SELECT 19 UNION ALL SELECT 20
	) numbers
	WHERE n <= 1 + LENGTH(t.required_judge) - LENGTH(REPLACE(t.required_judge, '|', ''))
	AND SUBSTRING_INDEX(SUBSTRING_INDEX(t.required_judge, '|', numbers.n), '|', -1) <> ''
	UNION
	SELECT project_name, document_name,
		SUBSTRING_INDEX(SUBSTRING_INDEX(t.optional_judge, '|', numbers.n), '|', -1) AS open_id
	FROM feishu_project_documents t
	CROSS JOIN (
		SELECT 1 n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10  UNION ALL SELECT 11 UNION ALL SELECT 12 UNION ALL SELECT 13 UNION ALL SELECT 14 UNION ALL SELECT 15 UNION ALL SELECT 16 UNION ALL SELECT 17 UNION ALL SELECT 18 UNION ALL SELECT 19 UNION ALL SELECT 20
	) numbers
	WHERE n <= 1 + LENGTH(t.optional_judge) - LENGTH(REPLACE(t.optional_judge, '|', ''))
	AND SUBSTRING_INDEX(SUBSTRING_INDEX(t.optional_judge, '|', numbers.n), '|', -1) <> '' )as reviewData 
	left JOIN users u on
		reviewData.open_id = u.open_id 
	GROUP BY project_name,reviewData.document_name,open_id,u.username;`)
	err := easygorm.GetEasyGormDb().Table("pms_project_documents").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil

}

func GetDocumentWorkPacket() ([]*DocumentWorkPacket, error) {
	items := []*DocumentWorkPacket{}
	sql := fmt.Sprintf(`
SELECT
		work_packet_name,
		project_name,
		packet_manager_name,
		packet_manager_id,
		ROUND(MAX(total_codes), 2) as total_codes
from
		feishu_project_documents fpd
WHERE
	work_packet_name <> "项目级文档"
group by
		work_packet_name,
		packet_manager_name,
		packet_manager_id,
		project_name;`)
	err := easygorm.GetEasyGormDb().Table("pms_project_documents").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func GetDocumentTypeWorkPacket() ([]*DocumentTypeWorkPacket, error) {
	items := []*DocumentTypeWorkPacket{}
	sql := fmt.Sprintf(`
	SELECT
		work_packet_name,
		project_name,
		document_category_title
	from
		feishu_project_documents fpd
	group by
		work_packet_name,
		project_name,
		document_category_title;`)
	err := easygorm.GetEasyGormDb().Table("pms_project_documents").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func GetDocumentPSTLByProjectName(projectName, departmentName string) ([]*DocumentPSTL, error) {
	pstl := []*DocumentPSTL{}
	sql := fmt.Sprintf(`
		SELECT
			ppm.project_name, d.name as department_name,u.name,u.username,u.open_id
		FROM
			pms_project_members ppm
		left JOIN users u on
			ppm.username  = u.name
		LEFT JOIN user_departments ud ON
			ud.user_id = u.id
		LEFT JOIN departments d ON
			d.id = ud.department_id
		WHERE
			project_name = "%s" AND disabled =0 AND user_role ="PSTL" AND d.name="%s";`, projectName, departmentName)
	err := easygorm.GetEasyGormDb().Table("pms_project_documents").Raw(sql).Scan(&pstl).Error
	if err != nil {
		return pstl, err
	}
	return pstl, nil
}

func GetDocumentRequest() ([]*DocumentRequest, error) {
	items := []*DocumentRequest{}
	sql := fmt.Sprintf(`
		SELECT
		request_data.requirement,
		request_data.request_id,
		request_data.project_name,
		request_data.pgttm_user_id,
		request_data.total_codes
from
	(
	SELECT
		requirement,
		request_id,
		project_name,
		pgttm_user_id,
		ROUND(SUM(total_codes), 2) as total_codes
	from
		feishu_project_documents fpd
	WHERE
		request_id >0
	group by
		requirement,
		request_id,
		project_name,
		pgttm_user_id) request_data
LEFT JOIN pms_requests pr on
	pr.request_id = request_data.request_id
WHERE
		pr.disabled = 0
	AND pr.request_status <> "DISABLED";`)
	err := easygorm.GetEasyGormDb().Table("pms_project_documents").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func GetDocumentTypeRequest() ([]*DocumentTypeRequest, error) {
	items := []*DocumentTypeRequest{}
	sql := fmt.Sprintf(`
	SELECT
		request_data.requirement,
		request_data.request_id,
		request_data.project_name,
		request_data.document_category_title
from (SELECT
		requirement,
		request_id,
		project_name,
		document_category_title
from
		feishu_project_documents fpd
WHERE
	request_id >0
group by
		requirement,
		request_id,
		project_name,
		document_category_title) request_data
LEFT JOIN pms_requests pr on
	pr.request_id = request_data.request_id
WHERE
		pr.disabled = 0
	AND pr.request_status <> "DISABLED";`)
	err := easygorm.GetEasyGormDb().Table("pms_project_documents").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func GetDocumentByPMSDocID(PMSDocID uint64) (FeishuProjectDocument, error) {
	var document FeishuProjectDocument
	if err := easygorm.GetEasyGormDb().Model(Model()).
		Where("pms_doc_id=?", PMSDocID).
		Find(&document).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return document, err
	}
	return document, nil
}

func UpdateByPMSDocID(PMSDocID uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("pms_doc_id = ?", PMSDocID).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func ReSetAutoIncrement() error {
	var maxID int
	err := easygorm.GetEasyGormDb().Table("feishu_project_documents").Select("MAX(id)").Scan(&maxID).Error
	if err != nil {
		return err
	}
	newAutoIncrement := maxID + 1
	alterTableSQL := fmt.Sprintf("ALTER TABLE feishu_project_documents AUTO_INCREMENT=%d", newAutoIncrement)
	if err := easygorm.GetEasyGormDb().Exec(alterTableSQL).Error; err != nil {
		logging.ErrorLogger.Errorf("ALTER TABLE feishu_project_documents  get err ", err)
		return err
	}
	return nil
}

func GetDocumentByPMSDocIDAndProjectName(PMSDocID uint64, projectName string) (FeishuProjectDocument, error) {
	var document FeishuProjectDocument
	if err := easygorm.GetEasyGormDb().Model(Model()).
		Where("pms_doc_id=? and project_name=?", PMSDocID, projectName).
		Find(&document).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return document, err
	}
	return document, nil
}
