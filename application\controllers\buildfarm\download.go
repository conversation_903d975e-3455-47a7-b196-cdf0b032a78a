package buildfarm

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"irisAdminApi/application/controllers/ip"
	"irisAdminApi/application/libs"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/buildfarm/dcronmakejob"
	"irisAdminApi/service/dao/buildfarm/dmakejob"
	"irisAdminApi/service/dao/user/duserwhitelist"

	"github.com/gabriel-vasile/mimetype"
	"github.com/kataras/iris/v12"
)

// func CheckIP(ctx iris.Context) (string, bool, error) {
// 	var ip = Ip{}

// 	addr := GetRemoteAddr(ctx)
// 	if addr == "" {
// 		return "IP地址不合法", false, nil
// 	}

// 	err := easygorm.GetEasyGormDb().Model(&fileout.AllowAuditIp{}).Where("ip = ? or ip = ?", addr, strings.Join(strings.Split(addr, ".")[:3], ".")+"0").Find(&ip).Error
// 	if err != nil {
// 		return "数据库错误: " + err.Error(), false, err
// 	}

// 	if ip.ID == 0 {
// 		return "不在允许的IP范围，请切换至云办公环境", false, nil
// 	}
// 	fmt.Println(addr, ip)
// 	return "", true, nil
// }

func CheckIsKVM(jobID string) (bool, error) {
	// 权限校验
	isKVM, err := CheckMakeJob(jobID)
	if err != nil {
		return isKVM, err
	}
	if isKVM {
		return isKVM, nil
	}

	isKVM, err = CheckCronMakeJob(jobID)
	if err != nil {
		return isKVM, err
	}

	if isKVM {
		return isKVM, nil
	}

	// 默认禁止
	return false, nil
}

func CheckMakeJob(jobID string) (bool, error) {
	makeJob := dmakejob.MakeJob{}
	err := makeJob.FindEx("task_id", jobID)
	if err != nil {
		return false, err
	}
	if makeJob.ID > 0 {
		if strings.Contains(makeJob.Product, "kvm") {
			return true, nil
		}
	}
	return false, nil
}

func CheckCronMakeJob(jobID string) (bool, error) {
	cronMakeJob := dcronmakejob.CronMakeJob{}
	err := cronMakeJob.FindEx("job_id", jobID)
	if err != nil {
		return false, err
	}
	if cronMakeJob.Id > 0 {
		if strings.Contains(cronMakeJob.Product, "kvm") {
			return true, nil
		}
	}
	return false, nil
}

func GetArchiveFiles(ctx iris.Context) {
	root := ctx.Params().Get("root")
	if root == "" {
		ctx.StatusCode(iris.StatusForbidden)
		ctx.WriteString("403 - 禁止访问")
		return
	}

	jobID := strings.Split(root, "/")[0]

	// 检查是否KVM
	isKVM, err := CheckIsKVM(jobID)
	if err != nil {
		ctx.StatusCode(iris.StatusInternalServerError)
		ctx.WriteString("500 - 系统错误" + err.Error())
		return
	}

	if isKVM {
		permit, errMsg := ip.CheckIP(ctx)

		if !permit {
			userID, err := dao.GetAuthId(ctx)
			if err != nil {
				ctx.StatusCode(iris.StatusInternalServerError)
				ctx.WriteString("403 - 未登录或者登录过期" + err.Error())
				return
			}

			if userID != 1 {
				whitelist := duserwhitelist.UserWhiteList{}
				err = whitelist.FindEx("user_id", fmt.Sprintf("%v", userID))
				if err != nil {
					ctx.StatusCode(iris.StatusInternalServerError)
					ctx.WriteString("500 - 系统错误" + err.Error())
					return
				}

				if !whitelist.KvmDownload {
					ctx.WriteString("403 - 当前帐户云办公外无权限下载KVM镜像: " + errMsg.Error())
					return
				}
			}

			// if !libs.InArrayUint([]uint{1, 53}, userID) {
			// 	ctx.StatusCode(iris.StatusForbidden)
			// 	ctx.WriteString("403 - 当前帐户云办公外无权限下载KVM镜像: " + errMsg.Error())
			// 	return
			// }
		}
	}

	absPath := filepath.Join(libs.Config.Buildfarm.Archivepath, filepath.Clean(root))

	if fileInfo, err := os.Stat(absPath); err != nil {
		if os.IsNotExist(err) {
			ctx.StatusCode(iris.StatusNotFound)
			ctx.WriteString("404 - File not found")
			return
		}
		ctx.StatusCode(iris.StatusInternalServerError)
		ctx.WriteString("500 - Internal server error")
		return
	} else if fileInfo.IsDir() {
		var parentPath string
		dirSlice := strings.Split(filepath.Clean(root), string(filepath.Separator))
		if len(dirSlice) >= 2 {
			parentPath = filepath.Join(dirSlice[0 : len(dirSlice)-1]...)
		} else {
			parentPath = ""
		}

		files, err := os.ReadDir(absPath)
		if err != nil {
			ctx.StatusCode(iris.StatusInternalServerError)
			ctx.WriteString("500 - Internal server error")
			return
		}
		// parentPath := filepath.Dir(root)
		// 构建HTML内容
		htmlContent := `<html><body>`
		htmlContent += "<h1>Index of " + root + "</h1>"
		htmlContent += "<hr>"
		// 添加返回上一级入口

		htmlContent += "<pre>"
		htmlContent += fmt.Sprintf(`<a href="/output/%s">../</a>
`,
			parentPath,
		)
		for _, file := range files {
			info, _ := file.Info()
			fn := file.Name()
			if len(fn) > 51 {
				fn = fn[0:47] + "..>"
			}
			// filePath := filepath.Join(libs.Config.CoreDump.PathHeader, "/api/v1/coredump/files/", root, file.Name())
			if file.IsDir() {
				// 在文件夹名称后添加特殊符号或文本
				// htmlContent += fmt.Sprintf("<li><a href='%s'>%s📁</a></li>", filePath, file.Name())

				htmlContent += fmt.Sprintf(`<a href="/output/%s">%s/</a>%s%s                   -
`, filepath.Join(filepath.Clean(root), file.Name()), fn, strings.Repeat(" ", 51-len(fn)-1), info.ModTime().Format("2006-01-02 15:04:05"))
			}
		}

		for _, file := range files {
			info, _ := file.Info()
			fn := file.Name()
			if len(fn) > 51 {
				fn = fn[0:47] + "..>"
			}
			// filePath := filepath.Join(libs.Config.CoreDump.PathHeader, "/api/v1/coredump/files/", root, file.Name())
			if !file.IsDir() {
				fileSize := fmt.Sprintf("%d", info.Size())
				htmlContent += fmt.Sprintf(`<a href="/output/%s">%s</a>%s%s%s%s
`, filepath.Join(filepath.Clean(root), file.Name()), fn, strings.Repeat(" ", 51-len(fn)), info.ModTime().Format("2006-01-02 15:04:05"), strings.Repeat(" ", 20-len(fileSize)), fileSize)
			}
		}

		htmlContent += "</pre><hr>"
		htmlContent += "</body></html>"
		ctx.HTML(htmlContent)
	} else {
		mime, err := mimetype.DetectFile(absPath)
		if err != nil {
			ctx.StatusCode(iris.StatusInternalServerError)
			ctx.WriteString("500 - Internal server error")
			ctx.WriteString(err.Error())
			return
		}
		if strings.Contains(mime.String(), "text/plain") {
			b, err := os.ReadFile(absPath)
			if err != nil {
				ctx.StatusCode(iris.StatusInternalServerError)
				ctx.WriteString("500 - Internal server error")
				ctx.WriteString(err.Error())
				return
			}
			ctx.Write(b)
			return
		}
		ctx.SendFile(absPath, fileInfo.Name())
		return
	}
}
