package dproductionproc

import (
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	dproductionprocinst "irisAdminApi/service/dao/productionrelease/dproductionprocinst"
	dproductionproctask "irisAdminApi/service/dao/productionrelease/dproductionproctask"
	"irisAdminApi/service/dao/user/duser"
)

type Response struct {
	ID uint `json:"id"`
	// 当前执行流所在的节点
	NodeName string `json:"nodeName"`
	NodeID   string `json:"nodeId"`
	// Step   int    `json:"step"`
	// 流程实例id
	ProcInstID uint `json:"procInstID"`
	Assignee   uint `json:"assignee"`
	// 还未审批的用户数，等于0代表会签已经全部审批结束，默认值为1
	// MemberCount   int8 `json:"memberCount" gorm:"default:1"`
	// UnCompleteNum int8 `json:"unCompleteNum" gorm:"default:1"`
	//审批通过数
	// AgreeNum int8 `json:"agreeNum"`
	// and 为会签，or为或签，默认为or
	// ActType    string `json:"actType" gorm:"default:'or'"`
	Status     uint                          `gorm:"default:0" json:"status"`
	User       *duser.ApprovalResponse       `gorm:"-" json:"user"`
	ProcInst   *dproductionprocinst.Response `gorm:"-" json:"procInst"`
	UpdatedAt  string                        `json:"updated_at"`
	CreatedAt  string                        `json:"created_at"`
	Comment    string                        `json:"comment"`
	Flag       bool                          `json:"flag"`
	Attachment string                        `json:"attachment"`
}

type ListResponse struct {
	Response
}

func AllTasksByAssignee(uid uint, name, sort, orderBy string, page, pageSize int, status, start, end string) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(dproductionproctask.Model())
	db = db.Where("assignee = ?", uid).Where("flag =1")

	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	if len(status) > 0 {
		if status == "0" {
			db = db.Where("status = ?", status)
		} else {
			db = db.Where("status > 0")
		}
	}
	if len(start) > 0 {
		db = db.Where("updated_at >= ?", start)
	}
	if len(end) > 0 {
		db = db.Where("updated_at <= ?", end)
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	FormatResponse(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func AllTasksByProcInst(id uint, name, sort, orderBy string, page, pageSize int, status, start, end string) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(dproductionproctask.Model())
	db = db.Where("proc_inst_id = ?", id)

	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	if len(status) > 0 {
		db = db.Where("status = ?", status)
	} else {
		db = db.Where("status != 0")
	}
	if len(start) > 0 {
		db = db.Where("discovered_at >= ?", start)
	}
	if len(end) > 0 {
		db = db.Where("discovered_at <= ?", end)
	}
	if pageSize == 0 {
		pageSize = 100
	}
	db = db.Order("updated_at desc")
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	FormatAssinee(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func FormatResponse(items []*ListResponse) {
	userIds := []uint{}
	procInstIDs := []uint{}
	for _, item := range items {
		procInstIDs = append(procInstIDs, item.ProcInstID)
		userIds = append(userIds, item.Assignee)
	}
	users, _ := duser.FindSimpleInIds(userIds)
	var userMap = make(map[uint]*duser.ApprovalResponse)
	for _, user := range users {
		userMap[user.Id] = user
	}

	procInsts, _ := dproductionprocinst.FindInIds(procInstIDs)
	var procInstMap = map[uint]*dproductionprocinst.Response{}
	for _, procInst := range procInsts {
		procInstMap[procInst.ID] = &procInst.Response
	}
	for _, item := range items {
		item.User = userMap[item.Assignee]
		item.ProcInst = procInstMap[item.ProcInstID]
	}
}

func FormatAssinee(items []*ListResponse) {
	userIds := []uint{}
	for _, item := range items {
		userIds = append(userIds, item.Assignee)
	}
	users, _ := duser.FindSimpleInIds(userIds)
	var userMap = make(map[uint]*duser.ApprovalResponse)
	for _, user := range users {
		userMap[user.Id] = user
	}
	for _, item := range items {
		item.User = userMap[item.Assignee]
	}
}
