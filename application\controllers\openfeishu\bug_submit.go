package openfeishu

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"

	"github.com/kataras/iris/v12"
)

// BugInfo Bug信息结构体
type BugInfo struct {
	OS                    string `json:"os"`                    // 操作系统
	TestMethod            string `json:"testMethod"`            // 测试方法
	Product               string `json:"product"`               // 产品名称
	TestCase              string `json:"testCase"`              // 测试用例
	PstlCasUserId         string `json:"pstlCasUserId"`         // PSTL
	ChargeCasUserId       string `json:"chargeCasUserId"`       // bug负责人
	Source                string `json:"source"`                // 来源
	TestCaseNum           string `json:"testCaseNum"`           // 测试用例编号
	Summary               string `json:"summary"`               // bug简介
	TestTopo              string `json:"testTopo"`              // 测试拓扑
	TopoDesc              string `json:"topoDesc"`              // 拓扑描述
	DebugMess             string `json:"debugMess"`             // debug信息(约定传入url链接)
	VersionMess           string `json:"versionMess"`           // 版本信息
	DeviceUnderTestConfig string `json:"deviceUnderTestConfig"` // 被测设备配置
	Repeat                string `json:"repeat"`                // 可重复性
	Priority              string `json:"priority"`              // 严重性
	Severity              string `json:"severity"`              // 优先级
	LegacyBug             string `json:"legacyBug"`             // 遗留Bug
	AffectCaseCount       string `json:"affectCaseCount"`       // 影响用例执行数
	TestabilitySpecial    string `json:"testabilitySpecial"`    // 可测试性专题
	Locate                string `json:"locate"`                // 定位信息
	SubmitterCasUserId    string `json:"submitterCasUserId"`    // 提交者
	Ccusers               string `json:"ccusers"`               // 抄送者
	PkIds                 string `json:"pkIds"`                 // PK IDs
	Bugdescription        string `json:"bugDescription"`        // Bug描述
	OneKeyCollectionInfo  string `json:"oneKeyCollectionInfo"`  // 一键收集信息
}

// BugSubmitResponse Bug提交响应结构体
type BugSubmitResponse struct {
	Message string `json:"message"`
	Type    string `json:"type"`
	Content string `json:"content"`
	Code    int    `json:"code,omitempty"`
}

// BugSubmitter Bug提交器
type BugSubmitter struct {
	URL string // Bug提交API地址
}

// NewBugSubmitter 创建Bug提交器实例
func NewBugSubmitter() *BugSubmitter {
	return &BugSubmitter{
		URL: "http://bugs.ruijie.com.cn/bug_switch/service/outInterface_submitBugInfo",
	}
}

// NewDefaultBugInfo 创建默认的Bug信息（基于原Java代码的示例数据）
func NewDefaultBugInfo() *BugInfo {
	rand.Seed(time.Now().UnixNano())
	randomNum := rand.Uint64()

	return &BugInfo{
		OS:                    "CSBU-数智化建设解决方案-2025",
		TestMethod:            "场景测试",
		Product:               "其他",
		TestCase:              "TEST-ITC",
		PstlCasUserId:         "suchen1",
		ChargeCasUserId:       "suchen1",
		Source:                "开发测试(单元测试)",
		TestCaseNum:           fmt.Sprintf("xxx_%d", randomNum),
		Summary:               fmt.Sprintf("自动提交BUG001%d", randomNum),
		TestTopo:              "测试拓扑",
		TopoDesc:              "拓扑描述",
		DebugMess:             "http://*************:8080/resManage/",
		VersionMess:           "版本信息",
		DeviceUnderTestConfig: "被测设备配置",
		Repeat:                "有时重现",
		Priority:              "Major",
		Severity:              "Normal",
		LegacyBug:             "否",
		AffectCaseCount:       "0",
		TestabilitySpecial:    "",
		Locate:                "",
		SubmitterCasUserId:    "admin",
		Ccusers:               "",
		PkIds:                 "35561",
		Bugdescription:        "1<p>1、说明预期结果:1</p><p><p><br/></p><p>2说明实际结果:1</p><p><br/></p><p>3、说明问题出现步骤:1</p><p><br/></p><p><br/></p>",
		OneKeyCollectionInfo:  "<p>1、说明预期结果:1</p><p><br/></p><p>2、说明实际结果:1</p><p><br/></p><p>3、说明问题出现步骤:1</p><p><br/></p><p><br/></p>",
	}
}

// SubmitBug 提交Bug信息到锐捷Bug管理系统
func (bs *BugSubmitter) SubmitBug(bugInfo *BugInfo) (*BugSubmitResponse, error) {
	if bugInfo == nil {
		return nil, fmt.Errorf("Bug信息不能为空")
	}

	// 将结构体转换为map[string]interface{}以适配现有的HTTP请求库
	bugData := make(map[string]interface{})
	bugJSON, err := json.Marshal(bugInfo)
	if err != nil {
		logging.ErrorLogger.Errorf("序列化Bug信息失败: %v", err)
		return nil, fmt.Errorf("序列化Bug信息失败: %w", err)
	}

	err = json.Unmarshal(bugJSON, &bugData)
	if err != nil {
		logging.ErrorLogger.Errorf("转换Bug信息失败: %v", err)
		return nil, fmt.Errorf("转换Bug信息失败: %w", err)
	}

	// 记录请求日志
	logging.InfoLogger.Info("准备提交Bug信息到: %s", bs.URL)
	requestJSON, _ := json.MarshalIndent(bugData, "", "  ")
	logging.InfoLogger.Info("请求的JSON参数: %s", string(requestJSON))

	// 发送HTTP POST请求
	statusCode, headers, responseBody, err := libs.PostJson(bs.URL, bugData, nil)
	if err != nil {
		logging.ErrorLogger.Errorf("发送Bug提交请求失败: %v", err)
		return nil, fmt.Errorf("发送Bug提交请求失败: %w", err)
	}

	// 检查HTTP状态码
	if statusCode != 200 {
		logging.ErrorLogger.Errorf("Bug提交请求返回异常状态码: %d", statusCode)
		return nil, fmt.Errorf("Bug提交请求失败，状态码: %d", statusCode)
	}

	// 解析响应
	var response BugSubmitResponse
	if len(responseBody) > 0 {
		err = json.Unmarshal(responseBody, &response)
		if err != nil {
			logging.ErrorLogger.Errorf("解析Bug提交响应失败: %v, 响应内容: %s", err, string(responseBody))
			// 即使解析失败，也返回原始响应内容
			return &BugSubmitResponse{
				Message: string(responseBody),
				Type:    "unknown",
			}, nil
		}
	}

	// 记录响应日志
	logging.InfoLogger.Info("Bug提交响应: Message=%s, Type=%s,Content=%s", response.Message, response.Type, response.Content)
	if libs.Config.Debug {
		logging.InfoLogger.Info("响应头信息: %+v", headers)
	}

	return &response, nil
}

// TestBugSubmit 测试Bug提交功能
func TestBugSubmit(ctx iris.Context) {
	logging.InfoLogger.Info("开始测试Bug提交功能")

	// 创建Bug提交器
	submitter := NewBugSubmitter()

	// 创建默认Bug信息
	bugInfo := NewDefaultBugInfo()

	// 提交Bug
	response, err := submitter.SubmitBug(bugInfo)
	if err != nil {
		logging.ErrorLogger.Errorf("Bug提交测试失败: %v", err)
		fmt.Printf("Bug提交失败: %v\n", err)
		return
	}

	// 输出结果
	if response != nil {
		fmt.Printf("请求成功!\n")
		fmt.Printf("响应消息: %s\n", response.Message)
		fmt.Printf("响应类型: %s\n", response.Type)
		fmt.Printf("响应内容: %s\n", response.Content)
		logging.InfoLogger.Infof("Bug提交测试成功: Message=%s, Type=%s", response.Message, response.Type)
	}
}

// SubmitCustomBug 提交自定义Bug信息
func (bs *BugSubmitter) SubmitCustomBug(
	summary, description, submitter, chargeUser string,
	priority, severity string) (*BugSubmitResponse, error) {

	// 创建自定义Bug信息
	bugInfo := NewDefaultBugInfo()
	bugInfo.Summary = summary
	bugInfo.Bugdescription = description
	bugInfo.SubmitterCasUserId = submitter
	bugInfo.ChargeCasUserId = chargeUser
	bugInfo.Priority = priority
	bugInfo.Severity = severity

	// 生成唯一的测试用例编号
	rand.Seed(time.Now().UnixNano())
	bugInfo.TestCaseNum = fmt.Sprintf("CUSTOM_%d_%f", time.Now().Unix(), rand.Float64())

	return bs.SubmitBug(bugInfo)
}
