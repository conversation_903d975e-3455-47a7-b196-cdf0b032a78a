package libs

import (
	"context"
	"fmt"
	"time"

	"irisAdminApi/application/controllers/openfeishu/meeting/models"
	"irisAdminApi/application/logging"
)

// ErrorHandler 错误处理器
type ErrorHandler struct {
	retryConfig models.RetryConfig
	metrics     *MetricsCollector
}

// NewErrorHandler 创建错误处理器
func NewErrorHandler(retryConfig models.RetryConfig) *ErrorHandler {
	return &ErrorHandler{
		retryConfig: retryConfig,
		metrics:     GetGlobalMetrics(),
	}
}

// HandleError 处理错误
func (e *ErrorHandler) HandleError(err error, component string) *models.ErrorInfo {
	if err == nil {
		return nil
	}

	errorInfo := &models.ErrorInfo{
		Code:      e.classifyError(err),
		Message:   err.Error(),
		Details:   fmt.Sprintf("组件: %s", component),
		Timestamp: time.Now(),
	}

	// 记录错误指标
	e.metrics.GetMetrics().RecordError(errorInfo.Code, component)

	// 记录错误日志
	logging.ErrorLogger.Errorf("错误处理: 组件=%s, 错误码=%s, 错误信息=%s",
		component, errorInfo.Code, errorInfo.Message)

	return errorInfo
}

// classifyError 错误分类
func (e *ErrorHandler) classifyError(err error) string {
	errMsg := err.Error()

	// API相关错误
	if containsAny(errMsg, []string{"API", "api", "调用失败", "网络", "超时", "timeout"}) {
		return models.ErrCodeAPIError
	}

	// 配置相关错误
	if containsAny(errMsg, []string{"配置", "config", "未配置", "不能为空"}) {
		return models.ErrCodeConfigError
	}

	// 验证相关错误
	if containsAny(errMsg, []string{"验证", "validation", "格式", "无效", "不合法"}) {
		return models.ErrCodeInvalidRequest
	}

	// 处理相关错误
	if containsAny(errMsg, []string{"处理", "process", "转换", "映射"}) {
		return models.ErrCodeProcessingError
	}

	// 默认为无效请求
	return models.ErrCodeInvalidRequest
}

// containsAny 检查字符串是否包含任意一个子字符串
func containsAny(str string, substrings []string) bool {
	for _, substr := range substrings {
		if len(str) >= len(substr) {
			for i := 0; i <= len(str)-len(substr); i++ {
				if str[i:i+len(substr)] == substr {
					return true
				}
			}
		}
	}
	return false
}

// RetryWithBackoff 带退避的重试机制
func (e *ErrorHandler) RetryWithBackoff(ctx context.Context, operation func() error, operationName string) error {
	var lastErr error

	for attempt := 1; attempt <= e.retryConfig.MaxRetries; attempt++ {
		// 执行操作
		err := operation()
		if err == nil {
			if attempt > 1 {
				logging.InfoLogger.Infof("操作 %s 在第 %d 次尝试后成功", operationName, attempt)
			}
			return nil
		}

		lastErr = err
		logging.ErrorLogger.Errorf("操作 %s 第 %d 次尝试失败: %v", operationName, attempt, err)

		// 如果是最后一次尝试，不再等待
		if attempt == e.retryConfig.MaxRetries {
			break
		}

		// 计算退避时间
		backoffDuration := e.calculateBackoff(attempt)
		logging.InfoLogger.Infof("操作 %s 将在 %v 后重试", operationName, backoffDuration)

		// 等待退避时间或上下文取消
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(backoffDuration):
			continue
		}
	}

	// 记录重试耗尽错误
	e.metrics.GetMetrics().RecordError(models.ErrCodeProcessingError, operationName)
	return fmt.Errorf("操作 %s 重试 %d 次后仍然失败: %w", operationName, e.retryConfig.MaxRetries, lastErr)
}

// calculateBackoff 计算退避时间
func (e *ErrorHandler) calculateBackoff(attempt int) time.Duration {
	// 指数退避算法
	backoff := time.Duration(float64(e.retryConfig.BaseDelay) *
		pow(e.retryConfig.BackoffFactor, float64(attempt-1)))

	// 限制最大退避时间
	if backoff > e.retryConfig.MaxDelay {
		backoff = e.retryConfig.MaxDelay
	}

	return backoff
}

// pow 简单的幂运算实现
func pow(base float64, exp float64) float64 {
	if exp == 0 {
		return 1
	}
	result := base
	for i := 1; i < int(exp); i++ {
		result *= base
	}
	return result
}

// IsRetryableError 判断错误是否可重试
func (e *ErrorHandler) IsRetryableError(err error) bool {
	if err == nil {
		return false
	}

	errMsg := err.Error()

	// 网络相关错误通常可重试
	if containsAny(errMsg, []string{"网络", "超时", "timeout", "connection", "网络连接"}) {
		return true
	}

	// API限流错误可重试
	if containsAny(errMsg, []string{"限流", "rate limit", "too many requests"}) {
		return true
	}

	// 临时性错误可重试
	if containsAny(errMsg, []string{"临时", "temporary", "服务不可用", "service unavailable"}) {
		return true
	}

	// 配置错误和验证错误通常不可重试
	if containsAny(errMsg, []string{"配置", "验证", "格式", "无效"}) {
		return false
	}

	// 默认不重试
	return false
}

// WrapError 包装错误信息
func (e *ErrorHandler) WrapError(err error, context string) error {
	if err == nil {
		return nil
	}
	return fmt.Errorf("%s: %w", context, err)
}

// CreateProcessResult 创建处理结果
func (e *ErrorHandler) CreateProcessResult(recordID string, err error, component string) *models.ProcessResult {
	result := &models.ProcessResult{
		RecordID:    recordID,
		ProcessTime: time.Now(),
	}

	if err != nil {
		errorInfo := e.HandleError(err, component)
		result.Status = models.StatusFailed
		result.ErrorMessage = errorInfo.Message
	} else {
		result.Status = models.StatusSuccess
	}

	return result
}

// GetDefaultRetryConfig 获取默认重试配置
func GetDefaultRetryConfig() models.RetryConfig {
	return models.RetryConfig{
		MaxRetries:    3,
		BaseDelay:     1 * time.Second,
		MaxDelay:      30 * time.Second,
		BackoffFactor: 2.0,
	}
}

// ErrorRecoveryMiddleware 错误恢复中间件
func ErrorRecoveryMiddleware(handler func() error, component string) error {
	defer func() {
		if r := recover(); r != nil {
			logging.ErrorLogger.Errorf("组件 %s 发生panic并已恢复: %v", component, r)
		}
	}()

	return handler()
}
