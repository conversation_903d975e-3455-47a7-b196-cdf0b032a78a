package dperformance

import (
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/models"
	"irisAdminApi/application/models/performance"
	"reflect"
	"strings"
	"time"
)

const ModelName = "性能数据表"

type Performance struct {
	models.ModelBase
	VersionID       uint   `gorm:"not null" json:"version_id" validate:"required"`
	Version         string `gorm:"not null; type:varchar(60)" json:"version"`
	Product         string `gorm:"not null; type:varchar(60)" json:"product" validate:"required"`
	Branch          string `gorm:"not null; type:varchar(60)" json:"branch" validate:"required"`
	BasicThroughput uint   `gorm:"not null" json:"basic_throughput" check:"1"`
	BasicCps        uint   `gorm:"not null" json:"basic_cps" check:"1"`
	BasicCc         uint   `gorm:"not null" json:"basic_cc" check:"1"`
	AppidThroughput uint   `gorm:"not null" json:"appid_throughput" check:"1"`
	IpsThroughput   uint   `gorm:"not null" json:"ips_throughput" check:"1"`
	AvThroughput    uint   `gorm:"not null" json:"av_throughput" check:"1"`
	Status          bool   `gorm:"not null" json:"status"`
	Baseline        bool   `gorm:"not null;default:false" json:"baseline"`
}

type ListResponse struct {
	Performance
}

type Request struct {
	VersionID       uint   `gorm:"not null" json:"version_id" validate:"required"`
	Version         string `gorm:"not null; type:varchar(60)" json:"version"`
	Product         string `gorm:"not null; type:varchar(60)" json:"product" validate:"required"`
	Branch          string `gorm:"not null; type:varchar(60)" json:"branch" validate:"required"`
	BasicThroughput string `gorm:"not null" json:"basic_throughput"`
	BasicCps        string `gorm:"not null" json:"basic_cps"`
	BasicCc         string `gorm:"not null" json:"basic_cc"`
	AppidThroughput string `gorm:"not null" json:"appid_throughput"`
	IpsThroughput   string `gorm:"not null" json:"ips_throughput"`
	AvThroughput    string `gorm:"not null" json:"av_throughput"`
	Status          bool   `gorm:"not null" json:"status"`
}

func (a *Performance) ModelName() string {
	return ModelName
}

func Model() *performance.Performance {
	return &performance.Performance{}
}

func (a *Performance) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Preload("Assignees").Preload("Reviewers")
	where := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		where = where.Where("group_name_ch like ?", fmt.Sprintf("%%%s%%", name)).
			Or("group_name_en like ?", fmt.Sprintf("%%%s%%", name)).
			Or("category_name_ch like ?", fmt.Sprintf("%%%s%%", name)).
			Or("category_name_en like ?", fmt.Sprintf("%%%s%%", name)).
			Or("description_ch like ?", fmt.Sprintf("%%%s%%", name)).
			Or("description_en like ?", fmt.Sprintf("%%%s%%", name))
		if strings.Contains("海外库", name) {
			where = where.Or("oversea = 1")
		}
		if strings.Contains("大库", name) {
			where = where.Or("large = 1")
		}
		if strings.Contains("小库", name) {
			where = where.Or("small = 1")
		}
		if strings.Contains("中库", name) {
			where = where.Or("middle = 1")
		}
		if strings.Contains("默认阻断", name) {
			where = where.Or("pre_def_block = 1")
		}
	}
	db = db.Where(where)
	err := db.Count(&count).Error
	if err != nil {
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		return nil, err
	}

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Performance) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Performance) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		return err
	}

	return nil
}

func (this *Performance) CreateV2(object interface{}) error {
	return nil
}

func (a *Performance) BatchCreate(mrs []*Performance) error {
	return nil
}

func (a *Performance) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		return err
	}
	return nil
}

func (a *Performance) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		return err
	}
	return nil
}

func (a *Performance) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		return err
	}
	return nil
}

func (a *Performance) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		return err
	}
	return nil
}

func (u *Performance) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Preload("Assignees").Preload("Reviewers").Where("id = ?", id).Find(u).Error
	if err != nil {
		return err
	}
	return nil
}

func (u *Performance) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		return err
	}
	return nil
}

func (u *Performance) Save() error {
	err := easygorm.GetEasyGormDb().Model(u).Save(u).Error
	if err != nil {
		return err
	}
	return nil
}

func All(versionID, product, branch, version, start, end string, status *bool, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	where := easygorm.GetEasyGormDb().Model(Model())
	if len(versionID) > 0 {
		where = where.Where("version_id = ?", versionID)
	}

	if len(product) > 0 {
		where = where.Where("product = ?", product)
	}

	if len(branch) > 0 {
		where = where.Where("version = ?", branch)
	}

	if len(version) > 0 {
		where = where.Where("version = ?", version)
	}

	if len(start) > 0 {
		where = where.Where("created_at >= ?", start)
	}

	if len(end) > 0 {
		where = where.Where("created_at < ?", start)
	}

	if status != nil {
		where = where.Where("status = ?", *status)
	}
	db = db.Where(where)
	err := db.Count(&count).Error
	if err != nil {
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		return nil, err
	}

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func FindDailyCheck(product, branch string) ([]*ListResponse, error) {

	items := []*ListResponse{}
	db := easygorm.GetEasyGormDb().Model(Model()).Limit(2).Order("id desc").Where("product = ? and branch = ? and status = 1", product, branch)
	for _, column := range GetColumns() {
		if column == "basic_cc" {
			continue
		}
		db = db.Where(fmt.Sprintf("%s <> 0", column))
	}
	err := db.Find(&items).Error
	return items, err
}

func FindDistinctProductAndBranch() (items []*ListResponse, err error) {
	now := time.Now()
	thisWeek := []string{now.AddDate(0, 0, -7).Format("2006-01-02 15:04:05"), now.Format("2006-01-02 15:04:05")}
	err = easygorm.GetEasyGormDb().Model(Model()).Distinct("product", "branch").Where("status = 1 and created_at between ? and ?", thisWeek[0], thisWeek[1]).Find(&items).Error
	return items, err
}

func GetColumns() (columns []string) {
	xt := reflect.TypeOf(&Performance{})
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok1 := xt.Elem().Field(i).Tag.Lookup("check")
		column, ok2 := xt.Elem().Field(i).Tag.Lookup("json")

		if ok1 && ok2 {
			columns = append(columns, column)
		}
	}
	return columns
}

func FindBaseLineCheck(product, branch string) ([]*ListResponse, error) {
	items := []*ListResponse{}
	baseline := ListResponse{}
	last := ListResponse{}
	where := easygorm.GetEasyGormDb().Model(Model()).Where("product = ? and branch = ? and status = 1", product, branch)
	for _, column := range GetColumns() {
		where = where.Where(fmt.Sprintf("%s <> 0", column))
	}
	err := easygorm.GetEasyGormDb().Model(Model()).Where(where).Where("baseline = 1").Limit(1).Order("id desc").Find(&baseline).Error
	if err != nil {
		return nil, err
	}
	err = easygorm.GetEasyGormDb().Model(Model()).Where(where).Limit(1).Order("id desc").Find(&last).Error
	items = append(items, &baseline, &last)
	return items, err
}

func FindWeeklyCheck(product, branch string) ([]*ListResponse, error) {
	items := []*ListResponse{}
	thisWeekAvg := ListResponse{}
	lastWeekAvg := ListResponse{}
	last := ListResponse{}
	selectSlice := []string{"product", "branch"}
	now := time.Now()
	thisWeek := []string{now.AddDate(0, 0, -7).Format("2006-01-02 15:04:05"), now.Format("2006-01-02 15:04:05")}
	lastWeek := []string{now.AddDate(0, 0, -14).Format("2006-01-02 15:04:05"), now.AddDate(0, 0, -7).Format("2006-01-02 15:04:05")}

	where := easygorm.GetEasyGormDb().Model(Model()).Where("product = ? and branch = ? and status = 1", product, branch)
	for _, column := range GetColumns() {
		where = where.Where(fmt.Sprintf("%s <> 0", column))
		selectSlice = append(selectSlice, fmt.Sprintf("CAST(avg(%s) as unsigned) as %s", column, column))
	}
	err := easygorm.GetEasyGormDb().Model(Model()).Select(selectSlice).Where(where).Where("created_at between ? and ?", thisWeek[0], thisWeek[1]).Group("product").Group("branch").Find(&thisWeekAvg).Error
	if err != nil {
		return nil, err
	}
	err = easygorm.GetEasyGormDb().Model(Model()).Select(selectSlice).Where(where).Where("created_at between ? and ?", lastWeek[0], lastWeek[1]).Group("product").Group("branch").Find(&lastWeekAvg).Error
	if err != nil {
		return nil, err
	}
	err = easygorm.GetEasyGormDb().Model(Model()).Where(where).Limit(1).Order("id desc").Find(&last).Error

	items = append(items, &thisWeekAvg, &lastWeekAvg, &last)
	return items, err
}
