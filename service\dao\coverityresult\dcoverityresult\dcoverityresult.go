package dcoverityresult

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/coverityresult"
	"irisAdminApi/service/dao/coverityresult/dcoverityreporthistory"

	"gorm.io/gorm"
)

const ModelName = "coverity商业代码检查结果表"

type CoverityResult struct {
	coverityresult.CoverityResult
}

type ListResponse struct {
	CoverityResult
}

type CoverityResultSummary struct {
	Project        string `json:"project"`
	Product        string `json:"product"`
	ComponentName  string `json:"component_name"`
	Owner          string `json:"owner"`
	ReportFileName string `json:"report_file_name"`
	Total          uint   `json:"total"`
}

type Request struct{}

func (a *CoverityResult) ModelName() string {
	return ModelName
}

func Model() *coverityresult.CoverityResult {
	return &coverityresult.CoverityResult{}
}

func (a *CoverityResult) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *CoverityResult) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *CoverityResult) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *CoverityResult) CreateV2(object interface{}) error {
	return nil
}

func (a *CoverityResult) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *CoverityResult) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *CoverityResult) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *CoverityResult) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func BatchCreate(project, product string, objects []map[string]interface{}, history []map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		if len(objects) > 0 {
			err := tx.Where("project = ? and product = ?", project, product).Unscoped().Delete(Model()).Error
			if err != nil {
				return err
			}
			limit := 500

			for loop := 0; true; loop++ {
				start := loop * limit
				if start > len(objects) {
					break
				}
				end := (loop + 1) * limit
				if len(objects) <= (loop+1)*limit {
					end = len(objects)
				}
				err = tx.Model(Model()).Create(objects[start:end]).Error
				if err != nil {
					return err
				}
			}

			// 更新历史记录表
			return tx.Model(dcoverityreporthistory.Model()).Create(history).Error
		}

		return nil
	})

	return err
}

func Summary(projects []string) ([]*CoverityResultSummary, error) {
	items := []*CoverityResultSummary{}

	selects := []string{
		"project",
		"product",
		"component_name",
		"cc.owner owner",
		`COUNT(*) total`,
		`report_file_name`,
	}

	db := easygorm.GetEasyGormDb().
		Table(`coverity_results cres`).
		Joins(`LEFT JOIN coverity_rules crul ON crul.checker_name = cres.checker_name`).
		Joins(`LEFT JOIN (select distinct(component), OWNER, comment from common_components) cc ON cres.component_name = concat(cc.component, if(cc.COMMENT IS NOT NULL AND cc.COMMENT != '', CONCAT('[', cc.COMMENT, ']'), ''))`).
		Where("STATUS = '待分析' and project in ?", projects).
		Where("crul.epg_level = 'epg'").
		Group("project, product, component_name, cc.owner, report_file_name").
		Select(selects).
		Having("count(*) > 0")

	err := db.Scan(&items).Error
	if err != nil {
		return nil, err
	}

	return items, nil
}

type CoverityResultCountSummary struct {
	Project        string `json:"project"`
	Product        string `json:"product"`
	ComponentName  string `json:"component_name"`
	Owner          string `json:"owner"`
	ReportFileName string `json:"report_file_name"`
	ToAnalyze      int    `json:"to_analyze"`
	ToFix          int    `json:"to_fix"`
	FalsePositive  int    `json:"false_positive"`
	Intentional    int    `json:"intentional"`
}

func CountSummaryByProject(project string) ([]*CoverityResultCountSummary, error) {
	items := []*CoverityResultCountSummary{}

	selects := []string{
		"project",
		"product",
		"component_name",
		"cc.owner owner",
		`count(status='待分析' OR NULL) to_analyze`,
		`count(status='确认未修复' OR NULL) to_fix`,
		`count(status='误报' OR NULL) false_positive`,
		`count(status='特意' OR NULL) intentional`,
		`report_file_name`,
	}

	db := easygorm.GetEasyGormDb().
		Table(`(SELECT cr.* FROM fileOut.coverity_results AS cr
			WHERE project = '?' and checker_name in (select checker_name from fileOut.coverity_rules cr2 where epg_level = 'epg')
			ORDER BY cr.id DESC) cr`, project).
		Joins(`left join (select case when comment="是" then concat(component, "(开源)") when comment="否" then component when comment != '' then concat(component, "(", comment, ")") else component end component, owner
			from (select component, owner, comment from fileOut.common_components cc  group by component, owner, comment) cc) cc on cc.component = cr.component_name`).
		Group("project, product, component_name, status, cc.owner, report_file_name").
		Select(selects)

	err := db.Scan(&items).Error
	if err != nil {
		return nil, err
	}

	return items, nil
}
