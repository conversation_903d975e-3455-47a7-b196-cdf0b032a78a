s = "INSERT INTO resource_pool_resources \
    (name, domain, model, make, osname, mgt_ip, mgt_port, con_ip, con_port, testbed, type, status, reserved, sn, location, room) VALUES \
        ('{}', '', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '','{}',  '{}', '{}');"
with open("result.txt", 'w', encoding='utf8') as r:
    with open('1.txt', 'r', encoding='utf8') as f:
        for i in f:
            a = i.strip().split()
            r.write(s.format(a[0], a[1], a[2], a[3], a[4], a[5], a[6], a[7], a[8], a[9], a[10], a[11], a[12], a[13])+ "\n")