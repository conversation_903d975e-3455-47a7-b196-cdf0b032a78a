-- 添加自定义发布日期字段到patch_jobs表
-- 功能：为集合补丁添加指定发布日期的选项
-- 创建时间：2024-07-22
-- 作者：系统自动生成

-- 添加custom_release_date字段
ALTER TABLE `patch_jobs` ADD COLUMN `custom_release_date` VARCHAR(10) NULL COMMENT '自定义发布日期，格式YYYY-MM-DD，为空时使用created_at';

-- 注释说明
-- 此迁移脚本为patch_jobs表添加了一个新的可选字段custom_release_date，
-- 用于存储用户指定的补丁发布日期。
-- 
-- 字段特性：
-- - 类型：VARCHAR(10) - 存储"YYYY-MM-DD"格式的日期字符串
-- - 可空：NULL - 表示使用默认的created_at作为发布日期
-- - 默认值：NULL - 保持向后兼容性
-- 
-- 使用场景：
-- - 当用户在前端开启"自定义发布日期"开关并选择日期时，该字段存储用户选择的日期
-- - 当用户未指定或开关关闭时，该字段为NULL，系统使用created_at字段作为发布日期
-- - 在生成update.json文件时，优先使用custom_release_date，如果为空则使用created_at
-- 
-- 向后兼容性：
-- - 现有数据不受影响，所有现有记录的custom_release_date字段将为NULL
-- - 现有功能继续正常工作，使用created_at作为发布日期
-- - 新功能为可选功能，不会破坏现有的业务逻辑
