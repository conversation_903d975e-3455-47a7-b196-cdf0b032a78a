package taskmanagers

import (
	"fmt"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/service/dao/opensource/dvulnerability"
)

func getEmail(username string) string {
	return username + "@ruijie.com.cn"
}

func sendVulnerabilitiesEmail(subject string, vulnerabilities []*dvulnerability.OpenSourceVulnerability,
	responsibleUsers, responsibleLeaders, delegatedUsers []string) {
	var mailTo, cc []string
	for _, user := range responsibleUsers {
		mailTo = append(mailTo, getEmail(user))
	}
	for _, user := range responsibleLeaders {
		mailTo = append(mailTo, getEmail(user))
	}
	for _, user := range delegatedUsers {
		mailTo = append(mailTo, getEmail(user))
	}

	text := make([]string, len(vulnerabilities))
	for i, vulnerabilityRes := range vulnerabilities {
		text[i] = "<br>" + vulnerabilityRes.CveID +
			"<br>" + vulnerabilityRes.CveDescription +
			"<br>" + vulnerabilityRes.CveSeverity
	}
	content := "漏洞记录如下:" + strings.Join(text, "<br>")
	libs.SendMail(mailTo, subject, content, cc)
}

func vulnerabilityFoundSubject(componentName, componentVersion, productName, productVersion string) string {
	return fmt.Sprintf("[漏洞预警系统][漏洞发现通知]组件名:%s,组件版本:%s,关联产品:%s,关联项目:%s",
		componentName, componentVersion, productName, productVersion)
}

func vulnerabilityDelaySubject(componentName, componentVersion, productName, productVersion string) string {
	return fmt.Sprintf("[漏洞预警系统][漏洞超期处理通知]组件名:%s,组件版本:%s,关联产品:%s,关联项目:%s",
		componentName, componentVersion, productName, productVersion)
}

func vulnerabilityDelegatedSubject(componentName, componentVersion, productName, productVersion string) string {
	return fmt.Sprintf("[漏洞预警系统][漏洞转派处理通知]组件名:%s,组件版本:%s,关联产品:%s,关联项目:%s",
		componentName, componentVersion, productName, productVersion)
}

func durationToNextMonday() time.Duration {
	now := time.Now()
	todayZeroTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	days := int(now.Weekday() - time.Monday)
	if days == 0 {
		days = 7
	}
	return todayZeroTime.AddDate(0, 0, days).Sub(now)
}
