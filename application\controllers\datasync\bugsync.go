package datasync

import (
	"errors"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/datasync/dbranchinfo"
	"irisAdminApi/service/dao/datasync/dbug"
	"irisAdminApi/service/dao/datasync/dpackagepath"
	"irisAdminApi/service/dao/datasync/dsyncrecord"
	"strconv"
	"time"
)

// func ProjectSyncWorker() error {
// 	// todo: 检查同步记录，获取同步时间，如果没有，从七天前开始，按小时同步
// 	modifyDateArray := []string{}
// 	_url := "https://dataware.ruijie.com.cn/api/public/data-api/security_product_rgos_project/list.data"
// 	records, err := dsyncrecord.FindLastSuccessSyncRecord(_url)
// 	if err != nil {
// 		logging.ErrorLogger.Errorf("get last sync records", err.<PERSON>rror())
// 		return err
// 	}
// 	if len(records) > 0 {
// 		modifyDateArray = libs.GetDateRange(-30, records[0].MaxModifyDate)
// 	} else {
// 		modifyDateArray = libs.GetDateRange(-1000)
// 	}

// 	if len(modifyDateArray) < 2 && len(records) > 0 {
// 		return nil
// 	}

// 	for index := range modifyDateArray[0 : len(modifyDateArray)-1] {
// 		data := map[string]string{
// 			"sid":           "MTc3Y2NkZmJhNTIx",
// 			"minModifyDate": modifyDateArray[index],
// 			"maxModifyDate": modifyDateArray[index+1],
// 		}

// 		var result dproject.ProjectSyncResponse
// 		var errMsg dproject.ProjectSyncResponse
// 		resp, err := SyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(data).Get(_url)
// 		if err != nil {
// 			logging.ErrorLogger.Errorf("get bug error", err.Error())
// 			return err
// 		}
// 		if resp.IsSuccessState() {
// 			err := dproject.UpdateOrCreateBugProjectTransaction(result.Data, _url, data, resp.Request.Method, result.State, result.Message)
// 			if err != nil {
// 				logging.ErrorLogger.Errorf("update or create projects error", err.Error())
// 				return err
// 			}
// 		}
// 		time.Sleep(3 * time.Second)
// 	}
// 	return nil

// }

func PathSyncWorker() error {
	// todo: 检查同步记录，获取同步时间，如果没有，从七天前开始，按小时同步
	modifyDateArray := []string{}
	_url := "https://dataware.ruijie.com.cn/api/public/data-api/security_product_rgos_comp/list.data"
	records, err := dsyncrecord.FindLastSuccessSyncRecord(_url)
	if err != nil {
		logging.ErrorLogger.Errorf("get last sync records", err.Error())
		return err
	}
	if len(records) > 0 {
		modifyDateArray = libs.GetDateRange(-30, records[0].MaxModifyDate)
	} else {
		modifyDateArray = libs.GetDateRange(-1000)
	}

	if len(modifyDateArray) < 2 && len(records) > 0 {
		return nil
	}

	for index := range modifyDateArray[0 : len(modifyDateArray)-1] {
		data := map[string]string{
			"sid":           "NmY0NmM0YzQ5",
			"minModifyDate": modifyDateArray[index],
			"maxModifyDate": modifyDateArray[index+1],
		}

		var result dpackagepath.SyncResponse
		var errMsg dpackagepath.SyncResponse
		resp, err := SyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(data).Get(_url)
		if err != nil {
			logging.ErrorLogger.Errorf("get bug error", err.Error())
			return err
		}
		if resp.IsSuccessState() {
			err := dpackagepath.UpdateOrCreateBugPathTransaction(result.Data, _url, data, resp.Request.Method, result.State, result.Message)
			if err != nil {
				logging.ErrorLogger.Errorf("update or create projects error", err.Error())
				return err
			}
		}
		time.Sleep(3 * time.Second)
	}
	return nil

}

func BranchInfoSyncWorker() error {
	// todo: 检查同步记录，获取同步时间，如果没有，从七天前开始，按小时同步
	modifyDateArray := []string{}
	_url := "https://dataware.ruijie.com.cn/api/public/data-api/safe_product_branch_url_info/list.data"
	records, err := dsyncrecord.FindLastSuccessSyncRecord(_url)
	if err != nil {
		logging.ErrorLogger.Errorf("get last sync records", err.Error())
		return err
	}
	if len(records) > 0 {
		modifyDateArray = libs.GetDateRange(-30, records[0].MaxModifyDate)
	} else {
		modifyDateArray = libs.GetDateRange(-1000)
	}

	if len(modifyDateArray) < 2 && len(records) > 0 {
		return nil
	}

	for index := range modifyDateArray[0 : len(modifyDateArray)-1] {
		data := map[string]string{
			"sid":           "YjNlY2I3N2M4",
			"minModifyDate": modifyDateArray[index],
			"maxModifyDate": modifyDateArray[index+1],
		}

		var result dbranchinfo.SyncResponse
		var errMsg dbranchinfo.SyncResponse
		resp, err := SyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(data).Get(_url)
		if err != nil {
			logging.ErrorLogger.Errorf("get bug error", err.Error())
			return err
		}
		if resp.IsSuccessState() {
			err := dbranchinfo.UpdateOrCreateBugBranchInfoTransaction(result.Data, _url, data, resp.Request.Method, result.State, result.Message)
			if err != nil {
				logging.ErrorLogger.Errorf("update or create projects error", err.Error())
				return err
			}
		}
		time.Sleep(3 * time.Second)
	}
	return nil

}

func BugSyncWorker(delay int) error {
	// todo: 检查同步记录，获取同步时间，如果没有，从七天前开始，按小时同步
	modifyDateArray := []string{}
	_url := "https://dataware.ruijie.com.cn/api/public/data-api/security_product_sw_bug/list.data"
	records, err := dsyncrecord.FindLastSuccessSyncRecord(_url)
	if err != nil {
		logging.ErrorLogger.Errorf("get last sync records", err.Error())
		return err
	}
	if len(records) > 0 {
		modifyDateArray = libs.GetDateRange(-365, records[0].MaxModifyDate)
	} else {
		modifyDateArray = libs.GetDateRange(-365)
	}

	if len(modifyDateArray) < 2 {
		return nil
	}

	page := 1
	rows := 1000

	for {
		data := map[string]string{
			"sid":           "NjM5ZWQ5MjI5",
			"minModifyDate": modifyDateArray[0],
			"maxModifyDate": modifyDateArray[len(modifyDateArray)-1],
			"page":          strconv.Itoa(page),
			"rows":          strconv.Itoa(rows),
		}

		var result dbug.BugSyncResponse
		var errMsg dbug.BugSyncResponse
		resp, err := SyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(data).Get(_url)
		if err != nil {
			logging.ErrorLogger.Errorf("get bug error", err.Error())
			return err
		}
		if resp.IsSuccessState() {
			if result.State == "SUCCESS" {
				err := dbug.UpdateOrCreateBugTransaction(result.Data, _url, data, resp.Request.Method, result.State, result.Message)
				if err != nil {
					logging.ErrorLogger.Errorf("update or create bugs error", err.Error())
					return err
				}
			} else {
				logging.ErrorLogger.Errorf("get bugs failed", result.State, result.Message)
				return errors.New("get bugs failed")
			}
		} else {
			logging.ErrorLogger.Errorf("get bugs unkown error")
			return errors.New("unkown error")
		}
		time.Sleep(time.Duration(delay) * time.Second)
		if result.Total > rows*page {
			page++
		} else {
			break
		}
	}

	return nil
}

func BugSyncManualWorker(delay int) error {
	// todo: 检查同步记录，获取同步时间，如果没有，从七天前开始，按小时同步
	modifyDateArray := []string{}
	_url := "https://dataware.ruijie.com.cn/api/public/data-api/security_product_sw_bug/list.data"

	modifyDateArray = libs.GetDateRange(-1)

	if len(modifyDateArray) < 2 {
		return nil
	}

	page := 1
	rows := 1000

	for {
		data := map[string]string{
			"sid":           "NjM5ZWQ5MjI5",
			"minModifyDate": modifyDateArray[0],
			"maxModifyDate": modifyDateArray[len(modifyDateArray)-1],
			"page":          strconv.Itoa(page),
			"rows":          strconv.Itoa(rows),
		}

		var result dbug.BugSyncResponse
		var errMsg dbug.BugSyncResponse
		resp, err := SyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(data).Get(_url)
		if err != nil {
			logging.ErrorLogger.Errorf("get bug error", err.Error())
			return err
		}
		if resp.IsSuccessState() {
			if result.State == "SUCCESS" {
				err := dbug.UpdateOrCreateBugTransaction(result.Data, _url, data, resp.Request.Method, result.State, result.Message)
				if err != nil {
					logging.ErrorLogger.Errorf("update or create bugs error", err.Error())
					return err
				}
			} else {
				logging.ErrorLogger.Errorf("get bugs failed", result.State, result.Message)
				return errors.New("get bugs failed")
			}
		} else {
			logging.ErrorLogger.Errorf("get bugs unkown error")
			return errors.New("unkown error")
		}
		time.Sleep(time.Duration(delay) * time.Second)
		if result.Total > rows*page {
			page++
		} else {
			break
		}
	}

	return nil
}