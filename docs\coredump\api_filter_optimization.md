# Coredump记录自动化处理系统 - API筛选优化方案

## 📋 优化目标

基于飞书开放平台文档分析，将当前的"读取所有数据后客户端筛选"优化为"服务端直接筛选"，大幅提升系统性能和减少数据传输。

## 🔍 飞书API筛选能力分析

### ✅ 支持的核心功能

1. **服务端筛选**: SearchAppTableRecord接口完全支持filter参数
2. **复合条件**: 支持AND/OR逻辑，支持一层children嵌套
3. **单选字段**: 完美支持单选字段的各种筛选操作
4. **空值处理**: 支持isEmpty和isNotEmpty操作符
5. **性能优势**: 服务端筛选，减少网络传输和客户端处理

### 📊 支持的操作符

| 操作符 | 说明 | 支持字段类型 | 我们的使用场景 |
|--------|------|-------------|----------------|
| `is` | 等于 | 单选、多选、文本等 | ✅ 筛选"Y"、"N"、"待处理"等 |
| `isNot` | 不等于 | 单选、多选、文本等 | ✅ 排除特定状态 |
| `isEmpty` | 为空 | 所有字段类型 | ✅ 筛选空的处理状态 |
| `isNotEmpty` | 不为空 | 所有字段类型 | ✅ 筛选非空字段 |
| `contains` | 包含 | 单选、多选等 | ✅ 多值匹配 |

## 🎯 我们的筛选需求映射

### 当前筛选条件
```sql
-- 当前客户端筛选逻辑
WHERE 是否需要同步Bug系统 = 'Y'
  AND 是否已同步bug系统 IN ('', 'N')
  AND 处理状态 IN ('', '待处理', '失败', '处理中超时')
```

### 飞书API筛选实现
```json
{
  "filter": {
    "conjunction": "and",
    "children": [
      {
        "conjunction": "and",
        "conditions": [
          {
            "field_name": "是否需要同步Bug系统",
            "operator": "is",
            "value": ["Y"]
          }
        ]
      },
      {
        "conjunction": "or",
        "conditions": [
          {
            "field_name": "是否已同步bug系统",
            "operator": "isEmpty",
            "value": []
          },
          {
            "field_name": "是否已同步bug系统",
            "operator": "is",
            "value": ["N"]
          }
        ]
      },
      {
        "conjunction": "or",
        "conditions": [
          {
            "field_name": "处理状态",
            "operator": "isEmpty",
            "value": []
          },
          {
            "field_name": "处理状态",
            "operator": "is",
            "value": ["待处理"]
          },
          {
            "field_name": "处理状态",
            "operator": "is",
            "value": ["失败"]
          }
        ]
      }
    ]
  }
}
```

## 🔧 代码实现方案

### 1. 新增筛选条件构建器

```go
// FilterBuilder 筛选条件构建器
type FilterBuilder struct {
    config *CoredumpConfig
}

// NewFilterBuilder 创建筛选条件构建器
func NewFilterBuilder(config *CoredumpConfig) *FilterBuilder {
    return &FilterBuilder{
        config: config,
    }
}

// BuildCoredumpFilter 构建Coredump记录筛选条件
func (fb *FilterBuilder) BuildCoredumpFilter() map[string]interface{} {
    return map[string]interface{}{
        "conjunction": "and",
        "children": []map[string]interface{}{
            // 条件1: 是否需要同步Bug系统 = "Y"
            {
                "conjunction": "and",
                "conditions": []map[string]interface{}{
                    {
                        "field_name": fb.config.FieldMapping.SyncRequiredField,
                        "operator":   "is",
                        "value":      []string{"Y"},
                    },
                },
            },
            // 条件2: 是否已同步bug系统 为空或 = "N"
            {
                "conjunction": "or",
                "conditions": []map[string]interface{}{
                    {
                        "field_name": fb.config.FieldMapping.SyncStatusField,
                        "operator":   "isEmpty",
                        "value":      []string{},
                    },
                    {
                        "field_name": fb.config.FieldMapping.SyncStatusField,
                        "operator":   "is",
                        "value":      []string{"N"},
                    },
                },
            },
            // 条件3: 处理状态 为空、待处理或失败
            {
                "conjunction": "or",
                "conditions": []map[string]interface{}{
                    {
                        "field_name": fb.config.FieldMapping.ProcessingStatusField,
                        "operator":   "isEmpty",
                        "value":      []string{},
                    },
                    {
                        "field_name": fb.config.FieldMapping.ProcessingStatusField,
                        "operator":   "is",
                        "value":      []string{"待处理"},
                    },
                    {
                        "field_name": fb.config.FieldMapping.ProcessingStatusField,
                        "operator":   "is",
                        "value":      []string{"失败"},
                    },
                },
            },
        },
    }
}

// BuildTimeoutFilter 构建超时记录筛选条件
func (fb *FilterBuilder) BuildTimeoutFilter(timeoutMinutes int) map[string]interface{} {
    // 计算超时时间点
    timeoutTime := time.Now().Add(-time.Duration(timeoutMinutes) * time.Minute)
    timeoutTimestamp := timeoutTime.UnixMilli()
    
    return map[string]interface{}{
        "conjunction": "and",
        "conditions": []map[string]interface{}{
            {
                "field_name": fb.config.FieldMapping.ProcessingStatusField,
                "operator":   "is",
                "value":      []string{"处理中"},
            },
            {
                "field_name": fb.config.FieldMapping.ProcessingTimeField,
                "operator":   "isLess",
                "value":      []string{fmt.Sprintf("%d", timeoutTimestamp)},
            },
        },
    }
}
```

### 2. 优化数据读取方法

```go
// readCoredumpRecordsWithFilter 使用服务端筛选读取记录
func (s *CoredumpService) readCoredumpRecordsWithFilter() ([]*CoredumpRecord, error) {
    logging.InfoLogger.Info("开始使用服务端筛选读取Coredump记录")
    
    var allRecords []*CoredumpRecord
    pageToken := ""
    pageSize := s.config.PageSize
    
    // 构建筛选条件
    filterBuilder := NewFilterBuilder(s.config)
    filter := filterBuilder.BuildCoredumpFilter()
    
    for {
        // 构建请求
        reqBody := map[string]interface{}{
            "page_size":   pageSize,
            "field_names": s.config.FieldNames,
            "filter":      filter,
        }
        
        if pageToken != "" {
            reqBody["page_token"] = pageToken
        }
        
        // 调用API
        req := larkbitable.NewSearchAppTableRecordReqBuilder().
            AppToken(s.config.CoredumpAppToken).
            TableId(s.config.CoredumpTableID).
            Body(larkbitable.NewSearchAppTableRecordReqBodyBuilder().
                PageSize(pageSize).
                FieldNames(s.config.FieldNames).
                Filter(filter).
                PageToken(pageToken).
                Build()).
            Build()
        
        resp, err := s.feishuClient.Bitable.AppTableRecord.Search(context.Background(), req)
        if err != nil {
            return nil, fmt.Errorf("调用飞书API失败: %w", err)
        }
        
        if !resp.Success() {
            return nil, fmt.Errorf("飞书API返回错误: %s", resp.Msg)
        }
        
        // 解析记录
        records, err := s.parseRecords(resp.Data.Items)
        if err != nil {
            return nil, fmt.Errorf("解析记录失败: %w", err)
        }
        
        allRecords = append(allRecords, records...)
        
        // 检查是否还有更多数据
        if !resp.Data.HasMore {
            break
        }
        
        pageToken = *resp.Data.PageToken
        
        // API限流控制
        time.Sleep(100 * time.Millisecond)
    }
    
    logging.InfoLogger.Infof("服务端筛选完成，共读取 %d 条符合条件的记录", len(allRecords))
    return allRecords, nil
}
```

### 3. 超时记录处理优化

```go
// findTimeoutRecords 查找超时的处理中记录
func (s *CoredumpService) findTimeoutRecords() ([]*CoredumpRecord, error) {
    logging.InfoLogger.Info("查找超时的处理中记录")
    
    // 构建超时筛选条件
    filterBuilder := NewFilterBuilder(s.config)
    filter := filterBuilder.BuildTimeoutFilter(s.config.ProcessingTimeout)
    
    var timeoutRecords []*CoredumpRecord
    pageToken := ""
    
    for {
        req := larkbitable.NewSearchAppTableRecordReqBuilder().
            AppToken(s.config.CoredumpAppToken).
            TableId(s.config.CoredumpTableID).
            Body(larkbitable.NewSearchAppTableRecordReqBodyBuilder().
                PageSize(s.config.PageSize).
                FieldNames(s.config.FieldNames).
                Filter(filter).
                PageToken(pageToken).
                Build()).
            Build()
        
        resp, err := s.feishuClient.Bitable.AppTableRecord.Search(context.Background(), req)
        if err != nil {
            return nil, fmt.Errorf("查找超时记录失败: %w", err)
        }
        
        if !resp.Success() {
            return nil, fmt.Errorf("飞书API返回错误: %s", resp.Msg)
        }
        
        records, err := s.parseRecords(resp.Data.Items)
        if err != nil {
            return nil, fmt.Errorf("解析超时记录失败: %w", err)
        }
        
        timeoutRecords = append(timeoutRecords, records...)
        
        if !resp.Data.HasMore {
            break
        }
        
        pageToken = *resp.Data.PageToken
        time.Sleep(100 * time.Millisecond)
    }
    
    logging.InfoLogger.Infof("发现 %d 条超时记录", len(timeoutRecords))
    return timeoutRecords, nil
}
```

### 4. 更新主处理流程

```go
// ProcessCoredumpRecords 优化后的主处理方法
func (s *CoredumpService) ProcessCoredumpRecords() (*ProcessResult, error) {
    taskID := fmt.Sprintf("task_%d", time.Now().Unix())
    logging.InfoLogger.Infof("[%s] 开始处理Coredump记录（使用服务端筛选）", taskID)
    
    result := &ProcessResult{
        TaskID:    taskID,
        StartTime: time.Now(),
    }
    
    // 步骤1: 使用服务端筛选直接读取符合条件的记录
    filteredRecords, err := s.readCoredumpRecordsWithFilter()
    if err != nil {
        return nil, fmt.Errorf("读取筛选记录失败: %w", err)
    }
    
    result.TotalRecords = len(filteredRecords)
    result.FilteredRecords = len(filteredRecords)
    logging.InfoLogger.Infof("[%s] 服务端筛选获取到 %d 条待处理记录", taskID, len(filteredRecords))
    
    // 步骤2: 查找并重置超时记录
    timeoutRecords, err := s.findTimeoutRecords()
    if err != nil {
        logging.ErrorLogger.Errorf("[%s] 查找超时记录失败: %v", taskID, err)
    } else if len(timeoutRecords) > 0 {
        logging.InfoLogger.Infof("[%s] 发现 %d 条超时记录，正在重置", taskID, len(timeoutRecords))
        err = s.statusManager.ResetTimeoutRecords(timeoutRecords)
        if err != nil {
            logging.ErrorLogger.Errorf("[%s] 重置超时记录失败: %v", taskID, err)
        }
    }
    
    // 步骤3: 处理筛选后的记录
    for _, record := range filteredRecords {
        err := s.processRecord(record, result)
        if err != nil {
            logging.ErrorLogger.Errorf("[%s] 处理记录失败 [%s]: %v", taskID, record.RecordID, err)
            result.FailedRecords++
            
            // 更新为失败状态
            statusErr := s.statusManager.UpdateProcessingStatus(record.RecordID, StatusFailed, "", err.Error())
            if statusErr != nil {
                logging.ErrorLogger.Errorf("[%s] 更新失败状态失败 [%s]: %v", taskID, record.RecordID, statusErr)
            }
        } else {
            result.SuccessRecords++
        }
    }
    
    result.EndTime = time.Now()
    result.Duration = result.EndTime.Sub(result.StartTime)
    
    logging.InfoLogger.Infof("[%s] 处理完成: 总计=%d, 成功=%d, 失败=%d, 耗时=%v", 
        taskID, result.FilteredRecords, result.SuccessRecords, result.FailedRecords, result.Duration)
    
    return result, nil
}
```

## 📈 性能优化效果

### 🔥 优化前 vs 优化后

| 指标 | 优化前 | 优化后 | 改善幅度 |
|------|--------|--------|----------|
| **网络传输** | 读取所有记录 | 仅读取符合条件的记录 | 减少80-95% |
| **内存使用** | 加载所有记录到内存 | 仅加载需要的记录 | 减少80-95% |
| **处理时间** | 客户端筛选耗时 | 服务端筛选，几乎无耗时 | 减少90%+ |
| **API调用** | 多次分页读取所有数据 | 直接获取筛选结果 | 减少调用次数 |

### 📊 具体场景分析

假设飞书表格中有10000条记录，其中只有100条需要处理：

**优化前**:
- 需要读取10000条记录（分页读取，约20次API调用）
- 客户端筛选出100条记录
- 网络传输：~10MB数据
- 内存占用：~50MB
- 筛选耗时：~2-5秒

**优化后**:
- 直接读取100条符合条件的记录（1次API调用）
- 无需客户端筛选
- 网络传输：~100KB数据
- 内存占用：~500KB
- 筛选耗时：~0.1秒

**性能提升**:
- 网络传输减少99%
- 内存使用减少99%
- 处理时间减少95%
- API调用减少95%

## 🔧 配置更新

### 更新配置文件结构

```yaml
coredump:
  # 飞书配置
  coredump_app_token: "your_app_token"
  coredump_table_id: "your_table_id"
  
  # 分页配置
  page_size: 100  # 可以适当增加，因为都是有效记录
  
  # 字段映射配置
  field_mapping:
    sync_required_field: "是否需要同步Bug系统"
    sync_status_field: "是否已同步bug系统"
    processing_status_field: "处理状态"
    processing_time_field: "处理时间"
    error_message_field: "错误信息"
    retry_count_field: "重试次数"
    last_updated_field: "最后更新时间"
    bug_id_field: "Bug系统ID"
  
  # 处理配置
  processing_timeout: 60  # 处理超时时间（分钟）
  
  # 筛选优化配置
  filter_optimization:
    enabled: true  # 启用服务端筛选
    use_server_filter: true  # 使用服务端筛选
    fallback_to_client: false  # 不回退到客户端筛选
```

## 🧪 测试验证

### 1. 筛选条件测试

```go
func TestFilterBuilder(t *testing.T) {
    config := &CoredumpConfig{
        FieldMapping: FieldMapping{
            SyncRequiredField:      "是否需要同步Bug系统",
            SyncStatusField:        "是否已同步bug系统",
            ProcessingStatusField:  "处理状态",
        },
    }
    
    builder := NewFilterBuilder(config)
    filter := builder.BuildCoredumpFilter()
    
    // 验证筛选条件结构
    assert.Equal(t, "and", filter["conjunction"])
    assert.Len(t, filter["children"], 3)
    
    // 验证具体条件
    children := filter["children"].([]map[string]interface{})
    
    // 第一个条件：是否需要同步Bug系统 = "Y"
    firstCondition := children[0]["conditions"].([]map[string]interface{})[0]
    assert.Equal(t, "是否需要同步Bug系统", firstCondition["field_name"])
    assert.Equal(t, "is", firstCondition["operator"])
    assert.Equal(t, []string{"Y"}, firstCondition["value"])
}
```

### 2. 性能测试

```go
func BenchmarkFilterOptimization(b *testing.B) {
    service := NewCoredumpService()
    
    b.Run("优化前-客户端筛选", func(b *testing.B) {
        for i := 0; i < b.N; i++ {
            // 模拟读取所有记录后客户端筛选
            allRecords, _ := service.readAllRecords()
            filteredRecords := service.filterRecordsClient(allRecords)
            _ = filteredRecords
        }
    })
    
    b.Run("优化后-服务端筛选", func(b *testing.B) {
        for i := 0; i < b.N; i++ {
            // 直接使用服务端筛选
            filteredRecords, _ := service.readCoredumpRecordsWithFilter()
            _ = filteredRecords
        }
    })
}
```

## 🚀 部署建议

### 1. 渐进式部署

1. **第一阶段**: 添加服务端筛选功能，保留客户端筛选作为备选
2. **第二阶段**: 在测试环境验证服务端筛选效果
3. **第三阶段**: 生产环境启用服务端筛选
4. **第四阶段**: 移除客户端筛选代码

### 2. 监控指标

- API调用次数和耗时
- 网络传输数据量
- 内存使用情况
- 筛选结果准确性
- 错误率和重试次数

### 3. 回退机制

```go
// 带回退机制的筛选方法
func (s *CoredumpService) readCoredumpRecordsWithFallback() ([]*CoredumpRecord, error) {
    if s.config.FilterOptimization.UseServerFilter {
        records, err := s.readCoredumpRecordsWithFilter()
        if err != nil && s.config.FilterOptimization.FallbackToClient {
            logging.WarningLogger.Warnf("服务端筛选失败，回退到客户端筛选: %v", err)
            return s.readCoredumpRecordsLegacy()
        }
        return records, err
    }
    
    return s.readCoredumpRecordsLegacy()
}
```

## 📋 总结

### ✅ 优化成果

1. **性能大幅提升**: 网络传输和处理时间减少90%+
2. **资源使用优化**: 内存和CPU使用大幅降低
3. **代码简化**: 移除复杂的客户端筛选逻辑
4. **可维护性提升**: 筛选逻辑集中在API层面

### 🎯 关键优势

1. **服务端筛选**: 利用飞书API的原生筛选能力
2. **复合条件支持**: 完美支持我们的复杂筛选需求
3. **空值处理**: 正确处理新记录的空状态
4. **超时处理**: 独立的超时记录查找和处理

### 🔮 未来扩展

1. **动态筛选**: 支持用户自定义筛选条件
2. **缓存优化**: 对筛选结果进行智能缓存
3. **实时筛选**: 结合Webhook实现实时数据筛选
4. **多表联合**: 支持跨表格的联合筛选

这个优化方案完全基于飞书开放平台的官方API能力，确保了可靠性和性能，同时大幅简化了系统架构。