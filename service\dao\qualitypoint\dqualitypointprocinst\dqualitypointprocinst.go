package dqualitypointprocinst

import (
	"encoding/json"
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/qualitypoint"
	dqualitypointproctask "irisAdminApi/service/dao/qualitypoint/dqualitypointproctask"
	"irisAdminApi/service/dao/qualitypoint/dqualityviolation"
	"irisAdminApi/service/dao/user/duser"
	"strings"
)

const ModelName = "质量积分流程实例"

type Response struct {
	ID    uint   `json:"id"`
	Title string `json:"title"`
	// 当前节点
	NodeID string `json:"node_id"`
	// 审批人
	// Candidate string `json:"candidate"`
	// 当前任务
	TaskID              int                                   `json:"task_id"`
	StartUserID         uint                                  `json:"start_user_id"`
	ResponsiblePersonID uint                                  `json:"responsible_person_id" ` //责任角色ID
	AuditManagerID      uint                                  `json:"audit_manager_id" `      //违规项审计管理人员ID
	QualityViolationID  uint                                  `json:"quality_violation_id"`
	Status              uint                                  `json:"status"`
	Resource            string                                `gorm:"size:10000" json:"resource,omitempty"`
	User                *duser.ApprovalResponse               `gorm:"-" json:"user"`
	ResponsiblePerson   *duser.ApprovalResponse               `gorm:"-" json:"responsible_person"`
	AuditManager        *duser.ApprovalResponse               `gorm:"-" json:"audit_manager"`
	Qualityviolation    *dqualityviolation.Response           `gorm:"-" json:"qualityviolation"`
	Tasks               []*dqualitypointproctask.ListResponse `gorm:"-" json:"tasks"`
	DoneTasks           []*dqualitypointproctask.ListResponse `gorm:"-" json:"done_tasks"`

	AbstractCommonTasks        *dqualitypointproctask.Response `gorm:"-" json:"abstract_common_tasks"`
	CommonReview_1_Tasks       *dqualitypointproctask.Response `gorm:"-" json:"common_review_1_tasks"`
	CommonReview_2_Tasks       *dqualitypointproctask.Response `gorm:"-" json:"common_review_2_tasks"`
	RepeatIssueCheckTasks      *dqualitypointproctask.Response `gorm:"-" json:"repeat_issue_check_tasks"`
	DeductPlanTasks            *dqualitypointproctask.Response `gorm:"-" json:"deduct_plan_tasks"`
	DeductReviewTasks          *dqualitypointproctask.Response `gorm:"-" json:"deduct_review_tasks"`
	DeductEvidenceTasks        *dqualitypointproctask.Response `gorm:"-" json:"deduct_evidence_tasks"`
	DeductExecutionReviewTasks *dqualitypointproctask.Response `gorm:"-" json:"deduct_execution_review_tasks"`
}

type ListResponse struct {
	Response
}

type Request struct {
	Title string `json:"title"`
	// 当前节点
	NodeID string `json:"node_id"`
	// 审批人
	// Candidate string `json:"candidate"`
	// 当前任务
	TaskID             int    `json:"task_id"`
	StartUserID        string `json:"start_user_id"`
	QualityViolationID string `json:"quality_violation_id"`
	Resource           string `gorm:"size:10000" json:"resource,omitempty"`
	IsFinished         bool   `gorm:"default:false" json:"is_finished"`
}

type Node struct {
	Name       string `json:"name,omitempty"`
	NodeID     string `json:"nodeId,omitempty"`
	PrevNodeID string `json:"prevNodeId,omitempty"`
	Assignee   uint   `json:"assignee"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *qualitypoint.QualityViolationProcInst {
	return &qualitypoint.QualityViolationProcInst{}
}

func (a *Response) All(name, status, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	if len(status) > 0 {
		db = db.Where("status in ?", strings.Split(status, ","))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	formatResponses(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	formatResponse(u)
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindAll() ([]*Response, error) {
	var items []*Response

	if err := easygorm.GetEasyGormDb().Model(Model()).Find(&items).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return items, err
	}
	return items, nil
}

func FindInIds(ids []uint) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id in ?", ids).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	formatResponses(items)
	return items, nil
}

func DeleteByUUID(uuid string) error {
	err := easygorm.GetEasyGormDb().Unscoped().Where("uuid = ?", uuid).Delete(Model()).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func UpdateStatus(id uint, object map[string]interface{}) error {

	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func FormatResponse(items []*ListResponse) {
	userIds := []uint{}
	for _, item := range items {
		userIds = append(userIds, item.User.Id)
	}
	users, _ := duser.FindSimpleInIds(userIds)
	var userMap = make(map[uint]*duser.ApprovalResponse)
	for _, user := range users {
		userMap[user.Id] = user
	}
	for _, item := range items {
		item.User = userMap[item.User.Id]
	}
}

func createProcInst(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func AllMyAuditData(uid uint, name, status, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	if len(status) > 0 {
		db = db.Where("status in ?", strings.Split(status, ","))
	}
	if uid > 0 {
		db = db.Where("audit_manager_id = ?", uid)
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	formatResponses(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func AllMySubmissionsData(uid uint, name, status, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	if len(status) > 0 {
		db = db.Where("status in ?", strings.Split(status, ","))
	}
	if uid > 0 {
		db = db.Where("start_user_id = ?", uid)
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	formatResponses(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func GetPrevNodeIDs(resource string, nodeID string) ([]string, error) {
	prevNodeIDs := []string{}

	nodes := []*Node{}
	err := json.Unmarshal([]byte(resource), &nodes)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return prevNodeIDs, err
	}
	for _, node := range nodes {
		if node.NodeID == nodeID {
			prevNodeIDs = append(prevNodeIDs, node.PrevNodeID)
		}
	}
	return prevNodeIDs, nil
}

func GetNextNodeIDs(resource string, nodeID string) ([]string, error) {
	nextNodeIDs := []string{}

	nodes := []*Node{}
	err := json.Unmarshal([]byte(resource), &nodes)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return nextNodeIDs, err
	}
	for _, node := range nodes {
		if node.PrevNodeID == nodeID {
			nextNodeIDs = append(nextNodeIDs, node.NodeID)
		}
	}
	return nextNodeIDs, nil
}

func GetNode(resource string, nodeID string) (*Node, error) {
	nodes := []*Node{}

	err := json.Unmarshal([]byte(resource), &nodes)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return nil, err
	}
	for _, node := range nodes {
		if node.NodeID == nodeID {
			return node, nil
		}
	}
	return nil, nil
}

func GetNodes(resource string) ([]*Node, error) {
	nodes := []*Node{}

	err := json.Unmarshal([]byte(resource), &nodes)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return nodes, err
	}

	return nodes, nil
}

func AllMyResponsibleSubmissionsData(uid uint, name, status, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	if len(status) > 0 {
		db = db.Where("status in ?", strings.Split(status, ","))
	}
	if uid > 0 {
		db = db.Where("responsible_person_id = ?", uid)
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	formatResponses(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func formatResponses(items []*ListResponse) {
	qualityViolationIds := []uint{}
	procInstIDs := []uint{}
	userIds := []uint{}
	taskIds := []uint{}
	violationMap := map[uint]*dqualityviolation.Response{}
	for _, item := range items {
		qualityViolationIds = append(qualityViolationIds, item.QualityViolationID)
		userIds = append(userIds, item.StartUserID)
		userIds = append(userIds, item.AuditManagerID)
		userIds = append(userIds, item.ResponsiblePersonID)
		procInstIDs = append(procInstIDs, item.ID)
	}
	violations, err := dqualityviolation.FindInIds(qualityViolationIds)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return
	}

	for _, violation := range violations {
		violationMap[violation.ID] = &violation.Response
	}

	users, _ := duser.FindSimpleInIds(userIds)
	var userMap = make(map[uint]*duser.ApprovalResponse)
	for _, user := range users {
		userMap[user.Id] = user
	}
	taskMap := map[uint][]*dqualitypointproctask.ListResponse{}
	tasks, err := dqualitypointproctask.FindInProcInstIDs(procInstIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("find tasks err ", err)
		return
	}
	for _, task := range tasks {
		taskMap[task.ProcInstID] = append(taskMap[task.Response.ProcInstID], task)
	}
	//已完成节点
	abstractCommonTasksMap := map[uint]*dqualitypointproctask.Response{}
	commonReview1TasksMap := map[uint]*dqualitypointproctask.Response{}
	commonReview2TasksMap := map[uint]*dqualitypointproctask.Response{}
	repeatIssueCheckTasksMap := map[uint]*dqualitypointproctask.Response{}
	deductPlanTasksMap := map[uint]*dqualitypointproctask.Response{}
	deductReviewTasksMap := map[uint]*dqualitypointproctask.Response{}
	deductEvidenceTasksMap := map[uint]*dqualitypointproctask.Response{}
	deductExecutionReviewTasksMap := map[uint]*dqualitypointproctask.Response{}
	taskDoneMap := map[uint][]*dqualitypointproctask.ListResponse{}
	tasks2, err := dqualitypointproctask.FindInProcInstIDs2(procInstIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("find tasks err ", err)
		return
	}
	for _, task := range tasks2 {
		taskIds = append(taskIds, task.ID)
		if task.NodeID == "abstract_common" {
			abstractCommonTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "common_review_1" {
			commonReview1TasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "common_review_2" {
			commonReview2TasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "repeat_issue_check" {
			repeatIssueCheckTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "deduct_plan" {
			deductPlanTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "deduct_review" {
			deductReviewTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "deduct_evidence" {
			deductEvidenceTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "deduct_execution_review" {
			deductExecutionReviewTasksMap[task.Response.ProcInstID] = &task.Response
		}
	}
	tasks3, err := dqualitypointproctask.FindInProcInstIDs3(procInstIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("find tasks err ", err)
		return
	}
	for _, task := range tasks3 {
		taskDoneMap[task.ProcInstID] = append(taskDoneMap[task.Response.ProcInstID], task)
	}
	for _, item := range items {
		item.Qualityviolation = violationMap[item.QualityViolationID]
		item.User = userMap[item.StartUserID]
		item.ResponsiblePerson = userMap[item.ResponsiblePersonID]
		item.AuditManager = userMap[item.AuditManagerID]
		item.Tasks = taskMap[item.ID]
		item.DoneTasks = taskDoneMap[item.ID]

		item.AbstractCommonTasks = abstractCommonTasksMap[item.ID]
		item.CommonReview_1_Tasks = commonReview1TasksMap[item.ID]
		item.CommonReview_2_Tasks = commonReview2TasksMap[item.ID]

		item.RepeatIssueCheckTasks = repeatIssueCheckTasksMap[item.ID]

		item.DeductPlanTasks = deductPlanTasksMap[item.ID]
		item.DeductReviewTasks = deductReviewTasksMap[item.ID]
		item.DeductEvidenceTasks = deductEvidenceTasksMap[item.ID]
		item.DeductExecutionReviewTasks = deductExecutionReviewTasksMap[item.ID]
	}

}

func formatResponse(item *Response) {
	qualityViolationIds := []uint{}
	procInstIDs := []uint{}
	userIds := []uint{}
	violationMap := map[uint]*dqualityviolation.Response{}
	qualityViolationIds = append(qualityViolationIds, item.QualityViolationID)
	userIds = append(userIds, item.StartUserID)
	userIds = append(userIds, item.AuditManagerID)
	userIds = append(userIds, item.ResponsiblePersonID)
	procInstIDs = append(procInstIDs, item.ID)
	taskIds := []uint{}

	violations, err := dqualityviolation.FindInIds(qualityViolationIds)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return
	}

	for _, violation := range violations {
		violationMap[violation.ID] = &violation.Response
	}

	users, _ := duser.FindSimpleInIds(userIds)
	var userMap = make(map[uint]*duser.ApprovalResponse)
	for _, user := range users {
		userMap[user.Id] = user
	}
	taskMap := map[uint][]*dqualitypointproctask.ListResponse{}
	tasks, err := dqualitypointproctask.FindInProcInstIDs(procInstIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("find tasks err ", err)
		return
	}
	for _, task := range tasks {
		taskMap[task.ProcInstID] = append(taskMap[task.Response.ProcInstID], task)
	}
	//已完成节点数据
	abstractCommonTasksMap := map[uint]*dqualitypointproctask.Response{}
	commonReview1TasksMap := map[uint]*dqualitypointproctask.Response{}
	commonReview2TasksMap := map[uint]*dqualitypointproctask.Response{}
	repeatIssueCheckTasksMap := map[uint]*dqualitypointproctask.Response{}
	deductPlanTasksMap := map[uint]*dqualitypointproctask.Response{}
	deductReviewTasksMap := map[uint]*dqualitypointproctask.Response{}
	deductEvidenceTasksMap := map[uint]*dqualitypointproctask.Response{}
	deductExecutionReviewTasksMap := map[uint]*dqualitypointproctask.Response{}
	taskDoneMap := map[uint][]*dqualitypointproctask.ListResponse{}
	tasks2, err := dqualitypointproctask.FindInProcInstIDs2(procInstIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("find tasks err ", err)
		return
	}
	for _, task := range tasks2 {
		taskIds = append(taskIds, task.ID)
		if task.NodeID == "abstract_common" {
			abstractCommonTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "common_review_1" {
			commonReview1TasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "common_review_2" {
			commonReview2TasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "repeat_issue_check" {
			repeatIssueCheckTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "deduct_plan" {
			deductPlanTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "deduct_review" {
			deductReviewTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "deduct_evidence" {
			deductEvidenceTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "deduct_execution_review" {
			deductExecutionReviewTasksMap[task.Response.ProcInstID] = &task.Response
		}
	}
	tasks3, err := dqualitypointproctask.FindInProcInstIDs3(procInstIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("find tasks err ", err)
		return
	}
	for _, task := range tasks3 {
		taskDoneMap[task.ProcInstID] = append(taskDoneMap[task.Response.ProcInstID], task)
	}

	item.Qualityviolation = violationMap[item.QualityViolationID]
	item.User = userMap[item.StartUserID]
	item.ResponsiblePerson = userMap[item.ResponsiblePersonID]
	item.AuditManager = userMap[item.AuditManagerID]
	item.Tasks = taskMap[item.ID]
	item.DoneTasks = taskDoneMap[item.ID]

	item.AbstractCommonTasks = abstractCommonTasksMap[item.ID]
	item.RepeatIssueCheckTasks = repeatIssueCheckTasksMap[item.ID]
	item.CommonReview_1_Tasks = commonReview1TasksMap[item.ID]
	item.CommonReview_2_Tasks = commonReview2TasksMap[item.ID]
	item.DeductPlanTasks = deductPlanTasksMap[item.ID]
	item.DeductReviewTasks = deductReviewTasksMap[item.ID]
	item.DeductEvidenceTasks = deductEvidenceTasksMap[item.ID]
	item.DeductExecutionReviewTasks = deductExecutionReviewTasksMap[item.ID]
}

var ProductionProcInstUserMap = map[uint]uint{}
var ProductionIDProcInstIDMap = map[uint]uint{}

func UpdateFeatureProccessingCache(productionIds []uint) error {
	productionProcInsts := []*ListResponse{}
	if err := easygorm.GetEasyGormDb().Model(Model()).Where("quality_violation_id in ?", productionIds).Find(&productionProcInsts).Error; err != nil {
		return err
	}
	for _, inst := range productionProcInsts {
		ProductionProcInstUserMap[inst.QualityViolationID] = inst.StartUserID
		ProductionIDProcInstIDMap[inst.QualityViolationID] = inst.ID
	}
	return nil
}
