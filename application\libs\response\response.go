package response

type Response struct {
	Code int64       `json:"code"`
	Msg  interface{} `json:"message"`
	Data interface{} `json:"data"`
}

type MoreResponse struct {
	Data  interface{} `json:"data"`
	Total int         `json:"total"`
}

func NewResponse(code int64, objects interface{}, msg string) *Response {
	return &Response{Code: code, Data: objects, Msg: msg}
}

type ErrMsg struct {
	Code int64
	Msg  string
}

var (
	NoErr             = ErrMsg{20000, "请求成功"}
	AuthNameOrPassErr = ErrMsg{50007, "用户名密码错误"}
	AuthErr           = ErrMsg{50008, "Token 无效"}
	AuthExpireErr     = ErrMsg{50014, "Token 过期"}
	AuthActionErr     = ErrMsg{4003, "权限错误"}
	SystemErr         = ErrMsg{5000, "系统错误，请联系管理员"}
	DataEmptyErr      = ErrMsg{5001, "数据为空"}
	TokenCacheErr     = ErrMsg{50012, "TOKEN CACHE 错误"}
	ParamentErr       = ErrMsg{30000, "参数错误"}
	DuplicateErr      = ErrMsg{5002, "存在重复记录"}
	PermitErr         = ErrMsg{5003, "禁止访问"}
	FileNotExistsErr  = ErrMsg{5004, "文件不存在"}
)
