package openfeishu

import (
	"context"
	"fmt"
	"irisAdminApi/application/controllers/featurerelease"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"

	"github.com/kataras/iris/v12"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	larkbitable "github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
	larkdrive "github.com/larksuite/oapi-sdk-go/v3/service/drive/v1"
)

func GetdocComments(ctx iris.Context) {
	// testTopic()
	// readByReaderGroupID()
	// WriteByConn()
	grayRules, err := featurerelease.SecCloudClient.QueryGrayRules("https://secloud0.ruijie.com.cn/", "")
	if err != nil {
		logging.ErrorLogger.Errorf("get resources error", err.Error())
		return
	}
	fmt.Println(grayRules)
	addGrayRuleResponse, err := featurerelease.SecCloudClient.AddGrayRule("https://secloud0.ruijie.com.cn/", "测试规则1", "2024-07-20", 100, 100, "测试", 0)
	if err != nil {
		logging.ErrorLogger.Errorf("get resources error", err.Error())
		return
	}
	fmt.Println(addGrayRuleResponse)
	// go coredump.ReadByReaderFromDev()
	// CreateBiTable("文档&代码缺陷密度自动统计专项")
	return
	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)

	// 创建请求对象 获取文档全部评论
	req := larkdrive.NewListFileCommentReqBuilder().
		FileToken(`Sljfd40UEof5umxPUVDc2HgwnKe`).
		FileType(`docx`).
		Build()
	// req := larkdrive.NewListFileCommentReqBuilder().
	// 	FileToken(`IlQns4jYphVwnRtYFNbc32YLncI`).
	// 	FileType(`sheet`).
	// 	Build()
	// 发起请求
	resp, err := client.Drive.FileComment.List(context.Background(), req)

	// 处理错误
	if err != nil {
		fmt.Println(err)
		return
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		return
	}
	// 业务处理
	fmt.Println(larkcore.Prettify(resp.Data.Items))
	for _, item := range resp.Data.Items {

		for _, rlist := range item.ReplyList.Replies {
			for _, element := range rlist.Content.Elements {
				person1 := larkbitable.Person{Id: item.UserId}
				isSolved := *item.IsSolved
				resolvedString := "否"
				if isSolved {
					resolvedString = "是"
				}
				fmt.Println(*item.Quote, *element.TextRun.Text, *item.UserId)
				rec := map[string]interface{}{
					`划词内容`:  *item.Quote,
					`评论内容`:  *element.TextRun.Text,
					`评论人`:   []interface{}{person1},
					`是否已解决`: resolvedString}
				CreateRecord("tblpLWMbYitI7cM5", rec)
			}

		}
	}

}

// 创建多维数据表格
func CreateBiTable(tableName string) {
	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)
	// 创建请求对象
	req := larkbitable.NewCreateAppReqBuilder().
		ReqApp(larkbitable.NewReqAppBuilder().
			Name(tableName).
			FolderToken(libs.Config.FeiShuDoc.FolderToken).
			Build()).
		Build()

	// 发起请求
	resp, err := client.Bitable.App.Create(context.Background(), req)

	// 处理错误
	if err != nil {
		fmt.Println(err)
		return
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		return
	}

	// 业务处理
	fmt.Println(larkcore.Prettify(resp))
}

// 创建数据表
func CreateTable(tableName string) {
	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)
	// 创建请求对象
	req := larkbitable.NewCreateAppTableReqBuilder().
		AppToken(libs.Config.FeiShuDoc.BiAppToken).
		Body(larkbitable.NewCreateAppTableReqBodyBuilder().
			Table(larkbitable.NewReqTableBuilder().
				Name(tableName).
				DefaultViewName(`文档评审的表格视图`).
				Fields([]*larkbitable.AppTableCreateHeader{
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`划词内容`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`评论内容`).
						Type(1).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`评论人`).
						Type(11).
						Build(),
					larkbitable.NewAppTableCreateHeaderBuilder().
						FieldName(`是否已解决`).Property(larkbitable.NewAppTableFieldPropertyBuilder().
						Options([]*larkbitable.AppTableFieldPropertyOption{
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`是`).
								Build(),
							larkbitable.NewAppTableFieldPropertyOptionBuilder().
								Name(`否`).
								Build(),
						}).
						Build()).
						Type(3).
						Build(),
				}).
				Build()).
			Build()).
		Build()

	// 发起请求
	resp, err := client.Bitable.AppTable.Create(context.Background(), req)

	// 处理错误
	if err != nil {
		fmt.Println(err)
		return
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		return
	}

	// 业务处理
	fmt.Println(larkcore.Prettify(resp))

}

// 创建记录
func CreateRecord(tableID string, data map[string]interface{}) {
	// 创建 Client
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)
	// 创建请求对象
	req := larkbitable.NewCreateAppTableRecordReqBuilder().
		AppToken(libs.Config.FeiShuDoc.BiAppToken).
		TableId(tableID).
		AppTableRecord(larkbitable.NewAppTableRecordBuilder().
			Fields(data).
			Build()).
		Build()

	// 发起请求
	resp, err := client.Bitable.AppTableRecord.Create(context.Background(), req)

	// 处理错误
	if err != nil {
		fmt.Println(err)
		return
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		return
	}

	// 业务处理
	fmt.Println(larkcore.Prettify(resp))
}

