package opensource

import (
	"strconv"

	"github.com/kataras/iris/v12"

	"irisAdminApi/application/libs/response"
	"irisAdminApi/service/dao/opensource/dvulnerabilitycnvd"
)

func ListCNVDVulnerabilities(ctx iris.Context) {
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	cveId := ctx.FormValue("cve_id")
	sort := ctx.FormValue("sort")
	orderBy := ctx.FormValue("orderBy")
	createdAt := ctx.FormValue("created_at")
	updatedAt := ctx.FormValue("updated_at")

	list, err := dvulnerabilitycnvd.ListCNVDVulnerabilities(page, pageSize, cveId, sort, orderBy, createdAt, updatedAt)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetCNVDVulnerability(ctx iris.Context) {
	id, _ := ctx.Params().GetUint("id")
	res, err := dvulnerabilitycnvd.GetCNVDVulnerability(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	} else if res == nil {
		ctx.JSON(response.NewResponse(response.DataEmptyErr.Code, nil, "CNVD漏洞不存在"))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, res, response.NoErr.Msg))
	return
}
