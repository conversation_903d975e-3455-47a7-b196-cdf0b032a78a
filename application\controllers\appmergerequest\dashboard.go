package appmergerequest

import (
	"fmt"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/appmergerequest/dappmergerequestbugownersummary"
	"irisAdminApi/service/dao/appmergerequest/dappmergerequestbugsummary"
	"irisAdminApi/service/dao/appmergerequest/dappmergerequestbugworkgroupsummary"
	"irisAdminApi/service/dao/appmergerequest/dappmergerequestdashboard"
	"irisAdminApi/service/dao/datasync/dbug"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/xuri/excelize/v2"
)

func WorkPackageSummary(ctx iris.Context) {
	_releaseProjectID := ctx.FormValueDefault("release_project_id", "0")
	releaseProjectID, err := strconv.Atoi(_releaseProjectID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	requirment := ctx.FormValue("requirement")
	workGroup := ctx.FormValue("work_group")
	pstl := ctx.URLParamBoolDefault("pstl", false)
	order := ctx.FormValueDefault("order", "desc")
	by := ctx.FormValueDefault("by", "release_project")
	export := ctx.FormValue("export")

	pstlID := uint(0)
	if pstl {
		pstlID, err = dao.GetAuthId(ctx)
		if err != nil {
			ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
			return
		}

	}
	summary := dappmergerequestdashboard.WorkPackageSummary{}
	items, err := summary.All(uint(releaseProjectID), 0, pstlID, requirment, workGroup, order, by)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	if export == "1" {
		ExportWorkPackageSummary(ctx, summary, items)
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, items, response.NoErr.Msg))
	return
}

func WorkPackageSumaryColumns(ctx iris.Context) {
	summary := dappmergerequestdashboard.WorkPackageSummary{}
	columns, err := summary.GetColumns()
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, columns, response.NoErr.Msg))
	return
}

func ExportWorkPackageSummary(ctx iris.Context, summary dappmergerequestdashboard.WorkPackageSummary, items []*dappmergerequestdashboard.WorkPackageSummary) {
	fileName := fmt.Sprintf("工作包统计_%s.xlsx", time.Now().Format("20060102150405"))
	file := excelize.NewFile()
	streamWriter, err := file.NewStreamWriter("Sheet1")
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	rowNum := 1
	_, rows := summary.ExportWorkPackageSummary(items)
	for _, row := range rows {
		cell, _ := excelize.CoordinatesToCellName(1, rowNum)
		if err := streamWriter.SetRow(cell, row); err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
			return
		}
		rowNum++
	}
	if err := streamWriter.Flush(); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	if err := file.SaveAs(filepath.Join("/tmp", fileName)); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	defer os.Remove(filepath.Join("/tmp", fileName))
	ctx.SendFile(filepath.Join("/tmp", fileName), url.QueryEscape(fileName))
	return
}

func BugWorkGroupSummary(ctx iris.Context) {
	bugOS := ctx.FormValue("bug_os")
	bugOwnerGroup := ctx.FormValue("bug_owner_group")
	pstl := ctx.URLParamBoolDefault("pstl", false)
	order := ctx.FormValueDefault("order", "desc")
	by := ctx.FormValueDefault("by", "bug_os")
	export := ctx.FormValue("export")
	var err error
	pstlID := uint(0)
	if pstl {
		pstlID, err = dao.GetAuthId(ctx)
		if err != nil {
			ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
			return
		}

	}
	summary := dappmergerequestbugworkgroupsummary.BugGroupSummary{}
	items, err := summary.All(bugOS, bugOwnerGroup, pstlID, order, by)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	if export == "1" {
		ExportBugWorkGroupSummary(ctx, summary, items)
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, items, response.NoErr.Msg))
	return
}

func BugWorkGroupSumaryColumns(ctx iris.Context) {
	summary := dappmergerequestbugworkgroupsummary.BugGroupSummary{}
	columns, err := summary.GetColumns()
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, columns, response.NoErr.Msg))
	return
}

func ExportBugWorkGroupSummary(ctx iris.Context, summary dappmergerequestbugworkgroupsummary.BugGroupSummary, items []*dappmergerequestbugworkgroupsummary.BugGroupSummary) {
	fileName := fmt.Sprintf("BUG工作组统计_%s.xlsx", time.Now().Format("20060102150405"))
	file := excelize.NewFile()
	streamWriter, err := file.NewStreamWriter("Sheet1")
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	rowNum := 1
	_, rows := summary.ExportBugSummary(items)
	for _, row := range rows {
		cell, _ := excelize.CoordinatesToCellName(1, rowNum)
		if err := streamWriter.SetRow(cell, row); err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
			return
		}
		rowNum++
	}
	if err := streamWriter.Flush(); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	if err := file.SaveAs(filepath.Join("/tmp", fileName)); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	defer os.Remove(filepath.Join("/tmp", fileName))
	ctx.SendFile(filepath.Join("/tmp", fileName), url.QueryEscape(fileName))
	return
}

func BugOwnerSummary(ctx iris.Context) {
	bugOS := ctx.FormValue("bug_os")
	bugOwnerGroup := ctx.FormValue("bug_owner_group")
	pstl := ctx.URLParamBoolDefault("pstl", false)
	order := ctx.FormValueDefault("order", "desc")
	by := ctx.FormValueDefault("by", "bug_os")
	export := ctx.FormValue("export")
	var err error
	pstlID := uint(0)
	if pstl {
		pstlID, err = dao.GetAuthId(ctx)
		if err != nil {
			ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
			return
		}

	}
	summary := dappmergerequestbugownersummary.BugOwnerSummary{}
	items, err := summary.All(bugOS, bugOwnerGroup, pstlID, order, by)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	if export == "1" {
		ExportBugOwnerSummary(ctx, summary, items)
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, items, response.NoErr.Msg))
	return
}

func BugOwnerSumaryColumns(ctx iris.Context) {
	summary := dappmergerequestbugownersummary.BugOwnerSummary{}
	columns, err := summary.GetColumns()
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, columns, response.NoErr.Msg))
	return
}

func ExportBugOwnerSummary(ctx iris.Context, summary dappmergerequestbugownersummary.BugOwnerSummary, items []*dappmergerequestbugownersummary.BugOwnerSummary) {
	fileName := fmt.Sprintf("BUG负责人统计_%s.xlsx", time.Now().Format("20060102150405"))
	file := excelize.NewFile()
	streamWriter, err := file.NewStreamWriter("Sheet1")
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	rowNum := 1
	_, rows := summary.ExportBugSummary(items)
	for _, row := range rows {
		cell, _ := excelize.CoordinatesToCellName(1, rowNum)
		if err := streamWriter.SetRow(cell, row); err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
			return
		}
		rowNum++
	}
	if err := streamWriter.Flush(); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	if err := file.SaveAs(filepath.Join("/tmp", fileName)); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	defer os.Remove(filepath.Join("/tmp", fileName))
	ctx.SendFile(filepath.Join("/tmp", fileName), url.QueryEscape(fileName))
	return
}

func BugSummary(ctx iris.Context) {
	bugOS := ctx.FormValue("bug_os")
	pstl := ctx.URLParamBoolDefault("pstl", false)
	order := ctx.FormValueDefault("order", "desc")
	by := ctx.FormValueDefault("by", "bug_os")
	export := ctx.FormValue("export")
	var err error
	pstlID := uint(0)
	if pstl {
		pstlID, err = dao.GetAuthId(ctx)
		if err != nil {
			ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
			return
		}

	}
	summary := dappmergerequestbugsummary.BugSummary{}
	err = summary.All(bugOS, pstlID, order, by)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	if export == "1" {
		ExportBugSummary(ctx, summary)
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, summary, response.NoErr.Msg))
	return
}

func BugSumaryColumns(ctx iris.Context) {
	summary := dappmergerequestbugsummary.BugSummary{}
	columns, err := summary.GetColumns()
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, columns, response.NoErr.Msg))
	return
}

func ExportBugSummary(ctx iris.Context, summary dappmergerequestbugsummary.BugSummary) {
	// fileName := fmt.Sprintf("BUG负责人统计_%s.xlsx", time.Now().Format("20060102150405"))
	// file := excelize.NewFile()
	// streamWriter, err := file.NewStreamWriter("Sheet1")
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
	// 	return
	// }

	// rowNum := 1
	// _, rows := summary.ExportBugSummary(items)
	// for _, row := range rows {
	// 	cell, _ := excelize.CoordinatesToCellName(1, rowNum)
	// 	if err := streamWriter.SetRow(cell, row); err != nil {
	// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
	// 		return
	// 	}
	// 	rowNum++
	// }
	// if err := streamWriter.Flush(); err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
	// 	return
	// }
	// if err := file.SaveAs(filepath.Join("/tmp", fileName)); err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
	// 	return
	// }
	// defer os.Remove(filepath.Join("/tmp", fileName))
	// ctx.SendFile(filepath.Join("/tmp", fileName), url.QueryEscape(fileName))
	return
}

func GetBugDistinctColumns(ctx iris.Context) {

	bugOs := ctx.FormValue("bug_os")
	name := ctx.FormValue("name")
	if len(bugOs) == 0 || len(name) == 0 {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	ret, err := dbug.GetDistinctColumns(bugOs, name)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, ret, response.NoErr.Msg))
	return
}

func BugOwnerDelayTop(ctx iris.Context) {
	bugOS := ctx.FormValue("bug_os")
	bugOwnerGroup := ctx.FormValue("bug_owner_group")

	// pstl := ctx.URLParamBoolDefault("pstl", false)
	order := ctx.FormValueDefault("order", "desc")
	by := ctx.FormValueDefault("by", "total")
	// export := ctx.FormValue("export")
	var err error

	items, err := dappmergerequestbugsummary.GetBugOwnerDelayTop(bugOS, bugOwnerGroup, order, by, false)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, items, response.NoErr.Msg))
	return
}

func BugOwnerGroupDelayTop(ctx iris.Context) {
	bugOS := ctx.FormValue("bug_os")
	bugOwnerGroup := ctx.FormValue("bug_owner_group")

	// pstl := ctx.URLParamBoolDefault("pstl", false)
	order := ctx.FormValueDefault("order", "desc")
	by := ctx.FormValueDefault("by", "total")
	// export := ctx.FormValue("export")
	var err error

	items, err := dappmergerequestbugsummary.GetBugOwnerGroupDelayTop(bugOS, bugOwnerGroup, order, by, false)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, items, response.NoErr.Msg))
	return
}
