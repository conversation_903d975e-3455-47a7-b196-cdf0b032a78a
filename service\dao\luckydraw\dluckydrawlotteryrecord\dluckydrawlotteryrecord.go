package dluckydrawlotteryrecord

import (
	"fmt"
	"reflect"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/luckydraw"
)

const ModelName = "中奖记录表"

type Response struct {
	Id         uint   `json:"id" xlsx:"ID" idx:"1" `
	UpdatedAt  string `json:"updated_at" xlsx:"更新时间" idx:"2" `
	CreatedAt  string `json:"created_at" xlsx:"创建时间" idx:"3" `
	Name       string `json:"name" xlsx:"姓名" idx:"4" `
	WorkNumber string `json:"work_number"  xlsx:"工号" idx:"5" `
	Zodiac     uint   `json:"zodiac"`
	ZodiacName string `json:"zodiac_name" xlsx:"生肖" idx:"6" `
	City       uint   `json:"city" `
	CityName   string `json:"city_name"  xlsx:"所在城市" idx:"7" `
}

type ListResponse struct {
	Response
}

type Request struct {
	luckydraw.LuckydrawLotteryRecord
}

type ToolsList struct {
	ExtensionTool string `json:"extension_tool"`
	MD5           string `json:"md5"`
}

type ExtensionGroup struct {
	TaskID        uint        `json:"task_id" form:"task_id"`
	ExtensionName string      `json:"extension_name" form:"extension_name"`
	ToolsList     []ToolsList `json:"toolsLists"` // 包含所有相关的ExtensionTool和MD5
	ProcInstID    uint        ` json:"proc_inst_id" form:"proc_inst_id"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *luckydraw.LuckydrawLotteryRecord {
	return &luckydraw.LuckydrawLotteryRecord{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) ExportAll(city, zodiac int) ([]*Response, error) {
	var items []*Response

	db := easygorm.GetEasyGormDb().Model(Model())
	if city > 0 {
		db = db.Where("city = ?", city)
	}
	if zodiac > 0 {
		db = db.Where("zodiac = ?", zodiac)
	}

	err := db.Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	return items, nil
}

func (s *Response) ExportRecord(items []*Response) ([]interface{}, [][]interface{}) {
	xt := reflect.TypeOf(s)

	rows := [][]interface{}{}
	headers := []interface{}{}
	for i := 0; i < xt.Elem().NumField(); i++ {
		head, ok := xt.Elem().Field(i).Tag.Lookup("xlsx")
		if ok {
			headers = append(headers, head)
		}
	}
	rows = append(rows, headers)

	for _, e := range items {
		cells := []interface{}{}
		xv := reflect.ValueOf(e)
		for i := 0; i < xv.Elem().NumField(); i++ {
			_, ok := xt.Elem().Field(i).Tag.Lookup("xlsx")
			if ok {
				cells = append(cells, xv.Elem().Field(i).Interface())
			}

		}
		rows = append(rows, cells)
	}
	return headers, rows
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func All() ([]*ListResponse, error) {
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	err := db.Find(&items).Error
	return items, err
}

func FindInTaskIds(taskIDs []uint) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("task_id in ? ", taskIDs).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	return items, nil
}

func FindByProcinstID(procinstID, taskID uint) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("proc_inst_id = ? and task_id=?", procinstID, taskID).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	return items, nil
}

func (u *Response) CheckRecordExists(EmployeeID, Name string) error {
	// err := easygorm.GetEasyGormDb().Model(Model()).Where("work_number=? and name=? ", EmployeeID, Name).Find(u).Error
	err := easygorm.GetEasyGormDb().Model(Model()).Where("work_number=?", EmployeeID).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}
