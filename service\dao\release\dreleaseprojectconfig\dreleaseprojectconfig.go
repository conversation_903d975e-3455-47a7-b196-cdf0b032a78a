package dreleaseprojectconfig

import (
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/release"
	"strings"
	"time"

	"gorm.io/gorm"
)

const ModelName = "项目配置"

type ReleaseProject struct {
	ID                   int             `json:"id"`
	Name                 string          `json:"name"`
	PmoID                uint            `json:"pmo_id"`
	PmID                 uint            `json:"pm_id"`
	CapoID               uint            `json:"capo_id" form:"capo_id"`
	PtmID                uint            `json:"ptm_id" form:"ptm_id"`
	CmaID                uint            `json:"cma_id" form:"cma_id"`
	PqaID                uint            `json:"pqa_id" form:"pqa_id"`
	BaseReleaseProjectID uint            `json:"base_release_project_id"`
	BaseProject          *ReleaseProject `gorm:"->; foreignKey:BaseReleaseProjectID" json:"base_project"`
}

type Project struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
	Repo string `json:"repo"`
}

type User struct {
	ID       uint   `json:"id"`
	Name     string `json:"name"`
	Username string `json:"username"`
}

type MergeRequestWorkPackage struct {
	ID               uint   `json:"id"`
	Name             string `gorm:"not null;uniqueIndex:work_package_name_release_project_id_idx; type:varchar(60)" json:"name"`
	ReleaseProjectID uint   `gorm:"not null;uniqueIndex:work_package_name_release_project_id_idx" json:"release_project_id"`
	OwnerID          uint   `gorm:"not null" json:"owner_id"`
	PstlID           uint   `gorm:"not null" json:"pstl_id"`
}

type ReleaseProjectConfig struct {
	release.ReleaseProjectConfig
	ReleaseProject ReleaseProject             `gorm:"->;foreignKey:ReleaseProjectID" json:"release_project"`
	BuildProject   Project                    `gorm:"->;foreignKey:BuildProjectID" json:"build_project"`
	Workpackages   []*MergeRequestWorkPackage `gorm:"->;foreignKey:ReleaseProjectID;references:ReleaseProjectID" json:"workpackages"`
}

type ReleaseTrunkLog struct {
	release.ReleaseTrunkLog
	ReleaseProject ReleaseProject `gorm:"->;foreignKey:ReleaseProjectID" json:"release_project"`
}

// type MergeRequest struct {
// 	ID   uint   `json:"id"`
// 	Name string `json:"name"`
// }

type ListResponse struct {
	ReleaseProjectConfig
}

type Request struct {
	release.ReleaseProjectConfig
}

func (a *ReleaseProjectConfig) ModelName() string {
	return ModelName
}

func Model() *release.ReleaseProjectConfig {
	return &release.ReleaseProjectConfig{}
}

func (a *ReleaseProjectConfig) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Preload("Assignees").Preload("Reviewers")
	where := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		where = where.Where("group_name_ch like ?", fmt.Sprintf("%%%s%%", name)).
			Or("group_name_en like ?", fmt.Sprintf("%%%s%%", name)).
			Or("category_name_ch like ?", fmt.Sprintf("%%%s%%", name)).
			Or("category_name_en like ?", fmt.Sprintf("%%%s%%", name)).
			Or("description_ch like ?", fmt.Sprintf("%%%s%%", name)).
			Or("description_en like ?", fmt.Sprintf("%%%s%%", name))
		if strings.Contains("海外库", name) {
			where = where.Or("oversea = 1")
		}
		if strings.Contains("大库", name) {
			where = where.Or("large = 1")
		}
		if strings.Contains("小库", name) {
			where = where.Or("small = 1")
		}
		if strings.Contains("中库", name) {
			where = where.Or("middle = 1")
		}
		if strings.Contains("默认阻断", name) {
			where = where.Or("pre_def_block = 1")
		}
	}
	db = db.Where(where)
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *ReleaseProjectConfig) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *ReleaseProjectConfig) Create(object map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		if v, ok := object["BuildProjectBranch"]; ok && v.(*string) != nil && *v.(*string) == "Trunk" {
			now := time.Now()
			trunkLog := ReleaseTrunkLog{}
			err := tx.Model(&trunkLog).Order("id desc").Limit(1).Find(&trunkLog).Error
			if err != nil {
				return err
			}
			if trunkLog.ID > 0 && trunkLog.EndedAt.IsZero() {
				err = tx.Model(&trunkLog).Where("id = ?", trunkLog.ID).Updates(map[string]interface{}{"EndedAt": now, "UpdatedAt": now}).Error
				if err != nil {
					return err
				}
			}
			err = tx.Model(&trunkLog).Create(map[string]interface{}{
				"ReleaseProjectID": object["ReleaseProjectID"],
				"StartedAt":        now,
				"CreatedAt":        now,
			}).Error
			if err != nil {
				return err
			}
		}
		err := tx.Model(Model()).Create(object).Error
		return err
	})
	return err
}

func (this *ReleaseProjectConfig) CreateV2(object interface{}) error {
	return nil
}

func (a *ReleaseProjectConfig) BatchCreate(mrs []*ReleaseProjectConfig) error {
	err := easygorm.GetEasyGormDb().Create(&mrs).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (a *ReleaseProjectConfig) Update(id uint, object map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		if v, ok := object["BuildProjectBranch"]; ok && v.(*string) != nil && *v.(*string) == "Trunk" {
			now := time.Now()
			trunkLog := ReleaseTrunkLog{}
			err := tx.Model(&trunkLog).Order("id desc").Limit(1).Find(&trunkLog).Error
			if err != nil {
				return err
			}
			if trunkLog.ID > 0 && trunkLog.EndedAt.IsZero() {
				err = tx.Model(&trunkLog).Where("id = ?", trunkLog.ID).Updates(map[string]interface{}{"EndedAt": now, "UpdatedAt": now}).Error
				if err != nil {
					return err
				}
			}
			err = tx.Model(&trunkLog).Create(map[string]interface{}{
				"ReleaseProjectID": object["ReleaseProjectID"],
				"StartedAt":        now,
				"CreatedAt":        now,
			}).Error
			if err != nil {
				return err
			}
		}
		err := tx.Model(Model()).Where("id = ?", id).Updates(object).Error
		return err
	})
	return err
}

func (a *ReleaseProjectConfig) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *ReleaseProjectConfig) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *ReleaseProjectConfig) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *ReleaseProjectConfig) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("BuildProject").Preload("ReleaseProject").Preload("Workpackages").Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *ReleaseProjectConfig) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("BuildProject").Preload("ReleaseProject").Preload("Workpackages").Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *ReleaseProjectConfig) Last(mergeRequestID uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("merge_request_id = ?", mergeRequestID).Order("id desc").Limit(1).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func AllEnabledConfig() (result []*ReleaseProjectConfig, err error) {
	err = easygorm.GetEasyGormDb().Model(Model()).Where("enable_notice = true").Preload("BuildProject").Preload("ReleaseProject").Preload("Workpackages").Find(&result).Error
	return result, err
}
func AllGcovEnabledConfig() (result []*ReleaseProjectConfig, err error) {
	err = easygorm.GetEasyGormDb().Model(Model()).Where("enable_gcov = true").Preload("BuildProject").Preload("ReleaseProject").Preload("Workpackages").Find(&result).Error
	return result, err
}

func FindConfigByName(name string) (item *ReleaseProjectConfig, err error) {
	err = easygorm.GetEasyGormDb().Debug().
		Model(Model()).
		Preload("ReleaseProject").
		Preload("ReleaseProject.BaseProject").
		Preload("Workpackages").
		Where(
			"release_project_id in (?)",
			easygorm.
				GetEasyGormDb().
				Model(&ReleaseProject{}).
				Select("id").
				Where("name = ?", name),
		).
		Find(&item).
		Error
	return item, err
}

func FindConfigByBuildProjectAndBranch(buildProjectID uint, buildProjectBranch string) (items []*ReleaseProjectConfig, err error) {
	err = easygorm.GetEasyGormDb().Model(Model()).Where("build_project_id = ? and build_project_branch = ?", buildProjectID, buildProjectBranch).Preload("BuildProject").Preload("ReleaseProject").Preload("Workpackages").Find(&items).Error
	return items, err
}

func UpdateProjectInfoForce(releaseProjectID, buildProjectID uint, buildProjectBranch string) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(Model()).Where("build_project_id = ? and build_project_branch = ?", buildProjectID, buildProjectBranch).Updates(map[string]interface{}{
			"BuildProjectID":     0,
			"BuildProjectBranch": "",
		}).Error
		if err != nil {
			return err
		}

		config := ReleaseProjectConfig{}
		err = tx.Model(Model()).Where("release_project_id = ?", releaseProjectID).Find(&config).Error
		if err != nil {
			return err
		}
		if config.ID == 0 {
			err = tx.Model(Model()).Create(map[string]interface{}{
				"ReleaseProjectID":   releaseProjectID,
				"BuildProjectID":     buildProjectID,
				"BuildProjectBranch": buildProjectBranch,
			}).Error
		} else {
			err = tx.Model(Model()).Where("id = ?", config.ID).Updates(map[string]interface{}{
				"ReleaseProjectID":   releaseProjectID,
				"BuildProjectID":     buildProjectID,
				"BuildProjectBranch": buildProjectBranch,
			}).Error
		}
		return err
	})
	return err
}

func FindConfigByBranch(buildProjectBranch string) (items []*ReleaseProjectConfig, err error) {
	err = easygorm.GetEasyGormDb().Model(Model()).Where("build_project_branch = ?", buildProjectBranch).Preload("BuildProject").Preload("ReleaseProject").Find(&items).Error
	return items, err
}

func FindConfigByProject(buildProjectBranch string) (items []*ReleaseProjectConfig, err error) {
	err = easygorm.GetEasyGormDb().Model(Model()).Where("build_project_branch = ?", buildProjectBranch).Preload("BuildProject").Preload("ReleaseProject").Find(&items).Error
	return items, err
}

func FindByReleaseProjectID(releaseProjectID uint) (ReleaseProjectConfig, error) {
	item := ReleaseProjectConfig{}
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("BuildProject").Preload("ReleaseProject").Preload("Workpackages").Where("release_project_id = ?", releaseProjectID).Find(&item).Error
	return item, err
}
