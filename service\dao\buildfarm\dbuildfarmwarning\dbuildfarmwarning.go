package dbuildfarmwarning

import (
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/buildfarm"
	"time"

	"github.com/pkg/errors"
	"gorm.io/gorm"
)

const ModelName = "编译农场编译Warning"

type User struct {
	ID       uint   `json:"id"`
	Name     string `json:"name"`
	Username string `json:"username"`
}

type BuildfarmWarning struct {
	buildfarm.BuildfarmWarning
}

type BuildfarmWarningSummary struct {
	buildfarm.BuildfarmWarning
	FirstCheckedAt  time.Time `json:"first_checked_at"`
	LastCheckedAt   time.Time `json:"last_checked_at"`
	FullPath        string    `json:"full_path"`
	Component       string    `json:"component"`
	ComponentPacket string    `json:"component_packet"`
	Owner           string    `json:"owner"`
	Comment         string    `json:"comment"`
}

type ListResponse struct {
	BuildfarmWarning
}

type Request struct {
}

func (this *BuildfarmWarning) ModelName() string {
	return ModelName
}

func Model() *buildfarm.BuildfarmWarning {
	return &buildfarm.BuildfarmWarning{}
}

func (this *BuildfarmWarning) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *BuildfarmWarning) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *BuildfarmWarning) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *BuildfarmWarning) CreateV2(object interface{}) error {
	return nil
}

func (this *BuildfarmWarning) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	return errors.Wrap(err, "")
}

func (this *BuildfarmWarning) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmWarning) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmWarning) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmWarning) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Where("bug_id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmWarning) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func BatchCreate(objects []map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(Model()).Create(objects).Error
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func Summary() ([]*BuildfarmWarningSummary, error) {
	items := []*BuildfarmWarningSummary{}

	selects := []string{
		"bw.detail",
		"bw.position",
		"bw.filename",
		"bw.source_dir",
		"bw.project",
		"bw.branch",
		"bw.product",
		"bw.build_type",
		"bw.first_checked_at",
		"bw.last_checked_at",
		"cc.full_path",
		"cc.component",
		"cc.component_packet",
		"cc.owner",
		"cc.comment",
	}

	db := easygorm.GetEasyGormDb().
		Table(`(SELECT
					detail,
					POSITION,
					filename,
					source_dir,
					project,
					branch,
					product,
					build_type,
					MIN(created_at) first_checked_at,
					MAX(created_at) last_checked_at
				from buildfarm_warnings
				group by detail, position, filename, source_dir, project, branch, product, build_type
				)
			bw`).
		Joins(`left join common_ntos_file_components cc on cc.full_path LIKE CONCAT('%',"/", bw.filename) and cc.full_path like CONCAT(source_dir, "/", "%")`).
		Select(selects).Preload("user").
		Where("cc.component is not null and bw.project = 'Trunk' and bw.product = 'z5100'")

	err := db.Scan(&items).Error
	if err != nil {
		return nil, err
	}

	return items, nil
}

func MaxCheckedAt() (time.Time, error) {
	var maxCreatedAt time.Time
	err := easygorm.GetEasyGormDb().Model(Model()).Select("max(created_at) `max_created_at`").Scan(&maxCreatedAt).Error
	return maxCreatedAt, err
}
