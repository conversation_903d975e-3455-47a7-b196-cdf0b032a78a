package dbug

import (
	"fmt"
	"strings"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/bugsync"
)

const ModelName = "BUG表"

type BugSyncResponse struct {
	/*
		"state": "SUCCESS",
		"data": [
			{
				"rownum": 1,
				"bugId": 259201,                            //BUGID
				"bugBelong": "测试人",                        //bug归属人
				"bugOwner": "测试人",                        //bug负责人
				"bugTestCharger": "测试2",                    //测试负责人
				"bugOs": "测试项目",                        //bug操作系统
				"bugProduct": "测试产品",                    //bug产品
				"workpacketName": null,                        //bug工作包
				"summary": "描述",                            //bug描述
				"mainbugid": null,                            //bug是否是从某个BUG镜像出来的BUG，如果有直接是父bugid
				"samebugid": null,                            //bug是否是从某个BUGsameas出来的BUG，如果有直接是父bugid
				"thedate": "2015-06-23 11:20:56",            //bug创建时间
				"lastupdatedate": "2015-06-23 15:28:53",    //bug更新时间
				"bugState": "DENIAL-ByDevelopment",            //bug状态
				"disabled": null                            //bug是否无效 0或者null 表示无效；1或者true表示有效
			}
		],
		"total":1,
		"message": null
	*/
	State   string         `json:"state"`
	Data    []*BugResponse `json:"data"`
	Total   int            `json:"total"`
	Message string         `json:"message"`
}

type BugResponse struct {
	/*
		"rownum": 1,
		"bugId": 259201,                            //BUGID
		"bugBelong": "测试人",                        //bug归属人
		"bugOwner": "测试人",                        //bug负责人
		"bugTestCharger": "测试2",                    //测试负责人
		"bugOs": "测试项目",                        //bug操作系统
		"bugProduct": "测试产品",                    //bug产品
		"workpacketName": null,                        //bug工作包
		"summary": "描述",                            //bug描述
		"mainbugid": null,                            //bug是否是从某个BUG镜像出来的BUG，如果有直接是父bugid
		"samebugid": null,                            //bug是否是从某个BUGsameas出来的BUG，如果有直接是父bugid
		"thedate": "2015-06-23 11:20:56",            //bug创建时间
		"lastupdatedate": "2015-06-23 15:28:53",    //bug更新时间
		"bugState": "DENIAL-ByDevelopment",            //bug状态
		"disabled": null
	*/
	RowNum            int    `json:"rownum"`
	BugID             int    `json:"bugId"`
	BugBelong         string `json:"bugBelong"`
	BugOwner          string `json:"bugOwner"`
	BugTestCharger    string `json:"bugTestCharger"`
	BugOS             string `json:"bugOs"`
	BugProduct        string `json:"bugProduct"`
	BugWorkpacketName string `json:"workpacketName"`
	BugSummary        string `json:"summary"`
	MainBugID         int    `json:"mainbugid"`
	SameBugID         int    `json:"samebugid"`
	BugCreatedAt      string `json:"thedate"`
	BugUpdatedAt      string `json:"lastupdatedate"`
	BugState          string `json:"bugState"`
	BugDisabled       bool   `json:"disabled"`
}

type Response struct {
	bugsync.Bug
	Children []*ListResponse `gorm:"-" json:"children"`
}

type ListResponse struct {
	Response
}

type Request struct {
	Id uint `json:"id"`
}

func (this *Response) ModelName() string {
	return ModelName
}

func Model() *bugsync.Bug {
	return &bugsync.Bug{}
}

func (this *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (this *Response) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

/*
| NEW                  |
| DELAY                |
| CLOSED-ByTest        |
| ASSIGNED             |
| GIVEUP               |
| REQUEST              |
| DENIAL-ByDevelopment |
| CLOSED-ByDevelopment |
| CHECKED              |
| DENIAL-ByTest        |
| DENIAL-ByPSD         |
| REOPENED             |
| RESOLVED             |
BUG判定为需要镜像处理：CLOSED-ByTest， CLOSED-ByDevelopment， RESOLVED, CHECKED, DELAY?
BUG判定为无需镜像处理: DENIAL-ByTest, DENIAL-ByPSD, DENIAL-ByDevelopment, REQUEST, GIVEUP?, NEW, ASSIGNED
*/

func FindResolveMainBug() ([]*ListResponse, error) {
	var res []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where(`main_bug_id = 0 and bug_state in ("CLOSED-ByTest", "CLOSED-ByDevelopment", "RESOLVED", "CHECKED", "DELAY")`).Find(&res).Error // and bug_workpacket_name = "遗留或delay的bug(其他)"
	return res, err
}

func FindMirrorBug(mainBugIDs []int) ([]*ListResponse, error) {
	var res []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where(`main_bug_id in ?`, mainBugIDs).Find(&res).Error
	return res, err
}

func FindAllBug(page, pageSize int, sort, orderBy string, bugOS, mirrorStatus, resolveStatus, bugBelong, bugOwner, bugTestCharger, bugState, createdAt, updatedAt string) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Where(`main_bug_id = 0`)

	if len(bugOS) > 0 {
		db = db.Where("bug_os = ?", bugOS)
	}
	if len(bugBelong) > 0 {
		db = db.Where("bug_belong = ?", bugBelong)
	}
	if len(bugOwner) > 0 {
		db = db.Where("bug_owner = ?", bugOwner)
	}
	if len(bugTestCharger) > 0 {
		db = db.Where("bug_test_charger = ?", bugTestCharger)
	}
	if len(bugState) > 0 {
		db = db.Where("bug_state = ?", bugState)
	}
	if len(mirrorStatus) > 0 {
		db = db.Where("mirror_status = ?", mirrorStatus)
	}
	if len(resolveStatus) > 0 {
		db = db.Where("resolve_status = ?", resolveStatus)
	}
	if len(createdAt) > 0 {
		array := strings.Split(createdAt, ",")
		db = db.Where("bug_created_at between ? and ?", array[0], array[1])
	}
	if len(updatedAt) > 0 {
		array := strings.Split(updatedAt, ",")
		db = db.Where("bug_updated_at between ? and ?", array[0], array[1])
	}
	if len(orderBy) == 0 {
		orderBy = "bug_created_at"
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	AddMirrorBug(res)
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func AddMirrorBug(items []*ListResponse) {
	bugIds := []int{}
	for _, item := range items {
		bugIds = append(bugIds, item.BugID)
	}
	mirrorBugs := []*ListResponse{}
	mirrorBugMap := map[int][]*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("main_bug_id in ?", bugIds).Find(&mirrorBugs).Error
	if err != nil {
		fmt.Println(err)
	}
	for _, mirrorBug := range mirrorBugs {
		if _, ok := mirrorBugMap[mirrorBug.MainBugID]; !ok {
			mirrorBugMap[mirrorBug.MainBugID] = []*ListResponse{mirrorBug}
		} else {
			mirrorBugMap[mirrorBug.MainBugID] = append(mirrorBugMap[mirrorBug.MainBugID], mirrorBug)
		}
	}
	for _, item := range items {
		item.Children = mirrorBugMap[item.BugID]
	}
}

func FindDistinctByColumn(column string, bugOS string) ([]map[string]interface{}, error) {
	result := []map[string]interface{}{}

	db := easygorm.GetEasyGormDb().Model(Model()).Select("bug_os", column).Group("bug_os," + column)
	if len(bugOS) > 0 {
		db = db.Where("bug_os in ?", strings.Split(bugOS, ","))
	}

	err := db.Order(column).Find(&result).Error
	return result, err
}

// 仅同步NTOS、安全云、MACC项目BUG
// 同步为分内测及未决2张表
// 未决状态暂定为：NEW、ASSIGNED、VERIFIED、RENEW、REOPENED、REQUEST-REPRODUCE、REQUEST-NO_TO_SOLVE、REQUEST-CHANGE_REQ
func FinBugSyncToFeishu() ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).
		Where(`bug_os like "NTOS%" or bug_os like "安全云%" or bug_os like "MACC%" or bug_os like '12.%' or bug_os in ('11.9PR100', '11.9PR103', '11.9PR104')`).
		Where(`bug_os != "NTOS_TOOL"`).
		Order("bug_created_at desc").
		Find(&items).Error
	return items, err
}

// 仅同步NTOS、安全云、MACC项目BUG
// 同步为分内测及未决2张表
// 未决状态暂定为：NEW、ASSIGNED、VERIFIED、RENEW、REOPENED、REQUEST-REPRODUCE、REQUEST-NO_TO_SOLVE、REQUEST-CHANGE_REQ
func FinBugByProjectSyncToFeishu(project string) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).
		Where(`bug_os = ?`, project).
		Order("bug_created_at desc").
		Find(&items).Error
	return items, err
}
