package datasync

import (
	"errors"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/datasync/dbuglog"
	"strconv"
	"time"
)

func BugLogSyncWorker(delay int) error {
	// todo: 检查同步记录，获取同步时间，如果没有，从七天前开始，按小时同步
	modifyDateArray := []string{}
	_url := "https://dataware.ruijie.com.cn/api/public/data-api/safe_product_line_bug_log_list/info.data"

	modifyDateArray = libs.GetDateRange(-1)

	if len(modifyDateArray) < 2 {
		return nil
	}

	page := 1
	rows := 1000

	for {
		data := map[string]string{
			"sid":           "ODMzYjcxOTU1",
			"minModifyDate": modifyDateArray[0],
			"maxModifyDate": modifyDateArray[len(modifyDateArray)-1],
			"page":          strconv.Itoa(page),
			"rows":          strconv.Itoa(rows),
		}

		var result dbuglog.BugLogSyncResponse
		var errMsg dbuglog.BugLogSyncResponse
		resp, err := SyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(data).Get(_url)
		if err != nil {
			logging.ErrorLogger.Errorf("get bug error", err.Error())
			return err
		}
		if resp.IsSuccessState() {
			if result.State == "SUCCESS" {
				err := dbuglog.UpdateOrCreateBugLogTransaction(result.Data, _url, data, resp.Request.Method, result.State, result.Message)
				if err != nil {
					logging.ErrorLogger.Errorf("update or create bug logs error", err.Error())
					return err
				}
			} else {
				logging.ErrorLogger.Errorf("get bug logs failed", result.State, result.Message)
				return errors.New("get bugs failed")
			}
		} else {
			logging.ErrorLogger.Errorf("get bug logs unkown error")
			return errors.New("unkown error")
		}
		time.Sleep(time.Duration(delay) * time.Second)
		if result.Total > rows*page {
			page++
		} else {
			break
		}
	}

	return nil
}
