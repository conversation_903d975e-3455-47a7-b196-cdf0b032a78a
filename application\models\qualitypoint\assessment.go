package qualitypoint

import (
	"irisAdminApi/application/models"
)

// 考核项大类表
type QualityCategorie struct {
	models.ModelBase
	Name           string `gorm:"not null;type:varchar(100)"  json:"name"  form:"name"`
	AuditManagerID uint   `gorm:"not null" json:"audit_manager_id" form:"audit_manager_id"` //违规项审计管理人员ID
}

// 考核项细项表
type QualityAssessmentItem struct {
	models.ModelBase
	Name                     string  `gorm:"not null;type:varchar(100)"  json:"name" form:"name"`                           //考核项名称
	CategoryID               uint    `gorm:"not null"  json:"category_id"  form:"category_id"`                              //关联的大类别ID
	Code                     string  `gorm:"not null;type:varchar(100)"  json:"code" form:"code"`                           //考核项编号
	Direction                uint    `gorm:"not null"  json:"direction"  from:"direction"`                                  //考核方向 1：正向 2：负向
	Description              string  `gorm:"not null" json:"description"  from:"description"`                               //具体考核项描述
	AssessmentTarget         string  `gorm:"not null" json:"assessment_target" from:"assessment_target"`                    //责任角色，例如 开发负责人、质量负责人等
	AssessmentPoints         float32 `gorm:"not null" json:"assessment_points"  from:"assessment_points"`                   //扣分分值
	IssueLevel               uint    `gorm:"not null" json:"issue_level"  from:"issue_level"`                               //问题等级
	ImpactDegree             uint    `gorm:"not null" json:"impact_degree"  from:"impact_degree"`                           //影响程度
	DeductionTask            string  `gorm:"not null" json:"deduction_task"  from:"deduction_task"`                         //消分任务
	DeductionTaskDescription string  `gorm:"not null" json:"deduction_task_description"  from:"deduction_task_description"` //消分任务描述
}

// 用户积分表
type QualityUserPoint struct {
	models.ModelBase
	UserID          uint    `gorm:"not null" json:"user_id" form:"user_id"`                   //用户ID
	TotalPoints     float32 `gorm:"not null" json:"total_points" form:"total_points"`         //当前用户的总积分，初始为12分
	Year            uint    `gorm:"not null" json:"year" form:"year"`                         //当前积分的年度
	PointsDeducted  float32 `gorm:"not null" json:"points_deducted" form:"points_deducted"`   //当年已扣减积分（包括往年未消分的累计扣分）
	PointsRecovered float32 `gorm:"not null" json:"points_recovered" form:"points_recovered"` //当年已消分积分（恢复的积分总数）
}

// 积分扣减记录表
type QualityPointsDeduction struct {
	models.ModelBase
	UserID         uint    `gorm:"not null" json:"user_id" form:"user_id"`                 //用户ID
	ViolationID    uint    `gorm:"not null" json:"violation_id" form:"violation_id"`       //事项ID
	PointsDeducted float32 `gorm:"not null" json:"points_deducted" form:"points_deducted"` //扣减的积分值
	IssueLevel     uint    `gorm:"not null" json:"issue_level"  from:"issue_level"`        //问题等级
	ImpactDegree   uint    `gorm:"not null" json:"impact_degree"  from:"impact_degree"`    //影响程度
	Year           uint    `gorm:"not null" json:"year" form:"year"`                       //扣分操作的年度
}

// 积分消分记录表
type QualityPointsRecoverie struct {
	models.ModelBase
	UserID          uint    `gorm:"not null" json:"user_id" form:"user_id"`                   //用户ID
	ViolationID     uint    `gorm:"not null" json:"violation_id" form:"violation_id"`         //事项ID
	PointsRecovered float32 `gorm:"not null" json:"points_recovered" form:"points_recovered"` //消分的积分值
	Year            uint    `gorm:"not null" json:"year" form:"year"`                         //恢复操作的年度
	EffectiveDate   string  `gorm:"not null" json:"effective_date" form:"effective_date"`     //消分生效日期
	UsageState      uint    `gorm:"not null" json:"usage_state" form:"usage_state"`           //使用状态 0：未使用 1:使用中 2：已使用
	UsagePoint      float32 `gorm:"not null" json:"usage_point" form:"usage_point"`           //已使用分数
}

// 积分事项表
type QualityViolation struct {
	models.ModelBase
	Direction                 uint    `gorm:"not null"  json:"direction"  from:"direction"`                                             //考核方向 1：正向 2：负向
	IssueLevel                uint    `gorm:"not null" json:"issue_level"  from:"issue_level"`                                          //问题等级
	ImpactDegree              uint    `gorm:"not null" json:"impact_degree"  from:"impact_degree"`                                      //影响程度
	CategoryID                uint    `gorm:"not null"  json:"category_id"  form:"category_id"`                                         //关联的大类别ID
	AssessmentItemCode        string  `gorm:"not null;type:varchar(100)"  json:"assessment_item_code" form:"assessment_item_code"`      //考核项编号
	AssessmentItemID          string  `gorm:"not null;type:varchar(100)"  json:"assessment_item_id" form:"assessment_item_id"`          //考核项ID
	AssessmentItemName        string  `gorm:"not null;type:varchar(100)"  json:"assessment_item_name" form:"assessment_item_name"`      //考核项名称
	Description               string  `gorm:"not null" json:"description"  from:"description"`                                          //事项描述
	AuditTime                 string  `gorm:"not null" json:"audit_time" form:"audit_time"`                                             //稽核时间
	ScoreImpact               float32 `gorm:"not null" json:"score_impact" form:"score_impact"`                                         //扣减/增加分数
	ResponsiblePersonID       uint    `gorm:"not null" json:"responsible_person_id" form:"responsible_person_id"`                       //责任角色ID
	AuditManagerID            uint    `gorm:"not null" json:"audit_manager_id" form:"audit_manager_id"`                                 //违规项审计管理人员ID
	UserID                    uint    `gorm:"not null" json:"user_id" form:"user_id"`                                                   //事项提报人ID
	DepartmentManagerID       uint    `gorm:"not null" json:"department_manager_id" form:"department_manager_id"`                       //部门经理ID
	TeamLeaderID              uint    `gorm:"not null" json:"team_leader_id" form:"team_leader_id"`                                     //专业组组长
	Department                string  `gorm:"not null" json:"department" form:"department"`                                             //所在部门
	Team                      string  `gorm:"not null" json:"team" form:"team"`                                                         //所在专业组
	DepartmentID              uint    `gorm:"not null" json:"department_id" form:"department_id"`                                       //所在部门ID
	TeamID                    uint    `gorm:"not null" json:"team_id" form:"team_id"`                                                   //所在专业组ID
	IsRecurrent               uint    `gorm:"not null" json:"is_recurrent" form:"is_recurrent"`                                         //是否复发 (0: 否, 1: 是)
	IssueDescription          string  `gorm:"not null;type:varchar(512)" json:"issue_description" form:"issue_description"`             //抽象共性问题描述
	IsCommonIssue             uint    `gorm:"not null" json:"is_common_issue" form:"is_common_issue"`                                   //共性问题判定(0: 否, 1: 是)
	CommonReviewComments      string  `gorm:"not null;type:varchar(512)" json:"common_review_comments" form:"common_review_comments"`   //共性问题评审意见
	ReoccurrenceType          uint    `gorm:"not null" json:"reoccurrence_type" form:"reoccurrence_type"`                               //复发类型 0: 无复发，1：复发-本人，2：复发-专业组内 3:复发-部门内
	DeductTaskDescription     string  `gorm:"not null;type:varchar(512)" json:"deduct_task_description" form:"deduct_task_description"` //消分任务描述
	DeductCompletionTime      string  `gorm:"not null" json:"deduct_completion_time" form:"deduct_completion_time"`                     //消分任务完成时间
	DeductEvidenceDescription string  `gorm:"not null" json:"deduct_evidence_description" form:"deduct_evidence_description"`           //消分任务佐证说明
	DeductEvidenceMaterial    string  `gorm:"not null" json:"deduct_evidence_material" form:"deduct_evidence_material"`                 //消分任务佐证材料
	DeductionPoints           float32 `gorm:"not null" json:"deduction_points" form:"deduction_points"`                                 //消分分值
	Uuid                      string  `gorm:"not null; type:varchar(60)" json:"uuid" form:"uuid"`
	Status                    uint    `gorm:"not null" json:"status" form:"status"` //流程状态
}

// 部门经理信息表
type QualityDepartment struct {
	models.ModelBase
	Name                string `gorm:"not null;type:varchar(100)"  json:"name"  form:"name"`
	DepartmentID        uint   `gorm:"not null" json:"department_id" form:"department_id"`                 //部门ID
	DepartmentManagerID uint   `gorm:"not null" json:"department_manager_id" form:"department_manager_id"` //部门经理ID
	IsNotified          uint   `gorm:"not null" json:"is_notified" form:"is_notified"`
}

// 小组组长信息表
type QualityGroup struct {
	models.ModelBase
	Name          string `gorm:"not null;type:varchar(100)"  json:"name"  form:"name"`
	GroupID       uint   `gorm:"not null" json:"group_id" form:"group_id"`               //部门小组ID
	GroupLeaderID uint   `gorm:"not null" json:"group_leader_id" form:"group_leader_id"` //部门小组组长ID
}

type QualityViolationProcDef struct {
	models.ModelBase
	Name    string `json:"name,omitempty"`
	Version int    `json:"version,omitempty"`
	// 流程定义json字符串
	Resource      string `gorm:"size:10000" json:"resource,omitempty"`
	ProductModels string `gorm:"type:varchar(500)" json:"product_models"`
}

type QualityViolationProcTask struct {
	models.ModelBase
	// 当前执行流所在的节点
	NodeName   string `json:"nodeName"`
	PrevNodeID string `json:"prevNodeId"`
	NodeID     string `json:"nodeId"`
	// 流程实例id
	ProcInstID                uint    `json:"procInstID"`
	Assignee                  uint    `json:"assignee"`
	Comment                   string  `json:"comment"`
	Status                    uint    `gorm:"not null,default:0"` //0:进行中 1:通过 2：转派 3:回退
	Flag                      bool    `gorm:"not null,default:true"`
	Attachment                string  `gorm:"type:varchar(300)"`
	OverTimeNotice            bool    `gorm:"not null, default: false"`
	Done                      bool    `gorm:"not null, default: false"`
	IssueDescription          string  `gorm:"not null;type:varchar(512)" json:"issue_description" form:"issue_description"`             //抽象共性问题描述
	IsCommonIssue             uint    `gorm:"not null" json:"is_common_issue" form:"is_common_issue"`                                   //共性问题判定(0: 否, 1: 是)
	CommonReviewComments      string  `gorm:"not null;type:varchar(512)" json:"common_review_comments" form:"common_review_comments"`   //共性问题评审意见
	IsRecurrent               uint    `gorm:"not null" json:"is_recurrent" form:"is_recurrent"`                                         //是否复发 (0: 否, 1: 是)
	ReoccurrenceType          uint    `gorm:"not null" json:"reoccurrence_type" form:"reoccurrence_type"`                               //复发类型 0: 无复发，1：复发-本人，2：复发-专业组内 3:复发-部门内
	DeductTaskDescription     string  `gorm:"not null;type:varchar(512)" json:"deduct_task_description" form:"deduct_task_description"` //消分任务描述
	DeductCompletionTime      string  `gorm:"not null" json:"deduct_completion_time" form:"deduct_completion_time"`                     //消分任务完成时间
	DeductEvidenceDescription string  `gorm:"not null" json:"deduct_evidence_description" form:"deduct_evidence_description"`           //消分任务佐证说明
	DeductEvidenceMaterial    string  `gorm:"not null" json:"deduct_evidence_material" form:"deduct_evidence_material"`                 //消分任务佐证材料
	DeductionPoints           float32 `gorm:"not null" json:"deduction_points" form:"deduction_points"`                                 //消分分值
}

type QualityViolationProcInst struct {
	models.ModelBase
	// 流程定义ID
	// ProcDefID int `json:"proc_def_id"`
	// title 标题
	Title string `json:"title"`
	// 当前节点
	NodeID string `json:"node_id"`
	// 审批人
	// Candidate string `json:"candidate"`
	// 当前任务
	TaskID              uint   `json:"task_id"`
	StartUserID         uint   `json:"start_user_id"`
	ResponsiblePersonID uint   `gorm:"not null" json:"responsible_person_id" form:"responsible_person_id"` //责任角色ID
	AuditManagerID      uint   `gorm:"not null" json:"audit_manager_id" form:"audit_manager_id"`           //违规项审计管理人员ID
	QualityViolationID  uint   `json:"quality_violation_id"`
	Status              uint   `gorm:"not null,default:0"` //0:进行中 1:完成 2：失败
	Resource            string `gorm:"size:10000" json:"resource,omitempty"`
}

// 共性问题排查表
type QualityCrossCheck struct {
	models.ModelBase
	Description          string `gorm:"not null" json:"description"  from:"description"`                                        //事项描述
	AuditTime            string `gorm:"not null" json:"audit_time" form:"audit_time"`                                           //稽核时间
	ResponsiblePersonID  uint   `gorm:"not null" json:"responsible_person_id" form:"responsible_person_id"`                     //责任角色ID
	UserID               uint   `gorm:"not null" json:"user_id" form:"user_id"`                                                 //事项提报人ID
	DepartmentManagerID  uint   `gorm:"not null" json:"department_manager_id" form:"department_manager_id"`                     //部门经理ID
	TeamLeaderID         uint   `gorm:"not null" json:"team_leader_id" form:"team_leader_id"`                                   //专业组组长
	Department           string `gorm:"not null" json:"department" form:"department"`                                           //所在部门
	Team                 string `gorm:"not null" json:"team" form:"team"`                                                       //所在专业组
	DepartmentID         uint   `gorm:"not null" json:"department_id" form:"department_id"`                                     //所在部门ID
	TeamID               uint   `gorm:"not null" json:"team_id" form:"team_id"`                                                 //所在专业组ID
	IssueDescription     string `gorm:"not null;type:varchar(512)" json:"issue_description" form:"issue_description"`           //抽象共性问题描述
	CommonReviewComments string `gorm:"not null;type:varchar(512)" json:"common_review_comments" form:"common_review_comments"` //共性问题评审意见
	QualityViolationID   uint   `json:"quality_violation_id"`
	Uuid                 string `gorm:"not null; type:varchar(60)" json:"uuid" form:"uuid"`
	Status               uint   `gorm:"not null" json:"status" form:"status"` //流程状态
}

type QualityCrossCheckProcDef struct {
	models.ModelBase
	Name    string `json:"name,omitempty"`
	Version int    `json:"version,omitempty"`
	// 流程定义json字符串
	Resource      string `gorm:"size:10000" json:"resource,omitempty"`
	ProductModels string `gorm:"type:varchar(500)" json:"product_models"`
}

type QualityCrossCheckProcTask struct {
	models.ModelBase
	// 当前执行流所在的节点
	NodeName   string `json:"nodeName"`
	PrevNodeID string `json:"prevNodeId"`
	NodeID     string `json:"nodeId"`
	// 流程实例id
	ProcInstID           uint   `json:"procInstID"`
	Assignee             uint   `json:"assignee"`
	Comment              string `json:"comment"`
	Status               uint   `gorm:"not null,default:0"` //0:进行中 1:通过 2：转派 3:回退
	Flag                 bool   `gorm:"not null,default:true"`
	Attachment           string `gorm:"type:varchar(300)"`
	OverTimeNotice       bool   `gorm:"not null, default: false"`
	Done                 bool   `gorm:"not null, default: false"`
	IssueDescription     string `gorm:"not null;type:varchar(512)" json:"issue_description" form:"issue_description"`           //抽象共性问题描述
	CommonReviewComments string `gorm:"not null;type:varchar(512)" json:"common_review_comments" form:"common_review_comments"` //共性问题评审意见

}

type QualityCrossCheckProcInst struct {
	models.ModelBase
	Title string `json:"title"`
	// 当前节点
	NodeID              string `json:"node_id"`
	TaskID              uint   `json:"task_id"`
	StartUserID         uint   `json:"start_user_id"`
	ResponsiblePersonID uint   `gorm:"not null" json:"responsible_person_id" form:"responsible_person_id"` //责任角色ID
	QualityCrossCheckID uint   `json:"quality_cross_check_id"`
	Status              uint   `gorm:"not null,default:0"` //0:进行中 1:完成 2：失败
	Resource            string `gorm:"size:10000" json:"resource,omitempty"`
}
