package firewallflextrans

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/firewallflextrans/dfirewallflextrans"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

func CreateTranJob(ctx iris.Context) {
	userID, _ := dao.GetAuthId(ctx)
	request := dfirewallflextrans.Request{}
	if err := ctx.ReadForm(&request); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	jobID := libs.GetUniqueID()
	createdAt := time.Now()

	upload := filepath.Join(libs.Config.Sig.Upload, createdAt.Format("20060102"), jobID)
	os.MkdirAll(upload, 0750)
	os.Chmod(upload, 0750)
	//先执行文件上传，文件上传成功后执行创建申请单操作
	f, fh, err := ctx.FormFile("file")
	defer f.Close()

	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	_, err = ctx.SaveFormFile(fh, filepath.Join(upload, fh.Filename))
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	md5, err := libs.GetFileMd5(filepath.Join(upload, fh.Filename))
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	job := dfirewallflextrans.FirewallFlexTran{}
	if err := job.Create(map[string]interface{}{
		"JobID":          jobID,
		"InputFileName":  fh.Filename,
		"InputMD5":       md5,
		"OutputFileName": request.OutputFileName,
		"OutputMD5":      "",
		"WithLib":        request.WithLib,
		"UserID":         userID,
		"Status":         0,
		"CreatedAt":      time.Now(),
		"UpdatedAt":      time.Now(),
	}); err != nil {
		logging.ErrorLogger.Error(err)
		os.RemoveAll(upload)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	err = job.FindEx("job_id", jobID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if job.ID > 0 {
		go func() {
			md5, err := SigJobWorker(&job)
			if err != nil {
				logging.ErrorLogger.Error(err)
				err = job.Update(job.ID, map[string]interface{}{
					"Status": 2,
				})
				if err != nil {
					logging.ErrorLogger.Error(err)
				}
			} else {
				err = job.Update(job.ID, map[string]interface{}{
					"Status":    1,
					"OutputMD5": md5,
				})
				if err != nil {
					logging.ErrorLogger.Error(err)
				}
			}
		}()
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]interface{}{"job_id": job.JobID, "id": job.ID}, response.NoErr.Msg))
	return
}

func GetTranJobs(ctx iris.Context) {

	uId := uint(0)

	userID := ctx.FormValue("user_id")
	if userID == "" {
		userID = fmt.Sprintf("%v", uId)
	}

	jobID := ctx.FormValue("job_id")
	name := ctx.FormValue("name")
	md5 := ctx.FormValue("md5")

	start := ctx.FormValue("start")
	end := ctx.FormValue("end")
	status := ctx.FormValue("status")

	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dfirewallflextrans.All(userID, jobID, name, md5, start, end, status, sort, orderBy, page, pageSize)
	if err != nil {
		logging.ErrorLogger.Errorf("find sig job get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func SigJobWorker(job *dfirewallflextrans.FirewallFlexTran) (string, error) {
	workDir := job.Dir()
	tmpDir := filepath.Join(workDir, "tmp")

	os.RemoveAll(tmpDir)
	if err := os.MkdirAll(tmpDir, 0755); err != nil {
		return "", err
	}
	os.Chmod(tmpDir, 0755)

	err := unzip(tmpDir, filepath.Join(workDir, job.InputFileName))
	if err != nil {
		return "", err
	}

	command := fmt.Sprintf(
		`cd "%s" && while true; do rm "%s" -rf && make TARGET_NAME="%s" INPUT_DIR="%s" OUTPUT_DIR="%s" ARG_WITH_LIB="%s" && file -i "%s"|grep -i "application/octet-stream"; if [ $? -eq 0 ];then break;fi;done`,
		libs.Config.Sig.Sigpath,
		filepath.Join(workDir, job.OutputFileName),
		job.OutputFileName,
		tmpDir,
		workDir,
		job.WithLib,
		filepath.Join(workDir, job.OutputFileName),
	)

	f, err := os.OpenFile(filepath.Join(workDir, job.JobID+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	defer f.Close()
	stdout, err := libs.ExecCommand(command)
	if err != nil {
		f.WriteString(stdout)
		f.WriteString(err.Error() + "\n")
		return "", err
	}
	f.WriteString(stdout + "\n")
	md5, err := libs.GetFileMd5(filepath.Join(workDir, job.OutputFileName))
	if err != nil {
		return "", err
	}

	return md5, nil
}

func unzip(dest, fp string) error {

	check := checkType(fp)
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(30)*time.Second)
	defer cancel()

	var stderr bytes.Buffer
	switch check {
	case "zip":
		f, err := exec.LookPath("unzip")
		if err != nil {
			return errors.New("没有找到unzip命令")
		}
		cmd := exec.CommandContext(ctx, f, fp)
		cmd.Dir = dest
		cmd.Stderr = &stderr

		err = cmd.Run()
		if err != nil {
			return errors.New("解压失败")
		}

	case "tar":
		f, err := exec.LookPath("tar")
		if err != nil {
			return errors.New("没有找到tar命令")
		}
		cmd := exec.CommandContext(ctx, f, "-xf", fp)
		cmd.Dir = dest
		cmd.Stderr = &stderr
		err = cmd.Run()
		if err != nil {
			return errors.New("解压失败")
		}
	default:
		return errors.New("不支持压缩类型")
	}

	return nil
}

func checkType(fp string) string {
	fileInfo, err1 := fileCheck(fp)
	fileMime, err2 := fileCheckMime(fp)
	if err1 != nil && err2 != nil {
		logging.ErrorLogger.Errorf("检查类型失败", err1, err2)
	}
	// 检测是否压缩格式，进行循环解压
	switch {
	case strings.Contains(fileInfo, "gzip compressed data") && strings.Contains(fileMime, "application/gzip"):
		return "gzip"
	case strings.Contains(fileInfo, "gzip compressed data") && strings.Contains(fileMime, "application/x-gzip"):
		return "gzip"
	case strings.Contains(fileInfo, "Zip archive data") && strings.Contains(fileMime, "application/zip"):
		return "zip"
	case strings.Contains(fileInfo, "tar archive") && strings.Contains(fileMime, "application/x-tar"):
		return "tar"
	case strings.Contains(fileInfo, "current ar archive") && strings.Contains(fileMime, "application/x-archive"):
		return "ar"
	case strings.Contains(fileInfo, "RPM") && strings.Contains(fileMime, "application/x-rpm"):
		return "rpm"
	case strings.Contains(fileInfo, "Debian binary package") && strings.Contains(fileMime, "application/vnd.debian.binary-package"):
		return "deb"
		// case strings.Contains(fileInfo, "data") && strings.Contains(fileMime, "application/octet-stream"):
		// 	return "bin"
	}

	return ""
}

func fileCheck(fp string) (string, error) {
	f, err := exec.LookPath("file")
	if err != nil {
		return "没有找到file命令", errors.New("没有找到file命令")
	}
	cmd := exec.Command(f, fp)
	out, _ := cmd.CombinedOutput()
	return string(out), nil
}

func fileCheckMime(fp string) (string, error) {

	f, err := exec.LookPath("file")
	if err != nil {
		return "没有找到file命令", errors.New("没有找到file命令")
	}
	cmd := exec.Command(f, "-i", fp)
	out, _ := cmd.CombinedOutput()
	return string(out), nil
}

func GetTranJob(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	jobID := ctx.FormValue("job_id")
	job := dfirewallflextrans.FirewallFlexTran{}
	if len(jobID) > 0 {
		err := job.FindEx("job_id", jobID)
		if err != nil {
			logging.ErrorLogger.Errorf("find sig job get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	} else {
		err := job.Find(id)
		if err != nil {
			logging.ErrorLogger.Errorf("find sig job get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, job, response.NoErr.Msg))
	return
}

func DownloadTranFile(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	jobID := ctx.FormValue("job_id")
	job := dfirewallflextrans.FirewallFlexTran{}
	if len(jobID) > 0 {
		err := job.FindEx("job_id", jobID)
		if err != nil {
			logging.ErrorLogger.Errorf("find sig job get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	} else {
		err := job.Find(id)
		if err != nil {
			logging.ErrorLogger.Errorf("find sig job get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	}
	ctx.SendFile(filepath.Join(job.Dir(), job.OutputFileName), job.OutputFileName)
	return
}

func DownloadTranLog(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	jobID := ctx.FormValue("job_id")
	job := dfirewallflextrans.FirewallFlexTran{}
	if len(jobID) > 0 {
		err := job.FindEx("job_id", jobID)
		if err != nil {
			logging.ErrorLogger.Errorf("find sig job get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	} else {
		err := job.Find(id)
		if err != nil {
			logging.ErrorLogger.Errorf("find sig job get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	}
	data, err := os.ReadFile(filepath.Join(job.Dir(), job.JobID+".log"))
	if err != nil {
		logging.ErrorLogger.Errorf("find sig job get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.ContentType("text/plain")
	ctx.WriteString(string(data))
	return
}

func FileTest() {
	fortinetConfigPath := "/tmp/fortinet_config.conf"
	ruijieTemplatePath := "/tmp/Z8620-startup-R10.xml"
	outputPath := "/tmp/updated_ruijie_template.xml"

	shell := filepath.Join(libs.Config.FeiShuDoc.Plugin, "tranconfig.py")
	command := fmt.Sprintf("python3 %s %s %s %s", shell, fortinetConfigPath, ruijieTemplatePath, outputPath)
	output, err := libs.ExecCommand(command)
	if err != nil {
		fmt.Println(string(output))
	}
	fmt.Println(string(output))

}
