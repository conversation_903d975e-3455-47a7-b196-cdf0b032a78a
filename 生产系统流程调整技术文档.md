# 生产系统流程调整技术文档

## 1. 文档概述

### 1.1 文档范围

本文档详细说明了生产发布流程（Production Release Process）的调整和优化，重点关注新增PM（项目经理/产品经理）提交节点的技术实现和流程变更。

### 1.2 调整背景

原先的下生产流程是从"创建下生产"直接到"PGTTL提交系统模板"，为了更好地管理程序信息和提高流程的可控性，现在需要在中间添加一个"PM提交程序"节点，用于处理程序信息的提交和验证。

## 2. 核心变更内容

### 2.1 PM提交节点详细说明

#### 2.1.1 节点基本信息

- **节点名称**: PM提交程序
- **节点ID**: `pm_submit`
- **前置节点**: `start`（创建下生产）
- **后续节点**: `system_template`（PGTTL提交系统模板）
- **责任人**: 项目经理/产品经理（assignee: 100）

#### 2.1.2 节点在流程中的位置

```text
创建下生产(start) → PM提交程序(pm_submit) → PGTTL提交系统模板(system_template) → ...
```

#### 2.1.3 PM节点需要提交的具体材料和信息

**必填信息：**

1. **主程序URL** (`MainProgramUrl`)
   - 主程序文件的下载链接
   - 系统会自动提取文件名和计算MD5值
   - 支持从编译系统的输出链接获取

2. **OSPKG安装包URL** (`OspkgInstallUrl`)
   - OSPKG安装包的下载链接
   - 如果未提供，系统会尝试从主程序URL自动推导
   - 自动验证文件完整性

**自动处理信息：**

1. **软件版本号** (`SoftwareNumber`, `SoftwareVersion`)
   - 从编译记录中自动获取
   - 根据发布类型自动处理版本格式

2. **文件验证信息**
   - 文件MD5值自动计算
   - 文件大小自动获取
   - 重复发布检查（预鉴版本）

#### 2.1.4 审批流程和责任人

1. **提交阶段**: PM负责提交程序信息
2. **验证阶段**: 系统自动验证文件完整性和重复性
3. **审批阶段**: 自动流转到下一节点（PGTTL提交系统模板）

#### 2.1.5 与现有流程节点的衔接关系

- **上游节点**: 从"创建下生产"节点接收基本发布信息
- **下游节点**: 向"PGTTL提交系统模板"节点传递完整的程序信息
- **数据传递**: 程序信息会更新到production表中，供后续节点使用

## 3. 技术实现详情

### 3.1 数据库层面调整

#### 3.1.1 流程定义更新

```sql
-- 更新流程定义，添加 PM 节点
UPDATE production_proc_defs 
SET resource = '[
  {"name":"创建下生产","nodeId":"start","prevNodeId":"","assignee":0},
  {"name":"PM提交程序","nodeId":"pm_submit","prevNodeId":"start","assignee":100},
  {"name":"PGTTL提交系统模板","nodeId":"system_template","prevNodeId":"pm_submit","assignee":100},
  ...
]'
WHERE name = '下生产流程';
```

#### 3.1.2 数据表结构

主要涉及以下数据表：

- `production_proc_defs`: 流程定义表
- `production_proc_insts`: 流程实例表  
- `production_proc_tasks`: 任务表
- `productions`: 生产发布信息表

### 3.2 代码层面实现

#### 3.2.1 PM节点处理函数

```go
func handlePMSubmitNode(ctx iris.Context, procInst *dproductionprocinst.Response, request *TaskRequest) (map[string]interface{}, error)
```

**主要功能：**

1. 验证主程序URL的有效性
2. 自动推导OSPKG安装包URL
3. 下载文件并计算MD5值
4. 获取软件版本信息
5. 检查重复发布记录
6. 返回处理结果供数据库更新

#### 3.2.2 流程控制逻辑

在`UpdateProcInst`函数中添加PM节点处理：

```go
// 处理PM节点（程序提交）
if request.NodeID == "pm_submit" && request.Status == 1 {
    pmNodeFeatures, err := handlePMSubmitNode(ctx, &procInst, request)
    if err != nil {
        handleCustomError(ctx, err.Error())
        return
    }
    // 将PM节点处理的结果合并到featureObject
    for key, value := range pmNodeFeatures {
        featureObject[key] = value
    }
}
```

#### 3.2.3 数据传递机制

PM节点处理完成后，会将以下信息更新到production表：

- `MainProgramUrl`: 主程序下载链接
- `MainProgramFileName`: 主程序文件名
- `MainProgramFileSize`: 主程序文件大小
- `MainProgramFileMd5`: 主程序MD5值
- `OspkgInstallUrl`: OSPKG安装包链接
- `OspkgInstallName`: OSPKG安装包文件名
- `OspkgInstallSize`: OSPKG安装包大小
- `OspkgInstallMd5`: OSPKG安装包MD5值
- `SoftwareNumber`: 软件编号
- `SoftwareVersion`: 软件版本

### 3.3 打回处理机制

当流程被打回到PM节点时，系统会自动清除相关程序信息：

```go
// 处理打回到pm_submit节点的情况
if taskObject["NextNodeID"].(string) == "pm_submit" {
    // 清除相关数据，避免下次提交时检查到相同文件发布记录
    updateFields := map[string]interface{}{
        "MainProgramUrl":      "",
        "MainProgramFileName": "",
        "MainProgramFileSize": 0,
        "MainProgramFileMd5":  "",
        // ... 其他字段
    }
}
```

## 4. 流程图

### 4.1 调整前流程图

```mermaid
graph TD
    A[创建下生产] --> B[PGTTL提交系统模板]
    B --> C[平台负责人]
    B --> D[生测负责人]
    B --> E[CPLD在线升级]
    C --> F[PGTTL审核文档]
    D --> F
    E --> F
    F --> G[硬件代表审核文档]
    F --> H[测试代表审核文档]
    G --> I[硬件验收]
    H --> J[测试验收]
    I --> K[QA审核]
    J --> K
```

### 4.2 调整后流程图

```mermaid
graph TD
    A[创建下生产] --> PM[PM提交程序]
    PM --> B[PGTTL提交系统模板]
    B --> C[平台负责人]
    B --> D[生测负责人]
    B --> E[CPLD在线升级]
    C --> F[PGTTL审核文档]
    D --> F
    E --> F
    F --> G[硬件代表审核文档]
    F --> H[测试代表审核文档]
    G --> I[硬件验收]
    H --> J[测试验收]
    I --> K[QA审核]
    J --> K
    
    style PM fill:#e1f5fe
```

## 5. 角色职责

### 5.1 PM（项目经理/产品经理）

**职责：**

- 提交主程序下载链接
- 确认程序版本信息
- 验证程序文件完整性

**操作步骤：**

1. 登录生产发布系统
2. 进入待处理的PM提交程序任务
3. 填写主程序URL（必填）
4. 可选填写OSPKG安装包URL
5. 提交审批

### 5.2 系统管理员

**职责：**

- 维护流程定义
- 监控系统运行状态
- 处理异常情况

### 5.3 PGTTL（后续节点责任人）

**职责：**

- 基于PM提交的程序信息进行系统模板提交
- 验证程序信息的准确性

## 6. 检查清单

### 6.1 PM节点提交前检查

- [ ] 主程序URL是否有效
- [ ] 程序文件是否可正常下载
- [ ] 软件版本信息是否正确
- [ ] 是否存在重复发布记录

### 6.2 系统自动检查

- [ ] 文件MD5值计算
- [ ] 文件大小验证
- [ ] 编译记录关联
- [ ] 版本格式处理

### 6.3 流程流转检查

- [ ] 节点状态更新
- [ ] 数据传递完整性
- [ ] 后续节点可访问性

## 7. 影响分析

### 7.1 对现有生产发布流程的影响

#### 7.1.1 正面影响

1. **流程标准化**: PM节点的引入使程序信息提交更加规范化
2. **责任明确**: 明确了PM在程序信息管理中的职责
3. **质量提升**: 增加了程序文件的自动验证机制
4. **可追溯性**: 完善了程序信息的记录和追踪
5. **错误预防**: 提前发现和处理程序文件问题

#### 7.1.2 潜在风险

1. **流程延长**: 增加了一个审批节点，可能延长整体流程时间
2. **学习成本**: PM需要熟悉新的操作流程
3. **系统依赖**: 增加了对编译系统和文件服务器的依赖

#### 7.1.3 兼容性影响

- **已有流程实例**: 不受影响，继续按原流程执行
- **新流程实例**: 自动使用新的流程定义
- **历史数据**: 完全兼容，不影响历史记录查询

### 7.2 改进效果

#### 7.2.1 流程效率提升

- 程序信息验证前置，减少后续节点的返工
- 自动化处理减少人工错误
- 标准化操作提高处理速度

#### 7.2.2 质量保障增强

- 文件完整性自动验证
- 重复发布自动检测
- 版本信息自动关联

#### 7.2.3 管理可视化改进

- 流程节点更加细化
- 责任分工更加明确
- 操作记录更加完整

## 8. 部署和实施

### 8.1 部署步骤

#### 8.1.1 数据库更新

```bash
# 1. 备份数据库
mysqldump -u [用户名] -p [数据库名] > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 执行流程定义更新
mysql -u [用户名] -p[密码] [数据库名] < update_workflow.sql

# 3. 验证更新结果
mysql -u [用户名] -p[密码] [数据库名] -e "SELECT resource FROM production_proc_defs WHERE name = '下生产流程';"
```

#### 8.1.2 代码部署

1. 确认`handlePMSubmitNode`函数已实现
2. 确认`UpdateProcInst`函数已添加PM节点处理逻辑
3. 确认事务处理函数已支持PM节点数据更新
4. 部署更新后的代码到生产环境

#### 8.1.3 配置验证

- 验证文件存储路径配置
- 验证编译系统连接配置
- 验证邮件通知配置

### 8.2 测试验证

#### 8.2.1 功能测试

1. **PM节点基本功能**
   - 创建新的下生产流程实例
   - 验证PM节点是否正确出现在流程中
   - 测试程序URL提交功能

2. **文件处理功能**
   - 测试主程序文件下载
   - 测试MD5计算准确性
   - 测试OSPKG自动推导功能

3. **数据传递功能**
   - 验证程序信息是否正确保存到数据库
   - 验证后续节点是否能正确获取程序信息

#### 8.2.2 异常测试

1. **错误处理**
   - 测试无效URL处理
   - 测试文件下载失败处理
   - 测试重复发布检测

2. **打回流程**
   - 测试从后续节点打回到PM节点
   - 验证数据清除功能
   - 测试重新提交功能

### 8.3 回滚方案

如果发现问题需要回滚，执行以下步骤：

```bash
# 1. 执行回滚脚本
mysql -u [用户名] -p[密码] [数据库名] < rollback_workflow.sql

# 2. 部署原版本代码

# 3. 验证回滚结果
mysql -u [用户名] -p[密码] [数据库名] -e "SELECT resource FROM production_proc_defs WHERE name = '下生产流程';"
```

## 9. 监控和维护

### 9.1 关键指标监控

- PM节点处理时间
- 文件下载成功率
- 程序信息验证通过率
- 流程整体耗时变化

### 9.2 日志监控

- PM节点处理日志
- 文件下载错误日志
- 数据库更新日志
- 系统异常日志

### 9.3 定期维护

- 清理临时文件
- 检查存储空间使用情况
- 更新编译系统连接配置
- 优化数据库查询性能

## 10. 用户培训

### 10.1 PM用户培训内容

1. **新流程介绍**
   - PM节点的作用和意义
   - 操作界面介绍
   - 提交流程说明

2. **操作指南**
   - 如何获取程序URL
   - 如何填写提交表单
   - 如何处理错误提示

3. **注意事项**
   - URL格式要求
   - 文件大小限制
   - 重复提交处理

### 10.2 系统管理员培训

1. **技术架构理解**
2. **故障排查方法**
3. **性能优化技巧**
4. **数据备份恢复**

## 11. 总结

### 11.1 技术变更总结

本次流程调整通过在"创建下生产"和"PGTTL提交系统模板"之间添加"PM提交程序"节点，实现了：

- 程序信息管理的标准化
- 文件验证的自动化
- 流程责任的明确化
- 质量控制的前置化

### 11.2 预期收益

- 提高生产发布质量
- 减少后续节点返工
- 增强流程可控性
- 完善审计追踪

### 11.3 后续优化方向

- 增加更多自动化验证规则
- 优化文件处理性能
- 完善错误提示信息
- 增加流程分析报表

---

**文档版本**: v1.0
**编写日期**: 2025-01-25
**最后更新**: 2025-01-25
**审核状态**: 待审核
