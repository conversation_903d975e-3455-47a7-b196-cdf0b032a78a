package datasync

import "gorm.io/gorm"

/*
CREATE TABLE `csbu_pms_project_scale` (

	`id` VARCHAR(200) NOT NULL DEFAULT '' COLLATE 'utf8_general_ci',
	`pmsSrcProjectProductName` VARCHAR(200) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`pmsSrcProjectId` INT(11) NULL DEFAULT NULL,
	`pmsDestProjectId` INT(11) NULL DEFAULT NULL,
	`pmsSrcProjectName` VARCHAR(200) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`pmsDestProjectProductName` VARCHAR(200) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`pmsDestProjectName` VARCHAR(200) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`scale` FLOAT NULL DEFAULT NULL,
	INDEX `idx_id` (`id`) USING BTREE

)
COLLATE='utf8_general_ci'
ENGINE=InnoDB
;
*/
type CsbuNtosGcov struct {
	ID                 string `gorm:"column:id" json:"id"`
	Gcov               string `gorm:"column:gcov" json:"gcov"`
	Line               int    `gorm:"column:line" json:"line"`
	Request            string `gorm:"column:request" json:"request"`
	Workpackage        string `gorm:"column:workpackage" json:"workpackage"`
	WorkpackageManager int    `gorm:"column:workpackage_manager" json:"workpackage_manager"`
	Auto               string `gorm:"column:auto" json:"auto"`
}

func GcovBatchCreate(branch string, data []map[string]interface{}) error {
	db := DataEaseDB.db

	err := db.Transaction(func(tx *gorm.DB) error {
		err := tx.Unscoped().Where("branch = ?", branch).Delete(&CsbuNtosGcov{}).Error
		if err != nil {
			return err
		}
		const batchSize = 500
		for i := 0; i < len(data); i += batchSize {
			endIndex := i + batchSize
			if endIndex > len(data) {
				endIndex = len(data)
			}
			batchData := data[i:endIndex]
			if len(batchData) > 0 {
				err = tx.Model((&CsbuNtosGcov{})).Create(data).Error
				if err != nil {
					return err
				}
			}
		}

		return nil
	})
	return err
}
