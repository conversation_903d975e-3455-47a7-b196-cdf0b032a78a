// +build test

package libs

import (
	"reflect"
	"testing"
)

func TestStructToMap(t *testing.T) {
	type Js struct {
		Name  string
		Title string
	}

	js := Js{"name", "title"}
	want := map[string]interface{}{
		"name":  "name",
		"title": "title",
	}

	t.<PERSON>("TestStructToMap", func(t *testing.T) {
		if got := StructToMap(js); !reflect.DeepEqual(got, want) {
			t.<PERSON>("StructToMap() = %v, want %v", got, want)
		}
	})
}

func TestStructToString(t *testing.T) {
	type Js struct {
		Name  string
		Title string
	}

	js := Js{"name", "title"}
	want := `{"Name":"name","Title":"title"}`

	t.<PERSON>("TestStructToString", func(t *testing.T) {
		if got := StructToString(js); got != want {
			t.<PERSON><PERSON>rf("StructToString() = %v, want %v", got, want)
		}
	})

}
