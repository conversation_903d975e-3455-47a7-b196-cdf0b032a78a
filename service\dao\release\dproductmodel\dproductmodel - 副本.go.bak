package dproductmodel

import (
	"fmt"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/release"
)

const ModelName = "产品型号表"

type ReleaseBuildName struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
}

type ReleaseProductModel struct {
	ID                 uint             `json:"id"`
	ReleaseBuildNameID uint             `json:"release_build_name_id"`
	ReleaseBuildName   ReleaseBuildName `gorm:"->; foreignKey:ReleaseBuildNameID" json:"release_build_name"`
	Name               string           `json:"name"`
}

type ListResponse struct {
	ReleaseProductModel
}

type Request struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
}

func (a *ReleaseProductModel) ModelName() string {
	return ModelName
}

func Model() *release.ReleaseProductModel {
	return &release.ReleaseProductModel{}
}

func (a *ReleaseProductModel) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("ReleaseBuildName")
	if len(name) > 0 {
		db = db.Where("release_build_name_id = ?", name)
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *ReleaseProductModel) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("ReleaseBuildName")
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *ReleaseProductModel) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *ReleaseProductModel) CreateV2(object interface{}) error {
	return nil
}

func (a *ReleaseProductModel) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *ReleaseProductModel) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("ReleaseBuildName").Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *ReleaseProductModel) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("ReleaseBuildName").Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *ReleaseProductModel) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *ReleaseProductModel) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("ReleaseBuildName").Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *ReleaseProductModel) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("ReleaseBuildName").Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindAll() ([]*ReleaseProductModel, error) {
	var items []*ReleaseProductModel

	if err := easygorm.GetEasyGormDb().Model(Model()).Preload("ReleaseBuildName").Find(&items).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return items, err
	}
	return items, nil
}

func FindInIds(ids []uint) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("ReleaseBuildName").Where("id in ?", ids).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	return items, nil
}

func GetOrCreate(buildNameId uint, productModelId string) (uint, error) {
	var item release.ReleaseProductModel
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("ReleaseBuildName").Where("id = ? or name = ?", productModelId, productModelId).Find(&item).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return 0, err
	}
	if item.ID == 0 {
		err = libs.CheckProductModel(productModelId)
		if err != nil {
			return 0, err
		}
		item = release.ReleaseProductModel{Name: productModelId, ReleaseBuildNameID: buildNameId}
		err = easygorm.GetEasyGormDb().Model(Model()).Create(&item).Error
		if err != nil {
			logging.ErrorLogger.Errorf("find approval err ", err)
			return 0, err
		}
	}

	return item.ID, nil
}

func (a *ReleaseProductModel) FindByName(name string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("ReleaseBuildName").Where("name like ?", fmt.Sprintf("%%%v", name)).Find(a).Error
	return err
}
