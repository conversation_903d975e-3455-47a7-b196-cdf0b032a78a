package datasync

import "time"

type PmsProjectTestStage struct {
	// models.ModelBase
	ID            int        `gorm:"primarykey; autoIncrement:false" json:"id" `
	ProjectID     int        `gorm:"not null" json:"project_id" update:"1"`
	ProjectName   string     `gorm:"not null; type:varchar(100)" json:"project_name" update:"1"`
	StageName     string     `gorm:"not null; type:varchar(100)" json:"stage_name" update:"1"`
	PlanStartTime *time.Time `json:"plan_start_time" update:"1"`
	PlanEndTime   *time.Time `json:"plan_end_time" update:"1"`
	ActStartTime  *time.Time `json:"act_start_time" update:"1"`
	ActEndTime    *time.Time `json:"act_end_time" udpate:"1"`
}
