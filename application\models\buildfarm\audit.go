package buildfarm

import "irisAdminApi/application/models"

type BuildfarmAudit struct {
	models.ModelBase
	JobID  string `gorm:"not null; type:varchar(60)" json:"job_id"`
	TaskID string `gorm:"not null; type:varchar(60)" json:"task_id"`

	UserID        uint   `gorm:"not null" json:"user_id"`
	AuditorID     uint   `gorm:"not null" json:"auditor_id"`
	Level         uint   `gorm:"not null" json:"level"`
	Status        uint   `gorm:"not null" json:"status"`
	Comment       string `gorm:"not null" json:"comment"`
	LastAuditorID uint   `gorm:"not null" json:"last_auditor_id"`
}

type BuildfarmAuditHistory struct {
	models.ModelBase
	JobID     string `gorm:"not null; type:varchar(60)" json:"job_id"`
	TaskID    string `gorm:"not null; type:varchar(60)" json:"task_id"`
	Status    uint   `gorm:"not null" json:"status"`
	AuditorID uint   `gorm:"not null" json:"auditor_id"`
	Comment   string `gorm:"not null" json:"comment"`
}

type BuildfarmAuditDetail struct {
	models.ModelBase
	JobID  string `gorm:"not null; type:varchar(60)" json:"job_id"`
	TaskID string `gorm:"not null; type:varchar(60)" json:"task_id"`

	CurrentRepo         string `gorm:"not null; type:varchar(200)" json:"current_repo"`
	CurrentBranch       string `gorm:"not null; type:varchar(100)" json:"current_branch"`
	CurrentLastCommitID string `gorm:"not null; type:varchar(100)" json:"current_last_commit_id"`
	CommitIDs           string `gorm:"not null" json:"commit_ids"`

	UpstreamRepo         string `gorm:"not null; type:varchar(200)" json:"upstream_repo"`
	UpstreamBranch       string `gorm:"not null; type:varchar(100)" json:"upstream_branch"`
	UpstreamLastCommitID string `gorm:"not null; type:varchar(100)" json:"upstream_last_commit_id"`

	IsProduct  bool   `gorm:"not null; default:false" json:"is_product"`
	IsPersonal bool   `gorm:"not null; default:false" json:"is_personal"`
	Diff       string `gorm:"not null; type:varchar(200)" json:"diff"`
	UserID     uint   `gorm:"not null" json:"user_id"`
	AuditorID  uint   `gorm:"not null" json:"auditor_id"`
	Level      uint   `gorm:"not null" json:"level"`
	Status     uint   `gorm:"not null" json:"status"`
}

type BuildfarmAuditBlackList struct {
	models.ModelBase
	Text string `gorm:"not null; type:varchar(500)" json:"text"`
	Type uint   `gorm:"not null" json:"type"` // 1: string   2: regex
}

type BuildfarmDiffLevelRule struct {
	models.ModelBase
	Level uint   `gorm:"not null" json:"level"`
	Text  string `gorm:"not null; type:varchar(500)" json:"text"`
}
