package coverityresult

import "github.com/imroc/req/v3"

var covResultClient = req.C()

type CovResponse struct {
	ID           string   `json:"id"`
	ComponentMap string   `json:"componentMap"`
	Checkers     []string `json:"checkers"`
	Defects      []Defect `json:"defects"`
}

type Defect struct {
	CID           uint         `json:"cid"`
	Mergekey      string       `json:"mergekey"`
	Status        string       `json:"status"`
	ComponentName string       `json:"componentName"`
	CheckerName   string       `json:"checkerName"`
	Occurrences   []Occurrence `json:"occurrences"`
}

type Occurrence struct {
	EventDescription string  `json:"eventDescription"`
	EventKind        string  `json:"eventKind"`
	EventNumber      uint    `json:"eventNumber"`
	EventSet         uint    `json:"eventSet"`
	EventTag         string  `json:"eventTag"`
	FiledID          FiledID `json:"fileId"`
	LineNumber       uint    `json:"lineNumber"`
	Main             bool    `json:"main"`
	Polarity         bool    `json:"polarity"`
}

type FiledID struct {
	ContentsMD5  string `json:"contentsMD5"`
	FilePathName string `json:"filePathname"`
}

type EpgRule struct {
	Rule   string     `json:"rule"`
	Levels []EpgLevel `json:"levels"`
}

type EpgLevel struct {
	EpgLevel string `json:"epgLevel"`
}
