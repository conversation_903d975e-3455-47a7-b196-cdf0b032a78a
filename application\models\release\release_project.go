package release

import (
	"irisAdminApi/application/models"
	"time"
)

type ReleaseProject struct {
	models.ModelBase
	Name                 string `gorm:"uniqueIndex; not null; type:varchar(60)"`
	StatusID             uint   `gorm:"not null"`
	TypeID               uint   `gorm:"not null"`
	AttrID               uint   `gorm:"not null"`
	ClassID              uint   `gorm:"not null"`
	NewSupport           bool   `gorm:"not null; default:false"`
	NewProducts          string `gorm:"type:varchar(200)"`
	BugSync              bool   `gorm:"not null; default:false"`
	IsIpd                bool   `gorm:"not null; default:false"`
	BaseGitBranch        string `gorm:"type:varchar(200)"`
	BaseGitCommit        string `gorm:"type:varchar(200)"`
	BaseReleaseProjectID uint   `gorm:"not null"`
	SoftVersion          string `gorm:"not null; uniqueIndex; type:varchar(200)"`
	PiVersion            string `gorm:"type:varchar(200)"`
	PiProjectID          string `gorm:"type:varchar(200)"`
	DirectionID          uint   `gorm:"not null"`
	Customers            string `gorm:"type:varchar(200)"`
	Bugid                string `gorm:"type:varchar(60)"`
	DemandComment        string `gorm:"not null"`
	ManpowerCost         uint   `gorm:"not null"`
	PrototypeCost        uint   `gorm:"not null"`
	CapoID               uint   `gorm:"not null"`
	PmoID                uint   `gorm:"not null"`
	PmID                 uint   `gorm:"not null"`
	PtmID                uint   `gorm:"not null"`
	CmaID                uint   `gorm:"not null"`
	PqaID                uint   `gorm:"not null"`
	PgttlID              uint   `gorm:"not null"`
	PiID                 uint
	StartedAt            string `gorm:"not null;type:varchar(60)"`
	Comment              string
	Sow                  string `gorm:"type:varchar(200)"`
	UserID               uint   `gorm:"not null" json:"user_id"`
	MrControlStatus      uint   `gorm:"not null"`
	MrControlShowStatus  uint   `gorm:"not null"`
}

type ReleaseStatus struct {
	models.ModelBase
	Name string `gorm:"not null; type:varchar(60)"`
}

type ReleaseType struct {
	models.ModelBase
	Name string `gorm:"not null; type:varchar(60)"`
}

type ReleaseTypeAttr struct {
	models.ModelBase
	ReleaseTypeID uint `gorm:"primaryKey"`
	ReleaseAttrID uint `gorm:"primaryKey"`
}

type ReleaseAttr struct {
	models.ModelBase
	Name string `gorm:"not null; type:varchar(60)"`
}

type ReleaseClass struct {
	models.ModelBase
	Name string `gorm:"not null; type:varchar(60)"`
}

type ReleaseDirection struct {
	models.ModelBase
	Name string `gorm:"not null; type:varchar(60)"`
}

type ReleaseDemandSrc struct {
	models.ModelBase
	Name string `gorm:"not null; type:varchar(60)"`
}

type ReleaseProjectDemandSrc struct {
	models.ModelBase
	ReleaseProjectID   uint `gorm:"primaryKey"`
	ReleaseDemandSrcID uint `gorm:"primaryKey"`
}

type ReleaseProjectNotice struct {
	models.ModelBase
	ReleaseProjectID uint `gorm:"primaryKey"`
	UserID           uint `gorm:"primaryKey"`
}

type ReleaseProjectWrite struct {
	models.ModelBase
	ReleaseProjectID uint `gorm:"primaryKey"`
	UserID           uint `gorm:"primaryKey"`
}

type ReleaseAttachment struct {
	models.ModelBase
	ReleaseProjectID uint   `gorm:"primaryKey"`
	OriginName       string `gorm:"not null; type:varchar(60)"`
	Name             string `gorm:"index;not null; type:varchar(300)"`
	Type             uint   `gorm:"not null"` // 1: sow
}

type ReleaseProjectConfig struct {
	models.ModelBase
	ReleaseProjectID    uint    `gorm:"not null; unique" json:"release_project_id"`
	EnableNotice        bool    `gorm:"not null;default:false" json:"enable_notice"`
	EnableGcov          bool    `gorm:"not null;default:false" json:"enable_gcov"`
	BuildProjectID      *uint   `gorm:"uniqueIndex: idx_project_branch_unique" json:"build_project_id"`
	BuildProjectBranch  *string `gorm:"uniqueIndex: idx_project_branch_unique; type:varchar(60)" json:"build_project_branch"`
	FsKey               string  `gorm:"not null; type:varchar(60)" json:"fs_key"`
	UserID              uint    `gorm:"not null" json:"user_id"`
	Receivers           string  `gorm:"type:varchar(500)" json:"receivers"`
	BugMirror           bool    `gorm:"not null; default:false" json:"bug_mirror"`
	IntegrateStartAt    string  `gorm:"not null;type:varchar(60)" json:"integrate_start_at"`
	TestStartAt         string  `gorm:"not null;type:varchar(60)" json:"test_start_at"`
	FirstBugFixStartAt  string  `gorm:"not null;type:varchar(60)" json:"first_bug_fix_start_at"`
	SecondTestStartAt   string  `gorm:"not null;type:varchar(60)" json:"second_test_start_at"`
	SecondBugFixStartAt string  `gorm:"not null;type:varchar(60)" json:"second_bug_fix_start_at"`
	RegressTestStartAt  string  `gorm:"not null;type:varchar(60)" json:"regress_test_start_at"`
	ExperimentReleaseAt string  `gorm:"not null;type:varchar(60)" json:"experiment_release_at"`
	SendTestLinkRule    string  `json:"send_test_link_rule"`
	TestLinkRoles       string  `gorm:"type:varchar(500)" json:"test_link_roles"`
	TestLinkReceivers   string  `gorm:"type:varchar(500)" json:"test_link_receivers"`
	BugRoles            string  `gorm:"type:varchar(500)" json:"bug_roles"`
	BugReceivers        string  `gorm:"type:varchar(500)" json:"bug_receivers"`
}

type ReleaseTrunkLog struct {
	models.ModelBase
	ReleaseProjectID uint      `gorm:"not null" json:"release_project_id"`
	StartedAt        time.Time `json:"started_at"`
	EndedAt          time.Time `json:"ended_at"`
}
