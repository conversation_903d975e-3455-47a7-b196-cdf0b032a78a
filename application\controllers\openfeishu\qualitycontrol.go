package openfeishu

import (
	"fmt"
	"irisAdminApi/application/logging"
	"time"
)

// 品控系统数据同步主入口
type QualityControlSync struct {
	processor *QualityControlProcessor
	enabled   bool
}

// 创建新的品控同步实例
func NewQualityControlSync() *QualityControlSync {
	return &QualityControlSync{
		processor: NewQualityControlProcessor(nil), // 使用默认配置
		enabled:   true,
	}
}

// 执行品控数据同步
func (qcs *QualityControlSync) ExecuteSync() (*SyncStatistics, error) {
	if !qcs.enabled {
		return nil, fmt.Errorf("品控数据同步功能已禁用")
	}

	logging.InfoLogger.Info("=== 开始品控系统数据同步 ===")

	// 执行系统诊断
	err := qcs.diagnoseSystem()
	if err != nil {
		logging.ErrorLogger.Errorf("系统诊断失败: %v", err)
		return nil, fmt.Errorf("系统诊断失败: %v", err)
	}

	// 执行数据同步
	statistics, err := qcs.processor.ExecuteSync()
	if err != nil {
		logging.ErrorLogger.Errorf("数据同步失败: %v", err)
		return nil, fmt.Errorf("数据同步失败: %v", err)
	}

	// 记录同步结果
	qcs.logSyncResults(statistics)

	logging.InfoLogger.Info("=== 品控系统数据同步完成 ===")
	return statistics, nil
}

// 系统诊断
func (qcs *QualityControlSync) diagnoseSystem() error {
	logging.InfoLogger.Info("开始系统诊断...")

	// 步骤1：验证配置
	logging.InfoLogger.Info("步骤1: 验证配置")
	err := ValidateQualityTableConfig()
	if err != nil {
		return fmt.Errorf("配置验证失败: %v", err)
	}
	logging.InfoLogger.Info("✓ 配置验证通过")

	// 步骤2：测试飞书API连接
	logging.InfoLogger.Info("步骤2: 测试飞书API连接")
	err = TestFeishuAPIConnection()
	if err != nil {
		return fmt.Errorf("飞书API连接测试失败: %v", err)
	}
	logging.InfoLogger.Info("✓ 飞书API连接测试通过")
	logging.InfoLogger.Info("系统诊断完成，所有检查通过")
	return nil
}

// 记录同步结果
func (qcs *QualityControlSync) logSyncResults(statistics *SyncStatistics) {
	logging.InfoLogger.Info("=== 同步结果统计 ===")
	logging.InfoLogger.Infof("开始时间: %s", statistics.StartTime)
	logging.InfoLogger.Infof("结束时间: %s", statistics.EndTime)
	logging.InfoLogger.Infof("同步耗时: %s", statistics.Duration)
	logging.InfoLogger.Infof("总记录数: %d", statistics.TotalRecords)
	logging.InfoLogger.Infof("匹配记录: %d", statistics.MatchedRecords)
	logging.InfoLogger.Infof("更新记录: %d", statistics.UpdatedRecords)
	logging.InfoLogger.Infof("跳过记录: %d", statistics.SkippedRecords)
	logging.InfoLogger.Infof("错误记录: %d", statistics.ErrorRecords)

	// 计算成功率
	if statistics.TotalRecords > 0 {
		successRate := float64(statistics.UpdatedRecords) / float64(statistics.TotalRecords) * 100
		logging.InfoLogger.Infof("成功率: %.2f%%", successRate)
	}

	// 记录详细结果（如果有错误）
	results := qcs.processor.GetSyncResults()
	errorCount := 0
	for _, result := range results {
		if result.Status == "error" {
			errorCount++
			if errorCount <= 10 { // 只记录前10个错误
				logging.ErrorLogger.Errorf("错误记录 - 品控单号: %s, 错误: %s",
					result.QualityNumber, result.Message)
			}
		}
	}

	if errorCount > 10 {
		logging.ErrorLogger.Errorf("还有 %d 个错误记录未显示", errorCount-10)
	}
}

// 获取同步状态
func (qcs *QualityControlSync) GetSyncStatus() map[string]interface{} {
	return map[string]interface{}{
		"enabled":     qcs.enabled,
		"last_sync":   time.Now().Format("2006-01-02 15:04:05"),
		"status":      "ready",
		"description": "品控系统数据同步服务",
	}
}

// 启用同步功能
func (qcs *QualityControlSync) Enable() {
	qcs.enabled = true
	logging.InfoLogger.Info("品控数据同步功能已启用")
}

// 禁用同步功能
func (qcs *QualityControlSync) Disable() {
	qcs.enabled = false
	logging.InfoLogger.Info("品控数据同步功能已禁用")
}

// 获取详细的同步结果
func (qcs *QualityControlSync) GetDetailedResults() []SyncResult {
	return qcs.processor.GetSyncResults()
}

// 全局品控同步实例
var globalQualityControlSync *QualityControlSync

// 初始化品控同步服务
func InitQualityControlSync() {
	globalQualityControlSync = NewQualityControlSync()
	logging.InfoLogger.Info("品控数据同步服务初始化完成")
}

// 获取全局品控同步实例
func GetQualityControlSync() *QualityControlSync {
	if globalQualityControlSync == nil {
		InitQualityControlSync()
	}
	return globalQualityControlSync
}

// 执行品控数据同步（全局函数）
func ExecuteQualityControlSync() (*SyncStatistics, error) {
	sync := GetQualityControlSync()
	return sync.ExecuteSync()
}
