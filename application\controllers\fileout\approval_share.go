package fileout

import (
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/fileout/dapprovalshare"
	"irisAdminApi/service/dao/user/duser"
	"strconv"

	"github.com/jinzhu/copier"
	"github.com/kataras/iris/v12"
)

type Response struct {
	Id   uint   `copier:"must" json:"id"`
	Name string `copier:"must" json:"name"`
}
type UsersResponse struct {
	Response
}

func GetApprovalShares(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get approval id err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	shares, err := dapprovalshare.FindUsersByApprovalId(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	var userIds []string
	for _, share := range shares {
		userIds = append(userIds, strconv.FormatUint(uint64(share.UserId), 10))
	}
	users, err := duser.FindInId(userIds)
	if err != nil {
		logging.ErrorLogger.Errorf("get auditors err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	var shareUsers []*UsersResponse
	for _, user := range users {
		var shareUser UsersResponse
		copier.Copy(&shareUser, &user)
		shareUsers = append(shareUsers, &shareUser)
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, shareUsers, response.NoErr.Msg))
	return
}
