package dcomponenttempcheck

import (
	"strings"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/opensource"
)

const ModelName = "组件预查表"

type Response struct {
	opensource.OpenSourceComponentTempCheck
}

type ListResponse struct {
	Response
}

type Request struct {
	Id uint `json:"id"`
}

func (r *Response) ModelName() string {
	return ModelName
}

func Model() *opensource.OpenSourceComponentTempCheck {
	return &opensource.OpenSourceComponentTempCheck{}
}

func (r *Response) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update opensource component temp check get err %s", err.Error())
		return err
	}
	return nil
}

func CreateComponentTempCheck(res *Response) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(&res.OpenSourceComponentTempCheck).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create opensource component temp check get err %s", err.Error())
		return err
	}
	return nil
}

func GetComponentTempCheck(id uint) (*Response, error) {
	var res []*Response
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id=?", id).Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get opensource component temp check by id get err %s", err.Error())
		return nil, err
	} else {
		if len(res) == 0 {
			return nil, nil
		}
	}
	return res[0], nil
}

func ListComponentTempCheck(page, pageSize int, componentName, componentVersion string, status uint,
	sort, orderBy, createdAt, updatedAt string, userId uint) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())

	if len(componentName) != 0 {
		db = db.Where("component_name like ?", "%"+componentName+"%")
	}
	if len(componentVersion) != 0 {
		db = db.Where("component_version like ?", "%"+componentVersion+"%")
	}
	if status != 0 {
		db = db.Where("status=?", status)
	}
	if userId != 0 {
		db = db.Where("user_id=?", userId)
	}

	if len(createdAt) > 0 {
		array := strings.Split(createdAt, ",")
		db = db.Where("created_at between ? and ?", array[0], array[1])
	}
	if len(updatedAt) > 0 {
		array := strings.Split(updatedAt, ",")
		db = db.Where("updated_at between ? and ?", array[0], array[1])
	}
	if len(orderBy) == 0 {
		orderBy = "created_at"
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("count opensource component temp checks get err %s", err.Error())
		return nil, err
	}
	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("list opensource component temp checks get err %s", err.Error())
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func ListUnfinishedComponentTempChecks() ([]*Response, error) {
	var res []*Response
	err := easygorm.GetEasyGormDb().Model(Model()).
		Where("status=?", opensource.ComponentTempCheckStatusProcessing).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("list unifished component temp checks get err %s", err.Error())
		return nil, err
	}
	return res, err
}
