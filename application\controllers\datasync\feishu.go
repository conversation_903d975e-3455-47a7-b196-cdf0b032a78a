package datasync

import (
	"context"
	"fmt"
	"time"

	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	larkbitable "github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
	"github.com/pkg/errors"
)

func GetTableData(appToken, tableID string) ([]*larkbitable.AppTableRecord, error) {

	// 创建请求对象
	records := []*larkbitable.AppTableRecord{}
	pageToken := ""

	for {
		queryReqBuilder := larkbitable.NewListAppTableRecordReqBuilder().
			AppToken(appToken).
			TableId(tableID).
			PageSize(500)
		queryReq := queryReqBuilder.Build()
		if len(pageToken) > 0 {
			queryReq = queryReqBuilder.PageToken(pageToken).Build()
		}

		// 发起请求
		queryResp, err := FeiShuClient.Bitable.V1.AppTableRecord.List(context.Background(), queryReq)

		// 处理错误
		if err != nil {
			return records, errors.Wrap(err, "")
		}

		// 服务端错误处理
		if !queryResp.Success() {
			return records, fmt.Errorf("飞书服务端返回错误 %v %v %v", queryResp.Code, queryResp.Msg, queryResp.RequestId())
		}

		pageToken = larkcore.StringValue(queryResp.Data.PageToken)

		// fmt.Println(larkcore.Prettify(queryResp.Data))
		records = append(records, queryResp.Data.Items...)

		if len(queryResp.Data.Items) == 0 || pageToken == "" {
			break
		}
		time.Sleep(1 * time.Second)
	}
	return records, nil
}

func CleanTable(appToken, tableID string) error {

	// 创建请求对象

	for {
		recordIDs := []string{}
		queryReqBuilder := larkbitable.NewListAppTableRecordReqBuilder().
			AppToken(appToken).
			TableId(tableID).
			PageSize(500)

		queryReq := queryReqBuilder.Build()

		// 发起请求
		queryResp, err := FeiShuClient.Bitable.V1.AppTableRecord.List(context.Background(), queryReq)

		// 处理错误
		if err != nil {
			return errors.Wrap(err, "")
		}

		// 服务端错误处理
		if !queryResp.Success() {
			return fmt.Errorf("飞书服务端返回错误 %v %v %v", queryResp.Code, queryResp.Msg, queryResp.RequestId())
		}

		for _, item := range queryResp.Data.Items {
			recordIDs = append(recordIDs, *item.RecordId)
		}

		if len(recordIDs) == 0 {
			break
		}

		deleteReq := larkbitable.NewBatchDeleteAppTableRecordReqBuilder().
			AppToken(appToken).
			TableId(tableID).
			Body(larkbitable.NewBatchDeleteAppTableRecordReqBodyBuilder().Records(recordIDs).Build()).
			Build()
		deleteResp, err := FeiShuClient.Bitable.V1.AppTableRecord.BatchDelete(context.Background(), deleteReq)

		// 处理错误
		if err != nil {
			return errors.Wrap(err, "")
		}

		// 服务端错误处理
		if !deleteResp.Success() {
			return fmt.Errorf("飞书服务端返回错误 %v %v %v", deleteResp.Code, deleteResp.Msg, deleteResp.RequestId())
		}

		time.Sleep(1 * time.Second)
	}
	return nil
}

func GetTableDataHeaders(appToken, tableID string) ([]*larkbitable.AppTableFieldForList, error) {
	// 创建请求对象
	records := []*larkbitable.AppTableFieldForList{}
	pageToken := ""
	for {
		queryReqBuilder := larkbitable.NewListAppTableFieldReqBuilder().
			AppToken(appToken).
			TableId(tableID).
			PageSize(500)
		queryReq := queryReqBuilder.Build()
		if len(pageToken) > 0 {
			queryReq = queryReqBuilder.PageToken(pageToken).Build()
		}

		// 发起请求
		queryResp, err := FeiShuClient.Bitable.V1.AppTableField.List(context.Background(), queryReq)

		// 处理错误
		if err != nil {
			return records, errors.Wrap(err, "")
		}

		// 服务端错误处理
		if !queryResp.Success() {
			return records, fmt.Errorf("飞书服务端返回错误 %v %v %v", queryResp.Code, queryResp.Msg, queryResp.RequestId())
		}

		pageToken = larkcore.StringValue(queryResp.Data.PageToken)

		// fmt.Println(larkcore.Prettify(queryResp.Data))
		records = append(records, queryResp.Data.Items...)

		fmt.Println(len(queryResp.Data.Items), pageToken)

		if len(queryResp.Data.Items) == 0 || pageToken == "" {
			break
		}
		time.Sleep(1 * time.Second)
	}
	return records, nil
}

func CleanTableHeader(appToken, tableID string) error {
	fields, err := GetTableDataHeaders(appToken, tableID)
	if err != nil {
		return errors.Wrap(err, "")
	}

	for _, field := range fields {
		if *field.IsPrimary {
			continue
		}

		deleteReq := larkbitable.NewDeleteAppTableFieldReqBuilder().
			AppToken(appToken).
			TableId(tableID).
			FieldId(*field.FieldId).
			Build()
		deleteResp, err := FeiShuClient.Bitable.V1.AppTableField.Delete(context.Background(), deleteReq)

		// 处理错误
		if err != nil {
			return errors.Wrap(err, "")
		}

		// 服务端错误处理
		if !deleteResp.Success() {
			return fmt.Errorf("飞书服务端返回错误 %v %v %v", deleteResp.Code, deleteResp.Msg, deleteResp.RequestId())
		}
		time.Sleep(1 * time.Second)
	}
	return nil
}

func CreateTableDataHeaders(appToken, tableID string, data []map[string]interface{}) ([]*larkbitable.AppTableFieldForList, error) {
	// 创建请求对象
	records := []*larkbitable.AppTableFieldForList{}
	pageToken := ""
	for {
		queryReqBuilder := larkbitable.NewListAppTableFieldReqBuilder().
			AppToken(appToken).
			TableId(tableID).
			PageSize(500)
		queryReq := queryReqBuilder.Build()
		if len(pageToken) > 0 {
			queryReq = queryReqBuilder.PageToken(pageToken).Build()
		}

		// 发起请求
		queryResp, err := FeiShuClient.Bitable.V1.AppTableField.List(context.Background(), queryReq)

		// 处理错误
		if err != nil {
			return records, errors.Wrap(err, "")
		}

		// 服务端错误处理
		if !queryResp.Success() {
			return records, fmt.Errorf("飞书服务端返回错误 %v %v %v", queryResp.Code, queryResp.Msg, queryResp.RequestId())
		}

		pageToken = larkcore.StringValue(queryResp.Data.PageToken)

		// fmt.Println(larkcore.Prettify(queryResp.Data))
		records = append(records, queryResp.Data.Items...)

		if len(queryResp.Data.Items) == 0 || pageToken == "" {
			break
		}
		time.Sleep(1 * time.Second)
	}
	return records, nil
}
