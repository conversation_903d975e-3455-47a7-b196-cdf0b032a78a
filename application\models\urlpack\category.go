package urlpack

import "irisAdminApi/application/models"

type UrlPackDB struct {
	models.ModelBase
	Host     string `gorm:"not null; type:varchar(60)" json:"host"`
	Port     string `gorm:"not null; type:varchar(60)" json:"port"`
	Username string `gorm:"not null; type:varchar(60)" json:"username"`
	Password string `gorm:"not null; type:varchar(60)" json:"password"`
	Database string `gorm:"not null; type:varchar(60)" json:"database"`
}

// 分组编号 分组中文名 分组英文名 分类编号 分类中文名 分类英文名 中文描述 英文描述
type UrlPackCategory struct {
	models.ModelBase
	GroupID        uint   `gorm:"not null" json:"group_id"`
	GroupNameCH    string `gorm:"not null; type:varchar(60)" json:"group_name_ch"`
	GroupNameEN    string `grom:"not null; type:varchar(60)" json:"group_name_en"`
	CategoryID     uint   `gorm:"not null" json:"category_id"`
	CategoryNameCH string `gorm:"not null; type:varchar(60)" json:"category_name_ch"`
	CategoryNameEN string `gorm:"not null; type:varchar(60)" json:"category_name_en"`
	DescriptionCH  string `gorm:"not null; type:varchar(500)" json:"description_ch"`
	DescriptionEN  string `gorm:"not null; type:varchar(500)" json:"description_en"`
	OverseaLarge   bool   `gorm:"not null" json:"oversea_large"`
	OverseaSmall   bool   `gorm:"not null" json:"oversea_small"`
	Large          bool   `gorm:"not null" json:"large"`
	Small          bool   `gorm:"not null" json:"small"`
	PreDefBlock    bool   `gorm:"not null" json:"pre_def_block"`
}

type UrlPackJob struct {
	models.ModelBase
	JobID       string `gorm:"not null; type:varchar(60)" json:"job_id" form:"job_id"`
	WorkDir     string `gorm:"not null; type:varchar(200)" json:"work_dir" form:"work_dir"`
	Full        string `gorm:"not null; type:varchar(200)" json:"full" form:"full"`
	Incremental string `gorm:"not null; type:varchar(200)" json:"incremental" form:"incremental"`
	Status      uint   `gorm:"not null;default:0" json:"status" form:"status"` // 0 打包中  1 打包完成 2 打包失败
	Log         string `gorm:"not null; type:varchar(200)" json:"log" form:"log"`
	Type        string `gorm:"not null; type:varchar(60)" json:"type" form:"type"`
	// Oversea           bool   `gorm:"not null" json:"oversea" form:"oversea"`
	UserID      uint   `gorm:"not null" json:"user_id"`
	BaseVersion string `gorm:"not null; type:varchar(60)" json:"base_version" form:"base_version"`
	Version     string `gorm:"not null; type:varchar(60)" json:"version" form:"version"`
	Comment     string `json:"comment" form:"comment"`
}

type UrlPackConfig struct {
	models.ModelBase
	Type          string `gorm:"not null; type:varchar(60)" json:"type"`
	Prefix        string `gorm:"not null; type:varchar(60)" json:"prefix"`
	Size          int    `json:"size"`
	ProductModels string `gorm:"type:varchar(500)" json:"product_models"`
}
