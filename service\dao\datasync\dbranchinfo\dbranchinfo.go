package dbranchinfo

import (
	"encoding/json"
	"fmt"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/datasync"
	"irisAdminApi/service/dao/datasync/dsyncrecord"

	"gorm.io/gorm"
)

const ModelName = "分支信息表"

type SyncResponse struct {
	/*
		"state": "SUCCESS",
		"data": [
			{
				"id": 28,                                                //主键ID，可作为增量更新标识使用
				"projectName": "测试项目",                                //项目名称
				"componentName": "测试组件",                            //组件名称
				"newBranchUrl": "xxxx-eg-1.1.34",                        //新分支
				"baselineBranchUrl": "xxxx-eg-1.1.24",                    //基线分支
				"baselineBranchVersion": "1.0",                            //基线版本号
				"applyUserName": "测试人1",                                //分支申请人姓名
				"createDate": "2017-07-04 19:45:31",                    //分支开通时间
				"newBranchVersion": "2.0",                                //分支当前版本号
				"writerUserName": "测试人2",                            //分支可写人姓名
				"writerCasUserId": "ceshi2"                                //分支可写人邮箱前缀
			}
		],
		"total":1,
		"message": null
	*/
	State   string              `json:"state"`
	Data    []*SyncDataResponse `json:"data"`
	Total   int                 `json:"total"`
	Message string              `json:"message"`
}

type SyncDataResponse struct {
	/*
	   "id": 28,                                                //主键ID，可作为增量更新标识使用
	   "projectName": "测试项目",                                //项目名称
	   "componentName": "测试组件",                            //组件名称
	   "newBranchUrl": "xxxx-eg-1.1.34",                        //新分支
	   "baselineBranchUrl": "xxxx-eg-1.1.24",                    //基线分支
	   "baselineBranchVersion": "1.0",                            //基线版本号
	   "applyUserName": "测试人1",                                //分支申请人姓名
	   "createDate": "2017-07-04 19:45:31",                    //分支开通时间
	   "newBranchVersion": "2.0",                                //分支当前版本号
	   "writerUserName": "测试人2",                            //分支可写人姓名
	   "writerCasUserId": "ceshi2"                                //分支可写人邮箱前缀
	*/
	ID                    int    `gorm:"primarykey; autoIncrement:false" json:"id"`
	ProjectName           string `gorm:"not null; type:varchar(60)" json:"projectName"`
	ComponentName         string `gorm:"not null; type:varchar(300)" json:"componentName"`
	NewBranchUrl          string `gorm:"not null; type:varchar(300)" json:"newBranchUrl"`
	BaselineBranchUrl     string `gorm:"not null; type:varchar(300)" json:"baselineBranchUrl"`
	BaselineBranchVersion string `gorm:"not null; type:varchar(300)" json:"baselineBranchVersion"`
	ApplyUserName         string `gorm:"not null; type:varchar(300)" json:"applyUserName"`
	CreateDate            string `gorm:"not null; type:varchar(300)" json:"createDate"`
	NewBranchVersion      string `gorm:"not null; type:varchar(300)" json:"newBranchVersion"`
	WriterUserName        string `gorm:"not null; type:varchar(300)" json:"writerUserName"`
	WriterCasUserId       string `gorm:"not null; type:varchar(300)" json:"writerCasUserId"`
}

type Response struct {
	datasync.BugBranchInfo
}

type ListResponse struct {
	Response
}

type Request struct {
	Id uint `json:"id"`
}

func (this *Response) ModelName() string {
	return ModelName
}

func Model() *datasync.BugBranchInfo {
	return &datasync.BugBranchInfo{}
}

func (this *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (this *Response) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindByNewBranch(newBranches []string) ([]*ListResponse, error) {
	items := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("new_branch_url in ?", newBranches).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return items, err
	}
	return items, nil
}

func UpdateOrCreateBugBranchInfoTransaction(items []*SyncDataResponse, _url string, data map[string]string, method, state, errorMsg string) error {
	db := easygorm.GetEasyGormDb()
	batchCreateObjects := []map[string]interface{}{}
	_map := map[int]map[string]interface{}{}
	err := db.Transaction(func(tx *gorm.DB) error {
		for _, item := range items {
			response := Response{}
			object := map[string]interface{}{
				"id":                      item.ID,
				"project_name":            item.ProjectName,
				"component_name":          item.ComponentName,
				"new_branch_url":          item.NewBranchUrl,
				"baseline_branch_url":     item.BaselineBranchUrl,
				"baseline_branch_version": item.BaselineBranchVersion,
				"apply_user_name":         item.ApplyUserName,
				"create_date":             item.CreateDate,
				"new_branch_version":      item.NewBranchVersion,
				"writer_user_name":        item.WriterUserName,
				"writer_cas_user_id":      item.WriterCasUserId,
			}
			if err := tx.Model(Model()).Where("id = ?", item.ID).Find(&response).Error; err != nil {
				return err
			} else {
				if response.ID == 0 {
					if _, ok := _map[item.ID]; ok {
						_map[item.ID]["writer_user_name"] = _map[item.ID]["writer_user_name"].(string) + "," + item.WriterUserName
						_map[item.ID]["writer_cas_user_id"] = _map[item.ID]["writer_cas_user_id"].(string) + "," + item.WriterCasUserId
					} else {
						_map[item.ID] = object
					}
				} else {
					if err := tx.Model(Model()).Where("id = ?", item.ID).Updates(object).Error; err != nil {
						return err
					}
				}
			}
		}
		for _, _object := range _map {
			batchCreateObjects = append(batchCreateObjects, _object)
		}
		if len(batchCreateObjects) > 0 {
			if err := tx.Model(Model()).Create(batchCreateObjects).Error; err != nil {
				return err
			}
		}
		body, err := json.Marshal(data)
		if err != nil {
			return err
		}
		if err := tx.Model(dsyncrecord.Model()).Create(map[string]interface{}{
			"url":             _url,
			"body":            body,
			"method":          method,
			"state":           state,
			"message":         errorMsg,
			"min_modify_date": data["minModifyDate"],
			"max_modify_date": data["maxModifyDate"],
			"created_at":      time.Now(),
		}).Error; err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}
