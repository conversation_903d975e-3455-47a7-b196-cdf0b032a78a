package dgitjob

import (
	"fmt"
	"strings"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/buildfarm"
	"irisAdminApi/service/dao/buildfarm/dmakejob"

	"github.com/jinzhu/copier"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

const ModelName = "Git任务管理"

type User struct {
	ID       uint   `json:"id"`
	Username string `json:"username"`
	Name     string `json:"name"`
}

type GitJob struct {
	buildfarm.GitJob
	MakeJobs []*dmakejob.MakeJob `gorm:"->; foreignKey:JobID; references:JobID" json:"make_jobs"`
	User     User                `grom:"->; foreignKey:UserID; references:ID" json:"user"`
	CpuType  string              `gorm:"-" json:"cpu_type"` 
}

type ListResponse struct {
	GitJob
}

type GitJobReq struct {
	Id       uint   `json:"id"`
	UserId   uint   `json:"user_id"`
	Dir      string `json:"dir"`
	Status   uint   `json:"status"`    // 作业状态 0：运行，1：成功， 2：失败
	TaskType uint   `json:"task_type"` //编译类型，1：产品编译  2：组件编译
	Version  string `json:"version"`   // 版本hash值,初始为0
}

func (a *GitJob) ModelName() string {
	return ModelName
}

func Model() *buildfarm.GitJob {
	return &buildfarm.GitJob{}
}

func (a *GitJob) All(id uint, name, customized, taskType, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64

	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("MakeJobs", func(db *gorm.DB) *gorm.DB {
		return db.Order("make_jobs.id DESC")
	})

	db = db.Where("user_id = ?", id)
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}

	if customized != "" {
		db = db.Where("customized = ?", customized)
	}

	if taskType != "" {
		db = db.Where("task_type = ?", taskType)
	} else {
		db = db.Where("task_type in (1, 2)")
	}

	err := db.Count(&count).Error
	if err != nil {
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		return nil, err
	}
	// dgitjob.AddMakeJob(gitjobs)

	//提取CPU信息
	for _, item := range items {
		item.CpuType = extractCpuInfo(item)
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *GitJob) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *GitJob) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *GitJob) CreateV2(object interface{}) error {
	return nil
}

func (a *GitJob) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *GitJob) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *GitJob) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *GitJob) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *GitJob) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *GitJob) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindGitjobsInJobIds(ids []string) ([]*GitJob, error) {
	var gitjobs []*GitJob
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Where("job_id in ?", ids).Find(&gitjobs).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return nil, err
	}
	// AddUser(gitjobs)
	return gitjobs, nil
}

func FindGitjobs(jobID, userID, repo, branch, server, customized string) ([]*GitJob, error) {
	var gitjobs []*GitJob
	db := easygorm.GetEasyGormDb().Model(Model()).Preload("User")
	if jobID != "" {
		db = db.Where("job_id = ?", jobID)
	}
	if len(userID) > 0 && userID != "0" {
		db = db.Where("user_id = ?", userID)
	}
	if len(repo) > 0 {
		db = db.Where("repo like ?", fmt.Sprintf("%%%s%%", repo))
	}
	if len(branch) > 0 {
		db = db.Where("branch = ?", branch)
	}
	if len(server) > 0 {
		db = db.Where("server_id = ?", server)
	}
	if customized != "" {
		db = db.Where("customized = ?", customized)
	}
	err := db.Find(&gitjobs).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return nil, err
	}
	// AddUser(gitjobs)
	return gitjobs, nil
}

func AddMakeJob(gitjobs []*ListResponse) {
	for _, gitjob := range gitjobs {
		makejobs, _ := dmakejob.FindGitJobMakeJobs(gitjob.JobID)
		if len(makejobs) > 0 {
			gitjob.MakeJobs = makejobs[0:1]
		}
	}
}

// func AddUser(gitjobs []*GitJob) {
// 	var userIds []string
// 	for _, gitjob := range gitjobs {
// 		if libs.InArrayS(userIds, strconv.FormatUint(uint64(gitjob.UserID), 10)) {
// 			continue
// 		}
// 		userIds = append(userIds, strconv.FormatUint(uint64(gitjob.UserID), 10))
// 	}
// 	users, _ := duser.FindSimpleInId(userIds)
// 	for _, gitjob := range gitjobs {
// 		for _, user := range users {
// 			if gitjob.UserID == user.Id {
// 				gitjob.User = user
// 				continue
// 			}
// 		}
// 	}
// }

func AddGitjob(makejobs []*dmakejob.MakeJob) {
	var gitjobIds []string
	for _, makejob := range makejobs {
		if libs.InArrayS(gitjobIds, makejob.JobID) {
			continue
		}
		gitjobIds = append(gitjobIds, makejob.JobID)
	}
	gitjobs, _ := FindGitjobsInJobIds(gitjobIds)
	for _, makejob := range makejobs {
		for _, gitjob := range gitjobs {
			if makejob.JobID == gitjob.JobID {
				var makejobGitjob dmakejob.GitJob
				err := copier.Copy(&makejobGitjob, &gitjob)
				if err != nil {
					logging.ErrorLogger.Error(err)
				}
				makejob.GitJob = &makejobGitjob
				continue
			}
		}
	}
}

type Summary struct {
	ServerId uint `json:"server_id"`
	Running  uint `json:"running"`
	Queue    uint `json:"queue"`
	Total    uint `json:"total"`
}

func SummaryGitjobByServer(ids []string, name string) ([]*Summary, error) {
	db := easygorm.GetEasyGormDb().Model(Model())
	summary := []*Summary{}

	rows, err := db.Where("job_id in ?", ids).Select(fmt.Sprintf("server_id, count(*) as %s", name)).Group("server_id").Rows()
	if err != nil {
		logging.ErrorLogger.Error(err)
		return summary, err
	}
	for rows.Next() {
		db.ScanRows(rows, &summary)
	}
	rows.Close()
	return summary, nil
}

func FisrtGitjobByJobIdAndServer(ids []string, server uint) (GitJob, error) {
	var gitjobs []*ListResponse
	db := easygorm.GetEasyGormDb().Model(Model())
	err := db.Where("job_id in ? and server_id = ?", ids, server).Order("created_at asc").Find(&gitjobs).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find git job by server err ", err)
		return GitJob{}, err
	}
	if len(gitjobs) > 0 {
		return gitjobs[0].GitJob, nil
	}
	return GitJob{}, errors.New("not found")
}

func FindGcovGitJobsByLikeReleaseID(branch, releaseID string) ([]*ListResponse, error) {
	items := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).
		Preload("MakeJobs", "product !='' and build_type = 'gcov' and software_version like ?", fmt.Sprintf("%%%s%%", releaseID)).
		Preload("MakeJobs.Cpu").
		Where("customized = 0 and task_type = 1 and branch = ? and job_id in (?)", branch, easygorm.GetEasyGormDb().Table("make_jobs").Select("job_id").Where("product !='' and build_type = 'gcov' and software_version like ?", fmt.Sprintf("%%%s%%", releaseID))).
		Find(&items).Error
	return items, errors.Wrap(err, "")
}

// 提取CPU信息的辅助函数
func extractCpuInfo(item *ListResponse) string {
	for _, makeJob := range item.MakeJobs {
		//通过Product字段查询BuildfarmProductCpu表
		var cpuInfo BuildfarmProductCpu
		if makeJob.Product != "" {
			err := easygorm.GetEasyGormDb().
				Table("buildfarm_product_cpus").
				Where("product = ?", makeJob.Product).
				First(&cpuInfo).Error

			if err == nil && cpuInfo.Cpu != "" {
				return cpuInfo.Cpu
			}
		}
		//从Baseline字段解析
		if makeJob.Baseline != "" {
			// 解析Baseline字段获取CPU架构信息
			// 示例: http://10.51.135.15:9090/output/2af3a423debd72dd4e9ceee709cb99d5/armada7k-aarch64/armada7k-aarch64-image.tar.bz2
			parts := strings.Split(makeJob.Baseline, "/")
			if len(parts) >= 6 {
				return parts[len(parts)-2]
			}
		}
	}

	return ""
}

type BuildfarmProductCpu struct {
	Product string `json:"product"`
	Cpu     string `json:"cpu"`
}
