package dcontributionversion

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/kpi"
	"irisAdminApi/service/dao/kpi/dcontribution"
	"irisAdminApi/service/dao/kpi/dcontributionpoint"
	"irisAdminApi/service/dao/kpi/dcontributionreview"
)

const ModelName = "贡献版本管理"

/*
	Description        string `gorm:"not null; type:varchar(512)"`
	ContributedAt      string `gorm:"not null; type:varchar(30)"`
	ContributionTypeID uint   `gorm:"not null"`
	UserID             uint   `gorm:"not null"`
	Status             uint   `gorm:"not null；default:0"` //0:评审中  1:通过 2:不通过
*/

type Response struct {
	ID              uint                                `json:"id"`
	UpdatedAt       string                              `json:"updated_at"`
	CreatedAt       string                              `json:"created_at"`
	ContributionID  uint                                `json:"contribution_id"`
	ContributionIDs string                              `json:"contribution_ids"`
	Contributiton   *dcontribution.Response             `gorm:"-" json:"contribution"`
	Reviews         []*dcontributionreview.ListResponse `gorm:"-" json:"reviews,omitempty"`
	Points          []*dcontributionpoint.ListResponse  `gorm:"-" json:"points,omitempty"`
}

type ListResponse struct {
	Response
}

type Request struct {
	Description     string `json:"description"`
	ContributedAt   string `json:"contributed_at"`
	ContributionID  uint   `json:"contribution_id"`
	ContributionIDs string `json:"contribution_ids"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *kpi.ContributionVersion {
	return &kpi.ContributionVersion{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	GetContributions(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func All() ([]*ListResponse, error) {
	var items []*ListResponse

	err := easygorm.GetEasyGormDb().Model(Model()).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	return items, nil
}

func GetContribution(item *Response) error {
	contribution := dcontribution.Response{}
	err := contribution.Find(item.ContributionID)
	if err != nil {
		return err
	}
	item.Contributiton = &contribution
	return nil
}

func GetContributions(items []*ListResponse) error {
	contributionIds := []uint{}
	for _, item := range items {
		contributionIds = append(contributionIds, item.ContributionID)
	}

	contributions, err := dcontribution.FindInIds(contributionIds)
	if err != nil {
		return err
	}
	contributionMap := map[uint]*dcontribution.Response{}
	for _, contribution := range contributions {
		contributionMap[contribution.ID] = &contribution.Response
	}
	for _, item := range items {
		item.Contributiton = contributionMap[item.ContributionID]
	}
	GetReviews(items)
	GetPoints(items)
	return nil
}

func FindAllReviewsByUserID(uid uint, judge, name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*dcontributionreview.ListResponse

	contributionIds := []uint{}
	contributionVersions, err := All()
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}
	contributionMap := map[uint]*dcontribution.Response{}
	for _, contribution := range contributionVersions {
		contributionIds = append(contributionIds, contribution.ContributionID)
	}
	contributions, err := dcontribution.FindInIds(contributionIds)
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}
	for _, contribution := range contributions {
		contributionMap[contribution.ID] = &contribution.Response
	}
	db := easygorm.GetEasyGormDb().Model(dcontributionreview.Model())
	db = db.Where("contribution_id in ?", contributionIds)
	if judge == "0" {
		db = db.Where("reviewer_id = ? and judge = ?", uid, judge)
	} else {
		db = db.Where("reviewer_id = ? and judge != ?", uid, 0)
	}

	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err = db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	for _, item := range items {
		item.Contributiton = contributionMap[item.ContributionID]
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func GetReviews(items []*ListResponse) {
	contributionIDReviewsMap := map[uint][]*dcontributionreview.ListResponse{}
	contributionIDs := []uint{}
	for _, item := range items {
		contributionIDs = append(contributionIDs, item.ContributionID)
	}
	reviews, err := dcontributionreview.FindInContributionID(contributionIDs)

	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
	}
	for _, review := range reviews {
		if _, ok := contributionIDReviewsMap[review.ContributionID]; ok {
			contributionIDReviewsMap[review.ContributionID] = append(contributionIDReviewsMap[review.ContributionID], review)
		} else {
			contributionIDReviewsMap[review.ContributionID] = []*dcontributionreview.ListResponse{}
			contributionIDReviewsMap[review.ContributionID] = append(contributionIDReviewsMap[review.ContributionID], review)
		}

	}
	for _, item := range items {
		item.Reviews = contributionIDReviewsMap[item.ContributionID]
	}
}

func AllByUserID(uid uint, name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse
	contributionIds := []uint{}
	err := easygorm.GetEasyGormDb().Model(dcontributionpoint.Model()).Where("contributor_id = ?", uid).Select("contribution_id").Pluck("contribution_id", &contributionIds).Error
	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	db = db.Where("contribution_id in ?", contributionIds)
	err = db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	GetContributions(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func GetPoints(items []*ListResponse) {
	contributionIDPointsMap := map[uint][]*dcontributionpoint.ListResponse{}
	contributionIDs := []uint{}
	for _, item := range items {
		contributionIDs = append(contributionIDs, item.ContributionID)
	}
	reviews, err := dcontributionpoint.FindInContributionID(contributionIDs)

	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
	}
	for _, review := range reviews {
		if _, ok := contributionIDPointsMap[review.ContributionID]; ok {
			contributionIDPointsMap[review.ContributionID] = append(contributionIDPointsMap[review.ContributionID], review)
		} else {
			contributionIDPointsMap[review.ContributionID] = []*dcontributionpoint.ListResponse{}
			contributionIDPointsMap[review.ContributionID] = append(contributionIDPointsMap[review.ContributionID], review)
		}

	}
	for _, item := range items {
		item.Points = contributionIDPointsMap[item.ContributionID]
	}
}
