package datasync

import "irisAdminApi/application/models"

type PmsProjectDocument struct {
	models.ModelBase
	ProjectName           string `gorm:"not null; type:varchar(200)" json:"project_name"  update:"1"`
	DocumentName          string `gorm:"not null; type:varchar(200); default:''" json:"document_name"  update:"1"`
	WorkPacketName        string `gorm:"not null; type:varchar(200); default:''" json:"work_packet_name"  update:"1"`
	ChangeTypeName        string `gorm:"type:varchar(100)" json:"change_type_name" update:"1"`
	DocumentSubmitStatus  string `gorm:"type:varchar(100)" json:"document_submit_status" update:"1"`
	ReviewStatus          string `gorm:"type:varchar(100)" json:"review_status" update:"1"`
	IsSubmit              bool   `gorm:"not null; default:false" json:"is_submit" update:"1"`
	IsInstorage           bool   `gorm:"not null; default:false" json:"is_instorage" update:"1"`
	DocumentStatus        string `gorm:"type:varchar(100)" json:"document_status" update:"1"`
	ChargeUserName        string `gorm:"not null; type:varchar(200); default:''" json:"charge_user_name"  update:"1"`
	ComponentPacketName   string `gorm:"not null; type:varchar(200); default:''" json:"component_packet_name"  update:"1"`
	Disabled              bool   `grom:"not null; default:false" json:"disabled" update:"1"`
	DocumentCategoryTitle string `gorm:"not null; type:varchar(100); default:''" json:"document_category_title" update:"1"`
}
