package codesync

import (
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/codesync/dcodesyncproject"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

func GetCodeSyncProjects(ctx iris.Context) {
	// id, err := dao.GetAuthId(ctx)
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dcodesyncproject.ListResponse{}, ctx, name, sort, orderBy, page, pageSize)
	// list, err := dproblem.AllProblems(name, sort, orderBy, page, pageSize, status, start, end, department)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetCodeSyncProject(ctx iris.Context) {
	info := dcodesyncproject.Response{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

func CreateCodeSyncProject(ctx iris.Context) {
	// program, err := buildfarmProject.FindProduct()
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("create release project get err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未找到工程文件，请联系管理员"))
	// 	return
	// }

	request := &dcodesyncproject.Request{}
	if err := ctx.ReadJSON(request); err != nil {
		logging.ErrorLogger.Errorf("create policy read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	/*
		SourceRepositoryID   uint   `gorm:"not null;" json:"source_repository_id"`
		SourceRepositoryPath string `gorm:"not null; type:varchar(300)" json:"source_repository_path"`
		SourceBranch         string `gorm:"not null; type:varchar(300)" json:"source_branch"`
		TargetRepositoryID   uint   `gorm:"not null;" json:"target_repository_id"`
		TargetRepositoryPath string `gorm:"not null; type:varchar(300)" json:"target_repository_path"`
		TargetBranch         string `gorm:"not null; type:varchar(300)" json:"target_branch"`
		ExpiredAt            string `gorm:"not null; type:varchar(60)" json:"expired_at"`
		Enable               bool   `gorm:"not null; default:'false'" json:"enable"`
	*/

	err := dao.Create(&dcodesyncproject.Response{}, ctx, map[string]interface{}{
		"RepositoryID":   request.RepositoryID,
		"RepositoryPath": request.RepositoryPath,
		"RepositoryName": request.RepositoryName,
		"CreatedAt":      time.Now(),
		"UpdatedAt":      time.Now(),
	})
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, request, response.NoErr.Msg))
	return
}

func UpdateCodeSyncProject(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	request := &dcodesyncproject.Request{}
	if err := ctx.ReadJSON(request); err != nil {
		logging.ErrorLogger.Errorf("create approval read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	object := map[string]interface{}{
		"ID":             id,
		"RepositoryID":   request.RepositoryID,
		"RepositoryPath": request.RepositoryPath,
		"RepositoryName": request.RepositoryName,
		"CreatedAt":      time.Now(),
		"UpdatedAt":      time.Now(),
	}

	err := dao.Update(&dcodesyncproject.Response{}, ctx, object)
	// err = transproblem.UpdateProblem(uId, id, object)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func DeleteCodeSyncProject(ctx iris.Context) {
	info := dcodesyncproject.Response{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	err = dao.Delete(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}
