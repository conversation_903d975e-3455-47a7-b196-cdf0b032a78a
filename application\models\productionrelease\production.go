package productionrelease

import (
	"irisAdminApi/application/models"
)

type Production struct {
	models.ModelBase
	MainProgramUrl        string `gorm:"not null; type:varchar(200)" json:"main_program_url" form:"main_program_url"`
	MainProgramFileName   string `gorm:"not null; type:varchar(200)" json:"main_program_file_name" form:"main_program_file_name"`
	MainProgramFileSize   uint   `gorm:"not null" json:"main_program_file_size" form:"main_program_file_size"`
	MainProgramFileMd5    string `gorm:"not null; type:varchar(200)" json:"main_program_file_md5" form:"main_program_file_md5"`
	OspkgInstallUrl       string `gorm:"not null; type:varchar(200)" json:"ospkg_install_url" form:"ospkg_install_url"`
	OspkgInstallName      string `gorm:"not null; type:varchar(200)" json:"ospkg_install_name" form:"ospkg_install_name"`
	OspkgInstallSize      uint   `gorm:"not null" json:"ospkg_install_size" form:"ospkg_install_size"`
	OspkgInstallMd5       string `gorm:"not null; type:varchar(200)" json:"ospkg_install_md5" form:"ospkg_install_md5"`
	Version               string `gorm:"not null; type:varchar(60)" json:"version" form:"version"`
	ProductModel          string `json:"product_models" form:"product_model"`
	ProductSeries         string `json:"product_series" form:"product_series"`
	ProductionType        string `gorm:"not null; type:varchar(60)" json:"production_type" form:"production_type"`
	ProductionVersions    string `json:"production_versions" form:"production_versions"`
	ProductionBaseVersion uint   `gorm:"not null" json:"production_base_version" form:"production_base_version"`
	VersionType           uint   `gorm:"not null" json:"version_type" form:"version_type"`
	ReleaseDate           string `gorm:"not null; type:varchar(60)" json:"release_date" form:"release_date"`
	VersionDesc           string `gorm:"not null type:varchar(256)" json:"version_desc" form:"version_desc"`
	Uuid                  string `gorm:"not null; type:varchar(60)" json:"uuid" form:"uuid"`
	Status                uint   `gorm:"not null, default:0" json:"status" form:"status"` //0:失败 1:成功
	OverTimeNotice        bool   `gorm:"not null, default: false" json:"over_time_notice" form:"over_time_notice"`
	Urgency               bool   `gorm:"not null, default: false" json:"urgency" form:"urgency"`
	SoftwareNumber        string `gorm:"not null; type:varchar(60)" json:"software_nubmer" form:"software_nubmer"`
	SoftwareVersion       string `gorm:"not null; type:varchar(60)" json:"software_version" form:"software_version"`
	RegionType            uint   `gorm:"not null, default:1" json:"region_type" form:"region_type"` //1:国内 2:海外
}

type ProductionProcDef struct {
	models.ModelBase
	Name    string `json:"name,omitempty"`
	Version int    `json:"version,omitempty"`
	// 流程定义json字符串
	Resource      string `gorm:"size:10000" json:"resource,omitempty"`
	ProductModels string `gorm:"type:varchar(500)" json:"product_models"`
}

type ProductionProcInst struct {
	models.ModelBase
	// 流程定义ID
	// ProcDefID int `json:"proc_def_id"`
	// title 标题
	Title string `json:"title"`
	// 当前节点
	NodeID string `json:"node_id"`
	// 审批人
	// Candidate string `json:"candidate"`
	// 当前任务
	TaskID       uint   `json:"task_id"`
	StartUserID  uint   `json:"start_user_id"`
	ProductionID uint   `json:"production_id"`
	Status       uint   `gorm:"not null,default:0"` //0:进行中 1:完成 2：失败
	Resource     string `gorm:"size:10000" json:"resource,omitempty"`
}

type ProductionProcTask struct {
	models.ModelBase
	// 当前执行流所在的节点
	NodeName   string `json:"nodeName"`
	PrevNodeID string `json:"prevNodeId"`
	NodeID     string `json:"nodeId"`
	// Step   int    `json:"step"`
	// 流程实例id
	ProcInstID uint `json:"procInstID"`
	Assignee   uint `json:"assignee"`
	// 还未审批的用户数，等于0代表会签已经全部审批结束，默认值为1
	// MemberCount   int8 `json:"memberCount" gorm:"default:1"`
	// UnCompleteNum int8 `json:"unCompleteNum" gorm:"default:1"`
	//审批通过数
	// AgreeNum int8 `json:"agreeNum"`
	// and 为会签，or为或签，默认为or
	// ActType    string `json:"actType" gorm:"default:'or'"`
	Comment             string `json:"comment"`
	Status              uint   `gorm:"not null,default:0"` //0:进行中 1:通过 2：转派 3:回退
	Flag                bool   `gorm:"not null,default:true"`
	Attachment          string `gorm:"type:varchar(300)"`
	OverTimeNotice      bool   `gorm:"not null, default: false"`
	Uboot               string `gorm:"not null; default:'';type:varchar(255)" json:"uboot"` //uboot
	UbootMd5            string `gorm:"not null; default:'';type:varchar(100)" json:"uboot_md5"`
	UbootVersion        string `gorm:"not null; default:'';type:varchar(100)" json:"uboot_version"` //uboot版本
	UbootDoc            string `gorm:"not null; default:'';type:varchar(255)" json:"uboot_doc"`     //uboot说明文档
	RomProgram          string `gorm:"not null; default:'';type:varchar(255)" json:"rom_program"`   //烧片程序(ROM)
	RomProgramMd5       string `gorm:"not null; default:'';type:varchar(100)" json:"rom_program_md5"`
	SetmacTool          string `gorm:"not null; default:'';type:varchar(255)" json:"setmac_tool"` //setmac工具
	SetmacToolMd5       string `gorm:"not null; default:'';type:varchar(100)" json:"setmac_tool_md5"`
	SetmacIni           string `gorm:"not null; default:'';type:varchar(255)" json:"setmac_ini"`
	SetmacIniMd5        string `gorm:"not null; default:'';type:varchar(100)" json:"setmac_ini_md5"`
	SetmacDoc           string `gorm:"not null; default:'';type:varchar(255)" json:"setmac_doc"`             //setmac说明文档
	ManufTestProgram    string `gorm:"not null; default:'';type:varchar(255)" json:"manuf_test_program"`     //生测工具
	ManufTestProgramMD5 string `gorm:"not null; default:'';type:varchar(255)" json:"manuf_test_program_md5"` //生测工具MD5
	ManufTestProgramDoc string `gorm:"not null; default:'';type:varchar(255)" json:"manuf_test_program_doc"`
	ManufTestReport     string `gorm:"not null; default:'';type:varchar(255)" json:"manuf_test_report"`
	CpldUpdate          bool   `gorm:"not null, default: false"`
	CpldUpdateUrl       string `gorm:"not null; default:'';type:varchar(255)" json:"cpld_update_url"`
	Done                bool   `gorm:"not null, default: false"`
}

type SecCloud struct {
	models.ModelBase
	Name          string `gorm:"not null; default:''; type:varchar(60)" json:"name"`
	Url           string `gorm:"not null; default:''; type:varchar(2000)" json:"url"`
	ProductModels string `gorm:"not null; default:''; type:varchar(2000)" json:"product_models"`
	Status        bool   `gorm:"not null; default:true" json:"status"`
	Username      string `gorm:"not null; default:''; type:varchar(60)" json:"username"`
	Password      string `gorm:"not null; default:''; type:varchar(60)" json:"Password"`
	AccessToken   string `gorm:"not null" json:"access_token"`
	RefreshToken  string `gorm:"not null" json:"refresh_token"`
	Expires       int    `gorm:"not null" json:"expires"`
	Type          string `gorm:"not null; type:varchar(60)" json:"type"`
}

type ProductionProductModel struct {
	models.ModelBase
	Name         string `gorm:"not null; default:''; type:varchar(60)" json:"name"`
	ProductModel string `gorm:"not null; default:''; type:varchar(500)" json:"product_model"`
}

type ProductionDocumentTemplate struct {
	models.ModelBase
	ProductModel string `gorm:"not null; default:''; type:varchar(255)" json:"product_model"`
	TemplateName string `gorm:"not null; default:''; type:varchar(255)" json:"template_name"`
	TemplatePath string `gorm:"not null; default:''; type:varchar(255)" json:"template_path"`
	ProductionID uint   `gorm:"not null" json:"production_id" form:"production_id"`
	ProcInstID   uint   `gorm:"not null" json:"proc_inst_id" form:"proc_inst_id"`
}

type ProductionProcTaskDocument struct {
	models.ModelBase
	ProductionID          uint                       `gorm:"not null" json:"production_id" form:"production_id"`
	TaskID                uint                       `gorm:"not null" json:"task_id" form:"task_id"`
	DocumentTemplateID    uint                       `gorm:"not null" json:"document_template_id" form:"document_template_id"`
	DocName               string                     `gorm:"not null; default:''; type:varchar(255)" json:"doc_name"`
	DocType               string                     `gorm:"not null; default:''; type:varchar(100)" json:"doc_type"`
	GeneratedDocumentPath string                     `gorm:"not null; default:''; type:varchar(255)" json:"generated_document_path"`
	ProcInstID            uint                       `gorm:"not null" json:"proc_inst_id" form:"proc_inst_id"`
	DocumentTemplate      ProductionDocumentTemplate `gorm:"->; foreignKey:DocumentTemplateID;references:ID;"`
}

type ProductionProcTaskExtension struct {
	models.ModelBase
	TaskID        uint   `gorm:"not null" json:"task_id" form:"task_id"`
	ExtensionName string `gorm:"not null; default:''; type:varchar(255)" json:"extension_name" form:"extension_name"`
	ExtensionTool string `gorm:"not null; default:''; type:varchar(255)" json:"extension_tool"`
	MD5           string `gorm:"not null; default:''; type:varchar(100)" json:"md5"`
	ExtensionDoc  string `gorm:"not null; default:''; type:varchar(255)" json:"extension_doc"`
	ProcInstID    uint   `gorm:"not null" json:"proc_inst_id" form:"proc_inst_id"`
}

type ProductionProcTaskHost struct {
	models.ModelBase
	TaskID        uint   `gorm:"not null" json:"task_id" form:"task_id"`
	ExtensionName string `gorm:"not null; default:''; type:varchar(255)" json:"extension_name" form:"extension_name"`
	ExtensionTool string `gorm:"not null; default:''; type:varchar(255)" json:"extension_tool"`
	MD5           string `gorm:"not null; default:''; type:varchar(100)" json:"md5"`
	ProcInstID    uint   `gorm:"not null" json:"proc_inst_id" form:"proc_inst_id"`
}
