package kpi

import (
	// "irisAdminApi/application/libs"

	"errors"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"strings"
	"time"

	// "irisAdminApi/application/logging"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/kpi/dcontribution"
	"irisAdminApi/service/dao/kpi/dcontributionpoint"
	"irisAdminApi/service/dao/kpi/dcontributionversion"
	"irisAdminApi/service/dao/kpi/dproblem"
	"irisAdminApi/service/dao/user/ddepartment"
	"irisAdminApi/service/dao/user/duser"
	"irisAdminApi/service/dao/user/duserdepartment"
	"reflect"
	"sort"

	"github.com/kataras/iris/v12"
)

var statusMap = map[uint]string{
	1: "已闭环",
	0: "未闭环",
}

var statusEnMap = map[uint]string{
	1: "Close",
	0: "Open",
}

func OrderGroupResult(result []*Department, order, by string) {
	var name string
	var FieldIdx int
	switch by {
	case "close":
		name = "Close"
	case "open":
		name = "Open"
	case "total":
		name = "Total"
	case "close_rate":
		name = "CloseRate"
	case "point":
		name = "Point"
	case "avg_point":
		name = "AveragePoint"
	default:
		name = "Close"
	}
	if len(result) > 1 {
		v := reflect.Indirect(reflect.ValueOf(result[0]))
		t := reflect.TypeOf(result[0]).Elem()
		for i := 0; i < v.NumField(); i++ {
			// fmt.Printf("Field:%d \t type:%T \t name: %s\t value:%v\n",
			// 	i, v.Field(i), t.Field(i).Name, v.Field(i))
			if t.Field(i).Name == name {
				FieldIdx = i
			}
		}
		switch order {
		case "desc":
			sort.Slice(result, func(i, j int) bool {
				vi := reflect.Indirect(reflect.ValueOf(result[i]))
				vj := reflect.Indirect(reflect.ValueOf(result[j]))
				if !strings.Contains(result[i].Link, "/") {
					return true
				}
				if !strings.Contains(result[j].Link, "/") {
					return false
				}
				if libs.InArrayS([]string{"CloseRate", "AveragePoint"}, name) {
					return vi.Field(FieldIdx).Float() > vj.Field(FieldIdx).Float()
				} else {
					return vi.Field(FieldIdx).Int() > vj.Field(FieldIdx).Int()
				}

			})
		case "asc":
			sort.Slice(result, func(i, j int) bool {
				vi := reflect.Indirect(reflect.ValueOf(result[i]))
				vj := reflect.Indirect(reflect.ValueOf(result[j]))
				if !strings.Contains(result[i].Link, "/") {
					return true
				}
				if !strings.Contains(result[j].Link, "/") {
					return false
				}
				if libs.InArrayS([]string{"CloseRate", "AveragePoint"}, name) {
					return vi.Field(FieldIdx).Float() < vj.Field(FieldIdx).Float()
				} else {
					return vi.Field(FieldIdx).Int() < vj.Field(FieldIdx).Int()
				}
			})
		default:
			sort.Slice(result, func(i, j int) bool {
				vi := reflect.Indirect(reflect.ValueOf(result[i]))
				vj := reflect.Indirect(reflect.ValueOf(result[j]))
				if !strings.Contains(result[i].Link, "/") {
					return true
				}
				if !strings.Contains(result[j].Link, "/") {
					return false
				}
				if libs.InArrayS([]string{"CloseRate", "AveragePoint"}, name) {
					return vi.Field(FieldIdx).Float() > vj.Field(FieldIdx).Float()
				} else {
					return vi.Field(FieldIdx).Int() > vj.Field(FieldIdx).Int()
				}
			})
		}
	}
}

func OrderUserResult(result []*User, order, by string) {
	var name string
	var FieldIdx int
	switch by {
	case "close":
		name = "Close"
	case "open":
		name = "Open"
	case "point":
		name = "Point"
	case "close_rate":
		name = "CloseRate"
	default:
		name = "Close"
	}
	if len(result) > 1 {
		v := reflect.Indirect(reflect.ValueOf(result[0]))
		t := reflect.TypeOf(result[0]).Elem()
		for i := 0; i < v.NumField(); i++ {
			// fmt.Printf("Field:%d \t type:%T \t name: %s\t value:%v\n",
			// 	i, v.Field(i), t.Field(i).Name, v.Field(i))
			if t.Field(i).Name == name {
				FieldIdx = i
			}
		}
		switch order {
		case "desc":
			sort.Slice(result, func(i, j int) bool {
				vi := reflect.Indirect(reflect.ValueOf(result[i]))
				vj := reflect.Indirect(reflect.ValueOf(result[j]))
				if name != "CloseRate" {
					return vi.Field(FieldIdx).Int() > vj.Field(FieldIdx).Int()
				} else {
					return vi.Field(FieldIdx).Float() > vj.Field(FieldIdx).Float()
				}

			})
		case "asc":
			sort.Slice(result, func(i, j int) bool {
				vi := reflect.Indirect(reflect.ValueOf(result[i]))
				vj := reflect.Indirect(reflect.ValueOf(result[j]))
				if name != "CloseRate" {
					return vi.Field(FieldIdx).Int() < vj.Field(FieldIdx).Int()
				} else {
					return vi.Field(FieldIdx).Float() < vj.Field(FieldIdx).Float()
				}
			})
		default:
			sort.Slice(result, func(i, j int) bool {
				vi := reflect.Indirect(reflect.ValueOf(result[i]))
				vj := reflect.Indirect(reflect.ValueOf(result[j]))
				if name != "CloseRate" {
					return vi.Field(FieldIdx).Int() > vj.Field(FieldIdx).Int()
				} else {
					return vi.Field(FieldIdx).Float() > vj.Field(FieldIdx).Float()
				}
			})
		}
	}
}
func GetTime(timeStr, _type string) (time.Time, error) {
	var t time.Time
	timeDate, err := time.Parse("200601", timeStr)
	if err != nil {
		return time.Now(), err
	}
	switch _type {
	case "start":
		t = timeDate.AddDate(0, -1, 24)
	case "end":
		t = timeDate.AddDate(0, 0, 24)
	default:
		return time.Now(), errors.New("unkown type")
	}

	return t, nil
}

func DashboardProblem(ctx iris.Context) {
	// id, err := dao.GetAuthId(ctx)
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	var result = map[string]interface{}{}
	var _result interface{}
	scope := ctx.FormValue("scope")
	order := ctx.FormValue("order")
	by := ctx.FormValue("by")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")

	if len(start) == 0 {
		start = time.Now().Format("200601")
	}
	if len(end) == 0 {
		end = time.Now().Format("200601")
	}
	// start, err := GetTime(_start, "start")
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// }
	// end, err := GetTime(_end, "end")
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// }

	switch scope {
	case "group":
		_result = GroupProblemSummary()
		OrderGroupResult(_result.([]*Department), order, by)
	case "person":
		_result = PersonProblemSummary()
		OrderUserResult(_result.([]*User), order, by)
	default:
		_result = GroupProblemSummary()
		OrderGroupResult(_result.([]*Department), order, by)
	}
	result["group"] = _result
	result["total"] = AllProblemSummary()
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

type count struct {
	Status    uint
	Total     int
	CloseRate float64 `gorm:"-" json:"close_rate"`
}

type totalCount struct {
	Open      int     `gorm:"-" json:"open"`
	Close     int     `gorm:"-" json:"close"`
	Total     int     `gorm:"-" json:"total"`
	CloseRate float64 `gorm:"-" json:"close_rate"`
}

type personCount struct {
	OwnerID uint
	Total   int
}

type userPoint struct {
	ID           uint    `json:"id"`
	Name         string  `json:"name"`
	Point        int     `json:"point"`
	AveragePoint float64 `json:"average_point"`
}

type User struct {
	ID        uint    `json:"id"`
	Name      string  `json:"name"`
	Open      int     `gorm:"-" json:"open"`
	Close     int     `gorm:"-" json:"close"`
	Total     int     `gorm:"-" json:"total"`
	Point     int     `gorm:"-" json:"point"`
	CloseRate float64 `gorm:"-" json:"close_rate"`
}

type Department struct {
	ID           uint    `json:"id"`
	Name         string  `json:"name"`
	Link         string  `json:"link"`
	ParentID     string  `json:"parent_id"`
	ChildIDs     []uint  `gorm:"-" json:"child_ids"`
	Open         int     `gorm:"-" json:"open"`
	Close        int     `gorm:"-" json:"close"`
	Total        int     `gorm:"-" json:"total"`
	Point        int     `gorm:"-" json:"point"`
	AveragePoint float64 `gorm:"-" json:"average_point"`
	CloseRate    float64 `gorm:"-" json:"close_rate"`
	MemberCounts int     `gorm:"-" json:"member_counts"`
}

type DepartmentSummary struct {
	ID           uint `json:"id"`
	MemberCounts int  `json:"member_counts"`
}

type UserDepartment struct {
	UserID     uint `json:"user_id"`
	Department uint `json:"department_id"`
}

func AllProblemSummary() totalCount {
	c := []*count{}
	result := totalCount{}
	db := easygorm.GetEasyGormDb().Model(dproblem.Model())
	rows, err := db.Select("status, count(*) as total").Group("status").Rows()
	if err != nil {
		logging.ErrorLogger.Error(err)
	}
	for rows.Next() {
		db.ScanRows(rows, &c)
	}
	rows.Close()
	for _, i := range c {
		if i.Status == 0 {
			result.Open = i.Total
		} else {
			result.Close = i.Total
		}
	}
	result.Total = result.Open + result.Close
	result.CloseRate = libs.FloatRound((float64(result.Close)/float64(result.Total))*100, 2)
	return result
}

func GroupProblemSummary() []*Department {

	db := easygorm.GetEasyGormDb().Model(dproblem.Model())
	departments := []*Department{}
	err := easygorm.GetEasyGormDb().Model(ddepartment.Model()).Find(&departments).Error
	if err != nil {
		logging.ErrorLogger.Error(err)
	}
	departmentIdMap := map[string][]uint{}
	departmentMap := map[uint]*Department{}
	// 遍历生成部门包含关系映射
	for _, department := range departments {

		linkArrary := strings.Split(department.Link, "/")
		for i := 0; i < len(linkArrary); i++ {
			if _, ok := departmentIdMap[strings.Join(linkArrary[0:i+1], "/")]; ok {
				departmentIdMap[strings.Join(linkArrary[0:i+1], "/")] = append(departmentIdMap[strings.Join(linkArrary[0:i+1], "/")], department.ID)
			} else {
				departmentIdMap[strings.Join(linkArrary[0:i+1], "/")] = []uint{department.ID}
			}
		}
		departmentMap[department.ID] = department
	}
	for departmentId := range departmentMap {
		openCount := personCount{}
		closeCount := personCount{}
		departmentMap[departmentId].ChildIDs = departmentIdMap[departmentMap[departmentId].Link]
		// err := easygorm.GetEasyGormDb().Model(duserdepartment.Model()).Where("department_id in ?", departmentMap[departmentId].ChildIDs).Pluck("user_id", &userIds)
		// if err != nil {
		// 	logging.ErrorLogger.Error(err)
		// }
		for key := range statusMap {
			rows, err := easygorm.GetEasyGormDb().Model(dproblem.Model()).Where("department_id in ? and status = ?", departmentMap[departmentId].ChildIDs, key).Select("count(*) as total").Rows()
			if err != nil {
				logging.ErrorLogger.Error(err)
			}
			for rows.Next() {
				if key == 0 {
					db.ScanRows(rows, &openCount)
				} else {
					db.ScanRows(rows, &closeCount)
				}
			}
			rows.Close()
		}
		departmentMap[departmentId].Open = openCount.Total
		departmentMap[departmentId].Close = closeCount.Total
		departmentMap[departmentId].Total = openCount.Total + closeCount.Total
		if departmentMap[departmentId].Total != 0 {
			departmentMap[departmentId].CloseRate = libs.FloatRound((float64(departmentMap[departmentId].Close)/float64(departmentMap[departmentId].Total))*100, 2)
		} else {
			departmentMap[departmentId].CloseRate = 100
		}
	}

	return filterGroup(departments)
}

func filterGroup(items []*Department) []*Department {
	result := []*Department{}
	_map := map[uint]*Department{}
	f := []string{"安全产品事业部/研发三部", "安全产品事业部/应软开发组", "安全产品事业部/测试部", "安全产品事业部/架构部", "安全产品事业部/品质部"}
	for _, item := range items {
		flag := false
		if item.Name == "其他" {
			continue
		}
		for _, _f := range f {
			if strings.HasPrefix(item.Link, _f) && item.Link != _f {
				flag = true
				break
			}
		}
		if flag {
			continue
		}
		result = append(result, item)

	}
	for _, group := range _map {
		result = append(result, group)
	}
	return result
}

func PersonProblemSummary() []*User {
	db := easygorm.GetEasyGormDb().Model(dproblem.Model())
	result := []*User{}
	openCount := []*personCount{}
	closeCount := []*personCount{}
	for key := range statusMap {
		rows, err := easygorm.GetEasyGormDb().Model(dproblem.Model()).Where("status = ?", key).Select("owner_id, count(*) as total").Group("owner_id").Rows()
		if err != nil {
			logging.ErrorLogger.Error(err)
		}
		for rows.Next() {
			if key == 0 {
				db.ScanRows(rows, &openCount)
			} else {
				db.ScanRows(rows, &closeCount)
			}
		}
		rows.Close()
	}
	users := []*User{}
	err := easygorm.GetEasyGormDb().Model(duser.Model()).Find(&users)
	if err != nil {
		logging.ErrorLogger.Error(err)
	}
	userMap := map[uint]*User{}
	for _, user := range users {
		userMap[user.ID] = user
	}
	for _, i := range openCount {
		userMap[i.OwnerID].Open = userMap[i.OwnerID].Open + i.Total
		userMap[i.OwnerID].Total = userMap[i.OwnerID].Total + i.Total
	}
	for _, i := range closeCount {
		userMap[i.OwnerID].Close = userMap[i.OwnerID].Close + i.Total
		userMap[i.OwnerID].Total = userMap[i.OwnerID].Total + i.Total
	}

	for _, user := range userMap {
		if user.Total != 0 {
			user.CloseRate = libs.FloatRound((float64(user.Close)/float64(user.Total))*100, 2)
		} else {
			user.CloseRate = 100
		}
		result = append(result, user)
	}
	return result
}

func DashboardContribution(ctx iris.Context) {
	// id, err := dao.GetAuthId(ctx)
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	var result interface{}
	scope := ctx.FormValue("scope")
	order := ctx.FormValue("order")
	by := ctx.FormValue("by")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")

	if len(start) == 0 {
		start = time.Now().Format("200601")
	}
	if len(end) == 0 {
		end = time.Now().Format("200601")
	}
	switch scope {
	case "all":
		result = AllContributionSummary()
	case "group":
		result = GroupContributionSummary(start, end)
		OrderGroupResult(result.([]*Department), order, by)
	case "person":
		result = PersonContributionSummary(start, end)
		OrderUserResult(result.([]*User), order, by)
	default:
		result = AllContributionSummary()
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func AllContributionSummary() totalCount {
	c := []*count{}
	result := totalCount{}
	ids := []uint{}
	err := easygorm.GetEasyGormDb().Model(dcontributionversion.Model()).Select("contribution_id").Pluck("id", &ids).Error
	if err != nil {
		logging.ErrorLogger.Error(err)
	}
	rows, err := easygorm.GetEasyGormDb().Model(dcontribution.Model()).Where("id in ?", ids).Select("status, count(*) as total").Group("status").Rows()
	if err != nil {
		logging.ErrorLogger.Error(err)
	}
	for rows.Next() {
		easygorm.GetEasyGormDb().Model(dcontribution.Model()).ScanRows(rows, &c)
	}
	rows.Close()
	for _, i := range c {
		if i.Status == 0 {
			result.Open = i.Total
		} else {
			result.Close = i.Total
		}
	}
	result.Total = result.Open + result.Close
	result.CloseRate = libs.FloatRound((float64(result.Close)/float64(result.Total))*100, 2)
	return result
}

func GroupContributionSummary(start, end string) []*Department {
	userIds := []uint{}
	err := easygorm.GetEasyGormDb().Model(duser.Model()).Where("enable = 1").Pluck("id", &userIds).Error
	if err != nil {
		logging.ErrorLogger.Error(err)
	}

	departmentSummary := []*DepartmentSummary{}
	// err = easygorm.GetEasyGormDb().Model(duserdepartment.Model()).Where("user_id in ?", userIds).Find(&departmentSummary).Error
	// if err != nil {
	// 	logging.ErrorLogger.Error(err)
	// }
	rows, err := easygorm.GetEasyGormDb().Model(duserdepartment.Model()).Select("department_id as id, count(*) as member_counts").Group("department_id").Rows()
	if err != nil {
		logging.ErrorLogger.Error(err)
	}
	for rows.Next() {
		easygorm.GetEasyGormDb().Model(duserdepartment.Model()).ScanRows(rows, &departmentSummary)
	}
	rows.Close()
	departmentSummaryMap := map[uint]*DepartmentSummary{}
	for _, ds := range departmentSummary {
		departmentSummaryMap[ds.ID] = ds
	}
	departments := []*Department{}
	err = easygorm.GetEasyGormDb().Model(ddepartment.Model()).Find(&departments).Error
	if err != nil {
		logging.ErrorLogger.Error(err)
	}

	departmentIdMap := map[string][]uint{}
	departmentMap := map[uint]*Department{}
	// 遍历生成部门包含关系映射
	for _, department := range departments {

		linkArrary := strings.Split(department.Link, "/")
		for i := 0; i < len(linkArrary); i++ {
			if _, ok := departmentIdMap[strings.Join(linkArrary[0:i+1], "/")]; ok {
				departmentIdMap[strings.Join(linkArrary[0:i+1], "/")] = append(departmentIdMap[strings.Join(linkArrary[0:i+1], "/")], department.ID)
			} else {
				departmentIdMap[strings.Join(linkArrary[0:i+1], "/")] = []uint{department.ID}
			}
		}
		departmentMap[department.ID] = department
	}
	ids := []uint{}
	err = easygorm.GetEasyGormDb().Model(dcontributionversion.Model()).Select("contribution_id").Pluck("id", &ids).Error
	if err != nil {
		logging.ErrorLogger.Error(err)
	}

	closeIds := []uint{}
	err = easygorm.GetEasyGormDb().Model(dcontribution.Model()).Where("status = 1 and id in ? and contributed_at >= ? and contributed_at <= ?", ids, start, end).Select("id").Pluck("id", &closeIds).Error
	if err != nil {
		logging.ErrorLogger.Error(err)
	}

	for departmentId := range departmentMap {
		departmentMap[departmentId].ChildIDs = departmentIdMap[departmentMap[departmentId].Link]
		point := userPoint{}
		rows, err := easygorm.GetEasyGormDb().Model(dcontributionpoint.Model()).Where("contribution_id in ? and department_id in ?", closeIds, departmentMap[departmentId].ChildIDs).Select("sum(point) as point, avg(point) as average_point").Rows()
		if err != nil {
			logging.ErrorLogger.Error(err)
		}
		for rows.Next() {
			easygorm.GetEasyGormDb().Model(dcontributionpoint.Model()).ScanRows(rows, &point)
		}
		rows.Close()
		departmentMap[departmentId].Point = point.Point
		memberCounts := 0
		for _, val := range departmentMap[departmentId].ChildIDs {
			if _, ok := departmentSummaryMap[val]; ok {
				memberCounts = memberCounts + departmentSummaryMap[val].MemberCounts
			}
		}
		departmentMap[departmentId].MemberCounts = memberCounts
		if memberCounts != 0 {
			departmentMap[departmentId].AveragePoint = libs.FloatRound(float64(departmentMap[departmentId].Point)/float64(departmentMap[departmentId].MemberCounts), 2)
		}

	}
	return filterGroup(departments)
}

func PersonContributionSummary(start, end string) []*User {
	userIds := []uint{}
	err := easygorm.GetEasyGormDb().Model(duser.Model()).Where("enable = 1").Pluck("id", &userIds).Error
	if err != nil {
		logging.ErrorLogger.Error(err)
	}
	result := []*User{}
	ids := []uint{}
	err = easygorm.GetEasyGormDb().Model(dcontributionversion.Model()).Select("contribution_id").Pluck("id", &ids).Error
	if err != nil {
		logging.ErrorLogger.Error(err)
	}
	closeIds := []uint{}
	err = easygorm.GetEasyGormDb().Model(dcontribution.Model()).Where("status = 1 and id in ? and contributed_at >= ? and contributed_at <= ?", ids, start, end).Select("id").Pluck("id", &closeIds).Error
	if err != nil {
		logging.ErrorLogger.Error(err)
	}
	points := []*userPoint{}
	rows, err := easygorm.GetEasyGormDb().Model(dcontributionpoint.Model()).Where("contribution_id in ? and contributor_id in ?", closeIds, userIds).Select("contributor_id as id, sum(point) as point").Group("contributor_id").Rows()
	if err != nil {
		logging.ErrorLogger.Error(err)
	}
	for rows.Next() {
		easygorm.GetEasyGormDb().Model(dcontributionpoint.Model()).ScanRows(rows, &points)
	}
	rows.Close()

	users := []*User{}
	err = easygorm.GetEasyGormDb().Model(duser.Model()).Find(&users).Error
	if err != nil {
		logging.ErrorLogger.Error(err)
	}
	userMap := map[uint]*User{}
	for _, user := range users {
		userMap[user.ID] = user
	}

	for _, i := range points {
		userMap[i.ID].Point = i.Point
	}
	for _, user := range userMap {
		result = append(result, user)
	}
	return result
}

func DashboardUser(ctx iris.Context) {
	userId, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ids := []uint{}
	err = easygorm.GetEasyGormDb().Model(dcontributionversion.Model()).Select("contribution_id").Pluck("id", &ids).Error
	if err != nil {
		logging.ErrorLogger.Error(err)
	}
	closeIds := []uint{}
	err = easygorm.GetEasyGormDb().Model(dcontribution.Model()).Where("id in ? and status = 1", ids).Select("id").Pluck("id", &closeIds).Error
	if err != nil {
		logging.ErrorLogger.Error(err)
	}
	point := userPoint{}
	rows, err := easygorm.GetEasyGormDb().Model(dcontributionpoint.Model()).Where("contribution_id in ? and contributor_id = ?", closeIds, userId).Select("contributor_id as id, sum(point) as point, avg(point) as average_point").Group("contributor_id").Rows()
	if err != nil {
		logging.ErrorLogger.Error(err)
	}
	for rows.Next() {
		easygorm.GetEasyGormDb().Model(dcontributionpoint.Model()).ScanRows(rows, &point)
	}
	rows.Close()

	user := User{}
	err = easygorm.GetEasyGormDb().Model(duser.Model()).Where("id = ?", userId).Find(&user).Error
	if err != nil {
		logging.ErrorLogger.Error(err)
	}

	user.Point = point.Point

	openCount := personCount{}
	closeCount := personCount{}
	for key := range statusMap {
		rows, err := easygorm.GetEasyGormDb().Model(dproblem.Model()).Where("status = ? and owner_id = ?", key, userId).Select("owner_id, count(*) as total").Group("owner_id").Rows()
		if err != nil {
			logging.ErrorLogger.Error(err)
		}
		for rows.Next() {
			if key == 0 {
				easygorm.GetEasyGormDb().ScanRows(rows, &openCount)
			} else {
				easygorm.GetEasyGormDb().ScanRows(rows, &closeCount)
			}
		}
		rows.Close()
	}

	user.Open = openCount.Total
	user.Close = closeCount.Total
	user.Total = openCount.Total + closeCount.Total
	if user.Total != 0 {
		user.CloseRate = libs.FloatRound((float64(user.Close)/float64(user.Total))*100, 2)
	} else {
		user.CloseRate = 100
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, user, response.NoErr.Msg))
	return
}
