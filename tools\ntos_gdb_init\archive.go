package main

import (
	"fmt"
	"irisAdminApi/service/dao/release/drelease"
	"strings"
)

type archiveDReleasResult struct {
	Code uint               `json:"code"`
	Data archiveReleaseData `json:"data"`
}

type archiveReleaseData struct {
	Items []*drelease.ListResponse `json:"items"`
}

func (result *archiveDReleasResult) GetDownloadUrl(output, product, softnum, softversion, branch, releaseAttrID string) (string, error) {
	var errMsg map[string]interface{}
	var downloadUrl string

	request := client.
		SetOutputDirectory(output).
		R().                       // Use R() to create a request and set with chainable request settings.
		SetSuccessResult(&result). // Unmarshal response into struct automatically if status code >= 200 and <= 299.
		SetErrorResult(&errMsg)    // Unmarshal response into struct automatically if status code >= 400.
	// EnableDump()        // Enable dump at request level to help troubleshoot, log content only when an unexpected exception occurs.
	request.SetQueryParam("pageSize", "-1")
	if softversion != "" {
		request.SetQueryParam("softversion", softversion)
	}

	if releaseAttrID != "" {
		request.SetQueryParam("release_attr_id", releaseAttrID)
	}

	resp, err := request.Get(fmt.Sprintf("%s/release-api/api/v1/release/releases/archived", buildFarmServer))

	if err != nil {
		fmt.Println("查询编译接口报错", err)
		return downloadUrl, err
	}
	// fmt.Println(resp.String())
	if resp.IsSuccessState() {
		items := result.Data.Items
		for _, item := range items {
			fmt.Println(strings.ToLower(item.ProductModel.Name), strings.ToLower(product))
			fmt.Println(strings.ToLower(item.BuildName.Name), strings.ToLower(product))
			if strings.EqualFold(strings.ToLower(item.BuildName.Name), strings.ToLower(product)) || strings.HasSuffix(strings.ToLower(item.ProductModel.Name), strings.ToLower(product)) {
				downloadUrl = item.BuildFarmLink
				return downloadUrl, nil
			}
		}
	}
	return downloadUrl, nil
}
