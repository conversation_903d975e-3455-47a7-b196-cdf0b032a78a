package buildfarm

import (
	"encoding/json"
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/buildfarm/dpatchjob"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

type SubPatchInfo struct {
	FileName      string   `json:"file_name"`
	Version       string   `json:"version"`
	UpgradeType   uint     `json:"upgrade_type"`
	ProductModels []string `json:"product_models"`
	SoftVersions  []string `json:"soft_versions"`
	HardVersions  []string `json:"hard_versions"`
	PatchObjPath  string   `json:"patch_obj_path"`
}

type Primary struct {
	FileName    string `json:"file_name"`
	FileSize    uint   `json:"file_size"`
	FileMd5     string `json:"file_md5"`
	ReleaseDate string `json:"release_date"`
	FileDesc    string `json:"file_desc"`
	FileType    uint   `json:"file_type"`
}
type Extend struct {
	PatchDescZh   string         `json:"patch_desc_zh"`
	PatchDescEn   string         `json:"patch_desc_en"`
	Version       string         `json:"version"`
	PatchType     uint           `json:"patch_type"`
	ProductModels []string       `json:"product_models"`
	SoftVersions  []string       `json:"soft_versions"`
	HardVersions  []string       `json:"hard_versions"`
	SubPatchInfos []SubPatchInfo `json:"sub_patch_infos"`
}

type UpdateConfig struct {
	Primary Primary `json:"primary"`
	Extend  Extend  `json:"extend"`
}

// 创建update.json配置文件
func createConfigFileV3(patchjob *dpatchjob.Response, rmpFileName string) error {
	archivePath := filepath.Join(libs.Config.PatchFileStorage.ArchivePath, patchjob.TaskId) //保存路径
	//文件信息保存
	rpmFilePath := filepath.Join(archivePath, rmpFileName)
	fileInfo, err := os.Stat(rpmFilePath)
	if err != nil {
		logging.ErrorLogger.Errorf("file err ", err)
	}
	md5, err := libs.GetFileMd5(filepath.Join(rpmFilePath)) //MD5值
	if err != nil {
		logging.ErrorLogger.Errorf("GetFileMd5 err: %v", err)
		return err
	}
	daoErr := patchjob.Update(patchjob.Id, map[string]interface{}{
		"RPMFileSize": fileInfo.Size(),
		"RPMFileMd5":  md5,
	})
	if daoErr != nil {
		logging.ErrorLogger.Errorf("save rpm md5 update patch job err ", daoErr)
		return daoErr
	}
	// 构建子项信息数据
	subPatchInfos, err := buildSubPatchInfos(patchjob)
	if err != nil {
		return err
	}
	//构建配置对象
	primary, extend := buildConfigParts(patchjob, rmpFileName, fileInfo, md5, subPatchInfos)
	updateConfig := UpdateConfig{
		Primary: primary,
		Extend:  extend,
	}
	//创建json文件
	if err := writeToJSONFile(archivePath, updateConfig); err != nil {
		return err
	}
	//要压缩成一个zip的多个文件的路径
	jsonFilePath := filepath.Join(archivePath, "update.json")
	files := []string{filepath.Join(archivePath, rmpFileName), jsonFilePath}
	ZipFileName := strings.TrimSuffix(rmpFileName, ".MIX")
	//设置输出的zip的路径
	ZipFilePath := filepath.Join(archivePath, ZipFileName+".zip")
	if err := ZipFiles(ZipFilePath, files); err != nil {
		logging.ErrorLogger.Errorf("Create ZipFile failed", err.Error())
		return err
	}
	return nil
}

// 解析补丁项数据集，构建依赖图和计算入度
func buildGraph(subitems []*dpatchjob.Response) (map[string][]string, map[string]int) {
	graph := make(map[string][]string)
	inDegree := make(map[string]int)
	// 初始化入度为0
	for _, subitem := range subitems {
		inDegree[strconv.Itoa(int(subitem.Id))] = 0
	}

	// 构建反向依赖图和计算入度
	for _, subitem := range subitems {
		if subitem.DependencyIDs != "" {
			for _, dep := range strings.Split(subitem.DependencyIDs, ",") {
				if dep != "" {
					graph[dep] = append(graph[dep], strconv.Itoa(int(subitem.Id)))
					inDegree[strconv.Itoa(int(subitem.Id))]++
				}
			}
		}
	}

	return graph, inDegree
}

// 拓扑排序
func topologicalSort(graph map[string][]string, inDegree map[string]int) []string {
	var queue []string
	var result []string

	// 找到所有入度为0的节点
	for node, degree := range inDegree {
		if degree == 0 {
			queue = append(queue, node)
		}
	}

	// 进行排序
	for len(queue) > 0 {
		node := queue[0]
		queue = queue[1:]
		result = append(result, node)

		for _, adj := range graph[node] {
			inDegree[adj]--

			if inDegree[adj] == 0 {
				queue = append(queue, adj)
			}
		}
	}
	return result
}

func getSortedRPMFileNames(tSortResult []string, subitems []*dpatchjob.Response) []string {
	sortedRPMFileNames := make([]string, len(tSortResult))
	for i, id := range tSortResult {
		for _, subitem := range subitems {
			if strconv.Itoa(int(subitem.Id)) == id {
				sortedRPMFileNames[i] = subitem.RPMFileName
				break
			}
		}
	}
	return sortedRPMFileNames
}

func PreparePatchArchive(patchSubitemList []*dpatchjob.Response, upload, tempName, tempDir string) (string, string, error) {
	// 创建临时目录并设置权限
	tmp := tempDir + tempName + "/"
	if err := os.MkdirAll(tmp, 0750); err != nil {
		return "", "", fmt.Errorf("failed to create temporary directory: %w", err)
	}
	if err := os.Chmod(tmp, 0750); err != nil {
		return "", "", fmt.Errorf("failed to set permissions on temporary directory: %w", err)
	}

	// 复制单补丁文件到临时目录
	for _, item := range patchSubitemList {
		cmd := fmt.Sprintf(`find %s -name "%s"|xargs -I {} cp {} %s`,
			filepath.Join(libs.Config.PatchFileStorage.ArchivePath, item.TaskId),
			item.RPMFileName,
			tmp)
		output, err := libs.ExecCommand(cmd)
		if err != nil {
			return "", "", fmt.Errorf("error while copying file: %v, output: %s", err, output)
		}
	}
	// 验证临时目录中是否有文件
	files, err := os.ReadDir(tmp)
	if err != nil {
		return "", "", fmt.Errorf("failed to read temporary directory: %w", err)
	}
	if len(files) == 0 {
		return "", "", fmt.Errorf("no files found in temporary directory")
	}

	// 打包临时目录内容为.tar文件
	fileName := tempName + ".tar"
	fp := filepath.Join(upload, fileName)
	cmd := fmt.Sprintf(`cd %s && tar cvf %s ./`, tmp, fp)
	output, err := libs.ExecCommand(cmd)
	if err != nil {
		return "", "", fmt.Errorf("error while creating tar archive: %v, output: %s", err, output)
	}
	// 获取打包后文件的信息
	fi, err := os.Stat(fp)
	if err != nil {
		return "", "", fmt.Errorf("error while getting file info: %v", err)
	}
	fileSize := strconv.FormatInt(fi.Size(), 10)
	// 计算文件MD5值
	fileMd5, err := libs.GetFileMd5(fp)
	if err != nil {
		return "", "", fmt.Errorf("error while calculating file MD5: %v", err)
	}
	return fileSize, fileMd5, nil
}

// 构建子项数据
func buildSubPatchInfos(patchjob *dpatchjob.Response) ([]SubPatchInfo, error) {
	subPatchInfos := make([]SubPatchInfo, 0)
	fmt.Println("patchjob.Subitems:", patchjob.Subitems, patchjob.Id, len(patchjob.SubitemIDs))
	for _, subitem := range patchjob.Subitems {
		spInfo := SubPatchInfo{}
		spInfo.FileName = subitem.RPMFileName
		spInfo.Version = strconv.Itoa(int(subitem.SerialNumber))
		spInfo.UpgradeType = subitem.PatchUpgradeType

		models := strings.Split(subitem.AdapterModel, "|")
		versions := strings.Split(subitem.SoftwareVersion, "|")
		hardware := strings.Split(subitem.AdapterHardware, "|")
		spInfo.ProductModels = models
		spInfo.SoftVersions = versions
		spInfo.HardVersions = hardware
		spInfo.PatchObjPath = subitem.PatchObjectPath
		subPatchInfos = append(subPatchInfos, spInfo)
	}
	return subPatchInfos, nil
}

// 构建配置文件对象
func buildConfigParts(patchjob *dpatchjob.Response, rmpFileName string, fileInfo os.FileInfo, md5 string, subPatchInfos []SubPatchInfo) (Primary, Extend) {
	// 优先使用自定义发布日期，否则使用创建时间
	var releaseDate string
	if patchjob.CustomReleaseDate != nil && *patchjob.CustomReleaseDate != "" {
		releaseDate = *patchjob.CustomReleaseDate
	} else {
		releaseDate = patchjob.CreatedAt.Format("2006-01-02")
	}

	primary := Primary{
		FileName:    rmpFileName,
		FileSize:    uint(fileInfo.Size()),
		FileMd5:     md5,
		ReleaseDate: releaseDate,
		FileDesc:    strings.Replace(patchjob.PatchFileDesc, `\n`, "\n", -1),
		FileType:    1,
	}

	extend := Extend{
		PatchDescZh:   patchjob.PatchFileDesc,
		PatchDescEn:   patchjob.PatchFileDescEN,
		Version:       fmt.Sprintf("%d", patchjob.SerialNumber),
		PatchType:     patchjob.PatchType,
		ProductModels: strings.Split(patchjob.AdapterModel, "|"),
		SoftVersions:  strings.Split(patchjob.SoftwareVersion, "|"),
		HardVersions:  []string{},
		SubPatchInfos: subPatchInfos,
	}
	return primary, extend
}

func writeToJSONFile(archivePath string, updateConfig UpdateConfig) error {
	filePath := filepath.Join(archivePath, "update.json")
	filePtr, err := os.Create(filePath)
	if err != nil {
		logging.ErrorLogger.Errorf("Create file failed: %v", err.Error())
		return err
	}
	defer filePtr.Close()
	encoder := json.NewEncoder(filePtr)
	encoder.SetEscapeHTML(false) // 设置为false以避免转义HTML字符
	if err := encoder.Encode(updateConfig); err != nil {
		return err
	}
	return nil
}

func validateDateFormat(dateStr string) error {
	if dateStr == "" {
		return nil
	}
	// 验证日期格式和有效性
	_, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return fmt.Errorf("日期格式错误，请使用YYYY-MM-DD格式，例如：2024-01-15")
	}
	return nil
}
