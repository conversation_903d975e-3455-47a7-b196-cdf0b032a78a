package coredump

import (
	"context"
	"encoding/json"
	"fmt"
	"irisAdminApi/application/controllers/featurerelease"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/coredump/dcoredumpjob"
	"irisAdminApi/service/dao/coredump/dcoredumptechsupport"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/segmentio/kafka-go"
)

// 开发环境消息监听
func ReadByReaderFromDev() {
	for {
		func() {
			// 创建 Reader
			r := kafka.NewReader(kafka.ReaderConfig{
				Brokers:   []string{"10.51.134.132:9092"},
				Topic:     "coredump_info",
				Partition: 0,
				MaxBytes:  10e6, // 10MB
			})
			defer func() {
				// 程序退出前关闭 Reader
				if err := r.Close(); err != nil {
					fmt.Println("failed to close reader:", err.Error())
					logging.ErrorLogger.Errorf("failed to close reader: %s", err.Error())
				}
			}()
			r.<PERSON>et(kafka.FirstOffset) // 设置 Offset

			// 接收消息
			for {
				m, err := r.ReadMessage(context.Background())
				if err != nil {
					fmt.Println("failed to read message:", err.Error())
					logging.ErrorLogger.Errorf("failed to read message: %s", err.Error())
					break
				}
				webhookURL := "https://open.feishu.cn/open-apis/bot/v2/hook/8784310a-2b6a-4030-9e16-4a8765cf0cc7"
				secret := "P3ZIiMpNxMM6QxCHirPutd"
				fmt.Printf("message at offset %d: %s = %s\n", m.Offset, string(m.Key), string(m.Value))
				err = ProcessCoreDump(m, "https://dev.secloud.ruijie.com.cn/", webhookURL, secret)
				if err != nil {
					coreDumpInfo, _ := decodeJSON(m.Value)
					// 失败消息通知
					sendNotification(webhookURL, secret, coreDumpInfo, []CoredumpItem{}, false, err.Error())
					fmt.Println("处理消息失败:", err.Error())
					logging.ErrorLogger.Errorf("处理消息失败: %s", err.Error())
				}
			}
			// 休眠一段时间后重试
			time.Sleep(10 * time.Second)
		}()
	}
}

// 环境配置
type EnvironmentConfig struct {
	Environment   string
	Brokers       []string
	URL           string
	WebhookURL    string
	WebhookSecret string
}

// 返回所有环境的配置
func getConfig() []EnvironmentConfig {
	return []EnvironmentConfig{
		{
			Environment:   "development",
			Brokers:       []string{"10.51.134.132:9092"},
			URL:           "https://dev.secloud.ruijie.com.cn/",
			WebhookURL:    "https://open.feishu.cn/open-apis/bot/v2/hook/8784310a-2b6a-4030-9e16-4a8765cf0cc7",
			WebhookSecret: "P3ZIiMpNxMM6QxCHirPutd",
		},
		{
			Environment:   "testing",
			Brokers:       []string{"47.98.229.111:9093"},
			URL:           "https://secloud0.ruijie.com.cn/",
			WebhookURL:    "https://open.feishu.cn/open-apis/bot/v2/hook/8784310a-2b6a-4030-9e16-4a8765cf0cc7",
			WebhookSecret: "P3ZIiMpNxMM6QxCHirPutd",
		},
		{
			Environment:   "production",
			Brokers:       []string{"121.41.9.85:9093", "116.62.200.64:9093", "8.139.7.249:9093"},
			URL:           "https://secloud1.ruijie.com.cn/",
			WebhookURL:    "https://open.feishu.cn/open-apis/bot/v2/hook/07042a45-9c55-4055-9ce9-4caf3de2f0ba",
			WebhookSecret: "ppb8IGce0vm1b4FEDJYaYb",
		},
	}
}

func readMessages(config EnvironmentConfig) {
	for {
		func() {
			// 创建 Reader
			r := kafka.NewReader(kafka.ReaderConfig{
				Brokers:   config.Brokers,
				Topic:     "coredump_info",
				Partition: 0,
				MaxBytes:  10e6, // 10MB
			})
			defer func() {
				// 程序退出前关闭 Reader
				if err := r.Close(); err != nil {
					fmt.Println("failed to close reader:", err.Error())
					logging.ErrorLogger.Errorf("failed to close reader: %s", err.Error())
				}
			}()
			r.SetOffset(kafka.FirstOffset) // 设置 Offset

			// 接收消息
			for {
				m, err := r.ReadMessage(context.Background())
				if err != nil {
					fmt.Printf("failed to read message in %s environment: %s\n", config.Environment, err.Error())
					logging.ErrorLogger.Errorf("failed to read message in %s environment: %s", config.Environment, err.Error())
					break
				}
				fmt.Printf("message at offset %d in %s environment: %s = %s\n", m.Offset, config.Environment, string(m.Key), string(m.Value))
				err = ProcessCoreDump(m, config.URL, config.WebhookURL, config.WebhookSecret)
				if err != nil {
					coreDumpInfo, _ := decodeJSON(m.Value)
					// 失败消息通知
					sendNotification(config.WebhookURL, config.WebhookSecret, coreDumpInfo, []CoredumpItem{}, false, err.Error())
					fmt.Printf("处理消息失败 in %s environment: %s\n", config.Environment, err.Error())
					logging.ErrorLogger.Errorf("处理消息失败 in %s environment: %s", config.Environment, err.Error())
				}
			}

			// 休眠一段时间后重试
			time.Sleep(10 * time.Second)
		}()
	}
}
func RunReadKafkaMessage() {
	configs := getConfig()
	for _, config := range configs {
		go readMessages(config)
	}
}

func ProcessCoreDump(m kafka.Message, securityUrl, webhookURL, secret string) error {
	items := []CoredumpItem{}
	// 解码 JSON
	coreDumpInfo, err := decodeJSON(m.Value)
	if err != nil {
		return err
	}

	// 获取下载链接
	downloadUrl, err := getDownloadUrl(coreDumpInfo, securityUrl)
	if err != nil {
		logging.ErrorLogger.Errorf("get download url err ", err)
		return err
	}
	if downloadUrl == "" {
		logging.ErrorLogger.Errorf("download url is empty")
		return fmt.Errorf("从安全云获取下载链接获取失败,下载链接为空")
	}

	// 创建目录
	upload, tempName, err := createDirectory()
	if err != nil {
		return err
	}

	// 下载文件到本地
	err = DownloadFile(downloadUrl, filepath.Join(upload, coreDumpInfo.FileName))
	if err != nil {
		logging.ErrorLogger.Errorf("download file err ", err)
		return fmt.Errorf("下载文件失败,下载地址："+downloadUrl, err)
	}

	// 获取文件 md5
	fileMd5, err := getFileMd5(filepath.Join(upload, coreDumpInfo.FileName))
	if err != nil {
		return err
	}

	// 检查文件是否存在
	exists, err := checkFileExistence(coreDumpInfo.FileId, fileMd5)
	if err != nil {
		return err
	}
	if exists {
		logging.ErrorLogger.Errorf("文件已存在")
		return nil //已存在文件跳过
	}
	// 创建记录
	product := ""
	err = createRecord(coreDumpInfo, coreDumpInfo.FileName, fileMd5, upload, tempName, product)
	if err != nil {
		return err
	}

	errChan := make(chan error, 1)
	go func() {
		// 构建沙盒环境
		techSupportFile := filepath.Join(upload, coreDumpInfo.FileName)
		err = InitSandBox(techSupportFile, product, tempName)
		if err != nil {
			errChan <- err
			return
		}

		// 获取 coreDump 文件列表
		techSupport := dcoredumptechsupport.Response{}
		err = techSupport.FindByJobID(tempName)
		if err != nil {
			logging.ErrorLogger.Errorf("find techsupport file err ", err)
			errChan <- err
			return
		}
		if techSupport.ID == 0 {
			logging.ErrorLogger.Errorf("techsupport file not found ")
			errChan <- fmt.Errorf("techsupport file not found")
			return
		}
		if techSupport.Status != 1 {
			logging.ErrorLogger.Errorf("sandbox status not ok")
			errChan <- fmt.Errorf("sandbox status not ok")
			return
		}

		fileName := strings.TrimSuffix(filepath.Base(techSupportFile), filepath.Ext(techSupportFile))
		workCoreDumpDir := libs.Config.CoreDump.DecryptedDir + fileName + "_" + tempName + "/temp/tech_support/coredump"
		// 验证目录是否存在
		if _, err := os.Stat(workCoreDumpDir); os.IsNotExist(err) {
			logging.ErrorLogger.Errorf("workCoreDumpDir not exist")
			errChan <- err
			return
		}
		output, err := ListCoreDumpFiles(&techSupport)
		if err != nil {
			logging.ErrorLogger.Errorf("get user get err ", err)
			errChan <- err
			return
		}
		coredumps := strings.Split(strings.TrimSuffix(output, "\n"), " ")

		for _, coredump := range coredumps {
			// 解压 .gz 文件
			if strings.HasSuffix(coredump, ".gz") {
				shell := filepath.Join(libs.Config.CoreDump.Plugin, "unzip_coredump.sh")
				command := fmt.Sprintf("bash %s %s %s %s %s", shell, libs.Config.CoreDump.Username, libs.Config.CoreDump.Host, libs.Config.CoreDump.Port, coredump)

				if _, err := libs.ExecCommand(command); err != nil {
					errChan <- err
					return
				}
				coredump = strings.TrimSuffix(coredump, ".gz")
			}
			// 提取执行文件路径
			exeProgramPath, err := extractExecFilePath(&techSupport, coredump)
			if err != nil {
				errChan <- err
				return
			}
			items = append(items, CoredumpItem{
				Name: filepath.Base(coredump),
				Path: exeProgramPath,
			})
			// 收集指定堆栈
			if err := collectStack(&techSupport, coredump, exeProgramPath); err != nil {
				errChan <- err
				return
			}
		}

		// 发送通知
		err = sendNotification(webhookURL, secret, coreDumpInfo, items, true, "")
		if err != nil {
			errChan <- err
			return
		}
		errChan <- nil
	}()

	if err := <-errChan; err != nil {
		return err
	}
	return nil
}

func decodeJSON(value []byte) (CoreDumpInfo, error) {
	var coreDumpInfo CoreDumpInfo
	err := json.Unmarshal(value, &coreDumpInfo)
	if err != nil {
		logging.ErrorLogger.Errorf("Error unmarshalling JSON: %v", err)
		return coreDumpInfo, err
	}
	return coreDumpInfo, nil
}

func getDownloadUrl(coreDumpInfo CoreDumpInfo, securityUrl string) (string, error) {
	downloadInfo, err := featurerelease.SecCloudClient.GetDownloadUrl(securityUrl, coreDumpInfo.SN, strconv.FormatInt(coreDumpInfo.FileId, 10))
	if err != nil {
		logging.ErrorLogger.Errorf("get download url err ", err)
		return "", err
	}
	if downloadInfo.Code != 0 {
		logging.ErrorLogger.Errorf("get download url err ", err)
		return "", fmt.Errorf("从安全云获取下载链接失败:" + downloadInfo.Msg)
	}
	return downloadInfo.Data, nil
}

func createDirectory() (string, string, error) {
	tempName := libs.GetUniqueID()
	if libs.Config.CoreDump.Upload == "" {
		logging.ErrorLogger.Errorf("未配置工作目录，请联系管理员")
		return "", "", fmt.Errorf("未配置工作目录，请联系管理员")
	}
	upload := filepath.Join(libs.Config.CoreDump.Upload, time.Now().Format("20060102"), tempName)
	err := os.MkdirAll(upload, 0750)
	if err != nil {
		logging.ErrorLogger.Errorf("创建目录失败：%s", err.Error())
		return "", "", err
	}
	os.Chmod(upload, 0750)
	return upload, tempName, nil
}

func getFileMd5(filePath string) (string, error) {
	fileMd5, err := libs.GetFileMd5(filePath)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		return "", err
	}
	return fileMd5, nil
}

func checkFileExistence(fileId int64, fileMd5 string) (bool, error) {
	coredumpFind := dcoredumptechsupport.Response{}
	err := coredumpFind.FindByFileIDAndMD5(fileId, fileMd5)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		return false, err
	}
	return coredumpFind.ID > 0, nil
}

func createRecord(coreDumpInfo CoreDumpInfo, fileName, fileMd5, upload, tempName, product string) error {
	Dcoredumptechsupport := dcoredumptechsupport.Response{}
	err := Dcoredumptechsupport.Create(map[string]interface{}{
		"JobID":     tempName,
		"FileName":  fileName,
		"MD5":       fileMd5,
		"TempDir":   upload,
		"CreatedAt": time.Now(),
		"UpdatedAt": time.Now(),
		"UserID":    0,
		"Product":   product,
		"SecFileID": coreDumpInfo.FileId,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("create tech support get err ", err)
		os.RemoveAll(upload)
		return err
	}
	return nil
}

func sendNotification(webhookURL, secret string, coreDumpInfo CoreDumpInfo, coredumpData []CoredumpItem, ok bool, errInfo string) error {
	message := "**💻设备sn：**" + coreDumpInfo.SN + "\n**🔢文件id：**" + strconv.Itoa(int(coreDumpInfo.FileId)) +
		"\n**🔢taskId：**" + coreDumpInfo.TaskId + "\n**🗳文件名：**" + coreDumpInfo.FileName + "\n**📝文件大小：**" + coreDumpInfo.FileSize
	msgTitle := "【coredump解析成功】文件已解析成功"
	msgTemplate := "green"
	msgURL := "http://10.51.134.126:8082/coredump/#/coredump/index?fileId=" + strconv.Itoa(int(coreDumpInfo.FileId))
	if ok {
		if len(coredumpData) > 0 {
			message += "\n**💥coredump文件列表：**\n"
			for _, coredump := range coredumpData {
				message += "  - 📄**coredump文件**:" + coredump.Name + "   - ⚙️**执行路径**:" + coredump.Path + "\n"
			}
		}
	} else {
		msgTitle = "【coredump解析失败】文件解析失败"
		msgTemplate = "red"
		message += "\n**❌coredump文件解析失败，返回信息：**" + errInfo
	}

	if err := SendCardMessage(webhookURL, secret, message, msgTitle, msgTemplate, msgURL); err != nil {
		logging.ErrorLogger.Errorf("Error sending message: %v", err)
		return err
	}
	return nil
}

func extractExecFilePath(techSupport *dcoredumptechsupport.Response, coredump string) (string, error) {
	shell := filepath.Join(libs.Config.CoreDump.Plugin, "list_coredump_program.sh")
	command := fmt.Sprintf("bash %s %s %s %s %s", shell, libs.Config.CoreDump.Username, libs.Config.CoreDump.Host, libs.Config.CoreDump.Port, coredump)
	fmt.Println(command)
	output, err := libs.ExecCommand(command)
	if err != nil {
		return "", err
	}
	outputStr := string(output)
	execFilePath, err := extractPathFromOutput(outputStr, "execfn: '")
	if err != nil {
		// 如果没有找到execfn字段，尝试使用from字段
		execFilePath, err = extractPathFromOutput(outputStr, " from '")
		if err != nil {
			logging.ErrorLogger.Errorf("could not find executable file path in output")
			//使用默认路径
			execFilePath = "/usr/bin/fp-rte"
			// return "", fmt.Errorf("could not find executable file path in output")
		}
	}
	execFilePath = filepath.Join(techSupport.RemoteWorkDir, "rootfs_debug", execFilePath)
	// 进一步校验提取到的路径是否有效
	if !filepath.IsAbs(execFilePath) {
		logging.ErrorLogger.Errorf("extracted path is not an absolute path: %s", execFilePath)
		return "", fmt.Errorf("extracted path is not an absolute path: %s", execFilePath)
	}
	return execFilePath, nil
}

func extractPathFromOutput(outputStr, prefix string) (string, error) {
	startIdx := strings.Index(outputStr, prefix)
	if startIdx == -1 {
		return "", fmt.Errorf("could not find prefix %s in output", prefix)
	}
	startIdx += len(prefix)
	endIdx := strings.Index(outputStr[startIdx:], "'")
	if endIdx == -1 {
		return "", fmt.Errorf("could not find end of prefix %s in output", prefix)
	}
	execFilePath := outputStr[startIdx : startIdx+endIdx]
	return execFilePath, nil
}

func collectStack(techSupport *dcoredumptechsupport.Response, coredump, exeProgramPath string) error {
	if libs.Config.CoreDump.Output == "" {
		logging.ErrorLogger.Errorf("未配置工作目录，请联系管理员--collectStack")
		return fmt.Errorf("未配置工作目录，请联系管理员")
	}
	var outputDir = filepath.Join(libs.Config.CoreDump.Upload, time.Now().Format("20060102"), techSupport.JobID)
	err := os.MkdirAll(outputDir, 0750)
	os.Chmod(outputDir, 0750)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		return err
	}
	jobID := libs.GetUniqueID()
	logFile := filepath.Join(outputDir, fmt.Sprintf("%s_%s_%s.log", filepath.Base(exeProgramPath), filepath.Base(coredump), jobID))
	f, err := os.OpenFile(logFile, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		logging.ErrorLogger.Errorf("create coredump job err ", err)
		return err
	}
	Dcoredumpjob := dcoredumpjob.Response{}
	err = Dcoredumpjob.Create(map[string]interface{}{
		"JobID":         jobID,
		"TechSupportID": techSupport.ID,
		"ProcessName":   filepath.Base(exeProgramPath),
		"ProcessPath":   exeProgramPath,
		"FileName":      filepath.Base(coredump),
		"FilePath":      coredump,
		"LogFile":       logFile,
		"CreatedAt":     time.Now(),
		"UpdatedAt":     time.Now(),
		"Status":        0,
	})

	if err != nil {
		logging.ErrorLogger.Errorf("create coredump job err ", err)
		return err
	}
	go func() {
		defer f.Close()
		job := dcoredumpjob.Response{}
		err = job.FindEx("job_id", jobID)
		if err != nil {
			logging.ErrorLogger.Errorf("create coredump job err ", err)
		}
		output, err := CollectBT(techSupport, exeProgramPath, coredump)
		if err != nil {
			logging.ErrorLogger.Errorf("get user get err ", err)
			job.Update(job.ID, map[string]interface{}{
				"status":     2,
				"updated_at": time.Now(),
			})
			return
		}
		_, err = f.Write([]byte(output))
		if err != nil {
			logging.ErrorLogger.Errorf("get user get err ", err)
			job.Update(job.ID, map[string]interface{}{
				"status":     2,
				"updated_at": time.Now(),
			})
			return
		}
		job.Update(job.ID, map[string]interface{}{
			"status":     1,
			"updated_at": time.Now(),
		})
	}()
	return nil
}
