package transqualitycrosscheck

import (
	"errors"
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/qualitypoint/dqualitycrosscheck"
	"irisAdminApi/service/dao/qualitypoint/dqualitycrosscheckprocdef"
	"irisAdminApi/service/dao/qualitypoint/dqualitycrosscheckprocinst"
	"irisAdminApi/service/dao/qualitypoint/dqualitycrosscheckproctask"
	"irisAdminApi/service/dao/user/duser"
	"time"

	"gorm.io/gorm"
)

func CreateCrossCheckTransaction(userID uint, crossCheckObject map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		resource := crossCheckObject["Resource"].(string)
		delete(crossCheckObject, "Resource")
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		//创建拉通排查
		if err := tx.Model(dqualitycrosscheck.Model()).Create(crossCheckObject).Error; err != nil {
			// 返回任何错误都会回滚事务
			return err
		}
		qualitycrosscheck := dqualitycrosscheck.Response{}
		if err := tx.Model(dqualitycrosscheck.Model()).Where("uuid = ?", crossCheckObject["Uuid"]).Find(&qualitycrosscheck).Error; err != nil {
			return err
		}
		if qualitycrosscheck.ID == 0 {
			return errors.New("创建拉通排查失败")
		}
		//创建事项实例
		procInstObject := map[string]interface{}{
			"Title": qualitycrosscheck.Uuid,
			// 当前节点
			"NodeID":              "start",
			"TaskID":              0,
			"StartUserID":         userID,
			"QualityCrossCheckID": qualitycrosscheck.ID,
			"ResponsiblePersonID": qualitycrosscheck.ResponsiblePersonID,
			"CreatedAt":           time.Now(),
			"UpdatedAt":           time.Now(),
			"Resource":            resource,
			"Status":              0,
		}
		if err := tx.Model(dqualitycrosscheckprocinst.Model()).Create(procInstObject).Error; err != nil {
			// 返回任何错误都会回滚事务
			return err
		}
		procInst := dqualitycrosscheckprocinst.Response{}
		if err := tx.Model(dqualitycrosscheckprocinst.Model()).Where("quality_cross_check_id = ?", qualitycrosscheck.ID).Find(&procInst).Error; err != nil {
			return err
		}
		if procInst.ID == 0 {
			return errors.New("创建拉通排查实例失败")
		}

		procTaskObjects := []map[string]interface{}{}
		defResource := procInst.Resource
		nodes, _ := dqualitycrosscheckprocdef.GetNodes(defResource)
		for _, node := range nodes {
			if node.NodeID == "start" {
				procTaskObjects = append(procTaskObjects, map[string]interface{}{
					"CreatedAt":  time.Now(),
					"UpdatedAt":  time.Now(),
					"NodeName":   node.Name,
					"NodeID":     node.NodeID,
					"PrevNodeID": node.PrevNodeID,
					"ProcInstID": procInst.ID,
					"Assignee":   userID,
					"Status":     1,
					"Flag":       true,
					"Done":       true,
				})
				break
			}
		}
		for _, node := range nodes {
			assignee := node.Assignee
			if node.Assignee == 0 {
				assignee = userID
			}
			if node.PrevNodeID == "start" {
				procTaskObjects = append(procTaskObjects, map[string]interface{}{
					"CreatedAt":  time.Now(),
					"UpdatedAt":  time.Now(),
					"NodeName":   node.Name,
					"NodeID":     node.NodeID,
					"PrevNodeID": node.PrevNodeID,
					"ProcInstID": procInst.ID,
					"Assignee":   assignee,
					"Status":     0,
					"Flag":       true,
				})
			}
		}

		if len(procTaskObjects) > 0 {
			if err := tx.Model(dqualitycrosscheckproctask.Model()).Create(procTaskObjects).Error; err != nil {
				return err
			}
		}
		//返回 nil 提交事务
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func UpdateViolationTransaction(userID, procInstID, taskID uint, featureObject map[string]interface{}, taskObject map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 查检当前处理节点是否重复处理
		task := dqualitycrosscheckproctask.Response{}
		if err := tx.Model(dqualitycrosscheckproctask.Model()).Where("id = ? and status = 0 and flag = true", taskID).Find(&task).Error; err != nil {
			return err
		}

		if task.ID == 0 {
			return errors.New("已处理任务，无法重复处理")
		}
		if task.Assignee != userID {
			return errors.New("不是当前用户的任务")
		}
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		procInst := dqualitycrosscheckprocinst.Response{}
		if err := tx.Model(dqualitycrosscheckprocinst.Model()).Where("id = ?", procInstID).Find(&procInst).Error; err != nil {
			return err
		}
		if procInst.ID == 0 {
			return errors.New("未找到拉通排查发布实例")
		}

		violation := dqualitycrosscheck.Response{}
		if err := tx.Model(dqualitycrosscheck.Model()).Where("id = ?", procInst.QualityCrossCheckID).Find(&violation).Error; err != nil {
			return err
		}
		if violation.ID == 0 {
			return errors.New("未找到拉通排查数据")
		}

		resource := procInst.Resource
		nodes, _ := dqualitycrosscheckprocdef.GetNodes(resource)
		procTaskObjects := []map[string]interface{}{}
		levelNodeIDs := []string{}
		prevTasks := []*dqualitycrosscheckproctask.Response{}
		levelTasks := []*dqualitycrosscheckproctask.Response{}
		nextTasks := []*dqualitycrosscheckproctask.Response{}

		prevNodeIDs, _ := dqualitycrosscheckprocdef.GetPrevNodeIDs(resource, taskObject["NodeID"].(string))
		nextNodeIDs, _ := dqualitycrosscheckprocdef.GetNextNodeIDs(resource, taskObject["NodeID"].(string))

		//通过nextNodeIDs的前置节点寻找同级任务节点
		for _, nextNodeID := range nextNodeIDs {
			nextNodePrevNodeIDs, _ := dqualitycrosscheckprocdef.GetPrevNodeIDs(resource, nextNodeID)
			for _, nextNodePrevNodeID := range nextNodePrevNodeIDs {
				// 同级任务去掉当前任务节点id
				if nextNodePrevNodeID != task.NodeID {
					levelNodeIDs = append(levelNodeIDs, nextNodePrevNodeID)
				}
			}
		}
		// fmt.Println(taskObject["NodeID"].(string), task.NodeID, prevNodeIDs, nextNodeIDs, _levelNodeIDs, levelNodeIDs)
		switch taskObject["Status"].(uint) {
		case 1:
			//检查是否为驳回订单，如果是驳回订单，将后续节点flag 置为false
			taskObject["Done"] = true
			// 检查前置任务是否都通过
			if len(prevNodeIDs) > 0 {
				if err := tx.Model(dqualitycrosscheckproctask.Model()).Where("node_id in ? and proc_inst_id = ? and status = 1 and flag = true", prevNodeIDs, procInst.ID).Find(&prevTasks).Error; err != nil {
					return err
				}
				if len(prevTasks) < len(prevNodeIDs) {
					return errors.New("前置节点未全部通过")
				}
			}

			// 检查同级任务是否都通过
			if err := tx.Model(dqualitycrosscheckproctask.Model()).Where("node_id in ? and proc_inst_id = ? and status = 1 and flag = true", levelNodeIDs, procInst.ID).Find(&levelTasks).Error; err != nil {
				return err
			}
			// fmt.Println(levelTasks, levelNodeIDs, len(levelNodeIDs), len(levelTasks))

			if len(levelNodeIDs) <= len(levelTasks) {
				if len(nextNodeIDs) > 0 {
					if err := tx.Model(dqualitycrosscheckproctask.Model()).Where("node_id in ? and proc_inst_id = ? and status = 0 and flag = true", nextNodeIDs, procInst.ID).Find(&nextTasks).Error; err != nil {
						return err
					}
				out:
					for _, nextNodeID := range nextNodeIDs {
						for _, nextTask := range nextTasks {
							if nextTask.NodeID == nextNodeID {
								continue out
							}
						}
						// 获取
						nextNodes, _ := dqualitycrosscheckprocdef.GetNodesByNodeID(procInst.Resource, nextNodeID)
						for _, node := range nextNodes {
							procTaskObjects = append(procTaskObjects, map[string]interface{}{
								"CreatedAt":  time.Now(),
								"NodeName":   node.Name,
								"NodeID":     node.NodeID,
								"PrevNodeID": node.PrevNodeID,
								"ProcInstID": procInst.ID,
								"Assignee":   node.Assignee,
								"Status":     0,
								"Flag":       true,
							})
						}
					}

				} else {
					featureObject["Status"] = 1
				}
			}
		case 2:
			taskObject["Flag"] = false
			prevTask := dqualitycrosscheckproctask.Response{}
			if err := tx.Model(dqualitycrosscheckproctask.Model()).Last(&prevTask, "node_id = ? and proc_inst_id = ? and status != 0 and flag = true", taskObject["NextNodeID"].(string), procInst.ID).Error; err != nil {
				return err
			}
			// 处理回退节点之后的未处理任务
			handleIDs := []string{}
			handleNodes, _ := dqualitycrosscheckprocdef.GetAfterNodes(nodes, taskObject["NextNodeID"].(string))
			for _, node := range handleNodes {
				handleIDs = append(handleIDs, node.NodeID)
			}
			handleIDs = append(handleIDs, taskObject["NodeID"].(string))
			handleIDs = append(handleIDs, taskObject["NextNodeID"].(string))
			handleIDs = UniqueStrings(handleIDs)
			if err := tx.Delete(dqualitycrosscheckproctask.Model(), "node_id = ? and proc_inst_id = ? and status = 1 and flag = true and id != ?", prevTask.NodeID, procInst.ID, taskID).Error; err != nil {
				return err
			}
			// fmt.Println(handleIDs)
			if err := tx.Model(dqualitycrosscheckproctask.Model()).Where("node_id in ? and proc_inst_id = ? and status != 0 and flag = true", handleIDs, procInst.ID).UpdateColumns(map[string]interface{}{"status": 0, "flag": false}).Error; err != nil {
				return err
			}

			//生成新的任务
			procTaskObjects = append(procTaskObjects, map[string]interface{}{
				"CreatedAt":  time.Now(),
				"NodeName":   prevTask.NodeName,
				"NodeID":     prevTask.NodeID,
				"PrevNodeID": prevTask.PrevNodeID,
				"ProcInstID": procInst.ID,
				"Assignee":   prevTask.Assignee,
				"Status":     0,
				"Flag":       true,
			})

		case 3:
			var users []*duser.ApprovalResponse
			if err := tx.Model(duser.Model()).Where("id in ?", []uint{userID, taskObject["UserID"].(uint)}).Find(&users).Error; err != nil {
				return err
			}
			var userMap = make(map[uint]*duser.ApprovalResponse)
			for _, user := range users {
				userMap[user.Id] = user
			}
			procTaskObjects = append(procTaskObjects, map[string]interface{}{
				"CreatedAt":  time.Now(),
				"NodeName":   task.NodeName,
				"NodeID":     task.NodeID,
				"PrevNodeID": task.PrevNodeID,
				"ProcInstID": procInst.ID,
				"Assignee":   taskObject["UserID"].(uint),
				"Status":     0,
				"Flag":       true,
			})
			if len(taskObject["Comment"].(string)) > 0 {
				taskObject["Comment"] = fmt.Sprintf("%s \n %s -> %s", taskObject["Comment"], userMap[userID].Name, userMap[taskObject["UserID"].(uint)].Name)
			} else {
				taskObject["Comment"] = fmt.Sprintf("%s -> %s", userMap[userID].Name, userMap[taskObject["UserID"].(uint)].Name)
			}
			//更新节点信息
			dqualitycrosscheckprocdef.UpdateAssignee(nodes, task.NodeID, taskObject["UserID"].(uint))
			nodeResource, err := dqualitycrosscheckprocdef.NodesToJSON(nodes)
			if err != nil {
				return err
			}
			// 更新流程实例的resource值
			if err := tx.Model(dqualitycrosscheckprocinst.Model()).Where("id = ?", procInst.ID).Update("resource", nodeResource).Error; err != nil {
				return err
			}

		case 4:
			featureObject["Status"] = 4
		}

		delete(taskObject, "NextNodeID")
		delete(taskObject, "UserID")

		if len(procTaskObjects) > 0 {

			//判断新建节点类型
			// fmt.Print(procTaskObjects)
			// todoTask := procTaskObjects
			switch taskObject["Status"].(uint) {
			case 1:
				//提取回滚节点
				rollBackIDs := []string{}
				for _, task := range procTaskObjects {
					rollBackIDs = append(rollBackIDs, task["NodeID"].(string))
				}
				//查找是否存在回滚节点
				rollBackTasks := []*dqualitycrosscheckproctask.Response{}
				if err := tx.Model(dqualitycrosscheckproctask.Model()).Where("node_id in ?  and proc_inst_id = ? and status = 0 and flag = 0 ", rollBackIDs, procInst.ID).Find(&rollBackTasks).Error; err != nil {
					return err
				}
				//存在的情况下,重置相关任务信息，flag置为true
				if len(rollBackTasks) > 0 {
					if err := tx.Debug().Model(dqualitycrosscheckproctask.Model()).Where("node_id in ? and proc_inst_id = ? and status = 0 and flag = 0", rollBackIDs, procInst.ID).
						UpdateColumns(map[string]interface{}{"status": 0, "flag": true}).Error; err != nil {
						return err
					}
					// 移除回滚节点
					for _, task := range rollBackTasks {
						procTaskObjects = RemoveNodeID(procTaskObjects, task.NodeID)
					}
				}
			case 3:
				//提取节点数据
				rollBackIDs := []string{}
				for _, task := range procTaskObjects {
					rollBackIDs = append(rollBackIDs, task["NodeID"].(string))
				}
			}
			if len(procTaskObjects) > 0 {
				if err := tx.Model(dqualitycrosscheckproctask.Model()).Create(procTaskObjects).Error; err != nil {
					return err
				}
			}

			// go SendMail(todoTask, production.ID, production.Urgency)
		}
		fmt.Println(taskID, taskObject)
		if err := tx.Model(dqualitycrosscheckproctask.Model()).Where("id = ?", taskID).Updates(taskObject).Error; err != nil {
			return err
		}
		// 将相同nodeid的任务置为完成
		_taskObject := map[string]interface{}{
			"NodeID":    taskObject["NodeID"],
			"UpdatedAt": time.Now(),
			"Status":    taskObject["Status"],
			"Comment":   fmt.Sprintf("无需处理"),
		}
		if err := tx.Model(dqualitycrosscheckproctask.Model()).Where(" proc_inst_id = ? and id != ? and node_id = ? and node_name != ?", procInstID, taskID, taskObject["NodeID"].(string), task.NodeName).Updates(_taskObject).Error; err != nil {
			return err
		}

		if err := tx.Model(dqualitycrosscheck.Model()).Where("id = ?", procInst.QualityCrossCheckID).UpdateColumns(featureObject).Error; err != nil {
			return err
		}

		if err := tx.Model(dqualitycrosscheckprocinst.Model()).Where("id = ?", procInst.ID).UpdateColumns(featureObject).Error; err != nil {
			return err
		}
		if featureObject["Status"] == 1 {
			allTasks := []*dqualitycrosscheckproctask.Response{}
			if err := tx.Model(dqualitycrosscheckproctask.Model()).Where("proc_inst_id = ? and status = 1 and flag = true", procInst.ID).Find(&allTasks).Error; err != nil {
				return err
			}
			// go SendMailSuccess(allTasks, production.ID, production.Urgency)
		}
		// 返回 nil 提交事务
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func SendMail(procTaskObjects []map[string]interface{}, featureID uint, urgency bool) {
	for _, procTaskObject := range procTaskObjects {
		if procTaskObject["Status"] == 0 {
			subject := fmt.Sprintf("[质量积分管理系统][下生产ID:%d][%s][待处理]", featureID, procTaskObject["NodeName"].(string))

			if urgency {
				subject = fmt.Sprintf("[紧急][质量积分管理系统][下生产ID:%d][%s][待处理]", featureID, procTaskObject["NodeName"].(string))
			}

			body := fmt.Sprintf(`%s<br><p>质量积分管理系统链接: <a href="http://10.51.134.126/productionSYS/#/prodSys/todo">http://10.51.134.126/productionSYS/#/prodSys/todo</a><p>`, subject)
			if !libs.Config.Debug {
				err := libs.SendMail([]string{fmt.Sprintf("%<EMAIL>", duser.UserMap[procTaskObject["Assignee"].(uint)].Username)}, subject, body, []string{})
				// err := libs.SendMail([]string{fmt.Sprintf("%<EMAIL>", "linjiakai")}, subject, body)
				if err != nil {
					logging.ErrorLogger.Error(err)
				}
			}
			logging.DebugLogger.Debugf("send mail", []string{fmt.Sprintf("%<EMAIL>", duser.UserMap[procTaskObject["Assignee"].(uint)].Username)}, subject, body)
		}
	}
}

func SendMailSuccess(taskObjects []*dqualitycrosscheckproctask.Response, featureID uint, urgency bool) {
	mailTo := []string{"<EMAIL>", "<EMAIL>"}
	for _, taskObject := range taskObjects {
		if !libs.InArrayS(mailTo, fmt.Sprintf("%<EMAIL>", duser.UserMap[taskObject.Assignee].Username)) {
			mailTo = append(mailTo, fmt.Sprintf("%<EMAIL>", duser.UserMap[taskObject.Assignee].Username))
		}
	}
	subject := fmt.Sprintf("[下生产管理系统][下生产库ID:%d][已发布成功]", featureID)
	if urgency {
		subject = fmt.Sprintf("[紧急][下生产管理系统][下生产库ID:%d][已发布成功]", featureID)
	}
	body := fmt.Sprintf(`%s<br><p>下生产库链接: <a href="http://10.51.134.126/productionSYS/#/prodSys/success">http://10.51.134.126/productionSYS/#/prodSys/success</a><p>`, subject)
	if !libs.Config.Debug {
		err := libs.SendMail(mailTo, subject, body, []string{})
		// err := libs.SendMail([]string{fmt.Sprintf("%<EMAIL>", "linjiakai")}, subject, body)
		if err != nil {
			logging.ErrorLogger.Error(err)
		}
	}
	logging.DebugLogger.Debugf("send mail", mailTo, subject, body)

}

func UniqueStrings(input []string) []string {
	uniqueMap := make(map[string]bool)
	var uniqueSlice []string

	for _, value := range input {
		if _, exists := uniqueMap[value]; !exists {
			uniqueMap[value] = true
			uniqueSlice = append(uniqueSlice, value)
		}
	}

	return uniqueSlice
}

func RemoveNodeID(procTaskObjects []map[string]interface{}, nodeIDToRemove string) []map[string]interface{} {
	filteredProcTaskObjects := make([]map[string]interface{}, 0)
	for _, taskObject := range procTaskObjects {
		if nodeID, ok := taskObject["NodeID"].(string); !ok || nodeID != nodeIDToRemove {
			filteredProcTaskObjects = append(filteredProcTaskObjects, taskObject)
		}
	}
	return filteredProcTaskObjects
}

func FindByNodeID(procTaskObjects []map[string]interface{}, nodeID string) map[string]interface{} {
	for _, taskObject := range procTaskObjects {
		if taskObject["NodeID"] == nodeID {
			return taskObject
		}
	}

	return nil
}
