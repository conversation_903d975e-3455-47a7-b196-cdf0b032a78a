package buildfarm

import "irisAdminApi/application/models"

type CpldBuildJob struct {
	models.ModelBase
	JobID string `gorm:"not null; type:varchar(60)" json:"job_id" form:"job_id"`

	InputFileName string `gorm:"not null; type:varchar(200)" json:"intput_file_name"`
	InputFileMD5  string `gorm:"not null; type:varchar(60)" json:"input_file_md5"`

	CronMakeJobID uint   `gorm:"not null" json:"cron_make_job_id" form:"cron_make_job_id"`
	SupportList   string `gorm:"not null; type:varchar(500)" json:"support_list" form:"support_list"`
	Version       string `gorm:"not null; type:varchar(60)" json:"version"`

	OutputFileName string `gorm:"not null; type:varchar(200)" json:"output_file_name" form:"output_file_name"`
	OutputFileMD5  string `gorm:"not null; type:varchar(200)" json:"output_file_md5" form:"output_file_md5"`

	Status uint   `gorm:"not null" json:"status"` // 3: 排队中  0: 进行中 1: 成功 2: 失败
	UserID uint   `gorm:"not null" json:"user_id"`
	App    string `gorm:"not null; type:varchar(60); default:''" json:"app"`
}
