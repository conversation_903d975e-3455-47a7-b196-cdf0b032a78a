package mergerequest

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/mergerequest/dmergerequest"
	"irisAdminApi/service/dao/mergerequest/dmergerequestaicheck"
	"irisAdminApi/service/dao/mergerequest/dmergerequestaicheckhistory"
	"irisAdminApi/service/dao/mergerequest/dmergerequestcodecheck"
	"irisAdminApi/service/dao/mergerequest/dmergerequestdiscussioncps"

	"github.com/kataras/iris/v12"
	"github.com/pkg/errors"
)

var (
	CodeCheckWorkerLock    sync.Mutex
	CodeCheckWorkerRunning sync.Map
)

func UpdateCheckWorker(mrID uint) {
	mr := dmergerequest.MergeRequest{}
	for i := 0; i < 9999; i++ {
		time.Sleep(1 * time.Second)
		value, ok := CodeCheckWorkerRunning.Load(mrID)
		if !ok {
			return
		}
		err := mr.CheckCodeCheckStatus(mrID)
		if err != nil {
			logging.ErrorLogger.Errorf("find mr get err ", err)
		} else {
			if mr.CodeCheckStatus == dmergerequest.Waiting || mr.Status == 4 || mr.Status == 3 || mr.ButtonClickStatus == 1 {
				logging.InfoLogger.Info("cancel ai check", mr.Status, mr.ButtonClickStatus, mr.TargetProject, mr.MergeRequestIID)
				(*value.(*context.CancelFunc))()
				return
			}
		}
	}
	logging.ErrorLogger.Errorf("update check worker timeout", mrID)
}

func CodeCheckWorker() {
	if CodeCheckWorkerLock.TryLock() {
		defer CodeCheckWorkerLock.Unlock()
		filterNotCheckProjects := []string{"NTOS1.0R10P6S1"}
		mrs, err := dmergerequest.FindAllCodeCheckMRs(filterNotCheckProjects)
		if err != nil {
			logging.ErrorLogger.Errorf("find code check mrs get err ", err)
			return
		}

		for _, mr := range mrs {
			bgcontext := context.Background()
			bgcontext, cancel := context.WithCancel(bgcontext)

			CodeCheckWorkerRunning.Store(mr.ID, &cancel)
			defer CodeCheckWorkerRunning.Delete(mr.ID)

			mr.CodeCheckStatus = dmergerequest.Running
			if err := mr.Update(mr.ID, map[string]interface{}{"CodeCheckStatus": mr.CodeCheckStatus}); err != nil {
				logging.ErrorLogger.Errorf("update mr CodeCheckStatus err ", err)
				continue
			}

			// 开始检查状态
			go UpdateCheckWorker(mr.ID)

			err := InitCodeCheckJob(&mr.MergeRequest, bgcontext)
			if err != nil {
				logging.ErrorLogger.Errorf("init code check job get err ", err)
				continue
			}

			workDir := fmt.Sprintf("%s/code_check_%v", libs.CWD(), mr.ID)
			defer os.RemoveAll(workDir)

			if libs.Config.MergeRequest.CodeCheckEnable {
				var start, end time.Time
				var result []string
				start, end, result, err = CodeCheckJob(&mr.MergeRequest, bgcontext)
				if err != nil {
					logging.ErrorLogger.Errorf("code check get err ", err)
					continue
				}
				codeCheck := dmergerequestcodecheck.MergeRequestCodeCheck{}
				err = codeCheck.Create(map[string]interface{}{
					"CreatedAt":      start,
					"UpdatedAt":      end,
					"MergeRequestID": mr.ID,
					"Log":            "",
					"Errors":         strings.Join(result, "\n"),
				})
				if err != nil {
					logging.ErrorLogger.Errorf("save code check get err ", err)
				}
			}

			if libs.Config.MergeRequest.AICheckEnable {
				err = AiCheckJob(&mr.MergeRequest, bgcontext)
				if err != nil {
					logging.ErrorLogger.Errorf("ai check get err ", err)
					continue
				}
			}

			mr.CodeCheckStatus = dmergerequest.Success
			if err := mr.Update(mr.ID, map[string]interface{}{"CodeCheckStatus": mr.CodeCheckStatus}); err != nil {
				logging.ErrorLogger.Errorf("update mr CodeCheckStatus err ", err)
				continue
			}
			if err != nil {
				logging.ErrorLogger.Errorf("save mr get err ", err)
			} else {
				err = SendCodeCheckMail(&mr.MergeRequest)
				if err != nil {
					logging.ErrorLogger.Errorf("send mail err ", err)
				}
			}

			CodeCheckWorkerRunning.Delete(mr.ID)
		}
	}
}

func InitCodeCheckJob(mr *dmergerequest.MergeRequest, ctx context.Context) error {
	// fmt.Println("start code check ")
	targetProjectID := mr.TargetProjectID
	targetBranch := mr.TargetBranch
	// fmt.Println("start code check ", sourceProjectID, mr.SourceBranch, targetProjectID, targetBranch)

	targetProjectDetail, err := GetGitlabProjectDetail(targetProjectID)
	if err != nil {
		return err
	}

	targetProjectUrl := targetProjectDetail.SshUrlToRepo

	// clone repo
	var command string
	workDir := fmt.Sprintf("%s/code_check_%v", libs.CWD(), mr.ID)

	command = fmt.Sprintf("rm %s -rf && git clone -b %s %s %s", workDir, targetBranch, targetProjectUrl, workDir)
	_, err = libs.ExecCommand(command)
	if err != nil {
		return err
	}

	command = fmt.Sprintf("cd %s && git fetch origin merge-requests/%d/head:mr-origin-%d && git checkout mr-origin-%d", workDir, mr.MergeRequestIID, mr.MergeRequestIID, mr.MergeRequestIID)
	_, err = libs.ExecCommand(command)
	if err != nil {
		return err
	}
	select {
	case <-ctx.Done():
		return errors.Errorf("任务取消")
	default:
	}
	return nil
}

func CodeCheckJob(mr *dmergerequest.MergeRequest, ctx context.Context) (time.Time, time.Time, []string, error) {
	// codecheck
	// codecheck需要区分类型
	result := []string{}
	start := time.Now()
	end := time.Now()
	codecheck := libs.Config.MergeRequest.CodeCheck
	workDir := fmt.Sprintf("%s/code_check_%v", libs.CWD(), mr.ID)
	sourceBranch := fmt.Sprintf("mr-origin-%d", mr.MergeRequestIID)
	targetBranch := mr.TargetBranch

	command := fmt.Sprintf(
		"cd %s && c=`git diff origin/%s...%s --name-only --diff-filter=AM |grep -E '\\.c$|\\.py$'|wc -l`; if [ $c -gt 0 ]; then git diff origin/%s...%s --name-only --diff-filter=AM |grep -E '\\.c$|\\.py$'; fi",
		workDir,
		targetBranch,
		sourceBranch,
		targetBranch,
		sourceBranch)

	logging.DebugLogger.Debugf(command)
	filelist, err := libs.ExecCommand(command)
	if err != nil {
		return start, end, result, err
	}
	logging.DebugLogger.Debugf(filelist)

	var codeCheckOutput string
	for _, fp := range strings.Split(strings.TrimSpace(filelist), "\n") {

		select {
		case <-ctx.Done():
			return start, end, result, errors.Errorf("任务取消")
		default:
		}

		if fp == "" {
			continue
		}

		command = fmt.Sprintf(
			"cd %s && %s --novim %s",
			workDir,
			codecheck,
			fp,
		)

		_codeCheckOutput, err := libs.ExecCommand(command)
		if err != nil {
			logging.ErrorLogger.Errorf("code check get err ", err)
			continue
		}

		codeCheckOutput = fmt.Sprintf("%s\n%s\n", codeCheckOutput, _codeCheckOutput)
	}

	result = append(result, strings.Split(codeCheckOutput, "\n")...)
	end = time.Now()
	return start, end, result, nil
}

func AiCheckJob(mr *dmergerequest.MergeRequest, ctx context.Context) error {
	// codecheck
	// codecheck需要区分类型

	workDir := fmt.Sprintf("%s/code_check_%v", libs.CWD(), mr.ID)
	sourceBranch := fmt.Sprintf("mr-origin-%d", mr.MergeRequestIID)
	targetBranch := mr.TargetBranch

	command := fmt.Sprintf(
		"cd %s && c=`git diff origin/%s...%s --name-only --diff-filter=AM |grep -E '\\.c$|\\.py$|\\.h$|\\.cpp$' | wc -l`; if [ $c -gt 0 ]; then git diff origin/%s...%s --name-only --diff-filter=AM |grep -E '\\.c$|\\.py$|\\.h$|\\.cpp$'; fi",
		workDir,
		targetBranch,
		sourceBranch,
		targetBranch,
		sourceBranch)

	filelist, err := libs.ExecCommand(command)
	if err != nil {
		return err
	}
	// fmt.Println("========>", filelist)
	filethreadsMap := map[string][]*Thread{}
	fileLineMap := map[string]map[int]int{}

	mrDetail, err := GetMergeRequestDetail(mr.TargetProjectID, mr.MergeRequestIID)
	if err != nil {
		return err
	}
	chunkHistories := []map[string]interface{}{}
	for _, fp := range strings.Split(strings.TrimSpace(filelist), "\n") {
		if fp == "" {
			continue
		}
		command = fmt.Sprintf(
			"cd %s && git diff -U9999 origin/%s...%s -- %s",
			workDir,
			targetBranch,
			sourceBranch,
			fp)

		diffContent, err := libs.ExecCommand(command)
		if err != nil {
			logging.ErrorLogger.Errorf("get diffcontent err ", err)
			continue
		}

		diffLines := []int{}
		blocks, lineMap := parseDiff(diffContent)
		for _, block := range blocks {
			diffLines = append(diffLines, block.ModifiedLineNums...)
		}
		fileLineMap[fp] = lineMap

		ext := filepath.Ext(fp)

		codeByte, err := os.ReadFile(filepath.Join(workDir, fp))
		if err != nil {
			logging.ErrorLogger.Errorf("read code file err ", err)
			continue
		}

		codeChunks, err := CodeSplit(ext, string(codeByte))
		if err != nil {
			logging.ErrorLogger.Errorf("code split err ", err)
			continue
		}

		for _, chunk := range codeChunks {
			select {
			case <-ctx.Done():
				return errors.Errorf("任务取消")
			default:
			}
			// 修订分词逻辑，超过10000的AI组不建议进行审查
			if len(chunk) > 10000 {
				continue
			}

			lines := strings.Split(strings.TrimSpace(chunk), "\n")
			if !CheckIfCodeChanged(lines, diffLines) {
				continue
			}
			chunkMD5 := libs.MD5(chunk)
			// todo: 增加统计数据处理， 生成AI调用统计表
			// project_id, merge_request_i_id, new_path, chunk_md5, chunk, line_count, suggestions, suggestion_count, created_at, updated_at
			// 调用AI前检查是否已经评审成功
			h := dmergerequestaicheckhistory.MergeRequestAiCheckHistory{}
			err := h.CheckChunkMD5(mr.TargetProjectID, mr.MergeRequestIID, fp, chunkMD5)
			if err == nil && h.ID > 0 {
				continue
			}
			startedAt := time.Now()

			suggestionResponse, err := CodeAnalisys(ext, chunk)
			if err != nil {
				logging.ErrorLogger.Errorf("code analysis err ", err)
				return err
			}

			filethreadsMap[fp] = append(filethreadsMap[fp], &Thread{
				Suggestions: suggestionResponse,
				Func:        lines,
			})
			endedAt := time.Now()
			// 创建统计记录
			suggestions, err := json.Marshal(suggestionResponse)
			if err != nil {
				logging.ErrorLogger.Errorf("suggestions marshal err ", err)
			} else {
				chunkHistory := map[string]interface{}{
					"created_at":         time.Now(),
					"project_id":         mr.TargetProjectID,
					"merge_request_i_id": mr.MergeRequestIID,
					"new_path":           fp,
					"chunk_md5":          chunkMD5,
					"chunk":              chunk,
					"line_count":         len(lines),
					"suggestions":        suggestions,
					"suggestion_count":   len(suggestionResponse),
					"updated_at":         time.Now(),
					"position_base_sha":  mrDetail.DiffRefs.BaseSha,
					"position_start_sha": mrDetail.DiffRefs.StartSha,
					"position_head_sha":  mrDetail.DiffRefs.HeadSha,
					"started_at":         startedAt,
					"ended_at":           endedAt,
				}
				chunkHistories = append(chunkHistories, chunkHistory)
			}
		}
	}

	select {
	case <-ctx.Done():
		return errors.Errorf("任务取消")
	default:
	}

	if len(chunkHistories) > 0 {
		err = dmergerequestaicheckhistory.BatchCreate(chunkHistories)
		if err != nil {
			logging.ErrorLogger.Errorf(err.Error())
		}
	}

	records := []map[string]interface{}{}
	for fp, threads := range filethreadsMap {
		for _, thread := range threads {
			for _, suggestion := range thread.Suggestions {
				lineContent := ""
				for _, line := range thread.Func {
					if strings.HasPrefix(line, fmt.Sprintf("<%s> ", suggestion.Line)) {
						lineContent = strings.Replace(line, fmt.Sprintf("<%s> ", suggestion.Line), "", 1)
						break
					}
				}

				if lineContent == "" {
					continue
				}
				lineContentMd5 := libs.MD5(lineContent)
				records = append(records, map[string]interface{}{
					"created_at":         time.Now(),
					"project_id":         mr.TargetProjectID,
					"merge_request_i_id": mr.MergeRequestIID,
					"new_path":           fp,
					"new_line":           suggestion.Line,
					"content_md5":        lineContentMd5,
					"content":            lineContent,
					"suggestion":         suggestion.Suggestion,
					"position_base_sha":  mrDetail.DiffRefs.BaseSha,
					"position_start_sha": mrDetail.DiffRefs.StartSha,
					"position_head_sha":  mrDetail.DiffRefs.HeadSha,
				})
			}
		}
	}

	if len(records) > 0 {
		err = dmergerequestaicheck.BatchCreate(records)
		if err != nil {
			return err
		}

		aiCheckResults, err := dmergerequestaicheck.BatchFind(mr.TargetProjectID, mr.MergeRequestIID, mrDetail.DiffRefs.BaseSha, mrDetail.DiffRefs.StartSha, mrDetail.DiffRefs.HeadSha)
		if err != nil {
			return err
		}

		for _, aiCheck := range aiCheckResults {
			if aiCheck.DiscussionID == "" {
				lineNum, err := strconv.Atoi(aiCheck.NewLine)
				if err != nil {
					logging.ErrorLogger.Errorf("ai code check line num invalid ", err)
					continue
				}

				if lineMap, ok := fileLineMap[aiCheck.NewPath]; ok {
					var data DiscussionRequest
					if oldLine, ok := lineMap[lineNum]; !ok {
						data = DiscussionRequest{
							Body: aiCheck.Suggestion,
							Position: Position{
								NewPath:      aiCheck.NewPath,
								OldPath:      aiCheck.NewPath,
								NewLine:      uint(lineNum),
								PositionType: "text",
							},
						}
					} else {
						data = DiscussionRequest{
							Body: aiCheck.Suggestion,
							Position: Position{
								NewPath:      aiCheck.NewPath,
								OldPath:      aiCheck.NewPath,
								NewLine:      uint(lineNum),
								OldLine:      uint(oldLine),
								PositionType: "text",
							},
						}
					}

					discussionID, err := CreateMergeRequestDiscussion(mrDetail, &data)
					if err != nil {
						logging.ErrorLogger.Errorf("create merge request discussion err ", err)
						continue
					}
					aiCheck.DiscussionID = discussionID
					err = aiCheck.Save()
					if err != nil {
						logging.ErrorLogger.Errorf("save merge request ai check err ", err)
						continue
					}
				}
			}
		}
	}

	return nil
}

func GetMergeRequestDetail(projectId, mergeRequestIID uint) (*MergeRequestResponse, error) {
	mergeRequestResponse := MergeRequestResponse{}
	var errMsg interface{}
	resp, err := GitlabAIClient.R().SetSuccessResult(&mergeRequestResponse).SetErrorResult(&errMsg).Get(fmt.Sprintf("projects/%d/merge_requests/%d", projectId, mergeRequestIID))
	if err != nil {
		logging.ErrorLogger.Errorf("get pipeline error", err.Error())
		return &mergeRequestResponse, err
	}
	if resp.IsSuccessState() {
		return &mergeRequestResponse, nil
	}
	return nil, fmt.Errorf("%v", errMsg)
}

func CreateMergeRequestDiscussion(mrDetail *MergeRequestResponse, data *DiscussionRequest) (string, error) {
	data.Position.BaseSha = mrDetail.DiffRefs.BaseSha
	data.Position.HeadSha = mrDetail.DiffRefs.HeadSha
	data.Position.StartSha = mrDetail.DiffRefs.StartSha

	discussionID, err := mrDetail.CreateMergeRequsetDiscussion(data)
	if err != nil {
		logging.ErrorLogger.Errorf("create merge request discussion err ", err, data)
		return discussionID, err
	}
	return discussionID, nil
}

func (r *MergeRequestResponse) CreateMergeRequsetDiscussion(body *DiscussionRequest) (string, error) {
	var errMsg interface{}
	var result DiscussionResponse
	resp, err := GitlabAIClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetBody(body).Post(fmt.Sprintf("projects/%d/merge_requests/%d/discussions", r.ProjectID, r.IID))
	if err != nil {
		logging.ErrorLogger.Errorf("get pipeline error", err.Error())
		return result.ID, err
	}
	logging.DebugLogger.Debugf(resp.String())
	if resp.IsSuccessState() {
		return result.ID, nil
	}
	return result.ID, errors.New(fmt.Sprintf("Unknow error, %s", errMsg))
}

type Thread struct {
	Suggestions []*Suggestion `json:"suggestions"`
	Func        []string      `json:"func"`
}

type AnalisysResponse struct {
	Suggestions []*Suggestion `json:"错误列表"`
}

type Suggestion struct {
	Line       string `json:"行数"`
	Suggestion string `json:"改进建议"`
}

type ProjectResponse struct {
	ID           int    `json:"id"`
	SshUrlToRepo string `json:"ssh_url_to_repo"`
}

func GetGitlabProjectDetail(id uint) (*ProjectResponse, error) {
	url := fmt.Sprintf("%s/api/%s/projects/%d?private_token=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, id, libs.Config.Gitlab.Token)
	var result *ProjectResponse
	var errMsg interface{}

	resp, err := GitlabWebClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).Get(url)
	if err != nil {
		return result, err
	}
	if resp.IsSuccessState() {
		return result, nil
	} else {
		return result, fmt.Errorf("未知错误: gitlab project id: %d %s", id, resp.String())
	}
}

func CodeCheckYangJob(mr *dmergerequest.MergeRequest) (bool, error) {
	fmt.Println("start code check yang ")
	sourceProjectID := mr.SourceProjectID
	sourceBranch := mr.SourceBranch
	targetProjectID := mr.TargetProjectID
	targetBranch := mr.TargetBranch
	fmt.Println("start code check yang", sourceProjectID, mr.SourceBranch, targetProjectID, targetBranch)
	sourceProjectDetail, err := GetGitlabProjectDetail(sourceProjectID)
	if err != nil {
		return false, err
	}
	targetProjectDetail, err := GetGitlabProjectDetail(targetProjectID)
	if err != nil {
		return false, err
	}
	sourceProjectUrl := sourceProjectDetail.SshUrlToRepo
	targetProjectUrl := targetProjectDetail.SshUrlToRepo

	// clone repo
	// codeCheckResutl := ""
	workDir := fmt.Sprintf("%s/code_yang_check_%v", libs.CWD(), libs.GetUUID())
	defer os.RemoveAll(workDir)

	command := fmt.Sprintf("rm %s -rf && git clone -b %s %s %s && cd %s && git remote add upstream %s && git fetch upstream", workDir, sourceBranch, sourceProjectUrl, workDir, workDir, targetProjectUrl)
	// codeCheckResutl = fmt.Sprintf("%s\n开始执行命令：%s\n", codeCheckResutl, command)
	_, err = libs.ExecCommand(command)
	if err != nil {
		return false, err
	}
	// codeCheckResutl = fmt.Sprintf("%s\n%s\n", codeCheckResutl, output)
	command = fmt.Sprintf(
		"cd %s && c=`git diff remotes/upstream/%s...%s --name-only --diff-filter=AM |grep '\\.yang$'|wc -l`; if [ $c -gt 0 ]; then echo 'found yang file'; else echo 'not found yang file'; fi",
		workDir,
		targetBranch,
		sourceBranch)
	// codeCheckResutl = fmt.Sprintf("%s\n开始执行命令：%s\n", codeCheckResutl, command)

	_codeCheckOutput, err := libs.ExecCommand(command)
	if err != nil {
		return false, err
	}

	codeCheckOutput := strings.ReplaceAll(_codeCheckOutput, "\n", "")
	if codeCheckOutput == "found yang file" {
		return false, nil
	}
	return true, nil
}

func GetCodeCheckErrors(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	codeCheck := dmergerequestcodecheck.MergeRequestCodeCheck{}
	err := codeCheck.FindEx("merge_request_id", fmt.Sprintf("%v", id))
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, err.Error(), response.SystemErr.Msg))
		return
	}
	ctx.WriteString(codeCheck.Errors)
	return
}

// func RunCodeCheckManual(ctx iris.Context) {
// 	id, _ := dao.GetId(ctx)
// 	mr := dmergerequest.MergeRequest{}
// 	err := mr.Find(id)
// 	if err != nil {
// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, err.Error(), response.SystemErr.Msg))
// 		return
// 	}
// 	CodeCheckJob(&mr)
// 	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
// 	return
// }

func SendCodeCheckMail(mr *dmergerequest.MergeRequest) error {
	subject := mr.Title + "[CodeCheck检查结果]"
	body := `<p>AI代码评审完成，AI代码评审意见反馈：<a href="https://ruijie.feishu.cn/share/base/form/shrcnCoRFxBQt2zJpo7qiqor1Cd">点此反馈</a></p>`
	if mr.User.Username == "" {
		return fmt.Errorf("接收人为空")
	}
	mailTo := []string{mr.User.Username + "@ruijie.com.cn"}
	cc := []string{}

	codeCheck := dmergerequestcodecheck.MergeRequestCodeCheck{}
	err := codeCheck.FindEx("merge_request_id", fmt.Sprintf("%v", mr.ID))
	if err != nil {
		body = fmt.Sprintf(`
		%s
		<p>获取CodeCheck检查结果失败！可尝试访问以下链接获取结果：<a href="%s">CodeCheck检查结果</a></p>
		`,
			body,
			fmt.Sprintf("http://10.51.135.15:9090/mergerequest-api/api/v1/mr/codecheck/%d", mr.ID))
		libs.SendMailRedis("MR表单", mailTo, subject, body, cc)
	} else {
		body = fmt.Sprintf(`%s<p>codecheck代码检查结果如下：</p>`, body)
		for _, line := range strings.Split(codeCheck.Errors, "\n") {
			body = fmt.Sprintf(`%s<p>%s</p>`, body, line)
		}
		libs.SendMailRedis("MR表单", mailTo, subject, body, cc)
	}

	return nil
}

func IgnoreAllAi(ctx iris.Context) {
	fmt.Println("---------------->1")
	userName, _ := getGitlabUserInfoByCookie(libs.Config.Gitlab.Url+"/", ctx)
	// 通过用户名获取gitlab私有token
	privateToken, _, err := GetGitlabTokenByUserName(userName)
	if err != nil {
		logging.ErrorLogger.Errorf("Error getting Gitlab token:", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": "获取用户token失败"}, response.SystemErr.Msg))
		return
	}
	fmt.Println("---------------->2")
	_url := ctx.Request().Header.Get("X-Original-Url")

	// [http://10.51.134.126/gitlab-instance-aa5fe906/Monitoring/-/merge_requests/59/ignore_all_ai
	var mergeRequestIID, project string
	endpointSlice := strings.Split(_url, "/")
	libs.ReverseSlice(endpointSlice)

	if len(endpointSlice) > 5 && endpointSlice[0] == "ignore_all_ai" {
		mergeRequestIID = endpointSlice[1]
		project = fmt.Sprintf("%s/%s", endpointSlice[5], endpointSlice[4])
	} else {
		fmt.Println("---------------->3")
		logging.ErrorLogger.Errorf("split url err ", _url)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": "split url err"}, response.SystemErr.Msg))
		return
	}

	encodeGitlabID := url.QueryEscape(project)
	page := 1
	for {
		fmt.Println("---------------->4")
		discussionsUrl := fmt.Sprintf("%s/api/%s/projects/%s/merge_requests/%s/discussions?private_token=%s&page=%d&per_page=100",
			libs.Config.Gitlab.Url,
			libs.Config.Gitlab.Version,
			encodeGitlabID,
			mergeRequestIID,
			libs.Config.Gitlab.AiToken,
			page,
		)
		fmt.Println(discussionsUrl)
		discussions := []*DiscussionResponse{}
		resp, err := GitlabWebClient.R().SetSuccessResult(&discussions).Get(discussionsUrl)
		if err != nil {
			fmt.Println("---------------->5")
			logging.ErrorLogger.Errorf("get discussions by endpoint err ", err, discussionsUrl)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": "get discussions by endpoint err: " + err.Error()}, response.SystemErr.Msg))
			return
		}
		if resp.IsSuccessState() {
			fmt.Println("---------------->6")
			fmt.Println(len(discussions))
			if len(discussions) == 0 {
				break
			}
			err := ResolveDiscussions(userName, privateToken, encodeGitlabID, mergeRequestIID, discussions)
			if err != nil {
				fmt.Println("---------------->7")
				logging.ErrorLogger.Errorf("resolve discussions by endpoint err ", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": "resolve discussions by endpoint err"}, response.SystemErr.Msg))
				return
			}
		} else {
			fmt.Println("---------------->8")
			logging.ErrorLogger.Errorf("get discussions by endpoint err ", resp.String())
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": "created discussion by endpoint err"}, response.SystemErr.Msg))
			return
		}
		page++
	}
	fmt.Println("---------------->9")
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func ResolveDiscussions(userName, privateToken, encodeGitlabID, mergeRequestIID string, discussions []*DiscussionResponse) error {
	fmt.Println("---------------->11")
	targetProjectID, err := GetGitlabProjectID(encodeGitlabID)
	if err != nil {
		fmt.Println("---------------->12")
		logging.ErrorLogger.Errorf("get project int id err ", err)
		return err
	}
	cpses := []map[string]interface{}{}
	for _, discussion := range discussions {
		if !discussion.IndividualNote &&
			len(discussion.Notes) > 0 &&
			(discussion.Notes[0].Resolved != nil && !*(discussion.Notes[0].Resolved)) &&
			(discussion.Notes[0].Author.Username == "aicodereviewer" || discussion.Notes[0].Author.Username == "buildfarm") {
			fmt.Println("---------------->13")
			discussionCps := DiscussionCpsResponse{}
			url := fmt.Sprintf("%s/api/%s/projects/%s/merge_requests/%s/discussions/%s?private_token=%s",
				libs.Config.Gitlab.Url,
				libs.Config.Gitlab.Version,
				encodeGitlabID,
				mergeRequestIID,
				discussion.ID,
				libs.Config.Gitlab.AiToken,
			)

			resp, err := GitlabWebClient.R().SetSuccessResult(&discussionCps).Get(url)
			if err != nil {
				fmt.Println("---------------->14")
				logging.ErrorLogger.Errorf("get discussion by id request err ", err, resp.String())
				return err
			}

			if resp.IsErrorState() {
				fmt.Println("---------------->15")
				logging.ErrorLogger.Errorf("get discussion by id response err ", resp.String())
				return fmt.Errorf(resp.String())
			}

			var object string

			discussionNotes := []string{}
			reviewer := ""

			for _, note := range discussionCps.Notes {
				if !note.System && (note.Type == "DiffNote" || note.Type == "DiscussionNote") {
					if len(note.Position.NewPath) > 0 && note.Position.NewLine != nil {
						object = note.Position.NewPath
						object = fmt.Sprintf("new:%s:%d", object, *note.Position.NewLine)

					} else if len(note.Position.OldPath) > 0 && note.Position.OldLine != nil {
						object = note.Position.OldPath
						object = fmt.Sprintf("old:%s:%d", object, *note.Position.OldLine)
					}

					discussionNotes = append(discussionNotes, fmt.Sprintf("%s:%s", note.Author.Username, note.Body))
					if len(reviewer) == 0 {
						reviewer = note.Author.Username
					}
				}
			}

			discussionNoteString := strings.Join(discussionNotes, "\n")

			data := map[string]interface{}{
				"CreatedAt":          time.Now(),
				"UpdatedAt":          time.Now(),
				"TargetProjectID":    targetProjectID,
				"MergeRequestIID":    mergeRequestIID,
				"Object":             object,
				"Discussion":         discussionNoteString,
				"DiscussionID":       discussionCps.ID,
				"Reviewer":           reviewer,
				"Category":           1,
				"Severity":           2,
				"Confirm":            6,
				"Introduction":       9,
				"Condition":          0,
				"Comment":            "",
				"State":              1,
				"LastChangeUsername": userName,
			}
			cpses = append(cpses, data)

			resolveUrl := fmt.Sprintf("%s/api/%s/projects/%s/merge_requests/%s/discussions/%s?private_token=%s&resolved=true",
				libs.Config.Gitlab.Url,
				libs.Config.Gitlab.Version,
				encodeGitlabID,
				mergeRequestIID,
				discussion.ID,
				privateToken,
			)
			fmt.Println(resolveUrl)
			resp, err = GitlabWebClient.R().Put(resolveUrl)
			if err != nil {
				fmt.Println(0o00000000000000000)
				return err
			}
			fmt.Println(111111111111111111)
			if resp.IsSuccessState() {
				fmt.Println(222222222222222222)
				continue
			} else {
				fmt.Println(333333333333333333)
				return err
			}
		}
	}
	fmt.Println(444444444444444444)
	discussionCps := dmergerequestdiscussioncps.MergeRequestDiscussionCps{}
	fmt.Println(len(cpses))
	err = discussionCps.BatchCreate(cpses, nil, nil)
	if err != nil {
		return err
	}
	fmt.Println(555555555555555555)
	return nil
}
