package urlpack

import (
	"errors"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
	"gorm.io/plugin/dbresolver"
)

var UrlDb *UrlDbServer

type UrlDbServer struct {
	adapter string
	conn    string
	db      *gorm.DB
}

// initGormDb
func (db *UrlDbServer) Init() error {
	var err error
	var dialector gorm.Dialector
	switch db.adapter {
	case "mysql":
		dialector = mysql.Open(db.conn)
	case "postgres":
		dialector = postgres.Open(db.conn)
	case "sqlite3":
		dialector = sqlite.Open(db.conn)
	default:
		return errors.New("not supported database adapter")
	}

	db.db, err = gorm.Open(dialector, &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			TablePrefix:   "",   // 表名前缀，`User` 的表名应该是 `t_users`
			SingularTable: true, // 使用单数表名，启用该选项，此时，`User` 的表名应该是 `t_user`
		},
	})
	if err != nil {
		return err
	}

	err = db.db.Use(
		dbresolver.Register(
			dbresolver.Config{ /* xxx */ }).
			SetConnMaxIdleTime(time.Hour).
			SetConnMaxLifetime(24 * time.Hour).
			SetMaxIdleConns(100).
			SetMaxOpenConns(200),
	)
	if err != nil {
		return err
	}

	db.db.Session(&gorm.Session{FullSaveAssociations: true, AllowGlobalUpdate: false})
	return nil
}

type UrlOutLibOut struct {
	Url string `gorm:"varchar(350)" json:"url"`
}
