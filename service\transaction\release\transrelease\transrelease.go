package transrelease

import (
	"errors"
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/service/dao/mergerequest/dmergerequestworkpackage"
	"irisAdminApi/service/dao/release/dbranch"
	"irisAdminApi/service/dao/release/dproductmodel"
	"irisAdminApi/service/dao/release/dproject"
	"irisAdminApi/service/dao/release/drelease"
	"irisAdminApi/service/dao/release/dreleasearchive"
	"irisAdminApi/service/dao/release/dreleaseodm"
	"strings"
	"time"

	"gorm.io/gorm"
)

func CreateReleaseORMArchiveTransaction(productModel string, releaseObject map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		// 返回任何错误都会回滚事务
		ProductModels := strings.Split(productModel, "|")
		releaseArchiveObjects := []map[string]interface{}{}
		for _, item := range ProductModels {
			releaseArchiveObjects = append(releaseArchiveObjects, map[string]interface{}{
				"ProjectName":           releaseObject["ProjectName"],
				"ProductModel":          item,
				"ReleaseAttrID":         releaseObject["ReleaseAttrID"],
				"ReleasedAt":            releaseObject["ReleasedAt"],
				"SystemSoftwareVersion": releaseObject["SystemSoftwareVersion"],
				"PackageName":           releaseObject["PackageName"],
				"ReleaseLink":           releaseObject["ReleaseLink"],
				"Comment":               releaseObject["Comment"],
				"UserID":                releaseObject["UserID"],
				"Uuid":                  releaseObject["Uuid"],
				"CreatedAt":             time.Now(),
				"UpdatedAt":             time.Now(),
			})
		}

		if err := tx.Model(dreleasearchive.Model()).Create(releaseArchiveObjects).Error; err != nil {
			return err
		}
		releaseodmObject := map[string]interface{}{
			"Archive": 1,
		}
		if err := tx.Model(dreleaseodm.Model()).Where("id = ?", releaseObject["ID"]).UpdateColumns(releaseodmObject).Error; err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func UpdateReleaseTransaction(releaseId uint, releaseObject map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		release := drelease.ReleaseRelease{}
		if err := tx.Model(drelease.Model()).Where("id = ?", releaseId).Find(&release).Error; err != nil {
			return err
		}

		if release.ID == 0 {
			return errors.New("版本数据不存在，无法处理")
		}
		if err := tx.Model(drelease.Model()).Where("id = ?", releaseId).UpdateColumns(releaseObject).Error; err != nil {
			return err
		}
		project := dproject.Response{}
		if err := tx.Model(dproject.Model()).Where("id = ?", release.ReleaseProjectID).Find(&project).Error; err != nil {
			return err
		}
		productmodel := dproductmodel.ReleaseProductModel{}
		if err := tx.Model(dproductmodel.Model()).Where("id = ?", release.ProductModelID).Find(&productmodel).Error; err != nil {
			return err
		}
		releaseArchiveObjects := map[string]interface{}{
			"ProjectName":           project.Name,
			"ProductModel":          productmodel.Name,
			"ReleaseAttrID":         release.ReleaseAttrID,
			"ReleasedAt":            release.ReleasedAt,
			"SystemSoftwareVersion": releaseObject["SystemSoftwareVersion"],
			"PackageName":           releaseObject["PackageName"],
			"ReleaseLink":           releaseObject["ReleaseLink"],
			"Comment":               release.Comment,
			"UserID":                release.UserID,
			"Uuid":                  release.Uuid,
			"CreatedAt":             time.Now(),
			"UpdatedAt":             time.Now(),
		}
		if err := tx.Model(dreleasearchive.Model()).Create(releaseArchiveObjects).Error; err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return err
	}
	return nil
}

func UpdateProjecMrControlStatuTransaction(releaseProjectId, status uint) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		//项目表更新
		project := dproject.Response{}
		if err := tx.Model(dproject.Model()).Where("id = ?", releaseProjectId).Find(&project).Error; err != nil {
			return err
		}

		if project.ID == 0 {
			return errors.New("项目数据不存在，无法处理")
		}
		projectObject := map[string]interface{}{
			"MrControlStatus":     status,
			"MrControlShowStatus": status,
		}
		if err := tx.Model(dproject.Model()).Where("id = ?", releaseProjectId).UpdateColumns(projectObject).Error; err != nil {
			return err
		}
		//分支表更新
		branchs := dbranch.ListResponse{}
		if err := tx.Model(dbranch.Model()).Where("release_project_id = ? and status = 1 ", releaseProjectId).Find(&branchs).Error; err != nil {
			return err
		}
		branchObject := map[string]interface{}{
			"MrControlStatus": status,
		}
		if err := tx.Model(dbranch.Model()).Where("release_project_id = ? and status = 1 ", releaseProjectId).UpdateColumns(branchObject).Error; err != nil {
			return err
		}
		//工作包表更新
		workPackages := dmergerequestworkpackage.ListResponse{}
		if err := tx.Model(dmergerequestworkpackage.Model()).Where("release_project_id = ?", releaseProjectId).Find(&workPackages).Error; err != nil {
			return err
		}
		workPackageObject := map[string]interface{}{
			"MrControlStatus": status,
		}
		if err := tx.Model(dmergerequestworkpackage.Model()).Where("release_project_id = ?", releaseProjectId).UpdateColumns(workPackageObject).Error; err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return err
	}
	return nil
}

func UpdateBranchMrControlStatuTransaction(branchId, status uint) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		//获取分支数据
		branch := dbranch.ReleaseBranch{}
		if err := tx.Model(dbranch.Model()).Where("id = ? and status = 1 ", branchId).Find(&branch).Error; err != nil {
			return err
		}
		//获取项目数据
		project := dproject.Response{}
		if err := tx.Model(dproject.Model()).Where("id = ?", branch.ReleaseProjectID).Find(&project).Error; err != nil {
			return err
		}
		//更新当前分支状态
		branchObject := map[string]interface{}{
			"MrControlStatus": status,
		}
		if err := tx.Model(dbranch.Model()).Where("id = ?  and status = 1 ", branchId).UpdateColumns(branchObject).Error; err != nil {
			return err
		}
		branchStatus := project.MrControlStatus
		//判断项目mr状态是否与当前设置状态一致，一致=>查找当前项目分支数据状态与项目不一致的状态值，没有就设置与项目mr状态值一；不一致=>当前设置状态作为分支状态
		if project.MrControlStatus == status {
			//获取项目分支数据状态与项目不一致的状态值，没有就设置与项目mr状态值一
			branchs := []*dbranch.ListResponse{}
			if err := tx.Model(dbranch.Model()).Where("release_project_id = ? and status = 1 ", branch.ReleaseProjectID).Find(&branchs).Error; err != nil {
				return err
			}
			if len(branchs) > 0 {
				for _, branch := range branchs {
					if branch.MrControlStatus != project.MrControlStatus {
						branchStatus = branch.MrControlStatus
						break
					}
				}
			}
		} else {
			branchStatus = status
		}

		//获取项目的工作包数据，查找与项目mr状态不一致的值，作为工作包状态，没有就设置与项目mr状态值一致
		workPackageStatus := project.MrControlStatus
		workPackages := []*dmergerequestworkpackage.ListResponse{}
		if err := tx.Model(dmergerequestworkpackage.Model()).Where("release_project_id = ?", branch.ReleaseProjectID).Find(&workPackages).Error; err != nil {
			return err
		}
		if len(workPackages) > 0 {
			for _, workPackage := range workPackages {
				if workPackage.MrControlStatus != project.MrControlStatus {
					workPackageStatus = workPackage.MrControlStatus
					break
				}
			}
		}
		//计算项目当前状态显示值 更新项目显示值
		mrShowStatus, _ := encodeStatus(project.MrControlStatus, branchStatus, workPackageStatus)
		//更新项目显示值
		projectObject := map[string]interface{}{
			"MrControlShowStatus": mrShowStatus,
		}
		if err := tx.Model(dproject.Model()).Where("id = ?", branch.ReleaseProjectID).UpdateColumns(projectObject).Error; err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return err
	}
	return nil
}

func UpdateWorkPackageMrControlStatuTransaction(workpackageId, status uint) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		//获取工作包数据
		workpackage := dmergerequestworkpackage.MergeRequestWorkPackage{}
		if err := tx.Model(dmergerequestworkpackage.Model()).Where("id = ? ", workpackageId).Find(&workpackage).Error; err != nil {
			return err
		}
		//获取项目数据
		project := dproject.Response{}
		if err := tx.Model(dproject.Model()).Where("id = ?", workpackage.ReleaseProjectID).Find(&project).Error; err != nil {
			return err
		}
		//更新当前工作包状态
		workPackageObject := map[string]interface{}{
			"MrControlStatus": status,
		}
		if err := tx.Model(dmergerequestworkpackage.Model()).Where("id = ?  ", workpackageId).UpdateColumns(workPackageObject).Error; err != nil {
			return err
		}
		workPackageStatus := project.MrControlStatus
		//判断项目mr状态是否与当前设置状态一致，一致=>查找当前项目工作包数据状态与项目不一致的状态值，没有就设置与项目mr状态值一；不一致=>当前设置状态作为工作包状态
		if project.MrControlStatus == status {
			//获取项目工作包数据状态与项目不一致的状态值，没有就设置与项目mr状态值一
			workPackages := []*dmergerequestworkpackage.ListResponse{}
			if err := tx.Model(dmergerequestworkpackage.Model()).Where("release_project_id = ? ", workpackage.ReleaseProjectID).Find(&workPackages).Error; err != nil {
				return err
			}
			if len(workPackages) > 0 {
				for _, workPackage := range workPackages {
					if workPackage.MrControlStatus != project.MrControlStatus {
						workPackageStatus = workPackage.MrControlStatus
						break
					}
				}
			}
		} else {
			workPackageStatus = status
		}

		//获取项目的分支数据，查找与项目mr状态不一致的值，作为分支状态，没有就设置与项目mr状态值一致
		branchStatus := project.MrControlStatus
		branchs := []*dbranch.ListResponse{}
		if err := tx.Model(dbranch.Model()).Where("release_project_id = ?", workpackage.ReleaseProjectID).Find(&branchs).Error; err != nil {
			return err
		}
		if len(branchs) > 0 {
			for _, branch := range branchs {
				if branch.MrControlStatus != project.MrControlStatus {
					branchStatus = branch.MrControlStatus
					break
				}
			}
		}
		//计算项目当前状态显示值 更新项目显示值
		mrShowStatus, _ := encodeStatus(project.MrControlStatus, branchStatus, workPackageStatus)
		//更新项目显示值
		projectObject := map[string]interface{}{
			"MrControlShowStatus": mrShowStatus,
		}
		if err := tx.Model(dproject.Model()).Where("id = ?", workpackage.ReleaseProjectID).UpdateColumns(projectObject).Error; err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return err
	}
	return nil
}

// 状态编码函数，将三个状态编码为一个三位数的编号 项目、分支、工作包
func encodeStatus(projectStatus, branchStatus, packageStatus uint) (int, string) {
	encodedStatus := fmt.Sprintf("%03d", projectStatus*100+branchStatus*10+packageStatus)
	switch encodedStatus {
	case "000":
		return 0, "打开"
	case "001":
		return 3, "存在部分工作包为关闭状态"
	case "002":
		return 4, "存在部分工作包为管控状态"
	case "010":
		return 5, "存在部分分支为关闭状态"
	case "011":
		return 6, "存在部分分支和工作包均为关闭状态"
	case "012":
		return 7, "存在部分分支为关闭状态,部分工作包为管控状态"
	case "020":
		return 8, "存在部分分支为管控状态"
	case "021":
		return 9, "存在部分分支为管控状态，部分工作包为关闭状态"
	case "022":
		return 10, "存在部分分支和工作包均为管控状态"
	case "100":
		return 11, "存在部分分支和工作包均为打开状态"
	case "101":
		return 12, "存在部分分支为打开状态"
	case "102":
		return 13, "存在部分分支为打开状态，部分工作包为管控状态"
	case "110":
		return 14, "存在部分工作包为打开状态"
	case "111":
		return 1, "关闭"
	case "112":
		return 15, "存在部分工作包为管控状态"
	case "120":
		return 16, "存在部分分支为管控状态，部分工作包为打开状态"
	case "121":
		return 17, "存在部分分支为管控状态"
	case "122":
		return 18, "存在部分分支和工作包均为管控状态 "
	case "200":
		return 19, "存在部分分支和工作包均为打开状态 "
	case "201":
		return 20, "存在部分分支为打开状态，部分工作包为关闭状态"
	case "202":
		return 21, "存在部分分支为打开状态"
	case "210":
		return 22, "存在部分分支为关闭状态，部分工作包为打开状态"
	case "211":
		return 23, "存在部分分支和工作包均为关闭状态"
	case "212":
		return 24, "存在部分分支为关闭状态"
	case "220":
		return 25, "存在部分工作包为打开状态"
	case "221":
		return 26, "存在部分工作包为关闭状态"
	case "222":
		return 2, "管控"
	// 添加其他状态组合的编码
	default:
		return 27, "状态异常"
	}
}
