package main

import (
	"fmt"
	"strings"

	"github.com/xuri/excelize/v2"
)

func main() {
	f, err := excelize.OpenFile("./1.xlsx")
	if err != nil {
		fmt.Println(err)
		return
	}
	//获取当前工作表索引
	// sheet := f.GetActiveSheetIndex()
	// fmt.Println("active sheet=", sheet)
	// // 获取单元格内容
	// cell, err := f.GetCellValue("Sheet1", "B2")
	// if err != nil {
	// 	fmt.Println(err)
	// 	return
	// }
	// fmt.Println(cell)

	rows, err := f.GetRows("Sheet1")
	// // 获取sheet1中所有的行
	// for _, row := range rows {
	// 	for _, colCell := range row {
	// 		fmt.Print(colCell, "\t")
	// 	}
	// 	fmt.Println() //第一次看到这种换行格式哈哈哈哈
	// }

	//对sheet1中的内容进行操作
	departList := map[string]string{}
	userDepartMap := map[string]string{}
	userRoleMap := map[string]string{}
	for i, row := range rows {
		//标题不操作
		if i == 0 {
			continue
		}
		id := row[0]
		name := row[1]
		post := row[2]
		departs := row[3:7]
		formation := row[9]
		mail := row[10]
		username := strings.Split(mail, "@")[0]
		if formation == "正编" {
			_departs := []string{}
			for _, depart := range departs {
				if depart != "" && !strings.Contains(depart, "本部") {
					_departs = append(_departs, depart)
				}
			}
			link := strings.Join(_departs, "/")
			role := "4"
			if strings.Contains(post, "经理") {
				role = "3"
			}
			fmt.Println(id, username, name, role, link, _departs[len(_departs)-1])
			departList[_departs[len(_departs)-1]] = link
			userDepartMap[username] = link
			userRoleMap[username] = role
		}
	}
	// for key, val := range departList {
	// 	sql := fmt.Sprintf(`insert into departments (name, link) values ("%s", "%s");`, key, val)
	// 	fmt.Println(sql)
	// }
	// for key, val := range userDepartMap {
	// 	sql := fmt.Sprintf(`insert into user_departments (user_id, department_id) select id, (select id from departments where link = '%s') from users where username = '%s';`, val, key)
	// 	fmt.Println(sql)
	// }
	for key, val := range userRoleMap {

		if val == "3" {
			sql := fmt.Sprintf(`insert into casbin_rule (ptype, v0, v1) select 'g', id, '%s' from users where username = %s;`, val, key)
			fmt.Println(sql)
		}

	}
}
