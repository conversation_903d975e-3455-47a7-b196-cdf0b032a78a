package coredump

import (
	"context"
	"fmt"
	"time"

	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkbitable "github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
	"irisAdminApi/application/logging"
)

// FilterBuilder 筛选条件构建器
type FilterBuilder struct {
	config *CoredumpConfig
}

// NewFilterBuilder 创建筛选条件构建器
func NewFilterBuilder(config *CoredumpConfig) *FilterBuilder {
	return &FilterBuilder{
		config: config,
	}
}

// BuildCoredumpFilter 构建Coredump记录筛选条件
// 实现逻辑: (是否需要同步Bug系统 = "Y") AND (是否已同步bug系统 为空 OR = "N") AND (处理状态 为空 OR = "待处理" OR = "失败")
func (fb *FilterBuilder) BuildCoredumpFilter() map[string]interface{} {
	return map[string]interface{}{
		"conjunction": "and",
		"children": []map[string]interface{}{
			// 条件组1: 是否需要同步Bug系统 = "Y"
			{
				"conjunction": "and",
				"conditions": []map[string]interface{}{
					{
						"field_name": fb.config.FieldMapping.SyncRequiredField,
						"operator":   "is",
						"value":      []string{"Y"},
					},
				},
			},
			// 条件组2: 是否已同步bug系统 为空或 = "N"
			{
				"conjunction": "or",
				"conditions": []map[string]interface{}{
					{
						"field_name": fb.config.FieldMapping.SyncStatusField,
						"operator":   "isEmpty",
						"value":      []string{},
					},
					{
						"field_name": fb.config.FieldMapping.SyncStatusField,
						"operator":   "is",
						"value":      []string{"N"},
					},
				},
			},
			// 条件组3: 处理状态 为空、待处理或失败
			{
				"conjunction": "or",
				"conditions": []map[string]interface{}{
					{
						"field_name": fb.config.FieldMapping.ProcessingStatusField,
						"operator":   "isEmpty",
						"value":      []string{},
					},
					{
						"field_name": fb.config.FieldMapping.ProcessingStatusField,
						"operator":   "is",
						"value":      []string{"待处理"},
					},
					{
						"field_name": fb.config.FieldMapping.ProcessingStatusField,
						"operator":   "is",
						"value":      []string{"失败"},
					},
				},
			},
		},
	}
}

// BuildTimeoutFilter 构建超时记录筛选条件
// 筛选处理中且超过指定时间的记录
func (fb *FilterBuilder) BuildTimeoutFilter(timeoutMinutes int) map[string]interface{} {
	// 计算超时时间点
	timeoutTime := time.Now().Add(-time.Duration(timeoutMinutes) * time.Minute)
	timeoutTimestamp := timeoutTime.UnixMilli()

	return map[string]interface{}{
		"conjunction": "and",
		"conditions": []map[string]interface{}{
			{
				"field_name": fb.config.FieldMapping.ProcessingStatusField,
				"operator":   "is",
				"value":      []string{"处理中"},
			},
			{
				"field_name": fb.config.FieldMapping.ProcessingTimeField,
				"operator":   "isLess",
				"value":      []string{fmt.Sprintf("%d", timeoutTimestamp)},
			},
		},
	}
}

// BuildCustomFilter 构建自定义筛选条件
func (fb *FilterBuilder) BuildCustomFilter(conditions []FilterCondition, conjunction string) map[string]interface{} {
	apiConditions := make([]map[string]interface{}, 0, len(conditions))

	for _, condition := range conditions {
		apiCondition := map[string]interface{}{
			"field_name": condition.FieldName,
			"operator":   condition.Operator,
			"value":      condition.Value,
		}
		apiConditions = append(apiConditions, apiCondition)
	}

	return map[string]interface{}{
		"conjunction": conjunction,
		"conditions":  apiConditions,
	}
}

// FilterCondition 筛选条件
type FilterCondition struct {
	FieldName string   `json:"field_name"`
	Operator  string   `json:"operator"`
	Value     []string `json:"value"`
}

// OptimizedCoredumpReader 优化的Coredump记录读取器
type OptimizedCoredumpReader struct {
	feishuClient  *lark.Client
	config        *CoredumpConfig
	filterBuilder *FilterBuilder
}

// NewOptimizedCoredumpReader 创建优化的记录读取器
func NewOptimizedCoredumpReader(feishuClient *lark.Client, config *CoredumpConfig) *OptimizedCoredumpReader {
	return &OptimizedCoredumpReader{
		feishuClient:  feishuClient,
		config:        config,
		filterBuilder: NewFilterBuilder(config),
	}
}

// ReadFilteredRecords 使用服务端筛选读取记录
func (r *OptimizedCoredumpReader) ReadFilteredRecords() ([]*CoredumpRecord, error) {
	logging.InfoLogger.Info("开始使用服务端筛选读取Coredump记录")
	startTime := time.Now()

	var allRecords []*CoredumpRecord
	pageToken := ""
	pageSize := r.config.PageSize
	totalApiCalls := 0

	// 构建筛选条件
	filter := r.filterBuilder.BuildCoredumpFilter()

	for {
		totalApiCalls++
		
		// 构建请求
		reqBuilder := larkbitable.NewSearchAppTableRecordReqBuilder().
			AppToken(r.config.CoredumpAppToken).
			TableId(r.config.CoredumpTableID)

		bodyBuilder := larkbitable.NewSearchAppTableRecordReqBodyBuilder().
			PageSize(pageSize).
			FieldNames(r.config.FieldNames).
			Filter(filter)

		if pageToken != "" {
			bodyBuilder.PageToken(pageToken)
		}

		req := reqBuilder.Body(bodyBuilder.Build()).Build()

		// 调用API
		resp, err := r.feishuClient.Bitable.AppTableRecord.Search(context.Background(), req)
		if err != nil {
			return nil, fmt.Errorf("调用飞书API失败 (第%d次调用): %w", totalApiCalls, err)
		}

		if !resp.Success() {
			return nil, fmt.Errorf("飞书API返回错误 (第%d次调用): %s", totalApiCalls, resp.Msg)
		}

		// 解析记录
		records, err := r.parseRecords(resp.Data.Items)
		if err != nil {
			return nil, fmt.Errorf("解析记录失败 (第%d次调用): %w", totalApiCalls, err)
		}

		allRecords = append(allRecords, records...)
		logging.DebugLogger.Debugf("第%d次API调用获取到 %d 条记录", totalApiCalls, len(records))

		// 检查是否还有更多数据
		if !resp.Data.HasMore {
			break
		}

		pageToken = *resp.Data.PageToken

		// API限流控制
		time.Sleep(100 * time.Millisecond)
	}

	duration := time.Since(startTime)
	logging.InfoLogger.Infof("服务端筛选完成: 共%d次API调用, 获取%d条记录, 耗时%v", 
		totalApiCalls, len(allRecords), duration)

	return allRecords, nil
}

// ReadTimeoutRecords 读取超时的处理中记录
func (r *OptimizedCoredumpReader) ReadTimeoutRecords() ([]*CoredumpRecord, error) {
	logging.InfoLogger.Info("查找超时的处理中记录")
	startTime := time.Now()

	// 构建超时筛选条件
	filter := r.filterBuilder.BuildTimeoutFilter(r.config.ProcessingTimeout)

	var timeoutRecords []*CoredumpRecord
	pageToken := ""
	totalApiCalls := 0

	for {
		totalApiCalls++
		
		reqBuilder := larkbitable.NewSearchAppTableRecordReqBuilder().
			AppToken(r.config.CoredumpAppToken).
			TableId(r.config.CoredumpTableID)

		bodyBuilder := larkbitable.NewSearchAppTableRecordReqBodyBuilder().
			PageSize(r.config.PageSize).
			FieldNames(r.config.FieldNames).
			Filter(filter)

		if pageToken != "" {
			bodyBuilder.PageToken(pageToken)
		}

		req := reqBuilder.Body(bodyBuilder.Build()).Build()

		resp, err := r.feishuClient.Bitable.AppTableRecord.Search(context.Background(), req)
		if err != nil {
			return nil, fmt.Errorf("查找超时记录失败: %w", err)
		}

		if !resp.Success() {
			return nil, fmt.Errorf("飞书API返回错误: %s", resp.Msg)
		}

		records, err := r.parseRecords(resp.Data.Items)
		if err != nil {
			return nil, fmt.Errorf("解析超时记录失败: %w", err)
		}

		timeoutRecords = append(timeoutRecords, records...)

		if !resp.Data.HasMore {
			break
		}

		pageToken = *resp.Data.PageToken
		time.Sleep(100 * time.Millisecond)
	}

	duration := time.Since(startTime)
	logging.InfoLogger.Infof("超时记录查找完成: 共%d次API调用, 发现%d条超时记录, 耗时%v", 
		totalApiCalls, len(timeoutRecords), duration)

	return timeoutRecords, nil
}

// ReadRecordsByCustomFilter 使用自定义筛选条件读取记录
func (r *OptimizedCoredumpReader) ReadRecordsByCustomFilter(conditions []FilterCondition, conjunction string) ([]*CoredumpRecord, error) {
	logging.InfoLogger.Infof("使用自定义筛选条件读取记录: %d个条件, 逻辑=%s", len(conditions), conjunction)

	filter := r.filterBuilder.BuildCustomFilter(conditions, conjunction)

	var records []*CoredumpRecord
	pageToken := ""

	for {
		reqBuilder := larkbitable.NewSearchAppTableRecordReqBuilder().
			AppToken(r.config.CoredumpAppToken).
			TableId(r.config.CoredumpTableID)

		bodyBuilder := larkbitable.NewSearchAppTableRecordReqBodyBuilder().
			PageSize(r.config.PageSize).
			FieldNames(r.config.FieldNames).
			Filter(filter)

		if pageToken != "" {
			bodyBuilder.PageToken(pageToken)
		}

		req := reqBuilder.Body(bodyBuilder.Build()).Build()

		resp, err := r.feishuClient.Bitable.AppTableRecord.Search(context.Background(), req)
		if err != nil {
			return nil, fmt.Errorf("自定义筛选失败: %w", err)
		}

		if !resp.Success() {
			return nil, fmt.Errorf("飞书API返回错误: %s", resp.Msg)
		}

		pageRecords, err := r.parseRecords(resp.Data.Items)
		if err != nil {
			return nil, fmt.Errorf("解析记录失败: %w", err)
		}

		records = append(records, pageRecords...)

		if !resp.Data.HasMore {
			break
		}

		pageToken = *resp.Data.PageToken
		time.Sleep(100 * time.Millisecond)
	}

	logging.InfoLogger.Infof("自定义筛选完成: 获取到 %d 条记录", len(records))
	return records, nil
}

// parseRecords 解析飞书API返回的记录数据
func (r *OptimizedCoredumpReader) parseRecords(items []*larkbitable.AppTableRecord) ([]*CoredumpRecord, error) {
	records := make([]*CoredumpRecord, 0, len(items))

	for _, item := range items {
		record, err := r.parseRecord(item)
		if err != nil {
			logging.ErrorLogger.Errorf("解析记录失败 [%s]: %v", item.RecordId, err)
			continue
		}
		records = append(records, record)
	}

	return records, nil
}

// parseRecord 解析单条记录
func (r *OptimizedCoredumpReader) parseRecord(item *larkbitable.AppTableRecord) (*CoredumpRecord, error) {
	record := &CoredumpRecord{
		RecordID: *item.RecordId,
	}

	// 解析字段数据
	fields := item.Fields
	fieldMapper := NewCoredumpFieldMapper(r.config)

	// 使用字段映射器解析各个字段
	if err := fieldMapper.MapFields(fields, record); err != nil {
		return nil, fmt.Errorf("字段映射失败: %w", err)
	}

	return record, nil
}

// OptimizedCoredumpService 优化后的Coredump服务
type OptimizedCoredumpService struct {
	feishuClient  *lark.Client
	config        *CoredumpConfig
	reader        *OptimizedCoredumpReader
	statusManager *CoredumpStatusManager
	bugSubmitter  *BugSubmitter
}

// NewOptimizedCoredumpService 创建优化的Coredump服务
func NewOptimizedCoredumpService() *OptimizedCoredumpService {
	config := LoadCoredumpConfig()
	feishuClient := openfeishu.GetFeishuClient()

	return &OptimizedCoredumpService{
		feishuClient:  feishuClient,
		config:        config,
		reader:        NewOptimizedCoredumpReader(feishuClient, config),
		statusManager: NewCoredumpStatusManager(),
		bugSubmitter:  NewBugSubmitter(),
	}
}

// ProcessCoredumpRecords 优化后的主处理方法
func (s *OptimizedCoredumpService) ProcessCoredumpRecords() (*ProcessResult, error) {
	taskID := fmt.Sprintf("task_%d", time.Now().Unix())
	logging.InfoLogger.Infof("[%s] 开始处理Coredump记录（使用服务端筛选优化）", taskID)

	result := &ProcessResult{
		TaskID:    taskID,
		StartTime: time.Now(),
	}

	// 步骤1: 使用服务端筛选直接读取符合条件的记录
	filteredRecords, err := s.reader.ReadFilteredRecords()
	if err != nil {
		return nil, fmt.Errorf("读取筛选记录失败: %w", err)
	}

	result.TotalRecords = len(filteredRecords)
	result.FilteredRecords = len(filteredRecords)
	logging.InfoLogger.Infof("[%s] 服务端筛选获取到 %d 条待处理记录", taskID, len(filteredRecords))

	// 步骤2: 查找并重置超时记录
	timeoutRecords, err := s.reader.ReadTimeoutRecords()
	if err != nil {
		logging.ErrorLogger.Errorf("[%s] 查找超时记录失败: %v", taskID, err)
	} else if len(timeoutRecords) > 0 {
		logging.InfoLogger.Infof("[%s] 发现 %d 条超时记录，正在重置", taskID, len(timeoutRecords))
		err = s.statusManager.ResetTimeoutRecords(timeoutRecords)
		if err != nil {
			logging.ErrorLogger.Errorf("[%s] 重置超时记录失败: %v", taskID, err)
		}
	}

	// 步骤3: 处理筛选后的记录
	for _, record := range filteredRecords {
		err := s.processRecord(record, result)
		if err != nil {
			logging.ErrorLogger.Errorf("[%s] 处理记录失败 [%s]: %v", taskID, record.RecordID, err)
			result.FailedRecords++

			// 更新为失败状态
			statusErr := s.statusManager.UpdateProcessingStatus(record.RecordID, StatusFailed, "", err.Error())
			if statusErr != nil {
				logging.ErrorLogger.Errorf("[%s] 更新失败状态失败 [%s]: %v", taskID, record.RecordID, statusErr)
			}
		} else {
			result.SuccessRecords++
		}
	}

	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)

	logging.InfoLogger.Infof("[%s] 处理完成: 总计=%d, 成功=%d, 失败=%d, 耗时=%v",
		taskID, result.FilteredRecords, result.SuccessRecords, result.FailedRecords, result.Duration)

	return result, nil
}

// processRecord 处理单条记录
func (s *OptimizedCoredumpService) processRecord(record *CoredumpRecord, result *ProcessResult) error {
	logging.InfoLogger.Infof("开始处理记录 [%s]: SN=%s", record.RecordID, record.SN)

	// 步骤1: 标记为处理中（如果还不是）
	if record.ProcessingStatus != StatusProcessing {
		err := s.statusManager.UpdateProcessingStatus(record.RecordID, StatusProcessing, "", "")
		if err != nil {
			logging.ErrorLogger.Errorf("更新处理状态失败 [%s]: %v", record.RecordID, err)
			// 继续处理，不因状态更新失败而中断
		}
	}

	// 步骤2: 字段映射
	bugData, err := s.mapRecordToBugData(record)
	if err != nil {
		return fmt.Errorf("字段映射失败: %w", err)
	}

	// 步骤3: 提交Bug
	bugID, err := s.bugSubmitter.SubmitBug(bugData)
	if err != nil {
		return fmt.Errorf("Bug提交失败: %w", err)
	}

	// 步骤4: 更新状态为成功
	err = s.statusManager.UpdateProcessingStatus(record.RecordID, StatusSuccess, bugID, "")
	if err != nil {
		logging.ErrorLogger.Errorf("状态更新失败 [%s]: %v", record.RecordID, err)
		// 注意：这里不返回错误，因为Bug已经提交成功
	}

	logging.InfoLogger.Infof("记录处理成功 [%s]: Bug ID=%s", record.RecordID, bugID)
	return nil
}

// mapRecordToBugData 将记录映射为Bug数据
func (s *OptimizedCoredumpService) mapRecordToBugData(record *CoredumpRecord) (*BugData, error) {
	mapper := NewCoredumpFieldMapper(s.config)
	return mapper.MapToBugData(record)
}

// GetProcessingStatistics 获取处理统计信息
func (s *OptimizedCoredumpService) GetProcessingStatistics() (*ProcessingStatistics, error) {
	logging.InfoLogger.Info("获取处理统计信息")

	// 使用自定义筛选获取各种状态的记录数量
	stats := &ProcessingStatistics{}

	// 统计待处理记录
	pendingConditions := []FilterCondition{
		{FieldName: s.config.FieldMapping.SyncRequiredField, Operator: "is", Value: []string{"Y"}},
		{FieldName: s.config.FieldMapping.ProcessingStatusField, Operator: "isEmpty", Value: []string{}},
	}
	pendingRecords, err := s.reader.ReadRecordsByCustomFilter(pendingConditions, "and")
	if err != nil {
		return nil, fmt.Errorf("统计待处理记录失败: %w", err)
	}
	stats.PendingCount = len(pendingRecords)

	// 统计处理中记录
	processingConditions := []FilterCondition{
		{FieldName: s.config.FieldMapping.ProcessingStatusField, Operator: "is", Value: []string{"处理中"}},
	}
	processingRecords, err := s.reader.ReadRecordsByCustomFilter(processingConditions, "and")
	if err != nil {
		return nil, fmt.Errorf("统计处理中记录失败: %w", err)
	}
	stats.ProcessingCount = len(processingRecords)

	// 统计成功记录
	successConditions := []FilterCondition{
		{FieldName: s.config.FieldMapping.ProcessingStatusField, Operator: "is", Value: []string{"成功"}},
	}
	successRecords, err := s.reader.ReadRecordsByCustomFilter(successConditions, "and")
	if err != nil {
		return nil, fmt.Errorf("统计成功记录失败: %w", err)
	}
	stats.SuccessCount = len(successRecords)

	// 统计失败记录
	failedConditions := []FilterCondition{
		{FieldName: s.config.FieldMapping.ProcessingStatusField, Operator: "is", Value: []string{"失败"}},
	}
	failedRecords, err := s.reader.ReadRecordsByCustomFilter(failedConditions, "and")
	if err != nil {
		return nil, fmt.Errorf("统计失败记录失败: %w", err)
	}
	stats.FailedCount = len(failedRecords)

	stats.TotalCount = stats.PendingCount + stats.ProcessingCount + stats.SuccessCount + stats.FailedCount

	logging.InfoLogger.Infof("统计完成: 总计=%d, 待处理=%d, 处理中=%d, 成功=%d, 失败=%d",
		stats.TotalCount, stats.PendingCount, stats.ProcessingCount, stats.SuccessCount, stats.FailedCount)

	return stats, nil
}

// ProcessingStatistics 处理统计信息
type ProcessingStatistics struct {
	TotalCount      int `json:"total_count"`
	PendingCount    int `json:"pending_count"`
	ProcessingCount int `json:"processing_count"`
	SuccessCount    int `json:"success_count"`
	FailedCount     int `json:"failed_count"`
}