package featurerelease

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/featurerelease/dfeature"
	"irisAdminApi/service/dao/featurerelease/dfeatureproc"
	"irisAdminApi/service/dao/featurerelease/dfeatureprocdef"
	"irisAdminApi/service/dao/featurerelease/dfeatureprocinst"
	"irisAdminApi/service/dao/featurerelease/dfeatureproctask"
	"irisAdminApi/service/dao/featurerelease/dfeatureseccloud"
	"irisAdminApi/service/dao/sig/dsigjob"
	"irisAdminApi/service/transaction/featurerelease/transfeaturerelease"

	"github.com/kataras/iris/v12"
)

func CreateProcInst(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	uuid := libs.GetUUID()
	request := &dfeature.Request{}
	if err := ctx.ReadForm(request); err != nil && !iris.IsErrPath(err) {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	// 解析 sec_cloud_configs
	var secCloudConfigs []map[string]interface{}
	if request.SecCloudConfigData != "" {
		if err := json.Unmarshal([]byte(request.SecCloudConfigData), &secCloudConfigs); err != nil {
			logging.ErrorLogger.Errorf("failed to unmarshal sec_cloud_configs: %v", err)
			ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "安全云配置格式不正确"))
			return
		}
	}

	check := dfeature.Response{}
	err = check.CheckExists(request.FileMd5, request.SecCloud)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if check.ID > 0 {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "已经存在相同文件发布记录"))
		return
	}

	f, fh, err := ctx.FormFile("file")
	defer f.Close()

	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	// 构造文件名称
	fileName := fh.Filename

	// 创建最终存放路径以及保存文件
	upload := filepath.Join(libs.Config.FeatureRelease.Upload, time.Now().Format("20060102"), request.FileMd5)
	err = os.MkdirAll(upload, 0o750)
	os.Chmod(upload, 0o750)

	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	// 创建申请单
	tempFn := filepath.Join(upload, fileName)
	_, err = ctx.SaveFormFile(fh, tempFn)
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if err := CheckSig(tempFn); err != nil {
		os.RemoveAll(upload)
		logging.ErrorLogger.Errorf("Error while Get File Md5 ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if request.FeatureBaseVersion != "" && request.IncrementName != "" {
		f, fh, err := ctx.FormFile("increment")
		defer f.Close()

		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}

		// 构造文件名称
		fileName := fh.Filename

		// 创建最终存放路径以及保存文件
		upload := filepath.Join(libs.Config.FeatureRelease.Upload, time.Now().Format("20060102"), request.IncrementMd5)
		err = os.MkdirAll(upload, 0o750)
		os.Chmod(upload, 0o750)

		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		// 创建申请单
		tempFn := filepath.Join(upload, fileName)
		_, err = ctx.SaveFormFile(fh, tempFn)
		if err != nil {
			logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		if err := CheckSig(tempFn); err != nil {
			os.RemoveAll(upload)
			logging.ErrorLogger.Errorf("Error while Get File Md5 ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
	}
	err = transfeaturerelease.CreateFeatureTransaction(id, map[string]interface{}{
		"FileName":      request.FileName,
		"FileSize":      request.FileSize,
		"FileMd5":       request.FileMd5,
		"IncrementName": request.IncrementName,
		"IncrementSize": request.IncrementSize,
		"IncrementMd5":  request.IncrementMd5,

		"Version":            request.Version,
		"FeatureType":        request.FeatureType,
		"FeatureVersions":    request.FeatureVersions,
		"FeatureBaseVersion": request.FeatureBaseVersion,
		"ProductModels":      request.ProductModels,
		"SoftVersions":       request.SoftVersions,
		"FileType":           request.FileType,
		"ReleaseDate":        request.ReleaseDate,
		"VersionDesc":        request.VersionDesc,
		"Desc":               request.Desc,
		"CreatedAt":          time.Now(),
		"UpdatedAt":          time.Now(),
		"Uuid":               uuid,
		"Resource":           request.Resource,
		"UpdateToMain":       request.UpdateToMain,
		"Urgency":            request.Urgency,
		"SecCloud":           request.SecCloud,
		"GrayRuleID":         request.GrayRuleID,
		"IsNewFormat":        1,
	}, secCloudConfigs)
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func CheckSig(tempFn string) error {
	// 检查文件MD5是否存在于加密系统表中
	md5, err := libs.GetFileMd5(tempFn)
	if err != nil {
		return err
	}
	sigJob := dsigjob.SigJob{}

	err = sigJob.FindEx("output_md5", md5)
	if err != nil {
		return err
	}

	if sigJob.ID == 0 {
		return fmt.Errorf("此文件不是由加密系统加密生成，不允许发布")
	}
	return nil
	// 检查结束
}

func GetProcDef(ctx iris.Context) {
	// name := ctx.FormValue("name")
	defs, err := dfeatureprocdef.GetDefs()
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, defs, response.NoErr.Msg))
	return
}

func GetFeatureProcInsts(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")

	procInst := dfeatureprocinst.FeatureProcInst{}
	list, err := procInst.All(name, status, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetFeatureProcTasks(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")

	list, err := dfeatureproc.AllTasksByAssignee(uId, name, sort, orderBy, page, pageSize, status, start, end)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetFeatureProcInstTasks(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")

	list, err := dfeatureproc.AllTasksByProcInst(id, name, sort, orderBy, page, pageSize, status, start, end)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetProcInst(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	info := dfeatureprocinst.FeatureProcInst{}
	err := info.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

type TaskRequest struct {
	Comment       string `form:"comment"`
	Status        uint   `form:"status"`
	NodeID        string `form:"nodeId"`
	NextNodeID    string `form:"nextNodeID"`
	NextNodeIDs   string `form:"nextNodeIDs"`
	TaskID        uint   `form:"taskId"`
	UserID        uint   `form:"userId"`
	ProductModels string `json:"product_models" form:"product_models"`
	SoftVersions  string `json:"soft_versions" form:"soft_versions"`
}

func UpdateProcInst(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	procInst := dfeatureprocinst.FeatureProcInst{}

	err = procInst.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	// uuid := libs.GetUUID()
	request := TaskRequest{}
	if err := ctx.ReadForm(&request); err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	featureObject := map[string]interface{}{
		"UpdatedAt": time.Now(),
	}

	fileName := ""
	logging.DebugLogger.Debugf("update", request.NodeID, request.Status)
	if request.NodeID == "test_check" && request.Status == 1 {
		// if libs.Config.FeatureRelease.Enable {
		// 	md5 := procInst.Feature.FileMd5
		// 	sign, err := getSign(md5)
		// 	if err != nil {
		// 		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		// 		return
		// 	}
		// 	featureObject["Sign"] = sign
		// }

		f, fh, err := ctx.FormFile("file")
		defer f.Close()

		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}

		// 构造文件名称
		fileName = fh.Filename

		// 创建最终存放路径以及保存文件
		upload := filepath.Join(libs.Config.FeatureRelease.Upload, procInst.Feature.CreatedAt.Format("20060102"), procInst.Feature.FileMd5)
		err = os.MkdirAll(upload, 0o750)
		os.Chmod(upload, 0o750)

		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		// 创建申请单
		tempFn := filepath.Join(upload, fileName)
		_, err = ctx.SaveFormFile(fh, tempFn)
		if err != nil {
			logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	}

	if request.NodeID == "experiment" && request.Status == 1 {
		f, fh, err := ctx.FormFile("file")
		defer f.Close()

		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}

		// 构造文件名称
		fileName = fh.Filename

		// 创建最终存放路径以及保存文件
		upload := filepath.Join(libs.Config.FeatureRelease.Upload, procInst.Feature.CreatedAt.Format("20060102"), procInst.Feature.FileMd5)
		err = os.MkdirAll(upload, 0o750)
		os.Chmod(upload, 0o750)

		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		// 创建申请单
		tempFn := filepath.Join(upload, fileName)
		_, err = ctx.SaveFormFile(fh, tempFn)
		if err != nil {
			logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	}

	if request.NodeID == "cmd_release" && request.Status == 1 {
		if libs.Config.FeatureRelease.Enable {
			// 创建增量描述
			files := map[uint]map[string]string{
				2: {
					"filename": procInst.Feature.FileName,
					"md5":      procInst.Feature.FileMd5,
				},
			}
			if procInst.Feature.FeatureBaseVersion != "" && procInst.Feature.IncrementName != "" {
				files = map[uint]map[string]string{
					2: {
						"filename": procInst.Feature.FileName,
						"md5":      procInst.Feature.FileMd5,
					},
					1: {
						"filename": procInst.Feature.IncrementName,
						"md5":      procInst.Feature.IncrementMd5,
					},
				}
			}
			if procInst.Feature.IsNewFormat {
				// 遍历全量和增量
				for packageType, fileInfo := range files {
					// 遍历每个安全云配置
					for _, config := range procInst.Feature.FeatureSecCloudConfigs {
						if err := uploadFileToSecCloud(&procInst, packageType, config, fileInfo); err != nil {
							logging.ErrorLogger.Errorf("所有尝试上传文件 %s 到 %s 失败: %v", fileInfo["filename"], config.SecCloud, err)
							ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, config.SecCloud+":"+err.Error()))
							return
						}
					}
				}
			} else {
				for key, info := range files {
					upload, err := CreateDescFile(&procInst, key)
					if err != nil {
						logging.ErrorLogger.Errorf("create approval get err ", err)
						ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
						return
					}
					if err := createUploadFile(upload, info["filename"]); err != nil {
						logging.ErrorLogger.Errorf("create approval get err ", err)
						ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
						return
					}
					logging.DebugLogger.Debugf("start upload", request.NodeID, request.Status)
				out:
					for _, name := range strings.Split(procInst.Feature.SecCloud, ",") {
						fn := info["filename"]
						secCloud := dfeatureseccloud.Response{}
						if err := secCloud.FindEx("name", name); err != nil {
							logging.ErrorLogger.Errorf("find sec cloud name err ", err)
							ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
							return
						}
						// 检查文件是否已经上传过了，如果已经上传过了就跳过
						resp, err := SecCloudClient.QueryFeatureVersions(secCloud.Url, fn, procInst.Feature.FeatureType, procInst.Feature.Version)
						if err != nil {
							logging.ErrorLogger.Errorf("query version err ", err, resp.Msg)
							ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()+resp.Msg))
							return
						}
						featureVesions := resp.Data.Recoreds

						for _, featureVersion := range featureVesions {
							if featureVersion.FileMd5 == info["md5"] && featureVersion.ReleaseDate == procInst.Feature.ReleaseDate {
								continue out
							}
						}

						// err = UploadV2(secCloud.Url, filepath.Join(upload, procInst.Feature.FileName), filepath.Join(upload, "featureDesc.txt"), procInst.Feature.FeatureType)
						fn = strings.TrimSuffix(info["filename"], ".zip") + "_upload.zip"
						err = UploadV4(secCloud.Url, filepath.Join(upload, fn), procInst.Feature.GrayRuleID)
						if err != nil {
							logging.ErrorLogger.Errorf("upload to sec cloud err ", err, resp.Msg)
							ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()+resp.Msg))
							return
						}
					}
				}
			}
		}
	}

	// if request.NodeID == "feature_dev" && request.Status == 1 {
	// 	featureObject["ProductModels"] = request.ProductModels
	// 	featureObject["SoftVersions"] = request.SoftVersions
	// }

	taskObject := map[string]interface{}{
		"NodeID":     request.NodeID,
		"UpdatedAt":  time.Now(),
		"Status":     request.Status,
		"NextNodeID": request.NextNodeID,
		"Comment":    request.Comment,
		"Attachment": fileName,
		"UserID":     request.UserID,
	}

	// 如果是回退操作（Status=2）且存在多个回退节点，将其添加到taskObject中
	if request.Status == 2 && request.NextNodeIDs != "" {
		// 将逗号分隔的字符串转换为字符串数组
		nodeIDs := strings.Split(request.NextNodeIDs, ",")
		// 去除空字符串和额外的空格
		var cleanNodeIDs []string
		for _, nodeID := range nodeIDs {
			trimmedNodeID := strings.TrimSpace(nodeID)
			if trimmedNodeID != "" {
				cleanNodeIDs = append(cleanNodeIDs, trimmedNodeID)
			}
		}

		if len(cleanNodeIDs) > 0 {
			taskObject["NextNodeIDs"] = cleanNodeIDs
			logging.DebugLogger.Debugf("多节点回退: %v", cleanNodeIDs)
		}
	}

	err = transfeaturerelease.UpdateFeatureTransaction(uId, id, request.TaskID, featureObject, taskObject)
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetProcPrevNodes(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	nodeID := ctx.FormValue("nodeId")
	procInst := dfeatureprocinst.FeatureProcInst{}
	err := procInst.Find(id)
	if err != nil || procInst.ID == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	resource := procInst.Resource
	nodes, err := dfeatureprocdef.GetNodes(resource)
	// var result []*dfeatureprocdef.Node
	// dfeatureprocdef.GetBeforeNodesV2(nodes, nodeID)

	result, err := dfeatureprocdef.GetBeforeNodes(nodes, nodeID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func DownloadDescFile(ctx iris.Context) {
	packageType, err := strconv.Atoi(ctx.FormValue("package_type"))
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	secCloud := ctx.FormValue("sec_cloud") // 安全云名称
	id, _ := dao.GetId(ctx)
	procInst := dfeatureprocinst.FeatureProcInst{}

	err = procInst.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	var upload string
	if procInst.Feature.IsNewFormat {
		// 获取对应安全云的配置信息
		config := dfeature.FeatureSecCloudConfig{}
		if len(procInst.Feature.FeatureSecCloudConfigs) > 0 {
			for _, configItem := range procInst.Feature.FeatureSecCloudConfigs {
				if configItem.SecCloud == secCloud {
					config = *configItem
					break
				}
			}
		}

		upload, err = CreateDescFileV2(&procInst, uint(packageType), config)
		if err != nil {
			logging.ErrorLogger.Errorf("create desc file err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	} else {
		upload, err = CreateDescFile(&procInst, uint(packageType))
		if err != nil {
			logging.ErrorLogger.Errorf("create desc file err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	}

	if packageType == 2 {
		ctx.SendFile(filepath.Join(upload, "update.json"), url.QueryEscape(procInst.Feature.FileName+"_update.json"))
	} else if packageType == 1 {
		ctx.SendFile(filepath.Join(upload, "update.json"), url.QueryEscape(procInst.Feature.IncrementName+"_update.json"))
	}

	// ctx.SendFile(filepath.Join(upload, "featureDesc.txt"), procInst.Feature.FileMd5+"_featureDesc.txt")
	// ctx.SendFile(filepath.Join(upload, "update.json"), procInst.Feature.FileMd5+"_update.json")
	return
}

func DownloadFeatureFile(ctx iris.Context) {
	packageType, err := strconv.Atoi(ctx.FormValue("package_type"))
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	id, _ := dao.GetId(ctx)
	procInst := dfeatureprocinst.FeatureProcInst{}

	err = procInst.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	var upload string
	if packageType == 2 {
		upload = filepath.Join(libs.Config.FeatureRelease.Upload, procInst.Feature.CreatedAt.Format("20060102"), procInst.Feature.FileMd5)
		ctx.SendFile(filepath.Join(upload, procInst.Feature.FileName), url.QueryEscape(procInst.Feature.FileName))
	} else if packageType == 1 {
		upload = filepath.Join(libs.Config.FeatureRelease.Upload, procInst.Feature.CreatedAt.Format("20060102"), procInst.Feature.IncrementMd5)
		ctx.SendFile(filepath.Join(upload, procInst.Feature.IncrementName), url.QueryEscape(procInst.Feature.IncrementName))
	}

	return
}

func DownloadTestReport(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	procInst := dfeatureprocinst.FeatureProcInst{}

	err := procInst.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	attachment, err := dfeatureproctask.FindAttachmentByProc(id, "test_check")
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	upload := filepath.Join(libs.Config.FeatureRelease.Upload, procInst.Feature.CreatedAt.Format("20060102"), procInst.Feature.FileMd5)

	ctx.SendFile(filepath.Join(upload, attachment), url.QueryEscape(attachment))
	return
}

func DownloadExpReport(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	procInst := dfeatureprocinst.FeatureProcInst{}

	err := procInst.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	attachment, err := dfeatureproctask.FindAttachmentByProc(id, "experiment")
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if attachment == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "没有找到文件"))
		return
	}
	upload := filepath.Join(libs.Config.FeatureRelease.Upload, procInst.Feature.CreatedAt.Format("20060102"), procInst.Feature.FileMd5)

	ctx.SendFile(filepath.Join(upload, attachment), url.QueryEscape(attachment))
	return
}

func getSign(md5 string) (string, error) {
	// url := "https://47.99.121.215/api/updateCommon/encrypt/md5"

	url := fmt.Sprintf("%s/api/updateCommon/encrypt/md5", strings.Split(libs.Config.FeatureRelease.Url, ",")[0])
	jsonStr, _ := json.Marshal(map[string]interface{}{
		"md5": md5,
	})
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	if err != nil {
		logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
		return "", err
	}
	req.Header.Add("iamfeign", "1")
	req.Header.Add("Content-Type", "application/json")
	if err != nil {
		logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
		return "", err
	}
	result, err := libs.HandlerRequest(req)
	if err != nil {
		logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
		return "", err
	}

	if result["success"].(bool) {
		sign := result["result"].(string)
		return sign, nil
	}
	return "", errors.New("获取签名失败")
}

func UploadV2(url, featureFile, descFile, featureType string) error {
	bodyBuffer := &bytes.Buffer{}
	bodyWriter := multipart.NewWriter(bodyBuffer)

	err := bodyWriter.WriteField("featureType", featureType)
	if err != nil {
		return errors.New("上传规则库文件失败")
	}
	// file1
	fileWriter1, _ := bodyWriter.CreateFormFile("featureFile", featureFile)
	file1, err := os.Open(featureFile)
	if err != nil {
		return errors.New("找不到规则库文件")
	}
	defer file1.Close()
	io.Copy(fileWriter1, file1)

	// file2
	fileWriter2, _ := bodyWriter.CreateFormFile("descFile", descFile)
	file2, err := os.Open(descFile)
	if err != nil {
		return errors.New("找不到规则库描述文件")
	}
	defer file2.Close()
	io.Copy(fileWriter2, file2)

	contentType := bodyWriter.FormDataContentType()
	bodyWriter.Close()

	url = fmt.Sprintf("%s/api/featureManager/addVersion", url)
	req, err := http.NewRequest("POST", url, bodyBuffer)
	req.Header.Add("Content-Type", contentType)
	req.Header.Add("iamfeign", "1")

	if err != nil {
		logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
		return err
	}
	result, err := libs.HandlerRequest(req)
	if err != nil {
		logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
		return err
	}
	logging.DebugLogger.Debugf("upload", result)
	if result["success"].(bool) {
		if strings.Contains(result["result"].(string), "文件转发成功") {
			return nil
		}
	}

	return errors.New("发布失败，云平台返回失败！")
}

func UploadV3(url, featureFile string) error {
	bodyBuffer := &bytes.Buffer{}
	bodyWriter := multipart.NewWriter(bodyBuffer)

	// file1
	fileWriter1, _ := bodyWriter.CreateFormFile("featureFile", featureFile)
	file1, err := os.Open(featureFile)
	if err != nil {
		return errors.New("找不到规则库文件")
	}
	defer file1.Close()
	io.Copy(fileWriter1, file1)

	contentType := bodyWriter.FormDataContentType()
	bodyWriter.Close()

	url = fmt.Sprintf("%s/api/featureManager/v1/addVersion", url)
	req, err := http.NewRequest("POST", url, bodyBuffer)
	req.Header.Add("Content-Type", contentType)
	req.Header.Add("iamfeign", "1")

	if err != nil {
		logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
		return err
	}
	result, err := libs.HandlerRequest(req)
	if err != nil {
		logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
		return err
	}
	logging.DebugLogger.Debugf("upload", result)
	if result["success"].(bool) {
		if strings.Contains(result["result"].(string), "文件转发成功") {
			return nil
		}
	}

	return errors.New("发布失败，云平台返回失败！")
}

func UploadV4(url, featureFile string, grayId ...string) error {
	_, err := os.Stat(featureFile)
	if err != nil {
		return errors.New("找不到规则库文件")
	}
	fmt.Println(url)
	if len(grayId) > 0 {
		response, err := SecCloudClient.AddFeatureVersions(url, featureFile, grayId[0])
		fmt.Println(err)
		if err != nil {
			return errors.New(fmt.Sprintf("发布失败，云平台返回: %s！", err.Error()+response.Msg))
		}
	} else {
		response, err := SecCloudClient.AddFeatureVersions(url, featureFile)
		if err != nil {
			return errors.New(fmt.Sprintf("发布失败，云平台返回: %s！", err.Error()+response.Msg))
		}
	}
	return nil
}

func createUploadFile(upload, featureFile string) error {
	os.RemoveAll(filepath.Join(upload, strings.TrimSuffix(featureFile, ".zip")+"_upload.zip"))
	err := ZipFiles(filepath.Join(upload, strings.TrimSuffix(featureFile, ".zip")+"_upload.zip"), []string{featureFile, "update.json"})
	if err != nil {
		return err
	}
	return nil
}

func ZipFiles(zipname string, files []string) error {
	_, err := exec.LookPath("zip")
	if err != nil {
		return errors.New("zip 未安装")
	}
	command := fmt.Sprintf(`zip -qruD %s %s`, zipname, strings.Join(files, " "))
	cmd := exec.Command("/bin/bash", "-c", command)
	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout // 标准输出
	cmd.Stderr = &stderr // 标准错误
	cmd.Dir = filepath.Dir(zipname)
	cmdErr := cmd.Run()
	outStr, _ := stdout.String(), stderr.String()
	if cmdErr != nil {
		logging.ErrorLogger.Errorf("cmd.Run() failed err", cmdErr)
		logging.ErrorLogger.Errorf("cmd.Run() outStr err", outStr)
		return cmdErr
	}
	return nil
}

func CreateDescFile(procInst *dfeatureprocinst.FeatureProcInst, packageType uint) (string, error) {
	var desc DescFileV2
	productModels, softVersions, hardVersions, featureVersions := []string{}, []string{}, []string{}, []string{}
	if len(procInst.Feature.ProductModels) > 0 {
		productModels = strings.Split(procInst.Feature.ProductModels, "|")
	}
	if len(procInst.Feature.SoftVersions) > 0 {
		softVersions = strings.Split(procInst.Feature.SoftVersions, "|")
	}
	if len(procInst.Feature.HardVersions) > 0 {
		hardVersions = strings.Split(procInst.Feature.HardVersions, "|")
	}
	var upload string
	// fileType 2为全量，1为增量
	if packageType == 2 {
		if len(procInst.Feature.FeatureVersions) > 0 {
			featureVersions = strings.Split(procInst.Feature.FeatureVersions, "|")
		}
		desc = DescFileV2{
			Primary: DescFileV2Primary{
				FileName:    procInst.Feature.FileName,
				FileSize:    procInst.Feature.FileSize,
				FileMd5:     procInst.Feature.FileMd5,
				ReleaseDate: procInst.Feature.ReleaseDate,
				FileDesc:    procInst.Feature.VersionDesc,
				FileType:    2,
			},
			Extend: DescFileV2Extend{
				Version:         procInst.Feature.Version,
				VersionDesc:     procInst.Feature.VersionDesc,
				ProductModels:   productModels,
				SoftVersions:    softVersions,
				HardVersions:    hardVersions,
				FeatureType:     procInst.Feature.FeatureType,
				PackageType:     packageType,
				FeatureVersions: featureVersions,
				RetainDay:       7,
				Sign:            procInst.Feature.Sign,
			},
		}

		upload = filepath.Join(libs.Config.FeatureRelease.Upload, procInst.Feature.CreatedAt.Format("20060102"), procInst.Feature.FileMd5)
	} else if packageType == 1 {
		featureVersions = []string{procInst.Feature.FeatureBaseVersion}
		desc = DescFileV2{
			Primary: DescFileV2Primary{
				FileName:    procInst.Feature.IncrementName,
				FileSize:    procInst.Feature.IncrementSize,
				FileMd5:     procInst.Feature.IncrementMd5,
				ReleaseDate: procInst.Feature.ReleaseDate,
				FileDesc:    procInst.Feature.VersionDesc,
				FileType:    2,
			},
			Extend: DescFileV2Extend{
				Version:         procInst.Feature.Version,
				VersionDesc:     procInst.Feature.VersionDesc,
				ProductModels:   productModels,
				SoftVersions:    softVersions,
				HardVersions:    hardVersions,
				FeatureType:     procInst.Feature.FeatureType,
				PackageType:     packageType,
				FeatureVersions: featureVersions,
				RetainDay:       7,
				Sign:            procInst.Feature.Sign,
			},
		}
		upload = filepath.Join(libs.Config.FeatureRelease.Upload, procInst.Feature.CreatedAt.Format("20060102"), procInst.Feature.IncrementMd5)
	} else {
		return upload, errors.New("非法文件类型")
	}

	// err = os.MkdirAll(upload, 0750)
	// os.Chmod(upload, 0750)
	// os.RemoveAll(filepath.Join(upload, "featureDesc.txt"))
	os.RemoveAll(filepath.Join(upload, "update.json"))
	// desc_file, err := os.OpenFile(filepath.Join(upload, "featureDesc.txt"), os.O_RDWR|os.O_CREATE, 0666)
	desc_file, err := os.OpenFile(filepath.Join(upload, "update.json"), os.O_RDWR|os.O_CREATE, 0o666)

	jsonBytes, err := json.Marshal(desc)
	if err != nil {
		return upload, err
	}
	_, err = desc_file.WriteString(string(jsonBytes))
	defer desc_file.Close()
	return upload, nil
}

func getSecCloudURL(name string) (string, error) {
	secCloud := dfeatureseccloud.Response{}
	if err := secCloud.FindEx("name", name); err != nil {
		return "", fmt.Errorf("获取安全云失败 by name '%s': %w", name, err)
	}
	return secCloud.Url, nil
}

func CreateDescFileV2(procInst *dfeatureprocinst.FeatureProcInst, packageType uint, config dfeature.FeatureSecCloudConfig) (string, error) {
	var desc DescFileV2
	productModels, softVersions, hardVersions, featureVersions := []string{}, []string{}, []string{}, []string{}
	var versionDesc string
	// 从安全云配置读取数据
	if len(config.ProductModels) > 0 {
		productModels = strings.Split(config.ProductModels, "|")
	}
	if len(config.SoftVersions) > 0 {
		softVersions = strings.Split(config.SoftVersions, "|")
	}
	if len(config.VersionDesc) > 0 {
		versionDesc = config.VersionDesc
	} else {
		versionDesc = procInst.Feature.VersionDesc
	}
	if len(procInst.Feature.HardVersions) > 0 {
		hardVersions = strings.Split(procInst.Feature.HardVersions, "|")
	}

	if packageType == 2 { // 全量包
		if len(procInst.Feature.FeatureVersions) > 0 {
			featureVersions = strings.Split(procInst.Feature.FeatureVersions, "|")
		}
		desc = DescFileV2{
			Primary: DescFileV2Primary{
				FileName:    procInst.Feature.FileName,
				FileSize:    procInst.Feature.FileSize,
				FileMd5:     procInst.Feature.FileMd5,
				ReleaseDate: procInst.Feature.ReleaseDate,
				FileDesc:    versionDesc,
				FileType:    2,
			},
			Extend: DescFileV2Extend{
				Version:         procInst.Feature.Version,
				VersionDesc:     versionDesc,
				ProductModels:   productModels,
				SoftVersions:    softVersions,
				HardVersions:    hardVersions,
				FeatureType:     procInst.Feature.FeatureType,
				PackageType:     packageType,
				FeatureVersions: featureVersions,
				RetainDay:       7,
				Sign:            procInst.Feature.Sign,
			},
		}
	} else if packageType == 1 {
		featureVersions = []string{procInst.Feature.FeatureBaseVersion}
		desc = DescFileV2{
			Primary: DescFileV2Primary{
				FileName:    procInst.Feature.IncrementName,
				FileSize:    procInst.Feature.IncrementSize,
				FileMd5:     procInst.Feature.IncrementMd5,
				ReleaseDate: procInst.Feature.ReleaseDate,
				FileDesc:    procInst.Feature.VersionDesc,
				FileType:    2,
			},
			Extend: DescFileV2Extend{
				Version:         procInst.Feature.Version,
				VersionDesc:     procInst.Feature.VersionDesc,
				ProductModels:   productModels,
				SoftVersions:    softVersions,
				HardVersions:    hardVersions,
				FeatureType:     procInst.Feature.FeatureType,
				PackageType:     packageType,
				FeatureVersions: featureVersions,
				RetainDay:       7,
				Sign:            procInst.Feature.Sign,
			},
		}
	} else {
		return "", errors.New("unsupported package type")
	}

	// 创建目标路径
	upload := filepath.Join(
		libs.Config.FeatureRelease.Upload,
		procInst.Feature.CreatedAt.Format("20060102"),
		desc.Primary.FileMd5,
		config.SecCloud,
	)
	if err := os.MkdirAll(upload, 0o750); err != nil {
		return "", fmt.Errorf("failed to create upload directory: %w", err)
	}

	// 写入 update.json 文件
	descFilePath := filepath.Join(upload, "update.json")
	descFile, err := os.OpenFile(descFilePath, os.O_RDWR|os.O_CREATE|os.O_TRUNC, 0o666)
	if err != nil {
		return "", fmt.Errorf("failed to create description file: %w", err)
	}
	defer descFile.Close()

	jsonBytes, err := json.Marshal(desc)
	if err != nil {
		return "", fmt.Errorf("failed to serialize description: %w", err)
	}
	if _, err := descFile.Write(jsonBytes); err != nil {
		return "", fmt.Errorf("failed to write description file: %w", err)
	}

	return upload, nil
}

func createUploadFileV2(upload, featureFile string, descFile string) error {
	// 构造目标压缩文件名
	zipName := filepath.Join(upload, strings.TrimSuffix(featureFile, ".zip")+"_upload.zip")

	// 删除旧的压缩文件
	if err := os.RemoveAll(zipName); err != nil {
		return fmt.Errorf("failed to remove existing zip: %w", err)
	}
	// 获取upload目录的上一级目录
	uploadDir := filepath.Dir(upload)
	_, err := CopyFile(filepath.Join(uploadDir, featureFile), filepath.Join(upload, featureFile))
	if err != nil {
		return fmt.Errorf("failed to copy file: %w", err)
	}

	// 调用压缩工具
	err = ZipFiles(zipName, []string{
		featureFile, descFile,
	})
	if err != nil {
		return fmt.Errorf("failed to create zip file: %w", err)
	}
	return nil
}

func uploadFileToSecCloud(procInst *dfeatureprocinst.FeatureProcInst, packageType uint, config *dfeature.FeatureSecCloudConfig, fileInfo map[string]string) error {
	retries := 3 // 重试次数
	var uploadErr error
	// 获取安全云 URL
	secCloudURL, err := getSecCloudURL(config.SecCloud)
	if err != nil {
		logging.ErrorLogger.Errorf("获取安全云失败: %v", err)
		return err
	}

	for attempt := 1; attempt <= retries; attempt++ {
		// 生成描述文件
		uploadDir, descErr := CreateDescFileV2(procInst, packageType, *config)
		if descErr != nil {
			uploadErr = fmt.Errorf("尝试 %d: 生成描述文件失败: %s: %w", attempt, fileInfo["filename"], descErr)
			logging.ErrorLogger.Errorf(uploadErr.Error())
			continue
		}

		zipFileName := fileInfo["filename"]
		if strings.HasSuffix(zipFileName, ".zip") {
			zipFileName = strings.TrimSuffix(zipFileName, ".zip") + "_upload.zip"
		} else {
			zipFileName += "_upload.zip"
		}
		uploadPath := filepath.Join(uploadDir, zipFileName)
		// 打包配置文件
		if zipErr := createUploadFileV2(uploadDir, fileInfo["filename"], "update.json"); zipErr != nil {
			uploadErr = fmt.Errorf("尝试 %d: 生成上传压缩文件失败: %s: %w", attempt, fileInfo["filename"], zipErr)
			logging.ErrorLogger.Errorf(uploadErr.Error())
			continue
		}
		// 检查是否已上传过
		resp, queryErr := SecCloudClient.QueryFeatureVersions(secCloudURL, fileInfo["filename"], procInst.Feature.FeatureType, procInst.Feature.Version)
		if queryErr != nil {
			uploadErr = fmt.Errorf("尝试 %d: 查询特征版本失败: %s: %w", attempt, fileInfo["filename"], queryErr)
			logging.ErrorLogger.Errorf(uploadErr.Error())
			continue
		}
		// 跳过已上传的文件
		isUploaded := false
		for _, version := range resp.Data.Recoreds {
			if version.FileMd5 == fileInfo["md5"] && version.ReleaseDate == procInst.Feature.ReleaseDate {
				logging.DebugLogger.Debugf("文件 %s 已经上传到 %s; 跳过", fileInfo["filename"], secCloudURL)
				isUploaded = true
				break
			}
		}
		if isUploaded {
			return nil
		}
		// 上传文件
		if uploadErr = UploadV4(secCloudURL, uploadPath, config.GrayRuleID); uploadErr != nil {
			logging.ErrorLogger.Errorf("尝试 %d: 上传文件 %s 到 %s 失败: %v", attempt, zipFileName, secCloudURL, uploadErr)
			if strings.Contains(uploadErr.Error(), "同种特征库类别的版本号重复") {
				return uploadErr
			}
			time.Sleep(time.Duration(attempt) * time.Second)
			continue
		}

		logging.DebugLogger.Debugf("文件 %s 成功上传到 %s", fileInfo["filename"], secCloudURL)
		break
	}

	return uploadErr
}

// 文件复制
func CopyFile(src, des string) (written int64, err error) {
	srcFile, err := os.Open(src)
	if err != nil {
		return 0, err
	}
	defer srcFile.Close()

	// 获取源文件的权限
	fi, _ := srcFile.Stat()
	perm := fi.Mode()

	// desFile, err := os.Create(des)  //无法复制源文件的所有权限
	desFile, err := os.OpenFile(des, os.O_RDWR|os.O_CREATE|os.O_TRUNC, perm) // 复制源文件的所有权限
	if err != nil {
		return 0, err
	}
	defer desFile.Close()

	return io.Copy(desFile, srcFile)
}
