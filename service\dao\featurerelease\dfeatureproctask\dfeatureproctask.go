package dfeatureproctask

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/featurerelease"
	"irisAdminApi/service/dao/user/duser"
)

const ModelName = "规则库流程任务"

type Feature struct {
	// ID uint `json:"id"`

	// FileName           string `json:"file_name"`
	// FileSize           uint   `json:"file_size"`
	// FileMd5            string `json:"file_md5"`
	// Version            string `json:"version"`
	// ProductModels      string `json:"product_models"`
	// SoftVersions       string `json:"soft_versions"`
	// FeatureType        string `json:"feature_type"`
	// FeatureVersions    string `json:"feature_versions"`
	// FeatureBaseVersion string `json:"feature_base_version"`
	// FileType           uint   `json:"file_type"`
	// ReleaseDate        string `json:"release_date"`
	// VersionDesc        string `json:"version_desc"`
	// Desc               string `json:"desc"`
	// Sign               string `json:"sign"`
	// UpdatedAt          string `json:"updated_at"`
	// CreatedAt          string `json:"created_at"`
	// Status             uint   `json:"status"`
	// UpdateToMain       bool   `json:"update_to_main"`
	// Urgency            bool   `json:"urgency"`
	// OverTimeNotice     bool   `json:"over_time_notice"`
	featurerelease.Feature
}

type FeatureProcInst struct {
	ID    uint   `json:"id"`
	Title string `json:"title"`
	// 当前节点
	NodeID string `json:"node_id"`
	// 审批人
	// Candidate string `json:"candidate"`
	// 当前任务
	TaskID      int     `json:"task_id"`
	StartUserID uint    `json:"start_user_id"`
	FeatureID   uint    `json:"feature_id"`
	Status      uint    `json:"status"`
	Resource    string  `gorm:"size:10000" json:"resource,omitempty"`
	Feature     Feature `gorm:"->; foreignKey:FeatureID" json:"feature"`
}

type FeatureProcTask struct {
	ID uint `json:"id"`
	// 当前执行流所在的节点
	NodeName   string `json:"nodeName"`
	NodeID     string `json:"nodeId"`
	PrevNodeID string `json:"prevNodeID"`
	// Step   int    `json:"step"`
	// 流程实例id
	ProcInstID uint            `json:"procInstID"`
	ProcInst   FeatureProcInst `gorm:"->; foreignKey:ProcInstID" json:"proc_inst"`
	Assignee   uint            `json:"assignee"`
	// 还未审批的用户数，等于0代表会签已经全部审批结束，默认值为1
	// MemberCount   int8 `json:"memberCount" gorm:"default:1"`
	// UnCompleteNum int8 `json:"unCompleteNum" gorm:"default:1"`
	//审批通过数
	// AgreeNum int8 `json:"agreeNum"`
	// and 为会签，or为或签，默认为or
	// ActType    string `json:"actType" gorm:"default:'or'"`
	Status         uint                    `gorm:"default:0" json:"status"`
	User           *duser.ApprovalResponse `gorm:"-" json:"user"`
	Flag           bool                    `json:"flag"`
	Attachment     string                  `json:"attachment"`
	CreatedAt      string                  `json:"created_at"`
	OverTimeNotice bool                    `json:"over_time_notice"`
}

type ListResponse struct {
	FeatureProcTask
}

type Request struct {
	// 当前执行流所在的节点
	NodeName string `json:"nodeName"`
	NodeID   string `json:"nodeId"`
	// Step   int    `json:"step"`
	// 流程实例id
	ProcInstID int  `json:"procInstID"`
	Assignee   uint `json:"assignee"`
	// 还未审批的用户数，等于0代表会签已经全部审批结束，默认值为1
	// MemberCount   int8 `json:"memberCount" gorm:"default:1"`
	// UnCompleteNum int8 `json:"unCompleteNum" gorm:"default:1"`
	//审批通过数
	// AgreeNum int8 `json:"agreeNum"`
	// and 为会签，or为或签，默认为or
	// ActType    string `json:"actType" gorm:"default:'or'"`
	Comment string `json:"comment"`
	Status  uint   `gorm:"default:0" json:"status"`
}

func (a *FeatureProcTask) ModelName() string {
	return ModelName
}

func Model() *featurerelease.FeatureProcTask {
	return &featurerelease.FeatureProcTask{}
}

func (a *FeatureProcTask) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	FormatResponse(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *FeatureProcTask) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *FeatureProcTask) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *FeatureProcTask) CreateV2(object interface{}) error {
	return nil
}

func (a *FeatureProcTask) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *FeatureProcTask) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *FeatureProcTask) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *FeatureProcTask) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *FeatureProcTask) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *FeatureProcTask) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindAll() ([]*FeatureProcTask, error) {
	var items []*FeatureProcTask

	if err := easygorm.GetEasyGormDb().Model(Model()).Find(&items).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return items, err
	}
	return items, nil
}

func FindInIds(ids []uint) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id in ?", ids).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	return items, nil
}

func DeleteByUUID(uuid string) error {
	err := easygorm.GetEasyGormDb().Unscoped().Where("uuid = ?", uuid).Delete(Model()).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func UpdateStatus(id uint, object map[string]interface{}) error {

	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func FormatResponse(items []*ListResponse) {
	userIds := []uint{}
	for _, item := range items {
		userIds = append(userIds, item.Assignee)
	}
	users, _ := duser.FindSimpleInIds(userIds)
	var userMap = make(map[uint]*duser.ApprovalResponse)
	for _, user := range users {
		userMap[user.Id] = user
	}
	for _, item := range items {
		item.User = userMap[item.Assignee]
	}
}

func FindInProcInstIDs(procInstIDs []uint) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("proc_inst_id in ? and status = 0", procInstIDs).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	FormatResponse(items)
	return items, nil
}

func FindAttachmentByProc(procInstID uint, nodeID string) (string, error) {
	var item FeatureProcTask
	err := easygorm.GetEasyGormDb().Model(Model()).Where("proc_inst_id = ? and status = 1 and flag = 1 and node_id = ? and (attachment != '' and attachment is not null)", procInstID, nodeID).Find(&item).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return "", err
	}
	return item.Attachment, nil
}

var FeatureTasksProccessingCache []*ListResponse

func UpdateFeatureTasksProccessingCache() error {
	if err := easygorm.GetEasyGormDb().Model(Model()).Where("flag =1 and status = 0").Preload("ProcInst").Preload("ProcInst.Feature").Find(&FeatureTasksProccessingCache).Error; err != nil {
		return err
	}
	// for _, item := range FeatureTasksProccessingCache {
	// 	fmt.Println(item.ProcInst, item.ProcInst.Feature)
	// }
	return nil
}
