package datasync

import "time"

type BugProject struct {
	/*
		"projectId": 99999,                        //rgos项目ID
		"projectName": "测试项目",                //项目名称
		"pmName": null,                            //pm名称
		"projectStatus": "完成(正式发布)",        //项目状态
		"projectType": "补丁项目",                //项目类别
		"baseProject": "测试项目",                //基线项目
		"applyTime": "2014-04-01 17:46:51",        //申请时间
		"lastUpdateTime": "2014-04-24 12:30:07"    //最后更新时间
	*/
	ProjectID     int       `gorm:"primarykey; autoIncrement:false" json:"project_id"`
	ProjectName   string    `gorm:"not null; type:varchar(60)" json:"project_name" update:"1"`
	PMName        string    `gorm:"not null; type:varchar(60)" json:"pm_name" update:"1"`
	ProjectStatus string    `gorm:"not null; type:varchar(60)" json:"project_status" update:"1"`
	ProjectType   string    `gorm:"not null; type:varchar(60)" json:"project_type" update:"1"`
	BaseProject   string    `gorm:"not null; type:varchar(60)" json:"base_project" update:"1"`
	CreatedAt     time.Time `json:"created_at" update:"1"`
	UpdatedAt     time.Time `json:"updated_at" update:"1"`
}
