package dfeishupmsreviewdata

import (
	"fmt"
	"reflect"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/feishu"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "评审数据表"

type PmsReviewData struct {
	feishu.PmsReviewData
}

type ListResponse struct {
	PmsReviewData
}

type SyncListResponse struct {
	PmsReviewData
	ReviewUserGroup string `json:"review_user_group"`
}

type Request struct {
	Id uint `json:"id"`
}

func (a *PmsReviewData) ModelName() string {
	return ModelName
}

func Model() *feishu.PmsReviewData {
	return &feishu.PmsReviewData{}
}

func (a *PmsReviewData) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (a *PmsReviewData) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (a *PmsReviewData) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (a *PmsReviewData) CreateV2(object interface{}) error {
	return nil
}

func (a *PmsReviewData) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (u *PmsReviewData) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (u *PmsReviewData) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *PmsReviewData) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *PmsReviewData) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *PmsReviewData) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func BatchCreate(items []map[string]interface{}) error {
	db := easygorm.GetEasyGormDb().Model(Model())
	return db.Create(items).Error
}

func DeleteAll() error {
	db := easygorm.GetEasyGormDb().Model(Model())
	err := db.Unscoped().Delete(Model(), "id>0").Error
	if err != nil {
		return err
	}
	// 重置id值
	alterTableSQL := fmt.Sprintf("ALTER TABLE feishu_file_lists AUTO_INCREMENT=%d", 1)
	if err := easygorm.GetEasyGormDb().Exec(alterTableSQL).Error; err != nil {
		logging.ErrorLogger.Errorf("ALTER TABLE feishu_file_lists  get err ", err)
		return err
	}
	return nil
}

func CreateOrUpdateReviewData(objects []map[string]interface{}) error {
	columns := []string{"updated_at"}
	xt := reflect.TypeOf(Model())
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")
		if ok && ok2 {
			columns = append(columns, name)
		}
	}
	db := easygorm.GetEasyGormDb()
	limit := 1000
	err := db.Transaction(func(tx *gorm.DB) error {
		if len(objects) > 0 {
			for i := 0; i < len(objects); i += limit {
				end := i + limit
				if end >= len(objects) {
					end = len(objects)
				}
				err := tx.Model(Model()).Clauses(clause.OnConflict{
					Columns:   []clause.Column{{Name: "id"}},
					DoUpdates: clause.AssignmentColumns(columns),
				}).Create(objects[i:end]).Error
				if err != nil {
					return err
				}
			}
		}
		return nil
	})

	return err
}

func All() ([]*ListResponse, error) {
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	err := db.Order("project_id asc, orders asc").Find(&items).Error
	return items, err
}

func FindByProjectNames(projectNames []string, page, pageSize int) ([]*SyncListResponse, error) {
	var items []*SyncListResponse
	rawSql := `(SELECT
			pd.id id,
			deleted_at,
			entity_name, 
			document_category_name, 
			document_charge_name, 
			flaw_type, 
			is_valid_review, 
			project_name,  
			question_type, 
			severity_level, 
			review_subject, 
			review_content, 
			review_description,
			review_user_name,
			create_date,
			modify_date,
			close_date,
			(case 
				when pbm.five_department_name IS NOT NULL AND pbm.five_department_name != '' then pbm.five_department_name
				when pbm.four_department_name IS not NULL AND pbm.four_department_name != '' then pbm.four_department_name
				when pbm.three_department_name IS not NULL AND pbm.three_department_name != '' then pbm.three_department_name
				when pbm.two_department_name IS NOT NULL AND pbm.two_department_name != '' then pbm.two_department_name
				when pbm.one_department_name IS NOT NULL AND pbm.one_department_name != '' then pbm.one_department_name
				ELSE ''
			end
			) review_user_group,
			accept_status,
			accept_content,
			source
			FROM pms_review_data pd
			LEFT JOIN pms_bu_members pbm ON pbm.user_cn = pd.review_user_name) as pms_review_data`
	offset := (page - 1) * pageSize
	db := easygorm.GetEasyGormDb().Table(rawSql).Where("project_name in (?)", projectNames)
	err := db.Order("id desc").Offset(offset).
		Limit(pageSize).Find(&items).Error
	return items, err
}

// func GetHardwareProjectData(page int, pageSize int) ([]*ListResponse, error) {
// 	items := []*ListResponse{}
// 	offset := (page - 1) * pageSize
// 	err := easygorm.GetEasyGormDb().Model(Model()).
// 		Offset(offset).
// 		Limit(pageSize).
// 		Find(&items).Error

// 	return items, err
// }
