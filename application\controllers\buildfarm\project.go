package buildfarm

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/buildfarm/dproject"

	"github.com/kataras/iris/v12"
)

func GetEnableProjects(ctx iris.Context) {
	projects, err := dproject.FindAllEnableProjects()
	if err != nil {
		logging.ErrorLogger.Errorf("get projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, projects, response.NoErr.Msg))
	return
}

func GetProjects(ctx iris.Context) {
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	var count int64
	var projects []*dproject.ListResponse

	db := easygorm.GetEasyGormDb().Model(dproject.Model())

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("db count err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&projects).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	list := map[string]interface{}{"items": projects, "total": count, "limit": pageSize}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func CreateProjects(ctx iris.Context) {
	request := &dproject.Request{}
	if err := ctx.ReadJSON(request); err != nil {
		logging.ErrorLogger.Errorf("create project read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		logging.ErrorLogger.Errorf("create project read json err ", strings.Join(errs, ";"))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	err := dao.Create(&dproject.Response{}, ctx, map[string]interface{}{
		"Name":         request.Name,
		"GitlabId":     request.GitlabId,
		"Repo":         request.Repo,
		"TaskType":     request.TaskType,
		"Enable":       request.Enable,
		"PatchEnable":  request.PatchEnable,
		"PatchDirName": request.PatchDirName,
		"CreatedAt":    time.Now(),
		"Owner":        request.Owner,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		if strings.Contains(err.Error(), "Duplicate entry") {
			ctx.JSON(response.NewResponse(response.DuplicateErr.Code, nil, response.DuplicateErr.Msg))
			return
		}
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, request, response.NoErr.Msg))
	return
}

func UpdateProject(ctx iris.Context) {
	request := &dproject.Request{}
	if err := ctx.ReadJSON(request); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	data := map[string]interface{}{
		"Name":         request.Name,
		"GitlabId":     request.GitlabId,
		"Repo":         request.Repo,
		"TaskType":     request.TaskType,
		"Enable":       request.Enable,
		"PatchEnable":  request.PatchEnable,
		"PatchDirName": request.PatchDirName,
		"UpdatedAt":    time.Now(),
		"Owner":        request.Owner,
	}
	err := dao.Update(&dproject.Response{}, ctx, data)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func DeleteProject(ctx iris.Context) {
	err := dao.Delete(&dproject.Response{}, ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetBuildProjects(ctx iris.Context) {
	token := libs.Config.Buildfarm.Token
	if len(token) == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置gitlab token,请联系管理员"))
		return
	}
	url := fmt.Sprintf("%s/api/%s/projects?private_token=%s&simple=true", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, token)

	// page, _ := strconv.Atoi(ctx.FormValue("page"))
	// pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))

	url = fmt.Sprintf("%s&per_page=%d", url, 100)

	var items []map[string]interface{}
	list := map[string]interface{}{}
	page := 1
	for i := 0; i <= 10; i++ {
		_url := fmt.Sprintf("%s&page=%d", url, page)
		req, err := http.NewRequest("GET", _url, nil)
		if err != nil {
			logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
			continue
		}

		_result, err := libs.HandlerRequest(req)
		if err != nil {
			logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
			continue
		}
		items = append(items, _result["items"].([]map[string]interface{})...)
		list["total"] = _result["total"]
		list["limit"] = _result["limit"]
		page++
		if _result["total"].(int) <= len(items) {
			break
		}
	}
	list["items"] = items
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetUserProjects(ctx iris.Context) {
	token, err := dao.GetGitlabToken(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置gitlab token,请前往个人资料添加"))
		return
	}
	url := fmt.Sprintf("%s/api/%s/projects?private_token=%s&simple=true", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, token)

	// page, _ := strconv.Atoi(ctx.FormValue("page"))
	// pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))

	// if page > 0 {
	// 	url = fmt.Sprintf("%s&page=%d", url, page)
	// }
	// if pageSize > 0 {
	// 	url = fmt.Sprintf("%s&per_page=%d", url, pageSize)
	// }
	url = fmt.Sprintf("%s&per_page=%d", url, 100)

	var items []map[string]interface{}
	list := map[string]interface{}{}
	page := 1
	for i := 0; i <= 10; i++ {
		_url := fmt.Sprintf("%s&page=%d", url, page)
		req, err := http.NewRequest("GET", _url, nil)
		if err != nil {
			logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
			continue
		}

		_result, err := libs.HandlerRequest(req)
		if err != nil {
			logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
			continue
		}
		if _result["items"] != nil {
			items = append(items, _result["items"].([]map[string]interface{})...)
		}
		list["total"] = _result["total"]
		list["limit"] = _result["limit"]
		page++
		if _result["total"] == nil || _result["total"].(int) <= len(items) {
			break
		}
	}
	list["items"] = items
	// req, err := http.NewRequest("GET", url, nil)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }

	// result, err := libs.HandlerRequest(req)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetProjectBranches(ctx iris.Context) {
	projectId, err := dao.GetId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	token := libs.Config.Buildfarm.Token
	if len(token) == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置gitlab token,请联系管理员"))
		return
	}
	url := fmt.Sprintf("%s/api/%s/projects/%d/repository/branches?private_token=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, projectId, token)

	// page, _ := strconv.Atoi(ctx.FormValue("page"))
	// pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))

	url = fmt.Sprintf("%s&per_page=%d", url, 100)

	var items []map[string]interface{}
	list := map[string]interface{}{}
	page := 1
	for i := 0; i <= 100; i++ {
		_url := fmt.Sprintf("%s&page=%d", url, page)
		req, err := http.NewRequest("GET", _url, nil)
		if err != nil {
			logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
			continue
		}

		_result, err := libs.HandlerRequest(req)
		if err != nil {
			logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
			continue
		}
		items = append(items, _result["items"].([]map[string]interface{})...)
		list["total"] = _result["total"]
		list["limit"] = _result["limit"]
		page++
		if _result["total"].(int) <= len(items) {
			break
		}
	}
	list["items"] = items
	// if page > 0 {
	// 	url = fmt.Sprintf("%s&page=%d", url, page)
	// }
	// if pageSize > 0 {
	// 	url = fmt.Sprintf("%s&per_page=%d", url, pageSize)
	// }

	// req, err := http.NewRequest("GET", url, nil)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	// result, err := libs.HandlerRequest(req)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}
