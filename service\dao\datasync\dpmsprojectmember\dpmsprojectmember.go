package dpmsprojectmember

import (
	"encoding/json"
	"fmt"
	"reflect"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/datasync"
	"irisAdminApi/service/dao/datasync/dsyncrecord"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "PMS项目成员表"

type PmsProjectMemberSyncResponse struct {
	State   string                      `json:"state"`
	Data    []*PmsProjectMemberResponse `json:"data"`
	Total   int                         `json:"total"`
	Message string                      `json:"message"`
}

type PmsProjectMemberResponse struct {
	RowNum      int    `json:"rownum"`
	ID          int    `gorm:"not null; index:idx_unique, unique" json:"id" `
	ProjectID   int    `gorm:"not null; index:idx_unique, unique" json:"projectId" `
	ProjectName string `gorm:"not null; type:varchar(100)" json:"projectName"  update:"1"`
	UserID      int    `gorm:"not null; index:idx_unique, unique" json:"userId" `
	Username    string `gorm:"not null; type:varchar(100); default:''" json:"userName"  update:"1"`
	UserRole    string `gorm:"not null; type:varchar(100); default:''" json:"userRole"  update:"1"`
	RoleRemark  string `gorm:"not null; type:varchar(100); default:''" json:"roleRemark"  update:"1"`
	Disabled    bool   `grom:"not null; default:false" json:"disabled" update:"1"`
	CasUserID   string `gorm:"not null; type:varchar(100); default:''" json:"casUserId"  update:"1"`
}

type PmsProjectMember struct {
	datasync.PmsProjectMember
}

type ListResponse struct {
	PmsProjectMember
}

type Request struct {
	Id uint `json:"id"`
}

func (this *PmsProjectMember) ModelName() string {
	return ModelName
}

func Model() *datasync.PmsProjectMember {
	return &datasync.PmsProjectMember{}
}

func (this *PmsProjectMember) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *PmsProjectMember) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *PmsProjectMember) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *PmsProjectMember) CreateV2(object interface{}) error {
	return nil
}

func (this *PmsProjectMember) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *PmsProjectMember) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *PmsProjectMember) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *PmsProjectMember) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *PmsProjectMember) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *PmsProjectMember) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func UpdateOrCreatePmsProjectMemberTransaction(items []*PmsProjectMemberResponse, _url string, data map[string]string, method, state, errorMsg string) error {
	objects := []map[string]interface{}{}
	for _, item := range items {

		object := map[string]interface{}{
			"ID":          item.ID,
			"ProjectID":   item.ProjectID,
			"ProjectName": item.ProjectName,
			"UserID":      item.UserID,
			"Username":    item.Username,
			"UserRole":    item.UserRole,
			"RoleRemark":  item.RoleRemark,
			"Disabled":    item.Disabled,

			"CreatedAt": time.Now(),
			"UpdatedAt": time.Now(),
			"CasUserID": item.CasUserID,
		}

		objects = append(objects, object)
	}

	columns := []string{"updated_at"}

	xt := reflect.TypeOf(Model())
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")
		if ok && ok2 {
			columns = append(columns, name)
		}
	}

	body, err := json.Marshal(data)
	if err != nil {
		return err
	}
	db := easygorm.GetEasyGormDb()
	err = db.Transaction(func(tx *gorm.DB) error {
		if len(objects) > 0 {
			err := tx.Model(Model()).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "id"}},
				DoUpdates: clause.AssignmentColumns(columns),
			}).Create(&objects).Error
			if err != nil {
				return err
			}
		}

		if err := tx.Model(dsyncrecord.Model()).Create(map[string]interface{}{
			"url":             _url,
			"body":            body,
			"method":          method,
			"state":           state,
			"message":         errorMsg,
			"min_modify_date": "",
			"max_modify_date": "",
			"created_at":      time.Now(),
		}).Error; err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func FindMembersByProjectAndRole(project string, roles []string) ([]*ListResponse, error) {
	items := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("project_name = ? and user_role in (?)", project, roles).Find(&items).Error
	return items, err
}

func FindRunningProjectsMembers() ([]*ListResponse, error) {
	items := []*ListResponse{}

	sqlRaw := `SELECT 
						ppm.id, ppm.project_id, ppm.project_name, username, user_role, role_remark, cas_user_id 
						from pms_project_members ppm 
						LEFT JOIN  pms_project_list_data ppld on ppld.project_name = ppm.project_name
						WHERE ppld.project_status = '进行中'`
	err := easygorm.GetEasyGormDb().
		Raw(sqlRaw).
		Scan(&items).Error
	return items, err
}
