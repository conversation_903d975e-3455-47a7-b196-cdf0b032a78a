package release

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/datasync/dproductdata"
	"irisAdminApi/service/dao/kpi/dcontributionpoint"
	"irisAdminApi/service/dao/release/dreleasearchive"
	"irisAdminApi/service/dao/release/dreleaseodm"
	"irisAdminApi/service/transaction/release/transrelease"
	"net/url"
	"path/filepath"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/xuri/excelize/v2"
)

func GetReleasesODM(ctx iris.Context) {

	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dreleaseodm.ListResponse{}, ctx, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetAllArchiveReleasesODM(ctx iris.Context) {

	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dreleaseodm.AllArchiveReleases(name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetReleaseODM(ctx iris.Context) {
	info := dreleaseodm.Response{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

func CreateReleaseODM(ctx iris.Context) {

	userId, _ := dao.GetAuthId(ctx)
	request := &dreleaseodm.Request{}
	if err := ctx.ReadJSON(request); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	//产品型号验证
	ProductModels := strings.Split(request.ProductModel, "|")
	var productModelStr, ReqProductModel string
	var productModelNum float64
	for _, item := range ProductModels {
		if item != "" {
			productModelStr = productModelStr + fmt.Sprintf("'%s',", item)
			ReqProductModel = ReqProductModel + fmt.Sprintf("%s|", item)
			productModelNum = productModelNum + 1
		}
	}
	request.ProductModel = strings.TrimSuffix(ReqProductModel, "|")
	productModelStr = strings.TrimSuffix(productModelStr, ",")
	// checkErr :=libs.CheckProductModelList(productModelStr,productModelNum)
	// if checkErr !=nil{
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, checkErr.Error()))
	// 	return
	// }
	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	uuid := libs.GetUUID()
	object := map[string]interface{}{
		"UserID":    userId,
		"CreatedAt": time.Now(),
		"UpdatedAt": time.Now(),
		"Uuid":      uuid,
	}
	t := reflect.TypeOf(*request)
	v := reflect.ValueOf(*request)
	for k := 0; k < t.NumField(); k++ {
		if t.Field(k).Type == reflect.TypeOf([]uint{}) {
			continue
		}
		object[t.Field(k).Name] = v.Field(k).Interface()
	}

	err := dao.Create(&dreleaseodm.Response{}, ctx, object)
	if err != nil {
		logging.ErrorLogger.Errorf("create release project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	release := dreleaseodm.Response{}
	err = release.FindEx("uuid", uuid)

	if err != nil {
		release.Delete(release.ID)
		logging.ErrorLogger.Errorf("create release project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, request, response.NoErr.Msg))
	return
}

func ArchiveReleaseODM(ctx iris.Context) {

	releaseODM := dreleaseodm.Response{}
	err := dao.Find(&releaseODM, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if releaseODM.Archive == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "正在归档中，请忽重复提交"))
		return
	}
	if releaseODM.Archive == 1 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "已归档成功，无法重复归档"))
		return
	}
	releaseObject := map[string]interface{}{
		"ID":                    releaseODM.ID,
		"ProjectName":           releaseODM.ProjectName,
		"ProductModel":          releaseODM.ProductModel,
		"ReleaseAttrID":         releaseODM.ReleaseAttrID,
		"ReleasedAt":            releaseODM.ReleasedAt,
		"SystemSoftwareVersion": releaseODM.SystemSoftwareVersion,
		"PackageName":           releaseODM.PackageName,
		"ReleaseLink":           releaseODM.ReleaseLink,
		"Comment":               releaseODM.Comment,
		"UserID":                releaseODM.UserID,
		"Uuid":                  releaseODM.Uuid,
	}
	err = transrelease.CreateReleaseORMArchiveTransaction(releaseODM.ProductModel, releaseObject)
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func DeleteReleaseODM(ctx iris.Context) {

	userId, _ := dao.GetAuthId(ctx)
	releaseODM := dreleaseodm.Response{}
	err := dao.Find(&releaseODM, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if releaseODM.Archive == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "正在归档中，无法删除"))
		return
	}
	if releaseODM.Archive == 1 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "已归档成功，无法删除"))
		return
	}
	if releaseODM.UserID != userId {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "仅允许创建者删除"))
		return
	}

	err = dao.Delete(&releaseODM, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetProductFamily(ctx iris.Context) {
	type ProductFamily struct {
		ProductFamily string `json:"product_family"`
	}
	productFamilyData := []ProductFamily{}
	rows, err := easygorm.GetEasyGormDb().Model(dproductdata.Model()).Select("product_family").Group("product_family").Rows()
	if err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
	}
	for rows.Next() {
		easygorm.GetEasyGormDb().Model(dcontributionpoint.Model()).ScanRows(rows, &productFamilyData)
	}
	rows.Close()
	ctx.JSON(response.NewResponse(response.NoErr.Code, productFamilyData, response.NoErr.Msg))
}

func GetBIProductModel(ctx iris.Context) {
	productfamily := ctx.FormValue("productfamily")
	if productfamily == "" {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "请选择产品系列"))
		return
	}
	productDataList, err := dproductdata.FindProductDatas("product_family", productfamily)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, productDataList, response.NoErr.Msg))
}

type ReleaseApiReq struct {
	Sysid     string `json:"sysid"`
	PageIndex int    `json:"PageIndex"`
	PageSize  int    `json:"PageSize"`
	StartTime string `json:"StartTime"`
	EndTime   string `json:"EndTime"`
}

func GetReleaseArchivedData(ctx iris.Context) {
	headerSysID := strings.Join(ctx.Request().Header["Sysid"], "")
	if headerSysID != libs.Config.Release.DPRSYSID {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "验证失败"))
		return
	}
	ReleaseApiReq := &ReleaseApiReq{}
	if err := ctx.ReadJSON(ReleaseApiReq); err != nil {
		logging.ErrorLogger.Errorf("create ReleaseApiReq read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if ReleaseApiReq.StartTime == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "查询开始时间不可为空"))
		return
	}
	if ReleaseApiReq.EndTime == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "查询结束时间不可为空"))
		return
	}
	if ReleaseApiReq.Sysid == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "sysid不可为空"))
		return
	}
	if ReleaseApiReq.PageSize == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "PageSize参数不正确"))
		return
	}
	if ReleaseApiReq.PageIndex == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "PageIndex参数不正确"))
		return
	}
	if ReleaseApiReq.Sysid != libs.Config.Release.DPRSYSID {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "验证失败,请核实"))
		return
	}
	startTime, err := time.Parse("2006-01-02 15:04:05", ReleaseApiReq.StartTime)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "开始时间格式不正确"))
		return
	}
	endTime, err := time.Parse("2006-01-02 15:04:05", ReleaseApiReq.EndTime)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "结束时间格式不正确"))
		return
	}
	sub1 := endTime.Sub(startTime)
	if sub1.Seconds() <= 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "开始时间要早于结束时间，请确认"))
		return
	}
	page := ReleaseApiReq.PageIndex
	pageSize := ReleaseApiReq.PageSize
	var sort, orderBy string
	list, err := dreleasearchive.AllArchiveReleases(sort, orderBy, ReleaseApiReq.StartTime, ReleaseApiReq.EndTime, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	var ids []uint
	releaseArchiveData := []map[string]interface{}{}
	for _, value := range list["items"].([]*dreleasearchive.ListResponse) {
		ids = append(ids, value.ID)
		releaseArchiveData = append(releaseArchiveData, map[string]interface{}{
			"ID":                    value.ID,
			"ProjectName":           value.ProjectName,
			"ProductModel":          value.ProductModel,
			"ReleaseAttrName":       value.ReleaseAttr.Name,
			"ReleasedAt":            value.ReleasedAt,
			"SystemSoftwareVersion": value.SystemSoftwareVersion,
			"PackageName":           value.PackageName,
			"ReleaseLink":           value.ReleaseLink,
			"Comment":               value.Comment,
		})
	}

	data := map[string]interface{}{"items": releaseArchiveData, "total": list["total"], "count": len(ids)}
	ctx.JSON(response.NewResponse(response.NoErr.Code, data, response.NoErr.Msg))
	return
}

func UpdateReleaseODM(ctx iris.Context) {

	userId, _ := dao.GetAuthId(ctx)
	request := &dreleaseodm.Request{}
	if err := ctx.ReadJSON(request); err != nil {
		logging.ErrorLogger.Errorf("Update ReleaseODM json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	release := dreleaseodm.Response{}
	id, _ := ctx.Params().GetUint("id")
	err := release.FindEx("id", fmt.Sprintf("%d", id))
	if err != nil {
		logging.ErrorLogger.Errorf("create release project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if release.UserID != userId {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "仅允许创建者修改"))
		return
	}
	if release.Archive == 1 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "已归档项目，无法进行修改"))
		return
	}
	//产品型号验证
	ProductModels := strings.Split(request.ProductModel, "|")
	var ReqProductModel string
	var productModelNum float64
	for _, item := range ProductModels {
		if item != "" {
			ReqProductModel = ReqProductModel + fmt.Sprintf("%s|", item)
			productModelNum = productModelNum + 1
		}
	}
	request.ProductModel = strings.TrimSuffix(ReqProductModel, "|")
	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	object := map[string]interface{}{
		"UpdatedAt": time.Now(),
	}
	t := reflect.TypeOf(*request)
	v := reflect.ValueOf(*request)
	for k := 0; k < t.NumField(); k++ {
		if t.Field(k).Type == reflect.TypeOf([]uint{}) {
			continue
		}
		object[t.Field(k).Name] = v.Field(k).Interface()
	}

	err = dao.Update(&dreleaseodm.Response{}, ctx, object)
	if err != nil {
		logging.ErrorLogger.Errorf("Update ReleaseODM project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, request, response.NoErr.Msg))
	return
}

func ExportReleaseODM(ctx iris.Context) {
	name := ctx.FormValue("name")
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	// list, err := dao.All(&dproblem.ListResponse{}, ctx, name, sort, orderBy, page, pageSize)
	list, err := dreleaseodm.AllReleaseodms(name, sort, orderBy, 1, -1)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	fileName := fmt.Sprintf("ODM版本发布数据_%s.xlsx", time.Now().Format("20060102150405"))
	file := excelize.NewFile()
	streamWriter, err := file.NewStreamWriter("Sheet1")

	// styleID, err := file.NewStyle(&excelize.Style{Font: &excelize.Font{Color: "#777777"}})
	if err != nil {
		logging.ErrorLogger.Error(err)
	}
	header := []interface{}{"项目名称", "产品型号", "版本属性", "发布时间", "system_software _version", "package_name", "发布链接", "版本备注"}
	cell, _ := excelize.CoordinatesToCellName(1, 1)
	if err := streamWriter.SetRow(cell, header); err != nil {
		logging.ErrorLogger.Error(err)
	}
	for idx, item := range list["items"].([]*dreleaseodm.ListResponse) {
		cell, _ := excelize.CoordinatesToCellName(1, 2+idx)
		row := []interface{}{item.ProjectName, item.ProductModel, item.ReleaseAttr.Name, item.ReleasedAt, item.SystemSoftwareVersion, item.PackageName, item.ReleaseLink, item.Comment}
		if err := streamWriter.SetRow(cell, row); err != nil {
			logging.ErrorLogger.Error(err)
		}
	}
	if err := streamWriter.Flush(); err != nil {
		logging.ErrorLogger.Error(err)
	}
	if err := file.SaveAs(filepath.Join("/tmp", fileName)); err != nil {
		logging.ErrorLogger.Error(err)
	}
	ctx.SendFile(filepath.Join("/tmp", fileName), url.QueryEscape(fileName))
	return
}
