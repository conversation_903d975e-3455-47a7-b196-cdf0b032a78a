package bugapproval

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/bugapproval/dbugapproval"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/xuri/excelize/v2"
)

func GetBugApprovals(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	// id, err := uint(0), errors.Wrap(nil, "")
	bugID, _ := strconv.Atoi(ctx.FormValue("bug_id"))
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	projectName := ctx.FormValue("project_name")
	scope := ctx.FormValue("scope")
	export := ctx.FormValue("export")
	// start := ctx.FormValue("start")
	// end := ctx.FormValue("end")
	var list map[string]interface{}

	if export == "1" {
		pageSize = -1
	}

	switch scope {
	case "owned":
		list, err = dbugapproval.All(false, id, uint(bugID), projectName, status, sort, orderBy, page, pageSize)
	case "audit":
		list, err = dbugapproval.All(true, id, uint(bugID), projectName, status, sort, orderBy, page, pageSize)
	default:
		list, err = dbugapproval.All(false, 0, uint(bugID), projectName, status, sort, orderBy, page, pageSize)
	}

	// list, err := dproblem.AllProblems(name, sort, orderBy, page, pageSize, status, start, end, department)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if export == "1" {
		items := list["items"]
		fileName := fmt.Sprintf("Bug延期及专项清单_%d.xlsx", time.Now().Unix())
		file := excelize.NewFile()
		streamWriter, err := file.NewStreamWriter("Sheet1")
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
			return
		}

		rowNum := 1

		for _, row := range items.([]*dbugapproval.ListResponse) {
			cell, _ := excelize.CoordinatesToCellName(1, rowNum)
			if rowNum == 1 {
				if err := streamWriter.SetRow(cell, []interface{}{
					"BugID",
					"申请类型",
					"申请人",
					"申请说明",
					"申请时间",
					"计划完成时间",
				}); err != nil {
					ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
					return
				}
			} else {
				planFinishAtStr := ""
				if row.PlanFinishAt != nil {
					planFinishAtStr = row.PlanFinishAt.Format("2006-01-02 15:04:05")
				}
				if err := streamWriter.SetRow(cell, []interface{}{
					row.BugID,
					row.ApprovalType,
					row.User.Name,
					row.Comment,
					row.CreatedAt.Format("2006-01-02 15:04:05"),
					planFinishAtStr,
				}); err != nil {
					ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
					return
				}
			}

			rowNum++
		}
		if err := streamWriter.Flush(); err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
			return
		}
		if err := file.SaveAs(filepath.Join("/tmp", fileName)); err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
			return
		}
		defer os.Remove(filepath.Join("/tmp", fileName))
		ctx.SendFile(filepath.Join("/tmp", fileName), fileName)
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetBugApproval(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}

	userID, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	bugApproval := dbugapproval.BugApproval{}
	err = bugApproval.Find(id)
	// list, err := dproblem.AllProblems(name, sort, orderBy, page, pageSize, status, start, end, department)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	bugApproval.IsAuditor = false
	for _, audit := range bugApproval.Audits {
		if audit.UserID == userID && audit.Status == 0 {
			bugApproval.IsAuditor = true
			break
		}
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, bugApproval, response.NoErr.Msg))
	return
}

func CreateBugApproval(ctx iris.Context) {
	userID, _ := dao.GetAuthId(ctx)
	request := dbugapproval.Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(&request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	object := map[string]interface{}{
		"UserID":       userID,
		"BugID":        request.BugID,
		"ApprovalType": request.ApprovalType,
		"Status":       uint(0),
		"CreatedAt":    time.Now(),
		"Round":        request.Round,
		"Comment":      request.Comment,
		"CCIDs":        request.CCIDs,
		"AuditorIDs":   request.AuditorIDs,
	}
	if request.PlanFinishAt != nil {
		if request.PlanFinishAt.Sub(time.Now()).Hours() > 24*30 {
			ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "专项时间超过30天，请调整计划结束时间。"))
			return
		}
		object["PlanFinishAt"] = request.PlanFinishAt
	}

	bugapproval := dbugapproval.BugApproval{}

	err := bugapproval.Create(object)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	// mailTo := []string{}
	// go SendMail(mailTo)
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func DeleteBugApproval(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	userId, _ := dao.GetAuthId(ctx)

	bugapproval := dbugapproval.BugApproval{}
	err := bugapproval.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if bugapproval.UserID != userId {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "仅允许删除自己创建的申请"))
	}

	err = bugapproval.Delete(id)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, bugapproval, response.NoErr.Msg))
}

type AuditRequest struct {
	Comment string `json:"comment"`
	Pass    uint   `json:"pass"`
}

func AuditBugApproval(ctx iris.Context) {
	request := AuditRequest{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(&request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	id, err := dao.GetId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}

	userID, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	bugApproval := dbugapproval.BugApproval{}
	bugApprovalAuditor := dbugapproval.BugApprovalAuditor{}
	err = bugApproval.Find(id)
	// list, err := dproblem.AllProblems(name, sort, orderBy, page, pageSize, status, start, end, department)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	bugApproval.IsAuditor = false
	for _, auditor := range bugApproval.Auditors {
		if auditor.ID == userID {
			bugApproval.IsAuditor = true
			break
		}
	}

	for _, audit := range bugApproval.Audits {
		if audit.UserID == userID && audit.Status == 0 {
			err := bugApprovalAuditor.Update(audit.ID, &bugApproval, request.Pass, request.Comment)
			if err != nil {
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
				return
			}
			ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
			return
		}
	}

	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	return
}

func SendMail(mailTo []string) {

}

func GetBug(ctx iris.Context) {
	bugID, err := dao.GetId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}

	approvalType := ctx.FormValue("approval_type")
	bug := dbugapproval.Bug{}
	err = bug.FindBugByBugID(int(bugID), approvalType)
	// list, err := dproblem.AllProblems(name, sort, orderBy, page, pageSize, status, start, end, department)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, bug, response.NoErr.Msg))
	return
}
