package ip

import (
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/fileout"
	"net"
	"strings"

	"github.com/kataras/iris/v12"
)

type Ip struct {
	ID uint   `json:"id"`
	Ip string `json:"ip"`
}

func GetRemoteAddr(ctx iris.Context) string {
	addr := ctx.GetHeader("X-Real-Ip")
	if len(addr) == 0 {
		addr := strings.TrimSpace(ctx.Request().RemoteAddr)
		if addr != "" {
			// if addr has port use the net.SplitHostPort otherwise(error occurs) take as it is
			if ip, _, err := net.SplitHostPort(addr); err == nil {
				return ip
			}
		}
	} else {
		if ip, _, err := net.SplitHostPort(addr); err == nil {
			return ip
		}
	}
	return addr
}

func CheckIP(ctx iris.Context) (bool, error) {
	_addr := GetRemoteAddr(ctx)
	addr := net.ParseIP(_addr)
	if addr == nil {
		logging.ErrorLogger.Errorf("非法IP地址", _addr)
		return false, fmt.Errorf("不在允许的IP地址范围内: 非法IP地址")
	}

	var ips = []*Ip{}

	err := easygorm.GetEasyGormDb().Model(&fileout.AllowAuditIp{}).Find(&ips).Error
	if err != nil {
		return false, fmt.Errorf("系统错误: %s", err.Error())
	}

	if len(ips) == 0 {
		return false, fmt.Errorf("不在允许的IP地址范围内: 未配置白名单")
	}

	for _, ip := range ips {
		_, ipNet, err := net.ParseCIDR(ip.Ip)
		if err != nil {
			logging.ErrorLogger.Errorf("白名单地址不合法", ip.Ip, _addr)
			continue
		}
		if ipNet.Contains(addr) {
			return true, nil
		}
	}
	return false, fmt.Errorf("不在允许的IP地址范围内")
}
