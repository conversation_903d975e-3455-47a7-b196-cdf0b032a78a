package opensource

import (
	"github.com/kataras/iris/v12"

	"irisAdminApi/application/controllers/opensource/taskmanagers"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/opensource/dcomponent"
	"irisAdminApi/service/dao/user/duser"
)

func InitOpenSourceTaskManagers() {
	if libs.Config.OpenSource.Enable {
		taskmanagers.InitScheduledCVESyncTaskManager()
		taskmanagers.InitScheduledSendEmailTaskManager()
		taskmanagers.InitScheduledSyncCnvdTask()
		taskmanagers.InitTempSyncCVETaskManager()
	}
}

func isAdminUser(userId uint) bool {
	user := &duser.User{ID: userId}
	duser.GetUserRoles(user)
	for _, role := range user.Roles {
		if role == "超级管理员" {
			return true
		}
	}
	return false
}

func getComponent(ctx iris.Context, componentId uint) *dcomponent.OpenSourceComponent {
	res, err := dcomponent.FindById(componentId)
	if err != nil {
		logging.ErrorLogger.Errorf("get opensource component get err %s ", err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return nil
	} else if res == nil {
		ctx.JSON(response.NewResponse(response.DataEmptyErr.Code, nil, "组件不存在"))
		return nil
	}
	return res
}
