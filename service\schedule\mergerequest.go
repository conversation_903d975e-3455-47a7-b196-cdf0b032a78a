package schedule

import (
	"irisAdminApi/application/controllers/mergerequest"
	"irisAdminApi/application/logging"
)

func MergeRequestSechdule() {
	// mergerequest.SendBugSummary()
	// mergerequest.BugSummaryWorker()
	// mergerequest.CheckMergeRequestOverTime()
	// mergerequest.Test()
	// buildfarm.CleanMakeJob()
	// _, err := Cron.AddFunc("0 9 * * *", func() { mergerequest.BugSummaryWorker() })
	// if err != nil {
	// 	logging.ErrorLogger.Error("add send bug summary cron err ", err)
	// }

	_, err := Cron.AddFunc("*/15 * * * *", func() { mergerequest.UpdateMergeRequestDiffWorker() })
	if err != nil {
		logging.ErrorLogger.Error("add update merge request diff worker err ", err)
	}

	// _, err = Cron.AddFunc("0 9 * * *", func() { mergerequest.CheckMergeRequestOverTime() })
	// if err != nil {
	// 	logging.ErrorLogger.Error("add check mergerequest overtime cron err ", err)
	// }
	// mergerequest.SyncCpsSchedule()

	// _, err = Cron.AddFunc("0 9 * * *", func() { mergerequest.SyncCpsSchedule() })
	// if err != nil {
	// 	logging.ErrorLogger.Error("add sync cps schedule cron err ", err)
	// }
	// _, err = Cron.AddFunc("00 20 28 10 *", func() { mergerequest.BugSummaryWorkerOnce() })
	// if err != nil {
	// 	logging.ErrorLogger.Error("add check mergerequest overtime cron err ", err)
	// }
	_, err = Cron.AddFunc("*/1 * * * *", func() { mergerequest.CodeCheckWorker() })
	if err != nil {
		logging.ErrorLogger.Error("add update merge request diff worker err ", err)
	}
}
