insert into casbin_rule (p_type, v0, v1, v2) values ('p', '3', '/api/v1/approval/audited', 'GET');
insert into allow_audit_ips (ip) values ('************');



|  382 | g     | 1    | 6                                                           |        |      |      |      |
|  385 | g     | 2    | 2                                                           |        |      |      |      |
|  383 | g     | 2    | 6                                                           |        |      |      |      |
|  394 | g     | 3    | 3                                                           |        |      |      |      |
|  395 | g     | 4    | 4                                                           |        |      |      |      |
|  396 | g     | 5    | 3                                                           |        |      |      |      |
|  397 | g     | 6    | 4                                                           |        |      |      |      |
| 1362 | g     | 7    | 2                                                           |        |      |      |      |


insert into casbin_rule (ptype, v0, v1) values ('g', '1', '1');
insert into casbin_rule (ptype, v0, v1) values ('g', '1', '2');
insert into casbin_rule (ptype, v0, v1) values ('g', '1', '3');
insert into casbin_rule (ptype, v0, v1) values ('g', '1', '4');
insert into casbin_rule (ptype, v0, v1) values ('g', '1', '5');
insert into casbin_rule (ptype, v0, v1) values ('g', '1', '6');
insert into casbin_rule (ptype, v0, v1) values ('g', '1', '7');



| 2318 | p     | 1    | /api/v1/coverity/builds/start               | POST |      |      |      |
| 2317 | p     | 1    | /api/v1/coverity/builds/{id:uint}/stop      | GET  |      |      |      |
| 2316 | p     | 1    | /api/v1/coverity/builds/{id:uint}/output    | GET  |      |      |      |
| 2315 | p     | 1    | /api/v1/coverity/builds/last                | GET  |      |      |      |
| 2314 | p     | 1    | /api/v1/coverity/builds                     | GET  |      |      |      |