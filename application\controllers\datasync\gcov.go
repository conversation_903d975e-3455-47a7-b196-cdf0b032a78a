package datasync

import (
	"context"
	"fmt"
	"time"

	"irisAdminApi/application/controllers/openfeishu"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/datasync/dgcov"

	influxdb2 "github.com/influxdata/influxdb-client-go/v2"
	"github.com/influxdata/influxdb-client-go/v2/api"
	"github.com/pkg/errors"
)

var QueryApi api.QueryAPI

func InitQueryAPI() api.QueryAPI {
	if QueryApi == nil {
		client := influxdb2.NewClient(libs.Config.DataSync.Influxdb.Url, libs.Config.DataSync.Influxdb.Token)

		// Get query client
		queryApi := client.QueryAPI(libs.Config.DataSync.Influxdb.Org)
		QueryApi = queryApi
	}
	return QueryApi
}

func GcovClean() error {
	objects := []map[string]interface{}{}
	queryAPI := InitQueryAPI()
	// Get QueryTableResult
	result, err := queryAPI.Query(context.Background(), fmt.Sprintf(`
		from(bucket:"%s")
			|> range(start: -30d)
			|> filter(fn: (r) => r["_measurement"] == "gcov-file" and r._field == "gcov" and r._value > 0)
			|> group(columns: ["host", "branch", "softversion", "file", "type"])
			|> last(column:"_value")
			|> group()
			|> keep(columns: ["_time", "_value", "file", "branch", "softversion", "host", "type"])
			|>  sort(desc: true)
		`,
		libs.Config.DataSync.Influxdb.Bucket),
	)
	if err != nil {
		return err
	}
	/*
		清洗流程：
		File ->
		select full_path from common_ntos_files where full_path  like "%FILE" ->

	*/
	// Iterate over query response
	for result.Next() {
		record := result.Record()
		object := map[string]interface{}{
			"Branch":    record.Values()["branch"],
			"ReleaseID": record.Values()["softversion"],
			"File":      record.Values()["file"],
			"CreatedAt": record.Values()["_time"].(time.Time).UTC(),
			"Host":      record.Values()["host"],
			"Gcov":      record.Values()["_value"],
			"Type":      record.Values()["type"],
			"UpdatedAt": time.Now(),
		}
		objects = append(objects, object)
	}

	// Check for an error
	if result.Err() != nil {
		fmt.Printf("query parsing error: %s\n", result.Err().Error())
		return result.Err()
	}

	err = dgcov.UpdateOrCreatePmsDocTransaction(objects)
	if err != nil {
		logging.ErrorLogger.Error(err)
		return err
	}
	return nil
}

func GetBranches() ([]string, error) {
	var branches []string
	queryAPI := InitQueryAPI()
	result, err := queryAPI.Query(context.Background(), fmt.Sprintf(`
		from(bucket: "gcov-request")
			|> range(start: -1d)
			|> filter(fn: (r) => r["_measurement"] == "gcov-file")
			|> keyValues(keyColumns: ["branch"])
			|> group()
			|> keep(columns: ["branch"])
			|> distinct(column: "branch")
		`))

	if err == nil {
		// Iterate over query response
		for result.Next() {
			v := result.Record().Values()
			if vv, ok := v["_value"]; ok {
				if _, ok := vv.(string); ok {
					branches = append(branches, vv.(string))
				}
			}
		}
		// Check for an error
		if result.Err() != nil {
			return branches, errors.Wrap(result.Err(), "query parsing error")
		} else {
			return branches, nil
		}
	}
	return branches, errors.Wrap(err, "query error")
}

func GetMaxReleaseID(branch string) (string, error) {
	var releaseID string
	queryAPI := InitQueryAPI()
	result, err := queryAPI.Query(context.Background(), fmt.Sprintf(`
			from(bucket: "gcov-request")
				|> range(start: -1d)
				|> filter(fn: (r) => r["_measurement"] == "gcov-file")
				|> filter(fn: (r) => r["branch"] == "%s")
				|> filter(fn: (r) => exists r.softversion)
				|> keyValues(keyColumns: ["softversion"])
				|> group()
				|> keep(columns: ["softversion"])
				|> sort(columns: ["softversion"], desc: true)
				|> distinct(column: "softversion")
				|> first()
		`, branch))

	if err == nil {
		// Iterate over query response
		for result.Next() {
			v := result.Record().Values()
			if vv, ok := v["_value"]; ok {
				if _, ok := vv.(string); ok {
					releaseID = vv.(string)
					break
				}
			}
		}
		// Check for an error
		if result.Err() != nil {
			return releaseID, errors.Wrap(result.Err(), "query parsing error")
		} else {
			return releaseID, nil
		}
	}
	return releaseID, errors.Wrap(err, "query error")
}

func GetGcovRequestWorkpackageData(branch string, releaseID string) ([]map[string]interface{}, error) {
	objects := []map[string]interface{}{}
	queryAPI := InitQueryAPI()

	result, err := queryAPI.Query(context.Background(), fmt.Sprintf(`
			from(bucket: "gcov-workpackage")
				|> range(start: -1d)
				|> filter(fn: (r) => r["_measurement"] == "gcov-file")
				|> filter(fn: (r) => r["branch"] == "%s")
				|> filter(fn: (r) => r["auto"] == "all")
				|> filter(fn: (r) => r["type"] == "line")
				|> filter(fn: (r) => exists r.workpackage)
				|> filter(fn: (r) => r["softversion"] == "%s")
				|> filter(fn: (r) => r["_field"] == "add-covline" or r["_field"] == "add")

				|> last()
				|> keep(columns: ["workpackage", "request", "workpackage_manager", "_field", "_value"])
				|> group()
				|> sort(columns: ["workpackage"], desc: true)
				|> pivot(rowKey:["workpackage", "request", "workpackage_manager"], columnKey: ["_field"], valueColumn: "_value")

				|> map(fn: (r) => (
						{ 
								workpackage: r.workpackage,
								request: r.request, 
								workpackage_manager: r.workpackage_manager,
								line: r.add,
								gcov: if r.add != 0 and r["add-covline"] != 0 then r["add-covline"]/r.add else 0.0 
						}
					)
				)
			|> yield(name: "mapped_data")
		`, branch, releaseID),
	)

	if err == nil {
		// Iterate over query response
		for result.Next() {
			v := result.Record().Values()
			objects = append(objects, v)
		}
		// Check for an error
		if result.Err() != nil {
			return objects, errors.Wrap(result.Err(), "query parsing error")
		} else {
			return objects, nil
		}
	}
	return objects, errors.Wrap(result.Err(), "query error")
}

func SyncGcovDataToFeiShu(appToken, tableID string, items []map[string]interface{}) {
	// appToken := "Hi7rbhcSxaAEvnsm7HrcQDHkn1e"
	// tableID := "tblZ37ZhPHt3HTNu"
	// 批量删除线上数据

	openfeishu.DeleteTableRecordDataV4(appToken, tableID, []string{"需求"})

	// 批量同步到线上
	now := time.Now()
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, item := range batchData {
				rec := map[string]interface{}{
					"更新时间": now.UnixMilli(),
				}

				flag := true

				if gcov, ok := item["gcov"]; ok {
					if v, ok := gcov.(float64); ok {
						rec["覆盖率"] = v
					} else {
						rec["覆盖率"] = 0.0
						flag = false
					}
				} else {
					rec["覆盖率"] = 0.0
					flag = false
				}

				if line, ok := item["line"]; ok {
					if v, ok := line.(float64); ok {
						rec["逻辑代码行"] = v
					} else {
						rec["逻辑代码行"] = -1
						flag = false
					}
				} else {
					rec["逻辑代码行"] = -9
					flag = false
				}

				if request, ok := item["request"]; ok {
					if v, ok := request.(string); ok {
						rec["需求"] = v
					} else {
						rec["需求"] = fmt.Sprintf("不是有效的string")
						flag = false
					}
				} else {
					rec["需求"] = fmt.Sprintf("字段不存在")
					flag = false
				}

				if workpackage, ok := item["workpackage"]; ok {
					if v, ok := workpackage.(string); ok {
						rec["工作包"] = v
					} else {
						rec["工作包"] = fmt.Sprintf("不是有效的string")
						flag = false
					}
				} else {
					rec["工作包"] = fmt.Sprintf("字段不存在")
					flag = false
				}

				if workpackage_manager, ok := item["workpackage_manager"]; ok {
					if v, ok := workpackage_manager.(string); ok {
						rec["工作包负责人"] = v
					} else {
						rec["工作包负责人"] = fmt.Sprintf("不是有效的string")
						flag = false
					}
				} else {
					rec["工作包负责人"] = fmt.Sprintf("字段不存在")
					flag = false
				}

				// if releaseID, ok := item["softversion"]; ok {
				// 	if v, ok := releaseID.(string); ok {
				// 		rec["ReleaseID"] = v
				// 	} else {
				// 		rec["ReleaseID"] = fmt.Sprintf("不是有效的string")
				// 	}
				// } else {
				// 	rec["ReleaseID"] = fmt.Sprintf("字段不存在")
				// }
				if !flag {
					logging.ErrorLogger.Error(rec, "数据异常")
				} else {
					records = append(records, rec)
				}
			}

			tableRecordResp, err := openfeishu.BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("批量创建记录失败: %s", err.Error(), string(tableRecordResp.RawBody))
				continue
			}
		}
	}
}

func SyncGcovDataToFeiShuWorker(branch, appToken, tableID string) {
	releaseID, err := GetMaxReleaseID(branch)
	if err != nil {
		logging.ErrorLogger.Errorf("获取最大ReleaseID失败: %s", err.Error())
		return
	}
	items, err := GetGcovRequestWorkpackageData(branch, releaseID)
	if err != nil {
		logging.ErrorLogger.Errorf("获取Gcov数据失败: %s", err.Error())
		return
	}
	SyncGcovDataToFeiShu(appToken, tableID, items)
}

func GetGcovRequestWorkpackageDataWithAuto(branch string, releaseID string) ([]map[string]interface{}, error) {
	objects := []map[string]interface{}{}
	queryAPI := InitQueryAPI()

	result, err := queryAPI.Query(context.Background(), fmt.Sprintf(`
			from(bucket: "gcov-workpackage")
				|> range(start: -1d)
				|> filter(fn: (r) => r["_measurement"] == "gcov-file")
				|> filter(fn: (r) => r["branch"] == "%s")
				|> filter(fn: (r) => r["auto"] == "test-all" or r["auto"] == "dev-all" or r["auto"] == "all")
				|> filter(fn: (r) => r["type"] == "line")
				|> filter(fn: (r) => exists r.workpackage)
				|> filter(fn: (r) => r["softversion"] == "%s")
				|> filter(fn: (r) => r["_field"] == "add-covline" or r["_field"] == "add")

				|> last()
				|> keep(columns: ["workpackage", "request", "workpackage_manager", "auto", "_field", "_value"])
				|> group()
				|> sort(columns: ["workpackage"], desc: true)
				|> pivot(rowKey:["workpackage", "request", "workpackage_manager", "auto"], columnKey: ["_field"], valueColumn: "_value")

				|> map(fn: (r) => (
						{ 
								workpackage: r.workpackage,
								request: r.request, 
								workpackage_manager: r.workpackage_manager,
								line: r.add,
								gcov: if r.add != 0 and r["add-covline"] != 0 then r["add-covline"]/r.add else 0.0,
								auto: r.auto
						}
					)
				)
			|> yield(name: "mapped_data")
		`, branch, releaseID),
	)

	if err == nil {
		// Iterate over query response
		for result.Next() {
			v := result.Record().Values()
			objects = append(objects, v)
		}
		// Check for an error
		if result.Err() != nil {
			return objects, errors.Wrap(result.Err(), "query parsing error")
		} else {
			return objects, nil
		}
	}
	return objects, errors.Wrap(result.Err(), "query error")
}

func SyncGcovDataToMysqlWorker() {
	branches, err := GetBranches()
	if err != nil {
		logging.ErrorLogger.Errorf("获取分支列表失败: %s", err.Error())
		return
	}
	for _, branch := range branches {
		SyncGcovDataToMysql(branch)
	}
}

func SyncGcovDataToMysql(branch string) {
	releaseID, err := GetMaxReleaseID(branch)
	if err != nil {
		logging.ErrorLogger.Errorf("获取最大ReleaseID失败: %s", err.Error())
		return
	}
	items, err := GetGcovRequestWorkpackageDataWithAuto(branch, releaseID)
	if err != nil {
		logging.ErrorLogger.Errorf("获取Gcov数据失败: %s", err.Error())
		return
	}
	records := []map[string]interface{}{}
	if len(items) > 0 {
		// 将新增数据按照批次插入飞书数据表,每批次500条
		for _, item := range items {
			rec := map[string]interface{}{}
			rec["branch"] = branch
			rec["workpackage"] = item["workpackage"]
			rec["request"] = item["request"]
			rec["workpackage_manager"] = item["workpackage_manager"]
			rec["auto"] = item["auto"]
			rec["line"] = item["line"]
			rec["gcov"] = item["gcov"]
			records = append(records, rec)
		}
	}

	err = GcovBatchCreate(branch, records)
	if err != nil {
		logging.ErrorLogger.Errorf("批量创建Gcov数据失败: %s", err.Error())
		return
	}

	// 将新增数据按照批次插入飞书数据表,每批次500条
}
