package bugsync

import (
	"fmt"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/bugsync/dbug"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/xuri/excelize/v2"
)

func GetMainBugs(ctx iris.Context) {
	// token := ctx.GetHeader("sysid")
	// if token != "cvd20220614" {
	// 	uid, err := dao.GetAuthId(ctx)
	// 	if err != nil || uid == 0 {
	// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "无权限访问"))
	// 		return
	// 	}
	// }

	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	bugOS := ctx.FormValue("bug_os")
	bugBelong := ctx.FormValue("bug_belong")
	bugOwner := ctx.FormValue("bug_owner")
	bugState := ctx.FormValue("bug_state")
	bugTestCharger := ctx.FormValue("bug_test_charger")
	mirrorStatus := ctx.FormValue("mirror_status")
	resolveStatus := ctx.FormValue("resolve_status")
	createdAt := ctx.FormValue("created_at")
	updatedAt := ctx.FormValue("updated_at")
	export := ctx.FormValue("export")

	if export == "1" {
		list, err := dbug.FindAllBug(page, -1, sort, orderBy, bugOS, mirrorStatus, resolveStatus, bugBelong, bugOwner, bugTestCharger, bugState, createdAt, updatedAt)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		ExportBugs(ctx, list["items"])
		return
	}
	list, err := dbug.FindAllBug(page, pageSize, sort, orderBy, bugOS, mirrorStatus, resolveStatus, bugBelong, bugOwner, bugTestCharger, bugState, createdAt, updatedAt)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetFilter(ctx iris.Context) {
	column := ctx.FormValue("column")
	bugOS := ctx.FormValue("bug_os")

	result, err := dbug.FindDistinctByColumn(column, bugOS)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func ExportBugs(ctx iris.Context, items interface{}) {
	mirrorStatus := map[uint]string{
		0: "未镜像",
		1: "已镜像",
		2: "部分镜像",
	}
	resolveStatus := map[uint]string{
		0: "未解决",
		1: "已解决",
		2: "部分解决",
	}
	fileName := fmt.Sprintf("bugs_%s.xlsx", time.Now().Format("20060102150405"))
	file := excelize.NewFile()
	streamWriter, err := file.NewStreamWriter("Sheet1")

	// styleID, err := file.NewStyle(&excelize.Style{Font: &excelize.Font{Color: "#777777"}})
	if err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	header := []interface{}{"bugID", "BUG描述", "操作系统", "产品型号", "BUG负责人", "BUG归属人", "BUG测试人", "BUG状态", "BUG创新时间", "BUG更新时间", "镜像ID", "需要镜像操作系统", "已镜像操作系统", "镜像状态", "解决状态"}
	cell, _ := excelize.CoordinatesToCellName(1, 1)
	if err := streamWriter.SetRow(cell, header); err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	rowNum := 1
	for _, item := range items.([]*dbug.ListResponse) {
		rowNum++
		cell, _ := excelize.CoordinatesToCellName(1, rowNum)
		row := []interface{}{item.BugID, item.BugSummary, item.BugOS, item.BugProduct, item.BugOwner, item.BugBelong, item.BugTestCharger, item.BugState, item.BugCreatedAt, item.BugUpdatedAt, item.MainBugID, item.NeedMirrorProjects, item.MirroredProjects, mirrorStatus[item.MirrorStatus], resolveStatus[item.ResolveStatus]}
		if err := streamWriter.SetRow(cell, row); err != nil {
			logging.ErrorLogger.Error(err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}

		for _, item2 := range item.Children {
			rowNum++
			cell, _ := excelize.CoordinatesToCellName(1, rowNum)
			row := []interface{}{item2.BugID, item2.BugSummary, item2.BugOS, item2.BugProduct, item2.BugOwner, item2.BugBelong, item2.BugTestCharger, item2.BugState, item2.BugCreatedAt, item2.BugUpdatedAt, item2.MainBugID, item2.NeedMirrorProjects, item2.MirroredProjects, mirrorStatus[item2.MirrorStatus], resolveStatus[item2.ResolveStatus]}
			if err := streamWriter.SetRow(cell, row); err != nil {
				logging.ErrorLogger.Error(err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
				return
			}
		}

	}
	if err := streamWriter.Flush(); err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if err := file.SaveAs(filepath.Join("/tmp", fileName)); err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	defer os.Remove(filepath.Join("/tmp", fileName))
	ctx.SendFile(filepath.Join("/tmp", fileName), url.QueryEscape(fileName))
	return
}
