package dproductionprocinst

import (
	"encoding/json"
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/productionrelease"
	"irisAdminApi/service/dao/productionrelease/dproduction"
	dproductionproctask "irisAdminApi/service/dao/productionrelease/dproductionproctask"
	"irisAdminApi/service/dao/productionrelease/dproductionproctaskdocument"
	"irisAdminApi/service/dao/productionrelease/dproductionproctaskextension"
	"irisAdminApi/service/dao/productionrelease/dproductionproctaskhost"
	"irisAdminApi/service/dao/user/duser"
	"strings"
)

const ModelName = "下生产流程实例"

type Response struct {
	ID    uint   `json:"id"`
	Title string `json:"title"`
	// 当前节点
	NodeID string `json:"node_id"`
	// 审批人
	// Candidate string `json:"candidate"`
	// 当前任务
	TaskID         int                                            `json:"task_id"`
	StartUserID    uint                                           `json:"start_user_id"`
	ProductionID   uint                                           `json:"production_id"`
	Status         uint                                           `json:"status"`
	Resource       string                                         `gorm:"size:10000" json:"resource,omitempty"`
	User           *duser.ApprovalResponse                        `gorm:"-" json:"user"`
	Production     *dproduction.Response                          `gorm:"-" json:"production"`
	Tasks          []*dproductionproctask.ListResponse            `gorm:"-" json:"tasks"`
	DoneTasks      []*dproductionproctask.ListResponse            `gorm:"-" json:"done_tasks"`
	MainProgramDoc *dproductionproctaskdocument.Response          `gorm:"-" json:"main_program_doc"`
	OspkgDoc       *dproductionproctaskdocument.Response          `gorm:"-" json:"ospkg_doc"`
	MachineDoc     []*dproductionproctaskdocument.Response        `gorm:"-" json:"machine_doc"`
	PlatformTasks  *dproductionproctask.Response                  `gorm:"-" json:"platform_tasks"`
	ChiefTasks     *dproductionproctask.Response                  `gorm:"-" json:"chief_tasks"`
	CpldTasks      *dproductionproctask.Response                  `gorm:"-" json:"cpld_tasks"`
	TestTasks      *dproductionproctask.Response                  `gorm:"-" json:"test_tasks"`
	HardwareTasks  *dproductionproctask.Response                  `gorm:"-" json:"hardware_tasks"`
	PmTasks        *dproductionproctask.Response                  `gorm:"-" json:"pm_tasks"`
	Extensions     []*dproductionproctaskextension.ExtensionGroup `gorm:"-" json:"extensions"`
	SetmacHosts    []*dproductionproctaskhost.ListResponse        `gorm:"-" json:"setmac_hosts"`
}

type ListResponse struct {
	Response
}

type Request struct {
	Title string `json:"title"`
	// 当前节点
	NodeID string `json:"node_id"`
	// 审批人
	// Candidate string `json:"candidate"`
	// 当前任务
	TaskID       int    `json:"task_id"`
	StartUserID  string `json:"start_user_id"`
	ProductionID string `json:"production_id"`
	Resource     string `gorm:"size:10000" json:"resource,omitempty"`
	IsFinished   bool   `gorm:"default:false" json:"is_finished"`
}

type Node struct {
	Name       string `json:"name,omitempty"`
	NodeID     string `json:"nodeId,omitempty"`
	PrevNodeID string `json:"prevNodeId,omitempty"`
	Assignee   uint   `json:"assignee"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *productionrelease.ProductionProcInst {
	return &productionrelease.ProductionProcInst{}
}

func (a *Response) All(name, status, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	if len(status) > 0 {
		db = db.Where("status in ?", strings.Split(status, ","))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	formatResponses(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	formatResponse(u)
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindAll() ([]*Response, error) {
	var items []*Response

	if err := easygorm.GetEasyGormDb().Model(Model()).Find(&items).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return items, err
	}
	return items, nil
}

func FindInIds(ids []uint) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id in ?", ids).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	formatResponses(items)
	return items, nil
}

func DeleteByUUID(uuid string) error {
	err := easygorm.GetEasyGormDb().Unscoped().Where("uuid = ?", uuid).Delete(Model()).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func UpdateStatus(id uint, object map[string]interface{}) error {

	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

// func FormatResponse(items []*ListResponse) {
// 	userIds := []uint{}
// 	for _, item := range items {
// 		userIds = append(userIds, item.UserID)
// 	}
// 	users, _ := duser.FindSimpleInIds(userIds)
// 	var userMap = make(map[uint]*duser.ApprovalResponse)
// 	for _, user := range users {
// 		userMap[user.Id] = user
// 	}
// 	for _, item := range items {
// 		item.User = userMap[item.UserID]
// 	}
// }

func createProcInst(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func GetPrevNodeIDs(resource string, nodeID string) ([]string, error) {
	prevNodeIDs := []string{}

	nodes := []*Node{}
	err := json.Unmarshal([]byte(resource), &nodes)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return prevNodeIDs, err
	}
	for _, node := range nodes {
		if node.NodeID == nodeID {
			prevNodeIDs = append(prevNodeIDs, node.PrevNodeID)
		}
	}
	return prevNodeIDs, nil
}

func GetNextNodeIDs(resource string, nodeID string) ([]string, error) {
	nextNodeIDs := []string{}

	nodes := []*Node{}
	err := json.Unmarshal([]byte(resource), &nodes)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return nextNodeIDs, err
	}
	for _, node := range nodes {
		if node.PrevNodeID == nodeID {
			nextNodeIDs = append(nextNodeIDs, node.NodeID)
		}
	}
	return nextNodeIDs, nil
}

func GetNode(resource string, nodeID string) (*Node, error) {
	nodes := []*Node{}

	err := json.Unmarshal([]byte(resource), &nodes)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return nil, err
	}
	for _, node := range nodes {
		if node.NodeID == nodeID {
			return node, nil
		}
	}
	return nil, nil
}

func GetNodes(resource string) ([]*Node, error) {
	nodes := []*Node{}

	err := json.Unmarshal([]byte(resource), &nodes)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return nodes, err
	}

	return nodes, nil
}

func formatResponses(items []*ListResponse) {
	productionIds := []uint{}
	procInstIDs := []uint{}
	userIds := []uint{}
	taskIds := []uint{}
	productionMap := map[uint]*dproduction.Response{}
	for _, item := range items {
		productionIds = append(productionIds, item.ProductionID)
		userIds = append(userIds, item.StartUserID)
		procInstIDs = append(procInstIDs, item.ID)
	}
	productions, err := dproduction.FindInIds(productionIds)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return
	}

	for _, production := range productions {
		productionMap[production.ID] = &production.Response
	}

	users, _ := duser.FindSimpleInIds(userIds)
	var userMap = make(map[uint]*duser.ApprovalResponse)
	for _, user := range users {
		userMap[user.Id] = user
	}
	taskMap := map[uint][]*dproductionproctask.ListResponse{}
	tasks, err := dproductionproctask.FindInProcInstIDs(procInstIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("find tasks err ", err)
		return
	}
	for _, task := range tasks {
		taskMap[task.ProcInstID] = append(taskMap[task.Response.ProcInstID], task)
	}
	//已完成节点
	platformTasksMap := map[uint]*dproductionproctask.Response{}
	chiefTasksMap := map[uint]*dproductionproctask.Response{}
	cpldTasksMap := map[uint]*dproductionproctask.Response{}
	testTasksMap := map[uint]*dproductionproctask.Response{}
	hardwareTasksMap := map[uint]*dproductionproctask.Response{}
	pmTasksMap := map[uint]*dproductionproctask.Response{}
	taskDoneMap := map[uint][]*dproductionproctask.ListResponse{}
	tasks2, err := dproductionproctask.FindInProcInstIDs2(procInstIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("find tasks err ", err)
		return
	}
	for _, task := range tasks2 {
		taskIds = append(taskIds, task.ID)
		if task.NodeID == "platform_mg" {
			platformTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "chief_tester" {
			chiefTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "cpld_check" {
			cpldTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "test_check" {
			testTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "hardware_audit" {
			hardwareTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "pm_submit" {
			pmTasksMap[task.Response.ProcInstID] = &task.Response
		}
	}
	tasks3, err := dproductionproctask.FindInProcInstIDs3(procInstIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("find tasks err ", err)
		return
	}
	for _, task := range tasks3 {
		taskDoneMap[task.ProcInstID] = append(taskDoneMap[task.Response.ProcInstID], task)
	}
	mainProgramDocMap := map[uint]*dproductionproctaskdocument.Response{}
	ospkgDocMap := map[uint]*dproductionproctaskdocument.Response{}
	machineDocMap := map[uint][]*dproductionproctaskdocument.Response{}
	docs, ererr := dproductionproctaskdocument.FindInTaskIds(taskIds)
	if ererr != nil {
		logging.ErrorLogger.Errorf("find docs err ", ererr)
		return
	}
	for _, doc := range docs {
		if doc.DocType == "mainProgram" {
			mainProgramDocMap[doc.ProcInstID] = &doc.Response
		}
		if doc.DocType == "ospkg" {
			ospkgDocMap[doc.ProcInstID] = &doc.Response
		}
		if doc.DocType == "machine" {
			machineDocMap[doc.ProcInstID] = append(machineDocMap[doc.ProcInstID], &doc.Response)
		}
	}
	//查找主机数据
	hostsMap := map[uint][]*dproductionproctaskhost.ListResponse{}
	hosts, err := dproductionproctaskhost.FindInTaskIds(taskIds)
	if err != nil {
		logging.ErrorLogger.Errorf("find hosts err ", err)
		return
	}
	for _, host := range hosts {
		hostsMap[host.ProcInstID] = append(hostsMap[host.ProcInstID], host)
	}
	//查找扩展项目
	extensionMap := map[uint][]*dproductionproctaskextension.ExtensionGroup{}
	extensions, err := dproductionproctaskextension.FindInTaskIdsV2(taskIds)
	if err != nil {
		logging.ErrorLogger.Errorf("find extensions err ", err)
		return
	}
	for _, extension := range extensions {
		extensionMap[extension.ProcInstID] = append(extensionMap[extension.ProcInstID], extension)
	}
	for _, item := range items {
		item.Production = productionMap[item.ProductionID]
		item.User = userMap[item.StartUserID]
		item.Tasks = taskMap[item.ID]
		item.DoneTasks = taskDoneMap[item.ID]
		item.MainProgramDoc = mainProgramDocMap[item.ID]
		item.OspkgDoc = ospkgDocMap[item.ID]
		item.MachineDoc = machineDocMap[item.ID]
		item.PlatformTasks = platformTasksMap[item.ID]
		item.CpldTasks = cpldTasksMap[item.ID]
		item.ChiefTasks = chiefTasksMap[item.ID]
		item.Extensions = extensionMap[item.ID]
		item.TestTasks = testTasksMap[item.ID]
		item.HardwareTasks = hardwareTasksMap[item.ID]
		item.PmTasks = pmTasksMap[item.ID]
		item.SetmacHosts = hostsMap[item.ID]
	}
}

func formatResponse(item *Response) {
	productionIds := []uint{}
	procInstIDs := []uint{}
	userIds := []uint{}
	productionMap := map[uint]*dproduction.Response{}
	productionIds = append(productionIds, item.ProductionID)
	userIds = append(userIds, item.StartUserID)
	procInstIDs = append(procInstIDs, item.ID)
	taskIds := []uint{}

	productions, err := dproduction.FindInIds(productionIds)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return
	}

	for _, production := range productions {
		productionMap[production.ID] = &production.Response
	}

	users, _ := duser.FindSimpleInIds(userIds)
	var userMap = make(map[uint]*duser.ApprovalResponse)
	for _, user := range users {
		userMap[user.Id] = user
	}
	taskMap := map[uint][]*dproductionproctask.ListResponse{}
	tasks, err := dproductionproctask.FindInProcInstIDs(procInstIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("find tasks err ", err)
		return
	}
	for _, task := range tasks {
		taskMap[task.ProcInstID] = append(taskMap[task.Response.ProcInstID], task)
	}
	//已完成节点数据
	platformTasksMap := map[uint]*dproductionproctask.Response{}
	chiefTasksMap := map[uint]*dproductionproctask.Response{}
	cpldTasksMap := map[uint]*dproductionproctask.Response{}
	testTasksMap := map[uint]*dproductionproctask.Response{}
	hardwareTasksMap := map[uint]*dproductionproctask.Response{}
	pmTasksMap := map[uint]*dproductionproctask.Response{}
	taskDoneMap := map[uint][]*dproductionproctask.ListResponse{}
	tasks2, err := dproductionproctask.FindInProcInstIDs2(procInstIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("find tasks err ", err)
		return
	}
	for _, task := range tasks2 {
		taskIds = append(taskIds, task.ID)
		if task.NodeID == "platform_mg" {
			platformTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "chief_tester" {
			chiefTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "cpld_check" {
			cpldTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "test_check" {
			testTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "hardware_audit" {
			hardwareTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "pm_submit" {
			pmTasksMap[task.Response.ProcInstID] = &task.Response
		}
	}
	tasks3, err := dproductionproctask.FindInProcInstIDs3(procInstIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("find tasks err ", err)
		return
	}
	for _, task := range tasks3 {
		taskDoneMap[task.ProcInstID] = append(taskDoneMap[task.Response.ProcInstID], task)
	}
	//查找系统生成的文档
	mainProgramDocMap := map[uint]*dproductionproctaskdocument.Response{}
	ospkgDocMap := map[uint]*dproductionproctaskdocument.Response{}
	machineDocMap := map[uint][]*dproductionproctaskdocument.Response{}
	docs, ererr := dproductionproctaskdocument.FindInTaskIds(taskIds)
	if ererr != nil {
		logging.ErrorLogger.Errorf("find docs err ", ererr)
		return
	}
	for _, doc := range docs {
		if doc.DocType == "mainProgram" {
			mainProgramDocMap[doc.ProcInstID] = &doc.Response
			item.MainProgramDoc = &doc.Response
		}
		if doc.DocType == "ospkg" {
			ospkgDocMap[doc.ProcInstID] = &doc.Response
		}
		if doc.DocType == "machine" {
			machineDocMap[doc.ProcInstID] = append(machineDocMap[doc.ProcInstID], &doc.Response)
		}
	}
	//查找主机数据
	hostsMap := map[uint][]*dproductionproctaskhost.ListResponse{}
	hosts, err := dproductionproctaskhost.FindInTaskIds(taskIds)
	if err != nil {
		logging.ErrorLogger.Errorf("find hosts err ", err)
		return
	}
	for _, host := range hosts {
		hostsMap[host.ProcInstID] = append(hostsMap[host.ProcInstID], host)
	}
	//查找扩展项目
	extensionMap := map[uint][]*dproductionproctaskextension.ExtensionGroup{}
	extensions, err := dproductionproctaskextension.FindInTaskIdsV2(taskIds)
	if err != nil {
		logging.ErrorLogger.Errorf("find extensions err ", err)
		return
	}
	for _, extension := range extensions {
		extensionMap[extension.ProcInstID] = append(extensionMap[extension.ProcInstID], extension)
	}
	item.Production = productionMap[item.ProductionID]
	item.User = userMap[item.StartUserID]
	item.Tasks = taskMap[item.ID]
	item.DoneTasks = taskDoneMap[item.ID]
	item.MainProgramDoc = mainProgramDocMap[item.ID]
	item.OspkgDoc = ospkgDocMap[item.ID]
	item.MachineDoc = machineDocMap[item.ID]
	item.PlatformTasks = platformTasksMap[item.ID]
	item.CpldTasks = cpldTasksMap[item.ID]
	item.ChiefTasks = chiefTasksMap[item.ID]
	item.Extensions = extensionMap[item.ID]
	item.TestTasks = testTasksMap[item.ID]
	item.HardwareTasks = hardwareTasksMap[item.ID]
	item.PmTasks = pmTasksMap[item.ID]
	item.SetmacHosts = hostsMap[item.ID]
}

var ProductionProcInstUserMap = map[uint]uint{}
var ProductionIDProcInstIDMap = map[uint]uint{}

func UpdateFeatureProccessingCache(productionIds []uint) error {
	productionProcInsts := []*ListResponse{}
	if err := easygorm.GetEasyGormDb().Model(Model()).Where("production_id in ?", productionIds).Find(&productionProcInsts).Error; err != nil {
		return err
	}
	for _, inst := range productionProcInsts {
		ProductionProcInstUserMap[inst.ProductionID] = inst.StartUserID
		ProductionIDProcInstIDMap[inst.ProductionID] = inst.ID
	}
	return nil
}
