// Coredump记录自动化处理系统 - 集成代码示例
// 这些代码展示了如何将我们的系统集成到现有的飞书定时调度服务中

package examples

import (
	"context"
	"fmt"
	"sync"
	"time"

	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkbitable "github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
)

// ============================================================================
// 1. 扩展配置结构 (libs/config.go)
// ============================================================================

// 在现有的 FeiShuDoc 结构中添加新字段
type FeiShuDoc struct {
	// 现有字段
	Enable     bool   `default:"false"`
	AppId      string `default:""`
	AppSecret  string `default:""`
	BiAppToken string `default:""`

	// 新增Coredump自动同步配置
	CoredumpAutoSync CoredumpAutoSyncConfig `yaml:"coredump_auto_sync"`
}

type CoredumpAutoSyncConfig struct {
	Enable            bool                `yaml:"enable" default:"false"`
	CoredumpTableID   string              `yaml:"coredump_table_id"`
	CronExpr          string              `yaml:"cron_expr" default:"0 0 * * * *"`
	PageSize          int                 `yaml:"page_size" default:"100"`
	ProcessingTimeout int                 `yaml:"processing_timeout" default:"60"`
	MaxRunTime        int                 `yaml:"max_run_time" default:"30"`
	FieldNames        []string            `yaml:"field_names"`
	FieldMapping      FieldMappingConfig  `yaml:"field_mapping"`
	BugSystem         BugSystemConfig     `yaml:"bug_system"`
}

type FieldMappingConfig struct {
	SyncRequiredField     string `yaml:"sync_required_field" default:"是否需要同步Bug系统"`
	SyncStatusField       string `yaml:"sync_status_field" default:"是否已同步bug系统"`
	ProcessingStatusField string `yaml:"processing_status_field" default:"处理状态"`
	BugIDField           string `yaml:"bug_id_field" default:"Bug系统ID"`
	ProcessingTimeField  string `yaml:"processing_time_field" default:"处理时间"`
	ErrorMessageField    string `yaml:"error_message_field" default:"错误信息"`
	RetryCountField      string `yaml:"retry_count_field" default:"重试次数"`
	LastUpdatedField     string `yaml:"last_updated_field" default:"最后更新时间"`
	
	// 业务字段映射
	SNField                   string `yaml:"sn_field" default:"SN"`
	ComponentField            string `yaml:"component_field" default:"coredump组件"`
	SoftwareVersionField      string `yaml:"software_version_field" default:"软件版本"`
	DeviceModelField          string `yaml:"device_model_field" default:"设备型号"`
	CoredumpTimeField         string `yaml:"coredump_time_field" default:"coredump时间"`
	ComponentResponsibleField string `yaml:"component_responsible_field" default:"组件负责人"`
	ProcessResponsibleField   string `yaml:"process_responsible_field" default:"进程负责人"`
	DescriptionField          string `yaml:"description_field" default:"说明"`
}

type BugSystemConfig struct {
	BaseURL      string `yaml:"base_url"`
	Username     string `yaml:"username"`
	Password     string `yaml:"password"`
	ProjectKey   string `yaml:"project_key"`
	IssueType    string `yaml:"issue_type" default:"Bug"`
	Priority     string `yaml:"priority" default:"Medium"`
}

// ============================================================================
// 2. 在 service/schedule/feishu.go 中添加新的定时任务
// ============================================================================

var coredumpAutoSyncMutex sync.Mutex

// CoredumpAutoSync Coredump记录自动同步到Bug系统
func CoredumpAutoSync() {
	coredumpAutoSyncMutex.Lock()
	defer coredumpAutoSyncMutex.Unlock()

	// 检查功能是否启用
	if !libs.Config.FeiShuDoc.CoredumpAutoSync.Enable {
		return
	}

	logging.InfoLogger.Info("开始执行Coredump记录自动同步任务")
	startTime := time.Now()

	// 创建服务实例
	service := NewCoredumpAutoSyncService()

	// 执行同步处理
	result, err := service.ProcessCoredumpRecords()
	if err != nil {
		logging.ErrorLogger.Errorf("Coredump自动同步失败: %v", err)
		return
	}

	duration := time.Since(startTime)
	logging.InfoLogger.Infof("Coredump自动同步完成: 处理%d条记录, 成功%d条, 失败%d条, 耗时%v",
		result.FilteredRecords, result.SuccessRecords, result.FailedRecords, duration)

	// 记录统计信息
	if result.FailedRecords > 0 {
		logging.WarningLogger.Warnf("Coredump同步存在失败记录: %d条", result.FailedRecords)
	}
}

// ============================================================================
// 3. 业务逻辑服务实现 (application/controllers/openfeishu/coredump/service.go)
// ============================================================================

// CoredumpAutoSyncService Coredump自动同步服务
type CoredumpAutoSyncService struct {
	feishuClient  *lark.Client
	config        *CoredumpAutoSyncConfig
	filterBuilder *FilterBuilder
	statusManager *StatusManager
	bugSubmitter  *BugSubmitter
}

// NewCoredumpAutoSyncService 创建Coredump自动同步服务
func NewCoredumpAutoSyncService() *CoredumpAutoSyncService {
	config := &libs.Config.FeiShuDoc.CoredumpAutoSync
	feishuClient := GetFeishuClient() // 复用现有的飞书客户端

	return &CoredumpAutoSyncService{
		feishuClient:  feishuClient,
		config:        config,
		filterBuilder: NewFilterBuilder(config),
		statusManager: NewStatusManager(feishuClient, config),
		bugSubmitter:  NewBugSubmitter(config.BugSystem),
	}
}

// ProcessResult 处理结果
type ProcessResult struct {
	TaskID          string        `json:"task_id"`
	StartTime       time.Time     `json:"start_time"`
	EndTime         time.Time     `json:"end_time"`
	Duration        time.Duration `json:"duration"`
	TotalRecords    int           `json:"total_records"`
	FilteredRecords int           `json:"filtered_records"`
	SuccessRecords  int           `json:"success_records"`
	FailedRecords   int           `json:"failed_records"`
	Errors          []string      `json:"errors"`
}

// ProcessCoredumpRecords 处理Coredump记录
func (s *CoredumpAutoSyncService) ProcessCoredumpRecords() (*ProcessResult, error) {
	taskID := fmt.Sprintf("coredump_sync_%d", time.Now().Unix())
	logging.InfoLogger.Infof("[%s] 开始处理Coredump记录（使用服务端筛选优化）", taskID)

	result := &ProcessResult{
		TaskID:    taskID,
		StartTime: time.Now(),
	}

	// 步骤1: 使用服务端筛选直接读取符合条件的记录
	filteredRecords, err := s.readFilteredRecords()
	if err != nil {
		return nil, fmt.Errorf("读取筛选记录失败: %w", err)
	}

	result.TotalRecords = len(filteredRecords)
	result.FilteredRecords = len(filteredRecords)
	logging.InfoLogger.Infof("[%s] 服务端筛选获取到 %d 条待处理记录", taskID, len(filteredRecords))

	// 步骤2: 查找并重置超时记录
	timeoutRecords, err := s.findTimeoutRecords()
	if err != nil {
		logging.ErrorLogger.Errorf("[%s] 查找超时记录失败: %v", taskID, err)
	} else if len(timeoutRecords) > 0 {
		logging.InfoLogger.Infof("[%s] 发现 %d 条超时记录，正在重置", taskID, len(timeoutRecords))
		err = s.statusManager.ResetTimeoutRecords(timeoutRecords)
		if err != nil {
			logging.ErrorLogger.Errorf("[%s] 重置超时记录失败: %v", taskID, err)
		}
	}

	// 步骤3: 处理筛选后的记录
	for _, record := range filteredRecords {
		err := s.processRecord(record, result)
		if err != nil {
			logging.ErrorLogger.Errorf("[%s] 处理记录失败 [%s]: %v", taskID, record.RecordID, err)
			result.FailedRecords++
			result.Errors = append(result.Errors, fmt.Sprintf("记录 %s: %v", record.RecordID, err))

			// 更新为失败状态
			statusErr := s.statusManager.UpdateProcessingStatus(record.RecordID, "失败", "", err.Error())
			if statusErr != nil {
				logging.ErrorLogger.Errorf("[%s] 更新失败状态失败 [%s]: %v", taskID, record.RecordID, statusErr)
			}
		} else {
			result.SuccessRecords++
		}
	}

	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)

	logging.InfoLogger.Infof("[%s] 处理完成: 总计=%d, 成功=%d, 失败=%d, 耗时=%v",
		taskID, result.FilteredRecords, result.SuccessRecords, result.FailedRecords, result.Duration)

	return result, nil
}

// readFilteredRecords 使用服务端筛选读取记录
func (s *CoredumpAutoSyncService) readFilteredRecords() ([]*CoredumpRecord, error) {
	logging.InfoLogger.Info("开始使用服务端筛选读取Coredump记录")

	var allRecords []*CoredumpRecord
	pageToken := ""
	pageSize := s.config.PageSize
	totalApiCalls := 0

	// 构建筛选条件
	filter := s.filterBuilder.BuildCoredumpFilter()

	for {
		totalApiCalls++

		// 构建请求
		reqBuilder := larkbitable.NewSearchAppTableRecordReqBuilder().
			AppToken(libs.Config.FeiShuDoc.BiAppToken).
			TableId(s.config.CoredumpTableID)

		bodyBuilder := larkbitable.NewSearchAppTableRecordReqBodyBuilder().
			PageSize(pageSize).
			FieldNames(s.config.FieldNames).
			Filter(filter)

		if pageToken != "" {
			bodyBuilder.PageToken(pageToken)
		}

		req := reqBuilder.Body(bodyBuilder.Build()).Build()

		// 调用API
		resp, err := s.feishuClient.Bitable.AppTableRecord.Search(context.Background(), req)
		if err != nil {
			return nil, fmt.Errorf("调用飞书API失败 (第%d次调用): %w", totalApiCalls, err)
		}

		if !resp.Success() {
			return nil, fmt.Errorf("飞书API返回错误 (第%d次调用): %s", totalApiCalls, resp.Msg)
		}

		// 解析记录
		records, err := s.parseRecords(resp.Data.Items)
		if err != nil {
			return nil, fmt.Errorf("解析记录失败 (第%d次调用): %w", totalApiCalls, err)
		}

		allRecords = append(allRecords, records...)
		logging.DebugLogger.Debugf("第%d次API调用获取到 %d 条记录", totalApiCalls, len(records))

		// 检查是否还有更多数据
		if !resp.Data.HasMore {
			break
		}

		pageToken = *resp.Data.PageToken

		// API限流控制
		time.Sleep(100 * time.Millisecond)
	}

	logging.InfoLogger.Infof("服务端筛选完成: 共%d次API调用, 获取%d条记录",
		totalApiCalls, len(allRecords))

	return allRecords, nil
}

// ============================================================================
// 4. 筛选条件构建器 (application/controllers/openfeishu/coredump/filter_builder.go)
// ============================================================================

// FilterBuilder 筛选条件构建器
type FilterBuilder struct {
	config *CoredumpAutoSyncConfig
}

// NewFilterBuilder 创建筛选条件构建器
func NewFilterBuilder(config *CoredumpAutoSyncConfig) *FilterBuilder {
	return &FilterBuilder{
		config: config,
	}
}

// BuildCoredumpFilter 构建Coredump记录筛选条件
func (fb *FilterBuilder) BuildCoredumpFilter() map[string]interface{} {
	return map[string]interface{}{
		"conjunction": "and",
		"children": []map[string]interface{}{
			// 条件组1: 是否需要同步Bug系统 = "Y"
			{
				"conjunction": "and",
				"conditions": []map[string]interface{}{
					{
						"field_name": fb.config.FieldMapping.SyncRequiredField,
						"operator":   "is",
						"value":      []string{"Y"},
					},
				},
			},
			// 条件组2: 是否已同步bug系统 为空或 = "N"
			{
				"conjunction": "or",
				"conditions": []map[string]interface{}{
					{
						"field_name": fb.config.FieldMapping.SyncStatusField,
						"operator":   "isEmpty",
						"value":      []string{},
					},
					{
						"field_name": fb.config.FieldMapping.SyncStatusField,
						"operator":   "is",
						"value":      []string{"N"},
					},
				},
			},
			// 条件组3: 处理状态 为空、待处理或失败
			{
				"conjunction": "or",
				"conditions": []map[string]interface{}{
					{
						"field_name": fb.config.FieldMapping.ProcessingStatusField,
						"operator":   "isEmpty",
						"value":      []string{},
					},
					{
						"field_name": fb.config.FieldMapping.ProcessingStatusField,
						"operator":   "is",
						"value":      []string{"待处理"},
					},
					{
						"field_name": fb.config.FieldMapping.ProcessingStatusField,
						"operator":   "is",
						"value":      []string{"失败"},
					},
				},
			},
		},
	}
}

// ============================================================================
// 5. 在 service/schedule/schedule.go 中注册新任务
// ============================================================================

// 在 Start() 函数中添加新的定时任务注册
func Start() {
	// 现有任务注册...

	// 添加Coredump自动同步任务
	if libs.Config.FeiShuDoc.CoredumpAutoSync.Enable {
		cronExpr := libs.Config.FeiShuDoc.CoredumpAutoSync.CronExpr
		if cronExpr == "" {
			cronExpr = "0 0 * * * *" // 默认每小时执行一次
		}

		_, err := Cron.AddFunc(cronExpr, feishu.CoredumpAutoSync)
		if err != nil {
			logging.ErrorLogger.Errorf("添加Coredump自动同步定时任务失败: %v", err)
		} else {
			logging.InfoLogger.Infof("Coredump自动同步定时任务已启动: %s", cronExpr)
		}
	}
}

// ============================================================================
// 6. 数据模型定义 (application/controllers/openfeishu/coredump/models.go)
// ============================================================================

// CoredumpRecord Coredump记录
type CoredumpRecord struct {
	RecordID                string    `json:"record_id"`
	SN                      string    `json:"sn"`
	Component               string    `json:"component"`
	SoftwareVersion         string    `json:"software_version"`
	DeviceModel             string    `json:"device_model"`
	CoredumpTime            time.Time `json:"coredump_time"`
	ComponentResponsible    string    `json:"component_responsible"`
	ProcessResponsible      string    `json:"process_responsible"`
	Description             string    `json:"description"`
	SyncRequired            string    `json:"sync_required"`
	SyncStatus              string    `json:"sync_status"`
	ProcessingStatus        string    `json:"processing_status"`
	BugID                   string    `json:"bug_id"`
	ProcessingTime          time.Time `json:"processing_time"`
	ErrorMessage            string    `json:"error_message"`
	RetryCount              int       `json:"retry_count"`
	LastUpdated             time.Time `json:"last_updated"`
}

// BugData Bug提交数据
type BugData struct {
	Summary         string `json:"summary"`
	Description     string `json:"description"`
	IssueType       string `json:"issue_type"`
	Priority        string `json:"priority"`
	ProjectKey      string `json:"project_key"`
	Reporter        string `json:"reporter"`
	Assignee        string `json:"assignee"`
	Components      string `json:"components"`
	Labels          []string `json:"labels"`
	CustomFields    map[string]interface{} `json:"custom_fields"`
}

// ============================================================================
// 7. HTTP接口控制器 (可选，用于手动触发和状态查询)
// ============================================================================

// CoredumpController Coredump控制器
type CoredumpController struct {
	service *CoredumpAutoSyncService
}

// NewCoredumpController 创建控制器
func NewCoredumpController() *CoredumpController {
	return &CoredumpController{
		service: NewCoredumpAutoSyncService(),
	}
}

// ManualSync 手动触发同步
func (c *CoredumpController) ManualSync(ctx iris.Context) {
	logging.InfoLogger.Info("收到手动触发Coredump同步请求")

	result, err := c.service.ProcessCoredumpRecords()
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, result, "同步完成"))
}

// GetSyncStatus 获取同步状态
func (c *CoredumpController) GetSyncStatus(ctx iris.Context) {
	// 实现状态查询逻辑
	status := map[string]interface{}{
		"enabled":     libs.Config.FeiShuDoc.CoredumpAutoSync.Enable,
		"cron_expr":   libs.Config.FeiShuDoc.CoredumpAutoSync.CronExpr,
		"last_run":    "2025-01-14 10:00:00", // 从实际运行记录中获取
		"next_run":    "2025-01-14 11:00:00", // 从Cron计算得出
		"status":      "running",
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, status, "获取状态成功"))
}

// ============================================================================
// 8. 配置文件示例 (application.yml)
// ============================================================================

/*
feishudoc:
  enable: true
  appid: "cli_xxxxxxxxxxxxxxxx"
  appsecret: "xxxxxxxxxxxxxxxxxxxxxxxx"
  biapptoken: "your_app_token"
  
  coredump_auto_sync:
    enable: true
    coredump_table_id: "your_coredump_table_id"
    cron_expr: "0 0 * * * *"  # 每小时执行一次
    page_size: 100
    processing_timeout: 60    # 处理超时时间（分钟）
    max_run_time: 30         # 最大运行时间（分钟）
    
    field_names:
      - "SN"
      - "coredump组件"
      - "软件版本"
      - "设备型号"
      - "coredump时间"
      - "组件负责人"
      - "进程负责人"
      - "说明"
      - "是否需要同步Bug系统"
      - "是否已同步bug系统"
      - "处理状态"
      - "Bug系统ID"
      - "处理时间"
      - "错误信息"
      - "重试次数"
      - "最后更新时间"
    
    field_mapping:
      sync_required_field: "是否需要同步Bug系统"
      sync_status_field: "是否已同步bug系统"
      processing_status_field: "处理状态"
      bug_id_field: "Bug系统ID"
      processing_time_field: "处理时间"
      error_message_field: "错误信息"
      retry_count_field: "重试次数"
      last_updated_field: "最后更新时间"
      sn_field: "SN"
      component_field: "coredump组件"
      software_version_field: "软件版本"
      device_model_field: "设备型号"
      coredump_time_field: "coredump时间"
      component_responsible_field: "组件负责人"
      process_responsible_field: "进程负责人"
      description_field: "说明"
    
    bug_system:
      base_url: "https://your-jira-instance.com"
      username: "your_username"
      password: "your_password"
      project_key: "COREDUMP"
      issue_type: "Bug"
      priority: "Medium"
*/