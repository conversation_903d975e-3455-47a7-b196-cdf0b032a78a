package libs

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"time"

	"irisAdminApi/application/logging"
)

// PermissionValidator 权限验证器
type PermissionValidator struct {
	errorHandler *FeishuErrorHandler
}

// NewPermissionValidator 创建权限验证器
func NewPermissionValidator() *PermissionValidator {
	return &PermissionValidator{
		errorHandler: NewFeishuErrorHandler(),
	}
}

// CalendarInfo 日历信息
type CalendarInfo struct {
	CalendarID  string `json:"calendar_id"`
	Type        string `json:"type"`
	Role        string `json:"role"`
	Summary     string `json:"summary"`
	Description string `json:"description"`
}

// AppInfo 应用信息
type AppInfo struct {
	AppID     string `json:"app_id"`
	BotID     string `json:"bot_id"`
	HasBot    bool   `json:"has_bot"`
	BotStatus string `json:"bot_status"`
}

// ValidateCalendarID 验证日历ID格式
func (v *PermissionValidator) ValidateCalendarID(calendarID string) error {
	if calendarID == "" {
		return fmt.Errorf("日历ID不能为空")
	}

	// 检查是否为特殊值
	if calendarID == "primary" {
		return nil // primary是有效的特殊值
	}

	// 验证飞书日历ID格式
	// 格式1: <EMAIL> (应用日历)
	// 格式2: ou_xxx (用户主日历的简化格式)
	// 格式3: oc_xxx (群组日历)
	patterns := []string{
		`^feishu\.cn_[a-zA-Z0-9_]+@group\.calendar\.feishu\.cn$`,
		`^ou_[a-zA-Z0-9_]+$`,
		`^oc_[a-zA-Z0-9_]+$`,
	}

	for _, pattern := range patterns {
		matched, err := regexp.MatchString(pattern, calendarID)
		if err != nil {
			return fmt.Errorf("日历ID格式验证失败: %w", err)
		}
		if matched {
			return nil
		}
	}

	return fmt.Errorf("日历ID格式无效: %s，应为 <EMAIL> 或 ou_xxx 或 oc_xxx 格式", calendarID)
}

// ValidateTimezone 验证时区格式
func (v *PermissionValidator) ValidateTimezone(timezone string) error {
	if timezone == "" {
		return nil // 空值使用默认时区
	}

	// 常见的IANA时区格式验证
	validTimezones := []string{
		"Asia/Shanghai", "Asia/Beijing", "Asia/Hong_Kong", "Asia/Taipei",
		"UTC", "GMT", "America/New_York", "America/Los_Angeles",
		"Europe/London", "Europe/Paris", "Asia/Tokyo", "Asia/Seoul",
	}

	for _, valid := range validTimezones {
		if timezone == valid {
			return nil
		}
	}

	// 检查基本格式
	if matched, _ := regexp.MatchString(`^[A-Za-z]+/[A-Za-z_]+$`, timezone); matched {
		return nil // 基本格式正确，允许通过
	}

	return fmt.Errorf("时区格式无效: %s，应使用IANA时区标准，如 Asia/Shanghai", timezone)
}

// ValidateEventID 验证日程ID格式
func (v *PermissionValidator) ValidateEventID(eventID string) error {
	if eventID == "" {
		return fmt.Errorf("日程ID不能为空")
	}

	// 飞书日程ID格式通常为: uuid_timestamp 或 uuid
	patterns := []string{
		`^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}_\d+$`, // uuid_timestamp
		`^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$`,     // uuid
	}

	for _, pattern := range patterns {
		matched, err := regexp.MatchString(pattern, eventID)
		if err != nil {
			return fmt.Errorf("日程ID格式验证失败: %w", err)
		}
		if matched {
			return nil
		}
	}

	return fmt.Errorf("日程ID格式无效: %s", eventID)
}

// CheckBotCapability 检查应用机器人能力
func (v *PermissionValidator) CheckBotCapability(ctx context.Context, accessToken string) error {
	logging.InfoLogger.Info("开始检查应用机器人能力")

	// 使用正确的API端点获取当前应用信息
	// 根据飞书官方文档，使用 "me" 表示当前应用
	apiURL := "https://open.feishu.cn/open-apis/application/v6/applications/me?lang=zh_cn"

	req, err := http.NewRequestWithContext(ctx, "GET", apiURL, nil)
	if err != nil {
		return fmt.Errorf("创建机器人能力检查请求失败: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+accessToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送机器人能力检查请求失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取机器人能力检查响应失败: %w", err)
	}

	var apiResp struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			App struct {
				AppID     string `json:"app_id"`
				Status    int    `json:"status"`
				SceneType int    `json:"scene_type"`
				AppName   string `json:"app_name"`
				AvatarURL string `json:"avatar_url"`
				CreatorID string `json:"creator_id"`
			} `json:"app"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &apiResp); err != nil {
		return fmt.Errorf("解析机器人能力检查响应失败: %w", err)
	}

	if apiResp.Code != 0 {
		feishuErr := v.errorHandler.ParseAPIError(apiResp.Code, apiResp.Msg)
		logging.ErrorLogger.Error(v.errorHandler.FormatErrorForLogging(feishuErr, map[string]interface{}{
			"操作": "检查机器人能力",
		}))

		// 如果是字段验证失败错误，尝试使用备用方法
		if apiResp.Code == 99992402 {
			logging.InfoLogger.Info("应用信息API调用失败，尝试使用机器人信息API进行检查")
			return v.CheckBotCapabilityAlternative(ctx, accessToken)
		}

		return feishuErr
	}

	// 检查应用状态
	// 状态说明：0-停用状态，1-启用状态，2-未启用状态，3-未知状态
	if apiResp.Data.App.Status != 1 {
		statusDesc := map[int]string{
			0: "停用状态",
			1: "启用状态",
			2: "未启用状态",
			3: "未知状态",
		}
		desc, exists := statusDesc[apiResp.Data.App.Status]
		if !exists {
			desc = "未知状态"
		}
		return fmt.Errorf("应用状态异常: %s (状态码: %d)，请确保应用已启用", desc, apiResp.Data.App.Status)
	}

	logging.InfoLogger.Infof("应用机器人能力检查通过 - AppID: %s, 应用名称: %s, 状态: 启用",
		apiResp.Data.App.AppID, apiResp.Data.App.AppName)
	return nil
}

// CheckBotCapabilityAlternative 备用机器人能力检查方法
func (v *PermissionValidator) CheckBotCapabilityAlternative(ctx context.Context, accessToken string) error {
	logging.InfoLogger.Info("使用备用方法检查机器人能力")

	// 使用机器人信息API检查机器人能力
	apiURL := "https://open.feishu.cn/open-apis/bot/v3/info"

	req, err := http.NewRequestWithContext(ctx, "GET", apiURL, nil)
	if err != nil {
		return fmt.Errorf("创建机器人信息检查请求失败: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+accessToken)

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送机器人信息检查请求失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取机器人信息检查响应失败: %w", err)
	}

	var apiResp struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Bot  struct {
			ActivateStatus int    `json:"activate_status"`
			AppName        string `json:"app_name"`
			AvatarURL      string `json:"avatar_url"`
			OpenID         string `json:"open_id"`
		} `json:"bot"`
	}

	if err := json.Unmarshal(body, &apiResp); err != nil {
		return fmt.Errorf("解析机器人信息检查响应失败: %w", err)
	}

	if apiResp.Code != 0 {
		feishuErr := v.errorHandler.ParseAPIError(apiResp.Code, apiResp.Msg)
		logging.ErrorLogger.Error(v.errorHandler.FormatErrorForLogging(feishuErr, map[string]interface{}{
			"操作": "检查机器人信息",
		}))
		return feishuErr
	}

	// 检查机器人激活状态
	// 0: 初始化，租户待安装; 1: 租户停用; 2: 租户启用; 3: 安装后待启用; 4: 升级待启用; 5: license过期停用; 6: Lark套餐到期或降级停用
	if apiResp.Bot.ActivateStatus != 2 {
		statusDesc := map[int]string{
			0: "初始化，租户待安装",
			1: "租户停用",
			2: "租户启用",
			3: "安装后待启用",
			4: "升级待启用",
			5: "license过期停用",
			6: "Lark套餐到期或降级停用",
		}
		desc, exists := statusDesc[apiResp.Bot.ActivateStatus]
		if !exists {
			desc = "未知状态"
		}
		return fmt.Errorf("机器人状态异常: %s (状态码: %d)，请确保机器人已启用", desc, apiResp.Bot.ActivateStatus)
	}

	logging.InfoLogger.Infof("机器人能力检查通过 - 应用名称: %s, 机器人OpenID: %s, 状态: 租户启用",
		apiResp.Bot.AppName, apiResp.Bot.OpenID)
	return nil
}

// CheckCalendarPermission 检查日历权限
func (v *PermissionValidator) CheckCalendarPermission(ctx context.Context, accessToken, calendarID string) (*CalendarInfo, error) {
	logging.InfoLogger.Infof("开始检查日历权限: %s", calendarID)

	// 调用飞书API获取日历信息
	apiURL := fmt.Sprintf("https://open.feishu.cn/open-apis/calendar/v4/calendars/%s", calendarID)

	req, err := http.NewRequestWithContext(ctx, "GET", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建日历权限检查请求失败: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+accessToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送日历权限检查请求失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取日历权限检查响应失败: %w", err)
	}

	var apiResp struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			CalendarID  string `json:"calendar_id"`
			Type        string `json:"type"`
			Role        string `json:"role"`
			Summary     string `json:"summary"`
			Description string `json:"description"`
		} `json:"data"`
	}

	// 添加调试日志
	logging.DebugLogger.Debugf("日历权限检查API响应: %s", string(body))

	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, fmt.Errorf("解析日历权限检查响应失败: %w", err)
	}

	if apiResp.Code != 0 {
		feishuErr := v.errorHandler.ParseAPIError(apiResp.Code, apiResp.Msg)
		logging.ErrorLogger.Error(v.errorHandler.FormatErrorForLogging(feishuErr, map[string]interface{}{
			"操作":   "检查日历权限",
			"日历ID": calendarID,
		}))
		return nil, feishuErr
	}

	calendarInfo := &CalendarInfo{
		CalendarID:  apiResp.Data.CalendarID,
		Type:        apiResp.Data.Type,
		Role:        apiResp.Data.Role,
		Summary:     apiResp.Data.Summary,
		Description: apiResp.Data.Description,
	}

	// 添加详细的调试日志
	logging.DebugLogger.Debugf("解析到的日历信息: ID=%s, Type=%s, Role=%s, Summary=%s",
		calendarInfo.CalendarID, calendarInfo.Type, calendarInfo.Role, calendarInfo.Summary)

	// 验证日历类型
	if err := v.validateCalendarType(calendarInfo.Type); err != nil {
		return nil, err
	}

	// 验证权限级别
	if err := v.validateCalendarRole(calendarInfo.Role); err != nil {
		return nil, err
	}

	logging.InfoLogger.Infof("日历权限检查通过 - ID: %s, 类型: %s, 权限: %s",
		calendarInfo.CalendarID, calendarInfo.Type, calendarInfo.Role)

	return calendarInfo, nil
}

// validateCalendarType 验证日历类型
func (v *PermissionValidator) validateCalendarType(calendarType string) error {
	// 根据飞书官方文档，支持的日历类型包括：
	// - unknown: 未知类型
	// - primary: 用户或应用的主日历
	// - shared: 由用户或应用创建的共享日历
	// - google: 用户绑定的谷歌日历
	// - resource: 会议室日历
	// - exchange: 用户绑定的 Exchange 日历
	validTypes := []string{"unknown", "primary", "shared", "google", "resource", "exchange"}

	for _, validType := range validTypes {
		if calendarType == validType {
			return nil
		}
	}

	return fmt.Errorf("日历类型无效: %s，支持的类型: %v", calendarType, validTypes)
}

// validateCalendarRole 验证日历权限级别
func (v *PermissionValidator) validateCalendarRole(role string) error {
	validRoles := []string{"owner", "writer", "reader"}

	// 检查是否为有效权限
	for _, validRole := range validRoles {
		if role == validRole {
			break
		}
	}

	// 检查是否有足够的权限（需要writer或owner）
	if role != "owner" && role != "writer" {
		return fmt.Errorf("日历权限不足: %s，需要 writer 或 owner 权限", role)
	}

	return nil
}

// ValidateAttendees 验证参会人数据
func (v *PermissionValidator) ValidateAttendees(attendees []string) error {
	if len(attendees) == 0 {
		return nil // 允许无参会人
	}

	if len(attendees) > 1000 {
		return fmt.Errorf("参会人数量超限: %d，单次最多添加1000人", len(attendees))
	}

	// 验证每个参会人ID格式
	openIDPattern := `^ou_[a-zA-Z0-9_]+$`
	for i, attendee := range attendees {
		if attendee == "" {
			return fmt.Errorf("第%d个参会人ID为空", i+1)
		}

		matched, err := regexp.MatchString(openIDPattern, attendee)
		if err != nil {
			return fmt.Errorf("第%d个参会人ID格式验证失败: %w", i+1, err)
		}
		if !matched {
			return fmt.Errorf("第%d个参会人ID格式无效: %s，应为 ou_xxx 格式", i+1, attendee)
		}
	}

	return nil
}

// ValidateTimeRange 验证时间范围
func (v *PermissionValidator) ValidateTimeRange(startTime, endTime time.Time) error {
	if startTime.IsZero() || endTime.IsZero() {
		return fmt.Errorf("开始时间和结束时间不能为空")
	}

	if endTime.Before(startTime) {
		return fmt.Errorf("结束时间不能早于开始时间")
	}

	if endTime.Equal(startTime) {
		return fmt.Errorf("结束时间不能等于开始时间")
	}

	// 检查时间范围是否合理（不超过30天）
	duration := endTime.Sub(startTime)
	if duration > 30*24*time.Hour {
		return fmt.Errorf("日程时长不能超过30天")
	}

	// 检查是否为过去时间（允许1小时内的过去时间，考虑时区差异）
	now := time.Now()
	if startTime.Before(now.Add(-time.Hour)) {
		return fmt.Errorf("开始时间不能是过去时间")
	}

	return nil
}
