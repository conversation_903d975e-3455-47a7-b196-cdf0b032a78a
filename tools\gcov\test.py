import xlrd


def excel_merge_cell(sheet, row_index, col_index):
    """
    根据输入坐标，输出合并单元格或者普通单元格的值
    :param row_index int型，行数
    :param col_index int型，列数
    :return ：返回对应坐标的值
    """
    # merged_cells 获取当前表格所有合并单元格的位置信息 ，返回一个列
    merged = sheet.merged_cells
    for rlow, rhigh, clow, chigh in merged:  # 遍历表格中所有合并单元格位置信息
        if row_index >= rlow and row_index < rhigh:  # 行坐标判断
            if col_index >= clow and col_index < chigh:  # 列坐标判断
                # 如果满足条件，就把合并单元格第一个位置的值赋给其它合并单元格
                cell_value = sheet.cell_value(rlow, clow)
                return cell_value
            else:
                cell_value = sheet.cell_value(row_index, col_index)  # 如果不是合并单元格，直接输出普通单元格的值
                return cell_value

x = xlrd.open_workbook("components2.xlsx")
# if x.nsheets != 3 or x._sheet_names != ['封面', '帮助', '内容']:
#     print(fn, ":", "模板错误：sheet页面不规范")
#     continue
s = x.sheet_by_index(0)

sqls = []
# 读取合并单元格的列表
merged_cells = s.merged_cells


# 创建一个函数来检查给定的行和列是否在合并单元格范围内
def cell_value(sheet, row_index, col_index):
    for (rlow, rhigh, clow, chigh) in merged_cells:
        if (row_index >= rlow and row_index < rhigh) and (col_index >= clow and col_index < chigh):
            return sheet.cell_value(rlow, clow)
    return sheet.cell_value(row_index, col_index)


sqls = []

# 遍历所有行和列
for row_index in range(1, s.nrows):
    # 检查当前单元格是否为合并单元格
    component_packet = cell_value(s, row_index, 0)
    component = cell_value(s, row_index, 1)
    if not component_packet and component:
        component_packet = component
    elif component_packet and not component:
        component = component_packet
    elif not component_packet and not component:
        continue
    _paths = cell_value(s, row_index, 2)

    for _path in _paths.split("\n"):
        if _path.strip():
            sql = (component_packet, component, _path.replace("*", ""))
            if sql not in sqls:
                sqls.append(sql)


    # print(component_packet, component)
    # 打印单元格数据

#     row = s.row(rx)
#     if row[0].value:
#         component_packet = row[0].value
#     if row[1].value:
#         component = row[1].value
#     else:
#         component = component_packet
#     if "\n" in row[2].value:
#         paths = row[2].value.split("\n")
#         for path in paths:
#             sqls.append((component_packet, component, path.replace("*", "")))
#     else:
#         sqls.append((row[0].value,  row[1].value, row[2].value.replace("*", "")))

with open("222.sql", 'w', encoding='utf8') as f:
    for sql in sqls:
        print(sql)
        f.write(f'''insert into common_components (component_packet, component, path) values ("{sql[0]}", "{sql[1]}", "{sql[2]}");\n''')
