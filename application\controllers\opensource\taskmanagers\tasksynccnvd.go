package taskmanagers

import (
	"crypto/sha1"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"net/http"
	"net/http/cookiejar"
	"net/url"
	"regexp"
	"strings"
	"time"

	"github.com/dop251/goja"
	"github.com/gocolly/colly"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/opensource/dcnvdsharexmlfile"
	"irisAdminApi/service/dao/opensource/dvulnerabilitycnvd"
)

const userAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.69"
const hostURL = "https://www.cnvd.org.cn/"
const downloadURL = "https://www.cnvd.org.cn/shareData/download/"

type sharedVulnerabilityXML struct {
	XMLName       xml.Name `xml:"vulnerabilitys"`
	Text          string   `xml:",chardata"`
	Vulnerability []struct {
		Text   string `xml:",chardata"`
		Number string `xml:"number"`
		Cves   struct {
			Text string `xml:",chardata"`
			Cve  struct {
				Text      string `xml:",chardata"`
				CveNumber string `xml:"cveNumber"`
				CveUrl    string `xml:"cveUrl"`
			} `xml:"cve"`
		} `xml:"cves"`
		Title     string `xml:"title"`
		Serverity string `xml:"serverity"`
		Products  struct {
			Text    string   `xml:",chardata"`
			Product []string `xml:"product"`
		} `xml:"products"`
		IsEvent          string `xml:"isEvent"`
		SubmitTime       string `xml:"submitTime"`
		OpenTime         string `xml:"openTime"`
		ReferenceLink    string `xml:"referenceLink"`
		FormalWay        string `xml:"formalWay"`
		Description      string `xml:"description"`
		PatchName        string `xml:"patchName"`
		PatchDescription string `xml:"patchDescription"`
		DiscovererName   string `xml:"discovererName"`
	} `xml:"vulnerability"`
}

type JslClearanceS struct {
	Bts   []string `json:"bts"`
	Chars string   `json:"chars"`
	Ct    string   `json:"ct"`
	Ha    string   `json:"ha"`
	Tn    string   `json:"tn"`
	Vt    string   `json:"vt"`
	Wt    string   `json:"wt"`
}

func getJslClearanceS(data []byte) string {
	var obj = &JslClearanceS{}
	err := json.Unmarshal(data, &obj)
	if err != nil {
		return ""
	}
	for i := range obj.Chars {
		for j := range obj.Chars {
			jslClearanceS := obj.Bts[0] + string(obj.Chars[i]) + string(obj.Chars[j]) + obj.Bts[1]
			encrypted := ""
			if obj.Ha == "md5" {
				encrypted = libs.MD5(jslClearanceS)
			} else if obj.Ha == "sha1" {
				h := sha1.New()
				h.Write([]byte(jslClearanceS))
				encrypted = hex.EncodeToString(h.Sum(nil))
			} else if obj.Ha == "sha256" {
				h := sha256.New()
				h.Write([]byte(jslClearanceS))
				encrypted = hex.EncodeToString(h.Sum(nil))
			}
			if encrypted == obj.Ct {
				return jslClearanceS
			}
		}
	}
	return ""
}

func getCookie(cookieJar *cookiejar.Jar, urlStr string) {
	var jslClearanceS string

	c := colly.NewCollector()
	// get __jsluid_s cookie
	c.SetCookieJar(cookieJar)
	c.OnRequest(func(req *colly.Request) {
		req.Headers.Set("User-Agent", userAgent)
	})
	c.Visit(urlStr)

	// get unencrypted __jsl_clearance_s cookie
	time.Sleep(3 * time.Second)
	c = colly.NewCollector()
	c.SetCookieJar(cookieJar)
	c.OnRequest(func(req *colly.Request) {
		req.Headers.Set("User-Agent", userAgent)
	})
	c.OnError(func(res *colly.Response, err error) {
		if res.StatusCode == 521 {
			resScript := regexp.MustCompile("cookie=(.*?);location").Find(res.Body)
			cookieScript := strings.Split(string(resScript), ";location")[0]

			vm := goja.New()
			if result, _ := vm.RunString(cookieScript); result != nil {
				jslClearanceS = strings.Split(strings.Split(result.String(), ";")[0], "=")[1]
			}
		}
	})
	c.Visit(urlStr)

	parsedURL, _ := url.Parse(urlStr)
	cookies := cookieJar.Cookies(parsedURL)
	cookie := &http.Cookie{Name: "__jsl_clearance_s", Value: jslClearanceS}
	cookies = append(cookies, cookie)
	cookieJar.SetCookies(parsedURL, cookies)

	// // get encrypted __jsl_clearance_s cookie
	time.Sleep(3 * time.Second)
	c = colly.NewCollector()
	c.SetCookieJar(cookieJar)
	c.OnRequest(func(req *colly.Request) {
		req.Headers.Set("User-Agent", userAgent)
	})
	c.OnError(func(res *colly.Response, err error) {
		if res.StatusCode == 521 {
			resScript := regexp.MustCompile(";go\\((.*?)\\)").FindSubmatch(res.Body)
			jslClearanceS = getJslClearanceS(resScript[1])
		}
	})
	c.Visit(urlStr)

	cookies = cookieJar.Cookies(parsedURL)
	for _, cookie := range cookies {
		if cookie.Name == "__jsl_clearance_s" {
			cookie.Value = jslClearanceS
		}
	}
	cookieJar.SetCookies(parsedURL, cookies)
}

func cnvdSpider(syncTime time.Time, latestShareXMLFileRes *dcnvdsharexmlfile.Response) *dcnvdsharexmlfile.Response {
	logging.DebugLogger.Debugf("[CNVD共享数据同步] 开始同步, 同步时间: %s", syncTime)
	var index = 243
	if latestShareXMLFileRes != nil {
		logging.DebugLogger.Debugf("[CNVD共享数据同步] 最近同步地址:%s", latestShareXMLFileRes.DownloadURL)
		index = latestShareXMLFileRes.FileIndex + 1
	}

	cookieJar, _ := cookiejar.New(nil)
	getCookie(cookieJar, hostURL)

	for {
		c := colly.NewCollector()
		c.SetCookieJar(cookieJar)
		time.Sleep(3 * time.Second)
		downloadURL := fmt.Sprintf("https://www.cnvd.org.cn/shareData/download/%d", index)
		var startTime time.Time
		var endTime time.Time
		c.OnRequest(func(req *colly.Request) {
			req.Headers.Set("User-Agent", userAgent)
		})
		c.OnResponse(func(res *colly.Response) {
			if res.StatusCode == 200 {
				filename := ""
				if contentDisposition := res.Headers.Get("Content-Disposition"); contentDisposition != "" {
					filename = strings.Split(contentDisposition, "filename=")[1]
				}
				startEndTimeStr := strings.Split(filename, ".xml")[0]
				startEndTimeParts := strings.Split(startEndTimeStr, "_")
				startTime, _ = time.ParseInLocation("2006-01-02", startEndTimeParts[0], time.Local)
				endTime, _ = time.ParseInLocation("2006-01-02", startEndTimeParts[1], time.Local)
				newShareXMLFileRes := processCNVDSharedXMLFile(syncTime, index, downloadURL, startTime, endTime, res.Body)
				if newShareXMLFileRes != nil {
					latestShareXMLFileRes = newShareXMLFileRes
				}
			}
		})
		c.Visit(downloadURL)
		if syncTime.Sub(endTime) < 7*24*time.Hour {
			return latestShareXMLFileRes
		} else {
			index += 1
		}
	}
}

func processCNVDSharedXMLFile(syncTime time.Time, index int, downloadURL string,
	startTime, endTime time.Time, content []byte) *dcnvdsharexmlfile.Response {
	var latestCNVDId string
	var latestCNVDFileIndex int
	latestCNVDVulnerabilityRes, _ := dvulnerabilitycnvd.GetLatestCNVDVulnerability()
	if latestCNVDVulnerabilityRes != nil {
		logging.DebugLogger.Debugf("[CNVD共享数据同步] 最近同步的cnvd:%s", latestCNVDVulnerabilityRes.CnvdID)
		latestCNVDId = latestCNVDVulnerabilityRes.CnvdID
		latestCNVDFileIndex = latestCNVDVulnerabilityRes.FileIndex
	}
	var cnvdListRes []*dvulnerabilitycnvd.Response
	svx := new(sharedVulnerabilityXML)
	if err := xml.Unmarshal(content, svx); err != nil {
		return nil
	}
	for _, v := range svx.Vulnerability {
		if index > latestCNVDFileIndex || (index == latestCNVDFileIndex && v.Number > latestCNVDId) {
			cnvdRes := &dvulnerabilitycnvd.Response{}
			cnvdRes.CnvdID = v.Number
			cnvdRes.CveIDs = v.Cves.Cve.CveNumber
			cnvdRes.FormalWay = v.FormalWay
			cnvdRes.Description = v.Description
			cnvdRes.PatchName = v.PatchName
			cnvdRes.PatchDescription = v.PatchDescription
			cnvdRes.ReferenceLink = v.ReferenceLink
			cnvdRes.FileIndex = index
			cnvdListRes = append(cnvdListRes, cnvdRes)
		}
	}
	if err := dvulnerabilitycnvd.CreateCNVDVulnerabilities(cnvdListRes); err != nil {
		return nil
	}

	shareXMLFileRes := &dcnvdsharexmlfile.Response{}
	shareXMLFileRes.FileIndex = index
	shareXMLFileRes.DownloadURL = downloadURL
	shareXMLFileRes.StartTime = &startTime
	shareXMLFileRes.EndTime = &endTime
	shareXMLFileRes.SyncTime = &syncTime
	err := dcnvdsharexmlfile.CreateCNVDShareXMLFile(shareXMLFileRes)
	if err != nil {
		return nil
	}

	return shareXMLFileRes
}

func InitScheduledSyncCnvdTask() {
	logging.DebugLogger.Debug("[CNVD共享数据同步] 启动周期性同步任务")
	var cnvdSyncTaskInterval = time.Duration(libs.Config.OpenSource.CNVDSyncIntervalInHours) * time.Hour
	go func() {
		latestShareXMLFileRes, _ := dcnvdsharexmlfile.GetLatestCNVDShareXMLFile()
		var latestEndTime *time.Time
		if latestShareXMLFileRes != nil {
			latestEndTime = latestShareXMLFileRes.EndTime
			logging.DebugLogger.Debugf("[CNVD共享数据同步] 最近同步数据的结束时间: %s, 同步的地址: %s", latestEndTime, latestShareXMLFileRes.DownloadURL)
		}

		now := time.Now()
		if latestEndTime == nil || latestEndTime.IsZero() || now.Sub(*latestEndTime) > cnvdSyncTaskInterval {
			latestShareXMLFileRes = cnvdSpider(now, latestShareXMLFileRes)
		} else {
			t := time.NewTicker(cnvdSyncTaskInterval - now.Sub(*latestEndTime))
			select {
			case <-t.C:
				latestShareXMLFileRes = cnvdSpider(time.Now(), latestShareXMLFileRes)
			}
		}

		for {
			// 之后每周同步一次
			t := time.NewTicker(cnvdSyncTaskInterval)
			select {
			case <-t.C:
				latestShareXMLFileRes = cnvdSpider(time.Now(), latestShareXMLFileRes)
			}
		}
	}()
}
