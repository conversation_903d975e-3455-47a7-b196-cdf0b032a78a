package taskmanagers

import (
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/license/dauthtask"
	"sync"
	"time"

	"k8s.io/client-go/util/workqueue"
)

var DeviceAuthTaskManager *deviceAuthTaskManager

func InitDeviceAuthTaskManager() {
	if mgr, err := newDeviceAuthTaskManager(); err != nil {
		logging.ErrorLogger.Errorf("启动失败", err)
		return
	} else {
		DeviceAuthTaskManager = mgr
	}

	go func() {
		if res, err := dauthtask.ListAllUnfinishedAuthTask(); err == nil {
			for _, deviceAuthRes := range res {
				logging.DebugLogger.Debugf("添加未完成的授权任务: ID-<%d>", deviceAuthRes.ID)
				DeviceAuthTaskManager.AddDeviceAuthTask(deviceAuthRes)
			}
		}
	}()
	DeviceAuthTaskManager.run()
}

type deviceAuthTaskManager struct {
	basicAuthorization string
	adminToken         string
	adminTokenMutex    sync.RWMutex
	adminTokenExpires  int64
	userToken          string
	userTokenMutex     sync.RWMutex
	userTokenExpires   int64
	Queue              workqueue.DelayingInterface
}

func newDeviceAuthTaskManager() (*deviceAuthTaskManager, error) {
	mgr := &deviceAuthTaskManager{
		Queue: workqueue.NewNamedDelayingQueue("安全云设备自动授权队列"),
	}
	mgr.basicAuthorization = getBasicAuthorization()

	if adminToken, adminTokenExpires, err := getAdminToken(mgr.basicAuthorization); err != nil {
		logging.ErrorLogger.Errorf("安全云设备自动授权获取管理员token失败")
		return nil, err
	} else {
		mgr.adminToken = adminToken
		mgr.adminTokenExpires = adminTokenExpires
	}
	if userToken, userTokenExpires, err := getUserToken(mgr.basicAuthorization); err != nil {
		logging.ErrorLogger.Errorf("安全云设备自动授权获取用户token失败")
		return nil, err
	} else {
		mgr.userToken = userToken
		mgr.userTokenExpires = userTokenExpires
	}
	return mgr, nil
}

func (manager *deviceAuthTaskManager) AddDeviceAuthTask(res *dauthtask.Response) {
	manager.Queue.Add(res.ID)
}

func (manager *deviceAuthTaskManager) run() {
	go manager.refreshAdminToken()
	go manager.refreshUserToken()
	var deviceAuthTaskWorkers = libs.Config.License.DeviceAuthTaskWorkers
	for i := 0; i < deviceAuthTaskWorkers; i++ {
		go manager.runDeviceAuth()
	}
}

func (manager *deviceAuthTaskManager) refreshAdminToken() {
	for {
		now := time.Now().UnixMilli()
		if manager.adminTokenExpires != 0 {
			// 提前10分钟刷新adminToken
			duration := time.Duration(manager.adminTokenExpires-now-10*60*1000) * time.Millisecond
			time.Sleep(duration)
		}
		if adminToken, adminTokenExpires, err := getAdminToken(manager.basicAuthorization); err == nil {
			manager.adminTokenMutex.Lock()
			manager.adminToken = adminToken
			manager.adminTokenExpires = adminTokenExpires
			manager.adminTokenMutex.Unlock()
		}
		time.Sleep(60 * time.Second)
	}
}

func (manager *deviceAuthTaskManager) refreshUserToken() {
	for {
		now := time.Now().UnixMilli()
		if manager.userTokenExpires != 0 {
			// 提前10分钟刷新userToken
			duration := time.Duration(manager.userTokenExpires-now-10*60*1000) * time.Millisecond
			time.Sleep(duration)
		}
		if userToken, userTokenExpires, err := getUserToken(manager.basicAuthorization); err == nil {
			manager.userTokenMutex.Lock()
			manager.userToken = userToken
			manager.userTokenExpires = userTokenExpires
			manager.userTokenMutex.Unlock()
		}
		time.Sleep(60 * time.Second)
	}
}

func (manager *deviceAuthTaskManager) runDeviceAuth() {
	for manager.processNextDeviceAuthTask() {
	}
}

func (manager *deviceAuthTaskManager) processNextDeviceAuthTask() bool {
	item, quit := manager.Queue.Get()
	if quit {
		return false
	}
	authTaskId, ok := item.(uint)
	if !ok || authTaskId == 0 {
		// item出错, 删除
		manager.Queue.Done(item)
		return true
	}

	deviceAuthRes, err := dauthtask.GetAuthTask(authTaskId, 0)
	if err != nil {
		manager.Queue.Add(item)
		return true
	} else if deviceAuthRes == nil {
		manager.Queue.Done(item)
		return true
	}

	// step 1: 获取管理员token
	//manager.adminTokenMutex.RLock()
	//adminToken := manager.adminToken
	//manager.adminTokenMutex.RUnlock()
	adminToken, _, err := getAdminToken(manager.basicAuthorization)
	if err != nil {
		finishedAuthTask(authTaskId, false, "", "", "", err.Error())
		return true
	} else if adminToken == "" {
		finishedAuthTask(authTaskId, false, "", "", "", "获取到空的管理员token")
		return true
	}

	// step 2: 防火墙设备合法化接口
	if err := deviceInfoAddDevice(adminToken, deviceAuthRes); err != nil {
		finishedAuthTask(authTaskId, false, "", "", "", err.Error())
		return true
	}

	// step 3: 尝试解绑设备
	if err := unbindDevices(adminToken, deviceAuthRes); err != nil {
		finishedAuthTask(authTaskId, false, "", "", "", err.Error())
		return true
	}

	// step 4: 生产授权
	ticketCode, snCode := generateTicketCodeAndSnCode()
	if err := productionAddProductRecord(adminToken, ticketCode, snCode, deviceAuthRes); err != nil {
		finishedAuthTask(authTaskId, false, "", "", "", err.Error())
		return true
	}

	// step 5: 获取授权码
	authCode, err := getAuthCode(adminToken, snCode)
	if err != nil {
		finishedAuthTask(authTaskId, false, "", ticketCode, snCode, err.Error())
		return true
	}

	// step 6: 获取用户token
	//manager.userTokenMutex.RLock()
	//userToken := manager.userToken
	//manager.userTokenMutex.RUnlock()
	userToken, _, err := getUserToken(manager.basicAuthorization)
	if err != nil {
		finishedAuthTask(authTaskId, false, authCode, ticketCode, snCode, err.Error())
		return true
	} else if userToken == "" {
		finishedAuthTask(authTaskId, false, authCode, ticketCode, snCode, "获取到空的用户token")
		return true
	}

	// step 7: 激活授权码
	if err := activeAuthCode(userToken, deviceAuthRes.DeviceSn, authCode); err != nil {
		finishedAuthTask(authTaskId, false, authCode, ticketCode, snCode, err.Error())
		return true
	}

	finishedAuthTask(authTaskId, true, authCode, ticketCode, snCode, "")
	return true
}

func finishedAuthTask(authTaskId uint, isActive bool, authCode, ticketCode, snCode, message string) {
	updates := map[string]interface{}{
		"finished":  true,
		"is_active": isActive,
	}
	if authCode != "" {
		updates["auth_code"] = authCode
	}
	if ticketCode != "" {
		updates["ticket_code"] = ticketCode
	}
	if snCode != "" {
		updates["sn_code"] = snCode
	}
	if message != "" {
		updates["message"] = message
	}
	_ = dauthtask.UpdateAuthTask(authTaskId, updates)
}
