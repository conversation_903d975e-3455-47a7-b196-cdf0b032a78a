package datasync

type Resource struct {
	// models.ModelBase
	ResourceID     int     `gorm:"primarykey; autoIncrement:false" json:"resource_id" `
	DivisionName   string  `gorm:"not null; type:varchar(100)" json:"division_name" update:"1"`
	ProjectName    string  `gorm:"not null; type:varchar(200)" json:"project_name" update:"1"`
	DepartmentName string  `gorm:"not null; type:varchar(100)" json:"department_name" update:"1"`
	GroupName      string  `gorm:"not null; type:varchar(100)" json:"group_name" update:"1"`
	UserName       string  `gorm:"not null; type:varchar(100)" json:"user_name" update:"1"`
	WorkClassName  string  `gorm:"not null; type:varchar(200)" json:"work_class_name" update:"1"`
	StageName      string  `gorm:"not null; type:varchar(200)" json:"stage_name" update:"1"`
	StageNameClean string  `gorm:"not null; type:varchar(200)" json:"stage_name_clean" update:"1"`
	ActivityName   string  `gorm:"not null; type:varchar(200)" json:"activity_name" update:"1"`
	TaskName       string  `gorm:"not null; type:varchar(200)" json:"task_name" update:"1"`
	ProduceValue   string  `gorm:"not null; type:varchar(60)" json:"produce_Value" update:"1"`
	ReportDate     string  `gorm:"not null; type:varchar(60)" json:"report_date" update:"1"`
	Year           int     `gorm:"not null" json:"year" update:"1"`
	Month          int     `gorm:"not null" json:"month" update:"1"`
	WorkTime       float32 `gorm:"not null" json:"work_time" update:"1"`
	AddTime        float32 `gorm:"not null" json:"add_time" update:"1"`
	TotalTime      float32 `gorm:"not null" json:"total_time" update:"1"`
	Remarks        string  `gorm:"not null" json:"remarks" update:"1"`
	TemplateID     int     `json:"template_id" update:"1"`
	WorkpacketName string  `gorm:"not null; type:varchar(200)" json:"workpacket_name" update:"1"`
	// OverTimeAt          *time.Time `json:"over_time_at"`
}
