package appmergerequest

import (
	"context"
	"fmt"
	"time"

	"irisAdminApi/application/libs"

	larkbitable "github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
	"github.com/pkg/errors"
)

func CleanTable(tableID string) error {
	// 创建请求对象

	for {
		recordIDs := []string{}
		queryReqBuilder := larkbitable.NewListAppTableRecordReqBuilder().
			AppToken(libs.Config.FeiShuDoc.BiAppToken).
			TableId(tableID).
			PageSize(500)

		queryReq := queryReqBuilder.Build()

		// 发起请求
		queryResp, err := FeiShuClient.Bitable.V1.AppTableRecord.List(context.Background(), queryReq)
		// 处理错误
		if err != nil {
			return errors.Wrap(err, "")
		}

		// 服务端错误处理
		if !queryResp.Success() {
			return fmt.Errorf("飞书服务端返回错误 %v %v %v", queryResp.Code, queryResp.Msg, queryResp.RequestId())
		}

		for _, item := range queryResp.Data.Items {
			recordIDs = append(recordIDs, *item.RecordId)
		}

		if len(recordIDs) == 0 {
			break
		}

		deleteReq := larkbitable.NewBatchDeleteAppTableRecordReqBuilder().
			AppToken("KZQCbsWefa5e3MsamMccOYYPnMr").
			TableId(tableID).
			Body(larkbitable.NewBatchDeleteAppTableRecordReqBodyBuilder().Records(recordIDs).Build()).
			Build()
		deleteResp, err := FeiShuClient.Bitable.V1.AppTableRecord.BatchDelete(context.Background(), deleteReq)
		// 处理错误
		if err != nil {
			return errors.Wrap(err, "")
		}

		// 服务端错误处理
		if !deleteResp.Success() {
			return fmt.Errorf("飞书服务端返回错误 %v %v %v", deleteResp.Code, deleteResp.Msg, deleteResp.RequestId())
		}

		time.Sleep(1 * time.Second)
	}
	return nil
}
