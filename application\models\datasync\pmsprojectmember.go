package datasync

import "irisAdminApi/application/models"

type PmsProjectMember struct {
	models.ModelBase
	ProjectID   uint   `gorm:"not null" json:"project_id" udpate:"1"`
	ProjectName string `gorm:"not null; type:varchar(200)" json:"project_name"  update:"1"`
	UserID      uint   `gorm:"not null" json:"user_id" udpate:"1"`
	Username    string `gorm:"not null; type:varchar(200); default:''" json:"username"  update:"1"`
	UserRole    string `gorm:"not null; type:varchar(200); default:''" json:"user_role"  update:"1"`
	RoleRemark  string `gorm:"not null; type:varchar(200); default:''" json:"role_remark"  update:"1"`
	Disabled    bool   `grom:"not null; default:false" json:"disabled" update:"1"`
	CasUserID   string `gorm:"not null; type:varchar(100); default:''" json:"cas_user_id"  update:"1"`
}
