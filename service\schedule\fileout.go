package schedule

import (
	"irisAdminApi/service/cache"
	"irisAdminApi/service/dao/user/duser"
	"time"
)

func UpdateUserCache() {
	duser.UpdateUserMapCache()
	t := time.NewTicker(15 * time.Minute)
	for {
		<-t.C
		duser.UpdateUserMapCache()
	}
}

func UpdateFileCache() {
	cache.GetAutoAuditFileCache()
	cache.GetAutoAuditMd5Cache()

	t := time.NewTicker(15 * time.Minute)
	for {
		<-t.C
		cache.GetAutoAuditMd5Cache()
		cache.GetAutoAuditFileCache()
	}
}
