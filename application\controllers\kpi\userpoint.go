package kpi

import (
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/kpi/dcontribution"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

func GetUserPoints(ctx iris.Context) {
	// id, err := dao.GetAuthId(ctx)
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dcontribution.ListResponse{}, ctx, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetUserPoint(ctx iris.Context) {
	info := dcontribution.Response{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

func CreateUserPoint(ctx iris.Context) {
	// program, err := buildfarmProject.FindProduct()
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("create release project get err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未找到工程文件，请联系管理员"))
	// 	return
	// }

	userId, _ := dao.GetAuthId(ctx)
	request := &dcontribution.Request{}
	if err := ctx.ReadJSON(request); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	err := dao.Create(&dcontribution.Response{}, ctx, map[string]interface{}{
		"CreatedAt":          time.Now(),
		"Description":        request.Description,
		"ContributedAt":      request.ContributedAt,
		"ContributionTypeID": request.ContributionTypeID,
		"UserID":             userId,
		"Status":             request.Status,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, request, response.NoErr.Msg))
	return
}

func UpdateUserPoint(ctx iris.Context) {
	// uId, err := dao.GetAuthId(ctx)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
	// 	return
	// }

	info := dcontribution.Response{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	request := &dcontribution.Request{}
	if err := ctx.ReadJSON(request); err != nil {
		logging.ErrorLogger.Errorf("create approval read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	object := map[string]interface{}{
		"UpdatedAt":          time.Now(),
		"Description":        request.Description,
		"ContributedAt":      request.ContributedAt,
		"ContributionTypeID": request.ContributionTypeID,
		"Status":             request.Status,
	}

	err = dao.Update(&dcontribution.Response{}, ctx, object)

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}
