package dcommonntosfileworkpackage

import (
	"database/sql"
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/datasync"
	"irisAdminApi/service/dao/datasync/dpmsworkpacketinfo"
	"irisAdminApi/service/dao/mergerequest/dmergerequest"

	"gorm.io/gorm/clause"
)

const ModelName = "NTOS工作包组件映射关系"

type CommonNtosFileWorkPackage struct {
	datasync.CommonNtosFileWorkPackage
}

type ListResponse struct {
	CommonNtosFileWorkPackage
	Request []*RequestResponse `gorm:"-" json:"request"`
}

type RequestResponse struct {
	WorkpackageName string `gorm:"-" json:"workpackage_name"`
	RequestName     string `gorm:"-" json:"request_name"`
	Owner           string `gorm:"-" json:"owner"`
}

type Request struct {
	Id uint `json:"id"`
}

func (this *CommonNtosFileWorkPackage) ModelName() string {
	return ModelName
}

func Model() *datasync.CommonNtosFileWorkPackage {
	return &datasync.CommonNtosFileWorkPackage{}
}

func (this *CommonNtosFileWorkPackage) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *CommonNtosFileWorkPackage) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *CommonNtosFileWorkPackage) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *CommonNtosFileWorkPackage) CreateV2(object interface{}) error {
	return nil
}

func (this *CommonNtosFileWorkPackage) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *CommonNtosFileWorkPackage) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *CommonNtosFileWorkPackage) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *CommonNtosFileWorkPackage) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *CommonNtosFileWorkPackage) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *CommonNtosFileWorkPackage) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func BatchCreate(objects []map[string]interface{}) error {
	return easygorm.GetEasyGormDb().Model(Model()).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "merge_request_id"}, {Name: "full_path"}},
		DoUpdates: clause.AssignmentColumns([]string{"work_package"}),
	}).Create(&objects).Error
}

func All(projectName string) ([]*ListResponse, error) {
	var items []*ListResponse
	workPackageMap := map[string][]*RequestResponse{}
	workPackages, err := dpmsworkpacketinfo.AllProjectWorkPacket(projectName)
	if err != nil {
		return items, err
	}

	for _, workPackage := range workPackages {
		if workPackage.PmsRequest == nil {
			workPackageMap[workPackage.WorkPacketName] = append(workPackageMap[workPackage.WorkPacketName], &RequestResponse{
				RequestName:     "需求取消",
				WorkpackageName: workPackage.WorkPacketName,
				Owner:           workPackage.PacketManagerName,
			})
		} else {
			workPackageMap[workPackage.WorkPacketName] = append(workPackageMap[workPackage.WorkPacketName], &RequestResponse{
				RequestName:     workPackage.PmsRequest.RequestName,
				WorkpackageName: workPackage.WorkPacketName,
				Owner:           workPackage.PacketManagerName,
			})
		}
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("release_project = ?", projectName).Find(&items).Error
	for _, item := range items {
		if _, ok := workPackageMap[item.WorkPackage]; ok {
			item.Request = workPackageMap[item.WorkPackage]
		} else {
			item.Request = []*RequestResponse{{
				RequestName:     "需求取消",
				WorkpackageName: item.WorkPackage + "(工作包已删除)",
				Owner:           "",
			}}
		}
	}
	return items, err
}

func FindUserFiles(userId uint, releaseProjectID, workPackageID, keyword string) (map[string]interface{}, error) {
	items := []*ListResponse{}
	var count int64
	filter := easygorm.GetEasyGormDb().Model(&dmergerequest.MergeRequest{}).Where("release_project_id = ?", releaseProjectID)
	if workPackageID != "" {
		filter = filter.Where("work_package_id = ?", workPackageID)
	}
	filter = filter.Select("id")

	err := easygorm.GetEasyGormDb().Model(Model()).Unscoped().
		Where("full_path like ?", fmt.Sprintf("%%%s%%", keyword)).
		Where("merge_request_id in (?)", filter).
		Count(&count).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": items, "total": count}
	return list, nil
}

func BatchUpdate(ids []uint, workpackage string) error {
	return easygorm.GetEasyGormDb().Model(Model()).Unscoped().Where("id in ?", ids).Updates(map[string]interface{}{"work_package": workpackage, "deleted_at": sql.NullTime{Valid: false}}).Error
}

func BatchDelete(ids []uint, workpackage string) error {
	return easygorm.GetEasyGormDb().Model(Model()).Where("id in ?", ids).Delete(Model()).Error
}
