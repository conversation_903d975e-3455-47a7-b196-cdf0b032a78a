# 品控系统数据同步功能技术设计文档

## 1. 系统架构概述

### 1.1 系统目标
品控系统数据同步功能旨在实现品控系统与飞书多维表格之间的数据自动同步，确保数据的实时性、准确性和一致性。

### 1.2 核心架构
```
品控系统API ←→ 数据同步服务 ←→ 飞书多维表格API
                    ↓
              解决方案API
                    ↓
              闭环管理API
```

### 1.3 技术栈
- **编程语言**: Go 1.19+
- **HTTP客户端**: 标准库 net/http
- **JSON处理**: 标准库 encoding/json
- **日志系统**: 自定义logging包
- **飞书SDK**: 官方Go SDK

## 2. 数据结构定义

### 2.1 品控数据结构

#### QualityControlItem - 品控单项数据
```go
type QualityControlItem struct {
    // 基础信息
    ID         int    `json:"id"`         // 品控单号
    Title      string `json:"title"`      // 标题
    CreateDate string `json:"createDate"` // 创建时间
    ModifyDate string `json:"modifyDate"` // 修改时间

    // 责任人信息
    FaultUserName   string `json:"faultUserName"`   // 故障责任人
    QualityUserName string `json:"qualityUserName"` // 品控责任人
    SubmitterName   string `json:"submitterName"`   // 提交人

    // 状态信息
    PzkzStatus     string `json:"pzkzStatus"`     // 品控状态
    PzkzTypeName   string `json:"pzkzTypeName"`   // 品控类别（技术咨询、问题单等）
    IsFinish       int    `json:"isFinish"`       // 是否完成 (0/1)
    SolutionFinish int    `json:"solutionFinish"` // 解决方案是否完成 (0/1)

    // 时间相关
    TimeDay      float64 `json:"timeDay"`      // 超期天数
    DealDay      float64 `json:"dealDay"`      // 处理天数
    SolutionDate string  `json:"solutionDate"` // 解决方案时间
    FinishDate   string  `json:"finishDate"`   // 完成时间

    // 故障等级（修复：使用正确的字段）
    FaultLvlBaseName string `json:"faultLvlBaseName"` // 基础故障等级名称
    FaultLvlName     string `json:"faultLvlName"`     // 当前故障等级名称 ✅ 使用此字段
    FaultLvlTime     string `json:"faultLvlTime"`     // 故障等级时间（小时）
    FaultLvlBaseTime string `json:"faultLvlBaseTime"` // 基础故障等级时间（小时）
    FaultLvl         string `json:"faultLvl"`         // 故障等级代码
    FaultLvlBase     int    `json:"faultLvlBase"`     // 基础故障等级代码

    // 故障原因
    DevFaultReasonName string `json:"devFaultReasonName"` // 研发故障原因
    DevFaultReason     int    `json:"devFaultReason"`     // 研发故障原因代码
    CsmFaultReasonName string `json:"csmFaultReasonName"` // CSM故障原因
    CsmFaultReason     int    `json:"csmFaultReason"`     // CSM故障原因代码

    // 处理后的字段（API处理时添加）
    IsQualityProblemClosed string `json:"isQualityProblemClosed"` // 品控问题是否关闭
    ProblemTimeliness      string `json:"problemTimeliness"`      // 问题时效
    OverdueStatus          string `json:"overdueStatus"`          // 超期情况
}
```

#### ProcessedQualityControlItem - 处理后的品控数据
```go
type ProcessedQualityControlItem struct {
    QualityControlItem
    // 继承所有QualityControlItem字段
}
```

### 2.2 字段映射结构

#### FieldMapping - 字段映射配置
```go
type FieldMapping struct {
    FeishuField   string `json:"feishu_field"`   // 飞书字段名
    QualityField  string `json:"quality_field"`  // 品控系统字段名
    ClosedField   string `json:"closed_field"`   // 闭环管理字段名
    SolutionField string `json:"solution_field"` // 解决方案字段名
    DataType      string `json:"data_type"`      // 数据类型
    DefaultValue  string `json:"default_value"`  // 默认值
    Required      bool   `json:"required"`       // 是否必填
}
```

#### 默认字段映射配置
```go
func getDefaultFieldMappings() []FieldMapping {
    return []FieldMapping{
        {
            FeishuField:  "品控责任人",
            QualityField: "faultUserName",
            DataType:     "string",
            Required:     true,
        },
        {
            FeishuField:  "品控问题是否关闭",
            QualityField: "pzkzStatus",
            DataType:     "string",
            DefaultValue: "未关闭",
            Required:     true,
        },
        {
            FeishuField:  "超期情况",
            QualityField: "dealDay",
            DataType:     "float",
            Required:     false,
        },
        {
            FeishuField:  "问题时效",
            QualityField: "timeDay",
            DataType:     "float",
            Required:     false,
        },
        {
            FeishuField:  "品控问题是否闭环",
            ClosedField:  "closedStatus",
            DataType:     "string",
            DefaultValue: "未闭环",
            Required:     false,
        },
        {
            FeishuField:   "是否提供解决方案",
            SolutionField: "isFinish",
            DataType:      "string",
            DefaultValue:  "否",
            Required:      false,
        },
        // 注意：品控类别字段暂不同步到飞书，但PzkzTypeName仍用于内部智能判断
    }
}
```

### 2.3 解决方案数据结构

#### SolutionResponse - 解决方案API响应
```go
type SolutionResponse struct {
    DealDay          int                    `json:"dealDay"`
    AccessoryList    []interface{}          `json:"accessoryList"`
    QuestionID       int                    `json:"questionId"`
    ProgressDate     string                 `json:"progressDate"`
    ExcellentCase    int                    `json:"excellentCase"`
    PlanFinishDay    string                 `json:"planFinishDay"`
    IsDown           string                 `json:"isDown"`
    SolutionTypeName string                 `json:"solutionTypeName"`
    Disabled         int                    `json:"disabled"`
    ID               int                    `json:"id"`
    SolutionType     int                    `json:"solutionType"`
    Email            string                 `json:"email"`
    CreateDate       string                 `json:"createDate"`
    SolutionDesc     string                 `json:"solutionDesc"`
    ModifyDate       string                 `json:"modifyDate"`
    EntityType       string                 `json:"entityType"`
    DownProgress     string                 `json:"downProgress"`
    NeedWatch        string                 `json:"needWatch"`
    ProgressList     []interface{}          `json:"progressList"`
    IsFinish         int                    `json:"isFinish"` // 关键字段：0=否，1=是
    UserName         string                 `json:"userName"`
    Version          string                 `json:"version"`
    RejectTimes      int                    `json:"rejectTimes"`
    Prescription     int                    `json:"prescription"`
    Progress         string                 `json:"progress"`
    DayNum           int                    `json:"dayNum"`
    ReviewState      int                    `json:"reviewState"`
    QuestionType     string                 `json:"questionType"`
    ChargeUserID     int                    `json:"chargeUserId"`
}
```

### 2.4 飞书数据结构

#### FeishuTableItem - 飞书表格记录
```go
type FeishuTableItem struct {
    RecordID string                 `json:"record_id"`
    Fields   map[string]interface{} `json:"fields"`
}
```

#### RecordBasedIndex - 基于记录的索引（解决重复品控单号问题）
```go
type RecordBasedIndex struct {
    // key: 品控单号, value: 对应的飞书记录列表
    QualityNumberToRecords map[string][]FeishuTableItem
    // key: RecordID, value: 品控单号
    RecordIDToQualityNumber map[string]string
}
```

## 3. 核心功能模块

### 3.1 数据同步主流程

#### processAndUpdateDataV2 - 主处理流程
```go
func (p *QualityControlProcessor) processAndUpdateDataV2() (*SyncStatistics, error) {
    // 记录开始时间
    startTime := time.Now()
    p.statistics.StartTime = startTime.Format("2006-01-02 15:04:05")

    defer func() {
        // 记录结束时间和耗时
        endTime := time.Now()
        p.statistics.EndTime = endTime.Format("2006-01-02 15:04:05")
        p.statistics.Duration = endTime.Sub(startTime).String()
        logging.InfoLogger.Infof("品控数据同步完成，耗时: %s", p.statistics.Duration)
    }()

    // 步骤1：获取飞书表格数据并构建索引（包含品控责任人字段状态缓存）
    logging.InfoLogger.Info("步骤1: 获取飞书表格数据并预先缓存品控责任人字段状态")
    feishuRecords, qualityResponsiblePersonStatusMap, err := GetFeishuQualityRecords()
    if err != nil {
        return nil, fmt.Errorf("获取飞书数据失败: %v", err)
    }

    p.statistics.TotalRecords = len(feishuRecords)
    logging.InfoLogger.Infof("获取到 %d 条飞书记录", p.statistics.TotalRecords)

    // 构建基于RecordID的索引（解决重复品控单号问题）
    recordIndex := BuildRecordBasedIndex(feishuRecords)

    // 步骤2：获取品控系统数据
    logging.InfoLogger.Info("步骤2: 获取品控系统数据")
    qualityNumbers := recordIndex.GetUniqueQualityNumbers()
    logging.InfoLogger.Infof("需要获取 %d 个唯一品控单号的数据", len(qualityNumbers))

    // 调用品控API获取数据
    qualityData, err := p.getQualityControlData(qualityNumbers)
    if err != nil {
        return nil, fmt.Errorf("获取品控数据失败: %v", err)
    }

    logging.InfoLogger.Infof("获取到 %d 条品控数据", len(qualityData))

    // 步骤3：获取闭环管理数据（如果启用）
    var closedLoopData map[string]map[int]*ClosedLoopResponse
    if p.enableClosedLoop {
        logging.InfoLogger.Info("步骤3: 获取闭环管理数据")
        closedLoopManager := NewClosedLoopManager()
        closedLoopData, err = closedLoopManager.BatchFetchClosedLoopData(qualityData)
        if err != nil {
            logging.ErrorLogger.Errorf("获取闭环数据失败: %v", err)
            // 闭环数据获取失败不影响主流程，继续处理
            closedLoopData = make(map[string]map[int]*ClosedLoopResponse)
        }
    } else {
        logging.InfoLogger.Info("步骤3: 跳过闭环管理数据获取（功能未启用）")
        closedLoopData = make(map[string]map[int]*ClosedLoopResponse)
    }

    // 步骤4：处理和更新数据
    logging.InfoLogger.Info("步骤4: 处理和更新飞书表格数据")
    return p.processRecordsWithIndex(recordIndex, qualityData, closedLoopData, qualityResponsiblePersonStatusMap)
}
```

### 3.2 预先缓存优化功能

#### GetFeishuQualityRecords - 获取飞书数据并预先缓存
```go
func GetFeishuQualityRecords() ([]FeishuTableItem, map[string]bool, error) {
    logging.InfoLogger.Info("开始获取飞书品控表格记录（筛选品控单号不为空）+ 预先缓存品控责任人字段状态")
    var allRecords []FeishuTableItem
    qualityResponsiblePersonStatusMap := make(map[string]bool) // key: recordID, value: 品控责任人字段是否为空
    pageToken := ""
    pageSize := int64(100) // 分页大小

    for {
        // 调用飞书SDK获取记录
        records, nextPageToken, hasMore, err := queryFeishuRecordsPageSDK(pageToken, pageSize)
        if err != nil {
            logging.ErrorLogger.Error("获取飞书记录失败: ", err)
            return nil, nil, err
        }

        // 处理每条记录并缓存品控责任人字段状态
        for _, record := range records {
            // 缓存品控责任人字段状态
            qualityResponsiblePersonValue := record.Fields["品控责任人"]
            isEmpty := isFeishuUserFieldEmpty(qualityResponsiblePersonValue)
            qualityResponsiblePersonStatusMap[record.RecordID] = isEmpty
        }

        allRecords = append(allRecords, records...)
        logging.InfoLogger.Info("本页获取 %d 条有效记录，累计有效记录: %d 条", len(records), len(allRecords))

        if !hasMore {
            break
        }
        pageToken = nextPageToken
    }

    logging.InfoLogger.Infof("获取飞书记录完成，总计: %d 条记录，品控责任人状态缓存: %d 条", 
        len(allRecords), len(qualityResponsiblePersonStatusMap))
    return allRecords, qualityResponsiblePersonStatusMap, nil
}
```

#### 品控责任人字段优化处理
```go
func (p *QualityControlProcessor) processQualityResponsiblePersonFieldOptimized(value interface{}, recordID string, qualityResponsiblePersonStatusMap map[string]bool) (interface{}, error) {
    const defaultUserID = "ou_0bb538b58f9b736d5d0bcb3d98b0fde7"

    logging.InfoLogger.Infof("优化处理品控责任人字段（使用预先缓存）: recordID=%s, value=%v", recordID, value)

    // 检查是否已经是飞书API格式
    if userArray, ok := value.([]map[string]interface{}); ok {
        logging.InfoLogger.Infof("品控责任人字段已经是正确格式，直接返回: %+v", userArray)
        return value, nil
    }

    // 修复：直接尝试用户名转换，避免不必要的状态检查
    logging.InfoLogger.Infof("开始处理品控责任人字段，待转换值: %v", value)

    // 首先尝试转换品控系统中的用户名
    result, err := p.convertUserFieldWithoutDefault(value)
    if err == nil {
        // 转换成功，使用转换后的用户ID（无论飞书原字段是否为空）
        logging.InfoLogger.Infof("用户名转换成功，使用转换结果: %+v", result)
        return result, nil
    }

    // 用户名转换失败，此时才检查飞书原字段状态以决定处理方式
    logging.InfoLogger.Infof("用户名转换失败，检查飞书原字段状态以决定处理方式: %v", err)
    
    // 获取预先缓存的状态映射（仅在转换失败时使用）
    isOriginalFeishuEmpty, exists := qualityResponsiblePersonStatusMap[recordID]
    if !exists {
        logging.ErrorLogger.Errorf("记录 %s 在品控责任人状态映射中不存在", recordID)
        // 如果映射中不存在，使用默认用户ID
        logging.InfoLogger.Infof("映射中不存在记录，使用默认用户ID: %s", defaultUserID)
        return []map[string]interface{}{
            {"id": defaultUserID},
        }, nil
    }

    // 根据飞书原字段状态决定是否使用默认值
    if isOriginalFeishuEmpty {
        // 转换失败且飞书原字段为空，使用默认用户ID
        logging.InfoLogger.Infof("转换失败且飞书原字段为空，使用默认用户ID: %s", defaultUserID)
        return []map[string]interface{}{
            {"id": defaultUserID},
        }, nil
    } else {
        // 转换失败但飞书原字段不为空，跳过更新以保持原有值
        logging.InfoLogger.Infof("转换失败但飞书原字段不为空，跳过更新以保持原有值: recordID=%s", recordID)
        return nil, nil
    }
}
```

### 3.3 重复品控单号处理

#### BuildRecordBasedIndex - 构建基于记录的索引
```go
func BuildRecordBasedIndex(records []FeishuTableItem) *RecordBasedIndex {
    index := &RecordBasedIndex{
        QualityNumberToRecords:  make(map[string][]FeishuTableItem),
        RecordIDToQualityNumber: make(map[string]string),
    }

    for _, record := range records {
        qualityNumber := extractQualityNumber(record)
        if qualityNumber != "" {
            // 建立品控单号到记录的映射
            index.QualityNumberToRecords[qualityNumber] = append(index.QualityNumberToRecords[qualityNumber], record)
            // 建立记录ID到品控单号的映射
            index.RecordIDToQualityNumber[record.RecordID] = qualityNumber
        }
    }

    // 统计重复品控单号
    duplicateCount := 0
    for qualityNumber, records := range index.QualityNumberToRecords {
        if len(records) > 1 {
            duplicateCount++
            logging.InfoLogger.Infof("发现重复品控单号 %s，对应 %d 条记录", qualityNumber, len(records))
        }
    }

    logging.InfoLogger.Infof("索引构建完成，唯一品控单号: %d 个，重复品控单号: %d 个", 
        len(index.QualityNumberToRecords), duplicateCount)
    return index
}
```

## 4. 智能判断逻辑

### 4.1 "是否提供解决方案"字段的智能判断

#### getSolutionStatus - 基于品控状态和类别的智能判断
```go
func (p *QualityControlProcessor) getSolutionStatus(questionID string, pzkzStatus string, pzkzTypeName string, isQualityProblemClosed string) (string, error) {
    logging.InfoLogger.Infof("开始获取品控单号 %s 的解决方案状态，品控状态: %s，品控类别: %s，问题关闭状态: %s",
        questionID, pzkzStatus, pzkzTypeName, isQualityProblemClosed)

    // 优先级1：检查品控工单状态是否为"废弃"
    if pzkzStatus == "废弃" {
        logging.InfoLogger.Infof("品控单号 %s 状态为废弃，直接设置解决方案状态为: 是", questionID)
        return "是", nil
    }

    // 优先级2：根据品控类别采用不同的判断逻辑
    switch pzkzTypeName {
    case "技术咨询":
        // 技术咨询类型：基于"品控问题是否关闭"字段状态来确定
        logging.InfoLogger.Infof("品控单号 %s 为技术咨询类型，基于问题关闭状态判断", questionID)

        if isQualityProblemClosed == "是" {
            logging.InfoLogger.Infof("品控单号 %s 技术咨询已关闭，解决方案状态: 是", questionID)
            return "是", nil
        } else {
            logging.InfoLogger.Infof("品控单号 %s 技术咨询未关闭，解决方案状态: 否", questionID)
            return "否", nil
        }

    case "问题单":
        // 问题单类型：保持现有逻辑，通过调用解决方案API获取数据
        logging.InfoLogger.Infof("品控单号 %s 为问题单类型，调用解决方案API判断", questionID)
        return p.getSolutionStatusFromAPI(questionID)

    default:
        // 其他类型：使用默认值"否"
        logging.InfoLogger.Infof("品控单号 %s 为其他类型 (%s)，使用默认值: 否", questionID, pzkzTypeName)
        return "否", nil
    }
}
```

#### 判断优先级流程图
```
品控工单数据
    ↓
检查品控状态
    ↓
状态 = "废弃"？
    ↓ 是 → 返回"是"（最高优先级）
    ↓ 否
检查品控类别
    ↓
技术咨询？ → 基于问题关闭状态判断
    ↓
问题单？ → 调用解决方案API判断
    ↓
其他类型？ → 返回默认值"否"
```

### 4.2 故障等级字段修复

#### 字段使用修复
在所有使用故障等级的地方，统一使用 `FaultLvlName`（当前故障等级名称）而不是 `FaultLvlBaseName`（基础故障等级名称）：

```go
// 修复前（错误）
qualityItem.FaultLvlBaseName

// 修复后（正确）
qualityItem.FaultLvlName
```

#### 修复范围
1. **qualitycontrol_processor.go**：所有字段映射和处理逻辑
2. **qualitycontrol_closed.go**：闭环判断逻辑
3. **日志记录**：确保显示正确的故障等级信息

## 5. API接口设计

### 5.1 品控系统API

#### GetQualityControlList - 获取品控单列表
```go
func (api *QualityControlAPI) GetQualityControlList(page int, rows int, id string) (*QualityControlResponse, error) {
    // 构建请求URL
    baseURL := "http://yfzl.ruijie.com.cn/pzkz/load_pzkz_list"
    params := url.Values{}
    params.Add("page", strconv.Itoa(page))
    params.Add("rows", strconv.Itoa(rows))
    if id != "" {
        params.Add("id", id)
    }
    
    requestURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())

    // 创建HTTP请求
    req, err := http.NewRequest("POST", requestURL, nil)
    if err != nil {
        return nil, fmt.Errorf("创建请求失败: %v", err)
    }

    // 设置请求头
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

    // 添加认证Cookie
    if api.manager != nil && api.manager.cookieJar != nil {
        cookies := api.manager.cookieJar.Cookies(req.URL)
        for _, cookie := range cookies {
            req.AddCookie(cookie)
        }
    }

    // 发送请求
    client := &http.Client{
        Timeout: 30 * time.Second,
        Jar:     api.manager.cookieJar,
    }

    resp, err := client.Do(req)
    if err != nil {
        return nil, fmt.Errorf("请求失败: %v", err)
    }
    defer resp.Body.Close()

    // 读取响应
    body, err := ioutil.ReadAll(resp.Body)
    if err != nil {
        return nil, fmt.Errorf("读取响应失败: %v", err)
    }

    // 检查HTTP状态码
    if resp.StatusCode != http.StatusOK {
        return nil, fmt.Errorf("API返回错误状态码: %d, 响应: %s", resp.StatusCode, string(body))
    }

    // 解析JSON响应
    var response QualityControlResponse
    if err := json.Unmarshal(body, &response); err != nil {
        return nil, fmt.Errorf("解析响应失败: %v", err)
    }

    return &response, nil
}
```

### 5.2 解决方案API

#### GetSolutionList - 获取解决方案列表
```go
func (api *QualityControlAPI) GetSolutionList(questionID string) ([]SolutionResponse, error) {
    // 构建请求URL
    baseURL := "http://yfzl.ruijie.com.cn/solution/load_solution_list"
    params := url.Values{}
    params.Add("questionId", questionID)
    params.Add("questionType", "PzkzInfo")
    
    requestURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())

    // 创建HTTP请求
    req, err := http.NewRequest("POST", requestURL, nil)
    if err != nil {
        return nil, fmt.Errorf("创建请求失败: %v", err)
    }

    // 设置请求头
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

    // 添加认证Cookie（与品控系统API相同的认证机制）
    if api.manager != nil && api.manager.cookieJar != nil {
        cookies := api.manager.cookieJar.Cookies(req.URL)
        for _, cookie := range cookies {
            req.AddCookie(cookie)
        }
    }

    // 发送请求
    client := &http.Client{
        Timeout: 30 * time.Second,
        Jar:     api.manager.cookieJar,
    }

    resp, err := client.Do(req)
    if err != nil {
        return nil, fmt.Errorf("请求失败: %v", err)
    }
    defer resp.Body.Close()

    // 读取响应
    body, err := ioutil.ReadAll(resp.Body)
    if err != nil {
        return nil, fmt.Errorf("读取响应失败: %v", err)
    }

    // 检查HTTP状态码
    if resp.StatusCode != http.StatusOK {
        return nil, fmt.Errorf("API返回错误状态码: %d", resp.StatusCode)
    }

    // 解析JSON响应
    var solutions []SolutionResponse
    if err := json.Unmarshal(body, &solutions); err != nil {
        return nil, fmt.Errorf("解析响应失败: %v", err)
    }

    return solutions, nil
}
```

### 5.3 飞书API集成

#### 使用飞书官方SDK
```go
import (
    "github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
)

func queryFeishuRecordsPageSDK(pageToken string, pageSize int64) ([]FeishuTableItem, string, bool, error) {
    // 使用飞书SDK查询记录
    req := bitable.NewListAppTableRecordReqBuilder().
        AppToken(config.QualityControlAppToken).
        TableId(config.QualityControlTableID).
        PageSize(pageSize).
        PageToken(pageToken).
        Build()

    resp, err := client.Bitable.AppTableRecord.List(context.Background(), req)
    if err != nil {
        return nil, "", false, fmt.Errorf("飞书API调用失败: %v", err)
    }

    // 检查响应状态
    if !resp.Success() {
        return nil, "", false, fmt.Errorf("飞书API返回错误: %s", resp.Msg)
    }

    // 转换响应数据
    var records []FeishuTableItem
    for _, item := range resp.Data.Items {
        record := FeishuTableItem{
            RecordID: *item.RecordId,
            Fields:   item.Fields,
        }
        records = append(records, record)
    }

    nextPageToken := ""
    if resp.Data.PageToken != nil {
        nextPageToken = *resp.Data.PageToken
    }

    hasMore := resp.Data.HasMore != nil && *resp.Data.HasMore

    return records, nextPageToken, hasMore, nil
}
```

## 6. 性能优化实现

### 6.1 预先缓存优化

#### 优化原理
通过在获取飞书数据时预先缓存品控责任人字段的状态（是否为空），避免在处理每条记录时重复调用飞书API查询字段状态。

#### 性能提升效果
- **优化前**：每条记录处理时都需要调用飞书API查询字段状态
- **优化后**：一次性缓存所有记录的字段状态，处理时直接使用缓存
- **性能提升**：减少API调用次数从N次到1次，显著提升处理速度

### 6.2 智能判断优化

#### 废弃状态优先级处理
```go
// 优先级1：废弃状态直接返回，无需进一步判断
if pzkzStatus == "废弃" {
    return "是", nil
}
```

#### 技术咨询类型优化
```go
// 技术咨询类型：基于问题关闭状态判断，无需API调用
case "技术咨询":
    if isQualityProblemClosed == "是" {
        return "是", nil
    } else {
        return "否", nil
    }
```

#### 性能优化统计
```
假设品控数据分布：
- 废弃状态: 5% → 直接返回，最高效
- 技术咨询: 40% → 无需API调用
- 问题单: 50% → 需要API调用
- 其他类型: 5% → 无需API调用

API调用减少：50%
处理效率提升：显著
```

### 6.3 重复品控单号处理优化

#### 索引构建优化
通过构建基于RecordID的索引，实现：
1. **唯一品控单号提取**：避免重复获取相同品控单号的数据
2. **一对多映射**：一个品控单号对应多条飞书记录的高效处理
3. **批量更新**：同一品控数据可以同时更新多条飞书记录

## 7. 错误处理机制

### 7.1 API调用错误处理

#### 品控系统API错误处理
```go
func (api *QualityControlAPI) handleAPIError(resp *http.Response, body []byte) error {
    if resp.StatusCode != http.StatusOK {
        return fmt.Errorf("品控API返回错误状态码: %d, 响应: %s", resp.StatusCode, string(body))
    }
    return nil
}
```

#### 飞书API错误处理
```go
func formatFeishuError(code int, msg string, requestId string) string {
    switch code {
    case 1254003:
        return fmt.Sprintf("飞书API错误[%d]: app_token错误 - %s, RequestId: %s. 请检查QualityControlAppToken配置", code, msg, requestId)
    case 1254004:
        return fmt.Sprintf("飞书API错误[%d]: table_id错误 - %s, RequestId: %s. 请检查QualityControlTableID配置", code, msg, requestId)
    case 1254040:
        return fmt.Sprintf("飞书API错误[%d]: app_token不存在 - %s, RequestId: %s. 请检查多维表格是否存在", code, msg, requestId)
    case 1254041:
        return fmt.Sprintf("飞书API错误[%d]: table_id不存在 - %s, RequestId: %s. 请检查数据表是否存在", code, msg, requestId)
    case 1254302:
        return fmt.Sprintf("飞书API错误[%d]: 权限不足 - %s, RequestId: %s. 请检查应用权限配置", code, msg, requestId)
    case 1254290:
        return fmt.Sprintf("飞书API错误[%d]: 请求过快 - %s, RequestId: %s. 建议稍后重试", code, msg, requestId)
    case 1254291:
        return fmt.Sprintf("飞书API错误[%d]: 写冲突 - %s, RequestId: %s. 请避免并发调用", code, msg, requestId)
    case 1254607:
        return fmt.Sprintf("飞书API错误[%d]: 数据未就绪 - %s, RequestId: %s. 请稍后重试", code, msg, requestId)
    case 1255040:
        return fmt.Sprintf("飞书API错误[%d]: 请求超时 - %s, RequestId: %s. 请重试", code, msg, requestId)
    default:
        return fmt.Sprintf("飞书API错误[%d]: %s, RequestId: %s", code, msg, requestId)
    }
}
```

### 7.2 数据处理错误处理

#### 字段转换错误处理
```go
func (p *QualityControlProcessor) processFieldValuesForFeishuOptimized(updateData map[string]interface{}, recordID string, qualityResponsiblePersonStatusMap map[string]bool) (map[string]interface{}, error) {
    processedData := make(map[string]interface{})

    for fieldName, value := range updateData {
        if fieldName == "品控责任人" {
            // 特殊处理品控责任人字段：使用预先缓存的状态映射
            processedValue, err := p.processQualityResponsiblePersonFieldOptimized(value, recordID, qualityResponsiblePersonStatusMap)
            if err != nil {
                logging.ErrorLogger.Errorf("转换字段 %s 失败: %v", fieldName, err)
                continue // 跳过错误字段，继续处理其他字段
            }
            // 如果返回nil，表示跳过此字段的更新
            if processedValue != nil {
                processedData[fieldName] = processedValue
            }
        } else {
            // 其他字段使用原有逻辑
            processedValue, err := p.convertFieldValueForFeishuOriginal(fieldName, value)
            if err != nil {
                logging.ErrorLogger.Errorf("转换字段 %s 失败: %v", fieldName, err)
                continue // 跳过错误字段，继续处理其他字段
            }
            processedData[fieldName] = processedValue
        }
    }

    return processedData, nil
}
```

### 7.3 兜底策略

#### 默认值处理
```go
// 品控责任人字段的默认用户ID
const defaultUserID = "ou_0bb538b58f9b736d5d0bcb3d98b0fde7"

// 解决方案状态的默认值
if err != nil {
    logging.ErrorLogger.Errorf("获取品控单号 %d 解决方案状态失败: %v", qualityItem.ID, err)
    // 失败时使用默认值
    solutionStatus = mapping.DefaultValue
}
```

#### 数据缺失处理
```go
// 如果映射中不存在，使用默认用户ID
if !exists {
    logging.ErrorLogger.Errorf("记录 %s 在品控责任人状态映射中不存在", recordID)
    logging.InfoLogger.Infof("映射中不存在记录，使用默认用户ID: %s", defaultUserID)
    return []map[string]interface{}{
        {"id": defaultUserID},
    }, nil
}
```

## 8. 数据流程图

### 8.1 主数据流程
```
开始
  ↓
获取飞书表格数据 + 预先缓存品控责任人字段状态
  ↓
构建基于RecordID的索引（解决重复品控单号问题）
  ↓
提取唯一品控单号列表
  ↓
批量获取品控系统数据
  ↓
获取闭环管理数据（如果启用）
  ↓
处理和更新飞书表格数据
  ↓
统计和日志记录
  ↓
结束
```

### 8.2 智能判断流程
```
品控数据
  ↓
检查品控状态
  ↓
状态 = "废弃"？
  ↓ 是 → 返回"是"
  ↓ 否
检查品控类别
  ↓
技术咨询？ → 基于问题关闭状态 → 返回"是"/"否"
  ↓
问题单？ → 调用解决方案API → 检查isFinish → 返回"是"/"否"
  ↓
其他类型？ → 返回"否"
```

### 8.3 字段处理流程
```
字段数据
  ↓
字段类型判断
  ↓
品控责任人？ → 预先缓存优化处理 → 用户名转换/默认值
  ↓
单选字段？ → 单选字段转换 → 标准化值
  ↓
文本字段？ → 文本字段转换 → 字符串格式
  ↓
其他字段？ → 直接返回
```

## 9. 配置和部署

### 9.1 配置项
```go
// 飞书配置
QualityControlAppToken  string // 飞书应用Token
QualityControlTableID   string // 飞书表格ID

// 品控系统配置
QualityControlBaseURL   string // 品控系统基础URL
QualityControlCookies   string // 认证Cookie

// 功能开关
EnableClosedLoop        bool   // 是否启用闭环管理功能
EnableSolutionCheck     bool   // 是否启用解决方案检查

// 性能配置
PageSize               int    // 分页大小
RequestTimeout         int    // 请求超时时间（秒）
MaxRetries            int    // 最大重试次数
```

### 9.2 部署要求
- **Go版本**: 1.19+
- **网络要求**: 能够访问品控系统API和飞书API
- **认证要求**: 有效的飞书应用Token和品控系统认证Cookie
- **权限要求**: 飞书应用需要有多维表格的读写权限

### 9.3 监控和日志
- **日志级别**: INFO、ERROR
- **关键指标**: 处理记录数、成功率、耗时、API调用次数
- **错误监控**: API调用失败、数据转换错误、权限问题

## 10. 向后兼容性

### 10.1 数据结构兼容性
- 所有新增字段都是可选的，不影响现有数据结构
- 保持现有API接口的签名和行为不变
- 新增功能通过配置开关控制，默认保持原有行为

### 10.2 功能兼容性
- 预先缓存优化：完全透明，不影响现有处理逻辑
- 智能判断逻辑：增强现有功能，保持原有结果的准确性
- 重复品控单号处理：解决现有问题，不影响正常记录处理

### 10.3 配置兼容性
- 所有新增配置项都有合理的默认值
- 现有配置项保持不变
- 支持渐进式功能启用

## 11. 总结

品控系统数据同步功能通过以下关键技术实现了高效、准确、可靠的数据同步：

1. **预先缓存优化**：显著提升处理性能，减少API调用次数
2. **智能判断逻辑**：基于品控状态和类别的多层次判断，确保业务逻辑准确性
3. **重复数据处理**：通过索引机制解决重复品控单号问题
4. **错误处理机制**：完善的错误处理和兜底策略，确保系统稳定性
5. **向后兼容性**：所有优化和新增功能都保持向后兼容

系统设计充分考虑了性能、准确性、可维护性和扩展性，为品控数据管理提供了强有力的技术支持。

## 12. 附录

### 12.1 完整的字段转换逻辑

#### convertSingleSelectField - 单选字段转换
```go
func (p *QualityControlProcessor) convertSingleSelectField(fieldName string, value interface{}) (interface{}, error) {
    // 转换为字符串
    strValue := fmt.Sprintf("%v", value)

    // 根据字段名进行特定转换
    switch fieldName {
    case "品控问题是否关闭":
        switch strValue {
        case "开放", "未关闭", "否":
            return "否", nil
        case "关闭", "已关闭", "是":
            return "是", nil
        default:
            return "否", nil // 默认值
        }

    case "品控问题是否闭环":
        switch strValue {
        case "已闭环", "是":
            return "已闭环", nil
        case "未闭环", "否":
            return "未闭环", nil
        case "无需闭环":
            return "无需闭环", nil
        default:
            return "未闭环", nil // 默认值
        }

    case "是否提供解决方案":
        switch strValue {
        case "是", "1", "true", "已提供", "完成":
            return "是", nil
        case "否", "0", "false", "未提供", "未完成":
            return "否", nil
        default:
            return "否", nil // 默认值
        }
    }

    return strValue, nil
}
```

#### convertTextField - 文本字段转换
```go
func (p *QualityControlProcessor) convertTextField(value interface{}) (interface{}, error) {
    // 简单转换为字符串
    return fmt.Sprintf("%v", value), nil
}
```

#### convertUserFieldWithoutDefault - 用户字段转换
```go
func (p *QualityControlProcessor) convertUserFieldWithoutDefault(value interface{}) ([]map[string]interface{}, error) {
    // 检查是否已经是飞书API格式
    if userArray, ok := value.([]map[string]interface{}); ok {
        return userArray, nil
    }

    // 转换为字符串
    strValue := strings.TrimSpace(fmt.Sprintf("%v", value))
    if strValue == "" || strValue == "<nil>" {
        return nil, fmt.Errorf("用户名为空")
    }

    // 调用用户名转换API
    userID, err := p.convertUsernameToFeishuUserID(strValue)
    if err != nil {
        return nil, fmt.Errorf("用户名转换失败: %v", err)
    }

    return []map[string]interface{}{
        {"id": userID},
    }, nil
}
```

### 12.2 闭环管理数据结构

#### ClosedLoopResponse - 闭环管理响应
```go
type ClosedLoopResponse struct {
    Data struct {
        ID           int    `json:"id"`
        QuestionID   int    `json:"questionId"`
        ClosedStatus string `json:"closedStatus"` // 闭环状态
        ClosedDate   string `json:"closedDate"`   // 闭环时间
        ClosedReason string `json:"closedReason"` // 闭环原因
        CreateDate   string `json:"createDate"`   // 创建时间
        ModifyDate   string `json:"modifyDate"`   // 修改时间
    } `json:"data"`
    Success bool   `json:"success"`
    Message string `json:"message"`
}
```

#### ClosedLoopManager - 闭环管理器
```go
type ClosedLoopManager struct {
    api *QualityControlAPI
}

func NewClosedLoopManager() *ClosedLoopManager {
    return &ClosedLoopManager{
        api: NewQualityControlAPI(),
    }
}

func (clm *ClosedLoopManager) BatchFetchClosedLoopData(qualityData map[string]*ProcessedQualityControlItem) (map[string]map[int]*ClosedLoopResponse, error) {
    closedLoopData := make(map[string]map[int]*ClosedLoopResponse)
    processedCount := 0
    noClosedLoopCount := 0

    for qualityNumber, item := range qualityData {
        // 判断是否需要获取闭环数据
        if !clm.shouldFetchClosedLoop(item.FaultLvlName, item.DevFaultReasonName) {
            noClosedLoopCount++
            logging.InfoLogger.Infof("品控单号 %s 无需闭环，设置为空闭环数据以便后续处理基础字段", qualityNumber)
            // 不再跳过，而是设置空的闭环数据，确保记录会被处理
            closedLoopData[qualityNumber] = make(map[int]*ClosedLoopResponse)
            processedCount++
            continue
        }

        // 获取完整闭环数据
        completeClosedData, err := clm.fetchCompleteClosedLoopData(qualityNumber, item.FaultLvlName, item.DevFaultReasonName)
        if err != nil {
            logging.ErrorLogger.Errorf("获取品控单号 %s 完整闭环数据失败: %v", qualityNumber, err)
            // 即使获取失败，也要设置空数据确保记录被处理
            closedLoopData[qualityNumber] = make(map[int]*ClosedLoopResponse)
            processedCount++
            continue
        }

        closedLoopData[qualityNumber] = completeClosedData
        processedCount++
    }

    logging.InfoLogger.Infof("闭环数据批量获取完成，处理: %d 个品控单号，无需闭环: %d 个",
        processedCount, noClosedLoopCount)
    return closedLoopData, nil
}
```

### 12.3 数据处理统计

#### SyncStatistics - 同步统计信息
```go
type SyncStatistics struct {
    StartTime      string `json:"start_time"`      // 开始时间
    EndTime        string `json:"end_time"`        // 结束时间
    Duration       string `json:"duration"`        // 耗时
    TotalRecords   int    `json:"total_records"`   // 总记录数
    UpdatedRecords int    `json:"updated_records"` // 更新记录数
    SkippedRecords int    `json:"skipped_records"` // 跳过记录数
    ErrorRecords   int    `json:"error_records"`   // 错误记录数
    APICallCount   int    `json:"api_call_count"`  // API调用次数
}
```

### 12.4 配置管理

#### 配置结构
```go
type Config struct {
    // 飞书配置
    Feishu struct {
        AppToken string `json:"app_token"`
        TableID  string `json:"table_id"`
    } `json:"feishu"`

    // 品控系统配置
    QualityControl struct {
        BaseURL string `json:"base_url"`
        Cookies string `json:"cookies"`
    } `json:"quality_control"`

    // 功能开关
    Features struct {
        EnableClosedLoop    bool `json:"enable_closed_loop"`
        EnableSolutionCheck bool `json:"enable_solution_check"`
        EnableCache         bool `json:"enable_cache"`
    } `json:"features"`

    // 性能配置
    Performance struct {
        PageSize       int `json:"page_size"`
        RequestTimeout int `json:"request_timeout"`
        MaxRetries     int `json:"max_retries"`
        BatchSize      int `json:"batch_size"`
    } `json:"performance"`
}
```

### 12.5 错误代码定义

#### 错误代码常量
```go
const (
    // 飞书API错误代码
    ErrFeishuAppTokenInvalid   = 1254003
    ErrFeishuTableIDInvalid    = 1254004
    ErrFeishuAppTokenNotFound  = 1254040
    ErrFeishuTableIDNotFound   = 1254041
    ErrFeishuPermissionDenied  = 1254302
    ErrFeishuRateLimited       = 1254290
    ErrFeishuWriteConflict     = 1254291
    ErrFeishuDataNotReady      = 1254607
    ErrFeishuRequestTimeout    = 1255040

    // 品控系统错误代码
    ErrQualityAPITimeout       = 2001
    ErrQualityAPIUnauthorized  = 2002
    ErrQualityAPINotFound      = 2003
    ErrQualityAPIServerError   = 2004

    // 数据处理错误代码
    ErrDataConversionFailed    = 3001
    ErrFieldMappingNotFound    = 3002
    ErrUserConversionFailed    = 3003
    ErrRecordNotFound          = 3004
)
```

### 12.6 性能监控指标

#### 性能指标收集
```go
type PerformanceMetrics struct {
    // API调用统计
    FeishuAPICalls     int           `json:"feishu_api_calls"`
    QualityAPICalls    int           `json:"quality_api_calls"`
    SolutionAPICalls   int           `json:"solution_api_calls"`

    // 响应时间统计
    AvgFeishuAPITime   time.Duration `json:"avg_feishu_api_time"`
    AvgQualityAPITime  time.Duration `json:"avg_quality_api_time"`
    AvgSolutionAPITime time.Duration `json:"avg_solution_api_time"`

    // 数据处理统计
    RecordsProcessed   int           `json:"records_processed"`
    FieldsUpdated      int           `json:"fields_updated"`
    CacheHitRate       float64       `json:"cache_hit_rate"`

    // 错误统计
    APIErrors          int           `json:"api_errors"`
    ConversionErrors   int           `json:"conversion_errors"`
    ValidationErrors   int           `json:"validation_errors"`
}
```

### 12.7 测试用例示例

#### 单元测试示例
```go
func TestGetSolutionStatus(t *testing.T) {
    processor := &QualityControlProcessor{}

    // 测试废弃状态优先级
    t.Run("废弃状态应该返回是", func(t *testing.T) {
        result, err := processor.getSolutionStatus("123", "废弃", "问题单", "否")
        assert.NoError(t, err)
        assert.Equal(t, "是", result)
    })

    // 测试技术咨询类型
    t.Run("技术咨询已关闭应该返回是", func(t *testing.T) {
        result, err := processor.getSolutionStatus("123", "开放", "技术咨询", "是")
        assert.NoError(t, err)
        assert.Equal(t, "是", result)
    })

    t.Run("技术咨询未关闭应该返回否", func(t *testing.T) {
        result, err := processor.getSolutionStatus("123", "开放", "技术咨询", "否")
        assert.NoError(t, err)
        assert.Equal(t, "否", result)
    })

    // 测试其他类型
    t.Run("其他类型应该返回否", func(t *testing.T) {
        result, err := processor.getSolutionStatus("123", "开放", "其他", "是")
        assert.NoError(t, err)
        assert.Equal(t, "否", result)
    })
}
```

#### 集成测试示例
```go
func TestDataSyncIntegration(t *testing.T) {
    // 设置测试环境
    processor := NewQualityControlProcessor()

    // 模拟数据同步流程
    t.Run("完整数据同步流程", func(t *testing.T) {
        stats, err := processor.processAndUpdateDataV2()
        assert.NoError(t, err)
        assert.NotNil(t, stats)
        assert.Greater(t, stats.TotalRecords, 0)
    })
}
```

### 12.8 部署脚本示例

#### Docker部署配置
```dockerfile
FROM golang:1.19-alpine AS builder

WORKDIR /app
COPY . .
RUN go mod download
RUN go build -o quality-sync ./cmd/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/quality-sync .
COPY --from=builder /app/config.json .

CMD ["./quality-sync"]
```

#### 配置文件示例
```json
{
  "feishu": {
    "app_token": "bascnCMII2ORuAUfTXuBXqDQqLd",
    "table_id": "tblEnSV2PNAajtWE"
  },
  "quality_control": {
    "base_url": "http://yfzl.ruijie.com.cn",
    "cookies": "JSESSIONID=xxx"
  },
  "features": {
    "enable_closed_loop": true,
    "enable_solution_check": true,
    "enable_cache": true
  },
  "performance": {
    "page_size": 100,
    "request_timeout": 30,
    "max_retries": 3,
    "batch_size": 50
  }
}
```

## 13. 版本历史

### v1.0.0 - 基础功能
- 实现品控系统与飞书多维表格的基础数据同步
- 支持基本字段映射和数据转换

### v1.1.0 - 性能优化
- 实现预先缓存优化功能
- 优化品控责任人字段处理逻辑
- 减少API调用次数，提升处理性能

### v1.2.0 - 重复数据处理
- 实现基于RecordID的索引机制
- 解决重复品控单号问题
- 支持一对多数据映射

### v1.3.0 - 故障等级修复
- 修复故障等级字段使用错误问题
- 统一使用FaultLvlName字段
- 确保故障等级数据的准确性

### v1.4.0 - 智能判断逻辑
- 实现"是否提供解决方案"字段的智能判断
- 支持基于品控类别的不同处理策略
- 集成解决方案API调用

### v1.5.0 - 废弃状态优化
- 新增废弃状态的优先级处理
- 优化智能判断逻辑的性能
- 完善错误处理和日志记录

### v1.6.0 - 品控类别管理
- 实现品控类别字段的数据结构支持
- 支持品控类别字段的禁用配置
- 保持内部业务逻辑的完整性

## 14. 维护指南

### 14.1 日常维护任务
1. **监控API调用状态**：定期检查品控系统和飞书API的调用成功率
2. **检查数据同步准确性**：验证关键字段的数据一致性
3. **性能监控**：关注处理时间和资源使用情况
4. **日志分析**：定期分析错误日志，识别潜在问题

### 14.2 故障排查步骤
1. **检查配置**：验证飞书Token、表格ID、品控系统认证等配置
2. **网络连通性**：确认能够正常访问相关API
3. **权限验证**：检查飞书应用权限和品控系统访问权限
4. **数据格式**：验证API返回数据的格式是否符合预期
5. **日志分析**：查看详细的错误日志和处理日志

### 14.3 性能调优建议
1. **批量处理**：适当调整批量处理的大小
2. **缓存策略**：优化缓存的使用策略
3. **并发控制**：根据API限制调整并发请求数量
4. **超时设置**：根据网络状况调整请求超时时间

### 14.4 扩展开发指南
1. **新增字段**：在FieldMapping中添加新的字段映射
2. **新增数据源**：扩展API接口支持新的数据源
3. **新增转换逻辑**：在字段转换函数中添加新的处理逻辑
4. **新增验证规则**：在数据验证环节添加新的业务规则

本技术设计文档详细描述了品控系统数据同步功能的完整实现，包括架构设计、核心功能、性能优化、错误处理等各个方面，为系统的开发、部署、维护和扩展提供了全面的技术指导。
