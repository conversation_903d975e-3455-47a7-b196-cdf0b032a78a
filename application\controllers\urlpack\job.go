package urlpack

import (
	"bufio"
	"bytes"
	"encoding/binary"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/featurerelease/dfeature"
	"irisAdminApi/service/dao/urlpack/durlpackcategory"
	"irisAdminApi/service/dao/urlpack/durlpackconfig"
	"irisAdminApi/service/dao/urlpack/durlpackdb"
	"irisAdminApi/service/dao/urlpack/durlpackjob"
	"net/url"
	"os"
	"os/exec"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	"gorm.io/gorm"
)

func GetJobs(ctx iris.Context) {
	name := ctx.FormValue("name")
	version := ctx.FormValue("version")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	job := durlpackjob.UrlPackJob{}
	list, err := job.AllByNameAndVersionAndBaseVersion(name, version, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetJob(ctx iris.Context) {
	info := durlpackjob.UrlPackJob{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

func DeleteJob(ctx iris.Context) {
	userId, _ := dao.GetAuthId(ctx)
	info := durlpackjob.UrlPackJob{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if info.UserID != userId {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "仅允许删除自己创建的作业"))
		return
	}
	release := dfeature.Response{}
	err = release.FindEx("file_name", filepath.Base(info.Full))
	if err != nil {
		logging.ErrorLogger.Errorf("get feature release get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if release.ID > 0 && release.FeatureType == "url" && release.Status != 4 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "已发布，禁止删除！"))
		return
	}

	err = dao.Delete(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	os.RemoveAll(info.WorkDir)
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

func saveFormFile(ctx iris.Context, name, upload string) (string, error) {
	f, fh, err := ctx.FormFile(name)

	if err != nil {
		return "", err
	}
	defer f.Close()
	dest := filepath.Join(upload, name)

	err = os.MkdirAll(dest, 0750)
	if err != nil {
		return "", err
	}

	os.Chmod(dest, 0750)
	_, err = ctx.SaveFormFile(fh, filepath.Join(dest, fh.Filename))
	if err != nil {
		return "", err
	}
	return filepath.Join(dest, fh.Filename), nil
}

func CreateJob(ctx iris.Context) {
	userID, _ := dao.GetAuthId(ctx)
	jobID := libs.GetUniqueID()
	request := durlpackjob.Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	var upload = filepath.Join(libs.Config.UrlPack.Outputs, time.Now().Format("20060102"), jobID)
	job := durlpackjob.UrlPackJob{}
	baseVersion := request.BaseVersion
	baseJob := durlpackjob.UrlPackJob{}
	if !request.OnlyFull {
		if baseVersion != "" {
			err := baseJob.FindEx("version", baseVersion)
			if err != nil {
				logging.ErrorLogger.Errorf(fmt.Sprintf("find basejob record error: %s", err.Error()))
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未找到基线版本"))
				return
			}

		} else {
			err := baseJob.Last(request.Type)
			if err != nil {
				logging.ErrorLogger.Errorf(fmt.Sprintf("create job record: %s", err.Error()))
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未找到基线版本"))
				return
			}
		}

		baseVersion = baseJob.Version

		if _, err := os.Stat(baseJob.WorkDir); err != nil {
			baseVersion = ""
			baseJob.ID = 0
		}
	} else {
		baseVersion = ""
	}

	data := map[string]interface{}{
		"CreatedAt":   time.Now(),
		"UpdatedAt":   time.Now(),
		"JobID":       jobID,
		"WorkDir":     upload,
		"Full":        "",
		"Incremental": "",

		"Type":        request.Type,
		"Log":         "",
		"Status":      0,
		"UserID":      userID,
		"Version":     time.Now().Format("20060102.1504"),
		"BaseVersion": baseVersion,
		"Comment":     request.Comment,
	}

	err := job.Create(data)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("create job record: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	err = job.FindEx("job_id", jobID)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("create job record: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if job.ID == 0 {
		logging.ErrorLogger.Errorf(fmt.Sprintf("create job record failed"))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	go PackWorker(&job, &baseJob)
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func LogInit(jobID string) (string, *os.File, error) {
	var logDir = filepath.Join(libs.Config.UrlPack.Logs, time.Now().Format("20060102"))
	logFilePath := filepath.Join(logDir, fmt.Sprintf("%s.log", jobID))
	err := os.MkdirAll(logDir, 0750)
	os.Chmod(logDir, 0750)
	if err != nil {
		logging.ErrorLogger.Errorf("create logfile dir err", err)
		return logFilePath, nil, err
	}

	f, err := os.OpenFile(logFilePath, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		logging.ErrorLogger.Errorf("create logfile err", err)
		return logFilePath, nil, err

	}
	return logFilePath, f, nil
}

type IncrementalDB struct {
	Name string `json:"name"`
	Data []int  `json:"data"`
}

func PackWorker(job, baseJob *durlpackjob.UrlPackJob) {
	status := 2
	var logfile string

	defer func() {
		err := job.Update(job.ID, map[string]interface{}{"UpdatedAt": time.Now(), "Status": status, "Log": logfile})
		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("update job status err %s %d %s", job.JobID, status, err.Error()))
		}
	}()

	logfile, f, err := LogInit(job.JobID)
	if err != nil {
		return
	}

	err = job.Update(job.ID, map[string]interface{}{"UpdatedAt": time.Now(), "Log": logfile})
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("update job status err %s %d %s", job.JobID, status, err.Error()))
	}
	defer f.Close()

	categories, err := durlpackcategory.FindByName(job.Type)
	if err != nil {
		return
	}

	// 生成output临时目录

	outputTemp := filepath.Join(job.WorkDir, "temp")
	fullTemp := filepath.Join(outputTemp, "full")
	incrementalTemp := filepath.Join(outputTemp, "incremental")

	err = os.MkdirAll(fullTemp, 0755)
	os.Chmod(fullTemp, 0755)
	if err != nil {
		f.WriteString(fmt.Sprintf("%s %s, %s\r\n", time.Now().Format("2006-01-02 15:04:05"), "创建全量包输出临时目录失败", err.Error()))
		return
	}
	f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), "创建全量包输出临时目录成功"))

	err = os.MkdirAll(incrementalTemp, 0755)
	os.Chmod(incrementalTemp, 0755)
	if err != nil {
		f.WriteString(fmt.Sprintf("%s %s, %s\r\n", time.Now().Format("2006-01-02 15:04:05"), "创建增量包输出临时目录失败", err.Error()))
		return
	}
	f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), "创建增量包输出临时目录成功"))

	// 导出数据量分类数据
	incrementalDB := []IncrementalDB{}

	for _, category := range categories {
		err := exportdb(fullTemp, category.CategoryNameCH, category.CategoryNameEN, f, job)
		if err != nil {
			return
		}
		//生成增量
		if baseJob.ID > 0 {
			baseOutputTemp := filepath.Join(baseJob.WorkDir, "temp")
			baseFullTemp := filepath.Join(baseOutputTemp, "full")
			//比较跟基线版本大小是否一致
			_, err := os.Stat(filepath.Join(fullTemp, category.CategoryNameEN+".db"))
			if err != nil {
				logging.ErrorLogger.Errorf("check full db error", category.CategoryNameEN, err)
				return
			}
			_, err = os.Stat(filepath.Join(baseFullTemp, category.CategoryNameEN+".db"))
			if err != nil {
				logging.ErrorLogger.Errorf("check base db error", category.CategoryNameEN, err)
				// 基线文件不存在，直接拷贝
				command := fmt.Sprintf(`cp "%s" "%s"`, filepath.Join(fullTemp, category.CategoryNameEN+".db"), incrementalTemp)
				f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), command))
				stdout, err := libs.ExecCommand(command)
				if err != nil {
					f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), fmt.Sprintf("拷贝文件出错， %s -> %s", filepath.Join(fullTemp, category.CategoryNameEN+".db"), incrementalTemp)))
					f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), stdout))
					return
				}
				continue
			}

			fullName, fullSize, err := getBooleanFilterInfo(filepath.Join(fullTemp, category.CategoryNameEN+".db"))
			if err != nil {
				f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), fmt.Sprintf("获取bloom信息错误 %s %s", filepath.Join(fullTemp, category.CategoryNameEN+".db"), err.Error())))
				return
			}

			baseName, baseSize, err := getBooleanFilterInfo(filepath.Join(baseFullTemp, category.CategoryNameEN+".db"))
			if err != nil {
				f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), fmt.Sprintf("获取基线bloom信息错误 %s %s", filepath.Join(baseFullTemp, category.CategoryNameEN+".db"), err.Error())))
				return
			}

			// if fullFI.Size() != baseFI.Size() {
			if fullName != baseName || fullSize != baseSize {
				command := fmt.Sprintf(`cp "%s" "%s"`, filepath.Join(fullTemp, category.CategoryNameEN+".db"), incrementalTemp)
				f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), command))
				stdout, err := libs.ExecCommand(command)
				if err != nil {
					f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), fmt.Sprintf("拷贝文件出错， %s -> %s", filepath.Join(fullTemp, category.CategoryNameEN+".db"), incrementalTemp)))
					f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), stdout))
					return
				}
			} else {
				//
				data, err := createIncremental(filepath.Join(fullTemp, category.CategoryNameEN+".db"), filepath.Join(baseFullTemp, category.CategoryNameEN+".db"))
				if err != nil {
					f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), fmt.Sprintf("生成增量失败， %s -> %s", filepath.Join(fullTemp, category.CategoryNameEN+".db"), filepath.Join(baseFullTemp, category.CategoryNameEN+".db"))))
					return
				}
				if len(data) > 100 {
					command := fmt.Sprintf(`cp "%s" "%s"`, filepath.Join(fullTemp, category.CategoryNameEN+".db"), incrementalTemp)
					f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), command))
					stdout, err := libs.ExecCommand(command)
					if err != nil {
						f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), fmt.Sprintf("拷贝文件出错， %s -> %s", filepath.Join(fullTemp, category.CategoryNameEN+".db"), incrementalTemp)))
						f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), stdout))
						return
					}
				} else if len(data) > 0 {
					incrementalDB = append(incrementalDB, IncrementalDB{Name: category.CategoryNameEN, Data: data})
				}
			}
		}
	}
	outputFull := filepath.Join(job.WorkDir, "full")
	outputIncremental := filepath.Join(job.WorkDir, "incremental")
	err = os.MkdirAll(outputFull, 0755)
	os.Chmod(outputFull, 0755)
	if err != nil {
		f.WriteString(fmt.Sprintf("%s %s, %s\r\n", time.Now().Format("2006-01-02 15:04:05"), "创建全量包输出目录失败", err.Error()))
		return
	}

	err = os.MkdirAll(outputIncremental, 0755)
	os.Chmod(outputIncremental, 0755)
	if err != nil {
		f.WriteString(fmt.Sprintf("%s %s, %s\r\n", time.Now().Format("2006-01-02 15:04:05"), "创建全量包输出目录失败", err.Error()))
		return
	}

	// 导入精准库
	exactDir := GetExactDir()
	command := fmt.Sprintf(`cp "%s" "%s"`, filepath.Join(exactDir, ExactFileName), fullTemp)
	f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), command))
	stdout, err := libs.ExecCommand(command)
	if err != nil {
		f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), fmt.Sprintf("拷贝精准库文件出错， %s -> %s", filepath.Join(exactDir, ExactFileName), outputFull)))
		f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), stdout))
	}

	err = createGloablConfFile(job, categories, outputFull, false, f)
	if err != nil {
		f.WriteString(fmt.Sprintf("%s %s, %s\r\n", time.Now().Format("2006-01-02 15:04:05"), "生成全量包配置文件失败", err.Error()))
		return
	}

	err = createDescFiles(job, categories, fullTemp, false, f)
	if err != nil {
		f.WriteString(fmt.Sprintf("%s %s, %s\r\n", time.Now().Format("2006-01-02 15:04:05"), "生成全量包配置文件失败", err.Error()))
		return
	}

	err = packFiles(f, fullTemp, outputFull, nil, job, false)
	if err != nil {
		f.WriteString(fmt.Sprintf("%s %s, %s\r\n", time.Now().Format("2006-01-02 15:04:05"), "全量包打包失败", err.Error()))
		return
	}
	// tar 打包增量包
	if baseJob.ID > 0 {
		// 导入精准库
		command := fmt.Sprintf(`cp "%s" "%s"`, filepath.Join(exactDir, ExactFileName), incrementalTemp)
		f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), command))
		stdout, err := libs.ExecCommand(command)
		if err != nil {
			f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), fmt.Sprintf("拷贝精准库文件出错， %s -> %s", filepath.Join(exactDir, ExactFileName), outputIncremental)))
			f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), stdout))
		}

		err = createGloablConfFile(job, categories, outputIncremental, true, f)
		if err != nil {
			f.WriteString(fmt.Sprintf("%s %s, %s\r\n", time.Now().Format("2006-01-02 15:04:05"), "生成全量包配置文件失败", err.Error()))
			return
		}

		err = createDescFiles(job, categories, incrementalTemp, true, f)
		if err != nil {
			f.WriteString(fmt.Sprintf("%s %s, %s\r\n", time.Now().Format("2006-01-02 15:04:05"), "生成全量包配置文件失败", err.Error()))
			return
		}

		err = packFiles(f, incrementalTemp, outputIncremental, incrementalDB, job, true)
		if err != nil {
			f.WriteString(fmt.Sprintf("%s %s, %s\r\n", time.Now().Format("2006-01-02 15:04:05"), "增量包打包失败", err.Error()))
			return
		}
	}
	status = 1
}

func getBooleanFilterInfo(fp string) (string, int, error) {
	var name string
	var size int
	f, err := os.Open(fp)
	if err != nil {
		return name, size, err
	}

	defer f.Close()

	r := bufio.NewReader(f)
	for i := 0; i < 1; i++ {
		_, err := r.ReadBytes('\n')
		if err != nil {
			return name, size, err
		}
	}
	b, err := r.ReadBytes('\n')

	line := strings.Split(string(b), "#")
	name = line[0]
	size, err = strconv.Atoi(strings.TrimSpace(line[5]))
	if err != nil {
		return name, size, err
	}
	return name, size, nil
}

func copyFile(f *os.File, fn, fp, temp string, incremental bool) error {
	fn = fmt.Sprintf("%s.db", fn)
	// 全量包检查文件是否都存在
	if !incremental {
		_, err := os.Stat(filepath.Join(fp, fn))
		if err != nil {
			f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), fmt.Sprintf("全量包文件与分类不一致, %s不存在", fn)))
			return err
		}
	}

	command := fmt.Sprintf(`cp "%s" "%s"`, filepath.Join(fp, fn), temp)
	f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), command))
	stdout, err := libs.ExecCommand(command)
	if err != nil {
		f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), fmt.Sprintf("拷贝文件出错， %s -> %s", filepath.Join(fp, fn), temp)))
		f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), stdout))
		return err
	}
	return nil
}

func exportdb(temp, classCH, classEN string, f *os.File, job *durlpackjob.UrlPackJob) error {
	dbConfig := durlpackdb.UrlPackDB{}
	err := dbConfig.FindLast()
	if err != nil {
		return err
	}
	conn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8&parseTime=True&loc=Local", dbConfig.Username, dbConfig.Password, dbConfig.Host, dbConfig.Port, dbConfig.Database)
	// fmt.Println(conn)
	if UrlDb == nil {
		UrlDb = &UrlDbServer{
			adapter: "mysql",
			conn:    conn,
		}
		err := UrlDb.Init()
		if err != nil {
			UrlDb = nil
			return err
		}
		if UrlDb.db == nil {
			return errors.New("URL数据库初始化失败")
		}
	}

	var tx *gorm.DB
	switch job.Type {
	case "oversea_small":
		tx = UrlDb.db.Raw("select url from url_out_lib_out where class_ntos = ? and ntos_small_other = ?", classCH, classCH)
	case "small":
		tx = UrlDb.db.Raw("select url from url_out_lib_out where class_ntos = ? and ntos_small_cn = ?", classCH, classCH)
	default:
		tx = UrlDb.db.Raw("select url from url_out_lib_out where class_ntos = ?", classCH)
	}

	// rows, err = UrlDb.db.Raw("select url from url_out_lib_out where class_ntos = ? and state <> 204 and state <> 205 and state <> 206 and state <> 203", classCH).Rows()
	// err = UrlDb.db.Debug().Model(&UrlOutLibOut{}).Select("url").Where("class = ?", "软件更新").Limit(10).Find(&result).Error
	f.WriteString("查询语句:" + UrlDb.db.ToSQL(func(_tx *gorm.DB) *gorm.DB { return tx }) + "\n")
	rows, err := tx.Rows()
	if err != nil {
		logging.ErrorLogger.Errorf("query url db by class err", err)
		return err
	}

	defer rows.Close()
	// fmt.Println(result)
	txt, err := os.OpenFile(filepath.Join(temp, classEN+".txt"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		logging.ErrorLogger.Errorf("create temp class file err", err)
		return err

	}

	num := 0
	for rows.Next() {
		var url string
		err := rows.Scan(&url)
		if err != nil {
			return err
		}

		if strings.HasPrefix(url, "www.") {
			url = url[4:]
		}

		if url == "" {
			continue
		}

		_, err = txt.WriteString(strings.ToLower(url) + "\n")
		num++
		if err != nil {
			return err
		}

	}

	txt.Close()
	// if num > 0 {
	if num == 0 {
		f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), fmt.Sprintf("%s 分类条目为0，请修订分类表或者添加分类数据", classEN)))
		return errors.New(fmt.Sprintf("分类条目为0，请修订分类表或者添加分类数据"))
	}
	command := fmt.Sprintf(`cd %s && %s -f "%s" -o ./ -n "%s" -c %d -v %s -d %s && rm -f "%s"`, temp, libs.Config.UrlPack.Program, classEN+".txt", classEN, num, job.Version, time.Now().Format("2006-01-02"), classEN+".txt")
	// command := fmt.Sprintf(`cd %s && /projects/url_db_ntos_1.0/a.out -f "%s" -o ./ -n "%s" -c %d -v %s -d %s`, temp, classEN+".txt", classEN, num, job.Version, time.Now().Format("2006-01-02"))
	out, err := libs.ExecCommand(command)
	f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), command))
	if err != nil {
		f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), fmt.Sprintf("转换分类%s.db文件出错, %s", classEN, out)))
		return err
	}

	return nil
}

func ZipFiles(zipname string, files []string) error {
	_, err := exec.LookPath("zip")
	if err != nil {
		return errors.New("zip 未安装")
	}
	command := fmt.Sprintf(`zip -qruD %s %s`, zipname, strings.Join(files, " "))
	cmd := exec.Command("/bin/bash", "-c", command)
	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout // 标准输出
	cmd.Stderr = &stderr // 标准错误
	cmd.Dir = filepath.Dir(zipname)
	cmdErr := cmd.Run()
	outStr, _ := stdout.String(), stderr.String()
	if cmdErr != nil {
		logging.ErrorLogger.Errorf("cmd.Run() failed err", cmdErr)
		logging.ErrorLogger.Errorf("cmd.Run() outStr err", outStr)
		return cmdErr
	}
	return nil
}

func packFiles(f *os.File, temp, dst string, incrementalDB []IncrementalDB, job *durlpackjob.UrlPackJob, isIncrement bool) error {

	if incrementalDB != nil || len(incrementalDB) > 0 {
		incrementalDBFi, err := os.OpenFile(filepath.Join(temp, "increment.db"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
		if err != nil {
			return err
		}
		jb, err := json.Marshal(incrementalDB)
		if err != nil {
			return err
		}
		_, err = incrementalDBFi.Write(jb)
		if err != nil {
			return err
		}
		// command := fmt.Sprintf(`cd %s && cp increment.db %s`, temp, dst)
		// f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), command))
		// stdout, err := libs.ExecCommand(command)
		// if err != nil {
		// 	f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), stdout))
		// 	return err
		// }
		// f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), stdout))
	}
	//初始化压缩包文件名
	var prefix string
	var fileSizeLimit int64
	config, err := durlpackconfig.GetAllMap()
	if err != nil {
		return err
	}
	if c, ok := config[job.Type]; ok {
		prefix = c.Prefix
		fileSizeLimit = int64(c.Size)
	} else {
		return errors.New("非法类型")
	}

	size, err := libs.DirSize(temp)
	if err != nil {
		f.WriteString(fmt.Sprintf("%s %s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), "计算目录大小异常", temp))
		return err
	}

	//列出所有文件大小
	command := fmt.Sprintf(`cd %s && du -b * |sort -hr`, temp)
	stdout, err := libs.ExecCommand(command)
	if err != nil {
		f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), stdout))
		return err
	}
	f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), stdout))

	if size > fileSizeLimit {
		f.WriteString(fmt.Sprintf("%s %s %s %v %s %v\r\n", time.Now().Format("2006-01-02 15:04:05"), "包大小超过限制", temp, size, ">", fileSizeLimit))
		return fmt.Errorf("包大小超过限制")
	} else {
		f.WriteString(fmt.Sprintf("%s %s %s %v\r\n", time.Now().Format("2006-01-02 15:04:05"), "包大小统计", temp, size))
	}

	files, err := ioutil.ReadDir(temp)
	if err != nil {
		f.WriteString(fmt.Sprintf("%s %s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), "读取目录异常", temp))
		return err
	}

	if len(files) > 0 {
		command := fmt.Sprintf(`cd %s && tar -cvf  category.tar ./* && cp category.tar %s`, temp, dst)
		f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), command))
		stdout, err := libs.ExecCommand(command)
		if err != nil {
			f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), stdout))
			return err
		}
		f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), stdout))
	} else {
		if !isIncrement {
			f.WriteString(fmt.Sprintf("%s %s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), "全量包category不能为空", temp))
			return errors.New("全量包category不能为空")
		}
	}

	// fn := "url_db_full_v" + job.Version + ".zip"

	fn := fmt.Sprintf("%s_%s_%s.zip", prefix, "full", job.Version)
	updateData := map[string]interface{}{
		"Full": filepath.Join(job.WorkDir, fn),
	}
	if isIncrement {
		fn = fmt.Sprintf("%s_%s_%s.zip", prefix, "patch", job.Version)
		updateData = map[string]interface{}{
			"Incremental": filepath.Join(job.WorkDir, fn),
		}
	}

	//运行加密工具对包进行签名
	// command := fmt.Sprintf("cd %s && make TARGET_NAME=%s INPUT_DIR=%s OUTPUT_DIR=%s", libs.Config.UrlPack.Sigpath, fn, dst, job.WorkDir)
	command = fmt.Sprintf("%s -d %s -o %s -t %s ", libs.Config.UrlPack.Sigpath, dst, job.WorkDir, fn)
	f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), command))
	stdout, err = libs.ExecCommand(command)
	if err != nil {
		f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), stdout))
		return err
	} else {
		f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), stdout))
	}

	updateData["UpdatedAt"] = time.Now()
	err = job.Update(job.ID, updateData)
	if err != nil {
		f.WriteString(fmt.Sprintf("%s %s\r\n", time.Now().Format("2006-01-02 15:04:05"), "更新数据库失败"))
		return err
	}
	// 增加md5记录

	return nil
}

/*
	{
	        "version": "20221214.1300",
	        "format_version": "1.0.0",
	        "date": "2022-12-14",
	        "is_increment": 0,
	        "applicable_version": "20221214.1100",
	        "scale_level": 1,
	        "category_num": 9,
	        "subcategory_num": 54
	        "is_oversea":0
	}
*/
type GlobalConf struct {
	Version           string `json:"version"`
	FormatVersion     string `json:"format_version"`
	Date              string `json:"date"`
	IsIncrement       int    `json:"is_increment"`
	ApplicableVersion string `json:"applicable_version"`
	ScaleLevel        int    `json:"scale_level"`
	CategoryNum       int    `json:"category_num"`
	SubcategoryNum    int    `json:"subcategory_num"`
	IsOversea         int    `json:"is_oversea"`
}

/*
[

	{
	        "name": "Leisure",
	        "name_ch": "娱乐休闲相关",
	        "id": 2,
	        "subcategory": [{
	                        "name": "Videos",
	                        "name_ch": "视频",
	                        "desc": "Film, television, video playback or download, shoot-off, DV shooting and other sites",
	                        "desc_ch": "包括提供电影、电视剧、视频的播放或下载，拍客，DV拍摄等网站",
	                        "id": 513
	                },
	                {
	                        "name": "Music",
	                        "name_ch": "音乐",
	                        "desc": "Provides online music, music-related Web site download link",
	                        "desc_ch": "包括提供在线音乐播放、音乐相关的下载链接的网站",
	                        "id": 514
	                }
	        ]
	},
	{
	        "name": "Bad",
	        "name_ch": "非法与不良",
	        "id": 8,
	        "subcategory": [{
	                "name": "Virus",
	                "name_ch": "病毒木马",
	                "desc": "Including carrying viruses, malicious code, Trojans, and spyware and all without the end-user organization or agency informed and allowed the case to install and run the program websites",
	                "desc_ch": "包括携带病毒、恶意代码、木马以及间谍软件等一切未经终端用户或机构组织知情和允许的情况下，安装并运行程序的网站",
	                "id": 2049
	        }]
	}

]
*/

type Subcategory struct {
	Name   string `json:"name"`
	NameCH string `json:"name_ch"`
	Desc   string `json:"desc"`
	DescCH string `json:"desc_ch"`
	ID     int    `json:"id"`
}

type UrlCategoryConf struct {
	Name        string         `json:"name"`
	NameCH      string         `json:"name_ch"`
	ID          int            `json:"id"`
	Subcategory []*Subcategory `json:"subcategory"`
}

/*
	[{
	        "name": "default",
	        "desc": "Default profile. Block unsavory.",
	        "desc_ch": "默认模板。阻断非法与不良。",
	        "subcategory": [{
	                        "name": "Virus",
	                        "action": "block"
	                },
	                {
	                        "name": "Gambling",
	                        "action": "block"
	                },
	                {
	                        "name": "Violence",
	                        "action": "block"
	                },
	                {
	                        "name": "Crime",
	                        "action": "block"
	                },
	                {
	                        "name": "Illegal",
	                        "action": "block"
	                },
	                {
	                        "name": "Adult",
	                        "action": "block"
	                },
	                {
	                        "name": "Porn",
	                        "action": "block"
	                }
	        ]
	}]
*/
type PredefProfileConf struct {
}

func TypeToInt(t string) int {
	if strings.Contains(t, "large") {
		return 1
	} else if strings.Contains(t, "middle") {
		return 2
	} else if strings.Contains(t, "small") {
		return 3
	}
	return 0
}

func createGloablConfFile(job *durlpackjob.UrlPackJob, categories []*durlpackcategory.ListResponse, dst string, incremental bool, f *os.File) error {
	// 生成global.conf
	var applicableVersion string
	var isIncrement int
	if incremental {
		applicableVersion = job.BaseVersion
		isIncrement = 1
	}
	var isOversea int
	if strings.Contains(job.Type, "oversea") {
		isOversea = 1
	}
	var categoryNumArray, subCategoryNumArray []int
	for _, category := range categories {
		if !libs.InArrayInt(categoryNumArray, int(category.GroupID)) {
			categoryNumArray = append(categoryNumArray, int(category.GroupID))
		}
		if !libs.InArrayInt(subCategoryNumArray, int(category.GroupID)) {
			subCategoryNumArray = append(subCategoryNumArray, int(category.GroupID)*512+int(category.CategoryID))
		}
	}

	globalConf := GlobalConf{
		Version:           job.Version,
		FormatVersion:     "1.0.0",
		Date:              job.CreatedAt.Format("2006-01-02"),
		IsIncrement:       isIncrement,
		ApplicableVersion: applicableVersion,
		ScaleLevel:        TypeToInt(job.Type),
		CategoryNum:       len(categoryNumArray),
		SubcategoryNum:    len(subCategoryNumArray),
		IsOversea:         isOversea,
	}
	jsonByte, err := json.Marshal(globalConf)
	if err != nil {
		f.WriteString("marshal global conf data error" + err.Error() + "\n")
		return err
	}
	conf, err := os.OpenFile(filepath.Join(dst, "global.conf"), os.O_RDWR|os.O_CREATE, 0666)
	if err != nil {
		f.WriteString("create global conf err" + err.Error() + "\n")
		logging.ErrorLogger.Errorf("create global conf err", err)
		return err

	}
	conf.WriteString(string(jsonByte))
	conf.Close()
	return nil
}

type predefProfileConfSubcategory struct {
	Name   string `json:"name"`
	Action string `json:"action"`
}

type predefProfileConf struct {
	Name        string                          `json:"name"`
	Desc        string                          `json:"desc"`
	DescCH      string                          `json:"desc_ch"`
	Subcategory []*predefProfileConfSubcategory `json:"subcategory"`
}

func createDescFiles(job *durlpackjob.UrlPackJob, categories []*durlpackcategory.ListResponse, dst string, incremental bool, f *os.File) error {
	/*
		"name": "default",
		"desc": "Default profile. Block unsavory.",
		"desc_ch": "默认模板。阻断非法与不良。",
	*/

	predefProfileConf := []predefProfileConf{
		{
			Name:        "default",
			Desc:        "Default profile. Block unsavory.",
			DescCH:      "默认模板。阻断非法与不良。",
			Subcategory: []*predefProfileConfSubcategory{},
		},
	}

	urlCategoryConfMap := map[int]*UrlCategoryConf{}
	// 按分组编号排序

	for _, category := range categories {
		if _, ok := urlCategoryConfMap[int(category.GroupID)]; ok {
			subCategory := Subcategory{
				Name:   category.CategoryNameEN,
				NameCH: category.CategoryNameCH,
				ID:     int(category.GroupID)*512 + int(category.CategoryID),
				Desc:   category.DescriptionEN,
				DescCH: category.DescriptionCH,
			}
			urlCategoryConfMap[int(category.GroupID)].Subcategory = append(urlCategoryConfMap[int(category.GroupID)].Subcategory, &subCategory)
		} else {
			subCategory := Subcategory{
				Name:   category.CategoryNameEN,
				NameCH: category.CategoryNameCH,
				ID:     int(category.GroupID)*512 + int(category.CategoryID),
				Desc:   category.DescriptionEN,
				DescCH: category.DescriptionCH,
			}
			urlCategoryConfMap[int(category.GroupID)] = &UrlCategoryConf{
				Name:        category.GroupNameEN,
				NameCH:      category.GroupNameCH,
				ID:          int(category.GroupID),
				Subcategory: []*Subcategory{&subCategory},
			}
		}

		if category.PreDefBlock {
			subcategory := predefProfileConfSubcategory{
				Name:   category.CategoryNameEN,
				Action: "block",
			}
			predefProfileConf[0].Subcategory = append(predefProfileConf[0].Subcategory, &subcategory)
		}
	}

	// 生成url_category.conf
	urlCategoryConf := []*UrlCategoryConf{}
	for k := range urlCategoryConfMap {
		urlCategoryConf = append(urlCategoryConf, urlCategoryConfMap[k])
	}
	sort.Slice(urlCategoryConf, func(i, j int) bool {
		return urlCategoryConf[i].ID < urlCategoryConf[j].ID
	})

	jsonByte, err := json.Marshal(urlCategoryConf)
	if err != nil {
		f.WriteString("marshal url category conf err" + err.Error() + "\n")
		return err
	}

	conf, err := os.OpenFile(filepath.Join(dst, "url_category.conf"), os.O_RDWR|os.O_CREATE, 0666)
	if err != nil {
		f.WriteString("create url category conf err" + err.Error() + "\n")
		logging.ErrorLogger.Errorf("create url category conf err", err)
		return err

	}
	conf.WriteString(string(jsonByte))
	conf.Close()

	// 生成predef_profile.conf

	conf, err = os.OpenFile(filepath.Join(dst, "predef_profile.conf"), os.O_RDWR|os.O_CREATE, 0666)
	if err != nil {
		logging.ErrorLogger.Errorf("create logfile err", err)
		return err

	}

	jsonByte, err = json.Marshal(predefProfileConf)
	if err != nil {
		return err
	}

	conf.WriteString(string(jsonByte))
	conf.Close()
	return nil
}

func Bytes2Bits(data []byte) []int {
	dst := make([]int, 0)
	for _, v := range data {
		for i := 0; i < 8; i++ {
			move := uint(7 - i)
			dst = append(dst, int((v>>move)&1))
		}
	}
	return dst
}

func createIncremental(fp1, fp2 string) ([]int, error) {
	result := []int{}
	fi1, err := os.Open(fp1)
	if err != nil {
		return result, err
	}

	fi2, err := os.Open(fp2)
	if err != nil {
		return result, err
	}

	// 创建 Reader
	r1 := bufio.NewReader(fi1)
	r2 := bufio.NewReader(fi2)
	buf1 := make([]byte, 1)
	buf2 := make([]byte, 1)
	pos := 0
	// flag用于跳过1，2两行
	flag1 := 0
	flag2 := 0
	_buf1 := make([]byte, 1)
	_buf2 := make([]byte, 1)

	for flag1 < 2 {
		err1 := binary.Read(r1, binary.BigEndian, &_buf1) //转换有两种不同的方式，也就是大端和小端。大端就是内存中低地址对应着整数的高位。
		if err1 == io.EOF {
			break
		}
		if string(_buf1) == "\n" {
			flag1++
		}
	}

	for flag2 < 2 {
		err2 := binary.Read(r2, binary.BigEndian, &_buf2)
		if err2 == io.EOF {
			break
		}
		if string(_buf2) == "\n" {
			flag2++
		}
	}

	for {
		//func (b *Reader) Read(p []byte) (n int, err error) {}
		err1 := binary.Read(r1, binary.BigEndian, &buf1) //转换有两种不同的方式，也就是大端和小端。大端就是内存中低地址对应着整数的高位。
		err2 := binary.Read(r2, binary.BigEndian, &buf2)

		if err1 == io.EOF || err2 == io.EOF {
			break
		}

		bits1 := Bytes2Bits(buf1)
		bits2 := Bytes2Bits(buf2)

		for i := 0; i < 8; i++ {
			if bits1[i] != bits2[i] {
				result = append(result, pos)
			}
			pos++
		}

	}
	return result, nil
}

func GetJobLog(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	job := durlpackjob.UrlPackJob{}

	err := job.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if job.ID == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未找到记录"))
		return
	}
	f, err := os.Open(job.Log)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	defer f.Close()
	// ctx.SendFile(job.Log, url.QueryEscape(filepath.Base(job.Log)))
	ret, err := io.ReadAll(f)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.WriteString(string(ret))
	return
}

func GetJobFull(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	job := durlpackjob.UrlPackJob{}

	err := job.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if job.ID == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未找到记录"))
		return
	}

	ctx.SendFile(job.Full, url.QueryEscape(filepath.Base(job.Full)))
	return
}

func GetJobIncrement(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	job := durlpackjob.UrlPackJob{}

	err := job.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if job.ID == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未找到记录"))
		return
	}

	ctx.SendFile(job.Incremental, url.QueryEscape(filepath.Base(job.Incremental)))
	return
}
