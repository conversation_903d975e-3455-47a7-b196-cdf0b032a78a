package dproject

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/datasync"
	"irisAdminApi/service/dao/datasync/dsyncrecord"

	"gorm.io/gorm"
)

const ModelName = "项目表"

type ProjectSyncResponse struct {
	/*
		"state": "SUCCESS",
		"data": [
			{
				"rownum": 1,
				"bugId": 259201,                            //BUGID
				"bugBelong": "测试人",                        //bug归属人
				"bugOwner": "测试人",                        //bug负责人
				"bugTestCharger": "测试2",                    //测试负责人
				"bugOs": "测试项目",                        //bug操作系统
				"bugProduct": "测试产品",                    //bug产品
				"workpacketName": null,                        //bug工作包
				"summary": "描述",                            //bug描述
				"mainbugid": null,                            //bug是否是从某个BUG镜像出来的BUG，如果有直接是父bugid
				"samebugid": null,                            //bug是否是从某个BUGsameas出来的BUG，如果有直接是父bugid
				"thedate": "2015-06-23 11:20:56",            //bug创建时间
				"lastupdatedate": "2015-06-23 15:28:53",    //bug更新时间
				"bugState": "DENIAL-ByDevelopment",            //bug状态
				"disabled": null                            //bug是否无效 0或者null 表示无效；1或者true表示有效
			}
		],
		"total":1,
		"message": null
	*/
	State   string             `json:"state"`
	Data    []*ProjectResponse `json:"data"`
	Total   int                `json:"total"`
	Message string             `json:"message"`
}

type ProjectResponse struct {
	/*
		"projectId": 99999,                        //rgos项目ID
		"projectName": "测试项目",                //项目名称
		"pmName": null,                            //pm名称
		"projectStatus": "完成(正式发布)",        //项目状态
		"projectType": "补丁项目",                //项目类别
		"baseProject": "测试项目",                //基线项目
		"applyTime": "2014-04-01 17:46:51",        //申请时间
		"lastUpdateTime": "2014-04-24 12:30:07"    //最后更新时间
	*/
	RowNum        int    `json:"rownum"`
	ProjectID     int    `gorm:"primarykey; autoIncrement:false" json:"projectId"`
	ProjectName   string `gorm:"not null; type:varchar(60)" json:"projectName"`
	PMName        string `gorm:"not null; type:varchar(60)" json:"pmName"`
	ProjectStatus string `gorm:"not null; type:varchar(60)" json:"projectStatus"`
	ProjectType   string `gorm:"not null; type:varchar(60)" json:"projectType"`
	BaseProject   string `gorm:"not null; type:varchar(60)" json:"baseProject"`
	CreatedAt     string `gorm:"not null; type:varchar(60)" json:"applyTime"`
	UpdatedAt     string `gorm:"not null; type:varchar(60)" json:"lastUpdateTime"`
}

type Response struct {
	datasync.BugProject
}

type ListResponse struct {
	Response
}

type Request struct {
	Id uint `json:"id"`
}

func (this *Response) ModelName() string {
	return ModelName
}

func Model() *datasync.BugProject {
	return &datasync.BugProject{}
}

func (this *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Where(`project_name != ""`)
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (this *Response) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}

	return nil
}

func FindAllRunningProjects() (result []string, err error) {
	err = easygorm.GetEasyGormDb().Model(Model()).Where(`project_name != "" and project_status in ("已立项", "进行中", "试点发布")`).Select("project_name").Scan(&result).Error
	return result, err
}

func FindRunningProjects() ([]*ListResponse, error) {
	items := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where(`project_name != "" and project_name not like "%NTOS%" and project_status in ("已立项", "进行中", "试点发布")`).Find(&items).Error
	return items, err
}

func FindAllNTOSProjects() ([]*ListResponse, error) {
	items := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where(`project_name like "NTOS%"`).Find(&items).Error
	return items, err
}

type Node struct {
	*ListResponse
	Children []*Node `json:"children"`
}

func getTopElements(o []*ListResponse, fn func([]*ListResponse, *ListResponse) bool) (t []*ListResponse, anyLuck bool) {
	for _, elem1 := range o {
		isFound := false
		for _, elem2 := range o {
			if elem1.BaseProject == elem2.ProjectName {
				isFound = true
				break
			}
		}
		if !isFound {
			anyLuck = true
			if !fn(t, elem1) {
				t = append(t, elem1)
			}
		}
	}
	return
}

func seek(root *Node, origin []*ListResponse) {
	searchingNode := root.Children
	for {
		endFlag := true
		tmpNode := []*Node{}

		for _, item := range searchingNode {
			for _, o := range origin {
				if item.ProjectName == o.BaseProject {
					endFlag = false
					item.Children = append(item.Children, &Node{ListResponse: &ListResponse{Response: Response{datasync.BugProject{ProjectName: o.ProjectName, BaseProject: o.ProjectName, CreatedAt: o.CreatedAt}}}})
				}
			}
			for _, child := range item.Children {
				tmpNode = append(tmpNode, child)
			}
		}
		if endFlag {
			break
		} else {
			searchingNode = tmpNode
		}
	}
}

func GenPrintTree(n *Node, s []string) []string {
	for _, item := range n.Children {
		s = GenPrintTree(item, s)
	}
	s = append(s, n.ProjectName)
	return s
}

func FindNodes(n *Node, s []*Node) []*Node {
	for _, item := range n.Children {
		s = FindNodes(item, s)
	}
	s = append(s, n)
	return s
}

func PrintTree(n *Node, s []string) {
	sc := GenPrintTree(n, s)
	for idx := range sc {
		fmt.Printf("%v\n", sc[len(sc)-1-idx])
	}
}

func genPrintTreeV1(n *Node, indent int, s []string) []string {
	for _, item := range n.Children {
		s = genPrintTreeV1(item, indent+1, s)
	}
	s = append(s, strings.Repeat("  ", indent-1)+"  ."+n.ProjectName)
	return s
}

func PrintTreeV1(n *Node, indent int, s []string) {
	sc := genPrintTreeV1(n, indent, s)
	for idx := range sc {
		fmt.Printf("%v\n", sc[len(sc)-1-idx])
	}
}

func FindNodeByName(n *Node, name string) *Node {
	for idx := range n.Children {
		if n.Children[idx].ProjectName == name {
			return n.Children[idx]
		}
	}
	for idx := range n.Children {
		if result := FindNodeByName(n.Children[idx], name); result != nil {
			return result
		}
	}
	return nil
}

func GenerateTree(projects []*ListResponse) *Node {
	root := &Node{ListResponse: &ListResponse{Response: Response{datasync.BugProject{ProjectName: "root", BaseProject: "", CreatedAt: time.Now()}}}}
	if topElements, anyLuck := getTopElements(projects,
		func(t []*ListResponse, e *ListResponse) (isExist bool) {
			for _, item := range t {
				if item == e {
					isExist = true
					break
				}
			}
			return
		}); anyLuck {
		his := map[string]bool{}
		for _, item := range topElements {
			if item.BaseProject == "" {
				root.Children = append(root.Children, &Node{ListResponse: item})
			} else {
				if _, ok := his[item.BaseProject]; !ok {
					his[item.BaseProject] = true
					root.Children = append(root.Children, &Node{ListResponse: &ListResponse{Response: Response{datasync.BugProject{ProjectName: item.BaseProject, BaseProject: "", CreatedAt: time.Now()}}}})
				}
			}
		}
		seek(root, projects)
	}
	return root

}

func UpdateOrCreateBugProjectTransaction(projects []*ProjectResponse, _url string, data map[string]string, method, state, errorMsg string) error {
	db := easygorm.GetEasyGormDb()
	batchCreateObjects := []map[string]interface{}{}
	err := db.Transaction(func(tx *gorm.DB) error {
		for _, project := range projects {
			response := Response{}
			projectsObject := map[string]interface{}{
				"project_id":     project.ProjectID,
				"project_name":   project.ProjectName,
				"pm_name":        project.PMName,
				"project_status": project.ProjectStatus,
				"project_type":   project.ProjectType,
				"base_project":   project.BaseProject,
				"created_at":     project.CreatedAt,
				"updated_at":     project.UpdatedAt,
			}
			if err := tx.Model(Model()).Where("project_id = ?", project.ProjectID).Find(&response).Error; err != nil {
				return err
			} else {
				if response.ProjectID == 0 {
					batchCreateObjects = append(batchCreateObjects, projectsObject)
				} else {
					if err := tx.Model(Model()).Where("project_id = ?", project.ProjectID).Updates(projectsObject).Error; err != nil {
						return err
					}
				}
			}
		}
		if len(batchCreateObjects) > 0 {
			if err := tx.Model(Model()).Create(batchCreateObjects).Error; err != nil {
				return err
			}
		}
		body, err := json.Marshal(data)
		if err != nil {
			return err
		}
		if err := tx.Model(dsyncrecord.Model()).Create(map[string]interface{}{
			"url":             _url,
			"body":            body,
			"method":          method,
			"state":           state,
			"message":         errorMsg,
			"min_modify_date": data["minModifyDate"],
			"max_modify_date": data["maxModifyDate"],
			"created_at":      time.Now(),
		}).Error; err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}
