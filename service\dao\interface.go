package dao

type Dao interface {
	ModelName() string
	All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error)
	AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error)
	Create(object map[string]interface{}) error
	CreateV2(object interface{}) error
	Update(id uint, object map[string]interface{}) error
	Find(id uint) error
	FindEx(col, value string) error
	Delete(id uint) error
}
