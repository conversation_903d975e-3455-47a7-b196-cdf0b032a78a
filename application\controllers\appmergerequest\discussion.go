package appmergerequest

import (
	"fmt"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/appmergerequest/dappmergerequestdiscussioncps"

	"github.com/kataras/iris/v12"
	"github.com/pkg/errors"
)

var gitlabSessionPattern = regexp.MustCompile(`_gitlab_session=([a-zA-Z0-9]+)`)

type PositionResponse struct {
	OldPath string `json:"old_path"`
	NewPath string `json:"new_path"`
	OldLine *uint  `json:"old_line"`
	NewLine *uint  `json:"new_line"`
}

type AuthorResponse struct {
	ID       uint   `json:"id"`
	Username string `json:"username"`
}

type NoteResponse struct {
	ID        uint             `json:"id"`
	Type      string           `json:"type"`
	Author    AuthorResponse   `json:"author"`
	System    bool             `json:"system"`
	Body      string           `json:"body"`
	Position  PositionResponse `json:"position"`
	CreatedAt time.Time        `json:"created_at"`
}

type DiscussionCpsResponse struct {
	ID        string           `json:"id"`
	ProjectID uint             `json:"project_id"`
	Notes     []NoteResponse   `json:"notes"`
	Position  PositionResponse `json:"position"`
}

func GetGitlabCookie(ctx iris.Context) (http.Cookie, error) {
	var cookie http.Cookie
	headers := ctx.Request().Header
	_cookie, ok := headers["Cookie"]

	if !ok {
		return cookie, fmt.Errorf("无法获取gitlab cookie")
	}

	var gitlabSession string
	matches := gitlabSessionPattern.FindStringSubmatch(strings.Join(_cookie, " "))
	if len(matches) > 1 {
		// 提取 _gitlab_session 的值
		gitlabSession = matches[1]
	} else {
		return cookie, fmt.Errorf("无法获取gitlab session")
	}

	cookie = http.Cookie{
		Name:     "_gitlab_session",
		Value:    gitlabSession,
		Path:     "/",
		Domain:   libs.Config.Gitlab.Url,
		MaxAge:   36000,
		HttpOnly: false,
		Secure:   false,
	}
	return cookie, nil
}

func GetUsernameBySession(ctx iris.Context) (string, error) {
	cookie, err := GetGitlabCookie(ctx)
	if err != nil {
		return "", errors.Wrap(err, "")
	}
	user := AuthorResponse{}
	url := fmt.Sprintf("%s/api/%s/user", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version)
	resp, err := GitlabWebClient.R().SetCookies(&cookie).SetSuccessResult(&user).Get(url)
	if err != nil {
		return "", errors.Wrap(err, resp.String())
	}
	return user.Username, nil
}

func GetGitlabProjectID(pathWithNameSpace string) (int, error) {
	project := ProjectResponse{}
	url := fmt.Sprintf("%s/api/%s/projects/%s?private_token=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, pathWithNameSpace, libs.Config.Gitlab.Token)
	resp, err := GitlabWebClient.R().SetSuccessResult(&project).Get(url)
	if err != nil {
		return 0, errors.Wrap(err, resp.String())
	}
	return project.ID, nil
}

func CreateDiscussionCps(ctx iris.Context) {
	request := dappmergerequestdiscussioncps.Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	username, err := GetUsernameBySession(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get username by session err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	// endpoint: /gitlab-instance-aa5fe906/Monitoring/-/merge_requests/33/discussions/4286fe5d628c82315c99f6281db00776514608cc/resolve
	var mergeRequestIID, discussionID string
	endpointSlice := strings.Split(request.Endpoint, "/")

	libs.ReverseSlice(endpointSlice)
	if len(endpointSlice) > 3 {
		discussionID = endpointSlice[1]
		mergeRequestIID = endpointSlice[3]
	} else {
		logging.ErrorLogger.Errorf("created discussion by endpoint err ", request.Endpoint)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": "created discussion by endpoint err"}, response.SystemErr.Msg))
		return
	}

	// discussion, discussionJson, err := GoGitlabClient.Discussions.GetMergeRequestDiscussion(request.ProjectID, mergeRequestIID, discussionID)
	discussion := DiscussionCpsResponse{}
	url := fmt.Sprintf("%s/api/%s/projects/%d/merge_requests/%s/discussions/%s?private_token=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, request.ProjectID, mergeRequestIID, discussionID, libs.Config.Gitlab.Token)

	resp, err := GitlabWebClient.R().SetSuccessResult(&discussion).Get(url)
	if err != nil {
		logging.ErrorLogger.Errorf("get discussion by id request err ", err, resp.String())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error() + resp.String()}, response.SystemErr.Msg))
		return
	}

	if resp.IsErrorState() {
		logging.ErrorLogger.Errorf("get discussion by id response err ", resp.String())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": resp.String()}, response.SystemErr.Msg))
		return
	}

	var object string

	discussionNotes := []string{}
	reviewer := ""
	// for _, note := range discussion.Notes {
	// 	if !note.System {

	// 		if len(note.Position.NewPath) > 0 {
	// 			object = note.Position.NewPath
	// 			object = fmt.Sprintf("new:%s:%d", object, *note.Position.NewLine)
	// 		} else {
	// 			object = note.Position.OldPath
	// 			object = fmt.Sprintf("old:%s:%d", object, *note.Position.OldLine)
	// 		}

	// 		discussionNotes = append(discussionNotes, fmt.Sprintf("%s:%s", note.Author.Username, note.Body))
	// 		if len(reviewer) == 0 {
	// 			reviewer = note.Author.Username
	// 		}
	// 	}
	// }
	for _, note := range discussion.Notes {
		if !note.System && (note.Type == "DiffNote" || note.Type == "DiscussionNote") {
			if len(note.Position.NewPath) > 0 && note.Position.NewLine != nil {
				object = note.Position.NewPath
				object = fmt.Sprintf("new:%s:%d", object, *note.Position.NewLine)

			} else if len(note.Position.OldPath) > 0 && note.Position.OldLine != nil {
				object = note.Position.OldPath
				object = fmt.Sprintf("old:%s:%d", object, *note.Position.OldLine)
			}

			discussionNotes = append(discussionNotes, fmt.Sprintf("%s:%s", note.Author.Username, note.Body))
			if len(reviewer) == 0 {
				reviewer = note.Author.Username
			}
		}
	}

	// cps := dmergerequestdiscussioncps.MergeRequestDiscussionCps{}
	// err = cps.FindEx("discussion_id", discussionID)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("find discussion by id err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
	// 	return
	// }

	// if cps.Category == 1 {
	// 	reviewer := discussion.Notes[0].Author.Username
	// 	if username != reviewer && reviewer != "buildfarm" {
	// 		ctx.JSON(response.NewResponse(response.ParamentErr.Code, map[string]string{"err_msg": fmt.Sprintf("缺陷只允许评委: %s 修改", reviewer)}, response.ParamentErr.Msg))
	// 		return
	// 	}
	// }

	if request.State == 1 {
		mergerequest := MergeRequestResponse{}
		url := fmt.Sprintf("%s/api/%s/projects/%d/merge_requests/%s?private_token=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, request.ProjectID, mergeRequestIID, libs.Config.Gitlab.Token)

		resp, err := GitlabWebClient.R().SetSuccessResult(&mergerequest).Get(url)
		if err != nil {
			logging.ErrorLogger.Errorf("get discussion by id request err ", err, resp.String())
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error() + resp.String()}, response.SystemErr.Msg))
			return
		}

		if resp.IsErrorState() {
			logging.ErrorLogger.Errorf("get discussion by id response err ", resp.String())
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": resp.String()}, response.SystemErr.Msg))
			return
		}
		assigneeFlag := false
		reviewerFlag := false
		// author := mergerequest.Author.Username

		for _, assignee := range mergerequest.Assignees {
			if username == assignee.Username {
				assigneeFlag = true
				break
			}
		}

		for _, r := range mergerequest.Reviewers {
			if username == r.Username {
				reviewerFlag = true
				break
			}
		}

		switch reviewer {
		case "buildfarm":
			if !assigneeFlag && !reviewerFlag {
				ctx.JSON(response.NewResponse(response.ParamentErr.Code, map[string]string{"err_msg": "只允许合并人/评审委关闭"}, response.ParamentErr.Msg))
				return
			}
		case "aicodereviewer":
		default:
			if username != reviewer && !assigneeFlag {
				ctx.JSON(response.NewResponse(response.ParamentErr.Code, map[string]string{"err_msg": fmt.Sprintf("只允许合并人以及评委: %s 关闭", reviewer)}, response.ParamentErr.Msg))
				return
			}
		}
	}

	discussionNoteString := strings.Join(discussionNotes, "\n")

	data := map[string]interface{}{
		"CreatedAt":       time.Now(),
		"UpdatedAt":       time.Now(),
		"TargetProjectID": request.ProjectID,
		"MergeRequestIID": mergeRequestIID,
		"Object":          object,
		"Discussion":      discussionNoteString,
		"DiscussionID":    discussionID,
		"Reviewer":        reviewer,
		"Category":        request.Category,
		"Severity":        request.Severity,
		"Confirm":         request.Confirm,
		"Introduction":    request.Introduction,
		"Condition":       request.Condition,

		"Comment":            request.Comment,
		"State":              request.State,
		"LastChangeUsername": username,
	}

	discussionCps := dappmergerequestdiscussioncps.AppMergeRequestDiscussionCps{}
	err = discussionCps.Create(data)
	if err != nil {
		logging.ErrorLogger.Errorf("create cps err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetDiscussionCps(ctx iris.Context) {
	var lastFetchAt int
	var err error
	_lastFetchAt := ctx.GetHeader("X-Last-Fetched-At")
	if _lastFetchAt != "" {
		lastFetchAt, err = strconv.Atoi(_lastFetchAt)
		if err != nil {
			lastFetchAt = 0
		}
	}
	request := dappmergerequestdiscussioncps.Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if request.Endpoint == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Endpoint is null"))
		return
	}
	// endpoint: /gitlab-instance-aa5fe906/Monitoring/-/merge_requests/33/discussions.json
	var projectID, mergeRequestIID string
	endpointSlice := strings.Split(request.Endpoint, "/")
	libs.ReverseSlice(endpointSlice)
	projectSlice := strings.Split(request.Endpoint, "/-/merge_requests/")
	if len(projectSlice) > 1 {
		projectID = projectSlice[0]
		projectID = strings.TrimPrefix(projectID, "/")
		projectID = strings.TrimSuffix(projectID, "/")
		projectID = url.PathEscape(projectID)
	}
	mergeRequestIID = endpointSlice[1]
	targetProjectID, err := GetGitlabProjectID(projectID)
	if err != nil {
		logging.ErrorLogger.Errorf("get project int id err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	cpses, err := dappmergerequestdiscussioncps.FindCpsByProjectAndMergerequestIID(targetProjectID, mergeRequestIID, lastFetchAt)
	if err != nil {
		logging.ErrorLogger.Errorf("create cps err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	result := map[string]*dappmergerequestdiscussioncps.ListResponse{}
	for _, cps := range cpses {
		result[cps.DiscussionID] = cps
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func DeleteDiscussionCps(ctx iris.Context) {
	discussionID := ctx.Params().GetString("discussion_id")

	_, err := GetUsernameBySession(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get username by session err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	// endpoint: /gitlab-instance-aa5fe906/Monitoring/-/merge_requests/33/discussions.json
	err = dappmergerequestdiscussioncps.DeleteCpsByDiscussionID(discussionID)
	if err != nil {
		logging.ErrorLogger.Errorf("delete cps by discussion id err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

var addCommitPattern = regexp.MustCompile(`added \d+ commit`)

func GetGitlabDiscussionsByProjectAndMergeRequest(projectID, mergeRequestIID uint) ([]*DiscussionCpsResponse, error) {
	discussions := []*DiscussionCpsResponse{}
	page := 1
	pageSize := 100
	for {
		_discussions := []*DiscussionCpsResponse{}
		url := fmt.Sprintf("%s/api/%s/projects/%d/merge_requests/%d/discussions?private_token=%s&per_page=%d&per_page=%d", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, projectID, mergeRequestIID, libs.Config.Gitlab.Token, pageSize, page)

		resp, err := GitlabWebClient.R().SetSuccessResult(&_discussions).Get(url)
		if err != nil {
			return discussions, errors.Wrap(err, "")
		}
		if resp.IsErrorState() {
			return discussions, fmt.Errorf(resp.String())
		}
		discussions = append(discussions, _discussions...)
		totalPages, ok2 := resp.Header["X-Total-Pages"]
		if ok2 && len(totalPages) > 0 {
			t, err2 := strconv.Atoi(totalPages[0])
			if err2 == nil {
				page++
				if page > t {
					break
				}
			}
		} else {
			return discussions, fmt.Errorf("获取gitlab api total pages失败")
		}
		time.Sleep(1 * time.Second)
	}
	return discussions, nil
}

func GetGitlabNotesByProjectAndMergeRequest(projectID, mergeRequestIID uint) ([]*NoteResponse, error) {
	notes := []*NoteResponse{}
	page := 1
	pageSize := 100
	for {
		_notes := []*NoteResponse{}
		url := fmt.Sprintf("%s/api/%s/projects/%d/merge_requests/%d/notes?private_token=%s&per_page=%d&per_page=%d", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, projectID, mergeRequestIID, libs.Config.Gitlab.Token, pageSize, page)

		resp, err := GitlabWebClient.R().SetSuccessResult(&_notes).Get(url)
		if err != nil {
			return notes, errors.Wrap(err, "")
		}
		if resp.IsErrorState() {
			return notes, fmt.Errorf(resp.String())
		}
		notes = append(notes, _notes...)
		totalPages, ok2 := resp.Header["X-Total-Pages"]
		if ok2 && len(totalPages) > 0 {
			t, err2 := strconv.Atoi(totalPages[0])
			if err2 == nil {
				page++
				if page > t {
					break
				}
			}
		} else {
			return notes, fmt.Errorf("获取gitlab api total pages失败")
		}
		time.Sleep(1 * time.Second)
	}
	return notes, nil
}

func GitlabDiscussionResolve(ctx iris.Context) {
	// cookie, err := GetGitlabCookie(ctx)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("get username by session err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	username, err := GetUsernameBySession(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get username by session err ", err)
		ctx.StatusCode(iris.StatusBadRequest)
		ctx.ContentType("application/json")
		ctx.JSON("{}")
		return
	}

	origin := ctx.Request().Header.Get("Origin")
	originUrl := ctx.Request().Header.Get("Origin-Url")
	if originUrl == "" || !strings.HasSuffix(originUrl, "/resolve") {
		logging.ErrorLogger.Errorf("get resolve referer err ", originUrl)
		ctx.StatusCode(iris.StatusBadRequest)
		ctx.ContentType("application/json")
		ctx.JSON("{}")
		return
	}

	var projectID, mergeRequestIID, discussionID string
	endpointSlice := strings.Split(originUrl, "/")
	projectSlice := strings.Split(originUrl, "/-/merge_requests/")
	if len(projectSlice) > 1 {
		projectID = projectSlice[0]
		projectID = strings.TrimPrefix(projectID, "/")
		projectID = strings.TrimSuffix(projectID, "/")
		projectID = url.PathEscape(projectID)
	}
	libs.ReverseSlice(endpointSlice)
	discussionID = endpointSlice[1]
	mergeRequestIID = endpointSlice[3]
	discussion := DiscussionCpsResponse{}
	url := fmt.Sprintf("%s/api/%s/projects/%s/merge_requests/%s/discussions/%s?private_token=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, projectID, mergeRequestIID, discussionID, libs.Config.Gitlab.Token)

	resp, err := GitlabWebClient.R().SetSuccessResult(&discussion).Get(url)
	if err != nil {
		logging.ErrorLogger.Errorf("get discussion by id request err ", err, resp.String())
		ctx.StatusCode(iris.StatusBadRequest)
		ctx.ContentType("application/json")
		ctx.JSON("{}")
		return
	}

	if resp.IsErrorState() {
		logging.ErrorLogger.Errorf("get discussion by id response err ", resp.String())
		ctx.StatusCode(iris.StatusBadRequest)
		ctx.ContentType("application/json")
		ctx.JSON("{}")
		return
	}
	if len(discussion.Notes) == 0 {
		logging.ErrorLogger.Errorf("get discussion note err ", discussion)
		ctx.StatusCode(iris.StatusBadRequest)
		ctx.ContentType("application/json")
		ctx.JSON("{}")
		return
	}
	cps := dappmergerequestdiscussioncps.AppMergeRequestDiscussionCps{}
	err = cps.FindEx("discussion_id", discussionID)
	if err != nil {
		logging.ErrorLogger.Errorf("find discussion by id err ", err)
		ctx.StatusCode(iris.StatusBadRequest)
		ctx.ContentType("application/json")
		ctx.JSON("{}")
		return
	}

	reviewer := discussion.Notes[0].Author.Username
	if username != reviewer && reviewer != "buildfarm" && username != "buildfarm" && reviewer != "aicodereviewer" {
		ctx.StatusCode(iris.StatusForbidden)
		ctx.ContentType("application/json")
		ctx.JSON("{}")
		return
	}

	r := GitlabWebClient.R()

	resp, err = r.SetHeader("X-CSRF-Token", ctx.Request().Header.Get("X-CSRF-Token")).SetCookies(ctx.Request().Cookies()...).Post(origin + originUrl + "?real=1")
	if err != nil {
		logging.ErrorLogger.Errorf("resolve discussion by id request err ", err, resp.String())
		ctx.StatusCode(iris.StatusBadRequest)
		ctx.ContentType("application/json")
		ctx.JSON("{}")
		return
	}

	if resp.IsErrorState() {
		logging.ErrorLogger.Errorf("resolve discussion by id response err ", resp.String())
		ctx.StatusCode(iris.StatusBadRequest)
		ctx.ContentType("application/json")
		ctx.JSON("{}")
		return
	}

	ctx.StatusCode(resp.StatusCode)
	ctx.ContentType(resp.Header.Get("Content-Type"))
	ctx.WriteString(resp.String())
	return
}

func MergeRequestDiscussions(ctx iris.Context) {
	projectId := ctx.Params().GetString("project_id")
	mergeRequestIID := ctx.Params().GetString("merge_request_iid")
	if projectId == "" || mergeRequestIID == "" {
		logging.ErrorLogger.Errorf("params error")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	mr, err := MergeRequest(projectId, mergeRequestIID)
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if mr == nil {
		logging.ErrorLogger.Errorf("未找到mr")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	request := DiscussionRequest{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("create project read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(&request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		logging.ErrorLogger.Errorf("create project read json err ", strings.Join(errs, ";"))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	request.Position.BaseSha = mr.DiffRefs.BaseSha
	request.Position.HeadSha = mr.DiffRefs.HeadSha
	request.Position.StartSha = mr.DiffRefs.StartSha

	if _, err := mr.CreateMergeRequsetDiscussion(&request); err != nil {
		logging.ErrorLogger.Errorf("create merge request discussion err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func MergeRequest(projectId, mergeRequestIID string) (*MergeRequestResponse, error) {
	mergeRequestResponse := MergeRequestResponse{}
	var errMsg interface{}
	resp, err := GitlabClient.R().SetSuccessResult(&mergeRequestResponse).SetErrorResult(&errMsg).Get(fmt.Sprintf("projects/%s/merge_requests/%s", projectId, mergeRequestIID))
	if err != nil {
		logging.ErrorLogger.Errorf("get pipeline error", err.Error())
		return &mergeRequestResponse, err
	}
	if resp.IsSuccessState() {
		return &mergeRequestResponse, nil
	}
	return nil, fmt.Errorf("%v", errMsg)
}

func GetMergeRequset(ctx iris.Context) {
	projectId := ctx.Params().GetString("project_id")
	mergeRequestIID := ctx.Params().GetString("merge_request_iid")
	if projectId == "" || mergeRequestIID == "" {
		logging.ErrorLogger.Errorf("params error")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	mr, err := MergeRequest(projectId, mergeRequestIID)
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if mr == nil {
		logging.ErrorLogger.Errorf("未找到mr")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, mr, response.NoErr.Msg))
	return
}

func (r *MergeRequestResponse) GetMergeRequsetDiscussions() ([]*DiscussionResponse, error) {
	var errMsg interface{}
	var result []*DiscussionResponse
	resp, err := GitlabClient.R().SetErrorResult(&errMsg).SetSuccessResult(&result).Get(fmt.Sprintf("projects/%d/merge_requests/%d/discussions", r.ProjectID, r.IID))
	if err != nil {
		logging.ErrorLogger.Errorf("get pipeline error", err.Error())
		return result, err
	}
	if resp.IsError() {
		return result, errors.New(fmt.Sprintf("Unknow error, %s", errMsg))
	}
	return result, nil
}
