package duserdepartment

import (
	"errors"
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/user"
)

const ModelName = "用户部门关系管理"

type UserDepartment struct {
	Id           uint   `json:"id"`
	UserID       uint   `json:"user_id"`
	DepartmentID uint   `json:"department_id"`
	UpdatedAt    string `json:"updated_at"`
	CreatedAt    string `json:"created_at"`
}

type ListResponse struct {
	UserDepartment
}

func (u *UserDepartment) ModelName() string {
	return ModelName
}

func Model() *user.UserDepartment {
	return &user.UserDepartment{}
}

func (this *UserDepartment) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var groups []*ListResponse
	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&groups).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": groups, "total": count, "limit": pageSize}
	return list, nil
}

func (this *UserDepartment) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var groups []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&groups).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": groups, "total": count, "limit": pageSize}
	return list, nil
}

func (this *UserDepartment) FindByUserName(username string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("name = ?", username).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user by username ", username, " err ", err)
		return err
	}
	return nil
}

func (this *UserDepartment) Create(object map[string]interface{}) error {
	//检查GroupId是否存在
	// group := ddepartment.Response{}
	// if groupId, ok := object["DepartmentID"].(string); ok {
	// 	err := group.FindEx("id", groupId)
	// 	if err != nil {
	// 		logging.ErrorLogger.Errorf("create user group relation err ", err)
	// 		return err
	// 	}

	// 	if group.Id == 0 {
	// 		return errors.New(fmt.Sprintf("username %s is not exists", groupId))
	// 	}
	// }

	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *UserDepartment) CreateV2(object interface{}) error {
	return nil
}

func (this *UserDepartment) Update(id uint, object map[string]interface{}) error {
	err := this.Find(id)
	if err != nil {
		return err
	}

	if name, ok := object["Name"].(string); ok {
		err := this.FindByUserName(name)
		if err != nil {
			logging.ErrorLogger.Errorf("create user find by name get err ", err)
			return err
		}

		if this.Id > 0 && this.Id != id {
			return errors.New(fmt.Sprintf("name %s is being used", name))
		}
	}
	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).UpdateColumns(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *UserDepartment) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *UserDepartment) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *UserDepartment) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func FindByUserId(id uint) (UserDepartment, error) {
	var userGroup UserDepartment
	err := easygorm.GetEasyGormDb().Model(Model()).Where("user_id = ?", id).Find(&userGroup).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return userGroup, err
	}
	return userGroup, nil
}

func FindInUserIds(ids []uint) ([]*UserDepartment, error) {
	var items []*UserDepartment
	err := easygorm.GetEasyGormDb().Model(Model()).Where("user_id in ?", ids).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return items, err
	}
	return items, nil
}

func FindByDepartmentId(id uint) (UserDepartment, error) {
	var item UserDepartment
	err := easygorm.GetEasyGormDb().Model(Model()).Where("department_id = ?", id).Find(&item).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return item, err
	}
	return item, nil
}

func FindInDepartmentIds(ids []string) ([]*ListResponse, error) {
	var userListGroup []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("department_id in ?", ids).Find(&userListGroup).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return userListGroup, err
	}
	return userListGroup, nil
}

func DeleteByUserID(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), "user_id = ?", id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return err
	}
	return nil
}
