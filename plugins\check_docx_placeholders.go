package main

import (
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
)

// 占位符检查结果
type PlaceholderResult struct {
	Found        bool     `json:"found"`
	Placeholders []string `json:"placeholders"`
}

// CheckDocxPlaceholders 检查Word文档中是否存在指定的占位符
// docxPath: Word文档路径
// customPlaceholders: 自定义占位符列表，如果为nil则使用默认列表
// 返回值:
//   - *PlaceholderResult: 包含检查结果的结构体
//   - error: 错误信息
func CheckDocxPlaceholders(docxPath string, customPlaceholders []string) (*PlaceholderResult, error) {
	var cmd *exec.Cmd

	// 获取Python脚本的绝对路径
	scriptPath := filepath.Join("plugins", "check_docx_placeholders.py")

	if len(customPlaceholders) > 0 {
		// 将占位符列表转换为JSON
		placeholdersJSON, err := json.Marshal(customPlaceholders)
		if err != nil {
			return nil, fmt.Errorf("序列化占位符列表时出错: %v", err)
		}

		// 构建命令，包含自定义占位符
		cmd = exec.Command("python3", scriptPath, docxPath, string(placeholdersJSON))
	} else {
		// 使用默认占位符
		cmd = exec.Command("python3", scriptPath, docxPath)
	}

	// 执行命令
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("执行脚本时出错: %v", err)
	}

	// 解析结果
	var result PlaceholderResult
	if err := json.Unmarshal(output, &result); err != nil {
		return nil, fmt.Errorf("解析结果时出错: %v", err)
	}

	return &result, nil
}

// 默认占位符列表，与Python脚本中的默认列表保持一致
var DefaultPlaceholders = []string{
	"{{size}}",
	"{{md5}}",
	"{{name}}",
	"{{softwarenum}}",
	"{{softwarever}}",
	"{{ospkgName}}",
	"{{ospkgMD5}}",
	"{{ospkgSize}}",
	"{{ubootVersion}}",
	"{{time}}",
	"{{productModel}}",
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("用法: go run check_docx_placeholders.go <Word文档路径>")
		os.Exit(1)
	}

	docxPath := os.Args[1]

	// 检查文件是否存在
	if _, err := os.Stat(docxPath); os.IsNotExist(err) {
		fmt.Printf("错误: 文件 '%s' 不存在\n", docxPath)
		os.Exit(1)
	}

	// 使用默认占位符检查文档
	result, err := CheckDocxPlaceholders(docxPath, nil)
	if err != nil {
		fmt.Printf("检查文档时出错: %v\n", err)
		os.Exit(1)
	}

	// 输出结果
	if result.Found {
		fmt.Println("文档中存在以下占位符:")
		for i, placeholder := range result.Placeholders {
			fmt.Printf("  %d. %s\n", i+1, placeholder)
		}
	} else {
		fmt.Println("文档中不存在任何指定的占位符")
	}
}
