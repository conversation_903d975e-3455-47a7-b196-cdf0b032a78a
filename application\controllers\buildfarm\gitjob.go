package buildfarm

import (
	"bytes"
	"encoding/json"
	"fmt"
	"mime/multipart"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/buildfarm"
	"irisAdminApi/service/cache"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/buildfarm/dgitjob"
	"irisAdminApi/service/dao/buildfarm/dmakejob"
	"irisAdminApi/service/dao/user/duser"

	"github.com/kataras/iris/v12"
)

type SwitchBranches struct {
	Type1       string `json:"type1"`
	Type2       string `json:"type2"`
	Repo        string `json:"repo"`
	Branch      string `json:"branch"`
	UnitPackage string `json:"unitpackage"`
}

func PushCoverityAuditMailQueue(gitjob *dgitjob.GitJob, makejob *dmakejob.MakeJob, status int) {
	if libs.Config.Mail.Enable {
		rc := cache.GetRedisClusterClient()
		from := "编译农场"
		subject := fmt.Sprintf("[编译农场][普通编译][作业ID:%s][%s]", makejob.TaskID, statusMap[status])
		body := fmt.Sprintf(`%s<br>%s 提交商业代码审查前置作业，待审批。<br>前往:<a href="http://172.28.244.15:9090/buildfarm/#/manage/coverity">商业代码审批</a>进行审批`, subject, duser.UserMap[gitjob.ID].Name)
		for userId := range duser.UserMap {
			if libs.InArrayS(duser.UserMap[userId].Roles, "编译农场管理员") {
				to := duser.UserMap[gitjob.UserID].Username + "@ruijie.com.cn"
				msg := strings.Join([]string{from, to, subject, body}, "|")
				_, err := rc.LPush(libs.Config.Mail.Queue, msg)
				if err != nil {
					logging.ErrorLogger.Error(err)
				}
			}
		}
	}
}

func GetGitJobs(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	customized := ctx.FormValue("customized")
	taskType := ctx.FormValue("task_type")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	gitjob := dgitjob.GitJob{}
	list, err := gitjob.All(id, name, customized, taskType, sort, orderBy, page, pageSize)

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

type GitjobReq struct {
	Project    string `json:"project"`
	Repo       string `json:"repo"`
	Branch     string `json:"branch"`
	Product    string `json:"product"`
	TaskType   uint   `json:"task_type"`
	Customized uint   `json:"customized"`
}

func CreateGitJob(ctx iris.Context) {
	userID, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	makeJobReq := &dmakejob.MakejobReq{}
	if err := ctx.ReadForm(makeJobReq); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*makeJobReq)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	parentPath := libs.Config.Buildfarm.Compilepath

	jobID := libs.GetUUID()
	taskID := libs.GetUUID()
	var serverID uint

	feedsconfigcustom := ""
	feedsconfigcustomArray := []string{}
	switchBranches := []SwitchBranches{}
	if makeJobReq.CustomEnable {
		err := json.Unmarshal([]byte(makeJobReq.SwitchBranches), &switchBranches)
		if err != nil {
			logging.ErrorLogger.Errorf("unmarshal switch branches failed:", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		for _, switch_branch := range switchBranches {
			var line string
			if strings.HasPrefix(switch_branch.Branch, "^") {
				line = fmt.Sprintf("%s %s %s %s%s", switch_branch.Type1, switch_branch.Type2, switch_branch.UnitPackage, switch_branch.Repo, switch_branch.Branch)
			} else {
				line = fmt.Sprintf("%s %s %s %s;%s", switch_branch.Type1, switch_branch.Type2, switch_branch.UnitPackage, switch_branch.Repo, switch_branch.Branch)
			}
			feedsconfigcustomArray = append(feedsconfigcustomArray, line)
		}
		feedsconfigcustom = strings.Join(feedsconfigcustomArray, "\n")
	}

	if makeJobReq.PatchEnable {
		tempPatchDir, err := MakeTempDir(jobID, "patches")
		if err != nil {
			logging.ErrorLogger.Errorf("create temp dir failed:", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		if _, _, err := ctx.UploadFormFiles(tempPatchDir, func(ctx iris.Context, file *multipart.FileHeader) bool {
			return true
		}); err != nil {
			logging.ErrorLogger.Errorf("save file failed:", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		f, err := os.OpenFile(filepath.Join(tempPatchDir, "patch_infos.txt"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
		defer f.Close()
		f.Write([]byte(makeJobReq.Patches))
	}

	if makeJobReq.CustomDefconfig != "" {
		tempPatchDir, err := MakeTempDir(jobID, "defconfig")
		if err != nil {
			logging.ErrorLogger.Errorf("create temp dir failed:", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		if _, _, err = ctx.UploadFormFiles(tempPatchDir, func(ctx iris.Context, file *multipart.FileHeader) bool {
			if file.Filename == makeJobReq.CustomDefconfig {
				file.Filename = makeJobReq.Product + "_defconfig"
				return true
			}
			return false
		}); err != nil {
			logging.ErrorLogger.Errorf("save file failed:", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	}

	if makeJobReq.CustomKernelConfig != "" {
		tempPatchDir, err := MakeTempDir(jobID, "kernel_config")
		if err != nil {
			logging.ErrorLogger.Errorf("create temp dir failed:", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		if _, _, err = ctx.UploadFormFiles(tempPatchDir, func(ctx iris.Context, file *multipart.FileHeader) bool {
			if file.Filename == makeJobReq.CustomKernelConfig {
				file.Filename = "kernel_config"
				return true
			}
			return false
		}); err != nil {
			logging.ErrorLogger.Errorf("save file failed:", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	}

	if makeJobReq.ID == 0 {
		// todo: 增加事务创建作业及任务
		tempDir := filepath.Join(parentPath, jobID)
		gitjob := dgitjob.GitJob{}
		err = gitjob.Create(map[string]interface{}{
			"JobID":      jobID,
			"UserID":     userID,
			"Project":    makeJobReq.Project,
			"Repo":       makeJobReq.Repo,
			"Branch":     makeJobReq.Branch,
			"Customized": makeJobReq.Customized,
			"TaskType":   makeJobReq.TaskType,
			"ServerID":   0,
			"Status":     0,
			"Dir":        tempDir,
			"Version":    "",
			"CreatedAt":  time.Now(),
		})
		if err != nil {
			logging.ErrorLogger.Errorf("create git job get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	} else {
		gitjob := dgitjob.GitJob{}
		err := gitjob.Find(makeJobReq.ID)
		if err != nil {
			logging.ErrorLogger.Errorf("create git job get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		if gitjob.ID == 0 {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "作业不存在，无法提交编译"))
			return
		}
		jobID = gitjob.JobID
		serverID = gitjob.ServerID
	}

	// 创建makejob用于排队
	makejob := dmakejob.MakeJob{}
	status := 3
	if makeJobReq.TaskType == 3 {
		status = 4
	}

	err = makejob.Create(map[string]interface{}{
		"JobID":             jobID,
		"TaskID":            taskID,
		"UserID":            userID,
		"TaskType":          makeJobReq.TaskType,
		"Status":            status,
		"Product":           makeJobReq.Product,
		"Defconfig":         makeJobReq.Defconfig,
		"Target":            makeJobReq.Target,
		"Baseline":          makeJobReq.Baseline,
		"Version":           "",
		"CreatedAt":         time.Now(),
		"FeedsConfigCustom": feedsconfigcustom,
		"PatchEnable":       makeJobReq.PatchEnable,
		"BuildType":         makeJobReq.BuildType,
		"BuildOspkg":        makeJobReq.BuildOspkg,
		"DirClean":          makeJobReq.DirClean,
		"ServerID":          serverID,
		"Comment":           makeJobReq.Comment,
		"KernelRepo":        makeJobReq.KernelRepo,
		"KernelBranch":      makeJobReq.KernelBranch,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("create make job get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	go CheckQueue()

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func MakeTempDir(jobId, name string) (string, error) {
	tempPatchDir := filepath.Join(libs.Config.Buildfarm.Temp, jobId, name)
	if _, err := os.Stat(tempPatchDir); err == nil {
		return tempPatchDir, os.RemoveAll(tempPatchDir)
	}
	err := os.MkdirAll(tempPatchDir, 0o750)
	os.Chmod(tempPatchDir, 0o750)
	return tempPatchDir, err
}

func GetTempDir(jobId, name string) (string, error) {
	tempPatchDir := filepath.Join(libs.Config.Buildfarm.Temp, jobId, name)
	_, err := os.Stat(tempPatchDir)
	if err != nil {
		return "", err
	}
	return tempPatchDir, nil
}

func updateGitJob(gitjob *dgitjob.GitJob, status int, version string) {
	err := gitjob.Update(gitjob.ID, map[string]interface{}{
		"Status":    status,
		"ServerID":  gitjob.ServerID,
		"UpdatedAt": time.Now(),
		"Version":   version,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("update git job err ", err)
	}
}

func lsDefConfigJob(gitjob *dgitjob.GitJob) ([]string, error) {
	defconfig := []string{}

	client, err := SSHClient(gitjob.ServerID)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		return defconfig, err
	}
	defer client.Close()
	f, err := os.OpenFile(filepath.Join(libs.Config.Buildfarm.Logpath, gitjob.JobID+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		return defconfig, err
	}
	defer f.Close()
	command := fmt.Sprintf(`cd %s && make download`, gitjob.Dir)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("make download", err.Error())
		return defconfig, err
	}
	var stdOut bytes.Buffer
	command = fmt.Sprintf(`cd %s && make list-defconfigs|grep -v "="|grep defconfig|grep '-'|awk '{print $1}'`, gitjob.Dir)
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("get defconfig list", err.Error())
		return defconfig, err
	}

	defconfig = strings.Split(stdOut.String(), "\n")[:len(strings.Split(stdOut.String(), "\n"))-1]
	// 通过ch返回运行状态为运行

	// 定义方法,更新状态至数据库

	return defconfig, nil
}

func GetDefConfigs(ctx iris.Context) {
	id, _ := dao.GetId(ctx)

	gitjob := &dgitjob.GitJob{}
	err := gitjob.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("get gitjob err ", err)
	}
	list, err := lsDefConfigJob(gitjob)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, list, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

type BaselineReq struct {
	Baseline string `json:"baseline"`
}

func InstallDevel(ctx iris.Context) {
	id, _ := dao.GetId(ctx)

	gitjob := &dgitjob.GitJob{}
	err := gitjob.Find(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, err.Error(), response.SystemErr.Msg))
		return
	}
	baselineReq := BaselineReq{}
	if err := ctx.ReadJSON(&baselineReq); err != nil {
		logging.ErrorLogger.Errorf("create gitjob read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if len(baselineReq.Baseline) == 0 {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}

	client, err := SSHClient(gitjob.ServerID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, err.Error(), response.SystemErr.Msg))
		return
	}

	defer client.Close()
	var stdOut bytes.Buffer

	command := fmt.Sprintf("cd %s && make download && make install-devel IMG=%s", gitjob.Dir, baselineReq.Baseline)
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("run command error", command, err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, err.Error(), response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func makeShowTargetJob(gitjob *dgitjob.GitJob, defconfig string) ([]string, error) {
	targets := []string{}

	client, err := SSHClient(gitjob.ServerID)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		return targets, err
	}
	defer client.Close()
	var stdOut bytes.Buffer
	if len(defconfig) > 0 {
		command := fmt.Sprintf("cd %s && make %s && make show-targets", gitjob.Dir, defconfig)
		if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
			logging.ErrorLogger.Errorf("make show targets err", err.Error())
			return targets, err
		}
	} else {
		command := fmt.Sprintf("cd %s && make show-targets", gitjob.Dir)
		if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
			logging.ErrorLogger.Errorf("make show targets err", err.Error())
			return targets, err
		}
	}

	tempSlice := strings.Split(stdOut.String(), "\n")[:len(strings.Split(stdOut.String(), "\n"))-1]
	// get All packages
	var start, end int
	for idx, item := range tempSlice {

		if strings.Compare(strings.Trim(item, " "), "All packages:") == 0 {
			start = idx
		}
		if strings.Compare(strings.Trim(item, " "), "Compiled packages:") == 0 {
			end = idx
		}
	}

	allPackages := tempSlice[start+1 : end]
	if len(allPackages) > 0 {
		targets = strings.Split(allPackages[0], " ")
	}

	// 通过ch返回运行状态为运行
	// 定义方法,更新状态至数据库

	return targets, nil
}

func GetMakeTarget(ctx iris.Context) {
	id, _ := dao.GetId(ctx)

	gitjob := &dgitjob.GitJob{}
	err := gitjob.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("get gitjob err ", err)
	}
	defconfig := ctx.FormValue("defconfig")

	list, err := makeShowTargetJob(gitjob, defconfig)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, list, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func lsProdcuts(gitjob *dgitjob.GitJob) ([]string, error) {
	products := []string{}

	client, err := SSHClient(gitjob.ServerID)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		return products, err
	}
	defer client.Close()
	f, err := os.OpenFile(filepath.Join(libs.Config.Buildfarm.Logpath, gitjob.JobID+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		return products, err
	}
	defer f.Close()
	command := fmt.Sprintf(`cd %s && ls |grep prj_`, gitjob.Dir)
	var stdOut bytes.Buffer
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("get project list", err.Error())
		return products, err
	}

	products = strings.Split(strings.Replace(stdOut.String(), "prj_", "", -1), "\n")[:len(strings.Split(stdOut.String(), "\n"))-1]
	// 通过ch返回运行状态为运行
	// 定义方法,更新状态至数据库

	return products, nil
}

func catFeedsConfigDefault(gitjob *dgitjob.GitJob, product string) ([]map[string]interface{}, error) {
	client, err := SSHClient(gitjob.ServerID)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		return nil, err
	}
	defer client.Close()
	var stdOut bytes.Buffer

	command := fmt.Sprintf("cat %s", filepath.Join(gitjob.Dir, "prj_"+product, "project", "feeds.conf.default"))
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("make show targets err", err.Error())
		return nil, err
	}
	lines := strings.Split(stdOut.String(), "\n")
	result := []map[string]interface{}{}
	index := 0
	for _, line := range lines {
		arr := strings.Fields(line)
		if len(arr) > 2 {
			index++
			result = append(result, map[string]interface{}{
				"id":          index,
				"type1":       arr[0],
				"type2":       arr[1],
				"unitpackage": arr[2],
				"repo":        strings.Split(arr[3], ";")[0],
				"branch":      strings.Split(arr[3], ";")[1],
			})
		}
	}

	return result, nil
}

func GetFeedsConfigDefault(ctx iris.Context) {
	id, _ := dao.GetId(ctx)

	gitjob := &dgitjob.GitJob{}
	err := gitjob.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("get gitjob err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	product := ctx.FormValue("product")
	if len(product) == 0 {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	list, err := catFeedsConfigDefault(gitjob, product)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, list, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetProducts(ctx iris.Context) {
	id, _ := dao.GetId(ctx)

	gitjob := &dgitjob.GitJob{}
	err := gitjob.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("get gitjob err ", err)
	}
	list, err := lsProdcuts(gitjob)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, list, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetGitjob(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	gitjob := &dgitjob.GitJob{}
	err := easygorm.GetEasyGormDb().Model(&buildfarm.GitJob{}).Where("id = ?", id).Find(&gitjob).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get job status err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, gitjob, response.NoErr.Msg))
	return
}

func StopAndDeleteJob(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	gitjob := dgitjob.GitJob{}
	err := gitjob.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	serverId := gitjob.ServerID
	dir := gitjob.Dir

	err = dmakejob.DeleMakeJobByJobId(gitjob.JobID)
	if err != nil {
		logging.ErrorLogger.Errorf("delete record err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if serverId != 0 {
		client, err := SSHClient(serverId)
		if err != nil {
			logging.ErrorLogger.Errorf("create user get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		defer client.Close()
		// ps -ef|grep 'bash -c cd 	/mnt/sata0/a9084b221004db27e1b8d1f4430e04eb'|grep make|awk '{print $2,$3}'
		command := fmt.Sprintf("ps -ef | grep -v grep | grep 'bash -c cd' | grep %s | awk '{print $2,$3}'", dir)

		var stdOut bytes.Buffer
		if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
			logging.ErrorLogger.Errorf("kill job err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		pids := stdOut.String()
		if pids != "" {
			command = fmt.Sprintf("kill -9 %s", pids)
			RunCommandOutBuffer(client, command, &stdOut)
		}
		command = fmt.Sprintf("ls %s", dir)
		if err := RunCommandOutBuffer(client, command, &stdOut); err == nil {
			logging.DebugLogger.Debugf("check dir exists", err)
			command = fmt.Sprintf("rm %s -rf", dir)
			if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
				logging.ErrorLogger.Errorf("delete dir err ", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
				return
			}
		}
	}

	err = gitjob.Delete(gitjob.ID)
	if err != nil {
		logging.ErrorLogger.Errorf("delete record err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func RestartJob(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	gitjob := dgitjob.GitJob{}
	err := gitjob.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	makejob, err := dmakejob.FindMakeJobByJobId(gitjob.JobID)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if makejob.Status != 0 && makejob.Status != 1 && makejob.Status != 4 && makejob.Status != 5 {
		go MakeProductjob(&gitjob, &makejob, true, true)
		ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "当前状态不允许重新编译！"))
	return
}
