package drelease

import (
	"fmt"
	"strings"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/release"
	"irisAdminApi/service/dao/release/dbuildname"
	"irisAdminApi/service/dao/release/dproductmodel"
	"irisAdminApi/service/dao/release/dreleaseattr"
)

const ModelName = "发布管理"

type ReleaseProject struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
}

type ReleaseRelease struct {
	ID                    uint                               `json:"id"`
	ReleaseProjectID      uint                               `json:"release_project_id"`
	BuildNameID           uint                               `json:"build_name_id"`
	ProductModelID        uint                               `json:"product_model_id"`
	ReleaseAttrID         uint                               `json:"release_attr_id"`
	ReleasedAt            string                             `json:"released_at"`
	SystemSoftwareVersion string                             `json:"system_software_version"`
	SystemSoftwareNumber  string                             `json:"system_software_number"`
	SystemPatchNumber     string                             `json:"system_patch_number"`
	PackageName           string                             `json:"package_name"`
	BuildFarmLink         string                             `json:"build_farm_link"`
	ReleaseLink           string                             `json:"release_link"`
	Comment               string                             `json:"comment"`
	UserID                uint                               `json:"user_id"`
	Uuid                  string                             `json:"uuid"`
	Project               *ReleaseProject                    `gorm:"->;foreignKey:ReleaseProjectID;references:ID;" json:"project"`
	ReleaseAttr           *dreleaseattr.ReleaseReleaseAttr   `gorm:"->;foreignKey:ReleaseAttrID;references:ID;" json:"release_attr"`
	BuildName             *dbuildname.ReleaseBuildName       `gorm:"->;foreignKey:BuildNameID;references:ID;" json:"build_name"`
	ProductModel          *dproductmodel.ReleaseProductModel `gorm:"->;foreignKey:ProductModelID;references:ID;" json:"product_model"`
	Archive               uint                               `json:"archive"`
	Publish               bool                               `json:"publish"`
	Manufacture           bool                               `json:"manufacture"`
}

type ListResponse struct {
	ReleaseRelease
}

type Request struct {
	ReleaseProjectID      uint   `json:"release_project_id"`
	BuildNameID           string `json:"build_name_id"`
	ProductModelID        string `json:"product_model_id"`
	ReleaseAttrID         uint   `json:"release_attr_id"`
	ReleasedAt            string `json:"released_at"`
	SystemSoftwareVersion string `json:"system_software_version"`
	SystemSoftwareNumber  string `json:"system_software_number"`
	SystemPatchNumber     string `json:"system_patch_number"`
	PackageName           string `json:"package_name"`
	BuildFarmLink         string `json:"build_farm_link"`
	ReleaseLink           string `json:"release_link"`
	Comment               string `json:"comment"`
}

type ManufactureRequest struct {
	Manufacture bool `json:"manufacture"`
}

func (a *ReleaseRelease) ModelName() string {
	return ModelName
}

func Model() *release.ReleaseRelease {
	return &release.ReleaseRelease{}
}

func (a *ReleaseRelease) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("ProductModel").Preload("Project").Preload("ReleaseAttr").Preload("BuildName")
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	// GetDetails(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *ReleaseRelease) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("ProductModel").Preload("Project").Preload("ReleaseAttr").Preload("BuildName")
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *ReleaseRelease) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *ReleaseRelease) CreateV2(object interface{}) error {
	return nil
}

func (a *ReleaseRelease) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *ReleaseRelease) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("ProductModel").Preload("Project").Preload("ReleaseAttr").Preload("BuildName").Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *ReleaseRelease) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("ProductModel").Preload("Project").Preload("ReleaseAttr").Preload("BuildName").Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *ReleaseRelease) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *ReleaseRelease) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("ProductModel").Preload("Project").Preload("ReleaseAttr").Preload("BuildName").Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	// GetDetail(u)
	return nil
}

func (u *ReleaseRelease) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("ProductModel").Preload("Project").Preload("ReleaseAttr").Preload("BuildName").Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	// GetDetail(u)
	return nil
}

func FindReleaseInIds(ids []uint) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("ProductModel").Preload("Project").Preload("ReleaseAttr").Preload("BuildName").Where("id in ?", ids).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return items, err
	}
	// GetDetails(items)
	return items, nil
}

// func GetDetail(release *ReleaseRelease) {
// 	project := dproject.Response{}
// 	if err := project.Find(release.ReleaseProjectID); err != nil {
// 		logging.ErrorLogger.Errorf("get status get err ", err)
// 	}
// 	release.Project = &project

// 	attr := dreleaseattr.Response{}
// 	if err := attr.Find(release.ReleaseAttrID); err != nil {
// 		logging.ErrorLogger.Errorf("get status get err ", err)
// 	}
// 	release.ReleaseAttr = &attr

// 	buildname := dbuildname.Response{}
// 	if err := buildname.Find(release.BuildNameID); err != nil {
// 		logging.ErrorLogger.Errorf("get status get err ", err)
// 	}
// 	release.BuildName = &buildname

// 	productmodel := dproductmodel.ReleaseProductModel{}
// 	if err := productmodel.Find(release.ProductModelID); err != nil {
// 		logging.ErrorLogger.Errorf("get status get err ", err)
// 	}
// 	release.ProductModel = &productmodel

// }

// func GetDetails(items []*ListResponse) {
// 	projects, _ := dproject.FindAll()
// 	var projectMap = make(map[uint]*dproject.Response)
// 	for _, project := range projects {
// 		projectMap[project.ID] = project
// 	}

// 	attrs, _ := dreleaseattr.FindAll()
// 	var attrMap = make(map[uint]*dreleaseattr.Response)
// 	for _, attr := range attrs {
// 		attrMap[attr.ID] = attr
// 	}

// 	buildnames, _ := dbuildname.FindAll()
// 	var buildnameMap = make(map[uint]*dbuildname.Response)
// 	for _, buildname := range buildnames {
// 		buildnameMap[buildname.ID] = buildname
// 	}

// 	productmodels, _ := dproductmodel.FindAll()
// 	var productmodelMap = make(map[uint]*dproductmodel.ReleaseProductModel)
// 	for _, productmodel := range productmodels {
// 		productmodelMap[productmodel.ID] = productmodel
// 	}

// 	for _, item := range items {
// 		item.Project = projectMap[item.ReleaseProjectID]
// 		item.ReleaseAttr = attrMap[item.ReleaseAttrID]
// 		item.BuildName = buildnameMap[item.BuildNameID]
// 		item.ProductModel = productmodelMap[item.ProductModelID]
// 	}

// }

// func GetResponseDetails(items []*ReleaseRelease) {
// 	projects, _ := dproject.FindAll()
// 	var projectMap = make(map[uint]*dproject.Response)
// 	for _, project := range projects {
// 		projectMap[project.ID] = project
// 	}

// 	attrs, _ := dreleaseattr.FindAll()
// 	var attrMap = make(map[uint]*dreleaseattr.Response)
// 	for _, attr := range attrs {
// 		attrMap[attr.ID] = attr
// 	}

// 	buildnames, _ := dbuildname.FindAll()
// 	var buildnameMap = make(map[uint]*dbuildname.Response)
// 	for _, buildname := range buildnames {
// 		buildnameMap[buildname.ID] = buildname
// 	}

// 	productmodels, _ := dproductmodel.FindAll()
// 	var productmodelMap = make(map[uint]*dproductmodel.ReleaseProductModel)
// 	for _, productmodel := range productmodels {
// 		productmodelMap[productmodel.ID] = productmodel
// 	}

// 	for _, item := range items {
// 		item.Project = projectMap[item.ReleaseProjectID]
// 		item.ReleaseAttr = attrMap[item.ReleaseAttrID]
// 		item.BuildName = buildnameMap[item.BuildNameID]
// 		item.ProductModel = productmodelMap[item.ProductModelID]
// 	}

// }

func AllArchiveReleases(archiveType, name, sort, orderBy, softversion, softnumber, start, end string, page, pageSize, releaseattrID, projectID, buildNameID, productModelID int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("ProductModel").Preload("Project").Preload("ReleaseAttr").Preload("BuildName")
	if archiveType == "archive" {
		db = db.Where("archive = 1")
	}
	if len(name) > 0 {
		db = db.Where("name like ?", fmt.Sprintf("%%%s%%", name))
	}
	if len(softversion) > 0 {
		db = db.Where("system_software_version like ?", fmt.Sprintf("%%%s%%", softversion))
	}
	if len(softnumber) > 0 {
		db = db.Where("system_software_number like ?", fmt.Sprintf("%%%s%%", softnumber))
	}
	if len(start) > 0 {
		db = db.Where("created_at >= ?", start+" 00:00:00.000")
	}
	if len(end) > 0 {
		db = db.Where("created_at <= ?", end+" 23:59:59.999")
	}
	if projectID > 0 {
		db = db.Where("release_project_id = ?", projectID)
	}
	if buildNameID > 0 {
		db = db.Where("build_name_id = ?", buildNameID)
	}
	if productModelID > 0 {
		db = db.Where("product_model_id = ?", productModelID)
	}
	if releaseattrID > 0 {
		db = db.Where("release_attr_id = ?", releaseattrID)
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	// GetDetails(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func AllReleases(name, sort, orderBy, softversion, softnumber, start, end string, page, pageSize, releaseattrID, projectID, buildNameID, productModelID int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("ProductModel").Preload("Project").Preload("ReleaseAttr").Preload("BuildName")
	if len(name) > 0 {
		db = db.Where("name like ?", fmt.Sprintf("%%%s%%", name))
	}
	if len(softversion) > 0 {
		db = db.Where("system_software_version like ?", fmt.Sprintf("%%%s%%", softversion))
	}
	if len(softnumber) > 0 {
		db = db.Where("system_software_number like ?", fmt.Sprintf("%%%s%%", softnumber))
	}
	if len(start) > 0 {
		db = db.Where("created_at >= ?", start+" 00:00:00.000")
	}
	if len(end) > 0 {
		db = db.Where("created_at <= ?", end+" 23:59:59.999")
	}
	if projectID > 0 {
		db = db.Where("release_project_id = ?", projectID)
	}
	if buildNameID > 0 {
		db = db.Where("build_name_id = ?", buildNameID)
	}
	if productModelID > 0 {
		db = db.Where("product_model_id = ?", productModelID)
	}
	if releaseattrID > 0 {
		db = db.Where("release_attr_id = ?", releaseattrID)
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	// GetDetails(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func UpdateSoftVersionReleases(softVersion string, object map[string]interface{}) error {

	err := easygorm.GetEasyGormDb().Model(Model()).Where("system_software_version = ?  and archive=1", softVersion).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update SoftVersion Releases err ", err)
		return err
	}
	return nil
}

func All(releaseAttrID, product, software_version, name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("ProductModel").Preload("Project").Preload("ReleaseAttr").Preload("BuildName")
	if len(releaseAttrID) > 0 {
		db = db.Where("release_attr_id = ?", releaseAttrID)
	}

	if len(product) > 0 {
		db = db.Where("product = ?", product)
	}

	if len(software_version) > 0 {
		db = db.Where("system_software_version like ?", fmt.Sprintf("%%%s%%", software_version))
	}

	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	// GetDetails(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func FindAllReleasesByStatus(status uint) ([]*ReleaseRelease, error) {
	var items []*ReleaseRelease
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("ProductModel").Preload("Project").Preload("ReleaseAttr").Preload("BuildName").Where("archive = ?", status).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find release by item err ", err)
		return items, err
	}
	// GetResponseDetails(items)
	return items, nil
}

// GetArchivedReleasesByProduct 获取已归档的发布信息，并根据产品名称进行匹配
func GetArchivedReleasesByProduct(softversion, softnumber, product string) (*ReleaseRelease, error) {
	var allItems []*ReleaseRelease

	// 首先查询所有已归档的记录
	db := easygorm.GetEasyGormDb().Model(Model()).
		Preload("ProductModel").
		Preload("Project").
		Preload("ReleaseAttr").
		Preload("BuildName").Where("archive = 1")

	// 添加可选的软件版本和版本号过滤条件
	if len(softversion) > 0 {
		db = db.Where("system_software_version like ?", fmt.Sprintf("%%%s%%", softversion))
	}
	if len(softnumber) > 0 {
		db = db.Where("system_software_number like ?", fmt.Sprintf("%%%s%%", softnumber))
	}

	//获取最新记录
	db = db.Order("id DESC")

	// 查询所有可能匹配的记录
	err := db.Find(&allItems).Error
	if err != nil {
		logging.ErrorLogger.Errorf("获取列表数据出错: %v", err)
		return nil, err
	}

	// 如果没有查询到记录，直接返回nil
	if len(allItems) == 0 {
		return nil, nil
	}

	// 如果提供了产品名称，在内存中进行过滤
	if len(product) > 0 {
		productLower := strings.ToLower(product)
		for _, item := range allItems {
			// 检查BuildName.Name是否与输入参数product匹配
			buildNameMatch := item.BuildName != nil && strings.Contains(strings.ToLower(item.BuildName.Name), productLower)

			// 检查ProductModel.Name的后缀是否与输入参数product匹配
			var productModelMatch bool
			if item.ProductModel != nil {
				productModelName := strings.ToLower(item.ProductModel.Name)
				// 检查是否以product结尾（后缀匹配）
				productModelMatch = strings.HasSuffix(productModelName, productLower)
			}

			// 如果任一条件匹配，返回该记录
			if buildNameMatch || productModelMatch {
				return item, nil // 返回第一条匹配的记录（最新的）
			}
		}
		// 没有找到匹配的记录
		return nil, nil
	} else {
		return allItems[0], nil
	}
}

