package main

import (
	"bufio"
	"flag"
	"fmt"
	"io"
	"math/rand"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/models"
	"irisAdminApi/application/models/bugapproval"
	"irisAdminApi/application/models/bugsync"
	"irisAdminApi/application/models/buildfarm"
	"irisAdminApi/application/models/codesync"
	"irisAdminApi/application/models/coredump"
	"irisAdminApi/application/models/coverityresult"
	"irisAdminApi/application/models/datasync"
	"irisAdminApi/application/models/featurerelease"
	"irisAdminApi/application/models/featurerelease_11_x"
	"irisAdminApi/application/models/feishu"
	"irisAdminApi/application/models/fileout"
	"irisAdminApi/application/models/gitlab"
	"irisAdminApi/application/models/inspection"
	"irisAdminApi/application/models/kpi"
	"irisAdminApi/application/models/license"
	"irisAdminApi/application/models/mergerequest"
	"irisAdminApi/application/models/opensource"
	"irisAdminApi/application/models/performance"
	"irisAdminApi/application/models/release"
	"irisAdminApi/application/models/resourcepool"
	"irisAdminApi/application/models/sig"
	"irisAdminApi/application/models/urlpack"
	"irisAdminApi/application/models/user"
	"irisAdminApi/service/dao/user/dgroup"
	"irisAdminApi/service/dao/user/drole"
	"irisAdminApi/service/dao/user/duser"

	"github.com/jinzhu/configor"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"

	"github.com/azumads/faker"
)

var Fake *faker.Faker

var Seeds = struct {
	Perms []struct {
		Name        string `json:"name"`
		DisplayName string `json:"displayname"`
		Description string `json:"description"`
		Act         string `json:"act"`
	}
}{}

var tables = []interface{}{
	&bugsync.BugMirrorRecord{},
	&user.User{},
	&user.Role{},
	// &user.UserToken{},
	&user.Permission{},
	&models.Config{},
	&models.Oplog{},
	&fileout.Approval{},
	&user.Department{},
	&fileout.FileDetail{},
	&user.Group{},
	&user.GroupDepartment{},
	&user.UserGroup{},
	&user.UserDepartment{},
	&fileout.AllowAuditIp{},
	&fileout.AutoAuditFile{},
	&fileout.UserWhiteList{},
	&fileout.AutoAuditMd5{},
	&fileout.ApprovalComment{},
	&fileout.ApprovalShare{},
	&fileout.Fragment{},
	&fileout.FileStruction{},
	&buildfarm.Project{},
	&buildfarm.DefConfig{},
	&buildfarm.GitJob{},
	&buildfarm.Server{},
	&buildfarm.MakeJob{},
	&buildfarm.CronMakeJob{},
	&buildfarm.CronTab{},
	&buildfarm.Baseline{},
	&buildfarm.PatchJob{},
	&buildfarm.BuildfarmProductCpu{},
	&buildfarm.SoftVersions{},
	&buildfarm.BuildfarmProjectInfo{},
	&buildfarm.BuildfarmJob{},
	&buildfarm.BuildfarmAudit{},
	&buildfarm.BuildfarmAuditDetail{},
	&buildfarm.BuildfarmAuditHistory{},
	&buildfarm.BuildfarmWarning{},
	&buildfarm.BuildfarmAuditBlackList{},
	&buildfarm.BuildfarmDiffLevelRule{},
	&gitlab.GitlabToken{},
	&gitlab.UserRepositoryFavorite{},
	&release.ReleaseProject{},
	&release.ReleaseStatus{},
	&release.ReleaseType{},
	&release.ReleaseAttr{},
	&release.ReleaseClass{},
	&release.ReleaseDirection{},
	// &releasemodels.ReleaseCustomer{},
	&release.ReleaseDemandSrc{},
	// &releasemodels.ReleaseProjectCustomer{},
	&release.ReleaseProjectDemandSrc{},
	&release.ReleaseProjectNotice{},
	&release.ReleaseProjectWrite{},
	&release.ReleaseProjectConfig{},
	&release.ReleaseTypeAttr{},
	&release.ReleaseAttachment{},
	&release.ReleaseBranchNotice{},
	&release.ReleaseBranchWrite{},
	&release.ReleaseBranchAuditor{},
	&release.ReleaseBranch{},
	&release.ReleaseBranchAudit{},
	&release.ReleaseRelease{},
	&release.ReleaseBuildName{},
	&release.ReleaseProductModel{},
	&release.ReleaseReleaseAttr{},
	&release.ReleaseBranchInfo{},
	&release.ReleaseCompileInfo{},
	&release.ReleaseBuildTime{},
	&release.ReleaseBranchSwitch{},
	&release.ReleaseTrunkLog{},
	&featurerelease.Feature{},
	&featurerelease.FeatureProcDef{},
	&featurerelease.FeatureProcInst{},
	&featurerelease.FeatureProcTask{},
	&featurerelease.SecCloud{},
	&featurerelease_11_x.Feature_11_X{},
	&featurerelease_11_x.FeatureProcDef_11_X{},
	&featurerelease_11_x.FeatureProcInst_11_X{},
	&featurerelease_11_x.FeatureProcTask_11_X{},
	&kpi.Contribution{},
	&kpi.ContributionType{},
	&kpi.ContributionPoint{},
	&kpi.ContributionReview{},
	&kpi.ContributionVersion{},
	&kpi.ProblemSource{},
	&kpi.Problem{},
	&kpi.ProblemProccess{},
	&kpi.ProblemAttachment{},
	&kpi.UserPoint{},
	&kpi.KpiSummaryDepartment{},
	&datasync.PmsSoftwareProject{},
	&datasync.CommonNtosFileWorkPackage{},
	&datasync.BugProject{},
	&datasync.BugSyncRecord{},
	&datasync.BugPackagePath{},
	&datasync.BugBranchInfo{},
	&datasync.Bug{},
	&datasync.BugLog{},
	&datasync.BugMirrorApproval{},
	&datasync.BugMirrorHistory{},
	&datasync.BugMirrorApprovalHistory{},
	&datasync.Resource{},
	&datasync.CsbuResResourceApply{},
	&datasync.CsbuResResourceSplit{},
	&datasync.ResourceClean{},
	&datasync.PmsCaseInfo{},
	&datasync.PmsProjectTestStage{},
	&datasync.PmsRequest{},
	&datasync.PmsRequestCase{},
	&datasync.PmsWorkpacketInfo{},
	&datasync.PmsProjectDocument{},
	&datasync.PmsDocumentRequestRelation{},
	&datasync.PmsProjectMember{},
	&datasync.PmsDoc{},
	&datasync.CommonNtosFile{},
	&datasync.Gcov{},
	&datasync.CommonComponent{},
	&datasync.CommonComponentUser{},
	&datasync.CommonNtosFileComponent{},
	&datasync.DfxMemoryInfo{},
	&datasync.PmsBuMember{},
	&datasync.PmsProjectRole{},
	&datasync.PmsHardwareProjectMilestone{},
	&codesync.CodeSyncPolicy{},
	&codesync.CodeSyncHistory{},
	&codesync.CodeSyncQueue{},
	&codesync.CodeSyncProject{},
	&opensource.OpenSourceComponent{},
	&opensource.OpenSourceComponentPermission{},
	&opensource.OpenSourceVulnerability{},
	&opensource.OpenSourceVulnerabilityHistory{},
	&coredump.CoredumpTechSupport{},
	&coredump.CoredumpJob{},
	&resourcepool.ResourcePoolMonitor{},
	&resourcepool.ResourcePoolInterface{},
	&resourcepool.ResourcePoolResource{},
	&resourcepool.ResourcePoolJob{},
	&urlpack.UrlPackCategory{},
	&urlpack.UrlPackJob{},
	&urlpack.UrlPackDB{},
	&urlpack.UrlPackConfig{},
	&opensource.OpenSourceCNVDVulnerability{},
	&opensource.OpenSourceVulnerabilityPermission{},
	&opensource.OpenSourceCNVDShareXMLFile{},
	&opensource.OpenSourceComponentTempCheck{},
	&mergerequest.MergeRequest{},
	&mergerequest.MergeRequestHistory{},
	&mergerequest.MergeRequestReviewer{},
	&mergerequest.MergeRequestAssignee{},
	&mergerequest.MergeRequestWorkPackage{},
	&mergerequest.MergeRequestPhabricator{},
	&mergerequest.MergeRequestNotice{},
	&mergerequest.MergeRequestCodeCheck{},
	&mergerequest.MergeRequestDependencies{},
	&mergerequest.MergeRequestAiCheck{},
	&mergerequest.MergeRequestAiCheckHistory{},
	&license.LicenseMaterial{},
	&license.LicenseDeviceModel{},
	&license.LicenseAuthTask{},
	&bugapproval.BugApproval{},
	&bugapproval.BugApprovalCC{},
	&bugapproval.BugApprovalAuditor{},
	&performance.Performance{},
	&performance.Performance2544{},
	&sig.SigJob{},
	&sig.SigTechSupportDecryptJob{},
	&inspection.InspectionJob{},
	&inspection.InspectionLog{},
	&mergerequest.MergeRequestDiscussionCps{},
	&mergerequest.MergeRequestDiscussionCpsHistory{},
	&coverityresult.CoverityResult{},
	&coverityresult.CoverityRule{},
	&coverityresult.CoverityReportHistory{},
	&buildfarm.CronCoverityWindow{},
	&buildfarm.CronCoveritySchedule{},
	&buildfarm.CpldBuildJob{},
	&feishu.PmsMilestoneData{},
	&feishu.PmsProjectListData{},
	&feishu.PmsReviewData{},
}

func init() {
	Fake, _ = faker.New("en")
	Fake.Rand = rand.New(rand.NewSource(42))
	rand.Seed(time.Now().UnixNano())
}

var (
	config = flag.String("config", "", "配置路径")
	path   = flag.String("path", "", "数据路径")
)

func main() {
	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "usage: %s [options] [command]\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Commands:\n")
		fmt.Fprintf(os.Stderr, "\n")
		fmt.Fprintf(os.Stderr, "  --config <path>\n")
		fmt.Fprintf(os.Stderr, "    设置配置文件路径\n")
		fmt.Fprintf(os.Stderr, "  --path <path>\n")
		fmt.Fprintf(os.Stderr, "    设置填充数据路径\n")
		fmt.Fprintf(os.Stderr, "\n")
	}
	flag.Parse()

	err := libs.InitConfig(*config)
	if err != nil {
		panic(fmt.Sprintf("系统配置初始化失败: %+v\n", err))
	}

	err = easygorm.Init(&easygorm.Config{
		Adapter: libs.Config.DB.Adapter,
		Conn:    libs.GetDBConn(),
		GormConfig: &gorm.Config{
			NamingStrategy: schema.NamingStrategy{
				TablePrefix: libs.Config.DB.Prefix,
			},
			DisableForeignKeyConstraintWhenMigrating: true,
		},
		Casbin: &easygorm.Casbin{
			Path:   libs.Config.Casbin.Path,
			Prefix: libs.Config.Casbin.Prefix,
		},
		Models: tables,
	})
	if err != nil {
		panic(fmt.Sprintf("数据库初始化失败: %+v\n", err))
	}

	Seed()
	InitDb()
}

func InitDb() {
	// createDepartmentFromCsv()
	// createGroupFromCsv()
	// createUserFromCsv()
	// createLicenseDeviceModelsFromCsv()
	// createLicenseMaterialsFromCsv()
}

func createLicenseDeviceModelsFromCsv() {
	fp := filepath.Join(*path, "设备型号.csv")
	if _, err := os.Stat(fp); os.IsNotExist(err) {
		fmt.Println(err.Error())
		return
	}
	fd, err := os.Open(fp)
	defer fd.Close()
	if err != nil {
		fmt.Println(err.Error())
		return
	}
	buff := bufio.NewReader(fd)
	for {
		line, _, eof := buff.ReadLine()
		if eof == io.EOF {
			break
		}
		slice := strings.Split(string(line), ",")
		deviceModel := &license.LicenseDeviceModel{
			DeviceModel:        slice[0],
			DeviceMaterialCode: slice[1],
			ModelBase:          models.ModelBase{CreatedAt: time.Now()},
		}
		easygorm.GetEasyGormDb().Create(deviceModel)
	}
}

func createLicenseMaterialsFromCsv() {
	fp := filepath.Join(*path, "授权物料.csv")
	if _, err := os.Stat(fp); os.IsNotExist(err) {
		fmt.Println(err.Error())
		return
	}
	fd, err := os.Open(fp)
	defer fd.Close()
	if err != nil {
		fmt.Println(err.Error())
		return
	}
	buff := bufio.NewReader(fd)
	for {
		line, _, eof := buff.ReadLine()
		if eof == io.EOF {
			break
		}
		slice := strings.Split(string(line), ",")
		material := &license.LicenseMaterial{
			DeviceModel:  slice[0],
			MaterialCode: slice[1],
			MaterialName: slice[2],
			ModelBase:    models.ModelBase{CreatedAt: time.Now()},
		}
		authType, _ := strconv.Atoi(slice[3])
		material.AuthType = uint(authType)
		easygorm.GetEasyGormDb().Create(material)
	}
}

func createDepartmentFromCsv() {
	fp := filepath.Join(*path, "部门.csv")

	if _, err := os.Stat(fp); os.IsNotExist(err) {
		fmt.Println(err.Error())
		return
	}
	fd, err := os.Open(fp)
	defer fd.Close()
	if err != nil {
		fmt.Println(err.Error())
		return
	}
	buff := bufio.NewReader(fd)
	for {
		line, _, eof := buff.ReadLine()
		if eof == io.EOF {
			break
		}
		slice := strings.Split(string(line), ",")
		department := &user.Department{
			Name:      slice[1],
			ModelBase: models.ModelBase{CreatedAt: time.Now()},
		}
		easygorm.GetEasyGormDb().Create(department)
	}
}

func createGroupFromCsv() {
	fp := filepath.Join(*path, "组.csv")

	if _, err := os.Stat(fp); os.IsNotExist(err) {
		fmt.Println(err.Error())
		return
	}
	fd, err := os.Open(fp)
	defer fd.Close()
	if err != nil {
		fmt.Println(err.Error())
		return
	}
	buff := bufio.NewReader(fd)
	for {
		line, _, eof := buff.ReadLine()
		if eof == io.EOF {
			break
		}
		slice := strings.Split(string(line), ",")
		group := &user.Group{
			Name:        slice[1],
			Description: slice[2],
			ModelBase:   models.ModelBase{CreatedAt: time.Now()},
		}
		easygorm.GetEasyGormDb().Create(group)
		dgroup.UpdateDepartment(group.ID, slice[4])
	}
}

func createUserFromCsv() {
	fp := filepath.Join(*path, "用户.csv")

	if _, err := os.Stat(fp); os.IsNotExist(err) {
		fmt.Println(err.Error())
		return
	}
	fd, err := os.Open(fp)
	defer fd.Close()
	if err != nil {
		fmt.Println(err.Error())
		return
	}
	buff := bufio.NewReader(fd)
	for {
		line, _, eof := buff.ReadLine()
		if eof == io.EOF {
			break
		}
		slice := strings.Split(string(line), ",")
		user := &user.User{
			Username:  slice[1],
			Name:      slice[3],
			Password:  libs.HashPassword(slice[2]),
			Avatar:    "user_avatar.jpg",
			Intro:     "",
			ModelBase: models.ModelBase{CreatedAt: time.Now()},
		}
		easygorm.GetEasyGormDb().Create(user)
		duser.UpdateGroup(user.ID, slice[5])
	}
}

func Seed() {
	AutoMigrates()
	CreateAllPerms()
	for _, roleName := range []string{"超级管理员", "默认审核员", "组长", "组员", "审计员", "user"} {
		perms := CreatePerms(roleName)
		CreateOtherRole(roleName, perms)
	}
	CreateAdmin()
	CreateUser()
	easygorm.GetEasyGormEnforcer().LoadPolicy()
	// perms := CreatePerms("admin")
	// CreateRole(perms)
	// CreateOtherRole(perms)
	// CreateAdmin()
	// CreateUser()
	// CreateConfigs()
}

// CreateConfigs 新建权限
//func CreateConfigs() {
//	configs := []*models.Config{
//		{
//			Name:  "imageHost",
//			Value: "https://www.snowlyg.com",
//		},
//		{
//			Name:  "beianhao",
//			Value: "",
//		},
//	}
//	logging.DebugLogger.Debugf("系统设置填充：%+v\n", configs)
//		perm, err := models.GetConfig(s)
//		if err != nil && models.IsNotFound(err) {
//			if perm.ID == 0 {
//				perm = &models.Config{
//					Model: models.Model{CreatedAt: time.Now()},
//					Name:  m.Name,
//					Value: m.Value,
//				}
//				if err := perm.CreateConfig(); err != nil {
//					logging.ErrorLogger.Errorf("seeder data create config err：%+v\n", err)
//					return
//				}
//			}
//		}
//	}
//}

func CreateAllPerms() [][]string {
	Seeds := struct {
		Perms []struct {
			Name        string `json:"name"`
			DisplayName string `json:"displayname"`
			Description string `json:"description"`
			Act         string `json:"act"`
		}
	}{}

	fpaths, err := filepath.Glob(filepath.Join(*path, "超级管理员_perms.yml"))
	if err != nil {
		panic(fmt.Sprintf("数据填充YML文件路径加载失败: %+v\n", err))
	}

	fmt.Printf("数据填充YML文件路径：%+v\n", fpaths)

	if err := configor.Load(&Seeds, fpaths...); err != nil {
		panic(fmt.Sprintf("load config file err：%+v", err))
	}
	var insertPerms []user.Permission
	for _, m := range Seeds.Perms {
		perm := user.Permission{
			Name:        m.Name,
			DisplayName: m.DisplayName,
			Description: m.Description,
			Act:         m.Act,
		}
		insertPerms = append(insertPerms, perm)
	}

	if len(insertPerms) == 0 {
		return nil
	}

	create := easygorm.GetEasyGormDb().Create(&insertPerms)
	if err := create.Error; err != nil {
		fmt.Println(fmt.Sprintf("seeder data create perms err：%+v\n", err))
	}

	fmt.Println(fmt.Sprintf("\n填充权限数据："))
	for _, insertPerm := range insertPerms {
		fmt.Println(fmt.Sprintf("  %s:%s", insertPerm.Name, insertPerm.Act))
	}

	var perms [][]string
	for _, perm := range insertPerms {
		perms = append(perms, []string{
			perm.Name,
			perm.Act,
		})
	}
	return perms
}

// CreatePerms 新建权限
func CreatePerms(role string) [][]string {
	Seeds := struct {
		Perms []struct {
			Name        string `json:"name"`
			DisplayName string `json:"displayname"`
			Description string `json:"description"`
			Act         string `json:"act"`
		}
	}{}

	fpaths, err := filepath.Glob(filepath.Join(*path, role+"_perms.yml"))
	if err != nil {
		panic(fmt.Sprintf("数据填充YML文件路径加载失败: %+v\n", err))
	}

	fmt.Printf("数据填充YML文件路径：%+v\n", fpaths)

	if err := configor.Load(&Seeds, fpaths...); err != nil {
		panic(fmt.Sprintf("load config file err：%+v", err))
	}

	var insertPerms []user.Permission
	for _, m := range Seeds.Perms {
		perm := user.Permission{
			Name:        m.Name,
			DisplayName: m.DisplayName,
			Description: m.Description,
			Act:         m.Act,
		}
		insertPerms = append(insertPerms, perm)
	}

	if len(insertPerms) == 0 {
		return nil
	}

	fmt.Println(fmt.Sprintf("\n填充权限数据："))
	for _, insertPerm := range insertPerms {
		fmt.Println(fmt.Sprintf("  %s:%s", insertPerm.Name, insertPerm.Act))
	}

	var perms [][]string
	for _, perm := range insertPerms {
		perms = append(perms, []string{
			perm.Name,
			perm.Act,
		})
	}

	return perms
}

// CreateRole 新建管理角色
func CreateRole(perms [][]string) {
	role := &user.Role{
		Name:        libs.Config.Admin.Rolename,
		DisplayName: libs.Config.Admin.Rolename,
		Description: libs.Config.Admin.Rolename,
		ModelBase:   models.ModelBase{CreatedAt: time.Now()},
		Perms:       perms,
	}
	if err := easygorm.GetEasyGormDb().Create(&role).Error; err != nil {
		fmt.Println(fmt.Sprintf("seeder data create role err：%+v\n", err))
	}

	err := drole.AddPermForRole(role)
	if err != nil {
		fmt.Println(fmt.Sprintf("添加角色失败：%+v", err))
	}

	fmt.Println(fmt.Sprintf("填充角色：%+v", role.Name))
	fmt.Println(fmt.Sprintf("\n填充角色权限："))
	for _, perm := range role.Perms {
		fmt.Println(fmt.Sprintf("  %+v", perm))
	}
}

func CreateOtherRole(roleName string, perms [][]string) {
	role := &user.Role{
		Name:        roleName,
		DisplayName: roleName,
		Description: roleName,
		ModelBase:   models.ModelBase{CreatedAt: time.Now()},
		Perms:       perms,
	}
	if err := easygorm.GetEasyGormDb().Find(&role, "name =?", roleName).Error; err != nil {
		fmt.Println(fmt.Sprintf("seeder data get role err：%+v\n", err))
	}
	if role.ID == 0 {
		if err := easygorm.GetEasyGormDb().Create(&role).Error; err != nil {
			fmt.Println(fmt.Sprintf("seeder data create role err：%+v\n", err))
		}
	}

	err := drole.AddPermForRole(role)
	if err != nil {
		fmt.Println(fmt.Sprintf("添加角色失败：%+v", err))
	}

	fmt.Println(fmt.Sprintf("填充角色：%+v", role.Name))
	fmt.Println(fmt.Sprintf("\n填充角色权限："))
	for _, perm := range role.Perms {
		fmt.Println(fmt.Sprintf("  %+v", perm))
	}
}

type Role struct {
	Id   uint
	Name string
}

// CreateAdmin 新建管理员
func CreateAdmin() {
	var roleIds []uint
	var roleNames []string
	var roles []*Role
	easygorm.GetEasyGormDb().Model(&user.Role{}).Find(&roles)
	for _, role := range roles {
		roleIds = append(roleIds, role.Id)
		roleNames = append(roleNames, role.Name)
	}
	fmt.Println(roleIds)
	admin := &user.User{
		Username:  libs.Config.Admin.Username,
		Name:      libs.Config.Admin.Name,
		Password:  libs.HashPassword(libs.Config.Admin.Password),
		Avatar:    "user_avatar.jpg",
		Intro:     "admin",
		ModelBase: models.ModelBase{CreatedAt: time.Now()},
		// RoleIds:  roleIds,
		RoleIds: roleIds,
	}

	easygorm.GetEasyGormDb().Create(admin)

	err := duser.AddRoleForUser(admin)
	if err != nil {
		fmt.Println(fmt.Sprintf("添加管理员失败：%+v", err))
	}

	fmt.Println(fmt.Sprintf("管理员密码：%s", libs.Config.Admin.Password))
	fmt.Println(fmt.Sprintf("管理员角色：%+v", roleNames))
}

// CreateAdmin 新建普通用户
func CreateUser() {
	var roleIds []uint
	var roleNames []string
	var roles []*Role
	easygorm.GetEasyGormDb().Model(&user.Role{}).Find(&roles)
	for _, role := range roles {
		if role.Name == "user" {
			roleIds = append(roleIds, role.Id)
			roleNames = append(roleNames, role.Name)
		}
	}
	newuser := &user.User{
		Username:  "user",
		Name:      "user",
		Password:  libs.HashPassword("password"),
		Avatar:    "user_avatar.jpg",
		Intro:     "user",
		ModelBase: models.ModelBase{CreatedAt: time.Now()},
		// RoleIds:  roleIds,
		RoleIds: roleIds,
	}

	easygorm.GetEasyGormDb().Create(newuser)

	err := duser.AddRoleForUser(newuser)
	if err != nil {
		fmt.Println(fmt.Sprintf("添加普通失败：%+v", err))
	}

	fmt.Println(fmt.Sprintf("普通用户密码：%s", libs.Config.Admin.Password))
	fmt.Println(fmt.Sprintf("普通用户角色：%+v", roleNames))
}

// AutoMigrates 重置数据表
// easygorm.Egm.Db.DropTableIfExists 删除存在数据表
func AutoMigrates() {
	if err := DropTables(); err != nil {
		fmt.Println(fmt.Sprintf("seeder data  auto migrate  err：%+v\n", err))
		return
	}
	if err := easygorm.Migrate(tables); err != nil {
		fmt.Println(fmt.Sprintf("seeder data  auto migrate  err：%+v\n", err))
		return
	}
}

// DropTables 删除数据表
func DropTables() error {
	prefix := libs.Config.DB.Prefix
	err := easygorm.GetEasyGormDb().Migrator().DropTable(
		// prefix+"users",
		// prefix+"roles",
		prefix + "permissions",
		// prefix+"configs",
		// prefix+"file_details",
		// prefix+"approvals",
		// prefix+"groups",
		// prefix+"departments",
		// prefix+"user_groups",
		// prefix+"group_departments",
		// prefix+"allow_audit_ips",
		// prefix+"auto_audit_files",
		// prefix+"approval_comments",
		// prefix+"approval_shares",
		// prefix+"git_jobs",
		// prefix+"def_configs",
		// prefix+"gitlab_tokens",
		// prefix+"open_source_components",
		// prefix+"open_source_component_permissions",
		// prefix+"open_source_vulnerabilities",
		// prefix+"open_source_vulnerability_permissions",
		// prefix+"open_source_vulnerability_histories",
		// prefix+"open_source_cnvd_vulnerabilities",
		// prefix+"open_source_cnvd_share_xml_files",
		// prefix+"open_source_component_temp_checks",
		// prefix+"license_materials",
		// prefix+"license_device_models",
		// prefix+"license_auth_tasks",
	)
	if err != nil {
		return err
	}
	return nil
}
