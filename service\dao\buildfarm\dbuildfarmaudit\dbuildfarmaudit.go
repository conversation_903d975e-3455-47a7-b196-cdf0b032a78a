package dbuildfarmaudit

import (
	"fmt"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/buildfarm"
	"irisAdminApi/service/dao/user/ddepartment"
	"irisAdminApi/service/dao/user/duserdepartment"

	"github.com/pkg/errors"
	"gorm.io/gorm"
)

const ModelName = "编译农场个人仓编译评审任务表"

type User struct {
	ID       uint   `json:"id"`
	Name     string `json:"name"`
	Username string `json:"username"`
}

type BuildfarmAudit struct {
	buildfarm.BuildfarmAudit
	Auditor     *User `gorm:"->;foreignKey:AuditorID;references:ID" json:"auditor"`
	User        *User `gorm:"->;foreignKey:UserID;references:ID" json:"user"`
	LastAuditor *User `gorm:"->;foreignKey:LastAuditorID;references:ID" json:"last_auditor"`
}

type BuildfarmAuditDetail struct {
	buildfarm.BuildfarmAuditDetail
	Auditor *User `gorm:"->;foreignKey:AuditorID;references:ID" json:"auditor"`
	User    *User `gorm:"->;foreignKey:UserID;references:ID" json:"user"`
}

type ListResponse struct {
	BuildfarmAudit
}

type Request struct{}

func (this *BuildfarmAudit) ModelName() string {
	return ModelName
}

func Model() *buildfarm.BuildfarmAudit {
	return &buildfarm.BuildfarmAudit{}
}

func (this *BuildfarmAudit) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *BuildfarmAudit) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *BuildfarmAudit) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *BuildfarmAudit) CreateV2(object interface{}) error {
	return nil
}

func (this *BuildfarmAudit) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	return errors.Wrap(err, "")
}

func (this *BuildfarmAudit) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmAudit) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmAudit) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmAudit) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Where("bug_id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmAudit) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindTaskAudit(auditorID uint, status, level []uint, page, pageSize int, sort, orderBy string) (map[string]interface{}, error) {
	var count int64
	items := []*ListResponse{}
	// paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(status) > 0 {
		db = db.Where("status in ?", status)
	} else {
		db = db.Where("status = ?", 0)
	}

	if len(level) > 0 {
		db = db.Where("level in ?", level)
	}

	if auditorID != 0 {
		db = db.Where("auditor_id = ?", auditorID)
	}

	err := db.Count(&count).Error
	if err != nil {
		return nil, errors.Wrap(err, "")
	}

	err = db.Preload("User").Preload("Auditor").Preload("LastAuditor").Scopes(easygorm.PaginateScope(page, pageSize, sort, orderBy)).Find(&items).Error
	if err != nil {
		return nil, errors.Wrap(err, "")
	}

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

type Role struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

func GetAuditorID(userID uint) (uint, error) {
	department := ddepartment.Department{}
	if err := easygorm.GetEasyGormDb().
		Table("(SELECT d.id, d.parent_id, ud.user_id  FROM user_departments ud LEFT JOIN departments d ON d.id = ud.department_id) as departments").
		Where("user_id = ?", userID).
		Find(&department).Error; err != nil {
		return 0, err
	}
	departmentID := department.ID
	departmentParentID := department.ParentID
	manageRole := Role{}
	if err := easygorm.GetEasyGormDb().Model(Role{}).Where("name = ?", "组长").Find(&manageRole).Error; err != nil {
		return 0, err
	}

	if manageRole.ID == "" {
		return 0, errors.New("未找到组长，请联系管理员")
	}

	managerIDs, err := easygorm.GetEasyGormEnforcer().GetUsersForRole(manageRole.ID)
	if err != nil {
		return 0, err
	}
	_managerIDs, err := easygorm.GetEasyGormEnforcer().GetUsersForRole("role::" + manageRole.ID)
	if err != nil {
		return 0, err
	}

	managerIDs = append(managerIDs, _managerIDs...)

	userDepartments := []*duserdepartment.UserDepartment{}
	if err := easygorm.GetEasyGormDb().
		Model(&duserdepartment.UserDepartment{}).
		Where("user_id in ? and department_id = ?", managerIDs, departmentID).
		Find(&userDepartments).Error; err != nil {
		return 0, err
	}

	if len(userDepartments) == 0 {
		if err := easygorm.GetEasyGormDb().
			Model(&duserdepartment.UserDepartment{}).
			Where("user_id in ? and department_id = ?", managerIDs, departmentParentID).
			Find(&userDepartments).Error; err != nil {
			return 0, err
		}
		if len(userDepartments) == 0 {
			return 0, fmt.Errorf("未找到当前用户关联组长")
		}
	}

	return userDepartments[0].UserID, nil
}

func BatchCreate(audit map[string]interface{}, details []map[string]interface{}) error {
	auditModel := BuildfarmAudit{}
	detailModel := BuildfarmAuditDetail{}
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(&auditModel).Create(&audit).Error
		if err != nil {
			return err
		}
		err = tx.Model(&detailModel).Create(&details).Error
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func BatchUpdate(items []*ListResponse, object map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		for _, item := range items {
			err := tx.Model(Model()).Where("id = ?", item.ID).Updates(object).Error
			if err != nil {
				return err
			}
		}

		return nil
	})
	return errors.Wrap(err, "")
}

type BuildfarmAuditHistory struct {
	buildfarm.BuildfarmAuditHistory
}

func Update(jobID, taskID string, object map[string]interface{}) error {
	history := BuildfarmAuditHistory{}
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(&history).Create(map[string]interface{}{
			"JobID":     jobID,
			"TaskID":    taskID,
			"AuditorID": object["LastAuditorID"],
			"Status":    object["Status"],
			"Comment":   object["Comment"],
			"CreatedAt": time.Now(),
			"UpdatedAt": time.Now(),
		}).Error
		if err != nil {
			return errors.Wrap(err, "")
		}

		err = tx.Model(Model()).Where("task_id = ? and job_id = ?", taskID, jobID).Updates(object).Error
		if err != nil {
			return errors.Wrap(err, "")
		}

		// if details != nil && compareDetails != nil && len(details) > 0 && len(compareDetails) > 0 {
		// 	sameMap := map[string]int{}
		// 	for _, detail := range details {
		// 		for _, compareDetail := range compareDetails {
		// 			if compareDetail.TaskID != detail.TaskID {
		// 				if _, ok := sameMap[compareDetail.TaskID]; !ok {
		// 					sameMap[compareDetail.TaskID] = 0
		// 				}
		// 				if detail.CurrentRepo == compareDetail.CurrentRepo {
		// 					if detail.CurrentLastCommitID == compareDetail.CurrentLastCommitID && detail.CommitIDs == compareDetail.CommitIDs {
		// 						sameMap[compareDetail.TaskID]++
		// 					}
		// 				}
		// 			}
		// 		}
		// 	}

		// 	for k, v := range sameMap {
		// 		if v == len(details) {
		// 			var comment string
		// 			switch object["Comment"].(type) {
		// 			case string:
		// 				if len(object["Comment"].(string)) == 0 {
		// 					comment = fmt.Sprintf(`镜像自：<a href="/audit/audit/%s">%s</>评审`, taskID, taskID)
		// 				} else {
		// 					comment = fmt.Sprintf(`%s<br>镜像自：<a href="/audit/audit/%s">%s</>评审`, object["Comment"], taskID, taskID)
		// 				}
		// 			default:
		// 				comment = fmt.Sprintf(`%s<br>镜像自：<a href="/audit/audit/%s">%s</>评审`, object["Comment"], taskID, taskID)
		// 			}

		// 			err := tx.Model(&history).Create(map[string]interface{}{
		// 				"JobID":     jobID,
		// 				"TaskID":    k,
		// 				"AuditorID": object["LastAuditorID"],
		// 				"Status":    object["Status"],
		// 				"Comment":   comment,
		// 				"CreatedAt": time.Now(),
		// 				"UpdatedAt": time.Now(),
		// 			}).Error
		// 			if err != nil {
		// 				return errors.Wrap(err, "")
		// 			}
		// 			err = tx.Model(Model()).Where("task_id = ? and job_id = ?", k, jobID).Updates(map[string]interface{}{
		// 				"LastAuditorID": object["LastAuditorID"],
		// 				"Status":        object["Status"],
		// 				"Comment":       comment,
		// 			}).Error
		// 			if err != nil {
		// 				return errors.Wrap(err, "")
		// 			}
		// 		}
		// 	}
		// }

		return nil
	})

	return errors.Wrap(err, "")
}

func (this *BuildfarmAudit) FindJobIDAndTaskID(jobID, taskID string) error {
	err := easygorm.GetEasyGormDb().
		Model(Model()).
		Preload("User").
		Preload("Auditor").
		Preload("LastAuditor").
		Where("job_id = ? and task_id = ?", jobID, taskID).Find(&this).Error
	return errors.Wrap(err, "")
}

func FindAuditPassByJobID(jobID string) ([]*BuildfarmAudit, error) {
	items := []*BuildfarmAudit{}
	err := easygorm.GetEasyGormDb().Model(&BuildfarmAudit{}).Where("job_id = ? and status = 1", jobID).Order("id desc").Find(&items).Error
	return items, err
}
