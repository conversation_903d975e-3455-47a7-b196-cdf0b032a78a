package buildfarm

import (
	"bytes"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/buildfarm/dgitjob"
	"irisAdminApi/service/dao/buildfarm/dmakejob"
	"irisAdminApi/service/dao/buildfarm/dproject"
	"irisAdminApi/service/dao/buildfarm/dserver"
	"irisAdminApi/service/dao/user/duser"
	"irisAdminApi/service/transaction/buildfarm/transmakejob"

	"github.com/jinzhu/copier"
	"github.com/kataras/iris/v12"
	"github.com/tidwall/gjson"
	"golang.org/x/crypto/ssh"
)

var statusMap = map[int]string{
	0: "编译中",
	1: "编译成功",
	2: "编译失败",
}

func PushMailQueue(gitjob *dgitjob.GitJob, makejob *dmakejob.MakeJob, status int) {
	if libs.Config.Mail.Enable {
		// rc := cache.GetRedisClusterClient()
		from := "编译农场"
		to := duser.UserMap[gitjob.UserID].Username + "@ruijie.com.cn"
		subject := fmt.Sprintf("[编译农场][普通编译][作业ID: %s][%s]", makejob.TaskID, statusMap[status])
		body := fmt.Sprintf(`%s<br>
编译仓库: %s<br>
编译分支: %s<br>
编译产品：%s<br>
编译目标：%s<br>
编译日志: <a href="http://%s:%d/logs/%s.log">编译日志</a>`,
			subject,
			gitjob.Project,
			gitjob.Branch,
			makejob.Product,
			makejob.Target,
			libs.Config.Nginx.HOST,
			libs.Config.Nginx.Port,
			makejob.TaskID)
		if status == 1 {
			// http://*************:9090/output/6836284b1703e4433505de56022d8ca5/
			body = fmt.Sprintf(`%s<br>编译结果: <a href="http://%s:%d/output/%s/">编译结果</a>`, body, libs.Config.Nginx.HOST, libs.Config.Nginx.Port, makejob.TaskID)
		}
		// msg := strings.Join([]string{from, to, subject, body}, "|")
		// _, err := rc.LPush(libs.Config.Mail.Queue, msg)
		// if err != nil {
		// 	logging.ErrorLogger.Error(err)
		// }
		libs.SendMailRedis(from, []string{to}, subject, body, []string{})
	}
}

func updateMakeJob(gitjob *dgitjob.GitJob, makejob *dmakejob.MakeJob, status int, version string) {
	gitjob.ServerID = makejob.ServerID
	updateGitJob(gitjob, status, version)
	data := map[string]interface{}{
		"Status":    status,
		"UpdatedAt": time.Now(),
		"ServerID":  makejob.ServerID,
	}

	if version != "" {
		data["Version"] = version
	}

	err := makejob.Update(makejob.ID, data)
	if err != nil {
		logging.ErrorLogger.Errorf("update make job err ", err)
		return
	}

	if status == 1 || status == 2 {
		PushMailQueue(gitjob, makejob, status)
	}
}

func updateMakeJobSoftwareVersionAndNumber(makejob *dmakejob.MakeJob) {
	archivePath := filepath.Join(libs.Config.Buildfarm.Archivepath, makejob.TaskID)
	var systemSoftwareVersion, systemSoftwareNumber, releaseBinDir string
	var dirs []string
	dirs, err := libs.GetAllDir(archivePath, dirs)
	if err != nil {
		logging.ErrorLogger.Error("read fail", err)
		return
	}

	for _, f := range dirs {
		if strings.HasSuffix(f, "releaseID-bin") {
			releaseBinDir = f
			break
		}
	}

	if releaseBinDir == "" {
		logging.ErrorLogger.Errorf("releaseID-bin not found")
		return
	}

	releaseIdFiles, err := os.ReadDir(releaseBinDir)
	if err != nil {
		logging.ErrorLogger.Errorf("read releaseIdTempPath fail", releaseBinDir, err)
	}

	for _, f := range releaseIdFiles {
		if strings.HasSuffix(f.Name(), ".zip") {
			jsonStr, err := ReadUpdateJsonInZipFile(filepath.Join(releaseBinDir, f.Name()))
			if err != nil {
				logging.ErrorLogger.Errorf("read json fail", releaseBinDir, err)
			}
			systemSoftwareVersion = gjson.Get(jsonStr, "extend.version").String()
			systemSoftwareNumber = gjson.Get(jsonStr, "extend.system_software_number").String()
			if systemSoftwareVersion != "" && systemSoftwareNumber != "" {
				err = makejob.Update(makejob.ID, map[string]interface{}{
					"UpdatedAt":       time.Now(),
					"SoftwareVersion": systemSoftwareVersion,
					"SoftwareNumber":  systemSoftwareNumber,
				})
				if err != nil {
					logging.ErrorLogger.Errorf("update git job err ", err)
				}
				break
			}
		}
	}
}

func RestartRuning() {
	// 获取所有服务器状态
	// 获取所有运行作业

	queueMakeJobs, err := dmakejob.FindTMakeJobsByStatusAndTaskType([]uint{0}, []uint{1, 2, 3})
	if err != nil {
		logging.DebugLogger.Error("find running job err", err)
		return
	}
	if len(queueMakeJobs) == 0 {
		return
	}

	for _, queueMakeJob := range queueMakeJobs {
		gitjob := dgitjob.GitJob{}
		err := copier.Copy(&gitjob, &queueMakeJob.GitJob)
		if err != nil {
			logging.DebugLogger.Error("copy gitjob err", err)
			return
		}

		Selecter(&gitjob, queueMakeJob, true, false)
	}
}

var CheckQueueLock sync.Mutex

func CheckQueue() {
	// 获取所有服务器状态
	// 获取所有运行作业
	CheckQueueLock.Lock()
	defer CheckQueueLock.Unlock()
	logging.DebugLogger.Debugf("检查队列状态")
	queueMakeJobs, err := dmakejob.FindTMakeJobsByStatusAndTaskType([]uint{3}, []uint{1, 2, 3})
	if err != nil {
		return
	}
	if len(queueMakeJobs) == 0 {
		return
	}

	for _, queueMakeJob := range queueMakeJobs {
		gitjob := dgitjob.GitJob{}
		err := copier.Copy(&gitjob, &queueMakeJob.GitJob)
		if err != nil {
			logging.ErrorLogger.Errorf(err.Error())
			continue
		}
		if queueMakeJob.ServerID > 0 {
			Selecter(&gitjob, queueMakeJob, true, true)
		} else {
			serverID := ChooseServerV3(fmt.Sprintf("%v", queueMakeJob.GitJob.TaskType), queueMakeJob.TaskType == 2)
			logging.DebugLogger.Debugf("编译来源ID: %v  服务器ID: %v", queueMakeJob.GitJob.TaskType, serverID)
			if serverID > 0 {
				gitjob.ServerID = serverID
				queueMakeJob.ServerID = serverID
				Selecter(&gitjob, queueMakeJob, false, false)
			}
		}
	}
	time.Sleep(10 * time.Second)
}

func Selecter(gitjob *dgitjob.GitJob, makejob *dmakejob.MakeJob, restart, rebuild bool) {
	if strings.HasSuffix(gitjob.Project, "build-product") {
		if makejob.GitJob.TaskType == 3 {
			go CoverityMakeProductjob(gitjob, makejob)
		} else {
			go MakeProductjob(gitjob, makejob, restart, false)
		}
	} else {
		if makejob.GitJob.TaskType == 3 {
			go CoverityMakejob(gitjob, makejob)
		} else {
			go Makejob(gitjob, makejob, restart, rebuild)
		}
	}
}

func Makejob(gitjob *dgitjob.GitJob, makejob *dmakejob.MakeJob, restart, rebuild bool) {
	version := ""
	updateMakeJob(gitjob, makejob, 0, version)

	f, err := os.OpenFile(filepath.Join(libs.Config.Buildfarm.Logpath, makejob.TaskID+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}
	defer f.Close()

	client, err := SSHClient(makejob.ServerID)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}
	defer client.Close()

	command := fmt.Sprintf(`echo 当前编译服务器： $(hostname)`)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}

	if !restart {
		err = Gitjob(f, client, gitjob, makejob)
		if err != nil {
			logging.ErrorLogger.Errorf("create user get err ", err)
			updateMakeJob(gitjob, makejob, 2, version)
			return
		}
	}

	command = fmt.Sprintf("cd %s && git pull && make download", gitjob.Dir)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}

	command = fmt.Sprintf("cd %s && git rev-parse HEAD", gitjob.Dir)
	var stdOut bytes.Buffer
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}

	getVersion := strings.Replace(stdOut.String(), "\n", "", -1)
	if len(getVersion) == 40 {
		version = getVersion
		updateMakeJob(gitjob, makejob, 0, version)
	} else {
		logging.ErrorLogger.Errorf("获取版本号异常", makejob.ID)
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}

	f.WriteString(fmt.Sprintf("当前版本为：%s\r\n", version))

	// 性能编译及调试编译动作

	err = BuildTypePrevTask(client, f, gitjob.Dir, makejob.Product, gitjob.JobID, makejob.BuildType)
	if err != nil {
		logging.ErrorLogger.Errorf("switch defconfig failed", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}

	command = fmt.Sprintf("cd %s && rm gcov-fix -rf && git clone ssh://git@10.51.135.102:8022/temp/gcov-fix gcov-fix", gitjob.Dir)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}

	switch makejob.Defconfig {
	case "all":
		var defconfigs bytes.Buffer
		command = fmt.Sprintf("cd %s && make list-defconfigs|grep -E 'initrd_defconfig|rootfs_defconfig'|awk '{print $1}'", gitjob.Dir)
		if err := RunCommandOutBuffer(client, command, &defconfigs); err != nil {
			logging.ErrorLogger.Errorf("run command error", err.Error())
			updateMakeJob(gitjob, makejob, 2, version)
			return
		}
		for _, defconfig := range strings.Split(defconfigs.String(), "\n") {
			if defconfig == "" {
				continue
			}

			command = fmt.Sprintf("cd %s && source gcov-fix/env-config.sh && make %s && make clean && make %s", gitjob.Dir, defconfig, makejob.Target)
			if strings.HasSuffix(gitjob.Repo, "base-os.git") {
				command = fmt.Sprintf("cd %s && make %s && make clean && make %s && make pack-all", gitjob.Dir, defconfig, makejob.Target)
			}

			if err := RunCommandOutFile(client, command, f); err != nil {
				logging.ErrorLogger.Errorf("run command error", err.Error())
				updateMakeJob(gitjob, makejob, 2, version)
				return
			}
			if makejob.BuildOspkg {
				command = fmt.Sprintf("cd %s && make build-ospkg", gitjob.Dir)
				if err := RunCommandOutFile(client, command, f); err != nil {
					logging.ErrorLogger.Errorf("run command error", err.Error())
					updateMakeJob(gitjob, makejob, 2, version)
					return
				}
			}

		}

	case "all-initrd":
		var defconfigs bytes.Buffer
		command = fmt.Sprintf("cd %s && make list-defconfigs|grep initrd_defconfig|awk '{print $1}'", gitjob.Dir)
		if err := RunCommandOutBuffer(client, command, &defconfigs); err != nil {
			logging.ErrorLogger.Errorf("run command error", err.Error())
			updateMakeJob(gitjob, makejob, 2, version)
			return
		}
		for _, defconfig := range strings.Split(defconfigs.String(), "\n") {
			if defconfig == "" {
				continue
			}

			command = fmt.Sprintf("cd %s && source gcov-fix/env-config.sh && make %s && make clean && make %s", gitjob.Dir, defconfig, makejob.Target)
			if strings.HasSuffix(gitjob.Repo, "base-os.git") {
				command = fmt.Sprintf("cd %s && make %s && make clean && make %s && make pack-all", gitjob.Dir, defconfig, makejob.Target)
			}
			if err := RunCommandOutFile(client, command, f); err != nil {
				logging.ErrorLogger.Errorf("run command error", err.Error())
				updateMakeJob(gitjob, makejob, 2, version)
				return
			}
			if makejob.BuildOspkg {
				command = fmt.Sprintf("cd %s && make build-ospkg", gitjob.Dir)
				if err := RunCommandOutFile(client, command, f); err != nil {
					logging.ErrorLogger.Errorf("run command error", err.Error())
					updateMakeJob(gitjob, makejob, 2, version)
					return
				}
			}

		}

	case "all-rootfs":
		var defconfigs bytes.Buffer
		command = fmt.Sprintf("cd %s && make list-defconfigs|grep rootfs_defconfig|awk '{print $1}'", gitjob.Dir)
		if err := RunCommandOutBuffer(client, command, &defconfigs); err != nil {
			logging.ErrorLogger.Errorf("run command error", err.Error())
			updateMakeJob(gitjob, makejob, 2, version)
			return
		}
		for _, defconfig := range strings.Split(defconfigs.String(), "\n") {
			if defconfig == "" {
				continue
			}

			command = fmt.Sprintf("cd %s && source gcov-fix/env-config.sh && make %s && make clean && make %s", gitjob.Dir, defconfig, makejob.Target)
			if strings.HasSuffix(gitjob.Repo, "base-os.git") {
				command = fmt.Sprintf("cd %s && make %s && make clean && make %s && make pack-all", gitjob.Dir, defconfig, makejob.Target)
			}
			if err := RunCommandOutFile(client, command, f); err != nil {
				logging.ErrorLogger.Errorf("run command error", err.Error())
				updateMakeJob(gitjob, makejob, 2, version)
				return
			}
			if makejob.BuildOspkg {
				command = fmt.Sprintf("cd %s && make build-ospkg", gitjob.Dir)
				if err := RunCommandOutFile(client, command, f); err != nil {
					logging.ErrorLogger.Errorf("run command error", err.Error())
					updateMakeJob(gitjob, makejob, 2, version)
					return
				}
			}

		}

	default:
		if !restart {
			if makejob.Baseline != "" {
				command := fmt.Sprintf("cd %s && make install-devel IMG=%s", gitjob.Dir, makejob.Baseline)
				if err := RunCommandOutFile(client, command, f); err != nil {
					logging.ErrorLogger.Errorf("run command error", err.Error())
					updateMakeJob(gitjob, makejob, 2, version)
					return
				}
			} else if makejob.Defconfig != "" {
				command := fmt.Sprintf("cd %s && make %s", gitjob.Dir, makejob.Defconfig)
				if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
					logging.ErrorLogger.Errorf("run command error", err.Error())
					updateMakeJob(gitjob, makejob, 2, version)
					return
				}
			}

			if makejob.KernelRepo != "" && makejob.KernelBranch != "" {
				command := fmt.Sprintf(`cd %s && sed -i '/BR2_LINUX_KERNEL_CUSTOM_REPO_URL=/c\BR2_LINUX_KERNEL_CUSTOM_REPO_URL="%s"' ./output/.config`, gitjob.Dir, makejob.KernelRepo)
				if err := RunCommandOutFile(client, command, f); err != nil {
					logging.ErrorLogger.Errorf("run command error", err.Error())
					updateMakeJob(gitjob, makejob, 2, version)
					return
				}

				command = fmt.Sprintf(`cd %s && sed -i '/BR2_LINUX_KERNEL_CUSTOM_REPO_VERSION=/cBR2_LINUX_KERNEL_CUSTOM_REPO_VERSION="%s"' ./output/.config`, gitjob.Dir, makejob.KernelBranch)
				if err := RunCommandOutFile(client, command, f); err != nil {
					logging.ErrorLogger.Errorf("run command error", err.Error())
					updateMakeJob(gitjob, makejob, 2, version)
					return
				}

				command = fmt.Sprintf(`cd %s && cat ./output/.config`, gitjob.Dir)
				if err := RunCommandOutFile(client, command, f); err != nil {
					logging.ErrorLogger.Errorf("run command error", err.Error())
					updateMakeJob(gitjob, makejob, 2, version)
					return
				}
			}

		}

		if makejob.Target == "all" {
			command := fmt.Sprintf("cd %s", gitjob.Dir)
			if makejob.DirClean {
				command = fmt.Sprintf("%s && make clean", command)
			}

			command = fmt.Sprintf("%s && source gcov-fix/env-config.sh && make", command)

			if strings.HasSuffix(gitjob.Repo, "base-os.git") {
				command = fmt.Sprintf("%s && make pack-all", command)
			}

			if err := RunCommandOutFile(client, command, f); err != nil {
				logging.ErrorLogger.Errorf("run command error", err.Error())
				updateMakeJob(gitjob, makejob, 2, version)
				return
			}
		} else {
			for _, target := range strings.Split(makejob.Target, ",") {
				command := fmt.Sprintf("cd %s", gitjob.Dir)
				if makejob.DirClean {
					command = fmt.Sprintf("%s && make %s-dirclean", command, target)
				}

				if rebuild {
					command = fmt.Sprintf("%s && source gcov-fix/env-config.sh && make %s-rebuild", command, target)
				} else {
					command = fmt.Sprintf("%s && source gcov-fix/env-config.sh && make %s", command, target)
				}

				if strings.HasSuffix(gitjob.Repo, "base-os.git") {
					command = fmt.Sprintf("%s && make pack-all", command)
				}

				if err := RunCommandOutFile(client, command, f); err != nil {
					logging.ErrorLogger.Errorf("run command error", err.Error())
					updateMakeJob(gitjob, makejob, 2, version)
					return
				}
			}
		}
		if makejob.BuildOspkg {
			command = fmt.Sprintf("cd %s && make build-ospkg", gitjob.Dir)
			if err := RunCommandOutFile(client, command, f); err != nil {
				logging.ErrorLogger.Errorf("run command error", err.Error())
				updateMakeJob(gitjob, makejob, 2, version)
				return
			}
		}

	}
	// 增加检查个人仓检查时间信息
	f.WriteString(fmt.Sprintf("\n%s 开始检查个人仓修订", time.Now().Format("2006-01-02 15:04:05")))
	pass, err := CheckPersonalGit(client, gitjob.JobID, makejob.TaskID, gitjob.Dir, gitjob.UserID)
	if err != nil {
		f.WriteString(fmt.Sprintf("check personal git error, %s", err.Error()))
		logging.ErrorLogger.Errorf("check personal git error", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}
	f.WriteString(fmt.Sprintf("\n%s 检查个人仓修订结束", time.Now().Format("2006-01-02 15:04:05")))
	// pass = true

	ArchiveOutputWorker(pass, f, client, gitjob, makejob, version)
}

func Gitjob(f *os.File, client *ssh.Client, gitjob *dgitjob.GitJob, makejob *dmakejob.MakeJob) error {
	server := &dserver.Response{}
	err := server.Find(makejob.ServerID)
	if err != nil {
		logging.ErrorLogger.Errorf("find server get err ", err)
		return err
	}

	command := fmt.Sprintf("rm %s -rf && git clone -b %s %s %s -v && cd %s && git clean -dfx", gitjob.Dir, gitjob.Branch, gitjob.Repo, gitjob.Dir, gitjob.Dir)
	f.WriteString("执行拉取项目操作，并切换分支\r\n")

	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", command, err.Error())
		updateGitJob(gitjob, 2, "")
		return err
	}

	f.WriteString(fmt.Sprintf("拉取项目任务完成\r\n"))

	if makejob.PatchEnable {
		cmd := exec.Command("scp", "-o", "stricthostkeychecking=no", "-r", "-P", strconv.Itoa(int(server.Port)), makejob.GetTempPatchDir(), fmt.Sprintf("%s@%s:%s", server.Username, server.Host, gitjob.Dir))
		// cmd := exec.Command("scp", "-o", "stricthostkeychecking=no", "-r", "-P", "22", tempPatchDir, fmt.Sprintf("%s@%s:%s", "linjiakai", "127.0.0.1", filepath.Join(libs.Config.Buildfarm.Compilepath, "test123123")))
		var out bytes.Buffer
		var stderr bytes.Buffer
		cmd.Stdout = &out
		cmd.Stderr = &stderr
		err = cmd.Run()
		if err != nil {
			logging.ErrorLogger.Errorf("scp file failed", err, out.String(), stderr.String())
			return err
		}
	}

	customDefconfigPath := makejob.GetTempDefconfigPath()
	if _, err := os.Stat(customDefconfigPath); err == nil {
		cmd := exec.Command("scp", "-o", "stricthostkeychecking=no", "-r", "-P", strconv.Itoa(int(server.Port)), customDefconfigPath, fmt.Sprintf("%s@%s:%s", server.Username, server.Host, filepath.Join(gitjob.Dir, "prj_"+makejob.Product, "configs")))
		// cmd := exec.Command("scp", "-o", "stricthostkeychecking=no", "-r", "-P", "22", tempPatchDir, fmt.Sprintf("%s@%s:%s", "linjiakai", "127.0.0.1", filepath.Join(libs.Config.Buildfarm.Compilepath, "test123123")))
		var out bytes.Buffer
		var stderr bytes.Buffer
		cmd.Stdout = &out
		cmd.Stderr = &stderr
		err = cmd.Run()
		if err != nil {
			logging.ErrorLogger.Errorf("scp file failed", err, out.String(), stderr.String())
			return err
		}
	} else if !os.IsNotExist(err) {
		logging.ErrorLogger.Errorf("scp file failed", err)
		return err
	}

	return nil
}

func MakeProductjob(gitjob *dgitjob.GitJob, makejob *dmakejob.MakeJob, restart, cleanErrorPkg bool) {
	version := ""
	updateMakeJob(gitjob, makejob, 0, version)

	f, err := os.OpenFile(filepath.Join(libs.Config.Buildfarm.Logpath, makejob.TaskID+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}
	defer f.Close()

	client, err := SSHClient(makejob.ServerID)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}
	defer client.Close()

	command := fmt.Sprintf(`echo 当前编译服务器： $(hostname)`)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}

	if !restart {
		err = Gitjob(f, client, gitjob, makejob)
		if err != nil {
			logging.ErrorLogger.Errorf("create user get err ", err)
			updateMakeJob(gitjob, makejob, 2, version)
			return
		}
	}

	command = fmt.Sprintf("cd %s && git rev-parse HEAD", gitjob.Dir)
	var stdOut bytes.Buffer
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}

	getVersion := strings.Replace(stdOut.String(), "\n", "", -1)
	if len(getVersion) == 40 {
		version = getVersion
		updateMakeJob(gitjob, makejob, 0, version)
	} else {
		logging.ErrorLogger.Errorf("获取版本号异常", makejob.ID)
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}
	f.WriteString(fmt.Sprintf("当前版本为：%s\r\n", version))

	project := fmt.Sprintf("prj_%s", makejob.Product)
	if !restart {
		// 定制编译修改组件仓库及分支
		if (gitjob.Customized == 1 || gitjob.Customized == 2) && makejob.FeedsConfigCustom != "" {
			err = MakeJobCustomTask(client, f, gitjob.Dir, gitjob.JobID, project, makejob.Product, makejob.FeedsConfigCustom)
			if err != nil {
				logging.ErrorLogger.Errorf("run custom job error", err.Error())
				updateMakeJob(gitjob, makejob, 2, version)
				return
			}
		}
	}

	command = fmt.Sprintf("cd %s/prj_%s && source env-setup && make download", gitjob.Dir, makejob.Product)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}

	// 检查与Trunk 差异
	if !restart {
		err := CustomKernelConfigPrevTask(client, f, gitjob, makejob)
		if err != nil {
			logging.ErrorLogger.Errorf("switch defconfig failed", err.Error())
			updateMakeJob(gitjob, makejob, 2, version)
			return
		}
		// 性能编译及调试编译动作
		err = BuildTypePrevTask(client, f, gitjob.Dir, makejob.Product, gitjob.JobID, makejob.BuildType)
		if err != nil {
			logging.ErrorLogger.Errorf("switch defconfig failed", err.Error())
			updateMakeJob(gitjob, makejob, 2, version)
			return
		}

		// 增加检查，是否有更新，如果没有更新则跳过编译，从feeds.conf.default获取版本号
		if makejob.PatchEnable {
			err = MakeJobPatchTask(client, f, gitjob.Dir, gitjob.JobID, project)
			if err != nil {
				logging.ErrorLogger.Errorf("run patch job error", err.Error())
				updateMakeJob(gitjob, makejob, 2, version)
				return
			}
		}
	}

	if makejob.BuildType == "factory" {
		command = fmt.Sprintf("cd %s/prj_%s && source env-setup && make %s_fac_defconfig && make && make pack-all", gitjob.Dir, makejob.Product, makejob.Product)
	} else {
		if cleanErrorPkg {
			command = fmt.Sprintf("cd %s/prj_%s && source env-setup && make %s_defconfig && make clean-error-pkg && make && make pack-all", gitjob.Dir, makejob.Product, makejob.Product)
		} else {
			command = fmt.Sprintf("cd %s/prj_%s && source env-setup && make %s_defconfig && make && make pack-all", gitjob.Dir, makejob.Product, makejob.Product)
		}
	}

	// command = fmt.Sprintf("cd %s/prj_%s && source env-setup && make %s_defconfig && make && make pack-all", gitjob.Dir, makejob.Product, makejob.Product)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)

		return
	}

	// 增加检查个人仓检查时间信息
	f.WriteString(fmt.Sprintf("\n%s 开始检查个人仓修订", time.Now().Format("2006-01-02 15:04:05")))
	pass, err := CheckPersonalGit(client, gitjob.JobID, makejob.TaskID, gitjob.Dir, gitjob.UserID)
	if err != nil {
		f.WriteString(fmt.Sprintf("check personal git error, %s", err.Error()))
		logging.ErrorLogger.Errorf("check personal git error", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}
	f.WriteString(fmt.Sprintf("\n%s 检查个人仓修订结束", time.Now().Format("2006-01-02 15:04:05")))

	ArchiveOutputWorker(pass, f, client, gitjob, makejob, version)
}

func ArchiveOutputWorker(pass bool, f *os.File, client *ssh.Client, gitjob *dgitjob.GitJob, makejob *dmakejob.MakeJob, version string) {
	if pass {
		f.WriteString(fmt.Sprintf("\n%s 开始归档", time.Now().Format("2006-01-02 15:04:05")))
		var remoteDir string
		if makejob.Product != "" {
			remoteDir = fmt.Sprintf("%s/prj_%s/output/images/", gitjob.Dir, makejob.Product)
		} else {
			remoteDir = fmt.Sprintf("%s/output/images/", gitjob.Dir)
		}
		// // 归档 mkinstpkg
		// if makejob.Cpu.Cpu != "" && makejob.Product != "" {
		// 	if err := ArchiveMkinstpkg(makejob.ServerID, makejob.JobID, fmt.Sprintf("%s/prj_%s", gitjob.Dir, makejob.Product), makejob.Cpu.Cpu); err != nil {
		// 		f.WriteString(fmt.Sprintf("归档mkinstpkg失败：%s\r\n", err.Error()))
		// 	}
		// }

		if err := ArchiveOutputV2(gitjob.ServerID, makejob.TaskID, remoteDir, gitjob.Repo, gitjob.Branch, makejob.Target); err == nil {
			if makejob.Product != "" {
				updateMakeJobSoftwareVersionAndNumber(makejob)
				command := fmt.Sprintf("rm -rf %s", gitjob.Dir)
				if err := RunCommandOutFile(client, command, f); err != nil {
					logging.ErrorLogger.Errorf("run command error", err.Error())
				}
				BuildTypePostTask(client, f, makejob.TaskID, makejob.BuildType)
			}

			updateMakeJob(gitjob, makejob, 1, version)
			f.WriteString(fmt.Sprintf("\n%s 归档结束", time.Now().Format("2006-01-02 15:04:05")))
			return
		} else {
			f.WriteString(fmt.Sprintf("\n%s 归档失败", time.Now().Format("2006-01-02 15:04:05")))
			updateMakeJob(gitjob, makejob, 2, version)
			return
		}

	} else {
		updateMakeJob(gitjob, makejob, 4, version)
		return
	}
}

func StartCoverityMakeProductjobQueue(ctx iris.Context) {
	err := dao.Update(&dmakejob.MakeJob{}, ctx, map[string]interface{}{"status": 3})
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func CoverityMakeProductjob(gitjob *dgitjob.GitJob, makejob *dmakejob.MakeJob) {
	version := ""
	updateMakeJob(gitjob, makejob, 0, version)
	client, err := SSHClient(gitjob.ServerID)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}
	defer client.Close()

	f, _ := os.OpenFile(filepath.Join(libs.Config.Buildfarm.Logpath, makejob.TaskID+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
	defer f.Close()

	command := fmt.Sprintf(`echo 当前编译服务器： $(hostname)`)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}

	command = fmt.Sprintf("cd %s && git rev-parse HEAD", gitjob.Dir)
	var stdOut bytes.Buffer
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}

	getVersion := strings.Replace(stdOut.String(), "\n", "", -1)
	if len(getVersion) == 40 {
		version = getVersion
		updateMakeJob(gitjob, makejob, 0, version)
	} else {
		logging.ErrorLogger.Errorf("获取版本号异常", makejob.ID)
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}
	f.WriteString(fmt.Sprintf("当前版本为：%s\r\n", version))
	project := fmt.Sprintf("prj_%s", makejob.Product)
	if (gitjob.Customized == 1 || gitjob.Customized == 2) && makejob.FeedsConfigCustom != "" {
		err = MakeJobCustomTask(client, f, gitjob.Dir, gitjob.JobID, project, makejob.Product, makejob.FeedsConfigCustom)
		if err != nil {
			logging.ErrorLogger.Errorf("run custom job error", err.Error())
			updateMakeJob(gitjob, makejob, 2, version)
			return
		}
	}
	command = fmt.Sprintf("cd %s/prj_%s && source env-setup && make download", gitjob.Dir, makejob.Product)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}
	// 增加检查，是否有更新，如果没有更新则跳过编译，从feeds.conf.default获取版本号
	if makejob.PatchEnable {
		err = MakeJobPatchTask(client, f, gitjob.Dir, gitjob.JobID, project)
		if err != nil {
			logging.ErrorLogger.Errorf("run patch job error", err.Error())
			updateMakeJob(gitjob, makejob, 2, version)
			return
		}
	}
	covProject := gitjob.Branch
	if gitjob.Branch == "Trunk" {
		covProject = "NTOS_Trunk"
	}

	command = fmt.Sprintf("cd %s/prj_%s && source env-setup && make %s_defconfig && /root/cov-analysis-linux64-2021.12.2/bin/cov-build --dir ./%s_%s make", gitjob.Dir, makejob.Product, makejob.Product, covProject, makejob.Product)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}
	command = fmt.Sprintf("cd %s/prj_%s/%s_%s && tar -zcf ../%s_%s.tar.gz ./ && scp ../%s_%s.tar.gz ngcf@172.30.198.86:/home/<USER>/coverity/projects/ && ssh ngcf@172.30.198.86 /home/<USER>/coverity/shell/file_analy.sh %s %s", gitjob.Dir, makejob.Product, covProject, makejob.Product, covProject, makejob.Product, covProject, makejob.Product, covProject, makejob.Product)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}
	command = fmt.Sprintf(`curl -s -u admin:admin -X POST "http://172.30.198.135:8080/job/cov_result/buildWithParameters?token=cov_result&Project=%s&Product=%s&BaseProduct=$BaseProduct&BaseProject=$BaseProject&UserName=sec_user&Password=sec_user"`, covProject, makejob.Product)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}
	command = fmt.Sprintf(`ssh ngcf@172.30.198.86 /home/<USER>/coverity/shell/clear.sh %s %s`, covProject, makejob.Product)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}
	updateMakeJob(gitjob, makejob, 1, version)
}

func CoverityMakejob(gitjob *dgitjob.GitJob, makejob *dmakejob.MakeJob) {
	version := ""
	updateMakeJob(gitjob, makejob, 0, version)
	client, err := SSHClient(gitjob.ServerID)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}
	defer client.Close()

	f, _ := os.OpenFile(filepath.Join(libs.Config.Buildfarm.Logpath, makejob.TaskID+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
	defer f.Close()

	command := fmt.Sprintf(`echo 当前编译服务器： $(hostname)`)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}

	command = fmt.Sprintf("cd %s && git rev-parse HEAD", gitjob.Dir)
	var stdOut bytes.Buffer
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}

	getVersion := strings.Replace(stdOut.String(), "\n", "", -1)
	if len(getVersion) == 40 {
		version = getVersion
		updateMakeJob(gitjob, makejob, 0, version)
	} else {
		logging.ErrorLogger.Errorf("获取版本号异常", makejob.ID)
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}

	f.WriteString(fmt.Sprintf("当前版本为：%s\r\n", version))
	command = fmt.Sprintf("cd %s && make download", gitjob.Dir)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}
	covProject := gitjob.Project + "_" + gitjob.Branch

	command = fmt.Sprintf("cd %s &&  /root/cov-analysis-linux64-2021.12.2/bin/cov-build --dir ./%s_%s make %s", gitjob.Dir, covProject, "NoProduct", makejob.Target)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}
	command = fmt.Sprintf("cd %s/%s_%s && tar -zcf ../%s_%s.tar.gz ./ && scp ../%s_%s.tar.gz ngcf@172.30.198.86:/home/<USER>/coverity/projects/ && ssh ngcf@172.30.198.86 /home/<USER>/coverity/shell/file_analy.sh %s %s", gitjob.Dir, covProject, "NoProduct", covProject, "NoProduct", covProject, "NoProduct", covProject, "NoProduct")
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}
	command = fmt.Sprintf(`curl -s -u admin:admin -X POST "http://172.30.198.135:8080/job/cov_result/buildWithParameters?token=cov_result&Project=%s&Product=%s&BaseProduct=$BaseProduct&BaseProject=$BaseProject&UserName=sec_user&Password=sec_user"`, covProject, "NoProduct")
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}
	command = fmt.Sprintf(`ssh ngcf@172.30.198.86 /home/<USER>/coverity/shell/clear.sh %s %s`, covProject, "NoProduct")
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakeJob(gitjob, makejob, 2, version)
		return
	}
	updateMakeJob(gitjob, makejob, 1, version)
}

func ArchiveOutput(gitjob *dgitjob.GitJob, makejob *dmakejob.MakeJob) error {
	server := &dserver.Response{}
	err := server.Find(gitjob.ServerID)
	if err != nil {
		logging.ErrorLogger.Errorf("create gitjob find server get err ", err)
	}
	archivePath := filepath.Join(libs.Config.Buildfarm.Archivepath, makejob.TaskID)
	err = os.MkdirAll(archivePath, 0o755)
	os.Chmod(archivePath, 0o755)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("mkdir before scp error: %s", err.Error()))
		return err
	}
	cmd := exec.Command("scp", "-o", "stricthostkeychecking=no", "-r", "-P", strconv.Itoa(int(server.Port)), fmt.Sprintf("%s@%s:/%s/output/images/", server.Username, server.Host, gitjob.Dir), archivePath)
	if makejob.TaskType == 1 {
		cmd = exec.Command("scp", "-o", "stricthostkeychecking=no", "-r", "-P", strconv.Itoa(int(server.Port)), fmt.Sprintf("%s@%s:/%s/prj_%s/output/images/", server.Username, server.Host, gitjob.Dir, makejob.Product), archivePath)
	}

	var out bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &stderr
	err = cmd.Run()
	if err != nil {
		logging.ErrorLogger.Errorf("scp get err ", err, stderr.String())
		return err
	}
	return nil
}

func CreateMakeJobV2(ctx iris.Context) {
	id, _ := dao.GetId(ctx)

	gitjob := &dgitjob.GitJob{}
	err := gitjob.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("get gitjob err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if gitjob.ID == 0 {
		logging.ErrorLogger.Errorf("get gitjob err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	MakejobReq := &transmakejob.MakejobReq{}
	if err := ctx.ReadForm(MakejobReq); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*MakejobReq)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	if err = transmakejob.CreateMakeJobTransaction(ctx, MakejobReq, gitjob); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetMakeJobs(ctx iris.Context) {
	// uId, err := dao.GetAuthId(ctx)
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	jobID := ctx.FormValue("job_id")
	userID := ctx.FormValue("user_id")
	repo := ctx.FormValue("repo")
	branch := ctx.FormValue("branch")
	server := ctx.FormValue("server")
	taskType := ctx.FormValue("task_type")
	customized := ctx.FormValue("customized")

	start := ctx.FormValue("start")
	end := ctx.FormValue("end")
	status := ctx.FormValue("status")
	product := ctx.FormValue("product")
	defconfig := ctx.FormValue("defconfig")
	target := ctx.FormValue("target")
	buildType := ctx.FormValue("build_type")
	softnum := ctx.FormValue("softnum")
	softversion := ctx.FormValue("softversion")

	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	var gitjobIds []string

	gitjobs, err := dgitjob.FindGitjobs(jobID, userID, repo, branch, server, customized)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	for _, gitjob := range gitjobs {
		if libs.InArrayS(gitjobIds, gitjob.JobID) {
			continue
		}
		gitjobIds = append(gitjobIds, gitjob.JobID)
	}

	makejobs, err := dmakejob.FindMakeJobsInJobIds(gitjobIds, taskType, start, end, status, product, defconfig, target, buildType, softnum, softversion, sort, orderBy, page, pageSize)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	for _, makejob := range makejobs["items"].([]*dmakejob.ListResponse) {
		for _, gitjob := range gitjobs {
			if makejob.JobID == gitjob.JobID {
				var makejobGitjob dmakejob.GitJob
				err = copier.Copy(&makejobGitjob, &gitjob)
				if err != nil {
					logging.ErrorLogger.Errorf("copy git job data err", err)
				}
				makejob.GitJob = &makejobGitjob
			}
		}
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, makejobs, response.NoErr.Msg))
	return
}

func GetQueueJobs(ctx iris.Context) {
	// 获取所有排队作业
	queueMakeJobs, err := dmakejob.FindQueueJobs()
	if err != nil {
		logging.ErrorLogger.Errorf("get all running job get err ", err)
		return
	}

	queuejobIds := []string{}
	for _, job := range queueMakeJobs {
		queuejobIds = append(queuejobIds, job.JobID)
	}
	if len(queuejobIds) == 0 {
		ctx.JSON(response.NewResponse(response.NoErr.Code, queueMakeJobs, response.NoErr.Msg))
		return
	}

	queueGitjobs, err := dgitjob.FindGitjobsInJobIds(queuejobIds)
	if err != nil {
		logging.ErrorLogger.Errorf("get all running job get err ", err)
		return
	}

	for _, makejob := range queueMakeJobs {
		for _, gitjob := range queueGitjobs {
			if makejob.JobID == gitjob.JobID {
				var makejobGitjob dmakejob.GitJob
				err = copier.Copy(&makejobGitjob, &gitjob)
				if err != nil {
					logging.ErrorLogger.Errorf("copier copy err", err.Error())
				}
				makejob.GitJob = &makejobGitjob
			}
		}
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, queueMakeJobs, response.NoErr.Msg))
	return
}

func CheckVersion(ctx iris.Context) {
	taskId := ctx.FormValue("task_id")
	if taskId == "" {
		logging.ErrorLogger.Errorf("check make job version err ")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	archivePath := filepath.Join(libs.Config.Buildfarm.Archivepath, taskId)
	var buildInfoPath string
	filepath.Walk(archivePath, func(path string, info os.FileInfo, err error) error {
		pathSlice := strings.Split(path, "/")
		if pathSlice[len(pathSlice)-1] == "build_info" {
			buildInfoPath = path
			return io.EOF
		}
		return nil
	})
	if buildInfoPath == "" {
		logging.ErrorLogger.Errorf("check make job version err ")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未找到build_info文件，无法检查！"))
		return
	}

	f, err := ioutil.ReadFile(buildInfoPath)
	if err != nil {
		logging.ErrorLogger.Errorf("check make job version err ")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "读取build_info文件失败！"))
		return
	}
	buildInfoObjects := []map[string]interface{}{}
	count := 0
	failCount := 0
	for _, item := range strings.Split(string(f), "\n") {
		_project := map[string]interface{}{}
		if item == "" {
			continue
		}
		if strings.Contains(item, "BuildTime") {
			continue
		}
		slice := strings.Split(strings.Replace(item, "git:", "git ", -1), " ")
		_project["version"] = slice[len(slice)-1]
		version := slice[len(slice)-1]
		branch := slice[2]
		project := libs.PickProjectFromBuildInfo(item)
		url := slice[1]
		latesVersion, err := GetLatestBranchVersion(project, branch)
		if err != nil {
			version = "(Unkown) " + version
			failCount++
		}
		if version == latesVersion {
			version = "(Head) " + version
			count++
		}

		buildInfoObjects = append(buildInfoObjects, map[string]interface{}{
			"version": version,
			"url":     url,
			"branch":  branch,
			"project": project,
		})
	}
	result := map[string]interface{}{}
	result["items"] = buildInfoObjects
	if count == len(buildInfoObjects) {
		result["comment"] = "当前编译为最新编译，所有分支均为最新提交！"
	} else {
		if failCount > 0 {
			result["comment"] = "部分分支检查失败，请重试。"
		} else {
			result["comment"] = "当前编译不是最新编译，部分分支不是最新提交！"
		}
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func GetLatestBranchVersion(project, branch string) (string, error) {
	var version string
	for i := 0; i <= 3; i++ {
		// url := fmt.Sprintf("/projects/%s/repository/branches/%s")
		var err error
		token := libs.Config.Buildfarm.Token
		if token == "" {
			return "", errors.New("未配置Token,请联系管理员")
		}
		buildProject := dproject.Response{}

		err = buildProject.FindEx("name", project)
		if err != nil {
			continue
		}

		if buildProject.Id == 0 {
			return "", errors.New("编译农场找不到这个项目，请联系管理员")
		}

		url := fmt.Sprintf("%s/api/%s/projects/%d/repository/branches/%s?private_token=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, buildProject.GitlabId, branch, token)
		req, err := http.NewRequest("GET", url, nil)
		if err != nil {
			logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
			continue
		}
		result, err := libs.HandlerRequest(req)
		if err != nil {
			logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
			continue
		}
		logging.DebugLogger.Debug(result, url)
		if result["commit"] != nil {
			version = result["commit"].(map[string]interface{})["id"].(string)
		}
		return version, nil
	}
	return version, errors.New("检查项目版本失败，请重试")
}

func GetMakeJobPackages(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	makeJob := dmakejob.MakeJob{}
	err := makeJob.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("get cron job by id err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	if makeJob.ID == 0 {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "未到找记录"))
		return
	}

	command := fmt.Sprintf(`find %s -name "*.rpm" -printf "%s\n"`, filepath.Join(libs.Config.Buildfarm.Archivepath, makeJob.TaskID), "%f")
	output, err := libs.ExecCommand(command)
	if err != nil {
		logging.ErrorLogger.Errorf("get cron job by id err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": command + err.Error()}, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, strings.Split(output, "\n"), response.NoErr.Msg))
	return
}
