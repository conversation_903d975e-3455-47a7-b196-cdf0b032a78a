package buildfarm

import (
	"fmt"
	"irisAdminApi/application/controllers/ip"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/buildfarm/dcpldbuild"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

var JobQueue = make(chan string)
var JobLimit = make(chan string, 4)

func CreateCpldBuildJob(ctx iris.Context) {
	white, _ := ip.CheckIP(ctx)

	if white {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "只允许从云办公外上传"))
		return
	}

	f, fh, err := ctx.FormFile("file")
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	defer f.Close()

	userID, _ := dao.GetAuthId(ctx)
	jobID := libs.GetUniqueID()
	createdAt := time.Now()

	upload := filepath.Join(libs.Config.Buildfarm.Cpld.Upload, createdAt.Format("20060102"), jobID)
	os.MkdirAll(upload, 0750)
	os.Chmod(upload, 0750)
	//先执行文件上传，文件上传成功后执行创建申请单操作

	_, err = ctx.SaveFormFile(fh, filepath.Join(upload, fh.Filename))
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	md5, err := libs.GetFileMd5(filepath.Join(upload, fh.Filename))
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	request := &dcpldbuild.Request{}
	if err := ctx.ReadForm(request); err != nil {
		logging.ErrorLogger.Errorf("create gitjob read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		logging.ErrorLogger.Errorf("create gitjob read json err ", strings.Join(errs, ";"))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	ext := filepath.Ext(fh.Filename)
	outputFileName := strings.ReplaceAll(fh.Filename, ext, "_install"+ext)
	job := dcpldbuild.CpldBuildJob{}
	if err := job.Create(map[string]interface{}{
		"JobID":          jobID,
		"InputFileName":  fh.Filename,
		"InputFileMD5":   md5,
		"CronMakeJobID":  request.CronMakeJobID,
		"SupportList":    request.SupportList,
		"version":        request.Version,
		"OutputFileName": outputFileName,
		"OutputFileMD5":  "",
		"UserID":         userID,
		"Status":         3,
		"CreatedAt":      time.Now(),
		"UpdatedAt":      time.Now(),
	}); err != nil {
		logging.ErrorLogger.Error(err)
		os.RemoveAll(upload)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	JobQueue <- jobID
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetCpldBuildJobs(ctx iris.Context) {
	uId := uint(0)

	userID := ctx.FormValue("user_id")
	if userID == "" {
		userID = fmt.Sprintf("%v", uId)
	}

	jobID := ctx.FormValue("job_id")
	name := ctx.FormValue("name")
	md5 := ctx.FormValue("md5")

	start := ctx.FormValue("start")
	end := ctx.FormValue("end")
	status := ctx.FormValue("status")

	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dcpldbuild.All(userID, jobID, name, md5, start, end, status, sort, orderBy, page, pageSize)
	if err != nil {
		logging.ErrorLogger.Errorf("find cpld build job get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func RestartQueueJobs() {
	go CpldBuildWorker()
	queueJobs, err := dcpldbuild.FindAllQueueJobs()
	if err != nil {
		logging.ErrorLogger.Errorf("find queue cpld build job get err ", err)
		return
	}
	for _, queueJob := range queueJobs {
		JobQueue <- queueJob.JobID
	}
}

func UpdateCpldBuildJob(job *dcpldbuild.CpldBuildJob, status uint, outputFileMD5 string) {
	err := job.Update(job.ID, map[string]interface{}{"Status": status, "OutputFileMD5": outputFileMD5})
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
	}
}

func CpldBuildWorker() {
	for {
		select {
		case jobID := <-JobQueue:

			fmt.Println("start", jobID)
			JobLimit <- jobID
			go func(jobID string) {
				defer func() {
					<-JobLimit
				}()

				job := dcpldbuild.CpldBuildJob{}
				err := job.FindEx("job_id", jobID)
				if err != nil {
					logging.ErrorLogger.Error("find cpld build job err", err.Error())
					return
				}
				UpdateCpldBuildJob(&job, 0, "")

				workDir := job.UploadDir()
				tmpDir := filepath.Join(workDir, "tmp")
				if err := os.MkdirAll(tmpDir, 0755); err != nil {
					UpdateCpldBuildJob(&job, 2, "")
					return
				}
				os.Chmod(tmpDir, 0755)

				output := job.OutputDir()
				if err := os.MkdirAll(output, 0755); err != nil {
					UpdateCpldBuildJob(&job, 2, "")
					return
				}
				os.Chmod(output, 0750)

				f, err := os.OpenFile(filepath.Join(workDir, job.JobID+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
				if err != nil {
					logging.ErrorLogger.Errorf("create log file get err", err)
					return
				}
				defer f.Close()

				// 下载mkinstpkg
				fmt.Println(job.CronMakeJob)
				command := fmt.Sprintf(
					`cd %s && wget %s && chmod +x mkinstpkg && ./mkinstpkg -t firmware -c "%s" --version "%s" --supp-list="%s" --output "%s"`,
					tmpDir,
					fmt.Sprintf("http://************:9090/output/%s/%s/host-mkinstpkg/mkinstpkg", job.CronMakeJob.JobId, job.CronMakeJob.Cpu.Cpu),
					filepath.Join(workDir, job.InputFileName),
					job.Version,
					job.SupportList,
					filepath.Join(output, job.OutputFileName),
				)
				f.WriteString(command)
				stdout, err := libs.ExecCommand(command)
				if err != nil {
					UpdateCpldBuildJob(&job, 2, "")
					f.WriteString(stdout)
					f.WriteString(err.Error() + "\n")
					return
				}

				f.WriteString(stdout + "\n")
				md5, err := libs.GetFileMd5(filepath.Join(output, job.OutputFileName))
				if err != nil {
					logging.ErrorLogger.Errorf("get output file md5 err ", err)
				}
				UpdateCpldBuildJob(&job, 1, md5)
			}(jobID)
		default:
			time.Sleep(10 * time.Second)
		}
	}
}

func GetCpldBuildJob(ctx iris.Context) {
	id, _ := dao.GetId(ctx)

	job := dcpldbuild.CpldBuildJob{}

	err := job.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("find sig job get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, job, response.NoErr.Msg))
	return
}

func DownloadCpldFile(ctx iris.Context) {
	id, _ := dao.GetId(ctx)

	job := dcpldbuild.CpldBuildJob{}

	err := job.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("find sig job get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.SendFile(filepath.Join(job.OutputDir(), job.OutputFileName), job.OutputFileName)
	return
}

func DownloadCpldBuildLog(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	job := dcpldbuild.CpldBuildJob{}

	err := job.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("find cpld build job get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	data, err := os.ReadFile(filepath.Join(job.UploadDir(), job.JobID+".log"))
	if err != nil {
		logging.ErrorLogger.Errorf("open cpld build job log get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.ContentType("text/plain")
	ctx.WriteString(string(data))
	return
}
