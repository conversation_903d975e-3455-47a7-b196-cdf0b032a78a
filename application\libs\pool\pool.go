package pool

import (
	"context"
	"fmt"
	"io/ioutil"
	"irisAdminApi/application/libs"

	pool "github.com/jolestar/go-commons-pool/v2"
	"golang.org/x/crypto/ssh"
)

var ctx = context.Background()

func SSHClientV2(user, host string, port int) (*ssh.Client, error) {
	var (
		auth         []ssh.AuthMethod
		addr         string
		clientConfig *ssh.ClientConfig
		client       *ssh.Client
		err          error
	)

	auth = make([]ssh.AuthMethod, 0)

	if len(libs.Config.Rsa.Privatekey) > 0 {
		key, err := ioutil.ReadFile(libs.Config.Rsa.Privatekey)
		if err != nil {
			return nil, err
		}
		// Create the Signer for this private key.
		signer, err := ssh.ParsePrivateKey(key)
		if err != nil {
			return nil, err
		}
		auth = append(auth, ssh.PublicKeys(signer))
	}

	clientConfig = &ssh.ClientConfig{
		User: user,
		Auth: auth,
		// Timeout:             30 * time.Second,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	}

	// connet to ssh
	addr = fmt.Sprintf("%s:%d", host, port)

	if client, err = ssh.Dial("tcp", addr, clientConfig); err != nil {
		return nil, err
	}

	return client, nil
}

type SSHClientPoolObject struct {
	client *ssh.Client
}

type SSHClientV2Factory struct {
	user string
	host string
	port int
}

func (f *SSHClientV2Factory) MakeObject(ctx context.Context) (*pool.PooledObject, error) {
	client, err := SSHClientV2(f.user, f.host, f.port)
	return pool.NewPooledObject(
			&SSHClientPoolObject{
				client: client,
			}),
		err
}

func (f *SSHClientV2Factory) DestroyObject(ctx context.Context, object *pool.PooledObject) error {
	// do destroy
	return object.Object.(*SSHClientPoolObject).client.Close()
}

func (f *SSHClientV2Factory) ValidateObject(ctx context.Context, object *pool.PooledObject) bool {
	// do validate
	_, err := object.Object.(*SSHClientPoolObject).client.NewSession()
	return err == nil
}

func (f *SSHClientV2Factory) ActivateObject(ctx context.Context, object *pool.PooledObject) error {
	// do activate
	return nil
}

func (f *SSHClientV2Factory) PassivateObject(ctx context.Context, object *pool.PooledObject) error {
	// do passivate
	return nil
}

func Example_customFactory() {
	ctx := context.Background()
	p := pool.NewObjectPoolWithDefaultConfig(ctx, &SSHClientV2Factory{})
	p.Config.MaxTotal = 100

	obj1, err := p.BorrowObject(ctx)
	if err != nil {
		panic(err)
	}

	o := obj1.(*SSHClientPoolObject)
	fmt.Println(o.client)

	err = p.ReturnObject(ctx, obj1)
	if err != nil {
		panic(err)
	}

	// Output: 1
}
