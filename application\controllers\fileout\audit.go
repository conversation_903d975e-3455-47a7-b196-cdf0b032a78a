package fileout

import (
	"fmt"
	"net"
	"strconv"
	"strings"

	"irisAdminApi/application/controllers/ip"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/fileout"
	"irisAdminApi/service/dao/fileout/dapproval"
	"irisAdminApi/service/dao/user/duser"

	"github.com/kataras/iris/v12"
)

func GetRemoteAddr(ctx iris.Context) string {
	addr := ctx.GetHeader("X-Real-Ip")
	if len(addr) == 0 {
		addr := strings.TrimSpace(ctx.Request().RemoteAddr)
		if addr != "" {
			// if addr has port use the net.SplitHostPort otherwise(error occurs) take as it is
			if ip, _, err := net.SplitHostPort(addr); err == nil {
				return ip
			}
		}
	} else {
		if ip, _, err := net.SplitHostPort(addr); err == nil {
			return ip
		}
	}
	return addr
}

func AuditAllApprovals(ctx iris.Context) {
	// var ip = Ip{}
	// addr := GetRemoteAddr(ctx)
	// if addr == "" {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }

	// addr = strings.Join(strings.Split(addr, ".")[:3], ".")
	// err := easygorm.GetEasyGormDb().Model(&fileout.AllowAuditIp{}).Where("ip like ?", addr+"%").Find(&ip).Error
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }

	// if ip.ID == 0 {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "不在允许的IP范围，请切换至云办公环境"))
	// 	return
	// }
	permit, err := ip.CheckIP(ctx)
	if !permit {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	var count int64
	var approvals []*dapproval.ListResponse

	db := easygorm.GetEasyGormDb().Model(dapproval.Model())

	db = db.Where("audit = ?", true)

	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err = db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create filedetail get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create filedetail get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	dapproval.FormatResponse(approvals, false)
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func AuditAllFragments(ctx iris.Context) {
	// var ip = Ip{}
	// addr := GetRemoteAddr(ctx)
	// if addr == "" {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }

	// addr = strings.Join(strings.Split(addr, ".")[:3], ".")
	// err := easygorm.GetEasyGormDb().Model(&fileout.AllowAuditIp{}).Where("ip like ?", addr+"%").Find(&ip).Error
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }

	// if ip.ID == 0 {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "不在允许的IP范围，请切换至云办公环境"))
	// 	return
	// }

	permit, err := ip.CheckIP(ctx)
	if !permit {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	var count int64
	var fragments []*FragmentsResponse

	db := easygorm.GetEasyGormDb().Model(&fileout.Fragment{})

	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err = db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("count fragment get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&fragments).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create filedetail get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	FormatFragmentResponse(fragments)
	list := map[string]interface{}{"items": fragments, "total": count, "limit": pageSize}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func FormatFragmentResponse(fragments []*FragmentsResponse) {
	for _, fragment := range fragments {
		var user duser.ApprovalResponse
		user.Find(fragment.UserId)
		fragment.User = user
	}
}
