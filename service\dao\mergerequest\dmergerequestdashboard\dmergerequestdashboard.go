package dmergerequestdashboard

import (
	"fmt"
	"reflect"
	"sort"
	"strconv"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/models/mergerequest"
)

const ModelName = "MR表单统计"

/*
SELECT
	m.release_project AS '项目',
	m.work_package AS '工作包',
	w.ported_code_quantity AS '移植代码量(正式)',
	w.code_quantity AS '新增代码量' ,
	w.temporary_ported_code_quantity AS '移植代码量(临时)',
	w.total_code_quantity AS '总计代码量',
	Round(SUM(IF( m.portable = 1, m.code_quantity_add, 0))/1000, 2) AS '新增移植代码量(已合并)',
	round(SUM(IF(m.portable = 1, m.code_quantity_remove, 0))/1000, 2) AS '删除移植代码量(已合并)',
	round(SUM(IF(m.portable = 0, m.code_quantity_add, 0))/1000, 2) AS '新增代码量(已合并)',
	round(SUM(IF(m.portable=0, m.code_quantity_remove, 0))/1000, 2) AS '删除代码量(已合并)',
	round((SUM(IF(m.portable = 1 and status = 3, m.code_quantity_add, 0)) - SUM(IF(m.portable=1 and status = 3 , m.code_quantity_remove, 0)))/1000/w.ported_code_quantity*100, 2) as '工作包移植进度(预估)',
	round((SUM(IF(m.portable = 0 and status = 3, m.code_quantity_add, 0)) - SUM(IF(m.portable=0 and status = 3, m.code_quantity_remove, 0)))/1000/w.code_quantity*100, 2) as '工作包新增进度(预估)',
	round((SUM(IF(status = 3, m.code_quantity_add, 0)) - SUM(IF(status = 3, m.code_quantity_remove, 0)))/1000/w.code_quantity*100, 2) as '工作包总体进度(预估)',

	COUNT(IF(m.status =3, m.id, null)) AS '已合并',
	COUNT(IF(m.status =2, m.id, null)) AS '已评审',
	COUNT(IF(m.status =1, m.id, null)) AS '评审中',
	COUNT(m.id) AS 合计MR表单数,
	round(avg(timestampdiff(Hour,m.created_at, m.updated_at)), 1) AS "合并平均用时",
	MIN(m.updated_at) AS 集成测试开始时间
FROM merge_requests m, merge_request_work_packages w WHERE m.STATUS = 3 AND m.work_package_id = w.id group BY m.release_project, m.work_package,w.id ORDER BY m.release_project desc
*/

type WorkPackageSummary struct {
	ReleaseProject     string  `json:"release_project" xlsx:"项目" idx:"1"`
	ReleaseProjectID   uint    `json:"release_project_id"`
	WorkPackage        string  `json:"work_package" xlsx:"工作包"`
	WorkPackageID      uint    `json:"work_package_id"`
	Requirement        string  `json:"requirement" xlsx:"项目需求" idx:"2"`
	WorkGroup          string  `json:"work_group" xlsx:"所属专业组" idx:"3"`
	CodeQuantity       float32 `gorm:"not null" json:"code_quantity" xlsx:"新增代码量" idx:"3"`
	PortedCodeQuantity float32 `gorm:"not null" json:"ported_code_quantity" xlsx:"移植代码量(正式)" idx:"4"`
	// TemporaryPortedCodeQuantity float32 `gorm:"not null" json:"temporary_ported_code_quantity" xlsx:"移植代码量(临时)"`
	TotalCodeQuantity         float32 `gorm:"not null" json:"total_code_quantity" xlsx:"总计代码量" idx:"5"`
	PortedCodeQuantityAdd     float32 `gorm:"not null" json:"ported_code_quantity_add" xlsx:"新增移植代码量(已合并)" idx:"6"`
	PortedCodeQuantityRemove  float32 `gorm:"not null" json:"ported_code_quantity_remove" xlsx:"删除移植代码量(已合并)" idx:"7"`
	PortedCodeQuantityPercent float32 `gorm:"not null" json:"ported_code_quantity_percent" xlsx:"工作包移植进度(预估)" idx:"8"`
	CodeQuantityAdd           float32 `gorm:"not null" json:"code_quantity_add" xlsx:"新增代码量(已合并)" idx:"9"`
	CodeQuantityRemove        float32 `gorm:"not null" json:"code_quantity_remove" xlsx:"删除代码量(已合并)" idx:"10"`
	CodeQuantityPercent       float32 `gorm:"not null" json:"code_quantity_percent" xlsx:"工作包进度(预估)" idx:"11"`
	TotalCodeQuantityPercent  float32 `gorm:"not null" json:"total_code_quantity_percent" xlsx:"工作包总体进度(预估)" idx:"12"`
	MergedCount               int     `json:"merged_count" xlsx:"已合并MR数量" idx:"13"`
	ApprovedCount             int     `json:"approved_count" xlsx:"已评审MR数量" idx:"14"`
	CreatedCount              int     `json:"created_count" xlsx:"评审中MR数量" idx:"15"`
	OverTimeMergedCount       int     `json:"over_time_merged_count" xlsx:"超期已合并MR" idx:"16"`
	OverTimeUnmergedCount     int     `json:"over_time_unmerged_count" xlsx:"超期未合并MR" idx:"17"`
	TotalCount                int     `json:"total_count" xlsx:"总计MR数量" idx:"18"`
	AvgHourCostToMerged       float32 `json:"avg_hour_cost_to_merged" xlsx:"MR合并平均用时" idx:"19"`
	FirstMergedAt             string  `json:"first_merged_at" xlsx:"首次MR合并时间" idx:"20"`
	LastMergedAt              string  `json:"last_merged_at" xlsx:"末次MR合并时间" idx:"21"`
	ClosedBugCount            int     `json:"closed_bug_count" xlsx:"已关闭BUG数量" idx:"22"`
	UnclosedBugCount          int     `json:"unclosed_bug_count" xlsx:"未关闭BUG数量" idx:"23"`
	TotalBugCount             int     `json:"total_bug_count" xlsx:"总计BUG数量" idx:"22"`
}

func (a *WorkPackageSummary) ModelName() string {
	return ModelName
}

func Model() *mergerequest.MergeRequest {
	return &mergerequest.MergeRequest{}
}

func (s *WorkPackageSummary) All(projectID string, workPackageID uint, pstlID uint, requirement, workGroup, order, by string) ([]*WorkPackageSummary, error) {
	items := []*WorkPackageSummary{}
	selects := []string{
		"m.release_project AS release_project",
		"m.release_project_id AS release_project_id",
		"w.requirement as requirement",
		"w.work_group as work_group",
		"m.work_package AS work_package",
		"w.pstl_id as pstl_id",
		"m.work_package_id AS work_package_id",
		"w.ported_code_quantity AS ported_code_quantity",
		"w.code_quantity AS code_quantity",
		// "w.temporary_ported_code_quantity AS temporary_ported_code_quantity",
		"w.total_code_quantity AS total_code_quantity",
		"round(SUM(IF(m.portable = 1 and m.status = 3, m.code_quantity_add * m.discount, 0))/1000, 3) AS ported_code_quantity_add",
		"round(SUM(IF(m.portable = 1 and m.status = 3, m.code_quantity_remove * m.discount, 0))/1000, 3) AS ported_code_quantity_remove",
		"round(SUM(IF(m.portable = 0 and m.status = 3, m.code_quantity_add * m.discount, 0))/1000, 3) AS code_quantity_add",
		"round(SUM(IF(m.portable = 0 and m.status = 3, m.code_quantity_remove * m.discount, 0))/1000, 3) AS code_quantity_remove",
		"round((SUM(IF(m.portable = 1 and status = 3, m.code_quantity_add * m.discount, 0)) - SUM(IF(m.portable=1 and status = 3 , m.code_quantity_remove * m.discount, 0)))/1000/w.ported_code_quantity*100, 2) as ported_code_quantity_percent",
		"round((SUM(IF(m.portable = 0 and status = 3, m.code_quantity_add * m.discount, 0)) - SUM(IF(m.portable=0 and status = 3, m.code_quantity_remove * m.discount, 0)))/1000/w.code_quantity*100, 2) as code_quantity_percent",
		"round((SUM(IF(status = 3, m.code_quantity_add * m.discount, 0)) - SUM(IF(status = 3, m.code_quantity_remove * m.discount, 0)))/1000/w.code_quantity*100, 2) as total_code_quantity_percent",
		"COUNT(IF(m.status = 3, m.id, null)) AS merged_count",
		"COUNT(IF(m.status = 2, m.id, null)) AS approved_count",
		"COUNT(IF(m.status = 1, m.id, null)) AS created_count",
		"COUNT(IF(m.created_at < getWorkDay(m.updated_at, -3) and status = 3, m.id, null)) AS over_time_merged_count",
		"COUNT(IF(m.status in (1, 2) and m.created_at < getWorkDay(now(), -3), m.id, null)) AS over_time_unmerged_count",
		"COUNT(m.id) AS total_count",
		"round(avg(if(m.status = 3, timestampdiff(Hour,m.created_at, m.updated_at), null)), 1) AS avg_hour_cost_to_merged",
		"MIN(if(m.status = 3, m.updated_at, null)) AS first_merged_at",
		"MAX(if(m.status = 3, m.updated_at, null)) AS last_merged_at",
		`COUNT(IF(b.bug_state LIKE 'CLOSED%' OR b.bug_state LIKE 'DENIAL%' OR b.bug_state IN ("DELAY", "GIVEUP"), b.bug_id, null)) AS closed_bug_count`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP"), b.bug_id, null)) AS unclosed_bug_count`,
		"COUNT(m.bug_id) AS total_bug_count",
	}

	db := easygorm.GetEasyGormDb().Table("merge_requests as m").Joins("LEFT JOIN bugs b  ON b.bug_id = m.bug_id LEFT JOIN merge_request_work_packages w ON w.id = m.work_package_id").Select(selects).Group("m.release_project_id, m.work_package_id, m.release_project, m.work_package, w.id").Order(fmt.Sprintf("%s %s", by, order))
	where := easygorm.GetEasyGormDb().Where("m.STATUS in (1, 2, 3)")
	if len(projectID) > 0 || projectID != "0" {
		where = where.Where("m.release_project_id = ? or m.release_project = ?", projectID, projectID)
	}
	if workPackageID > 0 {
		where = where.Where("m.work_package_id = ?", workPackageID)
	}
	if len(requirement) > 0 {
		db = db.Having("requirement = ?", requirement)
	}
	if len(workGroup) > 0 {
		db = db.Having("work_group = ?", workGroup)
	}
	if pstlID > 0 {
		db = db.Having("pstl_id = ?", pstlID)
	}
	err := db.Where(where).Find(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func (s *WorkPackageSummary) ExportWorkPackageSummary(items []*WorkPackageSummary) ([]interface{}, [][]interface{}) {
	xt := reflect.TypeOf(s)
	// xv := reflect.ValueOf(s)

	rows := [][]interface{}{}
	headers := []interface{}{}
	for i := 0; i < xt.Elem().NumField(); i++ {
		head, ok := xt.Elem().Field(i).Tag.Lookup("xlsx")
		if ok {
			headers = append(headers, head)
		}
	}
	rows = append(rows, headers)

	for _, e := range items {
		cells := []interface{}{}
		xv := reflect.ValueOf(e)
		for i := 0; i < xv.Elem().NumField(); i++ {
			_, ok := xt.Elem().Field(i).Tag.Lookup("xlsx")
			if ok {
				cells = append(cells, xv.Elem().Field(i).Interface())
			}

		}
		rows = append(rows, cells)
	}
	return headers, rows
}

type Column struct {
	Idx   int    `json:"idx"`
	Key   string `json:"key"`
	Label string `json:"label"`
}

func (s *WorkPackageSummary) GetColumns() ([]*Column, error) {
	xt := reflect.TypeOf(s)

	columns := []*Column{}

	for i := 0; i < xt.Elem().NumField(); i++ {
		key, ok1 := xt.Elem().Field(i).Tag.Lookup("json")
		label, ok2 := xt.Elem().Field(i).Tag.Lookup("xlsx")
		_idx, ok3 := xt.Elem().Field(i).Tag.Lookup("idx")

		if ok1 && ok2 && ok3 {
			idx, err := strconv.Atoi(_idx)
			if err != nil {
				return columns, err
			}
			columns = append(columns, &Column{
				Idx:   idx,
				Key:   key,
				Label: label,
			})
		}
	}
	sort.SliceStable(columns, func(i, j int) bool {
		if columns[i].Idx < columns[j].Idx {
			return true
		}
		return false
	})
	return columns, nil
}
