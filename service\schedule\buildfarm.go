package schedule

import (
	"time"

	"irisAdminApi/application/controllers/buildfarm"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
)

func RunCheckQueue() {
	buildfarm.RestartRuning()
	t := time.NewTicker(60 * time.Second)
	go func() {
		// for {
		// 	select {
		// 	case <-t.C:
		// 		buildfarm.CheckQueue()
		// 		// buildfarm.CheckCoverityQueue()
		// 		buildfarm.CheckQueueV3()
		// 	}
		// }
		for range t.C {
			buildfarm.CheckQueue()
			// buildfarm.CheckCoverityQueue()
			buildfarm.CheckQueueV3()
		}
	}()
}

func RunCrontab() {
	libs.Cron.Start()
	buildfarm.AddNewCronTab()
	buildfarm.RestoreRunningJob()
	// _, err := Cron.AddFunc("0 0 * * *", func() { buildfarm.UpdateProjectsInfo() })
	// if err != nil {
	// 	logging.ErrorLogger.Error("add bug sync cron err ", err)
	// }
	// _, err := Cron.AddFunc("1 0 * * *", func() { buildfarm.CheckMergeWorker() })
	// if err != nil {
	// 	logging.ErrorLogger.Error("add bug sync cron err ", err)
	// }
	_, err := Cron.AddFunc("0 5 * * 1,3,5", func() { buildfarm.WarningAnalysisWorker() })
	if err != nil {
		logging.ErrorLogger.Error("add warning analysis cron err ", err)
	}

	_, err = Cron.AddFunc("0 0,12 * * *", func() { buildfarm.CleanWorker() })
	if err != nil {
		logging.ErrorLogger.Error("add clean worker cron err ", err)
	}
}
