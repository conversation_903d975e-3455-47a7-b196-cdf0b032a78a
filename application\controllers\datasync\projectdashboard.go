package datasync

import (
	"fmt"
	"time"

	"irisAdminApi/application/controllers/openfeishu"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/bugsync/dbug"
	"irisAdminApi/service/dao/datasync/dpmscaseinfo"
	"irisAdminApi/service/dao/datasync/dresourceclean"
	"irisAdminApi/service/dao/mergerequest/dmergerequestdashboard"
)

func SyncWorkPackageDataToFeishu(project, appToken, tableID string) {
	summary := dmergerequestdashboard.WorkPackageSummary{}
	items, err := summary.All(project, 0, 0, "", "", "desc", "release_project")
	if err != nil {
		logging.ErrorLogger.Error(err)
		return
	}
	openfeishu.DeleteTableRecordDataV4(appToken, tableID, []string{"项目"})

	// 批量同步到线上
	now := time.Now()
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, item := range batchData {
				rec := map[string]interface{}{
					"项目":           item.ReleaseProject,
					"工作包":          item.WorkPackage,
					"项目需求":         item.Requirement,
					"所属专业组":        item.WorkGroup,
					"新增代码量":        item.CodeQuantity,
					"移植代码量(正式)":    item.PortedCodeQuantity,
					"总计代码量":        item.TotalCodeQuantity,
					"新增移植代码量(已合并)": item.PortedCodeQuantityAdd - item.PortedCodeQuantityRemove,
					"删除移植代码量(已合并)": item.PortedCodeQuantityRemove,
					"工作包移植进度(预估)":  item.PortedCodeQuantityPercent,
					"新增代码量(已合并)":   item.CodeQuantityAdd - item.CodeQuantityRemove,
					"删除代码量(已合并)":   item.CodeQuantityRemove,
					"工作包进度(预估)":    item.CodeQuantityPercent,
					"工作包总体进度(预估)":  item.TotalCodeQuantityPercent,
					"已合并MR数量":      item.MergedCount,
					"已评审MR数量":      item.ApprovedCount,
					"评审中MR数量":      item.CreatedCount,
					"超期已合并MR":      item.OverTimeMergedCount,
					"超期未合并MR":      item.OverTimeUnmergedCount,
					"总计MR数量":       item.TotalCount,
					"MR合并平均用时":     item.AvgHourCostToMerged,
					"首次MR合并时间":     item.FirstMergedAt,
					"末次MR合并时间":     item.LastMergedAt,
					"已关闭BUG数量":     item.ClosedBugCount,
					"未关闭BUG数量":     item.UnclosedBugCount,
					"总计BUG数量":      item.TotalBugCount,
					"更新时间":         now.UnixMilli(),
				}

				records = append(records, rec)
			}

			tableRecordResp, err := openfeishu.BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("批量创建记录失败: %s", err.Error(), string(tableRecordResp.RawBody))
				continue
			}
		}
	}
}

func SyncResourceDataByProject(project, appToken, tableID string) {
	// 批量删除线上数据
	openfeishu.DeleteTableRecordDataV4(appToken, tableID, []string{"项目"})
	// 获取本地数据
	items, err := dresourceclean.GetAllMonthResourceDetailDataWithWorkpacket([]string{project})
	if err != nil {
		logging.ErrorLogger.Errorf("获取资源原始数据失败: %s", err.Error())
		return
	}
	// 批量同步到线上
	now := time.Now()
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, item := range batchData {
				reportTime, err := time.Parse("2006-01-02", item.ReportDate)
				if err != nil {
					logging.ErrorLogger.Error(err)
				}
				rec := map[string]interface{}{
					"ID":       item.ID,
					"项目":       item.ProjectName,
					"上班时间(小时)": item.WorkTime,
					"加班时间(小时)": item.AddTime,
					"总时间(小时)":  item.TotalTime,
					"数据过期时间":   now.Add(12 * time.Hour).UnixMilli(),
					"工作包":      item.WorkpacketName,
					"日期":       reportTime.UnixMilli(),
				}

				if len(item.DepartmentName) > 0 {
					rec["部门"] = item.DepartmentName
				}
				if len(item.GroupName) > 0 {
					rec["专业组"] = item.GroupName
				}
				if len(item.UserName) > 0 {
					rec["人员"] = item.UserName
				}
				if len(item.WorkClassName) > 0 {
					rec["工作大类"] = item.WorkClassName
				}
				if len(item.StageName) > 0 {
					rec["项目阶段"] = item.StageName
				}
				if len(item.ActivityName) > 0 {
					rec["项目工序"] = item.ActivityName
				}
				if len(item.TaskName) > 0 {
					rec["任务名称"] = item.TaskName
				}

				rec["年"] = item.Year

				rec["月"] = item.Month
				records = append(records, rec)
			}

			tableRecordResp, err := openfeishu.BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("批量创建记录失败: %s", err.Error(), string(tableRecordResp.RawBody))
				continue
			}
			if len(tableRecordResp.Data.Records) > 0 {
				fmt.Println(tableID)
			}
		}
	}
}

func parseAndConvertToTimestamp(dateStr string) (int64, error) {
	// 解析字符串为 time.Time 类型
	t, err := time.Parse(time.RFC3339, dateStr)
	if err != nil {
		return 0, err
	}

	// 将 time.Time 类型转换为 Unix 时间戳
	return t.Unix(), nil
}

func SyncBugDataByProject(project, appToken, tableID string) {
	// 获取本地数据

	bugs, err := dbug.FinBugByProjectSyncToFeishu(project)
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		return
	}
	fmt.Println("bugs:", len(bugs))
	openfeishu.DeleteTableRecordDataV4(appToken, tableID, []string{"操作系统"})

	limit := 500
	for loop := 0; true; loop++ {
		start := loop * limit
		if start > len(bugs) {
			break
		}
		end := (loop + 1) * limit
		if len(bugs) <= (loop+1)*limit {
			end = len(bugs)
		}
		records := []map[string]interface{}{}

		for _, bug := range bugs[start:end] {
			rec := map[string]interface{}{
				"BUGID": map[string]interface{}{
					"text": fmt.Sprintf("%d", bug.BugID),
					"link": "http://bugs.ruijie.com.cn/bug_switch/bug/main?bugId=" + fmt.Sprintf("%d", bug.BugID),
				},
				"状态":    bug.BugState,
				"BUG简介": bug.BugSummary,
				"操作系统":  bug.BugOS,
			}
			if len(bug.BugPriority) > 0 {
				rec["严重性"] = bug.BugPriority
			}
			if len(bug.BugSeverity) > 0 {
				rec["优先级"] = bug.BugSeverity
			}
			switch bug.BugRepro {
			case 1:
				rec["重复性"] = "必现"
			case 2:
				rec["重复性"] = "有时重现"
			case 3:
				rec["重复性"] = "未尝试重现"
			case 4:
				rec["重复性"] = "尝试单未重现"
			}
			if len(bug.BugSubmitter) > 0 {
				rec["提交者"] = bug.BugSubmitter
			}
			if len(bug.BugSubmitterGroup) > 0 {
				rec["提交人专业组"] = bug.BugSubmitterGroup
			}
			if len(bug.BugOwner) > 0 {
				rec["BUG负责人"] = bug.BugOwner
			}
			if len(bug.BugOwnerGroup) > 0 {
				rec["负责人专业组"] = bug.BugOwnerGroup
			}
			if len(bug.BugWorkpacketName) > 0 {
				rec["工作包名称"] = bug.BugWorkpacketName
			}
			if len(bug.BugTestCharger) > 0 {
				rec["测试负责人"] = bug.BugTestCharger
			}
			if !bug.BugCreatedAt.IsZero() {
				rec["提交时间"] = bug.BugCreatedAt.UnixMilli()
			}
			if !bug.BugUpdatedAt.IsZero() {
				rec["最后更新时间"] = bug.BugUpdatedAt.UnixMilli()
			}
			if len(bug.BugCbdAt) > 0 {
				CBDTimestamp, err := parseAndConvertToTimestamp(bug.BugCbdAt)
				if err != nil {
					logging.ErrorLogger.Errorf("GetbugDatas parseAndConvertToTimestamp error:%s", err.Error())
				}
				if CBDTimestamp > 0 {
					rec["CBD时间"] = CBDTimestamp * 1000
				}
			}
			if len(bug.BugCbtAt) > 0 {
				CBTTimestamp, err := parseAndConvertToTimestamp(bug.BugCbtAt)
				if err != nil {
					logging.ErrorLogger.Errorf("GetbugDatas parseAndConvertToTimestamp error:%s", err.Error())
				}
				if CBTTimestamp > 0 {
					rec["CBT时间"] = CBTTimestamp * 1000
				}
			}
			if !bug.BugResolvedAt.IsZero() {
				rec["解决时间"] = bug.BugResolvedAt.UnixMilli()
			}
			if len(bug.BugSource) > 0 {
				rec["BUG来源"] = bug.BugSource
			}
			if bug.SameBugID > 0 {
				rec["镜像BugID"] = map[string]interface{}{"text": fmt.Sprintf("%d", bug.SameBugID), "link": "http://bugs.ruijie.com.cn/bug_switch/bug/main?bugId=" + fmt.Sprintf("%d", bug.SameBugID)}
			}
			if bug.MainBugID > 0 {
				rec["主BugID"] = map[string]interface{}{"text": fmt.Sprintf("%d", bug.MainBugID), "link": "http://bugs.ruijie.com.cn/bug_switch/bug/main?bugId=" + fmt.Sprintf("%d", bug.MainBugID)}
			}

			records = append(records, rec)

		}
		if len(records) > 0 {
			resp, err := openfeishu.BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("GetbugDatas batchCreate error:%s", err.Error(), string(resp.RawBody))
			}
		}

		time.Sleep(120 * time.Millisecond)
	}
}

func SyncCaseDataByProject(project, appToken, tableID string) {
	// 获取本地数据

	items, err := dpmscaseinfo.FindByProject(project)
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		return
	}

	openfeishu.DeleteTableRecordDataV4(appToken, tableID, []string{"项目"})

	limit := 500
	for loop := 0; true; loop++ {
		start := loop * limit
		if start > len(items) {
			break
		}
		end := (loop + 1) * limit
		if len(items) <= (loop+1)*limit {
			end = len(items)
		}
		records := []map[string]interface{}{}

		for _, item := range items[start:end] {
			rec := map[string]interface{}{
				"ID":    item.CaseID,
				"项目":    item.ProjectName,
				"产品":    item.ProductName,
				"版本":    item.Version,
				"用例名称":  item.CaseName,
				"用例包名称": item.CasePacketName,
				"项目阶段":  item.TestProjectStageName,
				"执行结果":  item.ExecutionResult,
				"失败原因":  item.FailReason,
				"执行人":   item.AssignUserName,
			}

			if item.ExecutionTime != nil {
				rec["执行时间"] = item.ExecutionTime.UnixMilli()
			}

			records = append(records, rec)
		}
		if len(records) > 0 {
			resp, err := openfeishu.BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("GetbugDatas batchCreate error:%s", err.Error(), string(resp.RawBody))
			}
		}

		time.Sleep(120 * time.Millisecond)
	}
}
