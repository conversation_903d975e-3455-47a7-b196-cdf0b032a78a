package datasync

import (
	"errors"
	"fmt"
	"strconv"
	"time"

	"irisAdminApi/application/controllers/openfeishu"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/datasync/dpmshardwareproject"
	"irisAdminApi/service/dao/datasync/dpmshardwareprojectmilestone"
)

func PmsHardwareProjectSyncWorker() error {
	// todo: 检查同步记录，获取同步时间，如果没有，从七天前开始，按小时同步
	// modifyDateArray := []string{}
	_url := "https://dataware.ruijie.com.cn/api/public/data-api/safe_product_hardware_project_base_info/list.data"
	// records, err := dsyncrecord.FindLastSuccessSyncRecord(_url)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("get last sync records", err.Error())
	// 	return err
	// }

	// if len(records) > 0 {
	// 	modifyDateArray = libs.GetDateRange(-365, records[0].MaxModifyDate)
	// } else {
	// 	modifyDateArray = libs.GetDateRange(-365)
	// }

	// if len(modifyDateArray) < 2 {
	// 	return nil
	// }

	page := 1
	rows := 1000

	for {
		data := map[string]string{
			"sid":           "YTUzYzRlOGNh",
			"minModifyDate": "",
			"maxModifyDate": "",
			"page":          strconv.Itoa(page),
			"rows":          strconv.Itoa(rows),
		}

		var result dpmshardwareproject.PmsHardwareProjectSyncResponse
		var errMsg dpmshardwareproject.PmsHardwareProjectSyncResponse
		resp, err := SyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(data).Get(_url)
		if err != nil {
			logging.ErrorLogger.Errorf("get resources error", err.Error())
			return err
		}
		if resp.IsSuccessState() {
			if result.State == "SUCCESS" {
				err := dpmshardwareproject.UpdateOrCreatePmsHardwareProjectTransaction(result.Data, _url, data, resp.Request.Method, result.State, result.Message)
				if err != nil {
					logging.ErrorLogger.Errorf("update or create resources error", err.Error())
					return err
				}
			} else {
				logging.ErrorLogger.Errorf("get resources failed", result.State, result.Message)
				return fmt.Errorf("get resources failed, %s, %s", result.State, result.Message)
			}
		} else {
			logging.ErrorLogger.Errorf("get resources unkown error")
			return errors.New("unkown error")
		}

		time.Sleep(5 * time.Second)
		if result.Total > rows*page {
			page++
		} else {
			break
		}
	}

	return nil
}

func PmsHardwareProjectMilestoneSyncWorker() error {
	// todo: 检查同步记录，获取同步时间，如果没有，从七天前开始，按小时同步
	// modifyDateArray := []string{}
	_url := "https://dataware.ruijie.com.cn/api/public/data-api/safe_product_hardware_milestone_info/list.data"
	// records, err := dsyncrecord.FindLastSuccessSyncRecord(_url)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("get last sync records", err.Error())
	// 	return err
	// }

	// if len(records) > 0 {
	// 	modifyDateArray = libs.GetDateRange(-365, records[0].MaxModifyDate)
	// } else {
	// 	modifyDateArray = libs.GetDateRange(-365)
	// }

	// if len(modifyDateArray) < 2 {
	// 	return nil
	// }

	page := 1
	rows := 1000

	for {
		data := map[string]string{
			"sid":           "MmViNDY5NmQ2",
			"minModifyDate": "",
			"maxModifyDate": "",
			"page":          strconv.Itoa(page),
			"rows":          strconv.Itoa(rows),
		}

		var result dpmshardwareprojectmilestone.PmsHardwareProjectMilestoneSyncResponse
		var errMsg dpmshardwareprojectmilestone.PmsHardwareProjectMilestoneSyncResponse
		resp, err := SyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(data).Get(_url)
		if err != nil {
			logging.ErrorLogger.Errorf("get resources error", err.Error())
			return err
		}
		if resp.IsSuccessState() {
			if result.State == "SUCCESS" {
				err := dpmshardwareprojectmilestone.UpdateOrCreatePmsHardwareProjectMilestoneTransaction(result.Data, _url, data, resp.Request.Method, result.State, result.Message)
				if err != nil {
					logging.ErrorLogger.Errorf("update or create resources error", err.Error())
					return err
				}
			} else {
				logging.ErrorLogger.Errorf("get resources failed", result.State, result.Message)
				return fmt.Errorf("get resources failed, %s, %s", result.State, result.Message)
			}
		} else {
			logging.ErrorLogger.Errorf("get resources unkown error")
			return errors.New("unkown error")
		}

		time.Sleep(5 * time.Second)
		if result.Total > rows*page {
			page++
		} else {
			break
		}
	}

	return nil
}

func SyncFeiShuHardwareProjectMilestoneDatasV4() {
	appToken := "ZfoVbWxqTaI09Rs3Ml1cRzRCnuf"
	tableID := "tblDZMukrx2WViup"

	// 获取本地数据

	milestoneData, err := dpmshardwareprojectmilestone.All()
	if err != nil {
		logging.ErrorLogger.Errorf("GetAnalysisDatas error:%s", err.Error())
		return
	}

	projectIdDataMap := map[int][]*dpmshardwareprojectmilestone.ListResponse{}

	for _, item := range milestoneData {
		if _, ok := projectIdDataMap[item.ProjectID]; ok {
			projectIdDataMap[item.ProjectID] = append(projectIdDataMap[item.ProjectID], item)
		} else {
			projectIdDataMap[item.ProjectID] = []*dpmshardwareprojectmilestone.ListResponse{item}
		}
	}

	records := []map[string]interface{}{}
	for _, items := range projectIdDataMap {
		rec := map[string]interface{}{
			"项目": items[0].ProjectName,
		}
		for _, item := range items {
			openfeishu.SetHardwareProjectMileStoneColumnAndValue(&rec, item.MilestoneName, item.Disabled, item.PlanDate, item.ActDate, item.Status)
		}
		records = append(records, rec)
		// 将数据按照批次插入飞书数据表,每批次500条
	}

	// 批量删除线上数据
	openfeishu.DeleteTableRecordDataV4(appToken, tableID, []string{"项目"})
	page := 1
	pageSize := 500

	for {
		start := (page - 1) * pageSize
		end := page * pageSize
		if end > len(records) {
			end = len(records)
		}

		tableRecordResp, err := openfeishu.BatchCreate(tableID, appToken, records[start:end])
		if err != nil {
			logging.ErrorLogger.Errorf("GetAnalysisDatas batchCreate error:%s", err.Error(), string(tableRecordResp.RawBody))
		}

		// 检查是否还有更多数据

		time.Sleep(300 * time.Millisecond)
		if page*pageSize > len(records) {
			break
		}
		page++
	}
}
