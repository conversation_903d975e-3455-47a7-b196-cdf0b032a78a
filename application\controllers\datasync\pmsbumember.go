package datasync

import (
	"errors"
	"fmt"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/datasync/dpmsbumember"
	"strconv"
	"time"
)

func PmsBuMemberSyncWorker() error {
	// todo: 检查同步记录，获取同步时间，如果没有，从七天前开始，按小时同步
	// modifyDateArray := []string{}
	_url := "https://dataware.ruijie.com.cn/api/public/data-api/safe_product_user_dept_info/list.data"

	page := 1
	rows := 1000

	for {
		data := map[string]string{
			"sid":           "NmRjMDA2NDg5",
			"minModifyDate": "",
			"maxModifyDate": "",
			"page":          strconv.Itoa(page),
			"rows":          strconv.Itoa(rows),
		}

		var result dpmsbumember.PmsBuMemberResponse
		var errMsg dpmsbumember.PmsBuMemberResponse

		resp, err := SyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(data).Get(_url)
		if err != nil {
			logging.ErrorLogger.Errorf("get pms work packet infos error", err.Error())
			return err
		}
		if resp.IsSuccessState() {
			if result.State == "SUCCESS" {
				err := dpmsbumember.UpdateOrCreatePmsBuMemberTransaction(result.Data, _url, data, resp.Request.Method, result.State, result.Message)
				if err != nil {
					logging.ErrorLogger.Errorf("update or create pms work packet infos error", err.Error())
					return err
				}
			} else {
				logging.ErrorLogger.Errorf("get pms work packet infos failed", result.State, result.Message)
				return fmt.Errorf("get pms work packet infos failed, %s, %s", result.State, result.Message)
			}
		} else {
			logging.ErrorLogger.Errorf("get pms work packet infos unkown error")
			return errors.New("unkown error")
		}

		time.Sleep(5 * time.Second)
		if result.Total > rows*page {
			page++
		} else {
			break
		}
	}

	return nil
}
