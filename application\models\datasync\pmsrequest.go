package datasync

type PmsRequest struct {
	// models.ModelBase
	RequestID     int    `gorm:"primarykey; autoIncrement:false" json:"request_id" `
	ProjectID     int    `gorm:"not null;" json:"project_id" update:"1"`
	ProjectName   string `gorm:"not null; type:varchar(100)" json:"project_name" update:"1"`
	RequestName   string `gorm:"not null; type:varchar(100)" json:"request_name" update:"1"`
	RequestStatus string `gorm:"not null; type:varchar(100)" json:"request_status" update:"1"`
	PgttmUserName string `gorm:"not null; type:varchar(100)" json:"pgttm_user_name" update:"1"`
	ChangeType    string `gorm:"not null; type:varchar(100)" json:"change_type" update:"1"`
	Disabled      bool   `json:"disabled" update:"1"`
	Keyword       string `gorm:"type:varchar(100)" json:"keyword" update:"1"`
}
