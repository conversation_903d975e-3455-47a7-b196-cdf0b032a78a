package opensource

import (
	"fmt"
	"strconv"

	"github.com/kataras/iris/v12"

	"irisAdminApi/application/controllers/opensource/taskmanagers"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/opensource"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/opensource/dvulnerability"
	"irisAdminApi/service/dao/opensource/dvulnerabilityhistory"
	"irisAdminApi/service/dao/opensource/dvulnerabilitypermission"
	"irisAdminApi/service/dao/user/duser"
	transaction "irisAdminApi/service/transaction/opensource"
)

func ListVulnerabilityHistories(ctx iris.Context) {
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	componentName := ctx.FormValue("component_name")
	componentVersion := ctx.FormValue("component_version")
	productName := ctx.FormValue("product_name")
	productVersion := ctx.FormValue("product_version")
	vulnerabilityId, _ := strconv.Atoi(ctx.FormValue("vulnerability_id"))
	status, _ := strconv.Atoi(ctx.FormValue("status"))
	userId, _ := strconv.Atoi(ctx.FormValue("user_id"))
	processUserId, _ := strconv.Atoi(ctx.FormValue("process_user_id"))

	sort := ctx.FormValue("sort")
	orderBy := ctx.FormValue("orderBy")
	createdAt := ctx.FormValue("created_at")
	updatedAt := ctx.FormValue("updated_at")

	//authUserId, _ := dao.GetAuthId(ctx)
	//isAdmin := isAdminUser(authUserId)
	//uUserId := uint(userId)
	//if !isAdmin {
	//	if uUserId == 0 {
	//		uUserId = authUserId
	//	}
	//	if uUserId != authUserId {
	//		ctx.JSON(response.NewResponse(response.AuthActionErr.Code, nil, "你未拥有权限查询该用户的相关联的CVE漏洞处理列表，请联系管理员"))
	//		return
	//	}
	//}

	list, err := dvulnerabilityhistory.ListVulnerabilityHistories(page, pageSize,
		componentName, componentVersion, productName, productVersion, uint(vulnerabilityId), uint(status),
		sort, orderBy, createdAt, updatedAt, uint(userId), uint(processUserId))
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

type ProcessVulnerabilityReq struct {
	Status        uint   `json:"status"`
	Comment       string `json:"comment"`
	DelegatedUser uint   `json:"delegated_user"`
}

func doDelegateVulnerability(ctx iris.Context, req *ProcessVulnerabilityReq, vulnerabilityRes *dvulnerability.OpenSourceVulnerability, authUserId uint) {
	if req.DelegatedUser == 0 {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "delegated_user参数不能为空"))
		return
	}
	delegatedUserRes := &duser.User{}
	if err := delegatedUserRes.Find(req.DelegatedUser); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	} else if delegatedUserRes.ID == 0 {
		ctx.JSON(response.NewResponse(response.DataEmptyErr.Code, nil, fmt.Sprintf("该委派用户-<%d>不存在", req.DelegatedUser)))
		return
	}

	existPermissions, err := dvulnerabilitypermission.ListPermissionsByVulnerabilityIdAndUserId(vulnerabilityRes.ID, req.DelegatedUser)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if len(existPermissions) != 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "该用户已经是漏洞负责人或专业组长或委派人, 不需要委派"))
		return
	}

	permissionRes := &dvulnerabilitypermission.Response{}
	permissionRes.Type = opensource.DelegatedUserPermissionType
	permissionRes.UserID = req.DelegatedUser
	permissionRes.VulnerabilityID = vulnerabilityRes.ID
	permissionRes.ComponentID = vulnerabilityRes.ComponentID

	historyRes := &dvulnerabilityhistory.OpenSourceVulnerabilityHistory{}
	historyRes.Status = req.Status
	historyRes.UserID = authUserId
	historyRes.VulnerabilityID = vulnerabilityRes.ID
	historyRes.Comment = req.Comment
	historyRes.DelegatedUser = req.DelegatedUser
	err = transaction.DelegateVulnerability(permissionRes, historyRes)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	logging.DebugLogger.Debugf("漏洞委派成功: ID-<%d>, CVE-<%s>, Delegated-<%s>", vulnerabilityRes.ID, vulnerabilityRes.CveID, req.DelegatedUser)
	ctx.JSON(response.NewResponse(response.NoErr.Code, historyRes, response.NoErr.Msg))
	logging.DebugLogger.Debugf("发送委派消息: ID-<%d>, CVE-<%s>, Delegated-<%s>", vulnerabilityRes.ID, vulnerabilityRes.CveID, req.DelegatedUser)
	go taskmanagers.SendDelegatedEmail(vulnerabilityRes, delegatedUserRes.Username)
	return
}

func doCancelDelegateVulnerability(ctx iris.Context, req *ProcessVulnerabilityReq, vulnerabilityRes *dvulnerability.OpenSourceVulnerability, authUserId uint) {
	if req.DelegatedUser == 0 {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "delegated_user参数不能为空"))
		return
	}

	existPermission, err := dvulnerabilitypermission.FindByVulnerabilityIdAndUserIdAndType(
		vulnerabilityRes.ID, req.DelegatedUser, opensource.DelegatedUserPermissionType)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if existPermission == nil {
		ctx.JSON(response.NewResponse(response.DataEmptyErr.Code, nil, "该漏洞委派不存在, 不需要取消"))
		return
	}

	historyRes := &dvulnerabilityhistory.OpenSourceVulnerabilityHistory{}
	historyRes.Status = req.Status
	historyRes.UserID = authUserId
	historyRes.VulnerabilityID = vulnerabilityRes.ID
	historyRes.Comment = req.Comment
	historyRes.DelegatedUser = req.DelegatedUser

	err = transaction.CancelDelegateVulnerability(existPermission, historyRes)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	logging.DebugLogger.Debugf("漏洞取消委派成功: ID-<%d>, CVE-<%s>, Delegated-<%s>", vulnerabilityRes.ID, vulnerabilityRes.CveID, req.DelegatedUser)
	ctx.JSON(response.NewResponse(response.NoErr.Code, historyRes, response.NoErr.Msg))
	return
}

func CreateVulnerabilityHistory(ctx iris.Context) {
	vulnerabilityId, _ := ctx.Params().GetUint("id")

	req := &ProcessVulnerabilityReq{}
	if err := ctx.ReadJSON(req); err != nil {
		logging.ErrorLogger.Errorf("create vulnerability history read json err: %s", err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if req.Comment == "" {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "comment参数不能为空"))
		return
	}

	// 检查是否有权限执行操作
	authUserId, _ := dao.GetAuthId(ctx)
	isAdmin := isAdminUser(authUserId)
	if !isAllowedProcessVulnerability(ctx, vulnerabilityId, authUserId, isAdmin) {
		return
	}

	vulnerabilityRes, err := dvulnerability.FindById(vulnerabilityId)
	if err != nil {
		logging.ErrorLogger.Errorf("get opensource vulnerability get err %s", err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	} else if vulnerabilityRes == nil {
		ctx.JSON(response.NewResponse(response.DataEmptyErr.Code, nil, fmt.Sprintf("漏洞-<%d>不存在", vulnerabilityId)))
		return
	}

	if vulnerabilityRes.Status == opensource.VulnerabilityStatusConfirmed ||
		vulnerabilityRes.Status == opensource.VulnerabilityStatusIgnored {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, fmt.Sprintf("漏洞-<%d>已经处理完成", vulnerabilityId)))
		return
	}

	switch req.Status {
	case opensource.VulnerabilityHistoryStatusDelegated:
		doDelegateVulnerability(ctx, req, vulnerabilityRes, authUserId)
	case opensource.VulnerabilityHistoryStatusCancelDelegated:
		doCancelDelegateVulnerability(ctx, req, vulnerabilityRes, authUserId)
	default:
		historyRes := &dvulnerabilityhistory.OpenSourceVulnerabilityHistory{}

		historyRes.Status = req.Status
		historyRes.UserID = authUserId
		historyRes.VulnerabilityID = vulnerabilityId
		historyRes.Comment = req.Comment

		err = transaction.ConfirmOrIgnoreVulnerability(vulnerabilityRes, historyRes)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
			return
		}
		logging.DebugLogger.Debugf("漏洞处理完成: ID-<%d>, CVE-<%s>", vulnerabilityRes.ID, vulnerabilityRes.CveID)
		ctx.JSON(response.NewResponse(response.NoErr.Code, historyRes, response.NoErr.Msg))
		return
	}
}

func GetVulnerabilityHistory(ctx iris.Context) {
	vulnerabilityId, _ := ctx.Params().GetUint("vulnerability_id")
	id, _ := ctx.Params().GetUint("id")
	history, err := dvulnerabilityhistory.GetVulnerabilityHistoryById(id, vulnerabilityId)
	if err != nil {
		logging.ErrorLogger.Errorf("get opensource vulnerability history get err %s", err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	} else if history == nil {
		ctx.JSON(response.NewResponse(response.DataEmptyErr.Code, nil, fmt.Sprintf("漏洞处理记录-<%d>不存在", id)))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, history, response.NoErr.Msg))
}
