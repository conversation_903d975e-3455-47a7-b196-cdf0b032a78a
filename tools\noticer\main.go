package main

import (
	"bytes"
	"encoding/json"
	"errors"
	"flag"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"

	"github.com/jinzhu/configor"
)

var (
	client = &http.Client{}
	Config = struct {
		Hosts  string `env:"Host" default:""`
		Queues string `env:"Queue" default:""`
	}{}
)

type Queue struct {
	Code    int      `json:"code"`
	Data    []string `json:"data"`
	Message string   `json:"message"`
}

func InitConfig(config string) error {
	path := filepath.Join(libs.CWD(), "application.yml")
	if config != "" {
		path = config
	}

	if err := configor.Load(&Config, path); err != nil {
		return err
	}

	return nil
}

func GetQueue(host, name string, count int) ([]string, error) {
	url := fmt.Sprintf("%s/api/v1/queue?name=%s&count=%d", host, name, count)
	logging.DebugLogger.Debug("获取队列", url)
	reqest, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}
	// 处理返回结果
	response, err := client.Do(reqest)
	if err != nil {
		return nil, err
	}
	defer response.Body.Close()
	bodyByte, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return nil, err
	}
	response.Body.Close()

	ret := Queue{}
	err = json.Unmarshal(bodyByte, &ret)
	if err != nil {
		return nil, err
	}
	if ret.Code == 20000 {
		return ret.Data, nil
	}
	return nil, errors.New(ret.Message)
}

func PushQueue(host, key string, msgs []string) (string, error) {
	url := fmt.Sprintf("%s/api/v1/queue", host)
	result := []interface{}{}
	for _, msg := range msgs {
		items := strings.Split(msg, "|")
		if len(items) > 5 {
			items = strings.Split(msg, "|s|p|l|i|t|")
		}
		if len(items) > 5 {
			return msg, fmt.Errorf("邮件数据异常")
		}
		result = append(result, map[string]interface{}{
			"key":     key,
			"from":    items[0],
			"to":      items[1],
			"subject": items[2],
			"body":    items[3],
			"cc":      items[4],
		})
	}

	jsonStr, _ := json.Marshal(result)
	reqest, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	if err != nil {
		return string(jsonStr), err
	}
	// 处理返回结果
	response, err := client.Do(reqest)
	if err != nil {
		return string(jsonStr), err
	}
	defer response.Body.Close()
	bodyByte, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return string(jsonStr), err
	}
	response.Body.Close()

	ret := Queue{}
	err = json.Unmarshal(bodyByte, &ret)
	if err != nil {
		return string(jsonStr), err
	}
	if ret.Code == 20000 {
		return string(jsonStr), nil
	}
	return string(jsonStr), errors.New(ret.Message)
}

var filterPattern = regexp.MustCompile(`\d+#note_\d+`)

func Worker(host, queue string) {
	failMsgs := []string{}
	msgs, err := GetQueue(host, queue, 10)
	if err != nil {
		logging.ErrorLogger.Errorf("获取队列失败", err)
	}
	if msgs == nil {
		return
	}
	for _, msg := range msgs {
		items := strings.Split(msg, "|")
		if len(items) > 5 {
			items = strings.Split(msg, "|s|p|l|i|t|")
		}
		if len(items) > 5 {
			logging.ErrorLogger.Errorf(msg)
		}
		// 临时屏蔽
		if strings.Contains(items[1], "buildfarm@ruijie") {
			continue
		}

		_mailTo := strings.Split(items[1], ",")
		mailTo := []string{}

		for _, part := range _mailTo {
			if strings.TrimSpace(part) != "" {
				mailTo = append(mailTo, strings.TrimSpace(part))
			}
		}

		if len(mailTo) == 0 {
			continue
		}

		if len(items) == 5 {
			_cc := strings.Split(items[4], ",")
			cc := []string{}

			for _, part := range _cc {
				if strings.TrimSpace(part) != "" {
					cc = append(cc, strings.TrimSpace(part))
				}
			}

			if strings.Contains(items[3], "AI代码评审 started a new discussion") {
				continue
			}

			if !strings.Contains(items[3], " started a new discussion") && filterPattern.MatchString(items[3]) {
				continue
			}

			err := libs.SendMail(mailTo, strings.Replace(items[2], "\n", "", -1), items[3], cc)
			if err != nil {
				logging.ErrorLogger.Errorf("邮件发送失败，推送至重做队列", err)
				// logging.ErrorLogger.Errorf(" rec1: %v %v, subject: %v, body: %v, cc:%v %v", strings.Split(items[1], ","), len(strings.Split(items[1], ",")), items[2], items[3], strings.Split(items[4], ","), len(strings.Split(items[4], ",")))
				logging.ErrorLogger.Errorf(msg)
				failMsgs = append(failMsgs, msg)
			}
			// err = libs.SendCardMessage(mailTo, strings.Replace(items[2], "\n", "", -1), items[3], []string{})
			// if err != nil {
			// 	msg := fmt.Sprintf("发送飞书卡片失败, %v", err)
			// 	logging.ErrorLogger.Errorf(msg)
			// }

		}
		if len(items) == 4 {
			if strings.HasPrefix(items[3], "AI代码评审 started a new discussion") {
				continue
			}

			err := libs.SendMail(mailTo, items[2], items[3], []string{})
			if err != nil {
				logging.ErrorLogger.Errorf("邮件发送失败，推送至重做队列", err)
				// logging.ErrorLogger.Errorf("rec2: %v, subject: %v, body: %v, cc:%v", strings.Split(items[1], ","), items[2], items[3], strings.Split(items[4], ","))
				logging.ErrorLogger.Errorf(msg)
				failMsgs = append(failMsgs, msg)
			}
			// err = libs.SendCardMessage(mailTo, items[2], items[3], []string{})
			// if err != nil {
			// 	msg := fmt.Sprintf("发送飞书卡片失败, %v", err)
			// 	logging.ErrorLogger.Errorf(msg)
			// }
		}
	}
	if len(failMsgs) > 0 {
		ret, err := PushQueue(host, queue, failMsgs)
		if err != nil {
			logging.ErrorLogger.Errorf("推送重做队列失败", err, ret)
		}
	}
}

var (
	config  = flag.String("config", "", "配置路径")
	version = flag.Bool("version", false, "打印版本号")
	Version = "v0.1"
)

func main() {
	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "usage: %s [options] [command]\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Commands:\n")
		fmt.Fprintf(os.Stderr, "\n")
		fmt.Fprintf(os.Stderr, "  -config <path>\n")
		fmt.Fprintf(os.Stderr, "    设置项目配置文件路径，可选\n")
		fmt.Fprintf(os.Stderr, "  -version <true or false> 打印项目版本号，默认为: false\n")
		fmt.Fprintf(os.Stderr, "    打印版本号\n")
		fmt.Fprintf(os.Stderr, "\n")
	}
	flag.Parse()

	if *version {
		fmt.Println(fmt.Sprintf("版本号：%s\n", Version))
	}
	err := InitConfig(*config)
	if err != nil {
		logging.ErrorLogger.Errorf("系统配置初始化失败:", err)
	}

	hosts := strings.Split(Config.Hosts, ",")
	queues := strings.Split(Config.Queues, ",")
	if len(hosts) != len(queues) {
		logging.ErrorLogger.Errorf("系统配置初始化失败: 主机数量与队列名数量不一至")
	}
	fmt.Println(libs.CWD())
	fmt.Println("已启用监听数量:", len(hosts))
	fmt.Println("监听任务如下：")
	for i := 0; i < len(hosts); i++ {
		t := time.NewTicker(60 * time.Second)
		host := hosts[i]
		queue := queues[i]
		fmt.Println(host, queue)
		go func() {
			for {
				select {
				case <-t.C:
					Worker(host, queue)
				}
			}
		}()
	}

	select {}
	// //定义收件人
	// mailTo := []string{
	// 	"<EMAIL>",
	// }
	// //邮件主题为"Hello"
	// subject := "Hello"
	// // 邮件正文
	// body := "Good"
	// err := libs.SendMail(mailTo, subject, body)
	// fmt.Println(err)
}
