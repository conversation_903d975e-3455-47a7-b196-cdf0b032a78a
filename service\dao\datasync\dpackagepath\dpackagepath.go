package dpackagepath

import (
	"encoding/json"
	"fmt"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/datasync"
	"irisAdminApi/service/dao/datasync/dsyncrecord"

	"gorm.io/gorm"
)

const ModelName = "分支信息表"

type SyncResponse struct {
	/*
		"state": "SUCCESS",
		"data": [
			{
				"rownum": 1,
				"bugId": 259201,                            //BUGID
				"bugBelong": "测试人",                        //bug归属人
				"bugOwner": "测试人",                        //bug负责人
				"bugTestCharger": "测试2",                    //测试负责人
				"bugOs": "测试项目",                        //bug操作系统
				"bugProduct": "测试产品",                    //bug产品
				"workpacketName": null,                        //bug工作包
				"summary": "描述",                            //bug描述
				"mainbugid": null,                            //bug是否是从某个BUG镜像出来的BUG，如果有直接是父bugid
				"samebugid": null,                            //bug是否是从某个BUGsameas出来的BUG，如果有直接是父bugid
				"thedate": "2015-06-23 11:20:56",            //bug创建时间
				"lastupdatedate": "2015-06-23 15:28:53",    //bug更新时间
				"bugState": "DENIAL-ByDevelopment",            //bug状态
				"disabled": null                            //bug是否无效 0或者null 表示无效；1或者true表示有效
			}
		],
		"total":1,
		"message": null
	*/
	State   string              `json:"state"`
	Data    []*SyncDataResponse `json:"data"`
	Total   int                 `json:"total"`
	Message string              `json:"message"`
}

type SyncDataResponse struct {
	/*
	   "projectId": 1961,                    //rgos项目ID
	   "path": "路径",                        //组件路径
	   "pathType": "代码"                    //组件类型
	*/
	ProjectID int    `gorm:"index;not null" json:"projectId"`
	Path      string `gorm:"not null; type:varchar(300)" json:"path"`
	PathType  string `gorm:"not null; type:varchar(60)" json:"pathType"`
}

type Response struct {
	datasync.BugPackagePath
}

type ListResponse struct {
	Response
}

type Request struct {
	Id uint `json:"id"`
}

func (this *Response) ModelName() string {
	return ModelName
}

func Model() *datasync.BugPackagePath {
	return &datasync.BugPackagePath{}
}

func (this *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (this *Response) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func UpdateOrCreateBugPathTransaction(items []*SyncDataResponse, _url string, data map[string]string, method, state, errorMsg string) error {
	db := easygorm.GetEasyGormDb()
	batchCreateObjects := []map[string]interface{}{}
	err := db.Transaction(func(tx *gorm.DB) error {
		for _, item := range items {
			response := Response{}
			object := map[string]interface{}{
				"project_id": item.ProjectID,
				"path":       item.Path,
				"path_type":  item.PathType,
			}
			if err := tx.Model(Model()).Where("project_id = ?", item.ProjectID).Find(&response).Error; err != nil {
				return err
			} else {
				if response.ProjectID == 0 {
					batchCreateObjects = append(batchCreateObjects, object)
				} else {
					if err := tx.Model(Model()).Where("project_id = ?", item.ProjectID).Updates(object).Error; err != nil {
						return err
					}
				}
			}
		}
		if len(batchCreateObjects) > 0 {
			if err := tx.Model(Model()).Create(batchCreateObjects).Error; err != nil {
				return err
			}
		}
		body, err := json.Marshal(data)
		if err != nil {
			return err
		}
		if err := tx.Model(dsyncrecord.Model()).Create(map[string]interface{}{
			"url":             _url,
			"body":            body,
			"method":          method,
			"state":           state,
			"message":         errorMsg,
			"min_modify_date": data["minModifyDate"],
			"max_modify_date": data["maxModifyDate"],
			"created_at":      time.Now(),
		}).Error; err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}
