package documentauto

import (
	"irisAdminApi/application/libs/response"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/documentauto/ddagversion"
	"strconv"
	"time"

	"github.com/kataras/iris/v12"
)

func GetVersionLists(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	versionList, err := ddagversion.GetVersionList(name, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, versionList, response.NoErr.Msg))
}

func CreateVersion(ctx iris.Context) {
	request := ddagversion.Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "解析请求数据失败: "+err.Error()))
		return
	}
	if err := validate.Struct(request); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "数据验证失败: "+err.Error()))
		return
	}

	// 首先检查版本号是否已存在
	existingVersion, err := ddagversion.GetVersionData(request.VersionNumber)
	if err == nil && existingVersion != nil && existingVersion.ID > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "版本号已存在，请使用其他版本号"))
		return
	}

	// 验证排序值是否有效
	valid, message, err := ddagversion.ValidateSortValue(request.Sort)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "验证排序值失败: "+err.Error()))
		return
	}
	if !valid {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, message))
		return
	}

	// 更新现有版本的排序值
	if err := ddagversion.UpdateVersionSorts(request.Sort); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "更新版本排序失败: "+err.Error()))
		return
	}

	// 创建新版本
	version := &ddagversion.Response{}
	err = version.Create(map[string]interface{}{
		"VersionNumber": request.VersionNumber,
		"ReleaseDate":   request.ReleaseDate,
		"Status":        request.Status,
		"Sort":          request.Sort,
		"CreatedAt":     time.Now(),
		"UpdatedAt":     time.Now(),
	})
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "创建版本失败: "+err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, "创建版本成功"))
}

func UpdateVersion(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	request := ddagversion.Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "解析请求数据失败: "+err.Error()))
		return
	}
	if err := validate.Struct(request); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "数据验证失败: "+err.Error()))
		return
	}

	// 检查版本号是否与其他版本冲突（排除当前版本）
	existingVersion, err := ddagversion.GetVersionData(request.VersionNumber)
	if err == nil && existingVersion != nil && existingVersion.ID > 0 && existingVersion.ID != id {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "版本号已被其他版本使用，请使用其他版本号"))
		return
	}

	// 获取当前版本以检查排序是否已更改
	currentVersion := &ddagversion.Response{}
	if err := currentVersion.Find(id); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "获取当前版本失败: "+err.Error()))
		return
	}

	// 只有当排序值改变时才进行验证和更新
	if currentVersion.Sort != request.Sort {
		// 验证新的排序值是否有效
		valid, message, err := ddagversion.ValidateSortValue(request.Sort)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "验证排序值失败: "+err.Error()))
			return
		}
		if !valid {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, message))
			return
		}

		// 更新其他版本的排序值
		if err := ddagversion.UpdateVersionSortsOnEdit(currentVersion.Sort, request.Sort); err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "更新版本排序失败: "+err.Error()))
			return
		}
	}

	// 更新版本信息
	version := &ddagversion.Response{}
	err = version.Update(id, map[string]interface{}{
		"VersionNumber": request.VersionNumber,
		"ReleaseDate":   request.ReleaseDate,
		"Status":        request.Status,
		"Sort":          request.Sort,
		"UpdatedAt":     time.Now(),
	})
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "更新版本失败: "+err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, "更新版本成功"))
}

func DeleteVersion(ctx iris.Context) {
	id, _ := dao.GetId(ctx)

	// 获取当前版本，以便在删除后更新其他版本的排序
	currentVersion := &ddagversion.Response{}
	if err := currentVersion.Find(id); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "获取当前版本失败: "+err.Error()))
		return
	}

	// 删除版本
	version := &ddagversion.Response{}
	err := version.Delete(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "删除版本失败: "+err.Error()))
		return
	}

	// 更新剩余版本的排序值
	if err := ddagversion.DecrementVersionSorts(currentVersion.Sort); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "更新版本排序失败: "+err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, "删除版本成功"))
}

// CheckVersionSortConsistency 检查版本排序一致性
// 用于管理员验证系统中所有版本的排序是否正确
func CheckVersionSortConsistency(ctx iris.Context) {
	// 检查当前用户权限（可选）
	// 这里可以添加权限检查代码

	// 检查版本排序一致性
	consistent, issues, err := ddagversion.CheckSortConsistency()
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "检查排序一致性失败: "+err.Error()))
		return
	}

	// 返回检查结果
	result := map[string]interface{}{
		"consistent": consistent,
		"issues":     issues,
	}

	if consistent {
		ctx.JSON(response.NewResponse(response.NoErr.Code, result, "版本排序一致性检查通过"))
	} else {
		ctx.JSON(response.NewResponse(response.NoErr.Code, result, "版本排序存在问题，请查看详细信息"))
	}
}
