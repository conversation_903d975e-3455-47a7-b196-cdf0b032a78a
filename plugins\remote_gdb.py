# !/usr/bin/env python
import sys
import time
import subprocess
import os

username = sys.argv[1]
host = sys.argv[2]
port = sys.argv[3]

lib = sys.argv[4]
program = sys.argv[5]
coredump = sys.argv[6]
gdb = sys.argv[7]

if gdb == "":
    gdb = "ntos-gdb"

# command = f"""{gdb} --batch -q {program} {coredump} -ex "set sysroot {sysroot}" -ex 'bt full'"""
command = f"""{gdb} --batch -q -iex 'set solib-search-path {lib}' '{program}' '{coredump}' -ex 'thread apply all bt full'"""

command = '''ssh {}@{} -p {} "{}"'''.format(username, host, port, command)
print(command)
# todo: subprocss.popen
try:
    completed_process = subprocess.run(command, shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    if completed_process.stderr:
        print(completed_process.stderr.decode("utf-8"))
    print(completed_process.stdout.decode("utf-8"))

except subprocess.CalledProcessError as e:
    # Handle errors in the called executable
    print(f"An error occurred: {e}")
    sys.exit(1)
except Exception as e:
    # Handle other exceptions such as a subprocess.TimeoutExpired
    print(f"An unexpected error occurred: {e}")
    sys.exit(1)
