package dmergerequescontrolreview

import (
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models"
	"irisAdminApi/application/models/mergerequest"
	"irisAdminApi/service/dao/mergerequest/dmergerequest"
	"irisAdminApi/service/dao/mergerequest/dmergerequestworkpackage"
)

const ModelName = "MR表单管控审核单"

type User struct {
	ID       uint   `json:"id"`
	Name     string `json:"name"`
	Username string `json:"username"`
}

type ReleaseProject struct {
	models.ModelBase
	Name string `gorm:"uniqueIndex; not null; type:varchar(60)" json:"name"`
}

type Response struct {
	mergerequest.MergeRequestControlReview
	ReleaseProject ReleaseProject                                    `gorm:"->" json:"release_project"`
	User           User                                              `gorm:"->" json:"user"`
	WorkPackage    *dmergerequestworkpackage.MergeRequestWorkPackage `gorm:"-" json:"work_package"`
	MergeRequest   *dmergerequest.MergeRequest                       `gorm:"-" json:"merge_request"`
}

// type MergeRequest struct {
// 	ID   uint   `json:"id"`
// 	Name string `json:"name"`
// }

type ListResponse struct {
	Response
}

type Request struct {
	Response
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *mergerequest.MergeRequestControlReview {
	return &mergerequest.MergeRequestControlReview{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	where := easygorm.GetEasyGormDb().Model(Model())
	db = db.Where(where)
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {

	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) BatchCreate(mrs []*Response) error {
	err := easygorm.GetEasyGormDb().Create(&mrs).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("ReleaseProject").Preload("User").Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	formatResponse(u)
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) Last(mergeRequestID uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("merge_request_id = ?", mergeRequestID).Order("id desc").Limit(1).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindByReleaseProjectID(releaseProjectID, page, pageSize, status int, sort, orderBy string) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse
	db := easygorm.GetEasyGormDb().Model(Model()).Preload("ReleaseProject").Preload("User")
	if releaseProjectID > 0 {
		db = db.Where("release_project_id = ?", releaseProjectID)
	} else {
		db = db.Where("release_project_id != 0")
	}
	if status >= 0 {
		db = db.Where("status = ?", status)
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	formatResponses(items)

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func FindByMergeRequestID(mergeRequestID, releaseProjectID, workPackageID uint) (Response, error) {
	u := Response{}
	if err := easygorm.GetEasyGormDb().Model(Model()).Where("merge_request_id = ? and release_project_id = ? and work_package_id = ? and status=0", mergeRequestID, releaseProjectID, workPackageID).Order("updated_at DESC").Find(&u).Error; err != nil {
		logging.ErrorLogger.Errorf("find  mergerequescontrolreview by MergeRequestID err ", err)
		return u, err
	}
	return u, nil
}

func formatResponses(items []*ListResponse) {
	workpackageIds := []uint{}
	mergerequestIds := []uint{}
	uniqueIds := make(map[uint]bool)
	workpackageMap := map[uint]*dmergerequestworkpackage.MergeRequestWorkPackage{}
	mergerequestMap := map[uint]*dmergerequest.MergeRequest{}
	for _, item := range items {
		if _, ok := uniqueIds[item.WorkPackageID]; !ok {
			uniqueIds[item.WorkPackageID] = true
			workpackageIds = append(workpackageIds, item.WorkPackageID)
		}
		mergerequestIds = append(mergerequestIds, item.MergeRequestID)
	}

	workpackages, err := dmergerequestworkpackage.FindInIds(workpackageIds)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return
	}
	mergerequests, errerr := dmergerequest.FindInIds(mergerequestIds)
	if errerr != nil {
		logging.ErrorLogger.Errorf("find def resource err ", errerr)
		return
	}

	for _, workpackage := range workpackages {
		workpackageMap[workpackage.ID] = &workpackage.MergeRequestWorkPackage
	}

	for _, mergerequest := range mergerequests {
		mergerequestMap[mergerequest.ID] = mergerequest
	}
	for _, item := range items {
		item.WorkPackage = workpackageMap[item.WorkPackageID]
		item.MergeRequest = mergerequestMap[item.MergeRequestID]
	}
}

func formatResponse(item *Response) {
	workpackageIds := []uint{}
	mergerequestIds := []uint{}
	workpackageMap := map[uint]*dmergerequestworkpackage.MergeRequestWorkPackage{}
	mergerequestMap := map[uint]*dmergerequest.MergeRequest{}
	workpackageIds = append(workpackageIds, item.WorkPackageID)
	mergerequestIds = append(mergerequestIds, item.MergeRequestID)
	workpackages, err := dmergerequestworkpackage.FindInIds(workpackageIds)
	if err != nil {
		logging.ErrorLogger.Errorf("find def resource err ", err)
		return
	}
	mergerequests, errerr := dmergerequest.FindInIds(mergerequestIds)
	if errerr != nil {
		logging.ErrorLogger.Errorf("find def resource err ", errerr)
		return
	}

	for _, workpackage := range workpackages {
		workpackageMap[workpackage.ID] = &workpackage.MergeRequestWorkPackage
	}
	for _, mergerequest := range mergerequests {
		mergerequestMap[mergerequest.ID] = mergerequest
	}
	item.WorkPackage = workpackageMap[item.WorkPackageID]
	item.MergeRequest = mergerequestMap[item.MergeRequestID]

}
