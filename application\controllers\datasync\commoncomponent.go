package datasync

import (
	"strings"

	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/datasync/dcommoncomponent"
)

func SyncCommonComponent() error {
	commonComponents := []map[string]interface{}{}
	codePathExists := map[string]interface{}{}
	records, err := GetTableData("Bo0WbZpsOaNd8tsxMRgchY5rnZf", "tblAypIIlR5normx")
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		return err
	}

	for _, record := range records {
		var codes, component, componentPacket, owner, comment string
		for k, v := range record.Fields {
			if v != nil {
				if k == "是否开源" {
					if vv, ok := v.(string); ok {
						comment = vv
						if comment == "Y" {
							comment = "是"
						} else if comment == "N" {
							comment = "否"
						}
					}
				}
				if k == "代码路径" {
					if vv, ok := v.([]interface{}); ok {
						if vvv, ok := vv[0].(map[string]interface{}); ok {
							if vvvv, ok := vvv["text"]; ok {
								if vvvvv, ok := vvvv.(string); ok {
									codes = vvvvv
								}
							}
						}
					}
				}
				if k == "组件包" {
					// if vv, ok := v.([]interface{}); ok {
					// 	if vvv, ok := vv[0].(map[string]interface{}); ok {
					// 		if vvvv, ok := vvv["text"]; ok {
					// 			if vvvvv, ok := vvvv.(string); ok {
					// 				componentPacket = vvvvv
					// 			}
					// 		}
					// 	}
					// }
					if vv, ok := v.(string); ok {
						componentPacket = vv
					}
				}
				if k == "组件名" {
					if vv, ok := v.(string); ok {
						component = vv
					}
				}
				if k == "负责人" {
					if vv, ok := v.([]interface{}); ok {
						if vvv, ok := vv[0].(map[string]interface{}); ok {
							if vvvv, ok := vvv["name"]; ok {
								if vvvvv, ok := vvvv.(string); ok {
									owner = vvvvv
								}
							}
						}
					}
				}
			}
		}
		// fmt.Println(record)
		for _, codePath := range strings.Split(codes, "\n") {
			if codePath != "" {
				// 去掉所有空格，去掉尾部/
				codePath = strings.ReplaceAll(codePath, " ", "")
				codePath = strings.TrimSuffix(codePath, "/")

				if _, ok := codePathExists[codePath]; !ok {
					codePathExists[codePath] = nil
					commonComponents = append(commonComponents, map[string]interface{}{
						"path":             strings.ReplaceAll(codePath, "*", "%"),
						"component_packet": componentPacket,
						"component":        component,
						"owner":            owner,
						"comment":          comment,
					})
				}
			}
		}
	}
	err = dcommoncomponent.BatchCreate(commonComponents)
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		return err
	}
	return nil
}
