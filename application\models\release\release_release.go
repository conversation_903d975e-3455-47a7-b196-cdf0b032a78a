package release

import "irisAdminApi/application/models"

type ReleaseRelease struct {
	models.ModelBase
	ReleaseProjectID      uint   `gorm:"not null"`
	BuildNameID           uint   `gorm:"not null"`
	ProductModelID        uint   `gorm:"not null"`
	ReleaseAttrID         uint   `gorm:"not null"`
	ReleasedAt            string `gorm:"not null; type:varchar(60)"`
	SystemSoftwareVersion string `gorm:"not null; type:varchar(60)"`
	SystemSoftwareNumber  string `gorm:"not null; type:varchar(60)"`
	SystemPatchNumber     string `gorm:"not null; type:varchar(60)"`
	PackageName           string `gorm:"not null; type:varchar(60)"`
	BuildFarmLink         string `gorm:"not null; type:varchar(300)"`
	ReleaseLink           string `gorm:"not null; type:varchar(300)"`
	Comment               string `gorm:"not null; type:varchar(300)"`
	UserID                uint   `gorm:"not null"`
	Uuid                  string `gorm:"not null;index;type:varchar(60)"`
	Archive               uint   `gorm:"not null; default: 3"` // 3: not start, 0: start, 1: success, 2: false
	Publish               bool   `gorm:"default: false"`
	Manufacture           bool   `gorm:"default: false"`
}

type ReleaseBuildName struct {
	models.ModelBase
	Name string `gorm:"not null; type:varchar(60)"`
}

type ReleaseProductModel struct {
	models.ModelBase
	ReleaseBuildNameID uint   `gorm:"not null"`
	Name               string `gorm:"not null; type:varchar(60)"`
}

type ReleaseReleaseAttr struct {
	models.ModelBase
	Name string `gorm:"not null; type:varchar(60)"`
}

type ReleaseBranchInfo struct {
	models.ModelBase
	ReleaseProjectID     uint   `gorm:"not null"`
	ReleaseBranch        string `gorm:"not null; type:varchar(60)"`
	ReleaseBranchVersion string `gorm:"not null; type:varchar(60)"`
	BaseBranch           string `gorm:"not null; type:varchar(60)"`
	BaseVerison          string `gorm:"not null; type:varchar(60)"`
	Comment              string `gorm:"not null; type:varchar(200)"`
}

type ReleaseCompileInfo struct {
	models.ModelBase
	ReleaseID     uint   `gorm:"not null"`
	ReleaseBranch string `gorm:"not null; type:varchar(60)"`
	Version       string `gorm:"not null; type:varchar(60)"`
	Url           string `gorm:"not null; type:varchar(300)"`
	Unit          string `gorm:"not null; type:varchar(60)"`
	OpenMR        string `gorm:"not null; type:varchar(60)"`
}

type ReleaseBuildTime struct {
	models.ModelBase
	ReleaseID uint   `gorm:"not null"`
	BuildTime string `gorm:"not null; type:varchar(60)"`
}

type ReleaseReleaseODM struct {
	models.ModelBase
	ProjectName           string `gorm:"not null; type:varchar(128)"`
	ProductModel          string `gorm:"not null; type:varchar(128)"`
	ReleaseAttrID         uint   `gorm:"not null"`
	ReleasedAt            string `gorm:"not null; type:varchar(60)"`
	SystemSoftwareVersion string `gorm:"not null; type:varchar(60)"`
	PackageName           string `gorm:"not null; type:varchar(60)"`
	ReleaseLink           string `gorm:"not null; type:varchar(300)"`
	Comment               string `gorm:"not null; type:varchar(300)"`
	UserID                uint   `gorm:"not null"`
	Uuid                  string `gorm:"not null;index;type:varchar(60)"`
	Archive               uint   `gorm:"not null; default: 3"` // 3: not start, 0: start, 1: success, 2: false
}

type ReleaseReleaseArchive struct {
	models.ModelBase
	ProjectName           string `gorm:"not null; type:varchar(128)"`
	ProductModel          string `gorm:"not null; type:varchar(128)"`
	ReleaseAttrID         uint   `gorm:"not null"`
	ReleasedAt            string `gorm:"not null; type:varchar(60)"`
	SystemSoftwareVersion string `gorm:"not null; type:varchar(60)"`
	PackageName           string `gorm:"not null; type:varchar(60)"`
	ReleaseLink           string `gorm:"not null; type:varchar(300)"`
	Comment               string `gorm:"not null; type:varchar(300)"`
	UserID                uint   `gorm:"not null"`
	Uuid                  string `gorm:"not null;index;type:varchar(60)"`
	SyncStatus            uint   `gorm:"not null; default: 0"` // 0:未同步 1：已同步
}

type ProductData struct {
	models.ModelBase
	ProductId       string `gorm:"uniqueIndex: idx_product_id_unique; not null; type:varchar(60)" json:"product_id" ` //产品ID
	ItemNum         string `gorm:"not null; type:varchar(60)" json:"item_num" update:"1"`                             //编号
	ItemDesc        string `gorm:"not null; type:varchar(128)" json:"item_desc" update:"1"`                           //描述
	ItemAttType     string `gorm:"not null; type:varchar(60)" json:"item_att_type" update:"1"`                        //类型
	ProductLine     string `gorm:"not null; type:varchar(60)" json:"product_line" update:"1" `                        //产品线
	ProductFamily   string `gorm:"not null; type:varchar(60)" json:"product_family" update:"1"`                       //产品系列
	ProductCategory string `gorm:"not null; type:varchar(60)" json:"product_category" update:"1"`                     //产品类别
	ProductSeries   string `gorm:"not null; type:varchar(60)" json:"product_series" update:"1"`                       //产品系列号
	ProductModel    string `gorm:"not null; type:varchar(60)" json:"product_model" update:"1"`                        //产品型号
}
