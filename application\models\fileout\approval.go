package fileout

import "irisAdminApi/application/models"

type Approval struct {
	models.ModelBase
	OriginName string `gorm:"not null; type:varchar(60)" json:"origin_name" `
	Name       string `gorm:"index;not null; type:varchar(60)" json:"name" `
	Md5        string `gorm:"index;not null; type:varchar(60)" json:"md5" `
	UserId     uint   `gorm:"index;not null" json:"user_id" `
	Audit      bool   `json:"audit" `
	AuditorId  uint   `gorm:"index;not null" json:"auditor_id" `
	Force      uint   `gorm:"not null" json:"force" `
	Permit     bool   `json:"permit"`
	Status     uint   `json:"status"` // 0:system checking 1:system checked 2:system check failed
	Size       uint   `json:"size"`
}

type FileStruction struct {
	models.ModelBase
	ApprovalID uint `gorm:"index;not null" json:"approval_id" `
	// UserID     uint   `gorm:"not null;default:0" json:"user_id"`
	UserID uint   `json:"user_id"`
	Name   string `gorm:"not null; type:varchar(60)" json:"name"`
	Detail string `json:"detail"` //
	Status uint   `gorm:"status"` // 0 not pass, 1 pass
}
