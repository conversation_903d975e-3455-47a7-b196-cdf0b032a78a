# PM节点操作手册

## 1. 概述

本手册为项目经理/产品经理（PM）在生产发布流程中使用"PM提交程序"节点的操作指南。

## 2. 操作前准备

### 2.1 必需信息
- 主程序下载链接（必填）
- OSPKG安装包下载链接（可选，系统可自动推导）
- 编译系统访问权限

### 2.2 链接格式要求
- 主程序链接示例：`http://build.example.com/output/abc123.../releaseID-bin/program.bin`
- OSPKG链接示例：`http://build.example.com/output/abc123.../ospkg-install.tar.gz`

## 3. 操作步骤

### 3.1 进入PM节点
1. 登录生产发布系统
2. 在"待处理任务"中找到对应的PM提交程序任务
3. 点击"处理"按钮进入操作界面

### 3.2 填写程序信息
1. **主程序URL**（必填）
   - 粘贴完整的主程序下载链接
   - 确保链接可正常访问
   
2. **OSPKG安装包URL**（可选）
   - 如不填写，系统会自动推导
   - 如填写，请确保链接正确

3. **备注信息**（可选）
   - 填写相关说明或注意事项

### 3.3 提交审批
1. 检查填写信息的准确性
2. 点击"提交"按钮
3. 等待系统验证处理

## 4. 系统自动处理

### 4.1 文件验证
- 系统会自动下载程序文件
- 计算文件MD5值和大小
- 验证文件完整性

### 4.2 版本信息获取
- 自动关联编译记录
- 获取软件版本号
- 处理版本格式

### 4.3 重复检查
- 检查是否存在相同文件的发布记录
- 防止重复发布

## 5. 常见问题处理

### 5.1 链接无效
**错误提示**: "主程序URL不能为空" 或 "主程序链接异常"
**解决方法**: 
- 检查链接格式是否正确
- 确认链接可正常访问
- 联系编译系统管理员确认文件状态

### 5.2 文件下载失败
**错误提示**: "主程序文件下载失败" 或 "ospkg-install文件下载失败"
**解决方法**:
- 检查网络连接
- 确认文件服务器状态
- 重新获取下载链接

### 5.3 重复发布检测
**错误提示**: "已经存在相同文件发布记录"
**解决方法**:
- 确认是否确实需要重新发布
- 检查程序版本是否正确
- 如需强制发布，联系系统管理员

### 5.4 编译记录不存在
**错误提示**: "主程序编译记录不存在"
**解决方法**:
- 确认编译任务已完成
- 检查链接中的编译ID是否正确
- 联系编译系统管理员

## 6. 注意事项

### 6.1 链接有效性
- 确保提供的链接在提交时可正常访问
- 避免使用临时或即将过期的链接

### 6.2 文件完整性
- 系统会自动验证文件完整性
- 如发现文件损坏，请重新编译

### 6.3 版本一致性
- 确保提交的程序版本与发布计划一致
- 注意区分预鉴、中试、首量、质量等不同版本类型

### 6.4 时效性
- 及时处理待办任务，避免影响整体发布进度
- 如遇问题及时反馈给相关人员

## 7. 联系方式

### 7.1 技术支持
- 系统问题：联系系统管理员
- 编译问题：联系编译系统管理员
- 流程问题：联系流程管理员

### 7.2 紧急情况
- 如遇紧急发布需求，可联系相关负责人协调处理
- 系统故障时，可通过备用流程处理

## 8. 操作示例

### 8.1 正常提交示例
```text
主程序URL: http://build.example.com/output/a1b2c3d4.../releaseID-bin/RG-EG105G-E_V1.0.0.bin
OSPKG URL: （留空，系统自动推导）
备注: 正常版本发布
```

### 8.2 手动指定OSPKG示例
```text
主程序URL: http://build.example.com/output/a1b2c3d4.../releaseID-bin/RG-EG105G-E_V1.0.0.bin
OSPKG URL: http://build.example.com/output/a1b2c3d4.../ospkg-install.tar.gz
备注: 手动指定OSPKG包
```

## 9. 版本历史

| 版本 | 日期 | 变更内容 |
|------|------|----------|
| v1.0 | 2025-01-25 | 初始版本 |

---

**文档维护**: 系统管理员  
**最后更新**: 2025-01-25
