package libs

import (
	"sync"
	"time"

	"irisAdminApi/application/logging"
)

// MeetingMetrics 会议系统监控指标（简化版本，使用内存计数器）
type MeetingMetrics struct {
	mu sync.RWMutex

	// 处理计数器
	ProcessedTotal int64
	SuccessTotal   int64
	FailedTotal    int64

	// 处理时长统计
	ProcessingDurations []time.Duration

	// API调用计数器
	APICallsTotal map[string]int64

	// 错误计数器
	ErrorsTotal map[string]int64

	// 当前处理中的请求数量
	ProcessingRequests int64

	// 批处理大小统计
	BatchSizes []int

	// Redis操作计数器
	RedisOpsTotal map[string]int64

	// 飞书API响应时间统计
	FeishuAPIResponseTimes map[string][]time.Duration
}

// NewMeetingMetrics 创建会议系统监控指标实例
func NewMeetingMetrics() *MeetingMetrics {
	return &MeetingMetrics{
		APICallsTotal:          make(map[string]int64),
		ErrorsTotal:            make(map[string]int64),
		RedisOpsTotal:          make(map[string]int64),
		FeishuAPIResponseTimes: make(map[string][]time.Duration),
		ProcessingDurations:    make([]time.Duration, 0),
		BatchSizes:             make([]int, 0),
	}
}

// RecordProcessed 记录处理的会议数量
func (m *MeetingMetrics) RecordProcessed(count int) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.ProcessedTotal += int64(count)
	logging.DebugLogger.Debugf("记录处理会议数量: %d", count)
}

// RecordSuccess 记录成功处理的会议数量
func (m *MeetingMetrics) RecordSuccess(count int) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.SuccessTotal += int64(count)
	logging.DebugLogger.Debugf("记录成功处理会议数量: %d", count)
}

// RecordFailed 记录失败处理的会议数量
func (m *MeetingMetrics) RecordFailed(count int) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.FailedTotal += int64(count)
	logging.DebugLogger.Debugf("记录失败处理会议数量: %d", count)
}

// RecordProcessingDuration 记录处理时长
func (m *MeetingMetrics) RecordProcessingDuration(duration time.Duration) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.ProcessingDurations = append(m.ProcessingDurations, duration)
	logging.DebugLogger.Debugf("记录处理时长: %v", duration)
}

// RecordAPICall 记录API调用
func (m *MeetingMetrics) RecordAPICall(apiType, method, status string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	key := apiType + "_" + method + "_" + status
	m.APICallsTotal[key]++
	logging.DebugLogger.Debugf("记录API调用: %s %s %s", apiType, method, status)
}

// RecordError 记录错误
func (m *MeetingMetrics) RecordError(errorType, component string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	key := errorType + "_" + component
	m.ErrorsTotal[key]++
	logging.DebugLogger.Debugf("记录错误: %s in %s", errorType, component)
}

// IncProcessingRequests 增加处理中的请求数量
func (m *MeetingMetrics) IncProcessingRequests() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.ProcessingRequests++
	logging.DebugLogger.Debug("增加处理中请求数量")
}

// DecProcessingRequests 减少处理中的请求数量
func (m *MeetingMetrics) DecProcessingRequests() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.ProcessingRequests--
	logging.DebugLogger.Debug("减少处理中请求数量")
}

// RecordBatchSize 记录批处理大小
func (m *MeetingMetrics) RecordBatchSize(size int) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.BatchSizes = append(m.BatchSizes, size)
	logging.DebugLogger.Debugf("记录批处理大小: %d", size)
}

// RecordRedisOp 记录Redis操作
func (m *MeetingMetrics) RecordRedisOp(operation, status string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	key := operation + "_" + status
	m.RedisOpsTotal[key]++
	logging.DebugLogger.Debugf("记录Redis操作: %s %s", operation, status)
}

// RecordFeishuAPIResponseTime 记录飞书API响应时间
func (m *MeetingMetrics) RecordFeishuAPIResponseTime(apiType, endpoint string, duration time.Duration) {
	m.mu.Lock()
	defer m.mu.Unlock()
	key := apiType + "_" + endpoint
	if m.FeishuAPIResponseTimes[key] == nil {
		m.FeishuAPIResponseTimes[key] = make([]time.Duration, 0)
	}
	m.FeishuAPIResponseTimes[key] = append(m.FeishuAPIResponseTimes[key], duration)
	logging.DebugLogger.Debugf("记录飞书API响应时间: %s %s %v", apiType, endpoint, duration)
}

// MetricsCollector 指标收集器
type MetricsCollector struct {
	metrics *MeetingMetrics
}

// NewMetricsCollector 创建指标收集器
func NewMetricsCollector() *MetricsCollector {
	return &MetricsCollector{
		metrics: NewMeetingMetrics(),
	}
}

// GetMetrics 获取指标实例
func (c *MetricsCollector) GetMetrics() *MeetingMetrics {
	return c.metrics
}

// StartProcessing 开始处理（增加处理中请求数量）
func (c *MetricsCollector) StartProcessing() {
	c.metrics.IncProcessingRequests()
}

// EndProcessing 结束处理（减少处理中请求数量）
func (c *MetricsCollector) EndProcessing() {
	c.metrics.DecProcessingRequests()
}

// RecordBatchResult 记录批处理结果
func (c *MetricsCollector) RecordBatchResult(totalCount, successCount, failedCount int, duration time.Duration) {
	c.metrics.RecordProcessed(totalCount)
	c.metrics.RecordSuccess(successCount)
	c.metrics.RecordFailed(failedCount)
	c.metrics.RecordProcessingDuration(duration)
	c.metrics.RecordBatchSize(totalCount)

	logging.InfoLogger.Infof("记录批处理结果: 总数=%d, 成功=%d, 失败=%d, 耗时=%v",
		totalCount, successCount, failedCount, duration)
}

// RecordBitableAPICall 记录多维表格API调用
func (c *MetricsCollector) RecordBitableAPICall(method, status string, duration time.Duration) {
	c.metrics.RecordAPICall("bitable", method, status)
	c.metrics.RecordFeishuAPIResponseTime("bitable", method, duration)
}

// RecordCalendarAPICall 记录日历API调用
func (c *MetricsCollector) RecordCalendarAPICall(method, status string, duration time.Duration) {
	c.metrics.RecordAPICall("calendar", method, status)
	c.metrics.RecordFeishuAPIResponseTime("calendar", method, duration)
}

// RecordRedisOperation 记录Redis操作
func (c *MetricsCollector) RecordRedisOperation(operation string, err error) {
	status := "success"
	if err != nil {
		status = "error"
		c.metrics.RecordError("redis_error", "redis_helper")
	}
	c.metrics.RecordRedisOp(operation, status)
}

// RecordValidationError 记录验证错误
func (c *MetricsCollector) RecordValidationError(component string) {
	c.metrics.RecordError("validation_error", component)
}

// RecordProcessingError 记录处理错误
func (c *MetricsCollector) RecordProcessingError(component string) {
	c.metrics.RecordError("processing_error", component)
}

// RecordConfigError 记录配置错误
func (c *MetricsCollector) RecordConfigError(component string) {
	c.metrics.RecordError("config_error", component)
}

// GetCurrentMetrics 获取当前指标快照
func (c *MetricsCollector) GetCurrentMetrics() map[string]interface{} {
	c.metrics.mu.RLock()
	defer c.metrics.mu.RUnlock()

	return map[string]interface{}{
		"service":             "meeting",
		"status":              "monitoring",
		"processed_total":     c.metrics.ProcessedTotal,
		"success_total":       c.metrics.SuccessTotal,
		"failed_total":        c.metrics.FailedTotal,
		"processing_requests": c.metrics.ProcessingRequests,
		"api_calls_total":     c.metrics.APICallsTotal,
		"errors_total":        c.metrics.ErrorsTotal,
		"redis_ops_total":     c.metrics.RedisOpsTotal,
	}
}

// 全局指标收集器实例
var GlobalMetricsCollector *MetricsCollector

// InitMetrics 初始化指标收集器
func InitMetrics() {
	GlobalMetricsCollector = NewMetricsCollector()
	logging.InfoLogger.Info("会议系统监控指标初始化完成")
}

// GetGlobalMetrics 获取全局指标收集器
func GetGlobalMetrics() *MetricsCollector {
	if GlobalMetricsCollector == nil {
		InitMetrics()
	}
	return GlobalMetricsCollector
}
