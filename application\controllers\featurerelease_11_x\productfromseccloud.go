package featurerelease_11_x

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"net/http"

	"github.com/kataras/iris/v12"
)

func GetProductModelsFromSecCloud(ctx iris.Context) {
	if libs.Config.FeatureRelease.Url == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置云平台地址，请联系管理员"))
		return
	}

	url := fmt.Sprintf("%s/api/featureManager/queryAllSoftVersion", libs.Config.FeatureRelease.Url)
	req, err := http.NewRequest("POST", url, nil)
	req.Header.Add("iamfeign", "1")

	if err != nil {
		logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	result, err := libs.HandlerRequest(req)
	if err != nil {
		logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if result["success"].(bool) {
		ctx.JSON(response.NewResponse(response.NoErr.Code, result["result"], response.NoErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("未知错误: %s", result)))
	return
}
