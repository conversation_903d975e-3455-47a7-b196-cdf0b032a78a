package dpmssoftwareproject

import (
	"encoding/json"
	"fmt"
	"reflect"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/datasync"

	"irisAdminApi/service/dao/datasync/dproject"
	"irisAdminApi/service/dao/datasync/dsyncrecord"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "PMS软件项目表"

type PmsSoftwareProjectSyncResponse struct {
	State   string                        `json:"state"`
	Data    []*PmsSoftwareProjectResponse `json:"data"`
	Total   int                           `json:"total"`
	Message string                        `json:"message"`
}

type PmsSoftwareProjectResponse struct {
	RowNum           int    `json:"rownum"`
	ProjectID        int    `json:"projectId" `
	SetUpType        string `json:"setUpType"`
	ProjectName      string `json:"projectName" `
	ProjectClass     string `json:"projectClass"`
	Classification   string `json:"classification" `
	ProjectType      string `json:"projectType"`
	BaseProject      string `json:"baseProject"`
	CustomList       string `json:"customList"`
	ProductLineName  string `json:"productLineName"`
	ProjectRequire   string `json:"projectRequire"`
	FCS              string `json:"fcs"`
	ProjectBeginTime string `json:"project_begin_time"`
	ProjectEndTime   string `json:"project_end_time"`
	ProjectNotice    string `json:"projectNotice"`
	CaName           string `json:"caName"`
	CmaName          string `json:"cmaName"`
	PmName           string `json:"pmName"`
	PtmName          string `json:"ptmName"`
	PqaName          string `json:"pqaName"`
	Disabled         bool   `json:"disabled"`
	IsNormal         bool   `json:"isNormal"`
	ProjectStatus    string `json:"projectStatus"`
	ApplyTime        string `json:"applyTime"`
	LastUpdateTime   string `json:"lastUpdateTime"`
}

type PmsSoftwareProject struct {
	datasync.PmsSoftwareProject
}

type ListResponse struct {
	PmsSoftwareProject
}

type Request struct {
	Id uint `json:"id"`
}

func (this *PmsSoftwareProject) ModelName() string {
	return ModelName
}

func Model() *datasync.PmsSoftwareProject {
	return &datasync.PmsSoftwareProject{}
}

func (this *PmsSoftwareProject) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *PmsSoftwareProject) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *PmsSoftwareProject) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *PmsSoftwareProject) CreateV2(object interface{}) error {
	return nil
}

func (this *PmsSoftwareProject) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *PmsSoftwareProject) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *PmsSoftwareProject) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *PmsSoftwareProject) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *PmsSoftwareProject) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *PmsSoftwareProject) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func UpdateOrCreateBugProjectTransaction(tx *gorm.DB, projects []*PmsSoftwareProjectResponse) error {
	batchCreateObjects := []map[string]interface{}{}

	for _, project := range projects {
		response := dproject.Response{}
		projectsObject := map[string]interface{}{
			"project_id":     project.ProjectID,
			"project_name":   project.ProjectName,
			"pm_name":        project.PmName,
			"project_status": project.ProjectStatus,
			"project_type":   project.ProjectType,
			"base_project":   project.BaseProject,
			// "created_at":     project.ApplyTime,
			// "updated_at":     project.LastUpdateTime,
		}

		if project.ApplyTime != "" {
			projectsObject["created_at"] = project.ApplyTime
		}
		if project.LastUpdateTime != "" {
			projectsObject["updated_at"] = project.LastUpdateTime
		}

		if err := tx.Model(dproject.Model()).Where("project_id = ?", project.ProjectID).Find(&response).Error; err != nil {
			return err
		} else {
			if response.ProjectID == 0 {
				batchCreateObjects = append(batchCreateObjects, projectsObject)
			} else {
				if err := tx.Model(dproject.Model()).Where("project_id = ?", project.ProjectID).Updates(projectsObject).Error; err != nil {
					return err
				}
			}
		}
	}
	if len(batchCreateObjects) > 0 {
		if err := tx.Model(dproject.Model()).Create(batchCreateObjects).Error; err != nil {
			return err
		}
	}
	return nil
}

func UpdateOrCreatePmsSoftwareProjectTransaction(items []*PmsSoftwareProjectResponse, _url string, data map[string]string, method, state, errorMsg string) error {
	objects := []map[string]interface{}{}
	for _, item := range items {
		object := map[string]interface{}{
			"ProjectID":       item.ProjectID,
			"SetUpType":       item.SetUpType,
			"ProjectName":     item.ProjectName,
			"Classification":  item.Classification,
			"ProductLineName": item.ProductLineName,
			"ProjectType":     item.ProjectType,
			"BaseProject":     item.BaseProject,
			"CustomList":      item.CustomList,
			"ProjectRequire":  item.ProjectRequire,
			"ProjectClass":    item.ProjectClass,
			"ProjectNotice":   item.ProjectNotice,
			"CaName":          item.CaName,
			"CmaName":         item.CmaName,
			"PmName":          item.PmName,
			"PtmName":         item.PtmName,
			"PqaName":         item.PqaName,
			"ProjectStatus":   item.ProjectStatus,
			"disabled":        item.Disabled,
			"IsNormal":        item.IsNormal,
		}

		/*
			"ApplyTime":        item.ApplyTime,
			"LastUpdateTime":   item.LastUpdateTime,
			"ProjectBeginTime": item.ProjectBeginTime,
			"ProjectEndTime":   item.ProjectEndTime,
						"FCS":            item.FCS,
		*/
		if item.ApplyTime != "" {
			object["ApplyTime"] = item.ApplyTime
		}

		if item.LastUpdateTime != "" {
			object["LastUpdateTime"] = item.LastUpdateTime
		}
		if item.ProjectBeginTime != "" {
			object["ProjectBeginTime"] = item.ProjectBeginTime
		}
		if item.ProjectEndTime != "" {
			object["ProjectEndTime"] = item.ProjectEndTime
		}
		if item.FCS != "" {
			object["FCS"] = item.FCS
		}
		objects = append(objects, object)
	}

	columns := []string{}

	xt := reflect.TypeOf(Model())
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")
		if ok && ok2 {
			columns = append(columns, name)
		}
	}

	body, err := json.Marshal(data)
	if err != nil {
		return err
	}
	db := easygorm.GetEasyGormDb()
	err = db.Transaction(func(tx *gorm.DB) error {
		if len(objects) > 0 {
			err := tx.Model(Model()).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "project_id"}},
				DoUpdates: clause.AssignmentColumns(columns),
			}).Create(&objects).Error
			if err != nil {
				return err
			}
		}

		err := UpdateOrCreateBugProjectTransaction(tx, items)
		if err != nil {
			return err
		}

		if err := tx.Model(dsyncrecord.Model()).Create(map[string]interface{}{
			"url":             _url,
			"body":            body,
			"method":          method,
			"state":           state,
			"message":         errorMsg,
			"min_modify_date": data["minModifyDate"],
			"max_modify_date": data["maxModifyDate"],
			"created_at":      time.Now(),
		}).Error; err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func GetSoftwareProjectData(page int, pageSize int) ([]*ListResponse, error) {
	items := []*ListResponse{}
	offset := (page - 1) * pageSize
	err := easygorm.GetEasyGormDb().Model(Model()).
		Where("project_class != 'HARDWARE_PROJECT'").
		Order("project_id desc").
		Offset(offset).
		Limit(pageSize).
		Find(&items).Error

	return items, err
}
