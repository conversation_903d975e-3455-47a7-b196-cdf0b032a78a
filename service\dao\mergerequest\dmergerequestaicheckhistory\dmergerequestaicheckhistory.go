package dmergerequestaicheckhistory

import (
	"fmt"
	"strings"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/mergerequest"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "MR表单AI评审统计表"

type MergeRequestAiCheckHistory struct {
	mergerequest.MergeRequestAiCheckHistory
}

// type MergeRequest struct {
// 	ID   uint   `json:"id"`
// 	Name string `json:"name"`
// }

type ListResponse struct {
	MergeRequestAiCheckHistory
}

type Request struct {
	mergerequest.MergeRequestAiCheckHistory
}

func (a *MergeRequestAiCheckHistory) ModelName() string {
	return ModelName
}

func Model() *mergerequest.MergeRequestAiCheckHistory {
	return &mergerequest.MergeRequestAiCheckHistory{}
}

func (a *MergeRequestAiCheckHistory) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Preload("Assignees").Preload("Reviewers")
	where := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		where = where.Where("group_name_ch like ?", fmt.Sprintf("%%%s%%", name)).
			Or("group_name_en like ?", fmt.Sprintf("%%%s%%", name)).
			Or("category_name_ch like ?", fmt.Sprintf("%%%s%%", name)).
			Or("category_name_en like ?", fmt.Sprintf("%%%s%%", name)).
			Or("description_ch like ?", fmt.Sprintf("%%%s%%", name)).
			Or("description_en like ?", fmt.Sprintf("%%%s%%", name))
		if strings.Contains("海外库", name) {
			where = where.Or("oversea = 1")
		}
		if strings.Contains("大库", name) {
			where = where.Or("large = 1")
		}
		if strings.Contains("小库", name) {
			where = where.Or("small = 1")
		}
		if strings.Contains("中库", name) {
			where = where.Or("middle = 1")
		}
		if strings.Contains("默认阻断", name) {
			where = where.Or("pre_def_block = 1")
		}
	}
	db = db.Where(where)
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *MergeRequestAiCheckHistory) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *MergeRequestAiCheckHistory) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *MergeRequestAiCheckHistory) CreateV2(object interface{}) error {
	return nil
}

func (a *MergeRequestAiCheckHistory) BatchCreate(mrs []*MergeRequestAiCheckHistory) error {
	err := easygorm.GetEasyGormDb().Create(&mrs).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (a *MergeRequestAiCheckHistory) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *MergeRequestAiCheckHistory) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *MergeRequestAiCheckHistory) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *MergeRequestAiCheckHistory) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *MergeRequestAiCheckHistory) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *MergeRequestAiCheckHistory) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Limit(1).Order("id desc").Find(u).Error
	return err
}

func (u *MergeRequestAiCheckHistory) Last(mergeRequestID uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("merge_request_id = ?", mergeRequestID).Order("id desc").Limit(1).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *MergeRequestAiCheckHistory) Save() error {
	err := easygorm.GetEasyGormDb().Model(u).Save(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update merge request err ", err)
		return err
	}
	return nil
}

func BatchCreate(records []map[string]interface{}) error {
	uniqueColumns := []clause.Column{
		{Name: "project_id"},
		{Name: "merge_request_i_id"},
		{Name: "new_path"},
		{Name: "chunk_md5"},
	}
	db := easygorm.GetEasyGormDb().Model(Model())
	err := db.Transaction(func(tx *gorm.DB) error {
		limit := 500

		for loop := 0; true; loop++ {
			start := loop * limit
			if start > len(records) {
				break
			}
			end := (loop + 1) * limit
			if len(records) <= (loop+1)*limit {
				end = len(records)
			}

			err := tx.Model(Model()).Clauses(clause.OnConflict{
				Columns:   uniqueColumns,
				DoUpdates: clause.AssignmentColumns([]string{"updated_at"}),
			}).Create(records[start:end]).Error
			if err != nil {
				return err
			}
		}

		return nil
	})
	return err
}

func (u *MergeRequestAiCheckHistory) CheckChunkMD5(projectID, mergeRequestIID uint, new_path, md5 string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("project_id = ? and merge_request_i_id = ? and new_path = ? and chunk_md5 = ?", projectID, mergeRequestIID, new_path, md5).Limit(1).Order("id desc").Find(u).Error
	return err
}
