# PM节点技术实现代码示例

## 1. 核心处理函数

### 1.1 handlePMSubmitNode函数结构

<augment_code_snippet path="application/controllers/productionrelease/procinst.go" mode="EXCERPT">
````go
func handlePMSubmitNode(ctx iris.Context, procInst *dproductionprocinst.Response, request *TaskRequest) (map[string]interface{}, error) {
    featureObject := map[string]interface{}{
        "UpdatedAt": time.Now(),
    }

    // 处理主程序URL
    mainProgramUrl := request.MainProgramUrl
    if mainProgramUrl == "" {
        return nil, fmt.Errorf("主程序URL不能为空")
    }
    // ... 更多处理逻辑
}
````
</augment_code_snippet>

### 1.2 主要处理步骤

#### 1.2.1 URL验证和文件名提取
```go
// 验证主程序URL
featureObject["MainProgramUrl"] = mainProgramUrl
mainProgramFileName := extractFileNameFromURL(mainProgramUrl)
if mainProgramFileName == "" {
    return nil, fmt.Errorf("无法从URL提取主程序文件名")
}
featureObject["MainProgramFileName"] = mainProgramFileName
```

#### 1.2.2 OSPKG URL自动推导
```go
// 处理ospkg安装URL
ospkgInstallUrl := request.OspkgInstallUrl
if ospkgInstallUrl == "" {
    newURL, err := RemoveSpecificSegmentFromURL(mainProgramUrl, "releaseID-bin")
    if err != nil {
        return nil, err
    }
    
    statusCode, _, bodyBytes, err := libs.Get(newURL, map[string]string{}, map[string]string{})
    if err != nil {
        return nil, fmt.Errorf("主程序链接异常：%w", err)
    }
    
    content := string(bodyBytes)
    links := ExtractOspkgLinks(content)
    if len(links) > 0 {
        ospkgInstallUrl = generateNewURL(newURL, string(links[0]))
    }
}
```

#### 1.2.3 编译记录关联
```go
// 获取编译版本号
re := regexp.MustCompile(`/output/([a-f0-9]{32})/`)
mainProgramMatches := re.FindStringSubmatch(mainProgramUrl)
mainProgramJobID := ""
if len(mainProgramMatches) > 1 {
    mainProgramJobID = mainProgramMatches[1]
}

// 获取每日编译记录
cronMakeJob := &dcronmakejob.CronMakeJob{}
if mainProgramJobID != "" {
    err := easygorm.GetEasyGormDb().Model(&buildfarm.CronMakeJob{}).
        Where("job_id = ?", mainProgramJobID).Find(&cronMakeJob).Error
    if err != nil {
        return nil, fmt.Errorf("获取编译记录失败: %w", err)
    }
}
```

#### 1.2.4 文件下载和MD5计算
```go
// 创建临时存放文件夹
tempDir := filepath.Join(libs.Config.ProductionFileStorage.Temp, "production", 
    time.Now().Format("20060102"), uuid)
if err := createDirIfNotExist(tempDir); err != nil {
    return nil, fmt.Errorf("创建临时目录失败: %w", err)
}

// 下载主程序文件
mainProgramFilePath := filepath.Join(tempDir, mainProgramFileName)
if err := downloadFile(mainProgramUrl, mainProgramFilePath); err != nil {
    return nil, fmt.Errorf("主程序文件下载失败: %w", err)
}

// 获取主程序文件MD5和大小
mainProgramFileMd5, err := libs.GetFileMd5(mainProgramFilePath)
if err != nil {
    return nil, fmt.Errorf("获取主程序MD5失败: %w", err)
}
mainProgramFileMd5 = strings.ToUpper(mainProgramFileMd5)
mainProgramFileSize := getFileSize(mainProgramFilePath)
```

## 2. 数据库事务处理

### 2.1 事务更新逻辑

<augment_code_snippet path="service/transaction/productionrelease/transproductionrelease/transproductionrelease.go" mode="EXCERPT">
````go
// PM 节点处理逻辑
if task.NodeID == "pm_submit" {
    // 从 taskObject 中获取 PM 节点提交的程序信息
    updateFields := map[string]interface{}{}
    
    // 检查并更新各字段
    if mainProgramUrl, ok := taskObject["MainProgramUrl"]; ok {
        updateFields["MainProgramUrl"] = mainProgramUrl
    }
    if mainProgramFileName, ok := taskObject["MainProgramFileName"]; ok {
        updateFields["MainProgramFileName"] = mainProgramFileName
    }
    // ... 更多字段更新
}
````
</augment_code_snippet>

### 2.2 打回处理逻辑
```go
// 处理打回到pm_submit节点的情况
if taskObject["NextNodeID"].(string) == "pm_submit" {
    // 清除相关数据，避免下次提交时检查到相同文件发布记录
    updateFields := map[string]interface{}{
        "MainProgramUrl":      "",
        "MainProgramFileName": "",
        "MainProgramFileSize": 0,
        "MainProgramFileMd5":  "",
        "OspkgInstallUrl":     "",
        "OspkgInstallName":    "",
        "OspkgInstallSize":    0,
        "OspkgInstallMd5":     "",
        "SoftwareNumber":      "",
        "SoftwareVersion":     "",
    }
    
    // 更新production表
    if err := tx.Model(dproduction.Model()).Where("id = ?", procInst.ProductionID).
        UpdateColumns(updateFields).Error; err != nil {
        return err
    }
}
```

## 3. 流程控制集成

### 3.1 主控制器中的PM节点处理

<augment_code_snippet path="application/controllers/productionrelease/procinst.go" mode="EXCERPT">
````go
// 处理PM节点（程序提交）
if request.NodeID == "pm_submit" && request.Status == 1 {
    var err error
    // 使用PM节点处理函数
    pmNodeFeatures, err := handlePMSubmitNode(ctx, &procInst, request)
    if err != nil {
        handleCustomError(ctx, err.Error())
        return
    }
    
    // 将PM节点处理的结果合并到featureObject
    for key, value := range pmNodeFeatures {
        featureObject[key] = value
    }
}
````
</augment_code_snippet>

### 3.2 请求参数结构
```go
type TaskRequest struct {
    Comment             string `form:"comment"`
    Status              uint   `form:"status"`
    NodeID              string `form:"nodeId"`
    NextNodeID          string `form:"nextNodeID"`
    TaskID              uint   `form:"taskId"`
    UserID              uint   `form:"userId"`
    
    // PM节点特有字段
    MainProgramUrl      string `form:"main_program_url"`
    OspkgInstallUrl     string `form:"ospkg_install_url"`
    
    // 其他节点字段...
}
```

## 4. 辅助函数实现

### 4.1 文件名提取函数
```go
func extractFileNameFromURL(url string) string {
    parsedURL, err := url.Parse(url)
    if err != nil {
        return ""
    }
    return path.Base(parsedURL.Path)
}
```

### 4.2 OSPKG链接提取函数
```go
func ExtractOspkgLinks(content string) []string {
    re := regexp.MustCompile(`href="([^"]*ospkg[^"]*)"`)
    matches := re.FindAllStringSubmatch(content, -1)
    
    var links []string
    for _, match := range matches {
        if len(match) > 1 {
            links = append(links, match[1])
        }
    }
    return links
}
```

### 4.3 URL段移除函数
```go
func RemoveSpecificSegmentFromURL(originalURL, segment string) (string, error) {
    parsedURL, err := url.Parse(originalURL)
    if err != nil {
        return "", err
    }
    
    pathParts := strings.Split(parsedURL.Path, "/")
    var newParts []string
    
    for _, part := range pathParts {
        if part != segment {
            newParts = append(newParts, part)
        }
    }
    
    parsedURL.Path = strings.Join(newParts, "/")
    return parsedURL.String(), nil
}
```

## 5. 错误处理机制

### 5.1 统一错误处理
```go
func handleError(ctx iris.Context, err error, message string) {
    logging.ErrorLogger.Errorf("%s: %v", message, err)
    ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
}

func handleCustomError(ctx iris.Context, message string) {
    ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, message))
}
```

### 5.2 常见错误类型
```go
// URL相关错误
if mainProgramUrl == "" {
    return nil, fmt.Errorf("主程序URL不能为空")
}

// 文件下载错误
if err := downloadFile(mainProgramUrl, mainProgramFilePath); err != nil {
    return nil, fmt.Errorf("主程序文件下载失败: %w", err)
}

// 重复发布检查
if check.ID > 0 {
    return nil, fmt.Errorf("已经存在相同文件发布记录")
}
```

## 6. 配置和常量

### 6.1 配置项
```go
// 文件存储配置
libs.Config.ProductionFileStorage.Temp     // 临时目录
libs.Config.ProductionFileStorage.Upload   // 上传目录
libs.Config.ProductionFileStorage.OutputPath // 输出目录
```

### 6.2 节点ID常量
```go
const (
    NodeStart         = "start"
    NodePMSubmit      = "pm_submit"
    NodeSystemTemplate = "system_template"
    NodePlatformMg    = "platform_mg"
    // ... 其他节点
)
```

---

**说明**: 以上代码示例展示了PM节点的核心技术实现，包括数据处理、文件操作、数据库事务等关键部分。实际实现中还包含更多的错误处理、日志记录和性能优化代码。
