package dapprovalcomment

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/fileout"
	"irisAdminApi/service/dao/user/duser"
)

const ModelName = "申请单说明"

type Response struct {
	Id         uint                   `json:"id"`
	UpdatedAt  string                 `json:"updated_at"`
	CreatedAt  string                 `json:"created_at"`
	ApprovalId uint                   `json:"approval_id" `
	Action     string                 `json:"action" `
	Comment    string                 `json:"comment" `
	UserId     uint                   `json:"user_id" `
	User       duser.ApprovalResponse `gorm:"-" json:"user"`
}

type ListResponse struct {
	Response
}

type ApprovalCommentReq struct {
	Id         uint   `json:"id"`
	ApprovalId uint   `json:"approval_id" `
	Comment    string `json:"comment" `
	UserId     uint   `json:"user_id" `
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *fileout.ApprovalComment {
	return &fileout.ApprovalComment{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse
	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindCommentsByApprovalId(aId uint) (map[string]interface{}, error) {
	var count int64
	var comments []*ListResponse
	db := easygorm.GetEasyGormDb().Model(Model()).Where("approval_id = ?", aId)
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get filedetail count err ", err)
		return nil, err
	}
	err = db.Find(&comments).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get filedetail err ", err)
		return nil, err
	}
	formatResponse(comments)
	list := map[string]interface{}{"items": comments, "total": count, "limit": 0}
	return list, nil
}

func formatResponse(comments []*ListResponse) {
	for _, comment := range comments {
		var user duser.ApprovalResponse
		user.Find(comment.UserId)
		comment.User = user
	}
}
