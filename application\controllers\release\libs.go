package release

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/cache"
	"irisAdminApi/service/dao/user/duser"
	"strings"
)

func PushMailQueue(userID uint, subject, body string) {
	if libs.Config.Mail.Enable {
		rc := cache.GetRedisClusterClient()
		from := "版本发布系统"
		to := duser.UserMap[userID].Username + "@ruijie.com.cn"
		// subject := fmt.Sprintf("【版本发布系统】【项目:%s】【%s】", filename, status)
		// 	fileTable := `<div class=3D"pro-table">
		// 	<table width=3D"600" style=3D"border-collapse:collapse;">
		// 		<tr>
		// 			<td colspan=3D"2" style=3D"color:#000000; text-align:center; background=
		// -color:#E8F1FF;border-top: 1px solid #95B8E7;">=E4=BB=BB=E5=8A=A1=E5=9F=BA=
		// =E6=9C=AC=E4=BF=A1=E6=81=AF</td>
		// 		</tr>
		// 		<tr>
		// 			<th>=E4=BB=BB=E5=8A=A1=E5=90=8D=E7=A7=B0:</th>
		// 			<td>=E3=80=90linjiakai-web=E3=80=91=E5=B7=A5=E4=BD=9C=E5=8C=85=E7=BC=96=
		// =E7=A0=81</td>
		// 		</tr>
		// 		<tr>
		// 			<th>=E9=A1=B9=E7=9B=AE:</th>
		// 			<td>NTOS1.0R3</td>
		// 		</tr>
		// 		<tr>
		// 			<th>=E4=BB=BB=E5=8A=A1=E7=B1=BB=E5=9E=8B:</th>
		// 			<td>=E7=BC=96=E7=A0=81</td>
		// 		</tr>
		// 		<tr>
		// 			<th>=E5=88=86=E9=85=8D=E7=BB=99:</th>
		// 			<td>=E6=9E=97=E5=AE=B6=E6=A5=B7</td>
		// 		</tr>
		// 		<tr>
		// 			<th>=E8=AE=A1=E5=88=92=E5=BC=80=E5=A7=8B=E6=97=B6=E9=97=B4:</th>
		// 			<td>2022-04-25 00:00:00</td>
		// 		</tr>
		// 		<tr>
		// 			<th>=E8=AE=A1=E5=88=92=E7=BB=93=E6=9D=9F=E6=97=B6=E9=97=B4:</th>
		// 			<td>2022-05-30 00:00:00</td>
		// 		</tr>
		// 		<tr>
		// 			<th>=E5=88=9B=E5=BB=BA=E4=BA=BA:</th>
		// 			<td>=E7=8E=8B=E5=BF=97=E8=90=A5</td>
		// 		</tr>
		// 		<tr>
		// 			<th>=E5=88=9B=E5=BB=BA=E6=97=B6=E9=97=B4:</th>
		// 			<td>2022-04-12 14:21:39</td>
		// 		</tr>
		// 		<tr>
		// 			<th>=E4=BB=BB=E5=8A=A1=E7=AE=80=E8=BF=B0:</th>
		// 			<td>=E3=80=90NTOS1.0R3=E3=80=91=E9=A1=B9=E7=9B=AE=E3=80=90linjiakai-web=
		// =E3=80=91=E5=B7=A5=E4=BD=9C=E5=8C=85=E7=BC=96=E7=A0=81<br>=E3=80=90=E9=9C=
		// =80=E8=A6=81=E7=BB=93=E5=90=88=E9=AB=98=E4=BF=9D=E8=AE=BE=E8=AE=A1=E8=AF=
		// =84=E4=BC=B0=E5=B7=A5=E4=BD=9C=E9=87=8F=E3=80=912. =E6=8E=A5=E5=8F=A3=E6=
		// =B5=81=E9=87=8F=E8=B6=8B=E5=8A=BF=E5=9B=BE=EF=BC=8C=E6=97=A0=E6=B3=95=E6=
		// =A0=B9=E6=8D=AE=E8=87=AA=E5=B7=B1=E9=9C=80=E6=B1=82=EF=BC=8C=E5=90=8C=E6=
		// =97=B6=E9=80=89=E6=8B=A9=E5=A4=9A=E4=B8=AA=E6=8E=A5=E5=8F=A3=E7=9A=84=E6=
		// =B5=81=E9=87=8F=E6=9B=B2=E7=BA=BF</td>
		// 		</tr>
		// 	</table>
		// </div>`
		body := fmt.Sprintf(`%s<br>%s`, subject, body)
		msg := strings.Join([]string{from, to, subject, body}, "|")
		_, err := rc.LPush(libs.Config.Mail.Queue, msg)
		if err != nil {
			logging.ErrorLogger.Error(err)
		}
	}
}
