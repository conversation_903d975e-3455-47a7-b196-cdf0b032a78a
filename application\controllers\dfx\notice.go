package dfx

import (
	"fmt"
	"sort"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
)

// func WeeklyPerformanceCheck(distinctProductBranch []*dperformance.ListResponse) (map[string][]string, error) {
// 	msg := "检测到%s %s %v 指标%s本周平均值为: %v, 上周平均值为: %v, 下降超过 %s"

// 	result := map[string][]string{}
// 	columns := dperformance.GetColumns()

// 	for _, item := range distinctProductBranch {

// 		performances, err := dperformance.FindWeeklyCheck(item.Product, item.Branch)
// 		if err != nil {
// 			return result, err
// 		}
// 		if len(performances) < 2 {
// 			continue
// 		}
// 		last := performances[2]
// 		result[last.Branch] = []string{}
// 		for _, column := range columns {

// 			_perf0 := libs.StructToString(performances[1])
// 			_perf1 := libs.StructToString(performances[0])
// 			perf0 := map[string]interface{}{}
// 			perf1 := map[string]interface{}{}
// 			err := json.Unmarshal([]byte(_perf0), &perf0)
// 			if err != nil {
// 				return nil, err
// 			}
// 			err = json.Unmarshal([]byte(_perf1), &perf1)
// 			if err != nil {
// 				return nil, err
// 			}

// 			metric0, ok0 := perf0[column]
// 			metric1, ok1 := perf1[column]
// 			if ok0 && ok1 && metric0.(float64) > 0 {
// 				delta := float32((metric1.(float64) - metric0.(float64)) / metric0.(float64))
// 				if delta < -0.05 {
// 					result[last.Branch] = append(result[last.Branch], fmt.Sprintf(msg, last.Branch, last.Product, last.Version, column, metric1, metric0, "5%"))
// 				}
// 			}
// 		}
// 	}

// 	return result, nil
// }

func SendWeekReport() error {
	result, err := DfxMemoryWeekCheck()
	if err != nil {
		logging.ErrorLogger.Error(err)
		return err
	}

	mailTo := []string{"<EMAIL>"}
	// mailTo := []string{"<EMAIL>"}
	cc := []string{"<EMAIL>"}

	subject := "DFX内存周报"
	body := ""
	for branch := range result {
		for product := range result[branch] {
			for rfid := range result[branch][product] {
				for rfscene := range result[branch][product][rfid] {
					url := fmt.Sprintf("http://10.51.135.15:9090/grafana/d/6_0Y3vQSz/dfxnei-cun-zhou-bao?orgId=1&var-branch=%s&var-product=%s&var-rfid=%s&var-rfscene=%s", branch, product, rfid, rfscene)
					flag := false
					for _type := range result[branch][product][rfid][rfscene] {
						summaryMsgs := result[branch][product][rfid][rfscene][_type]["summary"]
						sort.Slice(summaryMsgs, func(i, j int) bool {
							return summaryMsgs[i][0].(float64) > summaryMsgs[j][0].(float64)
						})
						detialMsgs := result[branch][product][rfid][rfscene][_type]["detail"]
						sort.Slice(detialMsgs, func(i, j int) bool {
							return detialMsgs[i][0].(float64) > detialMsgs[j][0].(float64)
						})

						if len(detialMsgs) > 0 || len(summaryMsgs) > 0 {
							flag = true

							// 处理 summaryMsgs
							for _, msg := range summaryMsgs {
								highlightStyle := ""
								var value1, value2 float64

								// 安全地处理 msg[1] 类型转换
								switch v := msg[1].(type) {
								case float64:
									value1 = v
								case int:
									value1 = float64(v)
								default:
									// 如果不是 int 或 float64，跳过当前 msg
									continue
								}

								// 安全地处理 msg[2] 类型转换
								switch v := msg[2].(type) {
								case float64:
									value2 = v
								case int:
									value2 = float64(v)
								default:
									// 如果不是 int 或 float64，跳过当前 msg
									continue
								}

								// 比较并生成内容
								if value1 >= value2 {
									highlightStyle = ` style="color: red"`
								}

								body = fmt.Sprintf(`%s<h4%s>%s</h4>`, body, highlightStyle, msg[3].(string))
							}

							// 处理 detialMsgs
							for _, msg := range detialMsgs {
								body = fmt.Sprintf("%s<p>%s</p>", body, msg[1].(string))
							}
						}
					}
					if flag {
						body = fmt.Sprintf(`%s<p>详情查看：<a href="%s">DFX内存周报</a></p><br><br>`, body, url)
					}
				}
			}
		}
	}
	err = libs.SendMail(mailTo, subject, body, cc)
	if err != nil {
		logging.ErrorLogger.Error(err)
	}
	return nil
}
