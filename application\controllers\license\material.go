package license

import (
	"github.com/kataras/iris/v12"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/service/dao/license/dmaterial"
	"strconv"
)

func ListMaterials(ctx iris.Context) {
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	deviceModel := ctx.FormValue("device_model")
	materialCode := ctx.FormValue("material_code")
	id, _ := strconv.Atoi(ctx.FormValue("id"))
	sort := ctx.FormValue("sort")
	orderBy := ctx.FormValue("orderBy")
	createdAt := ctx.FormValue("created_at")
	updatedAt := ctx.FormValue("updated_at")

	list, err := dmaterial.ListMaterials(page, pageSize, deviceModel, materialCode, uint(id), sort, orderBy, createdAt, updatedAt)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}
