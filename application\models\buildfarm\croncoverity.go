package buildfarm

import "irisAdminApi/application/models"

type CronCoverityWindow struct {
	models.ModelBase
	Time         uint   `gorm:"not null" json:"time"`
	TimeLabel    string `gorm:"not null; type:varchar(50)" json:"time_label"`
	WeekDay      uint   `gorm:"week_day" json:"week_day"`
	WeekDayLabel string `gorm:"not null; type:varchar(50)" json:"week_day_label"`
}

type CronCoveritySchedule struct {
	models.ModelBase
	CronCoverityWindowID uint   `gorm:"not null" json:"cron_coverity_window_id"`
	Project              string `gorm:"not null; type:varchar(100)" json:"project"`
	Product              string `gorm:"not null; type:varchar(100)" json:"product"`
	BaseProduct          string `gorm:"not null; type:varchar(100)" json:"base_product"`
	UserID               uint   `gorm:"not null" json:"user_id"`
}
