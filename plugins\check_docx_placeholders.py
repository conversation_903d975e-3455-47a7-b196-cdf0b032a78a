# 检查docx文档中是否存在指定的占位符
import sys
import json
import re
from docx import Document

def check_placeholders_in_docx(docx_path, placeholders):
    """检查Word文档中是否存在指定的占位符"""
    try:
        doc = Document(docx_path)
        found_placeholders = []
        
        # 合并所有段落的文本
        all_text = ""
        for para in doc.paragraphs:
            all_text += para.text
        
        # 检查表格中的文本
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for para in cell.paragraphs:
                        all_text += para.text
        
        # 检查页眉和页脚
        for section in doc.sections:
            # 检查页眉
            if section.header:
                for para in section.header.paragraphs:
                    all_text += para.text
                for table in section.header.tables:
                    for row in table.rows:
                        for cell in row.cells:
                            for para in cell.paragraphs:
                                all_text += para.text
            
            # 检查页脚
            if section.footer:
                for para in section.footer.paragraphs:
                    all_text += para.text
                for table in section.footer.tables:
                    for row in table.rows:
                        for cell in row.cells:
                            for para in cell.paragraphs:
                                all_text += para.text
        
        # 检查所有占位符
        for placeholder in placeholders:
            if placeholder in all_text:
                found_placeholders.append(placeholder)
        
        return found_placeholders
    
    except Exception as e:
        print(f"检查文档时出错: {e}")
        return []

def main():
    if len(sys.argv) < 2:
        print("用法: python check_docx_placeholders.py <Word文档路径> [占位符JSON]")
        sys.exit(1)
    
    docx_path = sys.argv[1]
    
    # 默认占位符列表
    default_placeholders = [
        "{{size}}",
        "{{md5}}",
        "{{name}}",
        "{{softwarenum}}",
        "{{softwarever}}",
        "{{ospkgName}}",
        "{{ospkgMD5}}",
        "{{ospkgSize}}",
        "{{ubootVersion}}",
        "{{time}}",
        "{{productModel}}"
    ]
    
    # 如果提供了占位符列表，则使用提供的列表
    if len(sys.argv) > 2:
        try:
            placeholders = json.loads(sys.argv[2])
        except json.JSONDecodeError:
            print("错误: 占位符参数的JSON格式无效。使用默认占位符列表。")
            placeholders = default_placeholders
    else:
        placeholders = default_placeholders
    
    # 检查文档中的占位符
    found_placeholders = check_placeholders_in_docx(docx_path, placeholders)
    
    # 准备输出结果
    result = {
        "found": len(found_placeholders) > 0,
        "placeholders": found_placeholders
    }
    
    # 输出结果为JSON格式
    print(json.dumps(result, ensure_ascii=False))

if __name__ == "__main__":
    main() 