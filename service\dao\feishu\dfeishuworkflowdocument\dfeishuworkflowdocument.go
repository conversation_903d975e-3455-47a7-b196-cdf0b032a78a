package dfeishuworkflowdocument

import (
	"fmt"
	"reflect"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models"
	"irisAdminApi/application/models/feishu"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "飞书流程文档表"

type Response struct {
	models.ModelBase
	WorkflowID                string `gorm:"type:varchar(200)" json:"workflow_id" `
	ProjectKey                string `gorm:"type:varchar(100)" json:"project_key" `
	WorkItemTypeKey           string `gorm:"type:varchar(200)" json:"work_item_type_key" `
	ProjectName               string `gorm:"type:varchar(200)" json:"project_name" `
	PatchFile                 string `gorm:"type:varchar(200)" json:"patch_file" `
	Status                    uint   `gorm:"not null" json:"status" `
	PatchApplicableModels     string `gorm:"type:varchar(200)" json:"patch_applicable_models" `
	PatchApplicableVersions   string `gorm:"type:varchar(200)" json:"patch_applicable_versions" `
	IssuesResolvedTestVersion string `gorm:"type:varchar(200)" json:"issues_resolved_test_version" `
	DocFilePath               string `gorm:"type:varchar(200)" json:"doc_file_path" `
}

type ListResponse struct {
	Response
}

type Request struct {
	Id uint `json:"id"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *feishu.FeishuWorkflowDocument {
	return &feishu.FeishuWorkflowDocument{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (a *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (u *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (u *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindByNameAndProjectName(name, projectName string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("name = ? and project_name=?", name, projectName).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func GetProjectData() ([]*Response, error) {
	var items []*Response
	if err := easygorm.GetEasyGormDb().Model(Model()).Where("status =1").Find(&items).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return items, err
	}
	return items, nil
}

func CreateOrUpdateDocumentCommentData(objects []map[string]interface{}) error {
	ReSetAutoIncrement()
	columns := []string{"updated_at"}
	xt := reflect.TypeOf(Model())
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")
		if ok && ok2 {
			columns = append(columns, name)
		}
	}
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		if len(objects) > 0 {
			err := tx.Model(Model()).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "pms_doc_id"}, {Name: "project_name"}, {Name: "comment_id"}},
				DoUpdates: clause.AssignmentColumns(columns),
			}).Create(&objects).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func ReSetAutoIncrement() error {
	var maxID int
	err := easygorm.GetEasyGormDb().Table("feishu_workflow_documents").Select("MAX(id)").Scan(&maxID).Error
	if err != nil {
		return err
	}
	newAutoIncrement := maxID + 1
	alterTableSQL := fmt.Sprintf("ALTER TABLE feishu_workflow_documents AUTO_INCREMENT=%d", newAutoIncrement)
	if err := easygorm.GetEasyGormDb().Exec(alterTableSQL).Error; err != nil {
		logging.ErrorLogger.Errorf("ALTER TABLE feishu_workflow_documents  get err ", err)
		return err
	}
	return nil
}
