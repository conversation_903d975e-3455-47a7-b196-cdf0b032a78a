package dappmergerequesthistory

import (
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models"
	"irisAdminApi/application/models/appmergerequest"
	"strings"
)

const ModelName = "MR表单同步历史"

type AppMergeRequestHistory struct {
	models.ModelBase
	MergeRequestID     uint `gorm:"not null" json:"merge_request_id"`
	Status             int  `gorm:"not null; type: tinyint" json:"status"` //-1: 创建失败  0： 待评审  1：待合并   2：已合并    3：已关闭
	PipelineStatus     uint `gorm:"not null" json:"pipeline_status"`
	PhabricatorStatus  uint `gorm:"not null" json:"phabricator_status"`
	SyncStatus         uint `gorm:"not null" json:"sync_status"`
	CodeQuantityAdd    uint `gorm:"not null" json:"code_quantity_add"`
	CodeQuantityRemove uint `gorm:"not null" json:"code_quantity_remove"`
}

// type MergeRequest struct {
// 	ID   uint   `json:"id"`
// 	Name string `json:"name"`
// }

type ListResponse struct {
	AppMergeRequestHistory
}

type Request struct {
	appmergerequest.AppMergeRequestHistory
}

func (a *AppMergeRequestHistory) ModelName() string {
	return ModelName
}

func Model() *appmergerequest.AppMergeRequestHistory {
	return &appmergerequest.AppMergeRequestHistory{}
}

func (a *AppMergeRequestHistory) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Preload("Assignees").Preload("Reviewers")
	where := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		where = where.Where("group_name_ch like ?", fmt.Sprintf("%%%s%%", name)).
			Or("group_name_en like ?", fmt.Sprintf("%%%s%%", name)).
			Or("category_name_ch like ?", fmt.Sprintf("%%%s%%", name)).
			Or("category_name_en like ?", fmt.Sprintf("%%%s%%", name)).
			Or("description_ch like ?", fmt.Sprintf("%%%s%%", name)).
			Or("description_en like ?", fmt.Sprintf("%%%s%%", name))
		if strings.Contains("海外库", name) {
			where = where.Or("oversea = 1")
		}
		if strings.Contains("大库", name) {
			where = where.Or("large = 1")
		}
		if strings.Contains("小库", name) {
			where = where.Or("small = 1")
		}
		if strings.Contains("中库", name) {
			where = where.Or("middle = 1")
		}
		if strings.Contains("默认阻断", name) {
			where = where.Or("pre_def_block = 1")
		}
	}
	db = db.Where(where)
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *AppMergeRequestHistory) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *AppMergeRequestHistory) Create(object map[string]interface{}) error {

	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *AppMergeRequestHistory) CreateV2(object interface{}) error {
	return nil
}

func (a *AppMergeRequestHistory) BatchCreate(mrs []*AppMergeRequestHistory) error {
	err := easygorm.GetEasyGormDb().Create(&mrs).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (a *AppMergeRequestHistory) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *AppMergeRequestHistory) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *AppMergeRequestHistory) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *AppMergeRequestHistory) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *AppMergeRequestHistory) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *AppMergeRequestHistory) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *AppMergeRequestHistory) Last(mergeRequestID uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("merge_request_id = ?", mergeRequestID).Order("id desc").Limit(1).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

/*
	models.ModelBase
	MergeRequestID     uint `gorm:"not null" json:"merge_request_id"`
	Status             int  `gorm:"not null; type: tinyint" json:"status"` //-1: 创建失败  0： 创建中  1：待评审   2：待合并    3：已合并  4：已关闭
	PipelineStatus     uint `gorm:"not null" json:"pipeline_status"`
	PhabricatorStatus  uint `gorm:"not null" json:"phabricator_status"`
	SyncStatus         uint `gorm:"not null" json:"sync_status"`
	CodeQuantityAdd    uint `gorm:"not null" json:"code_quantity_add"`
	CodeQuantityRemove uint `gorm:"not null" json:"code_quantity_remove"`
*/

func (u *AppMergeRequestHistory) CreateHistory(mergeRequestID uint, data map[string]interface{}) error {
	his := AppMergeRequestHistory{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("merge_request_id = ?", mergeRequestID).Order("id desc").Limit(1).Find(&his).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find last merge request history err ", err)
		return err
	}
	if his.ID > 0 {
		if _, ok := data["Status"]; !ok {
			data["Status"] = u.Status
		}
		if _, ok := data["Status"]; !ok {
			data["PipelineStatus"] = u.PipelineStatus
		}
		if _, ok := data["PhabricatorStatus"]; !ok {
			data["PhabricatorStatus"] = u.PhabricatorStatus
		}
		if _, ok := data["CodeQuantityAdd"]; !ok {
			data["CodeQuantityAdd"] = u.CodeQuantityAdd
		}
		if _, ok := data["CodeQuantityRemove"]; !ok {
			data["CodeQuantityRemove"] = u.CodeQuantityRemove
		}
	} else {
		if _, ok := data["Status"]; !ok {
			data["Status"] = 0
		}
		if _, ok := data["Status"]; !ok {
			data["PipelineStatus"] = 0
		}
		if _, ok := data["PhabricatorStatus"]; !ok {
			data["PhabricatorStatus"] = 0
		}
		if _, ok := data["CodeQuantityAdd"]; !ok {
			data["CodeQuantityAdd"] = 0
		}
		if _, ok := data["CodeQuantityRemove"]; !ok {
			data["CodeQuantityRemove"] = 0
		}
		if _, ok := data["SyncStatus"]; !ok {
			data["SyncStatus"] = 0
		}
	}

	err = u.Create(data)
	return err
}
