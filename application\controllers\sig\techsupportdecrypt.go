package sig

import (
	"bytes"
	"fmt"
	"io"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/buildfarm/dcronmakejob"
	"irisAdminApi/service/dao/buildfarm/dmakejob"
	"irisAdminApi/service/dao/release/drelease"
	"irisAdminApi/service/dao/sig/dperfanalysisresult"
	"irisAdminApi/service/dao/sig/dtechsupportdecrypt"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gabriel-vasile/mimetype"
	"github.com/kataras/iris/v12"
	"github.com/pkg/sftp"
	"golang.org/x/crypto/ssh"
)

func CreateTechSupportDecryptJob(ctx iris.Context) {
	f, fh, err := ctx.FormFile("file")
	defer f.Close()
	mime, err := mimetype.DetectReader(f)
	if mime.Is("application/gzip") {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "这个是未加密的文件"))
		return
	} else if !mime.Is("application/octet-stream") {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "不是有效的加密文件"))
		return
	}

	userID, _ := dao.GetAuthId(ctx)
	jobID := libs.GetUniqueID()
	createdAt := time.Now()

	upload := filepath.Join(libs.Config.Sig.Upload, createdAt.Format("20060102"), jobID)
	os.MkdirAll(upload, 0750)
	os.Chmod(upload, 0750)
	//先执行文件上传，文件上传成功后执行创建申请单操作

	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	_, err = ctx.SaveFormFile(fh, filepath.Join(upload, fh.Filename))
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	md5, err := libs.GetFileMd5(filepath.Join(upload, fh.Filename))
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	job := dtechsupportdecrypt.SigTechSupportDecryptJob{}
	if err := job.Create(map[string]interface{}{
		"JobID":          jobID,
		"InputFileName":  fh.Filename,
		"InputMD5":       md5,
		"OutputFileName": strings.Replace(fh.Filename, ".tgz", "_decrypted.tgz", -1),
		"OutputMD5":      "",
		"UserID":         userID,
		"Status":         0,
		"CreatedAt":      time.Now(),
		"UpdatedAt":      time.Now(),
	}); err != nil {
		logging.ErrorLogger.Error(err)
		os.RemoveAll(upload)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	err = job.FindEx("job_id", jobID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if job.ID > 0 {
		go func() {
			md5, err := SigTechSupportDecryptWorker(&job)
			if err != nil {
				logging.ErrorLogger.Error(err)
				err = job.Update(job.ID, map[string]interface{}{
					"Status": 2,
				})
				if err != nil {
					logging.ErrorLogger.Error(err)
				}
			} else {
				err = job.Update(job.ID, map[string]interface{}{
					"Status":    1,
					"OutputMD5": md5,
				})
				if err != nil {
					logging.ErrorLogger.Error(err)
				}
			}

		}()
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]interface{}{"job_id": job.JobID, "id": job.ID}, response.NoErr.Msg))
	return
}

func GetTechSupportDecryptJobs(ctx iris.Context) {
	uId := uint(0)

	userID := ctx.FormValue("user_id")
	if userID == "" {
		userID = fmt.Sprintf("%v", uId)
	}

	jobID := ctx.FormValue("job_id")
	name := ctx.FormValue("name")
	md5 := ctx.FormValue("md5")

	start := ctx.FormValue("start")
	end := ctx.FormValue("end")
	status := ctx.FormValue("status")

	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dtechsupportdecrypt.All(userID, jobID, name, md5, start, end, status, sort, orderBy, page, pageSize)
	if err != nil {
		logging.ErrorLogger.Errorf("find sig job get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

// 提取perf文件到备份目录，返回是否找到perf文件
func extractPerfFiles(extractedDir, perfBackupDir string, f *os.File) bool {
	// 检查perf文件目录是否存在
	perfPath := filepath.Join(extractedDir, "fp-perf")
	if _, err := os.Stat(perfPath); err != nil {
		f.WriteString("未发现perf文件目录，跳过perf文件备份\n")
		return false
	}

	// 检查version.txt文件是否存在，没有则不进行perf分析
	versionTxtPath := filepath.Join(extractedDir, "version.txt")
	if _, err := os.Stat(versionTxtPath); err != nil {
		f.WriteString("未找到version.txt文件，后台perf分析需要此文件来获取系统信息，跳过perf分析\n")
		return false
	}

	// 获取所有perf文件
	perfFiles, err := filepath.Glob(filepath.Join(perfPath, "*"))
	if err != nil {
		f.WriteString(fmt.Sprintf("获取perf文件列表失败: %v\n", err))
		return false
	}

	if len(perfFiles) == 0 {
		f.WriteString("未发现perf文件，跳过perf分析\n")
		return false
	}

	// 创建perf备份目录
	if err := os.MkdirAll(perfBackupDir, 0755); err != nil {
		f.WriteString(fmt.Sprintf("创建perf备份目录失败: %v，无法进行perf分析\n", err))
		return false
	}

	// 创建fp-perf子目录，确保与原始目录结构相同
	perfBackupSubDir := filepath.Join(perfBackupDir, "fp-perf")
	if err := os.MkdirAll(perfBackupSubDir, 0755); err != nil {
		f.WriteString(fmt.Sprintf("创建fp-perf子目录失败: %v，无法进行perf分析\n", err))
		return false
	}

	// 复制所有perf文件到备份目录的fp-perf子目录
	f.WriteString("复制perf文件到备份目录的fp-perf子目录以供后台分析使用...\n")
	for _, perfFile := range perfFiles {
		destFile := filepath.Join(perfBackupSubDir, filepath.Base(perfFile))
		if err := copyFile(perfFile, destFile); err != nil {
			f.WriteString(fmt.Sprintf("复制perf文件 %s 失败: %v\n", perfFile, err))
		}
	}
	f.WriteString(fmt.Sprintf("成功复制 %d 个perf文件到备份目录的fp-perf子目录\n", len(perfFiles)))

	// 复制version.txt文件（用于获取系统信息）
	destVersionTxt := filepath.Join(perfBackupDir, "version.txt")
	if err := copyFile(versionTxtPath, destVersionTxt); err != nil {
		f.WriteString(fmt.Sprintf("复制version.txt文件失败: %v\n", err))
		return false
	}
	f.WriteString("成功复制version.txt文件到备份目录\n")

	return true
}

// 启动后台perf分析进程
func startBackgroundPerfAnalysis(jobID, perfBackupDir, tmpDir, workDir string) {
	logFilePath := filepath.Join(workDir, jobID+".log")

	// 启动goroutine执行perf分析
	go func(jobID, perfBackupDirPath, tmpDirPath, logFilePath string) {
		// 打开日志文件，追加模式
		bgLogFile, err := os.OpenFile(logFilePath, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
		if err != nil {
			// 如果无法打开日志文件，则创建一个专用的错误日志
			errorLogPath := filepath.Join(filepath.Dir(logFilePath), jobID+".perf.error.log")
			bgLogFile, _ = os.Create(errorLogPath)
			bgLogFile.WriteString(fmt.Sprintf("无法打开原始日志文件: %s\n", err.Error()))
		}
		defer bgLogFile.Close()

		bgLogFile.WriteString("\n============= 后台perf分析开始 (独立进程) =============\n")
		bgLogFile.WriteString(fmt.Sprintf("开始时间: %s\n", time.Now().Format("2006-01-02 15:04:05")))

		// 获取作业信息以更新状态
		job := dtechsupportdecrypt.SigTechSupportDecryptJob{}
		if err := job.FindEx("job_id", jobID); err != nil {
			bgLogFile.WriteString(fmt.Sprintf("无法获取作业信息: %s\n", err.Error()))
		}

		// 执行perf分析
		if err := processPerfDecryption(perfBackupDirPath, tmpDirPath, jobID, bgLogFile); err != nil {
			bgLogFile.WriteString(fmt.Sprintf("后台perf分析处理失败: %s\n", err.Error()))
			// 如果作业存在，则更新状态为失败
			if job.ID > 0 {
				if updateErr := job.Update(job.ID, map[string]interface{}{
					"PerfStatus": 3, // 失败
				}); updateErr != nil {
					bgLogFile.WriteString(fmt.Sprintf("更新PerfStatus失败: %s\n", updateErr.Error()))
				} else {
					bgLogFile.WriteString("已更新作业PerfStatus为失败\n")
				}
			}
		} else {
			bgLogFile.WriteString("后台perf分析处理成功完成\n")
			// 如果作业存在，则更新状态为完成
			if job.ID > 0 {
				if updateErr := job.Update(job.ID, map[string]interface{}{
					"PerfStatus": 2, // 完成
				}); updateErr != nil {
					bgLogFile.WriteString(fmt.Sprintf("更新PerfStatus失败: %s\n", updateErr.Error()))
				} else {
					bgLogFile.WriteString("已更新作业PerfStatus为完成\n")
				}
			}
		}

		bgLogFile.WriteString(fmt.Sprintf("结束时间: %s\n", time.Now().Format("2006-01-02 15:04:05")))
		bgLogFile.WriteString("============= 后台perf分析结束 =============\n")

		// 清理临时目录
		if err := os.RemoveAll(perfBackupDirPath); err != nil {
			bgLogFile.WriteString(fmt.Sprintf("清理perf备份目录失败: %v\n", err))
		} else {
			bgLogFile.WriteString("已清理perf备份目录\n")
		}
	}(jobID, perfBackupDir, tmpDir, logFilePath)
}

func SigTechSupportDecryptWorker(job *dtechsupportdecrypt.SigTechSupportDecryptJob) (string, error) {
	workDir := job.Dir()
	tmpDir := filepath.Join(workDir, "tmp")

	os.RemoveAll(tmpDir)
	if err := os.MkdirAll(tmpDir, 0755); err != nil {
		return "", err
	}
	os.Chmod(tmpDir, 0755)

	decryptTechsupportFilePath := filepath.Join(workDir, job.OutputFileName)
	cryptTechSupportFilePath := filepath.Join(workDir, job.InputFileName)

	// 创建日志文件
	f, err := os.OpenFile(filepath.Join(workDir, job.JobID+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	defer f.Close()

	// 第一步：使用rg_cpkg工具进行初步解密
	command := fmt.Sprintf(
		`cd "%s" && rm "%s" -rf && ./rg_cpkg -d -f "%s" -k "%s" -o "%s"`,
		libs.Config.Sig.Techsupportdecryptpath,
		decryptTechsupportFilePath,
		cryptTechSupportFilePath,
		libs.Config.Sig.Techsupportdecryptpath,
		decryptTechsupportFilePath,
	)

	stdout, err := libs.ExecCommand(command)
	if err != nil {
		f.WriteString(stdout)
		f.WriteString(err.Error() + "\n")
		return "", err
	}

	// 创建并准备解压目录
	extractedDir := strings.TrimSuffix(decryptTechsupportFilePath, ".tgz")
	if err := os.RemoveAll(extractedDir); err != nil {
		f.WriteString("清理解压目录失败: " + err.Error() + "\n")
		return "", err
	}

	if err := os.MkdirAll(extractedDir, 0755); err != nil {
		f.WriteString("创建解压目录失败: " + err.Error() + "\n")
		return "", err
	}

	// 解压文件到提取目录
	extractCmd := fmt.Sprintf(`tar -zxvf "%s" -C "%s"`, decryptTechsupportFilePath, extractedDir)
	extractOutput, err := libs.ExecCommand(extractCmd)
	if err != nil {
		f.WriteString(extractOutput)
		f.WriteString("解压文件失败: " + err.Error() + "\n")
		return "", err
	}
	f.WriteString("成功解压文件到临时目录\n")

	// 第二步：调用IKE解码处理
	if err := processIKEDecoding(extractedDir, f); err != nil {
		f.WriteString(fmt.Sprintf("IKE解码处理失败，错误信息: %s\n", err.Error()))
	}

	// 第三步：调用running-config文件解密
	if err := processRunningConfigDecryption(extractedDir, tmpDir, job.JobID, f); err != nil {
		f.WriteString(fmt.Sprintf("Running-config解密处理失败，错误信息: %s\n", err.Error()))
	}

	// 第四步：检查并提取perf文件到备份目录
	perfBackupDir := filepath.Join(tmpDir, "perf-backup")
	hasPerfFiles := extractPerfFiles(extractedDir, perfBackupDir, f)

	// 根据是否有perf文件设置PerfStatus
	if hasPerfFiles {
		// 有perf文件，设置状态为"进行中"
		if err := job.Update(job.ID, map[string]interface{}{
			"PerfStatus": 1, // 设置为进行中
		}); err != nil {
			f.WriteString(fmt.Sprintf("更新PerfStatus为进行中失败: %s\n", err.Error()))
		} else {
			f.WriteString("已更新PerfStatus为进行中\n")
		}
	} else {
		// 没有发现perf文件，设置状态为"未发现perf文件"
		if err := job.Update(job.ID, map[string]interface{}{
			"PerfStatus": 0, // 设置为未发现perf文件
		}); err != nil {
			f.WriteString(fmt.Sprintf("更新PerfStatus为未发现perf文件失败: %s\n", err.Error()))
		} else {
			f.WriteString("已更新PerfStatus为未发现perf文件\n")
		}
	}

	// 第五步：重新打包所有解密后的文件
	repackCmd := fmt.Sprintf(
		`cd "%s" && tar -zcvf "%s" ./ && rm -rf "%s"`,
		extractedDir,
		decryptTechsupportFilePath,
		extractedDir,
	)

	repackOutput, err := libs.ExecCommand(repackCmd)
	if err != nil {
		f.WriteString(repackOutput + "\n")
		f.WriteString("重新打包解密后的文件失败: " + err.Error() + "\n")
		return "", err
	}

	f.WriteString("成功完成所有解密操作并重新打包\n")

	// 第六步：如果有perf文件，则启动后台处理
	if hasPerfFiles {
		f.WriteString("\n发现perf文件和必要的系统信息，将在后台处理perf分析，不阻塞当前解密流程...\n")
		f.WriteString("注意：perf分析仍在后台执行，可能需要较长时间完成\n")
		f.WriteString("perf分析结果将在完成后添加到数据库，可稍后在perf分析结果页面查看\n")

		// 启动后台perf分析进程
		startBackgroundPerfAnalysis(job.JobID, perfBackupDir, tmpDir, workDir)
	}

	md5, err := libs.GetFileMd5(decryptTechsupportFilePath)
	if err != nil {
		return "", err
	}

	return md5, nil
}

// 辅助函数：复制文件
func copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	if err != nil {
		return err
	}

	// 保持文件权限
	sourceInfo, err := os.Stat(src)
	if err != nil {
		return err
	}
	return os.Chmod(dst, sourceInfo.Mode())
}

// 新增的独立IKE解码处理方法
func processIKEDecoding(extractedDir string, f *os.File) error {
	ipsecPath := filepath.Join(extractedDir, "ipsec")
	if _, err := os.Stat(ipsecPath); err == nil {
		f.WriteString("找到ipsec目录，开始IKE解码处理\n")

		// 执行IKE解码
		ikeDecodeCmd := fmt.Sprintf(`"%s" "%s" "%s"`,
			filepath.Join(libs.Config.Sig.DpdinfoPath, "dpd_info"),
			extractedDir,
			ipsecPath,
		)

		ikeOutput, err := libs.ExecCommand(ikeDecodeCmd)
		if err != nil {
			f.WriteString(ikeOutput)
			f.WriteString("IKE解码失败: " + err.Error() + "\n")
			return err
		}
		f.WriteString("IKE解码成功完成\n")
		return nil
	}

	f.WriteString("未找到ipsec目录，跳过IKE解码\n")
	return nil
}

// SSH连接函数
func trySSHConnect(remoteUser, remoteServer string, remotePort int, remotePass string, f *os.File) (*ssh.Client, error) {
	f.WriteString("准备建立SSH连接...\n")

	// 创建 SshClient 实例
	sshClient := &libs.SshClient{}

	// 增强的Connect方法，结合原有trySSHConnect的多种认证功能
	err := enhancedConnect(sshClient, remoteUser, remoteServer, remotePort, remotePass, f)
	if err != nil {
		f.WriteString(fmt.Sprintf("SSH连接失败: %s\n", err.Error()))
		return nil, err
	}

	f.WriteString("SSH连接成功!\n")
	return sshClient.SSH, nil
}

// 增强的SSH连接方法，支持多种认证方式
func enhancedConnect(sc *libs.SshClient, user, host string, port int, password string, f *os.File) error {
	var (
		auth         []ssh.AuthMethod
		addr         string
		clientConfig *ssh.ClientConfig
		client       *ssh.Client
		err          error
	)

	auth = make([]ssh.AuthMethod, 0)

	// 尝试系统密钥认证
	if len(libs.Config.Rsa.Privatekey) > 0 {
		f.WriteString("尝试使用系统密钥认证...\n")
		key, err := os.ReadFile(libs.Config.Rsa.Privatekey)
		if err != nil {
			f.WriteString(fmt.Sprintf("读取系统密钥文件失败: %s\n", err.Error()))
		} else {
			signer, err := ssh.ParsePrivateKey(key)
			if err != nil {
				f.WriteString(fmt.Sprintf("解析系统密钥失败: %s\n", err.Error()))
			} else {
				auth = append(auth, ssh.PublicKeys(signer))
				f.WriteString("已添加系统SSH密钥认证\n")
			}
		}
	}

	// 如果系统密钥无法使用，尝试用户目录下的密钥
	if len(auth) == 0 {
		f.WriteString("尝试使用用户目录下的密钥认证...\n")
		homeDir, err := os.UserHomeDir()
		if err == nil {
			// 尝试使用默认的私钥位置
			keyFiles := []string{
				filepath.Join(homeDir, ".ssh", "id_rsa"),
				filepath.Join(homeDir, ".ssh", "id_ed25519"),
				filepath.Join(homeDir, ".ssh", "identity"),
			}

			for _, keyFile := range keyFiles {
				if _, err := os.Stat(keyFile); err == nil {
					key, err := os.ReadFile(keyFile)
					if err != nil {
						continue
					}

					signer, err := ssh.ParsePrivateKey(key)
					if err != nil {
						continue
					}

					auth = append(auth, ssh.PublicKeys(signer))
					f.WriteString(fmt.Sprintf("使用密钥文件: %s\n", keyFile))
					break // 找到一个可用密钥就退出
				}
			}
		}
	}

	// 密码认证作为最后的备选
	if len(auth) == 0 {
		f.WriteString("未找到可用的SSH密钥，尝试使用密码认证...\n")
		auth = append(auth, ssh.Password(password))
	}

	// 连接服务器
	addr = fmt.Sprintf("%s:%d", host, port)
	f.WriteString(fmt.Sprintf("连接到 %s...\n", addr))

	// 首先尝试使用密钥认证
	if len(auth) > 0 {
		// 尝试使用现有的密钥创建连接配置
		clientConfig = &ssh.ClientConfig{
			User:            user,
			Auth:            auth,
			HostKeyCallback: ssh.InsecureIgnoreHostKey(),
			Timeout:         30 * time.Second,
		}

		// 尝试密钥认证连接
		client, err = ssh.Dial("tcp", addr, clientConfig)
		if err != nil {
			f.WriteString(fmt.Sprintf("密钥认证失败: %s，尝试密码认证...\n", err.Error()))

			// 密钥认证失败，尝试密码认证
			clientConfig = &ssh.ClientConfig{
				User:            user,
				Auth:            []ssh.AuthMethod{ssh.Password(password)},
				HostKeyCallback: ssh.InsecureIgnoreHostKey(),
				Timeout:         30 * time.Second,
			}

			// 尝试密码认证连接
			client, err = ssh.Dial("tcp", addr, clientConfig)
			if err != nil {
				f.WriteString(fmt.Sprintf("密码认证也失败: %s\n", err.Error()))

				// 所有认证方式都失败，发送飞书通知
				notifyErrorMsg := fmt.Sprintf("SSH连接异常: 服务器 %s 无法连接\n用户: %s\n错误信息: %s",
					addr, user, err.Error())
				sendFeishuNotification(notifyErrorMsg, f)

				return fmt.Errorf("所有认证方式都失败: %s", err.Error())
			}
			f.WriteString("密码认证成功!\n")
		} else {
			f.WriteString("密钥认证成功!\n")
		}
	} else {
		// 直接尝试密码认证
		clientConfig = &ssh.ClientConfig{
			User:            user,
			Auth:            []ssh.AuthMethod{ssh.Password(password)},
			HostKeyCallback: ssh.InsecureIgnoreHostKey(),
			Timeout:         30 * time.Second,
		}

		client, err = ssh.Dial("tcp", addr, clientConfig)
		if err != nil {
			f.WriteString(fmt.Sprintf("密码认证失败: %s\n", err.Error()))

			// 密码认证失败，发送飞书通知
			notifyErrorMsg := fmt.Sprintf("SSH连接异常: 服务器 %s 无法连接\n用户: %s\n错误信息: %s",
				addr, user, err.Error())
			sendFeishuNotification(notifyErrorMsg, f)

			return err
		}
		f.WriteString("密码认证成功!\n")
	}

	// 创建SFTP客户端
	sftp, err := sftp.NewClient(client)
	if err != nil {
		// 如果SFTP创建失败，关闭SSH连接并发送飞书通知
		client.Close()

		notifyErrorMsg := fmt.Sprintf("SFTP连接异常: 服务器 %s SFTP客户端创建失败\n用户: %s\n错误信息: %s",
			addr, user, err.Error())
		sendFeishuNotification(notifyErrorMsg, f)

		return fmt.Errorf("创建SFTP客户端失败: %s", err)
	}

	// 设置SshClient的属性
	sc.SSH = client
	sc.SFTP = sftp

	f.WriteString("SSH连接和SFTP客户端创建成功!\n")
	return nil
}

// 飞书消息通知
func sendFeishuNotification(errorMsg string, f *os.File) {
	// 使用固定邮箱列表
	adminEmails := []string{
		"<EMAIL>",
	}

	// 构建通知消息
	timestamp := time.Now().Format("2006-01-02 15:04:05")
	subject := "一键解密工具SSH连接异常警告"
	body := fmt.Sprintf("【一键解密工具SSH连接异常】\n\n时间: %s\n\n%s", timestamp, errorMsg)

	// 发送消息飞书
	go func() {
		err := libs.SendCardMessage(adminEmails, subject, body, []string{})
		if err != nil {
			errMsg := err.Error()
			logging.ErrorLogger.Errorf("一键解密工具SSH连接异常，飞书通知发送失败: %s", errMsg)
			f.WriteString(fmt.Sprintf("飞书通知发送失败: %s\n", errMsg))
		} else {
			logging.InfoLogger.Infof("一键解密工具SSH连接异常通知已发送到管理员邮箱")
			f.WriteString("飞书通知发送成功\n")
		}
	}()
}

// 新增running-config文件解密
func processRunningConfigDecryption(extractedDir string, tmpDir string, jobID string, f *os.File) error {
	runningConfigPath := filepath.Join(extractedDir, "running-config.tar.gz")
	if _, err := os.Stat(runningConfigPath); err == nil {
		f.WriteString("找到running-config.tar.gz文件，开始解密处理\n")

		// 创建临时目录用于解密操作
		runningConfigTmpDir := filepath.Join(tmpDir, "running-config-tmp")
		os.MkdirAll(runningConfigTmpDir, 0755)

		// 远程服务器的连接信息
		remoteServer := libs.Config.Sig.RunningConfigServer
		remoteUser := libs.Config.Sig.RunningConfigUser
		remotePass := libs.Config.Sig.RunningConfigPass
		remotePath := libs.Config.Sig.RunningConfigRemotePath
		remotePort := libs.Config.Sig.RunningConfigPort

		// 输出简洁的连接信息
		f.WriteString(fmt.Sprintf("准备连接到解密服务器: %s@%s:%d\n", remoteUser, remoteServer, remotePort))

		// 创建SSH客户端（现在包含SFTP功能）
		sshClientWithSFTP := &libs.SshClient{}
		// 使用defer确保资源释放
		defer func() {
			if sshClientWithSFTP.SSH != nil {
				sshClientWithSFTP.SSH.Close()
			}
			if sshClientWithSFTP.SFTP != nil {
				sshClientWithSFTP.SFTP.Close()
			}
		}()
		err := enhancedConnect(sshClientWithSFTP, remoteUser, remoteServer, remotePort, remotePass, f)
		if err != nil {
			f.WriteString(fmt.Sprintf("SSH连接失败: %s\n", err.Error()))
			return err
		}

		// 确保远程目录存在
		f.WriteString("创建远程解密目录...\n")
		mkdirSession, err := sshClientWithSFTP.SSH.NewSession()
		if err != nil {
			f.WriteString(fmt.Sprintf("创建会话失败: %s\n", err.Error()))
			return err
		}
		defer mkdirSession.Close()

		if err := mkdirSession.Run(fmt.Sprintf("mkdir -p %s", remotePath)); err != nil {
			f.WriteString(fmt.Sprintf("创建远程目录失败: %s\n", err.Error()))
			return err
		}

		// 使用SFTP上传文件到远程服务器，替代SCP命令
		f.WriteString("开始通过SFTP上传running-config文件到远程服务器...\n")
		remoteFilePath := filepath.Join(remotePath, fmt.Sprintf("running-config-%s.tar.gz", jobID))

		// 使用SFTP上传文件
		err = uploadFileToRemote(sshClientWithSFTP.SFTP, runningConfigPath, remoteFilePath, f)
		if err != nil {
			f.WriteString(fmt.Sprintf("SFTP上传文件失败: %s\n", err.Error()))
			return err
		}

		f.WriteString("文件成功上传到远程服务器\n")

		// 在远程服务器上执行yams -D解密操作
		f.WriteString("执行yams解密操作...\n")
		session, err := sshClientWithSFTP.SSH.NewSession()
		if err != nil {
			f.WriteString(fmt.Sprintf("创建SSH会话失败: %s\n", err.Error()))
			return err
		}
		defer session.Close()

		// 执行yams -D命令解密
		decryptCmd := fmt.Sprintf("yams -D %s", remoteFilePath)

		var sshStdout, sshStderr bytes.Buffer
		session.Stdout = &sshStdout
		session.Stderr = &sshStderr

		if err := session.Run(decryptCmd); err != nil {
			f.WriteString(fmt.Sprintf("远程yams解密命令执行失败: %s\n", err.Error()))
			f.WriteString(fmt.Sprintf("标准输出: %s\n", sshStdout.String()))
			f.WriteString(fmt.Sprintf("错误输出: %s\n", sshStderr.String()))
			return err
		}

		// 从输出中提取解密后的文件夹路径
		output := sshStdout.String()
		f.WriteString(fmt.Sprintf("yams解密输出: %s\n", output))

		// 解析输出以找到解密后的目录路径
		decryptedDirPath := ""
		lines := strings.Split(output, "\n")
		for _, line := range lines {
			if strings.Contains(line, "Unzip file successd in") {
				parts := strings.Split(line, "Unzip file successd in")
				if len(parts) >= 2 {
					decryptedDirPath = strings.TrimSpace(parts[1])
					break
				}
			}
		}

		if decryptedDirPath == "" {
			f.WriteString("无法从yams输出中找到解密后的目录路径\n")
			return fmt.Errorf("无法从yams输出中找到解密后的目录路径")
		}

		f.WriteString(fmt.Sprintf("找到解密后的目录: %s\n", decryptedDirPath))

		// 从远程服务器下载解密后的文件夹到本地
		f.WriteString("开始下载解密文件...\n")

		// 创建本地的running-config目录
		localRunningConfigDir := filepath.Join(extractedDir, "running-config")
		if err := os.RemoveAll(localRunningConfigDir); err != nil {
			f.WriteString(fmt.Sprintf("清理本地running-config目录失败: %s\n", err.Error()))
		}

		if err := os.MkdirAll(localRunningConfigDir, 0755); err != nil {
			f.WriteString(fmt.Sprintf("创建本地running-config目录失败: %s\n", err.Error()))
			return err
		}

		// 使用SFTP递归下载文件夹，替代SCP命令
		err = downloadRemoteDir(sshClientWithSFTP.SFTP, decryptedDirPath, localRunningConfigDir, f)
		if err != nil {
			f.WriteString(fmt.Sprintf("SFTP下载解密文件夹失败: %s\n", err.Error()))
			return err
		}

		f.WriteString("解密后的文件夹成功下载并重命名为running-config\n")

		// 清理远程服务器上的临时文件
		f.WriteString("清理远程服务器上的临时文件...\n")
		cleanupSession, err := sshClientWithSFTP.SSH.NewSession()
		if err != nil {
			f.WriteString(fmt.Sprintf("创建清理会话失败: %s\n", err.Error()))
		} else {
			defer cleanupSession.Close()
			// 清理远程的解密目录和上传的文件
			cleanupCmd := fmt.Sprintf("rm -rf %s %s", decryptedDirPath, remoteFilePath)
			if err := cleanupSession.Run(cleanupCmd); err != nil {
				f.WriteString(fmt.Sprintf("清理远程临时文件失败: %s\n", err.Error()))
			} else {
				f.WriteString("远程临时文件清理完成\n")
			}
		}

		return nil
	}

	f.WriteString("未找到running-config.tar.gz文件，跳过running-config解密\n")
	return nil
}

// 通过SFTP上传文件到远程服务器
func uploadFileToRemote(sftpClient *sftp.Client, localPath, remotePath string, f *os.File) error {
	// 打开本地文件
	localFile, err := os.Open(localPath)
	if err != nil {
		return fmt.Errorf("打开本地文件失败: %s", err)
	}
	defer localFile.Close()

	// 创建远程文件
	remoteFile, err := sftpClient.Create(remotePath)
	if err != nil {
		return fmt.Errorf("创建远程文件失败: %s", err)
	}
	defer remoteFile.Close()

	// 读取本地文件内容
	fileData, err := io.ReadAll(localFile)
	if err != nil {
		return fmt.Errorf("读取本地文件失败: %s", err)
	}

	// 写入远程文件
	_, err = remoteFile.Write(fileData)
	if err != nil {
		return fmt.Errorf("写入远程文件失败: %s", err)
	}

	f.WriteString(fmt.Sprintf("成功上传文件到: %s\n", remotePath))
	return nil
}

// 递归下载远程目录到本地
func downloadRemoteDir(sftpClient *sftp.Client, remoteDirPath, localDirPath string, f *os.File) error {
	// 显示正在下载的路径
	f.WriteString(fmt.Sprintf("下载远程目录: %s 到本地: %s\n", remoteDirPath, localDirPath))

	// 确保本地目录存在
	if err := os.MkdirAll(localDirPath, 0755); err != nil {
		return fmt.Errorf("创建本地目录失败: %s", err)
	}

	// 列出远程目录中的文件和子目录
	entries, err := sftpClient.ReadDir(remoteDirPath)
	if err != nil {
		return fmt.Errorf("读取远程目录失败: %s", err)
	}

	for _, entry := range entries {
		remotePath := filepath.Join(remoteDirPath, entry.Name())
		localPath := filepath.Join(localDirPath, entry.Name())

		if entry.IsDir() {
			// 如果是子目录，递归下载
			if err := downloadRemoteDir(sftpClient, remotePath, localPath, f); err != nil {
				return err
			}
		} else {
			// 如果是文件，直接下载
			remoteFile, err := sftpClient.Open(remotePath)
			if err != nil {
				return fmt.Errorf("打开远程文件失败: %s", err)
			}

			// 读取远程文件内容
			fileData, err := io.ReadAll(remoteFile)
			remoteFile.Close()
			if err != nil {
				return fmt.Errorf("读取远程文件失败: %s", err)
			}

			// 创建本地文件
			localFile, err := os.Create(localPath)
			if err != nil {
				return fmt.Errorf("创建本地文件失败: %s", err)
			}

			// 写入本地文件
			_, err = localFile.Write(fileData)
			localFile.Close()
			if err != nil {
				return fmt.Errorf("写入本地文件失败: %s", err)
			}
		}
	}

	return nil
}

func GetTechSupportDecryptJob(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	jobID := ctx.FormValue("job_id")
	job := dtechsupportdecrypt.SigTechSupportDecryptJob{}
	if len(jobID) > 0 {
		err := job.FindEx("job_id", jobID)
		if err != nil {
			logging.ErrorLogger.Errorf("find sig job get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	} else {
		err := job.Find(id)
		if err != nil {
			logging.ErrorLogger.Errorf("find sig job get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, job, response.NoErr.Msg))
	return
}

func DownloadTechSupportDecryptFile(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	jobID := ctx.FormValue("job_id")
	job := dtechsupportdecrypt.SigTechSupportDecryptJob{}
	if len(jobID) > 0 {
		err := job.FindEx("job_id", jobID)
		if err != nil {
			logging.ErrorLogger.Errorf("find sig job get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	} else {
		err := job.Find(id)
		if err != nil {
			logging.ErrorLogger.Errorf("find sig job get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	}
	ctx.SendFile(filepath.Join(job.Dir(), job.OutputFileName), job.OutputFileName)
	return
}

func CheckTechSupportDecryptToolVersion(ctx iris.Context) {
	goos := ctx.Params().GetString("goos")
	var fileName string
	if goos == "linux" {
		fileName = "sigcli"
	} else if goos == "windows" {
		fileName = "sigcli.exe"
	}
	md5, err := libs.GetFileMd5(filepath.Join(libs.CWD(), "uploads", goos, fileName))
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.WriteString(md5)
	return
}

func DownloadTechSupportDecryptTool(ctx iris.Context) {
	goos := ctx.Params().GetString("goos")
	if goos == "linux" {
		ctx.SendFile(filepath.Join(libs.CWD(), "uploads", goos, "sigcli"), "sigcli")
	} else if goos == "windows" {
		ctx.SendFile(filepath.Join(libs.CWD(), "uploads", goos, "sigcli.exe"), "sigcli.exe")
	}
	return
}

func DownloadTechSupportDecryptLog(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	jobID := ctx.FormValue("job_id")
	job := dtechsupportdecrypt.SigTechSupportDecryptJob{}
	if len(jobID) > 0 {
		err := job.FindEx("job_id", jobID)
		if err != nil {
			logging.ErrorLogger.Errorf("find sig job get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	} else {
		err := job.Find(id)
		if err != nil {
			logging.ErrorLogger.Errorf("find sig job get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	}
	data, err := os.ReadFile(filepath.Join(job.Dir(), job.JobID+".log"))
	if err != nil {
		logging.ErrorLogger.Errorf("find sig job get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.ContentType("text/plain")
	ctx.WriteString(string(data))
	return
}

// GetPerfAnalysisResults 获取perf分析结果
func GetPerfAnalysisResults(ctx iris.Context) {
	jobID := ctx.FormValue("job_id")
	if jobID == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "必须提供job_id参数"))
		return
	}

	perfFileName := ctx.FormValue("perf_file_name")
	analysisType := ctx.FormValue("analysis_type")
	status := ctx.FormValue("status")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dperfanalysisresult.All(jobID, perfFileName, analysisType, status, sort, orderBy, page, pageSize)
	if err != nil {
		logging.ErrorLogger.Errorf("find perf analysis results get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

// GetPerfAnalysisResult 获取单个perf分析结果
func GetPerfAnalysisResult(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	result := dperfanalysisresult.SigPerfAnalysisResult{}

	err := result.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("find perf analysis result get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	// 检查文件是否存在
	if _, err := os.Stat(result.ResultPath); os.IsNotExist(err) {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "结果文件不存在"))
		return
	}

	data, err := os.ReadFile(result.ResultPath)
	if err != nil {
		logging.ErrorLogger.Errorf("read perf analysis result get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.ContentType("text/plain")
	ctx.WriteString(string(data))
	return
}

// DownloadPerfAnalysisResult 下载perf分析结果文件
func DownloadPerfAnalysisResult(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	result := dperfanalysisresult.SigPerfAnalysisResult{}

	err := result.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("find perf analysis result get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	// 检查文件是否存在
	if _, err := os.Stat(result.ResultPath); os.IsNotExist(err) {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "结果文件不存在"))
		return
	}

	// 构造文件名：perf文件名_分析类型.perflog
	downloadFileName := fmt.Sprintf("%s_%s.perflog", result.PerfFileName, result.AnalysisType)

	// 发送文件给客户端
	ctx.SendFile(result.ResultPath, downloadFileName)
	return
}

// 从version.txt获取softnum信息
func GetSoftNum(dest string) (string, error) {
	files, err := os.ReadDir(dest)
	if err != nil {
		return "", err
	}
	for _, f := range files {
		if f.Name() == "version.txt" {
			f, err := os.ReadFile(filepath.Join(dest, f.Name()))
			if err != nil {
				fmt.Println("read version.txt err", err)
				return "", err
			}
			versionInfo := strings.Split(string(f), "\n")
			for _, line := range versionInfo {
				if strings.HasPrefix(line, "softnum:") {
					softnum := strings.Trim(strings.Split(line, ":")[1], " ")
					return softnum, nil
				}
			}
		}
	}
	return "", nil
}

// 从version.txt获取产品名称
func GetProduct(dest string) (string, error) {
	files, err := os.ReadDir(dest)
	if err != nil {
		return "", err
	}
	for _, f := range files {
		if f.Name() == "version.txt" {
			f, err := os.ReadFile(filepath.Join(dest, f.Name()))
			if err != nil {
				fmt.Println("read version.txt err", err)
				return "", err
			}
			versionInfo := strings.Split(string(f), "\n")
			for _, line := range versionInfo {
				if strings.HasPrefix(line, "ProductName:") {
					product := strings.Trim(strings.Split(line, ":")[1], " ")
					return strings.ToLower(product), nil
				}
			}
		}
	}
	return "", nil
}

// 从version.txt获取ReleaseID信息
func GetReleaseID(dest string) (string, error) {
	files, err := os.ReadDir(dest)
	if err != nil {
		return "", err
	}
	for _, f := range files {
		if f.Name() == "version.txt" {
			f, err := os.ReadFile(filepath.Join(dest, f.Name()))
			if err != nil {
				fmt.Println("read version.txt err", err)
				return "", err
			}
			versionInfo := strings.Split(string(f), "\n")
			for _, line := range versionInfo {
				if strings.Contains(line, "Release") {
					releaseLine := strings.Replace(strings.Replace(line, "(", " ", -1), ")", "", -1)
					releaseInfo := strings.Split(releaseLine, " ")
					releaseID := releaseInfo[len(releaseInfo)-1]
					return releaseID, nil
				}
			}
		}
	}
	return "", nil
}

// 从version.txt获取软件版本信息
func GetSoftVersion(dest string) (string, error) {
	files, err := os.ReadDir(dest)
	if err != nil {
		return "", err
	}
	for _, f := range files {
		if f.Name() == "version.txt" {
			f, err := os.ReadFile(filepath.Join(dest, f.Name()))
			if err != nil {
				fmt.Println("read version.txt err", err)
				return "", err
			}
			versionInfo := strings.Split(string(f), "\n")
			for _, line := range versionInfo {
				if strings.Contains(line, "NTOS") || strings.Contains(line, "IDP") {
					return line, nil
				}
			}
		}
	}
	return "", nil
}

// 下载rootfs-debug文件和image.tar.bz2文件
func downloadRootfsDebug(extractedDir, rootfsDebugPath string, f *os.File) (string, string) {
	// 读取系统信息
	softnum, err := GetSoftNum(extractedDir)
	if err != nil {
		f.WriteString(fmt.Sprintf("读取softnum信息失败: %v\n", err))
	}

	product, err := GetProduct(extractedDir)
	if err != nil {
		f.WriteString(fmt.Sprintf("读取product信息失败: %v\n", err))
	}

	softversion, err := GetSoftVersion(extractedDir)
	if err != nil {
		f.WriteString(fmt.Sprintf("读取softversion信息失败: %v\n", err))
	}

	releaseID, err := GetReleaseID(extractedDir)
	if err != nil {
		f.WriteString(fmt.Sprintf("读取releaseID信息失败: %v\n", err))
	}

	f.WriteString(fmt.Sprintf("读取到产品信息: softnum=%s, product=%s, softversion=%s, releaseID=%s\n",
		softnum, product, softversion, releaseID))

	// 检查是否获取到有效的系统信息
	if softnum == "" && product == "" && softversion == "" && releaseID == "" {
		f.WriteString("无法获取到任何有效的系统信息，可能是备份目录不包含version.txt或格式不正确\n")
		f.WriteString("尝试在备份目录下查找可能已存在的rootfs-debug.tar.gz文件...\n")
		return "", ""
	}

	// 使用默认的构建服务器地址
	buildFarmServer := "http://aqyfzx.ruijie.net:9090" // 默认地址
	rootfsDebugLink := ""

	// 尝试获取rootfs-debug下载链接
	if releaseID != "" {
		// 尝试从每日编译获取
		cronMakeJob, err := dcronmakejob.FindCronMakeJobsByProductAndSoftware(product, softnum, softversion)
		if err == nil && cronMakeJob != nil && cronMakeJob.JobId != "" {
			cpuInfo := ""
			if cronMakeJob.Cpu.Cpu != "" {
				cpuInfo = cronMakeJob.Cpu.Cpu
			}

			if cpuInfo != "" {
				rootfsDebugLink = fmt.Sprintf("%s/output/%s/%s/", buildFarmServer, cronMakeJob.JobId, cpuInfo)
			} else {
				rootfsDebugLink = fmt.Sprintf("%s/output/%s/", buildFarmServer, cronMakeJob.JobId)
			}
		} else {
			// 尝试从定制编译获取
			makeJob, err := dmakejob.FindMakeJobsByProductAndSoftware(product, softnum, softversion)
			if err == nil && makeJob != nil && makeJob.JobID != "" {
				cpuInfo := ""
				if makeJob.Cpu.Cpu != "" {
					cpuInfo = makeJob.Cpu.Cpu
				}

				if cpuInfo != "" {
					rootfsDebugLink = fmt.Sprintf("%s/output/%s/%s/", buildFarmServer, makeJob.TaskID, cpuInfo)
				} else {
					rootfsDebugLink = fmt.Sprintf("%s/output/%s/", buildFarmServer, makeJob.TaskID)
				}
			}
		}
	}

	// 如果上述方法未能获取到链接，尝试通过归档版本数据获取
	if rootfsDebugLink == "" {
		releaseInfo, err := drelease.GetArchivedReleasesByProduct(softversion, softnum, product)
		if err == nil && releaseInfo != nil && releaseInfo.BuildFarmLink != "" {
			rootfsDebugLink = releaseInfo.BuildFarmLink
			if !strings.HasSuffix(rootfsDebugLink, "/") {
				rootfsDebugLink += "/"
			}
		}
	}

	// 如果获取到了下载链接基址，获取实际的rootfs-debug和image.tar.bz2文件下载链接
	if rootfsDebugLink != "" {
		f.WriteString(fmt.Sprintf("获取到基础下载链接: %s，准备获取实际文件...\n", rootfsDebugLink))

		// 首先下载索引页面
		tempDir := filepath.Join(rootfsDebugPath, "temp")
		if err := os.MkdirAll(tempDir, 0755); err != nil {
			f.WriteString(fmt.Sprintf("创建临时目录失败: %s\n", err.Error()))
			return "", ""
		}
		defer os.RemoveAll(tempDir)

		indexFile := filepath.Join(tempDir, "index.html")
		// 使用wget下载目录列表
		getIndexCmd := fmt.Sprintf(`wget -q -O "%s" "%s"`, indexFile, rootfsDebugLink)
		if _, err := libs.ExecCommand(getIndexCmd); err != nil {
			f.WriteString(fmt.Sprintf("无法获取目录列表: %s\n", err.Error()))
			return "", ""
		}

		// 读取索引文件内容
		indexContent, err := os.ReadFile(indexFile)
		if err != nil {
			f.WriteString(fmt.Sprintf("读取索引文件失败: %s\n", err.Error()))
			return "", ""
		}

		// 解析HTML内容以查找rootfs-debug和image.tar.bz2文件
		rootfsDebugFile, imageTarFile := findRootfsDebugAndImageFiles(string(indexContent))

		if rootfsDebugFile == "" {
			f.WriteString("在目录列表中未找到rootfs-debug文件\n")
			return "", ""
		}

		f.WriteString(fmt.Sprintf("找到rootfs-debug文件: %s\n", rootfsDebugFile))
		if imageTarFile != "" {
			f.WriteString(fmt.Sprintf("找到image.tar.bz2文件: %s\n", imageTarFile))
		} else {
			f.WriteString("未找到image.tar.bz2文件\n")
		}

		rootfsFilePath := downloadSpecificFile(rootfsDebugPath, rootfsDebugLink, buildFarmServer, "rootfs-debug.tar.gz", rootfsDebugFile, f)

		var imageTarFilePath string
		if imageTarFile != "" {
			imageTarFilePath = downloadSpecificFile(rootfsDebugPath, rootfsDebugLink, buildFarmServer, "image.tar.bz2", imageTarFile, f)
		}

		return rootfsFilePath, imageTarFilePath
	} else {
		f.WriteString("未找到合适的rootfs-debug基础下载链接\n")
		return "", ""
	}
}

// 下载特定文件
func downloadSpecificFile(rootfsDebugPath, baseLink, buildFarmServer, localFileName, remoteFileName string, f *os.File) string {
	if remoteFileName == "" {
		return ""
	}

	// 构建完整的下载链接，并处理URL编码
	downloadURL := ""
	// 对文件名进行URL编码处理
	encodedFileName := remoteFileName
	// 替换URL中的特殊字符
	for _, char := range []string{" ", "(", ")", "[", "]", "{", "}", "!", "@", "#", "$", "%", "^", "&", "*", "+", "=", "'", "\""} {
		encodedFileName = strings.ReplaceAll(encodedFileName, char, url.QueryEscape(char))
	}

	// 检查文件名是否已经包含路径
	if strings.HasPrefix(encodedFileName, "/") {
		// 如果文件名以/开头，说明是完整路径，从buildFarmServer构建完整URL
		// 去掉前导斜杠，避免双斜杠
		encodedFileName = strings.TrimPrefix(encodedFileName, "/")
		downloadURL = fmt.Sprintf("%s/%s", buildFarmServer, encodedFileName)
	} else {
		// 如果是相对路径，按原来的方式拼接
		if strings.HasSuffix(baseLink, "/") {
			downloadURL = baseLink + encodedFileName
		} else {
			downloadURL = baseLink + "/" + encodedFileName
		}
	}

	f.WriteString(fmt.Sprintf("完整下载链接: %s\n", downloadURL))

	// 下载文件，不解压
	filePath := filepath.Join(rootfsDebugPath, localFileName)

	// 使用更安全的下载命令，添加超时和重试参数
	downloadCmd := fmt.Sprintf(
		`cd "%s" && wget --timeout=600 --tries=3 -q -O "%s" "%s"`,
		rootfsDebugPath,
		localFileName,
		downloadURL,
	)

	f.WriteString(fmt.Sprintf("开始下载%s文件...\n", localFileName))
	output, err := libs.ExecCommand(downloadCmd)
	if err != nil {
		f.WriteString(fmt.Sprintf("使用wget下载失败: %v\n", err))
		if output != "" {
			f.WriteString(fmt.Sprintf("下载输出信息: %s\n", output))
		}

		// 尝试使用curl作为替代方案
		f.WriteString("尝试使用curl作为替代下载方式...\n")
		curlCmd := fmt.Sprintf(
			`cd "%s" && curl -L --connect-timeout 60 --max-time 600 -o "%s" "%s"`,
			rootfsDebugPath,
			localFileName,
			downloadURL,
		)
		curlOutput, curlErr := libs.ExecCommand(curlCmd)
		if curlErr != nil {
			f.WriteString(fmt.Sprintf("使用curl下载也失败了: %v\n", curlErr))
			if curlOutput != "" {
				f.WriteString(fmt.Sprintf("curl输出信息: %s\n", curlOutput))
			}
			return ""
		}
	}

	// 验证下载文件是否存在且大小正常
	if fileInfo, err := os.Stat(filePath); err != nil || fileInfo.Size() == 0 {
		if err != nil {
			f.WriteString(fmt.Sprintf("下载完成后无法访问文件: %v\n", err))
		} else {
			f.WriteString("下载的文件大小为0，可能下载失败\n")
		}
		return ""
	}

	f.WriteString(fmt.Sprintf("%s文件下载成功\n", localFileName))
	return filePath
}

// 从HTML内容中查找rootfs-debug文件名和image.tar.bz2文件名
func findRootfsDebugAndImageFiles(htmlContent string) (string, string) {
	// 查找所有链接
	hrefRegex := regexp.MustCompile(`href="([^"]+)"`)
	matches := hrefRegex.FindAllStringSubmatch(htmlContent, -1)

	rootfsDebugFile := ""
	imageTarFile := ""

	// 查找以"rootfs-debug.tar.gz"和"image.tar.bz2"结尾的文件
	for _, match := range matches {
		if len(match) > 1 {
			filename := match[1]
			if strings.HasSuffix(filename, "rootfs-debug.tar.gz") {
				rootfsDebugFile = filename
			}
			if strings.HasSuffix(filename, "image.tar.bz2") {
				imageTarFile = filename
			}
		}
	}

	return rootfsDebugFile, imageTarFile
}

// 上传并解压rootfs-debug文件和image.tar.bz2文件
func uploadAndExtractRootfsDebug(
	sshClient *libs.SshClient,
	rootfsArchivePath string,
	imageTarPath string,
	remotePerfDir string,
	f *os.File,
) error {
	f.WriteString("开始上传和解压分析所需文件到远程服务器...\n")

	// 1. 上传并解压rootfs-debug.tar.gz
	if rootfsArchivePath == "" {
		f.WriteString("没有rootfs-debug.tar.gz文件可上传\n")
		return fmt.Errorf("缺少必要的rootfs-debug.tar.gz文件")
	}

	// 上传rootfs-debug.tar.gz
	remoteArchivePath := fmt.Sprintf("%s/rootfs-debug.tar.gz", remotePerfDir)
	f.WriteString("上传rootfs-debug.tar.gz文件到远程服务器...\n")
	if err := uploadFileToRemote(sshClient.SFTP, rootfsArchivePath, remoteArchivePath, f); err != nil {
		f.WriteString(fmt.Sprintf("上传rootfs-debug.tar.gz文件失败: %s\n", err.Error()))
		return err
	}

	// 解压rootfs-debug.tar.gz
	extractSession, err := sshClient.SSH.NewSession()
	if err != nil {
		f.WriteString(fmt.Sprintf("创建解压会话失败: %s\n", err.Error()))
		return err
	}
	defer extractSession.Close()

	// 使用tar -xjf解压rootfs-debug.tar.gz文件
	extractCmd := fmt.Sprintf(
		"cd %s && mkdir -p rootfs-debug && rm -rf rootfs-debug/* && tar -xjf rootfs-debug.tar.gz -C rootfs-debug && rm rootfs-debug.tar.gz",
		remotePerfDir,
	)

	if err := extractSession.Run(extractCmd); err != nil {
		f.WriteString(fmt.Sprintf("远程解压rootfs-debug.tar.gz失败: %s\n", err.Error()))
		return err
	}
	f.WriteString("远程解压rootfs-debug.tar.gz成功\n")

	// 2. 处理image.tar.bz2 (如果存在)
	if imageTarPath == "" {
		f.WriteString("没有image.tar.bz2文件可上传，仅使用rootfs-debug继续分析\n")
		return nil
	}

	// 上传image.tar.bz2
	remoteImagePath := fmt.Sprintf("%s/image.tar.bz2", remotePerfDir)
	f.WriteString("上传image.tar.bz2文件到远程服务器...\n")
	if err := uploadFileToRemote(sshClient.SFTP, imageTarPath, remoteImagePath, f); err != nil {
		f.WriteString(fmt.Sprintf("上传image.tar.bz2文件失败: %s\n", err.Error()))
		f.WriteString("继续处理，因为rootfs-debug已成功上传和解压\n")
		return nil
	}

	// 创建临时目录并解压image.tar.bz2
	f.WriteString("创建临时目录并解压image.tar.bz2...\n")
	processCmdResult, err := executeSSHCommandSequence(sshClient, []string{
		fmt.Sprintf("cd %s && mkdir -p temp_image", remotePerfDir),
		fmt.Sprintf("cd %s && tar -xjf image.tar.bz2 -C temp_image", remotePerfDir),
	}, f)

	if err != nil {
		f.WriteString(fmt.Sprintf("解压image.tar.bz2失败: %s\n%s\n", err.Error(), processCmdResult))
		f.WriteString("继续处理，因为rootfs-debug已成功上传和解压\n")
		return nil
	}

	// 递归查找sysroot目录
	f.WriteString("开始递归查找sysroot目录...\n")
	findSysrootCmd := fmt.Sprintf("cd %s && find temp_image -type d -name sysroot | head -n 1", remotePerfDir)
	findSession, err := sshClient.SSH.NewSession()
	if err != nil {
		f.WriteString(fmt.Sprintf("创建查找sysroot会话失败: %s\n", err.Error()))
		cleanupTempFiles(sshClient, remotePerfDir, f)
		return nil
	}

	var findOutput bytes.Buffer
	findSession.Stdout = &findOutput
	err = findSession.Run(findSysrootCmd)
	findSession.Close()

	if err != nil {
		f.WriteString(fmt.Sprintf("查找sysroot目录失败: %s\n", err.Error()))
		cleanupTempFiles(sshClient, remotePerfDir, f)
		return nil
	}

	sysrootPath := strings.TrimSpace(findOutput.String())
	if sysrootPath == "" {
		f.WriteString("在image.tar.bz2解压后的目录中未找到sysroot目录，跳过复制步骤\n")
	} else {
		f.WriteString(fmt.Sprintf("找到sysroot目录: %s，将其内容复制到rootfs-debug目录\n", sysrootPath))

		// 复制sysroot目录下的内容到rootfs-debug目录
		copyCmd := fmt.Sprintf("cd %s && cp -rf %s/* rootfs-debug/", remotePerfDir, sysrootPath)
		copyCmdResult, err := executeSSHCommand(sshClient, copyCmd)

		if err != nil {
			f.WriteString(fmt.Sprintf("复制sysroot内容失败: %s\n%s\n", err.Error(), copyCmdResult))
		} else {
			f.WriteString("成功将sysroot目录内容复制到rootfs-debug目录\n")
		}
	}

	// 清理临时文件
	cleanupTempFiles(sshClient, remotePerfDir, f)
	return nil
}

// 执行单个SSH命令并返回结果
func executeSSHCommand(client *libs.SshClient, command string) (string, error) {
	session, err := client.SSH.NewSession()
	if err != nil {
		return "", err
	}
	defer session.Close()

	var stdoutBuf, stderrBuf bytes.Buffer
	session.Stdout = &stdoutBuf
	session.Stderr = &stderrBuf

	err = session.Run(command)
	return fmt.Sprintf("stdout: %s\nstderr: %s", stdoutBuf.String(), stderrBuf.String()), err
}

// 按顺序执行多个SSH命令
func executeSSHCommandSequence(client *libs.SshClient, commands []string, f *os.File) (string, error) {
	var output strings.Builder

	for _, cmd := range commands {
		cmdOutput, err := executeSSHCommand(client, cmd)
		output.WriteString(fmt.Sprintf("命令: %s\n%s\n", cmd, cmdOutput))

		if err != nil {
			return output.String(), err
		}
	}

	return output.String(), nil
}

// 清理临时文件和目录
func cleanupTempFiles(client *libs.SshClient, remotePerfDir string, f *os.File) {
	f.WriteString("清理临时文件和目录...\n")
	cleanupCmd := fmt.Sprintf("cd %s && rm -rf temp_image image.tar.bz2", remotePerfDir)
	_, err := executeSSHCommand(client, cleanupCmd)
	if err != nil {
		f.WriteString(fmt.Sprintf("清理临时文件失败: %s\n", err.Error()))
	} else {
		f.WriteString("临时文件清理完成\n")
	}
}

// 处理perf加密文件解析
func processPerfDecryption(extractedDir, resultDir string, jobID string, f *os.File) error {
	f.WriteString("\n开始处理perf解析...\n")

	// 记录传入的jobID
	if jobID != "" {
		f.WriteString(fmt.Sprintf("收到的JobID: %s\n", jobID))
	} else {
		f.WriteString("未提供JobID，可能影响perf分析结果的关联，跳过perf解析\n")
		return nil
	}

	// 检查version.txt文件是否存在
	versionTxtPath := filepath.Join(extractedDir, "version.txt")
	if _, err := os.Stat(versionTxtPath); err != nil {
		f.WriteString("未找到version.txt文件，无法获取系统信息，跳过perf解析\n")
		return fmt.Errorf("未找到version.txt文件，无法进行perf分析")
	}

	// 检查是否存在perf文件目录或直接位于extractedDir下的perf文件
	var perfFiles []string

	// 首先检查标准的fp-perf目录
	perfPath := filepath.Join(extractedDir, "fp-perf")
	if _, err := os.Stat(perfPath); err == nil {
		// 获取fp-perf下的所有perf文件
		perfDirFiles, err := filepath.Glob(filepath.Join(perfPath, "*"))
		if err != nil {
			f.WriteString(fmt.Sprintf("获取fp-perf目录中的文件列表失败: %v\n", err))
		} else if len(perfDirFiles) > 0 {
			perfFiles = append(perfFiles, perfDirFiles...)
		}
	}

	if len(perfFiles) == 0 {
		f.WriteString("未发现perf文件，跳过perf解析\n")
		return nil
	}

	f.WriteString(fmt.Sprintf("发现 %d 个perf文件，准备进行解析\n", len(perfFiles)))

	// 创建perf结果目录
	perfResultDir := filepath.Join(resultDir, "perf-result")
	if err := os.MkdirAll(perfResultDir, 0755); err != nil {
		f.WriteString(fmt.Sprintf("创建perf结果目录失败: %v\n", err))
		return err
	}

	// 创建rootfs-debug目录
	rootfsDebugPath := filepath.Join(resultDir, "rootfs-debug")
	if err := os.MkdirAll(rootfsDebugPath, 0755); err != nil {
		f.WriteString(fmt.Sprintf("创建rootfs-debug目录失败: %v\n", err))
		return err
	}

	// 下载rootfs-debug文件和image.tar.bz2文件
	f.WriteString("开始从远程服务器下载rootfs-debug.tar.gz和image.tar.bz2文件...\n")
	rootfsDebugArchive, imageTarArchive := downloadRootfsDebug(extractedDir, rootfsDebugPath, f)

	if rootfsDebugArchive == "" {
		errMsg := "无法下载rootfs-debug文件，无法进行正确的perf分析，退出处理"
		f.WriteString(errMsg + "\n")
		return fmt.Errorf(errMsg)
	}

	f.WriteString(fmt.Sprintf("成功下载rootfs-debug文件到: %s\n", rootfsDebugArchive))
	if imageTarArchive != "" {
		f.WriteString(fmt.Sprintf("成功下载image.tar.bz2文件到: %s\n", imageTarArchive))
	}

	// 配置远程解析服务器
	config := perfRemoteConfig{
		server:     libs.Config.Sig.PerfServer,
		user:       libs.Config.Sig.PerfUser,
		password:   libs.Config.Sig.PerfPass,
		port:       libs.Config.Sig.PerfPort,
		workingDir: fmt.Sprintf("/tmp/perf-analysis-%d", time.Now().UnixMicro()),
	}

	// 检查服务器配置
	if config.server == "" || config.user == "" {
		f.WriteString("远程perf解析服务器配置不完整，无法执行远程解析\n")
		return fmt.Errorf("远程perf解析服务器配置不完整")
	}

	// 使用封装函数连接到远程服务器并设置环境
	sshClientWithSFTP, err := connectAndSetupPerfEnvironment(config, rootfsDebugArchive, imageTarArchive, perfFiles, f)
	if err != nil {
		f.WriteString(fmt.Sprintf("连接远程服务器或设置环境失败: %v\n", err))
		return err
	}
	defer func() {
		if sshClientWithSFTP.SSH != nil {
			sshClientWithSFTP.SSH.Close()
		}
		if sshClientWithSFTP.SFTP != nil {
			sshClientWithSFTP.SFTP.Close()
		}
	}()

	// 使用executePerfAnalysis函数处理perf文件
	err = executePerfAnalysis(sshClientWithSFTP, config, perfFiles, perfResultDir, jobID, f)
	if err != nil {
		f.WriteString(fmt.Sprintf("执行perf分析失败: %v\n", err))
		return err
	}

	// 清理远程临时文件
	cleanupRemoteTempFiles(sshClientWithSFTP, config.workingDir, f)

	f.WriteString("perf解析完成\n")
	return nil
}

// 远程perf服务器配置
type perfRemoteConfig struct {
	server     string // 服务器地址
	user       string // 用户名
	password   string // 密码
	port       int    // SSH端口
	workingDir string // 远程工作目录
}

// 连接到远程服务器并设置perf环境
func connectAndSetupPerfEnvironment(
	config perfRemoteConfig,
	rootfsArchivePath string,
	imageTarPath string,
	perfFiles []string,
	f *os.File,
) (*libs.SshClient, error) {
	f.WriteString(fmt.Sprintf("连接到远程服务器 %s 进行perf文件解析...\n", config.server))

	// 连接到远程服务器
	sshClient := &libs.SshClient{}
	err := enhancedConnect(sshClient, config.user, config.server, config.port, config.password, f)
	if err != nil {
		f.WriteString(fmt.Sprintf("SSH连接失败: %s\n", err.Error()))
		return nil, err
	}

	// 创建远程工作目录
	f.WriteString("在远程服务器创建工作目录...\n")
	session, err := sshClient.SSH.NewSession()
	if err != nil {
		f.WriteString(fmt.Sprintf("创建SSH会话失败: %s\n", err.Error()))
		closeSSHClient(sshClient, f)
		return nil, err
	}
	defer session.Close()

	// 创建远程目录结构
	mkdirCmd := fmt.Sprintf("mkdir -p %s/rootfs-debug %s/perf-files %s/output",
		config.workingDir, config.workingDir, config.workingDir)

	if err := session.Run(mkdirCmd); err != nil {
		f.WriteString(fmt.Sprintf("在远程服务器创建工作目录失败: %s\n", err.Error()))
		closeSSHClient(sshClient, f)
		return nil, err
	}

	// 上传rootfs-debug和image.tar.bz2文件并解压
	if err := uploadAndExtractRootfsDebug(sshClient, rootfsArchivePath, imageTarPath, config.workingDir, f); err != nil {
		f.WriteString(fmt.Sprintf("上传和解压rootfs-debug失败: %s\n", err.Error()))
		closeSSHClient(sshClient, f)
		return nil, err
	}

	// 上传perf文件
	f.WriteString("上传perf文件到远程服务器...\n")
	remotePerfFilesPath := fmt.Sprintf("%s/perf-files", config.workingDir)
	for _, perfFile := range perfFiles {
		perfFileName := filepath.Base(perfFile)
		remotePath := fmt.Sprintf("%s/%s", remotePerfFilesPath, perfFileName)
		uploadFileToRemote(sshClient.SFTP, perfFile, remotePath, f)
	}

	return sshClient, nil
}

// 执行perf分析（并行处理多个文件）
func executePerfAnalysis(
	sshClient *libs.SshClient,
	config perfRemoteConfig,
	perfFiles []string,
	perfResultDir string,
	jobID string,
	f *os.File,
) error {
	f.WriteString(fmt.Sprintf("开始解析%d个perf文件，使用最多3个并发任务...\n", len(perfFiles)))

	// 获取关联的techsupportdecrypt作业
	job := dtechsupportdecrypt.SigTechSupportDecryptJob{}
	var decryptJobID uint = 0
	if jobID != "" {
		err := job.FindEx("job_id", jobID)
		if err == nil {
			decryptJobID = job.ID
			f.WriteString(fmt.Sprintf("关联到解密作业ID: %d\n", decryptJobID))
		} else {
			f.WriteString(fmt.Sprintf("未找到与JobID %s 关联的解密作业: %s\n", jobID, err.Error()))
		}
	} else {
		f.WriteString("未提供JobID，将无法关联到解密作业\n")
	}

	// 创建远程目录路径
	remotePerfDir := config.workingDir
	remoteRootfsDebugDir := fmt.Sprintf("%s/rootfs-debug", remotePerfDir)
	remoteOutputPath := fmt.Sprintf("%s/output", remotePerfDir)

	// 使用WaitGroup和信号量控制并发
	var wg sync.WaitGroup
	// 增加并发数量到3
	sem := make(chan struct{}, 3)
	var logMutex sync.Mutex

	// 并行处理每个perf文件
	for _, perfFile := range perfFiles {
		sem <- struct{}{} // 获取信号量
		wg.Add(1)

		go func(perfFilePath string) {
			defer func() {
				<-sem // 释放信号量
				wg.Done()
			}()

			perfFileName := filepath.Base(perfFilePath)
			remotePerfFilePath := fmt.Sprintf("%s/perf-files/%s", remotePerfDir, perfFileName)

			logMutex.Lock()
			f.WriteString(fmt.Sprintf("开始处理文件: %s\n", perfFileName))
			f.WriteString(fmt.Sprintf("上传文件到远程服务器: %s\n", perfFileName))
			logMutex.Unlock()

			// 上传perf文件到远程服务器
			if err := uploadFileToRemote(sshClient.SFTP, perfFilePath, remotePerfFilePath, f); err != nil {
				logMutex.Lock()
				f.WriteString(fmt.Sprintf("上传文件失败: %s, 错误: %s\n", perfFileName, err.Error()))
				logMutex.Unlock()
				return
			}

			// 1. 先执行perf report命令（较快）
			logMutex.Lock()
			f.WriteString(fmt.Sprintf("开始执行perf report命令: %s\n", perfFileName))
			logMutex.Unlock()

			reportErr := executePerfCommand(
				sshClient,
				"report",
				remotePerfDir,
				remotePerfFilePath,
				remoteRootfsDebugDir,
				perfFileName,
				300, // 5分钟超时
				&logMutex,
				f,
			)

			if reportErr != nil {
				logMutex.Lock()
				reportErrorMsg := fmt.Sprintf("perf report命令执行失败: %s", reportErr.Error())
				f.WriteString(reportErrorMsg + "\n")
				logMutex.Unlock()
				// 即使report失败，仍然尝试执行annotate
			}

			// 2. report完成后再执行perf annotate命令（较慢）--留存待以后使用
			// logMutex.Lock()
			// f.WriteString(fmt.Sprintf("开始执行perf annotate命令 (可能需要10-20分钟): %s\n", perfFileName))
			// logMutex.Unlock()

			// annotateErr := executePerfCommand(
			// 	sshClient,
			// 	"annotate",
			// 	remotePerfDir,
			// 	remotePerfFilePath,
			// 	remoteRootfsDebugDir,
			// 	perfFileName,
			// 	1800, // 30分钟超时
			// 	&logMutex,
			// 	f,
			// )

			// if annotateErr != nil {
			// 	logMutex.Lock()
			// 	annotateErrorMsg := fmt.Sprintf("perf annotate命令执行失败: %s", annotateErr.Error())
			// 	f.WriteString(annotateErrorMsg + "\n")
			// 	logMutex.Unlock()
			// }

			// logMutex.Lock()
			// f.WriteString(fmt.Sprintf("完成文件 %s 的perf分析\n", perfFileName))
			// logMutex.Unlock()
		}(perfFile)
	}

	// 等待所有解析任务完成
	f.WriteString("等待所有perf文件解析任务完成，这可能需要较长时间...\n")
	wg.Wait()
	f.WriteString("所有perf文件解析任务已完成\n")

	// 下载解析结果文件
	f.WriteString("下载perf远程服务器上的解析结果文件...\n")
	downloadDirFromRemote(sshClient.SFTP, remoteOutputPath, perfResultDir, ".perflog", f)

	// 保存perf分析结果记录到数据库
	f.WriteString("开始将perf分析结果保存到数据库...\n")

	// 遍历perfResultDir中的所有.perflog文件
	perfResultFiles, err := filepath.Glob(filepath.Join(perfResultDir, "*.perflog"))
	if err != nil {
		f.WriteString(fmt.Sprintf("获取perf结果文件列表时出错: %s\n", err.Error()))
		return err
	}

	for _, resultFile := range perfResultFiles {
		fileName := filepath.Base(resultFile)

		// 从文件名中提取信息
		var perfFileName, analysisType string

		if strings.Contains(fileName, ".report.perflog") {
			perfFileName = strings.TrimSuffix(fileName, ".report.perflog")
			analysisType = "report"
		} else if strings.Contains(fileName, ".annotate.perflog") {
			perfFileName = strings.TrimSuffix(fileName, ".annotate.perflog")
			analysisType = "annotate"
		} else {
			// 跳过不符合命名规则的文件
			continue
		}

		// 获取文件大小
		fileInfo, err := os.Stat(resultFile)
		if err != nil {
			f.WriteString(fmt.Sprintf("获取文件 %s 信息时出错: %s\n", fileName, err.Error()))
			continue
		}
		fileSize := fileInfo.Size()

		// 保存到数据库
		status := uint(1) // 默认成功
		errorMsg := ""

		// 文件大小为0或过小可能表示执行失败
		if fileSize < 100 {
			status = 2 // 失败
			errorMsg = "结果文件异常小或为空，可能执行失败"
		}

		// 将perfResultDir中相对路径保存到数据库
		resultPath := resultFile

		// 创建perf分析结果记录
		_, err = dperfanalysisresult.SavePerfResult(
			jobID,
			decryptJobID,
			perfFileName,
			analysisType,
			resultPath,
			fileSize,
			status,
			errorMsg,
		)

		if err != nil {
			f.WriteString(fmt.Sprintf("保存 %s 的perf分析结果到数据库时出错: %s\n", fileName, err.Error()))
		} else {
			f.WriteString(fmt.Sprintf("成功保存 %s 的perf分析结果到数据库\n", fileName))
		}
	}

	f.WriteString("完成perf分析结果保存到数据库\n")

	return nil
}

// 执行单个perf命令（annotate或report）
func executePerfCommand(
	sshClient *libs.SshClient,
	cmdType string,
	remotePerfDir string,
	remotePerfFilePath string,
	remoteRootfsDebugDir string,
	perfFileName string,
	timeoutSeconds int,
	logMutex *sync.Mutex,
	f *os.File,
) error {
	// 创建SSH会话
	session, err := sshClient.SSH.NewSession()
	if err != nil {
		return fmt.Errorf("创建SSH会话失败: %s", err.Error())
	}
	defer session.Close()

	var cmd string
	var outputFilePath string

	switch cmdType {
	case "annotate":
		// 确保--symfs参数正确指向rootfs-debug目录
		outputFilePath = fmt.Sprintf("output/%s.annotate.perflog", perfFileName)
		cmd = fmt.Sprintf(
			"cd %s && timeout %d perf annotate -i %s --symfs=%s -l --stdio > %s 2> output/%s.annotate.error.log",
			remotePerfDir,
			timeoutSeconds,
			remotePerfFilePath,
			remoteRootfsDebugDir, // 确保这里是完整路径
			outputFilePath,
			perfFileName,
		)
		logMutex.Lock()
		f.WriteString(fmt.Sprintf("执行annotate命令: %s\n", cmd))
		logMutex.Unlock()
	case "report":
		// 修正report命令
		outputFilePath = fmt.Sprintf("output/%s.report.perflog", perfFileName)
		cmd = fmt.Sprintf(
			"cd %s && timeout %d perf report --stdio -i %s --symfs=%s > %s 2> output/%s.report.error.log",
			remotePerfDir,
			timeoutSeconds,
			remotePerfFilePath,
			remoteRootfsDebugDir, // 确保这里是完整路径
			outputFilePath,
			perfFileName,
		)
		logMutex.Lock()
		f.WriteString(fmt.Sprintf("执行report命令: %s\n", cmd))
		logMutex.Unlock()
	default:
		return fmt.Errorf("未知的perf命令类型: %s", cmdType)
	}

	// 捕获命令输出
	var cmdOutput bytes.Buffer
	session.Stdout = &cmdOutput
	session.Stderr = &cmdOutput

	// 记录开始执行时间
	startTime := time.Now()
	logMutex.Lock()
	f.WriteString(fmt.Sprintf("开始执行 perf %s 命令，时间: %s\n", cmdType, startTime.Format("15:04:05")))
	logMutex.Unlock()

	// 执行命令
	cmdErr := session.Run(cmd)

	// 记录结束时间和执行时长
	endTime := time.Now()
	duration := endTime.Sub(startTime)

	logMutex.Lock()
	f.WriteString(fmt.Sprintf("perf %s 命令执行完成，用时: %s\n", cmdType, duration))
	logMutex.Unlock()

	var errorMessage string = ""

	if cmdErr != nil {
		// 检查是否是超时错误
		if strings.Contains(cmdErr.Error(), "signal: killed") {
			logMutex.Lock()
			errorMessage = fmt.Sprintf("perf %s 命令执行超时（%d秒），已终止", cmdType, timeoutSeconds)
			f.WriteString(errorMessage + "\n")
			logMutex.Unlock()
		} else {
			errorMessage = fmt.Sprintf("执行失败: %s\n%s", cmdErr.Error(), cmdOutput.String())
			return fmt.Errorf(errorMessage)
		}
	}

	logMutex.Lock()
	f.WriteString(fmt.Sprintf("远程perf %s命令执行流程完成: %s\n", cmdType, perfFileName))
	logMutex.Unlock()
	return nil
}

// 清理远程临时文件
func cleanupRemoteTempFiles(sshClient *libs.SshClient, remotePerfDir string, f *os.File) {
	cleanupSession, err := sshClient.SSH.NewSession()
	if err == nil {
		defer cleanupSession.Close()
		cleanupCmd := fmt.Sprintf("rm -rf %s", remotePerfDir)
		cleanupSession.Run(cleanupCmd)
		f.WriteString("远程临时文件清理完成\n")
	} else {
		f.WriteString(fmt.Sprintf("创建清理会话失败: %s\n", err.Error()))
	}
}

// 关闭SSH和SFTP连接
func closeSSHClient(client *libs.SshClient, f *os.File) {
	if client == nil {
		return
	}

	if client.SSH != nil {
		client.SSH.Close()
	}
	if client.SFTP != nil {
		client.SFTP.Close()
	}
	f.WriteString("已关闭远程连接\n")
}

// 从远程下载文件到本地目录
func downloadDirFromRemote(sftpClient *sftp.Client, remoteDir, localDir string, fileExt string, f *os.File) error {
	// 列出远程目录中的文件
	remoteFiles, err := sftpClient.ReadDir(remoteDir)
	if err != nil {
		f.WriteString(fmt.Sprintf("读取远程目录失败: %s\n", err.Error()))
		return err
	}

	// 确保本地目录存在
	if err := os.MkdirAll(localDir, 0755); err != nil {
		f.WriteString(fmt.Sprintf("创建本地目录失败: %s\n", err.Error()))
		return err
	}

	// 下载匹配扩展名的文件
	for _, remoteFile := range remoteFiles {
		if remoteFile.IsDir() {
			continue
		}

		if fileExt == "" || strings.HasSuffix(remoteFile.Name(), fileExt) {
			remotePath := fmt.Sprintf("%s/%s", remoteDir, remoteFile.Name())
			localPath := filepath.Join(localDir, remoteFile.Name())

			// 打开远程文件
			srcFile, err := sftpClient.Open(remotePath)
			if err != nil {
				f.WriteString(fmt.Sprintf("打开远程文件 %s 失败: %s\n", remoteFile.Name(), err.Error()))
				continue
			}

			// 创建本地文件
			dstFile, err := os.Create(localPath)
			if err != nil {
				f.WriteString(fmt.Sprintf("创建本地文件 %s 失败: %s\n", remoteFile.Name(), err.Error()))
				srcFile.Close()
				continue
			}

			// 复制文件内容
			_, err = io.Copy(dstFile, srcFile)
			if err != nil {
				f.WriteString(fmt.Sprintf("复制文件内容 %s 失败: %s\n", remoteFile.Name(), err.Error()))
			} else {
				f.WriteString(fmt.Sprintf("成功下载文件: %s\n", remoteFile.Name()))
			}

			srcFile.Close()
			dstFile.Close()
		}
	}

	return nil
}

// uploadDirectory 递归上传本地目录到远程服务器
func uploadDirectory(client *libs.SshClient, localPath, remotePath string, logFile *os.File) error {
	if client.SFTP == nil {
		return fmt.Errorf("SFTP客户端未初始化")
	}

	// 确保远程目录存在
	_, err := runSSHCommand(client, fmt.Sprintf("mkdir -p %s", remotePath))
	if err != nil {
		return fmt.Errorf("创建远程目录失败: %v", err)
	}

	// 读取本地目录
	items, err := os.ReadDir(localPath)
	if err != nil {
		return fmt.Errorf("读取本地目录失败: %v", err)
	}

	for _, item := range items {
		localItemPath := filepath.Join(localPath, item.Name())
		remoteItemPath := filepath.Join(remotePath, item.Name())

		if item.IsDir() {
			// 如果是目录，则递归上传
			err = uploadDirectory(client, localItemPath, remoteItemPath, logFile)
			if err != nil {
				return err
			}
		} else {
			// 如果是文件，则上传文件
			logFile.WriteString(fmt.Sprintf("上传文件: %s -> %s\n", localItemPath, remoteItemPath))
			err = client.UploadFile(localItemPath, remoteItemPath)
			if err != nil {
				return fmt.Errorf("上传文件失败 %s: %v", item.Name(), err)
			}
		}
	}

	return nil
}

// runSSHCommand 执行远程命令并返回输出
func runSSHCommand(sc *libs.SshClient, command string) (string, error) {
	if sc.SSH == nil {
		return "", fmt.Errorf("SSH客户端未初始化")
	}

	session, err := sc.SSH.NewSession()
	if err != nil {
		return "", err
	}
	defer session.Close()

	output, err := session.CombinedOutput(command)
	return string(output), err
}

// downloadFile 从远程服务器下载文件到本地
func downloadFile(sc *libs.SshClient, remotePath, localPath string) error {
	if sc.SFTP == nil {
		return fmt.Errorf("SFTP客户端未初始化")
	}

	// 打开远程文件
	remoteFile, err := sc.SFTP.Open(remotePath)
	if err != nil {
		return fmt.Errorf("打开远程文件失败: %v", err)
	}
	defer remoteFile.Close()

	// 创建本地文件
	localFile, err := os.Create(localPath)
	if err != nil {
		return fmt.Errorf("创建本地文件失败: %v", err)
	}
	defer localFile.Close()

	// 读取远程文件内容
	remoteData, err := io.ReadAll(remoteFile)
	if err != nil {
		return fmt.Errorf("读取远程文件内容失败: %v", err)
	}

	// 写入本地文件
	_, err = localFile.Write(remoteData)
	if err != nil {
		return fmt.Errorf("写入本地文件失败: %v", err)
	}

	return nil
}
