package datasync

import (
	"time"

	"irisAdminApi/application/controllers/openfeishu"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/coverityresult/dcoverityresult"
)

func SyncCoverityResultDataToFeiShu(project, appToken, tableID string) {
	// appToken := "Hi7rbhcSxaAEvnsm7HrcQDHkn1e"
	// tableID := "tblZ37ZhPHt3HTNu"
	// 批量删除线上数据
	items, err := dcoverityresult.CountSummaryByProject(project)
	if err != nil {
		logging.ErrorLogger.Errorf("find count summary error", err)
		return
	}
	openfeishu.DeleteTableRecordDataV4(appToken, tableID, []string{"项目"})

	// 批量同步到线上
	now := time.Now()
	const batchSize = 500
	for i := 0; i < len(items); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(items) {
			endIndex = len(items)
		}
		batchData := items[i:endIndex]
		if len(batchData) > 0 {
			records := []map[string]interface{}{}
			// 将新增数据按照批次插入飞书数据表,每批次500条
			for _, item := range batchData {
				rec := map[string]interface{}{
					"项目":    item.Project,
					"产品":    item.Product,
					"组件":    item.ComponentName,
					"待分析":   item.ToAnalyze,
					"确认未修复": item.ToFix,
					"误报":    item.FalsePositive,
					"特意":    item.Intentional,
					"负责人":   item.Owner,
					"报告文件":  item.ReportFileName,
					"更新时间":  now.UnixMilli(),
				}

				records = append(records, rec)
			}

			tableRecordResp, err := openfeishu.BatchCreate(tableID, appToken, records)
			if err != nil {
				logging.ErrorLogger.Errorf("批量创建记录失败: %s", err.Error(), string(tableRecordResp.RawBody))
				continue
			}
		}
	}
}
