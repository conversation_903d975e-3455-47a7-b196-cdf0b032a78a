import re
import sys
import datetime
import os
import json

# 常量定义
"""
code: 1
  收集正常，解析检查结果判定为正常
code: -1
  收集正常，解析检查结果判定为异常
code: -2:
  配置异常，无法解析
"""

result = []
summary = {}
# 假设日志文本存储在一个名为 log_text 的字符串中
if len(sys.argv) > 1:
    filename = os.path.basename(sys.argv[1])
    a = filename.split("_")
    if len(a) == 4:
        _type = a[0]
        hostname = a[1]
        ip = a[2]
        created_at = a[3]
        summary = {"type": _type, "hostname": hostname, "ip": ip, "created_at": created_at, "result": result}
if not summary:
    print("error: 文件名不符合type_hostname_ip_timestamp.log要求。")
with open(sys.argv[1], "r", encoding="utf8") as f:
    log_text = f.read()

# 使用正则表达式匹配 "start" 和 "end" 之间的段落
pattern = re.compile(
    r"=============== start (\d+) (\w+) ===================\n(.*?)\n=============== end (\d+) (\w+) ===================",
    re.DOTALL,
)

matches = pattern.finditer(log_text)


for match in matches:
    start_num, start_type, content, end_num, end_type = match.groups()
    content = match.group()  # 获取段落内容
    lines = content.split("\n")
    # print(lines)
    s = lines[0].split()
    # print(len(s))
    if len(s) == 5:
        if s[1] == "start":
            rule_id = s[2]
            name = s[3]
    else:
        if s[1] == "start":
            rule_id = s[2]
            result.append(
                {
                    "rule_id": rule_id,
                    "name": "",
                    "code": "-2",
                    "msg": "配置异常，无法采集",
                    "data": [],
                }
            )
        continue
    if rule_id == "01":
        if "categraf" in lines:
            result.append(
                {
                    "rule_id": rule_id,
                    "name": name,
                    "code": "1",
                    "msg": "已安装categraf",
                    "data": lines[1:-1],
                }
            )
        else:
            result.append(
                {
                    "rule_id": rule_id,
                    "name": name,
                    "code": "-1",
                    "msg": "未安装categraf",
                    "data": lines[1:-1],
                }
            )
        continue
    if rule_id == "02":
        if "/opt/categraf/categraf" in "".join(lines):
            result.append(
                {
                    "rule_id": rule_id,
                    "name": name,
                    "code": "1",
                    "msg": "已运行categraf",
                    "data": lines[1:-1],
                }
            )
        else:
            result.append(
                {
                    "rule_id": rule_id,
                    "name": name,
                    "code": "-1",
                    "msg": "未运行categraf",
                    "data": lines[1:-1],
                }
            )
        continue
    if rule_id == "03":
        if "succeeded" in "".join(lines):
            result.append(
                {
                    "rule_id": rule_id,
                    "name": name,
                    "code": "1",
                    "msg": "与nightingale(监控服务器)连通性正常",
                    "data": lines[1:-1],
                }
            )
        else:
            result.append(
                {
                    "rule_id": rule_id,
                    "name": name,
                    "code": "-1",
                    "msg": "无法连接nightingale(监控服务器)",
                    "data": lines[1:-1],
                }
            )
        continue
    if rule_id == "04":
        _lines = []
        white_list = [
            "root",
            "aqyfzx",
            "sync",
            "aqsvn-1",
            "aqsvn-2",
            "bookstack-1",
            "bookstack-2",
            "compile-1",
            "compile-2",
            "compile-3",
            "compile-4",
            "baseos_sync",
            "shutdown",
            "halt",
            "redis",
            "git",
            "gitlab-psql",
            "gitlab-prometheus",
            "libvirt-qemu",
            "nova",
            "backup-beijing",
            "bootrelease"
        ]
        for line in lines[1: -1]:
            flag = False
            for user_name in white_list:
                if line.startswith(f"{user_name}:"):
                    flag = True
                    break
            if flag:
                continue
            _lines.append(line)
        if len(_lines) == 0:
            result.append(
                {
                    "rule_id": rule_id,
                    "name": name,
                    "code": "1",
                    "msg": "可登录帐号合规",
                    "data": lines[1:-1],
                }
            )
        else:
            result.append(
                {
                    "rule_id": rule_id,
                    "name": name,
                    "code": "-1",
                    "msg": "存在非白名单可登录帐号",
                    "data": _lines,
                }
            )
        continue
    if rule_id == "05":
        date_format = "%b %d, %Y"
        now = datetime.datetime.now()
        _result = {}
        _lines = []
        for line in lines:
            if line.startswith("Last password change"):
                _lines.append(line)
                change_time_str = line.split(":")[1].strip()
                parsed_date = datetime.datetime.strptime(change_time_str, date_format)
                delta = now - parsed_date
                if delta.days > 90:
                    _result = {
                        "rule_id": rule_id,
                        "name": name,
                        "code": "-1",
                        "msg": "存在帐号过超90天未修改密码",
                        "data": _lines,
                    }
        if _result:
            result.append(_result)
        else:
            result.append(
                {
                    "rule_id": rule_id,
                    "name": name,
                    "code": "1",
                    "msg": "所有帐号90天曾更新过密码",
                    "data": _lines,
                }
            )
        continue
    if rule_id == "06":
        _lines = []
        now = datetime.datetime.now()
        for line in lines[1: -1]:
            try:
                a = line.split("|")
                log_date = a[1].strip()
                log_time = a[2].strip()
            except Exception as e:
                if 'SEL has no entries' in line:
                    continue
                _lines.append(line)
            try:
                log_created_at = datetime.datetime.strptime(f'{log_date} {log_time}', "%m/%d/%Y %H:%M:%S")
                delta = now - log_created_at
                if delta.days < 30:
                    _lines.append(line)
            except:
                continue
        if len(_lines) == 0:
            result.append(
                {
                    "rule_id": rule_id,
                    "name": name,
                    "code": "1",
                    "msg": "硬件无告警日志",
                    "data": lines[1:-1],
                }
            )
        else:
            result.append(
                {
                    "rule_id": rule_id,
                    "name": name,
                    "code": "-1",
                    "msg": "硬件存在告警日志",
                    "data": _lines,
                }
            )
        continue
    if rule_id == "07":
        _lines = []
        white_list = [
            "HEST: Enabling Firmware First mode for corrected errors.",
            "ERST: Error Record Serialization Table (ERST) support is initialized.",
            "RAS: Correctable Errors collector initialized."
        ]
        for line in lines[1: -1]:
            flag = False
            for log in white_list:
                if log in line:
                    flag = True
                    break
            if flag:
                continue
            _lines.append(line)
        if len(_lines) == 0:
            result.append(
                {
                    "rule_id": rule_id,
                    "name": name,
                    "code": "1",
                    "msg": "内核无错误日志",
                    "data": lines[1:-1],
                }
            )
        else:
            result.append(
                {
                    "rule_id": rule_id,
                    "name": name,
                    "code": "-1",
                    "msg": "内核存在告警日志",
                    "data": _lines,
                }
            )
        continue
    if rule_id == "08":
        _lines = []
        for line in lines:
            if "E! exec_command:" in line:
                continue
            if "Server returned error NXDOMAIN" in line:
                continue
            if "snapd" in line:
                continue
            if "DEBUG" in line.upper():
                continue
            if " ERROR " not in line.upper():
                continue
            _lines.append(line)
        if len(_lines) <= 2:
            result.append(
                {
                    "rule_id": rule_id,
                    "name": name,
                    "code": "1",
                    "msg": "系统无错误日志",
                    "data": [],
                }
            )
        else:
            result.append(
                {
                    "rule_id": rule_id,
                    "name": name,
                    "code": "-1",
                    "msg": "系统存在错误日志",
                    "data": _lines[1:-1],
                }
            )
        continue
    if rule_id == "09":
        _lines = []

        for line in lines:
            if "/dev" not in line:
                continue
            a = line.split()
            mountpoint = a[-1]
            used_percent = a[-2].replace("%", "")
            if float(used_percent) > 90:
                _lines.append(line)
        if len(_lines) == 0:
            result.append(
                {
                    "rule_id": rule_id,
                    "name": name,
                    "code": "1",
                    "msg": "文件系统使用率无异常",
                    "data": [],
                }
            )
        else:
            result.append(
                {
                    "rule_id": rule_id,
                    "name": name,
                    "code": "-1",
                    "msg": "文件系统使用率大于90%",
                    "data": _lines,
                }
            )
        continue
    if rule_id == "10":
        _lines = []
        _error_lines = []
        for line in lines:
            if "test result" in line:
                _lines.append(line)
                a = line.split(":")
                # dev = a[0]
                status = a[-1].strip()
                if status != "PASSED":
                    _error_lines.append(line)

        if len(_lines) > 0 and len(_error_lines) == 0:
            result.append(
                {
                    "rule_id": rule_id,
                    "name": name,
                    "code": "1",
                    "msg": "磁盘状态无异常",
                    "data": _lines,
                }
            )
        else:
            result.append(
                {
                    "rule_id": rule_id,
                    "name": name,
                    "code": "-1",
                    "msg": "磁盘状态异常",
                    "data": _error_lines if _error_lines else _lines,
                }
            )
        continue

    if rule_id == "11":
        _lines = []
        _error_lines = []
        for line in lines:
            if "State" in line:
                _lines.append(line)
                a = line.split(":")
                # dev = a[0]
                status = a[-1].strip()
                if status != "Optimal":
                    _error_lines.append(line)

        if len(_lines) == 0 or len(_error_lines) == 0:
            result.append(
                {
                    "rule_id": rule_id,
                    "name": name,
                    "code": "1",
                    "msg": "RAID状态无异常",
                    "data": _lines,
                }
            )
        else:
            result.append(
                {
                    "rule_id": rule_id,
                    "name": name,
                    "code": "-1",
                    "msg": "RAID状态异常",
                    "data": _error_lines if _error_lines else _lines,
                }
            )
        continue

    if rule_id == "12":
        _lines = []
        _error_lines = []
        for line in lines[1: -1]:
            if "state" in line:
                _lines.append(line)
                a = line.split(":")
                # dev = a[0]
                status = a[-1].strip()
                # "Online, Spun Up" "Unconfigured(good), Spun down" "Hotspare, Spun down" "Hotspare, Spun Up"
                if status not in [
                    "Online, Spun Up",
                    "Unconfigured(good), Spun down",
                    "Hotspare, Spun down",
                    "Hotspare, Spun Up",
                ]:
                    _error_lines.append(line)

        if len(_lines) == 0 or len(_error_lines) == 0:
            result.append(
                {
                    "rule_id": rule_id,
                    "name": name,
                    "code": "1",
                    "msg": "megacli磁盘状态无异常",
                    "data": _lines,
                }
            )
        else:
            result.append(
                {
                    "rule_id": rule_id,
                    "name": name,
                    "code": "-1",
                    "msg": "megacli磁盘状态异常",
                    "data": _error_lines if _error_lines else _lines,
                }
            )
        continue
print(json.dumps(summary))

# 检查是否存在错误
# has_error = re.search(r'E! exec_command: .*\.sh error', content) or re.search(r'Server returned error NXDOMAIN', content)

# if has_error:
#     print(f"Error found in section {start_num} {start_type}:")
#     print(content)
# else:
#     print(f"No error found in section {start_num} {start_type}.")
