package dpmsrequest

import (
	"encoding/json"
	"fmt"
	"reflect"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/datasync"
	"irisAdminApi/service/dao/datasync/dsyncrecord"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "PMS需求表"

type PmsRequestSyncResponse struct {
	State   string                `json:"state"`
	Data    []*PmsRequestResponse `json:"data"`
	Total   int                   `json:"total"`
	Message string                `json:"message"`
}

type PmsRequestResponse struct {
	RowNum        int    `json:"rownum"`
	RequestID     int    `gorm:"primarykey; autoIncrement:false" json:"requestId" `
	ProjectID     int    `gorm:"not null;" json:"projectId" update:"1"`
	ProjectName   string `gorm:"not null; type:varchar(100)" json:"projectName" update:"1"`
	RequestName   string `gorm:"not null; type:varchar(100)" json:"requestName" update:"1"`
	RequestStatus string `gorm:"not null; type:varchar(100)" json:"requestStatus" update:"1"`
	PgttmUserName string `gorm:"not null; type:varchar(100)" json:"pgttmUserName" update:"1"`
	ChangeType    string `gorm:"not null; type:varchar(100)" json:"changeType" update:"1"`
	Keyword       string `gorm:"not null; type:varchar(100)" json:"keyword" update:"1"`
	Disabled      bool   `json:"disabled" update:"1"`
}

type PmsRequest struct {
	datasync.PmsRequest
}

type PmsRequestSummary struct {
	ProjectName   string  `json:"project_name"`
	PgttmUserName string  `json:"pgttm_user_name"`
	RequestName   string  `json:"request_name"`
	TotalCodes    float32 `json:"total_codes"`
}

type ListResponse struct {
	PmsRequest
}

type Request struct {
	Id uint `json:"id"`
}

func (this *PmsRequest) ModelName() string {
	return ModelName
}

func Model() *datasync.PmsRequest {
	return &datasync.PmsRequest{}
}

func (this *PmsRequest) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *PmsRequest) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *PmsRequest) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *PmsRequest) CreateV2(object interface{}) error {
	return nil
}

func (this *PmsRequest) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *PmsRequest) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *PmsRequest) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *PmsRequest) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *PmsRequest) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *PmsRequest) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func UpdateOrCreatePmsRequesTransaction(items []*PmsRequestResponse, _url string, data map[string]string, method, state, errorMsg string) error {
	objects := []map[string]interface{}{}
	for _, item := range items {
		object := map[string]interface{}{
			"RequestID":     item.RequestID,
			"ProjectID":     item.ProjectID,
			"ProjectName":   item.ProjectName,
			"RequestName":   item.RequestName,
			"RequestStatus": item.RequestStatus,
			"PgttmUserName": item.PgttmUserName,
			"ChangeType":    item.ChangeType,
			"Disabled":      item.Disabled,
			"Keyword":       item.Keyword,
		}

		objects = append(objects, object)
	}

	columns := []string{}

	xt := reflect.TypeOf(Model())
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")
		if ok && ok2 {
			columns = append(columns, name)
		}
	}

	body, err := json.Marshal(data)
	if err != nil {
		return err
	}
	db := easygorm.GetEasyGormDb()
	err = db.Transaction(func(tx *gorm.DB) error {
		if len(objects) > 0 {
			err := tx.Model(Model()).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "request_id"}},
				DoUpdates: clause.AssignmentColumns(columns),
			}).Create(&objects).Error
			if err != nil {
				return err
			}
		}

		if err := tx.Model(dsyncrecord.Model()).Create(map[string]interface{}{
			"url":             _url,
			"body":            body,
			"method":          method,
			"state":           state,
			"message":         errorMsg,
			"min_modify_date": data["minModifyDate"],
			"max_modify_date": data["maxModifyDate"],
			"created_at":      time.Now(),
		}).Error; err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func GetPmsRequestSummary() ([]*PmsRequestSummary, error) {
	items := []*PmsRequestSummary{}
	selects := []string{
		"pr.project_name project_name",
		"pr.pgttm_user_name pgttm_user_name",
		"pr.request_name request_name",
		"round(SUM(total_codes),2) total_codes",
	}

	db := easygorm.GetEasyGormDb().
		Table("pms_workpacket_infos pwi").
		Joins("LEFT JOIN pms_requests pr ON pr.request_id = pwi.request_id ").
		Select(selects).
		Group("pr.project_name, pr.pgttm_user_name, pr.request_name")

	where := easygorm.GetEasyGormDb().Where(`pr.pgttm_user_name is not null and pr.pgttm_user_name != '' and pwi.request_id IS NOT NULL AND pwi.disabled != 1 AND pwi.project_change_type != 'CHANGE_CANCEL' `)
	err := db.Where(where).Find(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func GetRequestData(page int, pageSize int) ([]*ListResponse, error) {
	items := []*ListResponse{}
	offset := (page - 1) * pageSize
	err := easygorm.GetEasyGormDb().Model(Model()).
		// Where(`project_name like "NTOS%" or project_name like "安全云%" or project_name like "MACC%" or project_name like "12.%"`).
		Offset(offset).
		Limit(pageSize).
		Find(&items).Error

	return items, err
}
