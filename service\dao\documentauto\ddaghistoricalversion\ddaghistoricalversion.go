package ddaghistoricalversion

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/documentauto"
)

const ModelName = "历史版本数据表"

type Response struct {
	documentauto.DagHistoricalVersion
}

type ListResponse struct {
	Response
}

type Request struct {
	SeriesID    uint   `json:"series_id"`    //系列ID
	VersionID   uint   `json:"version_id"`   //版本ID
	ReleaseDate string `json:"release_date"` //发布时间
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *documentauto.DagHistoricalVersion {
	return &documentauto.DagHistoricalVersion{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (a *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

type HistoricalVersion struct {
	ID            uint   `json:"id"`
	SeriesID      uint   `json:"series_id"` //系列ID
	SeriesName    string `json:"series_name"`
	VersionID     uint   `json:"version_id"`     //版本ID
	VersionNumber string `json:"version_number"` //版本号
	ReleaseDate   string `json:"release_date"`   //发布时间
	HReleaseDate  string `json:"h_release_date"` //历史版本发布时间
	Status        string `json:"status"`         //状态
}

func GetHistoricalVersion(seriesID uint, sort uint) ([]*HistoricalVersion, error) {
	items := []*HistoricalVersion{}
	sql := ""
	//若sort为0，则获取全部数据
	if sort > 0 {
		sql = fmt.Sprintf(`
	SELECT 
		dhv.series_id,
		dv.series_name,
		dhv.version_id,
		dv.version_number,
		dv.release_date,
		dv.status,
		dhv.release_date as h_release_date		
	FROM 
		dag_historical_versions dhv
	LEFT JOIN dag_versions dv ON dhv.version_id = dv.id
	WHERE 
		dhv.series_id = %d AND dv.sort < %d
	ORDER BY dv.sort ASC
		`, seriesID, sort)

	} else {
		sql = fmt.Sprintf(`
		SELECT 
			dhv.series_id,
			dhv.version_id,
			dv.version_number,
			dv.release_date,
			dv.status,
			dhv.release_date as h_release_date
		FROM 
			dag_historical_versions dhv
		LEFT JOIN dag_versions dv ON dhv.version_id = dv.id
		WHERE 
			dhv.series_id = %d
		ORDER BY dv.sort ASC
		`, seriesID)
	}
	err := easygorm.GetEasyGormDb().Table("dag_historical_versions").Raw(sql).Scan(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get historical version err ", err)
		return nil, err
	}
	return items, nil
}

func GetHistoryList(name string, seriesID, versionID, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*HistoricalVersion
	db := easygorm.GetEasyGormDb().Table("dag_historical_versions AS dhv").
		Select("dhv.*, dv.version_number as version_number,ds.name as series_name").
		Joins("LEFT JOIN dag_series AS ds ON dhv.series_id = ds.id").
		Joins("LEFT JOIN dag_versions as dv ON dhv.version_id = dv.id")
	if len(name) > 0 {
		db = db.Where("dv.version_number", "like", fmt.Sprintf("%%%s%%", name))
	}
	if seriesID > 0 {
		db = db.Where("dhv.series_id = ?", seriesID)
	}
	if versionID > 0 {
		db = db.Where("dhv.version_id = ?", versionID)
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}
	paginateScope := easygorm.PaginateScope(page, pageSize, "asc", "dhv.series_id,dv.version_number")
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}
