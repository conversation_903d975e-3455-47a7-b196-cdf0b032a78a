package datasync

import (
	"time"
)

/*
CREATE TABLE `csbu_res_resource_split` (
	`id` BIGINT(20) NULL DEFAULT NULL,
	`applyId` BIGINT(20) NULL DEFAULT NULL,
	`userId` BIGINT(20) NULL DEFAULT NULL,
	`year` VARCHAR(10) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`month` VARCHAR(5) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`hour` DOUBLE NULL DEFAULT NULL,
	`percent` DOUBLE NULL DEFAULT NULL,
	`projectId` BIGINT(20) NULL DEFAULT NULL,
	`beginTime` DATETIME NULL DEFAULT NULL,
	`endTime` DATETIME NULL DEFAULT NULL,
	`planHour` DOUBLE NULL DEFAULT NULL,
	`enable` INT(11) NULL DEFAULT NULL,
	`createDate` D<PERSON>ETIME NULL DEFAULT NULL,
	`modifyDate` DATETIME NULL DEFAULT NULL,
	`validDays` INT(11) NULL DEFAULT NULL,
	`planMan<PERSON><PERSON>h` DOUBLE NULL DEFAULT NULL,
	`targetDeptId` BIGINT(20) NULL DEFAULT NULL,
	`targetGroupId` BIGINT(20) NULL DEFAULT NULL,
	`userName` VARCHAR(100) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`casUserId` VARCHAR(100) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`virtualDept` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`businessDept` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`departmentName` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`groupName` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`isFormalEstablishment` INT(11) NULL DEFAULT NULL,
	`pmsProjectId` INT(11) NULL DEFAULT NULL,
	`pmsProjectName` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`oneDeptCode` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`twoDeptCode` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`threeDeptCode` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`fourDeptCode` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`fiveDeptCode` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`oneDeptName` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`twoDeptName` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`threeDeptName` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`fourDeptName` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`fiveDeptName` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`finalDeptCode` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	INDEX `idx_csbu_res_resource_split_lookup` (`id`) USING BTREE
)
COLLATE='utf8_general_ci'
ENGINE=InnoDB
;
*/

type CsbuResResourceSplit struct {
	ID                    int64      `gorm:"uniqueIndex" json:"id"`
	ApplyID               int64      `json:"apply_id" updated:"1"`
	UserID                int64      `json:"user_id" updated:"1"`
	Year                  string     `json:"year" updated:"1"`
	Month                 string     `json:"month" updated:"1"`
	Hour                  float64    `json:"hour" updated:"1"`
	Percent               float64    `json:"percent" updated:"1"`
	ProjectID             int64      `json:"project_id" updated:"1"`
	BeginTime             *time.Time `json:"begin_time" updated:"1"`
	EndTime               *time.Time `json:"end_time" updated:"1"`
	PlanHour              float64    `json:"plan_hour" updated:"1"`
	Enable                int        `json:"enable" updated:"1"`
	CreateDate            *time.Time `json:"create_date" updated:"1"`
	ModifyDate            *time.Time `json:"modify_date" updated:"1"`
	ValidDays             int        `json:"valid_days" updated:"1"`
	PlanManMonth          float64    `json:"plan_man_month" updated:"1"`
	TargetDeptID          int64      `json:"target_dept_id" updated:"1"`
	TargetGroupID         int64      `json:"target_group_id" updated:"1"`
	UserName              string     `json:"user_name" updated:"1"`
	CasUserID             string     `json:"cas_user_id" updated:"1"`
	VirtualDept           string     `json:"virtual_dept" updated:"1"`
	BusinessDept          string     `json:"business_dept" updated:"1"`
	DepartmentName        string     `json:"department_name" updated:"1"`
	GroupName             string     `json:"group_name" updated:"1"`
	IsFormalEstablishment int        `json:"is_formal_establishment" updated:"1"`
	PmsProjectID          int        `json:"pms_project_id" updated:"1"`
	PmsProjectName        string     `json:"pms_project_name" updated:"1"`
	OneDeptCode           string     `json:"one_dept_code" updated:"1"`
	TwoDeptCode           string     `json:"two_dept_code" updated:"1"`
	ThreeDeptCode         string     `json:"three_dept_code" updated:"1"`
	FourDeptCode          string     `json:"four_dept_code" updated:"1"`
	FiveDeptCode          string     `json:"five_dept_code" updated:"1"`
	OneDeptName           string     `json:"one_dept_name" updated:"1"`
	TwoDeptName           string     `json:"two_dept_name" updated:"1"`
	ThreeDeptName         string     `json:"three_dept_name" updated:"1"`
	FourDeptName          string     `json:"four_dept_name" updated:"1"`
	FiveDeptName          string     `json:"five_dept_name" updated:"1"`
	FinalDeptCode         string     `json:"final_dept_code" updated:"1"`
}

/*
CREATE TABLE `csbu_res_resource_apply` (
	`id` BIGINT(20) NULL DEFAULT NULL,
	`applyUserId` BIGINT(20) NULL DEFAULT NULL,
	`applyDate` DATETIME NULL DEFAULT NULL,
	`applyReason` MEDIUMTEXT NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`status` VARCHAR(2) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`verifyUserId` BIGINT(20) NULL DEFAULT NULL,
	`verifyDate` DATETIME NULL DEFAULT NULL,
	`verifyRemark` MEDIUMTEXT NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`targetDeptId` BIGINT(20) NULL DEFAULT NULL,
	`targetUserId` BIGINT(20) NULL DEFAULT NULL,
	`targetProjectId` BIGINT(20) NULL DEFAULT NULL,
	`workContent` MEDIUMTEXT NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`planPercent` DOUBLE NULL DEFAULT NULL,
	`planMonth` VARCHAR(2) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`planYear` VARCHAR(10) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`planManMonth` DOUBLE NULL DEFAULT NULL,
	`beginTime` DATETIME NULL DEFAULT NULL,
	`endTime` DATETIME NULL DEFAULT NULL,
	`planHour` DOUBLE NULL DEFAULT NULL,
	`enable` INT(11) NULL DEFAULT NULL,
	`createDate` DATETIME NULL DEFAULT NULL,
	`modifyDate` DATETIME NULL DEFAULT NULL,
	`source` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`applyUserName` VARCHAR(100) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`applyCasUserId` VARCHAR(100) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`pmsProjectId` INT(11) NULL DEFAULT NULL,
	`pmsProjectName` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`targetUserName` VARCHAR(100) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`targetCasUserId` VARCHAR(100) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`targetUserBusinessDept` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`targetUserDepartmentName` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`targetUserGroupName` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`targetDeptName` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`targetDeptCode` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`importKey` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`importUserId` BIGINT(20) NULL DEFAULT NULL,
	`changeReason` TEXT NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`oneDeptCode` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`twoDeptCode` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`threeDeptCode` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`fourDeptCode` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`fiveDeptCode` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`oneDeptName` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`twoDeptName` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`threeDeptName` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`fourDeptName` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`fiveDeptName` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	`finalDeptCode` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	INDEX `idx_csbu_res_resource_apply_lookup` (`id`) USING BTREE
)
COLLATE='utf8_general_ci'
ENGINE=InnoDB
;
*/

type CsbuResResourceApply struct {
	ID                       int64      `gorm:"uniqueIndex" json:"id"`
	ApplyUserID              int64      `json:"apply_user_id" updated:"1"`
	ApplyDate                *time.Time `json:"apply_date" updated:"1"`
	ApplyReason              string     `json:"apply_reason" updated:"1"`
	Status                   string     `json:"status" updated:"1"`
	VerifyUserID             int64      `json:"verify_user_id" updated:"1"`
	VerifyDate               *time.Time `json:"verify_date" updated:"1"`
	VerifyRemark             string     `json:"verify_remark" updated:"1"`
	TargetDeptID             int64      `json:"target_dept_id" updated:"1"`
	TargetUserID             int64      `json:"target_user_id" updated:"1"`
	TargetProjectID          int64      `json:"target_project_id" updated:"1"`
	WorkContent              string     `json:"work_content" updated:"1"`
	PlanPercent              float64    `json:"plan_percent" updated:"1"`
	PlanMonth                string     `json:"plan_month" updated:"1"`
	PlanYear                 string     `json:"plan_year" updated:"1"`
	PlanManMonth             float64    `json:"plan_man_month" updated:"1"`
	BeginTime                *time.Time `json:"begin_time" updated:"1"`
	EndTime                  *time.Time `json:"end_time" updated:"1"`
	PlanHour                 float64    `json:"plan_hour" updated:"1"`
	Enable                   int        `json:"enable" updated:"1"`
	CreateDate               *time.Time `json:"create_date" updated:"1"`
	ModifyDate               *time.Time `json:"modify_date" updated:"1"`
	Source                   string     `json:"source" updated:"1"`
	ApplyUserName            string     `json:"apply_user_name" updated:"1"`
	ApplyCasUserID           string     `json:"apply_cas_user_id" updated:"1"`
	PmsProjectID             int        `json:"pms_project_id" updated:"1"`
	PmsProjectName           string     `json:"pms_project_name" updated:"1"`
	TargetUserName           string     `json:"target_user_name" updated:"1"`
	TargetCasUserID          string     `json:"target_cas_user_id" updated:"1"`
	TargetUserBusinessDept   string     `json:"target_user_business_dept" updated:"1"`
	TargetUserDepartmentName string     `json:"target_user_department_name" updated:"1"`
	TargetUserGroupName      string     `json:"target_user_group_name" updated:"1"`
	TargetDeptName           string     `json:"target_dept_name" updated:"1"`
	TargetDeptCode           string     `json:"target_dept_code" updated:"1"`
	ImportKey                string     `json:"import_key" updated:"1"`
	ImportUserID             int64      `json:"import_user_id" updated:"1"`
	ChangeReason             string     `json:"change_reason" updated:"1"`
	OneDeptCode              string     `json:"one_dept_code" updated:"1"`
	TwoDeptCode              string     `json:"two_dept_code" updated:"1"`
	ThreeDeptCode            string     `json:"three_dept_code" updated:"1"`
	FourDeptCode             string     `json:"four_dept_code" updated:"1"`
	FiveDeptCode             string     `json:"five_dept_code" updated:"1"`
	OneDeptName              string     `json:"one_dept_name" updated:"1"`
	TwoDeptName              string     `json:"two_dept_name" updated:"1"`
	ThreeDeptName            string     `json:"three_dept_name" updated:"1"`
	FourDeptName             string     `json:"four_dept_name" updated:"1"`
	FiveDeptName             string     `json:"five_dept_name" updated:"1"`
	FinalDeptCode            string     `json:"final_dept_code" updated:"1"`
}
