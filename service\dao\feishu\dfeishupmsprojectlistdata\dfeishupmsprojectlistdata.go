package dfeishupmsprojectlistdata

import (
	"fmt"
	"reflect"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/feishu"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "项目ID列表"

type PmsProjectListData struct {
	feishu.PmsProjectListData
}

type ListResponse struct {
	PmsProjectListData
}

type Request struct {
	Id uint `json:"id"`
}

func (a *PmsProjectListData) ModelName() string {
	return ModelName
}

func Model() *feishu.PmsProjectListData {
	return &feishu.PmsProjectListData{}
}

func (a *PmsProjectListData) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (a *PmsProjectListData) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (a *PmsProjectListData) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (a *PmsProjectListData) CreateV2(object interface{}) error {
	return nil
}

func (a *PmsProjectListData) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (u *PmsProjectListData) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (u *PmsProjectListData) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *PmsProjectListData) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *PmsProjectListData) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *PmsProjectListData) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func BatchCreate(items []map[string]interface{}) error {
	db := easygorm.GetEasyGormDb().Model(Model())
	return db.Create(items).Error
}

func DeleteAll() error {
	db := easygorm.GetEasyGormDb().Model(Model())
	err := db.Unscoped().Delete(Model(), "id>0").Error
	if err != nil {
		return err
	}
	// 重置id值
	alterTableSQL := fmt.Sprintf("ALTER TABLE feishu_file_lists AUTO_INCREMENT=%d", 1)
	if err := easygorm.GetEasyGormDb().Exec(alterTableSQL).Error; err != nil {
		logging.ErrorLogger.Errorf("ALTER TABLE feishu_file_lists  get err ", err)
		return err
	}
	return nil
}

func CreateOrUpdateProjectListData(objects []map[string]interface{}, projectNames []string) error {
	columns := []string{"updated_at"}
	xt := reflect.TypeOf(Model())
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")
		if ok && ok2 {
			columns = append(columns, name)
		}
	}
	// 获取数据库当前状态， 如果与最新状态不一致，则判断是否最新状态是否属于完成、暂停、终止，如果是，则更新StopUpdateAt时间为当前时间
	// 后续判断 stop_update_at 超过3天，则不再爬取相关信息
	projectlistdata := []*ListResponse{}
	db := easygorm.GetEasyGormDb()
	err := db.Model(Model()).Where("project_name in ?", projectNames).Find(&projectlistdata).Error
	if err != nil {
		return err
	}

	lastStatusMap := map[string]*ListResponse{}
	for _, item := range projectlistdata {
		lastStatusMap[item.ProjectName] = item
	}
	now := time.Now()

	for _, item := range objects {
		if lastStatus, ok := lastStatusMap[item["ProjectName"].(string)]; ok {
			if lastStatus.StopUpdateAt.IsZero() || lastStatus.ProjectStatus != item["ProjectStatus"].(string) {
				if item["ProjectStatus"].(string) == "完成" || item["ProjectStatus"].(string) == "暂停" || item["ProjectStatus"].(string) == "终止" {
					item["StopUpdateAt"] = now
				} else {
					item["StopUpdateAt"] = nil
				}
			} else {
				item["StopUpdateAt"] = lastStatus.StopUpdateAt
			}
		}
	}
	err = db.Transaction(func(tx *gorm.DB) error {
		if len(objects) > 0 {
			err := tx.Model(Model()).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "project_id"}},
				DoUpdates: clause.AssignmentColumns(columns),
			}).Create(&objects).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
	return err
}

func AllProjectListData() ([]*ListResponse, error) {
	items := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Order("project_id desc").Find(&items).Error
	return items, err
}
