package schedule

import (
	"irisAdminApi/application/logging"
	"irisAdminApi/application/middleware"
	"time"
)

func RunAccessLogData() {
	middleware.AccessLogWirte()
	t := time.NewTicker(1 * time.Hour)
	go func() {
		// for {
		// 	select {
		// 	case <-t.C:
		// 		middleware.AccessLogWirte()
		// 	}
		// }
		for range t.C {
			middleware.AccessLogWirte()
		}
	}()
}

func RunAnalyzeLogs() {
	_, err := Cron.AddFunc("0 0 * * *", func() { middleware.AnalyzeLogs() })
	if err != nil {
		logging.ErrorLogger.Error("analyzeLogs cron err ", err)
	}
}
