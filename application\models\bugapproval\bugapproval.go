package bugapproval

import (
	"irisAdminApi/application/models"
	"time"
)

type BugApproval struct {
	models.ModelBase
	BugID        int        `gorm:"not null;index" json:"bug_id"`
	UserID       uint       `gorm:"not null" json:"user_id"`
	ApprovalType string     `gorm:"not null;type:varchar(20)" json:"approval_type"`
	Status       uint       `gorm:"not null" json:"status"` // 0: 审批中 1: 通过  2: 拒绝
	Round        int        `gorm:"not null" json:"round"`
	Comment      string     `json:"comment"`
	PlanFinishAt *time.Time `json:"plan_finish_at"`
}

type BugApprovalCC struct {
	models.ModelBase
	BugApprovalID uint `gorm:"not null" json:"bug_approval_id"`
	UserID        uint `gorm:"not null" json:"user_id"`
}

type BugApprovalAuditor struct {
	models.ModelBase
	BugApprovalID uint   `gorm:"not null" json:"bug_approval_id"`
	UserID        uint   `gorm:"not null" json:"user_id"`
	Comment       string `json:"comment"`
	Status        uint   `gorm:"not null;default:0" json:"status"` // 0: 审批中 1: 通过  2: 拒绝
}
