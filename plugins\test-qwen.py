import torch
from transformers import AutoModelForCausalLM, AutoTokenizer

# 训练后模型路径（包含 safetensors 分片）
trained_model_path = "./qwen_model"

# 加载 tokenizer
tokenizer = AutoTokenizer.from_pretrained(trained_model_path, trust_remote_code=True)

# 加载训练后的模型（依赖原始路径提供 modeling_qwen.py）
model = AutoModelForCausalLM.from_pretrained(
    trained_model_path,
    torch_dtype=torch.bfloat16,
    device_map="auto",
    trust_remote_code=True,
    local_files_only=False  # 允许从原始模型加载缺失的文件
)

# 输入防火墙配置
firewall_config = '''config firewall address
edit "***********-*************"
     set uuid 987da852-89c0-51ee-db79-7f4778c644b7
     set type iprange
     set start-ip ***********
     set end-ip *************
next
edit "***********"
     set uuid 98896f70-89c0-51ee-8e3c-5be630957f68
     set subnet *********** *************
next'''

# 构造输入提示，与训练时一致
prompt = f"Convert Firewall Config:\n{firewall_config}"

# Tokenize 输入
inputs = tokenizer(prompt, return_tensors="pt").to(model.device)

# 生成输出
outputs = model.generate(
    **inputs,
    max_length=512,
    num_return_sequences=1,
    do_sample=False,
    pad_token_id=2  # 使用 # 的 ID 作为填充，与训练时一致
)

# 解码输出
generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)

# 打印结果
print("Generated Output:")
print(generated_text)