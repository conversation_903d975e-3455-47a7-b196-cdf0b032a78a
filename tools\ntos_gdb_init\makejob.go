package main

import (
	"fmt"
	"irisAdminApi/service/dao/buildfarm/dmakejob"
	"strings"

	"github.com/pkg/errors"
)

type makeJobResult struct {
	Code uint        `json:"code"`
	Data makeJobData `json:"data"`
}

type makeJobData struct {
	Items []*dmakejob.MakeJob `json:"items"`
}

func (result *makeJobResult) GetDownloadUrl(output, product, softnum, softversion, branch string) (string, error) {
	var errMsg map[string]interface{}
	var downloadUrl string

	request := client.
		SetOutputDirectory(output).
		R().                      // Use R() to create a request and set with chainable request settings.
		SetSuccessResult(result). // Unmarshal response into struct automatically if status code >= 200 and <= 299.
		SetErrorResult(&errMsg)   // Unmarshal response into struct automatically if status code >= 400.
	// EnableDump()        // Enable dump at request level to help troubleshoot, log content only when an unexpected exception occurs.
	request.SetQueryParam("pageSize", "-1")
	if product != "" {
		//产品型号数据
		var err error
		product, err = GetProductBuildName(product)
		if err != nil {
			return downloadUrl, errors.Wrap(err, "产品型号数据获取失败")
		}
		request.SetQueryParam("product", product)
	}
	if softnum != "" {
		request.SetQueryParam("softnum", softnum)
	}

	if softversion != "" {
		request.SetQueryParam("softversion", softversion)
	}

	// if releaseID != "" {
	// 	request.SetQueryParam("softversion", releaseID)
	// }

	// if branch != "" {
	// 	request.SetQueryParam("branch", branch)
	// }

	resp, err := request.Get(fmt.Sprintf("%s/buildfarm-api/api/v1/farm/make", buildFarmServer))
	if err != nil {
		fmt.Println("查询编译接口报错", err)
		return downloadUrl, errors.Wrap(err, "查询编译接口报错")
	}
	if resp.IsSuccessState() {
		items := result.Data.Items
		for _, item := range items {
			if strings.EqualFold(strings.ToLower(item.Product), strings.ToLower(product)) {
				TaskId := item.TaskID
				downloadUrl = fmt.Sprintf("%s/output/%s/%s/", buildFarmServer, TaskId, item.Cpu.Cpu)
				return downloadUrl, nil
			}
		}
	}
	return downloadUrl, nil
}
