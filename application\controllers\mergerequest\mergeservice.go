package mergerequest

import (
	"errors"
	"fmt"
	"time"

	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/mergerequest/dmergerequescontrolreview"
	"irisAdminApi/service/dao/mergerequest/dmergerequest"

	"github.com/xanzy/go-gitlab"
)

// 合并请求参数
type MergeRequestParams struct {
	ProjectID         int    // 项目ID
	MergeRequestIID   int    // MR IID
	SHA               string // SHA值
	GitlabSession     string // Session信息（用于Session方式）
	CSRFToken         string // CSRF Token（用于Session方式）
	PathWithNamespace string // 项目路径（用于Session方式）
}

//合并结果
type MergeResult struct {
	Success    bool   // 是否成功
	Method     string // 使用的合并方式："api" 或 "session"
	Message    string // 结果消息
	Error      error  // 错误信息
	RetryCount int    // 重试次数
}

//合并请求上下文
type MergeRequestContext struct {
	MergeRequest   *dmergerequest.MergeRequest         // 数据库中的MR记录
	ControlReviews *dmergerequescontrolreview.Response // 管控审核记录
	Comment        string                              // 审核评论
	EnableSession  bool                                // 是否启用Session备用方式
}

//合并配置
type MergeConfig struct {
	MaxRetries    int  // 最大重试次数，默认5
	EnableSession bool // 是否启用Session备用
	ValidateSHA   bool // 是否进行SHA校验，默认true
}

//统一的MR合并方法
func UnifiedMergeRequest(gl *gitlab.Client, params MergeRequestParams, config ...MergeConfig) *MergeResult {
	// 使用默认配置或传入的配置
	cfg := MergeConfig{
		MaxRetries:    5,
		EnableSession: params.GitlabSession != "",
		ValidateSHA:   true,
	}
	if len(config) > 0 {
		cfg = config[0]
	}

	result := &MergeResult{}

	// 第一阶段：API方式重试
	for i := 0; i < cfg.MaxRetries; i++ {
		result.RetryCount = i + 1

		// 检查是否已经合并
		if isMerged := checkMergeStatus(gl, params); isMerged {
			result.Success = true
			result.Method = "api"
			result.Message = "MR已处于合并状态，无需重复合并"
			return result
		}

		// 尝试API合并
		_, _, err := gl.MergeRequests.AcceptMergeRequest(
			params.ProjectID,
			params.MergeRequestIID,
			&gitlab.AcceptMergeRequestOptions{SHA: &params.SHA},
		)

		if err == nil {
			// API调用成功，验证合并状态
			if isMerged := checkMergeStatus(gl, params); isMerged {
				result.Success = true
				result.Method = "api"
				result.Message = fmt.Sprintf("API方式合并成功，重试%d次", i+1)
				return result
			}
		}
		logging.ErrorLogger.Errorf("API方式合并第%d次重试失败: %v", i+1, err)
		if i < cfg.MaxRetries-1 {
			time.Sleep(time.Second * 2)
		}
	}

	// 第二阶段：Session方式（如果启用）
	if cfg.EnableSession && params.GitlabSession != "" {
		result.Method = "session"
		sessionResp, sessionErr := MergeMergeRequestByGitlabSession(
			params.MergeRequestIID,
			params.GitlabSession,
			params.CSRFToken,
			params.PathWithNamespace,
			params.SHA,
		)

		if sessionErr != nil {
			result.Success = false
			result.Error = sessionErr
			result.Message = fmt.Sprintf("Session方式合并失败: %v", sessionErr)
			logging.ErrorLogger.Errorf("Session方式合并失败: %v", sessionErr)
			return result
		}

		// 验证Session合并结果
		if isMerged := checkMergeStatus(gl, params); isMerged {
			result.Success = true
			result.Message = "Session方式合并成功"
			logging.InfoLogger.Infof("Session方式合并成功: %s", sessionResp)
		} else {
			result.Success = false
			result.Message = "Session方式合并失败，状态验证未通过"
		}

		return result
	}

	// 全部失败
	result.Success = false
	result.Method = "api"
	result.Message = fmt.Sprintf("API方式%d次重试全部失败", cfg.MaxRetries)
	result.Error = errors.New("merge failed after all retries")
	return result
}

//检查合并状态
func checkMergeStatus(gl *gitlab.Client, params MergeRequestParams) bool {
	mr, _, err := gl.MergeRequests.GetMergeRequest(
		params.ProjectID,
		params.MergeRequestIID,
		&gitlab.GetMergeRequestsOptions{},
	)
	return err == nil && mr.State == "merged"
}

//验证SHA值
func validateSHA(gl *gitlab.Client, params MergeRequestParams, mr *dmergerequest.MergeRequest) bool {
	gitlabMR, _, err := gl.MergeRequests.GetMergeRequest(
		params.ProjectID,
		params.MergeRequestIID,
		&gitlab.GetMergeRequestsOptions{},
	)
	if err != nil {
		logging.ErrorLogger.Errorf("获取GitLab MR详情失败: %v", err)
		return false
	}
	return gitlabMR.SHA == mr.MergeRequestSHA
}

//重置MR状态
func resetMergeRequestStatus(mr *dmergerequest.MergeRequest) {
	updateData := map[string]interface{}{
		"ButtonClickStatus": 0,
		"ButtonClickUser":   "",
		"MergeRequestSHA":   "",
	}
	mergeRequestUp := dmergerequest.MergeRequest{}
	if err := mergeRequestUp.Update(mr.ID, updateData); err != nil {
		logging.ErrorLogger.Errorf("重置MR状态失败: %v", err)
	}
}

// 更新管控审核状态
// - status: 状态值
//   - 0: 待审核（初始状态）
//   - 1: 审核通过/合并成功
//   - 2: 审核拒绝（人工拒绝）
//   - 3: 系统自动关闭
// - comment: 状态变更的备注说明
func updateControlReviewStatus(controlReviews *dmergerequescontrolreview.Response, status int, comment string) {
	if controlReviews == nil || controlReviews.ID == 0 {
		return
	}

	controlreviewObjects := map[string]interface{}{
		"Status":  status,
		"Comment": comment,
	}
	controlreviewUP := dmergerequescontrolreview.Response{}
	if err := controlreviewUP.Update(controlReviews.ID, controlreviewObjects); err != nil {
		logging.ErrorLogger.Errorf("更新管控审核状态失败: %v", err)
	}
}

// 统一MR合并方法
func UnifiedMergeRequestWithContext(gl *gitlab.Client, params MergeRequestParams, ctx MergeRequestContext, config ...MergeConfig) *MergeResult {
	// 使用默认配置或传入的配置
	cfg := MergeConfig{
		MaxRetries:    5,
		EnableSession: ctx.EnableSession,
		ValidateSHA:   true,
	}
	if len(config) > 0 {
		cfg = config[0]
	}

	// SHA校验逻辑
	if cfg.ValidateSHA && !validateSHA(gl, params, ctx.MergeRequest) {
		// SHA不匹配处理
		resetMergeRequestStatus(ctx.MergeRequest)
		if ctx.ControlReviews != nil && ctx.ControlReviews.ID > 0 {
			updateControlReviewStatus(ctx.ControlReviews, 3, "参数异常：MR表单SHA不一致，系统自动关闭")
		}
		go SendMailToReviewUser(*ctx.MergeRequest, 2)
		return &MergeResult{
			Success: false,
			Message: "SHA值不一致，需重新发起流程",
			Error:   errors.New("SHA mismatch"),
		}
	}

	// 执行合并操作
	result := UnifiedMergeRequest(gl, params, cfg)

	// 后续业务处理
	if result.Success {
		// 合并成功，更新管控审核状态
		if ctx.ControlReviews != nil && ctx.ControlReviews.ID > 0 {
			updateControlReviewStatus(ctx.ControlReviews, 1, ctx.Comment)
		}
	} else {
		// 合并失败，重置按钮状态
		resetMergeRequestStatus(ctx.MergeRequest)
		// 更新管控审核状态为系统自动关闭
		if ctx.ControlReviews != nil && ctx.ControlReviews.ID > 0 {
			failureComment := fmt.Sprintf("合并失败：%s", result.Message)
			if result.Error != nil {
				failureComment = fmt.Sprintf("合并失败：%s (错误：%v)", result.Message, result.Error)
			}
			updateControlReviewStatus(ctx.ControlReviews, 3, failureComment)
			logging.InfoLogger.Infof("管控审核状态已更新为系统自动关闭，MR ID: %d, 原因: %s", ctx.MergeRequest.ID, failureComment)
		}
		// 发送失败通知邮件
		go SendMailToReviewUser(*ctx.MergeRequest, 3) // 参数3表示合并失败
	}

	return result
}
