package productionrelease

import (
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/productionrelease/dproduction"
	"irisAdminApi/service/dao/productionrelease/dproductionseccloud"
	"strconv"

	"github.com/kataras/iris/v12"
)

type DescFile struct {
	Version            string   `json:"version" form:"version"`
	FileName           string   `json:"file_name" form:"file_name"`
	FileSize           uint     `json:"file_size" form:"file_size"`
	FileMd5            string   `json:"file_md5" form:"file_md5"`
	ProductModels      []string `json:"product_models" form:"product_models"`
	SoftVersions       []string `json:"soft_versions" form:"soft_versions"`
	FeatureType        string   `json:"feature_type" form:"feature_type"`
	FeatureVersions    []string `json:"feature_versions" form:"feature_versions"`
	FeatureBaseVersion string   `json:"feature_base_version" form:"feature_base_version"`
	FileType           uint     `json:"file_type" form:"file_type"`
	ReleaseDate        string   `json:"release_date" form:"release_date"`
	VersionDesc        string   `json:"version_desc" form:"version_desc"`
	Sign               string   `json:"sign" form:"sign"`
}

/*
	{"primary":{
		"file_name":"hash_20230215.1322_full_sig.zip",
		"file_size":12711950,
		"file_md5":"ac1cdb71b05aabb48b84aa3d1adfe541",
		"release_date":"2023-02-15",
		"file_desc":"hash_20230215.1322_full_sig.zip",
		"file_type":2
		},
		"extend":{
			"version":"20230215.1322",
			"product_models":["Z5100","Z5100-S","Z3200","Z3200-S"],
			"hard_versions":[],
			"soft_versions":[],
			"feature_versions":[],
			"feature_type":"av-hash",
			"package_type":2,
			"retain_day":7}
		}
*/
type DescFileV2 struct {
	Primary DescFileV2Primary `json:"primary"`
	Extend  DescFileV2Extend  `json:"extend"`
}

type DescFileV2Primary struct {
	FileName    string `json:"file_name" form:"file_name"`
	FileSize    uint   `json:"file_size" form:"file_size"`
	FileMd5     string `json:"file_md5" form:"file_md5"`
	ReleaseDate string `json:"release_date" form:"release_date"`
	FileDesc    string `json:"file_desc" form:"file_desc"`

	FileType uint `json:"file_type" form:"file_type"`
}

type DescFileV2Extend struct {
	Version         string   `json:"version" form:"version"`
	VersionDesc     string   `json:"version_desc" form:"version_desc"`
	ProductModels   []string `json:"product_models" form:"product_models"`
	SoftVersions    []string `json:"soft_versions" form:"soft_versions"`
	HardVersions    []string `json:"hard_versions" form:"hard_versions"`
	FeatureType     string   `json:"feature_type" form:"feature_type"`
	PackageType     uint     `json:"package_type" form:"package_type"`
	FeatureVersions []string `json:"feature_versions" form:"feature_versions"`
	RetainDay       uint     `json:"retain_day" form:"retain_day"`
	Sign            string   `json:"sign" form:"sign"`
}

// func CreateDescFile(ctx iris.Context) {
// 	id, err := dao.GetAuthId(ctx)
// 	if err != nil {
// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
// 		return
// 	}
// 	uuid := libs.GetUUID()
// 	request := &dfeature.Request{}
// 	if err := ctx.ReadForm(request); err != nil {
// 		logging.ErrorLogger.Errorf("create approval read form err ", err)
// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
// 		return
// 	}

// 	f, fh, err := ctx.FormFile("file")
// 	defer f.Close()

// 	if err != nil {
// 		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
// 		return
// 	}

// 	//构造文件名称
// 	fileName := fh.Filename

// 	// 创建最终存放路径以及保存文件
// 	var upload = filepath.Join(libs.Config.ProductionFileStorage.Upload, time.Now().Format("20060102"), request.FileMd5)
// 	err = os.MkdirAll(upload, 0750)
// 	os.Chmod(upload, 0750)

// 	if err != nil {
// 		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
// 		return
// 	}
// 	//创建申请单
// 	tempFn := filepath.Join(upload, fileName)
// 	_, err = ctx.SaveFormFile(fh, tempFn)
// 	if err != nil {
// 		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
// 		return
// 	}

// 	if request.BaseVersion != "" {

// 		f, fh, err := ctx.FormFile("increment")
// 		defer f.Close()

// 		if err != nil {
// 			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
// 			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
// 			return
// 		}

// 		//构造文件名称
// 		fileName := fh.Filename

// 		// 创建最终存放路径以及保存文件
// 		var upload = filepath.Join(libs.Config.ProductionFileStorage.Upload, time.Now().Format("20060102"), request.IncrementMd5)
// 		err = os.MkdirAll(upload, 0750)
// 		os.Chmod(upload, 0750)

// 		if err != nil {
// 			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
// 			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
// 			return
// 		}
// 		//创建申请单
// 		tempFn := filepath.Join(upload, fileName)
// 		_, err = ctx.SaveFormFile(fh, tempFn)
// 		if err != nil {
// 			logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
// 			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
// 			return
// 		}
// 	}

// 	err = dao.Create(&dfeature.Response{}, ctx, map[string]interface{}{
// 		"UserID":             id,
// 		"Version":            request.Version,
// 		"FileName":           request.FileName,
// 		"FileSize":           request.FileSize,
// 		"FileMd5":            request.FileMd5,
// 		"IncrementName":      request.IncrementName,
// 		"IncrementSize":      request.IncrementSize,
// 		"IncrementMd5":       request.IncrementMd5,
// 		"BaseVersion":        request.BaseVersion,
// 		"ProductModels":      request.ProductModels,
// 		"SoftVersions":       request.SoftVersions,
// 		"FeatureType":        request.FeatureType,
// 		"FeatureVersions":    request.FeatureVersions,
// 		"FeatureBaseVersion": request.FeatureBaseVersion,
// 		"FileType":           request.FileType,
// 		"ReleaseDate":        request.ReleaseDate,
// 		"VersionDesc":        request.VersionDesc,
// 		"CreatedAt":          time.Now(),
// 		"UpdatedAt":          time.Now(),
// 		"Sign":               request.Sign,
// 		"Status":             0,
// 		"Uuid":               uuid,
// 	})

// 	if err != nil {
// 		defer os.RemoveAll(upload)
// 		logging.ErrorLogger.Errorf("create approval get err ", err)
// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
// 		return
// 	}

// 	desc := DescFile{
// 		Version:            request.Version,
// 		FileName:           request.FileName,
// 		FileSize:           request.FileSize,
// 		FileMd5:            request.FileMd5,
// 		ProductModels:      strings.Split(request.ProductModels, ","),
// 		SoftVersions:       strings.Split(request.SoftVersions, ","),
// 		FeatureType:        request.FeatureType,
// 		FeatureVersions:    strings.Split(request.FeatureVersions, ","),
// 		FeatureBaseVersion: request.FeatureBaseVersion,
// 		FileType:           request.FileType,
// 		ReleaseDate:        request.ReleaseDate,
// 		VersionDesc:        request.VersionDesc,
// 		Sign:               request.Sign,
// 	}
// 	// desc := DescFileV2{
// 	// 	Primary: DescFileV2Primary{
// 	// 		FileName:    request.FileName,
// 	// 		FileSize:    request.FileSize,
// 	// 		FileMd5:     request.FileMd5,
// 	// 		ReleaseDate: request.ReleaseDate,
// 	// 		FileDesc:    request.VersionDesc,
// 	// 		FileType:    request.FileType,
// 	// 	},
// 	// 	Extend: DescFileV2Extend{
// 	// 		Version:         request.Version,
// 	// 		ProductModels:   strings.Split(request.ProductModels, ","),
// 	// 		SoftVersions:    strings.Split(request.SoftVersions, ","),
// 	// 		HardVersions:    strings.Split(request.HardVersions, ","),
// 	// 		FeatureType:     request.FeatureType,
// 	// 		PackageType:     1,
// 	// 		FeatureVersions: strings.Split(request.FeatureVersions, ","),
// 	// 		RetainDay:       7,
// 	// 		Sign:            request.Sign,
// 	// 	},
// 	// }
// 	desc_file, err := os.OpenFile(filepath.Join(upload, "featureDesc.txt"), os.O_RDWR|os.O_CREATE, 0666)

// 	jsonBytes, err := json.Marshal(desc)
// 	if err != nil {
// 		defer os.RemoveAll(upload)
// 		if err := dfeature.DeleteByUUID(uuid); err != nil {
// 			logging.ErrorLogger.Errorf("delete feature release get err ", err)
// 		}
// 		logging.ErrorLogger.Errorf("create approval get err ", err)
// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
// 		return
// 	}
// 	_, err = desc_file.WriteString(string(jsonBytes))
// 	defer desc_file.Close()
// 	// err = Upload(tempFn, filepath.Join(upload, "featureDesc.txt"))
// 	if err != nil {
// 		logging.ErrorLogger.Errorf("create approval get err ", err)
// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
// 		return
// 	}
// 	// if err := dfeature.UpdateStatus(uuid, map[string]interface{}{
// 	// 	"Status": 1,
// 	// }); err != nil {
// 	// 	logging.ErrorLogger.Errorf("update status get err ", err)
// 	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "发布成功，更新状态失败。"))
// 	// 	return
// 	// }
// 	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
// 	return
// }

func GetFeatureReleases(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dproduction.ListResponse{}, ctx, name, sort, orderBy, page, pageSize)

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetFeatureRelease(ctx iris.Context) {
	info := dproduction.Response{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

func GetSecClouds(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dproductionseccloud.Response{}, ctx, name, sort, orderBy, page, pageSize)

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}
