package mergerequest

import (
	"irisAdminApi/application/controllers/bugapproval"
	"irisAdminApi/application/controllers/bugmirror"
	"irisAdminApi/application/middleware"

	"github.com/kataras/iris/v12"
)

var Party = func(party iris.Party) {
	party.Get("/ignore_all_ai", IgnoreAllAi).Name = "一键忽略所有AI评审意见"
	party.Get("/mergerequests/test", GetMergeRequestDiff).Name = "获取CPS"
	party.Get("/mergerequests/tosync", GetMergeRequestsToSync).Name = "获取CPS"
	party.Get("/mergerequests/{id:uint}/discussion", GetMrCpses).Name = "获取CPS"
	party.Get("/mergerequests/{id:uint}/codequantity", GetMergeRequestCodeQuantity).Name = "获取PushEvent"
	party.Get("/bugapprovals", bugapproval.GetBugApprovals).Name = "获取BUG申请单"
	party.Post("/discussion/cps", CreateDiscussionCps).Name = "创建评审Cps"
	party.Post("/discussion/resolve", GitlabDiscussionResolve).Name = "评审关闭校验"
	party.Delete("/discussion/{discussion_id:string}",
		DeleteDiscussionCps).Name = "删除评审Cps"
	party.Post("/mergerequest/cps",
		GetDiscussionCps).Name = "获取评审Cps"

	party.Get("/projects/running", GetRunningProjects).Name = "获取所有在行项目"
	party.Get("/projects/mirror", GetMirrorProjects).Name = "获取所有在行项目"
	party.Get("/codecheck/{id:uint}/run", GetCodeCheckErrors).Name = "获取代码检查结果"
	party.Get("/codecheck/{id:uint}", GetCodeCheckErrors).Name = "获取代码检查结果"
	party.Get("/opensource/dirs", GetDirs).Name = "获取opensource路径"
	party.Get("/unitpackages", GetUnitPackages).Name = "组件列表"

	party.Post("/merge", MergeHandler).Name = "合并操作"
	party.Get("/merge", MergeHandler).Name = "合并操作"
	party.Post("/cached_widget", CachedWidgetHandler).Name = "合并操作提示语"
	party.Get("/cached_widget", CachedWidgetHandler).Name = "合并操作提示语"
	party.Post("/widget", WidgetHandler).Name = "合并操作按钮"
	party.Get("/widget", WidgetHandler).Name = "合并操作按钮"
	party.Get("/merge_requests", ProxyHandler).Name = "获取MR信息"
	party.Post("/merge_requests", ProxyHandler).Name = "获取MR信息"
	party.Get("/getbuginfo", GetBugInfoData).Name = "获取Bug信息"

	party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) // 登录验证

	party.Get("/mergerequests/workpackagefix", GetWorkPackageFix).Name = "工作包修正"
	party.Post("/mergerequests/workpackagefix", UpdateWorkPackageFix).Name = "工作包修正"
	party.Post("/mergerequests/sync", MergeRequestSyncWorker).Name = "代码同步功能"
	party.Post("/opensource/upload", CreateOpenSourceUpload).Name = "上传开源组件包"
	party.Post("/mergerequests", CreateMergeRequest).Name = "创建MR表单"
	party.Get("/mergerequests", GetMergeRequests).Name = "获取MR表单列表"
	party.Get("/mergerequests/{id:uint}", GetMergeRequest).Name = "获取MR表单详情"
	party.Get("/bugs/mirrors", bugmirror.GetBugs).Name = "获取所有BUG"
	party.Get("/bugs/branches", bugmirror.GetBranchUpdatedAt).Name = "获取分支创建时间"
	party.Get("/bugs", GetBugs).Name = "获取Bug列表"
	party.Get("/bugs/{id:uint}", GetBug).Name = "获取Bug详情"
	party.Post("/bugs/{id:uint}/mirror", CreateBugMirror).Name = "镜像BUG"
	party.Post("/bugs/mirror/ignore", IgnoreBugMirror).Name = "忽略镜像BUG"
	party.Post("/bugs/mirror/ignore/history", GetBugMirrorApprovalHistory).Name = "忽略镜像BUG历史"
	party.Post("/bugs/mirror/ignore/cancel", CancelIgnoreBugMirror).Name = "忽略镜像BUG"
	party.Get("/mergerequests/{id:uint}/weburl", GetMergeRequestWebUrl).Name = "获取MR URL"
	party.Get("/releaseprojects", GetReleaseProjects).Name = "获取在行项目列表"

	party.Get("/releaseprojects/{id:uint}/branches", GetTargetBranches).Name = "获取仓库分支"
	party.Get("/releaseprojects/{id:uint}/workpackages", GetWorkPackages).Name = "工作包列表"
	party.Get("/releaseprojects/{id:uint}/distinctcolumn", GetDistinctColumns).Name = "获取工作包需求"
	party.Post("/releaseprojects/{id:uint}/workpackages", ImportWorkPackage).Name = "导入工作包"
	party.Get("/projects/{id:uint}/personrepositories", GetPersonRepositories).Name = "获取个人仓库"
	party.Get("/projects/{id:uint}/repositories/branches", GetBranches).Name = "获取仓库分支"
	party.Get("/projects/{id:uint}/repositories/members", GetMembers).Name = "获取仓库成功"
	party.Get("/phabricator/differentials", GetPhabricatorUrls).Name = "获取Phabricator Diff表单"
	party.Get("/summary/workpackages", WorkPackageSummary).Name = "获取工作包统计"
	party.Get("/summary/workpackages/columns", WorkPackageSumaryColumns).Name = "获取工作包统计"
	party.Get("/summary/bugs", BugSummary).Name = "获取BUG统计"
	party.Get("/summary/bugs/distinctcolumn", GetBugDistinctColumns).Name = "获取工作包需求"
	party.Get("/summary/bugs/columns", BugSumaryColumns).Name = "获取BUG统计字段"

	party.Get("/summary/bugs/workgroup", BugWorkGroupSummary).Name = "获取BUG统计"
	party.Get("/summary/bugs/workgroup/columns", BugWorkGroupSumaryColumns).Name = "获取BUG统计字段"

	party.Get("/summary/bugs/owner", BugOwnerSummary).Name = "获取BUG统计"
	party.Get("/summary/bugs/owner/columns", BugOwnerSumaryColumns).Name = "获取BUG统计字段"

	party.Get("/summary/bugs/top/owner", BugOwnerDelayTop).Name = "获取BUG超时时长统计"
	party.Get("/summary/bugs/top/group", BugOwnerGroupDelayTop).Name = "获取BUG超时时长统计"

	// party.Get("/bugapprovals", bugapproval.GetBugApprovals).Name = "获取BUG申请单"
	party.Get("/bugapprovals/{id:uint}", bugapproval.GetBugApproval).Name = "获取BUG申请单详情"
	party.Delete("/bugapprovals/{id:uint}", bugapproval.DeleteBugApproval).Name = "删除BUG申请单"
	party.Post("/bugapprovals", bugapproval.CreateBugApproval).Name = "创建BUG申请单"
	party.Post("/bugapprovals/{id:uint}/audit", bugapproval.AuditBugApproval).Name = "审批申请单"
	party.Get("/bugapprovals/{id:uint}/bug", bugapproval.GetBug).Name = "导入BUG"
	party.Post("/uploadImage", UploadImage).Name = "上传图片"
	party.Get("/showImage/{id:uint}", GetImages).Name = "查看图片"
	party.Get("/projects/{id:uint}/dependencys", GetDependencys).Name = "获取依赖MR表单列表"
	party.Get("/cps/export", CpsExport).Name = "Cps导出"
}
