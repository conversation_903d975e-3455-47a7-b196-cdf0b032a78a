package buildfarm

import (
	"time"

	"irisAdminApi/application/models"
)

type CronMakeJob struct {
	models.ModelBase
	StartedAt       time.Time `json:"started_at"`
	FinishedAt      time.Time `json:"finished_at"`
	JobId           string    `gorm:"not null; type:varchar(60)" json:"job_id"`
	ServerId        uint      `gorm:"index;not null" json:"server_id"`
	Dir             string    `gorm:"not null; type:varchar(60)" json:"dir"`
	Project         string    `gorm:"not null; type:varchar(100)" json:"project"`
	Repo            string    `gorm:"not null; type:varchar(100)" json:"repo"`
	Branch          string    `gorm:"not null; type:varchar(100)" json:"branch"`
	TaskType        uint      `gorm:"not null" json:"task_type"` // 编译类型，1：产品编译  2：组件编译
	Product         string    `gorm:"not null; type:varchar(60)" json:"product"`
	Defconfig       string    `gorm:"not null; type:varchar(60)" json:"defconfig"`
	Target          string    `gorm:"not null; type:varchar(60)" json:"target"`
	Status          uint      `gorm:"not null" json:"status"`                       // 作业状态 3：排队，0：运行， 1：成功， 2: 失败 3:手动停止
	Version         string    `gorm:"not null; type:varchar(60)" json:"version"`    // 版本hash值,初始为0
	BuildType       string    `gorm:"not null; type:varchar(60)" json:"build_type"` // 编译类型： debug, performance
	SoftwareNumber  string    `gorm:"type:varchar(60)" json:"software_nubmer"`
	SoftwareVersion string    `gorm:"type:varchar(60)" json:"software_version"`
	SmokeStatus     string    `gorm:"type:varchar(2000)" json:"smoke_status"`
	CronTabID       uint      `json:"cron_tab_id"`
	UseCache        bool      `gorm:"not null; default:true" json:"use_cache"`
}

type CronTab struct {
	models.ModelBase
	ProjectID uint   `gorm:"not null;" json:"project_id"`
	Project   string `gorm:"not null; type:varchar(100)" json:"project"`
	Repo      string `gorm:"not null; type:varchar(100)" json:"repo"`
	Branch    string `gorm:"not null; type:varchar(100)" json:"branch"`
	TaskType  uint   `gorm:"not null" json:"task_type"` // 编译类型，1：产品编译  2：组件编译
	Product   string `gorm:"not null; type:varchar(300)" json:"product"`
	Defconfig string `gorm:"not null; type:varchar(60)" json:"defconfig"`
	Target    string `gorm:"not null; type:varchar(60)" json:"target"`
	Status    uint   `gorm:"not null" json:"status"`                       // 计划任务状态 0：禁用， 1：启用
	BuildType string `gorm:"not null; type:varchar(60)" json:"build_type"` // 编译类型： debug, performance
	Cron      string `gorm:"not null; type:varchar(60)" json:"cron"`
	UserID    uint   `gorm:"not null; default:0" json:"user_id"`
}
