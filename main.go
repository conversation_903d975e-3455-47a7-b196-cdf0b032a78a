package main

import (
	"flag"
	"fmt"
	"irisAdminApi/application"
	"os"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
)

var config = flag.String("config", "", "配置路径")
var version = flag.Bool("version", false, "打印版本号")

var (
	BuildVersion string
	BuildTime    string
	BuildName    string
	CommitID     string
)

func main() {
	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "usage: %s [options] [command]\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Commands:\n")
		fmt.Fprintf(os.Stderr, "\n")
		fmt.Fprintf(os.Stderr, "  -config <path>\n")
		fmt.Fprintf(os.Stderr, "    设置项目配置文件路径，可选\n")
		fmt.Fprintf(os.Stderr, "  -version <true or false> 打印项目版本号，默认为: false\n")
		fmt.Fprintf(os.Stderr, "    打印版本号\n")
		fmt.Fprintf(os.Stderr, "\n")
	}
	flag.Parse()

	if *version {
		// fmt.Println(fmt.Sprintf("版本号：%s\n", Version))
		// Printf( "build name:\t%s\nbuild ver:\t%s\nbuild time:\t%s\nCommitID:%s\n", BuildName, BuildVersion, BuildTime, CommitID )
		fmt.Printf("build name:\t%s\n", BuildName)
		fmt.Printf("build ver:\t%s\n", BuildVersion)
		fmt.Printf("build time:\t%s\n", BuildTime)
		fmt.Printf("Commit ID:\t%s\n", CommitID)
		os.Exit(0)
	}

	fmt.Printf("build name:\t%s\n", BuildName)
	fmt.Printf("build ver:\t%s\n", BuildVersion)
	fmt.Printf("build time:\t%s\n", BuildTime)
	fmt.Printf("Commit ID:\t%s\n", CommitID)

	irisServer := application.NewServer(*config)
	if irisServer == nil {
		panic("http server 初始化失败")
	}

	if libs.IsPortInUse(libs.Config.Port) {
		if !irisServer.Status {
			panic(fmt.Sprintf("端口 %d 已被使用\n", libs.Config.Port))
		}
		irisServer.Stop() // 停止
	}
	fmt.Println(libs.CWD())

	err := irisServer.Start()
	if err != nil {
		panic(fmt.Sprintf("http server 启动失败: %+v", err))
	}

	logging.InfoLogger.Infof("http server %s:%d start", libs.Config.Host, libs.Config.Port)

}
