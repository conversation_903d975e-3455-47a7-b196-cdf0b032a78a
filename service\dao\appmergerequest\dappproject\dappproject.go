package dappproject

import (
	"fmt"
	"strings"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/appmergerequest"
	"irisAdminApi/service/dao/release/dstatus" //项目状态表
	"irisAdminApi/service/dao/user/duser"
)

const ModelName = "项目管理"

type Response struct {
	ID                   uint                    `json:"id"`
	Name                 string                  `json:"name"`
	StatusID             uint                    `json:"status_id"`
	Status               *dstatus.Response       `gorm:"-" json:"status,omitempty"`
	BugSync              bool                    `json:"bug_sync"`
	PiVersion            string                  `json:"pi_version"`
	BaseGitBranch        string                  `json:"base_git_branch"`
	BaseGitCommit        string                  `json:"base_git_commit"`
	BaseReleaseProjectID uint                    `json:"base_release_project_id"`
	CapoID               uint                    `json:"capo_id"`
	Capo                 *duser.ApprovalResponse `gorm:"-" json:"capo,omitempty"`
	PmoID                uint                    `json:"pmo_id"`
	Pmo                  *duser.ApprovalResponse `gorm:"-" json:"pmo,omitempty"`
	PmID                 uint                    `json:"pm_id"`
	Pm                   *duser.ApprovalResponse `gorm:"-" json:"pm,omitempty"`
	PtmID                uint                    `json:"ptm_id"`
	Ptm                  *duser.ApprovalResponse `gorm:"-" json:"ptm,omitempty"`
	CmaID                uint                    `json:"cma_id"`
	Cma                  *duser.ApprovalResponse `gorm:"-" json:"cma,omitempty"`
	PqaID                uint                    `json:"pqa_id"`
	Pqa                  *duser.ApprovalResponse `gorm:"-" json:"pqa,omitempty"`
	PgttlID              uint                    `gorm:"not null" json:"pgttl_id"`
	Pgttl                *duser.ApprovalResponse `gorm:"-" json:"pgttl,omitempty"`
	PiID                 uint                    `json:"pi_id"`
	Pi                   *duser.ApprovalResponse `gorm:"-" json:"pi,omitempty"`
	StartedAt            string                  `json:"started_at"`
	Comment              string                  `json:"comment"`
	UserID               uint                    `json:"user_id"`
	User                 *duser.ApprovalResponse `gorm:"-" json:"user,omitempty"`
	BaseReleaseProject   *SimpleResponse         `gorm:"-" json:"base_relase_project,omitempty"`
}

type SimpleResponse struct {
	ID       uint              `json:"id"`
	Name     string            `json:"name"`
	StatusID uint              `json:"status_id"`
	Status   *dstatus.Response `gorm:"-" json:"status,omitempty"`
}

type ListResponse struct {
	Response
}

type Request struct {
	Name                 string `json:"name" form:"name"`
	StatusID             uint   `json:"status_id" form:"status_id"`
	BugSync              bool   `json:"bug_sync" form:"bug_sync"`
	PiVersion            string `json:"pi_version" form:"pi_version"`
	BaseGitBranch        string `json:"base_git_branch" form:"base_git_branch"`
	BaseGitCommit        string `json:"base_git_commit" form:"base_git_commit"`
	BaseReleaseProjectID uint   `json:"base_release_project_id" form:"base_release_project_id"`
	CapoID               uint   `json:"capo_id" form:"capo_id"`
	PmoID                uint   `json:"pmo_id" form:"pmo_id"`
	PmID                 uint   `json:"pm_id" form:"pm_id"`
	PtmID                uint   `json:"ptm_id" form:"ptm_id"`
	CmaID                uint   `json:"cma_id" form:"cma_id"`
	PqaID                uint   `json:"pqa_id" form:"pqa_id"`
	PgttlID              uint   `json:"pgttl_id" form:"pgttl_id"`
	PiID                 uint   `json:"pi_id" form:"pi_id"`
	StartedAt            string `json:"started_at" form:"started_at"`
	Comment              string `json:"comment" form:"comment"`
}

type Status struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
}

type AppProject struct {
	Name                 string  `json:"name"`
	BaseReleaseProjectID uint    `json:"base_project_id"`
	StatusID             uint    `json:"status_id"`
	Status               *Status `json:"status"`
	CapoID               uint    `json:"capo_id"`
	Capo                 *User   `gorm:"->;foreignKey:CapoID;references:ID" json:"capo"`
	PmoID                uint    `json:"pmo_id"`
	Pmo                  *User   `gorm:"->;foreignKey:PmoID;references:ID" json:"pmo"`
	PmID                 uint    `json:"pm_id"`
	Pm                   *User   `gorm:"->;foreignKey:PmID;references:ID" json:"pm"`
	PtmID                uint    `json:"ptm_id"`
	Ptm                  *User   `gorm:"->;foreignKey:PtmID;references:ID" json:"ptm"`
	CmaID                uint    `json:"cma_id"`
	Cma                  *User   `gorm:"->;foreignKey:CmaID;references:ID" json:"cma"`
	PqaID                uint    `json:"pqa_id"`
	Pqa                  *User   `gorm:"->;foreignKey:PqaID;references:ID" json:"pqa"`
	PgttlID              uint    `gorm:"not null" json:"pgttl_id"`
	Pgttl                *User   `gorm:"->;foreignKey:PgttlID;references:ID" json:"pgttl"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *appmergerequest.AppProject {
	return &appmergerequest.AppProject{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	getStatuses(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	GetDetail(u)
	return nil
}

func (u *SimpleResponse) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	getStatus(u)
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func getStatus(project *SimpleResponse) {
	status := dstatus.Response{}
	if err := status.Find(project.StatusID); err != nil {
		logging.ErrorLogger.Errorf("get status get err ", err)
	}
	project.Status = &status
}

func GetDetail(project *Response) {
	status := dstatus.Response{}
	if err := status.Find(project.StatusID); err != nil {
		logging.ErrorLogger.Errorf("get status get err ", err)
	}
	project.Status = &status

	var userIds = []uint{project.UserID, project.CapoID, project.PmID, project.PmoID, project.CmaID, project.PiID, project.PqaID, project.PtmID, project.PgttlID}

	users, _ := duser.FindSimpleInIds(userIds)
	var userMap = make(map[uint]*duser.ApprovalResponse)
	for _, user := range users {
		userMap[user.Id] = user
	}

	project.Capo = userMap[project.CapoID]
	project.Pmo = userMap[project.PmoID]
	project.Pm = userMap[project.PmID]
	project.Cma = userMap[project.CmaID]
	project.Pqa = userMap[project.PqaID]
	project.Ptm = userMap[project.PtmID]
	project.Pi = userMap[project.PiID]
	project.Pgttl = userMap[project.PgttlID]
	project.User = userMap[project.UserID]

	baseReleaseProject := SimpleResponse{}
	if err := baseReleaseProject.Find(project.BaseReleaseProjectID); err != nil {
		logging.ErrorLogger.Errorf("get type get err ", err)
	}
	project.BaseReleaseProject = &baseReleaseProject
}

func getStatuses(items []*ListResponse) {
	statuses, _ := dstatus.FindAll()
	var statusMap = make(map[uint]*dstatus.Response)
	for _, status := range statuses {
		statusMap[status.ID] = status
	}
	for _, item := range items {
		item.Status = statusMap[item.StatusID]
	}
}

func getStatusesV2(items []*Response) {
	statuses, _ := dstatus.FindAll()
	var statusMap = make(map[uint]*dstatus.Response)
	for _, status := range statuses {
		statusMap[status.ID] = status
	}
	for _, item := range items {
		item.Status = statusMap[item.StatusID]
	}
}

func FindInIds(ids []uint) ([]*Response, error) {
	var projects []*Response
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id in ?", ids).Find(&projects).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return nil, err
	}
	getStatusesV2(projects)
	return projects, nil
}

func FindAll() ([]*Response, error) {
	var items []*Response

	if err := easygorm.GetEasyGormDb().Model(Model()).Find(&items).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return items, err
	}
	getStatusesV2(items)
	return items, nil
}

func FindAuditProject(userId uint, name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {

	var items []*ListResponse
	status := dstatus.Response{}
	err := status.FindEx("name", "申请中")
	if err != nil {
		return nil, err
	}
	var count int64

	db := easygorm.GetEasyGormDb().Model(Model())
	db = db.Where("status_id = ? and cma_id = ?", status.ID, userId)
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err = db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	getStatuses(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func FindAuditedProject(userId uint, name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {

	var items []*ListResponse
	status := dstatus.Response{}
	err := status.FindEx("name", "申请中")
	if err != nil {
		return nil, err
	}
	var count int64

	db := easygorm.GetEasyGormDb().Model(Model())
	db = db.Where("status_id != ? and cma_id = ?", status.ID, userId)
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err = db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	getStatuses(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func FindMyProject(userId uint, name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var items []*ListResponse
	var count int64

	db := easygorm.GetEasyGormDb().Model(Model())
	if userId != 1 {
		db = db.Where("user_id = ? or pm_id = ?", userId, userId)
		if len(name) > 0 {
			db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
		}
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	getStatuses(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func FindReleaseProject() ([]*ListResponse, error) {
	var items []*ListResponse
	var releaseStatus dstatus.Response

	err := releaseStatus.FindEx("name", "完成(正式发布)")
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return items, err
	}

	db := easygorm.GetEasyGormDb().Model(Model())
	err = db.Where("status_id = ?", releaseStatus.ID).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return items, err
	}
	getStatuses(items)
	return items, nil
}

type NodeResponse struct {
	ProjectName string    `json:"project_name"`
	BaseProject string    `json:"base_project"`
	Status      string    `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type Node struct {
	*NodeResponse
	Children []*Node `json:"children"`
}

func FindRunningProject() ([]*NodeResponse, error) {
	items := []*NodeResponse{}
	// subQuery := easygorm.GetEasyGormDb().Model(dstatus.Model()).Where(`name in ("已立项", "进行中", "试点发布")`).Select("name")
	err := easygorm.GetEasyGormDb().Model(Model()).Select(`release_projects.name as project_name, c.name as base_project, release_statuses.name as status, release_projects.created_at, release_projects.updated_at`).Joins(`join release_statuses on release_statuses.id = release_projects.status_id and release_statuses.name in ("已立项", "进行中", "试点发布")`).Joins(`join release_projects c on c.id = release_projects.base_release_project_id`).Find(&items).Error
	return items, err

}

func getTopElements(o []*NodeResponse, fn func([]*NodeResponse, *NodeResponse) bool) (t []*NodeResponse, anyLuck bool) {
	for _, elem1 := range o {
		isFound := false
		for _, elem2 := range o {
			if elem1.BaseProject == elem2.ProjectName {
				isFound = true
				break
			}
		}
		if !isFound {
			anyLuck = true
			if !fn(t, elem1) {
				t = append(t, elem1)
			}
		}
	}
	return
}

func seek(root *Node, origin []*NodeResponse) {
	searchingNode := root.Children
	for {
		endFlag := true
		tmpNode := []*Node{}

		for _, item := range searchingNode {
			for _, o := range origin {
				if item.ProjectName == o.BaseProject {
					endFlag = false
					item.Children = append(item.Children, &Node{NodeResponse: &NodeResponse{ProjectName: o.ProjectName, BaseProject: o.ProjectName, CreatedAt: o.CreatedAt}})
				}
			}
			for _, child := range item.Children {
				tmpNode = append(tmpNode, child)
			}
		}
		if endFlag {
			break
		} else {
			searchingNode = tmpNode
		}
	}
}

func GenPrintTree(n *Node, s []string) []string {
	for _, item := range n.Children {
		s = GenPrintTree(item, s)
	}
	s = append(s, n.ProjectName)
	return s
}

func FindNodes(n *Node, s []*Node) []*Node {
	for _, item := range n.Children {
		s = FindNodes(item, s)
	}
	s = append(s, n)
	return s
}

func PrintTree(n *Node, s []string) {
	sc := GenPrintTree(n, s)
	for idx := range sc {
		fmt.Printf("%v\n", sc[len(sc)-1-idx])
	}
}

func genPrintTreeV1(n *Node, indent int, s []string) []string {
	for _, item := range n.Children {
		s = genPrintTreeV1(item, indent+1, s)
	}
	s = append(s, strings.Repeat("  ", indent-1)+"  ."+n.ProjectName)
	return s
}

func PrintTreeV1(n *Node, indent int, s []string) {
	sc := genPrintTreeV1(n, indent, s)
	for idx := range sc {
		fmt.Printf("%v\n", sc[len(sc)-1-idx])
	}
}

func FindNodeByName(n *Node, name string) *Node {
	for idx := range n.Children {
		if n.Children[idx].ProjectName == name {
			return n.Children[idx]
		}
	}
	for idx := range n.Children {
		if result := FindNodeByName(n.Children[idx], name); result != nil {
			return result
		}
	}
	return nil
}

func GenerateTree(projects []*NodeResponse) *Node {
	root := &Node{NodeResponse: &NodeResponse{ProjectName: "root", BaseProject: "", CreatedAt: time.Now()}}
	if topElements, anyLuck := getTopElements(projects,
		func(t []*NodeResponse, e *NodeResponse) (isExist bool) {
			for _, item := range t {
				if item == e {
					isExist = true
					break
				}
			}
			return
		}); anyLuck {
		his := map[string]bool{}
		for _, item := range topElements {
			if _, ok := his[item.BaseProject]; !ok {
				his[item.BaseProject] = true
				root.Children = append(root.Children, &Node{NodeResponse: &NodeResponse{ProjectName: item.BaseProject, BaseProject: "", CreatedAt: time.Now()}})
			}
		}
		seek(root, projects)
	}
	return root

}

/*
	{
		"table": "release_statuses",
		"rows":
		[
			[
				1,
				null,
				null,
				null,
				"申请中"
			],
			[
				2,
				null,
				null,
				null,
				"已预订"
			],
			[
				3,
				null,
				null,
				null,
				"完成(正式发布)"
			],
			[
				4,
				null,
				null,
				null,
				"取消"
			],
			[
				5,
				null,
				null,
				null,
				"暂停"
			],
			[
				6,
				null,
				null,
				null,
				"已立项"
			],
			[
				7,
				null,
				null,
				null,
				"转产品化"
			],
			[
				8,
				null,
				null,
				null,
				"CC"
			],
			[
				9,
				null,
				null,
				null,
				"EC"
			],
			[
				10,
				null,
				null,
				null,
				"转测试"
			],
			[
				11,
				null,
				null,
				null,
				"试点发布"
			],
			[
				12,
				null,
				null,
				null,
				"停止维护"
			],
			[
				13,
				null,
				null,
				null,
				"立项前"
			]
		]
	}
*/
func GetRunningProjects() ([]*Response, error) {
	var projects []*Response
	err := easygorm.GetEasyGormDb().Model(Model()).Where("status_id in ?", []uint{1, 2, 3, 6, 11, 13}).Where("type=1").Order("name desc").Find(&projects).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return nil, err
	}
	getStatusesV2(projects)
	return projects, nil

}

func GetBugMirrorProjects() ([]*Response, error) {
	var projects []*Response
	err := easygorm.GetEasyGormDb().Table("release_projects").Where("id in (?)", easygorm.GetEasyGormDb().Table("release_project_configs").Where("bug_mirror = 1").Select("release_project_id")).Order("name asc").Find(&projects).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return nil, err
	}
	getStatusesV2(projects)
	return projects, nil

}

type User struct {
	ID       uint   `json:"id"`
	Name     string `json:"name"`
	Username string `json:"username"`
}

func GetProjectsByName(names ...string) ([]*AppProject, error) {
	var projects []*AppProject
	/*
		Capo                 *User           `gorm:"->" json:"capo"`
		Pmo                  *User           `gorm:"->" json:"pmo"`
		Pm                   *User           `gorm:"->" json:"pm"`
		Ptm                  *User           `gorm:"->" json:"ptm"`
		Cma                  *User           `gorm:"->" json:"cma"`
		Pqa                  *User           `gorm:"->" json:"pqa"`
		Pgttl                *User           `gorm:"->" json:"pgttl"`
	*/
	err := easygorm.GetEasyGormDb().Model(&AppProject{}).Preload("Pm").Preload("Pmo").Preload("Capo").Preload("Ptm").Preload("Cma").Preload("Pqa").Preload("Pgttl").Where("name in ?", names).Order("name desc").Find(&projects).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return nil, err
	}
	return projects, nil
}
