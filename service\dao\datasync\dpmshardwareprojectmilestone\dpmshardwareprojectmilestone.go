package dpmshardwareprojectmilestone

import (
	"encoding/json"
	"fmt"
	"reflect"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/datasync"
	"irisAdminApi/service/dao/datasync/dsyncrecord"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "PMS-硬件项目里程碑表"

type PmsHardwareProjectMilestoneSyncResponse struct {
	State   string                                 `json:"state"`
	Data    []*PmsHardwareProjectMilestoneResponse `json:"data"`
	Total   int                                    `json:"total"`
	Message string                                 `json:"message"`
}

type PmsHardwareProjectMilestoneResponse struct {
	RowNum         int    `json:"rownum"`
	ID             int    `json:"id" `
	ProjectID      int    `json:"projectID"`
	ProjectName    string `json:"projectName"`
	MilestoneName  string `json:"milestoneName"`
	MilestoneValue string `json:"mileStoneValue"`

	Status        string `json:"status"`
	PlanDate      string `json:"planDate"`
	PlanStartDate string `json:"planStartDate"`
	ActStartDate  string `json:"actStartDate"`
	ActDate       string `json:"actDate"`
	Disabled      bool   `json:"disabled" `
}

type PmsHardwareProjectMilestone struct {
	datasync.PmsHardwareProjectMilestone
}

type ListResponse struct {
	PmsHardwareProjectMilestone
}

type Request struct {
	Id uint `json:"id"`
}

func (this *PmsHardwareProjectMilestone) ModelName() string {
	return ModelName
}

func Model() *datasync.PmsHardwareProjectMilestone {
	return &datasync.PmsHardwareProjectMilestone{}
}

func (this *PmsHardwareProjectMilestone) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *PmsHardwareProjectMilestone) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *PmsHardwareProjectMilestone) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *PmsHardwareProjectMilestone) CreateV2(object interface{}) error {
	return nil
}

func (this *PmsHardwareProjectMilestone) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *PmsHardwareProjectMilestone) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *PmsHardwareProjectMilestone) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *PmsHardwareProjectMilestone) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *PmsHardwareProjectMilestone) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *PmsHardwareProjectMilestone) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func UpdateOrCreatePmsHardwareProjectMilestoneTransaction(items []*PmsHardwareProjectMilestoneResponse, _url string, data map[string]string, method, state, errorMsg string) error {
	objects := []map[string]interface{}{}
	for _, item := range items {

		object := map[string]interface{}{
			"ID":             item.ID,
			"ProjectID":      item.ProjectID,
			"ProjectName":    item.ProjectName,
			"MilestoneName":  item.MilestoneName,
			"MilestoneValue": item.MilestoneValue,

			"Status":   item.Status,
			"Disabled": item.Disabled,
		}

		if len(item.ActStartDate) > 0 {
			_time, err := time.Parse("2006-01-02 15:04:05", item.ActStartDate)
			if err == nil {
				object["ActStartDate"] = _time
			}

		}
		if len(item.ActDate) > 0 {
			_time, err := time.Parse("2006-01-02 15:04:05", item.ActDate)
			if err == nil {
				object["ActDate"] = _time
			}
		}
		if len(item.PlanDate) > 0 {
			_time, err := time.Parse("2006-01-02 15:04:05", item.PlanDate)
			if err == nil {
				object["PlanDate"] = _time
			}
		}
		if len(item.ActStartDate) > 0 {
			_time, err := time.Parse("2006-01-02 15:04:05", item.ActStartDate)
			if err == nil {
				object["PlanStartDate"] = _time
			}
		}

		objects = append(objects, object)
	}

	columns := []string{}

	xt := reflect.TypeOf(Model())
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")
		if ok && ok2 {
			columns = append(columns, name)
		}
	}

	body, err := json.Marshal(data)
	if err != nil {
		return err
	}
	db := easygorm.GetEasyGormDb()
	err = db.Transaction(func(tx *gorm.DB) error {
		if len(objects) > 0 {
			err := tx.Model(Model()).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "id"}},
				DoUpdates: clause.AssignmentColumns(columns),
			}).Create(&objects).Error
			if err != nil {
				return err
			}
		}

		if err := tx.Model(dsyncrecord.Model()).Create(map[string]interface{}{
			"url":             _url,
			"body":            body,
			"method":          method,
			"state":           state,
			"message":         errorMsg,
			"min_modify_date": data["minModifyDate"],
			"max_modify_date": data["maxModifyDate"],
			"created_at":      time.Now(),
		}).Error; err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func GetHardwareProjectMilestoneData(page int, pageSize int) ([]*ListResponse, error) {
	items := []*ListResponse{}
	offset := (page - 1) * pageSize
	err := easygorm.GetEasyGormDb().Model(Model()).
		Offset(offset).
		Limit(pageSize).
		Find(&items).Error

	return items, err
}

func All() ([]*ListResponse, error) {
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	err := db.Order("project_id asc").Find(&items).Error
	return items, err
}
