package dmergerequestbugworkgroupsummary

import (
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/mergerequest"
	"irisAdminApi/application/vars"
	"irisAdminApi/service/dao/release/dreleaseprojectconfig"
	"reflect"
	"sort"
	"strconv"
)

const ModelName = "BUG统计"

/*
SELECT
bug_owner_group,
COUNT(*) AS total,
Round(COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVE<PERSON>"), bug_id, NULL))/COUNT(*) *100, 2) AS not_cbd_percent,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP"), bug_id, NULL)) AS not_cbd,
COUNT(IF(bug_workpacket_name ='', bug_id, NULL)) AS unrelate_work_packet,
COUNT(IF(bug_state IN ('CHECKED', 'RESOLVED'), bug_id, NULL)) AS CHECKED_RESOLVED,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_submitter_group LIKE '%测试%' AND bug_priority = 'blocking' AND bug_repro=1 AND bug_created_at < getWorkDay(bug_created_at, -3), bug_id, NULL)) AS  over_time_blocking_by_test,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_submitter_group LIKE '%测试%' AND bug_priority = 'critical' AND bug_repro=1 AND bug_created_at < getWorkDay(bug_created_at, -3), bug_id, NULL)) AS  over_time_critical_by_test,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_submitter_group LIKE '%测试%' AND bug_priority = 'major' AND bug_repro=1 AND bug_created_at <getWorkDay(bug_created_at, -5), bug_id, NULL)) AS  over_time_major_by_test,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_submitter_group LIKE '%测试%' AND bug_priority = 'normal' AND bug_repro=1 AND bug_created_at <  DATE_ADD(NOW(), INTERVAL -7 DAY), bug_id, NULL)) AS  over_time_normal_by_test,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_submitter_group NOT LIKE '%测试%' AND bug_repro=1 AND bug_created_at < DATE_ADD(NOW(), INTERVAL -7 DAY), bug_id, NULL)) AS  over_time_by_dev,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_priority = 'blocking', bug_id, NULL)) AS  blocking_not_cbd,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_priority = 'critical', bug_id, NULL)) AS  critical_not_cbd,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_priority = 'major', bug_id, NULL)) AS  major_not_cbd,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_priority = 'normal', bug_id, NULL)) AS  normal_not_cbd,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_priority = 'trivial', bug_id, NULL)) AS  trivial_not_cbd,
COUNT(IF(bug_submitter_group LIKE '%测试%', bug_id, NULL)) AS  total_by_test,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_submitter_group LIKE '%测试%', bug_id, NULL)) AS  not_cbd_by_test,
ROUND(COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_submitter_group LIKE '%测试%', bug_id, NULL))/COUNT(IF(bug_submitter_group LIKE '%测试%', bug_id, NULL))*100,2) AS not_cbd_by_test_percent,
COUNT(IF(bug_submitter_group not LIKE '%测试%', bug_id, NULL)) AS  total_by_dev,
COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_submitter_group not LIKE '%测试%', bug_id, NULL)) AS  not_cbd_by_dev,
Round(COUNT(IF(bug_state not LIKE 'CLOSED%' AND bug_state not LIKE 'DENIAL%' AND bug_state NOT IN ("DELAY", "GIVEUP") and bug_submitter_group not LIKE '%测试%', bug_id, NULL))/COUNT(IF(bug_submitter_group not LIKE '%测试%', bug_id, NULL))*100, 2) AS not_cbd_by_dev_percent
FROM bugs
WHERE bug_os = 'NTOS1.0R6'
GROUP BY bug_owner_group
*/

type BugGroupSummary struct {
	BugOs         string `json:"bug_os" xlsx:"项目" idx:"1"`
	BugOwnerGroup string `json:"bug_owner_group" xlsx:"所属工作组" idx:"2"`
	// BugWorkPacketName  string  `json:"bug_workpacket_name" xlsx:"工作包" idx:"3"`
	Total                  uint    `json:"total" xlsx:"合计提交" idx:"3"`
	NotCbdPercent          float32 `json:"not_cbd_percent" xlsx:"所有BUG未决率" idx:"2"`
	NotCbdCount            uint    `json:"not_cbd_count" xlsx:"未CBD" idx:"3"`
	NoWorkpacket           uint    `json:"no_workpacket" xlsx:"未关联工作包" idx:"4"`
	CheckedResolved        uint    `json:"checked_resolved" xlsx:"CHECKED+RESOLVED" idx:"5"`
	OvertimeBlockingByTest uint    `json:"overtime_blocking_by_test" xlsx:"测试提Blocking超期未决" idx:"6"`
	OvertimeCriticalByTest uint    `json:"overtime_critical_by_test" xlsx:"测试提Critical超期未决" idx:"7"`
	OvertimeMajorByTest    uint    `json:"overtime_major_by_test" xlsx:"测试提Major超期未决" idx:"8"`
	OvertimeNormalByTest   uint    `json:"overtime_normal_by_test" xlsx:"测试提Normal超期未决" idx:"9"`
	OvertimeByDev          uint    `json:"overtime_by_dev" xlsx:"开发提超期未决" idx:"10"`
	BlockingNotCbd         uint    `json:"blocking_not_cbd" xlsx:"Blocking未决" idx:"11"`
	CriticalNotCbd         uint    `json:"critical_not_cbd" xlsx:"Ctritical未决" idx:"12"`
	MajorNotCbd            uint    `json:"major_not_cbd" xlsx:"Major未决" idx:"13"`
	NormalNotCbd           uint    `json:"normal_not_cbd" xlsx:"Normal未决" idx:"14"`
	TrivialNotCbd          uint    `json:"trivial_not_cbd" xlsx:"Trivial未决" idx:"15"`
	TotalByTest            uint    `json:"total_by_test" xlsx:"测试提交bug数" idx:"16"`
	NotCbdCountByTest      uint    `json:"not_cbd_count_by_test" xlsx:"测试提交未CBD" idx:"17"`
	NotCbdPercentByTest    float32 `json:"not_cbd_percent_by_test" xlsx:"测试提交未决率" idx:"18"`
	TotalByDev             uint    `json:"total_by_dev" xlsx:"开发提交bug数" idx:"19"`
	NotCbdCountByDev       uint    `json:"not_cbd_count_by_dev" xlsx:"开发提交未CBD" idx:"20"`
	NotCbdPercentByDev     float32 `json:"not_cbd_percent_by_dev" xlsx:"开发提交未决率" idx:"21"`
	// 首轮BUG指标
	BugCreatedInFirstRound   uint    `json:"bug_created_in_first_round" xlsx:"首轮提交BUG数" idx:"31"`
	BugNotClosedInFirstRound uint    `json:"bug_not_closed_in_first_round" xlsx:"首轮未关闭BUG数" idx:"31"`
	CbdPercentInFirstRound   float32 `json:"cbd_percent_in_first_round" xlsx:"首轮Bug解决率" idx:"22"`

	BugCreatedByTestInFirstRound     uint    `json:"bug_created_by_test_in_first_round" xlsx:"首轮测试提交BUG数" idx:"31"`
	BugFromTestNotClosedInFirstRound uint    `json:"bug_from_test_not_closed_in_first_round" xlsx:"首轮测试提交未关闭BUG数" idx:"31"`
	CbdFromTestPercentInFirstRound   float32 `json:"cbd_from_test_percent_in_first_round" xlsx:"首轮测试提Bug解决率" idx:"23"`

	MajorCbdPercentInFirstRound float32 `json:"major_cbd_percent_in_first_round" xlsx:"首轮Major以上Bug解决率" idx:"24"`
	MajorNotCbdInFirstRound     uint    `json:"major_not_cbd_in_first_round" xlsx:"首轮Major以上未决BUG数" idx:"25"`
	MajorInFirstRound           uint    `json:"major_in_first_round" xlsx:"首轮Major以上BUG数" idx:"26"`

	// 次轮BUG指标
	BugCreatedInSecondRound   uint    `json:"bug_created_in_second_round" xlsx:"次轮提交BUG数" idx:"31"`
	BugNotClosedInSecondRound uint    `json:"bug_not_closed_in_second_round" xlsx:"次轮未关闭BUG数" idx:"31"`
	CbdPercentInSecondRound   float32 `json:"cbd_percent_in_second_round" xlsx:"次轮Bug解决率" idx:"27"`

	BugCreatedByTestInSecondRound     uint    `json:"bug_created_by_test_in_second_round" xlsx:"次轮测试提交BUG数" idx:"31"`
	BugFromTestNotClosedInSecondRound uint    `json:"bug_from_test_not_closed_in_second_round" xlsx:"次轮测试提交未关闭BUG数" idx:"31"`
	CbdFromTestPercentInSecondRound   float32 `json:"cbd_from_test_percent_in_second_round" xlsx:"次轮测试提Bug解决率" idx:"28"`

	MajorCbdPercentInSecondRound float32 `json:"major_cbd_percent_in_second_round" xlsx:"次轮Major以上Bug解决率" idx:"29"`
	MajorNotCbdInSecondRound     uint    `json:"major_not_cbd_in_second_round" xlsx:"次轮Major以上未决BUG数" idx:"30"`
	MajorInSecondRound           uint    `json:"major_in_second_round" xlsx:"次轮Major以上BUG数" idx:"31"`

	// 试点指标

	BugCreatedBeforeExperimentRelease   uint    `json:"bug_created_before_experiment_release" xlsx:"试点发布前提交BUG数" idx:"31"`
	BugNotClosedBeforeExperimentRelease uint    `json:"bug_not_closed_before_experiment_release" xlsx:"试点发布前未关闭BUG数" idx:"31"`
	CbdPercentBeforeExperimentRelease   float32 `json:"cbd_percent_before_experiment_release" xlsx:"试点发布前Bug解决率" idx:"27"`

	BugCreatedByTestBeforeExperimentRelease     uint    `json:"bug_created_by_test_before_experiment_release" xlsx:"试点发布前测试提交BUG数" idx:"31"`
	BugFromTestNotClosedBeforeExperimentRelease uint    `json:"bug_from_test_not_closed_before_experiment_release" xlsx:"试点发布前测试提交未关闭BUG数" idx:"31"`
	CbdFromTestPercentBeforeExperimentRelease   float32 `json:"cbd_from_test_percent_before_experiment_release" xlsx:"试点发布前测试提Bug解决率" idx:"28"`

	MajorCbdPercentBeforeExperimentRelease float32 `json:"major_cbd_percent_before_experiment_release" xlsx:"试点发布前Major以上Bug解决率" idx:"29"`
	MajorNotCbdBeforeExperimentRelease     uint    `json:"major_not_cbd_before_experiment_release" xlsx:"试点发布前Major以上未决BUG数" idx:"30"`
	MajorBeforeExperimentRelease           uint    `json:"major_before_experiment_release" xlsx:"试点发布前Major以上BUG数" idx:"31"`

	NeedBugReportCheck uint `json:"need_bug_report_check" xlsx:"需要BUG通告检查" idx:"31"`
}

func (a *BugGroupSummary) ModelName() string {
	return ModelName
}

func Model() *mergerequest.MergeRequest {
	return &mergerequest.MergeRequest{}
}

func (s *BugGroupSummary) All(bugOS, bugOwnerGroup string, pstlID uint, order, by, group string) ([]*BugGroupSummary, error) {
	items := []*BugGroupSummary{}

	selects := []string{
		"b.bug_os",
		"b.bug_owner_group",
		"COUNT(*) AS total",
		`Round(COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP"), b.bug_id, NULL))/COUNT(*) *100, 2) AS not_cbd_percent`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP"), b.bug_id, NULL)) AS not_cbd_count`,
		`COUNT(IF(b.bug_workpacket_name ='', b.bug_id, NULL)) AS no_workpacket`,
		`COUNT(IF(b.bug_state IN ('CHECKED', 'RESOLVED'), b.bug_id, NULL)) AS checked_resolved`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' and b.main_bug_id = 0 AND b.bug_severity = 'Blocking' AND b.bug_repro=1 AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < DATE_ADD(NOW(), INTERVAL -1*(a.round+1) DAY)) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < DATE_ADD(NOW(), INTERVAL -1 DAY))), b.bug_id, NULL)) AS  overtime_blocking_by_test`,

		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' and b.main_bug_id = 0 AND b.bug_priority = 'critical' AND b.bug_repro=1  AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -3*(a.round+1))) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -3))), b.bug_id, NULL)) AS  overtime_critical_by_test`,

		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' and b.main_bug_id = 0 AND b.bug_priority = 'major' AND b.bug_repro=1  AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -5*(a.round+1))) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -5))), b.bug_id, NULL)) AS  overtime_major_by_test`,

		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' and b.main_bug_id = 0 AND b.bug_priority = 'normal' AND b.bug_repro=1  AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) <  getWorkDay(now(), -7*(a.round+1))) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -7))), b.bug_id, NULL)) AS  overtime_normal_by_test`,

		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and (b.bug_submitter_group NOT LIKE '%测试%' or b.main_bug_id != 0) AND b.bug_repro=1 AND ((a.approval_type = '延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -7*(a.round+1))) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -7))), b.bug_id, NULL)) AS  overtime_by_dev`,

		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_severity = 'Blocking', b.bug_id, NULL)) AS  blocking_not_cbd`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_priority = 'critical', b.bug_id, NULL)) AS  critical_not_cbd`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_priority = 'major', b.bug_id, NULL)) AS  major_not_cbd`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_priority = 'normal', b.bug_id, NULL)) AS  normal_not_cbd`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_priority = 'trivial', b.bug_id, NULL)) AS  trivial_not_cbd`,
		`COUNT(IF(b.bug_submitter_group LIKE '%测试%', b.bug_id, NULL)) AS  total_by_test`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%', b.bug_id, NULL)) AS  not_cbd_count_by_test`,
		`ROUND(COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%', b.bug_id, NULL))/COUNT(IF(b.bug_submitter_group LIKE '%测试%', b.bug_id, NULL))*100,2) AS not_cbd_percent_by_test`,
		`COUNT(IF(b.bug_submitter_group not LIKE '%测试%', b.bug_id, NULL)) AS  total_by_dev`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group not LIKE '%测试%', b.bug_id, NULL)) AS  not_cbd_count_by_dev`,
		`Round(COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DEAY", "GIVEUP") and b.bug_submitter_group not LIKE '%测试%', b.bug_id, NULL))/COUNT(IF(b.bug_submitter_group not LIKE '%测试%', b.bug_id, NULL))*100, 2) AS not_cbd_percent_by_dev`,
		`COUNT(
			IF(
				b.bug_state not like "DENIAL%"
				and
				(b.bug_workpacket_name = '遗留或delay的bug(其他)' or b.bug_state = 'DELAY')
				and
				(
					b.resolvedok is null
					or b.needsendbugreport is null
					or (b.needsendbugreport = '是' and b.bugreportid = 0)
					or (b.resolvedok = '用户接口修订' and (b.bugreportid = 0 or b.needsendbugreport != '是'))
					or (b.check_owner not in (select name from users where id in (select v0 from casbin_rule where ptype = 'g' and v1 in (select id from roles where name = '组长'))))
				),
				b.bug_id,
				null
			)
		) as need_bug_report_check`,
	}

	if len(bugOS) > 0 {
		// 首轮BUG总关闭率
		config, err := dreleaseprojectconfig.FindConfigByName(bugOS)

		if err == nil {
			if len(config.FirstBugFixStartAt) > 0 && len(config.SecondTestStartAt) > 0 {
				bugCreatedInFirstRound := fmt.Sprintf(`
					COUNT(
						IF(
							b.bug_created_at < '%s',
							b.bug_id,
							NULL
						)
					) AS bug_created_in_first_round`,
					config.FirstBugFixStartAt,
				)
				bugNotClosedInFirstRound := fmt.Sprintf(`
					COUNT(
						IF(
							b.bug_created_at < '%s' and ((b.bug_state not LIKE 'CLOSED%%' and b.bug_state not LIKE 'DENIAL%%' and b.bug_state not IN ("DELAY", "GIVEUP")) or b.bug_closed_at > '%s'),
							b.bug_id,
							NULL
						)
					) AS bug_not_closed_in_first_round`,
					config.FirstBugFixStartAt,
					config.SecondTestStartAt,
				)
				cbdPercentInFirstRound := fmt.Sprintf(`
				Round(
					COUNT(
						IF(
							(b.bug_state LIKE 'CLOSED%%' Or b.bug_state LIKE 'DENIAL%%' Or b.bug_state IN ("DELAY", "GIVEUP")) and b.bug_created_at < '%s' and b.bug_closed_at < '%s',
							b.bug_id,
							NULL
						)
					)
					/
					COUNT(
						IF(
							b.bug_created_at < '%s',
							b.bug_id,
							NULL
						)
					)*100,
				2) AS cbd_percent_in_first_round`,
					config.FirstBugFixStartAt,
					config.SecondTestStartAt,
					config.FirstBugFixStartAt,
				)
				bugCreatedByTestInFirstRound := fmt.Sprintf(`
					COUNT(
						IF(
							b.main_bug_id = 0 and b.bug_submitter_group LIKE '%%测试%%' and b.bug_created_at < '%s',
							b.bug_id,
							NULL
						)
					) AS bug_created_by_test_in_first_round`,
					config.FirstBugFixStartAt,
				)
				bugFromTestNotClosedInFirstRound := fmt.Sprintf(`
					COUNT(
						IF(
							b.main_bug_id = 0 and b.bug_submitter_group LIKE '%%测试%%' and b.bug_created_at < '%s' and ((b.bug_state not LIKE 'CLOSED%%' and b.bug_state not LIKE 'DENIAL%%' and b.bug_state not IN ("DELAY", "GIVEUP")) or b.bug_closed_at > '%s'),
							b.bug_id,
							NULL
						)
					) AS bug_from_test_not_closed_in_first_round`,
					config.FirstBugFixStartAt,
					config.SecondTestStartAt,
				)
				cbdFromTestPercentInFirstRound := fmt.Sprintf(`
				Round(
					COUNT(
						IF(
							b.main_bug_id = 0 and b.bug_submitter_group LIKE '%%测试%%' and (b.bug_state LIKE 'CLOSED%%' Or b.bug_state LIKE 'DENIAL%%' Or b.bug_state IN ("DELAY", "GIVEUP")) and b.bug_created_at < '%s' and b.bug_closed_at < '%s',
							b.bug_id,
							NULL
						)
					)
					/
					COUNT(
						IF(
							b.main_bug_id = 0 and b.bug_submitter_group LIKE '%%测试%%' and b.bug_created_at < '%s',
							b.bug_id,
							NULL
						)
					)*100,
				2) AS cbd_from_test_percent_in_first_round`,
					config.FirstBugFixStartAt,
					config.SecondTestStartAt,
					config.FirstBugFixStartAt,
				)

				majorCbdPercentInFirstRound := fmt.Sprintf(`
				Round(
					COUNT(
						IF(
							(b.bug_severity = 'Blocking' or b.bug_priority = 'major' or b.bug_priority = 'critical') and (b.bug_state LIKE 'CLOSED%%' Or b.bug_state LIKE 'DENIAL%%' Or b.bug_state IN ("DELAY", "GIVEUP")) and b.bug_created_at < '%s' and b.bug_closed_at < '%s',
							b.bug_id,
							NULL
						)
					)
					/
					COUNT(
						IF(
							(b.bug_severity = 'Blocking' or b.bug_priority = 'major' or b.bug_priority = 'critical') and b.bug_created_at < '%s',
							b.bug_id,
							NULL
						)
					)*100,
				2) AS major_cbd_percent_in_first_round`,
					config.FirstBugFixStartAt,
					config.SecondTestStartAt,
					config.FirstBugFixStartAt,
				)

				majorNotCbdInFirstRound := fmt.Sprintf(`
					COUNT(
						IF(
							(b.bug_severity = 'Blocking' or b.bug_priority = 'major' or b.bug_priority = 'critical') and b.bug_created_at < '%s' and  ((b.bug_state not LIKE 'CLOSED%%' and b.bug_state not LIKE 'DENIAL%%' and b.bug_state not IN ("DELAY", "GIVEUP")) or b.bug_closed_at > '%s'),
							b.bug_id,
							NULL
						)
					) AS major_not_cbd_in_first_round`,
					config.FirstBugFixStartAt,
					config.SecondTestStartAt,
				)

				majorInFirstRound := fmt.Sprintf(`
				COUNT(
					IF(
						(b.bug_severity = 'Blocking' or b.bug_priority = 'major' or b.bug_priority = 'critical') and b.bug_created_at < '%s',
						b.bug_id,
						NULL
					)
				) AS major_in_first_round`,
					config.FirstBugFixStartAt,
				)

				selects = append(
					selects,
					cbdPercentInFirstRound,
					cbdFromTestPercentInFirstRound,
					majorCbdPercentInFirstRound,
					majorNotCbdInFirstRound,
					majorInFirstRound,
					bugCreatedInFirstRound,
					bugNotClosedInFirstRound,
					bugCreatedByTestInFirstRound,
					bugFromTestNotClosedInFirstRound,
				)
			}
			if len(config.SecondBugFixStartAt) > 0 && len(config.RegressTestStartAt) > 0 {
				bugCreatedInSecondRound := fmt.Sprintf(`
					COUNT(
						IF(
							b.bug_created_at < '%s',
							b.bug_id,
							NULL
							)
					) AS bug_created_in_second_round`,
					config.SecondBugFixStartAt,
				)
				bugNotClosedInSecondRound := fmt.Sprintf(`
					COUNT(
						IF(
							b.bug_created_at < '%s' and ((b.bug_state not LIKE 'CLOSED%%' and b.bug_state not LIKE 'DENIAL%%' and b.bug_state not IN ("DELAY", "GIVEUP")) or b.bug_closed_at > '%s'),
							b.bug_id,
							NULL
						)
					) AS bug_not_closed_in_second_round`,
					config.SecondBugFixStartAt,
					config.RegressTestStartAt,
				)
				cbdPercentInSecondRound := fmt.Sprintf(`
				Round(
					COUNT(
						IF(
							(b.bug_state LIKE 'CLOSED%%' Or b.bug_state LIKE 'DENIAL%%' Or b.bug_state IN ("DELAY", "GIVEUP")) and b.bug_created_at < '%s' and b.bug_closed_at < '%s',
							b.bug_id,
							NULL
						)
					)
					/
					COUNT(
						IF(
							b.bug_created_at < '%s',
							b.bug_id,
							NULL
						)
					)*100,
				2) AS cbd_percent_in_second_round`,
					config.SecondBugFixStartAt,
					config.RegressTestStartAt,
					config.SecondBugFixStartAt,
				)
				bugCreatedByTestInSecondRound := fmt.Sprintf(`
					COUNT(
						IF(
							b.main_bug_id = 0 and b.bug_submitter_group LIKE '%%测试%%' and b.bug_created_at < '%s',
							b.bug_id,
							NULL
						)
					) AS bug_created_by_test_in_second_round`,
					config.SecondBugFixStartAt,
				)
				bugFromTestNotClosedInSecondRound := fmt.Sprintf(`
				COUNT(
					IF(
						b.main_bug_id = 0 and b.bug_submitter_group LIKE '%%测试%%' and b.bug_created_at < '%s' and ((b.bug_state not LIKE 'CLOSED%%' and b.bug_state not LIKE 'DENIAL%%' and b.bug_state not IN ("DELAY", "GIVEUP")) or b.bug_closed_at > '%s'),
						b.bug_id,
						NULL
					)
				) AS bug_from_test_not_closed_in_second_round`,
					config.SecondBugFixStartAt,
					config.RegressTestStartAt,
				)
				cbdFromTestPercentInSecondRound := fmt.Sprintf(`
				Round(
					COUNT(
						IF(
							b.main_bug_id = 0 and b.bug_submitter_group LIKE '%%测试%%' and (b.bug_state LIKE 'CLOSED%%' Or b.bug_state LIKE 'DENIAL%%' Or b.bug_state IN ("DELAY", "GIVEUP")) and b.bug_created_at < '%s' and b.bug_closed_at < '%s',
							b.bug_id,
							NULL
						)
					)
					/
					COUNT(
						IF(
							b.main_bug_id = 0 and b.bug_submitter_group LIKE '%%测试%%' and b.bug_created_at < '%s',
							b.bug_id,
							NULL
						)
					)*100,
				2) AS cbd_from_test_percent_in_second_round`,
					config.SecondBugFixStartAt,
					config.RegressTestStartAt,
					config.SecondBugFixStartAt,
				)

				majorCbdPercentInSecondRound := fmt.Sprintf(`
				Round(
					COUNT(
						IF(
							(b.bug_severity = 'Blocking' or b.bug_priority = 'major' or b.bug_priority = 'critical') and (b.bug_state LIKE 'CLOSED%%' Or b.bug_state LIKE 'DENIAL%%' Or b.bug_state IN ("DELAY", "GIVEUP")) and b.bug_created_at < '%s' and b.bug_closed_at < '%s',
							b.bug_id,
							NULL
						)
					)
					/
					COUNT(
						IF(
							(b.bug_severity = 'Blocking' or b.bug_priority = 'major' or b.bug_priority = 'critical') and b.bug_created_at < '%s',
							b.bug_id,
							NULL
						)
					)*100,
				2) AS major_cbd_percent_in_second_round`,
					config.SecondBugFixStartAt,
					config.RegressTestStartAt,
					config.SecondBugFixStartAt,
				)

				majorNotCbdInSecondRound := fmt.Sprintf(`
					COUNT(
						IF(
							b.bug_created_at < '%s' and (b.bug_severity = 'Blocking' or b.bug_priority = 'major' or b.bug_priority = 'critical') and ((b.bug_state not LIKE 'CLOSED%%' and b.bug_state not LIKE 'DENIAL%%' and  b.bug_state not IN ("DELAY", "GIVEUP")) or b.bug_closed_at > '%s'),
							b.bug_id,
							NULL
						)
					) AS major_not_cbd_in_second_round`,
					config.SecondBugFixStartAt,
					config.RegressTestStartAt,
				)

				majorInSecondRound := fmt.Sprintf(`
				COUNT(
					IF(
						(b.bug_severity = 'Blocking' or b.bug_priority = 'major' or b.bug_priority = 'critical') and b.bug_created_at < '%s',
						b.bug_id,
						NULL
					)
				) AS major_in_second_round`,
					config.SecondBugFixStartAt,
				)
				selects = append(
					selects,
					cbdPercentInSecondRound,
					cbdFromTestPercentInSecondRound,
					majorCbdPercentInSecondRound,
					majorNotCbdInSecondRound,
					majorInSecondRound,
					bugCreatedInSecondRound,
					bugNotClosedInSecondRound,
					bugCreatedByTestInSecondRound,
					bugFromTestNotClosedInSecondRound,
				)
			}
			if len(config.ExperimentReleaseAt) > 0 {
				bugCreatedBeforeExperimentRelease := fmt.Sprintf(`
					COUNT(
						IF(
							b.bug_created_at < '%s',
							b.bug_id,
							NULL
							)
					) AS bug_created_before_experiment_release`,
					config.ExperimentReleaseAt+" 23:59:59",
				)
				bugNotClosedBeforeExperimentRelease := fmt.Sprintf(`
					COUNT(
						IF(
							b.bug_created_at < '%s' and ((b.bug_state not LIKE 'CLOSED%%' and b.bug_state not LIKE 'DENIAL%%' and b.bug_state not IN ("DELAY", "GIVEUP")) or b.bug_closed_at > '%s'),
							b.bug_id,
							NULL
						)
					) AS bug_not_closed_before_experiment_release`,
					config.ExperimentReleaseAt+" 23:59:59",
					config.ExperimentReleaseAt+" 23:59:59",
				)
				cbdPercentBeforeExperimentRelease := fmt.Sprintf(`
				Round(
					COUNT(
						IF(
							(b.bug_state LIKE 'CLOSED%%' Or b.bug_state LIKE 'DENIAL%%' Or b.bug_state IN ("DELAY", "GIVEUP")) and b.bug_created_at < '%s' and b.bug_closed_at < '%s',
							b.bug_id,
							NULL
						)
					)
					/
					COUNT(
						IF(
							b.bug_created_at < '%s',
							b.bug_id,
							NULL
						)
					)*100,
				2) AS cbd_percent_before_experiment_release`,
					config.ExperimentReleaseAt+" 23:59:59",
					config.ExperimentReleaseAt+" 23:59:59",
					config.ExperimentReleaseAt+" 23:59:59",
				)
				bugCreatedByTestBeforeExperimentRelease := fmt.Sprintf(`
					COUNT(
						IF(
							b.main_bug_id = 0 and b.bug_submitter_group LIKE '%%测试%%' and b.bug_created_at < '%s',
							b.bug_id,
							NULL
						)
					) AS bug_created_by_test_before_experiment_release`,
					config.ExperimentReleaseAt+" 23:59:59",
				)
				bugFromTestNotClosedBeforeExperimentRelease := fmt.Sprintf(`
				COUNT(
					IF(
						b.main_bug_id = 0 and b.bug_submitter_group LIKE '%%测试%%' and b.bug_created_at < '%s' and ((b.bug_state not LIKE 'CLOSED%%' and b.bug_state not LIKE 'DENIAL%%' and b.bug_state not IN ("DELAY", "GIVEUP")) or b.bug_closed_at > '%s'),
						b.bug_id,
						NULL
					)
				) AS bug_from_test_not_closed_before_experiment_release`,
					config.ExperimentReleaseAt+" 23:59:59",
					config.ExperimentReleaseAt+" 23:59:59",
				)
				cbdFromTestPercentBeforeExperimentRelease := fmt.Sprintf(`
				Round(
					COUNT(
						IF(
							b.main_bug_id = 0 and b.bug_submitter_group LIKE '%%测试%%' and (b.bug_state LIKE 'CLOSED%%' Or b.bug_state LIKE 'DENIAL%%' Or b.bug_state IN ("DELAY", "GIVEUP")) and b.bug_created_at < '%s' and b.bug_closed_at < '%s',
							b.bug_id,
							NULL
						)
					)
					/
					COUNT(
						IF(
							b.main_bug_id = 0 and b.bug_submitter_group LIKE '%%测试%%' and b.bug_created_at < '%s',
							b.bug_id,
							NULL
						)
					)*100,
				2) AS cbd_from_test_percent_before_experiment_release`,
					config.ExperimentReleaseAt+" 23:59:59",
					config.ExperimentReleaseAt+" 23:59:59",
					config.ExperimentReleaseAt+" 23:59:59",
				)

				majorCbdPercentBeforeExperimentRelease := fmt.Sprintf(`
				Round(
					COUNT(
						IF(
							(b.bug_severity = 'Blocking' or b.bug_priority = 'major' or b.bug_priority = 'critical') and (b.bug_state LIKE 'CLOSED%%' Or b.bug_state LIKE 'DENIAL%%' Or b.bug_state IN ("DELAY", "GIVEUP")) and b.bug_created_at < '%s' and b.bug_closed_at < '%s',
							b.bug_id,
							NULL
						)
					)
					/
					COUNT(
						IF(
							(b.bug_severity = 'Blocking' or b.bug_priority = 'major' or b.bug_priority = 'critical') and b.bug_created_at < '%s',
							b.bug_id,
							NULL
						)
					)*100,
				2) AS major_cbd_percent_before_experiment_release`,
					config.ExperimentReleaseAt+" 23:59:59",
					config.ExperimentReleaseAt+" 23:59:59",
					config.ExperimentReleaseAt+" 23:59:59",
				)

				majorNotCbdBeforeExperimentRelease := fmt.Sprintf(`
					COUNT(
						IF(
							b.bug_created_at < '%s' and (b.bug_severity = 'Blocking' or b.bug_priority = 'major' or b.bug_priority = 'critical') and ((b.bug_state not LIKE 'CLOSED%%' and b.bug_state not LIKE 'DENIAL%%' and  b.bug_state not IN ("DELAY", "GIVEUP")) or b.bug_closed_at > '%s'),
							b.bug_id,
							NULL
						)
					) AS major_not_cbd_before_experiment_release`,
					config.ExperimentReleaseAt+" 23:59:59",
					config.ExperimentReleaseAt+" 23:59:59",
				)

				majorBeforeExperimentRelease := fmt.Sprintf(`
				COUNT(
					IF(
						(b.bug_severity = 'Blocking' or b.bug_priority = 'major' or b.bug_priority = 'critical') and b.bug_created_at < '%s',
						b.bug_id,
						NULL
					)
				) AS major_before_experiment_release`,
					config.ExperimentReleaseAt+" 23:59:59",
				)
				selects = append(
					selects,
					cbdPercentBeforeExperimentRelease,
					cbdFromTestPercentBeforeExperimentRelease,
					majorCbdPercentBeforeExperimentRelease,
					majorNotCbdBeforeExperimentRelease,
					majorBeforeExperimentRelease,
					bugCreatedBeforeExperimentRelease,
					bugNotClosedBeforeExperimentRelease,
					bugCreatedByTestBeforeExperimentRelease,
					bugFromTestNotClosedBeforeExperimentRelease,
				)
			}
		} else {
			logging.ErrorLogger.Error(err)
		}
		// 首轮测试提BUG关闭率
	}

	db := easygorm.GetEasyGormDb().
		Table(`
		(
			SELECT  *, (case when bug_cbd_at >= IFNULL(bug_dbd_at, 0) AND bug_cbd_at >= IFNULL(bug_giveup_at, 0) AND bug_cbd_at >= IFNULL(bug_delay_at, 0) then bug_cbd_at
							when bug_dbd_at >= IFNULL(bug_cbd_at, 0) AND bug_dbd_at >= IFNULL(bug_giveup_at, 0) AND bug_dbd_at >= IFNULL(bug_delay_at, 0) then bug_dbd_at
							when bug_giveup_at >= IFNULL(bug_dbd_at, 0) AND bug_giveup_at >= IFNULL(bug_cbd_at, 0) AND bug_giveup_at >= IFNULL(bug_delay_at, 0) then bug_giveup_at
							when bug_delay_at >= IFNULL(bug_dbd_at, 0) AND bug_delay_at >= IFNULL(bug_cbd_at, 0) AND bug_delay_at >= IFNULL(bug_giveup_at, 0) then bug_delay_at
							else bug_dbt_at
					END) AS bug_closed_at
					FROM bugs
		) as b`).
		Joins("left join (select `round`, created_at, bug_id, `status`, approval_type from bug_approvals where id in (select max(id) as id from bug_approvals where status = 1 group by bug_id)) a on b.bug_id = a.bug_id").
		Joins("left join (select r.id as id, r.name as name, c.test_start_at as test_start_at from release_projects r left join release_project_configs c on r.id = c.release_project_id) c on c.name = b.bug_os").
		Select(selects).Group("b.bug_os, b.bug_owner_group").
		Order(fmt.Sprintf("%s %s", by, order))

	// Having("not_cbd_count > 0")

	where := easygorm.GetEasyGormDb().
		Where("b.bug_os like 'NTOS1.0R%'")

	if len(bugOS) > 0 {
		where = where.Where("b.bug_os = ?", bugOS)
	}
	if len(bugOwnerGroup) > 0 {
		where = where.Where("b.bug_owner_group = ?", bugOwnerGroup)
	}

	//增加开关用于控制是否展示其他专业的统计
	otherItems := []*BugGroupSummary{}
	switch group {
	case "other":
		db = db.Where("b.bug_owner_group not in ?", vars.FilterBugOwnerGroups)
	case "device":
		// 增加其他专业组，总计
		otherSelects := []string{}
		otherSelects = append(otherSelects, selects...)
		otherSelects[1] = "b.bug_owner_group_other as bug_owner_group"

		db = db.Where("b.bug_owner_group in ?", vars.FilterBugOwnerGroups)

		otherDb := easygorm.GetEasyGormDb().
			Table(`
					(
						SELECT *, '其他专业组' as bug_owner_group_other, (case when bug_cbd_at >= IFNULL(bug_dbd_at, 0) AND bug_cbd_at >= IFNULL(bug_giveup_at, 0) AND bug_cbd_at >= IFNULL(bug_delay_at, 0) then bug_cbd_at
										when bug_dbd_at >= IFNULL(bug_cbd_at, 0) AND bug_dbd_at >= IFNULL(bug_giveup_at, 0) AND bug_dbd_at >= IFNULL(bug_delay_at, 0) then bug_dbd_at
										when bug_giveup_at >= IFNULL(bug_dbd_at, 0) AND bug_giveup_at >= IFNULL(bug_cbd_at, 0) AND bug_giveup_at >= IFNULL(bug_delay_at, 0) then bug_giveup_at
										when bug_delay_at >= IFNULL(bug_dbd_at, 0) AND bug_delay_at >= IFNULL(bug_cbd_at, 0) AND bug_delay_at >= IFNULL(bug_giveup_at, 0) then bug_delay_at
										else bug_dbt_at
								END) AS bug_closed_at
								FROM bugs
					) as b`).
			Joins("left join (select `round`, created_at, bug_id, `status`, approval_type from bug_approvals where id in (select max(id) as id from bug_approvals where status = 1 group by bug_id)) a on b.bug_id = a.bug_id").
			Joins("left join (select r.id as id, r.name as name, c.test_start_at as test_start_at from release_projects r left join release_project_configs c on r.id = c.release_project_id) c on c.name = b.bug_os").
			Select(otherSelects).Group("b.bug_os, b.bug_owner_group_other").
			Where("b.bug_owner_group not in ?", vars.FilterBugOwnerGroups).
			Order(fmt.Sprintf("%s %s", by, order))

		err := otherDb.Where(where).Find(&otherItems).Error
		if err != nil {
			return otherItems, err
		}
	default:
	}

	err := db.Where(where).Find(&items).Error
	if err != nil {
		return nil, err
	}
	items = append(items, otherItems...)
	return items, nil
}

func (s *BugGroupSummary) ExportBugSummary(items []*BugGroupSummary) ([]interface{}, [][]interface{}) {
	xt := reflect.TypeOf(s)
	// xv := reflect.ValueOf(s)

	rows := [][]interface{}{}
	headers := []interface{}{}
	for i := 0; i < xt.Elem().NumField(); i++ {
		head, ok := xt.Elem().Field(i).Tag.Lookup("xlsx")
		if ok {
			headers = append(headers, head)
		}
	}
	rows = append(rows, headers)

	for _, e := range items {
		cells := []interface{}{}
		xv := reflect.ValueOf(e)
		for i := 0; i < xv.Elem().NumField(); i++ {
			_, ok := xt.Elem().Field(i).Tag.Lookup("xlsx")
			if ok {
				cells = append(cells, xv.Elem().Field(i).Interface())
			}

		}
		rows = append(rows, cells)
	}
	return headers, rows
}

type Column struct {
	Idx   int    `json:"idx"`
	Key   string `json:"key"`
	Label string `json:"label"`
}

func (s *BugGroupSummary) GetColumns() ([]*Column, error) {
	xt := reflect.TypeOf(s)

	columns := []*Column{}

	for i := 0; i < xt.Elem().NumField(); i++ {
		key, ok1 := xt.Elem().Field(i).Tag.Lookup("json")
		label, ok2 := xt.Elem().Field(i).Tag.Lookup("xlsx")
		_idx, ok3 := xt.Elem().Field(i).Tag.Lookup("idx")

		if ok1 && ok2 && ok3 {
			idx, err := strconv.Atoi(_idx)
			if err != nil {
				return columns, err
			}
			columns = append(columns, &Column{
				Idx:   idx,
				Key:   key,
				Label: label,
			})
		}
	}
	sort.SliceStable(columns, func(i, j int) bool {
		if columns[i].Idx < columns[j].Idx {
			return true
		}
		return false
	})
	return columns, nil
}
