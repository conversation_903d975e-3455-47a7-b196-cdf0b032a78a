package dqualitycrosscheck

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models"
	"irisAdminApi/application/models/qualitypoint"
)

const ModelName = "共性问题排查表"

type Response struct {
	models.ModelBase
	Description          string `json:"description"`                                          //事项描述
	AuditTime            string `json:"audit_time"`                                           //稽核时间
	ResponsiblePersonID  uint   `json:"responsible_person_id"`                                //责任角色ID
	DepartmentManagerID  uint   `json:"department_manager_id"`                                //部门经理ID
	TeamLeaderID         uint   `json:"team_leader_id"`                                       //专业组组长ID
	Department           string `json:"department"`                                           //所在部门
	Team                 string `json:"team"`                                                 //所在专业组
	IssueDescription     string `json:"issue_description" form:"issue_description"`           //抽象共性问题描述
	CommonReviewComments string `json:"common_review_comments" form:"common_review_comments"` //共性问题评审意见
	QualityViolationID   uint   `json:"quality_violation_id"`
	Uuid                 string `json:"uuid"`
	Status               uint   `json:"status"` //流程状态
}

type ListResponse struct {
	Response
}

type Request struct {
	ID                   uint   `json:"id"`
	UpdatedAt            string `json:"updated_at"`
	CreatedAt            string `json:"created_at"`
	Description          string `json:"description"  from:"description"`                      //事项描述
	AuditTime            string `json:"audit_time" form:"audit_time"`                         //稽核时间
	ResponsiblePersonID  uint   `json:"responsible_person_id" form:"responsible_person_id"`   //责任人ID
	DepartmentManagerID  uint   `json:"department_manager_id" form:"department_manager_id"`   //部门经理ID
	TeamLeaderID         uint   `json:"team_leader_id" form:"team_leader_id"`                 //专业组组长
	Department           string `json:"department" form:"department"`                         //所在部门
	Team                 string `json:"team" form:"team"`                                     //所在专业组
	DepartmentID         uint   `json:"department_id" form:"department_id"`                   //所在部门ID
	TeamID               uint   `json:"team_id" form:"team_id"`                               //所在专业组ID
	IssueDescription     string `json:"issue_description" form:"issue_description"`           //抽象共性问题描述
	CommonReviewComments string `json:"common_review_comments" form:"common_review_comments"` //共性问题评审意见
	Status               uint   `json:"status" form:"status"`                                 //流程状态
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *qualitypoint.QualityCrossCheck {
	return &qualitypoint.QualityCrossCheck{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindInIds(ids []uint) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id in ?", ids).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	return items, nil
}
