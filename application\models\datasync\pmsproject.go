package datasync

import "time"

type PmsHardwareProject struct {
	ID                      int        `gorm:"primarykey; autoIncrement:false" json:"id" `
	ProjectName             string     `gorm:"not null; type:varchar(200)" json:"project_name"  update:"1"`
	HardProjectName         string     `gorm:"not null; type:varchar(200)" json:"hard_project_name"  update:"1"`
	ProductLineName         string     `gorm:"not null; type:varchar(200)" json:"product_line_name"  update:"1"`
	ProjectAttribute        string     `gorm:"not null; type:varchar(200)" json:"project_attribute"  update:"1"`
	CategoryName            string     `gorm:"not null; type:varchar(200)" json:"category_name"  update:"1"`
	BaseProductName         string     `gorm:"not null; type:varchar(200)" json:"base_product_name"  update:"1"`
	ProductClass            string     `gorm:"not null; type:varchar(200)" json:"product_class"  update:"1"`
	ProductAttribute1       string     `gorm:"not null; type:varchar(200)" json:"product_attribute1"  update:"1"`
	EngineeringCategoryName string     `gorm:"not null; type:varchar(200)" json:"engineering_category_name"  update:"1"`
	CreateUserName          string     `gorm:"not null; type:varchar(200)" json:"create_user_name"  update:"1"`
	ProjectStatus           string     `gorm:"not null; type:varchar(200)" json:"project_status"  update:"1"`
	ActStartDate            *time.Time ` json:"act_start_date"  update:"1"`
	ActEndDate              *time.Time ` json:"act_end_date"  update:"1"`
	Disabled                bool       `grom:"not null; default:false" json:"disabled" update:"1"`
}

type PmsHardwareProjectMilestone struct {
	ID             int        `gorm:"primarykey; autoIncrement:false" json:"id" `
	ProjectID      int        `gorm:"not null" json:"project_id"  update:"1"`
	ProjectName    string     `gorm:"not null; type:varchar(200)" json:"project_name"  update:"1"`
	MilestoneName  string     `gorm:"not null; type:varchar(200)" json:"milestone_name"  update:"1"`
	MilestoneValue string     `gorm:"not null; type:varchar(200)" json:"milestone_value"  update:"1"`
	Status         string     `gorm:"not null; type:varchar(200)" json:"status"  update:"1"`
	PlanDate       *time.Time ` json:"plan_date"  update:"1"`
	PlanStartDate  *time.Time ` json:"plan_start_date"  update:"1"`
	ActStartDate   *time.Time ` json:"act_start_date"  update:"1"`
	ActDate        *time.Time ` json:"act_date"  update:"1"`
	Disabled       bool       `grom:"not null; default:false" json:"disabled" update:"1"`
}

type PmsSoftwareProject struct {
	ProjectID    int    `gorm:"primarykey; autoIncrement:false" json:"project_id" `
	ProjectName  string `gorm:"not null; type:varchar(200)" json:"project_name"  update:"1"`
	ProjectClass string `gorm:"not null; type:varchar(200)" json:"project_class"  update:"1"`
	SetUpType    string `gorm:"not null; type:varchar(200)" json:"set_up_type"  update:"1"`

	ProductLineName  string    `gorm:"not null; type:varchar(200)" json:"product_line_name"  update:"1"`
	Classification   string    `gorm:"not null; type:varchar(200)" json:"classification"  update:"1"`
	ProjectType      string    `gorm:"not null; type:varchar(200)" json:"project_type"  update:"1"`
	BaseProject      string    `gorm:"not null; type:varchar(200)" json:"base_project"  update:"1"`
	CustomList       string    `gorm:"not null; type:varchar(200)" json:"custom_list"  update:"1"`
	ProjectRequire   string    `gorm:"not null; type:varchar(200)" json:"project_require"  update:"1"`
	Fcs              time.Time `json:"fcs"  update:"1"`
	ProjectBeginTime time.Time `json:"project_begin_time"  update:"1"`
	ProjectEndTime   time.Time `json:"project_end_time"  update:"1"`
	ProjectNotice    string    `gorm:"not null; type:varchar(200); default:''" json:"project_notice"  update:"1"`
	CaName           string    `gorm:"not null; type:varchar(200); default:''" json:"ca_name"  update:"1"`
	PmName           string    `gorm:"not null; type:varchar(200); default:''" json:"pm_name"  update:"1"`
	PtmName          string    `gorm:"not null; type:varchar(200); default:''" json:"ptm_name"  update:"1"`
	PqaName          string    `gorm:"not null; type:varchar(200); default:''" json:"pqa_name"  update:"1"`
	CmaName          string    `gorm:"not null; type:varchar(200); default:''" json:"cma_name"  update:"1"`

	ProjectStatus  string    `gorm:"not null; type:varchar(200)" json:"project_status"  update:"1"`
	ApplyTime      time.Time `json:"apply_time"  update:"1"`
	LastUpdateTime time.Time `json:"last_update_time"  update:"1"`
	Disabled       bool      `grom:"not null; default:false" json:"disabled" update:"1"`
	IsNormal       bool      `grom:"not null; default:false" json:"is_normal" update:"1"`
}
