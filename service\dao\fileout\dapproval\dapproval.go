package dapproval

import (
	"fmt"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/fileout"
	"irisAdminApi/service/dao/user/duser"
)

const ModelName = "申请单管理"

type Response struct {
	Id         uint                   `json:"id"`
	OriginName string                 `json:"origin_name" `
	Name       string                 `json:"name" `
	Md5        string                 `json:"md5"`
	UserId     uint                   `json:"user_id"`
	Audit      bool                   `json:"audit"`
	AuditorId  uint                   `json:"audit_id"`
	UpdatedAt  time.Time              `json:"updated_at"`
	CreatedAt  time.Time              `json:"created_at"`
	Force      uint                   `json:"force"`
	Permit     bool                   `json:"permit"`
	User       duser.ApprovalResponse `gorm:"-" json:"user"`
	Auditor    duser.ApprovalResponse `gorm:"-" json:"auditor"`
	Status     uint                   `json:"status"` // 0: system check, 1: system check not contain c code, 2: system check cantian c code
}

type ApprovalNoAuditResponse struct {
	Id         uint   `json:"id"`
	OriginName string `json:"origin_name" `
	Md5        string `json:"md5"`
	UserId     uint   `json:"user_id"`
	Audit      bool   `json:"audit"`
	Permit     bool   `json:"permit"`
	Force      uint   `json:"force"`
	AuditorId  uint   `json:"audit_id"`
	UpdatedAt  string `json:"updated_at"`
	CreatedAt  string `json:"created_at"`
}

type ListResponse struct {
	Response
}

type ApprovalReq struct {
	Id         uint   `json:"id"`
	OriginName string `json:"origin_name" form:"origin_name"`
	AuditorId  uint   `json:"auditor_id" form:"auditor_id"`
	Audit      bool   `json:"audit"`
	Force      uint   `json:"force"`
	Permit     bool   `json:"permit"`
	Comment    string `json:"comment" form:"comment"`
	Shares     []uint `json:"users"`
	Md5        string `json:"md5" form:"md5"`
	IsCcode    bool   `json:"is_c_code"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *fileout.Approval {
	return &fileout.Approval{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	FormatResponse(approvals, true)
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	FormatResponse(approvals, false)
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func FormatResponse(approvals []*ListResponse, showName bool) {
	for _, approval := range approvals {
		if approval.Permit == false && !showName {
			approval.Name = ""
		}
		var auditor duser.ApprovalResponse
		auditor.Find(approval.AuditorId)
		var user duser.ApprovalResponse
		user.Find(approval.UserId)
		approval.Auditor = auditor
		approval.User = user
	}
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	FormatResponseV2(u, false)
	return nil
}

func FormatResponseV2(approval *Response, showName bool) {
	if approval.Permit == false && !showName {
		approval.Name = ""
	}
	var auditor duser.ApprovalResponse
	auditor.Find(approval.AuditorId)
	var user duser.ApprovalResponse
	user.Find(approval.UserId)
	approval.Auditor = auditor
	approval.User = user
}

func (u *Response) FindByMd5(md5 string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("md5 = ?", md5).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	FormatResponseV2(u, true)
	return nil
}
