package dfaultlist

import (
	"fmt"
	"strings"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/qualitydata"
	"irisAdminApi/service/dao/user/duser"
)

const ModelName = "数据清洗系统-故障清单列表"

type Response struct {
	Id                          uint                    `json:"id"`
	UpdatedAt                   string                  `json:"updated_at"`
	CreatedAt                   string                  `json:"created_at"`
	TechnicalFailureID          string                  `json:"technical_failure_id"`           //技服故障ID
	FaultManagerID              uint                    `json:"fault_manager_id"`               //故障负责人ID
	UserID                      uint                    `json:"user_id"`                        //故障记录人ID
	GroupID                     uint                    `json:"group_id"`                       //归属部门专业组ID
	ReportingTime               string                  `json:"reporting_time"`                 //提报时间
	ProblemDescription          string                  `json:"problem_description"`            //问题描述
	FaultModel                  string                  `json:"fault_model"`                    //故障型号
	FaultClassification         string                  `json:"fault_classification"`           //故障分类
	FailureVersion              string                  `json:"failure_version"`                //故障版本
	IntroductionVersion         string                  `json:"introduction_version"`           //引入版本
	FaultyComponent             string                  `json:"faulty_component"`               //故障组件
	IntroductionVersionTime     string                  `json:"introduction_version_time"`      //引入版本时间
	ResolutionVersion           string                  `json:"resolution_version"`             //解决版本
	SolutionScenarios           string                  `json:"solution_Scenarios"`             //解决方案场景
	CausesCategory              string                  `json:"causes_category"`                //原因大类
	CauseSubclass               string                  `json:"cause_subclass"`                 //原因小类
	QualityAttributeCategory    string                  `json:"quality_attribute_category"`     //质量属性类别
	QualityRootCauseDescription string                  `json:"quality_root_cause_description"` //质量属性根因描述
	State                       uint                    `json:"state"`                          //状态  0未定位 1已定位 2已完成
	User                        *duser.ApprovalResponse `gorm:"-" json:"user,omitempty"`
	FaultManager                *duser.ApprovalResponse `gorm:"-" json:"fault_manager,omitempty"`
}

type ListResponse struct {
	Response
}

type Request struct {
	TechnicalFailureID          string `json:"technical_failure_id"                  form:"technical_failure_id"`           //技服故障ID
	FaultManagerID              uint   `json:"fault_manager_id"                      form:"fault_manager_id"`               //故障负责人ID
	GroupID                     uint   `json:"group_id"                              form:"group_id"`                       //归属专业组ID
	ReportingTime               string `json:"reporting_time"                        form:"reporting_time"`                 //提报时间
	ProblemDescription          string `json:"problem_description"                   form:"problem_description"`            //问题描述
	FaultModel                  string `json:"fault_model"                           form:"fault_model"`                    //故障型号
	FaultClassification         string `json:"fault_classification"                  form:"fault_classification"`           //故障分类
	FailureVersion              string `json:"failure_version"                       form:"failure_version"`                //故障版本
	IntroductionVersion         string `json:"introduction_version"                  form:"introduction_version"`           //引入版本
	FaultyComponent             string `json:"faulty_component"                      form:"faulty_component"`               //故障组件
	IntroductionVersionTime     string `json:"introduction_version_time"             form:"introduction_version_time"`      //引入版本时间
	ResolutionVersion           string `json:"resolution_version"                    form:"resolution_version"`             //解决版本
	SolutionScenarios           string `json:"solution_scenarios"                    form:"solution_scenarios"`             //解决方案场景
	CausesCategory              string `json:"causes_category"                       form:"causes_category"`                //原因大类
	CauseSubclass               string `json:"cause_subclass"                        form:"cause_subclass"`                 //原因小类
	QualityAttributeCategory    string `json:"quality_attribute_category"            form:"quality_attribute_category"`     //质量属性类别
	QualityRootCauseDescription string `json:"quality_root_cause_description"        form:"quality_root_cause_description"` //质量属性根因描述
	State                       uint   `json:"state"                                 form:"state"`                          //状态
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *qualitydata.FaultList {
	return &qualitydata.FaultList{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	GetUsers(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	GetUsers(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find FaultList err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find FaultList err ", err)
		return err
	}
	return nil
}

func GetUsers(items []*ListResponse) error {
	userIds := []uint{}
	for _, item := range items {
		userIds = append(userIds, item.UserID)
		userIds = append(userIds, item.FaultManagerID)
	}
	users, _ := duser.FindSimpleInIds(userIds)
	var userMap = make(map[uint]*duser.ApprovalResponse)
	for _, user := range users {
		userMap[user.Id] = user
	}
	for _, item := range items {
		item.FaultManager = userMap[item.FaultManagerID]
		item.User = userMap[item.UserID]
	}
	return nil
}

func FindAll() ([]*Response, error) {
	var list []*Response
	err := easygorm.GetEasyGormDb().Model(Model()).Find(&list).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return nil, err
	}
	return list, nil
}

func GetFaultListInfos(version, applicableVersion, start, end, packageType, infoType, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var faultListInfos []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(start) > 0 {
		db = db.Where("created_at >= ?", start+" 00:00:00.000")
	}
	if len(end) > 0 {
		db = db.Where("created_at <= ?", end+" 23:59:59.999")
	}
	if len(packageType) > 0 {
		db = db.Where("package_type = ?", packageType)
	}
	if len(infoType) > 0 {
		db = db.Where("info_type = ?", infoType)
	}
	if len(applicableVersion) > 0 {
		db = db.Where("applicable_version  = ?", applicableVersion)
	}
	if len(version) > 0 {
		db = db.Where("version = ?", version)
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}
	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&faultListInfos).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	GetUsers(faultListInfos)
	list := map[string]interface{}{"items": faultListInfos, "total": count, "limit": pageSize}
	return list, nil
}

func AllFaultLists(name, sort, orderBy string, page, pageSize int, status, start, end, department string) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	if len(status) > 0 {
		db = db.Where("status = ?", status)
	}
	if len(start) > 0 {
		db = db.Where("reporting_time >= ?", start)
	}
	if len(end) > 0 {
		db = db.Where("reporting_time <= ?", end)
	}
	if len(department) > 0 {
		departmentIds := strings.Split(department, ",")
		db = db.Where("department_id in ?", departmentIds)
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	GetUsers(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}
