# Coredump记录自动化处理系统 - 核心代码实现方案

## 1. 项目结构

```
application/controllers/openfeishu/coredump/
├── coredump_service.go          # 主要业务逻辑服务
├── coredump_models.go           # 数据模型定义
├── coredump_field_mapper.go     # 字段映射器
├── coredump_tracker.go          # 本地状态跟踪器
├── coredump_controller.go       # HTTP接口控制器
├── coredump_config.go           # 配置管理
└── coredump_test.go             # 单元测试
```

## 2. 核心代码实现

### 2.1 数据模型定义 (coredump_models.go)

```go
package coredump

import (
    "time"
)

// CoredumpRecord Coredump记录数据结构
type CoredumpRecord struct {
    // 基础标识字段
    RecordID             string                 `json:"record_id"`              // 飞书记录ID
    SN                   string                 `json:"sn"`                     // 主键
    
    // Coredump核心信息
    CoredumpCollectURL   string                 `json:"coredump_collect_url"`   // coredump收集url
    CoredumpTime         time.Time              `json:"coredump_time"`          // coredump时间
    CoredumpComponent    string                 `json:"coredump_component"`     // coredump组件
    StackInfo            string                 `json:"stack_info"`             // 堆栈信息
    FileName             string                 `json:"file_name"`              // 文件名
    
    // 责任人信息
    ComponentResponsible []FeishuPersonField    `json:"component_responsible"`  // 组件负责人
    ProcessResponsible   []FeishuPersonField    `json:"process_responsible"`    // 进程负责人
    
    // 设备和版本信息
    DeviceModel          string                 `json:"device_model"`           // 设备型号
    SoftwareVersion      string                 `json:"software_version"`       // 软件版本
    
    // 时间和描述信息
    RecordDate           time.Time              `json:"record_date"`            // 记录日期
    Description          string                 `json:"description"`            // 说明
    
    // 处理状态信息
    IsKnownIssue         bool                   `json:"is_known_issue"`         // 是否已知问题
    ProcessResult        string                 `json:"process_result"`         // 处理结果
    FixVersion           string                 `json:"fix_version"`            // 修复版本
}

// FeishuPersonField 飞书人员字段结构
type FeishuPersonField struct {
    ID        string `json:"id"`        // OpenID
    Name      string `json:"name"`      // 姓名
    Email     string `json:"email"`     // 邮箱
    AvatarURL string `json:"avatar_url"` // 头像URL
}

// ProcessingCache 处理缓存结构
type ProcessingCache struct {
    LastProcessTime time.Time         `json:"last_process_time"`    // 最后处理时间
    ProcessedCount  int64             `json:"processed_count"`      // 已处理数量
    ErrorLog        []ProcessingError `json:"error_log"`            // 错误日志
}

// ProcessingError 处理错误记录
type ProcessingError struct {
    RecordID    string    `json:"record_id"`    // 飞书记录ID
    SN          string    `json:"sn"`           // Coredump SN
    ErrorType   string    `json:"error_type"`   // 错误类型
    ErrorMsg    string    `json:"error_msg"`    // 错误信息
    Timestamp   time.Time `json:"timestamp"`    // 错误时间
}

// ProcessResult 处理结果统计
type ProcessResult struct {
    StartTime          time.Time     `json:"start_time"`           // 开始时间
    EndTime            time.Time     `json:"end_time"`             // 结束时间
    Duration           time.Duration `json:"duration"`             // 处理耗时
    TotalRecords       int           `json:"total_records"`        // 总记录数
    NewRecords         int           `json:"new_records"`          // 新记录数
    SuccessRecords     int           `json:"success_records"`      // 成功处理数
    FailedRecords      int           `json:"failed_records"`       // 失败处理数
    Errors             []string      `json:"errors,omitempty"`     // 错误信息列表
}
```

### 2.2 配置管理 (coredump_config.go)

```go
package coredump

import (
    "time"
    "irisAdminApi/application/libs"
)

// CoredumpConfig Coredump处理配置
type CoredumpConfig struct {
    // 飞书配置
    CoredumpAppToken string   `yaml:"coredump_app_token"`
    CoredumpTableID  string   `yaml:"coredump_table_id"`
    PageSize         int      `yaml:"page_size"`
    FieldNames       []string `yaml:"field_names"`
    
    // 默认用户配置
    DefaultSubmitter   string `yaml:"default_submitter"`
    DefaultChargeUser  string `yaml:"default_charge_user"`
    DefaultPSTL        string `yaml:"default_pstl"`
    
    // 处理配置
    BatchSize        int           `yaml:"batch_size"`
    ProcessTimeout   time.Duration `yaml:"process_timeout"`
    RetryCount       int           `yaml:"retry_count"`
    SkipKnownIssues  bool          `yaml:"skip_known_issues"`
    
    // 字段映射配置
    FieldMapping *FieldMappingConfig `yaml:"field_mapping"`
}

// FieldMappingConfig 字段映射配置
type FieldMappingConfig struct {
    SNField                   string `yaml:"sn_field"`
    CoredumpURLField          string `yaml:"coredump_url_field"`
    CoredumpTimeField         string `yaml:"coredump_time_field"`
    ComponentField            string `yaml:"component_field"`
    StackInfoField            string `yaml:"stack_info_field"`
    ComponentResponsibleField string `yaml:"component_responsible_field"`
    ProcessResponsibleField   string `yaml:"process_responsible_field"`
    DeviceModelField          string `yaml:"device_model_field"`
    SoftwareVersionField      string `yaml:"software_version_field"`
    DescriptionField          string `yaml:"description_field"`
    IsKnownIssueField         string `yaml:"is_known_issue_field"`
}

// LoadCoredumpConfig 加载Coredump配置
func LoadCoredumpConfig() *CoredumpConfig {
    config := &CoredumpConfig{
        // 默认配置
        PageSize:         100,
        BatchSize:        50,
        ProcessTimeout:   30 * time.Second,
        RetryCount:       3,
        SkipKnownIssues:  true,
        DefaultSubmitter: "coredump_system",
        DefaultChargeUser: "wuzhensheng",
        DefaultPSTL:      "wangchunping",
        FieldNames: []string{
            "SN", "coredump收集url", "coredump时间", "coredump组件",
            "堆栈信息", "组件负责人", "进程负责人", "设备型号",
            "软件版本", "记录日期", "说明", "是否已知问题",
        },
        FieldMapping: &FieldMappingConfig{
            SNField:                   "SN",
            CoredumpURLField:          "coredump收集url",
            CoredumpTimeField:         "coredump时间",
            ComponentField:            "coredump组件",
            StackInfoField:            "堆栈信息",
            ComponentResponsibleField: "组件负责人",
            ProcessResponsibleField:   "进程负责人",
            DeviceModelField:          "设备型号",
            SoftwareVersionField:      "软件版本",
            DescriptionField:          "说明",
            IsKnownIssueField:         "是否已知问题",
        },
    }
    
    // 从现有配置中读取飞书相关配置
    config.CoredumpAppToken = libs.Config.FeiShuDoc.BiAppToken
    // 注意：需要在application.yml中添加coredumptableid配置
    
    return config
}
```

### 2.3 时间过滤器 (coredump_time_filter.go)

```go
package coredump

import (
    "encoding/json"
    "os"
    "sync"
    "time"
    "irisAdminApi/application/logging"
)

// CoredumpTimeFilter Coredump时间过滤器
type CoredumpTimeFilter struct {
    cacheFile    string
    lastRunTime  time.Time
    mutex        sync.RWMutex
}

// NewCoredumpTimeFilter 创建时间过滤器
func NewCoredumpTimeFilter(cacheFile string) *CoredumpTimeFilter {
    filter := &CoredumpTimeFilter{
        cacheFile: cacheFile,
    }

    // 加载上次运行时间
    filter.loadLastRunTime()

    return filter
}

// loadLastRunTime 加载上次运行时间
func (f *CoredumpTimeFilter) loadLastRunTime() {
    f.mutex.Lock()
    defer f.mutex.Unlock()

    data, err := os.ReadFile(f.cacheFile)
    if err != nil {
        // 文件不存在，使用默认时间（24小时前）
        f.lastRunTime = time.Now().Add(-24 * time.Hour)
        logging.InfoLogger.Infof("缓存文件不存在，使用默认时间: %v", f.lastRunTime)
        return
    }

    var cache ProcessingCache
    if err := json.Unmarshal(data, &cache); err != nil {
        f.lastRunTime = time.Now().Add(-24 * time.Hour)
        logging.ErrorLogger.Errorf("解析缓存文件失败: %v", err)
        return
    }

    f.lastRunTime = cache.LastProcessTime
    logging.InfoLogger.Infof("加载上次运行时间: %v", f.lastRunTime)
}

// saveLastRunTime 保存运行时间
func (f *CoredumpTimeFilter) saveLastRunTime() error {
    f.mutex.Lock()
    defer f.mutex.Unlock()

    cache := ProcessingCache{
        LastProcessTime: f.lastRunTime,
        ProcessedCount:  0, // 这里可以添加统计信息
    }

    data, err := json.MarshalIndent(cache, "", "  ")
    if err != nil {
        return err
    }

    return os.WriteFile(f.cacheFile, data, 0644)
}

// ShouldProcess 判断记录是否应该处理
func (f *CoredumpTimeFilter) ShouldProcess(record *CoredumpRecord) bool {
    f.mutex.RLock()
    defer f.mutex.RUnlock()

    // 如果记录时间晚于上次处理时间，则需要处理
    return record.CoredumpTime.After(f.lastRunTime) || record.RecordDate.After(f.lastRunTime)
}

// FilterNewRecords 过滤出新记录
func (f *CoredumpTimeFilter) FilterNewRecords(records []*CoredumpRecord) []*CoredumpRecord {
    var newRecords []*CoredumpRecord

    for _, record := range records {
        if f.ShouldProcess(record) {
            newRecords = append(newRecords, record)
        }
    }

    logging.InfoLogger.Infof("过滤结果: 总记录=%d, 新记录=%d", len(records), len(newRecords))
    return newRecords
}

// UpdateLastRunTime 更新最后运行时间
func (f *CoredumpTimeFilter) UpdateLastRunTime() error {
    f.mutex.Lock()
    f.lastRunTime = time.Now()
    f.mutex.Unlock()

    return f.saveLastRunTime()
}

// GetLastRunTime 获取最后运行时间
func (f *CoredumpTimeFilter) GetLastRunTime() time.Time {
    f.mutex.RLock()
    defer f.mutex.RUnlock()
    return f.lastRunTime
}
```

### 2.4 字段映射器 (coredump_field_mapper.go)

```go
package coredump

import (
    "fmt"
    "html"
    "strings"
    "time"
    "irisAdminApi/application/controllers/openfeishu"
    "irisAdminApi/application/logging"
)

// CoredumpFieldMapper Coredump字段映射器
type CoredumpFieldMapper struct {
    config *CoredumpConfig
}

// NewCoredumpFieldMapper 创建字段映射器
func NewCoredumpFieldMapper() *CoredumpFieldMapper {
    return &CoredumpFieldMapper{
        config: LoadCoredumpConfig(),
    }
}

// MapToBugInfo 将Coredump记录映射为BugInfo
func (m *CoredumpFieldMapper) MapToBugInfo(record *CoredumpRecord) (*openfeishu.BugInfo, error) {
    bugInfo := openfeishu.NewDefaultBugInfo()
    
    // 基础字段映射
    bugInfo.Summary = m.generateBugSummary(record)
    bugInfo.Product = record.CoredumpComponent
    bugInfo.VersionMess = record.SoftwareVersion
    bugInfo.DeviceUnderTestConfig = record.DeviceModel
    bugInfo.DebugMess = record.CoredumpCollectURL
    bugInfo.TestCaseNum = fmt.Sprintf("COREDUMP_%s_%d", record.SN, time.Now().Unix())
    bugInfo.Bugdescription = m.generateBugDescription(record)
    bugInfo.Source = "Coredump自动提交"
    
    // 人员字段映射
    chargeUser, err := m.determineChargeUser(record)
    if err != nil {
        logging.ErrorLogger.Errorf("确定负责人失败: %v", err)
        chargeUser = m.config.DefaultChargeUser
    }
    bugInfo.ChargeCasUserid = chargeUser
    bugInfo.SubmitterCasUserid = m.config.DefaultSubmitter
    bugInfo.PstlCasUserid = m.config.DefaultPSTL
    
    // 优先级和严重性设置
    bugInfo.Priority = m.determinePriority(record)
    bugInfo.Severity = m.determineSeverity(record)
    
    // 其他固定字段
    bugInfo.OS = "Coredump专项"
    bugInfo.TestMethod = "自动化检测"
    bugInfo.Repeat = "待确认"
    bugInfo.LegacyBug = "否"
    
    return bugInfo, nil
}

// determineChargeUser 确定Bug负责人
func (m *CoredumpFieldMapper) determineChargeUser(record *CoredumpRecord) (string, error) {
    // 优先级1: 组件负责人
    if len(record.ComponentResponsible) > 0 {
        email := record.ComponentResponsible[0].Email
        if username, err := m.extractUsernameFromEmail(email); err == nil {
            return username, nil
        }
    }
    
    // 优先级2: 进程负责人
    if len(record.ProcessResponsible) > 0 {
        email := record.ProcessResponsible[0].Email
        if username, err := m.extractUsernameFromEmail(email); err == nil {
            return username, nil
        }
    }
    
    // 回退: 使用默认负责人
    return m.config.DefaultChargeUser, nil
}

// extractUsernameFromEmail 从邮箱提取用户名
func (m *CoredumpFieldMapper) extractUsernameFromEmail(email string) (string, error) {
    if email == "" {
        return "", fmt.Errorf("邮箱地址为空")
    }
    
    // 提取@符号前的部分作为用户名
    parts := strings.Split(email, "@")
    if len(parts) < 1 || parts[0] == "" {
        return "", fmt.Errorf("无效的邮箱格式: %s", email)
    }
    
    return parts[0], nil
}

// generateBugSummary 生成Bug标题
func (m *CoredumpFieldMapper) generateBugSummary(record *CoredumpRecord) string {
    return fmt.Sprintf("Coredump异常 - %s [%s] SN:%s", 
        record.CoredumpComponent, record.DeviceModel, record.SN)
}

// generateBugDescription 生成Bug描述
func (m *CoredumpFieldMapper) generateBugDescription(record *CoredumpRecord) string {
    var desc strings.Builder
    
    // 基础信息部分
    desc.WriteString("<h3>Coredump基础信息</h3>")
    desc.WriteString(fmt.Sprintf("<p><strong>SN:</strong> %s</p>", record.SN))
    desc.WriteString(fmt.Sprintf("<p><strong>组件:</strong> %s</p>", record.CoredumpComponent))
    desc.WriteString(fmt.Sprintf("<p><strong>设备型号:</strong> %s</p>", record.DeviceModel))
    desc.WriteString(fmt.Sprintf("<p><strong>软件版本:</strong> %s</p>", record.SoftwareVersion))
    desc.WriteString(fmt.Sprintf("<p><strong>发生时间:</strong> %s</p>", 
        record.CoredumpTime.Format("2006-01-02 15:04:05")))
    
    // 收集链接部分
    if record.CoredumpCollectURL != "" {
        desc.WriteString("<h3>相关链接</h3>")
        desc.WriteString(fmt.Sprintf("<p><strong>收集链接:</strong> <a href=\"%s\">%s</a></p>", 
            record.CoredumpCollectURL, record.CoredumpCollectURL))
    }
    
    // 堆栈信息部分
    if record.StackInfo != "" {
        desc.WriteString("<h3>堆栈信息</h3>")
        desc.WriteString(fmt.Sprintf("<pre>%s</pre>", html.EscapeString(record.StackInfo)))
    }
    
    // 补充说明部分
    if record.Description != "" {
        desc.WriteString("<h3>补充说明</h3>")
        desc.WriteString(fmt.Sprintf("<p>%s</p>", html.EscapeString(record.Description)))
    }
    
    // 负责人信息部分
    desc.WriteString("<h3>负责人信息</h3>")
    if len(record.ComponentResponsible) > 0 {
        desc.WriteString(fmt.Sprintf("<p><strong>组件负责人:</strong> %s</p>",
            record.ComponentResponsible[0].Name))
        if record.ComponentResponsible[0].Email != "" {
            desc.WriteString(fmt.Sprintf("<p><strong>联系邮箱:</strong> %s</p>",
                record.ComponentResponsible[0].Email))
        }
    }
    if len(record.ProcessResponsible) > 0 {
        desc.WriteString(fmt.Sprintf("<p><strong>进程负责人:</strong> %s</p>",
            record.ProcessResponsible[0].Name))
        if record.ProcessResponsible[0].Email != "" {
            desc.WriteString(fmt.Sprintf("<p><strong>联系邮箱:</strong> %s</p>",
                record.ProcessResponsible[0].Email))
        }
    }
    
    return desc.String()
}

// determinePriority 确定优先级
func (m *CoredumpFieldMapper) determinePriority(record *CoredumpRecord) string {
    // 根据组件类型确定优先级
    component := strings.ToLower(record.CoredumpComponent)
    
    if strings.Contains(component, "核心") || strings.Contains(component, "内核") {
        return "Critical"
    } else if strings.Contains(component, "网络") || strings.Contains(component, "存储") {
        return "Major"
    } else {
        return "Normal"
    }
}

// determineSeverity 确定严重性
func (m *CoredumpFieldMapper) determineSeverity(record *CoredumpRecord) string {
    // Coredump通常都是比较严重的问题
    return "High"
}
```

### 2.5 主服务类 (coredump_service.go)

```go
package coredump

import (
    "context"
    "encoding/json"
    "fmt"
    "strconv"
    "strings"
    "time"
    
    "github.com/chyroc/lark"
    larkbitable "github.com/chyroc/lark/bitable"
    
    "irisAdminApi/application/controllers/openfeishu"
    "irisAdminApi/application/libs"
    "irisAdminApi/application/logging"
)

// CoredumpService Coredump处理服务
type CoredumpService struct {
    feishuClient   *lark.Client
    bugSubmitter   *openfeishu.BugSubmitter
    fieldMapper    *CoredumpFieldMapper
    timeFilter     *CoredumpTimeFilter
    config         *CoredumpConfig
}

// NewCoredumpService 创建Coredump处理服务
func NewCoredumpService() *CoredumpService {
    return &CoredumpService{
        feishuClient: lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret),
        bugSubmitter: openfeishu.NewBugSubmitter(),
        fieldMapper:  NewCoredumpFieldMapper(),
        timeFilter:   NewCoredumpTimeFilter("cache/coredump_last_run.json"),
        config:       LoadCoredumpConfig(),
    }
}

// ProcessCoredumpRecords 处理Coredump记录（主要入口方法）
func (s *CoredumpService) ProcessCoredumpRecords() (*ProcessResult, error) {
    logging.InfoLogger.Info("[COREDUMP] 开始处理Coredump记录")
    
    result := &ProcessResult{
        StartTime: time.Now(),
        Errors:    make([]string, 0),
    }
    
    // 步骤1: 读取飞书多维表格数据
    records, err := s.readCoredumpRecords()
    if err != nil {
        return nil, fmt.Errorf("读取Coredump记录失败: %w", err)
    }
    
    result.TotalRecords = len(records)
    logging.InfoLogger.Infof("[COREDUMP] 读取到 %d 条Coredump记录", len(records))
    
    // 步骤2: 过滤新记录（基于时间戳）
    newRecords := s.timeFilter.FilterNewRecords(records)

    result.NewRecords = len(newRecords)
    logging.InfoLogger.Infof("[COREDUMP] 新记录数: %d", len(newRecords))
    
    // 步骤3: 批量处理记录
    for _, record := range newRecords {
        err := s.processRecord(record, result)
        if err != nil {
            logging.ErrorLogger.Errorf("[COREDUMP] 处理记录失败 [%s]: %v", record.RecordID, err)
            result.FailedRecords++
        } else {
            result.SuccessRecords++
        }
    }

    // 步骤4: 更新最后处理时间
    if len(newRecords) > 0 {
        if err := s.timeFilter.UpdateLastRunTime(); err != nil {
            logging.ErrorLogger.Errorf("[COREDUMP] 更新处理时间失败: %v", err)
        }
    }

    result.EndTime = time.Now()
    result.Duration = result.EndTime.Sub(result.StartTime)

    logging.InfoLogger.Infof("[COREDUMP] 处理完成: 总计=%d, 新记录=%d, 成功=%d, 失败=%d, 耗时=%v",
        result.TotalRecords, result.NewRecords, result.SuccessRecords, result.FailedRecords, result.Duration)

    return result, nil
}

// readCoredumpRecords 读取飞书多维表格中的Coredump记录
func (s *CoredumpService) readCoredumpRecords() ([]*CoredumpRecord, error) {
    var allRecords []*CoredumpRecord
    pageToken := ""
    
    for {
        // 构建查询请求
        req := larkbitable.NewSearchAppTableRecordReqBuilder().
            AppToken(s.config.CoredumpAppToken).
            TableId(s.config.CoredumpTableID).
            PageSize(s.config.PageSize).
            PageToken(pageToken).
            Body(larkbitable.NewSearchAppTableRecordReqBodyBuilder().
                FieldNames(s.config.FieldNames).
                Build()).
            Build()
        
        // 调用飞书API
        resp, err := s.feishuClient.Bitable.AppTableRecord.Search(context.Background(), req)
        if err != nil {
            return nil, fmt.Errorf("飞书API调用失败: %w", err)
        }
        
        if !resp.Success() {
            return nil, fmt.Errorf("飞书API返回错误: %s", resp.Msg)
        }
        
        // 解析记录
        records, err := s.parseFeishuRecords(resp.Data.Items)
        if err != nil {
            return nil, fmt.Errorf("解析飞书记录失败: %w", err)
        }
        
        allRecords = append(allRecords, records...)
        
        // 检查是否还有更多数据
        if !resp.Data.HasMore {
            break
        }
        pageToken = resp.Data.PageToken
    }
    
    return allRecords, nil
}

// parseFeishuRecords 解析飞书记录数据
func (s *CoredumpService) parseFeishuRecords(items []*larkbitable.AppTableRecord) ([]*CoredumpRecord, error) {
    var records []*CoredumpRecord
    
    for _, item := range items {
        record, err := s.parseFeishuRecord(item)
        if err != nil {
            logging.ErrorLogger.Errorf("[COREDUMP] 解析记录失败 [%s]: %v", *item.RecordId, err)
            continue // 跳过解析失败的记录
        }
        
        // 过滤已知问题和已处理的记录
        if s.shouldSkipRecord(record) {
            logging.InfoLogger.Infof("[COREDUMP] 跳过记录 [%s]: 已知问题或已处理", record.RecordID)
            continue
        }
        
        records = append(records, record)
    }
    
    return records, nil
}

// parseFeishuRecord 解析单条飞书记录
func (s *CoredumpService) parseFeishuRecord(item *larkbitable.AppTableRecord) (*CoredumpRecord, error) {
    record := &CoredumpRecord{
        RecordID: *item.RecordId,
    }
    
    // 解析各个字段
    for fieldName, value := range item.Fields {
        switch fieldName {
        case s.config.FieldMapping.SNField:
            if str, ok := value.(string); ok {
                record.SN = str
            }
        case s.config.FieldMapping.CoredumpURLField:
            if str, ok := value.(string); ok {
                record.CoredumpCollectURL = str
            }
        case s.config.FieldMapping.ComponentField:
            if str, ok := value.(string); ok {
                record.CoredumpComponent = str
            }
        case s.config.FieldMapping.StackInfoField:
            if str, ok := value.(string); ok {
                record.StackInfo = str
            }
        case s.config.FieldMapping.DeviceModelField:
            if str, ok := value.(string); ok {
                record.DeviceModel = str
            }
        case s.config.FieldMapping.SoftwareVersionField:
            if str, ok := value.(string); ok {
                record.SoftwareVersion = str
            }
        case s.config.FieldMapping.DescriptionField:
            if str, ok := value.(string); ok {
                record.Description = str
            }
        case s.config.FieldMapping.ComponentResponsibleField:
            if persons, err := s.parsePersonField(value); err == nil {
                record.ComponentResponsible = persons
            }
        case s.config.FieldMapping.ProcessResponsibleField:
            if persons, err := s.parsePersonField(value); err == nil {
                record.ProcessResponsible = persons
            }
        case s.config.FieldMapping.CoredumpTimeField:
            if timestamp, err := s.parseTimeField(value); err == nil {
                record.CoredumpTime = timestamp
            }
        case s.config.FieldMapping.IsKnownIssueField:
            if b, ok := value.(bool); ok {
                record.IsKnownIssue = b
            }
        }
    }
    
    return record, nil
}

// parsePersonField 解析人员字段
func (s *CoredumpService) parsePersonField(value interface{}) ([]FeishuPersonField, error) {
    var persons []FeishuPersonField
    
    // 飞书人员字段通常是数组格式
    if personArray, ok := value.([]interface{}); ok {
        for _, personData := range personArray {
            if personMap, ok := personData.(map[string]interface{}); ok {
                person := FeishuPersonField{}
                
                if id, ok := personMap["id"].(string); ok {
                    person.ID = id
                }
                if name, ok := personMap["name"].(string); ok {
                    person.Name = name
                }
                if email, ok := personMap["email"].(string); ok {
                    person.Email = email
                }
                if avatar, ok := personMap["avatar_url"].(string); ok {
                    person.AvatarURL = avatar
                }
                
                persons = append(persons, person)
            }
        }
    }
    
    return persons, nil
}

// parseTimeField 解析时间字段
func (s *CoredumpService) parseTimeField(value interface{}) (time.Time, error) {
    switch v := value.(type) {
    case string:
        // 尝试多种时间格式
        formats := []string{
            "2006-01-02 15:04:05",
            "2006-01-02T15:04:05Z",
            "2006-01-02T15:04:05.000Z",
            "2006-01-02",
        }
        
        for _, format := range formats {
            if t, err := time.Parse(format, v); err == nil {
                return t, nil
            }
        }
        return time.Time{}, fmt.Errorf("无法解析时间格式: %s", v)
    case float64:
        // Unix时间戳
        return time.Unix(int64(v), 0), nil
    case int64:
        // Unix时间戳
        return time.Unix(v, 0), nil
    default:
        return time.Time{}, fmt.Errorf("不支持的时间类型: %T", value)
    }
}

// shouldSkipRecord 判断是否应该跳过记录
func (s *CoredumpService) shouldSkipRecord(record *CoredumpRecord) bool {
    // 跳过已知问题
    if s.config.SkipKnownIssues && record.IsKnownIssue {
        return true
    }
    
    // 跳过必要字段为空的记录
    if record.SN == "" || record.CoredumpComponent == "" {
        return true
    }
    
    return false
}

// shouldSkipRecord 判断是否应该跳过记录
func (s *CoredumpService) shouldSkipRecord(record *CoredumpRecord) bool {
    // 跳过已知问题
    if s.config.SkipKnownIssues && record.IsKnownIssue {
        return true
    }

    // 跳过必要字段为空的记录
    if record.SN == "" || record.CoredumpComponent == "" {
        return true
    }

    // 跳过已处理的记录（根据处理结果字段）
    if record.ProcessResult == "已解决" || record.ProcessResult == "已修复" {
        return true
    }

    return false
}

// processRecord 处理单条记录
func (s *CoredumpService) processRecord(record *CoredumpRecord, result *ProcessResult) error {
    logging.InfoLogger.Infof("[COREDUMP] 开始处理记录 [%s]: SN=%s", record.RecordID, record.SN)
    
    // 步骤1: 字段映射
    bugInfo, err := s.fieldMapper.MapToBugInfo(record)
    if err != nil {
        errMsg := fmt.Sprintf("字段映射失败: %v", err)
        s.tracker.MarkAsFailed(record.RecordID, record.SN, errMsg)
        result.Errors = append(result.Errors, errMsg)
        return err
    }
    
    // 步骤2: 提交Bug
    response, err := s.bugSubmitter.SubmitBug(bugInfo)
    if err != nil {
        errMsg := fmt.Sprintf("Bug提交失败: %v", err)
        result.Errors = append(result.Errors, errMsg)
        logging.ErrorLogger.Errorf("[COREDUMP] Bug提交失败 [%s]: %v", record.RecordID, err)
        return err
    }

    // 步骤3: 提取Bug ID（从响应中解析）
    bugID := s.extractBugIDFromResponse(response)

    logging.InfoLogger.Infof("[COREDUMP] 记录处理成功 [%s]: BugID=%s", record.RecordID, bugID)
    return nil
}

// extractBugIDFromResponse 从Bug提交响应中提取Bug ID
func (s *CoredumpService) extractBugIDFromResponse(response *openfeishu.BugSubmitResponse) string {
    if response == nil {
        return ""
    }
    
    // 尝试从响应消息中提取Bug ID
    // 假设响应格式类似: "Bug提交成功，Bug ID: BUG-2025-001"
    message := response.Message
    if strings.Contains(message, "Bug ID:") {
        parts := strings.Split(message, "Bug ID:")
        if len(parts) > 1 {
            return strings.TrimSpace(parts[1])
        }
    }
    
    // 如果无法提取，返回时间戳作为标识
    return fmt.Sprintf("AUTO_%d", time.Now().Unix())
}
```

### 2.6 HTTP控制器 (coredump_controller.go)

```go
package coredump

import (
    "github.com/kataras/iris/v12"
    "irisAdminApi/application/logging"
)

// CoredumpController Coredump处理控制器
type CoredumpController struct {
    service *CoredumpService
}

// NewCoredumpController 创建控制器
func NewCoredumpController() *CoredumpController {
    return &CoredumpController{
        service: NewCoredumpService(),
    }
}

// ProcessCoredumps 手动触发Coredump处理
func (c *CoredumpController) ProcessCoredumps(ctx iris.Context) {
    logging.InfoLogger.Info("[COREDUMP] 收到手动处理请求")
    
    result, err := c.service.ProcessCoredumpRecords()
    if err != nil {
        logging.ErrorLogger.Errorf("[COREDUMP] 处理失败: %v", err)
        ctx.JSON(iris.Map{
            "success": false,
            "message": err.Error(),
        })
        return
    }
    
    ctx.JSON(iris.Map{
        "success": true,
        "data":    result,
        "message": "处理完成",
    })
}

// GetProcessingStatus 获取处理状态
func (c *CoredumpController) GetProcessingStatus(ctx iris.Context) {
    lastRunTime := c.service.timeFilter.GetLastRunTime()

    ctx.JSON(iris.Map{
        "success": true,
        "data": iris.Map{
            "last_run_time": lastRunTime,
            "status":        "运行中",
            "message":       "系统正常运行",
        },
    })
}

// ResetLastRunTime 重置最后运行时间
func (c *CoredumpController) ResetLastRunTime(ctx iris.Context) {
    var req struct {
        ResetHours int `json:"reset_hours"` // 重置到几小时前
    }

    if err := ctx.ReadJSON(&req); err != nil {
        ctx.JSON(iris.Map{
            "success": false,
            "message": "请求参数错误",
        })
        return
    }

    if req.ResetHours <= 0 {
        req.ResetHours = 24 // 默认重置到24小时前
    }

    // 创建新的时间过滤器，设置到指定小时前
    resetTime := time.Now().Add(-time.Duration(req.ResetHours) * time.Hour)
    c.service.timeFilter.lastRunTime = resetTime

    err := c.service.timeFilter.saveLastRunTime()
    if err != nil {
        logging.ErrorLogger.Errorf("[COREDUMP] 重置运行时间失败: %v", err)
        ctx.JSON(iris.Map{
            "success": false,
            "message": err.Error(),
        })
        return
    }

    logging.InfoLogger.Infof("[COREDUMP] 重置运行时间成功: %v", resetTime)
    ctx.JSON(iris.Map{
        "success": true,
        "message": "重置成功",
        "reset_time": resetTime,
    })
}
```

## 3. 路由注册

在现有的路由文件中添加以下代码：

```go
// 在 routes/api.go 或相应的路由文件中添加
package routes

import (
    "github.com/kataras/iris/v12"
    "irisAdminApi/application/controllers/openfeishu/coredump"
)

// RegisterCoredumpRoutes 注册Coredump相关路由
func RegisterCoredumpRoutes(app *iris.Application) {
    controller := coredump.NewCoredumpController()
    
    coredumpGroup := app.Party("/api/coredump")
    {
        // 处理相关接口
        coredumpGroup.Post("/process", controller.ProcessCoredumps)

        // 查询相关接口
        coredumpGroup.Get("/status", controller.GetProcessingStatus)

        // 管理相关接口
        coredumpGroup.Post("/reset", controller.ResetLastRunTime)
    }
}
```

## 4. 配置文件更新

### 4.1 更新 application.yml

```yaml
feishudoc:
  enable: true
  appid: cli_a6864542233d900e
  appsecret: f9FupAX6aw65B6FLgyvNXbEywHq7keJF
  foldertoken: LSfAfsbqGlaC6Rd0wttc8yLynRd
  biapptoken: KZQCbsWefa5e3MsamMccOYYPnMr
  
  # Coredump配置 (新增)
  coredumpapptoken: "your_coredump_app_token_here"
  coredumptableid: "your_coredump_table_id_here"
  coredumpenable: true
```

### 4.2 创建 config/coredump_config.yaml

```yaml
coredump:
  # 飞书多维表格配置
  page_size: 100
  field_names:
    - "SN"
    - "coredump收集url"
    - "coredump时间"
    - "coredump组件"
    - "堆栈信息"
    - "组件负责人"
    - "进程负责人"
    - "设备型号"
    - "软件版本"
    - "记录日期"
    - "说明"
    - "是否已知问题"
    - "处理结果"
    - "修复版本"
    - "文件名"
  
  # 默认用户配置
  default_submitter: "coredump_system"
  default_charge_user: "wuzhensheng"
  default_pstl: "wangchunping"
  
  # 处理配置
  batch_size: 50
  process_timeout: "30s"
  retry_count: 3
  skip_known_issues: true
  
  # 字段映射配置
  field_mapping:
    sn_field: "SN"
    coredump_url_field: "coredump收集url"
    coredump_time_field: "coredump时间"
    component_field: "coredump组件"
    stack_info_field: "堆栈信息"
    component_responsible_field: "组件负责人"
    process_responsible_field: "进程负责人"
    device_model_field: "设备型号"
    software_version_field: "软件版本"
    description_field: "说明"
    is_known_issue_field: "是否已知问题"
```

## 5. 单元测试示例

### 5.1 创建 coredump_test.go

```go
package coredump

import (
    "testing"
    "time"
    "github.com/stretchr/testify/assert"
)

func TestCoredumpFieldMapper_MapToBugInfo(t *testing.T) {
    mapper := NewCoredumpFieldMapper()
    
    // 测试用例1: 完整数据映射
    record := &CoredumpRecord{
        RecordID:          "rec123",
        SN:                "ABC123",
        CoredumpComponent: "网络模块",
        SoftwareVersion:   "v2.1.3",
        DeviceModel:       "RG-S6220",
        CoredumpCollectURL: "http://example.com/coredump/123",
        ComponentResponsible: []FeishuPersonField{
            {Email: "<EMAIL>", Name: "张三"},
        },
        CoredumpTime: time.Now(),
    }
    
    bugInfo, err := mapper.MapToBugInfo(record)
    assert.NoError(t, err)
    assert.Equal(t, "网络模块", bugInfo.Product)
    assert.Equal(t, "zhangsan", bugInfo.ChargeCasUserid)
    assert.Contains(t, bugInfo.Summary, "ABC123")
    assert.Equal(t, "Coredump自动提交", bugInfo.Source)
}

func TestCoredumpFieldMapper_ExtractUsernameFromEmail(t *testing.T) {
    mapper := NewCoredumpFieldMapper()
    
    // 测试正常邮箱
    username, err := mapper.extractUsernameFromEmail("<EMAIL>")
    assert.NoError(t, err)
    assert.Equal(t, "zhangsan", username)
    
    // 测试空邮箱
    _, err = mapper.extractUsernameFromEmail("")
    assert.Error(t, err)
    
    // 测试无效格式
    _, err = mapper.extractUsernameFromEmail("invalid-email")
    assert.Error(t, err)
}

func TestCoredumpTimeFilter_ShouldProcess(t *testing.T) {
    // 创建临时缓存文件
    cacheFile := "/tmp/test_coredump_cache.json"
    defer os.Remove(cacheFile)

    filter := NewCoredumpTimeFilter(cacheFile)

    // 测试新记录（时间晚于上次处理时间）
    newRecord := &CoredumpRecord{
        RecordID:     "rec123",
        SN:           "ABC123",
        CoredumpTime: time.Now(),
    }
    assert.True(t, filter.ShouldProcess(newRecord))

    // 测试旧记录（时间早于上次处理时间）
    oldRecord := &CoredumpRecord{
        RecordID:     "rec456",
        SN:           "DEF456",
        CoredumpTime: time.Now().Add(-48 * time.Hour),
    }
    assert.False(t, filter.ShouldProcess(oldRecord))
}

func TestCoredumpService_ShouldSkipRecord(t *testing.T) {
    service := NewCoredumpService()
    
    // 测试跳过已知问题
    record1 := &CoredumpRecord{
        SN:           "ABC123",
        CoredumpComponent: "测试组件",
        IsKnownIssue: true,
    }
    assert.True(t, service.shouldSkipRecord(record1))
    
    // 测试跳过空SN
    record2 := &CoredumpRecord{
        SN:           "",
        CoredumpComponent: "测试组件",
        IsKnownIssue: false,
    }
    assert.True(t, service.shouldSkipRecord(record2))
    
    // 测试正常记录
    record3 := &CoredumpRecord{
        SN:           "ABC123",
        CoredumpComponent: "测试组件",
        IsKnownIssue: false,
    }
    assert.False(t, service.shouldSkipRecord(record3))
}
```

## 6. 部署脚本示例

### 6.1 创建 deploy.sh

```bash
#!/bin/bash

# Coredump系统部署脚本

echo "开始部署Coredump记录自动化处理系统..."

# 1. 检查Go环境
if ! command -v go &> /dev/null; then
    echo "错误: Go环境未安装"
    exit 1
fi

# 2. 编译项目
echo "编译项目..."
go build -o app main.go
if [ $? -ne 0 ]; then
    echo "错误: 编译失败"
    exit 1
fi

# 3. 创建必要目录
echo "创建目录结构..."
mkdir -p config
mkdir -p logs
mkdir -p docs/coredump

# 4. 复制配置文件
echo "复制配置文件..."
if [ ! -f "config/coredump_config.yaml" ]; then
    cp docs/coredump/coredump_config.yaml config/
    echo "请编辑 config/coredump_config.yaml 配置文件"
fi

# 5. 检查数据库连接
echo "检查数据库连接..."
# 这里可以添加数据库连接检查逻辑

# 6. 运行测试
echo "运行单元测试..."
go test ./application/controllers/openfeishu/coredump -v

# 7. 启动服务
echo "启动服务..."
./app &

echo "部署完成！"
echo "API接口地址: http://localhost:8080/api/coredump"
echo "日志文件位置: logs/"
```

## 7. API使用示例

### 7.1 创建 api_examples.sh

```bash
#!/bin/bash

# Coredump API使用示例

BASE_URL="http://localhost:8080/api/coredump"

echo "=== Coredump API 使用示例 ==="

# 1. 手动触发处理
echo "1. 手动触发Coredump处理..."
curl -X POST "$BASE_URL/process" \
  -H "Content-Type: application/json" | jq .

echo -e "\n"

# 2. 查看处理状态
echo "2. 查看处理状态..."
curl "$BASE_URL/status" | jq .

echo -e "\n"

# 3. 重置最后运行时间（重新处理最近24小时的记录）
echo "3. 重置最后运行时间..."
curl -X POST "$BASE_URL/reset" \
  -H "Content-Type: application/json" \
  -d '{"reset_hours": 24}' | jq .

echo -e "\n=== 示例完成 ==="
```

## 8. 监控脚本示例

### 8.1 创建 monitor.sh

```bash
#!/bin/bash

# Coredump系统监控脚本

LOG_FILE="logs/coredump_monitor.log"
API_URL="http://localhost:8080/api/coredump/status"

# 记录日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 检查API健康状态
check_api_health() {
    response=$(curl -s -w "%{http_code}" "$API_URL" -o /tmp/api_response.json)
    http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        log "API健康检查: 正常"
        return 0
    else
        log "API健康检查: 异常 (HTTP $http_code)"
        return 1
    fi
}

# 检查处理状态
check_processing_status() {
    if check_api_health; then
        processed_count=$(jq -r '.data.processed_count' /tmp/api_response.json)
        failed_count=$(jq -r '.data.failed_count' /tmp/api_response.json)
        
        log "处理状态: 成功=$processed_count, 失败=$failed_count"
        
        # 检查失败率
        if [ "$failed_count" -gt 0 ] && [ "$processed_count" -gt 0 ]; then
            failure_rate=$(echo "scale=2; $failed_count * 100 / ($processed_count + $failed_count)" | bc)
            if (( $(echo "$failure_rate > 10" | bc -l) )); then
                log "警告: 失败率过高 ($failure_rate%)"
            fi
        fi
    fi
}

# 检查日志错误
check_log_errors() {
    error_count=$(grep -c "ERROR" logs/error.log 2>/dev/null || echo 0)
    if [ "$error_count" -gt 10 ]; then
        log "警告: 错误日志数量过多 ($error_count)"
    fi
}

# 主监控循环
main() {
    log "开始Coredump系统监控"
    
    while true; do
        check_processing_status
        check_log_errors
        
        # 每5分钟检查一次
        sleep 300
    done
}

# 启动监控
main
```

这个实现方案提供了完整的代码结构和实现细节，包括：

1. **完整的Go代码实现**：所有核心组件的详细代码
2. **配置管理**：灵活的配置文件结构
3. **API接口**：完整的HTTP接口实现
4. **测试用例**：单元测试示例
5. **部署脚本**：自动化部署和监控脚本
6. **使用示例**：API调用示例

所有代码都基于现有的项目结构，复用了 `bug_submit.go` 中的 `BugInfo` 和 `BugSubmitter`，确保与现有系统的完美集成。