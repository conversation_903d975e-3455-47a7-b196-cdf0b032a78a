package coredump

import (
	"encoding/json"
	"fmt"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
)

// BugSubmitter Bug提交器
type BugSubmitter struct {
	config    *BugSystemConfig
	debugMode bool
	stats     *BugSubmitStatistics
}

// BugSubmitStatistics Bug提交统计信息
type BugSubmitStatistics struct {
	TotalSubmissions int `json:"total_submissions"`
	SuccessCount     int `json:"success_count"`
	FailureCount     int `json:"failure_count"`
	RetryCount       int `json:"retry_count"`
}

// NewBugSubmitter 创建Bug提交器
func NewBugSubmitter() *BugSubmitter {
	config := GetBugSystemConfig()

	return &BugSubmitter{
		config:    config,
		debugMode: libs.Config.FeiShuDoc.CoredumpDebugMode,
		stats:     &BugSubmitStatistics{},
	}
}

// SubmitBug 提交Bug到Bug管理系统
func (bs *BugSubmitter) SubmitBug(bugData *BugData) (string, error) {
	if bugData == nil {
		return "", fmt.Errorf("Bug数据不能为空")
	}

	bs.stats.TotalSubmissions++

	// 检查干运行模式
	if libs.Config.FeiShuDoc.CoredumpDryRun {
		return bs.simulateBugSubmit(bugData)
	}

	// 验证Bug数据
	if err := bs.validateBugData(bugData); err != nil {
		bs.stats.FailureCount++
		return "", fmt.Errorf("Bug数据验证失败: %v", err)
	}

	// 构建Bug信息（适配现有Bug系统格式）
	bugInfo, err := bs.buildBugInfoFromCoredump(bugData)
	if err != nil {
		bs.stats.FailureCount++
		return "", fmt.Errorf("构建Bug信息失败: %v", err)
	}

	// 调试模式输出Bug信息
	if bs.debugMode {
		logging.InfoLogger.Infof("准备提交Bug: %s", bugData.Summary)
		logging.InfoLogger.Infof("Bug详情: 组件=%s, 优先级=%s", bugData.Components, bugData.Priority)
	}

	// 带重试机制的Bug提交
	bugID, err := bs.submitBugWithRetry(bugInfo)
	if err != nil {
		bs.stats.FailureCount++
		return "", fmt.Errorf("Bug提交失败: %v", err)
	}

	bs.stats.SuccessCount++
	logging.InfoLogger.Infof("Bug提交成功: ID=%s, 标题=%s", bugID, bugData.Summary)

	return bugID, nil
}

// GetBugStatus 获取Bug状态
func (bs *BugSubmitter) GetBugStatus(bugID string) (string, error) {
	if bugID == "" {
		return "", fmt.Errorf("Bug ID不能为空")
	}

	// 检查干运行模式
	if libs.Config.FeiShuDoc.CoredumpDryRun {
		logging.InfoLogger.Infof("干运行模式：模拟查询Bug状态 %s", bugID)
		return "Open", nil
	}

	// 构建查询URL（根据实际Bug系统API调整）
	queryURL := fmt.Sprintf("%s/query?bugId=%s", bs.config.BaseURL, bugID)

	// 发送查询请求（使用GET方法，暂时用PostJson模拟）
	statusCode, _, responseBody, err := libs.PostJson(queryURL, map[string]interface{}{}, nil)
	if err != nil {
		return "", fmt.Errorf("查询Bug状态失败: %v", err)
	}

	if statusCode != 200 {
		return "", fmt.Errorf("查询Bug状态HTTP错误: %d", statusCode)
	}

	// 解析响应
	var response map[string]interface{}
	if len(responseBody) > 0 {
		err = json.Unmarshal(responseBody, &response)
		if err != nil {
			return "", fmt.Errorf("解析Bug状态响应失败: %v", err)
		}
	}

	// 提取状态信息
	if status, ok := response["status"]; ok {
		return fmt.Sprintf("%v", status), nil
	}

	logging.InfoLogger.Infof("查询Bug状态: %s", bugID)
	return "Unknown", nil
}

// UpdateBug 更新Bug信息
func (bs *BugSubmitter) UpdateBug(bugID string, updateData map[string]interface{}) error {
	if bugID == "" {
		return fmt.Errorf("Bug ID不能为空")
	}

	if len(updateData) == 0 {
		return fmt.Errorf("更新数据不能为空")
	}

	// 检查干运行模式
	if libs.Config.FeiShuDoc.CoredumpDryRun {
		logging.InfoLogger.Infof("干运行模式：模拟更新Bug %s", bugID)
		logging.InfoLogger.Infof("更新数据: %+v", updateData)
		return nil
	}

	// 构建更新请求数据
	requestData := map[string]interface{}{
		"bugId": bugID,
		"data":  updateData,
	}

	// 构建更新URL
	updateURL := fmt.Sprintf("%s/update", bs.config.BaseURL)

	// 发送更新请求
	statusCode, _, responseBody, err := libs.PostJson(updateURL, requestData, nil)
	if err != nil {
		return fmt.Errorf("更新Bug失败: %v", err)
	}

	if statusCode != 200 {
		return fmt.Errorf("更新Bug HTTP错误: %d", statusCode)
	}

	// 解析响应
	var response map[string]interface{}
	if len(responseBody) > 0 {
		err = json.Unmarshal(responseBody, &response)
		if err != nil {
			logging.ErrorLogger.Errorf("解析Bug更新响应失败: %v", err)
			// 不返回错误，因为更新可能已经成功
		}
	}

	logging.InfoLogger.Infof("Bug更新成功: %s", bugID)
	return nil
}

// simulateBugSubmit 模拟Bug提交（干运行模式）
func (bs *BugSubmitter) simulateBugSubmit(bugData *BugData) (string, error) {
	bugID := fmt.Sprintf("COREDUMP-DRY-%d", time.Now().Unix())

	logging.InfoLogger.Info("干运行模式：模拟Bug提交")
	logging.InfoLogger.Infof("模拟Bug ID: %s", bugID)
	logging.InfoLogger.Infof("Bug标题: %s", bugData.Summary)
	logging.InfoLogger.Infof("Bug描述: %s", bugData.Description)

	bs.stats.SuccessCount++
	return bugID, nil
}

// validateBugData 验证Bug数据
func (bs *BugSubmitter) validateBugData(bugData *BugData) error {
	if bugData.Summary == "" {
		return fmt.Errorf("Bug标题不能为空")
	}

	if bugData.Description == "" {
		return fmt.Errorf("Bug描述不能为空")
	}

	if bugData.Components == "" {
		return fmt.Errorf("Bug组件不能为空")
	}

	if bugData.Priority == "" {
		bugData.Priority = "中" // 设置默认优先级
	}

	if bugData.IssueType == "" {
		bugData.IssueType = "Bug" // 设置默认问题类型
	}

	return nil
}

// buildBugInfoFromCoredump 从Coredump数据构建Bug信息（适配现有Bug系统）
func (bs *BugSubmitter) buildBugInfoFromCoredump(bugData *BugData) (map[string]interface{}, error) {
	// 参考现有bug_submit.go的BugInfo结构，构建完整的Bug信息
	bugInfo := map[string]interface{}{
		// 基础信息字段
		"summary":  bugData.Summary,
		"priority": bs.mapPriority(bugData.Priority),
		"severity": bs.mapSeverity(bugData.Priority), // 映射严重程度

		// 必要的系统字段（修复缺失参数问题）
		"pstlCasUserId":      bs.getPstlUser(bugData),              // PSTL用户
		"chargeCasUserId":    bs.getChargeUser(bugData),            // 负责人
		"submitterCasUserid": bs.getSubmitterUser(bugData),         // 提交者
		"bugdescription":     bs.formatBugDescriptionHTML(bugData), // HTML格式描述

		// 产品和测试相关字段
		"os":          "Coredump自动化系统",
		"testMethod":  "自动化测试",
		"product":     bugData.Components,
		"testCase":    "Coredump记录处理",
		"source":      "Coredump自动提交",
		"testCaseNum": fmt.Sprintf("COREDUMP_%d", time.Now().Unix()),

		// 描述和诊断信息
		"oneKeyCollectionInfo": bs.formatDiagnosticInfoHTML(bugData),

		// 其他必要字段
		"testTopo":        "Coredump处理拓扑",
		"topoDesc":        "Coredump记录自动化处理系统",
		"legacyBug":       "否",
		"affectCaseCount": "1",
		"locate":          bugData.Components,
		"ccusers":         "",
		"pkIds":           bs.config.ProjectKey,
	}

	// 添加自定义字段
	if len(bugData.CustomFields) > 0 {
		for key, value := range bugData.CustomFields {
			bugInfo[key] = value
		}
	}

	return bugInfo, nil
}

// submitBugWithRetry 带重试机制的Bug提交
func (bs *BugSubmitter) submitBugWithRetry(bugInfo map[string]interface{}) (string, error) {
	maxRetries := 3
	baseDelay := time.Second

	for attempt := 0; attempt <= maxRetries; attempt++ {
		if attempt > 0 {
			bs.stats.RetryCount++
			delay := time.Duration(attempt) * baseDelay
			logging.InfoLogger.Infof("Bug提交重试 %d/%d，等待 %v", attempt, maxRetries, delay)
			time.Sleep(delay)
		}

		bugID, err := bs.submitBugToSystem(bugInfo)
		if err == nil {
			return bugID, nil
		}

		logging.ErrorLogger.Errorf("Bug提交尝试 %d 失败: %v", attempt+1, err)

		// 最后一次尝试失败
		if attempt == maxRetries {
			return "", fmt.Errorf("Bug提交失败，已重试 %d 次: %v", maxRetries, err)
		}
	}

	return "", fmt.Errorf("Bug提交失败")
}

// submitBugToSystem 实际提交Bug到系统
func (bs *BugSubmitter) submitBugToSystem(bugInfo map[string]interface{}) (string, error) {
	// 使用现有的HTTP客户端提交Bug
	statusCode, _, responseBody, err := libs.PostJson(bs.config.BaseURL, bugInfo, nil)
	if err != nil {
		return "", fmt.Errorf("HTTP请求失败: %v", err)
	}

	if statusCode != 200 {
		return "", fmt.Errorf("HTTP状态码异常: %d", statusCode)
	}

	// 解析响应获取Bug ID
	var response map[string]interface{}
	if len(responseBody) > 0 {
		err = json.Unmarshal(responseBody, &response)
		if err != nil {
			return "", fmt.Errorf("解析响应失败: %v", err)
		}
	}

	// 从响应中提取Bug ID（根据实际API响应格式调整）
	if bugID, ok := response["bugId"]; ok {
		return fmt.Sprintf("%v", bugID), nil
	}

	if _, ok := response["content"]; ok {
		return fmt.Sprintf("COREDUMP-%d", time.Now().Unix()), nil // 使用时间戳作为备用ID
	}

	return fmt.Sprintf("COREDUMP-%d", time.Now().Unix()), nil
}

// mapPriority 映射优先级
func (bs *BugSubmitter) mapPriority(priority string) string {
	switch priority {
	case "高", "High":
		return "高"
	case "中", "Medium":
		return "中"
	case "低", "Low":
		return "低"
	default:
		return "中" // 默认中等优先级
	}
}

// formatBugDescription 格式化Bug描述
func (bs *BugSubmitter) formatBugDescription(bugData *BugData) string {
	description := fmt.Sprintf("<h3>Coredump自动提交Bug</h3>")
	description += fmt.Sprintf("<p><strong>问题描述:</strong> %s</p>", bugData.Description)
	description += fmt.Sprintf("<p><strong>组件:</strong> %s</p>", bugData.Components)
	description += fmt.Sprintf("<p><strong>优先级:</strong> %s</p>", bugData.Priority)

	if bugData.Reporter != "" {
		description += fmt.Sprintf("<p><strong>报告人:</strong> %s</p>", bugData.Reporter)
	}

	if bugData.Assignee != "" {
		description += fmt.Sprintf("<p><strong>指派给:</strong> %s</p>", bugData.Assignee)
	}

	description += "<p><strong>问题来源:</strong> Coredump记录自动化处理系统</p>"

	return description
}

// formatDiagnosticInfo 格式化诊断信息
func (bs *BugSubmitter) formatDiagnosticInfo(bugData *BugData) string {
	info := "<h3>诊断信息</h3>"
	info += fmt.Sprintf("<p><strong>问题类型:</strong> %s</p>", bugData.IssueType)
	info += fmt.Sprintf("<p><strong>项目:</strong> %s</p>", bugData.ProjectKey)

	if len(bugData.Labels) > 0 {
		info += fmt.Sprintf("<p><strong>标签:</strong> %v</p>", bugData.Labels)
	}

	if len(bugData.CustomFields) > 0 {
		info += "<p><strong>自定义字段:</strong></p><ul>"
		for key, value := range bugData.CustomFields {
			info += fmt.Sprintf("<li>%s: %v</li>", key, value)
		}
		info += "</ul>"
	}

	info += fmt.Sprintf("<p><strong>创建时间:</strong> %s</p>", time.Now().Format("2006-01-02 15:04:05"))

	return info
}

// GetBugSubmitStatistics 获取Bug提交统计信息
func (bs *BugSubmitter) GetBugSubmitStatistics() *BugSubmitStatistics {
	return bs.stats
}

// ResetStatistics 重置统计信息
func (bs *BugSubmitter) ResetStatistics() {
	bs.stats = &BugSubmitStatistics{}
}

// mapSeverity 映射严重程度
func (bs *BugSubmitter) mapSeverity(priority string) string {
	switch priority {
	case "高", "High":
		return "High"
	case "中", "Medium":
		return "Normal"
	case "低", "Low":
		return "Low"
	default:
		return "Normal" // 默认普通严重程度
	}
}

// getPstlUser 获取PSTL用户
func (bs *BugSubmitter) getPstlUser(bugData *BugData) string {
	if bugData.Reporter != "" {
		return bugData.Reporter
	}
	return "admin" // 默认PSTL用户
}

// getChargeUser 获取负责人
func (bs *BugSubmitter) getChargeUser(bugData *BugData) string {
	if bugData.Assignee != "" {
		return bugData.Assignee
	}
	if bugData.Reporter != "" {
		return bugData.Reporter
	}
	return "admin" // 默认负责人
}

// getSubmitterUser 获取提交者
func (bs *BugSubmitter) getSubmitterUser(bugData *BugData) string {
	if bugData.Reporter != "" {
		return bugData.Reporter
	}
	return "admin" // 默认提交者
}

// formatBugDescriptionHTML 格式化Bug描述为HTML格式（符合系统要求）
func (bs *BugSubmitter) formatBugDescriptionHTML(bugData *BugData) string {
	// 参考现有bug_submit.go中的Bugdescription格式
	description := "<p><strong>Coredump自动提交Bug</strong></p>"
	description += "<p><br/></p>"
	description += "<p><strong>1、说明预期结果:</strong></p>"
	description += fmt.Sprintf("<p>系统正常运行，无Coredump产生</p>")
	description += "<p><br/></p>"
	description += "<p><strong>2、说明实际结果:</strong></p>"
	description += fmt.Sprintf("<p>%s</p>", bugData.Description)
	description += "<p><br/></p>"
	description += "<p><strong>3、说明问题出现步骤:</strong></p>"
	description += fmt.Sprintf("<p>组件: %s</p>", bugData.Components)
	if bugData.Reporter != "" {
		description += fmt.Sprintf("<p>报告人: %s</p>", bugData.Reporter)
	}
	if bugData.Assignee != "" {
		description += fmt.Sprintf("<p>指派给: %s</p>", bugData.Assignee)
	}
	description += "<p><br/></p>"
	description += "<p><strong>问题来源:</strong> Coredump记录自动化处理系统</p>"

	return description
}

// formatDiagnosticInfoHTML 格式化诊断信息为HTML格式
func (bs *BugSubmitter) formatDiagnosticInfoHTML(bugData *BugData) string {
	info := "<p><strong>一键收集信息</strong></p>"
	info += "<p><br/></p>"
	info += "<p><strong>1、问题类型:</strong></p>"
	info += fmt.Sprintf("<p>%s</p>", bugData.IssueType)
	info += "<p><br/></p>"
	info += "<p><strong>2、优先级:</strong></p>"
	info += fmt.Sprintf("<p>%s</p>", bugData.Priority)
	info += "<p><br/></p>"
	info += "<p><strong>3、组件信息:</strong></p>"
	info += fmt.Sprintf("<p>%s</p>", bugData.Components)

	if len(bugData.Labels) > 0 {
		info += "<p><br/></p>"
		info += "<p><strong>4、标签:</strong></p>"
		for _, label := range bugData.Labels {
			info += fmt.Sprintf("<p>- %s</p>", label)
		}
	}

	if len(bugData.CustomFields) > 0 {
		info += "<p><br/></p>"
		info += "<p><strong>5、自定义字段:</strong></p>"
		for key, value := range bugData.CustomFields {
			info += fmt.Sprintf("<p>%s: %v</p>", key, value)
		}
	}

	info += "<p><br/></p>"
	info += fmt.Sprintf("<p><strong>创建时间:</strong> %s</p>", time.Now().Format("2006-01-02 15:04:05"))

	return info
}
