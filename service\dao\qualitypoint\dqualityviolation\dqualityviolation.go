package dqualityviolation

import (
	"fmt"
	"strings"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models"
	"irisAdminApi/application/models/qualitypoint"
	"irisAdminApi/service/dao/qualitypoint/dqualitypointproctask"
	"irisAdminApi/service/dao/user/duser"
)

const ModelName = "积分事项表"

type Response struct {
	models.ModelBase
	Direction                 uint                                  `json:"direction" `                                                     //考核方向 1：正向 2：负向
	IssueLevel                uint                                  `json:"issue_level"`                                                    //问题等级
	ImpactDegree              uint                                  `json:"impact_degree"`                                                  //影响程度
	CategoryID                uint                                  `json:"category_id"`                                                    //关联的大类别ID
	AssessmentItemCode        string                                `json:"code"`                                                           //考核项编号
	AssessmentItemID          uint                                  `json:"assessment_item_id" `                                            //考核项ID
	AssessmentItemName        string                                `json:"assessment_item_name"`                                           //考核项名称
	Description               string                                `json:"description"`                                                    //事项描述
	AuditTime                 string                                `json:"audit_time"`                                                     //稽核时间
	ScoreImpact               float32                               `json:"score_impact"`                                                   //扣减/增加分数
	ResponsiblePersonID       uint                                  `json:"responsible_person_id"`                                          //责任角色ID
	AuditManagerID            uint                                  `json:"audit_manager_id"`                                               //违规项审计管理人员ID
	UserID                    uint                                  `json:"user_id"`                                                        //事项提报人ID
	DepartmentManagerID       uint                                  `json:"department_manager_id"`                                          //部门经理ID
	TeamLeaderID              uint                                  `json:"team_leader_id"`                                                 //专业组组长ID
	Department                string                                `json:"department"`                                                     //所在部门
	Team                      string                                `json:"team"`                                                           //所在专业组
	DepartmentID              uint                                  `json:"department_id" form:"department_id"`                             //所在部门ID
	TeamID                    uint                                  `json:"team_id" form:"team_id"`                                         //所在专业组ID
	IsRecurrent               uint                                  `json:"is_recurrent"`                                                   //是否复发 (0: 否, 1: 是)
	IssueDescription          string                                `json:"issue_description" form:"issue_description"`                     //抽象共性问题描述
	IsCommonIssue             uint                                  `json:"is_common_issue" form:"is_common_issue"`                         //共性问题判定(0: 否, 1: 是)
	CommonReviewComments      string                                `json:"common_review_comments" form:"common_review_comments"`           //共性问题评审意见
	ReoccurrenceType          uint                                  `json:"reoccurrence_type" form:"reoccurrence_type"`                     //复发类型 0: 无复发，1：复发-本人，2：复发-专业组内 3:复发-部门内
	DeductTaskDescription     string                                `json:"deduct_task_description" form:"deduct_task_description"`         //消分任务描述
	DeductCompletionTime      string                                `json:"deduct_completion_time" form:"deduct_completion_time"`           //消分任务完成时间
	DeductEvidenceDescription string                                `json:"deduct_evidence_description" form:"deduct_evidence_description"` //消分任务佐证说明
	DeductEvidenceMaterial    string                                `json:"deduct_evidence_material" form:"deduct_evidence_material"`       //消分任务佐证材料
	DeductionPoints           float32                               `json:"deduction_points" form:"deduction_points"`                       //消分分值
	Uuid                      string                                `json:"uuid"`
	Status                    uint                                  `json:"status"` //流程状态
	User                      *duser.ApprovalResponse               `gorm:"-" json:"user"`
	ResponsiblePerson         *duser.ApprovalResponse               `gorm:"-" json:"responsible_person"`
	AuditManager              *duser.ApprovalResponse               `gorm:"-" json:"audit_manager"`
	TeamLeader                *duser.ApprovalResponse               `gorm:"-" json:"team_leader"`
	DepartmentManager         *duser.ApprovalResponse               `gorm:"-" json:"department_manager"`
	Tasks                     []*dqualitypointproctask.ListResponse `gorm:"-" json:"tasks"`
	DoneTasks                 []*dqualitypointproctask.ListResponse `gorm:"-" json:"done_tasks"`

	AbstractCommonTasks        *dqualitypointproctask.Response `gorm:"-" json:"abstract_common_tasks"`
	CommonReview_1_Tasks       *dqualitypointproctask.Response `gorm:"-" json:"common_review_1_tasks"`
	CommonReview_2_Tasks       *dqualitypointproctask.Response `gorm:"-" json:"common_review_2_tasks"`
	RepeatIssueCheckTasks      *dqualitypointproctask.Response `gorm:"-" json:"repeat_issue_check_tasks"`
	DeductPlanTasks            *dqualitypointproctask.Response `gorm:"-" json:"deduct_plan_tasks"`
	DeductReviewTasks          *dqualitypointproctask.Response `gorm:"-" json:"deduct_review_tasks"`
	DeductEvidenceTasks        *dqualitypointproctask.Response `gorm:"-" json:"deduct_evidence_tasks"`
	DeductExecutionReviewTasks *dqualitypointproctask.Response `gorm:"-" json:"deduct_execution_review_tasks"`
}

type ListResponse struct {
	Response
}

type Request struct {
	ID                        uint    `json:"id"`
	UpdatedAt                 string  `json:"updated_at"`
	CreatedAt                 string  `json:"created_at"`
	Direction                 uint    `json:"direction"  from:"direction"`                                    //考核方向 1：负向 2：正向 3:连带
	IssueLevel                uint    `json:"issue_level"  from:"issue_level"`                                //问题等级
	ImpactDegree              uint    `json:"impact_degree"  from:"impact_degree"`                            //影响程度
	CategoryID                uint    `json:"category_id"  form:"category_id"`                                //关联的大类别ID
	AssessmentItemID          uint    `json:"assessment_item_id" form:"assessment_item_id"`                   //考核项ID
	AssessmentItemCode        string  `json:"assessment_item_code" form:"assessment_item_code"`               //考核项编号
	AssessmentItemName        string  `json:"assessment_item_name" form:"assessment_item_name"`               //考核项名称
	Description               string  `json:"description"  from:"description"`                                //事项描述
	AuditTime                 string  `json:"audit_time" form:"audit_time"`                                   //稽核时间
	ScoreImpact               float32 `json:"score_impact" form:"score_impact"`                               //扣减分数
	DeductionPoints           float32 `json:"deduction_points" form:"deduction_points"`                       //消分分值
	ResponsiblePersonID       uint    `json:"responsible_person_id" form:"responsible_person_id"`             //责任角色ID
	AuditManagerID            uint    `json:"audit_manager_id" form:"audit_manager_id"`                       //违规项审计管理人员ID
	UserID                    uint    `json:"user_id" form:"user_id"`                                         //事项提报人ID
	DepartmentManagerID       uint    `json:"department_manager_id" form:"department_manager_id"`             //部门经理ID
	TeamLeaderID              uint    `json:"team_leader_id" form:"team_leader_id"`                           //专业组组长
	Department                string  `json:"department" form:"department"`                                   //所在部门
	Team                      string  `json:"team" form:"team"`                                               //所在专业组
	DepartmentID              uint    `json:"department_id" form:"department_id"`                             //所在部门ID
	TeamID                    uint    `json:"team_id" form:"team_id"`                                         //所在专业组ID
	IsRecurrent               uint    `json:"is_recurrent" form:"is_recurrent"`                               //是否复发 (0: 否, 1: 是)
	IssueDescription          string  `json:"issue_description" form:"issue_description"`                     //抽象共性问题描述
	IsCommonIssue             uint    `json:"is_common_issue" form:"is_common_issue"`                         //共性问题判定(0: 否, 1: 是)
	CommonReviewComments      string  `json:"common_review_comments" form:"common_review_comments"`           //共性问题评审意见
	ReoccurrenceType          uint    `json:"reoccurrence_type" form:"reoccurrence_type"`                     //复发类型 0: 无复发，1：复发-本人，2：复发-专业组内 3:复发-部门内
	DeductTaskDescription     string  `json:"deduct_task_description" form:"deduct_task_description"`         //消分任务描述
	DeductCompletionTime      string  `json:"deduct_completion_time" form:"deduct_completion_time"`           //消分任务完成时间
	DeductEvidenceDescription string  `json:"deduct_evidence_description" form:"deduct_evidence_description"` //消分任务佐证说明
	DeductEvidenceMaterial    string  `json:"deduct_evidence_material" form:"deduct_evidence_material"`       //消分任务佐证材料
	Status                    uint    `json:"status" form:"status"`                                           //流程状态
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *qualitypoint.QualityViolation {
	return &qualitypoint.QualityViolation{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindInIds(ids []uint) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id in ?", ids).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	return items, nil
}

func AllMyDepartmentSubmissionsData(uid, departmentType uint, name, status, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	if len(status) > 0 {
		db = db.Where("status in ?", strings.Split(status, ","))
	}
	if departmentType == 1 {
		db = db.Where("team_leader_id = ?", uid)
	}
	if departmentType == 2 {
		db = db.Where("department_manager_id = ?", uid)
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	formatResponses(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func AllCommonIssueSubmissionsData(uid uint, name, status, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	if len(status) > 0 {
		db = db.Where("status in ?", strings.Split(status, ","))
	}
	db = db.Where("is_common_issue = ?", 1)
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	formatResponses(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}




func formatResponses(items []*ListResponse) {
	procInstIDs := []uint{}
	userIds := []uint{}
	taskIds := []uint{}
	for _, item := range items {
		userIds = append(userIds, item.DepartmentManagerID)
		userIds = append(userIds, item.TeamLeaderID)
		userIds = append(userIds, item.AuditManagerID)
		userIds = append(userIds, item.ResponsiblePersonID)
		procInstIDs = append(procInstIDs, item.ID)
	}

	users, _ := duser.FindSimpleInIds(userIds)
	var userMap = make(map[uint]*duser.ApprovalResponse)
	for _, user := range users {
		userMap[user.Id] = user
	}
	taskMap := map[uint][]*dqualitypointproctask.ListResponse{}
	tasks, err := dqualitypointproctask.FindInProcInstIDs(procInstIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("find tasks err ", err)
		return
	}
	for _, task := range tasks {
		taskMap[task.ProcInstID] = append(taskMap[task.Response.ProcInstID], task)
	}
	//已完成节点
	abstractCommonTasksMap := map[uint]*dqualitypointproctask.Response{}
	commonReview1TasksMap := map[uint]*dqualitypointproctask.Response{}
	commonReview2TasksMap := map[uint]*dqualitypointproctask.Response{}
	repeatIssueCheckTasksMap := map[uint]*dqualitypointproctask.Response{}
	deductPlanTasksMap := map[uint]*dqualitypointproctask.Response{}
	deductReviewTasksMap := map[uint]*dqualitypointproctask.Response{}
	deductEvidenceTasksMap := map[uint]*dqualitypointproctask.Response{}
	deductExecutionReviewTasksMap := map[uint]*dqualitypointproctask.Response{}
	taskDoneMap := map[uint][]*dqualitypointproctask.ListResponse{}
	tasks2, err := dqualitypointproctask.FindInProcInstIDs2(procInstIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("find tasks err ", err)
		return
	}
	for _, task := range tasks2 {
		taskIds = append(taskIds, task.ID)
		if task.NodeID == "abstract_common" {
			abstractCommonTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "common_review_1" {
			commonReview1TasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "common_review_2" {
			commonReview2TasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "repeat_issue_check" {
			repeatIssueCheckTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "deduct_plan" {
			deductPlanTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "deduct_review" {
			deductReviewTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "deduct_evidence" {
			deductEvidenceTasksMap[task.Response.ProcInstID] = &task.Response
		}
		if task.NodeID == "deduct_execution_review" {
			deductExecutionReviewTasksMap[task.Response.ProcInstID] = &task.Response
		}
	}
	tasks3, err := dqualitypointproctask.FindInProcInstIDs3(procInstIDs)
	if err != nil {
		logging.ErrorLogger.Errorf("find tasks err ", err)
		return
	}
	for _, task := range tasks3 {
		taskDoneMap[task.ProcInstID] = append(taskDoneMap[task.Response.ProcInstID], task)
	}
	for _, item := range items {

		item.ResponsiblePerson = userMap[item.ResponsiblePersonID]
		item.AuditManager = userMap[item.AuditManagerID]
		item.TeamLeader = userMap[item.TeamLeaderID]
		item.DepartmentManager = userMap[item.DepartmentManagerID]
		item.Tasks = taskMap[item.ID]
		item.DoneTasks = taskDoneMap[item.ID]

		item.AbstractCommonTasks = abstractCommonTasksMap[item.ID]
		item.CommonReview_1_Tasks = commonReview1TasksMap[item.ID]
		item.CommonReview_2_Tasks = commonReview2TasksMap[item.ID]

		item.RepeatIssueCheckTasks = repeatIssueCheckTasksMap[item.ID]

		item.DeductPlanTasks = deductPlanTasksMap[item.ID]
		item.DeductReviewTasks = deductReviewTasksMap[item.ID]
		item.DeductEvidenceTasks = deductEvidenceTasksMap[item.ID]
		item.DeductExecutionReviewTasks = deductExecutionReviewTasksMap[item.ID]
	}

}
