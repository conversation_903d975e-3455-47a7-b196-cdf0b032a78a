package mergerequest

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/mergerequest/dmergerequest"
	"irisAdminApi/service/dao/user/duserwhitelist"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

var sc = libs.SshClient{}

func SSHClientInit() {
	if sc.SSH == nil || sc.SFTP == nil {
		err := sc.Connect(libs.Config.MergeRequest.OpenSource.Username, libs.Config.MergeRequest.OpenSource.Host, libs.Config.MergeRequest.OpenSource.Port, libs.Config.MergeRequest.OpenSource.Password)
		if err != nil {
			logging.ErrorLogger.Error(err)
		}
	}
}

func GetDirs(ctx iris.Context) {

	SSHClientInit()
	dirs := []string{}

	fis, err := sc.SFTP.ReadDir("/mnt/sata0/data/open-source")
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	for _, fi := range fis {
		if fi.IsDir() {
			dirs = append(dirs, fi.Name())
		}
	}
	dirs = append(dirs, "./")
	ctx.JSON(response.NewResponse(response.NoErr.Code, dirs, response.NoErr.Msg))
	return
}

func CreateOpenSourceUpload(ctx iris.Context) {
	userId, _ := dao.GetAuthId(ctx)
	whitelist := duserwhitelist.UserWhiteList{}
	err := whitelist.FindEx("user_id", fmt.Sprintf("%v", userId))

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	if !whitelist.Opensource {
		ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, response.PermitErr.Msg+": 开源组件上传需要申请权限。"))
		return
	}

	SSHClientInit()
	request := dmergerequest.Request{}
	if err := ctx.ReadForm(&request); err != nil {
		logging.ErrorLogger.Errorf("read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	upload := filepath.Join(libs.CWD(), "open-source", time.Now().Format("2006-01-02"), libs.GetUniqueID())
	err = os.MkdirAll(upload, 0750)
	if err != nil {
		logging.ErrorLogger.Errorf("batch create merge request get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	f, fh, err := ctx.FormFile("file")
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	defer f.Close()

	_, err = ctx.SaveFormFile(fh, filepath.Join(upload, fh.Filename))
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if request.Type == "bugfix" {
		if request.BugID == "" {
			ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "BUGID不能为空"))
			return
		} else {
			for _, bugID := range strings.Split(request.BugID, ",") {
				check, err := CheckBugExists(bugID)
				if err != nil {
					ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()+"请重试或联系管理员。"))
					return
				}
				if !check {
					ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "bug:"+bugID+" 不存在，请确认并重试。"))
					return
				}
			}
		}
	}

	var title string
	switch request.Type {
	case "new":
		// [新增][工作包][TITLE]
		title = fmt.Sprintf("[%s][%s][%s]", TypeMap[request.Type], request.WorkPackage, request.OriginTitle)
	case "bugfix":
		// [BUG修订][BUGID][工作包][TITLE]
		title = fmt.Sprintf("[%s][%s][%s][%s]", TypeMap[request.Type], request.BugID, request.WorkPackage, request.OriginTitle)
	default:
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "变更类型: "+response.ParamentErr.Msg))
		return
	}

	bugid := request.BugID
	if request.BugID == "" {
		bugid = "N/A"
	}

	localBuildPass := "是"
	if !request.LocalBuildPass {
		localBuildPass = "否"
	}
	preCheck := "是"
	if !request.PreCheck {
		preCheck = "否"
	}
	portable := "是"
	if !request.Portable {
		portable = "否"
	}
	// 为了格式正确，请匆修订对齐
	description := fmt.Sprintf(
		`[修订类型]：%s
[BUGID]：%s
[工作包]：%s
[移植]：%s
[本地编译]：%s
[代码检查]：%s
[Phabricator]：%s
包名：%s, md5: %s
%s
`,
		TypeMap[request.Type]+"  ",
		bugid+"  ",
		request.WorkPackage+"  ",
		portable+"  ",
		localBuildPass+"  ",
		preCheck+"  ",
		MfalMap[request.MFAL]+"  ",
		request.FileName,
		request.FileMd5,
		request.OriginDescription+"  ",
	)

	// 批量创建记录表
	objects := []*dmergerequest.MergeRequest{
		{
			ReleaseProjectID: request.ReleaseProjectID,
			ReleaseProject:   request.ReleaseProject,
			WorkPackageID:    request.WorkPackageID,
			WorkPackage:      request.WorkPackage,

			TargetProjectID: request.TargetProjectID,
			TargetProject:   "open-source",
			TargetBranch:    request.TargetBranch,
			MirrorFrom:      "",

			SourceProjectID: request.SourceProjectID,
			SourceProject:   "upload",
			SourceBranch:    filepath.Join(upload, fh.Filename),
			Type:            request.Type,
			BugID:           request.BugID,
			LocalBuildPass:  request.LocalBuildPass,
			Portable:        request.Portable,
			PreCheck:        request.PreCheck,
			MFAL:            request.MFAL,

			Title:              title,
			OriginTitle:        request.OriginTitle,
			Description:        description,
			OriginDescription:  request.OriginDescription,
			UserID:             userId,
			Status:             0,
			MergeRequestID:     0,
			MergeRequestIID:    0,
			PipelineStatus:     0,
			PhabricatorStatus:  0,
			CodeQuantityAdd:    0,
			CodeQuantityRemove: 0,
			ReviewerIDs:        libs.UintJoin(request.ReviewerIDs, ","),
			ReviewerUsernames:  request.ReviewerUsernames,
			AssigneeIDs:        libs.UintJoin(request.AssigneeIDs, ","),
			AssigneeUsernames:  request.AssigneeUsernames,
			Discount:           request.Discount,
		},
	}

	mr := dmergerequest.MergeRequest{}
	err = mr.BatchCreate(objects)
	if err != nil {
		logging.ErrorLogger.Errorf("batch create merge request get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	go CountCodeLines(objects[0])
	go Upload(objects[0], objects[0].SourceBranch, filepath.Join("/mnt/sata0/data/open-source/", objects[0].TargetBranch, fh.Filename))
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func CountCodeLines(mr *dmergerequest.MergeRequest) {
	tempName := libs.GetUniqueID()
	tempDir := filepath.Join("/tmp", tempName)
	command := fmt.Sprintf("rm %s -rf && mkdir -p %s && cd %s && cp %s %s", tempDir, tempDir, tempDir, mr.SourceBranch, tempDir)
	output, err := libs.ExecCommand(command)
	if err != nil {
		logging.ErrorLogger.Error(output, err)
		return
	}
	if strings.HasSuffix(mr.SourceBranch, ".zip") {
		command = fmt.Sprintf("cd %s && unzip %s", tempDir, mr.SourceBranch)
	} else if strings.HasSuffix(mr.SourceBranch, ".tar.gz") {
		command = fmt.Sprintf("cd %s && tar -zxvf %s", tempDir, mr.SourceBranch)
	}
	output, err = libs.ExecCommand(command)
	if err != nil {
		logging.ErrorLogger.Error(output, err)
		return
	}
	command = fmt.Sprintf("cd %s && find ./ -type f|xargs cat|wc -l", tempDir)
	output, err = libs.ExecCommand(command)
	if err != nil {
		logging.ErrorLogger.Error(output, err)
		return
	}
	count, err := strconv.Atoi(strings.TrimSpace(output))
	if err != nil {
		logging.ErrorLogger.Error(err)
		return
	}
	err = mr.Update(mr.ID, map[string]interface{}{"CodeQuantityAdd": uint(count)})
	if err != nil {
		logging.ErrorLogger.Error(err)
	}
}

func Upload(mr *dmergerequest.MergeRequest, src, dst string) {
	SSHClientInit()
	err := sc.UploadFile(src, dst)
	if err != nil {
		err := mr.Update(mr.ID, map[string]interface{}{"Status": 4})
		if err != nil {
			logging.ErrorLogger.Error(err)
		}
	}
	err = mr.Update(mr.ID, map[string]interface{}{"Status": 3})
	if err != nil {
		logging.ErrorLogger.Error(err)
	}
}
