package openfeishu

import (
	"irisAdminApi/application/controllers/openfeishu/coredump"
	"irisAdminApi/application/controllers/openfeishu/meeting/controllers"

	"github.com/kataras/iris/v12"
)

// Party 飞书开放平台路由配置
var Party = func(party iris.Party) {
	// 初始化会议控制器
	meetingController := controllers.NewMeetingController()
	// 会议处理相关路由
	party.Get("/meeting/process", meetingController.ProcessMeetings).Name = "处理会议数据"

	// 初始化Coredump控制器
	coredumpController := coredump.NewCoredumpController()

	// Coredump手动触发相关路由
	party.Post("/coredump/process", coredumpController.ProcessCoredumps).Name = "手动触发Coredump处理"
	party.Get("/coredump/status", coredumpController.GetProcessingStatus).Name = "获取Coredump处理状态"
	party.Get("/coredump/history", coredumpController.GetProcessingHistory).Name = "获取Coredump处理历史"

	// Coredump定时任务管理路由
	party.Post("/coredump/scheduler/start", coredumpController.StartScheduler).Name = "启动Coredump定时任务"
	party.Post("/coredump/scheduler/stop", coredumpController.StopScheduler).Name = "停止Coredump定时任务"
	party.Get("/coredump/scheduler/status", coredumpController.GetSchedulerStatus).Name = "获取Coredump定时任务状态"

	// Coredump系统管理路由
	party.Get("/coredump/system/info", coredumpController.GetSystemInfo).Name = "获取Coredump系统信息"
	party.Post("/coredump/system/reset", coredumpController.ResetLastRunTime).Name = "重置Coredump最后运行时间"

	// 测试bug请求
	party.Get("/bug/submit", TestBugSubmit).Name = "提交Bug"
}
