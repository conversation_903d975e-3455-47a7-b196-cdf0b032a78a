package todo

import (
	"fmt"
	"irisAdminApi/application/controllers/buildfarm"
	"irisAdminApi/application/controllers/openfeishu"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/coverityresult/dcoverityresult"
	"irisAdminApi/service/dao/datasync/dsyncrecord"
	"irisAdminApi/service/dao/mergerequest/dmergerequest"
	"irisAdminApi/service/dao/mergerequest/dmergerequestbugsummary"
	"irisAdminApi/service/dao/release/dreleaseprojectconfig"
	"irisAdminApi/service/dao/user/duser"

	"github.com/pkg/errors"
)

type TodoInstance struct {
	Name                      string                                   `json:"name"`
	OpenID                    string                                   `json:"open_id"`
	Mail                      string                                   `json:"mail"`
	UserCoverityTodoTotal     int                                      `json:"coverity_todo_total"`
	UserCoverityTodoSummary   []*dcoverityresult.CoverityResultSummary `json:"coverity_todo_summary"`
	WarningTodoTotal          int                                      `json:"warning_todo_total"`
	UserMergeRequestTodoTotal int                                      `json:"user_merge_request_todo_total"`
	UserMergeRequestTodo      []*dmergerequest.MergeRequest            `json:"user_merge_request_todo"`

	UserBugTodoTotal               int                            `json:"user_bug_todo_total"`
	UserBugBeforeOvertimeTodoTotal int                            `json:"user_bug_todo_before_overtime_total"`
	UserBugOvertimeTodoTotal       int                            `json:"user_bug_todo_overtime_total"`
	UserBugTodo                    []*dmergerequestbugsummary.Bug `json:"user_bug_todo"`
	UpdatedAt                      string                         `json:"updated_at"`

	PstlMergeRequestTodoTotal int                           `json:"pstl_merge_request_todo_total"`
	PstlMergeRequestTodo      []*dmergerequest.MergeRequest `json:"pstl_merge_request_todo"`

	Erros []error `json:"errors"`
}

func (i *TodoInstance) Init(name, openID, mail, updatedAt string) {
	i.Name = name
	i.OpenID = openID
	i.Mail = mail
	i.UpdatedAt = updatedAt
	if libs.Config.Debug {
		i.OpenID = "ou_844642732286f2ae9e7c8885d9757696"
		i.Mail = "<EMAIL>"
	}
}

func (i *TodoInstance) Send() {
	i.SendFeishuMsg()
	i.SendMailMsg()
}

func (i *TodoInstance) SendFeishuMsg() {
	msg := fmt.Sprintf(`
<b>【待办清单】</b>
%s
%s
%s
%s`,
		i.FormatFeishuBugMsg(),
		i.FormatFeishuMergeReqeustMsg(),
		i.FormatFeishuCoverityMsg(),
		i.FormatFeishuWarningMsg(),
	)
	resp, err := openfeishu.SendMessage(i.OpenID, msg)
	if err != nil {
		logging.ErrorLogger.Errorf("待办飞书通知发送失败 %s %v %v %v %v", resp.Code, resp.Msg, resp.RequestId(), err)
	}
}

func (i *TodoInstance) SendMailMsg() {
	subject := fmt.Sprintf("【待办清单】")
	msg := fmt.Sprintf(`
<h3>【待办清单】</h3>
%s
%s
%s
%s`,
		i.FormatMailBugMsg(),
		i.FormatMailMergeReqeustMsg(),
		i.FormatMailCoverityMsg(),
		i.FormatMailWarningMsg(),
	)
	err := libs.SendMail([]string{i.Mail}, subject, msg, []string{})
	if err != nil {
		logging.ErrorLogger.Errorf("待办邮件发送失败 %v", err)
	}
}

func (i *TodoInstance) FormatFeishuWarningMsg() string {
	msg := fmt.Sprintf(`
<b>编译warning:</b>
未修订编译warning数: %d
详情查看：[编译WARNING整改]("https://ruijie.feishu.cn/base/KZQCbsWefa5e3MsamMccOYYPnMr?table=tblpZxL2Su2Q202U&view=vewPllxSES")`, i.WarningTodoTotal)
	return msg
}

func (i *TodoInstance) FormatMailWarningMsg() string {
	msg := fmt.Sprintf(`
<h4>编译warning:</h4>
未修订编译warning数: %d
详情查看: <a href="https://ruijie.feishu.cn/base/KZQCbsWefa5e3MsamMccOYYPnMr?table=tblpZxL2Su2Q202U&view=vewPllxSES">编译WARNING整改</a>`, i.WarningTodoTotal)
	return msg
}

func (i *TodoInstance) FormatFeishuMergeReqeustMsg() string {
	msg := fmt.Sprintf(`
<b>MR表单:</b>
未合并MR表单数: %d`, i.UserMergeRequestTodoTotal)

	return msg
}

func (i *TodoInstance) FormatMailMergeReqeustMsg() string {
	msg := fmt.Sprintf(`
<h4>MR表单:</h4>
未合并MR表单数: %d`, i.UserMergeRequestTodoTotal)
	return msg
}

func (i *TodoInstance) FormatFeishuBugMsg() string {
	msg := fmt.Sprintf(`
<b>Bug:</b>
<i>基于%s同步的BUG数据</i>
未决BUG数: %d, 即将超期BUG数: %d, 超期BUG数: %d`,
		i.UpdatedAt,
		i.WarningTodoTotal,
		i.UserBugBeforeOvertimeTodoTotal,
		i.UserBugOvertimeTodoTotal)
	return msg
}

func (i *TodoInstance) FormatMailBugMsg() string {
	msg := fmt.Sprintf(`
<h4>Bug:</h4>
基于%s同步的BUG数据</br>
未决BUG数: %d, 即将超期BUG数: %d, 超期BUG数: %d`,
		i.UpdatedAt,
		i.WarningTodoTotal,
		i.UserBugBeforeOvertimeTodoTotal,
		i.UserBugOvertimeTodoTotal)
	return msg
}

func (i *TodoInstance) FormatFeishuCoverityMsg() string {
	msg := fmt.Sprintf(`
<b>Coverity待分析: %d  (检查规则: EPG规则)</b>
`,
		i.UserCoverityTodoTotal)
	for _, item := range i.UserCoverityTodoSummary {
		msg = fmt.Sprintf(
			`%s项目:%s 产品:%s 组件: %s 数量: %d
`, msg, item.Project, item.Product, item.ComponentName, item.Total)
	}
	return msg
}

func (i *TodoInstance) FormatMailCoverityMsg() string {
	msg := fmt.Sprintf(`
<h4>Coverity待分析: %d (检查规则: EPG规则)</h4>`,
		i.UserCoverityTodoTotal)

	for _, item := range i.UserCoverityTodoSummary {
		msg = fmt.Sprintf(`%s<p>项目:%s 产品:%s 组件: %s 数量: %d</p>`, msg, item.Project, item.Product, item.ComponentName, item.Total)
	}
	return msg
}

func SendTodoWorker() error {
	users, err := duser.All()
	if err != nil {
		return errors.Wrap(err, "")
	}

	var updatedAt string
	_url := "https://dataware.ruijie.com.cn/api/public/data-api/security_product_sw_bug/list.data"
	records, err := dsyncrecord.FindLastSuccessSyncRecord(_url)
	if err != nil {
		logging.ErrorLogger.Errorf("get last sync records", err.Error())
		return err
	}
	if len(records) > 0 {
		updatedAt = records[0].CreatedAt.Format("2006-01-02 15:04:05")
	}

	todoInstanceMap := map[string]*TodoInstance{}

	for _, user := range users {
		instance := &TodoInstance{}
		instance.Init(user.Name, user.OpenID, fmt.Sprintf("%<EMAIL>", user.Username), updatedAt)
		todoInstanceMap[user.Name] = instance
	}

	WarningTodo(todoInstanceMap)
	MergeRquestTodo(todoInstanceMap)
	BugTodo(todoInstanceMap)
	CoverityTodo(todoInstanceMap)
	SendTodo(todoInstanceMap)
	return nil
}

func SendTodo(todoInstanceMap map[string]*TodoInstance) error {
	for _, ins := range todoInstanceMap {
		if ins.UserMergeRequestTodoTotal == 0 && ins.UserBugTodoTotal == 0 && ins.WarningTodoTotal == 0 && ins.UserCoverityTodoTotal == 0 {
			continue
		}

		if len(ins.OpenID) > 0 {
			ins.SendFeishuMsg()
		}
		if len(ins.Mail) > 0 {
			ins.SendMailMsg()
		}
	}

	return nil
}

func WarningTodo(todoInstanceMap map[string]*TodoInstance) error {
	records, err := buildfarm.GetWarningSummaryFromFeishu()
	if err != nil {
		return errors.Wrap(err, "")
	}

	for _, record := range records {
		if record["Status"].(string) != "已修订" && record["Status"].(string) != "无需修订" {
			owner := record["Owner"].(string)
			if instance, ok := todoInstanceMap[owner]; ok {
				instance.WarningTodoTotal++
			}
		}
	}

	// 	for user := range userWarningCountMap {
	// 		if userWarningCountMap[user] > 0 {
	// 			if instance, ok := todoInstanceMap[user]; ok {
	// 				msg := fmt.Sprintf(`
	// <b>编译warning:</b>
	// 你所负责的组件当前存在编译农场warning共%d未处理,请及时处理。[编译WARNING整改]("https://ruijie.feishu.cn/base/KZQCbsWefa5e3MsamMccOYYPnMr?table=tblpZxL2Su2Q202U&view=vewPllxSES")`, userWarningCountMap[user])
	// 				instance.WarningTodoTotal = msg
	// 				// resp, err := openfeishu.SendMessage(openID, msg)
	// 				// if err != nil {
	// 				// 	logging.ErrorLogger.Errorf("待办飞书通知发送失败 %v %v %v %v", resp.Code, resp.Msg, resp.RequestId(), err)
	// 				// }

	// 			}
	// 			if mail, ok := userMailMap[user]; ok {
	// 				subject := fmt.Sprintf("【待办清单】")
	// 				msg := fmt.Sprintf(`
	// <h4>编译warning:</h4>
	// 你所负责的组件当前存在编译农场warning共%d未处理,请及时处理。<a href="https://ruijie.feishu.cn/base/KZQCbsWefa5e3MsamMccOYYPnMr?table=tblpZxL2Su2Q202U&view=vewPllxSES">编译WARNING整改</a>`, userWarningCountMap[user])

	// 				err := libs.SendMail([]string{mail}, subject, msg, []string{})
	// 				if err != nil {
	// 					logging.ErrorLogger.Errorf("待办邮件发送失败 %v", err)
	// 				}

	// 			}
	// 		}
	// }

	return nil
}

func MergeRquestTodo(todoInstanceMap map[string]*TodoInstance) error {
	mrs, err := dmergerequest.MergeRequestFilterNotMerged()
	if err != nil {
		logging.ErrorLogger.Error("find mr error", err)
	}

	for _, mr := range mrs {
		if mr.RelateWorkPackage.Pstl.ID != 0 {
			if instance, ok := todoInstanceMap[mr.RelateWorkPackage.Pstl.Name]; ok {
				instance.PstlMergeRequestTodo = append(instance.PstlMergeRequestTodo, mr)
				instance.PstlMergeRequestTodoTotal++
			}
		}
		if instance, ok := todoInstanceMap[mr.User.Name]; ok {
			instance.UserMergeRequestTodo = append(instance.UserMergeRequestTodo, mr)
			instance.UserMergeRequestTodoTotal++
		}
	}
	return nil
}

func BugTodo(todoInstanceMap map[string]*TodoInstance) error {
	bugs, err := dmergerequestbugsummary.GetBugOwnerTodo([]string{}, "asc", "before_over_time")
	if err != nil {
		return err
	}

	for _, bug := range bugs {
		if instance, ok := todoInstanceMap[bug.BugOwner]; ok {
			instance.UserBugTodo = append(instance.UserBugTodo, bug)
			instance.UserBugTodoTotal++
			if bug.BeforeOverTime <= 2 && bug.BeforeOverTime >= 0 {
				instance.UserBugBeforeOvertimeTodoTotal++
			} else if bug.OverTime > 0 {
				instance.UserBugOvertimeTodoTotal++
			}
		}
	}

	return nil
}

func CoverityTodo(todoInstanceMap map[string]*TodoInstance) error {
	projects := []string{}
	enabledNoticeProjects, err := dreleaseprojectconfig.AllEnabledConfig()
	if err != nil {
		logging.ErrorLogger.Error("get project config err ", err)
		return err
	}
	for _, config := range enabledNoticeProjects {
		projects = append(projects, config.ReleaseProject.Name)
	}

	summary, err := dcoverityresult.Summary(projects)
	if err != nil {
		logging.ErrorLogger.Error("get coverity summary err ", err)
		return err
	}

	for _, item := range summary {
		if instance, ok := todoInstanceMap[item.Owner]; ok {
			instance.UserCoverityTodoSummary = append(instance.UserCoverityTodoSummary, item)
			instance.UserCoverityTodoTotal = instance.UserCoverityTodoTotal + int(item.Total)
		}
	}
	return nil
}

func TodoScheduler() {
	err := SendTodoWorker()
	if err != nil {
		logging.ErrorLogger.Errorf("发送待办作业失败", err)
	}
}
