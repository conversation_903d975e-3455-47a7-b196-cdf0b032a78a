package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"irisAdminApi/application/controllers/openfeishu/meeting/libs"
	"irisAdminApi/application/controllers/openfeishu/meeting/models"
	"irisAdminApi/application/logging"

	lark "github.com/larksuite/oapi-sdk-go/v3"
)

// CalendarService 日历服务
type CalendarService struct {
	client              *lark.Client
	apiClient           *libs.APIClient
	permissionValidator *libs.PermissionValidator
	errorHandler        *libs.FeishuErrorHandler
	config              *models.ServiceConfig
}

// NewCalendarService 创建日历服务实例
func NewCalendarService(config *models.ServiceConfig) *CalendarService {
	// 复用现有飞书客户端初始化
	client := lark.NewClient(config.AppID, config.AppSecret)

	// 设置默认值
	if config.DefaultCalendarID == "" {
		config.DefaultCalendarID = models.DefaultCalendarID
	}
	if config.DefaultTimezone == "" {
		config.DefaultTimezone = models.DefaultTimezone
	}

	service := &CalendarService{
		client:              client,
		apiClient:           libs.NewAPIClient(),
		permissionValidator: libs.NewPermissionValidator(),
		errorHandler:        libs.NewFeishuErrorHandler(),
		config:              config,
	}

	logging.InfoLogger.Infof("日历服务初始化完成 - 默认日历ID: %s, 默认时区: %s",
		config.DefaultCalendarID, config.DefaultTimezone)

	return service
}

// CreateEvent 创建日历事件
func (c *CalendarService) CreateEvent(ctx context.Context, meetingData *models.StandardMeetingData) (*models.CalendarEvent, error) {
	if meetingData == nil {
		return nil, fmt.Errorf("会议数据不能为空")
	}

	logging.InfoLogger.Infof("开始创建日程: %s", meetingData.Title)

	// 使用优化的参数验证
	if err := c.validateCreateEventParams(meetingData); err != nil {
		return nil, fmt.Errorf("参数验证失败: %w", err)
	}

	// 获取访问令牌
	accessToken, err := c.getAccessToken(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("获取访问令牌失败: %v", err)
		return nil, fmt.Errorf("获取访问令牌失败: %w", err)
	}

	// 权限验证
	if err := c.validatePermissions(ctx, accessToken); err != nil {
		return nil, fmt.Errorf("权限验证失败: %w", err)
	}

	// 实现真实的飞书日历API调用
	logging.InfoLogger.Infof("准备调用飞书日历API创建日程: %s", meetingData.Title)

	// 🔧 修复：使用 buildCreateEventRequest 方法构建请求体（包含视频会议设置）
	createEventReq, err := c.buildCreateEventRequest(meetingData)
	if err != nil {
		return nil, fmt.Errorf("构建创建事件请求失败: %w", err)
	}

	// 将结构化请求转换为 map[string]interface{} 以便API调用
	requestBodyBytes, err := json.Marshal(createEventReq)
	if err != nil {
		return nil, fmt.Errorf("序列化请求体失败: %w", err)
	}

	var requestBody map[string]interface{}
	if err := json.Unmarshal(requestBodyBytes, &requestBody); err != nil {
		return nil, fmt.Errorf("反序列化请求体失败: %w", err)
	}

	// 设置参与人权限为最高级别，允许参会人员修改日程
	requestBody["attendee_ability"] = "can_modify_event"

	// 🔧 调试：记录完整的请求体，特别关注视频会议设置
	if vchatData, exists := requestBody["vchat"]; exists {
		logging.InfoLogger.Infof("视频会议设置已包含在请求中: %+v", vchatData)
	} else {
		logging.ErrorLogger.Errorf("警告：请求体中缺少视频会议设置")
	}

	// 动态获取真实的日历ID
	var calendarID string
	if c.config.DefaultCalendarID == "primary" || c.config.DefaultCalendarID == "" {
		// 如果配置的是"primary"或为空，则动态获取真实的主日历ID
		calendarID, err = c.getPrimaryCalendarID(ctx, accessToken)
		if err != nil {
			logging.ErrorLogger.Errorf("获取主日历ID失败: %v", err)
			return nil, fmt.Errorf("获取主日历ID失败: %w", err)
		}
		logging.InfoLogger.Infof("动态获取到日历ID: %s", calendarID)
	} else {
		// 使用配置的日历ID
		calendarID = c.config.DefaultCalendarID
		logging.InfoLogger.Infof("使用配置的日历ID: %s", calendarID)
	}

	// 调用飞书日历API
	apiURL := fmt.Sprintf("https://open.feishu.cn/open-apis/calendar/v4/calendars/%s/events?user_id_type=open_id", calendarID)
	eventID, err := c.callCreateEventAPI(ctx, apiURL, accessToken, requestBody)
	if err != nil {
		logging.ErrorLogger.Errorf("飞书日历API调用失败: %v", err)
		return nil, fmt.Errorf("创建日程失败: %w", err)
	}

	// 记录成功信息
	logging.InfoLogger.Infof("飞书日历API调用成功，真实EventID: %s", eventID)

	// 构建返回的事件对象
	event := &models.CalendarEvent{
		EventID:     eventID,
		CalendarID:  c.config.DefaultCalendarID,
		Title:       meetingData.Title,
		Description: meetingData.Description,
		StartTime:   meetingData.StartTime,
		EndTime:     meetingData.EndTime,
		Location:    meetingData.Location,
		Attendees:   meetingData.AttendeeOpenIDs,
		IsAllDay:    meetingData.IsAllDay,
		Timezone:    meetingData.Timezone,
		EventURL:    c.generateEventURL(eventID),
	}

	logging.InfoLogger.Infof("成功创建日程: %s, EventID: %s", meetingData.Title, event.EventID)

	// 如果有参会人员，添加参会人员
	if len(meetingData.AttendeeOpenIDs) > 0 {
		if err := c.AddAttendees(ctx, event.EventID, meetingData.AttendeeOpenIDs); err != nil {
			logging.ErrorLogger.Errorf("添加参会人员失败: %v", err)
			// 不返回错误，因为日程已经创建成功
		}
	}

	return event, nil
}

// AddAttendees 添加参会人员
func (c *CalendarService) AddAttendees(ctx context.Context, eventID string, attendeeOpenIDs []string) error {
	if len(attendeeOpenIDs) == 0 {
		return nil
	}

	logging.InfoLogger.Infof("添加参会人员到日程 %s，人数: %d，参会人员: %v", eventID, len(attendeeOpenIDs), attendeeOpenIDs)

	// 构建参会人员数据
	attendees := make([]map[string]interface{}, 0, len(attendeeOpenIDs))
	for _, openID := range attendeeOpenIDs {
		if openID != "" {
			attendee := map[string]interface{}{
				"type":        "user",
				"user_id":     openID,
				"is_optional": false,
			}
			attendees = append(attendees, attendee)

		}
	}

	if len(attendees) == 0 {
		logging.InfoLogger.Info("无有效参会人员，跳过添加")
		return nil
	}

	// 构建API请求数据
	requestData := map[string]interface{}{
		"attendees":         attendees,
		"need_notification": false, // 不发送Bot通知给参会人员
	}

	// 获取访问令牌
	accessToken, err := c.getAccessToken(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("获取访问令牌失败: %v", err)
		return fmt.Errorf("获取访问令牌失败: %w", err)
	}

	// 动态获取日历ID（与创建事件时保持一致）
	var calendarID string
	if c.config.DefaultCalendarID == "primary" || c.config.DefaultCalendarID == "" {
		calendarID, err = c.getPrimaryCalendarID(ctx, accessToken)
		if err != nil {
			logging.ErrorLogger.Errorf("获取主日历ID失败: %v", err)
			return fmt.Errorf("获取主日历ID失败: %w", err)
		}
	} else {
		calendarID = c.config.DefaultCalendarID
	}

	// 调用飞书日历API添加参会人员
	apiURL := fmt.Sprintf("https://open.feishu.cn/open-apis/calendar/v4/calendars/%s/events/%s/attendees?user_id_type=open_id", calendarID, eventID)
	err = c.callAddAttendeesAPI(ctx, apiURL, accessToken, requestData)
	if err != nil {
		logging.ErrorLogger.Errorf("飞书日历添加参会人员API调用失败: %v", err)
		return fmt.Errorf("添加参会人员失败: %w", err)
	}

	logging.InfoLogger.Infof("成功添加 %d 名参会人员到日程 %s", len(attendees), eventID)
	return nil
}

// RemoveAttendeesByAttendeeIDs 通过attendee_id删除参会人员（修复版本）
func (c *CalendarService) RemoveAttendeesByAttendeeIDs(ctx context.Context, eventID string, attendeeIDs []string) error {
	if len(attendeeIDs) == 0 {
		return nil
	}

	logging.InfoLogger.Infof("从日程 %s 删除参会人员，参与人ID数量: %d", eventID, len(attendeeIDs))

	// 验证参与人ID
	validAttendeeIDs := make([]string, 0, len(attendeeIDs))
	for _, attendeeID := range attendeeIDs {
		if attendeeID != "" {
			validAttendeeIDs = append(validAttendeeIDs, attendeeID)

		}
	}

	if len(validAttendeeIDs) == 0 {
		logging.InfoLogger.Info("无有效参与人ID，跳过删除")
		return nil
	}

	// 构建删除请求数据（使用正确的attendee_ids格式）
	requestData := map[string]interface{}{
		"attendee_ids":      validAttendeeIDs,
		"need_notification": true, // 发送通知
	}

	// 获取访问令牌和日历ID
	accessToken, calendarID, err := c.getAccessTokenAndCalendarID(ctx)
	if err != nil {
		return err
	}

	// 调用飞书日历API删除参会人员
	apiURL := fmt.Sprintf("https://open.feishu.cn/open-apis/calendar/v4/calendars/%s/events/%s/attendees/batch_delete?user_id_type=open_id", calendarID, eventID)
	err = c.callRemoveAttendeesAPI(ctx, apiURL, accessToken, requestData)
	if err != nil {
		logging.ErrorLogger.Errorf("飞书日历删除参会人员API调用失败: %v", err)
		return fmt.Errorf("删除参会人员失败: %w", err)
	}

	logging.InfoLogger.Infof("成功删除 %d 名参会人员从日程 %s", len(validAttendeeIDs), eventID)
	return nil
}

// AttendeeInfo 参会人员信息
type AttendeeInfo struct {
	AttendeeID string `json:"attendee_id"` // 参与人ID（用于删除操作）
	UserID     string `json:"user_id"`     // 用户ID（用于业务逻辑）
	Type       string `json:"type"`        // 参与人类型
	RsvpStatus string `json:"rsvp_status"` // RSVP状态（用于过滤已删除的参会人员）
}

// GetEventAttendees 获取日程的当前参会人员列表
func (c *CalendarService) GetEventAttendees(ctx context.Context, eventID string) ([]string, error) {
	logging.InfoLogger.Infof("获取日程 %s 的参会人员列表", eventID)

	// 获取访问令牌和日历ID
	accessToken, calendarID, err := c.getAccessTokenAndCalendarID(ctx)
	if err != nil {
		return nil, err
	}

	// 调用飞书API获取参会人员列表
	apiURL := fmt.Sprintf("https://open.feishu.cn/open-apis/calendar/v4/calendars/%s/events/%s/attendees?user_id_type=open_id&page_size=100", calendarID, eventID)

	req, err := http.NewRequestWithContext(ctx, "GET", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建获取参会人员请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Authorization", "Bearer "+accessToken)

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送获取参会人员请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取获取参会人员响应失败: %w", err)
	}

	// 解析响应
	var apiResp struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			Items []struct {
				Type        string `json:"type"`
				UserID      string `json:"user_id"`
				ChatID      string `json:"chat_id"`
				RoomID      string `json:"room_id"`
				Email       string `json:"third_party_email"`
				RsvpStatus  string `json:"rsvp_status"`  // 新增：RSVP状态字段
				AttendeeID  string `json:"attendee_id"`  // 新增：参会人员ID
				IsOptional  bool   `json:"is_optional"`  // 新增：是否可选参会人员
				IsOrganizer bool   `json:"is_organizer"` // 新增：是否组织者
			} `json:"items"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, fmt.Errorf("解析获取参会人员响应失败: %w", err)
	}

	// 检查API响应
	if apiResp.Code != 0 {
		return nil, fmt.Errorf("飞书获取参会人员API返回错误: %s (code: %d)", apiResp.Msg, apiResp.Code)
	}

	// 提取有效状态的用户类型参会人员OpenID
	var attendeeOpenIDs []string
	var totalCount, validCount, filteredCount int

	for _, item := range apiResp.Data.Items {
		if item.Type == "user" && item.UserID != "" {
			totalCount++

			// 🔧 修复：过滤有效的参会人员状态
			if c.isValidAttendeeStatus(item.RsvpStatus) {
				attendeeOpenIDs = append(attendeeOpenIDs, item.UserID)
				validCount++
			} else {
				filteredCount++
				logging.DebugLogger.Debugf("过滤掉无效状态的参会人员: UserID=%s, Status=%s",
					item.UserID, item.RsvpStatus)
			}
		}
	}

	logging.InfoLogger.Infof("日程 %s 参会人员统计 - 总数: %d, 有效: %d, 已过滤: %d",
		eventID, totalCount, validCount, filteredCount)
	return attendeeOpenIDs, nil
}

// isValidAttendeeStatus 检查参会人员状态是否有效
func (c *CalendarService) isValidAttendeeStatus(rsvpStatus string) bool {
	// 定义有效的参会人员状态
	validStatuses := map[string]bool{
		"accepted":     true, // 已接受
		"tentative":    true, // 待定
		"needs_action": true, // 需要操作（默认状态）
		"":             true, // 空状态（默认为有效）
	}

	// 定义无效的参会人员状态
	invalidStatuses := map[string]bool{
		"declined": true, // 已拒绝
		"removed":  true, // 已移除
		"deleted":  true, // 已删除
	}

	// 优先检查无效状态
	if invalidStatuses[rsvpStatus] {
		return false
	}

	// 检查有效状态，如果不在有效列表中但也不在无效列表中，默认为有效
	return validStatuses[rsvpStatus] || (!invalidStatuses[rsvpStatus])
}

// GetEventAttendeesDetailed 获取日程的详细参会人员信息（包含attendee_id）
func (c *CalendarService) GetEventAttendeesDetailed(ctx context.Context, eventID string) ([]AttendeeInfo, error) {
	logging.InfoLogger.Infof("获取日程 %s 的详细参会人员信息", eventID)

	// 添加重试机制以处理可能的数据同步延迟
	maxRetries := 3
	for attempt := 1; attempt <= maxRetries; attempt++ {
		if attempt > 1 {
			logging.InfoLogger.Infof("第 %d 次尝试获取日程 %s 的参会人员信息", attempt, eventID)
			time.Sleep(time.Duration(attempt) * time.Second) // 递增延迟
		}

		attendeeInfos, err := c.getEventAttendeesDetailedOnce(ctx, eventID)
		if err != nil {
			if attempt == maxRetries {
				return nil, err
			}
			logging.ErrorLogger.Errorf("第 %d 次获取参会人员信息失败: %v，将重试", attempt, err)
			continue
		}

		logging.InfoLogger.Infof("第 %d 次尝试成功获取到 %d 名参会人员", attempt, len(attendeeInfos))
		return attendeeInfos, nil
	}

	return nil, fmt.Errorf("获取参会人员信息失败，已重试 %d 次", maxRetries)
}

// getEventAttendeesDetailedOnce 单次获取日程的详细参会人员信息
func (c *CalendarService) getEventAttendeesDetailedOnce(ctx context.Context, eventID string) ([]AttendeeInfo, error) {

	// 获取访问令牌
	accessToken, err := c.getAccessToken(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("获取访问令牌失败: %v", err)
		return nil, fmt.Errorf("获取访问令牌失败: %w", err)
	}

	// 获取日历ID
	calendarID, err := c.getCalendarID(ctx, accessToken)
	if err != nil {
		return nil, fmt.Errorf("获取日历ID失败: %w", err)
	}

	// 调用飞书API获取参会人员列表
	apiURL := fmt.Sprintf("https://open.feishu.cn/open-apis/calendar/v4/calendars/%s/events/%s/attendees?user_id_type=open_id&page_size=100", calendarID, eventID)

	req, err := http.NewRequestWithContext(ctx, "GET", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建获取参会人员请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Authorization", "Bearer "+accessToken)

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送获取参会人员请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取获取参会人员响应失败: %w", err)
	}

	// 解析响应
	var apiResp struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			Items []struct {
				Type        string `json:"type"`
				AttendeeID  string `json:"attendee_id"`
				RsvpStatus  string `json:"rsvp_status"` // 关键字段：参与人RSVP状态
				UserID      string `json:"user_id"`
				ChatID      string `json:"chat_id"`
				RoomID      string `json:"room_id"`
				Email       string `json:"third_party_email"`
				IsOptional  bool   `json:"is_optional"`
				IsOrganizer bool   `json:"is_organizer"`
				IsExternal  bool   `json:"is_external"`
				DisplayName string `json:"display_name"`
			} `json:"items"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, fmt.Errorf("解析获取参会人员响应失败: %w", err)
	}

	// 检查API响应
	if apiResp.Code != 0 {
		return nil, fmt.Errorf("飞书获取参会人员API返回错误: %s (code: %d)", apiResp.Msg, apiResp.Code)
	}

	// 提取有效状态的参会人员信息
	var attendeeInfos []AttendeeInfo
	var totalCount, validCount, filteredCount int

	for _, item := range apiResp.Data.Items {
		if item.Type == "user" && item.UserID != "" && item.AttendeeID != "" {
			totalCount++

			// 🔧 修复：使用统一的状态过滤逻辑
			if c.isValidAttendeeStatus(item.RsvpStatus) {
				attendeeInfo := AttendeeInfo{
					AttendeeID: item.AttendeeID,
					UserID:     item.UserID,
					Type:       item.Type,
					RsvpStatus: item.RsvpStatus,
				}
				attendeeInfos = append(attendeeInfos, attendeeInfo)
				validCount++
			} else {
				filteredCount++
				logging.DebugLogger.Debugf("过滤掉无效状态的详细参会人员: UserID=%s, AttendeeID=%s, Status=%s",
					item.UserID, item.AttendeeID, item.RsvpStatus)
			}
		}
	}

	logging.InfoLogger.Infof("日程 %s 详细参会人员统计 - 总数: %d, 有效: %d, 已过滤: %d",
		eventID, totalCount, validCount, filteredCount)
	logging.DebugLogger.Debugf("日程 %s 有效详细参会人员信息: %v", eventID, attendeeInfos)
	return attendeeInfos, nil
}

// SyncEventAttendees 同步日程参会人员到目标状态
func (c *CalendarService) SyncEventAttendees(ctx context.Context, eventID string, targetAttendees []string) error {
	logging.InfoLogger.Infof("开始同步日程 %s 的参会人员到目标状态，目标人数: %d", eventID, len(targetAttendees))

	// 使用增强的刷新和同步逻辑，处理数据不一致问题
	err := c.refreshAndSyncAttendees(ctx, eventID, targetAttendees)
	if err != nil {
		logging.ErrorLogger.Errorf("刷新并同步参会人员失败: %v", err)
		return err
	}

	logging.InfoLogger.Infof("完成日程 %s 的参会人员同步", eventID)
	return nil
}

// calculateAttendeeDifference 计算参会人员差异
func (c *CalendarService) calculateAttendeeDifference(current, target []string) (toAdd, toRemove []string) {
	// 转换为map以提高查找效率
	currentMap := make(map[string]bool)
	for _, attendee := range current {
		currentMap[attendee] = true
	}

	targetMap := make(map[string]bool)
	for _, attendee := range target {
		targetMap[attendee] = true
	}

	// 计算需要添加的参会人员（在目标中但不在当前中）
	for _, attendee := range target {
		if !currentMap[attendee] {
			toAdd = append(toAdd, attendee)
		}
	}

	// 计算需要删除的参会人员（在当前中但不在目标中）
	for _, attendee := range current {
		if !targetMap[attendee] {
			toRemove = append(toRemove, attendee)
		}
	}

	return toAdd, toRemove
}

// validateAttendeeDataConsistency 验证参会人员数据一致性
func (c *CalendarService) validateAttendeeDataConsistency(ctx context.Context, eventID string, expectedCount int) (bool, []AttendeeInfo, error) {
	logging.InfoLogger.Infof("验证日程 %s 的参会人员数据一致性，期望人数: %d", eventID, expectedCount)

	// 获取当前参会人员信息
	attendeeInfos, err := c.GetEventAttendeesDetailed(ctx, eventID)
	if err != nil {
		return false, nil, fmt.Errorf("获取参会人员信息失败: %w", err)
	}

	actualCount := len(attendeeInfos)
	isConsistent := actualCount == expectedCount

	logging.InfoLogger.Infof("数据一致性验证结果 - 期望: %d人, 实际: %d人, 一致性: %v",
		expectedCount, actualCount, isConsistent)

	if !isConsistent {
		logging.ErrorLogger.Errorf("参会人员数据不一致！期望 %d 人，实际获取到 %d 人", expectedCount, actualCount)
		logging.ErrorLogger.Errorf("实际参会人员详情: %v", attendeeInfos)
	}

	return isConsistent, attendeeInfos, nil
}

// refreshAndSyncAttendees 刷新并同步参会人员（处理数据不一致问题）
func (c *CalendarService) refreshAndSyncAttendees(ctx context.Context, eventID string, targetAttendees []string) error {
	logging.InfoLogger.Infof("开始刷新并同步日程 %s 的参会人员，目标人数: %d", eventID, len(targetAttendees))

	// 1. 多次验证数据一致性，处理可能的API延迟
	var currentAttendeesInfo []AttendeeInfo
	var err error

	maxValidationAttempts := 3
	for attempt := 1; attempt <= maxValidationAttempts; attempt++ {
		if attempt > 1 {
			logging.InfoLogger.Infof("第 %d 次验证数据一致性", attempt)
			time.Sleep(2 * time.Second) // 等待API数据同步
		}

		currentAttendeesInfo, err = c.GetEventAttendeesDetailed(ctx, eventID)
		if err != nil {
			logging.ErrorLogger.Errorf("第 %d 次获取参会人员信息失败: %v", attempt, err)
			if attempt == maxValidationAttempts {
				return fmt.Errorf("获取参会人员信息失败: %w", err)
			}
			continue
		}

		// 检查数据是否合理（不为空且包含有效数据）
		if len(currentAttendeesInfo) > 0 {
			allValid := true
			for _, info := range currentAttendeesInfo {
				if info.UserID == "" || info.AttendeeID == "" {
					allValid = false
					break
				}
			}
			if allValid {
				logging.InfoLogger.Infof("第 %d 次验证成功，获取到 %d 名有效参会人员", attempt, len(currentAttendeesInfo))
				break
			}
		}

		if attempt < maxValidationAttempts {
			logging.InfoLogger.Infof("第 %d 次获取的数据不完整，将重试", attempt)
		}
	}

	// 2. 提取当前参会人员的用户ID列表
	currentUserIDs := make([]string, len(currentAttendeesInfo))
	for i, info := range currentAttendeesInfo {
		currentUserIDs[i] = info.UserID
	}

	// 详细日志：显示当前和目标参会人员列表
	logging.InfoLogger.Infof("刷新后当前参会人员详细信息: %v", currentAttendeesInfo)
	logging.InfoLogger.Infof("刷新后当前参会人员用户ID列表: %v", currentUserIDs)
	logging.InfoLogger.Infof("目标参会人员用户ID列表: %v", targetAttendees)

	// 3. 计算差异
	toAddUserIDs, toRemoveUserIDs := c.calculateAttendeeDifference(currentUserIDs, targetAttendees)

	logging.InfoLogger.Infof("刷新后参会人员同步计算结果 - 当前: %d人, 目标: %d人, 需添加: %d人, 需删除: %d人",
		len(currentUserIDs), len(targetAttendees), len(toAddUserIDs), len(toRemoveUserIDs))
	logging.InfoLogger.Infof("需要添加的参会人员: %v", toAddUserIDs)
	logging.InfoLogger.Infof("需要删除的参会人员: %v", toRemoveUserIDs)

	// 4. 执行同步操作
	return c.executeSyncOperations(ctx, eventID, currentAttendeesInfo, toAddUserIDs, toRemoveUserIDs)
}

// executeSyncOperations 执行同步操作
func (c *CalendarService) executeSyncOperations(ctx context.Context, eventID string, currentAttendeesInfo []AttendeeInfo, toAddUserIDs, toRemoveUserIDs []string) error {
	// 执行删除操作（先删除再添加，确保最终状态正确）
	if len(toRemoveUserIDs) > 0 {
		// 找到需要删除的参会人员的attendee_id
		var toRemoveAttendeeIDs []string
		for _, userID := range toRemoveUserIDs {
			for _, info := range currentAttendeesInfo {
				if info.UserID == userID {
					toRemoveAttendeeIDs = append(toRemoveAttendeeIDs, info.AttendeeID)
					break
				}
			}
		}

		logging.InfoLogger.Infof("删除参会人员: 用户ID=%v, 参与人ID=%v", toRemoveUserIDs, toRemoveAttendeeIDs)
		if err := c.RemoveAttendeesByAttendeeIDs(ctx, eventID, toRemoveAttendeeIDs); err != nil {
			logging.ErrorLogger.Errorf("删除参会人员失败: %v", err)
			// 记录错误但继续执行添加操作
		}
	}

	// 执行添加操作
	var addError error
	if len(toAddUserIDs) > 0 {
		logging.InfoLogger.Infof("添加参会人员: %v", toAddUserIDs)
		if err := c.AddAttendees(ctx, eventID, toAddUserIDs); err != nil {
			logging.ErrorLogger.Errorf("添加参会人员失败: %v", err)
			addError = fmt.Errorf("添加参会人员失败: %w", err)
			// 记录错误但不回滚删除操作，继续执行后续逻辑
		} else {
			logging.InfoLogger.Infof("成功添加 %d 名参会人员", len(toAddUserIDs))
		}
	}

	// 如果没有变更，记录日志
	if len(toAddUserIDs) == 0 && len(toRemoveUserIDs) == 0 {
		logging.InfoLogger.Infof("参会人员列表无变更，跳过同步操作")
	}

	// 如果添加操作失败，返回错误信息
	if addError != nil {
		return addError
	}

	return nil
}

// buildCreateEventRequest 构建创建事件请求
func (c *CalendarService) buildCreateEventRequest(meetingData *models.StandardMeetingData) (*models.CreateEventRequest, error) {
	// 转换时间为时间戳（秒）
	startTimestamp := strconv.FormatInt(meetingData.StartTime.Unix(), 10)
	endTimestamp := strconv.FormatInt(meetingData.EndTime.Unix(), 10)

	// 确定时区
	timezone := meetingData.Timezone
	if timezone == "" {
		timezone = c.config.DefaultTimezone
	}

	req := &models.CreateEventRequest{
		Summary:          meetingData.Title,
		Description:      meetingData.Description,
		NeedNotification: c.config.AutoNotifyAttendees,
		Timezone:         timezone,
	}

	// 设置开始时间
	if meetingData.IsAllDay {
		req.StartTime = models.EventTime{
			Date:     meetingData.StartTime.Format("2006-01-02"),
			Timezone: "UTC",
		}
		req.EndTime = models.EventTime{
			Date:     meetingData.EndTime.Format("2006-01-02"),
			Timezone: "UTC",
		}
	} else {
		req.StartTime = models.EventTime{
			Timestamp: startTimestamp,
			Timezone:  timezone,
		}
		req.EndTime = models.EventTime{
			Timestamp: endTimestamp,
			Timezone:  timezone,
		}
	}

	// 设置地点
	if meetingData.Location != "" {
		req.Location = &models.EventLocation{
			Name: meetingData.Location,
		}
	}

	// 新增：设置视频会议信息（主持人和自动录制功能）
	req.VChat = &models.VChatInfo{
		VCType: "vc", // 使用飞书视频会议
		MeetingSettings: &models.MeetingSettings{
			// 🔧 修复：根据飞书API文档，设置入会范围为企业内用户
			JoinMeetingPermission: "only_organization_employees",
			// 当使用tenant_access_token（应用身份）创建日程时，组织者为Bot，此时不能设置主持人
			// 必须确保AllowAttendeesStart为true，允许参与者发起会议
			// 开启自动录制
			AutoRecord: true,
			// 其他默认设置
			OpenLobby:           true, // 开启等候室
			AllowAttendeesStart: true, // 允许参与者发起会议（API要求：Bot组织者必须为true）
		},
	}

	logging.InfoLogger.Infof("创建日程时设置视频会议 - 自动录制: %v, 入会权限: %s, 允许参与者发起: %v (注意：Bot组织者无法指定主持人)",
		req.VChat.MeetingSettings.AutoRecord,
		req.VChat.MeetingSettings.JoinMeetingPermission,
		req.VChat.MeetingSettings.AllowAttendeesStart)

	return req, nil
}

// generateEventURL 生成飞书日程链接（日程ID回填增强）
func (c *CalendarService) generateEventURL(eventID string) string {
	return fmt.Sprintf("https://feishu.cn/calendar/event/%s", eventID)
}

// ValidateConfig 验证配置
func (c *CalendarService) ValidateConfig() error {
	if c.config.AppID == "" {
		return fmt.Errorf("AppID 配置不能为空")
	}
	if c.config.AppSecret == "" {
		return fmt.Errorf("AppSecret 配置不能为空")
	}
	if c.config.DefaultCalendarID == "" {
		return fmt.Errorf("默认日历ID不能为空")
	}
	return nil
}

// GetPrimaryCalendar 获取主日历信息
func (c *CalendarService) GetPrimaryCalendar(ctx context.Context) (interface{}, error) {
	return map[string]interface{}{
		"calendar_id": c.config.DefaultCalendarID,
		"name":        "主日历",
	}, nil
}

// BuildProcessResult 构建处理结果（日程ID回填增强）
func (c *CalendarService) BuildProcessResult(recordID string, event *models.CalendarEvent, err error) *models.ProcessResult {
	result := &models.ProcessResult{
		RecordID:    recordID,
		ProcessTime: time.Now(),
	}

	if err != nil {
		result.Status = models.StatusFailed
		result.ErrorMessage = err.Error()
	} else {
		result.Status = models.StatusSuccess
		// 日程ID回填增强：记录飞书日历事件信息
		result.EventID = event.EventID
		result.EventURL = event.EventURL
	}

	return result
}

// getAccessToken 获取飞书访问令牌
func (c *CalendarService) getAccessToken(ctx context.Context) (string, error) {
	// 使用HTTP直接调用获取访问令牌API
	tokenURL := "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"

	requestBody := map[string]string{
		"app_id":     c.config.AppID,
		"app_secret": c.config.AppSecret,
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return "", fmt.Errorf("序列化令牌请求失败: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", tokenURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("创建令牌请求失败: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送令牌请求失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取令牌响应失败: %w", err)
	}

	var tokenResp struct {
		Code              int    `json:"code"`
		Msg               string `json:"msg"`
		TenantAccessToken string `json:"tenant_access_token"`
	}

	if err := json.Unmarshal(body, &tokenResp); err != nil {
		return "", fmt.Errorf("解析令牌响应失败: %w", err)
	}

	if tokenResp.Code != 0 {
		return "", fmt.Errorf("获取访问令牌失败: %s (code: %d)", tokenResp.Msg, tokenResp.Code)
	}

	if tokenResp.TenantAccessToken == "" {
		return "", fmt.Errorf("访问令牌为空")
	}

	return tokenResp.TenantAccessToken, nil
}

// callCreateEventAPI 调用飞书日历创建事件API
func (c *CalendarService) callCreateEventAPI(ctx context.Context, apiURL, accessToken string, requestBody map[string]interface{}) (string, error) {
	// 🔧 调试：记录完整的请求体
	logging.InfoLogger.Infof("飞书API请求URL: %s", apiURL)

	// 序列化请求体
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return "", fmt.Errorf("序列化请求体失败: %w", err)
	}

	// 🔧 调试：记录请求体内容（特别关注视频会议设置）
	logging.InfoLogger.Infof("飞书API请求体: %s", string(jsonData))

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+accessToken)

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %w", err)
	}

	// 解析响应
	var apiResp struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			Event struct {
				EventId string `json:"event_id"`
			} `json:"event"`
		} `json:"data"`
	}

	// 🔧 调试：记录完整的API响应
	logging.InfoLogger.Infof("飞书API响应: %s", string(body))

	if err := json.Unmarshal(body, &apiResp); err != nil {
		return "", fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查API响应
	if apiResp.Code != 0 {
		feishuErr := c.errorHandler.ParseAPIError(apiResp.Code, apiResp.Msg)
		logging.ErrorLogger.Error(c.errorHandler.FormatErrorForLogging(feishuErr, map[string]interface{}{
			"操作":  "创建日程",
			"API": apiURL,
		}))
		// 🔧 调试：记录详细的错误信息
		logging.ErrorLogger.Errorf("飞书API调用失败 - Code: %d, Msg: %s, 完整响应: %s",
			apiResp.Code, apiResp.Msg, string(body))
		return "", feishuErr
	}

	if apiResp.Data.Event.EventId == "" {
		return "", fmt.Errorf("API响应中缺少事件ID")
	}

	return apiResp.Data.Event.EventId, nil
}

// callAddAttendeesAPI 调用飞书日历添加参会人员API
func (c *CalendarService) callAddAttendeesAPI(ctx context.Context, apiURL, accessToken string, requestData map[string]interface{}) error {
	// 序列化请求体
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return fmt.Errorf("序列化参会人员请求体失败: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建参会人员HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+accessToken)

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送参会人员HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取参会人员响应失败: %w", err)
	}

	// 解析响应
	var apiResp struct {
		Code    int         `json:"code"`
		Msg     string      `json:"msg"`
		Data    interface{} `json:"data"`
		Details interface{} `json:"details"` // 添加details字段用于错误详情
	}

	if err := json.Unmarshal(body, &apiResp); err != nil {
		return fmt.Errorf("解析参会人员响应失败: %w", err)
	}

	// 检查API响应
	if apiResp.Code != 0 {
		// 记录详细的错误信息
		detailsStr := ""
		if apiResp.Details != nil {
			if detailsBytes, err := json.Marshal(apiResp.Details); err == nil {
				detailsStr = string(detailsBytes)
			}
		}
		logging.ErrorLogger.Errorf("添加参会人员API错误 - 错误码: %d, 错误信息: %s, 详细信息: %s",
			apiResp.Code, apiResp.Msg, detailsStr)
		return fmt.Errorf("飞书日历添加参会人员API返回错误: %s (code: %d), 详细信息: %s",
			apiResp.Msg, apiResp.Code, detailsStr)
	}

	return nil
}

// callRemoveAttendeesAPI 调用飞书日历删除参会人员API
func (c *CalendarService) callRemoveAttendeesAPI(ctx context.Context, apiURL, accessToken string, requestData map[string]interface{}) error {
	// 序列化请求体
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return fmt.Errorf("序列化删除参会人员请求体失败: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建删除参会人员HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+accessToken)

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送删除参会人员HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取删除参会人员响应失败: %w", err)
	}

	// 解析响应
	var apiResp struct {
		Code    int         `json:"code"`
		Msg     string      `json:"msg"`
		Data    struct{}    `json:"data"`
		Details interface{} `json:"details"` // 添加details字段用于错误详情
	}

	if err := json.Unmarshal(body, &apiResp); err != nil {
		return fmt.Errorf("解析删除参会人员响应失败: %w", err)
	}

	// 检查API响应
	if apiResp.Code != 0 {
		// 特别处理错误码190014
		if apiResp.Code == 190014 {
			detailsStr := ""
			if apiResp.Details != nil {
				if detailsBytes, err := json.Marshal(apiResp.Details); err == nil {
					detailsStr = string(detailsBytes)
				}
			}
			logging.ErrorLogger.Errorf("删除参会人员API参数错误 - 错误码: %d, 错误信息: %s, 详细信息: %s",
				apiResp.Code, apiResp.Msg, detailsStr)
			return fmt.Errorf("删除参会人员API参数错误: %s (code: %d), 详细信息: %s",
				apiResp.Msg, apiResp.Code, detailsStr)
		}
		return fmt.Errorf("飞书删除参会人员API返回错误: %s (code: %d)", apiResp.Msg, apiResp.Code)
	}

	return nil
}

// getPrimaryCalendarID 获取用户的主日历ID
func (c *CalendarService) getPrimaryCalendarID(ctx context.Context, accessToken string) (string, error) {
	// 调用飞书API获取日历列表
	apiURL := "https://open.feishu.cn/open-apis/calendar/v4/calendars?page_size=100"

	req, err := http.NewRequestWithContext(ctx, "GET", apiURL, nil)
	if err != nil {
		return "", fmt.Errorf("创建获取日历列表请求失败: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+accessToken)

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("获取日历列表请求失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取日历列表响应失败: %w", err)
	}

	var apiResp struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			CalendarList []struct {
				CalendarId string `json:"calendar_id"`
				Summary    string `json:"summary"`
				Type       string `json:"type"`
				Role       string `json:"role"`
			} `json:"calendar_list"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &apiResp); err != nil {
		return "", fmt.Errorf("解析日历列表响应失败: %w", err)
	}

	if apiResp.Code != 0 {
		feishuErr := c.errorHandler.ParseAPIError(apiResp.Code, apiResp.Msg)
		logging.ErrorLogger.Error(c.errorHandler.FormatErrorForLogging(feishuErr, map[string]interface{}{
			"操作": "获取日历列表",
		}))
		return "", feishuErr
	}

	// 查找主日历或第一个可写日历
	for _, calendar := range apiResp.Data.CalendarList {
		// 优先选择主日历（primary类型）或有写权限的日历
		if calendar.Type == "primary" || calendar.Role == "owner" || calendar.Role == "writer" {
			logging.InfoLogger.Infof("找到可用日历: ID=%s, 名称=%s, 类型=%s, 权限=%s",
				calendar.CalendarId, calendar.Summary, calendar.Type, calendar.Role)
			return calendar.CalendarId, nil
		}
	}

	// 如果没有找到主日历，使用第一个日历
	if len(apiResp.Data.CalendarList) > 0 {
		firstCalendar := apiResp.Data.CalendarList[0]
		logging.InfoLogger.Infof("使用第一个可用日历: ID=%s, 名称=%s, 类型=%s, 权限=%s",
			firstCalendar.CalendarId, firstCalendar.Summary, firstCalendar.Type, firstCalendar.Role)
		return firstCalendar.CalendarId, nil
	}

	return "", fmt.Errorf("未找到可用的日历")
}

// CancelEvent 取消日程事件
func (c *CalendarService) CancelEvent(ctx context.Context, eventID string, reason string) error {
	if eventID == "" {
		return fmt.Errorf("事件ID不能为空")
	}

	logging.InfoLogger.Infof("开始取消日程: %s, 原因: %s", eventID, reason)

	// 获取访问令牌
	accessToken, err := c.getAccessToken(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("获取访问令牌失败: %v", err)
		return fmt.Errorf("获取访问令牌失败: %w", err)
	}

	// 获取日历ID
	calendarID, err := c.getCalendarID(ctx, accessToken)
	if err != nil {
		return fmt.Errorf("获取日历ID失败: %w", err)
	}

	// 调用飞书删除日程API
	apiURL := fmt.Sprintf("https://open.feishu.cn/open-apis/calendar/v4/calendars/%s/events/%s?need_notification=true", calendarID, eventID)
	err = c.callDeleteEventAPI(ctx, apiURL, accessToken)
	if err != nil {
		logging.ErrorLogger.Errorf("飞书删除日程API调用失败: %v", err)
		return fmt.Errorf("取消日程失败: %w", err)
	}

	logging.InfoLogger.Infof("成功取消日程: %s", eventID)
	return nil
}

// UpdateEvent 更新日程事件
func (c *CalendarService) UpdateEvent(ctx context.Context, eventID string, updates *models.EventUpdates) (*models.CalendarEvent, error) {
	if eventID == "" {
		return nil, fmt.Errorf("事件ID不能为空")
	}
	if updates == nil {
		return nil, fmt.Errorf("更新数据不能为空")
	}

	logging.InfoLogger.Infof("开始更新日程: %s", eventID)

	// 获取访问令牌
	accessToken, err := c.getAccessToken(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("获取访问令牌失败: %v", err)
		return nil, fmt.Errorf("获取访问令牌失败: %w", err)
	}

	// 动态获取日历ID
	var calendarID string
	if c.config.DefaultCalendarID == "primary" || c.config.DefaultCalendarID == "" {
		calendarID, err = c.getPrimaryCalendarID(ctx, accessToken)
		if err != nil {
			logging.ErrorLogger.Errorf("获取主日历ID失败: %v", err)
			return nil, fmt.Errorf("获取主日历ID失败: %w", err)
		}
	} else {
		calendarID = c.config.DefaultCalendarID
	}

	// 构建更新请求体
	requestBody := c.buildUpdateRequestBody(updates)

	// 调用飞书更新日程API
	apiURL := fmt.Sprintf("https://open.feishu.cn/open-apis/calendar/v4/calendars/%s/events/%s?user_id_type=open_id", calendarID, eventID)
	updatedEvent, err := c.callUpdateEventAPI(ctx, apiURL, accessToken, requestBody)
	if err != nil {
		logging.ErrorLogger.Errorf("飞书更新日程API调用失败: %v", err)
		return nil, fmt.Errorf("更新日程失败: %w", err)
	}

	// 智能处理参会人员同步
	// 优先使用AttendeeOpenIDs进行状态同步，这是主要的业务场景
	if len(updates.AttendeeOpenIDs) > 0 {
		logging.InfoLogger.Infof("检测到参会人员状态同步需求，目标人数: %d", len(updates.AttendeeOpenIDs))
		if err := c.SyncEventAttendees(ctx, eventID, updates.AttendeeOpenIDs); err != nil {
			logging.ErrorLogger.Errorf("参会人员状态同步失败: %v", err)
			// 不返回错误，因为日程更新已经成功
		}
	} else {
		// 兼容原有的增量更新逻辑（用于特殊场景）
		if len(updates.AddAttendees) > 0 {
			logging.InfoLogger.Infof("更新日程后添加参会人员: %d人", len(updates.AddAttendees))
			if err := c.AddAttendees(ctx, eventID, updates.AddAttendees); err != nil {
				logging.ErrorLogger.Errorf("添加参会人员失败: %v", err)
			}
		}

		if len(updates.RemoveAttendees) > 0 {
			logging.InfoLogger.Infof("更新日程后移除参会人员: %d人", len(updates.RemoveAttendees))
			if err := c.RemoveAttendeesByAttendeeIDs(ctx, eventID, updates.RemoveAttendees); err != nil {
				logging.ErrorLogger.Errorf("移除参会人员失败: %v", err)
			}
		}
	}

	logging.InfoLogger.Infof("成功更新日程: %s", eventID)
	return updatedEvent, nil
}

// callDeleteEventAPI 调用飞书删除日程API
func (c *CalendarService) callDeleteEventAPI(ctx context.Context, apiURL, accessToken string) error {
	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "DELETE", apiURL, nil)
	if err != nil {
		return fmt.Errorf("创建删除请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Authorization", "Bearer "+accessToken)

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送删除请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取删除响应失败: %w", err)
	}

	// 解析响应
	var apiResp struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	}

	if err := json.Unmarshal(body, &apiResp); err != nil {
		return fmt.Errorf("解析删除响应失败: %w", err)
	}

	// 检查API响应
	if apiResp.Code != 0 {
		return fmt.Errorf("飞书删除日程API返回错误: %s (code: %d)", apiResp.Msg, apiResp.Code)
	}

	return nil
}

// buildUpdateRequestBody 构建更新请求体
func (c *CalendarService) buildUpdateRequestBody(updates *models.EventUpdates) map[string]interface{} {
	requestBody := make(map[string]interface{})

	// 基本信息更新
	if updates.Title != nil {
		requestBody["summary"] = *updates.Title
	}
	if updates.Description != nil {
		requestBody["description"] = *updates.Description
	}
	if updates.Location != nil {
		requestBody["location"] = map[string]interface{}{
			"name": *updates.Location,
		}
	}

	// 时间更新
	if updates.StartTime != nil {
		requestBody["start_time"] = map[string]interface{}{
			"timestamp": strconv.FormatInt(updates.StartTime.Unix(), 10),
			"timezone":  c.getTimezone(updates.Timezone),
		}
	}
	if updates.EndTime != nil {
		requestBody["end_time"] = map[string]interface{}{
			"timestamp": strconv.FormatInt(updates.EndTime.Unix(), 10),
			"timezone":  c.getTimezone(updates.Timezone),
		}
	}

	return requestBody
}

// getTimezone 获取时区设置
func (c *CalendarService) getTimezone(timezone *string) string {
	if timezone != nil && *timezone != "" {
		return *timezone
	}
	return c.config.DefaultTimezone
}

// callUpdateEventAPI 调用飞书更新日程API
func (c *CalendarService) callUpdateEventAPI(ctx context.Context, apiURL, accessToken string, requestBody map[string]interface{}) (*models.CalendarEvent, error) {
	// 序列化请求体
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("序列化更新请求体失败: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "PATCH", apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建更新请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+accessToken)

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送更新请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取更新响应失败: %w", err)
	}

	// 解析响应
	var apiResp struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			Event struct {
				EventId string `json:"event_id"`
				Summary string `json:"summary"`
			} `json:"event"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, fmt.Errorf("解析更新响应失败: %w", err)
	}

	// 检查API响应
	if apiResp.Code != 0 {
		return nil, fmt.Errorf("飞书更新日程API返回错误: %s (code: %d)", apiResp.Msg, apiResp.Code)
	}

	// 构建返回的事件对象
	event := &models.CalendarEvent{
		EventID:    apiResp.Data.Event.EventId,
		CalendarID: c.config.DefaultCalendarID,
		Title:      apiResp.Data.Event.Summary,
		EventURL:   c.generateEventURL(apiResp.Data.Event.EventId),
	}

	return event, nil
}

// validateCreateEventParams 验证创建事件的参数
func (c *CalendarService) validateCreateEventParams(meetingData *models.StandardMeetingData) error {
	if meetingData.Title == "" {
		return fmt.Errorf("会议标题不能为空")
	}

	// 验证时间范围
	if err := c.permissionValidator.ValidateTimeRange(meetingData.StartTime, meetingData.EndTime); err != nil {
		return err
	}

	// 验证时区
	timezone := meetingData.Timezone
	if timezone == "" {
		timezone = c.config.DefaultTimezone
	}
	if err := c.permissionValidator.ValidateTimezone(timezone); err != nil {
		return err
	}

	// 验证参会人员
	if err := c.permissionValidator.ValidateAttendees(meetingData.AttendeeOpenIDs); err != nil {
		return err
	}

	return nil
}

// validatePermissions 验证权限
func (c *CalendarService) validatePermissions(ctx context.Context, accessToken string) error {
	// 检查机器人能力
	if err := c.permissionValidator.CheckBotCapability(ctx, accessToken); err != nil {
		return fmt.Errorf("机器人能力检查失败: %w", err)
	}

	// 获取实际使用的日历ID
	calendarID, err := c.getCalendarID(ctx, accessToken)
	if err != nil {
		return fmt.Errorf("获取日历ID失败: %w", err)
	}

	// 验证日历ID格式
	if err := c.permissionValidator.ValidateCalendarID(calendarID); err != nil {
		return err
	}

	// 检查日历权限
	calendarInfo, err := c.permissionValidator.CheckCalendarPermission(ctx, accessToken, calendarID)
	if err != nil {
		return fmt.Errorf("日历权限检查失败: %w", err)
	}

	logging.InfoLogger.Infof("权限验证通过 - 日历: %s (%s), 权限: %s",
		calendarInfo.Summary, calendarInfo.Type, calendarInfo.Role)

	return nil
}

// getCalendarID 获取实际使用的日历ID（统一方法）
func (c *CalendarService) getCalendarID(ctx context.Context, accessToken string) (string, error) {
	if c.config.DefaultCalendarID == "primary" || c.config.DefaultCalendarID == "" {
		return c.getPrimaryCalendarID(ctx, accessToken)
	}
	return c.config.DefaultCalendarID, nil
}

// getAccessTokenAndCalendarID 获取访问令牌和日历ID（通用方法）
func (c *CalendarService) getAccessTokenAndCalendarID(ctx context.Context) (string, string, error) {
	accessToken, err := c.getAccessToken(ctx)
	if err != nil {
		return "", "", fmt.Errorf("获取访问令牌失败: %w", err)
	}

	calendarID, err := c.getCalendarID(ctx, accessToken)
	if err != nil {
		return "", "", fmt.Errorf("获取日历ID失败: %w", err)
	}

	return accessToken, calendarID, nil
}
