package transmakejob

import (
	"encoding/json"
	"errors"
	"fmt"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/cache"
	"irisAdminApi/service/dao/buildfarm/dgitjob"
	"irisAdminApi/service/dao/buildfarm/dmakejob"
	"irisAdminApi/service/dao/user/duser"

	"github.com/kataras/iris/v12"
	"gorm.io/gorm"
)

type MakejobReq struct {
	TaskType           uint   `json:"task_type" form:"task_type"`
	Defconfig          string `json:"defconfig" form:"defconfig"`
	Target             string `json:"target" form:"target"`
	Product            string `json:"product" form:"product"`
	Baseline           string `json:"baseline" form:"baseline"`
	PatchEnable        bool   `json:"patch_enable" form:"patch_enable"`
	Patches            string `json:"patches" form:"patches"`
	CustomEnable       bool   `json:"custom_enable" form:"custom_enable"`
	SwitchBranches     string `json:"switch_branches" form:"switch_branches"`
	CustomDefconfig    string `json:"custom_defconfig" form:"custom_defconfig"`
	CustomKernelConfig string `json:"custom_kernel_config" form:"custom_kernel_config"`
	BuildType          string `json:"build_type" form:"build_type"`   // 编译类型： debug, performance, factory
	BuildOspkg         bool   `json:"build_ospkg" form:"build_ospkg"` // bsp仓库可能需要make build-ospkg
	DirClean           bool   `json:"dir_clean" form:"dir_clean"`     // bsp仓库可能需要make build-ospkg
}

type SwitchBranches struct {
	Type1       string `json:"type1"`
	Type2       string `json:"type2"`
	Repo        string `json:"repo"`
	Branch      string `json:"branch"`
	UnitPackage string `json:"unitpackage"`
}

func CreateMakeJobTransaction(ctx iris.Context, makeJobReq *MakejobReq, gitjob *dgitjob.GitJob) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		// 检查当前作业是否已经存在排队中的编译申请
		makejob := dmakejob.MakeJob{}
		if err := tx.Model(dmakejob.Model()).Where("job_id = ? and (status = 3 or status = 0)", gitjob.JobID).Find(&makejob).Error; err != nil {
			logging.ErrorLogger.Errorf("another job is running or queuing", err)
			return err
		}

		if makejob.ID != 0 {
			return errors.New("当前作业存在未完成编译任务")
		}

		tempName := libs.GetUUID()

		feedsconfigcustom := ""
		feedsconfigcustomArray := []string{}
		switchBranches := []SwitchBranches{}
		if makeJobReq.CustomEnable {
			err := json.Unmarshal([]byte(makeJobReq.SwitchBranches), &switchBranches)
			if err != nil {
				logging.ErrorLogger.Errorf("unmarshal switch branches failed:", err)
				return err
			}
			for _, switch_branch := range switchBranches {
				var line string
				if strings.HasPrefix(switch_branch.Branch, "^") {
					line = fmt.Sprintf("%s %s %s %s%s", switch_branch.Type1, switch_branch.Type2, switch_branch.UnitPackage, switch_branch.Repo, switch_branch.Branch)
				} else {
					line = fmt.Sprintf("%s %s %s %s;%s", switch_branch.Type1, switch_branch.Type2, switch_branch.UnitPackage, switch_branch.Repo, switch_branch.Branch)
				}
				feedsconfigcustomArray = append(feedsconfigcustomArray, line)
			}
			feedsconfigcustom = strings.Join(feedsconfigcustomArray, "\n")
		}
		status := 3
		if makeJobReq.TaskType == 3 {
			status = 4
			if err := tx.Model(dmakejob.Model()).Unscoped().Delete(dmakejob.Model(), "user_id = ? and status in (3, 4) and task_type = 3", gitjob.UserID).Error; err != nil {
				return err
			}

		}
		if err := tx.Model(dmakejob.Model()).Create(map[string]interface{}{
			"JobId":             gitjob.JobID,
			"TaskId":            tempName,
			"TaskType":          makeJobReq.TaskType,
			"Status":            status,
			"Product":           makeJobReq.Product,
			"Defconfig":         makeJobReq.Defconfig,
			"Target":            makeJobReq.Target,
			"Baseline":          makeJobReq.Baseline,
			"Version":           "",
			"CreatedAt":         time.Now(),
			"FeedsConfigCustom": feedsconfigcustom,
			"PatchEnable":       makeJobReq.PatchEnable,
			"BuildType":         makeJobReq.BuildType,
			"BuildOspkg":        makeJobReq.BuildOspkg,
			"DirClean":          makeJobReq.DirClean,
			"UserID":            gitjob.UserID,
		}).Error; err != nil {
			// 返回任何错误都会回滚事务
			logging.ErrorLogger.Errorf("create make job failed:", err)
			return err
		}

		if err := tx.Model(dmakejob.Model()).Where("task_id = ?", tempName).Find(&makejob).Error; err != nil {
			logging.ErrorLogger.Errorf("find new make job failed:", err)
			return err
		}

		if makeJobReq.PatchEnable {
			tempPatchDir, err := MakeTempDir(gitjob.JobID, "patches")
			if err != nil {
				logging.ErrorLogger.Errorf("create temp dir failed:", err)
				return err
			}
			if _, _, err := ctx.UploadFormFiles(tempPatchDir, func(ctx iris.Context, file *multipart.FileHeader) bool {
				return true
			}); err != nil {
				logging.ErrorLogger.Errorf("save file failed:", err)
				return err
			}
			f, err := os.OpenFile(filepath.Join(tempPatchDir, "patch_infos.txt"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
			defer f.Close()
			f.Write([]byte(makeJobReq.Patches))

			// server := &dserver.Response{}
			// err = server.Find(gitjob.ServerID)
			// if err != nil {
			// 	logging.ErrorLogger.Errorf("create gitjob find server get err ", err)
			// 	return err
			// }
			// cmd := exec.Command("scp", "-o", "stricthostkeychecking=no", "-r", "-P", strconv.Itoa(int(server.Port)), tempPatchDir, fmt.Sprintf("%s@%s:%s", server.Username, server.Host, gitjob.Dir))
			// // cmd := exec.Command("scp", "-o", "stricthostkeychecking=no", "-r", "-P", "22", tempPatchDir, fmt.Sprintf("%s@%s:%s", "linjiakai", "127.0.0.1", filepath.Join(libs.Config.Buildfarm.Compilepath, "test123123")))
			// var out bytes.Buffer
			// var stderr bytes.Buffer
			// cmd.Stdout = &out
			// cmd.Stderr = &stderr
			// err = cmd.Run()
			// if err != nil {
			// 	logging.ErrorLogger.Errorf("scp file failed", err, out.String(), stderr.String())
			// 	return err
			// }
		}
		if makeJobReq.CustomDefconfig != "" {
			success := false

			tempPatchDir, err := MakeTempDir(gitjob.JobID, "defconfig")
			if err != nil {
				logging.ErrorLogger.Errorf("create temp dir failed:", err)
				return err
			}
			if _, _, err = ctx.UploadFormFiles(tempPatchDir, func(ctx iris.Context, file *multipart.FileHeader) bool {
				if file.Filename == makeJobReq.CustomDefconfig {
					success = true
					file.Filename = makeJobReq.Product + "_defconfig"
					return true
				}
				return false
			}); err != nil {
				logging.ErrorLogger.Errorf("save file failed:", err)
				return err
			}
			if !success {
				return errors.New("未找到自定义配置文件")
			}
			// server := &dserver.Response{}
			// err = server.Find(gitjob.ServerID)
			// if err != nil {
			// 	logging.ErrorLogger.Errorf("create gitjob find server get err ", err)
			// 	return err
			// }
			// cmd := exec.Command("scp", "-o", "stricthostkeychecking=no", "-r", "-P", strconv.Itoa(int(server.Port)), filepath.Join(tempPatchDir, makeJobReq.Product+"_defconfig"), fmt.Sprintf("%s@%s:%s", server.Username, server.Host, filepath.Join(gitjob.Dir, "prj_"+makeJobReq.Product, "configs")))
			// // cmd := exec.Command("scp", "-o", "stricthostkeychecking=no", "-r", "-P", "22", tempPatchDir, fmt.Sprintf("%s@%s:%s", "linjiakai", "127.0.0.1", filepath.Join(libs.Config.Buildfarm.Compilepath, "test123123")))
			// var out bytes.Buffer
			// var stderr bytes.Buffer
			// cmd.Stdout = &out
			// cmd.Stderr = &stderr
			// err = cmd.Run()
			// if err != nil {
			// 	logging.ErrorLogger.Errorf("scp file failed", err, out.String(), stderr.String())
			// 	return err
			// }
			if makejob.TaskType == 3 {
				PushCoverityAuditMailQueue(gitjob, &makejob, status)
			}
		}

		if makeJobReq.CustomKernelConfig != "" {
			success := false

			tempPatchDir, err := MakeTempDir(gitjob.JobID, "kernel_config")
			if err != nil {
				logging.ErrorLogger.Errorf("create temp dir failed:", err)
				return err
			}
			if _, _, err = ctx.UploadFormFiles(tempPatchDir, func(ctx iris.Context, file *multipart.FileHeader) bool {
				if file.Filename == makeJobReq.CustomKernelConfig {
					success = true
					file.Filename = makeJobReq.Product + "_kernel_config"
					return true
				}
				return false
			}); err != nil {
				logging.ErrorLogger.Errorf("save file failed:", err)
				return err
			}
			if !success {
				return errors.New("未找到自定义配置文件")
			}
			// server := &dserver.Response{}
			// err = server.Find(gitjob.ServerID)
			// if err != nil {
			// 	logging.ErrorLogger.Errorf("create gitjob find server get err ", err)
			// 	return err
			// }
			// cmd := exec.Command("scp", "-o", "stricthostkeychecking=no", "-r", "-P", strconv.Itoa(int(server.Port)), filepath.Join(tempPatchDir, makeJobReq.Product+"_kernel_config"), fmt.Sprintf("%s@%s:%s", server.Username, server.Host, filepath.Join(gitjob.Dir, "prj_"+makeJobReq.Product, "configs")))
			// // cmd := exec.Command("scp", "-o", "stricthostkeychecking=no", "-r", "-P", "22", tempPatchDir, fmt.Sprintf("%s@%s:%s", "linjiakai", "127.0.0.1", filepath.Join(libs.Config.Buildfarm.Compilepath, "test123123")))
			// var out bytes.Buffer
			// var stderr bytes.Buffer
			// cmd.Stdout = &out
			// cmd.Stderr = &stderr
			// err = cmd.Run()
			// if err != nil {
			// 	logging.ErrorLogger.Errorf("scp file failed", err, out.String(), stderr.String())
			// 	return err
			// }
			if makejob.TaskType == 3 {
				PushCoverityAuditMailQueue(gitjob, &makejob, status)
			}
		}

		// 返回 nil 提交事务
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func CreateIncrementMakeJobTransaction(ctx iris.Context, makeJobReq *MakejobReq, gitjob *dgitjob.GitJob) (*dmakejob.MakeJob, error) {
	makejob := dmakejob.MakeJob{}
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		// 检查当前作业是否已经存在排队中的编译申请

		if err := tx.Model(dmakejob.Model()).Where("job_id = ? and (status = 3 or status = 0)", gitjob.JobID).Find(&makejob).Error; err != nil {
			logging.ErrorLogger.Errorf("another job is running or queuing", err)
			return err
		}

		if makejob.ID != 0 {
			return errors.New("当前作业存在未完成编译任务")
		}

		tempName := libs.GetUUID()
		status := 3
		if err := tx.Model(dmakejob.Model()).Create(map[string]interface{}{
			"JobId":             gitjob.JobID,
			"TaskId":            tempName,
			"TaskType":          makeJobReq.TaskType,
			"Status":            status,
			"Product":           makeJobReq.Product,
			"Defconfig":         makeJobReq.Defconfig,
			"Target":            makeJobReq.Target,
			"Baseline":          makeJobReq.Baseline,
			"Version":           "",
			"CreatedAt":         time.Now(),
			"FeedsConfigCustom": "",
			"PatchEnable":       makeJobReq.PatchEnable,
			"BuildType":         makeJobReq.BuildType,
			"UserID":            gitjob.UserID,
			"BuildOspkg":        makeJobReq.BuildOspkg,
			"DirClean":          makeJobReq.DirClean,
		}).Error; err != nil {
			// 返回任何错误都会回滚事务
			logging.ErrorLogger.Errorf("create make job failed:", err)
			return err
		}

		if err := tx.Model(dmakejob.Model()).Where("task_id = ?", tempName).Find(&makejob).Error; err != nil {
			logging.ErrorLogger.Errorf("find new make job failed:", err)
			return err
		}

		// 返回 nil 提交事务
		return nil
	})
	if err != nil {
		return nil, err
	}
	return &makejob, nil
}

func MakeTempDir(jobId, name string) (string, error) {
	tempPatchDir := filepath.Join("/tmp", jobId, name)
	if _, err := os.Stat(tempPatchDir); err == nil {
		return tempPatchDir, os.RemoveAll(tempPatchDir)
	}
	err := os.MkdirAll(tempPatchDir, 0o750)
	os.Chmod(tempPatchDir, 0o750)
	return tempPatchDir, err
}

var statusMap = map[int]string{
	0: "编译中",
	1: "编译完成",
	2: "编译失败",
	3: "排队中",
	4: "待审批",
}

func PushCoverityAuditMailQueue(gitjob *dgitjob.GitJob, makejob *dmakejob.MakeJob, status int) {
	if libs.Config.Mail.Enable {
		rc := cache.GetRedisClusterClient()
		from := "编译农场"
		subject := fmt.Sprintf("[编译农场][普通编译][作业ID:%s][%s]", makejob.TaskID, statusMap[status])
		body := fmt.Sprintf(`%s<br>%s 提交商业代码审查前置作业，待审批。<br>前往:<a href="http://172.28.244.15:9090/buildfarm/#/manage/coverity">商业代码审批</a>进行审批`, subject, duser.UserMap[gitjob.UserID].Name)
		for userId := range duser.UserMap {
			if libs.InArrayS(duser.UserMap[userId].Roles, "编译农场管理员") {
				to := duser.UserMap[gitjob.UserID].Username + "@ruijie.com.cn"
				msg := strings.Join([]string{from, to, subject, body}, "|")
				_, err := rc.LPush(libs.Config.Mail.Queue, msg)
				if err != nil {
					logging.ErrorLogger.Error(err)
				}
			}
		}
	}
}
