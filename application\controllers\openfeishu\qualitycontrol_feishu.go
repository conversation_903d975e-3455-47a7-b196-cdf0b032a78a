package openfeishu

import (
	"context"
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"strconv"
	"strings"
	"time"

	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkbitable "github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
)

// 飞书多维表格字段映射结构
type FeishuFieldMapping struct {
	FieldID   string `json:"field_id"`
	FieldName string `json:"field_name"`
	Type      int    `json:"type"`
	UIType    string `json:"ui_type"`
}

// 品控系统字段映射常量
var QualityControlFieldMappings = map[string]FeishuFieldMapping{
	"品控单号": {
		FieldName: "品控单号",
		Type:      1,
		UIType:    "Text",
	},
	"品控问题是否关闭": {
		FieldName: "品控问题是否关闭",
		Type:      3,
		UIType:    "SingleSelect",
	},
	"品控问题是否闭环": {
		FieldName: "品控问题是否闭环",
		Type:      3,
		UIType:    "SingleSelect",
	},
	"品控责任人": {
		FieldName: "品控责任人",
		Type:      11,
		UIType:    "User",
	},
	"超期情况": {
		FieldName: "超期情况",
		Type:      1,
		UIType:    "Text",
	},
	"问题时效": {
		FieldName: "问题时效",
		Type:      2,
		UIType:    "Text",
	},
}

// 单选字段选项映射
var SingleSelectOptions = map[string]map[string]string{
	"品控问题是否关闭": {
		"是": "optbya0XdU",
		"否": "optEe3D8Uh",
	},
	"品控问题是否闭环": {
		"已闭环":  "optix8lQwT",
		"未闭环":  "optAV9JzTC",
		"无需闭环": "optifjbt5i",
	},
}

// 记录更新请求结构
type FeishuRecordUpdateRequest struct {
	RecordID string                 `json:"record_id"`
	Fields   map[string]interface{} `json:"fields"`
}

// 批量记录更新请求结构
type FeishuBatchUpdateRequest struct {
	Records []FeishuRecordUpdateRequest `json:"records"`
}

// 转换字段值为飞书API格式
func ConvertFieldValue(fieldName string, value interface{}) (interface{}, error) {
	mapping, exists := QualityControlFieldMappings[fieldName]
	if !exists {
		return nil, fmt.Errorf("未找到字段映射: %s", fieldName)
	}

	switch mapping.Type {
	case 1: // Text类型
		if value == nil {
			return nil, nil
		}
		return fmt.Sprintf("%v", value), nil

	case 2: // Number类型
		if value == nil {
			return nil, nil
		}
		switch v := value.(type) {
		case int:
			return v, nil
		case int64:
			return int(v), nil
		case float64:
			return int(v), nil
		case string:
			if v == "" {
				return nil, nil
			}
			num, err := strconv.Atoi(v)
			if err != nil {
				return nil, fmt.Errorf("无法将字符串 '%s' 转换为数字", v)
			}
			return num, nil
		default:
			return nil, fmt.Errorf("不支持的数字类型: %T", value)
		}

	case 3: // SingleSelect类型
		if value == nil {
			return nil, nil
		}
		valueStr := fmt.Sprintf("%v", value)
		if valueStr == "" {
			return nil, nil
		}

		// 检查是否有选项映射
		if options, exists := SingleSelectOptions[fieldName]; exists {
			if _, found := options[valueStr]; found {
				return valueStr, nil // 飞书API使用选项名称，不是ID
			}
			return nil, fmt.Errorf("字段 '%s' 的选项值 '%s' 不存在", fieldName, valueStr)
		}

		// 如果没有预定义选项，直接使用值（飞书会自动创建新选项）
		return valueStr, nil

	case 11: // User类型
		if value == nil {
			return nil, nil
		}

		switch v := value.(type) {
		case string:
			if v == "" {
				return nil, nil
			}
			// 返回用户ID格式
			return []map[string]interface{}{
				{"id": v},
			}, nil
		case []string:
			if len(v) == 0 {
				return nil, nil
			}
			users := make([]map[string]interface{}, len(v))
			for i, userID := range v {
				users[i] = map[string]interface{}{"id": userID}
			}
			return users, nil
		default:
			return nil, fmt.Errorf("不支持的用户类型: %T", value)
		}

	default:
		return nil, fmt.Errorf("不支持的字段类型: %d", mapping.Type)
	}
}

// 处理字段值（保持字段名称，只转换值的格式）
func ProcessFieldValues(fieldsData map[string]interface{}) (map[string]interface{}, error) {
	result := make(map[string]interface{})

	for fieldName, value := range fieldsData {
		// 检查字段是否在支持的字段列表中
		if _, exists := QualityControlFieldMappings[fieldName]; !exists {
			logging.ErrorLogger.Errorf("不支持的字段: %s", fieldName)
			continue // 跳过未知字段，继续处理其他字段
		}

		// 转换字段值格式
		convertedValue, err := ConvertFieldValue(fieldName, value)
		if err != nil {
			return nil, fmt.Errorf("字段 '%s' 值转换失败: %v", fieldName, err)
		}

		// 如果转换后的值为nil，跳过该字段
		if convertedValue != nil {
			result[fieldName] = convertedValue // 保持字段名称
		}
	}

	return result, nil
}

// 格式化飞书API错误信息（基于最新错误码规范）
func formatFeishuAPIError(code int, msg, requestId string) string {
	switch code {
	case 1254003:
		return fmt.Sprintf("飞书API错误[%d]: app_token错误 - %s, RequestId: %s. 请检查QualityControlAppToken配置", code, msg, requestId)
	case 1254004:
		return fmt.Sprintf("飞书API错误[%d]: table_id错误 - %s, RequestId: %s. 请检查QualityControlTableID配置", code, msg, requestId)
	case 1254040:
		return fmt.Sprintf("飞书API错误[%d]: app_token不存在 - %s, RequestId: %s. 请检查多维表格是否存在", code, msg, requestId)
	case 1254041:
		return fmt.Sprintf("飞书API错误[%d]: table_id不存在 - %s, RequestId: %s. 请检查数据表是否存在", code, msg, requestId)
	case 1254302:
		return fmt.Sprintf("飞书API错误[%d]: 权限不足 - %s, RequestId: %s. 请检查应用权限配置", code, msg, requestId)
	case 1254290:
		return fmt.Sprintf("飞书API错误[%d]: 请求过快 - %s, RequestId: %s. 建议稍后重试", code, msg, requestId)
	case 1254291:
		return fmt.Sprintf("飞书API错误[%d]: 写冲突 - %s, RequestId: %s. 请避免并发调用", code, msg, requestId)
	case 1254607:
		return fmt.Sprintf("飞书API错误[%d]: 数据未就绪 - %s, RequestId: %s. 请稍后重试", code, msg, requestId)
	case 1255040:
		return fmt.Sprintf("飞书API错误[%d]: 请求超时 - %s, RequestId: %s. 请重试", code, msg, requestId)
	default:
		return fmt.Sprintf("飞书API错误[%d]: %s, RequestId: %s", code, msg, requestId)
	}
}

// 获取飞书表格所有记录（仅包含品控单号不为空的记录）+ 预先缓存品控责任人字段状态
func GetFeishuQualityRecords() ([]FeishuTableItem, map[string]bool, error) {
	logging.InfoLogger.Info("开始获取飞书品控表格记录（筛选品控单号不为空）+ 预先缓存品控责任人字段状态")
	var allRecords []FeishuTableItem
	qualityResponsiblePersonStatusMap := make(map[string]bool) // key: recordID, value: 品控责任人字段是否为空
	pageToken := ""
	pageSize := int64(100) // 分页大小

	for {
		// 调用飞书SDK获取记录
		records, nextPageToken, hasMore, err := queryFeishuRecordsPageSDK(pageToken, pageSize)
		if err != nil {
			logging.ErrorLogger.Error("获取飞书记录失败: ", err)
			return nil, nil, err
		}

		// 处理每条记录并缓存品控责任人字段状态
		for _, record := range records {
			// 缓存品控责任人字段状态
			qualityResponsiblePersonValue := record.Fields["品控责任人"]
			isEmpty := isFeishuUserFieldEmpty(qualityResponsiblePersonValue)
			qualityResponsiblePersonStatusMap[record.RecordID] = isEmpty
		}

		allRecords = append(allRecords, records...)
		logging.InfoLogger.Info("本页获取 %d 条有效记录，累计有效记录: %d 条", len(records), len(allRecords))

		if !hasMore {
			break
		}

		pageToken = nextPageToken

		// 添加延迟避免频率限制
		time.Sleep(100 * time.Millisecond)
	}

	emptyFieldCount := countEmptyFields(qualityResponsiblePersonStatusMap)
	logging.InfoLogger.Info("飞书品控表格记录获取完成，有效记录: %d 条（已过滤品控单号为空的记录）", len(allRecords))
	logging.InfoLogger.Info("品控责任人字段状态缓存完成，空字段记录数: %d", emptyFieldCount)
	return allRecords, qualityResponsiblePersonStatusMap, nil
}

// 判断飞书用户字段是否为空
func isFeishuUserFieldEmpty(value interface{}) bool {
	if value == nil {
		return true
	}

	// 检查是否为空字符串
	if strValue, ok := value.(string); ok {
		return strValue == ""
	}

	// 检查是否为空的用户数组
	if userArray, ok := value.([]interface{}); ok {
		return len(userArray) == 0
	}

	// 检查是否为空的map数组
	if userArray, ok := value.([]map[string]interface{}); ok {
		if len(userArray) == 0 {
			return true
		}
		// 检查第一个元素的id是否为空
		if userID, exists := userArray[0]["id"]; exists {
			if strID, ok := userID.(string); ok {
				return strID == ""
			}
		}
		return true
	}

	return false
}

// 统计空字段的数量
func countEmptyFields(statusMap map[string]bool) int {
	count := 0
	for _, isEmpty := range statusMap {
		if isEmpty {
			count++
		}
	}
	return count
}

// 使用飞书SDK分页查询记录
func queryFeishuRecordsPageSDK(pageToken string, pageSize int64) ([]FeishuTableItem, string, bool, error) {
	// 获取配置
	qualityControlAppToken := libs.Config.FeiShuDoc.QualityControlAppToken
	qualityControlTableID := libs.Config.FeiShuDoc.QualityControlTableID

	if qualityControlAppToken == "" || qualityControlTableID == "" {
		return nil, "", false, fmt.Errorf("品控系统配置不完整")
	}

	// 重试配置
	maxRetries := 3
	baseDelay := time.Second

	// 创建飞书客户端
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)

	// 构建查询请求
	reqBuilder := larkbitable.NewSearchAppTableRecordReqBuilder().
		AppToken(qualityControlAppToken).
		TableId(qualityControlTableID)

	// 设置分页参数
	if pageSize > 0 {
		reqBuilder = reqBuilder.PageSize(int(pageSize))
	}

	if pageToken != "" {
		reqBuilder = reqBuilder.PageToken(pageToken)
	}

	// 只获取关键字段以减少数据传输量
	fieldNames := []string{
		"品控单号",
		"品控责任人",
		"品控问题是否关闭",
		"超期情况",
		"问题时效",
		"品控问题是否闭环",
	}

	// 构建请求体（基于最新API规范，添加过滤条件）
	bodyBuilder := larkbitable.NewSearchAppTableRecordReqBodyBuilder()
	// 设置字段名称
	if len(fieldNames) > 0 {
		bodyBuilder = bodyBuilder.FieldNames(fieldNames)
	}
	reqBuilder = reqBuilder.Body(bodyBuilder.Build())

	req := reqBuilder.Build()

	// 带重试的API调用
	var resp *larkbitable.SearchAppTableRecordResp
	var err error

	for attempt := 0; attempt <= maxRetries; attempt++ {
		// 发起请求（使用正确的SDK方法路径）
		resp, err = client.Bitable.V1.AppTableRecord.Search(context.Background(), req)

		// 如果没有错误，检查响应状态
		if err == nil {
			// 检查是否需要重试的错误码（基于最新错误码规范）
			if !resp.Success() {
				switch resp.Code {
				case 1254290: // 请求过快，稍后重试
					if attempt < maxRetries {
						delay := baseDelay * time.Duration(1<<attempt) // 指数退避
						logging.InfoLogger.Info("飞书API请求过快，%d秒后重试 (尝试 %d/%d)", delay/time.Second, attempt+1, maxRetries+1)
						time.Sleep(delay)
						continue
					}
				case 1254291: // 写冲突
					if attempt < maxRetries {
						delay := baseDelay * time.Duration(1<<attempt)
						logging.InfoLogger.Info("飞书API写冲突，%d秒后重试 (尝试 %d/%d)", delay/time.Second, attempt+1, maxRetries+1)
						time.Sleep(delay)
						continue
					}
				case 1254607: // 数据未就绪，请稍后重试
					if attempt < maxRetries {
						delay := baseDelay * time.Duration(1<<attempt)
						logging.InfoLogger.Info("飞书数据未就绪，%d秒后重试 (尝试 %d/%d)", delay/time.Second, attempt+1, maxRetries+1)
						time.Sleep(delay)
						continue
					}
				case 1255040: // 请求超时
					if attempt < maxRetries {
						delay := baseDelay * time.Duration(1<<attempt)
						logging.InfoLogger.Info("飞书API超时，%d秒后重试 (尝试 %d/%d)", delay/time.Second, attempt+1, maxRetries+1)
						time.Sleep(delay)
						continue
					}
				}
			}
			// 成功或不需要重试的错误，跳出循环
			break
		} else {
			// 网络错误等，重试
			if attempt < maxRetries {
				delay := baseDelay * time.Duration(1<<attempt)
				logging.InfoLogger.Infof("飞书API网络错误，%d秒后重试 (尝试 %d/%d): %v", delay/time.Second, attempt+1, maxRetries+1, err)
				time.Sleep(delay)
				continue
			}
		}
	}

	// 处理错误
	if err != nil {
		logging.ErrorLogger.Errorf("飞书API调用失败: %v", err)
		return nil, "", false, err
	}

	// 服务端错误处理（使用最新错误码格式化）
	if !resp.Success() {
		errorMsg := formatFeishuAPIError(resp.Code, resp.Msg, resp.RequestId())
		logging.ErrorLogger.Error(errorMsg)
		return nil, "", false, fmt.Errorf(errorMsg)
	}

	// 转换响应数据，同时过滤品控单号为空的记录
	var records []FeishuTableItem
	var totalRawRecords, validRecords, nilRecords, emptyRecords, invalidRecords int

	if resp.Data != nil && resp.Data.Items != nil {
		totalRawRecords = len(resp.Data.Items)
		logging.InfoLogger.Infof("📊 获取到 %d 条原始记录", totalRawRecords)

		for _, item := range resp.Data.Items {
			record := FeishuTableItem{
				RecordID: *item.RecordId,
				Fields:   make(map[string]interface{}),
			}

			// 转换字段数据
			if item.Fields != nil {
				for fieldName, fieldValue := range item.Fields {
					record.Fields[fieldName] = fieldValue
				}
			}

			// 使用新的字段值提取函数检查品控单号
			qualityNumberRaw := record.Fields["品控单号"]

			// 分类统计
			if qualityNumberRaw == nil {
				nilRecords++

				continue
			}

			qualityNumberStr := extractFeishuFieldValue(qualityNumberRaw, "品控单号")

			// 只有品控单号不为空时才添加到结果中
			if qualityNumberStr != "" {
				records = append(records, record)
				validRecords++

			} else {
				if qualityNumberRaw != nil {
					invalidRecords++

				} else {
					emptyRecords++

				}
			}
		}

		// 过滤统计
		logging.InfoLogger.Infof("过滤统计: 原始记录 %d 条，有效记录 %d 条", totalRawRecords, validRecords)
		filteredOutCount := nilRecords + emptyRecords + invalidRecords
		if filteredOutCount > 0 {
			logging.InfoLogger.Infof("过滤掉无效记录 %d 条", filteredOutCount)
		}
	}

	nextPageToken := ""
	hasMore := false

	if resp.Data != nil {
		if resp.Data.PageToken != nil {
			nextPageToken = *resp.Data.PageToken
		}
		if resp.Data.HasMore != nil {
			hasMore = *resp.Data.HasMore
		}
	}

	return records, nextPageToken, hasMore, nil
}

// 查询飞书表格字段信息，验证字段名称是否正确
func VerifyFeishuTableFields() error {
	logging.InfoLogger.Info("🔍 开始验证飞书表格字段信息...")

	// 获取配置
	qualityControlAppToken := libs.Config.FeiShuDoc.QualityControlAppToken
	qualityControlTableID := libs.Config.FeiShuDoc.QualityControlTableID

	if qualityControlAppToken == "" || qualityControlTableID == "" {
		return fmt.Errorf("飞书配置未设置")
	}

	// 创建飞书客户端
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)

	// 构建列出字段的请求
	req := larkbitable.NewListAppTableFieldReqBuilder().
		AppToken(qualityControlAppToken).
		TableId(qualityControlTableID).
		Build()

	// 调用API获取字段列表
	resp, err := client.Bitable.V1.AppTableField.List(context.Background(), req)
	if err != nil {
		return fmt.Errorf("获取字段列表失败: %v", err)
	}

	if !resp.Success() {
		return fmt.Errorf("API调用失败[%d]: %s", resp.Code, resp.Msg)
	}

	// 分析字段信息
	logging.InfoLogger.Infof("📊 表格字段总数: %d", len(resp.Data.Items))

	var qualityNumberField *larkbitable.AppTableFieldForList
	var allFieldNames []string

	for _, field := range resp.Data.Items {
		fieldName := *field.FieldName
		fieldType := *field.Type
		allFieldNames = append(allFieldNames, fieldName)

		// 查找品控单号字段
		if fieldName == "品控单号" {
			qualityNumberField = field
			logging.InfoLogger.Infof("找到品控单号字段: '%s' (类型: %d)", fieldName, fieldType)
		}
	}

	// 验证品控单号字段是否存在
	if qualityNumberField == nil {
		logging.ErrorLogger.Error("未找到'品控单号'字段")
		logging.InfoLogger.Info("所有可用字段:")
		for i, fieldName := range allFieldNames {
			logging.InfoLogger.Infof("  %d. '%s'", i+1, fieldName)
		}

		// 查找相似的字段名
		similarFields := findSimilarFieldNames(allFieldNames, "品控单号")
		if len(similarFields) > 0 {
			logging.InfoLogger.Info("🔍 可能的相似字段:")
			for _, similar := range similarFields {
				logging.InfoLogger.Infof("  - '%s'", similar)
			}
		}

		return fmt.Errorf("品控单号字段不存在，请检查字段名称是否正确")
	}

	// 验证字段类型是否适合过滤
	fieldType := *qualityNumberField.Type
	supportedTypes := []int{1, 2, 3, 4, 5, 7, 11, 13, 15, 17, 18, 19, 20, 21, 22, 23} // 文本、数字、单选、多选、日期、人员、超链接等

	isSupported := false
	for _, supportedType := range supportedTypes {
		if fieldType == supportedType {
			isSupported = true
			break
		}
	}

	if !isSupported {
		logging.ErrorLogger.Errorf("品控单号字段类型 '%d' 可能不支持 isNotEmpty 操作符", fieldType)
	} else {
		logging.InfoLogger.Infof("品控单号字段类型 '%d' 支持过滤操作", fieldType)
	}

	logging.InfoLogger.Info("字段验证完成")
	return nil
}

// 查找相似的字段名
func findSimilarFieldNames(allFields []string, target string) []string {
	var similar []string

	for _, field := range allFields {
		// 简单的相似度检查：包含关键词
		if strings.Contains(field, "品控") || strings.Contains(field, "单号") ||
			strings.Contains(field, "编号") || strings.Contains(field, "号码") {
			similar = append(similar, field)
		}
	}

	return similar
}

// 文本清理和验证函数
// 处理换行符、空白字符，并过滤无效值
func cleanAndValidateText(text string, fieldName string) string {
	if text == "" {
		return ""
	}

	// 步骤1: 去除首尾空白字符
	text = strings.TrimSpace(text)

	// 步骤2: 处理各种换行符
	// 替换Windows换行符 \r\n
	text = strings.ReplaceAll(text, "\r\n", " ")
	// 替换Unix换行符 \n
	text = strings.ReplaceAll(text, "\n", " ")
	// 替换Mac换行符 \r
	text = strings.ReplaceAll(text, "\r", " ")
	// 替换制表符
	text = strings.ReplaceAll(text, "\t", " ")

	// 步骤3: 处理多个连续空格，替换为单个空格
	for strings.Contains(text, "  ") {
		text = strings.ReplaceAll(text, "  ", " ")
	}

	// 步骤4: 再次去除首尾空白字符
	text = strings.TrimSpace(text)

	// 步骤5: 检查是否为无效值
	if isInvalidQualityNumber(text) {

		return ""
	}

	return text
}

// 检查是否为无效的品控单号
func isInvalidQualityNumber(text string) bool {
	if text == "" {
		return true
	}

	// 转换为小写进行比较（大小写不敏感）
	lowerText := strings.ToLower(text)

	// 定义无效值列表
	invalidValues := []string{
		"无",
		"na",
		"n/a",
		"null",
		"nil",
		"none",
		"空",
		"无效",
		"不适用",
		"未知",
		"unknown",
		"undefined",
		"--",
		"---",
		"////",
		"\\\\\\\\",
	}

	// 检查是否匹配无效值
	for _, invalid := range invalidValues {
		if lowerText == invalid {
			return true
		}
	}

	// 检查是否只包含特殊字符（如 -, _, /, \, 空格等）
	specialCharOnly := true
	for _, char := range text {
		if char != '-' && char != '_' && char != '/' && char != '\\' && char != ' ' && char != '.' {
			specialCharOnly = false
			break
		}
	}

	if specialCharOnly {
		return true
	}

	return false
}

// 专门用于品控单号的验证函数
func validateQualityNumber(text string) string {
	cleaned := cleanAndValidateText(text, "品控单号")

	// 品控单号的额外验证规则
	if cleaned != "" {
		// 检查长度（品控单号通常有一定的长度要求）
		if len(cleaned) < 3 {

			return ""
		}

		// 检查是否包含有效字符（数字、字母、常见分隔符）
		validPattern := true
		for _, char := range cleaned {
			if !((char >= '0' && char <= '9') ||
				(char >= 'A' && char <= 'Z') ||
				(char >= 'a' && char <= 'z') ||
				char == '-' || char == '_' || char == '.' || char == ' ') {
				validPattern = false
				break
			}
		}

		if !validPattern {

			return ""
		}
	}

	return cleaned
}

// 通用的飞书字段值提取函数
// 根据飞书官方文档，正确解析各种字段类型的复杂数据结构
func extractFeishuFieldValue(fieldValue interface{}, fieldName string) string {
	if fieldValue == nil {
		return ""
	}

	// 处理不同的数据类型
	var rawValue string

	switch v := fieldValue.(type) {
	case string:
		// 简单字符串类型
		rawValue = v

	case float64:
		// 数字类型
		rawValue = strconv.FormatFloat(v, 'f', 0, 64)

	case int:
		// 整数类型
		rawValue = strconv.Itoa(v)

	case int64:
		// 长整数类型
		rawValue = strconv.FormatInt(v, 10)

	case []interface{}:
		// 数组类型（如多行文本、条码等）
		return extractFromArray(v, fieldName)

	case map[string]interface{}:
		// 对象类型（如超链接、地理位置等）
		return extractFromObject(v, fieldName)

	default:
		// 未知类型，尝试转换为字符串
		rawValue = fmt.Sprintf("%v", v)
	}

	// 对于字符串类型的值，应用文本清理和验证
	if fieldName == "品控单号" {
		return validateQualityNumber(rawValue)
	} else {
		return cleanAndValidateText(rawValue, fieldName)
	}
}

// 从数组中提取值（处理多行文本、条码等字段类型）
func extractFromArray(arr []interface{}, fieldName string) string {
	if len(arr) == 0 {
		return ""
	}

	var results []string
	for i, item := range arr {
		if i >= 10 { // 限制最多处理10个元素，避免过长
			break
		}

		switch itemValue := item.(type) {
		case map[string]interface{}:
			// 处理 {text: "value", type: "text"} 格式
			if text, exists := itemValue["text"]; exists {
				if textStr, ok := text.(string); ok && textStr != "" {
					// 应用文本清理和验证
					var cleanedText string
					if fieldName == "品控单号" {
						cleanedText = validateQualityNumber(textStr)
					} else {
						cleanedText = cleanAndValidateText(textStr, fieldName)
					}
					if cleanedText != "" {
						results = append(results, cleanedText)
					}
				}
			} else {
				// 处理其他对象格式
				if str := extractFromObject(itemValue, fieldName); str != "" {
					results = append(results, str)
				}
			}
		case string:
			// 直接字符串
			if itemValue != "" {
				// 应用文本清理和验证
				var cleanedText string
				if fieldName == "品控单号" {
					cleanedText = validateQualityNumber(itemValue)
				} else {
					cleanedText = cleanAndValidateText(itemValue, fieldName)
				}
				if cleanedText != "" {
					results = append(results, cleanedText)
				}
			}
		default:
			// 其他类型转字符串
			if str := fmt.Sprintf("%v", itemValue); str != "" && str != "<nil>" {
				// 应用文本清理和验证
				var cleanedText string
				if fieldName == "品控单号" {
					cleanedText = validateQualityNumber(str)
				} else {
					cleanedText = cleanAndValidateText(str, fieldName)
				}
				if cleanedText != "" {
					results = append(results, cleanedText)
				}
			}
		}
	}

	// 返回第一个非空值（对于品控单号等单值字段）
	if len(results) > 0 {
		result := results[0]

		return result
	}

	return ""
}

// 从对象中提取值（处理超链接、地理位置等字段类型）
func extractFromObject(obj map[string]interface{}, fieldName string) string {
	var rawText string

	// 处理超链接格式 {text: "显示文本", link: "URL"}
	if text, exists := obj["text"]; exists {
		if textStr, ok := text.(string); ok {
			rawText = textStr
		}
	}

	// 处理地理位置格式 {name: "地名", address: "地址", ...}
	if rawText == "" {
		if name, exists := obj["name"]; exists {
			if nameStr, ok := name.(string); ok {
				rawText = nameStr
			}
		}
	}

	// 处理其他可能的文本字段
	if rawText == "" {
		for _, key := range []string{"value", "title", "label"} {
			if val, exists := obj[key]; exists {
				if valStr, ok := val.(string); ok && valStr != "" {
					rawText = valStr
					break
				}
			}
		}
	}

	// 如果找到了文本，应用清理和验证
	if rawText != "" {
		if fieldName == "品控单号" {
			return validateQualityNumber(rawText)
		} else {
			return cleanAndValidateText(rawText, fieldName)
		}
	}

	// 如果没有找到合适的字段，返回空字符串
	return ""
}

// 构建品控单号索引映射（增加重复检测和处理）
func BuildQualityNumberIndex(records []FeishuTableItem) map[string]string {
	logging.InfoLogger.Info("开始构建品控单号索引")

	index := make(map[string]string)
	duplicateCount := 0
	duplicateNumbers := make([]string, 0)
	qualityNumberField := "品控单号" // 品控单号字段名

	for _, record := range records {
		// 使用新的字段值提取函数
		qualityNumberRaw := record.Fields[qualityNumberField]
		qualityNumberStr := extractFeishuFieldValue(qualityNumberRaw, qualityNumberField)

		if qualityNumberStr != "" {
			// 检查是否已存在相同品控单号
			if existingRecordID, exists := index[qualityNumberStr]; exists {
				duplicateCount++
				duplicateNumbers = append(duplicateNumbers, qualityNumberStr)
				logging.ErrorLogger.Errorf("🚨 发现重复品控单号: %s", qualityNumberStr)
				logging.ErrorLogger.Errorf("   已存在RecordID: %s", existingRecordID)
				logging.ErrorLogger.Errorf("   当前RecordID: %s", record.RecordID)
				logging.ErrorLogger.Errorf("   ⚠️  后一条记录将覆盖前一条记录，导致前一条记录无法更新")
			}
			index[qualityNumberStr] = record.RecordID
		} else {
			logging.InfoLogger.Infof("跳过空品控单号记录: RecordID=%s", record.RecordID)
		}
	}

	// 重复品控单号问题总结
	if duplicateCount > 0 {
		logging.ErrorLogger.Errorf("🚨 重复品控单号问题总结:")
		logging.ErrorLogger.Errorf("   发现 %d 个重复品控单号: %v", duplicateCount, duplicateNumbers)
		logging.ErrorLogger.Errorf("   影响: 除最后一条记录外，其他相同品控单号的记录将无法被更新")
		logging.ErrorLogger.Errorf("   建议: 请清理飞书表格中的重复品控单号记录以确保数据同步准确性")
		logging.ErrorLogger.Errorf("   解决方案: 1) 删除重复记录 2) 修改重复的品控单号使其唯一")
	}

	logging.InfoLogger.Info("品控单号索引构建完成，共 %d 条映射，发现 %d 个重复", len(index), duplicateCount)
	return index
}

// 基于RecordID的索引结构（解决重复品控单号问题的根本方案）
type RecordBasedIndex struct {
	RecordToQualityNumber  map[string]string   // RecordID → 品控单号
	QualityNumberToRecords map[string][]string // 品控单号 → []RecordID (用于统计和重复检测)
}

// 构建基于RecordID的索引（彻底解决重复品控单号问题）
func BuildRecordBasedIndex(records []FeishuTableItem) *RecordBasedIndex {
	logging.InfoLogger.Info("开始构建基于RecordID的索引（解决重复品控单号问题）")

	index := &RecordBasedIndex{
		RecordToQualityNumber:  make(map[string]string),
		QualityNumberToRecords: make(map[string][]string),
	}

	qualityNumberField := "品控单号"
	duplicateCount := 0
	emptyCount := 0

	for _, record := range records {
		// 使用新的字段值提取函数
		qualityNumberRaw := record.Fields[qualityNumberField]
		qualityNumberStr := extractFeishuFieldValue(qualityNumberRaw, qualityNumberField)

		if qualityNumberStr != "" {
			// 建立RecordID到品控单号的映射
			index.RecordToQualityNumber[record.RecordID] = qualityNumberStr

			// 建立品控单号到RecordID列表的映射（用于重复检测）
			index.QualityNumberToRecords[qualityNumberStr] = append(
				index.QualityNumberToRecords[qualityNumberStr],
				record.RecordID,
			)
		} else {
			emptyCount++
			logging.InfoLogger.Infof("跳过空品控单号记录: RecordID=%s", record.RecordID)
		}
	}

	// 统计重复品控单号情况
	for qualityNumber, recordIDs := range index.QualityNumberToRecords {
		if len(recordIDs) > 1 {
			duplicateCount++
			logging.InfoLogger.Infof("🔍 品控单号 %s 有 %d 条记录: %v", qualityNumber, len(recordIDs), recordIDs)
		}
	}

	logging.InfoLogger.Info("✅ 基于RecordID的索引构建完成:")
	logging.InfoLogger.Infof("  - 总记录数: %d", len(records))
	logging.InfoLogger.Infof("  - 有效记录数: %d", len(index.RecordToQualityNumber))
	logging.InfoLogger.Infof("  - 空品控单号记录数: %d", emptyCount)
	logging.InfoLogger.Infof("  - 唯一品控单号数: %d", len(index.QualityNumberToRecords))
	logging.InfoLogger.Infof("  - 重复品控单号数: %d", duplicateCount)

	if duplicateCount > 0 {
		logging.InfoLogger.Info("✅ 重复品控单号问题已彻底解决：所有记录都将被正确处理")
	}

	return index
}

// 兼容性函数：从新索引结构提取品控单号列表
func (index *RecordBasedIndex) GetUniqueQualityNumbers() []string {
	qualityNumbers := make([]string, 0, len(index.QualityNumberToRecords))
	for qualityNumber := range index.QualityNumberToRecords {
		qualityNumbers = append(qualityNumbers, qualityNumber)
	}
	return qualityNumbers
}

// 获取重复品控单号的统计信息
func (index *RecordBasedIndex) GetDuplicateStatistics() map[string][]string {
	duplicates := make(map[string][]string)
	for qualityNumber, recordIDs := range index.QualityNumberToRecords {
		if len(recordIDs) > 1 {
			duplicates[qualityNumber] = recordIDs
		}
	}
	return duplicates
}

// ValidateQualityTableConfig 验证品控表格配置
func ValidateQualityTableConfig() error {
	// 检查基本配置
	if libs.Config.FeiShuDoc.AppID == "" {
		return fmt.Errorf("飞书AppID未配置")
	}
	if libs.Config.FeiShuDoc.AppSecret == "" {
		return fmt.Errorf("飞书AppSecret未配置")
	}
	if libs.Config.FeiShuDoc.QualityControlAppToken == "" {
		return fmt.Errorf("品控系统AppToken未配置")
	}
	if libs.Config.FeiShuDoc.QualityControlTableID == "" {
		return fmt.Errorf("品控系统TableID未配置")
	}

	return nil
}

// TestFeishuAPIConnection 测试飞书API连接
func TestFeishuAPIConnection() error {
	logging.InfoLogger.Info("测试飞书API连接")

	// 获取并验证配置
	qualityControlAppToken := libs.Config.FeiShuDoc.QualityControlAppToken
	qualityControlTableID := libs.Config.FeiShuDoc.QualityControlTableID

	if qualityControlAppToken == "" || qualityControlTableID == "" {
		return fmt.Errorf("飞书配置不完整")
	}

	// 创建飞书客户端
	client := lark.NewClient(libs.Config.FeiShuDoc.AppID, libs.Config.FeiShuDoc.AppSecret)

	// 构建最简单的查询请求
	req := larkbitable.NewSearchAppTableRecordReqBuilder().
		AppToken(qualityControlAppToken).
		TableId(qualityControlTableID).
		PageSize(1).
		Body(larkbitable.NewSearchAppTableRecordReqBodyBuilder().
			AutomaticFields(false).
			Build()).
		Build()

	// 发送请求
	resp, err := client.Bitable.V1.AppTableRecord.Search(context.Background(), req)
	if err != nil {
		return fmt.Errorf("飞书API调用失败: %v", err)
	}

	if !resp.Success() {
		return fmt.Errorf("飞书API返回错误: Code=%d, Msg=%s", resp.Code, resp.Msg)
	}

	logging.InfoLogger.Info("飞书API连接测试成功")
	return nil
}

// 更新单条飞书多维表格记录（使用字段名称）
func UpdateFeishuRecordByFieldNames(recordID string, fieldsData map[string]interface{}) error {
	logging.InfoLogger.Infof("开始更新飞书记录: %s", recordID)

	// 验证输入参数
	if recordID == "" {
		return fmt.Errorf("记录ID不能为空")
	}
	if len(fieldsData) == 0 {
		return fmt.Errorf("更新字段数据不能为空")
	}

	// 验证和转换字段值（保持字段名称，只转换值的格式）
	processedFields, err := ProcessFieldValues(fieldsData)
	if err != nil {
		return fmt.Errorf("字段值处理失败: %v", err)
	}

	if len(processedFields) == 0 {
		return fmt.Errorf("没有有效的字段需要更新")
	}

	// 调用底层更新方法
	return updateFeishuRecordByFieldNames(recordID, processedFields)
}

// 更新单条飞书多维表格记录（使用现有的UpdateTableRecordByID函数）
func updateFeishuRecordByFieldNames(recordID string, fieldsData map[string]interface{}) error {
	// 获取配置
	qualityControlAppToken := libs.Config.FeiShuDoc.QualityControlAppToken
	qualityControlTableID := libs.Config.FeiShuDoc.QualityControlTableID

	if qualityControlAppToken == "" || qualityControlTableID == "" {
		return fmt.Errorf("品控系统配置不完整")
	}

	// 使用现有的UpdateTableRecordByID函数，直接支持字段名称
	resp, err := UpdateTableRecordByID(qualityControlTableID, qualityControlAppToken, recordID, fieldsData)
	if err != nil {
		logging.ErrorLogger.Errorf("飞书记录更新失败: %v", err)
		return fmt.Errorf("飞书记录更新失败: %v", err)
	}

	// 检查响应状态
	if !resp.Success() {
		errorMsg := formatFeishuAPIError(resp.Code, resp.Msg, resp.RequestId())
		logging.ErrorLogger.Error(errorMsg)
		return fmt.Errorf(errorMsg)
	}

	logging.InfoLogger.Infof("飞书记录更新成功: %s", recordID)
	return nil
}

// 批量更新飞书多维表格记录（使用字段名称）
func BatchUpdateFeishuRecordsByFieldNames(records []FeishuRecordUpdateRequest) error {
	logging.InfoLogger.Infof("开始批量更新飞书记录，共 %d 条", len(records))

	// 验证输入参数
	if len(records) == 0 {
		return fmt.Errorf("批量更新记录列表不能为空")
	}

	// 获取配置
	qualityControlAppToken := libs.Config.FeiShuDoc.QualityControlAppToken
	qualityControlTableID := libs.Config.FeiShuDoc.QualityControlTableID

	if qualityControlAppToken == "" || qualityControlTableID == "" {
		return fmt.Errorf("品控系统配置不完整")
	}

	// 处理每条记录的字段值
	processedRecords := make([]map[string]interface{}, 0, len(records))
	for _, record := range records {
		if record.RecordID == "" {
			logging.ErrorLogger.Error("跳过空记录ID的记录")
			continue
		}

		// 处理字段值
		processedFields, err := ProcessFieldValues(record.Fields)
		if err != nil {
			logging.ErrorLogger.Errorf("记录 %s 字段处理失败: %v", record.RecordID, err)
			continue
		}

		if len(processedFields) > 0 {
			// 添加record_id字段用于批量更新
			processedFields["record_id"] = record.RecordID
			processedRecords = append(processedRecords, processedFields)
		}
	}

	if len(processedRecords) == 0 {
		return fmt.Errorf("没有有效的记录需要更新")
	}

	// 使用现有的BatchUpdate函数进行批量更新
	resp, err := BatchUpdate(qualityControlTableID, qualityControlAppToken, processedRecords)
	if err != nil {
		logging.ErrorLogger.Errorf("飞书批量更新失败: %v", err)
		return fmt.Errorf("飞书批量更新失败: %v", err)
	}

	// 检查响应状态
	if !resp.Success() {
		errorMsg := formatFeishuAPIError(resp.Code, resp.Msg, resp.RequestId())
		logging.ErrorLogger.Error(errorMsg)
		return fmt.Errorf(errorMsg)
	}

	logging.InfoLogger.Infof("飞书批量记录更新成功，共更新 %d 条记录", len(processedRecords))
	return nil
}
