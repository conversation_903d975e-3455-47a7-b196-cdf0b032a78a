package productionrelease

import (
	"bytes"
	"errors"
	"fmt"
	"irisAdminApi/application/controllers/openfeishu"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/buildfarm"
	"irisAdminApi/application/models/productionrelease"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/buildfarm/dcronmakejob"
	"irisAdminApi/service/dao/productionrelease/dproduction"
	"irisAdminApi/service/dao/productionrelease/dproductionproc"
	"irisAdminApi/service/dao/productionrelease/dproductionprocdef"
	"irisAdminApi/service/dao/productionrelease/dproductionprocinst"
	"irisAdminApi/service/dao/productionrelease/dproductionproctask"
	"irisAdminApi/service/dao/productionrelease/dproductionproctaskdocument"
	"irisAdminApi/service/dao/productionrelease/dproductionproctaskextension"
	"irisAdminApi/service/dao/productionrelease/dproductionproctaskhost"
	"irisAdminApi/service/dao/productionrelease/dproductionproductmodel"
	"irisAdminApi/service/transaction/productionrelease/transproductionrelease"
	"net/url"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"irisAdminApi/service/cache"

	"github.com/kataras/iris/v12"
)

var lock sync.Mutex

func CreateProcInst(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	uuid := libs.GetUUID()
	request := &dproduction.Request{}
	if err := ctx.ReadForm(request); err != nil && !iris.IsErrPath(err) {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	//获取产品型号数据
	productSeries := ""
	var productModelList []*dproductionproductmodel.Response
	err = easygorm.GetEasyGormDb().Model(&productionrelease.ProductionProductModel{}).
		Where("name =?", request.ProductModel).
		Find(&productModelList).Error
	if err != nil {
		logging.ErrorLogger.Errorf("productmodel get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if len(productModelList) > 0 {
		productModelSlice := []string{}
		for _, _item := range productModelList {
			if !libs.InArrayS(productModelSlice, _item.ProductModel) {
				productModelSlice = append(productModelSlice, _item.ProductModel)
			}
		}
		productSeries = request.ProductModel
		if len(productModelSlice) > 0 {
			request.ProductModel = strings.Join(productModelSlice, "|")
		}
	}

	//查看是否存在相同预鉴版本记录
	var productionBaseVersion uint = 0
	productionCheck := dproduction.Response{}
	if request.ProductionType != "1" {
		err = productionCheck.CheckProductionExists(request.ProductModel, "1")
		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		if productionCheck.ID <= 0 {
			if len(productModelList) > 0 {
				ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, productSeries+"系列产品，没有预鉴版本,需进行预鉴发布"))
			} else {
				ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "型号:"+request.ProductModel+"，没有预鉴版本,需进行预鉴发布"))
			}
			return
		} else {
			productionBaseVersion = productionCheck.ID
		}
	}

	// 创建下生产时只需要基本信息，程序信息将由PM节点处理
	err = transproductionrelease.CreateProductionTransaction(id, map[string]interface{}{
		// 基本信息
		"Version":               request.Version,
		"ProductionType":        request.ProductionType,
		"RegionType":            request.RegionType,
		"ProductModel":          request.ProductModel,
		"ProductSeries":         productSeries,
		"VersionType":           request.VersionType,
		"ReleaseDate":           request.ReleaseDate,
		"VersionDesc":           request.VersionDesc,
		"ProductionBaseVersion": productionBaseVersion,
		"CreatedAt":             time.Now(),
		"UpdatedAt":             time.Now(),
		"Uuid":                  uuid,
		"Resource":              request.Resource,
		"Urgency":               request.Urgency,

		// 初始化程序信息字段为空值，将由PM节点填充
		"MainProgramUrl":      "",
		"MainProgramFileName": "",
		"MainProgramFileSize": 0,
		"MainProgramFileMd5":  "",
		"OspkgInstallUrl":     "",
		"OspkgInstallName":    "",
		"OspkgInstallSize":    0,
		"OspkgInstallMd5":     "",
		"SoftwareNumber":      "",
		"SoftwareVersion":     "",
	})

	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetProcDef(ctx iris.Context) {
	// name := ctx.FormValue("name")
	defs, err := dproductionprocdef.GetDefs()
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, defs, response.NoErr.Msg))
	return
}

func GetFeatureProcInsts(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")

	procInst := dproductionprocinst.Response{}
	list, err := procInst.All(name, status, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetFeatureProcTasks(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")

	list, err := dproductionproc.AllTasksByAssignee(uId, name, sort, orderBy, page, pageSize, status, start, end)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetFeatureProcInstTasks(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")

	list, err := dproductionproc.AllTasksByProcInst(id, name, sort, orderBy, page, pageSize, status, start, end)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetProcInst(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	info := dproductionprocinst.Response{}
	err := info.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

type TaskRequest struct {
	Comment             string `form:"comment"`
	Status              uint   `form:"status"`
	NodeID              string `form:"nodeId"`
	NextNodeID          string `form:"nextNodeID"`
	TaskID              uint   `form:"taskId"`
	UserID              uint   `form:"userId"`
	ProductModels       string `json:"product_models" form:"product_models"`
	SoftVersions        string `json:"soft_versions" form:"soft_versions"`
	Uboot               string `form:"uboot"`
	UbootVersion        string `form:"uboot_version"`
	UbootDoc            string `form:"uboot_doc"`
	RomProgram          string `form:"rom_program"`
	SetmacTool          string `form:"setmac_tool"`
	SetmacIni           string `form:"setmac_ini"`
	SetmacDoc           string `form:"setmac_doc"`
	ManufTestProgram    string `form:"manuf_test_program"`
	ManufTestProgramDoc string `form:"manuf_test_program_doc"`
	ManufTestReport     string `form:"manuf_test_report"`
	CpldUpdate          bool   `form:"cpld_update"`
	CpldUpdateUrl       string `form:"cpld_update_url"`
	MainProgramUrl      string `form:"main_program_url"`
	OspkgInstallUrl     string `form:"ospkg_install_url"`
	VersionDesc         string `form:"version_desc"`
	VersionType         string `form:"version_type"`
	RegionType          string `form:"region_type"`
	ProductModel        string `form:"product_model"`
	ReleaseDate         string `form:"release_date"`
	Urgency             bool   `form:"urgency"`
	SetmacItems         []SetmacItem
	SetmacHosts         []SetmacHost
}

type SetmacItem struct {
	Name string `form:"name"`
	Tool string `form:"tool"`
	MD5  string
	Doc  string `form:"doc"`
}

type SetmacHost struct {
	Name string `form:"name"`
	Tool string `form:"tool"`
	MD5  string
}

func UpdateProcInst(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		handleError(ctx, err, "获取用户ID失败")
		return
	}

	// 获取流程实例
	procInst := dproductionprocinst.Response{}
	if err := procInst.Find(id); err != nil {
		handleError(ctx, err, "查找流程实例失败")
		return
	}

	// 解析请求参数
	request, err := parseRequestForm(ctx)
	if err != nil {
		handleError(ctx, err, "解析表单数据失败")
		return
	}

	// 初始化对象
	featureObject := map[string]interface{}{
		"UpdatedAt": time.Now(),
	}

	fileName := ""
	logging.DebugLogger.Debugf("update", request.NodeID, request.Status)

	taskObject := map[string]interface{}{
		"NodeID":     request.NodeID,
		"UpdatedAt":  time.Now(),
		"Status":     request.Status,
		"NextNodeID": request.NextNodeID,
		"Comment":    request.Comment,
		"Attachment": fileName,
		"UserID":     request.UserID,
	}

	// 注意：打回处理逻辑已移至事务中处理，这里不需要重复处理
	// 在打回流程中，request.NodeID是打回发起的节点，而不是目标节点
	// 正确的打回目标节点是request.NextNodeID
	// 数据清除操作应在事务中进行，以确保数据一致性

	// 处理开始节点
	if request.NodeID == "start" && request.Status == 1 {
		var err error
		// 使用优化后的start节点处理函数
		startNodeFeatures, err := handleStartNode(ctx, &procInst, request)
		if err != nil {
			handleCustomError(ctx, err.Error())
			return
		}

		// 将start节点处理的结果合并到featureObject
		for key, value := range startNodeFeatures {
			featureObject[key] = value
		}
	}

	// 处理PM节点（程序提交）
	if request.NodeID == "pm_submit" && request.Status == 1 {
		var err error
		// 使用PM节点处理函数
		pmNodeFeatures, err := handlePMSubmitNode(ctx, &procInst, request)
		if err != nil {
			handleCustomError(ctx, err.Error())
			return
		}

		// 将PM节点处理的结果合并到featureObject
		for key, value := range pmNodeFeatures {
			featureObject[key] = value
		}
	}

	// 处理system_template节点
	if request.NodeID == "system_template" && request.Status == 1 {
		// 使用优化后的system_template节点处理函数
		if err := handleSystemTemplateNode(ctx, &procInst, request); err != nil {
			handleCustomError(ctx, err.Error())
			return
		}
	}

	if request.NodeID == "platform_mg" && request.Status == 1 {
		//检测该项任务是否已有数据
		//判断是否为迭代发布
		//删除扩展字段表数据
		// if procInst.Production.ProductionType != "1" {
		//查看是否存在Setmac扩展项
		lastPlatformExtensions := []*dproductionproctaskextension.Response{}
		if err := easygorm.GetEasyGormDb().Model(dproductionproctaskextension.Model()).Where("proc_inst_id =?  and task_id=? ", procInst.ID, request.TaskID).Find(&lastPlatformExtensions).Error; err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "任务数据查询失败"))
			return
		}
		//删除多余的Setmac扩展项数据
		if len(lastPlatformExtensions) > 0 {
			if err := easygorm.GetEasyGormDb().Delete(dproductionproctaskextension.Model(), "proc_inst_id =? and task_id=? ", procInst.ID, request.TaskID).Error; err != nil {
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Setmac扩展项数据处理失败"))
				return
			}
		}
		//查看是否存在SetmacHost项
		lastSetmacHosts := []*dproductionproctaskhost.Response{}
		if err := easygorm.GetEasyGormDb().Model(dproductionproctaskhost.Model()).Where("proc_inst_id =?  and task_id=? ", procInst.ID, request.TaskID).Find(&lastSetmacHosts).Error; err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "任务数据查询失败"))
			return
		}
		//删除多余的SetmacHost项
		if len(lastSetmacHosts) > 0 {
			if err := easygorm.GetEasyGormDb().Delete(dproductionproctaskhost.Model(), "proc_inst_id =? and task_id=? ", procInst.ID, request.TaskID).Error; err != nil {
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Setmac扩展项数据处理失败"))
				return
			}
		}

		UbootVersion := request.UbootVersion
		//uboot、烧片程序(ROM)、uboot版本、uboot说明文档、Setmac工具、Setmac-ini、setmac说明文档
		//svn下载文件并获得MD5值和大小
		//对比文档中的MD5值和大小是否与svn下载文件一致
		taskObject["Uboot"] = request.Uboot
		taskObject["UbootVersion"] = request.UbootVersion
		taskObject["UbootDoc"] = request.UbootDoc
		taskObject["RomProgram"] = request.RomProgram
		taskObject["SetmacTool"] = request.SetmacTool
		// taskObject["SetmacIni"] = request.SetmacIni
		taskObject["SetmacDoc"] = request.SetmacDoc
		UbootMd5, UbootFieSize, err := downloadSVNFile(request.Uboot, libs.Config.ProductionFileStorage.Upload)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "uboot文件下载失败,请输入正确的SVN地址"))
			return
		}
		ubootDocFilePath, err := downloadSVNDocFile(request.UbootDoc, libs.Config.ProductionFileStorage.Upload)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "uboot说明文档下载失败,请输入正确的SVN地址"))
			return
		}
		defer os.Remove(ubootDocFilePath)
		taskObject["UbootMd5"] = strings.ToUpper(UbootMd5)
		//验证Uboot文档MD5值验证
		UbootMd5found, err := CheckMD5InDoc(ubootDocFilePath, strings.ToUpper(UbootMd5))
		fmt.Println(strings.ToUpper(UbootMd5), UbootMd5, UbootFieSize, request.Uboot)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "uboot说明文档格式不正确，请输入正确docx格式的Word文档:"+err.Error()))
			return
		}
		if !UbootMd5found {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "uboot说明文档未找到匹配的UbootMd5,请输入正确的文档或者uboot地址"))
			return
		}

		//验证Uboot文档FileSize验证
		UbootFieSizefound, err := CheckNumberInDoc(ubootDocFilePath, UbootFieSize)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "uboot说明文档格式不正确，请输入正确docx格式的Word文档:"+err.Error()))
			return
		}
		if !UbootFieSizefound {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "uboot说明文档未找到匹配的文件大小,请输入正确的文档或者uboot地址"))
			return
		}

		ROMProgramMd5, _, err := downloadSVNFile(request.RomProgram, libs.Config.ProductionFileStorage.Upload)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "ROM文件下载失败,请输入正确的SVN地址"))
			return
		}
		taskObject["RomProgramMd5"] = strings.ToUpper(ROMProgramMd5)

		SetmacDocFilePath, err := downloadSVNDocFile(request.SetmacDoc, libs.Config.ProductionFileStorage.Upload)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Setmac工具说明文档下载失败,请输入正确的SVN地址"))
			return
		}
		defer os.Remove(SetmacDocFilePath)

		SetmacToolMd5, SetmacToolFieSize, err := downloadSVNFile(request.SetmacTool, libs.Config.ProductionFileStorage.Upload)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Setmac工具下载失败,请输入正确的SVN地址"))
			return
		}
		taskObject["SetmacToolMd5"] = strings.ToUpper(SetmacToolMd5)
		//验证Setmac工具说明文档MD5值验证
		SetmacToolMd5found, err := CheckMD5InDoc(SetmacDocFilePath, strings.ToUpper(SetmacToolMd5))
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Setmac工具说明文档格式不正确，请输入正确docx格式的Word文档:"+err.Error()))
			return
		}
		if !SetmacToolMd5found {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Setmac工具说明文档未找到匹配的SetmacToolMd5,请输入正确的文档或者Setmac工具地址"))
			return
		}
		//验证Uboot文档FileSize验证
		SetmacToolFieSizefound, err := CheckNumberInDoc(SetmacDocFilePath, SetmacToolFieSize)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Setmac工具说明文档格式不正确，请输入正确docx格式的Word文档:"+err.Error()))
			return
		}
		if !SetmacToolFieSizefound {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Setmac工具说明文档未找到匹配的工具文件大小,请输入正确的文档或者Setmac工具地址"))
			return
		}

		// SetmacIniMd5, SetmacIniFieSize, err := downloadSVNFile(request.SetmacIni, libs.Config.ProductionFileStorage.Upload)
		// if err != nil {
		// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Setmac-ini下载失败,请输入正确的SVN地址"))
		// 	return
		// }
		// taskObject["SetmacIniMd5"] = strings.ToUpper(SetmacIniMd5)
		// //验证Setmac工具说明文档MD5值验证
		// SetmacIniMd5found, err := CheckMD5InDoc(SetmacDocFilePath, strings.ToUpper(SetmacIniMd5))
		// if err != nil {
		// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Setmac工具说明文档格式不正确，请输入正确docx格式的Word文档:"+err.Error()))
		// 	return
		// }
		// if !SetmacIniMd5found {
		// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Setmac工具说明文档未找到匹配的SetmacIniMd5,请输入正确的文档或者Setmac工具地址"))
		// 	return
		// }
		// //验证Setmac工具说明文档FileSize验证
		// SetmacIniFieSizefound, err := CheckNumberInDoc(SetmacDocFilePath, SetmacIniFieSize)
		// if err != nil {
		// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Setmac工具说明文档格式不正确，请输入正确docx格式的Word文档:"+err.Error()))
		// 	return
		// }
		// if !SetmacIniFieSizefound {
		// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Setmac工具说明文档未找到匹配的SetmacIni文件大小,请输入正确的文档或者Setmac工具地址"))
		// 	return
		// }
		setmacHostObjects := []map[string]interface{}{}
		if len(request.SetmacHosts) > 0 {
			//处理SetmacHosts项
			for index, setmacHostItem := range request.SetmacHosts {
				itemMd5, itemFileSize, err := downloadSVNFile(setmacHostItem.Tool, libs.Config.ProductionFileStorage.Upload)
				if err != nil {
					errorMsg := fmt.Sprintf("Setmac主机(%s)下载失败，请输入正确的SVN地址", setmacHostItem.Name)
					ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, errorMsg))
					return
				}
				//验证说明文档MD5值验证
				itemMd5found, err := CheckMD5InDoc(SetmacDocFilePath, strings.ToUpper(itemMd5))
				if err != nil {
					ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Setmac工具说明文档未找到匹配的SetmacIniMd5，请输入正确docx格式的Word文档:"+err.Error()))
					return
				}
				if !itemMd5found {
					ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Setmac工具说明文档未找到匹配的SetmacIniMd5，请输入正确docx格式的Word文档"))
					return
				}
				//验证说明文档FileSize验证
				itemFieSizefound, err := CheckNumberInDoc(SetmacDocFilePath, itemFileSize)
				if err != nil {
					ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Setmac工具说明文档未找到匹配的SetmacIni文件大小，请输入正确docx格式的Word文档:"+err.Error()))
					return
				}
				if !itemFieSizefound {
					ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Setmac工具说明文档未找到匹配的SetmacIni文件大小，请输入正确docx格式的Word文档"))
					return
				}
				request.SetmacHosts[index].MD5 = strings.ToUpper(itemMd5)
				setmacHostObjects = append(setmacHostObjects, map[string]interface{}{
					"CreatedAt":     time.Now(),
					"TaskID":        request.TaskID,
					"ExtensionName": setmacHostItem.Name,
					"ExtensionTool": setmacHostItem.Tool,
					"MD5":           strings.ToUpper(itemMd5),
					"ProcInstID":    procInst.ID,
				})
				//插入到setmacHostHost数据库表中
				err = easygorm.GetEasyGormDb().Model(&productionrelease.ProductionProcTaskHost{}).Create(setmacHostObjects).Error
				if err != nil {
					logging.ErrorLogger.Errorf("create detection Error while SaveFormFile ", err)
					ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
					return
				}
			}
		}

		setmacObjects := []map[string]interface{}{}
		if len(request.SetmacItems) > 0 {
			// 处理Setmac扩展项
			for index, setmacItem := range request.SetmacItems {
				docFilePath, err := downloadSVNDocFile(setmacItem.Doc, libs.Config.ProductionFileStorage.Upload)
				if err != nil {
					fullMessage := fmt.Sprintf("%s说明文档下载失败,请输入正确的SVN地址", setmacItem.Name)
					ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fullMessage))
					return
				}
				defer os.Remove(docFilePath)
				itemMd5, itemFieSize, err := downloadSVNFile(setmacItem.Tool, libs.Config.ProductionFileStorage.Upload)
				if err != nil {
					errorMsg := fmt.Sprintf("Setmac扩展项(%s)下载失败，请输入正确的SVN地址", setmacItem.Name)
					ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, errorMsg))
					return
				}
				//验证说明文档MD5值验证
				itemMd5found, err := CheckMD5InDoc(docFilePath, strings.ToUpper(itemMd5))
				if err != nil {
					errorMsg := fmt.Sprintf("Setmac扩展项(%s)说明文档格式不正确，请输入正确docx格式的Word文档:", setmacItem.Name)
					ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, errorMsg+err.Error()))
					return
				}
				if !itemMd5found {
					errorMsg := fmt.Sprintf("Setmac扩展项(%s)说明文档未找到匹配的Md5,请输入正确的文档或者Setmac扩展项下载地址", setmacItem.Name)
					ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, errorMsg))
					return
				}
				//验证文档FileSize验证
				itemFieSizefound, err := CheckNumberInDoc(docFilePath, itemFieSize)
				if err != nil {
					errorMsg := fmt.Sprintf("Setmac扩展项(%s)说明文档格式不正确，请输入正确docx格式的Word文档:", setmacItem.Name)
					ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, errorMsg+err.Error()))
					return
				}
				if !itemFieSizefound {
					errorMsg := fmt.Sprintf("Setmac扩展项(%s)说明文档未找到匹配的文件大小值,请输入正确的文档或者Setmac扩展项下载地址", setmacItem.Name)
					ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, errorMsg))
					return
				}
				request.SetmacItems[index].MD5 = strings.ToUpper(itemMd5)
				setmacObjects = append(setmacObjects, map[string]interface{}{
					"CreatedAt":     time.Now(),
					"TaskID":        request.TaskID,
					"ExtensionName": setmacItem.Name,
					"ExtensionTool": setmacItem.Tool,
					"MD5":           strings.ToUpper(itemMd5),
					"ExtensionDoc":  setmacItem.Doc,
					"ProcInstID":    procInst.ID,
				})
			}
			//插入到扩展项数据库中
			err = easygorm.GetEasyGormDb().Model(&productionrelease.ProductionProcTaskExtension{}).Create(setmacObjects).Error
			if err != nil {
				logging.ErrorLogger.Errorf("create detection Error while SaveFormFile ", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
				return
			}
		}

		// 替换词、输出路径
		replacements := map[string]string{
			"{{time}}":         procInst.Production.ReleaseDate,
			"{{size}}":         strconv.FormatUint(uint64(procInst.Production.MainProgramFileSize), 10),
			"{{md5}}":          procInst.Production.MainProgramFileMd5,
			"{{name}}":         procInst.Production.MainProgramFileName,
			"{{softwarenum}}":  procInst.Production.SoftwareNumber,
			"{{softwarever}}":  procInst.Production.SoftwareVersion,
			"{{ospkgName}}":    procInst.Production.OspkgInstallName,
			"{{ospkgMD5}}":     procInst.Production.OspkgInstallMd5,
			"{{ospkgSize}}":    strconv.FormatUint(uint64(procInst.Production.OspkgInstallSize), 10),
			"{{ubootVersion}}": UbootVersion,
			"{{productModel}}": procInst.Production.ProductModel,
		}
		//运行模板生成方法，生成对应文档
		err = CreateDocumentFileV2(&procInst, replacements, request.TaskID)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
	}

	if request.NodeID == "chief_tester" && request.Status == 1 {
		// 使用辅助函数处理 chief_tester 节点
		result, err := handleChiefTesterNode(ctx, &procInst, request)
		if err != nil {
			handleCustomError(ctx, err.Error())
			return
		}

		// 将处理结果合并到 taskObject
		for key, value := range result {
			taskObject[key] = value
		}
	}

	if request.NodeID == "cpld_check" && request.Status == 1 {
		// 使用辅助函数处理 cpld_check 节点
		result, err := handleCpldCheckNode(request)
		if err != nil {
			handleCustomError(ctx, err.Error())
			return
		}

		// 将处理结果合并到 taskObject
		for key, value := range result {
			taskObject[key] = value
		}
	}

	if request.NodeID == "test_check" && request.Status == 1 {
		// 使用辅助函数处理 test_check 节点的文件上传
		result, newFileName, err := handleFileUploadNode(ctx, &procInst)
		if err != nil {
			handleCustomError(ctx, err.Error())
			return
		}

		// 将处理结果合并到 taskObject
		for key, value := range result {
			taskObject[key] = value
		}

		// 更新 fileName 变量
		fileName = newFileName
	}

	if request.NodeID == "hardware_audit" && request.Status == 1 {
		// 使用辅助函数处理 hardware_audit 节点的文件上传
		result, newFileName, err := handleFileUploadNode(ctx, &procInst)
		if err != nil {
			handleCustomError(ctx, err.Error())
			return
		}

		// 将处理结果合并到 taskObject
		for key, value := range result {
			taskObject[key] = value
		}

		// 更新 fileName 变量
		fileName = newFileName
	}

	err = transproductionrelease.UpdateFeatureTransaction(uId, id, request.TaskID, featureObject, taskObject)
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetProcPrevNodes(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	nodeID := ctx.FormValue("nodeId")
	procInst := dproductionprocinst.Response{}
	err := procInst.Find(id)
	if err != nil || procInst.ID == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	resource := procInst.Resource
	nodes, err := dproductionprocdef.GetNodes(resource)
	result, err := dproductionprocdef.GetBeforeNodes(nodes, nodeID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func DownloadDocFile(ctx iris.Context) {
	docType := ctx.FormValue("doc_type")
	docID, err := strconv.ParseUint(ctx.FormValue("doc_id"), 10, 64)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	document := dproductionproctaskdocument.Response{}
	err = document.FindByIdAndName(uint(docID), docType)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.SendFile(document.GeneratedDocumentPath, document.DocName)
}

func DownloadFeatureFile(ctx iris.Context) {
	packageType, err := strconv.Atoi(ctx.FormValue("package_type"))
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}
	id, _ := dao.GetId(ctx)
	procInst := dproductionprocinst.Response{}

	err = procInst.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	var upload string
	if packageType == 2 {
		upload = filepath.Join(libs.Config.ProductionFileStorage.Upload, procInst.Production.CreatedAt.Format("20060102"), procInst.Production.MainProgramFileMd5)

		ctx.SendFile(filepath.Join(upload, procInst.Production.MainProgramFileName), url.QueryEscape(procInst.Production.MainProgramFileName))
	} else if packageType == 1 {
		upload = filepath.Join(libs.Config.ProductionFileStorage.Upload, procInst.Production.CreatedAt.Format("20060102"), procInst.Production.MainProgramFileMd5)

		ctx.SendFile(filepath.Join(upload, procInst.Production.OspkgInstallName), url.QueryEscape(procInst.Production.OspkgInstallName))
	}

	return
}

func DownloadTestReport(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	procInst := dproductionprocinst.Response{}

	err := procInst.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	attachment, err := dproductionproctask.FindAttachmentByProc(id, "test_check")
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if attachment == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "没有找到文件"))
		return
	}
	var upload = filepath.Join(libs.Config.ProductionFileStorage.Upload, procInst.Production.CreatedAt.Format("20060102"), procInst.Production.MainProgramFileMd5)
	ctx.SendFile(filepath.Join(upload, attachment), attachment)
	return
}

func DownloadExpReport(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	procInst := dproductionprocinst.Response{}

	err := procInst.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	attachment, err := dproductionproctask.FindAttachmentByProc(id, "hardware_audit")
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if attachment == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "没有找到文件"))
		return
	}
	var upload = filepath.Join(libs.Config.ProductionFileStorage.Upload, procInst.Production.CreatedAt.Format("20060102"), procInst.Production.MainProgramFileMd5)
	ctx.SendFile(filepath.Join(upload, attachment), attachment)
	return
}

func GetDocumentList(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	docObjects := []map[string]interface{}{}
	tasks, err := dproductionproctask.FindByProcInstID(id)
	if err != nil {
		logging.ErrorLogger.Errorf("get task get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	for _, task := range tasks {

		// 处理创建下生产节点
		if task.NodeID == "start" {
			docObjects = append(docObjects, map[string]interface{}{
				"nodeId":     "start",
				"docName":    "创建下生产--节点",
				"ProcInstID": id,
			})
		}

		// 处理PM提交程序节点
		if task.NodeID == "pm_submit" {
			docObjects = append(docObjects, map[string]interface{}{
				"nodeId":     "pm_submit",
				"docName":    "PM提交程序--节点",
				"ProcInstID": id,
			})
		}

		// 处理PGTTL提交系统模板节点
		if task.NodeID == "system_template" {
			docObjects = append(docObjects, map[string]interface{}{
				"nodeId":     "system_template",
				"docName":    "PGTTL提交系统模板--节点",
				"ProcInstID": id,
			})
		}

		// 处理平台负责人节点
		if task.NodeID == "platform_mg" {
			docObjects = append(docObjects, map[string]interface{}{
				"nodeId":     "platform_mg",
				"docName":    "平台负责人--节点",
				"ProcInstID": id,
			})
		}

		// 处理PGTTL审核文档节点
		if task.NodeID == "pgttl_check" {
			docObjects = append(docObjects, map[string]interface{}{
				"nodeId":     "pgttl_check",
				"docName":    "PGTTL审核文档--节点",
				"ProcInstID": id,
			})
		}

		// 处理生测负责人节点
		if task.NodeID == "chief_tester" {
			docObjects = append(docObjects, map[string]interface{}{
				"nodeId":     "chief_tester",
				"docName":    "生测负责人--节点",
				"ProcInstID": id,
			})
		}

		// 处理硬件代表审核文档节点
		if task.NodeID == "hardware_rep" {
			docObjects = append(docObjects, map[string]interface{}{
				"nodeId":     "hardware_rep",
				"docName":    "硬件代表审核文档--节点",
				"ProcInstID": id,
			})
		}

		// 处理测试代表审核文档节点
		if task.NodeID == "test_rep" {
			docObjects = append(docObjects, map[string]interface{}{
				"nodeId":     "test_rep",
				"docName":    "测试代表审核文档--节点",
				"ProcInstID": id,
			})
		}

		// 处理CPLD在线升级节点，仅当有CpldUpdateUrl时才添加
		if task.NodeID == "cpld_check" {
			if task.CpldUpdateUrl != "" {
				docObjects = append(docObjects, map[string]interface{}{
					"nodeId":     "cpld_check",
					"docName":    "CPLD在线升级--节点",
					"ProcInstID": id,
				})
			}
		}

		// 处理测试验收节点，仅当有附件时才添加
		if task.NodeID == "test_check" {
			if task.Attachment != "" {
				docObjects = append(docObjects, map[string]interface{}{
					"nodeId":     "test_check",
					"docName":    "测试验收--节点",
					"ProcInstID": id,
				})
			}
		}

		// 处理硬件验收节点，仅当有附件时才添加
		if task.NodeID == "hardware_audit" {
			if task.Attachment != "" {
				docObjects = append(docObjects, map[string]interface{}{
					"nodeId":     "hardware_audit",
					"docName":    "硬件验收--节点",
					"ProcInstID": id,
				})
			}
		}

		// 处理QA审核节点
		if task.NodeID == "qa_audit" {
			docObjects = append(docObjects, map[string]interface{}{
				"nodeId":     "qa_audit",
				"docName":    "QA审核--节点",
				"ProcInstID": id,
			})
		}

	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, docObjects, response.NoErr.Msg))
	return

}

func ZipFiles(zipname string, files []string) error {
	_, err := exec.LookPath("zip")
	if err != nil {
		return errors.New("zip 未安装")
	}
	command := fmt.Sprintf(`zip -qruD %s %s`, zipname, strings.Join(files, " "))
	cmd := exec.Command("/bin/bash", "-c", command)
	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout // 标准输出
	cmd.Stderr = &stderr // 标准错误
	cmd.Dir = filepath.Dir(zipname)
	cmdErr := cmd.Run()
	outStr, _ := stdout.String(), stderr.String()
	if cmdErr != nil {
		logging.ErrorLogger.Errorf("cmd.Run() failed err", cmdErr)
		logging.ErrorLogger.Errorf("cmd.Run() outStr err", outStr)
		return cmdErr
	}
	return nil
}

func UpdateProcInstTemplate(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	taskID := ctx.FormValue("taskId")
	name := ctx.FormValue("name")

	// 限制频繁调用，使用带有过期时间的操作记录
	lastOpKey := fmt.Sprintf("procinst_template_lastop_%d", id)
	lastOpValue, err := cache.GetRedisClusterClient().GetKey(lastOpKey)
	if err == nil && lastOpValue != nil {
		// 如果存在上次操作记录，并且间隔小于5秒，则拒绝请求
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "操作太频繁，请稍后再试"))
		return
	}
	// 记录本次操作，有效期5秒
	cache.GetRedisClusterClient().Set(lastOpKey, time.Now().String(), 10*time.Second)

	procInst := dproductionprocinst.Response{}
	err = procInst.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	//保存文档
	f, fh, err := ctx.FormFile("template_file")
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	defer f.Close()
	//文档文件名称
	templateFileName := fh.Filename
	// 创建最终存放路径以及保存文件
	var upload = filepath.Join(libs.Config.ProductionFileStorage.Upload, procInst.Production.CreatedAt.Format("20060102"), procInst.Production.MainProgramFileMd5)
	err = os.MkdirAll(upload, 0750)
	os.Chmod(upload, 0750)

	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	templateFilePath := filepath.Join(upload, templateFileName)
	_, err = ctx.SaveFormFile(fh, templateFilePath)
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	//验证文档内是否存在标记词
	searchStrings := []string{
		"{{size}}",
		"{{md5}}",
		"{{name}}",
		"{{softwarenum}}",
		"{{softwarever}}",
		"{{ospkgName}}",
		"{{ospkgMD5}}",
		"{{ospkgSize}}",
		"{{ubootVersion}}",
		"{{time}}",
		"{{productModel}}",
	}
	docTemplateObjects := []map[string]interface{}{}
	//主程序模板验证
	found := CheckKeywordsInDocV2(templateFilePath, searchStrings)
	if !found {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "模板未包含标记符，请输入正确的模板"))
		return
	}
	docTemplateObjects = append(docTemplateObjects, map[string]interface{}{
		"CreatedAt":    time.Now(),
		"ProductModel": procInst.Production.ProductModel,
		"TemplateName": name,
		"TemplatePath": templateFilePath,
		"ProductionID": procInst.Production.ID,
		"ProcInstID":   procInst.ID,
	})

	//插入到文档模板数据库中
	err = easygorm.GetEasyGormDb().Model(&productionrelease.ProductionDocumentTemplate{}).Create(docTemplateObjects).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create detection Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	// 替换词、输出路径
	replacements := map[string]string{
		"{{time}}":               procInst.Production.ReleaseDate,
		"{{size}}":               strconv.FormatUint(uint64(procInst.Production.MainProgramFileSize), 10),
		"{{md5}}":                procInst.Production.MainProgramFileMd5,
		"{{name}}":               procInst.Production.MainProgramFileName,
		"{{softwarenum}}":        procInst.Production.SoftwareNumber,
		"{{softwarever}}":        procInst.Production.SoftwareVersion,
		"{{ospkgName}}":          procInst.Production.OspkgInstallName,
		"{{ospkgMD5}}":           procInst.Production.OspkgInstallMd5,
		"{{ospkgSize}}":          strconv.FormatUint(uint64(procInst.Production.OspkgInstallSize), 10),
		"{{ubootVersion}}":       procInst.PlatformTasks.UbootVersion,
		"{{productModel}}":       procInst.Production.ProductModel,
		"{{productModelSuffix}}": extractProductModelSuffix(procInst.Production.ProductModel),
	}
	//运行模板生成方法，生成对应文档
	uinttaskID, _ := strconv.ParseUint(taskID, 10, 0)
	err = CreateDocumentFileV2(&procInst, replacements, uint(uinttaskID))
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

// DEPRECATED: 此函数已被弃用，请使用 CreateDocumentFileV2 替代
// 保留此函数是为了兼容性，新代码应该直接使用 CreateDocumentFileV2
func CreateDocumentFile(procInst *dproductionprocinst.Response, replacements map[string]string, taskID uint) error {
	// 为了向后兼容性，现在此函数仅调用新版本的函数
	return CreateDocumentFileV2(procInst, replacements, taskID)
}

func CreateDocumentFileV2(procInst *dproductionprocinst.Response, replacements map[string]string, taskID uint) error {
	// 创建最终存放路径以及保存文件
	output := filepath.Join(libs.Config.ProductionFileStorage.OutputPath, procInst.Production.CreatedAt.Format("20060102"), procInst.Production.MainProgramFileMd5)
	if err := os.MkdirAll(output, 0750); err != nil {
		logging.ErrorLogger.Errorf("Error while creating output directory: %s", err)
		return fmt.Errorf("创建输出文档路径失败：%s", err)
	}
	os.Chmod(output, 0750)

	// 获取软件版本号
	softwareNum := extractSoftwareNum(procInst.Production.SoftwareVersion)

	// 检查 taskID 是否为平台任务
	if !isPlatformTask(taskID, procInst.ID) {
		// 如果不是平台任务，获取平台任务的 taskID
		platformTaskID, err := getPlatformTaskID(procInst.ID)
		if err != nil {
			return err
		}
		taskID = platformTaskID
	}

	// 定义文档信息
	documents := []struct {
		docType      string
		fileName     string
		templateID   *int
		templatePath *string
		replacements map[string]string
	}{
		{
			docType:      "mainProgram",
			fileName:     procInst.Production.ProductModel + "主程序-软件版本说明单.docx",
			templateID:   new(int),
			templatePath: new(string),
			replacements: replacements,
		},
		{
			docType:      "ospkg",
			fileName:     procInst.Production.ProductModel + "主程序ospkg-软件版本说明单.docx",
			templateID:   new(int),
			templatePath: new(string),
			replacements: replacements,
		},
	}

	// 获取模板路径
	for i := range documents {
		err := getTemplatePath(procInst, documents[i].docType, documents[i].templateID, documents[i].templatePath)
		if err != nil {
			return err
		}
	}

	// 删除旧文档
	if err := deleteOldDocuments(procInst.ID); err != nil {
		return err
	}

	// 生成新文档
	documentObjects := []map[string]interface{}{}
	for _, doc := range documents {
		err := replaceTextInDocByPython(*doc.templatePath, doc.replacements, filepath.Join(output, doc.fileName))
		if err != nil {
			logging.ErrorLogger.Errorf("替换文本时出错：%s", err)
			return fmt.Errorf("%s文档生成失败：%s", doc.docType, err)
		}
		documentObjects = append(documentObjects, map[string]interface{}{
			"CreatedAt":             time.Now(),
			"TaskID":                taskID,
			"ProductionID":          procInst.ProductionID,
			"ProcInstID":            procInst.ID,
			"DocName":               doc.fileName,
			"DocType":               doc.docType,
			"DocumentTemplateID":    *doc.templateID,
			"GeneratedDocumentPath": filepath.Join(output, doc.fileName),
		})
	}

	// 处理产品系列
	if procInst.Production.ProductSeries != "" {
		productModelSlice := strings.Split(procInst.Production.ProductModel, "|")
		for _, productModel := range productModelSlice {
			replacements["{{productModel}}"] = productModel
			replacements["{{productModelSuffix}}"] = extractProductModelSuffix(productModel)
			tempMachineFileName := productModel + "_" + softwareNum + "-整机测试说明.docx"

			// 动态获取模板路径
			machineTemplateID := new(int)
			machineTemplatePath := new(string)
			err := getTemplatePath(procInst, "machine", machineTemplateID, machineTemplatePath)
			if err != nil {
				return err
			}

			err = replaceTextInDocByPython(*machineTemplatePath, replacements, filepath.Join(output, tempMachineFileName))
			if err != nil {
				logging.ErrorLogger.Errorf("替换文本时出错：%s", err)
				return fmt.Errorf("整机测试文档生成失败:%s", err)
			}
			documentObjects = append(documentObjects, map[string]interface{}{
				"CreatedAt":             time.Now(),
				"TaskID":                taskID,
				"ProductionID":          procInst.ProductionID,
				"ProcInstID":            procInst.ID,
				"DocName":               tempMachineFileName,
				"DocType":               "machine",
				"DocumentTemplateID":    *machineTemplateID,
				"GeneratedDocumentPath": filepath.Join(output, tempMachineFileName),
			})
		}
	} else {
		// 单个产品型号处理
		machineTemplateID := new(int)
		machineTemplatePath := new(string)
		err := getTemplatePath(procInst, "machine", machineTemplateID, machineTemplatePath)
		if err != nil {
			return err
		}

		machineFileName := procInst.Production.ProductModel + "_" + softwareNum + "-整机测试说明.docx"
		err = replaceTextInDocByPython(*machineTemplatePath, replacements, filepath.Join(output, machineFileName))
		if err != nil {
			logging.ErrorLogger.Errorf("替换文本时出错：%s", err)
			return fmt.Errorf("整机测试文档生成失败:%s", err)
		}
		documentObjects = append(documentObjects, map[string]interface{}{
			"CreatedAt":             time.Now(),
			"TaskID":                taskID,
			"ProductionID":          procInst.ProductionID,
			"ProcInstID":            procInst.ID,
			"DocName":               machineFileName,
			"DocType":               "machine",
			"DocumentTemplateID":    *machineTemplateID,
			"GeneratedDocumentPath": filepath.Join(output, machineFileName),
		})
	}

	// 插入到文档表中
	if err := easygorm.GetEasyGormDb().Model(&productionrelease.ProductionProcTaskDocument{}).Create(documentObjects).Error; err != nil {
		logging.ErrorLogger.Errorf("create ProductionProcTaskDocument Error: %s", err)
		return fmt.Errorf("文档生成失败:%s", err)
	}

	return nil
}

// 检查 taskID 是否为平台任务
func isPlatformTask(taskID, procInstID uint) bool {
	task := dproductionproctask.Response{}
	err := easygorm.GetEasyGormDb().Model(dproductionproctask.Model()).Where("id = ? AND proc_inst_id = ? AND node_id = 'platform_mg'", taskID, procInstID).First(&task).Error
	return err == nil && task.ID > 0
}

// 获取平台任务的 taskID
func getPlatformTaskID(procInstID uint) (uint, error) {
	task := dproductionproctask.Response{}
	err := easygorm.GetEasyGormDb().Model(dproductionproctask.Model()).Where("proc_inst_id = ? AND node_id = 'platform_mg' AND status = 1 AND flag = true", procInstID).First(&task).Error
	if err != nil {
		return 0, fmt.Errorf("未找到平台任务: %s", err)
	}
	return task.ID, nil
}

func UpdateProcInstProcess(ctx iris.Context) {
	//更新下生产流程实例dproductionprocinst
	id, _ := dao.GetId(ctx)
	procInst := dproductionprocinst.Response{}
	err := procInst.Find(id)
	if err != nil || procInst.ID == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	resource := ctx.FormValue("resource")
	nodes, err := dproductionprocdef.GetNodes(resource)
	if err != nil {
		logging.ErrorLogger.Errorf("获取节点信息失败:%s", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	//遍历nodes提取节点	NodeID 一一对应的Assignee 数组 即[nodeid]=assignee
	nodeMap := make(map[string]uint)
	for _, node := range nodes {
		nodeMap[node.NodeID] = node.Assignee
	}
	//更新任务节点数据  done =0 AND statu=0 AND node_id = 'node_id'
	err = transproductionrelease.UpdateProcInstProcessTransaction(id, resource, nodes, nodeMap)
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveResource ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return

}

func downloadSVNFile(svnURL, savePath string, deleteAfter ...bool) (string, int64, error) {
	deleteAfterFlag := true
	if len(deleteAfter) > 0 {
		deleteAfterFlag = deleteAfter[0]
	}
	fileName := filepath.Base(svnURL)
	filePath := fmt.Sprintf("%s/%s", savePath, fileName)
	username := libs.Config.ProductionFileStorage.SVNUser
	password := libs.Config.ProductionFileStorage.SVNPass
	cmd := exec.Command("svn", "export", "--username", username, "--password", password, svnURL, filePath)

	cmd.Stdin = strings.NewReader(fmt.Sprintf("%s\n%s\n", username, password))

	err := cmd.Run()
	if err != nil {
		return "", 0, fmt.Errorf("SVN download failed: %s", err)
	}

	fmt.Println("SVN download successful!")

	// 计算文件的MD5值
	md5sum, err := libs.GetFileMd5(filePath)
	if err != nil {
		return "", 0, fmt.Errorf("Failed to calculate MD5: %s", err)
	}
	fileSize := getFileSize(filePath)
	if fileSize <= 0 {
		return "", 0, fmt.Errorf("文件下载大小为0,请确认SVN下载地址")
	}
	defer func() {
		if deleteAfterFlag {
			os.Remove(filePath)
		}
	}()

	return md5sum, fileSize, nil
}

func downloadSVNDocFile(svnURL, savePath string) (string, error) {

	fileName := filepath.Base(svnURL)
	filePath := filepath.Join(fmt.Sprintf("%s/%s", savePath, fileName))
	username := libs.Config.ProductionFileStorage.SVNUser
	password := libs.Config.ProductionFileStorage.SVNPass
	os.Remove(filePath)
	cmd := exec.Command("svn", "export", "--username", username, "--password", password, svnURL, filePath)
	cmd.Stdin = strings.NewReader(fmt.Sprintf("%s\n%s\n", username, password))
	cmd.Stderr = os.Stderr
	err := cmd.Run()
	if err != nil {
		fmt.Printf("SVN download failed: %s\n", err)
		return "", fmt.Errorf("SVN download failed: %s", err)
	}

	fmt.Println("SVN download doc successful!")
	return filePath, nil
}

func GetFilenameFromURL(rawURL string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(rawURL)
	if err != nil {
		return "", fmt.Errorf("error parsing URL: %v", err)
	}

	// 获取路径部分
	pathStr := parsedURL.Path

	// 使用path.Base获取文件名
	filename := path.Base(pathStr)

	return filename, nil
}

func UpdateProcInstDoc(ctx iris.Context) {
	id := ctx.FormValue("id")
	taskID := ctx.FormValue("taskId")
	procInst := dproductionprocinst.Response{}
	idUint, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		logging.ErrorLogger.Errorf("invalid id format: %s", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "Invalid ID format"))
		return
	}
	err = procInst.Find(uint(idUint))
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	// 替换词、输出路径
	replacements := map[string]string{
		"{{time}}":               procInst.Production.ReleaseDate,
		"{{size}}":               strconv.FormatUint(uint64(procInst.Production.MainProgramFileSize), 10),
		"{{md5}}":                procInst.Production.MainProgramFileMd5,
		"{{name}}":               procInst.Production.MainProgramFileName,
		"{{softwarenum}}":        procInst.Production.SoftwareNumber,
		"{{softwarever}}":        procInst.Production.SoftwareVersion,
		"{{ospkgName}}":          procInst.Production.OspkgInstallName,
		"{{ospkgMD5}}":           procInst.Production.OspkgInstallMd5,
		"{{ospkgSize}}":          strconv.FormatUint(uint64(procInst.Production.OspkgInstallSize), 10),
		"{{ubootVersion}}":       procInst.PlatformTasks.UbootVersion,
		"{{productModel}}":       procInst.Production.ProductModel,
		"{{productModelSuffix}}": extractProductModelSuffix(procInst.Production.ProductModel),
	}
	//运行模板生成方法，生成对应文档
	uinttaskID, _ := strconv.ParseUint(taskID, 10, 0)
	err = CreateDocumentFileV2(&procInst, replacements, uint(uinttaskID))
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func SynPMSData(ctx iris.Context) {
	name := ctx.FormValue("name")

	switch name {
	case "bug":
		ctx.WriteString("开始同步Bug数据,预计需要3分钟\n")
		go func() {
			lock.Lock()
			defer lock.Unlock()
			openfeishu.SyncAllBugDataV2("tbl9WQZVpeBXKPF1", "KmVRbOUQEa1MyIsDGGQcULohnMb") //BUG数据同步
			logging.ErrorLogger.Info("---同步Bug数据成功---")
		}()

	case "workpacket":
		ctx.WriteString("开始同步工作包数据,预计需要3分钟\n")
		go func() {
			lock.Lock()
			defer lock.Unlock()
			openfeishu.SyncAllWorkPacketData()
			logging.ErrorLogger.Info("---同步工作包数据成功----")
		}()

	default:
		ctx.WriteString("未定义的操作")
	}
}

// ------------------- 辅助函数 -------------------

// handleError 统一处理错误和返回错误响应
func handleError(ctx iris.Context, err error, message string) {
	logging.ErrorLogger.Errorf("%s: %v", message, err)
	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
}

// handleCustomError 处理自定义错误消息
func handleCustomError(ctx iris.Context, message string) {
	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, message))
}

// createDirIfNotExist 如果目录不存在则创建
func createDirIfNotExist(path string) error {
	err := os.MkdirAll(path, 0750)
	if err != nil {
		return err
	}
	os.Chmod(path, 0750)
	return nil
}

// downloadAndVerifyFile 下载文件并返回MD5和文件大小
func downloadAndVerifyFile(url, savePath string) (md5 string, fileSize int64, err error) {
	// 确保先删除可能已存在的文件
	os.Remove(savePath)

	// 尝试普通下载
	err = downloadFile(url, savePath)
	if err != nil {
		// 如果普通下载失败，尝试SVN下载
		md5, fileSize, err = downloadSVNFile(url, filepath.Dir(savePath), false)
		if err != nil {
			return "", 0, fmt.Errorf("下载文件失败: %w", err)
		}
		return md5, fileSize, nil
	}

	// 计算MD5和文件大小
	md5, err = libs.GetFileMd5(savePath)
	if err != nil {
		return "", 0, fmt.Errorf("计算MD5失败: %w", err)
	}

	fileSize = getFileSize(savePath)
	return strings.ToUpper(md5), fileSize, nil
}

// verifyDocContents 验证文档是否包含指定的MD5和文件大小
func verifyDocContents(docPath string, md5 string, fileSize int64) error {
	// 验证MD5
	md5found, err := CheckMD5InDoc(docPath, strings.ToUpper(md5))
	if err != nil {
		return fmt.Errorf("文档格式不正确: %w", err)
	}
	if !md5found {
		return fmt.Errorf("文档中未找到匹配的MD5值")
	}

	// 验证文件大小
	sizefound, err := CheckNumberInDoc(docPath, fileSize)
	if err != nil {
		return fmt.Errorf("文档格式不正确: %w", err)
	}
	if !sizefound {
		return fmt.Errorf("文档中未找到匹配的文件大小")
	}

	return nil
}

// TempFileManager 临时文件管理器
type TempFileManager struct {
	files []string
}

// NewTempFileManager 创建新的临时文件管理器
func NewTempFileManager() *TempFileManager {
	return &TempFileManager{files: []string{}}
}

// Add 添加文件到管理器
func (m *TempFileManager) Add(filepath string) {
	m.files = append(m.files, filepath)
}

// CleanAll 清理所有临时文件
func (m *TempFileManager) CleanAll() {
	for _, file := range m.files {
		os.Remove(file)
	}
}

// parseRequestForm 解析请求表单数据
func parseRequestForm(ctx iris.Context) (*TaskRequest, error) {
	request := &TaskRequest{}
	formValues := ctx.FormValues()

	// 解析除了setmacItems、setmacHosts之外的常规字段
	for key, values := range formValues {
		if len(values) == 0 {
			continue
		}

		value := values[0]
		switch key {
		case "status":
			fmt.Sscanf(value, "%d", &request.Status)
		case "nodeId":
			request.NodeID = value
		case "nextNodeID":
			request.NextNodeID = value
		case "userId":
			uintValue, _ := strconv.ParseUint(value, 10, 0)
			request.UserID = uint(uintValue)
		case "taskId":
			uintValue, _ := strconv.ParseUint(value, 10, 0)
			request.TaskID = uint(uintValue)
		case "comment":
			request.Comment = value
		case "product_models":
			request.ProductModels = value
		case "soft_versions":
			request.SoftVersions = value
		case "uboot":
			request.Uboot = value
		case "uboot_version":
			request.UbootVersion = value
		case "uboot_doc":
			request.UbootDoc = value
		case "rom_program":
			request.RomProgram = value
		case "setmac_tool":
			request.SetmacTool = value
		case "setmac_ini":
			request.SetmacIni = value
		case "setmac_doc":
			request.SetmacDoc = value
		case "manuf_test_program":
			request.ManufTestProgram = value
		case "manuf_test_program_doc":
			request.ManufTestProgramDoc = value
		case "manuf_test_report":
			request.ManufTestReport = value
		case "cpld_update":
			request.CpldUpdate = bool(value == "true")
		case "cpld_update_url":
			request.CpldUpdateUrl = value
		case "main_program_url":
			request.MainProgramUrl = value
		case "ospkg_install_url":
			request.OspkgInstallUrl = value
		case "version_desc":
			request.VersionDesc = value
		case "version_type":
			request.VersionType = value
		case "region_type":
			request.RegionType = value
		case "product_model":
			request.ProductModel = value
		case "release_date":
			request.ReleaseDate = value
		case "urgency":
			request.Urgency = bool(value == "true")
		}
	}

	// 解析setmacHosts动态字段
	parseSetmacHosts(formValues, request)

	// 解析setmacItems动态字段
	parseSetmacItems(formValues, request)

	return request, nil
}

// parseSetmacHosts 解析setmacHosts动态字段
func parseSetmacHosts(formValues map[string][]string, request *TaskRequest) {
	for key, values := range formValues {
		if !strings.HasPrefix(key, "setmacHosts[") || len(values) == 0 {
			continue
		}

		var index int
		var field string
		fmt.Sscanf(key, "setmacHosts[%d][%s]", &index, &field)

		// 根据解析出的索引扩展切片长度
		for len(request.SetmacHosts) <= index {
			request.SetmacHosts = append(request.SetmacHosts, SetmacHost{})
		}

		// 修剪可能的左右方括号
		field = strings.Trim(field, "[]")

		// 根据字段名设置值
		item := &request.SetmacHosts[index]
		switch field {
		case "tool":
			item.Tool = values[0]
			fileName, _ := extractFilename(values[0])
			if fileName != "" {
				item.Name = fileName
			}
		}
	}
}

// parseSetmacItems 解析setmacItems动态字段
func parseSetmacItems(formValues map[string][]string, request *TaskRequest) {
	for key, values := range formValues {
		if !strings.HasPrefix(key, "setmacItems[") || len(values) == 0 {
			continue
		}

		var index int
		var field string
		fmt.Sscanf(key, "setmacItems[%d][%s]", &index, &field)

		// 根据解析出的索引扩展切片长度
		for len(request.SetmacItems) <= index {
			request.SetmacItems = append(request.SetmacItems, SetmacItem{})
		}

		// 修剪可能的左右方括号
		field = strings.Trim(field, "[]")

		// 根据字段名设置值
		item := &request.SetmacItems[index]
		switch field {
		case "name":
			item.Name = values[0]
		case "tool":
			item.Tool = values[0]
		case "doc":
			item.Doc = values[0]
		}
	}
}

// handleStartNode 处理"start"节点的任务
func handleStartNode(ctx iris.Context, procInst *dproductionprocinst.Response, request *TaskRequest) (map[string]interface{}, error) {
	featureObject := map[string]interface{}{
		"UpdatedAt": time.Now(),
	}

	// 处理start节点表单数据
	if request.VersionDesc != "" {
		featureObject["VersionDesc"] = request.VersionDesc
	}
	if request.VersionType != "" {
		featureObject["VersionType"] = request.VersionType
	}
	if request.RegionType != "" {
		featureObject["RegionType"] = request.RegionType
	}
	if request.ProductModel != "" {
		featureObject["ProductModel"] = request.ProductModel
	}
	if request.ReleaseDate != "" {
		featureObject["ReleaseDate"] = request.ReleaseDate
	}
	featureObject["Urgency"] = request.Urgency

	// 注意：start节点不处理主程序链接信息，这些信息在PM节点中处理

	return featureObject, nil
}

// handleSystemTemplateNode 处理system_template节点的任务
func handleSystemTemplateNode(ctx iris.Context, procInst *dproductionprocinst.Response, request *TaskRequest) error {
	// 获取上传的文件
	f, fh, err := ctx.FormFile("main_program_file")
	if err != nil {
		return fmt.Errorf("获取主程序文件失败: %w", err)
	}
	defer f.Close()

	f2, fh2, err := ctx.FormFile("ospkg_install_file")
	if err != nil {
		return fmt.Errorf("获取ospkg安装文件失败: %w", err)
	}
	defer f2.Close()

	f3, fh3, err := ctx.FormFile("machine_file")
	if err != nil {
		return fmt.Errorf("获取机器文件失败: %w", err)
	}
	defer f3.Close()

	// 获取文件名
	mainProgramFileName := fh.Filename
	ospkgInstallFileName := fh2.Filename
	machineFileName := fh3.Filename

	// 创建最终存放路径
	upload := filepath.Join(libs.Config.ProductionFileStorage.Upload, procInst.Production.CreatedAt.Format("20060102"), procInst.Production.MainProgramFileMd5)
	if err := createDirIfNotExist(upload); err != nil {
		return fmt.Errorf("创建上传目录失败: %w", err)
	}

	// 保存文件
	mainProgramFilePath := filepath.Join(upload, mainProgramFileName)
	if _, err := ctx.SaveFormFile(fh, mainProgramFilePath); err != nil {
		return fmt.Errorf("保存主程序文件失败: %w", err)
	}

	ospkgInstallFilePath := filepath.Join(upload, ospkgInstallFileName)
	if _, err := ctx.SaveFormFile(fh2, ospkgInstallFilePath); err != nil {
		return fmt.Errorf("保存ospkg安装文件失败: %w", err)
	}

	machineFilePath := filepath.Join(upload, machineFileName)
	if _, err := ctx.SaveFormFile(fh3, machineFilePath); err != nil {
		return fmt.Errorf("保存机器文件失败: %w", err)
	}

	// 验证文档标记词
	searchStrings := []string{
		"{{size}}",
		"{{md5}}",
		"{{name}}",
		"{{softwarenum}}",
		"{{softwarever}}",
		"{{ospkgName}}",
		"{{ospkgMD5}}",
		"{{ospkgSize}}",
		"{{ubootVersion}}",
		"{{time}}",
		"{{productModel}}",
	}

	// 准备文档模板对象
	docTemplateObjects := []map[string]interface{}{}

	// 验证主程序模板
	if !CheckKeywordsInDocV2(mainProgramFilePath, searchStrings) {
		return fmt.Errorf("主程序模板未包含标记符，请输入正确的模板")
	}

	docTemplateObjects = append(docTemplateObjects, map[string]interface{}{
		"CreatedAt":    time.Now(),
		"ProductModel": procInst.Production.ProductModel,
		"TemplateName": "mainProgram",
		"TemplatePath": mainProgramFilePath,
		"ProductionID": procInst.Production.ID,
		"ProcInstID":   procInst.ID,
	})

	// 验证ospkg_install_file模板
	if !CheckKeywordsInDocV2(ospkgInstallFilePath, searchStrings) {
		return fmt.Errorf("ospkg_install_file模板未包含标记符，请输入正确的模板")
	}

	docTemplateObjects = append(docTemplateObjects, map[string]interface{}{
		"CreatedAt":    time.Now(),
		"ProductModel": procInst.Production.ProductModel,
		"TemplateName": "ospkg",
		"TemplatePath": ospkgInstallFilePath,
		"ProductionID": procInst.Production.ID,
		"ProcInstID":   procInst.ID,
	})

	// 验证machine_file模板
	if !CheckKeywordsInDocV2(machineFilePath, searchStrings) {
		return fmt.Errorf("machine_file模板未包含标记符，请输入正确的模板")
	}

	docTemplateObjects = append(docTemplateObjects, map[string]interface{}{
		"CreatedAt":    time.Now(),
		"ProductModel": procInst.Production.ProductModel,
		"TemplateName": "machine",
		"TemplatePath": machineFilePath,
		"ProductionID": procInst.Production.ID,
		"ProcInstID":   procInst.ID,
	})

	// 插入到文档模板数据库中
	err = easygorm.GetEasyGormDb().Model(&productionrelease.ProductionDocumentTemplate{}).Create(docTemplateObjects).Error
	if err != nil {
		return fmt.Errorf("创建文档模板记录失败: %w", err)
	}

	return nil
}

// handlePlatformMgNode 处理platform_mg节点的任务
func handlePlatformMgNode(ctx iris.Context, procInst *dproductionprocinst.Response, request *TaskRequest) (map[string]interface{}, error) {
	taskObject := map[string]interface{}{}

	// 清理历史数据
	// 查看是否存在Setmac扩展项
	lastPlatformExtensions := []*dproductionproctaskextension.Response{}
	if err := easygorm.GetEasyGormDb().Model(dproductionproctaskextension.Model()).Where("proc_inst_id =? and task_id=?", procInst.ID, request.TaskID).Find(&lastPlatformExtensions).Error; err != nil {
		return nil, fmt.Errorf("任务数据查询失败: %w", err)
	}

	// 删除多余的Setmac扩展项数据
	if len(lastPlatformExtensions) > 0 {
		if err := easygorm.GetEasyGormDb().Delete(dproductionproctaskextension.Model(), "proc_inst_id =? and task_id=?", procInst.ID, request.TaskID).Error; err != nil {
			return nil, fmt.Errorf("Setmac扩展项数据处理失败: %w", err)
		}
	}

	// 查看是否存在SetmacHost项
	lastSetmacHosts := []*dproductionproctaskhost.Response{}
	if err := easygorm.GetEasyGormDb().Model(dproductionproctaskhost.Model()).Where("proc_inst_id =? and task_id=?", procInst.ID, request.TaskID).Find(&lastSetmacHosts).Error; err != nil {
		return nil, fmt.Errorf("任务数据查询失败: %w", err)
	}

	// 删除多余的SetmacHost项
	if len(lastSetmacHosts) > 0 {
		if err := easygorm.GetEasyGormDb().Delete(dproductionproctaskhost.Model(), "proc_inst_id =? and task_id=?", procInst.ID, request.TaskID).Error; err != nil {
			return nil, fmt.Errorf("Setmac扩展项数据处理失败: %w", err)
		}
	}

	// 处理各种文件和验证
	// 记录基本信息
	taskObject["Uboot"] = request.Uboot
	taskObject["UbootVersion"] = request.UbootVersion
	taskObject["UbootDoc"] = request.UbootDoc
	taskObject["RomProgram"] = request.RomProgram
	taskObject["SetmacTool"] = request.SetmacTool
	taskObject["SetmacDoc"] = request.SetmacDoc

	// 下载并验证Uboot文件
	UbootMd5, UbootFieSize, err := downloadSVNFile(request.Uboot, libs.Config.ProductionFileStorage.Upload)
	if err != nil {
		return nil, fmt.Errorf("uboot文件下载失败,请输入正确的SVN地址: %w", err)
	}

	// 下载并验证Uboot文档
	ubootDocFilePath, err := downloadSVNDocFile(request.UbootDoc, libs.Config.ProductionFileStorage.Upload)
	if err != nil {
		return nil, fmt.Errorf("uboot说明文档下载失败,请输入正确的SVN地址: %w", err)
	}
	defer os.Remove(ubootDocFilePath)

	taskObject["UbootMd5"] = strings.ToUpper(UbootMd5)

	// 验证Uboot文档中的MD5值
	UbootMd5found, err := CheckMD5InDoc(ubootDocFilePath, strings.ToUpper(UbootMd5))
	if err != nil {
		return nil, fmt.Errorf("uboot说明文档格式不正确，请输入正确docx格式的Word文档: %w", err)
	}

	if !UbootMd5found {
		return nil, fmt.Errorf("uboot说明文档未找到匹配的UbootMd5,请输入正确的文档或者uboot地址")
	}

	// 验证Uboot文档中的文件大小
	UbootFieSizefound, err := CheckNumberInDoc(ubootDocFilePath, UbootFieSize)
	if err != nil {
		return nil, fmt.Errorf("uboot说明文档格式不正确，请输入正确docx格式的Word文档: %w", err)
	}

	if !UbootFieSizefound {
		return nil, fmt.Errorf("uboot说明文档未找到匹配的文件大小,请输入正确的文档或者uboot地址")
	}

	// 处理ROM程序
	ROMProgramMd5, _, err := downloadSVNFile(request.RomProgram, libs.Config.ProductionFileStorage.Upload)
	if err != nil {
		return nil, fmt.Errorf("ROM文件下载失败,请输入正确的SVN地址: %w", err)
	}
	taskObject["RomProgramMd5"] = strings.ToUpper(ROMProgramMd5)

	// 处理Setmac工具文档
	SetmacDocFilePath, err := downloadSVNDocFile(request.SetmacDoc, libs.Config.ProductionFileStorage.Upload)
	if err != nil {
		return nil, fmt.Errorf("Setmac工具说明文档下载失败,请输入正确的SVN地址: %w", err)
	}
	defer os.Remove(SetmacDocFilePath)

	// 处理Setmac工具
	SetmacToolMd5, SetmacToolFieSize, err := downloadSVNFile(request.SetmacTool, libs.Config.ProductionFileStorage.Upload)
	if err != nil {
		return nil, fmt.Errorf("Setmac工具下载失败,请输入正确的SVN地址: %w", err)
	}
	taskObject["SetmacToolMd5"] = strings.ToUpper(SetmacToolMd5)

	// 验证Setmac工具的MD5
	SetmacToolMd5found, err := CheckMD5InDoc(SetmacDocFilePath, strings.ToUpper(SetmacToolMd5))
	if err != nil {
		return nil, fmt.Errorf("Setmac工具说明文档格式不正确，请输入正确docx格式的Word文档: %w", err)
	}

	if !SetmacToolMd5found {
		return nil, fmt.Errorf("Setmac工具说明文档未找到匹配的SetmacToolMd5,请输入正确的文档或者Setmac工具地址")
	}

	// 验证Setmac工具的文件大小
	SetmacToolFieSizefound, err := CheckNumberInDoc(SetmacDocFilePath, SetmacToolFieSize)
	if err != nil {
		return nil, fmt.Errorf("Setmac工具说明文档格式不正确，请输入正确docx格式的Word文档: %w", err)
	}

	if !SetmacToolFieSizefound {
		return nil, fmt.Errorf("Setmac工具说明文档未找到匹配的工具文件大小,请输入正确的文档或者Setmac工具地址")
	}

	// 处理SetmacHosts
	setmacHostObjects := []map[string]interface{}{}
	if len(request.SetmacHosts) > 0 {
		for index, setmacHostItem := range request.SetmacHosts {
			itemMd5, itemFileSize, err := downloadSVNFile(setmacHostItem.Tool, libs.Config.ProductionFileStorage.Upload)
			if err != nil {
				return nil, fmt.Errorf("Setmac主机(%s)下载失败，请输入正确的SVN地址", setmacHostItem.Name)
			}

			// 验证文档中的MD5
			itemMd5found, err := CheckMD5InDoc(SetmacDocFilePath, strings.ToUpper(itemMd5))
			if err != nil {
				return nil, fmt.Errorf("Setmac工具说明文档未找到匹配的SetmacIniMd5，请输入正确docx格式的Word文档: %w", err)
			}

			if !itemMd5found {
				return nil, fmt.Errorf("Setmac工具说明文档未找到匹配的SetmacIniMd5，请输入正确docx格式的Word文档")
			}

			// 验证文档中的文件大小
			itemFieSizefound, err := CheckNumberInDoc(SetmacDocFilePath, itemFileSize)
			if err != nil {
				return nil, fmt.Errorf("Setmac工具说明文档未找到匹配的SetmacIni文件大小，请输入正确docx格式的Word文档: %w", err)
			}

			if !itemFieSizefound {
				return nil, fmt.Errorf("Setmac工具说明文档未找到匹配的SetmacIni文件大小，请输入正确docx格式的Word文档")
			}

			request.SetmacHosts[index].MD5 = strings.ToUpper(itemMd5)
			setmacHostObjects = append(setmacHostObjects, map[string]interface{}{
				"CreatedAt":     time.Now(),
				"TaskID":        request.TaskID,
				"ExtensionName": setmacHostItem.Name,
				"ExtensionTool": setmacHostItem.Tool,
				"MD5":           strings.ToUpper(itemMd5),
				"ProcInstID":    procInst.ID,
			})
		}

		// 插入到数据库
		if len(setmacHostObjects) > 0 {
			if err := easygorm.GetEasyGormDb().Model(&productionrelease.ProductionProcTaskHost{}).Create(setmacHostObjects).Error; err != nil {
				return nil, fmt.Errorf("创建Setmac主机记录失败: %w", err)
			}
		}
	}

	// 处理SetmacItems
	setmacObjects := []map[string]interface{}{}
	if len(request.SetmacItems) > 0 {
		for index, setmacItem := range request.SetmacItems {
			// 下载说明文档
			docFilePath, err := downloadSVNDocFile(setmacItem.Doc, libs.Config.ProductionFileStorage.Upload)
			if err != nil {
				return nil, fmt.Errorf("%s说明文档下载失败,请输入正确的SVN地址", setmacItem.Name)
			}
			defer os.Remove(docFilePath)

			// 下载工具
			itemMd5, itemFieSize, err := downloadSVNFile(setmacItem.Tool, libs.Config.ProductionFileStorage.Upload)
			if err != nil {
				return nil, fmt.Errorf("Setmac扩展项(%s)下载失败，请输入正确的SVN地址", setmacItem.Name)
			}

			// 验证文档中的MD5
			itemMd5found, err := CheckMD5InDoc(docFilePath, strings.ToUpper(itemMd5))
			if err != nil {
				return nil, fmt.Errorf("Setmac扩展项(%s)说明文档格式不正确，请输入正确docx格式的Word文档: %w", setmacItem.Name, err)
			}

			if !itemMd5found {
				return nil, fmt.Errorf("Setmac扩展项(%s)说明文档未找到匹配的Md5,请输入正确的文档或者Setmac扩展项下载地址", setmacItem.Name)
			}

			// 验证文档中的文件大小
			itemFieSizefound, err := CheckNumberInDoc(docFilePath, itemFieSize)
			if err != nil {
				return nil, fmt.Errorf("Setmac扩展项(%s)说明文档格式不正确，请输入正确docx格式的Word文档: %w", setmacItem.Name, err)
			}

			if !itemFieSizefound {
				return nil, fmt.Errorf("Setmac扩展项(%s)说明文档未找到匹配的文件大小值,请输入正确的文档或者Setmac扩展项下载地址", setmacItem.Name)
			}

			request.SetmacItems[index].MD5 = strings.ToUpper(itemMd5)
			setmacObjects = append(setmacObjects, map[string]interface{}{
				"CreatedAt":     time.Now(),
				"TaskID":        request.TaskID,
				"ExtensionName": setmacItem.Name,
				"ExtensionTool": setmacItem.Tool,
				"MD5":           strings.ToUpper(itemMd5),
				"ExtensionDoc":  setmacItem.Doc,
				"ProcInstID":    procInst.ID,
			})
		}

		// 插入到扩展项数据库
		if len(setmacObjects) > 0 {
			if err := easygorm.GetEasyGormDb().Model(&productionrelease.ProductionProcTaskExtension{}).Create(setmacObjects).Error; err != nil {
				return nil, fmt.Errorf("创建扩展项记录失败: %w", err)
			}
		}
	}

	// 处理文档替换
	replacements := map[string]string{
		"{{time}}":         procInst.Production.ReleaseDate,
		"{{size}}":         strconv.FormatUint(uint64(procInst.Production.MainProgramFileSize), 10),
		"{{md5}}":          procInst.Production.MainProgramFileMd5,
		"{{name}}":         procInst.Production.MainProgramFileName,
		"{{softwarenum}}":  procInst.Production.SoftwareNumber,
		"{{softwarever}}":  procInst.Production.SoftwareVersion,
		"{{ospkgName}}":    procInst.Production.OspkgInstallName,
		"{{ospkgMD5}}":     procInst.Production.OspkgInstallMd5,
		"{{ospkgSize}}":    strconv.FormatUint(uint64(procInst.Production.OspkgInstallSize), 10),
		"{{ubootVersion}}": request.UbootVersion,
		"{{productModel}}": procInst.Production.ProductModel,
	}

	// 生成文档
	if err := CreateDocumentFileV2(procInst, replacements, request.TaskID); err != nil {
		return nil, err
	}

	return taskObject, nil
}

// handleChiefTesterNode 处理chief_tester节点的任务
func handleChiefTesterNode(ctx iris.Context, procInst *dproductionprocinst.Response, request *TaskRequest) (map[string]interface{}, error) {
	taskObject := map[string]interface{}{}

	// 获取生测程序文件名
	mainTestProgramName := extractFileNameFromURL(request.ManufTestProgram)

	// 创建临时存放文件夹
	tempDir := filepath.Join(libs.Config.ProductionFileStorage.Temp, "manuf", time.Now().Format("20060102"), procInst.Production.Uuid)
	if err := createDirIfNotExist(tempDir); err != nil {
		return nil, fmt.Errorf("创建临时目录失败: %w", err)
	}

	// 下载生测程序文件
	mainTestProgramPath := filepath.Join(tempDir, mainTestProgramName)
	os.Remove(mainTestProgramPath) // 确保不存在旧文件

	if err := downloadFile(request.ManufTestProgram, mainTestProgramPath); err != nil {
		// 如果普通下载失败，尝试SVN下载
		_, _, err := downloadSVNFile(request.ManufTestProgram, tempDir, false)
		if err != nil {
			return nil, fmt.Errorf("生测程序文件下载失败: %w", err)
		}
	}

	// 获取生测程序文件MD5和大小
	mainTestProgramMD5, err := libs.GetFileMd5(mainTestProgramPath)
	if err != nil {
		return nil, fmt.Errorf("生测程序文件MD5获取失败: %w", err)
	}
	mainTestFileSize := getFileSize(mainTestProgramPath)

	// 下载生产测试文档说明
	docFilePath, err := downloadSVNDocFile(request.ManufTestProgramDoc, libs.Config.ProductionFileStorage.Upload)
	if err != nil {
		return nil, fmt.Errorf("生测程序说明文档下载失败,请输入正确的SVN地址: %w", err)
	}
	defer os.Remove(docFilePath)

	// 验证生产测试说明文档中的MD5值
	mainTestMd5found, err := CheckMD5InDoc(docFilePath, strings.ToUpper(mainTestProgramMD5))
	if err != nil {
		return nil, fmt.Errorf("生测程序说明文档格式不正确，请输入正确docx格式的Word文档: %w", err)
	}

	if !mainTestMd5found {
		return nil, fmt.Errorf("生测程序说明文档未找到匹配的Md5,请输入正确的文档或者生测程序地址")
	}

	// 验证生产测试说明文档中的文件大小
	mainTestFieSizefound, err := CheckNumberInDoc(docFilePath, mainTestFileSize)
	if err != nil {
		return nil, fmt.Errorf("生测程序说明文档格式不正确，请输入正确docx格式的Word文档: %w", err)
	}

	if !mainTestFieSizefound {
		return nil, fmt.Errorf("生测程序说明文档未找到匹配的文件大小,请输入正确的文档或者生测程序地址")
	}

	// 设置任务对象
	taskObject["ManufTestProgram"] = request.ManufTestProgram
	taskObject["ManufTestProgramMD5"] = strings.ToUpper(mainTestProgramMD5)
	taskObject["ManufTestProgramDoc"] = request.ManufTestProgramDoc
	taskObject["ManufTestReport"] = request.ManufTestReport

	return taskObject, nil
}

// handleCpldCheckNode 处理cpld_check节点的任务
func handleCpldCheckNode(request *TaskRequest) (map[string]interface{}, error) {
	// CPLD 不需要验证
	taskObject := map[string]interface{}{
		"CpldUpdate":    request.CpldUpdate,
		"CpldUpdateUrl": request.CpldUpdateUrl,
	}

	return taskObject, nil
}

// handleFileUploadNode 处理文件上传的节点（test_check 和 hardware_audit）
func handleFileUploadNode(ctx iris.Context, procInst *dproductionprocinst.Response) (map[string]interface{}, string, error) {
	f, fh, err := ctx.FormFile("file")
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		return nil, "", err
	}
	defer f.Close()

	// 构造文件名称
	fileName := fh.Filename
	taskObject := map[string]interface{}{
		"Attachment": fileName,
	}

	// 创建最终存放路径以及保存文件
	var upload = filepath.Join(libs.Config.ProductionFileStorage.Upload, procInst.Production.CreatedAt.Format("20060102"), procInst.Production.MainProgramFileMd5)
	err = os.MkdirAll(upload, 0750)
	os.Chmod(upload, 0750)

	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		return nil, "", err
	}

	// 创建申请单
	tempFn := filepath.Join(upload, fileName)
	_, err = ctx.SaveFormFile(fh, tempFn)
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		return nil, "", err
	}

	return taskObject, fileName, nil
}

func handlePMSubmitNode(ctx iris.Context, procInst *dproductionprocinst.Response, request *TaskRequest) (map[string]interface{}, error) {
	featureObject := map[string]interface{}{
		"UpdatedAt": time.Now(),
	}

	// 处理主程序URL
	mainProgramUrl := request.MainProgramUrl
	if mainProgramUrl == "" {
		return nil, fmt.Errorf("主程序URL不能为空")
	}

	featureObject["MainProgramUrl"] = mainProgramUrl
	mainProgramFileName := extractFileNameFromURL(mainProgramUrl)
	if mainProgramFileName == "" {
		return nil, fmt.Errorf("无法从URL提取主程序文件名")
	}
	featureObject["MainProgramFileName"] = mainProgramFileName

	// 处理ospkg安装URL
	ospkgInstallUrl := request.OspkgInstallUrl
	if ospkgInstallUrl == "" {
		newURL, err := RemoveSpecificSegmentFromURL(mainProgramUrl, "releaseID-bin")
		if err != nil {
			return nil, err
		}

		statusCode, _, bodyBytes, err := libs.Get(newURL, map[string]string{}, map[string]string{})
		if err != nil {
			return nil, fmt.Errorf("主程序链接异常：%w", err)
		}
		if statusCode != 200 {
			return nil, fmt.Errorf("主程序链接信息获取失败，返回非200，响应码为：%d", statusCode)
		}

		content := string(bodyBytes)
		links := ExtractOspkgLinks(content)
		if len(links) > 0 {
			ospkgInstallUrl = generateNewURL(newURL, string(links[0]))
			request.OspkgInstallUrl = ospkgInstallUrl
		} else {
			return nil, fmt.Errorf("无法自动获取ospkg-install URL")
		}
	}
	featureObject["OspkgInstallUrl"] = ospkgInstallUrl

	// 获取编译版本号
	re := regexp.MustCompile(`/output/([a-f0-9]{32})/`)
	mainProgramMatches := re.FindStringSubmatch(mainProgramUrl)
	mainProgramJobID := ""
	if len(mainProgramMatches) > 1 {
		mainProgramJobID = mainProgramMatches[1]
	}

	ospkgInstallMatches := re.FindStringSubmatch(ospkgInstallUrl)
	ospkgInstallJobID := ""
	if len(ospkgInstallMatches) > 1 {
		ospkgInstallJobID = ospkgInstallMatches[1]
	}

	// 产品类型验证
	if procInst.Production.ProductionType == "1" {
		if mainProgramJobID == "" || ospkgInstallJobID == "" {
			return nil, fmt.Errorf("主程序和ospkg-install链接不正确")
		}
	}

	// 获取每日编译记录
	cronMakeJob := &dcronmakejob.CronMakeJob{}
	if mainProgramJobID != "" {
		err := easygorm.GetEasyGormDb().Model(&buildfarm.CronMakeJob{}).Where("job_id = ?", mainProgramJobID).Find(&cronMakeJob).Error
		if err != nil {
			return nil, fmt.Errorf("获取编译记录失败: %w", err)
		}
	}
	if cronMakeJob.Id <= 0 {
		return nil, fmt.Errorf("主程序编译记录不存在")
	}

	// 处理软件版本号
	softwareNumber := cronMakeJob.SoftwareNumber
	softwareVersion := cronMakeJob.SoftwareVersion

	// 预鉴、中试带release  首量和质量版本不带release 需要去除release
	if procInst.Production.ProductionType == "3" || procInst.Production.ProductionType == "4" {
		re := regexp.MustCompile(`[,\s]*Release\(.*\)`)
		softwareVersion = re.ReplaceAllString(softwareVersion, "")
	}

	featureObject["SoftwareNumber"] = softwareNumber
	featureObject["SoftwareVersion"] = softwareVersion

	ospkgInstallName := extractFileNameFromURL(ospkgInstallUrl)
	featureObject["OspkgInstallName"] = ospkgInstallName

	// 创建临时存放文件夹
	uuid := procInst.Production.Uuid
	if uuid == "" {
		uuid = libs.GetUUID() // 如果没有UUID则生成一个
	}

	tempDir := filepath.Join(libs.Config.ProductionFileStorage.Temp, "production", time.Now().Format("20060102"), uuid)
	if err := createDirIfNotExist(tempDir); err != nil {
		return nil, fmt.Errorf("创建临时目录失败: %w", err)
	}

	var mainProgramFileMd5, ospkgInstallMd5 string
	var mainProgramFileSize, ospkgInstallFileSize int64

	// 检查是否为迭代发布，如果是，检查主程序链接和上个版本是否一致
	productionCheck := dproduction.Response{}
	if procInst.Production.ProductionBaseVersion > 0 {
		if err := easygorm.GetEasyGormDb().Model(&productionrelease.Production{}).
			Where("id = ?", procInst.Production.ProductionBaseVersion).
			Find(&productionCheck).Error; err != nil {
			return nil, fmt.Errorf("获取基础版本失败: %w", err)
		}

		if request.MainProgramUrl == productionCheck.MainProgramUrl {
			// 使用上个版本的数据
			mainProgramFileMd5 = productionCheck.MainProgramFileMd5
			mainProgramFileSize = int64(productionCheck.MainProgramFileSize)
		}
	}

	// 如果不是使用上个版本的数据，则下载文件并计算MD5
	if mainProgramFileMd5 == "" {
		// 下载主程序文件
		mainProgramFilePath := filepath.Join(tempDir, mainProgramFileName)
		os.Remove(mainProgramFilePath) // 确保不存在旧文件

		if err := downloadFile(mainProgramUrl, mainProgramFilePath); err != nil {
			return nil, fmt.Errorf("主程序文件下载失败: %w", err)
		}

		// 获取主程序文件MD5和大小
		var err error
		mainProgramFileMd5, err = libs.GetFileMd5(mainProgramFilePath)
		if err != nil {
			return nil, fmt.Errorf("获取主程序MD5失败: %w", err)
		}
		mainProgramFileMd5 = strings.ToUpper(mainProgramFileMd5)
		mainProgramFileSize = getFileSize(mainProgramFilePath)
	}

	featureObject["MainProgramFileSize"] = mainProgramFileSize
	featureObject["MainProgramFileMd5"] = mainProgramFileMd5

	// 检查是否为迭代发布，如果是，检查ospkg链接和上个版本是否一致
	if procInst.Production.ProductionBaseVersion > 0 && request.OspkgInstallUrl == productionCheck.OspkgInstallUrl {
		// 使用上个版本的数据
		ospkgInstallMd5 = productionCheck.OspkgInstallMd5
		ospkgInstallFileSize = int64(productionCheck.OspkgInstallSize)
	} else {
		// 下载ospkg-install文件
		ospkgInstallFilePath := filepath.Join(tempDir, ospkgInstallName)
		os.Remove(ospkgInstallFilePath) // 确保不存在旧文件

		if err := downloadFile(ospkgInstallUrl, ospkgInstallFilePath); err != nil {
			return nil, fmt.Errorf("ospkg-install文件下载失败: %w", err)
		}

		// 获取ospkg-install文件MD5和大小
		var err error
		ospkgInstallMd5, err = libs.GetFileMd5(ospkgInstallFilePath)
		if err != nil {
			return nil, fmt.Errorf("获取ospkg-install MD5失败: %w", err)
		}
		ospkgInstallMd5 = strings.ToUpper(ospkgInstallMd5)
		ospkgInstallFileSize = getFileSize(ospkgInstallFilePath)
	}

	featureObject["OspkgInstallSize"] = ospkgInstallFileSize
	featureObject["OspkgInstallMd5"] = ospkgInstallMd5

	// 如果是预鉴版本，检查是否已存在相同文件发布记录
	if procInst.Production.ProductionType == "1" {
		check := dproduction.Response{}
		err := check.CheckExists(mainProgramFileMd5, ospkgInstallMd5, procInst.Production.ProductModel, procInst.Production.ProductionType, procInst.Production.RegionType)
		if err != nil {
			return nil, fmt.Errorf("检查文件发布记录失败: %w", err)
		}

		if check.ID > 0 {
			return nil, fmt.Errorf("已经存在相同文件发布记录")
		}
	}

	return featureObject, nil
}
