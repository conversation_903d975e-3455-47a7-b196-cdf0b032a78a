package schedule

import (
	"sync"
	"time"

	"irisAdminApi/application/controllers/openfeishu"
	"irisAdminApi/application/logging"
)

var feishuLock sync.Mutex

// 获取项目数据
func RunFeiShuData() {
	feishuLock.Lock()
	defer feishuLock.Unlock()
	openfeishu.ProjectDocumentSyncWorker()
	t := time.NewTicker(1 * time.Hour)
	go func() {
		for {
			select {
			case <-t.C:
				openfeishu.ProjectDocumentSyncWorker()
			}
		}
	}()
}

// 更新项目数据
func RunUpdateFeiShuData() {
	feishuLock.Lock()
	defer feishuLock.Unlock()
	// openfeishu.ProjectDocumentUpdateWorker()
	t := time.NewTicker(70 * time.Minute)
	go func() {
		for {
			select {
			case <-t.C:
				openfeishu.ProjectDocumentUpdateWorker()
			}
		}
	}()
}

// 更新文档评论数据
func RunFeiShuDocumentCommentData() {
	feishuLock.Lock()
	defer feishuLock.Unlock()
	openfeishu.ProjectDocumentCommentWorker()
	t := time.NewTicker(40 * time.Minute)
	go func() {
		for {
			select {
			case <-t.C:
				openfeishu.ProjectDocumentCommentWorker()
			}
		}
	}()
}

// 获取云端评审状态数据
func RunFeiShuDocumentReviewStatus() {
	feishuLock.Lock()
	defer feishuLock.Unlock()
	openfeishu.DocumentReviewStatusWorker()
	t := time.NewTicker(20 * time.Minute)
	go func() {
		for {
			select {
			case <-t.C:
				openfeishu.DocumentReviewStatusWorker()
			}
		}
	}()
}

// 更新用户OpenID数据
func RunGetFeiShuOpenID() {
	feishuLock.Lock()
	defer feishuLock.Unlock()
	openfeishu.SyncFeiShuOpenID()
	t := time.NewTicker(12 * time.Hour)
	go func() {
		for {
			select {
			case <-t.C:
				openfeishu.SyncFeiShuOpenID()
			}
		}
	}()
}

// 提醒评委更新评审状态和评论性质
func RunFeiShuDocumentReviewStatusRemind() {
	feishuLock.Lock()
	defer feishuLock.Unlock()
	_, err := Cron.AddFunc("0 9 * * *", func() { openfeishu.NotifyReviewersForReviewTypeAndStatus() })
	if err != nil {
		logging.ErrorLogger.Error("add daily 9am NotifyReviewersForReviewTypeAndStatus cron job err", err)
	}
	_, err = Cron.AddFunc("30 9 * * *", func() { openfeishu.NotifyReviewersForReviewComment() })
	if err != nil {
		logging.ErrorLogger.Error("add daily 9:30am NotifyReviewersForReviewComment cron job err", err)
	}
}

// 获取PMS数据
func RunPMSData() {
	feishuLock.Lock()
	defer feishuLock.Unlock()
	_, err := Cron.AddFunc("10 * * * *", func() { openfeishu.SyncPMSData() })
	if err != nil {
		logging.ErrorLogger.Error("add SyncPMSData cron job err", err)
	}
	_, err = Cron.AddFunc("0 8 * * 1", func() { openfeishu.SyncAllResourceData() })
	if err != nil {
		logging.ErrorLogger.Error("add SyncAllResourceData cron job err", err)
	}
}
// 补丁项目文档制作
func RunFeishuProjectData() {
	lock.Lock()
	defer lock.Unlock()
	_, err := Cron.AddFunc("50 * * * *", func() { openfeishu.GetWorkItemData() })
	if err != nil {
		logging.ErrorLogger.Error("add GetWorkItemData cron job err", err)
	}
}
