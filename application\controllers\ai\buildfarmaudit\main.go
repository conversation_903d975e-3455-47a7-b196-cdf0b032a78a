package ai

import (
	"bufio"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"irisAdminApi/application/controllers/buildfarm"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/buildfarm/dbuildfarmaudit"
	"irisAdminApi/service/dao/buildfarm/dbuildfarmauditdetail"
	"irisAdminApi/service/dao/buildfarm/dbuildfarmaudithistory"
	"irisAdminApi/service/dao/buildfarm/dgitjob"
	"irisAdminApi/service/dao/buildfarm/dmakejob"

	"github.com/imroc/req/v3"
)

var (
	WorkerLock sync.Mutex
	level      = []uint{9, 3}
)

func StartBuidfarmAuditWorker() {
	t := time.NewTicker(time.Minute * 1)
	for range t.C {
		BuildfarmAuditWorker()
	}
}

func BuildfarmAuditWorker() {
	if WorkerLock.TryLock() {
		defer WorkerLock.Unlock()
		_items, err := dbuildfarmaudit.FindTaskAudit(0, []uint{0}, level, 1, 10, "asc", "created_at")
		if err != nil {
			logging.ErrorLogger.Errorf("get task audit list err ", err)
			return
		}
		items := _items["items"].([]*dbuildfarmaudit.ListResponse)
	out:
		for _, item := range items {
			histories, err := dbuildfarmaudithistory.FindHistoryByTaskID(item.TaskID)
			if err != nil {
				logging.ErrorLogger.Errorf("get task audit history err ", err)
				libs.SendMailRedis("编译农场", []string{"<EMAIL>"}, fmt.Sprintf("%s %s", item.TaskID, "AI评审失败"), err.Error(), nil)
				continue
			}
			if len(histories) == 0 {
				diffs, err := GetDiffs(item.TaskID)
				if err != nil {
					logging.ErrorLogger.Errorf("get task audit detail err ", err)
					libs.SendMailRedis("编译农场", []string{"<EMAIL>"}, fmt.Sprintf("%s %s", item.TaskID, "AI评审失败"), err.Error(), nil)
					continue out
				}
				for _, diff := range diffs {
					problems, err := AiAudit(diff.Diff)
					if err != nil {
						logging.ErrorLogger.Errorf("get task audit list err ", err)
						libs.SendMailRedis("编译农场", []string{"<EMAIL>"}, fmt.Sprintf("%s %s", item.TaskID, "AI评审失败"), err.Error(), nil)
						continue out
					}
					for _, problem := range problems {
						for _, _problem := range problem.Problems {
							if _problem.LeakLevel == "高" || _problem.LeakLevel == "中" {
								err = UpdateAudit(item.BuildfarmAudit, 0, fmt.Sprintf("AI分析结果：风险：%s, %s", _problem.LeakLevel, _problem.Problem))
								if err != nil {
									logging.ErrorLogger.Error(err)
									libs.SendMailRedis("编译农场", []string{"<EMAIL>"}, fmt.Sprintf("%s %s", item.TaskID, "AI评审失败"), err.Error(), nil)
								}
								continue out
							}
						}
					}
				}
				err = UpdateAudit(item.BuildfarmAudit, 1, "AI分析结果：低风险")
				if err != nil {
					logging.ErrorLogger.Error(err)
					libs.SendMailRedis("编译农场", []string{"<EMAIL>"}, fmt.Sprintf("%s %s", item.TaskID, "AI评审失败"), err.Error(), nil)
					continue out
				}
				libs.SendMailRedis("编译农场", []string{"<EMAIL>"}, fmt.Sprintf("%s %s", item.TaskID, "AI评审通过"), "", nil)
			}
		}
	}
}

var BugWebClient = req.C().
	SetCommonRetryCount(3).
	// Set the retry sleep interval with a commonly used algorithm: capped exponential backoff with jitter (https://aws.amazon.com/blogs/architecture/exponential-backoff-and-jitter/).
	SetCommonRetryBackoffInterval(1*time.Second, 5*time.Second).
	AddCommonRetryCondition(func(resp *req.Response, err error) bool {
		return err != nil
	}).
	SetCookieJar(nil)

type Problem struct {
	Problem    string `json:"问题"`
	Suggestion string `json:"建议"`
	LeakLevel  string `json:"泄漏风险"`
}

type Problems struct {
	Problems []*Problem `json:"问题列表"`
}

func AiAudit(diff string) ([]*Problems, error) {
	problems := []*Problems{}
	url := "http://127.0.0.1:5000/api/audit"
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	payload := map[string]string{
		"diff": diff,
	}

	resp, err := BugWebClient.R().SetHeaders(headers).SetBody(payload).SetSuccessResult(&problems).Post(url)
	if err != nil {
		return problems, err
	}

	if resp.IsSuccessState() {
		return problems, nil
	}

	return nil, errors.New(fmt.Sprintf("Unknow error, %s", resp.String()))
}

func GetDiffs(task_id string) ([]*dbuildfarmauditdetail.ListResponse, error) {
	var list []*dbuildfarmauditdetail.ListResponse
	items, err := dbuildfarmauditdetail.FindTaskAudit(task_id)
	if err != nil {
		logging.ErrorLogger.Errorf("get task audit list err ", err)
		return list, err
	}

	for _, item := range items {
		if len(item.Diff) > 0 {
			filterDiffArr := []string{}
			diffContent, err := os.ReadFile(item.Diff)
			if err != nil {
				logging.ErrorLogger.Errorf("get task audit detail err ", err)
				return list, err
			}

			scanner := bufio.NewScanner(strings.NewReader(string(diffContent)))
			var currentFile string
			diffs := map[string][]string{}
			var _diffs []string

			for scanner.Scan() {
				line := scanner.Text()
				// 检测文件名行
				// 由---行开始，防止截取时，多出---行，导致格式异常
				if strings.HasPrefix(line, "diff --git") {
					if currentFile != "" {
						diffs[currentFile] = []string{}
						diffs[currentFile] = append(diffs[currentFile], _diffs...)
						_diffs = []string{}
						currentFile = ""
					}
				} else if strings.HasPrefix(line, "+++ ") {
					parts := strings.Split(line, " ")
					if len(parts) >= 2 {
						currentFile = strings.TrimPrefix(parts[1], "b/")
					}
				}
				_diffs = append(_diffs, line)
			}
			// 补齐最后一个变更
			if currentFile != "" {
				diffs[currentFile] = []string{}
				diffs[currentFile] = append(diffs[currentFile], _diffs...)
			}

			extendDiffLevelExtMap := buildfarm.GetExtendDiffLevelExtMap()
			for newFile, diff := range diffs {
				_level := buildfarm.GetLevelByFilePath(extendDiffLevelExtMap, newFile)
				if libs.InArrayUint(level, _level) {
					filterDiffArr = append(filterDiffArr, strings.Join(diff, "\n"))
				}
			}

			if len(filterDiffArr) > 0 {
				item.Diff = strings.Join(filterDiffArr, "\n")
				list = append(list, item)
			}
		}
	}
	return list, nil
}

func UpdateAudit(audit dbuildfarmaudit.BuildfarmAudit, status uint, comment string) error {
	var gitjob dgitjob.GitJob
	var makejob dmakejob.MakeJob
	err := gitjob.FindEx("job_id", audit.JobID)
	if err != nil {
		logging.ErrorLogger.Errorf("find git job err ", err)
		return err
	}
	err = makejob.FindEx("task_id", audit.TaskID)
	if err != nil {
		logging.ErrorLogger.Errorf("find make job err ", err)
		return err
	}

	f, _ := os.OpenFile(filepath.Join(libs.Config.Buildfarm.Logpath, makejob.TaskID+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
	defer f.Close()

	if status == 1 {
		client, err := buildfarm.SSHClient(gitjob.ServerID)
		if err != nil {
			logging.ErrorLogger.Errorf("ssh connect err ", err)
			return err
		}
		defer client.Close()
		buildfarm.ArchiveOutputWorker(true, f, client, &gitjob, &makejob, "")
	}

	// err = dbuildfarmaudit.Update(audit.JobID, audit.TaskID, map[string]interface{}{"Status": request.Status, "LastAuditorID": userID, "Comment": request.Comment}, details, compareDetails)
	// err = audit.Update(audit.ID, map[string]interface{}{"Status": status, "LastAuditorID": 0, "Comment": comment})
	err = dbuildfarmaudit.Update(audit.JobID, audit.TaskID, map[string]interface{}{"Status": status, "LastAuditorID": 0, "Comment": comment})
	if err != nil {
		logging.ErrorLogger.Errorf("update task audit err ", err)
		return err
	}
	return nil
}
