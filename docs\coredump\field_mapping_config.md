# Coredump记录字段映射配置方案

## 1. 字段映射概述

本文档详细描述了飞书多维表格中的coredump记录字段到bug管理系统字段的映射关系和配置方案。

### 1.1 映射原则

- **数据完整性**: 确保关键信息不丢失
- **字段语义**: 保持字段含义的一致性
- **可配置性**: 支持灵活的字段映射配置
- **向后兼容**: 与现有bug系统保持兼容

## 2. 飞书多维表格字段结构

### 2.1 Coredump记录字段定义

| 字段名称 | 字段类型 | 是否必填 | 描述 | 示例值 |
|---------|----------|----------|------|--------|
| SN | 单行文本 | 是 | 设备序列号，唯一标识 | "ABC123456" |
| coredump收集url | URL | 否 | coredump文件收集链接 | "http://example.com/coredump/123" |
| coredump时间 | 日期时间 | 是 | coredump发生的时间 | "2025-01-14 10:30:00" |
| coredump组件 | 单选 | 是 | 发生coredump的组件名称 | "网络模块" |
| 堆栈信息 | 多行文本 | 否 | 详细的堆栈跟踪信息 | "Stack trace..." |
| 组件负责人 | 人员 | 否 | 组件的主要负责人 | {"name": "张三", "email": "<EMAIL>"} |
| 进程负责人 | 人员 | 否 | 进程的负责人 | {"name": "李四", "email": "<EMAIL>"} |
| 设备型号 | 单行文本 | 是 | 设备的型号信息 | "RG-S6220-48XS6QXS" |
| 软件版本 | 单行文本 | 是 | 软件版本号 | "v2.1.3-build20250114" |
| 记录日期 | 日期 | 是 | 记录创建日期 | "2025-01-14" |
| 说明 | 多行文本 | 否 | 补充说明信息 | "用户反馈网络异常..." |
| 是否已知问题 | 复选框 | 否 | 标识是否为已知问题 | true/false |
| 处理结果 | 单选 | 否 | 处理状态 | "待处理/处理中/已解决" |
| 修复版本 | 单行文本 | 否 | 修复的版本号 | "v2.1.4" |
| 文件名 | 单行文本 | 否 | coredump文件名 | "core.12345" |

### 2.2 飞书人员字段数据格式

```json
{
  "id": "ou_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
  "name": "张三",
  "email": "<EMAIL>",
  "avatar_url": "https://example.com/avatar.jpg"
}
```

## 3. Bug系统字段结构

### 3.1 BugInfo结构体字段

基于现有的 `bug_submit.go` 文件，Bug系统包含以下字段：

| 字段名称 | 类型 | 是否必填 | 描述 | 示例值 |
|---------|------|----------|------|--------|
| Summary | string | 是 | Bug标题摘要 | "Coredump异常 - 网络模块 [RG-S6220] SN:ABC123" |
| Product | string | 是 | 产品/组件名称 | "网络模块" |
| VersionMess | string | 否 | 版本信息 | "v2.1.3-build20250114" |
| DeviceUnderTestConfig | string | 否 | 测试设备配置 | "RG-S6220-48XS6QXS" |
| DebugMess | string | 否 | 调试信息 | "http://example.com/coredump/123" |
| TestCaseNum | string | 是 | 测试用例编号 | "COREDUMP_ABC123_1705123456" |
| Bugdescription | string | 是 | Bug详细描述 | HTML格式的详细描述 |
| ChargeCasUserid | string | 是 | 负责人用户ID | "zhangsan" |
| SubmitterCasUserid | string | 是 | 提交者用户ID | "coredump_system" |
| PstlCasUserid | string | 否 | PSTL负责人 | "wangchunping" |
| Priority | string | 否 | 优先级 | "Critical/Major/Normal/Minor" |
| Severity | string | 否 | 严重性 | "High/Medium/Low" |
| Source | string | 否 | 来源 | "Coredump自动提交" |
| OS | string | 否 | 操作系统 | "Coredump专项" |
| TestMethod | string | 否 | 测试方法 | "自动化检测" |
| Repeat | string | 否 | 重现性 | "待确认" |
| LegacyBug | string | 否 | 是否历史Bug | "否" |

## 4. 详细字段映射规则

### 4.1 直接映射字段

| 飞书字段 | Bug字段 | 映射规则 | 备注 |
|---------|---------|----------|------|
| coredump组件 | Product | 直接赋值 | 组件名称直接映射 |
| 软件版本 | VersionMess | 直接赋值 | 版本信息直接映射 |
| 设备型号 | DeviceUnderTestConfig | 直接赋值 | 设备型号直接映射 |
| coredump收集url | DebugMess | 直接赋值 | 调试链接直接映射 |

### 4.2 格式化映射字段

#### 4.2.1 Bug标题生成 (Summary)

```go
// 生成规则
func generateBugSummary(record *CoredumpRecord) string {
    return fmt.Sprintf("Coredump异常 - %s [%s] SN:%s", 
        record.CoredumpComponent,  // 组件名称
        record.DeviceModel,        // 设备型号
        record.SN)                 // 序列号
}

// 示例输出
"Coredump异常 - 网络模块 [RG-S6220-48XS6QXS] SN:ABC123456"
```

#### 4.2.2 测试用例编号生成 (TestCaseNum)

```go
// 生成规则
func generateTestCaseNum(record *CoredumpRecord) string {
    timestamp := time.Now().Unix()
    return fmt.Sprintf("COREDUMP_%s_%d", record.SN, timestamp)
}

// 示例输出
"COREDUMP_ABC123456_1705123456"
```

#### 4.2.3 Bug详细描述生成 (Bugdescription)

```go
// HTML格式的详细描述
func generateBugDescription(record *CoredumpRecord) string {
    var desc strings.Builder
    
    // 基础信息部分
    desc.WriteString("<h3>Coredump基础信息</h3>")
    desc.WriteString(fmt.Sprintf("<p><strong>SN:</strong> %s</p>", record.SN))
    desc.WriteString(fmt.Sprintf("<p><strong>组件:</strong> %s</p>", record.CoredumpComponent))
    desc.WriteString(fmt.Sprintf("<p><strong>设备型号:</strong> %s</p>", record.DeviceModel))
    desc.WriteString(fmt.Sprintf("<p><strong>软件版本:</strong> %s</p>", record.SoftwareVersion))
    desc.WriteString(fmt.Sprintf("<p><strong>发生时间:</strong> %s</p>", 
        record.CoredumpTime.Format("2006-01-02 15:04:05")))
    
    // 收集链接部分
    if record.CoredumpCollectURL != "" {
        desc.WriteString("<h3>相关链接</h3>")
        desc.WriteString(fmt.Sprintf("<p><strong>收集链接:</strong> <a href=\"%s\">%s</a></p>", 
            record.CoredumpCollectURL, record.CoredumpCollectURL))
    }
    
    // 堆栈信息部分
    if record.StackInfo != "" {
        desc.WriteString("<h3>堆栈信息</h3>")
        desc.WriteString(fmt.Sprintf("<pre>%s</pre>", html.EscapeString(record.StackInfo)))
    }
    
    // 补充说明部分
    if record.Description != "" {
        desc.WriteString("<h3>补充说明</h3>")
        desc.WriteString(fmt.Sprintf("<p>%s</p>", html.EscapeString(record.Description)))
    }
    
    // 负责人信息部分
    desc.WriteString("<h3>负责人信息</h3>")
    if len(record.ComponentResponsible) > 0 {
        desc.WriteString(fmt.Sprintf("<p><strong>组件负责人:</strong> %s (%s)</p>", 
            record.ComponentResponsible[0].Name, record.ComponentResponsible[0].Email))
    }
    if len(record.ProcessResponsible) > 0 {
        desc.WriteString(fmt.Sprintf("<p><strong>进程负责人:</strong> %s (%s)</p>", 
            record.ProcessResponsible[0].Name, record.ProcessResponsible[0].Email))
    }
    
    return desc.String()
}
```

### 4.3 人员字段映射

#### 4.3.1 负责人确定逻辑 (ChargeCasUserid)

```go
// 负责人优先级策略
func determineChargeUser(record *CoredumpRecord) (string, error) {
    // 优先级1: 组件负责人
    if len(record.ComponentResponsible) > 0 {
        email := record.ComponentResponsible[0].Email
        if username, err := extractUsernameFromEmail(email); err == nil {
            return username, nil
        }
    }
    
    // 优先级2: 进程负责人
    if len(record.ProcessResponsible) > 0 {
        email := record.ProcessResponsible[0].Email
        if username, err := extractUsernameFromEmail(email); err == nil {
            return username, nil
        }
    }
    
    // 回退: 使用默认负责人
    return config.DefaultChargeUser, nil
}

// 从邮箱提取用户名
func extractUsernameFromEmail(email string) (string, error) {
    if email == "" {
        return "", fmt.Errorf("邮箱地址为空")
    }
    
    parts := strings.Split(email, "@")
    if len(parts) < 1 || parts[0] == "" {
        return "", fmt.Errorf("无效的邮箱格式: %s", email)
    }
    
    return parts[0], nil
}
```

#### 4.3.2 固定人员字段

| Bug字段 | 映射值 | 说明 |
|---------|--------|------|
| SubmitterCasUserid | "coredump_system" | 固定为系统自动提交 |
| PstlCasUserid | "wangchunping" | 固定为默认PSTL负责人 |

### 4.4 优先级和严重性映射

#### 4.4.1 优先级确定 (Priority)

```go
func determinePriority(record *CoredumpRecord) string {
    component := strings.ToLower(record.CoredumpComponent)
    
    // 核心组件 -> Critical
    if strings.Contains(component, "核心") || 
       strings.Contains(component, "内核") ||
       strings.Contains(component, "kernel") {
        return "Critical"
    }
    
    // 重要组件 -> Major
    if strings.Contains(component, "网络") || 
       strings.Contains(component, "存储") ||
       strings.Contains(component, "安全") {
        return "Major"
    }
    
    // 其他组件 -> Normal
    return "Normal"
}
```

#### 4.4.2 严重性确定 (Severity)

```go
func determineSeverity(record *CoredumpRecord) string {
    // Coredump通常都是比较严重的问题
    // 可以根据具体业务需求调整
    return "High"
}
```

### 4.5 固定值字段

| Bug字段 | 固定值 | 说明 |
|---------|--------|------|
| Source | "Coredump自动提交" | 标识来源 |
| OS | "Coredump专项" | 操作系统分类 |
| TestMethod | "自动化检测" | 测试方法 |
| Repeat | "待确认" | 重现性待确认 |
| LegacyBug | "否" | 非历史Bug |

## 5. 配置文件结构

### 5.1 字段映射配置

```yaml
# config/field_mapping.yaml
field_mapping:
  version: "1.0"
  last_updated: "2025-01-14T10:00:00Z"
  
  # 飞书字段名称配置
  feishu_fields:
    sn_field: "SN"
    coredump_url_field: "coredump收集url"
    coredump_time_field: "coredump时间"
    component_field: "coredump组件"
    stack_info_field: "堆栈信息"
    component_responsible_field: "组件负责人"
    process_responsible_field: "进程负责人"
    device_model_field: "设备型号"
    software_version_field: "软件版本"
    record_date_field: "记录日期"
    description_field: "说明"
    is_known_issue_field: "是否已知问题"
    process_result_field: "处理结果"
    fix_version_field: "修复版本"
    file_name_field: "文件名"
  
  # Bug系统字段映射规则
  bug_field_mapping:
    # 直接映射
    direct_mapping:
      Product: "component_field"
      VersionMess: "software_version_field"
      DeviceUnderTestConfig: "device_model_field"
      DebugMess: "coredump_url_field"
    
    # 格式化映射
    formatted_mapping:
      Summary:
        template: "Coredump异常 - {component} [{device_model}] SN:{sn}"
        fields: ["component_field", "device_model_field", "sn_field"]
      
      TestCaseNum:
        template: "COREDUMP_{sn}_{timestamp}"
        fields: ["sn_field"]
        include_timestamp: true
    
    # 固定值映射
    fixed_mapping:
      Source: "Coredump自动提交"
      OS: "Coredump专项"
      TestMethod: "自动化检测"
      Repeat: "待确认"
      LegacyBug: "否"
  
  # 人员字段映射配置
  person_field_mapping:
    charge_user_priority:
      - "component_responsible_field"  # 组件负责人优先
      - "process_responsible_field"    # 进程负责人次之
    
    default_users:
      submitter: "coredump_system"
      pstl: "wangchunping"
      charge_fallback: "wuzhensheng"
    
    email_extraction:
      enabled: true
      domain_filter: ["company.com", "ruijie.com.cn"]  # 允许的邮箱域名
  
  # 优先级和严重性映射
  priority_mapping:
    rules:
      - condition: "component contains '核心' or component contains '内核'"
        priority: "Critical"
        severity: "High"
      - condition: "component contains '网络' or component contains '存储'"
        priority: "Major"
        severity: "High"
      - condition: "default"
        priority: "Normal"
        severity: "Medium"
  
  # 过滤规则配置
  filter_rules:
    skip_known_issues: true
    required_fields: ["sn_field", "component_field"]
    skip_conditions:
      - "is_known_issue == true"
      - "process_result == '已解决'"
      - "sn_field == ''"
```

### 5.2 配置加载器实现

```go
// FieldMappingConfigLoader 字段映射配置加载器
type FieldMappingConfigLoader struct {
    configPath string
    config     *FieldMappingConfig
}

// LoadFieldMappingConfig 加载字段映射配置
func LoadFieldMappingConfig(configPath string) (*FieldMappingConfig, error) {
    data, err := os.ReadFile(configPath)
    if err != nil {
        return nil, fmt.Errorf("读取配置文件失败: %w", err)
    }
    
    var config FieldMappingConfig
    if err := yaml.Unmarshal(data, &config); err != nil {
        return nil, fmt.Errorf("解析配置文件失败: %w", err)
    }
    
    // 验证配置
    if err := validateFieldMappingConfig(&config); err != nil {
        return nil, fmt.Errorf("配置验证失败: %w", err)
    }
    
    return &config, nil
}

// validateFieldMappingConfig 验证配置有效性
func validateFieldMappingConfig(config *FieldMappingConfig) error {
    // 检查必要字段是否配置
    requiredFields := []string{
        config.FeishuFields.SNField,
        config.FeishuFields.ComponentField,
        config.FeishuFields.DeviceModelField,
    }
    
    for _, field := range requiredFields {
        if field == "" {
            return fmt.Errorf("必要字段配置为空")
        }
    }
    
    // 检查默认用户配置
    if config.PersonFieldMapping.DefaultUsers.Submitter == "" {
        return fmt.Errorf("默认提交者未配置")
    }
    
    return nil
}
```

## 6. 字段映射执行器

### 6.1 映射执行器实现

```go
// FieldMappingExecutor 字段映射执行器
type FieldMappingExecutor struct {
    config *FieldMappingConfig
}

// NewFieldMappingExecutor 创建字段映射执行器
func NewFieldMappingExecutor(config *FieldMappingConfig) *FieldMappingExecutor {
    return &FieldMappingExecutor{
        config: config,
    }
}

// ExecuteMapping 执行字段映射
func (e *FieldMappingExecutor) ExecuteMapping(record *CoredumpRecord) (*BugInfo, error) {
    bugInfo := NewDefaultBugInfo()
    
    // 执行直接映射
    if err := e.executeDirectMapping(record, bugInfo); err != nil {
        return nil, fmt.Errorf("直接映射失败: %w", err)
    }
    
    // 执行格式化映射
    if err := e.executeFormattedMapping(record, bugInfo); err != nil {
        return nil, fmt.Errorf("格式化映射失败: %w", err)
    }
    
    // 执行固定值映射
    e.executeFixedMapping(bugInfo)
    
    // 执行人员字段映射
    if err := e.executePersonMapping(record, bugInfo); err != nil {
        return nil, fmt.Errorf("人员字段映射失败: %w", err)
    }
    
    // 执行优先级映射
    e.executePriorityMapping(record, bugInfo)
    
    return bugInfo, nil
}

// executeDirectMapping 执行直接映射
func (e *FieldMappingExecutor) executeDirectMapping(record *CoredumpRecord, bugInfo *BugInfo) error {
    mapping := e.config.BugFieldMapping.DirectMapping
    
    if fieldName, exists := mapping["Product"]; exists {
        bugInfo.Product = e.getFieldValue(record, fieldName)
    }
    
    if fieldName, exists := mapping["VersionMess"]; exists {
        bugInfo.VersionMess = e.getFieldValue(record, fieldName)
    }
    
    if fieldName, exists := mapping["DeviceUnderTestConfig"]; exists {
        bugInfo.DeviceUnderTestConfig = e.getFieldValue(record, fieldName)
    }
    
    if fieldName, exists := mapping["DebugMess"]; exists {
        bugInfo.DebugMess = e.getFieldValue(record, fieldName)
    }
    
    return nil
}

// getFieldValue 根据字段名获取记录中的值
func (e *FieldMappingExecutor) getFieldValue(record *CoredumpRecord, fieldName string) string {
    switch fieldName {
    case "sn_field":
        return record.SN
    case "component_field":
        return record.CoredumpComponent
    case "software_version_field":
        return record.SoftwareVersion
    case "device_model_field":
        return record.DeviceModel
    case "coredump_url_field":
        return record.CoredumpCollectURL
    case "description_field":
        return record.Description
    default:
        return ""
    }
}
```

## 7. 映射验证和测试

### 7.1 映射验证器

```go
// MappingValidator 映射验证器
type MappingValidator struct {
    config *FieldMappingConfig
}

// ValidateMapping 验证映射结果
func (v *MappingValidator) ValidateMapping(record *CoredumpRecord, bugInfo *BugInfo) []ValidationError {
    var errors []ValidationError
    
    // 验证必填字段
    if bugInfo.Summary == "" {
        errors = append(errors, ValidationError{
            Field:   "Summary",
            Message: "Bug标题不能为空",
        })
    }
    
    if bugInfo.Product == "" {
        errors = append(errors, ValidationError{
            Field:   "Product",
            Message: "产品组件不能为空",
        })
    }
    
    if bugInfo.ChargeCasUserid == "" {
        errors = append(errors, ValidationError{
            Field:   "ChargeCasUserid",
            Message: "负责人不能为空",
        })
    }
    
    // 验证字段长度
    if len(bugInfo.Summary) > 200 {
        errors = append(errors, ValidationError{
            Field:   "Summary",
            Message: "Bug标题长度超过限制",
        })
    }
    
    // 验证邮箱格式
    if bugInfo.ChargeCasUserid != "" && !v.isValidUsername(bugInfo.ChargeCasUserid) {
        errors = append(errors, ValidationError{
            Field:   "ChargeCasUserid",
            Message: "用户名格式无效",
        })
    }
    
    return errors
}

// ValidationError 验证错误
type ValidationError struct {
    Field   string `json:"field"`
    Message string `json:"message"`
}

// isValidUsername 验证用户名格式
func (v *MappingValidator) isValidUsername(username string) bool {
    // 用户名只能包含字母、数字和下划线
    matched, _ := regexp.MatchString("^[a-zA-Z0-9_]+$", username)
    return matched && len(username) >= 3 && len(username) <= 20
}
```

### 7.2 映射测试用例

```go
// TestFieldMapping 字段映射测试
func TestFieldMapping(t *testing.T) {
    config := LoadTestFieldMappingConfig()
    executor := NewFieldMappingExecutor(config)
    validator := NewMappingValidator(config)
    
    // 测试用例1: 完整数据映射
    t.Run("完整数据映射", func(t *testing.T) {
        record := &CoredumpRecord{
            SN:                "ABC123456",
            CoredumpComponent: "网络模块",
            SoftwareVersion:   "v2.1.3",
            DeviceModel:       "RG-S6220",
            CoredumpCollectURL: "http://example.com/coredump/123",
            ComponentResponsible: []FeishuPersonField{
                {Email: "<EMAIL>", Name: "张三"},
            },
            CoredumpTime: time.Now(),
        }
        
        bugInfo, err := executor.ExecuteMapping(record)
        assert.NoError(t, err)
        
        // 验证映射结果
        assert.Equal(t, "网络模块", bugInfo.Product)
        assert.Equal(t, "v2.1.3", bugInfo.VersionMess)
        assert.Equal(t, "RG-S6220", bugInfo.DeviceUnderTestConfig)
        assert.Equal(t, "zhangsan", bugInfo.ChargeCasUserid)
        assert.Contains(t, bugInfo.Summary, "ABC123456")
        
        // 验证映射有效性
        errors := validator.ValidateMapping(record, bugInfo)
        assert.Empty(t, errors)
    })
    
    // 测试用例2: 缺失数据处理
    t.Run("缺失数据处理", func(t *testing.T) {
        record := &CoredumpRecord{
            SN:                "ABC123456",
            CoredumpComponent: "网络模块",
            // 缺失负责人信息
        }
        
        bugInfo, err := executor.ExecuteMapping(record)
        assert.NoError(t, err)
        
        // 应该使用默认负责人
        assert.Equal(t, config.PersonFieldMapping.DefaultUsers.ChargeFallback, 
            bugInfo.ChargeCasUserid)
    })
    
    // 测试用例3: 优先级映射
    t.Run("优先级映射", func(t *testing.T) {
        record := &CoredumpRecord{
            SN:                "ABC123456",
            CoredumpComponent: "核心模块",
        }
        
        bugInfo, err := executor.ExecuteMapping(record)
        assert.NoError(t, err)
        
        assert.Equal(t, "Critical", bugInfo.Priority)
        assert.Equal(t, "High", bugInfo.Severity)
    })
}
```

## 8. 映射配置管理

### 8.1 配置热更新

```go
// ConfigManager 配置管理器
type ConfigManager struct {
    configPath   string
    config       *FieldMappingConfig
    lastModTime  time.Time
    mutex        sync.RWMutex
    watchers     []ConfigWatcher
}

// ConfigWatcher 配置变更监听器
type ConfigWatcher interface {
    OnConfigChanged(config *FieldMappingConfig)
}

// StartWatching 开始监听配置文件变更
func (m *ConfigManager) StartWatching() error {
    watcher, err := fsnotify.NewWatcher()
    if err != nil {
        return err
    }
    
    err = watcher.Add(m.configPath)
    if err != nil {
        return err
    }
    
    go func() {
        for {
            select {
            case event := <-watcher.Events:
                if event.Op&fsnotify.Write == fsnotify.Write {
                    m.reloadConfig()
                }
            case err := <-watcher.Errors:
                logging.ErrorLogger.Errorf("配置文件监听错误: %v", err)
            }
        }
    }()
    
    return nil
}

// reloadConfig 重新加载配置
func (m *ConfigManager) reloadConfig() {
    newConfig, err := LoadFieldMappingConfig(m.configPath)
    if err != nil {
        logging.ErrorLogger.Errorf("重新加载配置失败: %v", err)
        return
    }
    
    m.mutex.Lock()
    oldConfig := m.config
    m.config = newConfig
    m.lastModTime = time.Now()
    m.mutex.Unlock()
    
    // 通知监听器
    for _, watcher := range m.watchers {
        watcher.OnConfigChanged(newConfig)
    }
    
    logging.InfoLogger.Info("配置文件已重新加载")
}

// GetConfig 获取当前配置
func (m *ConfigManager) GetConfig() *FieldMappingConfig {
    m.mutex.RLock()
    defer m.mutex.RUnlock()
    return m.config
}
```

### 8.2 配置版本管理

```go
// ConfigVersion 配置版本信息
type ConfigVersion struct {
    Version     string    `yaml:"version"`
    LastUpdated time.Time `yaml:"last_updated"`
    Author      string    `yaml:"author"`
    Description string    `yaml:"description"`
    Changes     []string  `yaml:"changes"`
}

// VersionedFieldMappingConfig 带版本的字段映射配置
type VersionedFieldMappingConfig struct {
    Version      ConfigVersion       `yaml:"version"`
    FieldMapping FieldMappingConfig  `yaml:"field_mapping"`
}

// ValidateConfigVersion 验证配置版本兼容性
func ValidateConfigVersion(config *VersionedFieldMappingConfig) error {
    currentVersion := "1.0"
    
    if config.Version.Version != currentVersion {
        return fmt.Errorf("配置版本不兼容: 当前=%s, 配置=%s", 
            currentVersion, config.Version.Version)
    }
    
    return nil
}
```

## 9. 映射性能优化

### 9.1 字段映射缓存

```go
// MappingCache 映射缓存
type MappingCache struct {
    cache map[string]*BugInfo
    mutex sync.RWMutex
    ttl   time.Duration
}

// GetCachedMapping 获取缓存的映射结果
func (c *MappingCache) GetCachedMapping(recordID string) (*BugInfo, bool) {
    c.mutex.RLock()
    defer c.mutex.RUnlock()
    
    bugInfo, exists := c.cache[recordID]
    return bugInfo, exists
}

// SetCachedMapping 设置映射结果缓存
func (c *MappingCache) SetCachedMapping(recordID string, bugInfo *BugInfo) {
    c.mutex.Lock()
    defer c.mutex.Unlock()
    
    c.cache[recordID] = bugInfo
    
    // 定时清理过期缓存
    go func() {
        time.Sleep(c.ttl)
        c.mutex.Lock()
        delete(c.cache, recordID)
        c.mutex.Unlock()
    }()
}
```

### 9.2 批量映射优化

```go
// BatchMappingExecutor 批量映射执行器
type BatchMappingExecutor struct {
    executor *FieldMappingExecutor
    cache    *MappingCache
}

// ExecuteBatchMapping 执行批量映射
func (e *BatchMappingExecutor) ExecuteBatchMapping(records []*CoredumpRecord) ([]*BugInfo, error) {
    var bugInfos []*BugInfo
    var errors []error
    
    // 并发处理映射
    const maxWorkers = 10
    jobs := make(chan *CoredumpRecord, len(records))
    results := make(chan MappingResult, len(records))
    
    // 启动工作协程
    for w := 0; w < maxWorkers; w++ {
        go func() {
            for record := range jobs {
                // 检查缓存
                if bugInfo, exists := e.cache.GetCachedMapping(record.RecordID); exists {
                    results <- MappingResult{BugInfo: bugInfo, Error: nil}
                    continue
                }
                
                // 执行映射
                bugInfo, err := e.executor.ExecuteMapping(record)
                if err == nil {
                    e.cache.SetCachedMapping(record.RecordID, bugInfo)
                }
                
                results <- MappingResult{BugInfo: bugInfo, Error: err}
            }
        }()
    }
    
    // 发送任务
    for _, record := range records {
        jobs <- record
    }
    close(jobs)
    
    // 收集结果
    for i := 0; i < len(records); i++ {
        result := <-results
        if result.Error != nil {
            errors = append(errors, result.Error)
        } else {
            bugInfos = append(bugInfos, result.BugInfo)
        }
    }
    
    if len(errors) > 0 {
        return bugInfos, fmt.Errorf("批量映射部分失败: %d个错误", len(errors))
    }
    
    return bugInfos, nil
}

// MappingResult 映射结果
type MappingResult struct {
    BugInfo *BugInfo
    Error   error
}
```

这个字段映射配置方案提供了：

1. **完整的字段映射规则**：详细定义了飞书字段到Bug系统字段的映射关系
2. **灵活的配置管理**：支持YAML配置文件和热更新
3. **人员字段处理**：完善的人员信息提取和映射逻辑
4. **验证和测试**：包含映射验证器和测试用例
5. **性能优化**：缓存机制和批量处理优化
6. **版本管理**：配置版本控制和兼容性检查

该方案确保了字段映射的准确性、可维护性和高性能。