package dvulnerabilitypermission

import (
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/opensource"
	"irisAdminApi/service/dao/user/duser"
)

const ModelName = "漏洞人员表"

type Response struct {
	opensource.OpenSourceVulnerabilityPermission
	Username       string `gorm:"-" json:"username"`
	PermissionType string `gorm:"-" json:"permission_type"`
}

type ListResponse struct {
	Response
}

type Request struct {
	Id uint `json:"id"`
}

func (this *Response) ModelName() string {
	return ModelName
}

func Model() *opensource.OpenSourceVulnerabilityPermission {
	return &opensource.OpenSourceVulnerabilityPermission{}
}

func getPermissionUsername(res *Response) {
	user := &duser.User{}
	err := user.Find(res.UserID)
	if err != nil {
		return
	}
	res.Username = user.Username
	switch res.Type {
	case 1:
		res.PermissionType = "负责人"
	case 3:
		res.PermissionType = "专业组长"
	case 4:
		res.PermissionType = "委派人"
	}
}

func getPermissionsUsernames(res []*ListResponse) {
	for _, per := range res {
		getPermissionUsername(&per.Response)
	}
}

func ListPermissionsByVulnerabilityId(vulnerabilityId uint) []*ListResponse {
	var res []*ListResponse

	err := easygorm.GetEasyGormDb().Model(Model()).
		Where("vulnerability_id=?", vulnerabilityId).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("list vulnerability permissions by vulnerability-id get err %s", err.Error())
		return nil
	}
	getPermissionsUsernames(res)
	return res
}

func ListPermissionsByVulnerabilityIdAndUserId(vulnerabilityId, userId uint) ([]*ListResponse, error) {
	var res []*ListResponse

	err := easygorm.GetEasyGormDb().Model(Model()).
		Where("vulnerability_id=?", vulnerabilityId).
		Where("user_id=?", userId).
		Find(&res).Error
	if err != nil {
		return nil, err
	}
	getPermissionsUsernames(res)
	return res, nil
}

func FindByVulnerabilityIdAndUserIdAndType(vulnerabilityId, userId, permissionType uint) (*Response, error) {
	var res []*Response

	err := easygorm.GetEasyGormDb().Model(Model()).
		Where("vulnerability_id=?", vulnerabilityId).
		Where("user_id=?", userId).
		Where("type=?", permissionType).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find vulnerability permission by vulnerability-id and user-id and permission type get err %s", err.Error())
		return nil, err
	}
	if len(res) == 0 {
		return nil, nil
	}
	return res[0], nil
}
