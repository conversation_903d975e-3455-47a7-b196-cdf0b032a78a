package libs

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"irisAdminApi/application/controllers/openfeishu/meeting/models"
	"irisAdminApi/application/logging"
)

// FieldMapper 字段映射器
type FieldMapper struct {
	mappings map[string]models.FieldMapping
}

// NewFieldMapper 创建字段映射器
func NewFieldMapper() *FieldMapper {
	return &FieldMapper{
		mappings: getDefaultFieldMappings(),
	}
}

// getDefaultFieldMappings 获取默认字段映射配置
func getDefaultFieldMappings() map[string]models.FieldMapping {
	return map[string]models.FieldMapping{
		"工作计划": {
			BitableFieldName:  "工作计划",
			StandardFieldName: "title",
			FieldType:         models.FieldTypeText,
			Required:          true,
		},
		"会议开始时间": {
			BitableFieldName:  "会议开始时间",
			StandardFieldName: "start_time",
			FieldType:         models.FieldTypeDateTime,
			Required:          true,
		},
		"会议结束时间": {
			BitableFieldName:  "会议结束时间",
			StandardFieldName: "end_time",
			FieldType:         models.FieldTypeDateTime,
			Required:          true,
		},
		"地点/方式": {
			BitableFieldName:  "地点/方式",
			StandardFieldName: "location",
			FieldType:         models.FieldTypeText,
			Required:          false,
		},
		"参会人员": {
			BitableFieldName:  "参会人员",
			StandardFieldName: "attendee_names",
			FieldType:         models.FieldTypeText,
			Required:          false,
		},
		"主要内容&总结": {
			BitableFieldName:  "主要内容&总结",
			StandardFieldName: "description",
			FieldType:         models.FieldTypeText,
			Required:          false,
		},
		"运行状态": {
			BitableFieldName:  "运行状态",
			StandardFieldName: "status",
			FieldType:         models.FieldTypeSingleSelect,
			Required:          false,
		},
		"日程ID": {
			BitableFieldName:  "日程ID",
			StandardFieldName: "event_id",
			FieldType:         models.FieldTypeText,
			Required:          false,
		},
		"日程链接": {
			BitableFieldName:  "日程链接",
			StandardFieldName: "event_url",
			FieldType:         models.FieldTypeText,
			Required:          false,
		},
		"错误信息": {
			BitableFieldName:  "错误信息",
			StandardFieldName: "error_message",
			FieldType:         models.FieldTypeText,
			Required:          false,
		},
		"操作类型": {
			BitableFieldName:  "操作类型",
			StandardFieldName: "操作类型",
			FieldType:         models.FieldTypeSingleSelect,
			Required:          false,
		},
		"操作状态": {
			BitableFieldName:  "操作状态",
			StandardFieldName: "操作状态",
			FieldType:         models.FieldTypeSingleSelect,
			Required:          false,
		},
		// 新增字段 - 实际参会人员信息
		"实际参会人员": {
			BitableFieldName:  "实际参会人员",
			StandardFieldName: "实际参会人员",
			FieldType:         models.FieldTypeUser,
			Required:          false,
		},
		"实际参会人数": {
			BitableFieldName:  "实际参会人数",
			StandardFieldName: "实际参会人数",
			FieldType:         models.FieldTypeText,
			Required:          false,
		},
	}
}

// MapRecord 映射记录数据
func (fm *FieldMapper) MapRecord(record *models.MeetingRecord) (*models.StandardMeetingData, error) {
	if record == nil {
		return nil, fmt.Errorf("记录不能为空")
	}

	data := &models.StandardMeetingData{
		RecordID: record.RecordID,
		Timezone: models.DefaultTimezone,
	}

	// 遍历字段进行映射
	for bitableFieldName, value := range record.Fields {
		mapping, exists := fm.mappings[bitableFieldName]
		if !exists {
			logging.DebugLogger.Debugf("未找到字段映射: %s", bitableFieldName)
			continue
		}

		convertedValue, err := fm.convertFieldValue(value, mapping.FieldType)
		if err != nil {
			if mapping.Required {
				return nil, fmt.Errorf("必填字段 %s 转换失败: %v", bitableFieldName, err)
			}
			logging.ErrorLogger.Errorf("字段 %s 转换失败: %v", bitableFieldName, err)
			continue
		}

		// 设置转换后的值
		if err := fm.setFieldValue(data, mapping.StandardFieldName, convertedValue); err != nil {
			return nil, fmt.Errorf("设置字段 %s 失败: %v", mapping.StandardFieldName, err)
		}
	}

	// 验证必填字段
	if err := fm.validateRequiredFields(data); err != nil {
		return nil, err
	}

	return data, nil
}

// convertFieldValue 转换字段值
func (fm *FieldMapper) convertFieldValue(value interface{}, fieldType string) (interface{}, error) {
	if value == nil {
		return nil, nil
	}

	switch fieldType {
	case models.FieldTypeText:
		return fm.convertTextValue(value)
	case models.FieldTypeDateTime:
		return fm.convertDateTimeValue(value)
	case models.FieldTypeUser:
		return fm.convertUserListValue(value)
	case models.FieldTypeSingleSelect:
		return fm.convertSingleSelectValue(value)
	default:
		return value, nil
	}
}

// convertTextValue 转换文本值
func (fm *FieldMapper) convertTextValue(value interface{}) (string, error) {
	switch v := value.(type) {
	case string:
		return v, nil
	case []interface{}:
		if len(v) > 0 {
			if textObj, ok := v[0].(map[string]interface{}); ok {
				if text, exists := textObj["text"]; exists {
					return fmt.Sprintf("%v", text), nil
				}
			}
		}
		return "", nil
	default:
		return fmt.Sprintf("%v", value), nil
	}
}

// convertDateTimeValue 转换日期时间值
func (fm *FieldMapper) convertDateTimeValue(value interface{}) (time.Time, error) {
	switch v := value.(type) {
	case string:
		// 尝试解析时间戳（毫秒）
		if timestamp, err := strconv.ParseInt(v, 10, 64); err == nil {
			return time.Unix(timestamp/1000, (timestamp%1000)*1000000), nil
		}
		// 尝试解析ISO时间格式
		if t, err := time.Parse(time.RFC3339, v); err == nil {
			return t, nil
		}
		return time.Time{}, fmt.Errorf("无法解析时间格式: %s", v)
	case int64:
		// 毫秒时间戳
		return time.Unix(v/1000, (v%1000)*1000000), nil
	case float64:
		// 毫秒时间戳
		timestamp := int64(v)
		return time.Unix(timestamp/1000, (timestamp%1000)*1000000), nil
	default:
		return time.Time{}, fmt.Errorf("不支持的时间类型: %T", value)
	}
}

// convertUserListValue 转换用户列表值
func (fm *FieldMapper) convertUserListValue(value interface{}) ([]string, error) {
	var openIDs []string

	switch v := value.(type) {
	case []interface{}:
		for _, item := range v {
			if userObj, ok := item.(map[string]interface{}); ok {
				if id, exists := userObj["id"]; exists {
					openIDs = append(openIDs, fmt.Sprintf("%v", id))
				}
			}
		}
	case string:
		// 单个用户ID
		if v != "" {
			openIDs = append(openIDs, v)
		}
	default:
		return nil, fmt.Errorf("不支持的用户字段类型: %T", value)
	}

	return openIDs, nil
}

// convertSingleSelectValue 转换单选值
func (fm *FieldMapper) convertSingleSelectValue(value interface{}) (string, error) {
	switch v := value.(type) {
	case string:
		return v, nil
	case map[string]interface{}:
		if text, exists := v["text"]; exists {
			return fmt.Sprintf("%v", text), nil
		}
	case []interface{}:
		if len(v) > 0 {
			if optionObj, ok := v[0].(map[string]interface{}); ok {
				if text, exists := optionObj["text"]; exists {
					return fmt.Sprintf("%v", text), nil
				}
			}
		}
	}
	return "", nil
}

// setFieldValue 设置字段值
func (fm *FieldMapper) setFieldValue(data *models.StandardMeetingData, fieldName string, value interface{}) error {
	switch fieldName {
	case "title":
		if v, ok := value.(string); ok {
			data.Title = v
		}
	case "start_time":
		if v, ok := value.(time.Time); ok {
			data.StartTime = v
		}
	case "end_time":
		if v, ok := value.(time.Time); ok {
			data.EndTime = v
		}
	case "location":
		if v, ok := value.(string); ok {
			data.Location = v
		}
	case "description":
		if v, ok := value.(string); ok {
			data.Description = v
		}
	case "attendee_names":
		if v, ok := value.(string); ok {
			// 预处理参会人员字符串：清理空格和空值
			data.AttendeeNames = fm.preprocessAttendeeNames(v)
		}
	case "attendee_open_ids":
		if v, ok := value.([]string); ok {
			data.AttendeeOpenIDs = v
		}
	case "remove_attendee_open_ids":
		if v, ok := value.([]string); ok {
			data.RemoveAttendeeOpenIDs = v
		}
	case "status":
		if v, ok := value.(string); ok {
			data.Status = v
		}
	case "event_id":
		if v, ok := value.(string); ok {
			data.EventID = v
		}
	case "event_url":
		if v, ok := value.(string); ok {
			data.EventURL = v
		}
	case "error_message":
		if v, ok := value.(string); ok {
			data.ErrorMessage = v
		}
	case "操作类型":
		if v, ok := value.(string); ok {
			data.OperationType = v
		}
	case "操作状态":
		if v, ok := value.(string); ok {
			data.OperationStatus = v
		}
	default:
		logging.DebugLogger.Debugf("未知字段: %s", fieldName)
	}
	return nil
}

// validateRequiredFields 验证必填字段
func (fm *FieldMapper) validateRequiredFields(data *models.StandardMeetingData) error {
	if strings.TrimSpace(data.Title) == "" {
		return fmt.Errorf("日程标题不能为空")
	}
	if data.StartTime.IsZero() {
		return fmt.Errorf("开始时间不能为空")
	}
	if data.EndTime.IsZero() {
		return fmt.Errorf("结束时间不能为空")
	}
	if data.EndTime.Before(data.StartTime) {
		return fmt.Errorf("结束时间不能早于开始时间")
	}
	return nil
}

// preprocessAttendeeNames 预处理参会人员名称字符串
func (fm *FieldMapper) preprocessAttendeeNames(attendeesStr string) string {
	if attendeesStr == "" {
		return ""
	}

	// 1. 去除前后空格
	attendeesStr = strings.TrimSpace(attendeesStr)
	if attendeesStr == "" {
		return ""
	}

	// 2. 使用'|'分割，清理每个名称，然后重新组合
	names := strings.Split(attendeesStr, "|")
	validNames := make([]string, 0, len(names))

	for _, name := range names {
		// 去除每个名称的前后空格
		trimmed := strings.TrimSpace(name)
		if trimmed != "" {
			validNames = append(validNames, trimmed)
		}
	}

	// 3. 如果没有有效名称，返回空字符串
	if len(validNames) == 0 {
		return ""
	}

	// 4. 重新组合为标准格式（用|分割，无多余空格）
	return strings.Join(validNames, "|")
}

// BuildUpdateFields 构建更新字段（日程ID回填增强）
func (fm *FieldMapper) BuildUpdateFields(result *models.ProcessResult) map[string]interface{} {
	fields := make(map[string]interface{})

	// 统一状态管理：主要使用操作状态，同时更新运行状态保持兼容
	switch result.Status {
	case models.StatusSuccess:
		fields["操作状态"] = "已完成"
		fields["运行状态"] = "完成" // 向后兼容
		// 日程ID回填增强：记录创建成功的日程信息
		if result.EventID != "" {
			fields["日程ID"] = result.EventID
		}
		if result.EventURL != "" {
			// 飞书多维表格URL字段需要特定格式
			fields["日程链接"] = map[string]interface{}{
				"text": "查看日程",
				"link": result.EventURL,
			}
		}
		// 🔧 新增：实际参会人员信息回填
		if len(result.ActualAttendeeOpenIDs) > 0 {
			// 实际参会人员字段（飞书人员字段格式）
			attendeeUsers := make([]map[string]interface{}, 0, len(result.ActualAttendeeOpenIDs))
			for _, openID := range result.ActualAttendeeOpenIDs {
				if openID != "" {
					attendeeUsers = append(attendeeUsers, map[string]interface{}{
						"id":   openID,
						"type": "user",
					})
				}
			}
			if len(attendeeUsers) > 0 {
				fields["实际参会人员"] = attendeeUsers
			}
		}
		// 实际参会人数字段
		if result.ActualAttendeeCount >= 0 {
			fields["实际参会人数"] = fmt.Sprintf("%d", result.ActualAttendeeCount)
		}
	case models.StatusFailed:
		fields["操作状态"] = "失败"
		fields["运行状态"] = "失败" // 向后兼容
		// 日程ID回填增强：记录失败的详细错误信息
		if result.ErrorMessage != "" {
			fields["错误信息"] = result.ErrorMessage
		}
	case models.StatusProcessing:
		fields["操作状态"] = "处理中"
		// 运行状态保持为空，不设置
	case models.StatusPending:
		fields["操作状态"] = "待处理"
		// 运行状态保持为空，不设置
	case models.StatusSkipped:
		fields["操作状态"] = "已跳过"
		fields["运行状态"] = "跳过" // 向后兼容
		// 记录跳过原因
		if result.ErrorMessage != "" {
			fields["错误信息"] = result.ErrorMessage
		}
	}

	// 新增字段 - 日程管理功能
	// 操作类型（转换为中文显示）
	if result.OperationType != "" {
		fields["操作类型"] = models.GetOperationTypeChinese(result.OperationType)
	}

	// 操作状态映射已在上面的统一状态管理中处理

	return fields
}
