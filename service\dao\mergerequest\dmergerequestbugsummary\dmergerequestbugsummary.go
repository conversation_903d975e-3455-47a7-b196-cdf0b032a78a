package dmergerequestbugsummary

import (
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/models/mergerequest"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm/clause"
)

const ModelName = "BUG统计"

type Bug struct {
	BugID         int    `gorm:"primarykey" json:"bug_id"`
	BugSummary    string `gorm:"not null; type:varchar(300)" json:"bug_summary" update:"1"`
	BugOwner      string `gorm:"not null; type:varchar(60)" json:"bug_owner" update:"1"`
	BugOwnerGroup string `gorm:"not null; type:varchar(60)" json:"bug_owner_group" update:"1"`
	BugSubmitter  string `gorm:"not null; type:varchar(60)" json:"bug_submitter" update:"1"`

	BugBelong           string `gorm:"not null; type:varchar(60)" json:"bug_belong" update:"1"`
	BugTestCharger      string `gorm:"not null; type:varchar(60)" json:"bug_test_charger" update:"1"`
	BugTestChargerGroup string `gorm:"not null; type:varchar(60)" json:"bug_test_charger_group" update:"1"`
	BugPstlName         string `gorm:"not null; type:varchar(60)" json:"bug_pstl_name" update:"1"`
	BugGroupName        string `gorm:"not null; type:varchar(60)" json:"bug_group_name" update:"1"`
	BugOS               string `gorm:"not null; type:varchar(60)" json:"bug_os" update:"1"`
	BugProduct          string `gorm:"not null; type:varchar(60)" json:"bug_product" update:"1"`
	BugWorkpacketName   string `gorm:"not null; type:varchar(60)" json:"bug_workpacket_name" update:"1"`
	MainBugID           int    `gorm:"not null" json:"main_bug_id" update:"1"`
	SameBugID           int    `gorm:"not null" json:"same_bug_id" update:"1"`

	BugDisabled bool `gorm:"not null" json:"bug_disabled" update:"1"`

	BugCreatedAt      *time.Time `json:"bug_created_at"`
	BugUpdatedAt      *time.Time `json:"bug_updated_at"`
	BugState          string     `json:"bug_state"`
	BugPriority       string     `gorm:"not null; type:varchar(60)" json:"priority"`
	BugSeverity       string     `grom:"not null; type:varchar(60)" json:"severity"`
	BugRepro          int        `gorm:"not null" json:"repro"` //可重复性 1必现，2有时重现 3未尝试重现 4尝试但未重现
	BugCbdAt          *time.Time `json:"bug_cbd_at"`
	BugCbtAt          *time.Time `json:"bug_cbt_at"`
	BugDbdAt          *time.Time `json:"bug_dbd_at"`
	BugDbtAt          *time.Time `json:"bug_dbt_at"`
	BugGiveupAt       *time.Time `json:"bug_giveup_at"`
	BugDelayAt        *time.Time `json:"bug_delay_at"`
	BugCreatedAtFix   string     `gorm:"->" json:"bug_created_at_fix"`
	BugSubmitterGroup string     `json:"bug_submitter_group"`

	ApprovalType   string     `gorm:"->" json:"approval_type"`
	OverTime       int        `json:"over_time"`
	OverTimeAt     string     `gorm:"->" json:"over_time_at"`
	BeforeOverTime int        `json:"before_over_time"`
	TestStartAt    string     `gorm:"->" json:"test_start_at"`
	PlanFinishAt   *time.Time `gorm:"->" json:"plan_finish_at"`
}

type BugSummary struct {
	// BugOs string `json:"bug_os" xlsx:"项目" idx:"1"`
	// BugWorkPacketName  string  `json:"bug_workpacket_name" xlsx:"工作包" idx:"3"`
	Total                  uint    `json:"total" xlsx:"合计提交" idx:"2"`
	NotCbdPercent          float32 `json:"not_cbd_percent" xlsx:"所有BUG未决率" idx:"2"`
	NotCbdCount            uint    `json:"not_cbd_count" xlsx:"未决BUG" idx:"3"`
	CbdCount               uint    `json:"cbd_count" xlsx:"已决BUG" idx:"3"`
	Request                uint    `json:"request" xlsx:"REQUEST" idx:"4"`
	NewAssigned            uint    `json:"new_assigned" xlsx:"New+Assigned" idx:"5"`
	CheckedResolved        uint    `json:"checked_resolved" xlsx:"Checked+Resolved" idx:"6"`
	OvertimeBlockingByTest uint    `json:"overtime_blocking_by_test" xlsx:"测试提Blocking超期未决" idx:"7"`
	OvertimeCriticalByTest uint    `json:"overtime_critical_by_test" xlsx:"测试提Critical超期未决" idx:"8"`
	OvertimeMajorByTest    uint    `json:"overtime_major_by_test" xlsx:"测试提Major超期未决" idx:"9"`
	OvertimeNormalByTest   uint    `json:"overtime_normal_by_test" xlsx:"测试提Normal超期未决" idx:"10"`
	OvertimeByDev          uint    `json:"overtime_by_dev" xlsx:"开发提超期未决" idx:"11"`

	BlockingNotCbd uint `json:"blocking_not_cbd" xlsx:"Blocking未决" idx:"12"`
	CriticalNotCbd uint `json:"critical_not_cbd" xlsx:"Ctritical未决" idx:"13"`
	MajorNotCbd    uint `json:"major_not_cbd" xlsx:"Major未决" idx:"14"`
}

type BugOwnerDelayTop struct {
	BugOs         string `json:"bug_os" xlsx:"项目" idx:"1"`
	BugOwner      string `json:"bug_owner" xlsx:"Bug负责人" idx:"2"`
	BugOwnerGroup string `json:"bug_owner_group" xlsx:"所属工作组" idx:"3"`
	Count         uint   `json:"count" xlsx:"超时BUG数" idx:"4"`
	Total         uint   `json:"total" xlsx:"超时时长" idx:"5"`
	Round         uint   `json:"round" xlsx:"延期次数" idx:"6"`
}

type BugOwnerGroupDelayTop struct {
	BugOs         string `json:"bug_os" xlsx:"项目" idx:"1"`
	BugOwnerGroup string `json:"bug_owner_group" xlsx:"工作组" idx:"2"`
	Count         uint   `json:"count" xlsx:"超时BUG数" idx:"3"`
	Total         uint   `json:"total" xlsx:"超时时长" idx:"4"`
	Round         uint   `json:"round" xlsx:"延期次数" idx:"5"`
}

type WorkDay struct {
	Date      string `json:"date"`
	IsWorkDay bool   `json:"is_work_day"`
}

func (a *BugSummary) ModelName() string {
	return ModelName
}

func Model() *mergerequest.MergeRequest {
	return &mergerequest.MergeRequest{}
}

func (b *Bug) Save() error {
	err := easygorm.GetEasyGormDb().Model(b).Save(b).Error
	if err != nil {
		return err
	}
	return nil
}

var LastUpdateOverTime *time.Time

func UpdateOverTime() error {
	now := time.Now()
	// if LastUpdateOverTime != nil && now.Sub(*LastUpdateOverTime).Minutes() <= 10 {
	// 	return nil
	// }

	workdays := []*WorkDay{}
	err := easygorm.GetEasyGormDb().
		Table("workdays").
		Where(`is_work_day = false`).
		Find(&workdays).Error
	if err != nil {
		return err
	}
	workDayMap := map[string]bool{}
	for _, workday := range workdays {
		workDayMap[workday.Date] = workday.IsWorkDay
	}

	bugs := []*Bug{}
	updateBugs := []*Bug{}
	selects := []string{
		"b.bug_id as bug_id",
		"b.bug_created_at as bug_created_at",
		"b.bug_updated_at as bug_updated_at",
		"b.bug_submitter_group as bug_submitter_group",
		"b.bug_state as bug_state",
		"b.bug_priority as bug_priority",
		"b.bug_severity as bug_severity",
		"b.bug_repro as bug_repro",
		"b.bug_cbd_at as bug_cbd_at",
		"b.bug_cbt_at as bug_cbt_at",
		"b.bug_dbd_at as bug_dbd_at",
		"b.bug_dbt_at as bug_dbt_at",
		"b.bug_giveup_at as bug_giveup_at",
		"b.bug_delay_at as bug_delay_at",
		`IF(a.approval_type ='延期',
			IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_severity = 'Blocking' AND b.bug_repro=1 and main_bug_id = 0,
				date_format(DATE_ADD(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), INTERVAL + 1*a.round DAY), "%Y-%m-%d %T"),
				IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_priority = 'critical' AND b.bug_repro=1 and main_bug_id = 0,
					getWorkDay(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), 3*a.round),
					IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_priority = 'major' AND b.bug_repro=1 and main_bug_id = 0,
						getWorkDay(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), 5*a.round),
						getWorkDay(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), 7*a.round)
					)
				)
			),
			IF(a.approval_type = '专项', a.created_at, null)
		) as bug_created_at_fix`,
		"a.approval_type as approval_type",
		"a.plan_finish_at as plan_finish_at",
		"c.test_start_at as test_start_at",
		// `IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_severity = 'Blocking' AND b.bug_repro=1,
		// 	STR_TO_DATE(getWorkDay(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), IF(a.approval_type ='延期', 3*a.round, 3)), "YYYY-MM-DD"),
		// 	IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_severity = 'critical' AND b.bug_repro=1,
		// 		STR_TO_DATE(getWorkDay(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), IF(a.approval_type ='延期', 3*a.round, 3)), "YYYY-MM-DD"),
		// 		IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_severity = 'major' AND b.bug_repro=1,
		// 			STR_TO_DATE(getWorkDay(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), IF(a.approval_type ='延期', 5*a.round, 5)), "YYYY-MM-DD"),
		// 			DATE_ADD(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), INTERVAL + IF(a.approval_type ='延期', 7*a.round, 7) DAY)
		// 		)
		// 	)
		// ) as over_time_at`,
	}
	/*
				getWorkDay(IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at), IF(a.approval_type ='延期', 3*a.round, 3)),
			DATE_ADD(IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at), IF(a.approval_type ='延期', INTERVAL + 7*a.round DAY, INTERVAL + 7 DAY))

		IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_severity = 'critical' AND b.bug_repro=1,
			getWorkDay(IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at), IF(a.approval_type ='延期', 3*a.round, 3)),
			IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_severity = 'major' AND b.bug_repro=1,
				getWorkDay(IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at), IF(a.approval_type ='延期', 5*a.round, 5)),
				DATE_ADD(IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at), IF(a.approval_type ='延期', INTERVAL + 7*a.round DAY, INTERVAL + 7 DAY))
			)
		)
	*/
	// `COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' AND b.bug_severity = 'Blocking' AND b.bug_repro=1 AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -3*a.round)) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -3))), b.bug_id, NULL)) AS  overtime_blocking_by_test`,
	// `COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' AND b.bug_priority = 'critical' AND b.bug_repro=1  AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -3*a.round)) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -3))), b.bug_id, NULL)) AS  overtime_critical_by_test`,
	// `COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' AND b.bug_priority = 'major' AND b.bug_repro=1  AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -5*a.round)) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -5))), b.bug_id, NULL)) AS  overtime_major_by_test`,
	// `COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' AND b.bug_priority = 'Normal' AND b.bug_repro=1  AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) <  DATE_ADD(NOW(), INTERVAL -7*a.round DAY)) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) <  DATE_ADD(NOW(), INTERVAL -7 DAY))), b.bug_id, NULL)) AS  overtime_normal_by_test`,
	// `COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group NOT LIKE '%测试%' AND b.bug_repro=1 AND ((a.approval_type='延期' AND  IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < DATE_ADD(NOW(), INTERVAL -7*a.round DAY)) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < DATE_ADD(NOW(), INTERVAL -7 DAY))), b.bug_id, NULL)) AS  overtime_by_dev`,

	err = easygorm.GetEasyGormDb().
		Table("bugs as b").
		Joins("left join (select `round`, created_at, plan_finish_at, bug_id, `status`, approval_type from bug_approvals where id in (select max(id) as id from bug_approvals where status = 1 group by bug_id)) a on b.bug_id = a.bug_id").
		Joins("left join (select r.id as id, r.name as name, c.test_start_at as test_start_at from release_projects r left join release_project_configs c on r.id = c.release_project_id) c on c.name = b.bug_os").
		Select(selects).
		Where(`b.bug_repro = 1`).
		Find(&bugs).Error

	if err != nil {
		return err
	}

	for _, bug := range bugs {
		bug.OverTime = 0
		bug.BeforeOverTime = 0
		var bugCreatedAtFix time.Time
		if bug.ApprovalType == "专项" {
			// 专项超期时间
			var end time.Time
			if strings.HasPrefix(bug.BugState, "CLOSED") {
				if bug.BugCbdAt == nil {
					end = *bug.BugUpdatedAt
				} else {
					end = *bug.BugCbdAt
				}
			} else if strings.HasPrefix(bug.BugState, "DENIAL") {
				if bug.BugDbdAt != nil {
					end = *bug.BugDbdAt
				} else if bug.BugDbtAt != nil {
					end = *bug.BugDbtAt
				} else {
					end = *bug.BugUpdatedAt
				}
			} else if bug.BugState == "DELAY" {
				if bug.BugDelayAt == nil {
					end = *bug.BugUpdatedAt
				} else {
					end = *bug.BugDelayAt
				}
			} else if bug.BugState == "GIVEUP" {
				if bug.BugGiveupAt == nil {
					end = *bug.BugUpdatedAt
				} else {
					end = *bug.BugGiveupAt
				}
			} else {
				end = time.Now()
				beforOverTime := int(end.Sub(*bug.PlanFinishAt).Hours()/24) - 2
				if beforOverTime > 0 {
					bug.BeforeOverTime = beforOverTime
				}
			}

			overTime := int(end.Sub(*bug.PlanFinishAt).Hours() / 24)
			if overTime > 0 {
				bug.OverTime = overTime
			}
			updateBugs = append(updateBugs, bug)
		} else {
			var start, end time.Time

			if bug.ApprovalType == "延期" && bug.BugCreatedAtFix != "" {
				bugCreatedAtFix, err = time.Parse("2006-01-02", bug.BugCreatedAtFix)
				if err != nil {
					bugCreatedAtFix, _ = time.Parse("2006-01-02 15:04:05", bug.BugCreatedAtFix)
				}
				start = bugCreatedAtFix
			} else {
				start = *bug.BugCreatedAt
			}

			if bug.TestStartAt != "" {
				test_start_at, _ := time.Parse("2006-01-02", bug.TestStartAt)
				if start.Compare(test_start_at) <= 0 {
					start = test_start_at
				}
			}

			if strings.HasPrefix(bug.BugState, "CLOSED") {
				if bug.BugCbdAt == nil {
					end = *bug.BugUpdatedAt
				} else {
					end = *bug.BugCbdAt
				}
			} else if strings.HasPrefix(bug.BugState, "DENIAL") {
				if bug.BugDbdAt != nil {
					end = *bug.BugDbdAt
				} else if bug.BugDbtAt != nil {
					end = *bug.BugDbtAt
				} else {
					end = *bug.BugUpdatedAt
				}
			} else if bug.BugState == "DELAY" {
				if bug.BugDelayAt == nil {
					end = *bug.BugUpdatedAt
				} else {
					end = *bug.BugDelayAt
				}
			} else if bug.BugState == "GIVEUP" {
				if bug.BugGiveupAt == nil {
					end = *bug.BugUpdatedAt
				} else {
					end = *bug.BugGiveupAt
				}
			} else {
				end = time.Now()
			}

			// overTimeAt := start.AddDate(0, 0, limit)
			// bug.OverTimeAt = &overTimeAt
			days := 0
			workDays := 0
			for i := start; i.Compare(end) < 0; i = i.AddDate(0, 0, 1) {
				_, ok := workDayMap[i.Format("2006-01-02")]
				if !ok {
					workDays++
				}
				days++
			}

			limit := 7

			if bug.BugSeverity == "Blocking" && strings.Contains(bug.BugSubmitterGroup, "测试") && bug.MainBugID == 0 && bug.BugRepro == 1 {
				limit = 1
			} else if bug.BugSeverity != "Blocking" && bug.BugPriority == "critical" && strings.Contains(bug.BugSubmitterGroup, "测试") && bug.MainBugID == 0 && bug.BugRepro == 1 {
				limit = 3
			} else if bug.BugSeverity != "Blocking" && bug.BugPriority == "major" && strings.Contains(bug.BugSubmitterGroup, "测试") && bug.MainBugID == 0 && bug.BugRepro == 1 {
				limit = 5
			}

			var overTime int
			if limit == 1 {
				overTime = days - limit - 1 // -1用于修正超期时间提前1天
			} else {
				overTime = workDays - limit - 1 // -1用于修正超期时间提前1天

				// 计算即将超期时间
				if limit-workDays > 0 {
					bug.BeforeOverTime = limit - workDays
				}
			}

			if overTime > 0 {
				bug.OverTime = overTime
			}

			// debug用于验证数据准确性
			// logging.DebugLogger.Debug(bug.ApprovalType, bug.MainBugID, bug.BugPriority, bug.BugSeverity, bug.BugSubmitterGroup, limit, days, workDays, bug.OverTime, bug.BeforeOverTime, start, end, bug.TestStartAt, bug.BugCreatedAtFix, bug.BugID)

			updateBugs = append(updateBugs, bug)
		}
	}

	for i := 0; i < len(updateBugs); i += 1000 {
		end := i + 1001
		if end > len(updateBugs) {
			end = len(updateBugs)
		}
		err := easygorm.GetEasyGormDb().Model(&Bug{}).Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "bug_id"}},
			DoUpdates: clause.AssignmentColumns([]string{"over_time", "before_over_time"}),
		}).Create(updateBugs[i:end]).Error
		if err != nil {
			return err
		}
	}

	LastUpdateOverTime = &now
	return nil
}

func GetBugOwnerDelayTop(bugOS, bugOwnerGroup string, order, by string, filterZero bool) ([]*BugOwnerDelayTop, error) {
	// err := UpdateOverTime()
	// if err != nil {
	// 	return nil, err
	// }
	items := []*BugOwnerDelayTop{}

	selects := []string{
		"bug_os",
		"bug_owner",
		"bug_owner_group",
		"count(if(over_time > 0, over_time, null)) as count",
		"sum(over_time)as total",
		"sum(a.round) as round",
	}

	db := easygorm.GetEasyGormDb().
		Table("bugs as b").
		Joins("left join (select `round`, created_at, bug_id, `status`, approval_type from bug_approvals where id in (select max(id) as id from bug_approvals where status = 1 group by bug_id)) a on b.bug_id = a.bug_id").
		Select(selects).
		Group("b.bug_os, b.bug_owner, b.bug_owner_group").
		Order(fmt.Sprintf("%s %s", by, order)).
		Having("total > 0 or round > 0 or count > 0")

	if filterZero {
		db = db.Having("total > 0")
	}

	where := easygorm.GetEasyGormDb().
		Where("b.bug_os like 'NTOS1.0R%'")
		// Where("b.bug_owner_group in ?", vars.FilterBugOwnerGroups)

	if len(bugOS) > 0 {
		where = where.Where("b.bug_os = ?", bugOS)
	}
	if len(bugOwnerGroup) > 0 {
		where = where.Where("b.bug_owner_group in ?", strings.Split(bugOwnerGroup, ","))
	}

	err := db.Where(where).Limit(5).Find(&items).Error
	if err != nil {
		return items, err
	}

	return items, nil
}

func GetBugOwnerGroupDelayTop(bugOS string, bugOwnerGroup, order, by string, filterZero bool) ([]*BugOwnerGroupDelayTop, error) {
	// err := UpdateOverTime()
	// if err != nil {
	// 	return nil, err
	// }
	items := []*BugOwnerGroupDelayTop{}

	selects := []string{
		"bug_os",
		"bug_owner_group",
		"count(if(over_time > 0, over_time, null)) as count",
		"sum(over_time)as total",
		"sum(a.round) as round",
	}

	db := easygorm.GetEasyGormDb().
		Table("bugs as b").
		Joins("left join (select `round`, created_at, bug_id, `status`, approval_type from bug_approvals where id in (select max(id) as id from bug_approvals where status = 1 group by bug_id)) a on b.bug_id = a.bug_id").
		Select(selects).
		Group("b.bug_os, b.bug_owner_group").Order(fmt.Sprintf("%s %s", by, order)).
		Having("total > 0 or round > 0 or count > 0")

	if filterZero {
		db = db.Having("total > 0")
	}
	where := easygorm.GetEasyGormDb().
		Where("b.bug_os like 'NTOS1.0R%'")
		// Where("b.bug_owner_group in ?", vars.FilterBugOwnerGroups)

	if len(bugOS) > 0 {
		where = where.Where("b.bug_os = ?", bugOS)
	}
	if len(bugOwnerGroup) > 0 {
		where = where.Where("b.bug_owner_group in ?", strings.Split(bugOwnerGroup, ","))
	}
	err := db.Where(where).Find(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func GetBugOwnerBeforeOverTime(bugOSes []string, order, by string) ([]*Bug, error) {
	// err := UpdateOverTime()
	// if err != nil {
	// 	return nil, err
	// }
	items := []*Bug{}

	/*
		<th>BUGID</th>
		<th style="width: 400px">Bug描述</th>
		<th>Bug严重性</th>
		<th>Bug优先级</th>
		<th>Bug状态</th>
		<th>Bug负责人</th>
		<th>提交时间</th>
		<th>超时时间</th>
	*/
	selects := []string{
		"b.bug_id bug_id",
		"b.bug_summary bug_summary",
		"b.bug_severity bug_severity",
		"b.bug_priority bug_priority",
		"b.bug_state bug_state",
		"b.bug_created_at bug_created_at",
		"b.bug_owner bug_owner",
		`IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_severity = 'Blocking' AND b.bug_repro=1 and main_bug_id = 0,
			DATE_ADD(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), INTERVAL + IF(a.approval_type ='延期', 1*(a.round+1)+1, 2) DAY),
			IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_priority = 'critical' AND b.bug_repro=1 and main_bug_id = 0,
				getWorkDay(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), IF(a.approval_type ='延期', 3*(a.round+1)+1, 4)),
				IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_priority = 'major' AND b.bug_repro=1 and main_bug_id = 0,
					getWorkDay(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), IF(a.approval_type ='延期', 5*(a.round+1)+1, 6)),
					getWorkDay(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), IF(a.approval_type ='延期', 7*(a.round+1)+1, 8))
				)
			)
		) as over_time_at`,
	}

	db := easygorm.GetEasyGormDb().
		Table("bugs as b").
		Joins("left join (select `round`, created_at, bug_id, `status`, approval_type from bug_approvals where id in (select max(id) as id from bug_approvals where status = 1 group by bug_id)) a on b.bug_id = a.bug_id").
		Joins("left join (select r.id as id, r.name as name, c.test_start_at as test_start_at from release_projects r left join release_project_configs c on r.id = c.release_project_id) c on c.name = b.bug_os").
		Select(selects).
		Order(fmt.Sprintf("%s %s", by, order))

	where := easygorm.GetEasyGormDb().
		// Where("b.bug_owner_group in ?", vars.FilterBugOwnerGroups).
		Where("b.before_over_time > 0").
		Where("b.before_over_time < 2").
		Where(`b.bug_state not like 'CLOSE%' and b.bug_state not like 'DENIAL%' and b.bug_state not in ("DELAY","GIVEUP")`)

	if len(bugOSes) > 0 {
		where = where.Where("b.bug_os in ?", bugOSes)
	}

	err := db.Where(where).Find(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func GetBugOwnerTodo(bugOSes []string, order, by string) ([]*Bug, error) {
	// err := UpdateOverTime()
	// if err != nil {
	// 	return nil, err
	// }
	items := []*Bug{}

	/*
		<th>BUGID</th>
		<th style="width: 400px">Bug描述</th>
		<th>Bug严重性</th>
		<th>Bug优先级</th>
		<th>Bug状态</th>
		<th>Bug负责人</th>
		<th>提交时间</th>
		<th>超时时间</th>
	*/
	selects := []string{
		"b.bug_id bug_id",
		"b.bug_summary bug_summary",
		"b.bug_severity bug_severity",
		"b.bug_priority bug_priority",
		"b.bug_state bug_state",
		"b.bug_created_at bug_created_at",
		"b.bug_owner bug_owner",
		`IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_severity = 'Blocking' AND b.bug_repro=1 and main_bug_id = 0,
			DATE_ADD(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), INTERVAL + IF(a.approval_type ='延期', 1*(a.round+1)+1, 2) DAY),
			IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_priority = 'critical' AND b.bug_repro=1 and main_bug_id = 0,
				getWorkDay(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), IF(a.approval_type ='延期', 3*(a.round+1)+1, 4)),
				IF(b.bug_submitter_group LIKE '%测试%' AND b.bug_priority = 'major' AND b.bug_repro=1 and main_bug_id = 0,
					getWorkDay(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), IF(a.approval_type ='延期', 5*(a.round+1)+1, 6)),
					getWorkDay(IF(c.test_start_at = '' OR c.test_start_at IS null or c.test_start_at <= b.bug_created_at,  b.bug_created_at, c.test_start_at), IF(a.approval_type ='延期', 7*(a.round+1)+1, 8))
				)
			)
		) as over_time_at`,
	}

	db := easygorm.GetEasyGormDb().
		Table("bugs as b").
		Joins("left join (select `round`, created_at, bug_id, `status`, approval_type from bug_approvals where id in (select max(id) as id from bug_approvals where status = 1 group by bug_id)) a on b.bug_id = a.bug_id").
		Joins("left join (select r.id as id, r.name as name, c.test_start_at as test_start_at from release_projects r left join release_project_configs c on r.id = c.release_project_id) c on c.name = b.bug_os").
		Select(selects).
		Order(fmt.Sprintf("%s %s", by, order))

	where := easygorm.GetEasyGormDb().
		// Where("b.bug_owner_group in ?", vars.FilterBugOwnerGroups).
		Where(`b.bug_state not like 'CLOSE%' and b.bug_state not like 'DENIAL%' and b.bug_state not in ("DELAY","GIVEUP")`)

	if len(bugOSes) > 0 {
		where = where.Where("b.bug_os in ?", bugOSes)
	}

	err := db.Where(where).Find(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func (s *BugSummary) All(bugOS string, pstlID uint, order, by string) error {

	selects := []string{
		"b.bug_os",
		"COUNT(*) AS total",
		`Round(COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP"), b.bug_id, NULL))/COUNT(*) *100, 2) AS not_cbd_percent`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP"), b.bug_id, NULL)) AS not_cbd_count`,
		`COUNT(IF(b.bug_state LIKE 'CLOSED%' OR b.bug_state LIKE 'DENIAL%' OR b.bug_state IN ("DELAY", "GIVEUP"), b.bug_id, NULL)) AS cbd_count`,
		`COUNT(IF(b.bug_state LIKE 'REQUEST%', b.bug_id, NULL)) AS request`,
		`COUNT(IF(b.bug_state IN ('NEW', 'ASSIGNED'), b.bug_id, NULL)) AS new_assigned`,
		`COUNT(IF(b.bug_state IN ('CHECKED', 'RESOLVED'), b.bug_id, NULL)) AS checked_resolved`,

		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' and b.main_bug_id = 0 AND b.bug_severity = 'Blocking' AND b.bug_repro=1 AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < DATE_ADD(NOW(), INTERVAL -1*(a.round+1) DAY)) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < DATE_ADD(NOW(), INTERVAL -1 DAY))), b.bug_id, NULL)) AS  overtime_blocking_by_test`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' and b.main_bug_id = 0 AND b.bug_priority = 'critical' AND b.bug_repro=1  AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -3*(a.round+1))) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -3))), b.bug_id, NULL)) AS  overtime_critical_by_test`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' and b.main_bug_id = 0 AND b.bug_priority = 'major' AND b.bug_repro=1  AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -5*(a.round+1))) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -5))), b.bug_id, NULL)) AS  overtime_major_by_test`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_submitter_group LIKE '%测试%' and b.main_bug_id = 0 AND (b.bug_severity = 'Normal' and b.bug_priority != 'critical' and b.bug_priority != 'major') AND b.bug_repro=1  AND ((a.approval_type ='延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) <  getWorkDay(now(), -7*(a.round+1))) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -7))), b.bug_id, NULL)) AS  overtime_normal_by_test`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and (b.bug_submitter_group not LIKE '%测试%' or b.main_bug_id != 0) AND b.bug_repro=1 AND ((a.approval_type = '延期' AND IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) <  getWorkDay(now(), -7*(a.round+1))) OR (a.approval_type is null and IF(c.test_start_at >= b.bug_created_at, c.test_start_at, b.bug_created_at) < getWorkDay(now(), -7))), b.bug_id, NULL)) AS  overtime_by_dev`,

		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_severity = 'Blocking', b.bug_id, NULL)) AS  blocking_not_cbd`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_priority = 'critical', b.bug_id, NULL)) AS  critical_not_cbd`,
		`COUNT(IF(b.bug_state not LIKE 'CLOSED%' AND b.bug_state not LIKE 'DENIAL%' AND b.bug_state NOT IN ("DELAY", "GIVEUP") and b.bug_priority = 'major', b.bug_id, NULL)) AS  major_not_cbd`,
	}

	db := easygorm.GetEasyGormDb().
		Table("bugs as b").
		Joins("left join (select `round`, created_at, bug_id, `status`, approval_type from bug_approvals where id in (select max(id) as id from bug_approvals where status = 1 group by bug_id)) a on b.bug_id = a.bug_id").
		Joins("left join (select r.id as id, r.name as name, c.test_start_at as test_start_at from release_projects r left join release_project_configs c on r.id = c.release_project_id) c on c.name = b.bug_os").
		Select(selects).
		Group("b.bug_os").
		Order(fmt.Sprintf("%s %s", by, order))

	where := easygorm.GetEasyGormDb().
		Where("b.bug_os like 'NTOS1.0R%'")
		// Where("b.bug_owner_group in ?", vars.FilterBugOwnerGroups)

	if len(bugOS) > 0 {
		where = where.Where("b.bug_os = ?", bugOS)
	}

	err := db.Where(where).Find(&s).Error
	if err != nil {
		return err
	}
	return nil
}

func (s *BugSummary) ExportBugSummary(items []*BugSummary) ([]interface{}, [][]interface{}) {
	xt := reflect.TypeOf(s)
	// xv := reflect.ValueOf(s)

	rows := [][]interface{}{}
	headers := []interface{}{}
	for i := 0; i < xt.Elem().NumField(); i++ {
		head, ok := xt.Elem().Field(i).Tag.Lookup("xlsx")
		if ok {
			headers = append(headers, head)
		}
	}
	rows = append(rows, headers)

	for _, e := range items {
		cells := []interface{}{}
		xv := reflect.ValueOf(e)
		for i := 0; i < xv.Elem().NumField(); i++ {
			_, ok := xt.Elem().Field(i).Tag.Lookup("xlsx")
			if ok {
				cells = append(cells, xv.Elem().Field(i).Interface())
			}

		}
		rows = append(rows, cells)
	}
	return headers, rows
}

type Column struct {
	Idx   int    `json:"idx"`
	Key   string `json:"key"`
	Label string `json:"label"`
}

func (s *BugSummary) GetColumns() ([]*Column, error) {
	xt := reflect.TypeOf(s)

	columns := []*Column{}

	for i := 0; i < xt.Elem().NumField(); i++ {
		key, ok1 := xt.Elem().Field(i).Tag.Lookup("json")
		label, ok2 := xt.Elem().Field(i).Tag.Lookup("xlsx")
		_idx, ok3 := xt.Elem().Field(i).Tag.Lookup("idx")

		if ok1 && ok2 && ok3 {
			idx, err := strconv.Atoi(_idx)
			if err != nil {
				return columns, err
			}
			columns = append(columns, &Column{
				Idx:   idx,
				Key:   key,
				Label: label,
			})
		}
	}
	sort.SliceStable(columns, func(i, j int) bool {
		if columns[i].Idx < columns[j].Idx {
			return true
		}
		return false
	})
	return columns, nil
}

func (s *BugSummary) Struct2Slice() ([]map[string]interface{}, error) {
	xt := reflect.TypeOf(s)
	v := reflect.ValueOf(*s)
	result := []map[string]interface{}{}
	for i := 0; i < xt.Elem().NumField(); i++ {
		key, ok1 := xt.Elem().Field(i).Tag.Lookup("json")
		label, ok2 := xt.Elem().Field(i).Tag.Lookup("xlsx")
		_idx, ok3 := xt.Elem().Field(i).Tag.Lookup("idx")

		if ok1 && ok2 && ok3 {
			idx, err := strconv.Atoi(_idx)
			if err != nil {
				return result, err
			}
			result = append(result, map[string]interface{}{
				"idx":   idx,
				"key":   key,
				"label": label,
				"value": v.Field(i).Interface(),
			})
		}
	}
	sort.SliceStable(result, func(i, j int) bool {
		if result[i]["idx"].(int) < result[j]["idx"].(int) {
			return true
		}
		return false
	})
	return result, nil
}
