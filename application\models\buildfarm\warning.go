package buildfarm

import "irisAdminApi/application/models"

type BuildfarmWarning struct {
	models.ModelBase
	MakeJobID uint   `gorm:"not null;" json:"make_job_id"`
	JobID     string `gorm:"not null; type:varchar(60)" json:"job_id"`
	Detail    string `gorm:"not null; type:varchar(500)" json:"detail"`
	Position  string `gorm:"not null; type:varchar(100)" json:"position"`
	Filename  string `gorm:"not null; type:varchar(100)" json:"filename"`
	SourceDir string `gorm:"not null; type:varchar(200)" json:"source_dir"`
	Project   string `gorm:"not null; type:varchar(60)" json:"project"`
	Branch    string `gorm:"not null; type:varchar(60)" json:"branch"`
	Product   string `gorm:"not null; type:varchar(60)" json:"product"`
	BuildType string `gorm:"not null; type:varchar(60)" json:"build_type"`
}

type BuildfarmWarningSummary struct {
	models.ModelBase
	MakeJobID       uint   `gorm:"not null;" json:"make_job_id" update:"1"`
	Detail          string `gorm:"uniqIndex: id_unique; not null; type:varchar(500)" json:"detail"`
	Position        string `gorm:"uniqIndex: id_unique; not null; type:varchar(200)" json:"position"`
	Filename        string `gorm:"uniqIndex: id_unique; not null; type:varchar(100)" json:"filename"`
	SourceDir       string `gorm:"not null; type:varchar(200)" json:"source_dir"`
	Project         string `gorm:"uniqIndex: id_unique; not null; type:varchar(60)" json:"project"`
	Branch          string `gorm:"uniqIndex: id_unique; not null; type:varchar(60)" json:"branch"`
	Product         string `gorm:"uniqIndex: id_unique; not null; type:varchar(60)" json:"product"`
	BuildType       string `gorm:"uniqIndex: id_unique; not null; type:varchar(60)" json:"build_type"`
	FullPath        string `gorm:"not null; type:varchar(500)" json:"full_path" update:"1"`
	Component       string `gorm:"not null; type:varchar(100)" json:"component" update:"1"`
	ComponentPacket string `gorm:"not null; type:varchar(100)" json:"component_packet" update:"1"`
	Owner           string `gorm:"not null; type:varchar(60)" json:"owner" update:"1"`
	Comment         string `gorm:"not null; type:varchar(60)" json:"comment" update:"1"`
	Status          string `gorm:"not null; type:varchar(60)" json:"status" update:"1"`
}

var BuildfarmWarningStatus = map[uint]string{
	0: "未分析",
	1: "确认未修订",
	2: "已修订",
	3: "无需修订",
}
