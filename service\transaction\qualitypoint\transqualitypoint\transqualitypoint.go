package transqualitypoint

import (
	"errors"
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/productionrelease/dproductionproctask"
	"irisAdminApi/service/dao/qualitypoint/dqualitypointprocdef"
	"irisAdminApi/service/dao/qualitypoint/dqualitypointprocinst"
	"irisAdminApi/service/dao/qualitypoint/dqualitypointproctask"
	"irisAdminApi/service/dao/qualitypoint/dqualitypointsdeduction"
	"irisAdminApi/service/dao/qualitypoint/dqualitypointsrecoverie"
	"irisAdminApi/service/dao/qualitypoint/dqualityuserpoint"
	"irisAdminApi/service/dao/qualitypoint/dqualityviolation"
	"irisAdminApi/service/dao/user/duser"
	"time"

	"gorm.io/gorm"
)

// 创建正项事项
func CreatePositiveViolationTransaction(userID uint, violationObject map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		//创建事项
		if err := tx.Model(dqualityviolation.Model()).Create(violationObject).Error; err != nil {
			// 返回任何错误都会回滚事务
			return err
		}
		qualityviolation := dqualityviolation.Response{}
		if err := tx.Model(dqualityviolation.Model()).Where("uuid = ?", violationObject["Uuid"]).Find(&qualityviolation).Error; err != nil {
			return err
		}
		if qualityviolation.ID == 0 {
			return errors.New("创建事项失败")
		}
		//创建事项实例
		procInstObject := map[string]interface{}{
			"Title": qualityviolation.AssessmentItemCode + ":" + qualityviolation.AssessmentItemName + ":" + qualityviolation.Uuid,
			// 当前节点
			"NodeID":              "start",
			"TaskID":              0,
			"StartUserID":         userID,
			"QualityViolationID":  qualityviolation.ID,
			"ResponsiblePersonID": qualityviolation.ResponsiblePersonID,
			"AuditManagerID":      qualityviolation.AuditManagerID,
			"CreatedAt":           time.Now(),
			"UpdatedAt":           time.Now(),
			"Status":              0,
		}
		if err := tx.Model(dqualitypointprocinst.Model()).Create(procInstObject).Error; err != nil {
			// 返回任何错误都会回滚事务
			return err
		}
		procInst := dqualitypointprocinst.Response{}
		if err := tx.Model(dqualitypointprocinst.Model()).Where("quality_violation_id = ?", qualityviolation.ID).Find(&procInst).Error; err != nil {
			return err
		}
		if procInst.ID == 0 {
			return errors.New("创建正向事项实例失败")
		}

		procTaskObjects := map[string]interface{}{
			"CreatedAt":  time.Now(),
			"UpdatedAt":  time.Now(),
			"NodeName":   "创建正向事项",
			"NodeID":     "start",
			"PrevNodeID": "",
			"ProcInstID": procInst.ID,
			"Assignee":   userID,
			"Status":     1,
			"Flag":       true,
			"Done":       true,
		}

		if err := tx.Model(dqualitypointproctask.Model()).Create(procTaskObjects).Error; err != nil {
			return err
		}
		procTask := dqualitypointproctask.Response{}
		if err := tx.Model(dqualitypointproctask.Model()).Where("proc_inst_id = ? and node_id=?", procInst.ID, "start").Find(&procTask).Error; err != nil {
			return err
		}
		if procTask.ID == 0 {
			return errors.New("创建正向事项任务失败")
		}

		//创建消分卡
		currentYear := time.Now().Year()
		nextYear := currentYear + 1
		effectiveDate := time.Date(nextYear, 1, 1, 0, 0, 0, 0, time.Now().Location())
		cardObject := map[string]interface{}{
			"CreatedAt":       time.Now(),
			"UpdatedAt":       time.Now(),
			"ViolationID":     qualityviolation.ID,
			"UserID":          qualityviolation.ResponsiblePersonID,
			"PointsRecovered": qualityviolation.DeductionPoints,
			"Year":            currentYear,
			"EffectiveDate":   effectiveDate,
			"UsageState":      0,
		}
		if libs.Config.QualityPoint.TrialRun { //试用期期间,消分可抵消当年被扣积分
			cardObject["EffectiveDate"] = time.Now()
		}
		//获取责任人积分信息
		userPoint := dqualityuserpoint.Response{}
		if err := tx.Model(dqualityuserpoint.Model()).Where("user_id=? and year=?", qualityviolation.ResponsiblePersonID, currentYear).Find(&userPoint).Error; err != nil {
			return err
		}
		//试用期期间，针对影响度低的问题，消分可抵消当年被扣积分，如果剩余积分为12分，则不进行操作，若积分小于12分，根据规则判断是否可以进行加分，并记录
		if libs.Config.QualityPoint.TrialRun && userPoint.TotalPoints != 12 && userPoint.PointsDeducted > 0 {
			//查找扣减积分数据详情---影响度低的分数扣减记录
			pointsdeductionList := []*dqualitypointsdeduction.ListResponse{}
			err := tx.Model(dqualitypointsdeduction.Model()).Where("user_id=? and year=? and impact_degree<>3", qualityviolation.ResponsiblePersonID, currentYear).Find(&pointsdeductionList).Error
			if err != nil {
				return err
			}
			//遍历统计扣减积分数据总数
			var totalPointsDeducted float32 //为正数
			for _, pointsdeduction := range pointsdeductionList {
				totalPointsDeducted += pointsdeduction.PointsDeducted
			}
			//查询已消分数据
			pointsrecoverieList := []*dqualitypointsrecoverie.ListResponse{}
			err = tx.Model(dqualitypointsrecoverie.Model()).Where("user_id=? and year=?", qualityviolation.ResponsiblePersonID, currentYear).Find(&pointsrecoverieList).Error
			if err != nil {
				return err
			}
			var totalPointsRecovered float32
			for _, pointsrecoverie := range pointsrecoverieList {
				totalPointsRecovered += pointsrecoverie.UsagePoint
			}
			//可增加积分数
			addablePoint := totalPointsDeducted - totalPointsRecovered
			if addablePoint > 0 {
				pointObject := map[string]interface{}{}
				//可抵消积分和扣减积分对比
				if addablePoint >= qualityviolation.DeductionPoints {
					cardObject["UsageState"] = 2 //已使用
					cardObject["UsagePoint"] = qualityviolation.DeductionPoints
					//更新用户积分总数
					pointObject["total_points"] = userPoint.TotalPoints + qualityviolation.DeductionPoints
					pointObject["points_recovered"] = userPoint.PointsRecovered + qualityviolation.DeductionPoints
					if userPoint.TotalPoints+qualityviolation.DeductionPoints > 12 {
						pointObject["total_points"] = 12
						pointObject["points_recovered"] = userPoint.PointsRecovered + (qualityviolation.DeductionPoints - (userPoint.TotalPoints + qualityviolation.DeductionPoints - 12))
					}
				} else {
					pointObject["total_points"] = userPoint.TotalPoints + addablePoint
					cardObject["UsageState"] = 1 //使用中
					cardObject["UsagePoint"] = addablePoint
					pointObject["points_recovered"] = userPoint.PointsRecovered + addablePoint
					if userPoint.TotalPoints+addablePoint > 12 {
						pointObject["total_points"] = 12
					}
				}
				err = tx.Model(dqualityuserpoint.Model()).Where("user_id=? and year=?", qualityviolation.ResponsiblePersonID, currentYear).Updates(pointObject).Error
				if err != nil {
					return err
				}
			}

		}
		if err := tx.Model(dqualitypointsrecoverie.Model()).Create(cardObject).Error; err != nil {
			return err
		}
		//事项完结
		//更新实例状态
		if err := tx.Model(dqualitypointprocinst.Model()).Where("id = ?", procInst.ID).Update("status", 1).Error; err != nil {
			return err
		}
		//更新事项状态
		if err := tx.Model(dqualityviolation.Model()).Where("id = ?", qualityviolation.ID).Update("status", 1).Error; err != nil {
			return err
		}
		//消息通知到责任人
		// go SendMail(procTaskObjects, qualityviolation.ID, false)
		//返回 nil 提交事务
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func CreateNegativeImpactViolationTransaction(userID uint, violationObject map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		resource := violationObject["Resource"].(string)
		delete(violationObject, "Resource")
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		//创建事项
		if err := tx.Model(dqualityviolation.Model()).Create(violationObject).Error; err != nil {
			// 返回任何错误都会回滚事务
			return err
		}
		qualityviolation := dqualityviolation.Response{}
		if err := tx.Model(dqualityviolation.Model()).Where("uuid = ?", violationObject["Uuid"]).Find(&qualityviolation).Error; err != nil {
			return err
		}
		if qualityviolation.ID == 0 {
			return errors.New("创建事项失败")
		}
		//创建事项实例
		procInstObject := map[string]interface{}{
			"Title": qualityviolation.AssessmentItemCode + ":" + qualityviolation.AssessmentItemName + ":" + qualityviolation.Uuid,
			// 当前节点
			"NodeID":              "start",
			"TaskID":              0,
			"StartUserID":         userID,
			"QualityViolationID":  qualityviolation.ID,
			"ResponsiblePersonID": qualityviolation.ResponsiblePersonID,
			"AuditManagerID":      qualityviolation.AuditManagerID,
			"CreatedAt":           time.Now(),
			"UpdatedAt":           time.Now(),
			"Resource":            resource,
			"Status":              0,
		}
		if err := tx.Model(dqualitypointprocinst.Model()).Create(procInstObject).Error; err != nil {
			// 返回任何错误都会回滚事务
			return err
		}
		procInst := dqualitypointprocinst.Response{}
		if err := tx.Model(dqualitypointprocinst.Model()).Where("quality_violation_id = ?", qualityviolation.ID).Find(&procInst).Error; err != nil {
			return err
		}
		if procInst.ID == 0 {
			return errors.New("创建违规事项实例失败")
		}

		procTaskObjects := []map[string]interface{}{}
		defResource := procInst.Resource
		nodes, _ := dqualitypointprocdef.GetNodes(defResource)
		for _, node := range nodes {
			if node.NodeID == "start" {
				procTaskObjects = append(procTaskObjects, map[string]interface{}{
					"CreatedAt":  time.Now(),
					"UpdatedAt":  time.Now(),
					"NodeName":   node.Name,
					"NodeID":     node.NodeID,
					"PrevNodeID": node.PrevNodeID,
					"ProcInstID": procInst.ID,
					"Assignee":   userID,
					"Status":     1,
					"Flag":       true,
					"Done":       true,
				})
				break
			}
		}
		for _, node := range nodes {
			assignee := node.Assignee
			if node.Assignee == 0 {
				assignee = userID
			}
			if node.PrevNodeID == "start" {
				procTaskObjects = append(procTaskObjects, map[string]interface{}{
					"CreatedAt":  time.Now(),
					"UpdatedAt":  time.Now(),
					"NodeName":   node.Name,
					"NodeID":     node.NodeID,
					"PrevNodeID": node.PrevNodeID,
					"ProcInstID": procInst.ID,
					"Assignee":   assignee,
					"Status":     0,
					"Flag":       true,
				})
			}
		}
		//积分扣减 qualityviolation.ResponsiblePersonID
		//创建积分扣减记录
		currentYear := time.Now().Year()
		deductionObject := map[string]interface{}{
			"CreatedAt":      time.Now(),
			"UpdatedAt":      time.Now(),
			"ViolationID":    qualityviolation.ID,
			"UserID":         qualityviolation.ResponsiblePersonID,
			"PointsDeducted": qualityviolation.ScoreImpact,
			"IssueLevel":     qualityviolation.IssueLevel,
			"ImpactDegree":   qualityviolation.ImpactDegree,
			"Year":           currentYear,
		}

		if err := tx.Model(dqualitypointsdeduction.Model()).Create(deductionObject).Error; err != nil {
			return err
		}
		//获取责任人积分信息
		userPoint := dqualityuserpoint.Response{}
		if err := tx.Model(dqualityuserpoint.Model()).Where("user_id=? and year=?", qualityviolation.ResponsiblePersonID, currentYear).Find(&userPoint).Error; err != nil {
			return err
		}
		//更新积分信息
		pointObject := map[string]interface{}{
			"updated_at":      time.Now(),
			"points_deducted": userPoint.PointsDeducted + qualityviolation.ScoreImpact,
		}
		pointObject["total_points"] = userPoint.TotalPoints - qualityviolation.ScoreImpact
		if err := tx.Model(dqualityuserpoint.Model()).Where("user_id=? and year=?", qualityviolation.ResponsiblePersonID, currentYear).Updates(pointObject).Error; err != nil {
			return err
		}
		if len(procTaskObjects) > 0 {
			if err := tx.Model(dqualitypointproctask.Model()).Create(procTaskObjects).Error; err != nil {
				return err
			}
		}
		//返回 nil 提交事务
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

// 更新正项事项
func UpdatePositiveViolationTransaction(violationID uint, violationObject map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		//更新事项
		if err := tx.Model(dqualityviolation.Model()).Where("id = ?", violationID).Updates(violationObject).Error; err != nil {
			// 返回任何错误都会回滚事务
			return err
		}
		qualityviolation := dqualityviolation.Response{}
		if err := tx.Model(dqualityviolation.Model()).Where("id = ?", violationID).Find(&qualityviolation).Error; err != nil {
			return err
		}
		if qualityviolation.ID == 0 {
			return errors.New("更新事项失败")
		}

		//更新事项实例数据
		procInstObject := map[string]interface{}{
			"Title":               qualityviolation.AssessmentItemCode + ":" + qualityviolation.AssessmentItemName + ":" + qualityviolation.Uuid,
			"QualityViolationID":  qualityviolation.ID,
			"ResponsiblePersonID": qualityviolation.ResponsiblePersonID,
			"AuditManagerID":      qualityviolation.AuditManagerID,
			"UpdatedAt":           time.Now(),
		}
		if err := tx.Model(dqualitypointprocinst.Model()).Where("quality_violation_id = ?", violationID).Updates(procInstObject).Error; err != nil {
			// 返回任何错误都会回滚事务
			return err
		}
		procInst := dqualitypointprocinst.Response{}
		if err := tx.Model(dqualitypointprocinst.Model()).Where("quality_violation_id = ?", qualityviolation.ID).Find(&procInst).Error; err != nil {
			return err
		}
		if procInst.ID == 0 {
			return errors.New("更新正向事项实例失败")
		}

		currentYear := time.Now().Year()
		nextYear := currentYear + 1
		effectiveDate := time.Date(nextYear, 1, 1, 0, 0, 0, 0, time.Now().Location())
		//获本次取消分卡信息
		card := dqualitypointsrecoverie.Response{}
		err := tx.Model(dqualitypointsrecoverie.Model()).Where("violation_id=? and user_id=? ", qualityviolation.ID, qualityviolation.ResponsiblePersonID).Find(&card).Error
		if err != nil {
			return err
		}

		// 先“重置”用户积分，恢复到原始状态，去除这张消分卡的影响
		//判断消分卡是否被使用,使用的话先重置回去
		if card.UsagePoint > 0 {
			// 获取用户当前积分信息
			userPointData := dqualityuserpoint.Response{}
			currentYear := time.Now().Year()
			if err := tx.Model(dqualityuserpoint.Model()).Where("user_id = ? AND year = ?", qualityviolation.ResponsiblePersonID, currentYear).Find(&userPointData).Error; err != nil {
				return err
			}
			resetPoints := userPointData.TotalPoints - card.UsagePoint
			resetRecoveredPoints := userPointData.PointsRecovered - card.UsagePoint
			// 更新用户积分信息，先回滚之前的消分效果
			resetUpdates := map[string]interface{}{
				"total_points":     resetPoints,
				"points_recovered": resetRecoveredPoints,
			}
			if err := tx.Model(dqualityuserpoint.Model()).Where("user_id = ? AND year = ?", card.UserID, currentYear).Updates(resetUpdates).Error; err != nil {
				return err
			}
		}

		//更新消分卡
		cardObject := map[string]interface{}{
			"UpdatedAt":       time.Now(),
			"UserID":          qualityviolation.ResponsiblePersonID,
			"PointsRecovered": qualityviolation.DeductionPoints,
			"Year":            currentYear,
			"EffectiveDate":   effectiveDate,
			"UsageState":      0,
		}
		if libs.Config.QualityPoint.TrialRun { //试用期期间,消分可抵消当年被扣积分
			cardObject["EffectiveDate"] = time.Now()
		}
		//获取责任人积分信息
		userPoint := dqualityuserpoint.Response{}
		if err := tx.Model(dqualityuserpoint.Model()).Where("user_id=? and year=?", qualityviolation.ResponsiblePersonID, currentYear).Find(&userPoint).Error; err != nil {
			return err
		}
		//试用期期间，针对影响度低的问题，消分可抵消当年被扣积分，如果剩余积分为12分，则不进行操作，若积分小于12分，根据规则判断是否可以进行加分，并记录
		if libs.Config.QualityPoint.TrialRun && userPoint.TotalPoints != 12 && userPoint.PointsDeducted > 0 {
			//查找扣减积分数据详情---影响度低的分数扣减记录
			pointsdeductionList := []*dqualitypointsdeduction.ListResponse{}
			err := tx.Model(dqualitypointsdeduction.Model()).Where("user_id=? and year=? and impact_degree<>3", qualityviolation.ResponsiblePersonID, currentYear).Find(&pointsdeductionList).Error
			if err != nil {
				return err
			}
			//遍历统计扣减积分数据总数
			var totalPointsDeducted float32
			for _, pointsdeduction := range pointsdeductionList {
				totalPointsDeducted += pointsdeduction.PointsDeducted
			}
			//查询已消分数据
			pointsrecoverieList := []*dqualitypointsrecoverie.ListResponse{}
			err = tx.Model(dqualitypointsrecoverie.Model()).Where("user_id=? and year=? and id<>?", qualityviolation.ResponsiblePersonID, currentYear, card.ID).Find(&pointsrecoverieList).Error
			if err != nil {
				return err
			}
			var totalPointsRecovered float32
			for _, pointsrecoverie := range pointsrecoverieList {
				totalPointsRecovered += pointsrecoverie.UsagePoint
			}
			//可增加积分数
			addablePoint := totalPointsDeducted - totalPointsRecovered
			if addablePoint > 0 {
				pointObject := map[string]interface{}{}
				//可抵消积分和扣减积分对比
				if addablePoint >= qualityviolation.DeductionPoints {
					cardObject["UsageState"] = 2 //已使用
					cardObject["UsagePoint"] = qualityviolation.DeductionPoints
					//更新用户积分总数
					pointObject["total_points"] = userPoint.TotalPoints + qualityviolation.DeductionPoints
					pointObject["points_recovered"] = userPoint.PointsRecovered + qualityviolation.DeductionPoints
					if userPoint.TotalPoints+qualityviolation.DeductionPoints > 12 {
						pointObject["total_points"] = 12
						pointObject["points_recovered"] = userPoint.PointsRecovered + (qualityviolation.DeductionPoints - (userPoint.TotalPoints + qualityviolation.DeductionPoints - 12))
					}
				} else {
					pointObject["total_points"] = userPoint.TotalPoints + addablePoint
					cardObject["UsageState"] = 1 //使用中
					cardObject["UsagePoint"] = addablePoint
					pointObject["points_recovered"] = userPoint.PointsRecovered + addablePoint
					if userPoint.TotalPoints+addablePoint > 12 {
						pointObject["total_points"] = 12
					}
				}
				err = tx.Model(dqualityuserpoint.Model()).Where("user_id=? and year=?", qualityviolation.ResponsiblePersonID, currentYear).Updates(pointObject).Error
				if err != nil {
					return err
				}
			}

		}
		if err := tx.Model(dqualitypointsrecoverie.Model()).Where("id=?", card.ID).Updates(cardObject).Error; err != nil {
			return err
		}
		//事项完结
		//更新实例状态
		if err := tx.Model(dqualitypointprocinst.Model()).Where("id = ?", procInst.ID).Update("status", 1).Error; err != nil {
			return err
		}
		//更新事项状态
		if err := tx.Model(dqualityviolation.Model()).Where("id = ?", qualityviolation.ID).Update("status", 1).Error; err != nil {
			return err
		}
		//消息通知到责任人
		// go SendMail(procTaskObjects, qualityviolation.ID, false)
		//返回 nil 提交事务
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

// 更新负向事项数据
func UpdateNegativeImpactViolationTransaction(violationID uint, violationObject map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		resource := violationObject["Resource"].(string)
		delete(violationObject, "Resource")
		//更新事项
		if err := tx.Model(dqualityviolation.Model()).Where("id = ?", violationID).Updates(violationObject).Error; err != nil {
			// 返回任何错误都会回滚事务
			return err
		}
		qualityviolation := dqualityviolation.Response{}
		if err := tx.Model(dqualityviolation.Model()).Where("id = ?", violationID).Find(&qualityviolation).Error; err != nil {
			return err
		}
		if qualityviolation.ID == 0 {
			return errors.New("更新事项失败")
		}
		//更新事项实例
		procInstObject := map[string]interface{}{
			"Title":               qualityviolation.AssessmentItemCode + ":" + qualityviolation.AssessmentItemName + ":" + qualityviolation.Uuid,
			"QualityViolationID":  qualityviolation.ID,
			"ResponsiblePersonID": qualityviolation.ResponsiblePersonID,
			"AuditManagerID":      qualityviolation.AuditManagerID,
			"UpdatedAt":           time.Now(),
			"Resource":            resource,
		}
		if err := tx.Model(dqualitypointprocinst.Model()).Where("quality_violation_id = ?", violationID).Updates(procInstObject).Error; err != nil {
			// 返回任何错误都会回滚事务
			return err
		}
		procInst := dqualitypointprocinst.Response{}
		if err := tx.Model(dqualitypointprocinst.Model()).Where("quality_violation_id = ?", qualityviolation.ID).Find(&procInst).Error; err != nil {
			return err
		}
		if procInst.ID == 0 {
			return errors.New("更新违规事项实例失败")
		}

		//获取扣减记录
		currentYear := time.Now().Year()
		deductionData := dqualitypointsdeduction.Response{}
		err := tx.Model(dqualitypointsdeduction.Model()).Where("violation_id=? and user_id=? ", qualityviolation.ID, qualityviolation.ResponsiblePersonID).Find(&deductionData).Error
		if err != nil {
			return err
		}

		// 获取用户当前积分信息
		userPointData := dqualityuserpoint.Response{}
		if err := tx.Model(dqualityuserpoint.Model()).Where("user_id = ? AND year = ?", qualityviolation.ResponsiblePersonID, currentYear).Find(&userPointData).Error; err != nil {
			return err
		}
		resetPoints := userPointData.TotalPoints + deductionData.PointsDeducted
		resetDeductionPoints := userPointData.PointsDeducted - deductionData.PointsDeducted
		// 更新用户积分信息，先回滚之前的消分效果
		resetUpdates := map[string]interface{}{
			"total_points":    resetPoints,
			"points_deducted": resetDeductionPoints,
		}

		if err := tx.Model(dqualityuserpoint.Model()).Where("user_id = ? AND year = ?", userPointData.UserID, currentYear).Updates(resetUpdates).Error; err != nil {
			return err
		}

		//积分扣减 qualityviolation.ResponsiblePersonID
		//创建积分扣减记录
		deductionObject := map[string]interface{}{
			"UpdatedAt":      time.Now(),
			"UserID":         qualityviolation.ResponsiblePersonID,
			"PointsDeducted": qualityviolation.ScoreImpact,
			"IssueLevel":     qualityviolation.IssueLevel,
			"ImpactDegree":   qualityviolation.ImpactDegree,
			"Year":           currentYear,
		}

		if err := tx.Model(dqualitypointsdeduction.Model()).Where("id=?", deductionData.ID).Updates(deductionObject).Error; err != nil {
			return err
		}
		//获取责任人积分信息
		userPoint := dqualityuserpoint.Response{}
		if err := tx.Model(dqualityuserpoint.Model()).Where("user_id=? and year=?", qualityviolation.ResponsiblePersonID, currentYear).Find(&userPoint).Error; err != nil {
			return err
		}

		//更新积分信息
		pointObject := map[string]interface{}{
			"updated_at":      time.Now(),
			"points_deducted": userPoint.PointsDeducted + qualityviolation.ScoreImpact,
		}

		pointObject["total_points"] = userPoint.TotalPoints - qualityviolation.ScoreImpact
		if err := tx.Model(dqualityuserpoint.Model()).Where("user_id=? and year=?", qualityviolation.ResponsiblePersonID, currentYear).Updates(pointObject).Error; err != nil {
			return err
		}
		//返回 nil 提交事务
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

// 废弃正项事项
func DiscardPositiveViolationTransaction(violationID uint) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		//更新事项
		violationObject := map[string]interface{}{
			"Status": 4,
		}
		if err := tx.Model(dqualityviolation.Model()).Where("id = ?", violationID).Updates(violationObject).Error; err != nil {
			// 返回任何错误都会回滚事务
			return err
		}
		qualityviolation := dqualityviolation.Response{}
		if err := tx.Model(dqualityviolation.Model()).Where("id = ?", violationID).Find(&qualityviolation).Error; err != nil {
			return err
		}
		if qualityviolation.ID == 0 {
			return errors.New("更新事项失败")
		}

		//更新事项实例数据
		procInstObject := map[string]interface{}{
			"Status":    4,
			"UpdatedAt": time.Now(),
		}
		if err := tx.Model(dqualitypointprocinst.Model()).Where("quality_violation_id = ?", violationID).Updates(procInstObject).Error; err != nil {
			// 返回任何错误都会回滚事务
			return err
		}
		procInst := dqualitypointprocinst.Response{}
		if err := tx.Model(dqualitypointprocinst.Model()).Where("quality_violation_id = ?", qualityviolation.ID).Find(&procInst).Error; err != nil {
			return err
		}
		if procInst.ID == 0 {
			return errors.New("更新正向事项实例失败")
		}

		//获本次取消分卡信息
		card := dqualitypointsrecoverie.Response{}
		err := tx.Model(dqualitypointsrecoverie.Model()).Where("violation_id=? and user_id=? ", qualityviolation.ID, qualityviolation.ResponsiblePersonID).Find(&card).Error
		if err != nil {
			return err
		}

		// 先“重置”用户积分，恢复到原始状态，去除这张消分卡的影响
		//判断消分卡是否被使用,使用的话先重置回去
		if card.UsagePoint > 0 {
			// 获取用户当前积分信息
			userPointData := dqualityuserpoint.Response{}
			currentYear := time.Now().Year()
			if err := tx.Model(dqualityuserpoint.Model()).Where("user_id = ? AND year = ?", qualityviolation.ResponsiblePersonID, currentYear).Find(&userPointData).Error; err != nil {
				return err
			}
			resetPoints := userPointData.TotalPoints - card.UsagePoint
			resetRecoveredPoints := userPointData.PointsRecovered - card.UsagePoint
			// 更新用户积分信息，先回滚之前的消分效果
			resetUpdates := map[string]interface{}{
				"total_points":     resetPoints,
				"points_recovered": resetRecoveredPoints,
			}
			if err := tx.Model(dqualityuserpoint.Model()).Where("user_id = ? AND year = ?", card.UserID, currentYear).Updates(resetUpdates).Error; err != nil {
				return err
			}
		}
		//删除消分卡
		if err := tx.Unscoped().Delete(dqualitypointsrecoverie.Model(), card.ID).Error; err != nil {
			return err
		}
		//事项完结
		//更新实例状态
		if err := tx.Model(dqualitypointprocinst.Model()).Where("id = ?", procInst.ID).Update("status", 4).Error; err != nil {
			return err
		}
		//更新事项状态
		if err := tx.Model(dqualityviolation.Model()).Where("id = ?", qualityviolation.ID).Update("status", 4).Error; err != nil {
			return err
		}
		//消息通知到责任人
		// go SendMail(procTaskObjects, qualityviolation.ID, false)
		//返回 nil 提交事务
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

// 废弃负向事项数据
func DiscardNegativeImpactViolationTransaction(violationID uint) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		//更新事项
		violationObject := map[string]interface{}{
			"Status": 4,
		}
		if err := tx.Model(dqualityviolation.Model()).Where("id = ?", violationID).Updates(violationObject).Error; err != nil {
			// 返回任何错误都会回滚事务
			return err
		}
		qualityviolation := dqualityviolation.Response{}
		if err := tx.Model(dqualityviolation.Model()).Where("id = ?", violationID).Find(&qualityviolation).Error; err != nil {
			return err
		}
		if qualityviolation.ID == 0 {
			return errors.New("更新事项失败")
		}

		//更新事项实例
		procInstObject := map[string]interface{}{
			"Status":    4,
			"UpdatedAt": time.Now(),
		}
		if err := tx.Model(dqualitypointprocinst.Model()).Where("quality_violation_id = ?", violationID).Updates(procInstObject).Error; err != nil {
			// 返回任何错误都会回滚事务
			return err
		}
		procInst := dqualitypointprocinst.Response{}
		if err := tx.Model(dqualitypointprocinst.Model()).Where("quality_violation_id = ?", qualityviolation.ID).Find(&procInst).Error; err != nil {
			return err
		}
		if procInst.ID == 0 {
			return errors.New("更新违规事项实例失败")
		}

		//获取扣减记录
		currentYear := time.Now().Year()
		deductionData := dqualitypointsdeduction.Response{}
		err := tx.Model(dqualitypointsdeduction.Model()).Where("violation_id=? and user_id=? ", qualityviolation.ID, qualityviolation.ResponsiblePersonID).Find(&deductionData).Error
		if err != nil {
			return err
		}

		// 获取用户当前积分信息
		userPointData := dqualityuserpoint.Response{}
		if err := tx.Model(dqualityuserpoint.Model()).Where("user_id = ? AND year = ?", qualityviolation.ResponsiblePersonID, currentYear).Find(&userPointData).Error; err != nil {
			return err
		}
		resetPoints := userPointData.TotalPoints + deductionData.PointsDeducted
		resetDeductionPoints := userPointData.PointsDeducted - deductionData.PointsDeducted
		// 更新用户积分信息，先回滚之前的消分效果
		resetUpdates := map[string]interface{}{
			"total_points":    resetPoints,
			"points_deducted": resetDeductionPoints,
		}

		if err := tx.Model(dqualityuserpoint.Model()).Where("user_id = ? AND year = ?", userPointData.UserID, currentYear).Updates(resetUpdates).Error; err != nil {
			return err
		}

		//删除积分扣减记录
		if err := tx.Unscoped().Delete(dqualitypointsdeduction.Model(), deductionData.ID).Error; err != nil {
			return err
		}
		//返回 nil 提交事务
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

// 消分操作
func ProcessPointDeductionTransaction(violationID uint, deductionPoint float32) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		qualityviolation := dqualityviolation.Response{}
		if err := tx.Model(dqualityviolation.Model()).Where("id = ?", violationID).Find(&qualityviolation).Error; err != nil {
			return err
		}
		if qualityviolation.ID == 0 {
			return errors.New("事项数据获取失败")
		}
		//创建消分卡
		currentYear := time.Now().Year()
		nextYear := currentYear + 1
		effectiveDate := time.Date(nextYear, 1, 1, 0, 0, 0, 0, time.Now().Location())
		cardObject := map[string]interface{}{
			"CreatedAt":       time.Now(),
			"UpdatedAt":       time.Now(),
			"ViolationID":     qualityviolation.ID,
			"UserID":          qualityviolation.ResponsiblePersonID,
			"PointsRecovered": deductionPoint,
			"Year":            currentYear,
			"EffectiveDate":   effectiveDate,
			"UsageState":      0,
		}
		if libs.Config.QualityPoint.TrialRun { //试用期期间,消分可抵消当年被扣积分
			cardObject["EffectiveDate"] = time.Now()
		}
		//获取责任人积分信息
		userPoint := dqualityuserpoint.Response{}
		if err := tx.Model(dqualityuserpoint.Model()).Where("user_id=? and year=?", qualityviolation.ResponsiblePersonID, currentYear).Find(&userPoint).Error; err != nil {
			return err
		}
		//试用期期间，针对影响度低的问题，消分可抵消当年被扣积分，如果剩余积分为12分，则不进行操作，若积分小于12分，根据规则判断是否可以进行加分，并记录
		if libs.Config.QualityPoint.TrialRun && userPoint.TotalPoints != 12 && userPoint.PointsDeducted > 0 {
			//查找扣减积分数据详情---影响度低的分数扣减记录
			pointsdeductionList := []*dqualitypointsdeduction.ListResponse{}
			err := tx.Model(dqualitypointsdeduction.Model()).Where("user_id=? and year=? and impact_degree<>3", qualityviolation.ResponsiblePersonID, currentYear).Find(&pointsdeductionList).Error
			if err != nil {
				return err
			}
			//遍历统计扣减积分数据总数
			var totalPointsDeducted float32 //为正数
			for _, pointsdeduction := range pointsdeductionList {
				totalPointsDeducted += pointsdeduction.PointsDeducted
			}
			//查询已消分数据
			pointsrecoverieList := []*dqualitypointsrecoverie.ListResponse{}
			err = tx.Model(dqualitypointsrecoverie.Model()).Where("user_id=? and year=?", qualityviolation.ResponsiblePersonID, currentYear).Find(&pointsrecoverieList).Error
			if err != nil {
				return err
			}
			var totalPointsRecovered float32
			for _, pointsrecoverie := range pointsrecoverieList {
				totalPointsRecovered += pointsrecoverie.UsagePoint
			}
			//可增加积分数
			addablePoint := totalPointsDeducted - totalPointsRecovered
			if addablePoint > 0 {
				pointObject := map[string]interface{}{}
				//可抵消积分和扣减积分对比
				if addablePoint >= deductionPoint {
					cardObject["UsageState"] = 2 //已使用
					cardObject["UsagePoint"] = deductionPoint
					//更新用户积分总数
					pointObject["total_points"] = userPoint.TotalPoints + deductionPoint
					pointObject["points_recovered"] = userPoint.PointsRecovered + deductionPoint
					if userPoint.TotalPoints+deductionPoint > 12 {
						pointObject["total_points"] = 12
						pointObject["points_recovered"] = userPoint.PointsRecovered + (deductionPoint - (userPoint.TotalPoints + deductionPoint - 12))
					}
				} else {
					pointObject["total_points"] = userPoint.TotalPoints + addablePoint
					cardObject["UsageState"] = 1 //使用中
					cardObject["UsagePoint"] = addablePoint
					pointObject["points_recovered"] = userPoint.PointsRecovered + addablePoint
					if userPoint.TotalPoints+addablePoint > 12 {
						pointObject["total_points"] = 12
					}
				}
				err = tx.Model(dqualityuserpoint.Model()).Where("user_id=? and year=?", qualityviolation.ResponsiblePersonID, currentYear).Updates(pointObject).Error
				if err != nil {
					return err
				}
			}

		}
		if err := tx.Model(dqualitypointsrecoverie.Model()).Create(cardObject).Error; err != nil {
			return err
		}
		//消息通知到责任人
		// go SendMail(procTaskObjects, qualityviolation.ID, false)
		//返回 nil 提交事务
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

// 扣减双倍积分
func DeductDoublePointsTransaction(violationID uint) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		qualityviolation := dqualityviolation.Response{}
		if err := tx.Model(dqualityviolation.Model()).Where("id = ?", violationID).Find(&qualityviolation).Error; err != nil {
			return err
		}
		if qualityviolation.ID == 0 {
			return errors.New("更新事项失败")
		}

		//获取扣减记录
		currentYear := time.Now().Year()
		deductionData := dqualitypointsdeduction.Response{}
		err := tx.Model(dqualitypointsdeduction.Model()).Where("violation_id=? and user_id=? ", qualityviolation.ID, qualityviolation.ResponsiblePersonID).Find(&deductionData).Error
		if err != nil {
			return err
		}

		// 获取用户当前积分信息
		userPointData := dqualityuserpoint.Response{}
		if err := tx.Model(dqualityuserpoint.Model()).Where("user_id = ? AND year = ?", qualityviolation.ResponsiblePersonID, currentYear).Find(&userPointData).Error; err != nil {
			return err
		}
		resetPoints := userPointData.TotalPoints + deductionData.PointsDeducted
		resetDeductionPoints := userPointData.PointsDeducted - deductionData.PointsDeducted
		// 更新用户积分信息，先回滚之前的消分效果
		resetUpdates := map[string]interface{}{
			"total_points":    resetPoints,
			"points_deducted": resetDeductionPoints,
		}

		if err := tx.Model(dqualityuserpoint.Model()).Where("user_id = ? AND year = ?", userPointData.UserID, currentYear).Updates(resetUpdates).Error; err != nil {
			return err
		}

		//积分扣减 qualityviolation.ResponsiblePersonID
		//创建积分扣减记录
		deductionObject := map[string]interface{}{
			"UpdatedAt":      time.Now(),
			"UserID":         qualityviolation.ResponsiblePersonID,
			"PointsDeducted": qualityviolation.ScoreImpact * 2,
			"IssueLevel":     qualityviolation.IssueLevel,
			"ImpactDegree":   qualityviolation.ImpactDegree,
			"Year":           currentYear,
		}

		if err := tx.Model(dqualitypointsdeduction.Model()).Where("id=?", deductionData.ID).Updates(deductionObject).Error; err != nil {
			return err
		}
		//获取责任人积分信息
		userPoint := dqualityuserpoint.Response{}
		if err := tx.Model(dqualityuserpoint.Model()).Where("user_id=? and year=?", qualityviolation.ResponsiblePersonID, currentYear).Find(&userPoint).Error; err != nil {
			return err
		}

		//更新积分信息
		pointObject := map[string]interface{}{
			"updated_at":      time.Now(),
			"points_deducted": userPoint.PointsDeducted + qualityviolation.ScoreImpact*2,
		}

		pointObject["total_points"] = userPoint.TotalPoints - qualityviolation.ScoreImpact*2
		if err := tx.Model(dqualityuserpoint.Model()).Where("user_id=? and year=?", qualityviolation.ResponsiblePersonID, currentYear).Updates(pointObject).Error; err != nil {
			return err
		}
		//返回 nil 提交事务
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func UpdateViolationTransaction(userID, procInstID, taskID uint, featureObject map[string]interface{}, taskObject map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 查检当前处理节点是否重复处理
		task := dqualitypointproctask.Response{}
		if err := tx.Model(dqualitypointproctask.Model()).Where("id = ? and status = 0 and flag = true", taskID).Find(&task).Error; err != nil {
			return err
		}

		if task.ID == 0 {
			return errors.New("已处理任务，无法重复处理")
		}
		if task.Assignee != userID {
			return errors.New("不是当前用户的任务")
		}
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		procInst := dqualitypointprocinst.Response{}
		if err := tx.Model(dqualitypointprocinst.Model()).Where("id = ?", procInstID).Find(&procInst).Error; err != nil {
			return err
		}
		if procInst.ID == 0 {
			return errors.New("未找到事项发布实例")
		}

		violation := dqualityviolation.Response{}
		if err := tx.Model(dqualityviolation.Model()).Where("id = ?", procInst.QualityViolationID).Find(&violation).Error; err != nil {
			return err
		}
		if violation.ID == 0 {
			return errors.New("未找到事项数据")
		}

		resource := procInst.Resource
		nodes, _ := dqualitypointprocdef.GetNodes(resource)
		procTaskObjects := []map[string]interface{}{}
		levelNodeIDs := []string{}
		prevTasks := []*dqualitypointproctask.Response{}
		levelTasks := []*dqualitypointproctask.Response{}
		nextTasks := []*dqualitypointproctask.Response{}

		prevNodeIDs, _ := dqualitypointprocdef.GetPrevNodeIDs(resource, taskObject["NodeID"].(string))
		nextNodeIDs, _ := dqualitypointprocdef.GetNextNodeIDs(resource, taskObject["NodeID"].(string))

		//通过nextNodeIDs的前置节点寻找同级任务节点
		for _, nextNodeID := range nextNodeIDs {
			nextNodePrevNodeIDs, _ := dqualitypointprocdef.GetPrevNodeIDs(resource, nextNodeID)
			for _, nextNodePrevNodeID := range nextNodePrevNodeIDs {
				// 同级任务去掉当前任务节点id
				if nextNodePrevNodeID != task.NodeID {
					levelNodeIDs = append(levelNodeIDs, nextNodePrevNodeID)
				}
			}
		}
		// fmt.Println(taskObject["NodeID"].(string), task.NodeID, prevNodeIDs, nextNodeIDs, _levelNodeIDs, levelNodeIDs)
		switch taskObject["Status"].(uint) {
		case 1:
			//检查是否为驳回订单，如果是驳回订单，将后续节点flag 置为false
			taskObject["Done"] = true
			// 检查前置任务是否都通过
			if len(prevNodeIDs) > 0 {
				if err := tx.Model(dqualitypointproctask.Model()).Where("node_id in ? and proc_inst_id = ? and status = 1 and flag = true", prevNodeIDs, procInst.ID).Find(&prevTasks).Error; err != nil {
					return err
				}
				if len(prevTasks) < len(prevNodeIDs) {
					return errors.New("前置节点未全部通过")
				}
			}

			// 检查同级任务是否都通过
			if err := tx.Model(dqualitypointproctask.Model()).Where("node_id in ? and proc_inst_id = ? and status = 1 and flag = true", levelNodeIDs, procInst.ID).Find(&levelTasks).Error; err != nil {
				return err
			}
			// fmt.Println(levelTasks, levelNodeIDs, len(levelNodeIDs), len(levelTasks))

			if len(levelNodeIDs) <= len(levelTasks) {
				if len(nextNodeIDs) > 0 {
					if err := tx.Model(dqualitypointproctask.Model()).Where("node_id in ? and proc_inst_id = ? and status = 0 and flag = true", nextNodeIDs, procInst.ID).Find(&nextTasks).Error; err != nil {
						return err
					}
				out:
					for _, nextNodeID := range nextNodeIDs {
						for _, nextTask := range nextTasks {
							if nextTask.NodeID == nextNodeID {
								continue out
							}
						}
						// 获取
						nextNodes, _ := dqualitypointprocdef.GetNodesByNodeID(procInst.Resource, nextNodeID)
						for _, node := range nextNodes {
							procTaskObjects = append(procTaskObjects, map[string]interface{}{
								"CreatedAt":  time.Now(),
								"NodeName":   node.Name,
								"NodeID":     node.NodeID,
								"PrevNodeID": node.PrevNodeID,
								"ProcInstID": procInst.ID,
								"Assignee":   node.Assignee,
								"Status":     0,
								"Flag":       true,
							})
						}
					}

				} else {
					featureObject["Status"] = 1
				}
			}
		case 2:
			taskObject["Flag"] = false
			prevTask := dqualitypointproctask.Response{}
			if err := tx.Model(dqualitypointproctask.Model()).Last(&prevTask, "node_id = ? and proc_inst_id = ? and status != 0 and flag = true", taskObject["NextNodeID"].(string), procInst.ID).Error; err != nil {
				return err
			}
			// 处理回退节点之后的未处理任务
			handleIDs := []string{}
			handleNodes, _ := dqualitypointprocdef.GetAfterNodes(nodes, taskObject["NextNodeID"].(string))
			for _, node := range handleNodes {
				handleIDs = append(handleIDs, node.NodeID)
			}
			handleIDs = append(handleIDs, taskObject["NodeID"].(string))
			handleIDs = append(handleIDs, taskObject["NextNodeID"].(string))
			handleIDs = UniqueStrings(handleIDs)
			if err := tx.Delete(dqualitypointproctask.Model(), "node_id = ? and proc_inst_id = ? and status = 1 and flag = true and id != ?", prevTask.NodeID, procInst.ID, taskID).Error; err != nil {
				return err
			}
			// fmt.Println(handleIDs)
			if err := tx.Model(dqualitypointproctask.Model()).Where("node_id in ? and proc_inst_id = ? and status != 0 and flag = true", handleIDs, procInst.ID).UpdateColumns(map[string]interface{}{"status": 0, "flag": false}).Error; err != nil {
				return err
			}

			//生成新的任务
			procTaskObjects = append(procTaskObjects, map[string]interface{}{
				"CreatedAt":  time.Now(),
				"NodeName":   prevTask.NodeName,
				"NodeID":     prevTask.NodeID,
				"PrevNodeID": prevTask.PrevNodeID,
				"ProcInstID": procInst.ID,
				"Assignee":   prevTask.Assignee,
				"Status":     0,
				"Flag":       true,
			})

		case 3:
			var users []*duser.ApprovalResponse
			if err := tx.Model(duser.Model()).Where("id in ?", []uint{userID, taskObject["UserID"].(uint)}).Find(&users).Error; err != nil {
				return err
			}
			var userMap = make(map[uint]*duser.ApprovalResponse)
			for _, user := range users {
				userMap[user.Id] = user
			}
			procTaskObjects = append(procTaskObjects, map[string]interface{}{
				"CreatedAt":  time.Now(),
				"NodeName":   task.NodeName,
				"NodeID":     task.NodeID,
				"PrevNodeID": task.PrevNodeID,
				"ProcInstID": procInst.ID,
				"Assignee":   taskObject["UserID"].(uint),
				"Status":     0,
				"Flag":       true,
			})
			if len(taskObject["Comment"].(string)) > 0 {
				taskObject["Comment"] = fmt.Sprintf("%s \n %s -> %s", taskObject["Comment"], userMap[userID].Name, userMap[taskObject["UserID"].(uint)].Name)
			} else {
				taskObject["Comment"] = fmt.Sprintf("%s -> %s", userMap[userID].Name, userMap[taskObject["UserID"].(uint)].Name)
			}
			//更新节点信息
			dqualitypointprocdef.UpdateAssignee(nodes, task.NodeID, taskObject["UserID"].(uint))
			nodeResource, err := dqualitypointprocdef.NodesToJSON(nodes)
			if err != nil {
				return err
			}
			// 更新流程实例的resource值
			if err := tx.Model(dqualitypointprocinst.Model()).Where("id = ?", procInst.ID).Update("resource", nodeResource).Error; err != nil {
				return err
			}

		case 4:
			featureObject["Status"] = 4
		}

		delete(taskObject, "NextNodeID")
		delete(taskObject, "UserID")

		if len(procTaskObjects) > 0 {

			//判断新建节点类型
			// fmt.Print(procTaskObjects)
			// todoTask := procTaskObjects
			switch taskObject["Status"].(uint) {
			case 1:
				//提取回滚节点
				rollBackIDs := []string{}
				for _, task := range procTaskObjects {
					rollBackIDs = append(rollBackIDs, task["NodeID"].(string))
				}
				//查找是否存在回滚节点
				rollBackTasks := []*dqualitypointproctask.Response{}
				if err := tx.Model(dqualitypointproctask.Model()).Where("node_id in ?  and proc_inst_id = ? and status = 0 and flag = 0 ", rollBackIDs, procInst.ID).Find(&rollBackTasks).Error; err != nil {
					return err
				}
				//存在的情况下,重置相关任务信息，flag置为true
				if len(rollBackTasks) > 0 {
					if err := tx.Debug().Model(dqualitypointproctask.Model()).Where("node_id in ? and proc_inst_id = ? and status = 0 and flag = 0", rollBackIDs, procInst.ID).
						UpdateColumns(map[string]interface{}{"status": 0, "flag": true}).Error; err != nil {
						return err
					}
					// 移除回滚节点
					for _, task := range rollBackTasks {
						procTaskObjects = RemoveNodeID(procTaskObjects, task.NodeID)
					}
				}
			case 3:
				//提取节点数据
				rollBackIDs := []string{}
				for _, task := range procTaskObjects {
					rollBackIDs = append(rollBackIDs, task["NodeID"].(string))
				}
			}
			if len(procTaskObjects) > 0 {
				if err := tx.Model(dqualitypointproctask.Model()).Create(procTaskObjects).Error; err != nil {
					return err
				}
			}

			// go SendMail(todoTask, production.ID, production.Urgency)
		}
		fmt.Println(taskID, taskObject)
		if err := tx.Model(dqualitypointproctask.Model()).Where("id = ?", taskID).Updates(taskObject).Error; err != nil {
			return err
		}
		// 将相同nodeid的任务置为完成
		_taskObject := map[string]interface{}{
			"NodeID":    taskObject["NodeID"],
			"UpdatedAt": time.Now(),
			"Status":    taskObject["Status"],
			"Comment":   fmt.Sprintf("无需处理"),
		}
		if err := tx.Model(dqualitypointproctask.Model()).Where(" proc_inst_id = ? and id != ? and node_id = ? and node_name != ?", procInstID, taskID, taskObject["NodeID"].(string), task.NodeName).Updates(_taskObject).Error; err != nil {
			return err
		}

		if err := tx.Model(dqualityviolation.Model()).Where("id = ?", procInst.QualityViolationID).UpdateColumns(featureObject).Error; err != nil {
			return err
		}
		// 去除多余基础数据
		delete(featureObject, "IssueDescription")
		delete(featureObject, "IsCommonIssue")
		delete(featureObject, "CommonReviewComments")
		delete(featureObject, "ReoccurrenceType")
		delete(featureObject, "DeductTaskDescription")
		delete(featureObject, "DeductCompletionTime")
		delete(featureObject, "DeductEvidenceDescription")
		delete(featureObject, "DeductEvidenceMaterial")
		delete(featureObject, "DeductionPoints")
		delete(featureObject, "IsRecurrent")
		if err := tx.Model(dqualitypointprocinst.Model()).Where("id = ?", procInst.ID).UpdateColumns(featureObject).Error; err != nil {
			return err
		}
		if featureObject["Status"] == 1 {
			allTasks := []*dqualitypointproctask.Response{}
			if err := tx.Model(dqualitypointproctask.Model()).Where("proc_inst_id = ? and status = 1 and flag = true", procInst.ID).Find(&allTasks).Error; err != nil {
				return err
			}
			// go SendMailSuccess(allTasks, production.ID, production.Urgency)
		}
		// 返回 nil 提交事务
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func SendMail(procTaskObjects []map[string]interface{}, featureID uint, urgency bool) {
	for _, procTaskObject := range procTaskObjects {
		if procTaskObject["Status"] == 0 {
			subject := fmt.Sprintf("[质量积分管理系统][下生产ID:%d][%s][待处理]", featureID, procTaskObject["NodeName"].(string))

			if urgency {
				subject = fmt.Sprintf("[紧急][质量积分管理系统][下生产ID:%d][%s][待处理]", featureID, procTaskObject["NodeName"].(string))
			}

			body := fmt.Sprintf(`%s<br><p>质量积分管理系统链接: <a href="http://10.51.134.126/productionSYS/#/prodSys/todo">http://10.51.134.126/productionSYS/#/prodSys/todo</a><p>`, subject)
			if !libs.Config.Debug {
				err := libs.SendMail([]string{fmt.Sprintf("%<EMAIL>", duser.UserMap[procTaskObject["Assignee"].(uint)].Username)}, subject, body, []string{})
				if err != nil {
					logging.ErrorLogger.Error(err)
				}
			}
			logging.DebugLogger.Debugf("send mail", []string{fmt.Sprintf("%<EMAIL>", duser.UserMap[procTaskObject["Assignee"].(uint)].Username)}, subject, body)
		}
	}
}

func SendMailSuccess(taskObjects []*dproductionproctask.Response, featureID uint, urgency bool) {
	mailTo := []string{"<EMAIL>", "<EMAIL>"}
	for _, taskObject := range taskObjects {
		if !libs.InArrayS(mailTo, fmt.Sprintf("%<EMAIL>", duser.UserMap[taskObject.Assignee].Username)) {
			mailTo = append(mailTo, fmt.Sprintf("%<EMAIL>", duser.UserMap[taskObject.Assignee].Username))
		}
	}
	subject := fmt.Sprintf("[质量积分管理系统][事项ID:%d][已完结成功]", featureID)
	if urgency {
		subject = fmt.Sprintf("[紧急][质量积分管理系统][事项ID:%d][已完结]", featureID)
	}
	body := fmt.Sprintf(`%s<br><p>质量积分管理系统: <a href="http://10.51.134.126/productionSYS/#/prodSys/success">http://10.51.134.126/productionSYS/#/prodSys/success</a><p>`, subject)
	if !libs.Config.Debug {
		err := libs.SendMail(mailTo, subject, body, []string{})
		// err := libs.SendMail([]string{fmt.Sprintf("%<EMAIL>", "linjiakai")}, subject, body)
		if err != nil {
			logging.ErrorLogger.Error(err)
		}
	}
	logging.DebugLogger.Debugf("send mail", mailTo, subject, body)

}

func UniqueStrings(input []string) []string {
	uniqueMap := make(map[string]bool)
	var uniqueSlice []string

	for _, value := range input {
		if _, exists := uniqueMap[value]; !exists {
			uniqueMap[value] = true
			uniqueSlice = append(uniqueSlice, value)
		}
	}

	return uniqueSlice
}

func RemoveNodeID(procTaskObjects []map[string]interface{}, nodeIDToRemove string) []map[string]interface{} {
	filteredProcTaskObjects := make([]map[string]interface{}, 0)
	for _, taskObject := range procTaskObjects {
		if nodeID, ok := taskObject["NodeID"].(string); !ok || nodeID != nodeIDToRemove {
			filteredProcTaskObjects = append(filteredProcTaskObjects, taskObject)
		}
	}
	return filteredProcTaskObjects
}

func FindByNodeID(procTaskObjects []map[string]interface{}, nodeID string) map[string]interface{} {
	for _, taskObject := range procTaskObjects {
		if taskObject["NodeID"] == nodeID {
			return taskObject
		}
	}

	return nil
}
