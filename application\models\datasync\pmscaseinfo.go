package datasync

import "time"

type PmsCaseInfo struct {
	// models.ModelBase
	ID                   int    `gorm:"not null; index:idx_unique, unique" json:"id" `
	CaseID               int    `gorm:"not null; index:idx_unique, unique" json:"case_id" `
	Version              int    `gorm:"not null" json:"version" update:"1"`
	CaseLevelID          int    `gorm:"not null" json:"case_level_id" update:"1"`
	CaseName             string `gorm:"not null; type:varchar(200)" json:"case_name" update:"1"`
	CasePacketID         int    `gorm:"not null" json:"case_packet_id" update:"1"`
	CasePacketName       string `gorm:"not null; type:varchar(200)" json:"case_packet_name" update:"1"`
	TestProjectStageName string `gorm:"not null; type:varchar(200)" json:"test_project_stage_name" update:"1"`
	ExecutionResult      string `gorm:"not null; type:varchar(200)" json:"execution_result" update:"1"`
	FailReason           string `gorm:"not null; type:varchar(2000)" json:"fail_reason" update:"1"`

	ProjectID     int        `gorm:"not null" json:"project_id" update:"1"`
	ProjectName   string     `gorm:"not null; type:varchar(200)" json:"project_name" update:"1"`
	ProductName   string     `gorm:"not null; type:varchar(200)" json:"product_name" update:"1"`
	ExecutionTime *time.Time `json:"execution_time" update:"1"`

	AssignUserName string `gorm:"not null; type:varchar(200)" json:"assign_user_name" update:"1"`
}
