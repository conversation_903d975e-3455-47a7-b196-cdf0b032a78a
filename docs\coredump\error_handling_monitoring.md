# Coredump记录自动化处理系统 - 错误处理和监控方案

## 1. 错误处理策略概述

### 1.1 错误分类体系

本系统采用分层错误处理策略，将错误按照严重程度和处理方式进行分类：

| 错误级别 | 描述 | 处理策略 | 示例 |
|---------|------|----------|------|
| **Fatal** | 系统级致命错误 | 停止处理，立即告警 | 数据库连接失败、配置文件损坏 |
| **Error** | 业务逻辑错误 | 记录错误，跳过当前记录 | Bug提交失败、字段映射错误 |
| **Warning** | 警告级别 | 记录警告，使用默认值继续 | 负责人信息缺失、非关键字段为空 |
| **Info** | 信息级别 | 仅记录日志 | 处理进度、状态变更 |

### 1.2 错误处理原则

- **快速失败**: 对于致命错误，立即停止处理并告警
- **优雅降级**: 对于非致命错误，使用默认值或跳过处理
- **详细记录**: 记录完整的错误上下文信息
- **自动恢复**: 支持重试机制和自动恢复
- **用户友好**: 提供清晰的错误信息和解决建议

## 2. 错误类型定义

### 2.1 错误结构体设计

```go
// CoredumpError 统一错误结构
type CoredumpError struct {
    Type        ErrorType   `json:"type"`         // 错误类型
    Level       ErrorLevel  `json:"level"`        // 错误级别
    Code        string      `json:"code"`         // 错误代码
    Message     string      `json:"message"`      // 错误消息
    Details     string      `json:"details"`      // 详细信息
    RecordID    string      `json:"record_id"`    // 相关记录ID
    SN          string      `json:"sn"`           // 相关SN
    Timestamp   time.Time   `json:"timestamp"`    // 发生时间
    StackTrace  string      `json:"stack_trace"`  // 堆栈跟踪
    Context     ErrorContext `json:"context"`     // 错误上下文
    Retryable   bool        `json:"retryable"`    // 是否可重试
    RetryCount  int         `json:"retry_count"`  // 重试次数
}

// ErrorType 错误类型枚举
type ErrorType string

const (
    ErrorTypeFeishuAPI    ErrorType = "feishu_api"      // 飞书API错误
    ErrorTypeFieldMapping ErrorType = "field_mapping"   // 字段映射错误
    ErrorTypeBugSubmit    ErrorType = "bug_submit"      // Bug提交错误
    ErrorTypeDatabase     ErrorType = "database"        // 数据库错误
    ErrorTypeConfig       ErrorType = "config"          // 配置错误
    ErrorTypeValidation   ErrorType = "validation"      // 数据验证错误
    ErrorTypeNetwork      ErrorType = "network"         // 网络错误
    ErrorTypeTimeout      ErrorType = "timeout"         // 超时错误
    ErrorTypePermission   ErrorType = "permission"      // 权限错误
    ErrorTypeSystem       ErrorType = "system"          // 系统错误
)

// ErrorLevel 错误级别枚举
type ErrorLevel string

const (
    ErrorLevelFatal   ErrorLevel = "fatal"   // 致命错误
    ErrorLevelError   ErrorLevel = "error"   // 错误
    ErrorLevelWarning ErrorLevel = "warning" // 警告
    ErrorLevelInfo    ErrorLevel = "info"    // 信息
)

// ErrorContext 错误上下文
type ErrorContext struct {
    Operation   string                 `json:"operation"`   // 操作名称
    Parameters  map[string]interface{} `json:"parameters"`  // 操作参数
    UserID      string                 `json:"user_id"`     // 用户ID
    RequestID   string                 `json:"request_id"`  // 请求ID
    Environment string                 `json:"environment"` // 环境信息
}
```

### 2.2 预定义错误代码

```go
// 错误代码常量定义
const (
    // 飞书API相关错误 (F001-F099)
    ErrCodeFeishuAPICallFailed     = "F001" // 飞书API调用失败
    ErrCodeFeishuAuthFailed        = "F002" // 飞书认证失败
    ErrCodeFeishuRateLimited       = "F003" // 飞书API限流
    ErrCodeFeishuDataParseError    = "F004" // 飞书数据解析错误
    ErrCodeFeishuPermissionDenied  = "F005" // 飞书权限不足
    
    // 字段映射相关错误 (M001-M099)
    ErrCodeFieldMappingFailed      = "M001" // 字段映射失败
    ErrCodeRequiredFieldMissing    = "M002" // 必填字段缺失
    ErrCodeInvalidFieldFormat      = "M003" // 字段格式无效
    ErrCodePersonFieldParseError   = "M004" // 人员字段解析错误
    ErrCodeTimeFieldParseError     = "M005" // 时间字段解析错误
    
    // Bug提交相关错误 (B001-B099)
    ErrCodeBugSubmitFailed         = "B001" // Bug提交失败
    ErrCodeBugValidationFailed     = "B002" // Bug数据验证失败
    ErrCodeBugSystemUnavailable    = "B003" // Bug系统不可用
    ErrCodeBugDuplicateSubmission  = "B004" // 重复提交Bug
    
    // 数据库相关错误 (D001-D099)
    ErrCodeDatabaseConnectionFailed = "D001" // 数据库连接失败
    ErrCodeDatabaseQueryFailed      = "D002" // 数据库查询失败
    ErrCodeDatabaseUpdateFailed     = "D003" // 数据库更新失败
    ErrCodeDatabaseTransactionFailed = "D004" // 数据库事务失败
    
    // 配置相关错误 (C001-C099)
    ErrCodeConfigLoadFailed        = "C001" // 配置加载失败
    ErrCodeConfigValidationFailed  = "C002" // 配置验证失败
    ErrCodeConfigNotFound          = "C003" // 配置文件不存在
    
    // 系统相关错误 (S001-S099)
    ErrCodeSystemTimeout           = "S001" // 系统超时
    ErrCodeSystemResourceExhausted = "S002" // 系统资源耗尽
    ErrCodeSystemPermissionDenied  = "S003" // 系统权限不足
)
```

## 3. 错误处理器实现

### 3.1 统一错误处理器

```go
// ErrorHandler 统一错误处理器
type ErrorHandler struct {
    logger       *logging.Logger
    alertManager *AlertManager
    metrics      *ErrorMetrics
    config       *ErrorHandlingConfig
}

// ErrorHandlingConfig 错误处理配置
type ErrorHandlingConfig struct {
    MaxRetryCount       int           `yaml:"max_retry_count"`       // 最大重试次数
    RetryDelay          time.Duration `yaml:"retry_delay"`           // 重试延迟
    BackoffFactor       float64       `yaml:"backoff_factor"`        // 退避因子
    EnableAlert         bool          `yaml:"enable_alert"`          // 是否启用告警
    AlertThreshold      int           `yaml:"alert_threshold"`       // 告警阈值
    ErrorLogLevel       string        `yaml:"error_log_level"`       // 错误日志级别
    EnableStackTrace    bool          `yaml:"enable_stack_trace"`    // 是否记录堆栈
    ErrorReportingURL   string        `yaml:"error_reporting_url"`   // 错误上报URL
}

// NewErrorHandler 创建错误处理器
func NewErrorHandler(config *ErrorHandlingConfig) *ErrorHandler {
    return &ErrorHandler{
        logger:       logging.GetLogger("coredump_error"),
        alertManager: NewAlertManager(),
        metrics:      NewErrorMetrics(),
        config:       config,
    }
}

// HandleError 处理错误
func (h *ErrorHandler) HandleError(err *CoredumpError) ErrorHandlingResult {
    // 记录错误指标
    h.metrics.RecordError(err)
    
    // 记录错误日志
    h.logError(err)
    
    // 根据错误级别决定处理策略
    switch err.Level {
    case ErrorLevelFatal:
        return h.handleFatalError(err)
    case ErrorLevelError:
        return h.handleError(err)
    case ErrorLevelWarning:
        return h.handleWarning(err)
    case ErrorLevelInfo:
        return h.handleInfo(err)
    default:
        return ErrorHandlingResult{Action: ActionContinue}
    }
}

// ErrorHandlingResult 错误处理结果
type ErrorHandlingResult struct {
    Action      ErrorAction `json:"action"`       // 处理动作
    ShouldRetry bool        `json:"should_retry"` // 是否应该重试
    RetryDelay  time.Duration `json:"retry_delay"` // 重试延迟
    Message     string      `json:"message"`      // 处理消息
}

// ErrorAction 错误处理动作
type ErrorAction string

const (
    ActionStop     ErrorAction = "stop"     // 停止处理
    ActionSkip     ErrorAction = "skip"     // 跳过当前记录
    ActionRetry    ErrorAction = "retry"    // 重试操作
    ActionContinue ErrorAction = "continue" // 继续处理
)

// handleFatalError 处理致命错误
func (h *ErrorHandler) handleFatalError(err *CoredumpError) ErrorHandlingResult {
    // 发送紧急告警
    h.alertManager.SendUrgentAlert(err)
    
    // 记录致命错误
    h.logger.Fatalf("[FATAL] %s: %s", err.Code, err.Message)
    
    return ErrorHandlingResult{
        Action:  ActionStop,
        Message: "系统遇到致命错误，已停止处理",
    }
}

// handleError 处理一般错误
func (h *ErrorHandler) handleError(err *CoredumpError) ErrorHandlingResult {
    // 检查是否可重试
    if err.Retryable && err.RetryCount < h.config.MaxRetryCount {
        delay := h.calculateRetryDelay(err.RetryCount)
        
        h.logger.Errorf("[ERROR] %s: %s (重试 %d/%d)", 
            err.Code, err.Message, err.RetryCount+1, h.config.MaxRetryCount)
        
        return ErrorHandlingResult{
            Action:      ActionRetry,
            ShouldRetry: true,
            RetryDelay:  delay,
            Message:     fmt.Sprintf("错误可重试，将在%v后重试", delay),
        }
    }
    
    // 不可重试或重试次数已达上限
    h.logger.Errorf("[ERROR] %s: %s (跳过处理)", err.Code, err.Message)
    
    // 检查是否需要告警
    if h.shouldSendAlert(err) {
        h.alertManager.SendAlert(err)
    }
    
    return ErrorHandlingResult{
        Action:  ActionSkip,
        Message: "错误无法恢复，跳过当前记录",
    }
}

// calculateRetryDelay 计算重试延迟
func (h *ErrorHandler) calculateRetryDelay(retryCount int) time.Duration {
    delay := h.config.RetryDelay
    for i := 0; i < retryCount; i++ {
        delay = time.Duration(float64(delay) * h.config.BackoffFactor)
    }
    return delay
}

// shouldSendAlert 判断是否应该发送告警
func (h *ErrorHandler) shouldSendAlert(err *CoredumpError) bool {
    if !h.config.EnableAlert {
        return false
    }
    
    // 检查错误频率
    errorCount := h.metrics.GetErrorCount(err.Type, time.Hour)
    return errorCount >= h.config.AlertThreshold
}
```

### 3.2 重试机制实现

```go
// RetryManager 重试管理器
type RetryManager struct {
    config *RetryConfig
}

// RetryConfig 重试配置
type RetryConfig struct {
    MaxRetries    int           `yaml:"max_retries"`
    InitialDelay  time.Duration `yaml:"initial_delay"`
    MaxDelay      time.Duration `yaml:"max_delay"`
    BackoffFactor float64       `yaml:"backoff_factor"`
    Jitter        bool          `yaml:"jitter"` // 是否添加随机抖动
}

// WithRetry 带重试的操作执行
func (r *RetryManager) WithRetry(operation func() error, errorType ErrorType) error {
    var lastErr error
    
    for attempt := 0; attempt <= r.config.MaxRetries; attempt++ {
        if attempt > 0 {
            delay := r.calculateDelay(attempt)
            logging.InfoLogger.Infof("重试操作 %s，第%d次，延迟%v", errorType, attempt, delay)
            time.Sleep(delay)
        }
        
        err := operation()
        if err == nil {
            if attempt > 0 {
                logging.InfoLogger.Infof("重试成功，共重试%d次", attempt)
            }
            return nil
        }
        
        lastErr = err
        
        // 检查是否为不可重试的错误
        if !r.isRetryableError(err, errorType) {
            logging.ErrorLogger.Errorf("不可重试的错误: %v", err)
            break
        }
        
        logging.ErrorLogger.Errorf("操作失败，第%d次尝试: %v", attempt+1, err)
    }
    
    return fmt.Errorf("操作失败，已重试%d次: %w", r.config.MaxRetries, lastErr)
}

// calculateDelay 计算重试延迟
func (r *RetryManager) calculateDelay(attempt int) time.Duration {
    delay := r.config.InitialDelay
    
    // 指数退避
    for i := 1; i < attempt; i++ {
        delay = time.Duration(float64(delay) * r.config.BackoffFactor)
        if delay > r.config.MaxDelay {
            delay = r.config.MaxDelay
            break
        }
    }
    
    // 添加随机抖动
    if r.config.Jitter {
        jitter := time.Duration(rand.Float64() * float64(delay) * 0.1)
        delay += jitter
    }
    
    return delay
}

// isRetryableError 判断错误是否可重试
func (r *RetryManager) isRetryableError(err error, errorType ErrorType) bool {
    switch errorType {
    case ErrorTypeFeishuAPI:
        // 飞书API限流、网络错误等可重试
        return strings.Contains(err.Error(), "rate limit") ||
               strings.Contains(err.Error(), "timeout") ||
               strings.Contains(err.Error(), "network")
    case ErrorTypeBugSubmit:
        // Bug系统临时不可用可重试
        return strings.Contains(err.Error(), "unavailable") ||
               strings.Contains(err.Error(), "timeout")
    case ErrorTypeDatabase:
        // 数据库连接错误可重试
        return strings.Contains(err.Error(), "connection") ||
               strings.Contains(err.Error(), "timeout")
    default:
        return false
    }
}
```

## 4. 监控指标体系

### 4.1 错误监控指标

```go
// ErrorMetrics 错误监控指标
type ErrorMetrics struct {
    errorCounts    map[string]*ErrorCounter
    errorRates     map[string]*RateCounter
    lastErrors     map[string]*CoredumpError
    mutex          sync.RWMutex
    startTime      time.Time
}

// ErrorCounter 错误计数器
type ErrorCounter struct {
    Total       int64     `json:"total"`
    LastHour    int64     `json:"last_hour"`
    LastDay     int64     `json:"last_day"`
    LastUpdated time.Time `json:"last_updated"`
}

// RateCounter 速率计数器
type RateCounter struct {
    Count     int64     `json:"count"`
    Window    time.Duration `json:"window"`
    StartTime time.Time `json:"start_time"`
}

// NewErrorMetrics 创建错误监控指标
func NewErrorMetrics() *ErrorMetrics {
    return &ErrorMetrics{
        errorCounts: make(map[string]*ErrorCounter),
        errorRates:  make(map[string]*RateCounter),
        lastErrors:  make(map[string]*CoredumpError),
        startTime:   time.Now(),
    }
}

// RecordError 记录错误
func (m *ErrorMetrics) RecordError(err *CoredumpError) {
    m.mutex.Lock()
    defer m.mutex.Unlock()
    
    key := string(err.Type)
    
    // 更新错误计数
    if counter, exists := m.errorCounts[key]; exists {
        counter.Total++
        counter.LastUpdated = time.Now()
    } else {
        m.errorCounts[key] = &ErrorCounter{
            Total:       1,
            LastUpdated: time.Now(),
        }
    }
    
    // 更新错误率
    if rate, exists := m.errorRates[key]; exists {
        rate.Count++
    } else {
        m.errorRates[key] = &RateCounter{
            Count:     1,
            Window:    time.Hour,
            StartTime: time.Now(),
        }
    }
    
    // 记录最新错误
    m.lastErrors[key] = err
}

// GetErrorCount 获取错误计数
func (m *ErrorMetrics) GetErrorCount(errorType ErrorType, window time.Duration) int64 {
    m.mutex.RLock()
    defer m.mutex.RUnlock()
    
    key := string(errorType)
    if counter, exists := m.errorCounts[key]; exists {
        return counter.Total
    }
    return 0
}

// GetErrorRate 获取错误率
func (m *ErrorMetrics) GetErrorRate(errorType ErrorType) float64 {
    m.mutex.RLock()
    defer m.mutex.RUnlock()
    
    key := string(errorType)
    if rate, exists := m.errorRates[key]; exists {
        elapsed := time.Since(rate.StartTime)
        if elapsed > 0 {
            return float64(rate.Count) / elapsed.Hours()
        }
    }
    return 0
}

// GetMetricsSummary 获取指标摘要
func (m *ErrorMetrics) GetMetricsSummary() *MetricsSummary {
    m.mutex.RLock()
    defer m.mutex.RUnlock()
    
    summary := &MetricsSummary{
        StartTime:    m.startTime,
        LastUpdated:  time.Now(),
        ErrorCounts:  make(map[string]int64),
        ErrorRates:   make(map[string]float64),
        LastErrors:   make(map[string]*CoredumpError),
    }
    
    for errorType, counter := range m.errorCounts {
        summary.ErrorCounts[errorType] = counter.Total
    }
    
    for errorType, rate := range m.errorRates {
        summary.ErrorRates[errorType] = m.GetErrorRate(ErrorType(errorType))
    }
    
    for errorType, err := range m.lastErrors {
        summary.LastErrors[errorType] = err
    }
    
    return summary
}

// MetricsSummary 指标摘要
type MetricsSummary struct {
    StartTime   time.Time                    `json:"start_time"`
    LastUpdated time.Time                    `json:"last_updated"`
    ErrorCounts map[string]int64             `json:"error_counts"`
    ErrorRates  map[string]float64           `json:"error_rates"`
    LastErrors  map[string]*CoredumpError    `json:"last_errors"`
}
```

### 4.2 性能监控指标

```go
// PerformanceMetrics 性能监控指标
type PerformanceMetrics struct {
    ProcessingTimes    []time.Duration `json:"processing_times"`
    ThroughputCounter  *ThroughputCounter `json:"throughput"`
    ResourceUsage      *ResourceUsage   `json:"resource_usage"`
    mutex              sync.RWMutex
}

// ThroughputCounter 吞吐量计数器
type ThroughputCounter struct {
    TotalProcessed    int64     `json:"total_processed"`
    SuccessCount      int64     `json:"success_count"`
    FailureCount      int64     `json:"failure_count"`
    StartTime         time.Time `json:"start_time"`
    LastProcessTime   time.Time `json:"last_process_time"`
}

// ResourceUsage 资源使用情况
type ResourceUsage struct {
    CPUUsage      float64 `json:"cpu_usage"`
    MemoryUsage   int64   `json:"memory_usage"`
    GoroutineCount int    `json:"goroutine_count"`
    LastUpdated   time.Time `json:"last_updated"`
}

// RecordProcessingTime 记录处理时间
func (m *PerformanceMetrics) RecordProcessingTime(duration time.Duration) {
    m.mutex.Lock()
    defer m.mutex.Unlock()
    
    m.ProcessingTimes = append(m.ProcessingTimes, duration)
    
    // 保持最近1000条记录
    if len(m.ProcessingTimes) > 1000 {
        m.ProcessingTimes = m.ProcessingTimes[1:]
    }
}

// GetAverageProcessingTime 获取平均处理时间
func (m *PerformanceMetrics) GetAverageProcessingTime() time.Duration {
    m.mutex.RLock()
    defer m.mutex.RUnlock()
    
    if len(m.ProcessingTimes) == 0 {
        return 0
    }
    
    var total time.Duration
    for _, duration := range m.ProcessingTimes {
        total += duration
    }
    
    return total / time.Duration(len(m.ProcessingTimes))
}

// UpdateResourceUsage 更新资源使用情况
func (m *PerformanceMetrics) UpdateResourceUsage() {
    m.mutex.Lock()
    defer m.mutex.Unlock()
    
    var memStats runtime.MemStats
    runtime.ReadMemStats(&memStats)
    
    m.ResourceUsage = &ResourceUsage{
        MemoryUsage:    int64(memStats.Alloc),
        GoroutineCount: runtime.NumGoroutine(),
        LastUpdated:    time.Now(),
    }
}
```

## 5. 告警系统设计

### 5.1 告警管理器

```go
// AlertManager 告警管理器
type AlertManager struct {
    config    *AlertConfig
    channels  []AlertChannel
    rules     []*AlertRule
    history   *AlertHistory
}

// AlertConfig 告警配置
type AlertConfig struct {
    Enabled           bool          `yaml:"enabled"`
    DefaultChannel    string        `yaml:"default_channel"`
    AlertCooldown     time.Duration `yaml:"alert_cooldown"`
    MaxAlertsPerHour  int           `yaml:"max_alerts_per_hour"`
    EnableEscalation  bool          `yaml:"enable_escalation"`
    EscalationDelay   time.Duration `yaml:"escalation_delay"`
}

// AlertChannel 告警通道接口
type AlertChannel interface {
    SendAlert(alert *Alert) error
    GetChannelType() string
    IsAvailable() bool
}

// Alert 告警信息
type Alert struct {
    ID          string      `json:"id"`
    Level       AlertLevel  `json:"level"`
    Title       string      `json:"title"`
    Message     string      `json:"message"`
    Source      string      `json:"source"`
    Timestamp   time.Time   `json:"timestamp"`
    Tags        []string    `json:"tags"`
    Metadata    map[string]interface{} `json:"metadata"`
    Resolved    bool        `json:"resolved"`
    ResolvedAt  *time.Time  `json:"resolved_at,omitempty"`
}

// AlertLevel 告警级别
type AlertLevel string

const (
    AlertLevelCritical AlertLevel = "critical" // 严重告警
    AlertLevelHigh     AlertLevel = "high"     // 高级告警
    AlertLevelMedium   AlertLevel = "medium"   // 中级告警
    AlertLevelLow      AlertLevel = "low"      // 低级告警
)

// SendAlert 发送告警
func (am *AlertManager) SendAlert(err *CoredumpError) error {
    alert := am.createAlertFromError(err)
    
    // 检查告警冷却期
    if am.isInCooldown(alert) {
        logging.InfoLogger.Infof("告警在冷却期内，跳过发送: %s", alert.Title)
        return nil
    }
    
    // 检查告警频率限制
    if am.exceedsRateLimit() {
        logging.WarningLogger.Warning("告警频率超过限制，暂停发送")
        return nil
    }
    
    // 发送告警到所有可用通道
    var errors []error
    for _, channel := range am.channels {
        if channel.IsAvailable() {
            if err := channel.SendAlert(alert); err != nil {
                errors = append(errors, err)
                logging.ErrorLogger.Errorf("告警发送失败 [%s]: %v", 
                    channel.GetChannelType(), err)
            }
        }
    }
    
    // 记录告警历史
    am.history.RecordAlert(alert)
    
    if len(errors) > 0 {
        return fmt.Errorf("部分告警通道发送失败: %d个错误", len(errors))
    }
    
    return nil
}

// createAlertFromError 从错误创建告警
func (am *AlertManager) createAlertFromError(err *CoredumpError) *Alert {
    alert := &Alert{
        ID:        generateAlertID(),
        Level:     am.mapErrorLevelToAlertLevel(err.Level),
        Title:     fmt.Sprintf("Coredump处理错误: %s", err.Code),
        Message:   err.Message,
        Source:    "coredump-system",
        Timestamp: time.Now(),
        Tags:      []string{string(err.Type), err.Code},
        Metadata: map[string]interface{}{
            "error_type":  err.Type,
            "error_code":  err.Code,
            "record_id":   err.RecordID,
            "sn":          err.SN,
            "retryable":   err.Retryable,
            "retry_count": err.RetryCount,
        },
    }
    
    return alert
}
```

### 5.2 告警通道实现

```go
// EmailAlertChannel 邮件告警通道
type EmailAlertChannel struct {
    config *EmailConfig
    client *smtp.Client
}

// EmailConfig 邮件配置
type EmailConfig struct {
    SMTPHost     string   `yaml:"smtp_host"`
    SMTPPort     int      `yaml:"smtp_port"`
    Username     string   `yaml:"username"`
    Password     string   `yaml:"password"`
    FromAddress  string   `yaml:"from_address"`
    ToAddresses  []string `yaml:"to_addresses"`
    Subject      string   `yaml:"subject"`
    EnableTLS    bool     `yaml:"enable_tls"`
}

// SendAlert 发送邮件告警
func (e *EmailAlertChannel) SendAlert(alert *Alert) error {
    subject := fmt.Sprintf("[%s] %s", strings.ToUpper(string(alert.Level)), alert.Title)
    body := e.formatEmailBody(alert)
    
    msg := fmt.Sprintf("To: %s\r\nSubject: %s\r\n\r\n%s",
        strings.Join(e.config.ToAddresses, ","), subject, body)
    
    return e.sendEmail(e.config.ToAddresses, []byte(msg))
}

// formatEmailBody 格式化邮件正文
func (e *EmailAlertChannel) formatEmailBody(alert *Alert) string {
    var body strings.Builder
    
    body.WriteString(fmt.Sprintf("告警时间: %s\n", alert.Timestamp.Format("2006-01-02 15:04:05")))
    body.WriteString(fmt.Sprintf("告警级别: %s\n", alert.Level))
    body.WriteString(fmt.Sprintf("告警来源: %s\n", alert.Source))
    body.WriteString(fmt.Sprintf("告警消息: %s\n\n", alert.Message))
    
    if len(alert.Metadata) > 0 {
        body.WriteString("详细信息:\n")
        for key, value := range alert.Metadata {
            body.WriteString(fmt.Sprintf("  %s: %v\n", key, value))
        }
    }
    
    body.WriteString("\n请及时处理相关问题。")
    
    return body.String()
}

// WebhookAlertChannel Webhook告警通道
type WebhookAlertChannel struct {
    config *WebhookConfig
    client *http.Client
}

// WebhookConfig Webhook配置
type WebhookConfig struct {
    URL         string            `yaml:"url"`
    Method      string            `yaml:"method"`
    Headers     map[string]string `yaml:"headers"`
    Timeout     time.Duration     `yaml:"timeout"`
    RetryCount  int               `yaml:"retry_count"`
}

// SendAlert 发送Webhook告警
func (w *WebhookAlertChannel) SendAlert(alert *Alert) error {
    payload, err := json.Marshal(alert)
    if err != nil {
        return fmt.Errorf("序列化告警数据失败: %w", err)
    }
    
    req, err := http.NewRequest(w.config.Method, w.config.URL, bytes.NewBuffer(payload))
    if err != nil {
        return fmt.Errorf("创建HTTP请求失败: %w", err)
    }
    
    // 设置请求头
    req.Header.Set("Content-Type", "application/json")
    for key, value := range w.config.Headers {
        req.Header.Set(key, value)
    }
    
    // 发送请求
    resp, err := w.client.Do(req)
    if err != nil {
        return fmt.Errorf("发送Webhook请求失败: %w", err)
    }
    defer resp.Body.Close()
    
    if resp.StatusCode >= 400 {
        return fmt.Errorf("Webhook请求失败，状态码: %d", resp.StatusCode)
    }
    
    return nil
}
```

## 6. 日志记录系统

### 6.1 结构化日志记录

```go
// StructuredLogger 结构化日志记录器
type StructuredLogger struct {
    logger *logrus.Logger
    fields logrus.Fields
}

// NewStructuredLogger 创建结构化日志记录器
func NewStructuredLogger(component string) *StructuredLogger {
    logger := logrus.New()
    logger.SetFormatter(&logrus.JSONFormatter{
        TimestampFormat: "2006-01-02 15:04:05",
    })
    
    return &StructuredLogger{
        logger: logger,
        fields: logrus.Fields{
            "component": component,
            "service":   "coredump-processor",
        },
    }
}

// LogError 记录错误日志
func (l *StructuredLogger) LogError(err *CoredumpError) {
    entry := l.logger.WithFields(l.fields).WithFields(logrus.Fields{
        "error_type":    err.Type,
        "error_level":   err.Level,
        "error_code":    err.Code,
        "record_id":     err.RecordID,
        "sn":            err.SN,
        "retryable":     err.Retryable,
        "retry_count":   err.RetryCount,
        "timestamp":     err.Timestamp,
    })
    
    switch err.Level {
    case ErrorLevelFatal:
        entry.Fatal(err.Message)
    case ErrorLevelError:
        entry.Error(err.Message)
    case ErrorLevelWarning:
        entry.Warn(err.Message)
    case ErrorLevelInfo:
        entry.Info(err.Message)
    }
}

// LogProcessingEvent 记录处理事件
func (l *StructuredLogger) LogProcessingEvent(event *ProcessingEvent) {
    entry := l.logger.WithFields(l.fields).WithFields(logrus.Fields{
        "event_type":    event.Type,
        "record_id":     event.RecordID,
        "sn":            event.SN,
        "duration":      event.Duration,
        "success":       event.Success,
        "timestamp":     event.Timestamp,
    })
    
    if event.Success {
        entry.Info(event.Message)
    } else {
        entry.Error(event.Message)
    }
}

// ProcessingEvent 处理事件
type ProcessingEvent struct {
    Type      string        `json:"type"`
    RecordID  string        `json:"record_id"`
    SN        string        `json:"sn"`
    Message   string        `json:"message"`
    Duration  time.Duration `json:"duration"`
    Success   bool          `json:"success"`
    Timestamp time.Time     `json:"timestamp"`
    Metadata  map[string]interface{} `json:"metadata"`
}
```

### 6.2 日志轮转和归档

```go
// LogRotator 日志轮转器
type LogRotator struct {
    config *LogRotationConfig
}

// LogRotationConfig 日志轮转配置
type LogRotationConfig struct {
    MaxSize    int    `yaml:"max_size"`    // 最大文件大小(MB)
    MaxAge     int    `yaml:"max_age"`     // 最大保存天数
    MaxBackups int    `yaml:"max_backups"` // 最大备份文件数
    Compress   bool   `yaml:"compress"`    // 是否压缩
    LocalTime  bool   `yaml:"local_time"`  // 是否使用本地时间
}

// SetupLogRotation 设置日志轮转
func (r *LogRotator) SetupLogRotation(logFile string) error {
    rotateWriter := &lumberjack.Logger{
        Filename:   logFile,
        MaxSize:    r.config.MaxSize,
        MaxAge:     r.config.MaxAge,
        MaxBackups: r.config.MaxBackups,
        Compress:   r.config.Compress,
        LocalTime:  r.config.LocalTime,
    }
    
    // 设置日志输出到轮转文件
    logrus.SetOutput(rotateWriter)
    
    return nil
}
```

## 7. 健康检查系统

### 7.1 健康检查器

```go
// HealthChecker 健康检查器
type HealthChecker struct {
    checks []HealthCheck
    config *HealthCheckConfig
}

// HealthCheck 健康检查接口
type HealthCheck interface {
    Name() string
    Check() HealthStatus
    Timeout() time.Duration
}

// HealthStatus 健康状态
type HealthStatus struct {
    Status    string                 `json:"status"`    // healthy, unhealthy, unknown
    Message   string                 `json:"message"`
    Details   map[string]interface{} `json:"details"`
    Timestamp time.Time              `json:"timestamp"`
    Duration  time.Duration          `json:"duration"`
}

// HealthCheckConfig 健康检查配置
type HealthCheckConfig struct {
    Interval    time.Duration `yaml:"interval"`
    Timeout     time.Duration `yaml:"timeout"`
    Enabled     bool          `yaml:"enabled"`
    Endpoint    string        `yaml:"endpoint"`
}

// DatabaseHealthCheck 数据库健康检查
type DatabaseHealthCheck struct {
    db *gorm.DB
}

func (d *DatabaseHealthCheck) Name() string {
    return "database"
}

func (d *DatabaseHealthCheck) Check() HealthStatus {
    start := time.Now()
    
    var result int
    err := d.db.Raw("SELECT 1").Scan(&result).Error
    
    duration := time.Since(start)
    
    if err != nil {
        return HealthStatus{
            Status:    "unhealthy",
            Message:   fmt.Sprintf("数据库连接失败: %v", err),
            Timestamp: time.Now(),
            Duration:  duration,
        }
    }
    
    return HealthStatus{
        Status:    "healthy",
        Message:   "数据库连接正常",
        Timestamp: time.Now(),
        Duration:  duration,
        Details: map[string]interface{}{
            "response_time_ms": duration.Milliseconds(),
        },
    }
}

func (d *DatabaseHealthCheck) Timeout() time.Duration {
    return 5 * time.Second
}

// FeishuAPIHealthCheck 飞书API健康检查
type FeishuAPIHealthCheck struct {
    client *lark.Client
}

func (f *FeishuAPIHealthCheck) Name() string {
    return "feishu_api"
}

func (f *FeishuAPIHealthCheck) Check() HealthStatus {
    start := time.Now()
    
    // 调用飞书API进行健康检查
    _, err := f.client.Auth.GetTenantAccessToken(context.Background())
    
    duration := time.Since(start)
    
    if err != nil {
        return HealthStatus{
            Status:    "unhealthy",
            Message:   fmt.Sprintf("飞书API连接失败: %v", err),
            Timestamp: time.Now(),
            Duration:  duration,
        }
    }
    
    return HealthStatus{
        Status:    "healthy",
        Message:   "飞书API连接正常",
        Timestamp: time.Now(),
        Duration:  duration,
        Details: map[string]interface{}{
            "response_time_ms": duration.Milliseconds(),
        },
    }
}

func (f *FeishuAPIHealthCheck) Timeout() time.Duration {
    return 10 * time.Second
}

// RunHealthChecks 运行所有健康检查
func (h *HealthChecker) RunHealthChecks() map[string]HealthStatus {
    results := make(map[string]HealthStatus)
    
    for _, check := range h.checks {
        // 使用超时控制
        done := make(chan HealthStatus, 1)
        
        go func(c HealthCheck) {
            done <- c.Check()
        }(check)
        
        select {
        case result := <-done:
            results[check.Name()] = result
        case <-time.After(check.Timeout()):
            results[check.Name()] = HealthStatus{
                Status:    "unhealthy",
                Message:   "健康检查超时",
                Timestamp: time.Now(),
                Duration:  check.Timeout(),
            }
        }
    }
    
    return results
}
```

## 8. 监控仪表板

### 8.1 监控数据API

```go
// MonitoringController 监控控制器
type MonitoringController struct {
    errorMetrics      *ErrorMetrics
    performanceMetrics *PerformanceMetrics
    healthChecker     *HealthChecker
}

// GetErrorMetrics 获取错误指标
func (m *MonitoringController) GetErrorMetrics(ctx iris.Context) {
    summary := m.errorMetrics.GetMetricsSummary()
    
    ctx.JSON(iris.Map{
        "success": true,
        "data":    summary,
    })
}

// GetPerformanceMetrics 获取性能指标
func (m *MonitoringController) GetPerformanceMetrics(ctx iris.Context) {
    // 更新资源使用情况
    m.performanceMetrics.UpdateResourceUsage()
    
    metrics := map[string]interface{}{
        "average_processing_time": m.performanceMetrics.GetAverageProcessingTime(),
        "throughput":             m.performanceMetrics.ThroughputCounter,
        "resource_usage":         m.performanceMetrics.ResourceUsage,
    }
    
    ctx.JSON(iris.Map{
        "success": true,
        "data":    metrics,
    })
}

// GetHealthStatus 获取健康状态
func (m *MonitoringController) GetHealthStatus(ctx iris.Context) {
    healthResults := m.healthChecker.RunHealthChecks()
    
    // 计算整体健康状态
    overallStatus := "healthy"
    for _, status := range healthResults {
        if status.Status == "unhealthy" {
            overallStatus = "unhealthy"
            break
        }
    }
    
    ctx.JSON(iris.Map{
        "success": true,
        "data": iris.Map{
            "overall_status": overallStatus,
            "checks":         healthResults,
            "timestamp":      time.Now(),
        },
    })
}

// GetSystemStatus 获取系统状态概览
func (m *MonitoringController) GetSystemStatus(ctx iris.Context) {
    errorSummary := m.errorMetrics.GetMetricsSummary()
    healthResults := m.healthChecker.RunHealthChecks()
    
    // 计算系统状态
    systemStatus := map[string]interface{}{
        "status":           "running",
        "uptime":           time.Since(errorSummary.StartTime),
        "total_errors":     m.getTotalErrorCount(errorSummary.ErrorCounts),
        "error_rate":       m.getOverallErrorRate(errorSummary.ErrorRates),
        "health_checks":    healthResults,
        "last_updated":     time.Now(),
    }
    
    ctx.JSON(iris.Map{
        "success": true,
        "data":    systemStatus,
    })
}

// getTotalErrorCount 计算总错误数
func (m *MonitoringController) getTotalErrorCount(errorCounts map[string]int64) int64 {
    var total int64
    for _, count := range errorCounts {
        total += count
    }
    return total
}

// getOverallErrorRate 计算整体错误率
func (m *MonitoringController) getOverallErrorRate(errorRates map[string]float64) float64 {
    var total float64
    for _, rate := range errorRates {
        total += rate
    }
    return total
}
```

## 9. 配置文件示例

### 9.1 错误处理配置

```yaml
# config/error_handling.yaml
error_handling:
  # 重试配置
  retry:
    max_retries: 3
    initial_delay: "1s"
    max_delay: "30s"
    backoff_factor: 2.0
    jitter: true
  
  # 告警配置
  alert:
    enabled: true
    default_channel: "email"
    alert_cooldown: "5m"
    max_alerts_per_hour: 10
    enable_escalation: true
    escalation_delay: "15m"
    
    # 邮件告警配置
    email:
      smtp_host: "smtp.company.com"
      smtp_port: 587
      username: "<EMAIL>"
      password: "password"
      from_address: "<EMAIL>"
      to_addresses:
        - "<EMAIL>"
        - "<EMAIL>"
      enable_tls: true
    
    # Webhook告警配置
    webhook:
      url: "https://hooks.slack.com/services/xxx/yyy/zzz"
      method: "POST"
      timeout: "10s"
      retry_count: 3
      headers:
        Content-Type: "application/json"
  
  # 日志配置
  logging:
    level: "info"
    enable_stack_trace: true
    rotation:
      max_size: 100    # MB
      max_age: 30      # days
      max_backups: 10
      compress: true
      local_time: true
  
  # 健康检查配置
  health_check:
    enabled: true
    interval: "30s"
    timeout: "10s"
    endpoint: "/health"
  
  # 监控配置
  monitoring:
    metrics_retention: "24h"
    enable_performance_monitoring: true
    enable_resource_monitoring: true
```

## 10. 部署和运维指南

### 10.1 监控部署脚本

```bash
#!/bin/bash
# monitoring_setup.sh

echo "设置Coredump系统监控..."

# 1. 创建监控目录
mkdir -p /var/log/coredump-system
mkdir -p /etc/coredump-system/monitoring

# 2. 复制配置文件
cp config/error_handling.yaml /etc/coredump-system/
cp config/monitoring.yaml /etc/coredump-system/monitoring/

# 3. 设置日志轮转
cat > /etc/logrotate.d/coredump-system << EOF
/var/log/coredump-system/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 coredump coredump
}
EOF

# 4. 创建监控脚本
cat > /usr/local/bin/coredump-monitor.sh << 'EOF'
#!/bin/bash
API_URL="http://localhost:8080/api/monitoring"

# 检查系统健康状态
health_status=$(curl -s "$API_URL/health" | jq -r '.data.overall_status')
if [ "$health_status" != "healthy" ]; then
    echo "$(date): 系统健康检查失败" >> /var/log/coredump-system/monitor.log
    # 发送告警
    curl -X POST "$API_URL/alert" -d '{"level":"high","message":"系统健康检查失败"}'
fi

# 检查错误率
error_rate=$(curl -s "$API_URL/metrics/errors" | jq -r '.data.error_rates.total')
if (( $(echo "$error_rate > 10" | bc -l) )); then
    echo "$(date): 错误率过高: $error_rate" >> /var/log/coredump-system/monitor.log
fi
EOF

chmod +x /usr/local/bin/coredump-monitor.sh

# 5. 设置定时任务
echo "*/5 * * * * /usr/local/bin/coredump-monitor.sh" | crontab -

echo "监控设置完成！"
```

### 10.2 故障排查指南

```bash
#!/bin/bash
# troubleshooting.sh

echo "=== Coredump系统故障排查 ==="

# 1. 检查服务状态
echo "1. 检查服务状态..."
systemctl status coredump-system

# 2. 检查日志
echo "2. 检查最近的错误日志..."
tail -n 50 /var/log/coredump-system/error.log

# 3. 检查系统资源
echo "3. 检查系统资源使用..."
echo "CPU使用率:"
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}'

echo "内存使用率:"
free -m | awk 'NR==2{printf "%.2f%%\n", $3*100/$2}'

echo "磁盘使用率:"
df -h | grep -vE '^Filesystem|tmpfs|cdrom'

# 4. 检查网络连接
echo "4. 检查网络连接..."
echo "飞书API连接:"
curl -s -o /dev/null -w "%{http_code}" "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"

echo "Bug系统连接:"
curl -s -o /dev/null -w "%{http_code}" "http://bugs.ruijie.com.cn/bug_switch/service/outinterface_submitBugInfo"

# 5. 检查数据库连接
echo "5. 检查数据库连接..."
mysql -h localhost -u username -p -e "SELECT 1" 2>/dev/null && echo "数据库连接正常" || echo "数据库连接失败"

# 6. 检查配置文件
echo "6. 检查配置文件..."
if [ -f "/etc/coredump-system/error_handling.yaml" ]; then
    echo "配置文件存在"
    yaml-lint /etc/coredump-system/error_handling.yaml && echo "配置文件格式正确" || echo "配置文件格式错误"
else
    echo "配置文件不存在"
fi

echo "=== 故障排查完成 ==="
```

这个错误处理和监控方案提供了：

1. **完整的错误分类体系**：按类型和级别分类错误
2. **统一的错误处理机制**：包含重试、告警、日志记录
3. **多维度监控指标**：错误指标、性能指标、资源监控
4. **灵活的告警系统**：支持多种告警通道和规则
5. **结构化日志记录**：便于分析和排查问题
6. **健康检查系统**：实时监控系统组件状态
7. **监控仪表板**：提供可视化的监控数据
8. **运维工具**：包含部署脚本和故障排查指南

该方案确保了系统的高可用性、可观测性和可维护性。