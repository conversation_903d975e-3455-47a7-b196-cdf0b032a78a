package common

import (
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"time"

	"github.com/kataras/iris/v12"
)

func CheckWorkDay(ctx iris.Context) {
	day := ctx.FormValue("day")
	dateTime, err := time.Parse("2006-01-02", day)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	check := libs.IsWorkDay(dateTime)

	ctx.JSON(response.NewResponse(response.NoErr.Code, check, response.NoErr.Msg))
	return
}
