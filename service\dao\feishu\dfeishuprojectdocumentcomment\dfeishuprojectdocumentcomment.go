package dfeishuprojectdocumentcomment

import (
	"fmt"
	"reflect"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/feishu"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "项目文档评论表"

type FeishuProjectDocumentComment struct {
	feishu.FeishuProjectDocumentComment
}

type ListResponse struct {
	FeishuProjectDocumentComment
}

type Request struct {
	Id uint `json:"id"`
}

func (a *FeishuProjectDocumentComment) ModelName() string {
	return ModelName
}

func Model() *feishu.FeishuProjectDocumentComment {
	return &feishu.FeishuProjectDocumentComment{}
}

func (a *FeishuProjectDocumentComment) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (a *FeishuProjectDocumentComment) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (a *FeishuProjectDocumentComment) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (a *FeishuProjectDocumentComment) CreateV2(object interface{}) error {
	return nil
}

func (a *FeishuProjectDocumentComment) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (u *FeishuProjectDocumentComment) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (u *FeishuProjectDocumentComment) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *FeishuProjectDocumentComment) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *FeishuProjectDocumentComment) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *FeishuProjectDocumentComment) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func CreateOrUpdateProjectDocumentCommentData(objects []map[string]interface{}) error {
	ReSetAutoIncrement()
	columns := []string{"updated_at"}
	xt := reflect.TypeOf(Model())
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")
		if ok && ok2 {
			columns = append(columns, name)
		}
	}
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		if len(objects) > 0 {
			err := tx.Model(Model()).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "pms_doc_id"}, {Name: "project_name"}, {Name: "comment_id"}},
				DoUpdates: clause.AssignmentColumns(columns),
			}).Create(&objects).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func GetSolvedCommentByPMSDocID(pmsDocID uint, isSolved int) ([]FeishuProjectDocumentComment, error) {
	var objects []FeishuProjectDocumentComment
	err := easygorm.GetEasyGormDb().Model(Model()).Where("pms_doc_id = ? and is_solved=?", pmsDocID, isSolved).Find(&objects).Error
	if err != nil {
		return nil, err
	}
	return objects, nil
}

func ReSetAutoIncrement() error {
	var maxID int
	err := easygorm.GetEasyGormDb().Table("feishu_project_document_comments").Select("MAX(id)").Scan(&maxID).Error
	if err != nil {
		return err
	}
	newAutoIncrement := maxID + 1
	alterTableSQL := fmt.Sprintf("ALTER TABLE feishu_project_document_comments AUTO_INCREMENT=%d", newAutoIncrement)
	if err := easygorm.GetEasyGormDb().Exec(alterTableSQL).Error; err != nil {
		logging.ErrorLogger.Errorf("ALTER TABLE feishu_project_document_comments  get err ", err)
		return err
	}
	return nil
}

func DeleteByCommentID(commentID uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), "comment_id=?", commentID).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func UpdateByCommentID(commentID uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("comment_id = ?", commentID).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}
