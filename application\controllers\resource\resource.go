package resource

import (
	"fmt"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/service/dao/datasync/dresourceclean"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/xuri/excelize/v2"
)

func ExportResource(ctx iris.Context) {
	year := ctx.FormValue("var-year")
	season := ctx.FormValue("var-season")
	month := ctx.FormValue("var-month")
	week := ctx.FormValue("var-week")
	department := ctx.FormValue("department")
	group := ctx.FormValue("group")
	user := ctx.FormValue("user")
	project := ctx.FormValue("project")
	projectMap := map[string]string{
		"soft":  "软件项目资源",
		"hard":  "硬件项目资源",
		"other": "其它资源",
	}
	if _, ok := projectMap[project]; !ok {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, map[string]string{"err_msg": "project参数错误"}, response.ParamentErr.Msg))
		return
	}

	_params := []string{projectMap[project], department, group, user, time.Now().Format("20060102150405")}
	params := []string{}
	for _, param := range _params {
		if param != "" {
			params = append(params, param)
		}
	}

	fileName := fmt.Sprintf("%s.xlsx", strings.Join(params, "_"))
	file := excelize.NewFile()
	streamWriter, err := file.NewStreamWriter("Sheet1")
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	rowNum := 1
	err, _, rows := dresourceclean.Export(year, season, month, week, project, department, group, user)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	for _, row := range rows {
		cell, _ := excelize.CoordinatesToCellName(1, rowNum)
		if err := streamWriter.SetRow(cell, row); err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
			return
		}
		rowNum++
	}
	if err := streamWriter.Flush(); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	if err := file.SaveAs(filepath.Join("/tmp", fileName)); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	defer os.Remove(filepath.Join("/tmp", fileName))
	ctx.SendFile(filepath.Join("/tmp", fileName), fileName)
	return
}

func ExportGroupResource(ctx iris.Context) {
	year := strings.ReplaceAll(ctx.FormValue("var-year"), " ", "")
	season := strings.ReplaceAll(ctx.FormValue("var-season"), " ", "")
	month := strings.ReplaceAll(ctx.FormValue("var-month"), " ", "")
	week := strings.ReplaceAll(ctx.FormValue("var-week"), " ", "")
	department := strings.ReplaceAll(ctx.FormValue("var-department"), " ", "")
	group := strings.ReplaceAll(ctx.FormValue("var-group"), " ", "")
	user := strings.ReplaceAll(ctx.FormValue("var-user"), " ", "")
	// project := ctx.FormValue("project")
	// projectMap := map[string]string{
	// 	"soft":  "软件项目资源",
	// 	"hard":  "硬件项目资源",
	// 	"other": "其它资源",
	// }
	// if _, ok := projectMap[project]; !ok {
	// 	ctx.JSON(response.NewResponse(response.ParamentErr.Code, map[string]string{"err_msg": "project参数错误"}, response.ParamentErr.Msg))
	// 	return
	// }

	_params := []string{"专业组资源投入情况", time.Now().Format("20060102150405")}
	params := []string{}
	for _, param := range _params {
		if param != "" {
			params = append(params, param)
		}
	}

	fileName := fmt.Sprintf("%s.xlsx", strings.Join(params, "_"))
	file := excelize.NewFile()
	streamWriter, err := file.NewStreamWriter("Sheet1")
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}

	rowNum := 1
	err, _, rows := dresourceclean.ExportGroupResource(year, season, month, week, department, group, user)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	for _, row := range rows {
		cell, _ := excelize.CoordinatesToCellName(1, rowNum)
		if err := streamWriter.SetRow(cell, row); err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
			return
		}
		rowNum++
	}
	if err := streamWriter.Flush(); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	if err := file.SaveAs(filepath.Join("/tmp", fileName)); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]string{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	defer os.Remove(filepath.Join("/tmp", fileName))
	ctx.SendFile(filepath.Join("/tmp", fileName), fileName)
	return
}
