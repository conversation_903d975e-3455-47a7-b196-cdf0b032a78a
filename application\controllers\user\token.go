package user

import (
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/user/dusertoken"
	"strconv"
	"time"

	"github.com/kataras/iris/v12"
)

func GetUserToken(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	token := dusertoken.Response{}
	err = token.FindEx("user_id", strconv.FormatUint(uint64(id), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("get token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	tokenStr := token.Token
	if token.Id == 0 {
		for {
			checkToken := dusertoken.Response{}
			tokenStr = libs.RandString(10)
			err = checkToken.FindEx("token", tokenStr)
			if err != nil {
				logging.ErrorLogger.Errorf("check user token get err ", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
				return
			}
			if checkToken.Id == 0 {
				break
			}
		}

		err = dao.Create(&token, ctx, map[string]interface{}{
			"UserID":    id,
			"Token":     tokenStr,
			"CreatedAt": time.Now(),
			"UpdatedAt": time.Now(),
		})
		if err != nil {
			logging.ErrorLogger.Errorf("create user token get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
	} else {
		tokenStr = token.Token
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]string{"token": tokenStr}, response.NoErr.Msg))
	return
}

func CreateUserToken(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get id for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	token := dusertoken.Response{}
	err = token.FindEx("user_id", strconv.FormatUint(uint64(id), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("get token for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	var tokenStr string
	for {
		checkToken := dusertoken.Response{}
		tokenStr = libs.RandString(10)
		err = checkToken.FindEx("token", tokenStr)
		if err != nil {
			logging.ErrorLogger.Errorf("check user token get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		if checkToken.Id == 0 {
			break
		}
	}
	if token.Id == 0 {
		err = dao.Create(&token, ctx, map[string]interface{}{
			"UserID":    id,
			"Token":     tokenStr,
			"CreatedAt": time.Now(),
			"UpdatedAt": time.Now(),
		})
		if err != nil {
			logging.ErrorLogger.Errorf("create user token get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
	} else {
		err = token.Update(token.Id, map[string]interface{}{
			"UserID":    id,
			"Token":     tokenStr,
			"UpdatedAt": time.Now(),
		})
		if err != nil {
			logging.ErrorLogger.Errorf("create user token get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]string{"token": tokenStr}, response.NoErr.Msg))
	return
}
