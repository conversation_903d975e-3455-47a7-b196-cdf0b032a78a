package datasync

import (
	"errors"
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/datasync/dpmscaseinfo"
	"irisAdminApi/service/dao/datasync/dsyncrecord"
	"strconv"
	"time"
)

func PmsCaseInfoSyncWorker() error {
	// todo: 检查同步记录，获取同步时间，如果没有，从七天前开始，按小时同步
	modifyDateArray := []string{}
	_url := "https://dataware.ruijie.com.cn/api/public/data-api/safe_product_line_pms_case_info/list.data"
	records, err := dsyncrecord.FindLastSuccessSyncRecord(_url)
	if err != nil {
		logging.ErrorLogger.Errorf("get last sync records", err.Error())
		return err
	}

	if len(records) > 0 {
		modifyDateArray = libs.GetDateRange(-365, records[0].MaxModifyDate)
	} else {
		modifyDateArray = libs.GetDateRange(-365)
	}

	if len(modifyDateArray) < 2 {
		return nil
	}

	page := 1
	rows := 1000

	for {
		data := map[string]string{
			"sid":           "MjE4MmM4ZGYw",
			"minModifyDate": modifyDateArray[0],
			"maxModifyDate": modifyDateArray[len(modifyDateArray)-1],
			"page":          strconv.Itoa(page),
			"rows":          strconv.Itoa(rows),
		}

		var result dpmscaseinfo.PmsCaseInfoSyncResponse
		var errMsg dpmscaseinfo.PmsCaseInfoSyncResponse
		resp, err := SyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(data).Get(_url)
		if err != nil {
			logging.ErrorLogger.Errorf("get resources error", err.Error())
			return err
		}
		if resp.IsSuccessState() {
			if result.State == "SUCCESS" {
				err := dpmscaseinfo.UpdateOrCreatePmsCaseInfoTransaction(result.Data, _url, data, resp.Request.Method, result.State, result.Message)
				if err != nil {
					logging.ErrorLogger.Errorf("update or create resources error", err.Error())
					return err
				}
			} else {
				logging.ErrorLogger.Errorf("get resources failed", result.State, result.Message)
				return fmt.Errorf("get resources failed, %s, %s", result.State, result.Message)
			}
		} else {
			logging.ErrorLogger.Errorf("get resources unkown error")
			return errors.New("unkown error")
		}

		time.Sleep(20 * time.Second)
		if result.Total > rows*page {
			page++
		} else {
			break
		}
	}

	return nil
}
