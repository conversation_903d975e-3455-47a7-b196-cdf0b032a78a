package qualitydata

import (
	"time"

	"gorm.io/gorm"
)

//故障清单列表
type FaultList struct {
	gorm.Model
	TechnicalFailureID          string    `gorm:"uniqueIndex;not null; type:varchar(200)" json:"technical_failure_id" `                //技服故障ID
	FaultManagerID              uint      `gorm:"not null,default:0" json:"fault_manager_id" `                                         //故障负责人
	UserID                      uint      `gorm:"not null,default:0" json:"user_id" `                                                            //故障记录人
	GroupID                     uint      `gorm:"not null,default:0" json:"group_id"`                                                  //归属专业组ID
	ReportingTime               time.Time `json:"reporting_time"`                                                                      //提报时间
	ProblemDescription          string    `gorm:"index;not null; type:varchar(512)"  json:"problem_description"`                       //问题描述
	FaultModel                  string    `gorm:"not null; type:varchar(512)"  json:"fault_model"`                                     //故障型号
	FaultClassification         uint      `gorm:"not null,default:0" json:"fault_classification"`                                      //故障分类
	FailureVersion              string    `gorm:"not null, type:varchar(256),default:''"  json:"failure_version"`                      //故障版本
	IntroductionVersion         string    `gorm:"not null, type:varchar(256),default:''"  json:"introduction_version"`                 //引入版本
	FaultyComponent             string    `gorm:"not null, type:varchar(512),default:''"  json:"faulty_component"`                     //故障组件
	IntroductionVersionTime     string    `gorm:"not null, type:varchar(512),default:''"  json:"introduction_version_time"`            //引入版本时间
	ResolutionVersion           string    `gorm:"index;not null, type:varchar(256),default:''"  json:"resolution_version"`             //解决版本
	SolutionScenarios           string    `gorm:"index;not null, type:varchar(256),default:''"  json:"solution_scenarios"`             //解决方案场景
	CausesCategory              string    `gorm:"not null, type:varchar(128),default:''"  json:"causes_category"`                      //原因大类
	CauseSubclass               string    `gorm:"index;not null, type:varchar(256),default:''"  json:"cause_subclass"`                 //原因小类
	QualityAttributeCategory    string    `gorm:"index;not null, type:varchar(128),default:''"  json:"quality_attribute_category"`     //质量属性类别
	QualityRootCauseDescription string    `gorm:"index;not null, type:varchar(256),default:''"  json:"quality_root_cause_description"` //质量属性根因描述
	State                       uint      `gorm:"not null,default:0" json:"state"`                                                     //状态
}

//故障组件表
type FaultyComponents struct {
	gorm.Model
	ComponentName string `gorm:"uniqueIndex;not null; type:varchar(256)" json:"component_name" ` //组件名称
}

//原因大类表
type CategoryCauses struct {
	gorm.Model
	CategoryName string `gorm:"index;not null, type:varchar(128),default:''"  json:"category_name"` //分类名称
}

//质量属性表
type QualityAttribute struct {
	gorm.Model
	QualityAttribute string `gorm:"index;not null;type:varchar(60)"  json:"quality_attribute"` //质量属性
}

//消息通知表
type Notify struct {
	gorm.Model
	Type    string `gorm:"index;not null;type:varchar(60)"  json:"type"` //消息类型
	Content string `gorm:"not null; type:varchar(256)" json:"content" `  //消息内容
	State   uint   `gorm:"not null,default:0" json:"state"`              //状态
}

//预警配置表
type AlertConfiguration struct {
	gorm.Model
	Type      string `gorm:"index;not null;type:varchar(60)"  json:"type"` //预警类型
	Content   string `gorm:"not null; type:varchar(256)" json:"content" `  //预警内容
	CycleTime uint   `gorm:"not null,default:0" json:"cycle_time"`         //时间周期
	State     uint   `gorm:"not null,default:0" json:"state"`              //状态
}

//预警类型表
type AlertType struct {
	gorm.Model
	ContentSql string `gorm:"index;not null;type:varchar(60)"  json:"content_sql"` //预警执行sql
	Explain    string `gorm:"not null; type:varchar(256)" json:"explain" `         //预警说明
	State      uint   `gorm:"not null,default:0" json:"state"`                     //状态
}

//关键词明细表
type KeyWord struct {
	gorm.Model
	KeyWord string `gorm:"index;not null;type:varchar(60)"  json:"key_word"` //关键词
	FaultID uint   `gorm:"not null,default:0" json:"fault_id"`               //故障表id
	State   uint   `gorm:"not null,default:0" json:"state"`                  //状态
}
