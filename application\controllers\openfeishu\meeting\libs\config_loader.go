package libs

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"gopkg.in/yaml.v3"
)

// UserMappingConfig 用户映射配置
type UserMappingConfig struct {
	UserMappings UserMappingsData `yaml:"user_mappings"`
}

// UserMappingsData 用户映射数据
type UserMappingsData struct {
	Version     string         `yaml:"version"`
	LastUpdated string         `yaml:"last_updated"`
	CommonUsers []UserMapping  `yaml:"common_users"`
	Settings    ConfigSettings `yaml:"settings"`
}

// UserMapping 用户映射
type UserMapping struct {
	Name       string   `yaml:"name"`
	OpenID     string   `yaml:"open_id"`
	Aliases    []string `yaml:"aliases"`
	Email      string   `yaml:"email"`
	Department string   `yaml:"department"`
}

// ConfigSettings 配置设置
type ConfigSettings struct {
	CacheTTL               string  `yaml:"cache_ttl"`
	MaxCacheSize           int     `yaml:"max_cache_size"`
	EnableFuzzyMatch       bool    `yaml:"enable_fuzzy_match"`
	FuzzyThreshold         float64 `yaml:"fuzzy_threshold"`
	EnableDatabaseFallback bool    `yaml:"enable_database_fallback"`
	BatchSize              int     `yaml:"batch_size"`
}

// ConfigLoader 配置加载器
type ConfigLoader struct {
	configPath  string
	config      *UserMappingConfig
	lastModTime time.Time
}

// NewConfigLoader 创建配置加载器
func NewConfigLoader(configPath string) *ConfigLoader {
	return &ConfigLoader{
		configPath: configPath,
	}
}

// LoadConfig 加载配置
func (c *ConfigLoader) LoadConfig() (*UserMappingConfig, error) {
	// 检查文件是否存在
	if !c.fileExists(c.configPath) {
		return nil, fmt.Errorf("配置文件不存在: %s", c.configPath)
	}

	// 读取文件内容
	data, err := os.ReadFile(c.configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 解析YAML
	var config UserMappingConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 验证配置
	if err := c.validateConfig(&config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %v", err)
	}

	// 更新缓存
	c.config = &config
	c.updateLastModTime()

	return &config, nil
}

// GetConfig 获取当前配置
func (c *ConfigLoader) GetConfig() *UserMappingConfig {
	return c.config
}

// IsConfigChanged 检查配置是否已更改
func (c *ConfigLoader) IsConfigChanged() bool {
	if c.config == nil {
		return true
	}

	fileInfo, err := filepath.Glob(c.configPath)
	if err != nil || len(fileInfo) == 0 {
		return false
	}

	// 这里简化处理，实际应该检查文件修改时间
	return false
}

// ReloadIfChanged 如果配置已更改则重新加载
func (c *ConfigLoader) ReloadIfChanged() (*UserMappingConfig, bool, error) {
	if !c.IsConfigChanged() {
		return c.config, false, nil
	}

	config, err := c.LoadConfig()
	if err != nil {
		return nil, false, err
	}

	return config, true, nil
}

// GetUserMappings 获取用户映射列表
func (c *ConfigLoader) GetUserMappings() []UserMapping {
	if c.config == nil {
		return nil
	}

	return c.config.UserMappings.CommonUsers
}

// GetSettings 获取配置设置
func (c *ConfigLoader) GetSettings() ConfigSettings {
	if c.config == nil {
		return ConfigSettings{
			CacheTTL:               "1h",
			MaxCacheSize:           1000,
			EnableFuzzyMatch:       true,
			FuzzyThreshold:         0.8,
			EnableDatabaseFallback: true,
			BatchSize:              50,
		}
	}

	return c.config.UserMappings.Settings
}

// GetCacheTTL 获取缓存TTL
func (c *ConfigLoader) GetCacheTTL() time.Duration {
	settings := c.GetSettings()
	duration, err := time.ParseDuration(settings.CacheTTL)
	if err != nil {
		return time.Hour // 默认1小时
	}
	return duration
}

// fileExists 检查文件是否存在
func (c *ConfigLoader) fileExists(path string) bool {
	_, err := os.Stat(path)
	return err == nil
}

// updateLastModTime 更新最后修改时间
func (c *ConfigLoader) updateLastModTime() {
	c.lastModTime = time.Now()
}

// validateConfig 验证配置
func (c *ConfigLoader) validateConfig(config *UserMappingConfig) error {
	if config == nil {
		return fmt.Errorf("配置为空")
	}

	// 验证版本
	if config.UserMappings.Version == "" {
		return fmt.Errorf("配置版本不能为空")
	}

	// 验证用户映射
	if len(config.UserMappings.CommonUsers) == 0 {
		return fmt.Errorf("用户映射列表不能为空")
	}

	// 验证每个用户映射
	for i, user := range config.UserMappings.CommonUsers {
		if err := c.validateUserMapping(&user, i); err != nil {
			return err
		}
	}

	// 验证设置
	if err := c.validateSettings(&config.UserMappings.Settings); err != nil {
		return err
	}

	return nil
}

// validateUserMapping 验证用户映射
func (c *ConfigLoader) validateUserMapping(user *UserMapping, index int) error {
	if user.Name == "" {
		return fmt.Errorf("用户映射[%d]: 名称不能为空", index)
	}

	if user.OpenID == "" {
		return fmt.Errorf("用户映射[%d]: OpenID不能为空", index)
	}

	// 验证OpenID格式
	if !c.isValidOpenID(user.OpenID) {
		return fmt.Errorf("用户映射[%d]: OpenID格式无效: %s", index, user.OpenID)
	}

	return nil
}

// validateSettings 验证设置
func (c *ConfigLoader) validateSettings(settings *ConfigSettings) error {
	// 验证缓存TTL
	if settings.CacheTTL != "" {
		if _, err := time.ParseDuration(settings.CacheTTL); err != nil {
			return fmt.Errorf("缓存TTL格式无效: %s", settings.CacheTTL)
		}
	}

	// 验证最大缓存大小
	if settings.MaxCacheSize <= 0 {
		return fmt.Errorf("最大缓存大小必须大于0")
	}

	// 验证模糊匹配阈值
	if settings.FuzzyThreshold < 0 || settings.FuzzyThreshold > 1 {
		return fmt.Errorf("模糊匹配阈值必须在0-1之间")
	}

	// 验证批量大小
	if settings.BatchSize <= 0 {
		return fmt.Errorf("批量大小必须大于0")
	}

	return nil
}

// isValidOpenID 验证OpenID格式
func (c *ConfigLoader) isValidOpenID(openID string) bool {
	// 飞书OpenID格式: ou_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
	if len(openID) != 35 {
		return false
	}

	if openID[:3] != "ou_" {
		return false
	}

	// 检查后32位是否为十六进制
	for _, char := range openID[3:] {
		if !((char >= '0' && char <= '9') || (char >= 'a' && char <= 'f') || (char >= 'A' && char <= 'F')) {
			return false
		}
	}

	return true
}

// GetUserByName 根据名称获取用户
func (c *ConfigLoader) GetUserByName(name string) *UserMapping {
	users := c.GetUserMappings()

	for i := range users {
		user := &users[i]
		if user.Name == name {
			return user
		}

		// 检查别名
		for _, alias := range user.Aliases {
			if alias == name {
				return user
			}
		}
	}

	return nil
}

// GetUserByOpenID 根据OpenID获取用户
func (c *ConfigLoader) GetUserByOpenID(openID string) *UserMapping {
	users := c.GetUserMappings()

	for i := range users {
		user := &users[i]
		if user.OpenID == openID {
			return user
		}
	}

	return nil
}

// GetAllNames 获取所有名称（包括别名）
func (c *ConfigLoader) GetAllNames() []string {
	users := c.GetUserMappings()
	var names []string

	for _, user := range users {
		names = append(names, user.Name)
		names = append(names, user.Aliases...)
	}

	return names
}

// GetConfigSummary 获取配置摘要
func (c *ConfigLoader) GetConfigSummary() map[string]interface{} {
	if c.config == nil {
		return map[string]interface{}{
			"loaded": false,
		}
	}

	return map[string]interface{}{
		"loaded":       true,
		"version":      c.config.UserMappings.Version,
		"last_updated": c.config.UserMappings.LastUpdated,
		"user_count":   len(c.config.UserMappings.CommonUsers),
		"settings":     c.config.UserMappings.Settings,
		"config_path":  c.configPath,
	}
}
