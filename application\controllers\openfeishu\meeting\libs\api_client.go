package libs

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"

	"irisAdminApi/application/logging"
)

// RateLimiter 频率限制器
type RateLimiter struct {
	mu          sync.Mutex
	requests    []time.Time
	maxRequests int           // 最大请求数
	timeWindow  time.Duration // 时间窗口
	lastCleanup time.Time     // 上次清理时间
}

// NewRateLimiter 创建频率限制器
func NewRateLimiter(maxRequests int, timeWindow time.Duration) *RateLimiter {
	return &RateLimiter{
		maxRequests: maxRequests,
		timeWindow:  timeWindow,
		lastCleanup: time.Now(),
	}
}

// Allow 检查是否允许请求
func (rl *RateLimiter) Allow() bool {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	now := time.Now()

	// 清理过期请求记录
	if now.Sub(rl.lastCleanup) > time.Minute {
		rl.cleanup(now)
		rl.lastCleanup = now
	}

	// 检查当前请求数是否超限
	if len(rl.requests) >= rl.maxRequests {
		return false
	}

	// 记录当前请求
	rl.requests = append(rl.requests, now)
	return true
}

// cleanup 清理过期的请求记录
func (rl *RateLimiter) cleanup(now time.Time) {
	cutoff := now.Add(-rl.timeWindow)
	validRequests := make([]time.Time, 0, len(rl.requests))

	for _, reqTime := range rl.requests {
		if reqTime.After(cutoff) {
			validRequests = append(validRequests, reqTime)
		}
	}

	rl.requests = validRequests
}

// GetWaitTime 获取需要等待的时间
func (rl *RateLimiter) GetWaitTime() time.Duration {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	if len(rl.requests) == 0 {
		return 0
	}

	// 找到最早的请求时间
	earliest := rl.requests[0]
	for _, reqTime := range rl.requests {
		if reqTime.Before(earliest) {
			earliest = reqTime
		}
	}

	// 计算需要等待的时间
	waitTime := rl.timeWindow - time.Since(earliest)
	if waitTime < 0 {
		return 0
	}

	return waitTime
}

// APIClient 优化的API客户端
type APIClient struct {
	httpClient   *http.Client
	rateLimiter  *RateLimiter
	errorHandler *FeishuErrorHandler
	maxRetries   int
}

// NewAPIClient 创建API客户端
func NewAPIClient() *APIClient {
	return &APIClient{
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
			Transport: &http.Transport{
				MaxIdleConns:        100,
				MaxIdleConnsPerHost: 10,
				IdleConnTimeout:     90 * time.Second,
			},
		},
		// 飞书API限制：1000次/分钟、50次/秒
		rateLimiter:  NewRateLimiter(45, time.Second), // 留5次余量
		errorHandler: NewFeishuErrorHandler(),
		maxRetries:   3,
	}
}

// handleError 统一错误处理方法
func (c *APIClient) handleError(operation string, err error) error {
	return fmt.Errorf("%s失败: %w", operation, err)
}

// APIRequest API请求结构
type APIRequest struct {
	Method      string
	URL         string
	Headers     map[string]string
	Body        interface{}
	Timeout     time.Duration
	RetryPolicy *RetryPolicy
}

// RetryPolicy 重试策略
type RetryPolicy struct {
	MaxRetries int
	BaseDelay  time.Duration
	MaxDelay   time.Duration
}

// APIResponse API响应结构
type APIResponse struct {
	StatusCode int
	Headers    map[string][]string
	Body       []byte
	RequestID  string
}

// DoRequest 执行API请求
func (c *APIClient) DoRequest(ctx context.Context, req *APIRequest) (*APIResponse, error) {
	// 应用默认值
	if req.Timeout == 0 {
		req.Timeout = 30 * time.Second
	}
	if req.RetryPolicy == nil {
		req.RetryPolicy = &RetryPolicy{
			MaxRetries: c.maxRetries,
			BaseDelay:  time.Second,
			MaxDelay:   30 * time.Second,
		}
	}

	var lastErr error
	for attempt := 0; attempt <= req.RetryPolicy.MaxRetries; attempt++ {
		// 检查频率限制
		if !c.rateLimiter.Allow() {
			waitTime := c.rateLimiter.GetWaitTime()
			select {
			case <-time.After(waitTime):
				// 等待完成，继续请求
			case <-ctx.Done():
				return nil, ctx.Err()
			}
		}

		// 执行请求
		resp, err := c.executeRequest(ctx, req)
		if err == nil {
			return resp, nil
		}

		lastErr = err

		// 检查是否为飞书API错误
		if feishuErr, ok := err.(*FeishuAPIError); ok {
			// 记录详细错误信息
			logging.ErrorLogger.Error(c.errorHandler.FormatErrorForLogging(feishuErr, map[string]interface{}{
				"尝试次数":  attempt + 1,
				"请求URL": req.URL,
				"请求方法":  req.Method,
			}))

			// 检查是否可重试
			if !feishuErr.IsRetryable() {
				return nil, feishuErr
			}

			// 如果是最后一次尝试，不再重试
			if attempt == req.RetryPolicy.MaxRetries {
				return nil, feishuErr
			}

			// 计算重试延迟
			delay := c.errorHandler.GetRetryDelay(feishuErr, attempt)
			logging.InfoLogger.Infof("API请求失败，%v 后重试 (第%d次)", delay, attempt+2)

			select {
			case <-time.After(delay):
				// 延迟完成，继续重试
			case <-ctx.Done():
				return nil, ctx.Err()
			}
		} else {
			// 非飞书API错误，记录并重试
			logging.ErrorLogger.Errorf("API请求失败: %v (第%d次尝试)", err, attempt+1)

			if attempt == req.RetryPolicy.MaxRetries {
				return nil, err
			}

			// 使用指数退避
			delay := req.RetryPolicy.BaseDelay * time.Duration(1<<uint(attempt))
			if delay > req.RetryPolicy.MaxDelay {
				delay = req.RetryPolicy.MaxDelay
			}

			select {
			case <-time.After(delay):
				// 延迟完成，继续重试
			case <-ctx.Done():
				return nil, ctx.Err()
			}
		}
	}

	return nil, lastErr
}

// executeRequest 执行单次请求
func (c *APIClient) executeRequest(ctx context.Context, req *APIRequest) (*APIResponse, error) {
	// 创建HTTP请求上下文
	reqCtx, cancel := context.WithTimeout(ctx, req.Timeout)
	defer cancel()

	// 序列化请求体
	var bodyReader io.Reader
	if req.Body != nil {
		jsonData, err := json.Marshal(req.Body)
		if err != nil {
			return nil, c.handleError("序列化请求体", err)
		}
		bodyReader = bytes.NewBuffer(jsonData)
	}

	// 创建HTTP请求
	httpReq, err := http.NewRequestWithContext(reqCtx, req.Method, req.URL, bodyReader)
	if err != nil {
		return nil, c.handleError("创建HTTP请求", err)
	}

	// 设置请求头
	for key, value := range req.Headers {
		httpReq.Header.Set(key, value)
	}

	// 如果有请求体，设置Content-Type
	if req.Body != nil && httpReq.Header.Get("Content-Type") == "" {
		httpReq.Header.Set("Content-Type", "application/json")
	}

	// 发送请求
	startTime := time.Now()
	httpResp, err := c.httpClient.Do(httpReq)
	duration := time.Since(startTime)

	if err != nil {
		logging.ErrorLogger.Errorf("HTTP请求失败: %v (耗时: %v)", err, duration)
		return nil, c.handleError("HTTP请求", err)
	}
	defer httpResp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %w", err)
	}

	// 记录请求日志
	logging.DebugLogger.Debugf("API请求完成 - 方法: %s, URL: %s, 状态码: %d, 耗时: %v",
		req.Method, req.URL, httpResp.StatusCode, duration)

	// 构建响应对象
	resp := &APIResponse{
		StatusCode: httpResp.StatusCode,
		Headers:    httpResp.Header,
		Body:       body,
		RequestID:  httpResp.Header.Get("X-Request-Id"),
	}

	// 检查HTTP状态码
	if httpResp.StatusCode >= 400 {
		// 尝试解析飞书API错误
		var apiResp struct {
			Code int    `json:"code"`
			Msg  string `json:"msg"`
		}

		if json.Unmarshal(body, &apiResp) == nil && apiResp.Code != 0 {
			feishuErr := c.errorHandler.ParseAPIError(apiResp.Code, apiResp.Msg)
			feishuErr.RequestID = resp.RequestID
			return nil, feishuErr
		}

		return nil, fmt.Errorf("HTTP错误: %d %s", httpResp.StatusCode, string(body))
	}

	return resp, nil
}

// DoJSONRequest 执行JSON请求并解析响应
func (c *APIClient) DoJSONRequest(ctx context.Context, req *APIRequest, result interface{}) error {
	resp, err := c.DoRequest(ctx, req)
	if err != nil {
		return err
	}

	// 解析JSON响应
	if err := json.Unmarshal(resp.Body, result); err != nil {
		return fmt.Errorf("解析JSON响应失败: %w", err)
	}

	return nil
}

// CreateStandardRequest 创建标准的飞书API请求
func (c *APIClient) CreateStandardRequest(method, url, accessToken string, body interface{}) *APIRequest {
	headers := map[string]string{
		"Authorization": "Bearer " + accessToken,
	}

	if body != nil {
		headers["Content-Type"] = "application/json"
	}

	return &APIRequest{
		Method:  method,
		URL:     url,
		Headers: headers,
		Body:    body,
		Timeout: 30 * time.Second,
	}
}
