package dproductionproctask

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/productionrelease"
	"irisAdminApi/service/dao/user/duser"
)

const ModelName = "下生产流程任务"

type Response struct {
	ID uint `json:"id"`
	// 当前执行流所在的节点
	NodeName   string `json:"nodeName"`
	NodeID     string `json:"nodeId"`
	PrevNodeID string `json:"prevNodeID"`
	// Step   int    `json:"step"`
	// 流程实例id
	ProcInstID uint `json:"procInstID"`
	Assignee   uint `json:"assignee"`
	// 还未审批的用户数，等于0代表会签已经全部审批结束，默认值为1
	// MemberCount   int8 `json:"memberCount" gorm:"default:1"`
	// UnCompleteNum int8 `json:"unCompleteNum" gorm:"default:1"`
	//审批通过数
	// AgreeNum int8 `json:"agreeNum"`
	// and 为会签，or为或签，默认为or
	// ActType    string `json:"actType" gorm:"default:'or'"`
	Status              uint                    `gorm:"default:0" json:"status"`
	User                *duser.ApprovalResponse `gorm:"-" json:"user"`
	Flag                bool                    `json:"flag"`
	Attachment          string                  `json:"attachment"`
	CreatedAt           string                  `json:"created_at"`
	OverTimeNotice      bool                    `json:"over_time_notice"`
	Uboot               string                  `json:"uboot"`
	UbootMd5            string                  `json:"uboot_md5"`
	UbootDoc            string                  `json:"uboot_doc"`
	UbootVersion        string                  `json:"uboot_version"`
	SetmacTool          string                  `json:"setmac_tool"`
	SetmacToolMd5       string                  `json:"setmac_tool_md5"`
	RomProgram          string                  `json:"rom_program"`
	RomProgramMd5       string                  `json:"rom_program_md5"`
	SetmacIni           string                  `json:"setmac_ini"`
	SetmacIniMd5        string                  `json:"setmac_ini_md5"`
	SetmacDoc           string                  `json:"setmac_doc"`
	ManufTestProgram    string                  `json:"manuf_test_program"`
	ManufTestProgramMD5 string                  `json:"manuf_test_program_md5"`
	ManufTestProgramDoc string                  `json:"manuf_test_program_doc"`
	ManufTestReport     string                  `json:"manuf_test_report"`
	CpldUpdateUrl       string                  `json:"cpld_update_url"`
	CpldUpdate          bool                    `json:"cpld_update"`
	Done                bool                    `json:"done"`
}

type ListResponse struct {
	Response
}

type Request struct {
	// 当前执行流所在的节点
	NodeName string `json:"nodeName"`
	NodeID   string `json:"nodeId"`
	// Step   int    `json:"step"`
	// 流程实例id
	ProcInstID int  `json:"procInstID"`
	Assignee   uint `json:"assignee"`
	// 还未审批的用户数，等于0代表会签已经全部审批结束，默认值为1
	// MemberCount   int8 `json:"memberCount" gorm:"default:1"`
	// UnCompleteNum int8 `json:"unCompleteNum" gorm:"default:1"`
	//审批通过数
	// AgreeNum int8 `json:"agreeNum"`
	// and 为会签，or为或签，默认为or
	// ActType    string `json:"actType" gorm:"default:'or'"`
	Comment string `json:"comment"`
	Status  uint   `gorm:"default:0" json:"status"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *productionrelease.ProductionProcTask {
	return &productionrelease.ProductionProcTask{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	FormatResponse(items)
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindAll() ([]*Response, error) {
	var items []*Response

	if err := easygorm.GetEasyGormDb().Model(Model()).Find(&items).Error; err != nil {
		logging.ErrorLogger.Errorf("get all status err ", err)
		return items, err
	}
	return items, nil
}

func FindInIds(ids []uint) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id in ?", ids).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	return items, nil
}

func DeleteByUUID(uuid string) error {
	err := easygorm.GetEasyGormDb().Unscoped().Where("uuid = ?", uuid).Delete(Model()).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func UpdateStatus(id uint, object map[string]interface{}) error {

	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func FormatResponse(items []*ListResponse) {
	userIds := []uint{}
	for _, item := range items {
		userIds = append(userIds, item.Assignee)
	}
	users, _ := duser.FindSimpleInIds(userIds)
	var userMap = make(map[uint]*duser.ApprovalResponse)
	for _, user := range users {
		userMap[user.Id] = user
	}
	for _, item := range items {
		item.User = userMap[item.Assignee]
	}
}

func FindInProcInstIDs(procInstIDs []uint) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("proc_inst_id in ? and status = 0", procInstIDs).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	FormatResponse(items)
	return items, nil
}

func FindInProcInstIDs2(procInstIDs []uint) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("proc_inst_id in ? and flag = 1 and done =1 and status not in(3) ", procInstIDs).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	FormatResponse(items)
	return items, nil
}

func FindInProcInstIDs3(procInstIDs []uint) ([]*ListResponse, error) {
	var items []*ListResponse
	err := easygorm.GetEasyGormDb().Model(Model()).Where("proc_inst_id in ? and flag = 1 and done =1 and status = 1 ", procInstIDs).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	FormatResponse(items)
	return items, nil
}

func FindAttachmentByProc(procInstID uint, nodeID string) (string, error) {
	var item Response
	err := easygorm.GetEasyGormDb().Model(Model()).Where("proc_inst_id = ? and status = 1 and flag = 1 and node_id = ?", procInstID, nodeID).Find(&item).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return "", err
	}
	return item.Attachment, nil
}

var ProductionTasksProccessingCache []*ListResponse

func UpdateFeatureTasksProccessingCache() error {
	if err := easygorm.GetEasyGormDb().Model(Model()).Where("flag =1 and status = 0").Find(&ProductionTasksProccessingCache).Error; err != nil {
		return err
	}
	return nil
}

func FindByProcInstID(procInstID uint) ([]*Response, error) {
	var items []*Response
	err := easygorm.GetEasyGormDb().Model(Model()).Where("proc_inst_id = ? and status = 1", procInstID).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return nil, err
	}
	return items, nil
}
