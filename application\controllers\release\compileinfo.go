package release

import (
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/buildfarm/dcronmakejob"
	"irisAdminApi/service/dao/buildfarm/dgitjob"
	"irisAdminApi/service/dao/buildfarm/dmakejob"
	"irisAdminApi/service/dao/release/dbuildtime"
	"irisAdminApi/service/dao/release/dcomplieinfo"
	"irisAdminApi/service/dao/release/drelease"
	"strconv"
	"strings"

	"github.com/kataras/iris/v12"
)

func GetCompileInfos(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dcomplieinfo.ListResponse{}, ctx, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetCompileInfo(ctx iris.Context) {
	release_id, _ := dao.GetId(ctx)
	release := drelease.ReleaseRelease{}
	err := release.Find(release_id)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	buildTime := dbuildtime.Response{}
	err = buildTime.FindEx("release_id", strconv.FormatUint(uint64(release_id), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	jobId := strings.Split(release.BuildFarmLink, "/")[4]
	makejob, err := dmakejob.FindMakeJobByTaskId(jobId)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	cronMakeJob, err := dcronmakejob.FindCronMakeJobByTaskId(jobId)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	var program, branch, product string
	if makejob.ID > 0 {
		gitjob := dgitjob.GitJob{}
		err := gitjob.FindEx("job_id", makejob.JobID)
		if err != nil {
			logging.ErrorLogger.Errorf("get user get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		program = gitjob.Project
		branch = gitjob.Branch
		product = makejob.Product
	}

	if cronMakeJob.Id > 0 {
		program = cronMakeJob.Project
		branch = cronMakeJob.Branch
		product = cronMakeJob.Product
	}
	infos, err := dcomplieinfo.FindByReleaseID(release_id)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	object := map[string]interface{}{
		"project":    program,
		"branch":     branch,
		"product":    product,
		"build_time": buildTime.BuildTime,
		"items":      infos,
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, object, response.NoErr.Msg))
	return
}

func GetReleaseVersion(ctx iris.Context) {
	name := ctx.FormValue("name")
	branch := ctx.FormValue("branch")
	if name == "" {
		ctx.JSON(response.NewResponse(response.NoErr.Code, []*dcomplieinfo.ListResponse{}, response.NoErr.Msg))
		return
	}
	var items []*dcomplieinfo.ListResponse
	var err error
	if branch == "" {
		items, err = dcomplieinfo.FindByUnit(name)
	} else {
		items, err = dcomplieinfo.FindByUnitAndBranch(name, branch)
	}

	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, items, response.NoErr.Msg))
	return
}
