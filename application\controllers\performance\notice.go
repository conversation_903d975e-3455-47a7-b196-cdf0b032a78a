package performance

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/performance/dperformance"
	"irisAdminApi/service/dao/release/dproject"
	"os"
	"time"

	"github.com/chromedp/chromedp"
)

func DailyPerformanceCheck(distinctProductBranch []*dperformance.ListResponse) (map[string][]string, error) {
	msg := "检测到%s %s %v 指标%s当前值为: %v, 上一次为: %v, 下降超过 %s"

	result := map[string][]string{}

	columns := dperformance.GetColumns()

	for _, item := range distinctProductBranch {
		performances, err := dperformance.FindDailyCheck(item.Product, item.Branch)
		if err != nil {
			return result, err
		}
		if len(performances) < 2 {
			continue
		}
		result[performances[0].Branch] = []string{}
		for _, column := range columns {
			// 倒序获取  0为最新数据, 1为上一次数据
			_perf0 := libs.StructToString(performances[1])
			_perf1 := libs.StructToString(performances[0])
			perf0 := map[string]interface{}{}
			perf1 := map[string]interface{}{}
			err := json.Unmarshal([]byte(_perf0), &perf0)
			if err != nil {
				return nil, err
			}
			err = json.Unmarshal([]byte(_perf1), &perf1)
			if err != nil {
				return nil, err
			}

			metric0, ok0 := perf0[column]
			metric1, ok1 := perf1[column]
			if ok0 && ok1 && metric0.(float64) > 0 {
				delta := float32((metric1.(float64) - metric0.(float64)) / metric0.(float64))
				if delta < -0.05 {
					result[performances[1].Branch] = append(result[performances[0].Branch], fmt.Sprintf(msg, performances[0].Branch, performances[0].Product, performances[0].Version, column, metric1, metric0, "5%"))
				}
			}
		}
	}
	return result, nil
}

func BaselinePerformanceCheck(distinctProductBranch []*dperformance.ListResponse) (map[string][]string, error) {
	msg := "检测到%s %s %v 指标%s当前值为: %v, 基线为: %v, 下降超过 %s"

	result := map[string][]string{}
	distinctProductBranch, err := dperformance.FindDistinctProductAndBranch()
	if err != nil {
		return result, err
	}
	columns := dperformance.GetColumns()

	for _, item := range distinctProductBranch {
		performances, err := dperformance.FindBaseLineCheck(item.Product, item.Branch)
		if err != nil {
			return result, err
		}
		if len(performances) < 2 {
			continue
		}
		result[performances[1].Branch] = []string{}
		for _, column := range columns {

			_perf0 := libs.StructToString(performances[0])
			_perf1 := libs.StructToString(performances[1])
			perf0 := map[string]interface{}{}
			perf1 := map[string]interface{}{}
			err := json.Unmarshal([]byte(_perf0), &perf0)
			if err != nil {
				return nil, err
			}
			err = json.Unmarshal([]byte(_perf1), &perf1)
			if err != nil {
				return nil, err
			}

			metric0, ok0 := perf0[column]
			metric1, ok1 := perf1[column]
			if ok0 && ok1 && metric0.(float64) > 0 {
				delta := float32((metric1.(float64) - metric0.(float64)) / metric0.(float64))
				if delta < -0.05 {
					result[performances[1].Branch] = append(result[performances[1].Branch], fmt.Sprintf(msg, performances[0].Branch, performances[0].Product, performances[0].Version, column, metric1, metric0, "5%"))
				}
			}
		}
	}

	return result, nil
}

func WeeklyPerformanceCheck(distinctProductBranch []*dperformance.ListResponse) (map[string][]string, error) {
	msg := "检测到%s %s %v 指标%s本周平均值为: %v, 上周平均值为: %v, 下降超过 %s"

	result := map[string][]string{}
	columns := dperformance.GetColumns()

	for _, item := range distinctProductBranch {

		performances, err := dperformance.FindWeeklyCheck(item.Product, item.Branch)
		if err != nil {
			return result, err
		}
		if len(performances) < 2 {
			continue
		}
		last := performances[2]
		result[last.Branch] = []string{}
		for _, column := range columns {

			_perf0 := libs.StructToString(performances[1])
			_perf1 := libs.StructToString(performances[0])
			perf0 := map[string]interface{}{}
			perf1 := map[string]interface{}{}
			err := json.Unmarshal([]byte(_perf0), &perf0)
			if err != nil {
				return nil, err
			}
			err = json.Unmarshal([]byte(_perf1), &perf1)
			if err != nil {
				return nil, err
			}

			metric0, ok0 := perf0[column]
			metric1, ok1 := perf1[column]
			if ok0 && ok1 && metric0.(float64) > 0 && metric1.(float64) > 0 {
				delta := float32((metric1.(float64) - metric0.(float64)) / metric0.(float64))
				if delta < -0.05 {
					result[last.Branch] = append(result[last.Branch], fmt.Sprintf(msg, last.Branch, last.Product, last.Version, column, metric1, metric0, "5%"))
				}
			}
		}
	}

	return result, nil
}

func fullScreenshot(urlstr string, quality int, res *[]byte) chromedp.Tasks {
	return chromedp.Tasks{
		chromedp.Navigate(urlstr),
		chromedp.Sleep(10 * time.Second),
		chromedp.FullScreenshot(res, quality),
	}
}

func GenerateScreenshot(product, branch string) error {
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.DisableGPU,
		chromedp.WindowSize(1920, 1080),
	)
	allocCtx, cancel := chromedp.NewExecAllocator(context.Background(), opts...)
	defer cancel()
	ctx, cancel := chromedp.NewContext(
		allocCtx,
		// chromedp.WithDebugf(log.Printf),
	)
	defer cancel()

	// capture screenshot of an element
	var buf []byte

	// capture entire browser viewport, returning png with quality=90
	if err := chromedp.Run(ctx, fullScreenshot(fmt.Sprintf(`http://10.51.135.15:9090/grafana/d/YY96ubRSk/ntosxing-neng-qu-xian?orgId=1&var-branch=%s&var-product=%s`, branch, product), 90, &buf)); err != nil {
		return err
	}
	if err := os.WriteFile(fmt.Sprintf("/tmp/%s_%s_screenshot.png", branch, product), buf, 0o644); err != nil {
		return err
	}
	return nil
}

func SendWeekReport() error {

	distinctProductBranch, err := dperformance.FindDistinctProductAndBranch()
	if err != nil {
		logging.ErrorLogger.Error(err)
		return err
	}
	names := []string{}
	for _, item := range distinctProductBranch {
		names = append(names, item.Branch)
	}
	releaseProjects, err := dproject.GetProjectsByName(names...)
	if err != nil {
		logging.ErrorLogger.Error(err)
		return err
	}

	mailTo := []string{"<EMAIL>", "<EMAIL>"}
	cc := []string{}
	mailToMap := map[string][]string{}
	for _, project := range releaseProjects {
		mailToMap[project.Name] = []string{project.Pgttl.Username + "@ruijie.com.cn", project.Ptm.Username + "@ruijie.com.cn", project.Pm.Username + "@ruijie.com.cn", project.Pmo.Username + "@ruijie.com.cn"}
		mailToMap[project.Name] = append(mailToMap[project.Name], mailTo...)
	}
	results := map[string]string{}
	for _, item := range distinctProductBranch {
		imgs := []string{}
		err := GenerateScreenshot(item.Product, item.Branch)
		if err != nil {
			return err
		}
		out, err := ImageToBase64(fmt.Sprintf("/tmp/%s_%s_screenshot.png", item.Branch, item.Product))
		if err != nil {
			return err
		}
		imgs = append(imgs, out)

		for _, img := range imgs {
			results[item.Branch] = fmt.Sprintf("%s<img src=\"data:image/png;base64,%s\"/>", results[item.Branch], img)
		}

		results[item.Branch] = fmt.Sprintf(`
			%s
			<p>详情查看：<a href="http://aqyfzx.ruijie.net:9090/grafana/d/YY96ubRSk/ntosxing-neng-qu-xian?orgId=1&var-branch=%s&var-product=%s">性能曲线面板</a></p>
			`,
			results[item.Branch],
			item.Branch,
			item.Product,
		)
	}

	for key, value := range results {
		err = libs.SendMail(mailToMap[key], fmt.Sprintf("%s性能曲线周报", key), value, cc)
		if err != nil {
			return err
		}
	}

	return nil
}

func ImageToBase64(fp string) (string, error) {
	img, err := ioutil.ReadFile(fp)
	if err != nil {
		// check errors
		return "", err
	}
	out := base64.StdEncoding.EncodeToString(img)
	return out, nil
}

func SendMail() {
	distinctProductBranch, err := dperformance.FindDistinctProductAndBranch()
	if err != nil {
		logging.ErrorLogger.Error(err)
		return
	}
	ret1, err := DailyPerformanceCheck(distinctProductBranch)
	if err != nil {
		logging.ErrorLogger.Error(err)
		return
	}
	ret2, err := BaselinePerformanceCheck(distinctProductBranch)
	if err != nil {
		logging.ErrorLogger.Error(err)
		return
	}
	ret3, err := WeeklyPerformanceCheck(distinctProductBranch)
	if err != nil {
		logging.ErrorLogger.Error(err)
		return
	}
	names := []string{}
	for _, item := range distinctProductBranch {
		names = append(names, item.Branch)
	}

	releaseProjects, err := dproject.GetProjectsByName(names...)
	if err != nil {
		logging.ErrorLogger.Error(err)
		return
	}

	mailTo := []string{}
	cc := []string{}
	for _, project := range releaseProjects {
		msgs := []string{}
		cc = []string{
			project.Ptm.Username + "@ruijie.com.cn",
			project.Pm.Username + "@ruijie.com.cn",
			project.Pmo.Username + "@ruijie.com.cn",
			"<EMAIL>",
			"<EMAIL>",
			"<EMAIL>",
			"<EMAIL>",
			"<EMAIL>",
			"<EMAIL>",
			"<EMAIL>",
			"<EMAIL>",
			"<EMAIL>",
			"<EMAIL>",
			"<EMAIL>",
			"<EMAIL>",
		}

		_mailTo := []string{project.Pgttl.Username + "@ruijie.com.cn"}
		_mailTo = append(_mailTo, mailTo...)
		if _, ok := ret1[project.Name]; ok && len(ret1[project.Name]) > 0 {
			msgs = append(msgs, ret1[project.Name]...)
		}

		if _, ok := ret2[project.Name]; ok && len(ret2[project.Name]) > 0 {
			msgs = append(msgs, ret2[project.Name]...)
		}

		if _, ok := ret3[project.Name]; ok && len(ret3[project.Name]) > 0 {
			msgs = append(msgs, ret3[project.Name]...)
		}

		if len(msgs) > 0 {
			body := fmt.Sprintf(`<p>项目：%s, 有如下性能监控告警：</p>`, project.Name)
			for _, msg := range msgs {
				body = fmt.Sprintf("%s<p>%s</p>", body, msg)
			}
			body = fmt.Sprintf(`%s<p>详情查看：<a href="http://aqyfzx.ruijie.net:9090/grafana/d/YY96ubRSk/ntosxing-neng-qu-xian?orgId=1&var-branch=%s">性能曲线面板</a></p>`, body, project.Name)
			subject := fmt.Sprintf("%s性能告警", project.Name)

			err := libs.SendMail(_mailTo, subject, body, cc)
			if err != nil {
				logging.ErrorLogger.Error(err)
				continue
			}
			// todo: 标记已告警信息
		}
	}
}
