package buildfarm

import (
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/buildfarm/dcronmakejob"
	"irisAdminApi/service/dao/buildfarm/dmakejob"
	"strings"

	"github.com/kataras/iris/v12"
)

type SmokeStatusReq struct {
	SmokeStatus string `json:"smoke_status" form:"smoke_status"`
}

func UpdateMakeJobSmokeStatus(ctx iris.Context) {
	smokeStatusReq := SmokeStatusReq{}
	if err := ctx.ReadJSON(&smokeStatusReq); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(smokeStatusReq)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	if err := dao.Update(&dmakejob.MakeJob{}, ctx, map[string]interface{}{"smoke_status": smokeStatusReq.SmokeStatus}); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func UpdateCronMakeJobSmokeStatus(ctx iris.Context) {
	smokeStatusReq := SmokeStatusReq{}
	if err := ctx.ReadJSON(&smokeStatusReq); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(smokeStatusReq)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	if err := dao.Update(&dcronmakejob.CronMakeJob{}, ctx, map[string]interface{}{"smoke_status": smokeStatusReq.SmokeStatus}); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetMakeJobSmokeStatus(ctx iris.Context) {
	job := dmakejob.MakeJob{}
	if err := dao.Find(&job, ctx); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]interface{}{"smoke_status": job.SmokeStatus}, response.NoErr.Msg))
	return
}

func GetCronMakeJobSmokeStatus(ctx iris.Context) {
	job := dcronmakejob.CronMakeJob{}
	if err := dao.Find(&job, ctx); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]interface{}{"smoke_status": job.SmokeStatus}, response.NoErr.Msg))
	return
}
