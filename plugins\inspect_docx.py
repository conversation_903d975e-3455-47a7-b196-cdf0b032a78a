import sys
from docx import Document
import logging

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,  # 设置日志级别为DEBUG
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("inspect_docx.log", encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

def inspect_paragraphs(paragraphs, level=0):
    for idx, para in enumerate(paragraphs, start=1):
        indent = '  ' * level
        logging.debug(f"{indent}段落 {idx}: {para.text}")

def inspect_tables(tables, level=0):
    for table_idx, table in enumerate(tables, start=1):
        indent = '  ' * level
        logging.debug(f"{indent}表格 {table_idx}:")
        for row_idx, row in enumerate(table.rows, start=1):
            row_text = [cell.text.strip() for cell in row.cells]
            logging.debug(f"{indent}  行 {row_idx}: {' | '.join(row_text)}")
        # 递归检查嵌套表格
        for row in table.rows:
            for cell in row.cells:
                if cell.tables:
                    inspect_tables(cell.tables, level + 1)
                if cell.paragraphs:
                    inspect_paragraphs(cell.paragraphs, level + 1)

def inspect_headers_footers(headers_footers, level=0):
    for hf_type, hf in headers_footers:
        indent = '  ' * level
        logging.debug(f"{indent}{hf_type}:")
        if hf.paragraphs:
            inspect_paragraphs(hf.paragraphs, level + 1)
        if hf.tables:
            inspect_tables(hf.tables, level + 1)

def inspect_shapes(shapes, level=0):
    for shape in shapes:
        if hasattr(shape, "text_frame"):
            indent = '  ' * level
            logging.debug(f"{indent}形状（文本框）:")
            text_frame = shape.text_frame
            if text_frame.paragraphs:
                inspect_paragraphs(text_frame.paragraphs, level + 1)
            if text_frame.tables:
                inspect_tables(text_frame.tables, level + 1)

def inspect_docx(file_path):
    logging.info(f"打开文档: {file_path}")
    doc = Document(file_path)

    # 检查所有段落
    logging.info("开始遍历段落...")
    inspect_paragraphs(doc.paragraphs)

    # 检查所有表格
    logging.info("开始遍历表格...")
    inspect_tables(doc.tables)

    # 检查页眉和页脚中的段落和表格
    logging.info("开始遍历页眉和页脚...")
    for section_idx, section in enumerate(doc.sections, start=1):
        headers = [
            ('页眉', section.header),
            ('首页页眉', section.first_page_header),
            ('偶数页页眉', section.even_page_header)
        ]
        footers = [
            ('页脚', section.footer),
            ('首页页脚', section.first_page_footer),
            ('偶数页页脚', section.even_page_footer)
        ]
        inspect_headers_footers(headers + footers, level=1)

    # 检查文档中的形状（如文本框）
    logging.info("开始遍历形状中的内容...")
    if hasattr(doc, "inline_shapes"):
        inspect_shapes(doc.inline_shapes, level=1)
    if hasattr(doc, "shapes"):
        inspect_shapes(doc.shapes, level=1)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        logging.error("用法: python inspect_docx.py <输入文件>")
        sys.exit(1)

    input_file = sys.argv[1]
    inspect_docx(input_file)