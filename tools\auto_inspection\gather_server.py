import subprocess
import utils
import sys
import os

job_id = sys.argv[1]
if not job_id:
    job_id = "unkown"

hostname = subprocess.getoutput("hostname")
ip = subprocess.getoutput(
    "ip a|grep 'inet 10'|grep -v inet6|grep -v 192|grep -v 127.0.0.1|awk '{print $2}'|awk -F '/' '{print $1}'"
)
ips = ip.split()
if len(ips) > 0:
    ip = ips[0]
log_path = f"/tmp/server_{hostname}_{ip}_{utils.get_now_str()}.log"
print(hostname)
print(ip)
print(utils.get_now_str())


with open(log_path, "w", encoding="utf8") as f:
    for _id, command in utils.common_commands.items():
        name = command.get("name")
        cmd = command.get("command")
        if name and cmd:
            f.write(f"=============== start {_id} {name} ===================\n")
            ret = subprocess.getoutput(cmd)
            f.write(ret)
            f.write("\n")
            f.write(f"=============== end {_id} {name} ===================\n")
        else:
            f.write(f"=============== start {_id} ===================\n")
            f.write("采集配置异常，无法采集。")
            f.write("\n")
            f.write(f"=============== end {_id} ===================\n")
# print(ret)

utils.upload_file(log_path, data={"hostname": hostname, "ip": ip, "job_id": job_id})
os.remove(log_path)
