package productionrelease

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/buildfarm"
	"irisAdminApi/application/models/productionrelease"
	"irisAdminApi/service/dao/buildfarm/dcronmakejob"
	"irisAdminApi/service/dao/buildfarm/dsoftversions"
	"irisAdminApi/service/dao/productionrelease/dproductionproductmodel"
	"irisAdminApi/service/dao/productionrelease/dproductionseccloud"
	"net/http"
	"regexp"
	"strings"

	"github.com/kataras/iris/v12"
)

func GetProductModelsFromSecCloud(ctx iris.Context) {

	// if libs.Config.ProductionFileStorage.Url == "" {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置云平台地址，请联系管理员"))
	// 	return
	// }
	url := ctx.FormValue("url")
	if url == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "请先选择要发布的安全云"))
		return
	}
	url = fmt.Sprintf("%s/api/featureManager/queryAllSoftVersion", url)
	req, err := http.NewRequest("POST", url, nil)
	req.Header.Add("iamfeign", "1")

	if err != nil {
		logging.ErrorLogger.Errorf("create reuqest for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	result, err := libs.HandlerRequest(req)
	if err != nil {
		logging.ErrorLogger.Errorf("handle reuqest for get git projects err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if result["success"].(bool) {
		ctx.JSON(response.NewResponse(response.NoErr.Code, result["result"], response.NoErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("未知错误: %s", result)))
	return
}

func GetProductModelsByMainProgram(ctx iris.Context) {

	// if libs.Config.ProductionFileStorage.Url == "" {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未配置云平台地址，请联系管理员"))
	// 	return
	// }
	mainProgramUrl := ctx.FormValue("main_program_url")
	if mainProgramUrl == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "请输入主程序和ospkg-install链接"))
		return
	}
	//获取主程序编译版本号
	re := regexp.MustCompile(`/output/([a-f0-9]{32})/`)
	matches := re.FindStringSubmatch(mainProgramUrl)
	mainProgramJobID := ""
	if len(matches) > 1 {
		mainProgramJobID = matches[1]
	}

	if mainProgramJobID == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "主程序和ospkg-install链接不正确"))
		return
	}
	//获取每日编译记录
	cronMakeJob := &dcronmakejob.CronMakeJob{}
	if mainProgramJobID != "" {
		err := easygorm.GetEasyGormDb().Model(&buildfarm.CronMakeJob{}).Where("job_id = ?", mainProgramJobID).Find(&cronMakeJob).Error
		if err != nil {
			logging.ErrorLogger.Errorf("create job get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	}
	//返回Product数据
	if cronMakeJob.Product == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未找到产品"))
		return
	}
	var productModelList []*dproductionproductmodel.Response
	err := easygorm.GetEasyGormDb().Model(&productionrelease.ProductionProductModel{}).Where("name =?", cronMakeJob.Product).Find(&productModelList).Error
	if err != nil {
		logging.ErrorLogger.Errorf("productmodel get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	items := []map[string]interface{}{}
	having := []string{}
	for _, _item := range productModelList {
		if !libs.InArrayS(having, _item.ProductModel) {
			items = append(items, map[string]interface{}{
				"product_model": _item.ProductModel,
				"value":         _item.ProductModel,
			})
			having = append(having, _item.ProductModel)
		}
	}
	if len(productModelList) > 1 {
		if !libs.InArrayS(having, cronMakeJob.Product+"系列") {
			items = append(items, map[string]interface{}{
				"product_model": cronMakeJob.Product + "系列",
				"value":         cronMakeJob.Product,
			})
			having = append(having, cronMakeJob.Product)
		}
	}
	data := map[string]interface{}{"items": items}
	ctx.JSON(response.NewResponse(response.NoErr.Code, data, response.NoErr.Msg))
	return
}

func RefreshSoftVersionsFromSecCloud() {
	secClouds, err := dproductionseccloud.All()
	if err != nil {
		logging.ErrorLogger.Errorf("get secclouds err", err)
	}
	softVersions := []map[string]interface{}{}
	his := []string{}
	for _, secCloud := range secClouds {
		softVersionsResponse, err := SecCloudClient.GetSoftVersions(secCloud.Url)
		if err != nil {
			logging.ErrorLogger.Errorf("get seccloud softversion err", err, secCloud.Url)
			continue
		}
		for _, item := range softVersionsResponse.Data {
			if !libs.InArrayS(his, item+secCloud.Type) {
				softVersions = append(softVersions, map[string]interface{}{
					"SoftVersion": item,
					"Type":        secCloud.Type,
				})
				his = append(his, item+secCloud.Type)
			}
		}
	}

	err = dsoftversions.BatchCreate(softVersions)
	if err != nil {
		logging.ErrorLogger.Errorf("batch create  soft versions err", err)
	}
}

/**
 * 获取所有可用产品型号，不依赖于主程序链接
 */
func GetAllProductModels(ctx iris.Context) {
	var productModelList []*dproductionproductmodel.Response
	err := easygorm.GetEasyGormDb().Model(&productionrelease.ProductionProductModel{}).Find(&productModelList).Error
	if err != nil {
		logging.ErrorLogger.Errorf("获取所有产品型号失败 ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	items := []map[string]interface{}{}
	having := []string{}

	// 提取产品型号名称，用于生成系列选项
	productModels := []string{}

	// 添加产品型号
	for _, _item := range productModelList {
		if !libs.InArrayS(having, _item.ProductModel) {
			items = append(items, map[string]interface{}{
				"product_model": _item.ProductModel,
				"value":         _item.ProductModel,
			})
			having = append(having, _item.ProductModel)

			// 记录产品型号名称
			if !libs.InArrayS(productModels, _item.Name) {
				productModels = append(productModels, _item.Name)
			}
		}
	}

	// 添加"系列"选项，为每个产品型号添加对应的系列选项
	for _, productModel := range productModels {
		if !libs.InArrayS(having, productModel+"系列") {
			items = append(items, map[string]interface{}{
				"product_model": productModel + "系列",
				"value":         productModel,
			})
			having = append(having, productModel+"系列")
		}
	}

	data := map[string]interface{}{"items": items}
	ctx.JSON(response.NewResponse(response.NoErr.Code, data, response.NoErr.Msg))
	return
}

/**
 * 根据发布类型获取产品型号
 */
func GetProductModelsByType(ctx iris.Context) {
	typeIDStr := ctx.FormValue("type_id")
	if typeIDStr == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "请先选择发布类型"))
		return
	}

	// 获取发布类型对应的产品型号列表
	// 首先从发布类型定义中获取产品型号信息
	procDef := &productionrelease.ProductionProcDef{}
	err := easygorm.GetEasyGormDb().Model(&productionrelease.ProductionProcDef{}).Where("id = ?", typeIDStr).Find(procDef).Error
	if err != nil {
		logging.ErrorLogger.Errorf("获取发布类型失败 ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	var productModels []string
	if procDef.ProductModels != "" {
		productModels = strings.Split(procDef.ProductModels, ",")
	}

	// 如果发布类型中没有定义产品型号，则获取所有产品型号
	if len(productModels) == 0 {
		GetAllProductModels(ctx)
		return
	}

	// 根据产品型号列表从数据库中获取详细信息
	var productModelList []*dproductionproductmodel.Response
	err = easygorm.GetEasyGormDb().Model(&productionrelease.ProductionProductModel{}).
		Where("name IN (?)", productModels).
		Find(&productModelList).Error

	if err != nil {
		logging.ErrorLogger.Errorf("根据发布类型获取产品型号失败 ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	items := []map[string]interface{}{}
	having := []string{}
	for _, _item := range productModelList {
		if !libs.InArrayS(having, _item.ProductModel) {
			items = append(items, map[string]interface{}{
				"product_model": _item.ProductModel,
				"value":         _item.ProductModel,
			})
			having = append(having, _item.ProductModel)
		}
	}

	// 添加"系列"选项，如果有多个同名产品
	for _, productModel := range productModels {
		if !libs.InArrayS(having, productModel+"系列") {
			items = append(items, map[string]interface{}{
				"product_model": productModel + "系列",
				"value":         productModel,
			})
			having = append(having, productModel)
		}
	}

	data := map[string]interface{}{"items": items}
	ctx.JSON(response.NewResponse(response.NoErr.Code, data, response.NoErr.Msg))
	return
}
