package resourcepool

import (
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/resourcepool/dresourcepoolresource"
	"irisAdminApi/service/dao/user/duser"

	"github.com/kataras/iris/v12"
)

func isAdminUser(userId uint) bool {
	user := &duser.User{ID: userId}
	duser.GetUserRoles(user)
	for _, role := range user.Roles {
		if role == "设备系统管理员" {
			return true
		}
	}

	return false
}

func checkPermissionCtx(ctx iris.Context, resource *dresourcepoolresource.ResourcePoolResource) (bool, error) {
	userId, err := dao.GetAuthId(ctx)
	if err != nil {
		return false, err
	}
	user := duser.User{}
	err = user.Find(userId)
	if err != nil {
		return false, err
	}
	err = duser.GetUserRoles(&user)
	if err != nil {
		return false, err
	}
	for _, role := range user.Roles {
		if role == "设备系统管理员" {
			return true, nil
		}
	}
	if resource.Reserved == user.Username {
		return true, nil
	}
	return false, nil
}
