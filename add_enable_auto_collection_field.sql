-- 添加EnableAutoCollection字段到patch_jobs表
ALTER TABLE `patch_jobs` ADD COLUMN `enable_auto_collection` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否自动创建集合补丁';

-- 更新已有的使用Crypt字段标记auto_collection的记录
UPDATE `patch_jobs` SET `enable_auto_collection` = 1 WHERE `crypt` = 'auto_collection';

-- 注释
-- 此迁移脚本添加了一个新的布尔字段enable_auto_collection到patch_jobs表，
-- 用于标识是否需要自动创建集合补丁。默认值为0（false）。
-- 同时，脚本会将已有的使用Crypt字段标记为'auto_collection'的记录更新为使用新字段。 