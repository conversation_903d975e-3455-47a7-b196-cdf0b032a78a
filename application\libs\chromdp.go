package libs

import (
	"context"
	"encoding/base64"
	"io/ioutil"
	"os"
	"time"

	"github.com/chromedp/chromedp"
)

func fullScreenshot(urlstr string, quality int, res *[]byte) chromedp.Tasks {
	return chromedp.Tasks{
		chromedp.Navigate(urlstr),
		chromedp.Sleep(10 * time.Second),
		// chromedp.ActionFunc(func(ctx context.Context) error {
		// 	// 执行 JS 脚本以滚动到页面底部
		// 	err := chromedp.Evaluate(`window.scrollTo(0, document.body.scrollHeight)`, nil).Do(ctx)
		// 	return err
		// }),
		// chromedp.Sleep(10 * time.Second),
		chromedp.FullScreenshot(res, quality),
	}
}

func GenerateScreenshot(url, fp string) error {
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.DisableGPU,
		chromedp.WindowSize(1920, 3080),
	)
	allocCtx, cancel := chromedp.NewExecAllocator(context.Background(), opts...)
	defer cancel()
	ctx, cancel := chromedp.NewContext(
		allocCtx,
		// chromedp.WithDebugf(log.Printf),
	)
	defer cancel()

	// capture screenshot of an element
	var buf []byte

	// capture entire browser viewport, returning png with quality=90
	if err := chromedp.Run(ctx, fullScreenshot(url, 90, &buf)); err != nil {
		return err
	}
	if err := os.WriteFile(fp, buf, 0o644); err != nil {
		return err
	}
	return nil
}

func ImageToBase64(fp string) (string, error) {
	img, err := ioutil.ReadFile(fp)
	if err != nil {
		// check errors
		return "", err
	}
	out := base64.StdEncoding.EncodeToString(img)
	return out, nil
}
