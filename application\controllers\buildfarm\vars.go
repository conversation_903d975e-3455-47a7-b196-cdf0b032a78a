package buildfarm

import (
	"sync"
)

type serverCount struct {
	ServerId     uint
	Parallel     int
	RunningCount int
}

var mutex sync.Mutex
var mutexApi sync.Mutex

var DiffLevelExtMap = map[uint][]string{
	0: {"feeds.conf.default", "*_defconfig", "_config", ".dts", "maintainers", ".yaml", ".yml"},
	1: {".h", ".c", ".cpp", ".yang", ".in", ".txt", ".conf", ".xml", ".json", ".vue", ".js", ".html", ".ini"},
	2: {".py", ".sh", ".pl"},
	3: {".mk", "makefile", ".zip"},
}
