package transfeaturerelease_11_x

import (
	"errors"
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	dfeature "irisAdminApi/service/dao/featurerelease_11_x/dfeature_11_x"
	dfeatureprocdef "irisAdminApi/service/dao/featurerelease_11_x/dfeatureprocdef_11_x"
	dfeatureprocinst "irisAdminApi/service/dao/featurerelease_11_x/dfeatureprocinst_11_x"
	dfeatureproctask "irisAdminApi/service/dao/featurerelease_11_x/dfeatureproctask_11_x"
	"irisAdminApi/service/dao/user/duser"
	"time"

	"gorm.io/gorm"
)

func CreateFeatureTransaction(userID uint, featureObject map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		resource := featureObject["Resource"].(string)
		delete(featureObject, "Resource")
		if err := tx.Model(dfeature.Model()).Create(featureObject).Error; err != nil {
			// 返回任何错误都会回滚事务
			return err
		}
		feature := dfeature.Response{}
		if err := tx.Model(dfeature.Model()).Where("uuid = ?", featureObject["Uuid"]).Find(&feature).Error; err != nil {
			return err
		}
		if feature.ID == 0 {
			return errors.New("创建规则库发布流程失败")
		}
		procInstObject := map[string]interface{}{
			"Title": feature.FileName + "_" + feature.Version,
			// 当前节点
			"NodeID":      "start",
			"TaskID":      0,
			"StartUserID": userID,
			"FeatureID":   feature.ID,
			"CreatedAt":   time.Now(),
			"UpdatedAt":   time.Now(),
			"Resource":    resource,
			"Status":      0,
		}
		if err := tx.Model(dfeatureprocinst.Model()).Create(procInstObject).Error; err != nil {
			// 返回任何错误都会回滚事务
			return err
		}
		procInst := dfeatureprocinst.Response{}
		if err := tx.Model(dfeatureprocinst.Model()).Where("feature_id = ?", feature.ID).Find(&procInst).Error; err != nil {
			return err
		}
		if procInst.ID == 0 {
			return errors.New("创建规则库发布实例失败")
		}
		procTaskObjects := []map[string]interface{}{}
		defResource, _ := dfeatureprocdef.GetResource("规则库发布流程")
		nodes1, _ := dfeatureprocdef.GetNodes(defResource)

		for _, node := range nodes1 {
			if node.NodeID == "start" {
				procTaskObjects = append(procTaskObjects, map[string]interface{}{
					"CreatedAt":  time.Now(),
					"UpdatedAt":  time.Now(),
					"NodeName":   node.Name,
					"NodeID":     node.NodeID,
					"PrevNodeID": node.PrevNodeID,
					"ProcInstID": procInst.ID,
					"Assignee":   userID,
					"Status":     1,
					"Flag":       true,
				})
				break
			}
		}
		nodes2, _ := dfeatureprocinst.GetNodes(procInst.Resource)
		for _, node := range nodes2 {
			if node.PrevNodeID == "start" {
				procTaskObjects = append(procTaskObjects, map[string]interface{}{
					"CreatedAt":  time.Now(),
					"UpdatedAt":  time.Now(),
					"NodeName":   node.Name,
					"NodeID":     node.NodeID,
					"PrevNodeID": node.PrevNodeID,
					"ProcInstID": procInst.ID,
					"Assignee":   node.Assignee,
					"Status":     0,
					"Flag":       true,
				})
			}
		}
		if err := tx.Model(dfeatureproctask.Model()).Create(procTaskObjects).Error; err != nil {
			return err
		}
		go SendMail(procTaskObjects, feature.ID)
		// 返回 nil 提交事务
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func UpdateFeatureTransaction(userID, procInstID, taskID uint, featureObject map[string]interface{}, taskObject map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 查检当前处理节点是否重复处理
		task := dfeatureproctask.Response{}
		if err := tx.Model(dfeatureproctask.Model()).Where("id = ? and status = 0 and flag = true", taskID).Find(&task).Error; err != nil {
			return err
		}

		if task.ID == 0 {
			return errors.New("已处理任务，无法重复处理")
		}
		if task.Assignee != userID {
			return errors.New("不是当前用户的任务")
		}
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		procInst := dfeatureprocinst.Response{}
		if err := tx.Model(dfeatureprocinst.Model()).Where("id = ?", procInstID).Find(&procInst).Error; err != nil {
			return err
		}
		if procInst.ID == 0 {
			return errors.New("未找到规则库发布实例")
		}

		feature := dfeature.Response{}
		if err := tx.Model(dfeature.Model()).Where("id = ?", procInst.FeatureID).Find(&feature).Error; err != nil {
			return err
		}
		if feature.ID == 0 {
			return errors.New("未找到规则库")
		}

		resource, _ := dfeatureprocdef.GetResource("规则库发布流程")
		nodes, _ := dfeatureprocdef.GetNodes(resource)
		procTaskObjects := []map[string]interface{}{}
		levelNodeIDs := []string{}
		prevTasks := []*dfeatureproctask.Response{}
		levelTasks := []*dfeatureproctask.Response{}
		nextTasks := []*dfeatureproctask.Response{}

		prevNodeIDs, _ := dfeatureprocdef.GetPrevNodeIDs(resource, taskObject["NodeID"].(string))
		nextNodeIDs, _ := dfeatureprocdef.GetNextNodeIDs(resource, taskObject["NodeID"].(string))
		_levelNodeIDs, _ := dfeatureprocdef.GetNextNodeIDs(resource, task.PrevNodeID)
		for _, id := range _levelNodeIDs {
			if id != task.NodeID {
				levelNodeIDs = append(levelNodeIDs, id)
			}
		}

		switch taskObject["Status"].(uint) {
		case 1:
			//检查是否为驳回订单，如果是驳回订单，将后续节点flag 置为false

			// 检查前置任务是否都通过
			if len(prevNodeIDs) > 0 {
				if err := tx.Model(dfeatureproctask.Model()).Where("node_id in ? and proc_inst_id = ? and status = 1 and flag = true", prevNodeIDs, procInst.ID).Find(&prevTasks).Error; err != nil {
					return err
				}
				if len(prevTasks) < len(prevNodeIDs) {
					return errors.New("前置节点未全部通过")
				}
			}

			// 检查同级任务是否都通过
			if err := tx.Model(dfeatureproctask.Model()).Where("node_id in ? and proc_inst_id = ? and status = 1 and flag = true", levelNodeIDs, procInst.ID).Find(&levelTasks).Error; err != nil {
				return err
			}
			if len(levelNodeIDs) == len(levelTasks) {
				if len(nextNodeIDs) > 0 {
					if err := tx.Model(dfeatureproctask.Model()).Where("node_id in ? and proc_inst_id = ? and status = 0 and flag = true", nextNodeIDs, procInst.ID).Find(&nextTasks).Error; err != nil {
						return err
					}
				out:
					for _, nextNodeID := range nextNodeIDs {
						for _, nextTask := range nextTasks {
							if nextTask.NodeID == nextNodeID {
								continue out
							}
						}
						node, _ := dfeatureprocinst.GetNode(procInst.Resource, nextNodeID)
						procTaskObjects = append(procTaskObjects, map[string]interface{}{
							"CreatedAt":  time.Now(),
							"NodeName":   node.Name,
							"NodeID":     node.NodeID,
							"PrevNodeID": node.PrevNodeID,
							"ProcInstID": procInst.ID,
							"Assignee":   node.Assignee,
							"Status":     0,
							"Flag":       true,
						})
					}

				} else {
					featureObject["Status"] = 1
				}
			}
		case 2:
			taskObject["Flag"] = false
			prevTask := dfeatureproctask.Response{}
			if err := tx.Model(dfeatureproctask.Model()).Last(&prevTask, "node_id = ? and proc_inst_id = ? and status != 0 and flag = true", taskObject["NextNodeID"].(string), procInst.ID).Error; err != nil {
				return err
			}
			// 处理回退节点之后的未处理任务
			handleIDs := []string{}
			handleNodes, _ := dfeatureprocdef.GetAfterNodes(nodes, taskObject["NextNodeID"].(string))
			for _, node := range handleNodes {
				handleIDs = append(handleIDs, node.NodeID)
			}
			handleIDs = append(handleIDs, taskObject["NodeID"].(string))
			handleIDs = append(handleIDs, taskObject["NextNodeID"].(string))
			if err := tx.Delete(dfeatureproctask.Model(), "node_id in ? and proc_inst_id = ? and status = 0 and flag = true and id != ?", handleIDs, procInst.ID, taskID).Error; err != nil {
				return err
			}

			if err := tx.Model(dfeatureproctask.Model()).Where("node_id in ? and proc_inst_id = ? and status != 0 and flag = true", handleIDs, procInst.ID).UpdateColumns(map[string]interface{}{"Flag": false}).Error; err != nil {
				return err
			}
			//生成新的任务
			procTaskObjects = append(procTaskObjects, map[string]interface{}{
				"CreatedAt":  time.Now(),
				"NodeName":   prevTask.NodeName,
				"NodeID":     prevTask.NodeID,
				"PrevNodeID": prevTask.PrevNodeID,
				"ProcInstID": procInst.ID,
				"Assignee":   prevTask.Assignee,
				"Status":     0,
				"Flag":       true,
			})
		case 3:
			var users []*duser.ApprovalResponse
			if err := tx.Model(duser.Model()).Where("id in ?", []uint{userID, taskObject["UserID"].(uint)}).Find(&users).Error; err != nil {
				return err
			}
			var userMap = make(map[uint]*duser.ApprovalResponse)
			for _, user := range users {
				userMap[user.Id] = user
			}
			procTaskObjects = append(procTaskObjects, map[string]interface{}{
				"CreatedAt":  time.Now(),
				"NodeName":   task.NodeName,
				"NodeID":     task.NodeID,
				"PrevNodeID": task.PrevNodeID,
				"ProcInstID": procInst.ID,
				"Assignee":   taskObject["UserID"].(uint),
				"Status":     0,
				"Flag":       true,
			})
			if len(taskObject["Comment"].(string)) > 0 {
				taskObject["Comment"] = fmt.Sprintf("%s \n %s -> %s", taskObject["Comment"], userMap[userID].Name, userMap[taskObject["UserID"].(uint)].Name)
			} else {
				taskObject["Comment"] = fmt.Sprintf("%s -> %s", userMap[userID].Name, userMap[taskObject["UserID"].(uint)].Name)
			}
		case 4:
			featureObject["Status"] = 4
			featureObject["file_md5"] = feature.FileMd5 + "_deleted_at_" + time.Now().Format("20160102150405")
		}

		delete(taskObject, "NextNodeID")
		delete(taskObject, "UserID")

		if len(procTaskObjects) > 0 {
			if err := tx.Model(dfeatureproctask.Model()).Create(procTaskObjects).Error; err != nil {
				return err
			}

			go SendMail(procTaskObjects, feature.ID)
		}
		if err := tx.Model(dfeatureproctask.Model()).Where("id = ?", taskID).Updates(taskObject).Error; err != nil {
			return err
		}
		if err := tx.Model(dfeature.Model()).Where("id = ?", procInst.FeatureID).UpdateColumns(featureObject).Error; err != nil {
			return err
		}
		// 返回 nil 提交事务

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func SendMail(procTaskObjects []map[string]interface{}, featureID uint) {
	for _, procTaskObject := range procTaskObjects {
		if procTaskObject["Status"] == 0 {
			subject := fmt.Sprintf("[规则库管理系统][规则库ID:%d][%s][待处理]", featureID, procTaskObject["NodeName"].(string))
			body := fmt.Sprintf(`%s<br><p>规则库链接: <a href="http://172.18.25.125:7070/feature/#/feature/todo</a>">http://172.18.25.125:7070/feature/#/feature/todo</a><p>`, subject)
			err := libs.SendMail([]string{fmt.Sprintf("%<EMAIL>", duser.UserMap[procTaskObject["Assignee"].(uint)].Username)}, subject, body, []string{})
			// err := libs.SendMail([]string{fmt.Sprintf("%<EMAIL>", "linjiakai")}, subject, body)
			if err != nil {
				logging.ErrorLogger.Error(err)
			}
		}

	}
}
