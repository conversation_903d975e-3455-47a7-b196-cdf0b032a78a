perms:
  - name: "/api/v1/user/logout"
    displayname: "退出"
    description: "退出"
    act: "GET"

  - name: "/api/v1/user/clear"
    displayname: "清除用户token"
    description: "清除用户token"
    act: "GET"

  - name: "/api/v1/user/expire"
    displayname: "刷新 token"
    description: "刷新 token"
    act: "GET"

  - name: "/api/v1/user/change_avatar"
    displayname: "修改头像"
    description: "修改头像"
    act: "POST"

  - name: "/api/v1/user/change_password"
    displayname: "修改密码"
    description: "修改密码"
    act: "POST"

  - name: "/api/v1/user/profile"
    displayname: "个人信息"
    description: "个人信息"
    act: "GET"

  - name: "/api/v1/user/profile"
    displayname: "修改个人资料"
    description: "修改个人资料"
    act: "POST"

  - name: "/api/v1/user/dashboard"
    displayname: "面板数据"
    description: "面板数据"
    act: "GET"

  - name: "/api/v1/user/users"
    displayname: "用户列表"
    description: "用户列表"
    act: "GET"

  - name: "/api/v1/user/users/{id:uint}"
    displayname: "用户详情"
    description: "用户详情"
    act: "GET"

  - name: "/api/v1/user/users"
    displayname: "创建用户"
    description: "创建用户"
    act: "POST"

  - name: "/api/v1/user/users/{id:uint}"
    displayname: "编辑用户"
    description: "编辑用户"
    act: "POST"

  - name: "/api/v1/user/users/{id:uint}"
    displayname: "删除用户"
    description: "删除用户"
    act: "DELETE"

  - name: "/api/v1/user/departments/{id:uint}/groups"
    displayname: "部门组列表"
    description: "部门组列表"
    act: "GET"


  - name: "/api/v1/user/groups"
    displayname: "用户组列表"
    description: "用户组列表"
    act: "GET"

  - name: "/api/v1/user/groups/{id:uint}"
    displayname: "用户组详情"
    description: "用户组详情"
    act: "GET"

  - name: "/api/v1/user/groups"
    displayname: "创建用户组"
    description: "创建用户组"
    act: "POST"

  - name: "/api/v1/user/groups/{id:uint}"
    displayname: "编辑用户组"
    description: "编辑用户组"
    act: "POST"

  - name: "/api/v1/user/groups/{id:uint}"
    displayname: "删除用户组"
    description: "删除用户组"
    act: "DELETE"


  - name: "/api/v1/user/departments"
    displayname: "部门列表"
    description: "部门列表"
    act: "GET"

  - name: "/api/v1/user/departments/{id:uint}"
    displayname: "部门详情"
    description: "部门详情"
    act: "GET"

  - name: "/api/v1/user/departments"
    displayname: "创建部门"
    description: "创建部门"
    act: "POST"

  - name: "/api/v1/user/departments/{id:uint}"
    displayname: "编辑部门"
    description: "编辑部门"
    act: "POST"

  - name: "/api/v1/user/departments/{id:uint}"
    displayname: "删除部门"
    description: "删除部门"
    act: "DELETE"

  - name: "/api/v1/user/roles"
    displayname: "角色列表"
    description: "角色列表"
    act: "GET"

  - name: "/api/v1/user/roles/{id:uint}"
    displayname: "角色详情"
    description: "角色详情"
    act: "GET"

  - name: "/api/v1/user/roles"
    displayname: "创建角色"
    description: "创建角色"
    act: "POST"

  - name: "/api/v1/user/roles/{id:uint}"
    displayname: "编辑角色"
    description: "编辑角色"
    act: "POST"

  - name: "/api/v1/user/roles/{id:uint}"
    displayname: "删除角色"
    description: "删除角色"
    act: "DELETE"

  - name: "/api/v1/user/perms"
    displayname: "权限列表"
    description: "权限列表"
    act: "GET"

  - name: "/api/v1/user/perms/{id:uint}"
    displayname: "权限详情"
    description: "权限详情"
    act: "GET"

  - name: "/api/v1/user/perms"
    displayname: "创建权限"
    description: "创建权限"
    act: "POST"

  - name: "/api/v1/user/perms/{id:uint}"
    displayname: "编辑权限"
    description: "编辑权限"
    act: "POST"

  - name: "/api/v1/user/perms/{id:uint}"
    displayname: "删除权限"
    description: "删除权限"
    act: "DELETE"

  - name: "/api/v1/user/upload"
    displayname: "文件上传"
    description: "文件上传"
    act: "POST"

  - name: "/api/v1/user/token"
    displayname: "获取token"
    description: "获取token"
    act: "GET"

  - name: "/api/v1/user/token"
    displayname: "更新token"
    description: "更新token"
    act: "POST"

  - name: "/api/v1/approval"
    displayname: "创建申请单"
    description: "创建申GET"
    act: "POST"

  - name: "/api/v1/approval/mine"
    displayname: "查看我的申请"
    description: "查看我的申请"
    act: "GET"

  - name: "/api/v1/approval/download/{id:uint}"
    displayname: "下载文件"
    description: "下载文件"
    act: "GET"

  - name: "/api/v1/approval/download/index/{id:uint}"
    displayname: "按索引下载文件"
    description: "按索引下载文件"
    act: "GET"

  - name: "/api/v1/approval/check"
    displayname: "检查文件是否已存在"
    description: "检查文件是否已存在"
    act: "GET"

  - name: "/api/v1/approval/checkupdate"
    displayname: "检查是否需要弹窗"
    description: "检查是否需要弹窗"
    act: "GET"

  - name: "/api/v1/approval/auditor"
    displayname: "获取我的审批人"
    description: "获取我的审批人"
    act: "GET"

  - name: "/api/v1/approval/allauditor"
    displayname: "获取所有审批人"
    description: "获取所有审批人"
    act: "GET"

  - name: "/api/v1/approval/audit"
    displayname: "查看我的审批"
    description: "查看我的审批"
    act: "GET"

  - name: "/api/v1/approval/audited"
    displayname: "查看我的历史审批"
    description: "查看我的历史审批"
    act: "GET"

  - name: "/api/v1/approval/filedetail/{id:uint}"
    displayname: "文件异常详情"
    description: "文件异常详情"
    act: "GET"

  - name: "/api/v1/approval/{id:uint}"
    displayname: "删除申请单"
    description: "删除申请单"
    act: "DELETE"

  - name: "/api/v1/approval/{id:uint}/updateauditor"
    displayname: "更新审批人"
    description: "更新审批人"
    act: "POST"

  - name: "/api/v1/download"
    displayname: "文件下载"
    description: "文件下载"
    act: "GET"

  - name: "/api/v1/manage/autoauditfile"
    displayname: "自动审批文件类型清单"
    description: "自动审批文件类型清单"
    act: "GET"

  - name: "/api/v1/manage/autoauditfile/{id:uint}"
    displayname: "删除自动审批文件类型"
    description: "删除自动审批文件类型"
    act: "DELETE"

  - name: "/api/v1/manage/autoauditfile"
    displayname: "创建自动审批文件类型"
    description: "创建自动审批文件类型"
    act: "POST"

  - name: "/api/v1/manage/autoauditfile/{id:uint}"
    displayname: "更新自动审批文件类型"
    description: "更新自动审批文件类型"
    act: "POST"

  - name: "/api/v1/audit/approval"
    displayname: "查看所有已审批申请"
    description: "查看所有已审批申请"
    act: "GET"

  - name: "/api/v1/approval/comment/{id:uint}"
    displayname: "文件申请说明"
    description: "文件申请说明"
    act: "GET"

  - name: "/api/v1/approval/share"
    displayname: "分享文件列表"
    description: "分享文件列表"
    act: "GET"

  - name: "/api/v1/approval/shareuser"
    displayname: "分享用户列表"
    description: "分享用户列表"
    act: "GET"

  - name: "/api/v1/approval/share/{id:uint}"
    displayname: "文件抄送用户列表"
    description: "文件抄送用户列表"
    act: "GET"

  - name: "/api/v1/farm/gitjob"
    displayname: "创建作业"
    description: "创建作业"
    act: "POST"

  - name: "/api/v1/farm/project"
    displayname: "获取项目列表"
    description: "获取项目列表"
    act: "GET"

  - name: "/api/v1/farm/baseline"
    displayname: "获取基线列表"
    description: "获取基线列表"
    act: "GET"


  - name: "/api/v1/farm/git/project"
    displayname: "获取GIT项目列表"
    description: "获取GIT项目列表"
    act: "GET"

  - name: "/api/v1/farm/git/userproject"
    displayname: "获取GIT项目列表"
    description: "获取GIT项目列表"
    act: "GET"


  - name: "/api/v1/farm/git/project/{id:uint}/branch"
    displayname: "获取项目分支列表"
    description: "获取项目分支列表"
    act: "GET"

  - name: "/api/v1/farm/gitjob"
    displayname: "获取作业"
    description: "获取作业"
    act: "GET"

  - name: "/api/v1/farm/gitjob/{id:uint}/log"
    displayname: "查看日志"
    description: "查看日志"
    act: "GET"

  - name: "/api/v1/manage/farm/crontab"
    displayname: "添加编译计划任务"
    description: "添加编译计划任务"
    act: "POST"

  - name: "/api/v1/manage/farm/project"
    displayname: "获取编译项目"
    description: "获取编译项目"
    act: "GET"

  - name: "/api/v1/manage/farm/project"
    displayname: "创建编译项目"
    description: "创建编译项目"
    act: "POST"

  - name: "/api/v1/manage/farm/project/{id:uint}"
    displayname: "更新编译项目"
    description: "更新编译项目"
    act: "POST"

  - name: "/api/v1/manage/farm/project/{id:uint}"
    displayname: "删除编译项目"
    description: "删除编译项目"
    act: "DELETE"

  - name: "/api/v1/manage/farm/crontab/{id:uint}"
    displayname: "更新编译计划任务"
    description: "更新编译计划任务"
    act: "POST"

  - name: "/api/v1/manage/farm/crontab"
    displayname: "查看编译计划任务"
    description: "查看编译计划任务"
    act: "GET"

  - name: "/api/v1/manage/farm/crontab/{id:uint}"
    displayname: "删除编译计划任务"
    description: "删除编译计划任务"
    act: "DELETE"

  - name: "/api/v1/farm/cronmake"
    displayname: "查询每日编译任务"
    description: "查询每日编译任务"
    act: "GET"


  - name: "/api/v1/farm/make"
    displayname: "查询编译任务"
    description: "查询编译任务"
    act: "GET"

  - name: "/api/v1/farm/queue"
    displayname: "查看排队作业"
    description: "查看排队作业"
    act: "GET"

  - name: "/api/v1/farm/gitjob/{id:uint}"
    displayname: "查看作业详情"
    description: "查看作业详情"
    act: "GET"

  - name: "/api/v1/farm/gitjob/{id:uint}/products"
    displayname: "获取products清单"
    description: "获取products清单"
    act: "GET"


  - name: "/api/v1/farm/gitjob/{id:uint}/defconfigs"
    displayname: "获取defconfig清单"
    description: "获取defconfig清单"
    act: "GET"

  - name: "/api/v1/farm/gitjob/{id:uint}/targets"
    displayname: "获取make show targets清单"
    description: "获取make show targets清单"
    act: "GET"

  - name: "/api/v1/farm/gitjob/{id:uint}/make"
    displayname: "运行Make任务"
    description: "运行Make任务"
    act: "POST"

  - name: "/api/v1/farm/gitjob/make"
    displayname: "运行Make任务"
    description: "运行Make任务"
    act: "POST"

  - name: "/api/v1/farm/checkversion"
    displayname: "检查分支版本"
    description: "检查分支版本"
    act: "GET"

  - name: "/api/v1/farm/server/add"
    displayname: "添加编译服务器"
    description: "添加编译服务器"
    act: "POST"

  - name: "/api/v1/farm/server/test"
    displayname: "测试服务器连接"
    description: "测试服务器连接"
    act: "POST"

  - name: "/api/v1/farm/server"
    displayname: "获取编译服务器列表"
    description: "获取编译服务器列表"
    act: "GET"

  - name: "/api/v1/farm/server/status"
    displayname: "获取编译服务器状态"
    description: "获取编译服务器状态"
    act: "GET"

  - name: "/api/v1/gitlab/project"
    displayname: "获取Gitlab项目"
    description: "获取Gitlab项目"
    act: "GET"

  - name: "/api/v1/gitlab/project/{id:uint}/issue"
    displayname: "获取Gitlab项目Issue"
    description: "获取Gitlab项目Issue"
    act: "GET"

  - name: "/api/v1/gitlab/project/{id:uint}/issue/{issue_id:uint}/note"
    displayname: "获取Gitlab项目Issue Note"
    description: "获取Gitlab项目Issue Note"
    act: "GET"


  - name: "/api/v1/gitlab/project/{id:uint}/mr"
    displayname: "获取Gitlab项目MR"
    description: "获取Gitlab项目MR"
    act: "GET"


  - name: "/api/v1/gitlab/project/{id:uint}/mr/{issue_id:uint}/note"
    displayname: "获取Gitlab项目Issue Note"
    description: "获取Gitlab项目Issue Note"
    act: "GET"

  - name: "/api/v1/gitlab/group/{id:uint}/issue"
    displayname: "获取Gitlab组Issue"
    description: "获取Gitlab组Issue"
    act: "GET"

  - name: "/api/v1/gitlab/group/{id:uint}/mr"
    displayname: "获取Gitlab组MR"
    description: "获取Gitlab组MR"
    act: "GET"

  - name: "/api/v1/gitlab/project/{id:uint}/commit"
    displayname: "获取Gitlab项目Commit"
    description: "获取Gitlab项目Commit"
    act: "GET"

  - name: "/api/v1/gitlab/commits"
    displayname: "获取Gitlab项目Commit"
    description: "获取Gitlab项目Commit"
    act: "GET"

  - name: "/api/v1/gitlab/favorites"
    displayname: "获取用户收藏夹"
    description: "获取用户收藏夹"
    act: "GET"

  - name: "/api/v1/gitlab/favorites"
    displayname: "新增用户收藏"
    description: "新增用户收藏"
    act: "POST"

  - name: "/api/v1/gitlab/favorites/{id:uint}"
    displayname: "删除用户收藏"
    description: "删除用户收藏"
    act: "DELETE"

  - name: "/api/v1/gitlab/project/{id:uint}/branch"
    displayname: "获取Gitlab项目Branch"
    description: "获取Gitlab项目Branch"
    act: "GET"

  - name: "/api/v1/gitlab/issue"
    displayname: "获取所有Issue"
    description: "获取所有Issue"
    act: "GET"

  - name: "/api/v1/gitlab/mr"
    displayname: "获取所有MR"
    description: "获取所有MR"
    act: "GET"

  - name: "/api/v1/gitlab/group"
    displayname: "获取Gitlab组"
    description: "获取Gitlab组"
    act: "GET"

  - name: "/api/v1/gitlab/token"
    displayname: "获取Gitlab Token"
    description: "获取Gitlab Token"
    act: "GET"

  - name: "/api/v1/gitlab/token"
    displayname: "添加Gitlab Token"
    description: "添加Gitlab Token"
    act: "POST"

  - name: "/api/v1/approval/fragment"
    displayname: "创建文本片段外出"
    description: "创建文本片段外出"
    act: "POST"

  - name: "/api/v1/approval/fragment"
    displayname: "获取文本片段外出列表"
    description: "获取文本片段外出列表"
    act: "GET"

  - name: "/api/v1/audit/fragment"
    displayname: "查看所有文本外出"
    description: "查看所有文本外出"
    act: "GET"

  - name: "/api/v1/release/project"
    displayname: "获取项目列表"
    description: "获取项目列表"
    act: "GET"

  - name: "/api/v1/release/project/audit"
    displayname: "获取项目列表"
    description: "获取项目列表"
    act: "GET"

  - name: "/api/v1/release/project/audited"
    displayname: "获取项目列表"
    description: "获取项目列表"
    act: "GET"

  - name: "/api/v1/release/project/mine"
    displayname: "获取项目列表"
    description: "获取项目列表"
    act: "GET"

  - name: "/api/v1/release/project"
    displayname: "创建项目"
    description: "创建项目"
    act: "POST"

  - name: "/api/v1/release/branches/{id:uint}"
    displayname: "查看分支详情"
    description: "查看分支详情"
    act: "GET"

  - name: "/api/v1/release/branches/{id:uint}"
    displayname: "更新分支详情"
    description: "更新分支详情"
    act: "POST"

  - name: "/api/v1/release/branches/{id:uint}/audit"
    displayname: "创建分支评审记录"
    description: "创建分支评审记录"
    act: "POST"

  - name: "/api/v1/release/branches"
    displayname: "获取分支列表"
    description: "获取分支列表"
    act: "GET"

  - name: "/api/v1/release/project/{id:uint}/newbranches"
    displayname: "获取新分支列表"
    description: "获取新分支列表"
    act: "GET"

  - name: "/api/v1/release/branches/audit"
    displayname: "获取分支列表"
    description: "获取分支列表"
    act: "GET"

  - name: "/api/v1/release/branches/audited"
    displayname: "获取分支列表"
    description: "获取分支列表"
    act: "GET"

  - name: "/api/v1/release/branches/mine"
    displayname: "获取分支列表"
    description: "获取分支列表"
    act: "GET"

  - name: "/api/v1/release/branches/{id:uint}/audit"
    displayname: "获取分支评审记录"
    description: "获取分支评审记录"
    act: "GET"


  - name: "/api/v1/release/branches"
    displayname: "创建分支"
    description: "创建分支"
    act: "POST"

  - name: "/api/v1/release/unitpackages"
    displayname: "获取组件包列表"
    description: "获取组件包列表"
    act: "GET"

  - name: "/api/v1/release/project/{id:uint}"
    displayname: "查看项目详情"
    description: "查看项目详情"
    act: "GET"

  - name: "/api/v1/release/project/{id:uint}"
    displayname: "更新项目详情"
    description: "更新项目详情"
    act: "POST"

  - name: "/api/v1/release/statuses"
    displayname: "获取状态列表"
    description: "获取状态列表"
    act: "GET"

  - name: "/api/v1/release/types"
    displayname: "获取类型列表"
    description: "获取类型列表"
    act: "GET"

  - name: "/api/v1/release/type/{id:uint}/attrs"
    displayname: "获取属性列表"
    description: "获取属性列表"
    act: "GET"

  - name: "/api/v1/release/classes"
    displayname: "获取分类列表"
    description: "获取分类列表"
    act: "GET"

  - name: "/api/v1/release/directions"
    displayname: "获取市场方向列表"
    description: "获取市场方向列表"
    act: "GET"

  - name: "/api/v1/release/demandsrcs"
    displayname: "获取需求来源列表"
    description: "获取需求来源列表"
    act: "GET"

  - name: "/api/v1/release/attachment/{id:uint}"
    displayname: "下载附件"
    description: "下载附件"
    act: "GET"

  - name: "/api/v1/release/releases"
    displayname: "获取发布列表"
    description: "获取发布列表"
    act: "GET"

  - name: "/api/v1/release/{id:uint}/manufacture"
    displayname: "更新下生产状态"
    description: "更新下生产状态"
    act: "POST"

  - name: "/api/v1/release/releases/{id:uint}"
    displayname: "获取发布详情"
    description: "获取发布详情"
    act: "GET"

  - name: "/api/v1/release/releases/{id:uint}"
    displayname: "删除发布记录"
    description: "删除发布记录"
    act: "DELETE"

  - name: "/api/v1/release/releases"
    displayname: "创建发布信息"
    description: "创建发布信息"
    act: "POST"

  - name: "/api/v1/release/attrs"
    displayname: "获取发布属性列表"
    description: "获取发布属性列表"
    act: "GET"

  - name: "/api/v1/release/buildnames"
    displayname: "获取编译工程名称列表"
    description: "获取编译工程名称列表"
    act: "GET"


  - name: "/api/v1/release/productmodels"
    displayname: "获取产品型号列表"
    description: "获取产品型号列表"
    act: "GET"

  - name: "/api/v1/release/basebranches"
    displayname: "获取基线分支列表"
    description: "获取基线分支列表"
    act: "GET"

  - name: "/api/v1/release/basecommits"
    displayname: "获取基线分支CommitID列表"
    description: "获取基线分支CommitID列表"
    act: "GET"

  - name: "/api/v1/release/{id:uint}/branchinfo"
    displayname: "获取分支信息"
    description: "获取分支信息"
    act: "GET"

  - name: "/api/v1/release/{id:uint}/compileinfo"
    displayname: "获取编译信息"
    description: "获取编译信息"
    act: "GET"

  - name: "/api/v1/release/releasebranches"
    displayname: "获取编译信息列表"
    description: "获取编译信息列表"
    act: "GET"

  - name: "/api/v1/release/releaseversions"
    displayname: "获取编译信息列表"
    description: "获取编译信息列表"
    act: "GET"


  - name: "/api/v1/release/branchswitches"
    displayname: "获取分支切换记录"
    description: "获取分支切换记录"
    act: "GET"

  - name: "/api/v1/release/branchswitches"
    displayname: "创建分支切换记录"
    description: "创建分支切换记录"
    act: "POST"

  - name: "/api/v1/feature/productmodels/fromseccloud"
    displayname: "仪表盘"
    description: "仪表盘"
    act: "GET"

  - name: "/api/v1/feature/procdef"
    displayname: "获取流程详情"
    description: "获取流程详情"
    act: "GET"

  - name: "/api/v1/feature/procinst/{id:uint}/prevnodes"
    displayname: "获取前置节点"
    description: "获取前置节点"
    act: "GET"

  - name: "/api/v1/feature/tasks"
    displayname: "获取流程详情"
    description: "获取流程详情"
    act: "GET"

  - name: "/api/v1/feature/{id:uint}/tasks"
    displayname: "获取进展详情"
    description: "获取进展详情"
    act: "GET"

  - name: "/api/v1/feature/{id:uint}/descfile"
    displayname: "获取进展详情"
    description: "获取进展详情"
    act: "GET"

  - name: "/api/v1/feature/{id:uint}/featurefile"
    displayname: "获取进展详情"
    description: "获取进展详情"
    act: "GET"

  - name: "/api/v1/feature/{id:uint}/testreport"
    displayname: "获取进展详情"
    description: "获取进展详情"
    act: "GET"

  - name: "/api/v1/feature/{id:uint}/expreport"
    displayname: "获取进展详情"
    description: "获取进展详情"
    act: "GET"

  - name: "/api/v1/feature/releases"
    displayname: "创建规则库发布记录"
    description: "创建规则库发布记录"
    act: "POST"

  - name: "/api/v1/feature/releases"
    displayname: "获取规则库发布列表"
    description: "获取规则库发布列表"
    act: "GET"

  - name: "/api/v1/feature/releases/{id:uint}"
    displayname: "获取规则库发布列表"
    description: "获取规则库发布列表"
    act: "GET"

  - name: "/api/v1/feature/releases/{id:uint}"
    displayname: "发布规则库"
    description: "发布规则库"
    act: "POST"

  - name: "/api/v1/feature/detection"
    displayname: "创建规则库检测记录"
    description: "创建规则库检测记录"
    act: "POST"

  - name: "/api/v1/feature/detection"
    displayname: "获取规则库检测记录列表"
    description: "获取规则库检测记录列表"
    act: "GET"

  - name: "/api/v1/feature/detection/todolist"
    displayname: "获取规则库正在检测任务列表"
    description: "获取规则库正在检测任务列表"
    act: "GET"

  - name: "/api/v1/feature/detection/donelist"
    displayname: "获取规则库完成检测任务列表"
    description: "获取规则库完成检测任务列表"
    act: "GET"

  - name: "/api/v1/feature/detection/{id:uint}"
    displayname: "获取规则库检测任务详情"
    description: "获取规则库检测任务详情"
    act: "GET"

  - name: "/api/v1/feature/detection/{id:uint}/logfile"
    displayname: "下载检测日志文件"
    description: "下载检测日志文件"
    act: "GET"

  - name: "/api/v1/feature/detection/toolinfo"
    displayname: "工具说明"
    description: "工具说明"
    act: "GET"

  - name: "/api/v1/release/handlebranch/{id:uint}"
    displayname: "获取规则库发布列表"
    description: "获取规则库发布列表"
    act: "POST"

  - name: "/api/v1/kpi/contributions"
    displayname: "创建贡献记录"
    description: "创建贡献记录"
    act: "POST"

  - name: "/api/v1/kpi/contributions"
    displayname: "获取贡献列表"
    description: "获取贡献列表"
    act: "GET"

  - name: "/api/v1/kpi/contributions/{id:uint}"
    displayname: "更新贡献记录"
    description: "更新贡献记录"
    act: "POST"

  - name: "/api/v1/kpi/contributions/{id:uint}"
    displayname: "更新贡献记录"
    description: "更新贡献记录"
    act: "GET"

  - name: "/api/v1/kpi/contribution/reviews/{id:uint}"
    displayname: "更新贡献记录"
    description: "更新贡献记录"
    act: "POST"

  - name: "/api/v1/kpi/contribution/reviews"
    displayname: "获取贡献评审列表"
    description: "获取贡献评审列表"
    act: "GET"

  - name: "/api/v1/kpi/contribution/types"
    displayname: "获取贡献类型"
    description: "获取贡献类型"
    act: "GET"


  - name: "/api/v1/kpi/contribution/reviews/{id:uint}"
    displayname: "获取贡献评审详情"
    description: "获取贡献评审详情"
    act: "GET"

  - name: "/api/v1/kpi/contributions/{id:uint}/reviews"
    displayname: "获取贡献评审记录"
    description: "获取贡献评审记录"
    act: "GET"

  - name: "/api/v1/kpi/contributions/{id:uint}/points"
    displayname: "获取贡献分数记录"
    description: "获取贡献分数记录"
    act: "GET"

  - name: "/api/v1/kpi/contributions/mine"
    displayname: "获取我的贡献列表"
    description: "获取我的贡献列表"
    act: "GET"

  - name: "/api/v1/kpi/problems"
    displayname: "创建问题记录"
    description: "创建问题记录"
    act: "POST"

  - name: "/api/v1/kpi/problems"
    displayname: "获取问题列表"
    description: "获取问题列表"
    act: "GET"

  - name: "/api/v1/kpi/problems/export"
    displayname: "获取问题列表"
    description: "获取问题列表"
    act: "GET"

  - name: "/api/v1/kpi/problems/{id:uint}/proccesses"
    displayname: "创建问题进展"
    description: "创建问题进展"
    act: "POST"

  - name: "/api/v1/kpi/problems/{id:uint}/proccesses"
    displayname: "获取问题进展"
    description: "获取问题进展"
    act: "GET"

  - name: "/api/v1/kpi/problems/{id:uint}"
    displayname: "获取问题详情"
    description: "获取问题详情"
    act: "GET"

  - name: "/api/v1/kpi/problems/{id:uint}"
    displayname: "获取问题详情"
    description: "获取问题详情"
    act: "DELETE"

  - name: "/api/v1/kpi/problem/sources"
    displayname: "获取问题来源列表"
    description: "获取问题来源列表"
    act: "GET"

  - name: "/api/v1/kpi/problems/{id:uint}"
    displayname: "获取问题详情"
    description: "获取问题详情"
    act: "POST"

  - name: "/api/v1/kpi/problems/mine"
    displayname: "获取问题详情"
    description: "获取问题详情"
    act: "GET"

  - name: "/api/v1/kpi/problems/mysubmit"
    displayname: "获取问题详情"
    description: "获取问题详情"
    act: "GET"

  - name: "/api/v1/kpi/attachment/{id:uint}"
    displayname: "下载附件"
    description: "下载附件"
    act: "GET"

  - name: "/api/v1/kpi/dashboard/problem"
    displayname: "仪表盘"
    description: "仪表盘"
    act: "GET"

  - name: "/api/v1/kpi/dashboard/contribution"
    displayname: "仪表盘"
    description: "仪表盘"
    act: "GET"

  - name: "/api/v1/kpi/dashboard/user"
    displayname: "仪表盘"
    description: "仪表盘"
    act: "GET"

  - name: "/api/v1/farm/patch"
    displayname: "补丁编译列表"
    description: "补丁编译列表"
    act: "GET"

  - name: "/api/v1/farm/patch"
    displayname: "创建补丁编译"
    description: "创建补丁编译"
    act: "POST"

  - name: "/api/v1/farm/patch/{id:uint}"
    displayname: "查看补丁编译任务"
    description: "查看补丁编译任务"
    act: "GET"

  - name: "/api/v1/farm/patch/{id:uint}/defconfigs"
    displayname: "获取补丁编译defconfigs列表"
    description: "获取补丁编译defconfigs列表"
    act: "GET"

  - name: "/api/v1/farm/patchquery"
    displayname: "补丁编译查询"
    description: "补丁编译查询"
    act: "GET"

  - name: "/api/v1/farm/patch/{id:uint}/make"
    displayname: "运行补丁编译Make任务"
    description: "运行补丁编译Make任务"
    act: "POST"

  - name: "/api/v1/farm/patch/showpatchlog"
    displayname: "补丁编译日志文件"
    description: "补丁编译日志文件"
    act: "GET"

  - name: "/api/v1/farm/patch/downloadrpmfile"
    displayname: "下载补丁编译的RPM文件"
    description: "下载补丁编译的RPM文件"
    act: "GET"

  - name: "/api/v1/farm/softversions"
    displayname: "获取产品软件版本"
    description: "获取产品软件版本"
    act: "GET"



  - name: "/api/v1/feature/11_x/productmodels/fromseccloud"
    displayname: "仪表盘"
    description: "仪表盘"
    act: "GET"

  - name: "/api/v1/feature/11_x/procdef"
    displayname: "获取流程详情"
    description: "获取流程详情"
    act: "GET"

  - name: "/api/v1/feature/11_x/procinst/prevnodes"
    displayname: "获取前置节点"
    description: "获取前置节点"
    act: "GET"

  - name: "/api/v1/feature/11_x/tasks"
    displayname: "获取流程详情"
    description: "获取流程详情"
    act: "GET"

  - name: "/api/v1/feature/11_x/{id:uint}/tasks"
    displayname: "获取进展详情"
    description: "获取进展详情"
    act: "GET"

  - name: "/api/v1/feature/11_x/{id:uint}/descfile"
    displayname: "获取进展详情"
    description: "获取进展详情"
    act: "GET"

  - name: "/api/v1/feature/11_x/{id:uint}/featurefile"
    displayname: "获取进展详情"
    description: "获取进展详情"
    act: "GET"

  - name: "/api/v1/feature/11_x/{id:uint}/testreport"
    displayname: "获取进展详情"
    description: "获取进展详情"
    act: "GET"

  - name: "/api/v1/feature/11_x/{id:uint}/expreport"
    displayname: "获取进展详情"
    description: "获取进展详情"
    act: "GET"

  - name: "/api/v1/feature/11_x/releases"
    displayname: "创建规则库发布记录"
    description: "创建规则库发布记录"
    act: "POST"

  - name: "/api/v1/feature/11_x/releases"
    displayname: "获取规则库发布列表"
    description: "获取规则库发布列表"
    act: "GET"

  - name: "/api/v1/feature/11_x/releases/{id:uint}"
    displayname: "获取规则库发布列表"
    description: "获取规则库发布列表"
    act: "GET"

  - name: "/api/v1/feature/11_x/releases/{id:uint}"
    displayname: "发布规则库"
    description: "发布规则库"
    act: "POST"


  - name: "/api/v1/infowork/filelogsquery"
    displayname: "Rsync文件日志查询"
    description: "Rsync文件日志查询"
    act: "GET"

  - name: "/api/v1/infowork/hashfilesquery"
    displayname: "病毒库Hash文件查询"
    description: "病毒库Hash文件查询"
    act: "GET"

  - name: "/api/v1/release/releasesODM"
    displayname: "获取ODM发布列表"
    description: "获取ODM发布列表"
    act: "GET"

  - name: "/api/v1/release/releasesODM/{id:uint}"
    displayname: "获取ODM发布详情"
    description: "获取ODM发布详情"
    act: "GET"

  - name: "/api/v1/release/releasesODM/{id:uint}"
    displayname: "删除ODM发布记录"
    description: "删除ODM发布记录"
    act: "DELETE"

  - name: "/api/v1/release/releasesODM"
    displayname: "创建ODM发布信息"
    description: "创建ODM发布信息"
    act: "POST"

  - name: "/api/v1/release/{id:uint}/archiveODM"
    displayname: "ODM版本发布归档"
    description: "ODM版本发布归档"
    act: "GET"

  - name: "/api/v1/release/releasesODM/archived"
    displayname: "获取ODM已归档发布列表"
    description: "获取ODM已归档发布列表"
    act: "GET"

  - name: "/api/v1/release/productfamily"
    displayname: "获取产品系列数据"
    description: "获取产品系列数据"
    act: "GET"

  - name: "/api/v1/release/getBIproductmodels"
    displayname: "获取BI产品型号数据"
    description: "获取BI产品型号数据"
    act: "GET"

  - name: "/api/v1/release/updatereleasesODM/{id:uint}"
    displayname: "更新ODM发布信息"
    description: "更新ODM发布信息"
    act: "POST"

  - name: "/api/v1/coverity/builds"
    displayname: "获取所有构建"
    description: "获取所有构建"
    act: "GET"

  - name: "/api/v1/coverity/builds/last"
    displayname: "获取最新构建"
    description: "获取最新构建"
    act: "GET"

  - name: "/api/v1/coverity/builds/{id:uint}/output"
    displayname: "获取构建输出"
    description: "获取构建输出"
    act: "GET"

  - name: "/api/v1/coverity/builds/{id:uint}/stop"
    displayname: "停止构建"
    description: "停止构建"
    act: "GET"

  - name: "/api/v1/coverity/builds/start"
    displayname: "提交构建请求"
    description: "提交构建请求"
    act: "POST"

  - name: "/api/v1/infowork/rework/{id:uint}/hashfile"
    displayname: "重新加工Hash文件"
    description: "重新加工Hash文件"
    act: "GET"

  - name: "/api/v1/infowork/showsdklog"
    displayname: "SDK文件日志查询"
    description: "SDK文件日志查询"
    act: "GET"

  - name: "/api/v1/infowork/sdkfilesquery"
    displayname: "SDK文件查询"
    description: "SDK文件查询"
    act: "GET"

  - name: "/api/v1/infowork/versioninfosquery"
    displayname: "情报发布版本查询"
    description: "情报发布版本查询"
    act: "GET"

  - name: "/api/v1/release/releasesODM/exportodmreleases/"
    displayname: "导出ODM发布信息"
    description: "导出ODM发布信息"
    act: "GET"

  - name: "/api/v1/infowork/rework/{id:uint}/sdkfile"
    displayname: "重新加工SDK文件"
    description: "重新加工SDK文件"
    act: "GET"

  - name: "/api/v1/infowork/versioninfos/{id:uint}"
    displayname: "删除版本数据"
    description: "删除版本数据"
    act: "DELETE"

  - name: "/api/v1/infowork/reset/sdkfileversion"
    displayname: "SDK版本数据重置"
    description: "SDK版本数据重置"
    act: "GET"

  - name: "/api/v1/infowork/reset/hashfileversion"
    displayname: "Hash版本数据重置"
    description: "Hash版本数据重置"
    act: "GET"

  - name: "/api/v1/bugsync/bugs"
    displayname: "获取所有BUG"
    description: "获取所有BUG"
    act: "GET"

  - name: "/api/v1/bugsync/projects"
    displayname: "获取所有项目"
    description: "获取所有项目"
    act: "GET"

  - name: "/api/v1/bugsync/summary"
    displayname: "获取所有BUG统计"
    description: "获取所有BUG统计"
    act: "GET"

  - name: "/api/v1/bugsync/filter"
    displayname: "获取过滤条件"
    description: "获取过滤条件"
    act: "GET"

  - name: "/api/v1/search/branchs"
    displayname: "搜索分支"
    description: "搜索分支"
    act: "GET"

  - name: "/api/v1/search/projects"
    displayname: "获取所有项目"
    description: "获取所有项目"
    act: "GET"

  - name: "/api/v1/codesync/policies"
    displayname: "获取所有代码同步策略"
    description: "获取所有代码同步策略"
    act: "GET"

  - name: "/api/v1/codesync/policies"
    displayname: "创建代码同步策略"
    description: "创建代码同步策略"
    act: "POST"

  - name: "/api/v1/codesync/policies/{id:uint}"
    displayname: "获取代码同步策略详情"
    description: "获取代码同步策略详情"
    act: "GET"

  - name: "/api/v1/codesync/policies/{id:uint}"
    displayname: "更新代码同步策略"
    description: "更新代码同步策略"
    act: "POST"

  - name: "/api/v1/codesync/policies/{id:uint}"
    displayname: "删除代码同步策略"
    description: "删除代码同步策略"
    act: "DELETE"

  - name: "/api/v1/codesync/queues"
    displayname: "获取所有代码同步队列"
    description: "获取所有代码同步队列"
    act: "POST"

  - name: "/api/v1/codesync/queues/{id:uint}"
    displayname: "手动更新同步MR地址"
    description: "手动更新同步MR地址"
    act: "POST"

  - name: "/api/v1/codesync/projects"
    displayname: "获取代码同步所有仓库"
    description: "获取代码同步所有仓库"
    act: "GET"


  - name: "/api/v1/codesync/projects"
    displayname: "创建代码同步仓库"
    description: "创建代码同步仓库"
    act: "POST"

  - name: "/api/v1/codesync/projects/{id:uint}"
    displayname: "获取代码同步仓库"
    description: "获取代码同步仓库"
    act: "GET"

  - name: "/api/v1/codesync/projects/{id:uint}"
    displayname: "更新代码同步仓库"
    description: "更新代码同步仓库"
    act: "POST"

  - name: "/api/v1/codesync/projects/{id:uint}"
    displayname: "删除代码同步仓库"
    description: "删除代码同步仓库"
    act: "DELETE"

  - name: "/api/v1/coredump/techsupports"
    displayname: "上传techsupport"
    description: "上传techsupport"
    act: "POST"

  - name: "/api/v1/coredump/techsupports"
    displayname: "获取techsupport记录"
    description: "获取techsupport记录"
    act: "GET"

  - name: "/api/v1/coredump/techsupports/{id:uint}/coredumps"
    displayname: "获取coredump文件列表"
    description: "获取coredump文件列表"
    act: "GET"

  - name: "/api/v1/coredump/techsupports/{id:uint}/program"
    displayname: "获取coredump应用文件"
    description: "获取coredump应用文件"
    act: "GET"

  - name: "/api/v1/coredump/techsupports/{id:uint}/bt"
    displayname: "获取堆栈"
    description: "获取堆栈"
    act: "POST"

  - name: "/api/v1/coredump/jobs/{id:uint}/log"
    displayname: "获取堆栈日志"
    description: "获取堆栈日志"
    act: "POST"

  - name: "/api/v1/coredump/jobs"
    displayname: "作业记录"
    description: "作业记录"
    act: "GET"

  - name: "/api/v1/resourcepool/monitors"
    displayname: "获取监控列表"
    description: "获取监控列表"
    act: "GET"

  - name: "/api/v1/resourcepool/monitors"
    displayname: "创建监控记录"
    description: "创建监控记录"
    act: "POST"

  - name: "/api/v1/resourcepool/monitors/{id:uint}"
    displayname: "更新监控记录"
    description: "更新监控记录"
    act: "POST"

  - name: "/api/v1/resourcepool/monitors/{id:uint}/flag"
    displayname: "更新监控记录flag"
    description: "更新监控记录flag"
    act: "POST"

  - name: "/api/v1/resourcepool/monitors/{id:uint}"
    displayname: "删除监控记录"
    description: "删除监控记录"
    act: "DELETE"
  - name: "/api/v1/opensource/components"
    displayname: "获取开源组件列表"
    description: "获取开源组件列表"
    act: "GET"

  - name: "/api/v1/opensource/components/{id:uint}"
    displayname: "获取开源组件详情"
    description: "获取开源组件详情"
    act: "GET"

  - name: "/api/v1/opensource/components"
    displayname: "添加开源组件"
    description: "添加开源组件"
    act: "POST"

  - name: "/api/v1/opensource/components/{id:uint}"
    displayname: "更新某个开源组件"
    description: "更新某个开源组件"
    act: "PUT"

  - name: "/api/v1/opensource/componentsimport/template"
    displayname: "下载组件导入模板"
    description: "下载组件导入模板"
    act: "GET"

  - name: "/api/v1/opensource/componentsimport"
    displayname: "导入开源组件"
    description: "导入开源组件"
    act: "POST"

  - name: "/api/v1/opensource/components/{id:uint}/cvesync"
    displayname: "同步开源组件漏洞"
    description: "同步开源组件漏洞"
    act: "POST"

  - name: "/api/v1/opensource/components/{component_id:uint}/permissions"
    displayname: "获取某个开源组件的负责人列表"
    description: "获取某个开源组件的负责人列表"
    act: "GET"

  - name: "/api/v1/opensource/components/{component_id:uint}/permissions"
    displayname: "为某个开源组件添加负责人"
    description: "为某个开源组件添加负责人"
    act: "POST"

  - name: "/api/v1/opensource/components/{component_id:uint}/permissions/{id:uint}"
    displayname: "为某个开源组件删除负责人"
    description: "为某个开源组件删除负责人"
    act: "DELETE"

  - name: "/api/v1/opensource/vulnerabilities"
    displayname: "获取漏洞列表"
    description: "获取漏洞列表"
    act: "GET"

  - name: "/api/v1/opensource/vulnerabilities/{id:uint}"
    displayname: "获取某个漏洞详情"
    description: "获取某个漏洞详情"
    act: "GET"

  - name: "/api/v1/opensource/vulnerabilityhistories"
    displayname: "获取漏洞处理列表"
    description: "获取漏洞处理列表"
    act: "GET"

  - name: "/api/v1/opensource/vulnerabilities/{vulnerability_id:uint}/process"
    displayname: "执行漏洞处理"
    description: "执行漏洞处理"
    act: "POST"

  - name: "/api/v1/opensource/vulnerabilities/{vulnerability_id:uint}/histories/{id:uint}"
    displayname: "获取某个漏洞处理详情"
    description: "获取某个漏洞处理详情"
    act: "GET"

  - name: "/api/v1/opensource/cnvdvulnerabilities"
    displayname: "获取cnvd漏洞列表"
    description: "获取cnvd漏洞列表"
    act: "GET"

  - name: "/api/v1/opensource/cnvdvulnerabilities/{id:uint}"
    displayname: "获取某个cnvd漏洞详情"
    description: "获取某个cnvd漏洞详情"
    act: "GET"

  - name: "/api/v1/opensource/tempchecks"
    displayname: "发起组件预查任务"
    description: "发起组件预查任务"
    act: "POST"

  - name: "/api/v1/opensource/tempchecks"
    displayname: "获取组件预查任务列表"
    description: "获取组件预查任务列表"
    act: "GET"

  - name: "/api/v1/opensource/tempchecks/{id:uint}"
    displayname: "获取某个组件预查任务详情"
    description: "获取某个组件预查任务详情"
    act: "GET"

  - name: "/api/v1/opensource/tempchecks/{id:uint}/download"
    displayname: "下载组件漏洞预查结果"
    description: "下载组件漏洞预查结果"
    act: "GET"

  - name: "/api/v1/opensource/summary/vulnerability/component"
    displayname: "基于组件获取CVE漏洞的统计数据"
    description: "基于组件获取CVE漏洞的统计数据"
    act: "GET"

  - name: "/api/v1/opensource/summary/vulnerability/product"
    displayname: "基于产品获取CVE漏洞的统计数据"
    description: "基于产品获取CVE漏洞的统计数据"
    act: "GET"

  - name: "/api/v1/resourcepool/interfaces"
    displayname: "获取设备列表"
    description: "获取设备列表"
    act: "GET"

  - name: "/api/v1/resourcepool/interfaces/{id:uint}"
    displayname: "删除设备记录"
    description: "删除设备记录"
    act: "DELETE"

  - name: "/api/v1/resourcepool/switchport/{id:uint}"
    displayname: "更新端口状态"
    description: "更新端口状态"
    act: "POST"

  - name: "/api/v1/resourcepool/creatcon"
    displayname: "建立连接"
    description: "建立连接"
    act: "POST"

  - name: "/api/v1/resourcepool/getconbyname"
    displayname: "获取设备对应的所有接口"
    description: "获取设备对应的所有接口"
    act: "GET"

  - name: "/api/v1/resourcepool/resources"
    displayname: "获取资源列表"
    description: "获取资源列表"
    act: "GET"

  - name: "/api/v1/resourcepool/resourcesruijie"
    displayname: "获取锐捷资源列表"
    description: "获取锐捷资源列表"
    act: "GET"

  - name: "/api/v1/resourcepool/resourcesfriend"
    displayname: "获取友商资源列表"
    description: "获取友商资源列表"
    act: "GET"

  - name: "/api/v1/resourcepool/resourcesname"
    displayname: "获取全部的设备信息"
    description: "获取全部的设备信息"
    act: "GET"

  - name: "/api/v1/resourcepool/resourcesusers"
    displayname: "获取全部的人员信息"
    description: "获取全部的人员信息"
    act: "GET"

  - name: "/api/v1/resourcepool/myresources"
    displayname: "获取个人资源列表"
    description: "获取个人资源列表"
    act: "GET"

  - name: "/api/v1/resourcepool/getnewresources/{id:uint}"
    displayname: "占用资源"
    description: "占用资源"
    act: "POST"

  - name: "/api/v1/resourcepool/dropresources/{id:uint}"
    displayname: "解除资源"
    description: "解除资源"
    act: "POST"

  - name: "/api/v1/resourcepool/giveresources/{id:uint}/reserved"
    displayname: "转移资源"
    description: "转移资源"
    act: "POST"

  - name: "/api/v1/license/authtasks"
    displayname: "获取安全云设备自动授权任务列表"
    description: "获取安全云设备自动授权任务列表"
    act: "GET"

  - name: "/api/v1/license/authtasks/{id:uint}"
    displayname: "获取某个安全云设备自动授权任务详情"
    description: "获取某个安全云设备自动授权任务详情"
    act: "GET"

  - name: "/api/v1/license/authtasks"
    displayname: "创建安全云设备自动任务授权"
    description: "创建安全云设备自动任务授权"
    act: "POST"

  - name: "/api/v1/license/authtasks/{id:uint}"
    displayname: "删除某个安全云设备自动任务授权"
    description: "删除某个安全云设备自动任务授权"
    act: "DELETE"

  - name: "/api/v1/license/materials"
    displayname: "获取物料列表"
    description: "获取物料列表"
    act: "GET"

  - name: "/api/v1/license/devicemodels"
    displayname: "获取设备型号列表"
    description: "获取设备型号列表"
    act: "GET"