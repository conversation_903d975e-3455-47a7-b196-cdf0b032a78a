package buildfarm

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/imroc/req/v3"
	"github.com/pkg/errors"
)

var GitlabWebClient = req.C().
	SetCommonRetryCount(3).
	// Set the retry sleep interval with a commonly used algorithm: capped exponential backoff with jitter (https://aws.amazon.com/blogs/architecture/exponential-backoff-and-jitter/).
	SetCommonRetryBackoffInterval(1*time.Second, 5*time.Second).
	AddCommonRetryCondition(func(resp *req.Response, err error) bool {
		if err == nil && libs.InArrayInt([]int{200, 201, 409}, resp.StatusCode) {
			return false
		}
		return err != nil
	})

type ProjectResponse struct {
	ID                int              `json:"id"`
	SshUrlToRepo      string           `json:"ssh_url_to_repo"`
	ForkedFromProject *ProjectResponse `json:"forked_from_project"`
}

type CommitResponse struct {
	ID      string `json:"id"`
	ShortID string `json:"short_id"`
}

func GetForkedFromProject(repoPathID string) (string, error) {
	project := ProjectResponse{}
	url := fmt.Sprintf("%s/api/%s/projects/%s?private_token=%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, repoPathID, libs.Config.Buildfarm.Token)
	resp, err := GitlabWebClient.R().SetSuccessResult(&project).Get(url)
	if err != nil {
		return "", errors.Wrap(err, "")
	}
	if resp.IsSuccessState() {
		if project.ForkedFromProject != nil {
			return project.ForkedFromProject.SshUrlToRepo, nil
		}
		return "", nil
	}
	return "", fmt.Errorf("unkown err: %s", resp.String())
}

func GetCommitIDFromGtilab(gitlabID, branch string) (string, error) {
	encodeGitlabID := url.QueryEscape(gitlabID)
	gitlabUrl := fmt.Sprintf("%s/api/%s/projects/%s/repository/commits?private_token=%s&ref_name=%s",
		libs.Config.Gitlab.Url,
		libs.Config.Gitlab.Version,
		encodeGitlabID,
		libs.Config.Buildfarm.Token,
		branch,
	)
	commits := []*CommitResponse{}
	resp, err := GitlabWebClient.R().SetSuccessResult(&commits).Get(gitlabUrl)
	if err != nil {
		return "", err
	}
	if resp.IsErrorState() {
		return "", fmt.Errorf(resp.String() + strings.ReplaceAll(gitlabUrl, libs.Config.Buildfarm.Token, ""))
	}
	if len(commits) > 0 {
		return commits[0].ID, nil
	}
	return "", fmt.Errorf(resp.String())
}

func GetFileRawFromGtilab(gitlabID, branch, fp string) (string, error) {
	encodeGitlabID := url.QueryEscape(gitlabID)
	encodeFp := url.QueryEscape(fp)
	gitlabUrl := fmt.Sprintf("%s/api/%s/projects/%s/repository/files/%s/raw?private_token=%s&ref=%s",
		libs.Config.Gitlab.Url,
		libs.Config.Gitlab.Version,
		encodeGitlabID,
		encodeFp,
		libs.Config.Buildfarm.Token,
		branch,
	)
	resp, err := GitlabWebClient.R().Get(gitlabUrl)
	if err != nil {
		return "", err
	}
	if resp.IsErrorState() {
		return "", fmt.Errorf(resp.String() + strings.ReplaceAll(gitlabUrl, libs.Config.Buildfarm.Token, ""))
	}
	return resp.String(), nil
}

type BranchResponse struct {
	Name string `json:"name"`
}

func GetBranchesFromGitlab(gitlabID string) ([]string, error) {
	encodeGitlabID := url.QueryEscape(gitlabID)
	branches := []string{}
	result := []BranchResponse{}

	page := 1
	for {
		gitlabUrl := fmt.Sprintf("%s/api/%s/projects/%s/repository/branches?private_token=%s&page=%d",
			libs.Config.Gitlab.Url,
			libs.Config.Gitlab.Version,
			encodeGitlabID,
			libs.Config.Buildfarm.Token,
			page,
		)

		resp, err := GitlabWebClient.R().SetSuccessResult(&result).Get(gitlabUrl)
		if err != nil {
			return branches, err
		}
		if resp.IsErrorState() {
			return branches, fmt.Errorf(resp.String() + strings.ReplaceAll(gitlabUrl, libs.Config.Buildfarm.Token, ""))
		}
		for _, item := range result {
			branches = append(branches, item.Name)
		}
		totalPageStr := resp.GetHeader("X-Total-Pages")
		totalPage, _ := strconv.Atoi(totalPageStr)

		if page < totalPage {
			page++
		} else {
			break
		}
	}

	return branches, nil
}

func GetTargetsFromFGitlab(gitlabID, branch string) ([]string, error) {
	packages := []string{}
	raw, err := GetFileRawFromGtilab(gitlabID, branch, "external.mk")
	if err != nil {
		return packages, err
	}

	for _, line := range strings.Split(raw, "\n") {
		if strings.HasPrefix(line, "include") {
			lineArr := strings.Split(line, "/")
			target := lineArr[len(lineArr)-2]
			if target == "*" {
				var tree []*TreeResponse

				if lineArr[len(lineArr)-3] == "package" {
					tree, err = GetTreeFromGitlab(gitlabID, branch, "package")
					if err != nil {
						logging.ErrorLogger.Errorf(err.Error())
					}
				} else {
					tree, err = GetTreeFromGitlab(gitlabID, branch, fmt.Sprintf("package/%s", lineArr[len(lineArr)-3]))
					if err != nil {
						logging.ErrorLogger.Errorf(err.Error())
					}
				}

				for _, item := range tree {
					if item.Type == "tree" {
						packages = append(packages, item.Name)
					}
				}
			} else {
				if !strings.Contains(lineArr[len(lineArr)-2], "include") {
					packages = append(packages, lineArr[len(lineArr)-2])
				}
			}
		}
	}
	return packages, nil
}

func GetFeedFromFileRaw(gitlabID, branch, product string) (string, error) {
	raw, err := GetFileRawFromGtilab(gitlabID, branch, fmt.Sprintf("prj_%s/project/feeds.conf.default", product))
	return raw, err
}

type TreeResponse struct {
	Name string `json:"name"`
	Path string `json:"path"`
	Type string `json:"type"`
}

func GetTreeFromGitlab(gitlabID, branch, dir string) ([]*TreeResponse, error) {
	encodeGitlabID := url.QueryEscape(gitlabID)

	tree := []*TreeResponse{}
	var gitlabUrl string
	page := 1
	for {
		_tree := []*TreeResponse{}

		if dir == "" {
			gitlabUrl = fmt.Sprintf("%s/api/%s/projects/%s/repository/tree?private_token=%s&ref=%s&page=%d",
				libs.Config.Gitlab.Url,
				libs.Config.Gitlab.Version,
				encodeGitlabID,
				libs.Config.Buildfarm.Token,
				branch,
				page,
			)
		} else {
			gitlabUrl = fmt.Sprintf("%s/api/%s/projects/%s/repository/tree?private_token=%s&ref=%s&page=%d&path=%s",
				libs.Config.Gitlab.Url,
				libs.Config.Gitlab.Version,
				encodeGitlabID,
				libs.Config.Buildfarm.Token,
				branch,
				page,
				dir,
			)
		}

		resp, err := GitlabWebClient.R().SetSuccessResult(&_tree).Get(gitlabUrl)
		if err != nil {
			return nil, err
		}
		if resp.IsErrorState() {
			return nil, fmt.Errorf(resp.String() + strings.ReplaceAll(gitlabUrl, libs.Config.Buildfarm.Token, ""))
		}
		tree = append(tree, _tree...)

		totalPageStr := resp.GetHeader("X-Total-Pages")
		totalPage, _ := strconv.Atoi(totalPageStr)

		if page < totalPage {
			page++

		} else {
			break
		}
	}
	return tree, nil
}

func GetProductsFromGitlab(gitlabID, branch string) ([]string, error) {
	products := []string{}
	tree, err := GetTreeFromGitlab(gitlabID, branch, "")
	if err != nil {
		return nil, errors.Wrap(err, "")
	}
	for _, item := range tree {
		if strings.HasPrefix(item.Name, "prj_") && item.Type == "tree" {
			products = append(products, strings.ReplaceAll(item.Name, "prj_", ""))
		}
	}

	return products, nil
}

func GetDefconfigsFromGitlab(gitlabID, branch, product string) ([]string, error) {
	defconfigs := []string{}

	var dir string
	if product == "" {
		dir = "configs"
	} else {
		dir = fmt.Sprintf("prj_%s/configs", product)
	}

	tree, err := GetTreeFromGitlab(gitlabID, branch, dir)
	if err != nil {
		return nil, errors.Wrap(err, "")
	}
	for _, item := range tree {
		if item.Type == "blob" {
			defconfigs = append(defconfigs, item.Name)
		}
	}

	return defconfigs, nil
}
