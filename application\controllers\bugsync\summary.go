package bugsync

import (
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/bugsync/dbug"
	"irisAdminApi/service/dao/datasync/dproject"
	"net/url"
	"os"
	"path/filepath"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/xuri/excelize/v2"
)

func GetBugSummarys(ctx iris.Context) {
	bugOS := ctx.FormValue("bug_os")
	export := ctx.FormValue("export")

	result := AllBugSummary(bugOS)
	if export == "1" {
		ExportSummary(ctx, result)
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func GetRunningProjects(ctx iris.Context) {
	result, err := dproject.FindRunningProjects()
	if err != nil {
		logging.ErrorLogger.Errorf("find running projects err ", err.Error())
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

var MirrorStatusMap = map[string]string{
	"0": "未同步",
	"1": "已同步",
	"2": "部分同步",
}

var ResolveStatusMap = map[string]string{
	"0": "未解决",
	"1": "已解决",
	"2": "部分解决",
}

type BugSummary struct {
	BugOS         string `json:"bug_os"`
	MirrorStatus  string `json:"mirror_status"`
	ResolveStatus string `json:"resolve_status"`
	Total         int    `json:"total"`
}

func AllBugSummary(bugOS string) []*BugSummary {
	c := []*BugSummary{}
	db := easygorm.GetEasyGormDb().Model(dbug.Model()).Where("main_bug_id = 0")
	if len(bugOS) > 0 {
		db = db.Where("bug_os = ?", bugOS)
	}
	rows, err := db.Select("bug_os, mirror_status, resolve_status, count(*) as total").Group("bug_os").Group("mirror_status").Group("resolve_status").Having(`mirror_status != "" and resolve_status != ""`).Order("bug_os").Order("mirror_status").Order("resolve_status").Order("total desc").Rows()
	if err != nil {
		logging.ErrorLogger.Error(err)
	}
	for rows.Next() {
		db.ScanRows(rows, &c)
	}
	rows.Close()
	for _, item := range c {
		item.MirrorStatus = MirrorStatusMap[item.MirrorStatus]
		item.ResolveStatus = ResolveStatusMap[item.ResolveStatus]
	}
	return c
}

func ExportSummary(ctx iris.Context, items interface{}) {

	fileName := fmt.Sprintf("bug_summary_%s.xlsx", time.Now().Format("20060102150405"))
	file := excelize.NewFile()
	streamWriter, err := file.NewStreamWriter("Sheet1")

	// styleID, err := file.NewStyle(&excelize.Style{Font: &excelize.Font{Color: "#777777"}})
	if err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	header := []interface{}{"操作系统", "同步状态", "解决状态", "BUG数量"}
	cell, _ := excelize.CoordinatesToCellName(1, 1)
	if err := streamWriter.SetRow(cell, header); err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	rowNum := 1
	for _, item := range items.([]*BugSummary) {
		rowNum++
		cell, _ := excelize.CoordinatesToCellName(1, rowNum)
		row := []interface{}{item.BugOS, item.MirrorStatus, item.ResolveStatus, item.Total}
		if err := streamWriter.SetRow(cell, row); err != nil {
			logging.ErrorLogger.Error(err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
	}
	if err := streamWriter.Flush(); err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if err := file.SaveAs(filepath.Join("/tmp", fileName)); err != nil {
		logging.ErrorLogger.Error(err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	defer os.Remove(filepath.Join("/tmp", fileName))
	ctx.SendFile(filepath.Join("/tmp", fileName), url.QueryEscape(fileName))
	return
}
