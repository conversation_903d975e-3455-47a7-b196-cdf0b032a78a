package mergerequest

import (
	"time"

	"irisAdminApi/application/models"

	"gorm.io/gorm"
)

type MergeRequest struct {
	models.ModelBase
	SourceProjectID uint   `gorm:"not null" json:"source_project_id" form:"source_project_id"`
	SourceProject   string `gorm:"not null; type:varchar(200)" json:"source_project" form:"source_project"`
	SourceBranch    string `gorm:"not null; type:varchar(200)" json:"source_branch" form:"source_branch"`

	TargetProjectID  uint   `gorm:"not null" json:"target_project_id" form:"target_project_id"`
	TargetProject    string `gorm:"not null; type:varchar(200)" json:"target_project" form:"target_project"`
	TargetBranch     string `gorm:"not null; type:varchar(200)" json:"target_branch" form:"target_branch"`
	MirrorFrom       string `gorm:"not null; type:varchar(200)"  json:"mirror_from" form:"mirror_from"`
	MergeRequestID   uint   `gorm:"not null" json:"merge_request_id" form:"merge_request_id"`
	MergeRequestIID  uint   `gorm:"not null" json:"merge_request_Iid" form:"merge_request_Iid"`
	ReleaseProjectID uint   `gorm:"not null" json:"release_project_id" form:"release_project_id"`
	ReleaseProject   string `gorm:"not null; type:varchar(200)" json:"release_project" form:"release_project"`
	AssigneeIDs      string `gorm:"not null; type:varchar(200)" json:"assignee_ids" form:"assignee_ids"`
	ReviewerIDs      string `gorm:"not null; type:varchar(200)" json:"reviewer_ids" form:"reviewer_ids"`
	WorkPackageID    uint   `gorm:"not null" json:"work_package_id" form:"work_package_id"`
	// 以下字段写入description
	PreCheck       bool   `gorm:"not null" json:"pre_check" form:"pre_check"`
	Portable       bool   `gorm:"not null" json:"portable" form:"portable"`
	Type           string `gorm:"not null; type:varchar(60)" json:"type" form:"type"` // new, bugfix
	BugID          string `gorm:"not null; type:varchar(200)" json:"bug_id" form:"bug_id"`
	WorkPackage    string `gorm:"not null; type:varchar(200)" json:"work_package" form:"work_package"`
	LocalBuildPass bool   `gorm:"not null" json:"local_build_pass" form:"local_build_pass"`
	MFAL           uint   `gorm:"not null" json:"mfal" form:"mfal"` // merge first audit later    //1: create  2: update

	Title             string `gorm:"not null; type:varchar(512)" json:"title" form:"title"`
	OriginTitle       string `gorm:"not null; type:varchar(512)" json:"origin_title" form:"origin_title"`
	Description       string `gorm:"not null;" json:"description" form:"description"`
	OriginDescription string `gorm:"not null;" json:"origin_description" form:"origin_description"`
	UserID            uint   `gorm:"not null" json:"user_id" form:"user_id"`

	// 同步MR数据
	Status             int     `gorm:"not null; type: tinyint" json:"status" form:"status"`          //-1: 创建失败  0： 待评审  1：待合并   2：已合并    3：已关闭
	PipelineStatus     uint    `gorm:"not null" json:"pipeline_status" form:"pipeline_status"`       // 0: 运行中  1： 成功  2：失败
	PhabricatorStatus  uint    `gorm:"not null" json:"phabricator_status" form:"phabricator_status"` // 0: 创建中 1：成功 2：失败
	CodeQuantityAdd    uint    `gorm:"not null" json:"code_quantity_add" form:"code_quantity_add"`
	CodeQuantityRemove uint    `gorm:"not null" json:"code_quantity_remove" form:"code_quantity_remove"`
	OverTime           bool    `gorm:"not null; default:false" json:"over_time" form:"over_time"`
	Discount           float32 `gorm:"not null; default:1.0" json:"discount" form:"discount"`
	CommitCount        uint    `gorm:"not null; default:1" json:"commit_count" form:"commit_count"`
	ScreenshotFile     string  `gorm:"not null;" json:"screenshot_file"`                                               // 截图文件
	DependencyIDs      string  `gorm:"not null; type:varchar(200)" json:"dependency_ids"`                              // MR依赖项
	ButtonClickStatus  int     `gorm:"not null; type: tinyint" json:"button_click_status" form:"button_click_status"`  // 0: 未点击  1： 已点击
	MergeRequestSHA    string  `gorm:"not null; type:varchar(200)" json:"merge_request_sha" form:"merge_request_sha"`  // merge  sha 合并唯一标记
	ButtonClickUser    string  `gorm:"not null; type: varchar(200)" json:"button_click_user" form:"button_click_user"` // 点击按钮人员                 // 点击按钮人员
	PmsSync            bool    `gorm:"not null; default: false" json:"pms_sync" form:"pms_sync"`                       // CPS同步PMS状态
	CodeDiffSync       bool    `gorm:"not null; default: false" json:"code_diff_sync"`
	AiCheckStatus      uint    `gorm:"not null; default: 0" json:"ai_check_status"`
	CodeCheckStatus    uint    `gorm:"not null; default: 0" json:"code_check_status"`
	MainID             uint    `json:"main_id"`
	ErrMsg             string  `json:"err_msg"`
}

type MergeRequestHistory struct {
	models.ModelBase
	MergeRequestID     uint `gorm:"not null" json:"merge_request_id"`
	Status             int  `gorm:"not null; type: tinyint" json:"status"` //-1: 创建失败  0： 待评审  1：待合并   2：已合并    3：已关闭
	PipelineStatus     uint `gorm:"not null" json:"pipeline_status"`
	PhabricatorStatus  uint `gorm:"not null" json:"phabricator_status"`
	SyncStatus         uint `gorm:"not null" json:"sync_status"`
	CodeQuantityAdd    uint `gorm:"not null" json:"code_quantity_add"`
	CodeQuantityRemove uint `gorm:"not null" json:"code_quantity_remove"`
}

// 项目，工作包，代码量
type MergeRequestWorkPackage struct {
	models.ModelBase

	Name                        string  `gorm:"not null;uniqueIndex:work_package_name_release_project_id_idx; type:varchar(60)" json:"name"`
	ReleaseProjectID            uint    `gorm:"not null;uniqueIndex:work_package_name_release_project_id_idx" json:"release_project_id"`
	CodeQuantity                float32 `gorm:"not null" json:"code_quantity"`
	PortedCodeQuantity          float32 `gorm:"not null" json:"ported_code_quantity"`
	TemporaryPortedCodeQuantity float32 `gorm:"not null" json:"temporary_ported_code_quantity"`
	TotalCodeQuantity           float32 `gorm:"not null" json:"total_code_quantity"`
	OwnerID                     uint    `gorm:"not null" json:"owner_id"`
	PstlID                      uint    `gorm:"not null" json:"pstl_id"`
	WorkGroup                   string  `gorm:"not null; type:varchar(60); deault:''" json:"work_group"`
	Requirement                 string  `gorm:"not null; default:''" json:"requirement"`
	MrControlStatus             uint    `gorm:"not null" json:"mr_control_status"`
}

type MergeRequestReviewer struct {
	models.ModelBase
	MergeRequestID uint `gorm:"not null" json:"merge_request_id"`
	UserID         uint `gorm:"not null" json:"user_id"`
}

type MergeRequestAssignee struct {
	models.ModelBase
	MergeRequestID uint `gorm:"not null" json:"merge_request_id"`
	UserID         uint `gorm:"not null" json:"user_id"`
}

type MergeRequestPhabricator struct {
	models.ModelBase
	MergeRequestID  uint   `gorm:"not null" json:"merge_request_id"`
	DifferentialUrl string `gorm:"not null; type:varchar(500)" json:"differential_url"`
}

type MergeRequestNotice struct {
	models.ModelBase
	MergeRequestID uint `gorm:"not null;uniqueIndex" json:"merge_request_id"`
}

type MergeRequestCodeCheck struct {
	models.ModelBase
	MergeRequestID uint   `gorm:"not null" json:"merge_request_id"`
	Log            string `json:"log"`
	Errors         string `json:"errors"`
}

type MergeRequestDependencies struct {
	models.ModelBase
	MergeRequestID uint `gorm:"not null" json:"merge_request_id"`
	DependencyID   uint `gorm:"not null" json:"dependency_id"`
}
type MergeRequestControlReview struct {
	models.ModelBase
	MergeRequestID   uint   `gorm:"not null" json:"merge_request_id"`
	ReleaseProjectID uint   `gorm:"not null" json:"release_project_id" form:"release_project_id"`
	ReleaseProject   string `gorm:"not null; type:varchar(200)" json:"release_project" form:"release_project"`
	WorkPackageID    uint   `gorm:"not null" json:"work_package_id" form:"work_package_id"`
	UnitPackage      uint   `gorm:"not null" json:"unit_package" form:"unit_package"`
	UserID           uint   `gorm:"not null" json:"user_id" form:"user_id"`
	UserName         string `gorm:"not null; type:varchar(200)" json:"user_name" form:"user_name"`
	Status           int    `gorm:"not null; type: tinyint" json:"status" form:"status"`       // 0： 待评审  1：评审通过  2：评审不通过 3：系统关闭
	Comment          string `gorm:"not null; type:varchar(256)" json:"comment" form:"comment"` // 评审意见
	Uuid             string `gorm:"not null; type:varchar(100)" json:"uuid" form:"uuid"`
}

type MergeRequestButtonClickLog struct {
	models.ModelBase
	MergeRequestID     uint   `gorm:"not null" json:"merge_request_id"`
	ReleaseProjectID   uint   `gorm:"not null" json:"release_project_id" form:"release_project_id"`
	ReleaseProject     string `gorm:"not null; type:varchar(200)" json:"release_project" form:"release_project"`
	WorkPackageID      uint   `gorm:"not null" json:"work_package_id" form:"work_package_id"`
	UnitPackage        uint   `gorm:"not null" json:"unit_package" form:"unit_package"`
	UserID             uint   `gorm:"not null" json:"user_id" form:"user_id"`
	AcceptMergeRequest string `gorm:"not null; type:varchar(512)" json:"accept_merge_request" form:"accept_merge_request"`
}

type MergeRequestDiscussionCps struct {
	ID                 uint           `gorm:"primarykey" json:"id"`
	CreatedAt          time.Time      `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt          time.Time      `gorm:"autoUpdateTime" json:"updated_at" update:"1"`
	DeletedAt          gorm.DeletedAt `gorm:"index" json:"deleted_at"`
	TargetProjectID    uint           `gorm:"not null" json:"target_project_id"`
	MergeRequestIID    uint           `gorm:"not null" json:"merge_request_iid"`
	Object             string         `gorm:"not null; type:varchar(200)" json:"object" update:"1"`
	Discussion         string         `gorm:"not null; type:varchar(512)" json:"discussion" update:"1"`
	DiscussionID       string         `gorm:"not null; uniqueIndex:discussion_unique_idx; type:varchar(100)" json:"discussion_id"`
	Reviewer           string         `gorm:"not null; type:varchar(100)" json:"reviewer" update:"1"`
	Category           uint           `gorm:"not null" json:"category" update:"1"`
	Severity           uint           `gorm:"not null" json:"severity" update:"1"`
	Confirm            uint           `gorm:"not null" json:"confirm" update:"1"`
	Introduction       uint           `gorm:"not null" json:"introduction" update:"1"`
	Condition          uint           `gorm:"not null" json:"condition" update:"1"`
	State              uint           `gorm:"not null" json:"state" update:"1"`
	Comment            string         `gorm:"not null" json:"comment" update:"1"`
	LastChangeUsername string         `gorm:"not null; type:varchar(100)" json:"last_change_username" update:"1"`
	RecordID           string         `gorm:"type:varchar(100)" json:"record_id" update:"1"`
}

type MergeRequestDiscussionCpsHistory struct {
	models.ModelBase
	TargetProjectID uint `gorm:"not null" json:"target_project_id"`
	MergeRequestIID uint `gorm:"not null" json:"merge_request_iid"`
}

// 检查数据库记录，是否存在, discussion_id, new_path, new_line, content, suggestion, project_id, merge_request_iid, position.base_sha, position.start_sha, position.head_sha
type MergeRequestAiCheck struct {
	models.ModelBase

	ProjectID       uint   `gorm:"uniqueIndex:ai_check_unique_idx; not null" json:"project_id"`
	MergeRequestIID uint   `gorm:"uniqueIndex:ai_check_unique_idx; not null" json:"merge_request_i_id"`
	NewPath         string `gorm:"uniqueIndex:ai_check_unique_idx; not null; type:varchar(200)" json:"new_path"`
	NewLine         string `gorm:"uniqueIndex:ai_check_unique_idx; not null; type:varchar(100)" json:"new_line"`
	ContentMD5      string `gorm:"uniqueIndex:ai_check_unique_idx; not null; type:varchar(100)" json:"content_md5"`
	Content         string `gorm:"not null; type:varchar(2000)" json:"content"`
	Suggestion      string `gorm:"not null; type:varchar(512)" json:"suggestion"`

	PositionBaseSha  string `gorm:"not null; type:varchar(100)" json:"position_base_sha"`
	PositionStartSha string `gorm:"not null; type:varchar(100)" json:"position_start_sha"`
	PositionHeadSha  string `gorm:"not null; type:varchar(100)" json:"position_head_sha"`
	DiscussionID     string `gorm:"type:varchar(100)" json:"discussion_id"`
}

type MergeRequestAiCheckHistory struct {
	models.ModelBase

	ProjectID       uint   `gorm:"uniqueIndex:ai_check_unique_idx; not null" json:"project_id"`
	MergeRequestIID uint   `gorm:"uniqueIndex:ai_check_unique_idx; not null" json:"merge_request_i_id"`
	NewPath         string `gorm:"uniqueIndex:ai_check_unique_idx; not null; type:varchar(200)" json:"new_path"`
	ChunkMD5        string `gorm:"uniqueIndex:ai_check_unique_idx; not null; type:varchar(100)" json:"chunk_md5"`

	Chunk            string    `json:"chunk"`
	LineCount        uint      `json:"line_count"`
	Suggestions      string    `json:"suggestion"`
	SuggestionCount  uint      `json:"suggestion_count"`
	StartedAt        time.Time `json:"started_at"`
	EndedAt          time.Time `json:"ended_at"`
	PositionBaseSha  string    `gorm:"not null; type:varchar(100)" json:"position_base_sha"`
	PositionStartSha string    `gorm:"not null; type:varchar(100)" json:"position_start_sha"`
	PositionHeadSha  string    `gorm:"not null; type:varchar(100)" json:"position_head_sha"`
}
