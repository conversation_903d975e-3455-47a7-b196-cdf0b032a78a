package buildfarm

import (
	"archive/zip"
	"bufio"
	"bytes"
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/buildfarm"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/buildfarm/dbuildfarmproductcpu"
	"irisAdminApi/service/dao/buildfarm/dcronmakejob"
	"irisAdminApi/service/dao/buildfarm/dmakejob"
	"irisAdminApi/service/dao/buildfarm/dpatchdefconfig"
	"irisAdminApi/service/dao/buildfarm/dpatchjob"
	"irisAdminApi/service/dao/buildfarm/dserver"
	"irisAdminApi/service/dao/buildfarm/dsoftversions"
	"math/rand"
	"net/url"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gabriel-vasile/mimetype"
	"github.com/kataras/iris/v12"
)

func GetPatchJobs(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx) //获取用户id
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	// name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	userId := fmt.Sprintf("%d", id) //用户id
	where := []map[string]string{{"column": "user_id", "condition": "=", "value": userId}, {"column": "patch_version", "condition": "=", "value": "1"}}

	list, err := dao.AllEx(&dpatchjob.PatchJobListResponse{}, ctx, where, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func CreatePatchJob(ctx iris.Context) {
	//校验用户权限
	uID, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	request := &dpatchjob.Request{}
	if err := ctx.ReadForm(request); err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	validErr := libs.Validate.Struct(*request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		logging.ErrorLogger.Errorf("create patchjob read json err ", strings.Join(errs, ";"))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	//请求参数校验
	// if request.Baseline == "" {
	// 	ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "请输入基线版本"))
	// 	return
	// }

	// if request.SoftwareVersion == "" {
	// 	ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "请输入适配软件版本"))
	// 	return
	// }

	// if request.PatchComponentName == "" {
	// 	ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "请输入补丁组件名"))
	// 	return
	// }

	// if request.AdapterModel == "" {
	// 	ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "请输入适配型号"))
	// 	return
	// }
	cpus, err := dbuildfarmproductcpu.FindDistinctCpuByProduct(strings.Split(request.AdapterModel, "|"))
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	aarches := []string{}
	for _, cpu := range cpus {
		if !libs.InArrayS(aarches, strings.Split(cpu, "-")[1]) {
			aarches = append(aarches, strings.Split(cpu, "-")[1])
		}
	}
	if len(aarches) > 1 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "适配产品型号不能同时包含多种架构，如：aarch64、x86_64"))
		return
	}
	// if request.AdapterHardware == "" {
	// 	ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "请输入适配硬件"))
	// 	return
	// }
	// if request.PatchFileDesc == "" {
	// 	ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "请输入补丁包描述"))
	// 	return
	// }
	//编译服务器选择
	serverId := ChoosePatchServer()
	if serverId == 0 {
		logging.ErrorLogger.Errorf("create patchjob serverID err ")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "没用可用的服务器"))
		return
	}
	//todo: url还是手动上传
	tempName := libs.GetUUID()
	var fileName, tempCompileDir, upload string
	upload = filepath.Join(libs.Config.PatchFileStorage.Upload, "tar", time.Now().Format("20060102"))
	err = os.MkdirAll(upload, 0750)
	os.Chmod(upload, 0750)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	//编译目录生成
	parentPath := libs.Config.PatchFileStorage.CompilePath
	tempCompileDir = filepath.Join(parentPath, tempName)
	var tempDir = libs.Config.PatchFileStorage.Temp + "patch/" + time.Now().Format("20060102") + "/"
	err = os.MkdirAll(tempDir, 0750)
	os.Chmod(tempDir, 0750)

	if request.Type == "生产" {
		var jobID string
		switch request.MakeFrom {
		case "每日编译":
			cronMakeJob := dcronmakejob.CronMakeJob{}
			err := cronMakeJob.Find(request.CronMakeJobID)
			if err != nil {
				logging.ErrorLogger.Errorf("get cron job by id err ", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
				return
			}

			if cronMakeJob.Id == 0 {
				ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "未到找记录"))
				return
			}
			jobID = cronMakeJob.JobId
		case "发布归档":
			cronMakeJob := dcronmakejob.CronMakeJob{}
			err := cronMakeJob.Find(request.CronMakeJobID)
			if err != nil {
				logging.ErrorLogger.Errorf("get cron job by id err ", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
				return
			}

			if cronMakeJob.Id == 0 {
				ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "未到找记录"))
				return
			}
			jobID = cronMakeJob.JobId
		case "定制编译":
			makeJob := dmakejob.MakeJob{}
			err := makeJob.Find(request.CronMakeJobID)
			if err != nil {
				logging.ErrorLogger.Errorf("get make job by id err ", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
				return
			}

			if makeJob.ID == 0 {
				ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "未到找记录"))
				return
			}
			jobID = makeJob.TaskID
		default:
			return
		}

		//通过url下载文件，并生成post.txt
		fileName = tempName + ".tar"
		fp := filepath.Join(upload, fileName)
		tmp := tempDir + tempName + "/"
		err = os.MkdirAll(tmp, 0750)
		os.Chmod(tmp, 0750)
		defer os.RemoveAll(tmp)
		f, err := os.OpenFile(filepath.Join(tmp, "post.txt"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
			return
		}
		defer f.Close()
		// 标准化文本内容，处理行结束符问题
		normalizedContent := normalizeTextContent(request.Post)
		_, err = f.WriteString(normalizedContent)
		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
			return
		}
		for _, packageName := range strings.Split(request.PatchFileName, "|") {
			output, err := libs.ExecCommand(fmt.Sprintf(`find %s -name "%s"|xargs -I {} cp {} %s`, filepath.Join(libs.Config.Buildfarm.Archivepath, jobID), packageName, tmp))
			if err != nil {
				logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
				ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": output}, response.SystemErr.Msg))
				return
			}
		}
		output, err := libs.ExecCommand(fmt.Sprintf(`cd %s && tar cvf %s ./`, tmp, fp))
		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": output}, response.SystemErr.Msg))
			return
		}
		fi, err := os.Stat(fp)
		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": output}, response.SystemErr.Msg))
			return
		}
		request.PatchFileSize = strconv.FormatInt(fi.Size(), 10)
		request.PatchFileMd5, err = libs.GetFileMd5(fp)
		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": output}, response.SystemErr.Msg))
			return
		}

	} else if request.Type == "测试" {
		//接收上传文件
		f, fh, err := ctx.FormFile("file") //获取文件数据
		defer f.Close()
		if err != nil {
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		//验证后缀名是否符合要求
		ext := path.Ext(fh.Filename)
		var AllowExtMap map[string]bool = map[string]bool{
			".tar": true,
			".zip": true,
		}

		//上传文件放入临时目录下
		//构造文件名称

		fileName = tempName + ext

		_, err = ctx.SaveFormFile(fh, filepath.Join(tempDir, fileName)) //保存文件到临时目录中
		defer os.Remove(filepath.Join(tempDir, fileName))               //最后删除临时文件
		if err != nil {
			logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}

		//检查文件类型与文件扩展为TAR格式 ZIP格式
		mime, err := mimetype.DetectFile(filepath.Join(tempDir, fileName))
		if err != nil {
			logging.ErrorLogger.Errorf("Error while Check File Type ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "无法判断文件类型"))
			return
		}
		if _, ok := AllowExtMap[mime.Extension()]; !ok {
			logging.ErrorLogger.Errorf("Error while Check File Type ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "只允许上传tar|zip压缩的文件"))
			return
		}

		if mime.Extension() == ".zip" {
			//解压压缩包文件到指定目录
			fileUnzipDirectory := tempDir + tempName + "/"
			err = unzip(fileUnzipDirectory, filepath.Join(tempDir, fileName))
			defer os.RemoveAll(fileUnzipDirectory)
			if err != nil {
				logging.ErrorLogger.Errorf("Error while Unzip File", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "压缩包存在问题，请重新打包后重试。"))
				return
			}
			//将目录打包成tar包
			tarfile := upload + "/" + tempName + ".tar"
			fileName = tempName + ".tar"
			err := tarFolder(fileUnzipDirectory, tarfile)
			if err != nil {
				logging.ErrorLogger.Errorf("Error while tar Directory  ", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "压缩包存在问题,打包tar失败，请重新打包后重试。"))
				return
			}
		} else {
			//创建最终存放路径以及保存文件
			tempFn := filepath.Join(upload, fileName) //路径拼接
			_, err = ctx.SaveFormFile(fh, tempFn)
			if err != nil {
				logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
				return
			}
		}
	}

	//创建任务单
	err = dao.Create(&dpatchjob.Response{}, ctx, map[string]interface{}{
		"JobId":               tempName,
		"UserID":              uID,
		"PatchFileName":       fileName,
		"PatchFileSize":       request.PatchFileSize,
		"PatchFileMd5":        request.PatchFileMd5,
		"PatchType":           request.PatchType,
		"PatchForm":           request.PatchForm,
		"PatchUpgradeType":    request.PatchUpgradeType,
		"PatchFileOriginName": request.PatchFileName,
		"PatchObjectPath":     request.PatchObjectPath,
		"SoftwareVersion":     request.SoftwareVersion,
		"PatchComponentName":  request.PatchComponentName,
		"Baseline":            request.Baseline,
		"AdapterModel":        request.AdapterModel,
		"AdapterHardware":     request.AdapterHardware,
		"PatchFileDesc":       request.PatchFileDesc,
		"CreatedAt":           time.Now(),
		"UpdatedAt":           time.Now(),
		"Dir":                 tempCompileDir,
		"LocalDir":            upload,
		"Version":             "",
		"TaskId":              "",
		"ServerId":            serverId,
		"Status":              0,
		"Type":                request.Type,
		"Post":                request.Post,
		"CronMakeJobID":       request.CronMakeJobID,
		"MakeFrom":            request.MakeFrom,
		"Defconfig":           aarches[0] + "_defconfig",
		"Crypt":               request.Crypt,
	})
	if err != nil {
		defer os.Remove(filepath.Join(upload, fileName)) //删除上传文件
		logging.ErrorLogger.Errorf("create detection Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	patchJob := &dpatchjob.Response{}
	err = easygorm.GetEasyGormDb().Model(&buildfarm.PatchJob{}).Where("job_id = ?", tempName).Find(&patchJob).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create detectionJob get err ", err)
	}
	//执行编译任务前置工作（拉取编译工具 拷贝文件到远程服务器）
	if patchJob.PatchForm == 1 && patchJob.Type == "生产" {
		go FunctionPatchJob(ctx, patchJob)
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, patchJob, response.NoErr.Msg))
	return
}

func GetPatchJob(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	patchjob := &dpatchjob.Response{}
	err := easygorm.GetEasyGormDb().Model(&buildfarm.PatchJob{}).Where("id = ?", id).Find(&patchjob).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get job status err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, patchjob, response.NoErr.Msg))
	return
}

type PatchMakejobReq struct {
	Defconfig           string `json:"defconfig" form:"defconfig"`
	PatchFileOriginName string `json:"patch_file_origin_name" form:"patch_file_origin_name"`
}

func CreatePatchMakeJob(ctx iris.Context) {
	id, _ := dao.GetId(ctx)

	patchJob := &dpatchjob.Response{}
	err := patchJob.Find(id)

	if err != nil {
		logging.ErrorLogger.Errorf("get patchjob err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if patchJob.Id == 0 {
		logging.ErrorLogger.Errorf("get patchjob err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	PatchMakejobReq := &PatchMakejobReq{}
	if err := ctx.ReadForm(PatchMakejobReq); err != nil {
		logging.ErrorLogger.Errorf("read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*PatchMakejobReq)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	//检查当前作业是否已经存在排队中的编译申请
	// patchmakejob := &dpatchjob.Response{}
	// err = easygorm.GetEasyGormDb().Model(&buildfarm.PatchJob{}).Where("job_id = ? and (make_status = 3 or make_status = 0)", dpatchJob.JobId).Find(&patchmakejob).Error
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("create job get err ", err)
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
	// 	return
	// }

	if patchJob.MakeStatus == 0 || patchJob.MakeStatus == 1 {
		logging.ErrorLogger.Errorf("create job get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "当前作业存在未完成编译任务"))
		return
	}
	rand.Seed(time.Now().UnixNano())
	randNum := fmt.Sprintf("%d", rand.Intn(9999)+1000)
	hashName := md5.Sum([]byte(time.Now().Format("2006_01_02_15_04_05_") + randNum))
	tempName := fmt.Sprintf("%x", hashName)
	//更新编译任务信息
	// defconfig := PatchMakejobReq.Defconfig
	// if patchJob.Defconfig == "" && defconfig == "" && (patchJob.PatchForm == 0 || patchJob.Type == "测试") {
	// 	defconfig = "aarch64_defconfig"
	// } else {
	// 	defconfig = patchJob.Defconfig
	// }
	data := map[string]interface{}{
		"UpdatedAt": time.Now(),
		"TaskId":    tempName,
		// "Defconfig":     defconfig,
		// "PatchFileOriginName": PatchMakejobReq.PatchFileOriginName,
		"MakeStatus": 0,
	}

	if PatchMakejobReq.Defconfig != "" {
		data["Defconfig"] = patchJob.Defconfig
	}

	if PatchMakejobReq.PatchFileOriginName != "" {
		data["PatchFileOriginName"] = PatchMakejobReq.PatchFileOriginName
	}

	err2 := patchJob.Update(patchJob.Id, data)

	if err2 != nil {
		logging.ErrorLogger.Errorf("update patch job err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	//开始编译
	MakePatchjob(patchJob)

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func UnitpackagePatchJob(patchjob *dpatchjob.Response) {
	// token := libs.Config.Buildfarm.Token

	client, err := SSHClient(patchjob.ServerId)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		updatePatchJob(patchjob, 2, "")
		return
	}
	//拉取仓库代码到指定目录

	defer client.Close()
	f, err := os.OpenFile(filepath.Join(libs.Config.PatchFileStorage.Temp, "patchjob_"+patchjob.JobId+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		updatePatchJob(patchjob, 2, "")
		return
	}
	defer f.Close()
	// repo_url := strings.Replace(libs.Config.PatchFileStorage.Gitrepo, "http://", fmt.Sprintf("http://oauth2:%s@", token), -1)
	repo_url := libs.Config.PatchFileStorage.Gitrepo
	command := fmt.Sprintf("git clone %s %s -v && cd %s && git checkout %s", repo_url, patchjob.Dir, patchjob.Dir, libs.Config.PatchFileStorage.Branch)
	f.WriteString("执行拉取项目操作，并切换分支\r\n")

	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updatePatchJob(patchjob, 2, "")
		return
	}

	command = fmt.Sprintf("cd %s && git rev-parse HEAD", patchjob.Dir)
	var stdOut bytes.Buffer
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updatePatchJob(patchjob, 2, "")
		return
	}

	version := strings.Replace(stdOut.String(), "\n", "", -1)

	if len(version) == 40 {
		updatePatchJob(patchjob, 1, version)
		f.WriteString(fmt.Sprintf("当前版本为：%s\r\n", version))
	} else {
		updatePatchJob(patchjob, 1, "")
		logging.ErrorLogger.Errorf("获取版本号异常", patchjob.JobId)
		f.WriteString(fmt.Sprintf("获取版本号异常：%s\r\n", version))
	}

	f.WriteString(fmt.Sprintf("拉取项目任务完成\r\n"))
	// 拷贝tar文件到远程服务器上
	server := &dserver.Response{}
	err = server.Find(patchjob.ServerId)
	tempPatchFile := patchjob.LocalDir + "/" + patchjob.PatchFileName
	if err != nil {
		logging.ErrorLogger.Errorf("create patchjob find server get err ", err)
	}
	//scp  -o stricthostkeychecking=no   /opt/soft/demo.tar root@************:/opt/soft/scptest
	command2 := fmt.Sprintf("scp -o stricthostkeychecking=no -P %s  %s %s@%s:%s", strconv.Itoa(int(server.Port)), tempPatchFile, server.Username, server.Host, patchjob.Dir)
	//记录日志
	f.WriteString(fmt.Sprintf("执行文件同步操作，执行命令如下:\r\n%s\r\n", command2))
	cmd := exec.Command("scp", "-o", "stricthostkeychecking=no", "-P", strconv.Itoa(int(server.Port)), tempPatchFile, fmt.Sprintf("%s@%s:%s", server.Username, server.Host, patchjob.Dir))
	var out bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &stderr
	err = cmd.Run()
	outStr, errStr := string(out.Bytes()), string(stderr.Bytes())
	if err != nil {
		logging.ErrorLogger.Errorf("scp command error", err.Error())
		updatePatchJob(patchjob, 2, "")
		return
	}
	f.WriteString(fmt.Sprintf("返回结果:\r\n%s\r\n", outStr))
	f.WriteString(fmt.Sprintf("错误信息:\r\n%s\r\n", errStr))

}

func FunctionPatchJob(ctx iris.Context, patchjob *dpatchjob.Response) {
	// token := libs.Config.Buildfarm.Token

	cronMakeJob := dcronmakejob.CronMakeJob{}
	err := cronMakeJob.Find(patchjob.CronMakeJobID)
	if err != nil {
		updatePatchJob(patchjob, 2, "")
		logging.ErrorLogger.Error("PatchJob: Find CronMakeJob Error", patchjob.Id, patchjob.CronMakeJobID, err)
		return
	}

	if cronMakeJob.Id == 0 {
		updatePatchJob(patchjob, 2, "")
		logging.ErrorLogger.Error("PatchJob: CronMakeJob Not Found", patchjob.Id, patchjob.CronMakeJobID)
		return
	}

	client, err := SSHClient(patchjob.ServerId)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		updatePatchJob(patchjob, 2, "")
		return
	}
	//拉取仓库代码到指定目录

	defer client.Close()
	f, err := os.OpenFile(filepath.Join(libs.Config.PatchFileStorage.Temp, "patchjob_"+patchjob.JobId+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		updatePatchJob(patchjob, 2, "")
		return
	}
	defer f.Close()
	// repo_url := strings.Replace(libs.Config.PatchFileStorage.Gitrepo, "http://", fmt.Sprintf("http://oauth2:%s@", token), -1)
	repo_url := libs.Config.PatchFileStorage.HotpatchRepo
	command := fmt.Sprintf("git clone -b %s %s %s -v", cronMakeJob.Branch, repo_url, patchjob.Dir)
	f.WriteString("执行拉取项目操作，并切换分支\r\n")

	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updatePatchJob(patchjob, 2, "")
		return
	}

	command = fmt.Sprintf("cd %s && git rev-parse HEAD", patchjob.Dir)
	var stdOut bytes.Buffer
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updatePatchJob(patchjob, 2, "")
		return
	}

	version := strings.Replace(stdOut.String(), "\n", "", -1)

	if len(version) == 40 {
		updatePatchJob(patchjob, 1, version)
		f.WriteString(fmt.Sprintf("当前版本为：%s\r\n", version))
	} else {
		updatePatchJob(patchjob, 1, "")
		logging.ErrorLogger.Errorf("获取版本号异常", patchjob.JobId)
		f.WriteString(fmt.Sprintf("获取版本号异常：%s\r\n", version))
	}

	f.WriteString(fmt.Sprintf("拉取项目任务完成\r\n"))
	// 拷贝tar文件到远程服务器上

}

func updatePatchJob(patch *dpatchjob.Response, status int, version string) {
	err := patch.Update(patch.Id, map[string]interface{}{
		"Status":    status,
		"UpdatedAt": time.Now(),
		"Version":   version,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("update patch job err ", err)
	}
}

func updateMakePatchJob(patch *dpatchjob.Response, makeStatus int, version string) {
	err := patch.Update(patch.Id, map[string]interface{}{
		"MakeStatus": makeStatus,
		"UpdatedAt":  time.Now(),
		"Version":    version,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("update patch job err ", err)
	}
}

func lsUnitPackagePatchDefConfigJob(patchjob *dpatchjob.Response) (map[string]interface{}, error) {
	defconfig := make(map[string]interface{})

	client, err := SSHClient(patchjob.ServerId)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		return defconfig, err
	}
	defer client.Close()
	f, err := os.OpenFile(filepath.Join(libs.Config.PatchFileStorage.Temp, "patchjob_"+patchjob.JobId+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		return defconfig, err
	}
	defer f.Close()
	command := fmt.Sprintf(`cd %s && make download`, patchjob.Dir)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("make download", err.Error())
		return defconfig, err
	}
	var stdOut bytes.Buffer
	command = fmt.Sprintf(`cd %s && make list-defconfigs|grep -v "="|grep defconfig|grep '-'|awk '{print $1}'`, patchjob.Dir)
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("get defconfig list", err.Error())
		return defconfig, err
	}

	// defconfig = strings.Split(stdOut.String(), "\n")[:len(strings.Split(stdOut.String(), "\n"))-1]
	// 通过ch返回运行状态为运行

	//获取配置数据
	patchDefconfigDatas, dfErr := dpatchdefconfig.FindAll()
	if dfErr != nil {
		return defconfig, dfErr
	}
	defconfig["items"] = patchDefconfigDatas

	return defconfig, nil
}

func lsFunctionPatchDefConfigJob(patchjob *dpatchjob.Response) (map[string]interface{}, error) {
	defconfig := make(map[string]interface{})

	client, err := SSHClient(patchjob.ServerId)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		return defconfig, err
	}
	defer client.Close()
	f, err := os.OpenFile(filepath.Join(libs.Config.PatchFileStorage.Temp, "patchjob_"+patchjob.JobId+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		return defconfig, err
	}
	defer f.Close()

	var stdOut bytes.Buffer
	command := fmt.Sprintf(`cd %s && ls ./package/ |grep -E '^HP'`, patchjob.Dir)

	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("get defconfig list", err.Error())
		return defconfig, err
	}

	defconfig["items"] = strings.Split(stdOut.String(), "\n")[:len(strings.Split(stdOut.String(), "\n"))-1]
	// defconfig = strings.Split(stdOut.String(), "\n")[:len(strings.Split(stdOut.String(), "\n"))-1]
	// 通过ch返回运行状态为运行

	return defconfig, nil
}

func GetPatchDefConfigs(ctx iris.Context) {
	id, _ := dao.GetId(ctx)

	patchjob := &dpatchjob.Response{}
	err := patchjob.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("get gitjob err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if patchjob.Id == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	var list map[string]interface{}
	if patchjob.PatchForm == 0 {
		list, err = lsUnitPackagePatchDefConfigJob(patchjob)
		if err != nil {
			logging.ErrorLogger.Errorf("create user get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, list, response.SystemErr.Msg))
			return
		}
	} else if patchjob.PatchForm == 1 {
		list, err = lsFunctionPatchDefConfigJob(patchjob)
		if err != nil {
			logging.ErrorLogger.Errorf("create user get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, list, response.SystemErr.Msg))
			return
		}
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func MakePatchjob(patchjob *dpatchjob.Response) {
	if patchjob.PatchForm == 0 || patchjob.Type == "测试" {
		UnitpackagePatchJob(patchjob)
		go MakeUnitPackagePatchjob(patchjob)
	} else if patchjob.PatchForm == 1 && patchjob.Type == "生产" {
		go MakeFunctionPatchjob(patchjob)
	}
}

func MakeFunctionPatchjob(patchjob *dpatchjob.Response) {
	cronMakeJob := dcronmakejob.CronMakeJob{}
	err := cronMakeJob.Find(patchjob.CronMakeJobID)
	if err != nil {
		updateMakePatchJob(patchjob, 2, "")
		logging.ErrorLogger.Error("PatchJob: Find CronMakeJob Error", patchjob.Id, patchjob.CronMakeJobID, err)
		return
	}

	if cronMakeJob.Id == 0 {
		updateMakePatchJob(patchjob, 2, "")
		logging.ErrorLogger.Error("PatchJob: CronMakeJob Not Found", patchjob.Id, patchjob.CronMakeJobID)
		return
	}

	version := ""
	updateMakePatchJob(patchjob, 0, version)
	client, err := SSHClient(patchjob.ServerId)
	if err != nil {
		logging.ErrorLogger.Errorf("MakePatchjob get err ", err)
		updateMakePatchJob(patchjob, 2, version)
		return
	}

	defer client.Close()
	f, err := os.OpenFile(filepath.Join(libs.Config.PatchFileStorage.Temp, "Make_patchjob_"+patchjob.TaskId+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		logging.ErrorLogger.Errorf("MakePatchjob get err ", err)
		updateMakePatchJob(patchjob, 2, version)
		return
	}
	defer f.Close()
	command := fmt.Sprintf("cd %s && git rev-parse HEAD", patchjob.Dir)
	var stdOut bytes.Buffer
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakePatchJob(patchjob, 2, version)
		return
	}

	getVersion := strings.Replace(stdOut.String(), "\n", "", -1)
	if len(getVersion) == 40 {
		version = getVersion
		updateMakePatchJob(patchjob, 0, version)
	} else {
		logging.ErrorLogger.Errorf("获取版本号异常", patchjob.Id)
		updateMakePatchJob(patchjob, 2, version)
		return
	}
	f.WriteString(fmt.Sprintf("当前版本为：%s\r\n", version))
	// http://10.51.135.15:9090/output/fcd3421edb5ad19c2da588a18954b0e5/armada7k-aarch64/armada7k-aarch64-image.tar.bz2
	imageUrl := fmt.Sprintf("http://10.51.135.15:9090/output/%s/%s/%s-image.tar.bz2", cronMakeJob.JobId, cronMakeJob.Cpu.Cpu, cronMakeJob.Cpu.Cpu)
	command = fmt.Sprintf("cd %s && make download && make install-devel IMG=%s", patchjob.Dir, imageUrl)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakePatchJob(patchjob, 2, version)
		return
	}
	command = fmt.Sprintf(`cd %s && echo 'BR2_PACKAGE_PRODUCT="%s"' >> output/.config && make %s`, patchjob.Dir, cronMakeJob.Product, patchjob.PatchFileOriginName)
	f.WriteString(fmt.Sprintf("\r\n%s\r\n", command))
	//执行最后编译命令
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakePatchJob(patchjob, 2, version)
		return
	}

	// 打包HPxxxx.rpm
	command = fmt.Sprintf(`cd %s && fp=$(find ./ -name %s*.rpm); tar -cvf %s.tar -C $(dirname $fp) $(basename $fp)`, patchjob.Dir, patchjob.PatchFileOriginName, patchjob.PatchFileOriginName)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakePatchJob(patchjob, 2, version)
		return
	}

	// 拉取补丁仓库
	repo_url := libs.Config.PatchFileStorage.Gitrepo
	command = fmt.Sprintf("cd %s && git clone -b %s %s -v", patchjob.Dir, libs.Config.PatchFileStorage.Branch, repo_url)
	f.WriteString("执行拉取项目操作，并切换分支\r\n")

	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakePatchJob(patchjob, 2, "")
		return
	}

	// 拼接命令字符串
	patchOrginal := fmt.Sprintf("%s/%s.tar", patchjob.Dir, patchjob.PatchFileOriginName) //补丁原始包
	patchOBJModel := patchjob.AdapterModel                                               //适配产品,用|分割
	patchOBJHardware := patchjob.AdapterHardware                                         //适配硬件
	patchOBJComponentName := patchjob.PatchComponentName                                 //组件补丁名
	patchOBJPath := patchjob.PatchObjectPath                                             //补丁对象路径
	patchOBJForm := patchjob.PatchForm                                                   //补丁形式：0组件补丁 1函数补丁
	patchUpgradeType := patchjob.PatchUpgradeType                                        //补丁升级方式 0自动升级 1手动升级 2强制升级 默认手动
	patchType := patchjob.PatchType                                                      //补丁类型 0：单组件补丁  1 补丁集合包 默认单组件
	patchOBJSW := patchjob.SoftwareVersion                                               //适配软件版本
	patchBase := patchjob.Baseline                                                       //补丁基线版本
	patchID := patchjob.Id                                                               //补丁版本
	patchFileDesc := patchjob.PatchFileDesc                                              //补丁描述
	crypt := patchjob.Crypt
	command = fmt.Sprintf(`cd %s/patch_build && make download && make %s && make PATCH_ORGINAL="%s" PATCH_OBJ_MODEL="%s" PATCH_OBJ_HW="%s" PATCH_OBJ_CMPNT="%s" PATCH_OBJ_FORM=%d PATCH_UPGRADE_TYPE=%d PATCH_TYPE=%d PATCH_OBJ_SW="%s" PATCH_BASE="%s" PATCH_VERSION=%d PATCH_DESC="%s" PATCH_OBJ_PATH="%s" CRYPT="%s"`,
		patchjob.Dir, patchjob.Defconfig, patchOrginal, patchOBJModel, patchOBJHardware, patchOBJComponentName, patchOBJForm, patchUpgradeType, patchType, patchOBJSW, patchBase, patchID, patchFileDesc, patchOBJPath, crypt)
	//执行最后编译命令
	f.WriteString(fmt.Sprintf("\r\n%s\r\n", command))
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakePatchJob(patchjob, 2, version)
		return
	}

	//更新状态
	updateMakePatchJob(patchjob, 1, version)
	if err := ArchiveCompileOutput(patchjob); err == nil {
		//保存文件信息（文件大小、MD5值）生成update.json文件
		//保存RMP文件名称
		rmpFilePath, findErr := findRPMFilePath(patchjob)
		if findErr != nil {
			logging.ErrorLogger.Errorf("rmpFile get err ", findErr)
			return
		}
		rmpFileName := strings.ReplaceAll(rmpFilePath, patchjob.Dir+"/patch_build/output/", "")
		daoErr := patchjob.Update(patchjob.Id, map[string]interface{}{
			"RPMFileName": rmpFileName,
			"UpdatedAt":   time.Now(),
		})
		if daoErr != nil {
			logging.ErrorLogger.Errorf("save rpm update patch job err ", daoErr)
		}
		if err := createConfigFile(patchjob, rmpFileName); err == nil {
			//删除远程服务器编译目录
			command = fmt.Sprintf("rm -rf %s", patchjob.Dir)
			if err := RunCommandOutFile(client, command, f); err != nil {
				logging.ErrorLogger.Errorf("run command error", err.Error())
				return
			}
		}
	}

}

func MakeUnitPackagePatchjob(patchjob *dpatchjob.Response) {

	version := ""
	updateMakePatchJob(patchjob, 0, version)
	client, err := SSHClient(patchjob.ServerId)
	if err != nil {
		logging.ErrorLogger.Errorf("MakePatchjob get err ", err)
		updateMakePatchJob(patchjob, 2, version)
		return
	}

	defer client.Close()
	f, err := os.OpenFile(filepath.Join(libs.Config.PatchFileStorage.Temp, "Make_patchjob_"+patchjob.TaskId+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		logging.ErrorLogger.Errorf("MakePatchjob get err ", err)
		updateMakePatchJob(patchjob, 2, version)
		return
	}
	defer f.Close()
	command := fmt.Sprintf("cd %s && git rev-parse HEAD", patchjob.Dir)
	var stdOut bytes.Buffer
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakePatchJob(patchjob, 2, version)
		return
	}

	getVersion := strings.Replace(stdOut.String(), "\n", "", -1)
	if len(getVersion) == 40 {
		version = getVersion
		updateMakePatchJob(patchjob, 0, version)
	} else {
		logging.ErrorLogger.Errorf("获取版本号异常", patchjob.Id)
		updateMakePatchJob(patchjob, 2, version)
		return
	}
	f.WriteString(fmt.Sprintf("当前版本为：%s\r\n", version))

	command = fmt.Sprintf("cd %s && make download && make %s", patchjob.Dir, patchjob.Defconfig)
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", err.Error())
		updateMakePatchJob(patchjob, 2, version)
		return
	}
	//拼接命令字符串
	patchOrginal := patchjob.Dir + "/" + patchjob.PatchFileName //补丁原始包
	patchOBJModel := patchjob.AdapterModel                      //适配产品,用|分割
	patchOBJHardware := patchjob.AdapterHardware                //适配硬件
	patchOBJComponentName := patchjob.PatchComponentName        //组件补丁名
	patchOBJPath := patchjob.PatchObjectPath                    //补丁对象路径
	patchOBJForm := patchjob.PatchForm                          //补丁形式：0组件补丁 1函数补丁
	patchUpgradeType := patchjob.PatchUpgradeType               //补丁升级方式 0自动升级 1手动升级 2强制升级 默认手动
	patchType := patchjob.PatchType                             //补丁类型 0：单组件补丁  1 补丁集合包 默认单组件
	patchOBJSW := patchjob.SoftwareVersion                      //适配软件版本
	patchBase := patchjob.Baseline                              //补丁基线版本
	patchID := patchjob.Id                                      //补丁版本
	patchFileDesc := patchjob.PatchFileDesc                     //补丁描述
	crypt := patchjob.Crypt
	command = fmt.Sprintf(`cd %s && make PATCH_ORGINAL="%s" PATCH_OBJ_MODEL="%s" PATCH_OBJ_HW="%s" PATCH_OBJ_CMPNT="%s" PATCH_OBJ_FORM=%d PATCH_UPGRADE_TYPE=%d PATCH_TYPE=%d PATCH_OBJ_SW="%s" PATCH_BASE="%s" PATCH_VERSION=%d PATCH_DESC="%s" PATCH_OBJ_PATH="%s" CRYPT="%s"`,
		patchjob.Dir, patchOrginal, patchOBJModel, patchOBJHardware, patchOBJComponentName, patchOBJForm, patchUpgradeType, patchType, patchOBJSW, patchBase, patchID, patchFileDesc, patchOBJPath, crypt)
	f.WriteString(fmt.Sprintf("\r\n%s\r\n", command))
	//执行最后编译命令
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", command, err.Error())
		updateMakePatchJob(patchjob, 2, version)
		return
	}
	//更新状态
	updateMakePatchJob(patchjob, 1, version)
	if err := ArchiveCompileOutput(patchjob); err == nil {
		//保存文件信息（文件大小、MD5值）生成update.json文件
		//保存RMP文件名称
		rmpFilePath, findErr := findRPMFilePath(patchjob)
		if findErr != nil {
			logging.ErrorLogger.Errorf("rmpFile get err ", findErr)
			return
		}
		rmpFileName := strings.ReplaceAll(rmpFilePath, patchjob.Dir+"/output/", "")
		daoErr := patchjob.Update(patchjob.Id, map[string]interface{}{
			"RPMFileName": rmpFileName,
			"UpdatedAt":   time.Now(),
		})
		if daoErr != nil {
			logging.ErrorLogger.Errorf("save rpm update patch job err ", daoErr)
		}
		if err := createConfigFile(patchjob, rmpFileName); err == nil {
			//删除远程服务器编译目录
			command = fmt.Sprintf("rm -rf %s", patchjob.Dir)
			if err := RunCommandOutFile(client, command, f); err != nil {
				logging.ErrorLogger.Errorf("run command error", err.Error())
				return
			}
		}
	}
}

// 拷贝远程编译服务器RPM文件到本地
func ArchiveCompileOutput(patchjob *dpatchjob.Response) error {
	server := &dserver.Response{}
	err := server.Find(patchjob.ServerId)
	if err != nil {
		logging.ErrorLogger.Errorf("create gitjob find server get err ", err)
	}
	archivePath := filepath.Join(libs.Config.PatchFileStorage.ArchivePath, patchjob.TaskId)
	err = os.MkdirAll(archivePath, 0755)
	os.Chmod(archivePath, 0755)

	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("mkdir before scp error: %s", err.Error()))
		return err
	}
	rmpFilePath, findErr := findRPMFilePath(patchjob)
	if findErr != nil {
		logging.ErrorLogger.Errorf("rmpFile get err ", findErr)
		return findErr
	}
	//编译输出文件夹拷贝
	// cmd := exec.Command("scp", "-o", "stricthostkeychecking=no", "-r", "-P", strconv.Itoa(int(server.Port)), fmt.Sprintf("%s@%s:/%s/output/", server.Username, server.Host, patchjob.Dir), archivePath)
	//编译输出RPM文件拷贝
	cmd2 := exec.Command("scp", "-o", "stricthostkeychecking=no", "-P", strconv.Itoa(int(server.Port)), fmt.Sprintf("%s@%s:/%s", server.Username, server.Host, rmpFilePath), archivePath)
	// var out,out2 bytes.Buffer
	// var stderr,stderr2 bytes.Buffer
	var out2 bytes.Buffer
	var stderr2 bytes.Buffer
	// cmd.Stdout = &out
	// cmd.Stderr = &stderr
	// err = cmd.Run()
	cmd2.Stdout = &out2
	cmd2.Stderr = &stderr2
	err = cmd2.Run()
	if err != nil {
		logging.ErrorLogger.Errorf("scp get err ", err, stderr2.String())
		return err
	}
	return nil
}

// 获取远程服务器上RPM包路径
func findRPMFilePath(patchjob *dpatchjob.Response) (string, error) {
	RPMFilePath := ""
	client, err := SSHClient(patchjob.ServerId)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		return RPMFilePath, err
	}
	defer client.Close()
	f, err := os.OpenFile(filepath.Join(libs.Config.PatchFileStorage.Temp, "patchjob_"+patchjob.JobId+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		return RPMFilePath, err
	}
	defer f.Close()
	var stdOut bytes.Buffer

	command := fmt.Sprintf(`find %s -name "*.pat" |awk '{print $1}'`, patchjob.Dir)
	if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
		logging.ErrorLogger.Errorf("get defconfig list", err.Error())
		return RPMFilePath, err
	}
	RPMFilePath = strings.Join(strings.Split(stdOut.String(), "\n")[:len(strings.Split(stdOut.String(), "\n"))-1], "")
	f.WriteString(fmt.Sprintf("RPM包路径:\r\n%s\r\n", RPMFilePath))
	return RPMFilePath, nil
}

// 获取补丁编译列表
func GetMakePatchJobs(ctx iris.Context) {
	userID := ctx.FormValue("user_id")
	fileOriginName := ctx.FormValue("file_origin_name")              //补丁包原名
	defconfig := ctx.FormValue("defconfig")                          //配置
	start := ctx.FormValue("start")                                  //开始日期
	end := ctx.FormValue("end")                                      //结束日期
	makeStatus := ctx.FormValue("make_status")                       //编译状态 3：排队，-1:未启动,0：运行， 1：成功， 2: 失败
	baseline := ctx.FormValue("baseline")                            //基线版本
	componentName := ctx.FormValue("component_name")                 //补丁组件名
	patchForm, _ := strconv.Atoi(ctx.FormValue("patch_form"))        //补丁形式
	typeClass := ctx.FormValue("type")                               //编译类型 生产 测试
	patch_version, _ := strconv.Atoi(ctx.FormValue("patch_version")) //补丁版本 1:旧版本 2:新版本
	patch_type, _ := strconv.Atoi(ctx.FormValue("patch_type"))       //补丁类型 0：单组件补丁  1 补丁集合包
	isdependen, _ := strconv.Atoi(ctx.FormValue("isdependen"))       //补丁依赖情况 0：无依赖 1：有依赖

	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	makejobs, err := dpatchjob.FindMakePatchJobs(userID, fileOriginName, start, end, makeStatus, baseline, defconfig, componentName, sort, orderBy, typeClass, patchForm, patch_type, patch_version, isdependen, page, pageSize)

	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, makejobs, response.NoErr.Msg))
	return
}

func GetMakePatchJob(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	job := dpatchjob.Response{}
	err := job.Find(id)

	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, job, response.NoErr.Msg))
	return
}

// 查看编译日志
func ShowPatchLog(ctx iris.Context) {
	jobId := ctx.FormValue("jobId")
	if jobId = strings.ReplaceAll(jobId, " ", ""); jobId == "" {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "作业id参数错误"))
		return
	}
	patchJob := &dpatchjob.Response{}
	err := patchJob.FindPatchJob(jobId)
	if err != nil {
		logging.ErrorLogger.Errorf("get patchJob err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if patchJob.Id == 0 {
		logging.ErrorLogger.Errorf("get patchJob err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "任务不存在"))
		return
	}
	if _, err := os.Stat(filepath.Join(libs.Config.PatchFileStorage.Temp, "Make_patchjob_"+patchJob.TaskId+".log")); os.IsNotExist(err) {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "日志文件不存在"))
		return
	}
	fd, err := os.Open(filepath.Join(libs.Config.PatchFileStorage.Temp, "Make_patchjob_"+patchJob.TaskId+".log"))
	defer fd.Close()
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	buff := bufio.NewReader(fd)
	var chunks []byte
	dataBuff := make([]byte, 1024)
	for {
		n, err := buff.Read(dataBuff)
		if err != nil && err != io.EOF {
			ctx.WriteString("获取文件错误！")
			return
		}
		if 0 == n {
			break
		}
		chunks = append(chunks, dataBuff[:n]...)
	}
	ctx.WriteString(string(chunks))
	return
}

// 下载RPM包文件
func DownloadRPMFile(ctx iris.Context) {
	jobId := ctx.FormValue("jobId")
	if jobId = strings.ReplaceAll(jobId, " ", ""); jobId == "" {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "作业id参数错误"))
		return
	}
	patchJob := &dpatchjob.Response{}
	err := patchJob.FindPatchJob(jobId)
	if err != nil {
		logging.ErrorLogger.Errorf("get patchJob err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if patchJob.Id == 0 {
		logging.ErrorLogger.Errorf("get patchJob err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "任务不存在"))
		return
	}
	archivePath := libs.Config.PatchFileStorage.ArchivePath + "/" + patchJob.TaskId
	ZipFile := ""
	if patchJob.PatchVersion == 1 {
		ZipFile = strings.TrimSuffix(patchJob.RPMFileName, ".pat") + ".zip"
		if _, err := os.Stat(filepath.Join(archivePath, ZipFile)); os.IsNotExist(err) {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "文件不存在"))
			return
		}
	} else {
		if patchJob.PatchType == 0 {
			ZipFile = strings.TrimSuffix(patchJob.RPMFileName, ".sig") + ".zip"
			if _, err := os.Stat(filepath.Join(archivePath, ZipFile)); os.IsNotExist(err) {
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "文件不存在"))
				return
			}
		} else {
			ZipFile = strings.TrimSuffix(patchJob.RPMFileName, ".MIX") + ".zip"
			if _, err := os.Stat(filepath.Join(archivePath, ZipFile)); os.IsNotExist(err) {
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "文件不存在"))
				return
			}
		}
	}

	var downloadFile = filepath.Join(archivePath + "/" + ZipFile)
	ctx.SendFile(downloadFile, url.QueryEscape(ZipFile))
	return
}

// 直接下载RPM原始文件
func DownloadRawRPMFile(ctx iris.Context) {
	jobId := ctx.FormValue("jobId")
	if jobId = strings.ReplaceAll(jobId, " ", ""); jobId == "" {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "作业id参数错误"))
		return
	}
	patchJob := &dpatchjob.Response{}
	err := patchJob.FindPatchJob(jobId)
	if err != nil {
		logging.ErrorLogger.Errorf("get patchJob err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if patchJob.Id == 0 {
		logging.ErrorLogger.Errorf("get patchJob err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "任务不存在"))
		return
	}

	if patchJob.RPMFileName == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "补丁RPM文件名为空"))
		return
	}

	//获取路径
	archivePath := libs.Config.PatchFileStorage.ArchivePath + "/" + patchJob.TaskId
	rpmFilePath := filepath.Join(archivePath, patchJob.RPMFileName)

	// 检查文件是否存在
	if _, err := os.Stat(rpmFilePath); os.IsNotExist(err) {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "RPM文件不存在"))
		return
	}

	ctx.SendFile(rpmFilePath, url.QueryEscape(patchJob.RPMFileName))
	return
}

// 获取产品软件版本
func GetSoftVersions(ctx iris.Context) {
	_, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dsoftversions.ListResponse{}, ctx, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

// 获取产品软件版本
func GetReleaseSoftVersions(ctx iris.Context) {
	_, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dsoftversions.ListResponse{}, ctx, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

// 创建update.json配置文件
func createConfigFile(patchjob *dpatchjob.Response, rmpFileName string) error {

	type Primary struct {
		FileName    string `json:"file_name"`
		FileSize    uint   `json:"file_size"`
		FileMd5     string `json:"file_md5"`
		ReleaseDate string `json:"release_date"`
		FileDesc    string `json:"file_desc"`
		FileType    uint   `json:"file_type"`
	}
	type Extend struct {
		Version string `json:"version"`
		// VersionDesc   string   `json:"version_desc"`
		PatchForm     uint     `json:"patch_form"`
		PatchType     uint     `json:"patch_type"`
		UpgradeType   uint     `json:"upgrade_type"`
		ProductModels []string `json:"product_models"`
		SoftVersions  []string `json:"soft_versions"`
		HardVersions  []string `json:"hard_versions"`
		Component     string   `json:"component"`
		PatchObjPath  string   `json:"patch_obj_path"`
	}
	type UpdateConfig struct {
		Primary Primary `json:"primary"`
		Extend  Extend  `json:"extend"`
	}

	archivePath := filepath.Join(libs.Config.PatchFileStorage.ArchivePath, patchjob.TaskId) //保存路径
	//文件信息保存
	rpmFilePath := filepath.Join(archivePath, rmpFileName)
	fileInfo, err := os.Stat(rpmFilePath)
	if err != nil {
		logging.ErrorLogger.Errorf("file err ", err)
	}
	md5, _ := libs.GetFileMd5(filepath.Join(rpmFilePath)) //MD5值
	daoErr := patchjob.Update(patchjob.Id, map[string]interface{}{
		"RPMFileSize": fileInfo.Size(),
		"RPMFileMd5":  md5,
	})
	if daoErr != nil {
		logging.ErrorLogger.Errorf("save rpm md5 update patch job err ", daoErr)
	}
	primary := Primary{
		FileName:    rmpFileName,
		FileSize:    uint(fileInfo.Size()),
		FileMd5:     md5,
		ReleaseDate: patchjob.CreatedAt.Format("2006-01-02"),
		FileDesc:    strings.Replace(patchjob.PatchFileDesc, `\n`, "\n", -1),
		FileType:    1,
	}

	extend := Extend{
		Version: fmt.Sprintf("%d", patchjob.Id),
		// VersionDesc:   strings.Replace(patchjob.PatchFileDesc, `\n`, "\n", -1),
		PatchForm:     patchjob.PatchForm,
		PatchType:     patchjob.PatchType,
		UpgradeType:   patchjob.PatchUpgradeType,
		ProductModels: strings.Split(patchjob.AdapterModel, "|"),
		SoftVersions:  strings.Split(patchjob.SoftwareVersion, "|"),
		HardVersions:  strings.Split(patchjob.AdapterHardware, "|"),
		Component:     strings.ReplaceAll(patchjob.PatchComponentName, "|", ","),
		PatchObjPath:  patchjob.PatchObjectPath,
	}

	updateConfig := UpdateConfig{
		Primary: primary,
		Extend:  extend,
	}
	//创建json文件
	filePtr, err := os.Create(filepath.Join(archivePath, "update.json"))
	if err != nil {
		logging.ErrorLogger.Errorf("Create file failed", err.Error())
		return err
	}
	defer filePtr.Close()
	//创建Json编码器
	encoder := json.NewEncoder(filePtr)
	err = encoder.Encode(updateConfig)
	if err != nil {
		return err
	}
	//将rmp包和update.json打包成zip 文件
	//要压缩成一个zip的多个文件的路径
	jsonFilePath := filepath.Join(archivePath, "update.json")
	files := []string{filepath.Join(archivePath, rmpFileName), jsonFilePath}
	ZipFileName := strings.TrimSuffix(rmpFileName, ".pat")
	//设置输出的zip的路径
	ZipFilePath := filepath.Join(archivePath, ZipFileName+".zip")
	if err := ZipFiles(ZipFilePath, files); err != nil {
		logging.ErrorLogger.Errorf("Create ZipFile failed", err.Error())
		return err
	}
	return nil

}

// 选择编译服务器
func ChoosePatchServer() uint {
	servers, err := dserver.FindAllServer()
	if err != nil {
		logging.ErrorLogger.Errorf("get all running job get err ", err)
		return 0
	}
	if len(servers) > 0 {
		rand.Seed(time.Now().UnixNano())
		return servers[rand.Intn(len(servers))].Id
	}
	return 0
}

func ZipFiles(filename string, files []string) error {
	//创建输出文件目录
	newZipFile, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer newZipFile.Close()
	//创建空的zip档案，准备写入
	zipWriter := zip.NewWriter(newZipFile)
	defer zipWriter.Close()
	// Add files to zip
	for _, file := range files {
		if err = AddFileToZip(zipWriter, file); err != nil {
			return err
		}
	}
	return nil
}

func AddFileToZip(zipWriter *zip.Writer, fileName string) error {
	if len(fileName) < 1 {
		return fmt.Errorf("将要压缩的文件列表不能为空")
	}
	//打开要压缩的文件
	fileToZip, err := os.Open(fileName)
	if err != nil {
		return err
	}
	defer fileToZip.Close()
	//获取文件的描述
	info, err := fileToZip.Stat()
	if err != nil {
		return err
	}
	//FileInfoHeader返回一个根据fi填写了部分字段的Header，可以理解成是将fileinfo转换成zip格式的文件信息
	header, err := zip.FileInfoHeader(info)
	if err != nil {
		return err
	}
	header.Name = filepath.Base(fileName)
	if info.IsDir() {
		header.Name += "/"
	}
	/*
	   预定义压缩算法。
	   archive/zip包中预定义的有两种压缩方式
	   Store   unit16 = 0  //仅存储文件
	   Deflate unit16 = 8  //压缩文件
	*/
	header.Method = zip.Deflate
	//创建压缩包头部信息
	writer, err := zipWriter.CreateHeader(header)
	if err != nil {
		return err
	}
	//将源复制到目标，将fileToZip 写入writer 是按默认的缓冲区32k循环操作的
	_, err = io.Copy(writer, fileToZip)
	return err
}

func unzip(pathname, fpath string) error {
	err := os.MkdirAll(pathname, 0750)
	os.Chmod(pathname, 0750)
	if err != nil {
		return err
	}
	r, err := zip.OpenReader(fpath)
	if err != nil {
		return errors.New("系统存在问题，请联系开发！")
	}
	defer r.Close()
	for _, k := range r.Reader.File {
		if k.FileInfo().IsDir() {
			err := os.MkdirAll(pathname+k.Name, 0750)
			os.Chmod(pathname, 0750)
			if err != nil {
				return errors.New("系统存在问题，请联系开发！")
			}
			continue
		}

		r, err := k.Open()
		if err != nil {
			return errors.New("请勿使用加密压缩包或压缩包已损坏，请检查")
		}
		defer r.Close()
		NewFile, err := os.Create(pathname + k.Name)
		if err != nil {
			return err
		}
		io.Copy(NewFile, r)
		NewFile.Close()
	}
	return nil
}

// 打包文件夹
func tarFolder(tempDir, tarfile string) error {
	var stderr bytes.Buffer
	_, err := exec.LookPath("tar")
	command := fmt.Sprintf("tar -cf %s  *", tarfile)
	cmd := exec.Command("/bin/bash", "-c", command)
	cmd.Dir = tempDir
	cmd.Stderr = &stderr
	err = cmd.Run()
	if err != nil {
		return errors.New("打包失败，需要人工检验")
	}
	return nil
}
