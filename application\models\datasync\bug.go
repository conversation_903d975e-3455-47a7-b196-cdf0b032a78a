package datasync

import (
	"irisAdminApi/application/models"
	"time"
)

/*
"bugId":882211,
"bugBelong":"龙黎江",
"bugOwner":"龙黎江",
"bugTestCharger":"龙黎江",
"bugOs":"11.9PR82",
"bugProduct":"RG-EG5210-JP V1.00",
"workpacketName":"DHCPV6 client支持64,94选项",
"summary":"需要将动态获取配置的接口ID一起同步给tunnel",
"mainbugid":null,
"samebugid":null,
"thedate":"2022-10-12 18:47:33",
"lastupdatedate":"2022-10-19 09:41:37",
"bugState":"CHECKED",
"disabled":null
*/
type Bug struct {
	// models.ModelBase
	BugID               int        `gorm:"primarykey; autoIncrement:false" json:"bug_id" `
	BugSummary          string     `gorm:"not null; type:varchar(300)" json:"bug_summary" update:"1"`
	BugOwner            string     `gorm:"not null; type:varchar(60)" json:"bug_owner" update:"1"`
	BugOwnerGroup       string     `gorm:"not null; type:varchar(60)" json:"bug_owner_group" update:"1"`
	BugSubmitter        string     `gorm:"not null; type:varchar(60)" json:"bug_submitter" update:"1"`
	BugSubmitterGroup   string     `gorm:"not null; type:varchar(60)" json:"bug_submitter_group" update:"1"`
	BugBelong           string     `gorm:"not null; type:varchar(60)" json:"bug_belong" update:"1"`
	BugTestCharger      string     `gorm:"not null; type:varchar(60)" json:"bug_test_charger" update:"1"`
	BugTestChargerGroup string     `gorm:"not null; type:varchar(60)" json:"bug_test_charger_group" update:"1"`
	BugPstlName         string     `gorm:"not null; type:varchar(60)" json:"bug_pstl_name" update:"1"`
	BugGroupName        string     `gorm:"not null; type:varchar(60)" json:"bug_group_name" update:"1"`
	BugOS               string     `gorm:"not null; type:varchar(60)" json:"bug_os" update:"1"`
	BugProduct          string     `gorm:"not null; type:varchar(60)" json:"bug_product" update:"1"`
	BugWorkpacketName   string     `gorm:"not null; type:varchar(60)" json:"bug_workpacket_name" update:"1"`
	MainBugID           int        `gorm:"not null" json:"main_bug_id" update:"1"`
	SameBugID           int        `gorm:"not null" json:"same_bug_id" update:"1"`
	BugCreatedAt        time.Time  `gorm:"not null;" json:"bug_created_at" update:"1"`
	BugUpdatedAt        time.Time  `gorm:"not null;" json:"bug_updated_at" update:"1"`
	BugState            string     `gorm:"not null; type:varchar(60)" json:"bug_state" update:"1"`
	BugDisabled         bool       `gorm:"not null" json:"bug_disabled" update:"1"`
	BugPriority         string     `gorm:"not null; type:varchar(60)" json:"bug_priority" update:"1"` //严重性
	BugSeverity         string     `gorm:"not null; type:varchar(60)" json:"bug_severity" update:"1"` //优先级
	BugRepro            int        `gorm:"not null" json:"bug_repro" update:"1"`                      //可重复性 1必现，2有时重现 3未尝试重现 4尝试但未重现
	MirroredProjects    string     `gorm:"type:varchar(600)" json:"mirrored_projects"`
	ResolvedProjects    string     `gorm:"type:varchar(600)" json:"resolved_projects"`
	NeedMirrorProjects  string     `gorm:"type:varchar(600)" json:"need_mirror_projects"`
	MirrorStatus        uint       `gorm:"type:varchar(60)" json:"mirror_status"`  //镜像状态： 0: 未同步  1:已同步  2:部分同步
	ResolveStatus       uint       `gorm:"type:varchar(60)" json:"resolve_status"` //所有同步BUG是否已经全部解决    0: 未解决   1: 已解决
	BugCbdAt            *time.Time `json:"bug_cbd_at" update:"1"`
	BugCbtAt            *time.Time `json:"bug_cbt_at" update:"1"`
	BugDbdAt            *time.Time `json:"bug_dbd_at" update:"1"`
	BugDbtAt            *time.Time `json:"bug_dbt_at" update:"1"`
	BugGiveupAt         *time.Time `json:"bug_giveup_at" update:"1"`
	BugDelayAt          *time.Time `json:"bug_delay_at" update:"1"`
	BugResolvedAt       *time.Time `json:"bug_resolved_at" update:"1"`
	OverTime            *int       `json:"over_time"`
	BeforeOverTime      *int       `json:"before_over_time"`
	// OverTimeAt          *time.Time `json:"over_time_at"`
	Bugreportid       string `gorm:"type:varchar(60)" json:"bugreportid"  update:"1"`
	Resolvedok        string `gorm:"type:varchar(60)" json:"resolvedok" update:"1"`
	Needsendbugreport string `gorm:"type:varchar(60)" json:"needsendbugreport" update:"1"`
	CheckOwner        string `gorm:"type:varchar(60)" json:"check_owner" update:"1"`
	BugSource         string `gorm:"type:varchar(60)" json:"bug_source" update:"1"`
}

type SyncProject struct {
	models.ModelBase
	KeyWord string `gorm:"not null; type:varchar(30)" json:"key_word"`
}

type BugMirrorRecord struct {
	models.ModelBase
	MainBugID   int `gorm:"not null" json:"main_bug_id"`
	MirrorBugID int `gorm:"not null" json:"mirror_bug_id"`
}

type BugMirrorApproval struct {
	models.ModelBase
	BugID   int    `gorm:"not null" json:"bug_id"`
	BugOs   string `gorm:"not null" json:"bug_os"`
	Comment string `gorm:"not null; type:varchar(200)" json:"comment"`
	UserID  uint   `gorm:"not null" json:"user_id"`
}

type BugMirrorApprovalHistory struct {
	models.ModelBase
	BugID   int    `gorm:"not null" json:"bug_id"`
	BugOs   string `gorm:"not null" json:"bug_os"`
	Comment string `gorm:"not null; type:varchar(200)" json:"comment"`
	UserID  uint   `gorm:"not null" json:"user_id"`
	Action  string `grom:"not null" json:"action"`
}

type BugMirrorHistory struct {
	models.ModelBase
	MainBugID int  `gorm:"not null" json:"main_bug_id"`
	BugID     int  `gorm:"not null" json:"bug_id"`
	UserID    uint `gorm:"not null" json:"user_id"`
}

type BugLog struct {
	models.ModelBase
	BugID    int    `gorm:"not null" json:"bug_id"`
	LogID    int    `gorm:"uniqueIndex: idx_unique; not null" json:"log_id"`
	Username string `gorm:"not null; type:varchar(200)" json:"username"`
	Info     string `json:"info"`
}
