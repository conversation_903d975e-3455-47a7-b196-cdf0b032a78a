package performance

import (
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/performance/dperformance"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

func CreatePerformance(ctx iris.Context) {

	request := dperformance.Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	validErr := libs.Validate.Struct(&request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	request.Status = true
	check := []string{
		request.BasicThroughput,
		request.BasicCps,
		request.BasicCc,
		request.AppidThroughput,
		request.IpsThroughput,
		request.AppidThroughput,
		request.AvThroughput,
	}
	checkMap := []string{
		"BasicThroughput",
		"BasicCps",
		"BasicCc",
		"AppidThroughput",
		"IpsThroughput",
		"AppidThroughput",
		"AvThroughput",
	}

	data := map[string]interface{}{
		"CreatedAt": time.Now(),
		"VersionID": request.VersionID,
		"Version":   request.Version,
		"Product":   request.Product,
		"Branch":    request.Branch,
	}

	for idx, item := range check {
		if request.Status && strings.TrimSpace(item) == "0" {
			request.Status = false
		}
		if strings.TrimSpace(item) != "" {
			val, err := strconv.Atoi(strings.TrimSpace(item))
			if err != nil {
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, checkMap[idx]+":"+"不是有效的整型数据"))
				return
			}
			data[checkMap[idx]] = uint(val)
		} else {
			data[checkMap[idx]] = uint(0)
		}
	}

	data["Status"] = request.Status

	performance := dperformance.Performance{}
	err := performance.Create(data)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return

}

func GetPerformances(ctx iris.Context) {
	versionID := ctx.FormValue("version_id")
	product := ctx.FormValue("product")
	branch := ctx.FormValue("branch")
	version := ctx.FormValue("version")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")
	var status *bool
	_status, err := ctx.URLParamBool("status")
	if err == nil {
		status = &_status
	}
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dperformance.All(versionID, product, branch, version, start, end, status, sort, orderBy, page, pageSize)

	// list, err := dproblem.AllProblems(name, sort, orderBy, page, pageSize, status, start, end, department)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return

}
