package codesync

import (
	"irisAdminApi/application/models"
)

type CodeSyncPolicy struct {
	models.ModelBase
	RepositoryID   int    `gorm:"not null; type:varchar(10)" json:"repository_id"`
	RepositoryName string `gorm:"not null; type:varchar(100)" json:"repository_name"`
	RepositoryPath string `gorm:"not null; type:varchar(300)" json:"repository_path"`
	SourceBranch   string `gorm:"not null; type:varchar(300)" json:"source_branch"`
	TargetBranch   string `gorm:"not null; type:varchar(300)" json:"target_branch"`
	ExpiredAt      string `json:"expired_at"`
	UpdatedAfter   string `json:"updated_after"` //2023-01-09 14:00:00
	Enable         bool   `gorm:"not null; default:false" json:"enable"`
}

type CodeSyncQueue struct {
	models.ModelBase
	SourceMergeRequestID       string `gorm:"not null; type:varchar(10)" json:"source_merge_request_id"`
	SourceMergeRequestIID      string `gorm:"not null; type:varchar(10)" json:"source_merge_request_iid"`
	SourceMergeRequestUsername string `gorm:"not null; type:varchar(60)" json:"source_merge_request_username"`
	SourceWebUrl               string `gorm:"not null; type:varchar(300)" json:"source_web_url"`

	RepositoryID   string `gorm:"not null; type:varchar(10)" json:"repository_id"`
	RepositoryPath string `gorm:"not null; type:varchar(300)" json:"repository_path"`
	RepositoryName string `gorm:"not null; type:varchar(100)" json:"repository_name"`
	SourceBranch   string `gorm:"not null; type:varchar(300)" json:"source_branch"`
	TargetBranch   string `gorm:"not null; type:varchar(300)" json:"target_branch"`

	TargetMergeRequestID  string `gorm:"type:varchar(10)" json:"target_merge_request_id"`
	TargetMergeRequestIID string `gorm:"type:varchar(10)" json:"target_merge_request_iid"`
	TargetWebUrl          string `gorm:"type:varchar(300)" json:"target_web_url"`
	Log                   string ` json:"log"`
	Status                uint   `gorm:"not null" json:"status"` // 0 处理中 1 创建MR成功 2 创建MR失败 3 MR已合并 4 MR已关闭 5 未知状态
}

type CodeSyncQueueUser struct {
	models.ModelBase
	CodeSyncQueueID uint `json:"code_sync_queue_id"`
	UserID          uint `json:"user_id"`
}

type CodeSyncHistory struct {
	models.ModelBase
	URL           string `gorm:"not null;" json:"url"`
	RepositoryID  string `gorm:"not null; type:varchar(60)" json:"repository_id"`
	UpdatedAfter  string `gorm:"not null; type:varchar(60)" json:"updated_after"`
	UpdatedBefore string `gorm:"not null; type:varchar(60)" json:"updated_before"`
	Status        uint   `gorm:"not null" json:"status"` // 0 处理中 1 成功 2 失败
}

type CodeSyncProject struct {
	models.ModelBase
	RepositoryID   int    `gorm:"not null; type:varchar(10)" json:"repository_id"`
	RepositoryName string `gorm:"not null; type:varchar(100)" json:"repository_name"`
	RepositoryPath string `gorm:"not null; type:varchar(300)" json:"repository_path"`
}
