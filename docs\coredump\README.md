# Coredump记录自动化处理系统 - 增强版

## 📋 项目概述

基于现有的Go项目架构和飞书多维表格API，开发一个功能完整的自动化系统，实现飞书多维表格中coredump记录与bug管理系统的双向同步。

### 🎯 核心功能

- **定时处理机制**: 支持定时任务和手动触发处理
- **条件筛选**: 基于"是否需要同步Bug系统"和"是否已同步bug系统"字段筛选
- **分页读取**: 避免一次性加载过多数据，支持大数据量处理
- **状态回填**: Bug提交成功后自动回写飞书多维表格状态
- **错误处理**: 完善的错误处理和重试机制
- **人员字段优化**: Bug系统使用邮箱前缀，Bug描述显示完整姓名

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   定时调度器     │    │  Coredump处理   │    │   Bug管理系统   │
│                │    │     系统        │    │                │
│  - 定时任务     │────▶│  - 分页读取     │────▶│  - Bug提交     │
│  - 手动触发     │    │  - 条件筛选     │    │  - 状态返回     │
│  - 任务管理     │    │  - 字段映射     │    └─────────────────┘
└─────────────────┘    │  - 错误处理     │             │
                       │  - 状态回填     │             │
┌─────────────────┐    └─────────────────┘             │
│   飞书多维表格   │◄───────────┐                      │
│                │            │                      │
│  - 记录读取     │            │                      │
│  - 状态更新     │            │                      │
│  - 条件筛选     │            │                      │
│  - 分页查询     │            │                      │
└─────────────────┘            │                      │
                              │                      │
                    ┌─────────────────┐              │
                    │   本地存储       │              │
                    │                │              │
                    │ - 处理日志      │◄─────────────┘
                    │ - 任务状态      │
                    │ - 错误记录      │
                    │ - 配置缓存      │
                    └─────────────────┘
```

## 🔄 数据流向

```
定时触发 → 分页读取 → 条件筛选 → 字段映射 → Bug提交 → 状态回填 → 日志记录
    ↑                                                    ↓
    └─────────── 错误重试机制 ←─────────────────────────────┘
```

## 📁 项目结构

```
application/controllers/openfeishu/coredump/
├── coredump_scheduler.go        # 定时任务调度器
├── coredump_service.go          # 主要业务逻辑服务
├── coredump_models.go           # 数据模型定义
├── coredump_field_mapper.go     # 字段映射器
├── coredump_filter.go           # 条件筛选器
├── coredump_status_manager.go   # 飞书状态管理器
├── coredump_controller.go       # HTTP接口控制器
├── coredump_config.go           # 配置管理
└── coredump_test.go             # 单元测试
```

## 🔧 核心特性

### ✅ 增强功能实现

1. **定时处理机制**:
   - 支持Cron表达式配置定时任务
   - 可启动/停止定时任务
   - 支持手动触发处理
   - 任务超时控制和并发控制

2. **条件筛选机制**:
   - 筛选条件1：`是否需要同步Bug系统` = "Y"
   - 筛选条件2：`是否已同步bug系统` = ""或"N"
   - 支持跳过已知问题
   - 详细的筛选统计信息

3. **分页读取机制**:
   - 真正的分页处理，避免内存溢出
   - 支持配置每页大小
   - API限流控制
   - 分页处理统计

4. **飞书状态管理**:
   - 完全基于飞书多维表格的状态管理
   - 处理状态：待处理/处理中/成功/失败
   - 自动记录Bug ID、处理时间、错误信息
   - 重试次数统计和最后更新时间

5. **人员字段优化**:
   - Bug系统的用户字段：使用邮箱前缀（如：`zhangsan`）
   - Bug描述中的负责人：显示完整姓名（如：`张三`）
   - 联系邮箱单独显示，提高可读性

### 🎯 核心功能

- **定时处理**: 支持定时任务和手动触发
- **分页读取**: 从飞书多维表格分页读取coredump记录
- **条件筛选**: 基于同步状态字段进行精确筛选
- **字段映射**: 将飞书字段映射到Bug系统字段
- **人员处理**: 优先级策略：组件负责人 > 进程负责人
- **Bug提交**: 复用现有的BugSubmitter功能
- **状态管理**: 基于飞书表格的完整状态管理
- **错误处理**: 完善的错误处理和重试机制

## 📊 字段映射规则

### 直接映射
| 飞书字段 | Bug字段 | 说明 |
|---------|---------|------|
| coredump组件 | Product | 组件名称 |
| 软件版本 | VersionMess | 版本信息 |
| 设备型号 | DeviceUnderTestConfig | 设备型号 |
| coredump收集url | DebugMess | 调试链接 |

### 人员字段映射
- **ChargeCasUserid**: 从邮箱提取用户名（如：`<EMAIL>` → `zhangsan`）
- **优先级**: 组件负责人 > 进程负责人
- **Bug描述**: 显示完整姓名和邮箱

### 状态管理字段

#### 用户控制字段
| 飞书字段 | 用途 | 取值 | 说明 |
|---------|------|------|------|
| 是否需要同步Bug系统 | 筛选控制 | Y/N | 用户设置，控制是否需要同步 |

#### 系统管理字段
| 飞书字段 | 用途 | 取值 | 说明 |
|---------|------|------|------|
| 是否已同步bug系统 | 同步状态 | Y/N/"" | 系统自动更新 |
| 处理状态 | 处理状态 | ""(新记录)/待处理/处理中/成功/失败 | 系统自动管理 |
| Bug系统ID | Bug ID | 自动填入 | 成功时填入Bug ID |
| 处理时间 | 处理时间 | 自动填入 | 开始处理时填入 |
| 错误信息 | 错误详情 | 失败时填入 | 失败时填入详细错误 |
| 重试次数 | 重试统计 | 自动计数 | 系统自动统计 |
| 最后更新时间 | 更新时间 | 自动填入 | 每次状态变更时更新 |

#### 筛选逻辑
```
需要处理的记录 =
  是否需要同步Bug系统 = "Y"
  AND 是否已同步bug系统 ∈ ["", "N"]
  AND 处理状态 ∈ ["", "待处理", "失败", "处理中超时"]
```

### 生成字段
- **Summary**: `Coredump异常 - {组件} [{设备型号}] SN:{SN}`
- **TestCaseNum**: `COREDUMP_{SN}_{时间戳}`
- **Bugdescription**: HTML格式的详细描述

## 🚀 API接口

### 接口列表

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 手动处理 | POST | `/api/coredump/process` | 手动触发Coredump处理 |
| 处理状态 | GET | `/api/coredump/status` | 获取处理状态统计 |
| 启动定时任务 | POST | `/api/coredump/scheduler/start` | 启动定时任务 |
| 停止定时任务 | POST | `/api/coredump/scheduler/stop` | 停止定时任务 |
| 定时任务状态 | GET | `/api/coredump/scheduler/status` | 获取定时任务状态 |
| 处理历史 | GET | `/api/coredump/history` | 获取处理历史记录 |

### 响应示例

```json
{
  "success": true,
  "data": {
    "task_id": "task_1705234800",
    "start_time": "2025-01-14T10:00:00Z",
    "end_time": "2025-01-14T10:05:30Z",
    "duration": "5m30s",
    "total_records": 100,
    "filtered_records": 25,
    "success_records": 23,
    "failed_records": 2,
    "updated_records": 23,
    "update_failed_records": 0
  }
}
```

## ⚙️ 配置文件

### application.yml
```yaml
feishudoc:
  enable: true
  appid: "cli_xxxxxxxxxxxxxxxx"
  appsecret: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
  coredumpapptoken: "your_coredump_app_token"
  coredumptableid: "your_coredump_table_id"
  coredumpenable: true
```

### coredump_config.yaml
```yaml
coredump:
  page_size: 100
  field_names:
    - "SN"
    - "coredump收集url"
    - "coredump时间"
    - "coredump组件"
    - "堆栈信息"
    - "组件负责人"
    - "进程负责人"
    - "设备型号"
    - "软件版本"
    - "是否需要同步Bug系统"    # 新增筛选字段
    - "是否已同步bug系统"      # 新增状态字段
    - "Bug系统ID"             # 新增回填字段（可选）

  # 定时任务配置
  scheduler:
    enabled: true
    cron_expr: "0 0 * * * *"    # 每小时执行一次
    max_run_time: 30            # 最大运行30分钟
    concurrent: false           # 不允许并发执行

  default_submitter: "coredump_system"
  default_charge_user: "wuzhensheng"
  default_pstl: "wangchunping"

  skip_known_issues: true
```

## 🔍 条件筛选机制

### 筛选条件
```go
// 判断记录是否应该处理
func (f *CoredumpFilter) shouldProcess(record *CoredumpRecord) bool {
    // 筛选条件1: 是否需要同步Bug系统 = "Y"
    if record.SyncRequired != "Y" {
        return false
    }

    // 筛选条件2: 是否已同步bug系统 为空或 = "N"
    if record.SyncStatus != "" && record.SyncStatus != "N" {
        return false
    }

    return true
}
```

### 飞书状态管理机制
```go
// 更新处理状态
func (m *CoredumpStatusManager) UpdateProcessingStatus(recordID, status, bugID, errorMsg string) error {
    updateFields := map[string]interface{}{
        "处理状态": status,           // 更新处理状态
        "最后更新时间": time.Now(),   // 更新时间
    }

    if status == "成功" {
        updateFields["是否已同步bug系统"] = "Y"
        updateFields["Bug系统ID"] = bugID
        updateFields["处理时间"] = time.Now()
    } else if status == "失败" {
        updateFields["错误信息"] = errorMsg
    }
    // 调用飞书API更新记录...
}
```

## 📝 部署步骤

1. **环境准备**
   ```bash
   # 创建必要目录
   mkdir -p cache logs config
   ```

2. **配置文件**
   ```bash
   # 复制配置模板
   cp docs/coredump/coredump_config.yaml config/
   # 编辑配置文件
   vim config/application.yml
   ```

3. **编译运行**
   ```bash
   go build -o coredump-system main.go
   ./coredump-system
   ```

4. **测试验证**
   ```bash
   # 健康检查
   curl http://localhost:8080/health
   
   # 手动触发处理
   curl -X POST http://localhost:8080/api/coredump/process
   ```

## 🎯 优势总结

### ✅ 核心优势
- **定时处理**: 支持自动化定时处理和手动触发
- **条件筛选**: 精确的字段条件筛选机制
- **分页处理**: 支持大数据量处理，避免内存溢出
- **飞书状态管理**: 完全基于飞书表格的状态管理，用户可见
- **人员字段优化**: 提高Bug描述的可读性
- **配置驱动**: 灵活的字段映射和任务配置
- **无本地存储**: 简化部署，数据完全透明

### 🔧 技术特点
- **基于现有架构**: 最大化复用现有代码
- **单一数据源**: 飞书表格作为唯一数据存储
- **模块化设计**: 各组件职责清晰，易于维护
- **并发控制**: 避免重复执行和资源冲突
- **错误处理**: 完善的重试和告警机制
- **用户友好**: 所有状态对用户完全透明

## 📚 文档结构

### 核心设计文档
- `coredump_system_design.md` - 完整技术设计文档
- `coredump_enhanced_implementation.md` - 详细代码实现方案
- `field_design_specification.md` - 字段设计规范文档
- `optimization_summary.md` - 系统优化总结

### 配置和部署文档
- `field_mapping_config.md` - 字段映射配置方案
- `error_handling_monitoring.md` - 错误处理和监控方案
- `deployment_testing_guide.md` - 部署和测试指南
- `deployment_checklist.md` - 部署检查清单

### 工具和脚本
- `api_test_script.sh` - API接口测试脚本

## 🚀 快速开始

1. 查看 `coredump_system_design.md` 了解整体设计
2. 参考 `coredump_implementation_guide.md` 进行代码实现
3. 使用 `deployment_testing_guide.md` 进行部署和测试

---

**注意**: 这是优化版设计，完全基于飞书多维表格进行状态管理，无需本地存储，实现了定时处理、条件筛选、分页读取和状态管理等核心功能，用户体验更加透明。