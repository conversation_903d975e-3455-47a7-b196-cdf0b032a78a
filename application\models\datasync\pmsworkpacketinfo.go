package datasync

import "time"

type PmsWorkpacketInfo struct {
	// models.ModelBase
	WorkPacketID int `gorm:"not null; index:idx_unique, unique" json:"work_packet_id" `
	RelationID   int `gorm:"not null; index:idx_unique, unique" json:"relation_id" `

	WorkPacketName    string `gorm:"not null; type:varchar(100)" json:"work_packet_name" update:"1"`
	ProjectID         int    `gorm:"not null" json:"project_id" update:"1"`
	ProjectName       string `gorm:"not null; type:varchar(100)" json:"project_name" update:"1" update:"1"`
	ProjectChangeType string `gorm:"not null; type:varchar(100)" json:"project_change_type" update:"1"`
	Disabled          bool   `gorm:"not null" json:"disabled" update:"1"`

	RequestID          int     `gorm:"not null" json:"request_id" update:"1"`
	AddCodes           float32 `gorm:"not null" json:"add_codes" update:"1"`
	TotalCodes         float32 `gorm:"not null" json:"total_codes" update:"1"`
	TransplantCodes    float32 `gorm:"not null" json:"transplant_codes" update:"1"`
	TransplantCodesTmp float32 `gorm:"not null" json:"transplant_codes_tmp" update:"1"`
	GroupName          string  `gorm:"not null" json:"group_name" update:"1"`
	PacketManagerName  string  `gorm:"not null" json:"packet_manager_name" update:"1"`
	FinishPercent      int     `gorm:"not null; default:0" json:"finish_percent" update:"1"`

	ChangeTypeName    string     `gorm:"not null; type:varchar(100)" json:"change_type_name" update:"1"`
	RelationSituation string     `gorm:"not null; type:varchar(100)" json:"relation_situation" update:"1"`
	PstlName          string     `gorm:"not null; type:varchar(100)" json:"pstl_name" update:"1"`
	PlanStartDate     *time.Time ` json:"plan_start_date" update:"1"`
	PlanEndDate       *time.Time ` json:"plan_end_date" update:"1"`
	PlanTime          float32    `gorm:"not null" json:"plan_time" update:"1"`
	ActStartDate      *time.Time ` json:"act_start_date" update:"1"`
	ActEndDate        *time.Time ` json:"act_end_date" update:"1"`
	UseTime           float32    `gorm:"not null" json:"use_time" update:"1"`
	KeyWord           string     `gorm:"not null; type:varchar(100)" json:"key_word" update:"1"`
	DeliverSituation  string     `gorm:"not null; type:varchar(100)" json:"deliver_situation" update:"1"`
	StageName         string     `gorm:"not null; type:varchar(100)" json:"stage_name" update:"1"`
}
