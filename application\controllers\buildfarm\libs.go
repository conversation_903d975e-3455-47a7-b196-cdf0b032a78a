package buildfarm

import (
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/url"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/buildfarm/dbuildfarmaudit"
	"irisAdminApi/service/dao/buildfarm/dbuildfarmauditblacklist"
	"irisAdminApi/service/dao/buildfarm/dbuildfarmauditdetail"
	"irisAdminApi/service/dao/buildfarm/dbuildfarmdifflevelrule"
	"irisAdminApi/service/dao/buildfarm/dcronmakejob"
	"irisAdminApi/service/dao/buildfarm/dgitjob"
	"irisAdminApi/service/dao/buildfarm/dmakejob"
	"irisAdminApi/service/dao/buildfarm/dproject"
	"irisAdminApi/service/dao/buildfarm/dserver"
	"irisAdminApi/service/dao/user/dgitlabtoken"
	"irisAdminApi/service/dao/user/duserwhitelist"

	"github.com/imroc/req/v3"
	"github.com/jinzhu/copier"
	"github.com/pkg/errors"
	"golang.org/x/crypto/ssh"
)

func SSHConnect(gitjob *dgitjob.GitJob) (*ssh.Session, error) {
	var (
		auth         []ssh.AuthMethod
		addr         string
		clientConfig *ssh.ClientConfig
		client       *ssh.Client
		session      *ssh.Session
		err          error
	)

	server := &dserver.Response{}
	err = server.Find(gitjob.ServerID)
	if err != nil {
		logging.ErrorLogger.Errorf("create gitjob find server get err ", err)
		return nil, err
	}

	// user, password, host string, port int
	host := server.Host
	port := int(server.Port)
	user := server.Username
	sshKey := server.Key

	if !sshKey {
		logging.ErrorLogger.Errorf("connect server get err ", err)
		return nil, errors.New("服务器添加异常，请联系管理员")
	}
	// 临时服务器
	// host = "*************"
	// port = 22
	// user = "linjiakai"
	// password := ""

	auth = make([]ssh.AuthMethod, 0)

	if len(libs.Config.Rsa.Privatekey) > 0 {
		key, err := ioutil.ReadFile(libs.Config.Rsa.Privatekey)
		if err != nil {
			return nil, err
		}
		// Create the Signer for this private key.
		signer, err := ssh.ParsePrivateKey(key)
		if err != nil {
			return nil, err
		}
		auth = append(auth, ssh.PublicKeys(signer))
	}

	// get auth method
	// if len(password) > 0 {
	// 	auth = append(auth, ssh.Password(password))
	// }

	// hostKeyCallbk := func(hostname string, remote net.Addr, key ssh.PublicKey) error {
	// 	return nil
	// }

	clientConfig = &ssh.ClientConfig{
		User: user,
		Auth: auth,
		// Timeout:             30 * time.Second,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	}

	// connet to ssh
	addr = fmt.Sprintf("%s:%d", host, port)

	if client, err = ssh.Dial("tcp", addr, clientConfig); err != nil {
		return nil, err
	}

	// create session
	if session, err = client.NewSession(); err != nil {
		return nil, err
	}

	return session, nil
}

func SSHClientV2(user, host string, port int) (*ssh.Client, error) {
	var (
		auth         []ssh.AuthMethod
		addr         string
		clientConfig *ssh.ClientConfig
		client       *ssh.Client
		err          error
	)

	auth = make([]ssh.AuthMethod, 0)

	if len(libs.Config.Rsa.Privatekey) > 0 {
		key, err := ioutil.ReadFile(libs.Config.Rsa.Privatekey)
		if err != nil {
			return nil, err
		}
		// Create the Signer for this private key.
		signer, err := ssh.ParsePrivateKey(key)
		if err != nil {
			return nil, err
		}
		auth = append(auth, ssh.PublicKeys(signer))
	}

	clientConfig = &ssh.ClientConfig{
		User: user,
		Auth: auth,
		// Timeout:             30 * time.Second,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	}

	// connet to ssh
	addr = fmt.Sprintf("%s:%d", host, port)

	if client, err = ssh.Dial("tcp", addr, clientConfig); err != nil {
		return nil, err
	}

	return client, nil
}

func SSHClient(serverId uint) (*ssh.Client, error) {
	var (
		auth         []ssh.AuthMethod
		addr         string
		clientConfig *ssh.ClientConfig
		client       *ssh.Client
		err          error
	)

	server := &dserver.Response{}
	err = server.Find(serverId)
	if err != nil {
		logging.ErrorLogger.Errorf("create gitjob find server get err ", err)
		return nil, err
	}

	// user, password, host string, port int
	host := server.Host
	port := int(server.Port)
	user := server.Username
	sshKey := server.Key

	if !sshKey {
		logging.ErrorLogger.Errorf("connect server get err ", err)
		return nil, errors.New("服务器添加异常，请联系管理员")
	}
	// 临时服务器
	// host = "*************"
	// port = 22
	// user = "linjiakai"
	// password := ""

	auth = make([]ssh.AuthMethod, 0)

	if len(libs.Config.Rsa.Privatekey) > 0 {
		key, err := os.ReadFile(libs.Config.Rsa.Privatekey)
		if err != nil {
			return nil, err
		}
		// Create the Signer for this private key.
		signer, err := ssh.ParsePrivateKey(key)
		if err != nil {
			return nil, err
		}
		auth = append(auth, ssh.PublicKeys(signer))
	}

	// get auth method
	// if len(password) > 0 {
	// 	auth = append(auth, ssh.Password(password))
	// }

	// hostKeyCallbk := func(hostname string, remote net.Addr, key ssh.PublicKey) error {
	// 	return nil
	// }

	clientConfig = &ssh.ClientConfig{
		User: user,
		Auth: auth,
		// Timeout:             30 * time.Second,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	}

	// connet to ssh
	addr = fmt.Sprintf("%s:%d", host, port)

	if client, err = ssh.Dial("tcp", addr, clientConfig); err != nil {
		return nil, err
	}
	return client, nil
	// // create session
	// if session, err = client.NewSession(); err != nil {
	// 	return nil, err
	// }

	// return session, nil
}

func RunCommandOutFile(client *ssh.Client, command string, f *os.File) error {
	session, err := client.NewSession()
	if err != nil {
		return err
	}
	defer session.Close()
	f.WriteString(fmt.Sprintf("%s 执行命令: %s\r\n", time.Now().Format("2006-01-02 15:04:05"), command))
	// 输出日志文件
	session.Stderr = f
	session.Stdout = f
	err = session.Start(command)
	if err != nil {
		return err
	}

	err = session.Wait()
	if err != nil {
		return err
	}
	f.WriteString(fmt.Sprintf("\r\n%s 命令执行完成\r\n", time.Now().Format("2006-01-02 15:04:05")))
	return nil
}

func RunCommandOutBuffer(client *ssh.Client, command string, out *bytes.Buffer) error {
	session, err := client.NewSession()
	if err != nil {
		return err
	}
	defer session.Close()

	// 输出日志文件
	stdErr := bytes.Buffer{}
	session.Stdout = out
	session.Stderr = &stdErr
	err = session.Start(command)
	if err != nil {
		return err
	}
	err = session.Wait()
	if err != nil {
		return errors.Wrap(err, stdErr.String())
	}
	return nil
}

func GetToken(gitjob *dgitjob.GitJob) (string, error) {
	token := dgitlabtoken.Response{}
	err := token.FindEx("user_id", strconv.FormatUint(uint64(gitjob.UserID), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("get token for get git projects err ", err)
		return "", err
	}

	if len(token.Token) == 0 {
		logging.ErrorLogger.Errorf("user doesn't hava token for get git projects err ", err)
		return "", errors.New("没有可用的gitlab token，请前往用户资料进行添加")
	}
	return token.Token, nil
}

type Patch struct {
	PatchDirName string `json:"patch_dir_name"`
	FileName     string `json:"FileName"`
}

func MakeJobPatchTask(client *ssh.Client, f *os.File, workDir, jobId, project string) error {
	patches := []*Patch{}
	if _, err := os.Stat(filepath.Join("/tmp", jobId, "patches")); err != nil {
		f.WriteString(fmt.Sprintf("%s [INFO] 未检测到补丁文件,跳过patch动作。\r\n", time.Now().Format("2006-01-02 15:04:05")))
		return nil
	} else {
		f.WriteString(fmt.Sprintf("%s [INFO] 检测到补丁文件，正在执行patch动作。\r\n", time.Now().Format("2006-01-02 15:04:05")))
		if _, err := os.Stat(filepath.Join("/tmp", jobId, "patches", "patch_infos.txt")); err != nil {
			f.WriteString(fmt.Sprintf("%s [INFO] 未检测到补丁信息文件，跳过patch动作。\r\n", time.Now().Format("2006-01-02 15:04:05")))
			return nil
		} else {
			patch_info, err := ioutil.ReadFile(filepath.Join("/tmp", jobId, "patches", "patch_infos.txt"))
			if err != nil {
				f.WriteString(fmt.Sprintf("%s [ERROR] 读取补丁信息文件失败，跳过patch动作。\r\n", time.Now().Format("2006-01-02 15:04:05")))
				return errors.New("读取补丁信息文件失败")
			}
			err = json.Unmarshal(patch_info, &patches)
			if err != nil {
				f.WriteString(fmt.Sprintf("%s [ERROR] 解析补丁信息文件格式失败，跳过patch动作。\r\n", time.Now().Format("2006-01-02 15:04:05")))
				return errors.New("解析补丁信息文件失败")
			}
			patch_success_count := 0
			for _, patch := range patches {
				f.WriteString(fmt.Sprintf("%s [INFO] 开始记录patch详情\r\n", time.Now().Format("2006-01-02 15:04:05")))
				f.WriteString(fmt.Sprintf("%s [INFO] 组件%s, patch详情:\r\n", time.Now().Format("2006-01-02 15:04:05"), patch.PatchDirName))
				patch_info, err := ioutil.ReadFile(filepath.Join("/tmp", jobId, "patches", patch.FileName))
				if err != nil {
					f.WriteString(fmt.Sprintf("%s [ERROR] 读取补丁文件失败\r\n", time.Now().Format("2006-01-02 15:04:05")))
					continue
				}
				f.WriteString(string(patch_info) + "\r\n")
				command := fmt.Sprintf(`cd %s && patch -p1 < "%s"`, filepath.Join(workDir, project, patch.PatchDirName), filepath.Join(workDir, "patches", patch.FileName))
				if err := RunCommandOutFile(client, command, f); err != nil {
					logging.ErrorLogger.Errorf("run command error", command, err.Error())
					continue
				}
				patch_success_count++
			}
			if patch_success_count == 0 {
				f.WriteString(fmt.Sprintf("%s [ERROR] 补丁共%d个，成功%d个，失败%d个。\r\n", time.Now().Format("2006-01-02 15:04:05"), len(patches), patch_success_count, len(patches)-patch_success_count))
				return errors.New("部分补丁patch失败")
			} else {
				f.WriteString(fmt.Sprintf("%s [INFO] 补丁共%d个，成功%d个，失败%d个。\r\n", time.Now().Format("2006-01-02 15:04:05"), len(patches), patch_success_count, len(patches)-patch_success_count))
				return nil
			}
		}
	}
	// return errors.New("未知错误")
}

func MakeJobCustomTask(client *ssh.Client, f *os.File, workDir, jobId, project, product string, feedsConfigCustomized string) error {
	f.WriteString(fmt.Sprintf("%s [INFO] 正在执行定制编译操作\r\n", time.Now().Format("2006-01-02 15:04:05")))
	f.WriteString(fmt.Sprintf("%s [INFO] 开始记录原始feedsconfigdefault内容\r\n", time.Now().Format("2006-01-02 15:04:05")))
	f.WriteString("====================================================================================================\r\n")
	command := fmt.Sprintf("cat %s", filepath.Join(workDir, project, "project", "feeds.conf.default"))
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", command, err.Error())
		f.WriteString(fmt.Sprintf("%s [ERROR] 记录原始feedsconfigdefault内容失败\r\n", time.Now().Format("2006-01-02 15:04:05")))
		return err
	}
	f.WriteString("====================================================================================================\r\n")
	f.WriteString(fmt.Sprintf("%s [INFO] 开始更新feedsconfigdefault文件\r\n", time.Now().Format("2006-01-02 15:04:05")))
	command = fmt.Sprintf(`echo "%s" > %s`, feedsConfigCustomized, filepath.Join(workDir, project, "project", "feeds.conf.default"))
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", command, err.Error())
		f.WriteString(fmt.Sprintf("%s [ERROR] 更新feedsconfigdefault文件失败\r\n", time.Now().Format("2006-01-02 15:04:05")))
		return err
	}
	f.WriteString(fmt.Sprintf("%s [INFO] 开始记录更新后feedsconfigdefault内容\r\n", time.Now().Format("2006-01-02 15:04:05")))
	f.WriteString("====================================================================================================\r\n")
	command = fmt.Sprintf("cat %s", filepath.Join(workDir, project, "project", "feeds.conf.default"))
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", command, err.Error())
		f.WriteString(fmt.Sprintf("%s [ERROR] 记录更新后feedsconfigdefault内容失败\r\n", time.Now().Format("2006-01-02 15:04:05")))
		return err
	}
	f.WriteString("====================================================================================================\r\n")
	f.WriteString(fmt.Sprintf("%s [INFO] 开始记录更新后配置文件内容\r\n", time.Now().Format("2006-01-02 15:04:05")))
	f.WriteString("====================================================================================================\r\n")
	command = fmt.Sprintf("cat %s", filepath.Join(workDir, project, "configs", product+"_defconfig"))
	if err := RunCommandOutFile(client, command, f); err != nil {
		logging.ErrorLogger.Errorf("run command error", command, err.Error())
		f.WriteString(fmt.Sprintf("%s [ERROR] 记录更新后feedsconfigdefault内容失败\r\n", time.Now().Format("2006-01-02 15:04:05")))
		return err
	}
	f.WriteString("====================================================================================================\r\n")
	return nil
}

func CustomKernelConfigPrevTask(client *ssh.Client, f *os.File, gitjob *dgitjob.GitJob, makejob *dmakejob.MakeJob) error {
	// 增加内核文件处理流程
	// 硬编码替换内核文件
	f.WriteString(fmt.Sprintf("%s [INFO] 检测是否存在自定义内核文件\r\n", time.Now().Format("2006-01-02 15:04:05")))
	customKernelConfigTempDir, err := GetTempDir(gitjob.JobID, "kernel_config")
	if err != nil {
		f.WriteString(fmt.Sprintf("%s [INFO] 未检测到存在自定义内核文件\r\n", time.Now().Format("2006-01-02 15:04:05")))
	} else {
		customKernelConfigFiles, err := os.ReadDir(customKernelConfigTempDir)
		if err != nil {
			logging.ErrorLogger.Errorf("read kernel path err ", err)
			return err
		}

		if len(customKernelConfigFiles) > 1 || len(customKernelConfigFiles) == 0 {
			f.WriteString(fmt.Sprintf("%s [INFO] 检测到自定义内核文件不存在或者存在多个\r\n", time.Now().Format("2006-01-02 15:04:05")))
			return errors.New("检测到自定义内核文件不存在或者存在多个")
		}
		customKernelConfigFile := filepath.Join(customKernelConfigTempDir, customKernelConfigFiles[0].Name())
		server := &dserver.Response{}
		err = server.Find(makejob.ServerID)
		if err != nil {
			logging.ErrorLogger.Errorf("find server get err ", err)
			return err
		}
		// 获取kernel_config目标路径
		command := fmt.Sprintf("cd %s/prj_%s/configs/ && cat %s_defconfig |grep 'BR2_LINUX_KERNEL_CUSTOM_CONFIG_FILE'", gitjob.Dir, makejob.Product, makejob.Product)
		stdOut := *bytes.NewBuffer([]byte{})
		if err := RunCommandOutBuffer(client, command, &stdOut); err != nil {
			logging.ErrorLogger.Errorf("run command error", command, err.Error())
			return err
		}

		lineArr := strings.Split(strings.ReplaceAll(stdOut.String(), "\n", ""), "=")
		targetKernelConfigFile := strings.ReplaceAll(lineArr[len(lineArr)-1], `"`, ``)
		fullTargetKernelConfigFile := strings.ReplaceAll(targetKernelConfigFile, `$(BR2_EXTERNAL_BSP_DRIVER_PATH)`, fmt.Sprintf("%s/prj_%s/bspdriver", gitjob.Dir, makejob.Product))
		f.WriteString(fmt.Sprintf("%s [INFO] 记录更新前自定义内核文件内容\r\n", time.Now().Format("2006-01-02 15:04:05")))
		command = fmt.Sprintf("cat %s", fullTargetKernelConfigFile)
		if err := RunCommandOutFile(client, command, f); err != nil {
			logging.ErrorLogger.Errorf("run command error", command, err.Error())
			return err
		}
		f.WriteString("==============================================================================================================\r\n")
		cmd := exec.Command("scp", "-o", "stricthostkeychecking=no", "-r", "-P", strconv.Itoa(int(server.Port)), customKernelConfigFile, fmt.Sprintf("%s@%s:%s", server.Username, server.Host, fullTargetKernelConfigFile))
		// cmd := exec.Command("scp", "-o", "stricthostkeychecking=no", "-r", "-P", "22", tempPatchDir, fmt.Sprintf("%s@%s:%s", "linjiakai", "127.0.0.1", filepath.Join(libs.Config.Buildfarm.Compilepath, "test123123")))
		var out bytes.Buffer
		var stderr bytes.Buffer
		cmd.Stdout = &out
		cmd.Stderr = &stderr
		err = cmd.Run()
		if err != nil {
			logging.ErrorLogger.Errorf("scp file failed", err, out.String(), stderr.String())
			return err
		}

		f.WriteString(fmt.Sprintf("%s [INFO] 记录更新后自定义内核文件内容\r\n", time.Now().Format("2006-01-02 15:04:05")))
		command = fmt.Sprintf("cat %s", fullTargetKernelConfigFile)
		if err := RunCommandOutFile(client, command, f); err != nil {
			logging.ErrorLogger.Errorf("run command error", command, err.Error())
			return err
		}
		f.WriteString("==============================================================================================================\r\n")
	}
	return nil
}

func BuildTypePrevTask(client *ssh.Client, f *os.File, workDir, product, jobId, buildType string) error {
	f.WriteString(fmt.Sprintf("%s [INFO] 检测当前编译类型，目前支持debug、release、gcov编译类型。\r\n", time.Now().Format("2006-01-02 15:04:05")))
	f.WriteString(fmt.Sprintf("%s [INFO] 检测当前编译类型为：%s。\r\n", time.Now().Format("2006-01-02 15:04:05"), buildType))
	switch buildType {
	case "release":
		f.WriteString(fmt.Sprintf("%s [INFO] 执行切换操作\r\n", time.Now().Format("2006-01-02 15:04:05")))
		f.WriteString(fmt.Sprintf("%s [INFO] 记录编译配置文件信息：\r\n", time.Now().Format("2006-01-02 15:04:05")))
		if product == "" {
			command := fmt.Sprintf(`cd %s && find ./ -regextype posix-extended -regex '.+[_-]perf(.mk|_defconfig)' |xargs -I {} bash -c "echo 切换使用：{} && cat {} && target=\$(echo {}|sed 's/[_-]perf//') && mv {} \$target"`, workDir)
			if err := RunCommandOutFile(client, command, f); err != nil {
				logging.ErrorLogger.Errorf("run command error", command, err.Error())
				f.WriteString(fmt.Sprintf("%s [ERROR] 切换配置文件失败：%s\r\n", time.Now().Format("2006-01-02 15:04:05"), err.Error()))
				return err
			}
		} else {
			command := fmt.Sprintf(`cd %s/prj_%s && find ./ -regextype posix-extended -regex '.+[_-]perf(.mk|_defconfig)' |xargs -I {} bash -c "echo 切换使用：{} && cat {} && target=\$(echo {}|sed 's/[_-]perf//') && mv {} \$target"`, workDir, product)
			if err := RunCommandOutFile(client, command, f); err != nil {
				logging.ErrorLogger.Errorf("run command error", command, err.Error())
				f.WriteString(fmt.Sprintf("%s [ERROR] 切换配置文件失败：%s\r\n", time.Now().Format("2006-01-02 15:04:05"), err.Error()))
				return err
			}
		}

		f.WriteString(fmt.Sprintf("%s [INFO] 切换操作完成。\r\n", time.Now().Format("2006-01-02 15:04:05")))
	case "debug":
		if product != "" {
			f.WriteString(fmt.Sprintf("%s [INFO] 执行切换操作\r\n", time.Now().Format("2006-01-02 15:04:05")))
			command := fmt.Sprintf(
				`cd %s/prj_%s &&
				echo $'\nBR2_PACKAGE_DPDK_MEMPOOL_DEBUG=y' >> ./configs/%s_defconfig &&
				echo $'BR2_PACKAGE_DPDK_MALLOC_DEBUG=y' >> ./configs/%s_defconfig &&
				echo $'BR2_PACKAGE_PYTHON_UN_ENCRYPTION=y' >> ./configs/%s_defconfig &&
				echo $'BR2_BUILD_DEBUG=y' >> ./configs/%s_defconfig &&
				cat ./configs/%s_defconfig`,
				workDir,
				product,
				product,
				product,
				product,
				product,
				product,
			)

			// command := fmt.Sprintf(`cd %s/prj_%s && echo "BR2_PACKAGE_DPDK_MEMPOOL_DEBUG=y" >> ./configs/%s_defconfig && echo "BR2_PACKAGE_DPDK_MALLOC_DEBUG=y" >> ./configs/%s_defconfig &&  cat ./configs/%s_defconfig`, workDir, product, product, product, product)
			if err := RunCommandOutFile(client, command, f); err != nil {
				logging.ErrorLogger.Errorf("run command error", err.Error())
				f.WriteString(fmt.Sprintf("%s [ERROR] 切换配置文件失败：%s, %s\r\n", time.Now().Format("2006-01-02 15:04:05"), buildType, err.Error()))
				return err
			}
		}
	case "gcov":
		if product != "" {
			f.WriteString(fmt.Sprintf("%s [INFO] 执行切换操作\r\n", time.Now().Format("2006-01-02 15:04:05")))
			// echo $'\n#BR2_STRIP_STRIP is not set' >> ./configs/%s_defconfig &&
			command := fmt.Sprintf(
				`cd %s/prj_%s &&

			echo $'\nBR2_PACKAGE_DPDK_MEMPOOL_DEBUG=y' >> ./configs/%s_defconfig &&
			echo $'BR2_PACKAGE_DPDK_MALLOC_DEBUG=y' >> ./configs/%s_defconfig &&
			echo $'BR2_PACKAGE_PYTHON_UN_ENCRYPTION=y' >> ./configs/%s_defconfig &&
			echo $'BR2_BUILD_GCOV=y' >> ./configs/%s_defconfig && cat ./configs/%s_defconfig`,
				workDir,
				// product,
				product,
				product,
				product,
				product,
				product,
				product,
			)
			// command := fmt.Sprintf(`cd %s/prj_%s && echo "BR2_PACKAGE_DPDK_MEMPOOL_DEBUG=y" >> ./configs/%s_defconfig && echo "BR2_PACKAGE_DPDK_MALLOC_DEBUG=y" >> ./configs/%s_defconfig &&  cat ./configs/%s_defconfig`, workDir, product, product, product, product)
			if err := RunCommandOutFile(client, command, f); err != nil {
				logging.ErrorLogger.Errorf("run command error", err.Error())
				f.WriteString(fmt.Sprintf("%s [ERROR] 切换配置文件失败：%s, %s\r\n", time.Now().Format("2006-01-02 15:04:05"), buildType, err.Error()))
				return err
			}
		}
	case "factory":
		if product != "" {
			f.WriteString(fmt.Sprintf("%s [INFO] 执行切换操作\r\n", time.Now().Format("2006-01-02 15:04:05")))
			command := fmt.Sprintf(`cd %s/prj_%s/project && if [ -f "feeds.conf.default" ];then mv feeds.conf.default feeds.conf.default.bak && cat feeds.conf.default.bak|grep -E 'bsp-driver|factory|common' > feeds.conf.default;fi`, workDir, product)
			if err := RunCommandOutFile(client, command, f); err != nil {
				logging.ErrorLogger.Errorf("run command error", err.Error())
				return err
			}
		}
	default:
		f.WriteString(fmt.Sprintf("%s [INFO] 跳过切换操作。\r\n", time.Now().Format("2006-01-02 15:04:05")))
	}

	return nil
	// return errors.New("未知错误")
}

func BuildTypePostTask(client *ssh.Client, f *os.File, jobId, buildType string) error {
	f.WriteString(fmt.Sprintf("%s [INFO] 输出编译类型信息。\r\n", time.Now().Format("2006-01-02 15:04:05")))
	archivePath := filepath.Join(libs.Config.Buildfarm.Archivepath, jobId)
	command := fmt.Sprintf(`cd %s && echo %s > %s/$(ls)/build_type && echo %s > %s/$(ls)/releaseID-bin/build_type `, archivePath, buildType, archivePath, buildType, archivePath)
	cmd := exec.Command("bash", "-c", command)
	var out bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &stderr
	err := cmd.Run()
	if err != nil {
		logging.ErrorLogger.Errorf("write build type info err ", cmd, err, stderr.String(), out.String())
		f.WriteString(fmt.Sprintf("%s [ERROR] 输出编译类型信息失败: %s, %s, %s\r\n", time.Now().Format("2006-01-02 15:04:05"), err.Error(), stderr.String(), out.String()))
		return err
	}

	f.WriteString(fmt.Sprintf("%s [INFO] 输出编译类型信息成功。\r\n", time.Now().Format("2006-01-02 15:04:05")))
	switch buildType {
	case "debug", "factory", "gcov":
		keyword := "_install.bin"
		command = fmt.Sprintf(`cd %s && cd $(ls)/releaseID-bin && ls|grep %s|sed 's/%s//'|xargs -i mv {}%s {}_%s%s`, archivePath, keyword, keyword, keyword, buildType, keyword)
		cmd = exec.Command("bash", "-c", command)
		err = cmd.Run()
		if err != nil {
			logging.ErrorLogger.Errorf("rename output filename err ", cmd, err, stderr.String(), out.String())
			f.WriteString(fmt.Sprintf("%s [ERROR] 输出编译类型信息失败: %s, %s, %s\r\n", time.Now().Format("2006-01-02 15:04:05"), err.Error(), stderr.String(), out.String()))
			return err
		}
		f.WriteString(fmt.Sprintf("%s [INFO] 输出文件名增加编译类型信息。\r\n", time.Now().Format("2006-01-02 15:04:05")))
	}

	// 归档检查与主仓差异
	// 1. 查找所有.git
	// 2. 查看该目录 remote -v

	return nil
	// return errors.New("未知错误")
}

func CheckPersonalGit(client *ssh.Client, jobID, taskID, dir string, userID uint) (bool, error) {
	workDir := filepath.Join(libs.Config.Buildfarm.CheckPersonalGit.WorkDir, jobID)
	err := os.MkdirAll(workDir, 0o666)
	if err != nil {
		logging.ErrorLogger.Errorf("create work dir err ", err, workDir)
		return false, err
	}

	logFile, err := os.OpenFile(filepath.Join(workDir, taskID+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
	if err != nil {
		logging.ErrorLogger.Errorf("create log file err ", err, taskID)
		return false, err
	}

	logger := logging.NewLogger(&logging.Options{
		TimesFormat: logging.TIMESECOND,
	})
	logger.SetOutput(logFile)

	ret := bytes.Buffer{}
	command := fmt.Sprintf("cd %s && find ./ -name .git|sed 's@/.git@@g'", dir)
	logger.Debugf("查找编译目录下所有仓库:", command)
	if err := RunCommandOutBuffer(client, command, &ret); err != nil {
		logger.Errorf("run command error", err.Error(), command, ret.String())
		return false, err
	}
	logger.Debugf("所有仓库:", ret.String())

	auditorID, _ := dbuildfarmaudit.GetAuditorID(userID)
	// 无评审人员，暂时先指派给林家楷评审。
	if auditorID == 0 {
		auditorID = 45
	}

	auditDetails := []map[string]interface{}{}
	now := time.Now()
	maxLevel := uint(0)
	minStatus := uint(1)

	var lastAuditTaskID string
	oldAudits, err := dbuildfarmaudit.FindAuditPassByJobID(jobID)
	if err != nil {
		logger.Errorf("find old audits err", err.Error())
	}
	if len(oldAudits) > 0 {
		lastAuditTaskID = oldAudits[0].TaskID
	}

	for _, subDir := range strings.Split(ret.String(), "\n") {
		if len(subDir) == 0 {
			continue
		}
		var isProductRepo bool = false
		var isPersonalRepo bool = false
		subDirRet := bytes.Buffer{}
		command := fmt.Sprintf("cd %s && cd %s && git remote -v |grep origin|grep push|grep -E 'aqyfzx.ruijie.net|10.51.135.102'|awk '{print $2}'", dir, subDir)
		logger.Debugf("查找NTOS仓库:", command)
		if err := RunCommandOutBuffer(client, command, &subDirRet); err != nil {
			logger.Errorf("run command error", err.Error(), command, subDirRet.String())
			return false, err
		}

		if len(strings.TrimSpace(subDirRet.String())) == 0 {
			logger.Debugf("非NTOS仓库:", subDirRet.String())
			continue
		}
		logger.Debugf("NTOS仓库:", subDirRet.String())

		repo := strings.TrimSpace(subDirRet.String())
		gitlabProjectPathID := GetGitlabProjectID(repo)
		logger.Debugf("ProjectID:", gitlabProjectPathID)
		projects, err := dproject.FindByRepoPathID(gitlabProjectPathID)
		if err != nil {
			logger.Errorf("find project by repo path id err", err.Error())
			return false, err
		}

		if len(projects) > 0 {
			isProductRepo = true
			logger.Debugf("生产仓库:", subDirRet.String())
			// continue
		}

		// 结果为空，git仓库非生产仓库，查找关联主仓
		forkedSshUrlToRepo, err := GetForkedFromProject(url.PathEscape(gitlabProjectPathID))
		if err != nil {
			logger.Errorf("get forked ssh url to repo error", err.Error())
			return false, err
		}

		if forkedSshUrlToRepo != "" {
			// 	logger.Errorf("非个人仓且不是生产仓库", repo)
			// 	continue
			// } else {
			isPersonalRepo = true
		} else {
			forkedSshUrlToRepo = repo
		}

		unshallowRet := bytes.Buffer{}
		command = fmt.Sprintf("cd %s && cd %s && git fetch --unshallow", dir, subDir)
		logger.Debug(command)
		if err := RunCommandOutBuffer(client, command, &unshallowRet); err != nil {
			if !strings.Contains(err.Error(), "complete repository") {
				logger.Errorf("run command error", err.Error(), command, unshallowRet.String())
				return false, err
			}
		}

		subDirRet = bytes.Buffer{}
		command = fmt.Sprintf("cd %s && cd %s && (git remote -v |grep upstream || git remote add upstream %s) && git fetch upstream", dir, subDir, forkedSshUrlToRepo)
		logger.Debug(command)
		if err := RunCommandOutBuffer(client, command, &subDirRet); err != nil {
			logger.Errorf("run command error", err.Error(), command, subDirRet.String())
			return false, err
		}

		upstreamBranch := bytes.Buffer{}
		// 修正部分情况下导致获取upstream branch为空
		command = fmt.Sprintf("cd %s && cd %s && for i in `git log --graph --pretty=format:'%%h' --abbrev-commit|grep '^\\*'|awk '{print $NF}'|head -n 1000`;do limit=`git branch -a --contains $i|grep upstream|wc -l`; if [ $limit -ge 1 ]; then git branch -a --contains $i|grep upstream|tail -n 1 && break; fi; done", dir, subDir)
		logger.Debug(command)
		if err := RunCommandOutBuffer(client, command, &upstreamBranch); err != nil {
			logger.Errorf("run command error", err.Error(), command, upstreamBranch.String())
			return false, err
		}
		upstreamBranchStr := strings.TrimSpace(upstreamBranch.String())

		// 增加限制，防止获取upstream分支失败后直接放行
		if upstreamBranchStr == "" {
			logger.Errorf("get upstream branch err", upstreamBranchStr)
			return false, err
		}

		currentBranch := bytes.Buffer{}
		command = fmt.Sprintf("cd %s && cd %s && git branch|grep '\\*'|awk '{print $NF}'", dir, subDir)
		logger.Debug(command)
		if err := RunCommandOutBuffer(client, command, &currentBranch); err != nil {
			logger.Errorf("run command error", err.Error(), command, currentBranch.String())
			return false, err
		}
		currentBranchStr := strings.TrimSpace(currentBranch.String())

		commitIDs := bytes.Buffer{}
		command = fmt.Sprintf("cd %s && cd %s && git log --pretty='%%H' %s..%s", dir, subDir, upstreamBranchStr, currentBranchStr)
		logger.Debug(command)
		if err := RunCommandOutBuffer(client, command, &commitIDs); err != nil {
			logger.Errorf("run command error", err.Error(), command, commitIDs.String())
			return false, err
		}

		upstreamLastCommitID := bytes.Buffer{}
		command = fmt.Sprintf("cd %s && cd %s && git log --pretty='%%H' %s | head -n 1", dir, subDir, upstreamBranchStr)
		logger.Debug(command)
		if err := RunCommandOutBuffer(client, command, &upstreamLastCommitID); err != nil {
			logger.Errorf("run command error", err.Error(), command, upstreamLastCommitID.String())
			return false, err
		}

		lastCommitID := bytes.Buffer{}
		command = fmt.Sprintf("cd %s && cd %s && git log --pretty='%%H' %s | head -n 1", dir, subDir, currentBranchStr)
		logger.Debug(command)
		if err := RunCommandOutBuffer(client, command, &lastCommitID); err != nil {
			logger.Errorf("run command error", err.Error(), command, lastCommitID.String())
			return false, err
		}

		diffFilePath := ""
		level := uint(0)
		if repo != forkedSshUrlToRepo {
			diff := bytes.Buffer{}
			// TODO: 优化检查逻辑，避免重新审批
			// 获取已审批记录，最后一个commitid,用于对比
			auditDetails, err := dbuildfarmauditdetail.FindAuditByjobIDAndTaskIDAndRepo(jobID, lastAuditTaskID, repo)
			if err != nil {
				logger.Errorf("run command error", err.Error(), command, diff.String())
				return false, err
			}

			if len(auditDetails) > 0 {
				command = fmt.Sprintf("cd %s && cd %s && git diff --binary %s...%s", dir, subDir, strings.TrimSpace(auditDetails[0].CurrentLastCommitID), lastCommitID.String())
				logger.Debug(command)
				if err := RunCommandOutBuffer(client, command, &diff); err != nil {
					logger.Errorf("run command error", err.Error(), command, diff.String())
					return false, err
				}
			} else {
				command = fmt.Sprintf("cd %s && cd %s && git diff --binary %s...%s", dir, subDir, upstreamBranchStr, currentBranchStr)
				logger.Debug(command)
				if err := RunCommandOutBuffer(client, command, &diff); err != nil {
					logger.Errorf("run command error", err.Error(), command, diff.String())
					return false, err
				}
			}

			diffFilePath = filepath.Join(workDir, taskID+"_"+strings.ReplaceAll(gitlabProjectPathID, "/", "-")+".diff")
			diffFile, err := os.OpenFile(diffFilePath, os.O_RDWR|os.O_CREATE|os.O_TRUNC, 0o666)
			if err != nil {
				logging.ErrorLogger.Errorf("create diff file err ", err, taskID)
				return false, err
			}

			defer diffFile.Close()
			_, err = diffFile.Write(diff.Bytes())
			if err != nil {
				logger.Errorf("write diff error", err.Error(), diff.String())
				return false, err
			}

			level, err = CheckDiffLevel(diffFilePath)
			if err != nil {
				logger.Errorf("检查风险等级遇到错误", err.Error())
				return false, err
			}
		}

		status := 0
		if isProductRepo {
			status = 1
		}

		auditDetails = append(auditDetails, map[string]interface{}{
			"CreatedAt":           now,
			"UpdatedAt":           now,
			"JobID":               jobID,
			"TaskID":              taskID,
			"CurrentRepo":         repo,
			"CurrentBranch":       currentBranchStr,
			"CurrentLastCommitID": lastCommitID.String(),
			"CommitIDs":           commitIDs.String(),

			"UpstreamRepo":         forkedSshUrlToRepo,
			"UpstreamBranch":       upstreamBranchStr,
			"UpstreamLastCommitID": upstreamLastCommitID.String(),

			"IsProduct":  isProductRepo,
			"IsPersonal": isPersonalRepo,
			"Diff":       diffFilePath,
			"UserID":     userID,
			"AuditorID":  auditorID,
			"Level":      level,
			"Status":     status,
		})

		if isPersonalRepo || !isProductRepo {
			minStatus = 0
		}

		if level >= uint(maxLevel) {
			maxLevel = level
		}
	}

	var comment string
	// 先判断是否属于高风险以上
	if maxLevel >= 3 {
		comment = "非白名单, 需要人工审批。"
		whitelist := duserwhitelist.UserWhiteList{}
		err = whitelist.FindEx("user_id", fmt.Sprintf("%v", userID))

		if err != nil {
			logger.Errorf("find user white list error ", err.Error())
			minStatus = 0
			comment = "白名单查询错误: " + err.Error()
		} else {
			if whitelist.Buildfarm {
				minStatus = 1
				comment = "白名单, 自动审批通过。"
			}
		}
	} else {
		minStatus = 1
		comment = LevelMap[maxLevel] + "风险, 自动审批通过。"
	}

	// 所有检查完成后才创建数据库记录
	audit := map[string]interface{}{
		"CreatedAt": now,
		"UpdatedAt": now,
		"JobID":     jobID,
		"TaskID":    taskID,

		"UserID":        userID,
		"AuditorID":     auditorID,
		"LastAuditorID": 0,
		"Level":         maxLevel,
		"Status":        minStatus,
		"Comment":       comment,
	}

	err = dbuildfarmaudit.BatchCreate(audit, auditDetails)
	if err != nil {
		logger.Errorf("create audit record error ", err.Error())
		return false, err
	}

	if auditorID > 0 && minStatus == 0 {
		go SendAuditMail(jobID, taskID)
		return false, nil
	} else if minStatus == 1 {
		return true, nil
	}

	return false, nil
}

var LevelMap = map[uint]string{
	9: "未知",
	3: "高",
	2: "中",
	1: "低",
	0: "无",
}

func SendAuditMail(jobID, taskID string) {
	audit := dbuildfarmaudit.BuildfarmAudit{}
	err := audit.FindJobIDAndTaskID(jobID, taskID)
	if err != nil {
		logging.ErrorLogger.Errorf("send mail err ", err)
		return
	}
	from := "编译农场"

	mailTo := []string{audit.Auditor.Username + "@ruijie.com.cn"}
	cc := []string{"<EMAIL>"}
	subject := "【编译农场】【个人仓编译评审】【新的评审单】"
	body := fmt.Sprintf(`
<p>个人仓编译需要评审</p>
<p>风险：%s</p>
<p>提交人：%s</p>
<a href="http://10.51.135.15:9090/buildfarm/audit/audit/%s">点击这里开始评审</a>
		`,
		LevelMap[audit.Level],
		audit.User.Name,
		audit.TaskID,
	)
	libs.SendMailRedis(from, mailTo, subject, body, cc)
}

func GetExtendDiffLevelExtMap() map[uint][]string {
	ExtendDiffLevelExtMap := map[uint][]string{}
	extendDiffLevelRule, err := dbuildfarmdifflevelrule.All()
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
	}

	for _, rule := range extendDiffLevelRule {
		if texts, ok := ExtendDiffLevelExtMap[rule.Level]; ok {
			if !libs.InArrayS(texts, rule.Text) {
				DiffLevelExtMap[rule.Level] = append(DiffLevelExtMap[rule.Level], rule.Text)
			}
		} else {
			ExtendDiffLevelExtMap[rule.Level] = []string{rule.Text}
		}
	}

	for level, exts := range DiffLevelExtMap {
		if _, ok := ExtendDiffLevelExtMap[level]; !ok {
			ExtendDiffLevelExtMap[level] = []string{}
		}
		ExtendDiffLevelExtMap[level] = append(ExtendDiffLevelExtMap[level], exts...)
	}
	return ExtendDiffLevelExtMap
}

func GetLevelByFilePath(ExtendDiffLevelExtMap map[uint][]string, fp string) uint {
	fp = strings.ToLower(fp)
	basename := filepath.Base(fp)
	ext := filepath.Ext(fp)

	for level, exts := range ExtendDiffLevelExtMap {
		for _, extPattern := range exts {
			// 处理文件名与扩展名的匹配
			if basename == extPattern || ext == extPattern {
				return level
			}

			// 处理以扩展名结尾的情况
			if strings.HasPrefix(extPattern, "*") && strings.HasSuffix(basename, strings.TrimPrefix(extPattern, "*")) {
				return level
			}

			// 处理文件路径
			if strings.Contains(extPattern, "/") && strings.HasSuffix(fp, strings.ToLower(extPattern)) {
				return level
			}
		}
	}

	return 9
}

func CheckDiffLevel(diffFile string) (uint, error) {
	maxLevel := uint(0)
	diffContent, err := os.ReadFile(diffFile)
	if err != nil {
		return 9, err
	}

	if len(diffContent) == 0 {
		return maxLevel, err
	}

	// 拆分diff

	scanner := bufio.NewScanner(strings.NewReader(string(diffContent)))
	var currentFile string
	diffs := map[string][]string{}
	var _diffs []string

	for scanner.Scan() {
		line := scanner.Text()
		// 检测文件名行
		// 由---行开始，防止截取时，多出---行，导致格式异常
		if strings.HasPrefix(line, "diff --git") {
			if currentFile != "" {
				diffs[currentFile] = []string{}
				diffs[currentFile] = append(diffs[currentFile], _diffs...)
				_diffs = []string{}
				currentFile = ""
			}
		} else if strings.HasPrefix(line, "+++ ") {
			parts := strings.Split(line, " ")
			if len(parts) >= 2 {
				currentFile = strings.TrimPrefix(parts[1], "b/")
			}
		}
		_diffs = append(_diffs, line)
	}
	// 补齐最后一个变更
	if currentFile != "" {
		diffs[currentFile] = []string{}
		diffs[currentFile] = append(diffs[currentFile], _diffs...)
		_diffs = []string{}
		currentFile = ""
	}

	extendDiffLevelExtMap := GetExtendDiffLevelExtMap()
	for newFile, diff := range diffs {
		if maxLevel < 9 {
			level := GetLevelByFilePath(extendDiffLevelExtMap, newFile)
			if level == 3 {
				rule, line, _level, err := CheckBlackWorker(diff)
				logging.DebugLogger.Debugf(newFile, rule, line, level)
				if err != nil {
					logging.ErrorLogger.Errorf(err.Error())
				}
				level = uint(_level)
			}

			if maxLevel < uint(level) {
				maxLevel = uint(level)
			}
		}
	}

	return maxLevel, nil
}

func CheckBlackWorker(diff []string) (string, string, int, error) {
	blackLists, err := dbuildfarmauditblacklist.All()
	if err != nil {
		return "", "", 3, err
	}

	if len(blackLists) == 0 {
		return "", "", 3, nil
	}

	patterns := []*regexp.Regexp{}
	strs := []string{}

	for _, black := range blackLists {
		switch black.Type {
		case 1:
			strs = append(strs, black.Text)
		case 2:
			pattern := regexp.MustCompile(black.Text)
			patterns = append(patterns, pattern)
		}
	}

	level := 2
	line := ""
	rule := ""
	limitQueue := make(chan string, 10)

	var wg sync.WaitGroup
	var levelLock sync.Mutex
	for _, _line := range diff[5:] {
		if strings.HasPrefix(_line, "+") && level != 3 {
			limitQueue <- _line
			wg.Add(1)
			go func(_line string) {
				defer wg.Done()
				if level != 3 {
					_rule, check := CheckBlack(patterns, strs, _line)
					levelLock.Lock()
					if check > level {
						level = check
						line = _line
						rule = _rule
					}
					levelLock.Unlock()
				}
				<-limitQueue
			}(_line)
		}
	}
	wg.Wait()
	return rule, line, level, nil
}

func CheckBlack(patterns []*regexp.Regexp, strs []string, line string) (string, int) {
	for _, pattern := range patterns {
		if pattern.MatchString(line) {
			return pattern.String(), 3
		}
	}
	for _, str := range strs {
		if strings.Contains(line, str) {
			return str, 3
		}
	}
	return "no rule match", 2
}

func GetGitlabProjectID(repo string) string {
	_repo := strings.TrimPrefix(repo, "ssh://")
	_repo = strings.TrimSuffix(_repo, ".git")
	_array := strings.Split(_repo, "/")
	return strings.Join(_array[1:], "/")
}

func BuildTypePostTaskV2(client *ssh.Client, f *os.File, jobId, buildType string) error {
	f.WriteString(fmt.Sprintf("%s [INFO] 输出编译类型信息。\r\n", time.Now().Format("2006-01-02 15:04:05")))
	archivePath := filepath.Join(libs.Config.Buildfarm.Archivepath, jobId)
	command := fmt.Sprintf(`cd %s && echo %s > %s/$(ls)/build_type && echo %s > %s/$(ls)/releaseID-bin/build_type `, archivePath, buildType, archivePath, buildType, archivePath)
	cmd := exec.Command("bash", "-c", command)
	var out bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &stderr
	err := cmd.Run()
	if err != nil {
		logging.ErrorLogger.Errorf("write build type info err ", cmd, err, stderr.String(), out.String())
		f.WriteString(fmt.Sprintf("%s [ERROR] 输出编译类型信息失败: %s, %s, %s\r\n", time.Now().Format("2006-01-02 15:04:05"), err.Error(), stderr.String(), out.String()))
		return err
	}

	f.WriteString(fmt.Sprintf("%s [INFO] 输出编译类型信息成功。\r\n", time.Now().Format("2006-01-02 15:04:05")))
	if buildType == "debug" || buildType == "gcov" {
		keyword := "_install.bin"
		command = fmt.Sprintf(`cd %s && cd $(ls)/releaseID-bin && ls|grep %s|sed 's/%s//'|xargs -i mv {}%s {}_%s%s`, archivePath, keyword, keyword, keyword, buildType, keyword)
		cmd = exec.Command("bash", "-c", command)
		err = cmd.Run()
		if err != nil {
			logging.ErrorLogger.Errorf("rename output filename err ", cmd, err, stderr.String(), out.String())
			f.WriteString(fmt.Sprintf("%s [ERROR] 输出编译类型信息失败: %s, %s, %s\r\n", time.Now().Format("2006-01-02 15:04:05"), err.Error(), stderr.String(), out.String()))
			return err
		}
		f.WriteString(fmt.Sprintf("%s [INFO] 输出文件名增加编译类型信息。\r\n", time.Now().Format("2006-01-02 15:04:05")))
	}

	return nil
	// return errors.New("未知错误")
}

func ServerStatus() ([]*dserver.StatusResponse, error) {
	var serverStatus []*dserver.StatusResponse
	servers, err := dserver.FindAllServer()
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		return serverStatus, err
	}
	err = copier.Copy(&serverStatus, servers)
	if err != nil {
		logging.ErrorLogger.Errorf("copier copy error", err)
	}
	runningMakeJobs, err := dmakejob.FindTMakeJobsByStatusAndTaskType([]uint{0}, []uint{1, 2, 3})
	if err != nil {
		logging.ErrorLogger.Errorf("get all running job get err ", err)
		return serverStatus, err
	}
	// dgitjob.AddGitjob(runningMakeJobs)
	queueMakeJobs, err := dmakejob.FindQueueJobs()
	if err != nil {
		logging.ErrorLogger.Errorf("get all running job get err ", err)
		return serverStatus, err
	}
	cronMakeJobs, err := dcronmakejob.FindRuningJobs()
	if err != nil {
		logging.ErrorLogger.Errorf("get all running job get err ", err)
		return serverStatus, err
	}
	// dgitjob.AddGitjob(queueMakeJobs)
	for _, server := range serverStatus {
		server.Running = []*dmakejob.MakeJob{}
		server.Queue = []*dmakejob.MakeJob{}
		for _, runningMakeJob := range runningMakeJobs {
			if runningMakeJob.ServerID == server.Id {
				server.Running = append(server.Running, runningMakeJob)
			}
		}
		for _, queueMakeJob := range queueMakeJobs {
			server.Queue = append(server.Queue, queueMakeJob)
		}
		for _, cronMakeJob := range cronMakeJobs {
			if cronMakeJob.ServerId == server.Id {
				server.CronRunning = append(server.CronRunning, cronMakeJob)
			}
		}
	}

	return serverStatus, nil
}

func ArchiveMkinstpkg(serverId uint, Id, workDir, cpu string) error {
	server := &dserver.Response{}
	err := server.Find(serverId)
	if err != nil {
		logging.ErrorLogger.Errorf("find server get err ", err)
	}

	// 归档 host-mkinstpkg
	// install -D -m 0755 mkinstpkg /mnt/sata0/a3b39518ea7eb08aea9078a5242a4f56/prj_kvm-x86_64/output/host/x86_64-x86_64/bin/mkinstpkg
	// install -D -m 0755 mkbininfo /mnt/sata0/a3b39518ea7eb08aea9078a5242a4f56/prj_kvm-x86_64/output/host/x86_64-x86_64/bin/mkbininfo
	// install -D -m 0755 mkospkg /mnt/sata0/a3b39518ea7eb08aea9078a5242a4f56/prj_kvm-x86_64/output/host/x86_64-x86_64/bin/mkospkg
	hostMkinstpkgDir := filepath.Join(libs.Config.Buildfarm.Archivepath, Id, cpu, "host-mkinstpkg") + "/"
	err = os.MkdirAll(hostMkinstpkgDir, 0o755)
	os.Chmod(hostMkinstpkgDir, 0o755)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("mkdir before sync error: %s", err.Error()))
		return err
	}

	for _, fn := range []string{"mkinstpkg", "mkbininfo", "mkospkg"} {
		var out, stderr bytes.Buffer
		cmd := exec.Command("rsync", "-avz", "-e", fmt.Sprintf("ssh -o StrictHostKeyChecking=no -p %s", strconv.Itoa(int(server.Port))), fmt.Sprintf("%s@%s:%s", server.Username, server.Host, fmt.Sprintf("%s/%s/%s", workDir, "output/host/*/bin", fn)), hostMkinstpkgDir)
		cmd.Stdout = &out
		cmd.Stderr = &stderr
		err = cmd.Run()
		if err != nil {
			logging.ErrorLogger.Errorf("rsync mkinstpkg get err ", cmd, err, stderr.String(), out.String())
		}
	}

	return nil
}

func ArchiveOutputV2(serverId uint, Id string, outPutPath string, repo, branch, target string) error {
	server := &dserver.Response{}
	err := server.Find(serverId)
	if err != nil {
		logging.ErrorLogger.Errorf("create gitjob find server get err ", err)
		return err
	}
	archivePath := filepath.Join(libs.Config.Buildfarm.Archivepath, Id) + "/"
	err = os.MkdirAll(archivePath, 0o755)
	os.Chmod(archivePath, 0o755)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("mkdir before scp error: %s", err.Error()))
		return err
	}
	cmd := exec.Command("rsync", "-avz", "-e", fmt.Sprintf("ssh -o StrictHostKeyChecking=no -p %s", strconv.Itoa(int(server.Port))), fmt.Sprintf("%s@%s:%s", server.Username, server.Host, outPutPath), archivePath)
	var out bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &stderr
	err = cmd.Run()
	if err != nil {
		logging.ErrorLogger.Errorf("rsync get err ", cmd, err, stderr.String(), out.String())
		return err
	}
	baseosArchiveConfig := libs.Config.Buildfarm.BaseosArchive
	if baseosArchiveConfig.Enable {
		if strings.HasSuffix(repo, "basesys/base-os.git") && (target == "all" || target == "") {
			out = *bytes.NewBuffer([]byte{})
			stderr = *bytes.NewBuffer([]byte{})
			dir := filepath.Join(baseosArchiveConfig.Dir, branch) + "/"
			clearKeyCommand := fmt.Sprintf("ssh-keygen -f ~/.ssh/known_hosts -R %s", baseosArchiveConfig.Host)
			cmd = exec.Command("bash", "-c", clearKeyCommand)
			err = cmd.Run()
			if err != nil {
				logging.ErrorLogger.Errorf("rsync clean ssh key err ", cmd, err, stderr.String(), out.String())
			}

			if baseosArchiveConfig.Password != "" {
				cmd = exec.Command("sshpass", "-p", baseosArchiveConfig.Password, "rsync", "-avz", "-e", fmt.Sprintf("ssh -o StrictHostKeyChecking=no -p %s", baseosArchiveConfig.Port), "--rsync-path", fmt.Sprintf("mkdir -p %s && rsync", dir), archivePath, fmt.Sprintf("%s@%s:%s", baseosArchiveConfig.User, baseosArchiveConfig.Host, dir))
			} else {
				cmd = exec.Command("rsync", "-avz", "-e", fmt.Sprintf("ssh -o StrictHostKeyChecking=no -p %s", baseosArchiveConfig.Port), "--rsync-path", fmt.Sprintf("mkdir -p %s && rsync", dir), archivePath, fmt.Sprintf("%s@%s:%s", baseosArchiveConfig.User, baseosArchiveConfig.Host, dir))
			}
			err = cmd.Run()
			if err != nil {
				logging.ErrorLogger.Errorf("rsync get err ", cmd, err, stderr.String(), out.String())
				return err
			}
		}
	}

	return nil
}

type BuildFarmWebConfig struct {
	WebClient *req.Client
}

var BuildFarmWebClient = BuildFarmWebConfig{}

func init() {
	BuildFarmWebClient.WebClient = req.C().
		SetCommonRetryCount(5).
		// Set the retry sleep interval with a commonly used algorithm: capped exponential backoff with jitter (https://aws.amazon.com/blogs/architecture/exponential-backoff-and-jitter/).
		SetCommonRetryBackoffInterval(1*time.Second, 5*time.Second).
		SetCommonQueryParam("private_token", libs.Config.Buildfarm.Token).
		SetBaseURL(fmt.Sprintf("%s/api/%s/", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version))
}

type CommonResponse struct {
	Message string `json:"string"`
}

type FileResponse struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

// func (sc *BuildFarmWebConfig) GetFiles(projectID, path string) ([]string, error) {

// 	var result AuthResponse
// 	var errMsg AuthResponse
// 	authorization := getBasicAuthorization(username, password)
// 	resp, err := sc.WebClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetHeader("Authorization", authorization).SetBody(map[string]string{
// 		"scope":     "server",
// 		"grantType": "clientCredentials",
// 	}).Post(url)
// 	if resp.IsSuccessState() {
// 		return result, nil
// 	}
// 	return errMsg, err
// }

func (r *BuildFarmWebConfig) GetFiles(projectID, path, branch string) ([]*FileResponse, error) {
	result := []*FileResponse{}
	var errMsg CommonResponse
	resp, err := r.WebClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(
		map[string]string{
			"path":     path,
			"per_page": "100",
			"ref":      branch,
		},
	).Get(fmt.Sprintf("projects/%s/repository/tree", projectID))
	if err != nil {
		logging.ErrorLogger.Errorf("get pipeline error", err.Error())
		return result, nil
	}
	if resp.IsSuccessState() {
		return result, nil
	}
	return result, errors.New("未知错误")
}
