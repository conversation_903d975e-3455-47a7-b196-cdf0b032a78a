debug: true
ddebug: true
loglevel: error # 日志级别 info debug disable error fatal warn 等等
host: 0.0.0.0 # host 地址
port: 9001
nginx:
  host: localhost
  port: 9527
  path: /api
readtimeout: 600
writetimeout: 600
maxsize: 10240 # 文件上传限制
pprof: true #  http://localhost:8086/debug/pprof/heap ...
casbin:
  prefix:
  # path: /data/goprojects/irisAdminApi/rbac_model.conf # go run main.go 运行必须指定路径
  path: /projects/iris-admin-api/rbac_model.conf
cache:
  # driver: redis # 缓存驱动 local redis
  driver: redis
limit:
  disable: false
  limit: 20 # 每秒允许请求 1 次
  burst: 100 # 最高允许并发
admin: # 管理员账号信息，用于数据填充
  username: admin
  rolename: 超级管理员
  name: 超级管理员
  password: password
db:
  adapter: mysql # mysql postgres sqlite3
  conn: "root:abc.123@tcp(127.0.0.1:3306)/iris?parseTime=True&loc=Local"
redis:
 host: 127.0.0.1
 port: 6379
 password: fileOut@2021!
qiniu:
  enable: false
  host:
  accesskey:
  secretkey:
  bucket:
filestorage:
  # 临时以及最后存在路径win/linux均以/作为分隔符，以/为结尾
  temp: /tmp/temp/ # win/linux均以/作为分隔符，以/为结尾
  upload: /tmp/upload/
  # objdump:
  # #   enable: true
  #   path: /usr/bin/objdump
  # filecommand:
  #   path: /usr/bin/file
  # rsa:
  # publickey: /home/<USER>/.ssh/id_rsa.pub
  # privatekey: /home/<USER>/.ssh/id_rsa

gitlab:
  url: http://*************:8080
  version: v4

buildfarm:
  cronmailrecivers: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
  logpath: /tmp/  #编译日志路径
  compilepath: /tmp/  #编译项目路径
  archivepath: /tmp/  #编译输出归档路径
  user: buildfarm
  # token: TiYcqp7Lj7A6qnJ4uuwa
  token: mHxh-ku5-gXz9sJxDPfE
  enable: false
  jenkinsurl: http://**************:8080
  jenkinsuser: admin
  jenkinspass: admin

release:
  releasepath: /tmp/
  host: http://**************:8080/release

featurerelease:
  upload: /tmp/
  url: https://*************
  enable: true

checkccode:
  script: /home/<USER>/doc2vec/check.py
  repository: /home/<USER>/repositorys/

mail:
  enable: true
  queue: mail

patchfilestorage:
  temp: /tmp/temp/ # 临时目录
  upload: /tmp/upload/patch/ #上传保存目录
  compilepath: /tmp/compile/  #编译项目路径
  archivepath: /tmp/compileoutput/  #编译输出路径
  gitrepo: ssh://git@**************:8022/daimingwei/patch_build.git #工具仓库地址
  branch: master #仓库分支

sync:
  enable: false

codesync:
  token: SAEwzuvybVP7dffsnGHx
  enable: false

coredump:
  upload: /tmp/coredump
  output: /tmp/coredump/output
  host: 127.0.0.1
  port: 9022
  username: root
  plugin: /projects/iris-admin-api/plugins
  remoteworkdir: /mnt/sata0

urlpack:
  db:
    adapter: mysql # mysql postgres sqlite3
    conn: "linjiakai:12345@tcp(************:3306)/iris?parseTime=True&loc=Local"
  outputs: /tmp/urlpack/outputs
  logs: /tmp/urlpack/logs
  enable: true

opensource:
  enable: false
  #cvesynctaskworkers: 2
  #sendemailtaskworkers: 3
  #tempchecktaskworkers: 4
  #cvesyncintervalinhours: 169
  #sendemailintervalinhours: 167
  #cnvdsyncintervalinhours: 166

mergerequest:
  enable: true

performance:
  enable: true
