package libs

import (
	"fmt"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/cache"
	"strconv"
	"strings"

	"gopkg.in/gomail.v2"
)

func SendMail(mailTo []string, subject string, body string, cc []string, files ...string) error {
	if len(mailTo) == 0 {
		return fmt.Errorf("mail to is empty")
	}

	if Config.Debug {
		mailTo = []string{"<EMAIL>"}
		cc = []string{"<EMAIL>"}
		fmt.Println("fake mail send")
	}

	//定义邮箱服务器连接信息，如果是阿里邮箱 pass填密码，qq邮箱填授权码
	// mailConn := map[string]string{
	// 	"user": "<EMAIL>",
	// 	"pass": "VK8d73s9",
	// 	"host": "casfz.ruijie.com.cn",
	// 	"port": "25",
	// }
	mailConn := map[string]string{
		"user": "<EMAIL>",
		"pass": "BUe4WMFPtgivmqaM",
		"host": "smtp.feishu.cn",
		"port": "465",
	}

	port, _ := strconv.Atoi(mailConn["port"]) //转换端口类型为int

	m := gomail.NewMessage()
	m.SetAddressHeader("From", mailConn["user"], "安全信息通告")
	// m.SetHeader("From", m.FormatAddress(mailConn["user"], "规则库发布系统")) //这种方式可以添加别名，即“XD Game”， 也可以直接用<code>m.SetHeader("From",mailConn["user"])</code> 读者可以自行实验下效果
	if len(mailTo) > 0 {
		m.SetHeader("To", mailTo...)
		if len(cc) > 0 {
			if cc[0] != "" {
				m.SetHeader("Cc", cc...)
			} else {
				m.SetHeader("Cc", cc[1:]...)
			}
		}
		m.SetHeader("Subject", subject) //设置邮件主题
		m.SetBody("text/html", body)    //设置邮件正文
		if len(files) > 0 {
			for _, f := range files {
				m.Embed(f)
			}
		}
		d := gomail.NewDialer(mailConn["host"], port, mailConn["user"], mailConn["pass"])
		// d.TLSConfig = &tls.Config{InsecureSkipVerify: true}
		err := d.DialAndSend(m)
		return err
	} else {
		for idx, item := range mailTo {
			fmt.Println(idx, item)
		}
	}
	return nil
}

func SendMailRedis(from string, mailTo []string, subject string, body string, cc []string) error {
	if len(mailTo) == 0 {
		return fmt.Errorf("mail to is empty")
	}

	if Config.Debug {
		SendMail(mailTo, subject, body, cc)
		return nil
	}

	rc := cache.GetRedisClusterClient()
	msg := strings.Join([]string{from, strings.Join(mailTo, ","), subject, body, strings.Join(cc, ",")}, "|s|p|l|i|t|")

	_, err := rc.LPush(Config.Mail.Queue, msg)
	if err != nil {
		logging.ErrorLogger.Error(err)
		return err
	}

	return nil
}
