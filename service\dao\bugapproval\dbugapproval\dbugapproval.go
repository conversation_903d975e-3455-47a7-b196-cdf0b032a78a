package dbugapproval

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models"
	"irisAdminApi/application/models/bugapproval"
	"irisAdminApi/service/dao/user/duser"
	"strings"
	"time"

	"github.com/pkg/errors"
	"gorm.io/gorm"
)

const ModelName = "BUG延期申请"

type ReleaseProject struct {
	ID      uint   `json:"id"`
	Name    string `json:"name"`
	CapoID  uint   `json:"capo_id"`
	PmoID   uint   `json:"pmo_id"`
	PmID    uint   `json:"pm_id"`
	PtmID   uint   `json:"ptm_id"`
	CmaID   uint   `json:"cma_id"`
	PqaID   uint   `json:"pqa_id"`
	PgttlID uint   `json:"pgttl_id"`
}

type Bug struct {
	BugID         int         `gorm:"primarykey; autoIncrement:false" json:"bug_id" `
	BugOS         string      `json:"bug_os"`
	BugSummary    string      `gorm:"not null; type:varchar(300)" json:"bug_summary" update:"1"`
	BugOwner      string      `gorm:"not null; type:varchar(60)" json:"bug_owner" update:"1"`
	BugOwnerGroup string      `gorm:"not null; type:varchar(60)" json:"bug_owner_group" update:"1"`
	BugApproval   BugApproval `gorm:"->;foreignKey:BugID;references:BugID" json:"bug_approval"`
	Auditors      []*User     `gorm:"-" json:"auditors"`
	CCs           []*User     `gorm:"-" json:"ccs"`
}

type BugDetail struct {
	BugID         int    `gorm:"primarykey; autoIncrement:false" json:"bug_id" `
	BugOS         string `json:"bug_os"`
	BugSummary    string `gorm:"not null; type:varchar(300)" json:"bug_summary" update:"1"`
	BugOwner      string `gorm:"not null; type:varchar(60)" json:"bug_owner" update:"1"`
	BugOwnerGroup string `gorm:"not null; type:varchar(60)" json:"bug_owner_group" update:"1"`
}

type BugApprovalAuditor struct {
	models.ModelBase
	BugApprovalID uint  `gorm:"not null" json:"bug_approval_id"`
	UserID        uint  `gorm:"not null" json:"user_id"`
	Status        uint  `gorm:"not null; default:0" json:"status"` // 0: 审批中 1: 通过  2: 拒绝
	User          *User `gorm:"->" json:"user"`
}

type BugApproval struct {
	models.ModelBase
	BugID        int        `gorm:"not null;index" json:"bug_id"`
	UserID       uint       `gorm:"not null" json:"user_id"`
	ApprovalType string     `gorm:"not null;type:varchar(20)" json:"approval_type"`
	Status       uint       `gorm:"not null" json:"status"` // 0: 审批中 1: 通过  2: 拒绝
	Round        int        `gorm:"not null" json:"round"`
	Comment      string     `json:"comment"`
	PlanFinishAt *time.Time `json:"plan_finish_at"`
	User         *User      `gorm:"->" json:"user"`
	// gorm:"many2many:merge_request_assignees; foreignKey:ID; references:ID; joinForeignKey: MergeRequestID; joinReferences: UserID" json:"assignees"
	CCs       []*User               `gorm:"many2many:bug_approval_ccs; foreignKey:ID; references:ID; joinForeignKey: BugApprovalID; joinReferences: UserID" json:"ccs"`
	Audits    []*BugApprovalAuditor `gorm:"->;foreignKey:BugApprovalID;references:ID" json:"audits"`
	Auditors  []*User               `gorm:"many2many:bug_approval_auditors; foreignKey:ID; references:ID; joinForeignKey: BugApprovalID; joinReferences: UserID" json:"auditors"`
	IsAuditor bool                  `gorm:"-" json:"is_auditor"`
}

type Department struct {
	ID       uint   `json:"id"`
	ParentID uint   `json:"parent_id"`
	Name     string `json:"name"`
	Link     string `json:"link"`
}

type User struct {
	ID         uint         `json:"id"`
	Username   string       `json:"username"`
	Name       string       `json:"name"`
	Department []Department `gorm:"->;many2many:user_departments;foreignKey:ID;joinForeignKey:UserID;References:ID;JoinReferences:DepartmentID" json:"department"`
}

type Role struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type ListResponse struct {
	BugApproval
}

type Request struct {
	bugapproval.BugApproval
	CCIDs      []uint `json:"cc_ids"`
	AuditorIDs []uint `json:"auditor_ids"`
}

func (a *BugApproval) ModelName() string {
	return ModelName
}

func Model() *bugapproval.BugApproval {
	return &bugapproval.BugApproval{}
}

func (a *BugApproval) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("CCs").Preload("Auditors").Preload("Auditors.User")
	where := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		where = where.Where("group_name_ch like ?", fmt.Sprintf("%%%s%%", name)).
			Or("group_name_en like ?", fmt.Sprintf("%%%s%%", name)).
			Or("category_name_ch like ?", fmt.Sprintf("%%%s%%", name)).
			Or("category_name_en like ?", fmt.Sprintf("%%%s%%", name)).
			Or("description_ch like ?", fmt.Sprintf("%%%s%%", name)).
			Or("description_en like ?", fmt.Sprintf("%%%s%%", name))
		if strings.Contains("海外大库", name) {
			where = where.Or("oversea = 1")
		}
		if strings.Contains("大库", name) {
			where = where.Or("large = 1")
		}
		if strings.Contains("小库", name) {
			where = where.Or("small = 1")
		}
		if strings.Contains("中库", name) {
			where = where.Or("middle = 1")
		}
		if strings.Contains("默认阻断", name) {
			where = where.Or("pre_def_block = 1")
		}
	}
	db = db.Where(where)
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *BugApproval) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *BugApproval) Create(object map[string]interface{}) error {
	bugApproval := BugApproval{}

	bugApproval.CreatedAt = time.Now()
	bugApproval.BugID = object["BugID"].(int)
	bugApproval.UserID = object["UserID"].(uint)
	bugApproval.ApprovalType = object["ApprovalType"].(string)
	bugApproval.Status = object["Status"].(uint)
	bugApproval.Round = object["Round"].(int)
	bugApproval.Comment = object["Comment"].(string)
	if _, ok := object["PlanFinishAt"]; ok {
		bugApproval.PlanFinishAt = object["PlanFinishAt"].(*time.Time)
	} else {

	}

	ccs := []*User{}
	auditors := []*User{}
	user := User{}
	bug := Bug{}

	err := easygorm.GetEasyGormDb().Model(&Bug{}).Where("bug_id = ?", object["BugID"]).Find(&bug).Error
	if err != nil {
		return err
	}

	if _, ok := object["CCIDs"]; ok {
		if ok && len(object["CCIDs"].([]uint)) > 0 {
			err := easygorm.GetEasyGormDb().Model(&User{}).Where("id in ?", object["CCIDs"]).Find(&ccs).Error
			if err != nil {
				return err
			}
		}
	}
	if _, ok := object["AuditorIDs"]; ok {
		if ok && len(object["AuditorIDs"].([]uint)) > 0 {
			err := easygorm.GetEasyGormDb().Model(&User{}).Where("id in ?", object["AuditorIDs"]).Find(&auditors).Error
			if err != nil {
				return err
			}
		}
	}

	if _, ok := object["UserID"]; ok {
		if ok && object["UserID"].(uint) > 0 {
			err := easygorm.GetEasyGormDb().Model(&User{}).Where("id = ?", object["UserID"]).Find(&user).Error
			if err != nil {
				return err
			}
		}
	}

	bugApproval.User = &user

	bugApproval.CCs = ccs

	bugApproval.Auditors = auditors

	db := easygorm.GetEasyGormDb().Model(Model())
	err = db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(Model()).Omit("CCs", "Auditors", "User").Create(&bugApproval).Error; err != nil {
			return err
		}
		if err := tx.Model(&bugApproval).Omit("Auditors").Association("CCs").Replace(ccs); err != nil {
			return err
		}
		if err := tx.Model(&bugApproval).Omit("CCs").Association("Auditors").Replace(auditors); err != nil {
			return err
		}
		return nil
	})
	go SendMail(0, &bugApproval, &bug)
	return err
}

func (this *BugApproval) CreateV2(object interface{}) error {
	return nil
}

func (a *BugApproval) BatchCreate(mrs []*BugApproval) error {

	return nil
}

func (a *BugApproval) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *BugApproval) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *BugApproval) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *BugApproval) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *BugApproval) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("CCs").Preload("Audits").Preload("User").Preload("Auditors").Preload("Audits.User").Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *BugApproval) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *BugApproval) Save() error {
	err := easygorm.GetEasyGormDb().Model(u).Save(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update merge request err ", err)
		return err
	}
	return nil
}

func (u *BugApproval) LastByBugIDAndStatus(bugID int) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and status = 1", bugID).Order("id desc").Limit(1).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func All(audit bool, userID, bugID uint, projectName, status, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("CCs").Preload("Audits").Preload("User").Preload("Auditors")

	where := easygorm.GetEasyGormDb().Model(Model())

	if audit {
		ids := easygorm.GetEasyGormDb().Model(&BugApprovalAuditor{}).Select("BugApprovalID").Where("user_id = ?", userID)
		if len(status) > 0 {
			ids = ids.Where("status in ?", strings.Split(status, ","))
		}
		where = where.Where("id in (?)", ids)
	} else {
		if userID > 0 {
			where = where.Where("user_id = ?", userID)
		}
		if len(status) > 0 {
			where = where.Where("status in ?", strings.Split(status, ","))
		}
	}

	if projectName != "" {
		bugIDs := []uint{}
		err := easygorm.GetEasyGormDb().Table("bugs").Where("bug_os = ?", projectName).Pluck("bug_id", &bugIDs).Error
		if err != nil {
			return nil, errors.Wrap(err, "")
		}
		where = where.Where("bug_id in ?", bugIDs)
		fmt.Println(bugIDs, projectName)
	}
	if bugID != 0 {
		where = where.Where("bug_id = ?", bugID)
	}
	db = db.Where(where)
	err := db.Count(&count).Error
	if err != nil {
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		return nil, err
	}

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (b *Bug) FindBugByBugID(bugID int, approvalType string) error {
	approval := BugApproval{}
	if err := easygorm.GetEasyGormDb().Model(&BugApproval{}).Where("bug_id = ? and approval_type = ? and status = 0", bugID, approvalType).Find(&approval).Error; err != nil {
		return err
	}
	if approval.ID != 0 {
		return errors.New("已有申请未完成评审")
	}

	if err := easygorm.GetEasyGormDb().Model(&Bug{}).Preload("BugApproval", "bug_id = ? and approval_type = ? and status = 1", bugID, approvalType, func(db *gorm.DB) *gorm.DB {
		return db.Order("bug_approvals.id desc").Limit(1)
	}).Where("bug_id = ?", bugID).Find(b).Error; err != nil {
		return err
	}

	releaseProject := ReleaseProject{}
	if err := easygorm.GetEasyGormDb().Model(&ReleaseProject{}).Where("name = ?", b.BugOS).Find(&releaseProject).Error; err != nil {
		return err
	}

	// 选择获取 用户
	// department := Department{}
	// if err := easygorm.GetEasyGormDb().Model(&Department{}).Where("name = ?", b.BugOwnerGroup).Find(&department).Error; err != nil {
	// 	return err
	// }

	users, err := duser.FindInNames([]string{b.BugOwner})
	if err != nil {
		return err
	}

	if len(users) == 0 {
		return fmt.Errorf("找不到该用户组织架构")
	}

	department := users[0].Department

	departmentID := department.ID
	departmentParentID := department.ParentID
	manageRole := Role{}
	if err := easygorm.GetEasyGormDb().Model(&Role{}).Where("name = ?", "组长").Find(&manageRole).Error; err != nil {
		return err
	}

	if manageRole.ID == "" {
		return errors.New("未找到组长，请联系管理员")
	}

	managerIDs1, err := easygorm.GetEasyGormEnforcer().GetUsersForRole(manageRole.ID)
	if err != nil {
		return err
	}
	managerIDs2, err := easygorm.GetEasyGormEnforcer().GetUsersForRole("role::" + manageRole.ID)
	if err != nil {
		return err
	}

	managerIDs1 = append(managerIDs1, managerIDs2...)

	userIDs := []uint{releaseProject.PmoID, releaseProject.PmID, releaseProject.CapoID, releaseProject.PtmID, releaseProject.CmaID, releaseProject.PqaID, releaseProject.PgttlID}
	if err := easygorm.GetEasyGormDb().Model(&User{}).Where("id in ?", userIDs).Find(&b.CCs).Error; err != nil {
		return err
	}

	switch approvalType {
	case "延期":
		if b.BugApproval.Round == 0 {
			userIDs := []uint{}
			if err := easygorm.GetEasyGormDb().Table("user_departments").Where("department_id = ?", departmentID).Pluck("user_id", &userIDs).Error; err != nil {
				return err
			}

			if err := easygorm.GetEasyGormDb().Model(&User{}).Where("id in ? and id in ?", userIDs, managerIDs1).Find(&b.Auditors).Error; err != nil {
				return err
			}

			if len(b.Auditors) == 0 {
				if err := easygorm.GetEasyGormDb().Table("user_departments").Where("department_id = ?", departmentParentID).Pluck("user_id", &userIDs).Error; err != nil {
					return err
				}

				if err := easygorm.GetEasyGormDb().Model(&User{}).Where("id in ? and id in ?", userIDs, managerIDs1).Find(&b.Auditors).Error; err != nil {
					return err
				}
			}

		} else if b.BugApproval.Round >= 1 {
			userIDs := []uint{}
			if err := easygorm.GetEasyGormDb().Table("user_departments").Where("department_id = ?", departmentParentID).Pluck("user_id", &userIDs).Error; err != nil {
				return err
			}

			if err := easygorm.GetEasyGormDb().Model(&User{}).Where("id in ? and id in ?", userIDs, managerIDs1).Find(&b.Auditors).Error; err != nil {
				return err
			}
			if len(b.Auditors) == 0 && strings.Contains(b.BugOwnerGroup, "研发一部") {
				ca := User{}
				if err := easygorm.GetEasyGormDb().Model(&User{}).Where("id = ?", releaseProject.CapoID).Find(&ca).Error; err != nil {
					return err
				}
				b.Auditors = append(b.Auditors, &ca)
			}
		}
	case "专项":
		userIDs := []uint{}
		if err := easygorm.GetEasyGormDb().Table("user_departments").Where("department_id in ?", []uint{departmentID, departmentParentID}).Pluck("user_id", &userIDs).Error; err != nil {
			return err
		}
		userIDs = append(userIDs, releaseProject.PgttlID, releaseProject.PmID, releaseProject.PtmID)
		if err := easygorm.GetEasyGormDb().Model(&User{}).Where("id in ? and id in ?", userIDs, managerIDs1).Find(&b.Auditors).Error; err != nil {
			return err
		}
	}

	return nil
}

func (a *BugApprovalAuditor) Update(id uint, bugApproval *BugApproval, pass uint, comment string) error {

	passCount := 0
	noPassCount := 0
	count := 0
	for _, audit := range bugApproval.Audits {
		count++
		if audit.Status == 1 {
			passCount++
		} else if audit.Status == 2 {
			noPassCount++
		}
	}
	if pass == 1 {
		passCount++
	} else if pass == 2 {
		noPassCount++
	}

	bug := Bug{}

	err := easygorm.GetEasyGormDb().Model(&Bug{}).Where("bug_id = ?", bugApproval.BugID).Find(&bug).Error
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Transaction(func(tx *gorm.DB) error {
		err := tx.Model(a).Where("id = ?", id).Updates(map[string]interface{}{"Status": pass, "Comment": comment}).Error
		if err != nil {
			return err
		}
		if passCount == count {
			err := tx.Model(&BugApproval{}).Where("id = ?", bugApproval.ID).Updates(map[string]interface{}{"Status": 1}).Error
			if err != nil {
				return err
			}
			go SendMail(1, bugApproval, &bug)
		} else if noPassCount > 0 {
			err := tx.Model(&BugApproval{}).Where("id = ?", bugApproval.ID).Updates(map[string]interface{}{"Status": 2}).Error
			if err != nil {
				return err
			}
			go SendMail(2, bugApproval, &bug)
		}
		return nil
	})
	return err
}

func SendMail(status uint, bugApproval *BugApproval, bug *Bug) {
	subject, body, mailTo, ccTo := FormatMsg(status, bugApproval, bug)
	libs.SendMailRedis("MR表单", mailTo, subject, body, ccTo)
	// libs.SendMail([]string{"<EMAIL>"}, subject, body)
}

func FormatMsg(status uint, bugApproval *BugApproval, bug *Bug) (string, string, []string, []string) {
	var subject, msg string
	var mailTo []string
	var ccTo []string
	statusMap := map[uint]string{
		0: "审批中",
		1: "通过",
		2: "取消",
	}

	subject = fmt.Sprintf(`[BUG%s申请][%v][%s]`, bugApproval.ApprovalType, bugApproval.BugID, statusMap[status])

	auditors := []string{}
	cc := []string{}
	for _, a := range bugApproval.Auditors {
		auditors = append(auditors, a.Name)
		mailTo = append(mailTo, a.Username+"@ruijie.com.cn")
	}
	for _, c := range bugApproval.CCs {
		cc = append(cc, c.Name)
		ccTo = append(ccTo, c.Username+"@ruijie.com.cn")
	}
	if bugApproval.ApprovalType == "专项" {
		msg = fmt.Sprintf(`
			<p>申请内容：Bug %v 申请 %s</p>
			<p>专项结束时间：%s</p>
			<p>Bug描述: %s</p>
			<p>申请人：%s</p>
			<p>审批人：%s</p>
			<p>抄送：%s</p>
			<p>状态：%s</p>
			<p>详情登录MR表单查看：<a href="http://10.51.135.15:9090/mergerequest">http://10.51.135.15:9090/mergerequest</a><p>`,
			bugApproval.BugID,
			bugApproval.ApprovalType,
			bugApproval.PlanFinishAt.Format("2006-01-02"),
			bug.BugSummary,
			bugApproval.User.Name,
			strings.Join(auditors, ","),
			strings.Join(cc, ","),
			statusMap[status],
		)
	} else {
		msg = fmt.Sprintf(`
			<p>申请内容：Bug %v 申请 %s</p>
			<p>Bug描述: %s</p>
			<p>申请人：%s</p>
			<p>审批人：%s</p>
			<p>抄送：%s</p>
			<p>状态：%s</p>
			<p>详情登录MR表单查看：<a href="http://10.51.135.15:9090/mergerequest">http://10.51.135.15:9090/mergerequest</a><p>`,
			bugApproval.BugID,
			bugApproval.ApprovalType,
			bug.BugSummary,
			bugApproval.User.Name,
			strings.Join(auditors, ","),
			strings.Join(cc, ","),
			statusMap[status],
		)
	}

	return subject, msg, mailTo, ccTo
}
