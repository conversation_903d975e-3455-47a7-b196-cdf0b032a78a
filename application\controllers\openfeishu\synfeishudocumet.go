package openfeishu

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/feishu/dfeishubitablelist"
	"irisAdminApi/service/dao/feishu/dfeishufilelist"
	"irisAdminApi/service/dao/feishu/dfeishuprojectdocument"
	"irisAdminApi/service/dao/feishu/dfeishuprojectlist"
	"irisAdminApi/service/dao/user/duser"
	"irisAdminApi/service/dao/user/duseropenid"
	"strconv"
	"strings"
	"time"
)

// 批量新增文档表数据
func ProjectDocumentSyncWorker() {
	//获取项目信息
	projectData, err := dfeishuprojectlist.GetProjectData()
	if err != nil {
		logging.ErrorLogger.Error("获取项目信息失败", err)
		return
	}
	for _, project := range projectData {
		projectName := project.ProjectName
		fmt.Println(projectName)
		//验证目录是否存在
		//获取项目对应的目录
		folderData, err := dfeishufilelist.GetFolderDataByProjectName(projectName)
		if err != nil {
			logging.ErrorLogger.Errorf("GetFolderDataByProjectName error:%s", err.Error())
			return
		}
		if folderData.ID == 0 {
			//更新目录数据
			GetFolderFileData()
			//等待5秒
			time.Sleep(5 * time.Second)
			folderData, err := dfeishufilelist.GetFolderDataByProjectName(projectName)
			if err != nil {
				logging.ErrorLogger.Errorf("GetFolderDataByProjectName error:%s", err.Error())
				return
			}
			if folderData.ID == 0 {
				// 创建飞书目录
				return
			}
		}
		//获取项目文档数据
		err = dfeishuprojectdocument.GetPMSProjectDocumentData(projectName)
		if err != nil {
			logging.ErrorLogger.Errorf("get pmsdoc error:%s", err.Error())
			return
		}
		//查看是否存在该项目的文档表
		tableName := "文档表"
		feishubitable := dfeishubitablelist.Response{}
		err = feishubitable.FindByNameAndProjectName(tableName, projectName)
		if err != nil {
			logging.ErrorLogger.Errorf("findByNameAndProjectName table error:%s", err.Error())
			return
		}
		tableID := feishubitable.TableID
		if feishubitable.ID == 0 {
			//创建飞书数据表文档表
			resp, err := CreateProjectDocumentTable(tableName, libs.Config.FeiShuDoc.BiAppToken)
			if err != nil {
				logging.ErrorLogger.Errorf("create table error:%s", err.Error())
				return
			}
			err = feishubitable.Create(map[string]interface{}{
				"name":              tableName,
				"table_id":          *resp.Data.TableId,
				"project_name":      projectName,
				"bitable_app_token": libs.Config.FeiShuDoc.BiAppToken,
			})
			tableID = *resp.Data.TableId
			if err != nil {
				logging.ErrorLogger.Errorf("create table error:%s", err.Error())
				return
			}
		}
		fmt.Println(tableID)
		//删除失效的文档数据
		BatchDeleteProjectDocument(projectName, tableID)
		//同步数据(新增+更新)
		BatchCreateProjectDocument(projectName, tableID)
		//根据文档类型获取模板自动创建文档
		BatchCreateProjectDocumentByType(projectName, tableID)
		//同步评审评委数据
		SyncFeiShuProjectDocumentJudge()
		//同步工作包数据
		SyncFeiShuProjectDocumentWorkPackage()
		//同步工作包文档类型
		SyncFeiShuProjectDocumentTypeWorkPackage()
		//同步评委和文档数据
		SyncFeiShuToDocumentAndJudge()
		//同步需求数据
		SyncFeiShuProjectDocumentRequests()
		//同步需求文档类型数据
		SyncFeiShuProjectDocumentTypeRequests()
		// 删除已删除的文档评论
		DeleteComments(projectName)
		//获取PMS上评测分析数据
		GetPMSAnalysisData(projectName)

		//获取云端文件夹文件清单
		// GetFolderFileData()
		// //获取云端评审详情表数据
		// GetDocumentCommentReviewData()
		// //批量更新数据
		// BatchUpdateProjectDocument(projectName, tableID)
		//匹配文档信息
		// MatchDocumentInfo(projectName)
		//获取云端文档评审状态信息
		// UpdateDocumentReviewStatus(projectName, "")
		// // //获取文档评论信息
		// GetDocumentComment(projectName)
		// // //生成评审详情表数据信息
		// CreateDocumentCommentReviewdetail(projectName)
		// //同步评审详情表数据信息(新增+更新)
		//获取云端评审结束确认信息
		// SyncFeiShuProjectDocumentEndReview()

	}

}

// 批量更新文档表数据
func ProjectDocumentUpdateWorker() {
	//获取项目信息
	projectData, err := dfeishuprojectlist.GetProjectData()
	if err != nil {
		logging.ErrorLogger.Error("获取项目信息失败", err)
		return
	}
	for _, project := range projectData {
		projectName := project.ProjectName
		//查看是否存在该项目的文档表
		tableName := "文档表"
		feishubitable := dfeishubitablelist.Response{}
		err = feishubitable.FindByNameAndProjectName(tableName, projectName)
		if err != nil {
			logging.ErrorLogger.Errorf("findByNameAndProjectName table error:%s", err.Error())
			return
		}
		tableID := feishubitable.TableID
		fmt.Println("批量更新文档表数据：" + tableID)
		//更新PSTL信息
		SyncDocumentPSTLInfo(projectName, tableID)
		//批量更新数据
		BatchUpdateProjectDocument(projectName, tableID)
	}
}

// 批量文档评论数据
func ProjectDocumentCommentWorker() {
	//获取项目信息
	projectData, err := dfeishuprojectlist.GetProjectData()
	if err != nil {
		logging.ErrorLogger.Error("获取项目信息失败", err)
		return
	}
	for _, project := range projectData {
		projectName := project.ProjectName
		//查看是否存在该项目的文档表
		tableName := "文档表"
		feishubitable := dfeishubitablelist.Response{}
		err = feishubitable.FindByNameAndProjectName(tableName, projectName)
		if err != nil {
			logging.ErrorLogger.Errorf("findByNameAndProjectName table error:%s", err.Error())
			return
		}
		tableID := feishubitable.TableID
		fmt.Println("批量文档表评论数据：" + tableID)
		//获取云端评审详情表数据
		GetDocumentCommentReviewData()
		//获取文档评论信息
		GetDocumentComment(projectName)
		//生成评审详情表数据信息
		CreateDocumentCommentReviewdetail(projectName)
	}
}

// 获取云端评审状态数据
func DocumentReviewStatusWorker() {
	//获取项目信息
	projectData, err := dfeishuprojectlist.GetProjectData()
	if err != nil {
		logging.ErrorLogger.Error("获取项目信息失败", err)
		return
	}
	for _, project := range projectData {
		projectName := project.ProjectName
		//查看是否存在该项目的文档表
		tableName := "文档表"
		feishubitable := dfeishubitablelist.Response{}
		err = feishubitable.FindByNameAndProjectName(tableName, projectName)
		if err != nil {
			logging.ErrorLogger.Errorf("findByNameAndProjectName table error:%s", err.Error())
			return
		}
		tableID := feishubitable.TableID
		fmt.Println("获取云端文档评审状态信息" + tableID)
		//获取云端评审结束确认信息
		SyncFeiShuProjectDocumentEndReview()
		//获取云端文档评审状态信息
		UpdateDocumentReviewStatus(projectName, "")
		//获取云端评审详情表数据
		GetDocumentCommentReviewData()
	}
}

func SyncFeiShuOpenID() {
	users, err := duser.All()
	if err != nil {
		logging.ErrorLogger.Errorf("get resources error", err.Error())
		return
	}
	for _, user := range users {
		if user.Enable {
			openid := duseropenid.Response{}
			err = openid.FindEx("user_id", strconv.FormatUint(uint64(user.ID), 10))
			if err != nil {
				logging.ErrorLogger.Errorf("get openid err ", err)
				continue
			}
			if openid.ID == 0 {
				// userName 存在v- 开头的去除v-
				userName := strings.Replace(user.Username, "v-", "", 1)
				// 获取用户openid
				openID, err := GetUserOpenIDByUserName(userName)
				if err != nil {
					logging.ErrorLogger.Info("get openid error", err.Error())
					continue
				}
				createOpenID := duseropenid.Response{}
				err = createOpenID.Create(map[string]interface{}{
					"Openid":    openID,
					"UserID":    user.ID,
					"CreatedAt": time.Now(),
					"UpdatedAt": time.Now(),
				})
				time.Sleep(time.Second * 1)
				if err != nil {
					logging.ErrorLogger.Errorf("create openid error", err.Error())
					continue
				} else {
					fmt.Println("create openid success:" + user.Username)
				}
			}
		}
	}
	fmt.Println("OpenID 数据更新成功")
}
