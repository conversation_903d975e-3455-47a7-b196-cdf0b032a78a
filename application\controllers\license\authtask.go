package license

import (
	"fmt"
	"irisAdminApi/application/controllers/license/taskmanagers"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/license/dauthtask"
	"irisAdminApi/service/dao/license/ddevicemodel"
	"irisAdminApi/service/dao/license/dmaterial"
	"strconv"

	"github.com/kataras/iris/v12"
)

type AuthTaskReq struct {
	DeviceSn     string `json:"device_sn"`
	DeviceType   string `json:"device_type"`
	DeviceModel  string `json:"device_model"`
	MaterialCode string `json:"material_code"`
	AuthType     uint   `json:"auth_type"`
}

func CreateAuthTask(ctx iris.Context) {
	req := &AuthTaskReq{}
	if err := ctx.ReadJSON(req); err != nil {
		logging.ErrorLogger.Errorf("create auth task read json err %s", err.Error())
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if req.DeviceSn == "" {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "device_sn 不能为空"))
		return
	}

	if req.DeviceModel == "" {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "device_model 不能为空"))
		return
	}

	if req.MaterialCode == "" {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, "material_code 不能为空"))
		return
	}

	authUserId, _ := dao.GetAuthId(ctx)
	//if res, err := dauthtask.FindAuthTaskByDeviceSn(req.DeviceSn, authUserId); err != nil {
	//	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	//	return
	//} else if res != nil {
	//	ctx.JSON(response.NewResponse(response.DuplicateErr.Code, nil, fmt.Sprintf("设备<%s>已经尝试进行授权", req.DeviceSn)))
	//	return
	//}

	deviceModelRes, err := ddevicemodel.GetByDeviceModel(req.DeviceModel)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if deviceModelRes == nil {
		ctx.JSON(response.NewResponse(response.DataEmptyErr.Code, nil,
			fmt.Sprintf("设备型号<%s>未入库", req.DeviceModel)))
		return
	}

	materailRes, err := dmaterial.GetByDeviceModelAndMaterialCode(req.DeviceModel, req.MaterialCode)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if materailRes == nil {
		ctx.JSON(response.NewResponse(response.DataEmptyErr.Code, nil,
			fmt.Sprintf("设备型号<%s>, 物料号<%s>未入库", req.DeviceModel, req.MaterialCode)))
		return
	}

	res := &dauthtask.Response{}
	res.DeviceSn = req.DeviceSn
	res.DeviceModel = req.DeviceModel
	res.DeviceType = deviceModelRes.DeviceType
	res.MaterialName = materailRes.MaterialName
	res.MaterialCode = materailRes.MaterialCode
	res.AuthType = req.AuthType
	res.DeviceMaterialCode = deviceModelRes.DeviceMaterialCode
	res.UserID = authUserId

	if err := dauthtask.AddAuthTask(res); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, res, response.NoErr.Msg))
	if taskmanagers.DeviceAuthTaskManager != nil {
		taskmanagers.DeviceAuthTaskManager.AddDeviceAuthTask(res)
	}
	return
}

func ListAuthTasks(ctx iris.Context) {
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	deviceSn := ctx.FormValue("device_sn")
	deviceModel := ctx.FormValue("device_model")
	materialCode := ctx.FormValue("material_code")
	authType, _ := strconv.Atoi(ctx.FormValue("auth_type"))
	id, _ := strconv.Atoi(ctx.FormValue("id"))
	userId, _ := strconv.Atoi(ctx.FormValue("user_id"))
	sort := ctx.FormValue("sort")
	orderBy := ctx.FormValue("orderBy")
	createdAt := ctx.FormValue("created_at")
	updatedAt := ctx.FormValue("updated_at")

	var isActive = new(bool)
	if active, err := ctx.URLParamBool("active"); err != nil {
		isActive = nil
	} else {
		*isActive = active
	}

	list, err := dauthtask.ListAuthTasks(
		page, pageSize, deviceSn, deviceModel, materialCode,
		uint(authType), uint(id), uint(userId),
		isActive, sort, orderBy, createdAt, updatedAt)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetAuthTask(ctx iris.Context) {
	authTaskId, _ := ctx.Params().GetUint("id")

	if res, err := dauthtask.GetAuthTask(authTaskId, 0); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	} else if res == nil {
		ctx.JSON(response.NewResponse(response.DataEmptyErr.Code, res, response.DataEmptyErr.Msg))
		return
	} else {
		ctx.JSON(response.NewResponse(response.NoErr.Code, res, response.NoErr.Msg))
		return
	}
}

func DeleteAuthTask(ctx iris.Context) {
	authTaskId, _ := ctx.Params().GetUint("id")
	authUserId, _ := dao.GetAuthId(ctx)
	isAdmin := isAdminUser(authUserId)
	userId := authUserId
	if isAdmin {
		userId = 0
	}

	if res, err := dauthtask.GetAuthTask(authTaskId, userId); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	} else if res == nil {
		ctx.JSON(response.NewResponse(response.DataEmptyErr.Code, nil, response.DataEmptyErr.Msg))
		return
	}

	if err := dauthtask.DeleteAuthTask(authTaskId, userId); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	} else {
		ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
		return
	}
}
