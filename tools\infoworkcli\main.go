package main

import (
	"bytes"
	"errors"
	"flag"
	"fmt"
	"io"
	"io/ioutil"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"strings"
	"time"

	"github.com/fsnotify/fsnotify"
)

type Wacth struct {
	wacth *fsnotify.Watcher
}

var TodoWork bool = false
var HashTodoWork bool = false
var LatestTimeStr string = "2006-01-02 15:04:05"
var HashLatestTimeStr string = "2006-01-02 15:04:05"
var Layout string = "2006-01-02 15:04:05"
var HashFileSuffix string = "ITW.zip"
var config = flag.String("config", "", "配置路径")

//监控SDK目录
func (w *Wacth) watchDir(SDKDir, HashDir string) {
	//遍历SDK目录下的所有子目录
	filepath.Walk(SDKDir, func(path string, info os.FileInfo, err error) error {
		//判断是否为目录
		if info.IsDir() {
			path, err := filepath.Abs(path)
			if err != nil {
				return err
			}
			err = w.wacth.Add(path)
			if err != nil {
				return err
			}
			fmt.Println("监控：", path)
		}
		return nil
	})

	//遍历HashDir目录下的所有子目录
	filepath.Walk(HashDir, func(path string, info os.FileInfo, err error) error {
		//判断是否为目录
		if info.IsDir() {
			path, err := filepath.Abs(path)
			if err != nil {
				return err
			}
			err = w.wacth.Add(path)
			if err != nil {
				return err
			}
			fmt.Println("监控(hash):", path)
		}
		return nil
	})
	done := make(chan bool)
	defer close(done)
	go func() {
		for {
			select {
			case ev := <-w.wacth.Events:
				{
					if ev.Op&fsnotify.Create == fsnotify.Create {
						if strings.Contains(ev.Name, SDKDir) {
							fmt.Println("创建文件:", ev.Name)
							fi, err := os.Stat(ev.Name)
							if err == nil && fi.IsDir() {
								w.wacth.Add(ev.Name)
								fmt.Println("添加监控:", ev.Name)
							}
							TodoWork = true
							LatestTimeStr = time.Now().Format(Layout)
							fmt.Println(LatestTimeStr)
						} else if strings.Contains(ev.Name, HashDir) {
							fmt.Println("创建文件(hash):", ev.Name)
							fi, err := os.Stat(ev.Name)
							if err == nil && fi.IsDir() {
								w.wacth.Add(ev.Name)
								fmt.Println("添加监控(hash):", ev.Name)
							}
							HashTodoWork = true
							HashLatestTimeStr = time.Now().Format(Layout)
							fmt.Println(HashLatestTimeStr)

						} else {
							fmt.Println("未匹配到路径(创建):", ev.Name)
						}

					}

					if ev.Op&fsnotify.Write == fsnotify.Write {
						if strings.Contains(ev.Name, SDKDir) {
							LatestTimeStr = time.Now().Format(Layout)
							TodoWork = true
							fmt.Println(LatestTimeStr+" 写入文件:", ev.Name)
						} else if strings.Contains(ev.Name, HashDir) {
							HashLatestTimeStr = time.Now().Format(Layout)
							fmt.Println(HashLatestTimeStr+" 写入文件(hash):", ev.Name)
							HashTodoWork = true

						} else {
							fmt.Println("未匹配到路径(写入):", ev.Name)
						}

					}

					if ev.Op&fsnotify.Remove == fsnotify.Remove {
						if strings.Contains(ev.Name, SDKDir) {
							fmt.Println("删除文件:", ev.Name)
							w.wacth.Remove(ev.Name)
							fmt.Println(time.Now().Format(Layout)+" 删除监控:", ev.Name)
						} else if strings.Contains(ev.Name, HashDir) {
							fmt.Println("删除文件(hash):", ev.Name)
							w.wacth.Remove(ev.Name)
							fmt.Println(time.Now().Format(Layout)+" 删除监控(hash):", ev.Name)
						} else {
							fmt.Println("未匹配到路径(删除):", ev.Name)
						}

					}

					if ev.Op&fsnotify.Remove == fsnotify.Rename {
						if strings.Contains(ev.Name, SDKDir) {
							fmt.Println("重命名文件:", ev.Name)
							TodoWork = true
							LatestTimeStr = time.Now().Format(Layout)
							fmt.Println(LatestTimeStr)
							w.wacth.Remove(ev.Name)
						} else if strings.Contains(ev.Name, HashDir) {
							fmt.Println("重命名文件(hash):", ev.Name)
							HashTodoWork = true
							HashLatestTimeStr = time.Now().Format(Layout)
							fmt.Println(HashLatestTimeStr)
							w.wacth.Remove(ev.Name)
						} else {
							fmt.Println("未匹配到路径(重命名):", ev.Name)
						}

					}
				}
			case err := <-w.wacth.Errors:
				{
					fmt.Println("error :", err)
					return
				}
			default:
				time.Sleep(time.Duration(1) * time.Second / 1000)
				if TodoWork {
					LatestTime, _ := time.ParseInLocation(Layout, LatestTimeStr, time.Local)
					sub1 := time.Since(LatestTime)
					subMinute := sub1.Minutes()
					if subMinute >= float64(libs.Config.InfoWorkFileWatch.WaitingTime) {
						fmt.Println("to do work")
						//复制监控文件夹 到 新文件
						signFile := libs.Config.InfoWorkFileWatch.SDKWorkSuccessFile + "_" + time.Now().Format("20060102150405")
						destPath := signFile + "folder/"
						MakeDir(destPath)
						err := CopyDir(libs.Config.InfoWorkFileWatch.SDKPath, destPath)
						if err != nil {
							fmt.Println("复制文件夹error :", err)
							os.RemoveAll(destPath)
						}
						CreateFile(signFile)
						TodoWork = false
						fmt.Println(time.Now().Format(Layout))
					}
				}
				if HashTodoWork {
					LatestTime, _ := time.ParseInLocation(Layout, HashLatestTimeStr, time.Local)
					sub1 := time.Since(LatestTime)
					subMinute := sub1.Minutes()
					if subMinute >= float64(libs.Config.InfoWorkFileWatch.WaitingTime) {
						fmt.Println("to do work(hash)")
						//复制监控文件夹 到 新文件
						hashSignFile := libs.Config.InfoWorkFileWatch.HashworkSuccessFile + "_" + time.Now().Format("20060102150405")
						destPath := hashSignFile + "folder/"
						MakeDir(destPath)
						//查找ITW.zip文件
						ITWFilePath, findErr := FindFile(libs.Config.InfoWorkFileWatch.HashPath, HashFileSuffix)
						if findErr != nil {
							fmt.Println("查找ITW文件error :", findErr)
						}
						//ITW.zip文件名称
						ITWFileName := path.Base(ITWFilePath)
						_, err := CopyFile(ITWFilePath, filepath.Join(destPath, ITWFileName))
						if err != nil {
							fmt.Println("复制ITW文件error :", err)
							fmt.Println(ITWFilePath, filepath.Join(destPath, ITWFileName))
							return
						}
						CreateFile(hashSignFile)
						HashTodoWork = false
						fmt.Println(time.Now().Format(Layout))
					}
				}
			}
		}

	}()
	<-done
}

// 获取目录中的文件列表（仅第一层目录）
func ListDir(dir, suffix string) (files []string, err error) {
	files = []string{}
	_dir, err := ioutil.ReadDir(dir)
	if err != nil {
		return nil, err
	}
	suffix = strings.ToLower(suffix) //匹配后缀
	for _, _file := range _dir {
		if _file.IsDir() {
			continue //忽略目录
		}
		if len(suffix) == 0 || strings.HasSuffix(strings.ToLower(_file.Name()), suffix) {
			//文件后缀匹配
			files = append(files, path.Join(dir, _file.Name()))
		}
	}

	return files, nil
}

// 获取目录中的所有文件列表（包含子目录）
func WalkDir(dir, suffix string) (files []string, err error) {
	files = []string{}

	err = filepath.Walk(dir, func(fname string, fi os.FileInfo, err error) error {
		if fi.IsDir() {
			//忽略目录
			return nil
		}

		if len(suffix) == 0 || strings.HasSuffix(strings.ToLower(fi.Name()), suffix) {
			//文件后缀匹配
			files = append(files, fname)
		}

		return nil
	})

	return files, err
}

//创建文件
func CreateFile(path string) {
	// 创建文件,返回两个值,一个是创建的文件信息,二是错误信息
	f, err := os.Create(path)
	if err != nil { //如果有错误 打印错误 返回
		fmt.Println("err=", err)
		return
	}
	defer f.Close()
}

//使用io.Copy
func CopyFile(src, des string) (written int64, err error) {
	srcFile, err := os.Open(src)
	if err != nil {
		return 0, err
	}
	defer srcFile.Close()

	//获取源文件的权限
	fi, _ := srcFile.Stat()
	perm := fi.Mode()

	//desFile, err := os.Create(des)  //无法复制源文件的所有权限
	desFile, err := os.OpenFile(des, os.O_RDWR|os.O_CREATE|os.O_TRUNC, perm) //复制源文件的所有权限
	if err != nil {
		return 0, err
	}
	defer desFile.Close()

	return io.Copy(desFile, srcFile)
}

func CopyDir(srcPath, desPath string) error {
	//检查目录是否正确
	if srcInfo, err := os.Stat(srcPath); err != nil {
		return err
	} else {
		if !srcInfo.IsDir() {
			return errors.New("源路径不是一个正确的目录！")
		}
	}

	if desInfo, err := os.Stat(desPath); err != nil {
		return err
	} else {
		if !desInfo.IsDir() {
			return errors.New("目标路径不是一个正确的目录！")
		}
	}

	if strings.TrimSpace(srcPath) == strings.TrimSpace(desPath) {
		return errors.New("源路径与目标路径不能相同！")
	}

	err := filepath.Walk(srcPath, func(path string, f os.FileInfo, err error) error {
		if f == nil {
			return err
		}

		//复制目录是将源目录中的子目录复制到目标路径中，不包含源目录本身
		if path == srcPath {
			return nil
		}

		//生成新路径
		destNewPath := strings.Replace(path, srcPath, desPath, -1)
		if !f.IsDir() {
			_, err := CopyFile(path, destNewPath)
			if err != nil {
				fmt.Println("复制文件error:", err)
				fmt.Println("源路径:", path, "   目标路径:", destNewPath)
				return err
			}
		} else {
			if !FileIsExisted(destNewPath) {
				return MakeDir(destNewPath)
			}
		}

		return nil
	})

	return err
}

func MakeDir(dir string) error {
	if !FileIsExisted(dir) {
		if err := os.MkdirAll(dir, 0777); err != nil { //os.ModePerm
			fmt.Println("MakeDir failed:", err)
			return err
		}
	}
	return nil
}
func FileIsExisted(filename string) bool {
	existed := true
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		existed = false
	}
	return existed
}

func FindFile(folderPath, suffix string) (outCmdStr string, err error) {
	tempPath := filepath.Join(folderPath)
	// find  -name "*ITW.zip" | xargs ls -lnta |awk '{print $9}'  -lrt 按时间降序  -lnt按时间升序
	findPackageCommand := fmt.Sprintf(`find %s -name "*%s" | xargs -r  ls  -lnta |awk '{print $9}'`, tempPath+"/", suffix)
	outStr, cmdErr := RunCommand(findPackageCommand)
	var packagePath string
	if cmdErr != nil {
		logging.ErrorLogger.Errorf("cmd.Run() find full tar err", cmdErr)
		return packagePath, cmdErr
	}
	packagePath = fmt.Sprintf("%s", strings.Split(outStr, "\n")[0])
	return packagePath, nil
}

func RunCommand(command string) (outCmdStr string, err error) {
	//执行本地命令,接收命令执行结果
	cmd := exec.Command("/bin/bash", "-c", command)
	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout // 标准输出
	cmd.Stderr = &stderr // 标准错误
	cmdErr := cmd.Run()
	outStr, _ := string(stdout.Bytes()), string(stderr.Bytes())
	if cmdErr != nil {
		logging.ErrorLogger.Errorf("cmd.Run() failed err", cmdErr)
		return outStr, cmdErr
	}
	return outStr, nil
}

func main() {
	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "usage: %s [options] [command]\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Commands:\n")
		fmt.Fprintf(os.Stderr, "\n")
		fmt.Fprintf(os.Stderr, "  --config <path>\n")
		fmt.Fprintf(os.Stderr, "    设置配置文件路径\n")
		fmt.Fprintf(os.Stderr, "\n")
	}
	flag.Parse()

	err := libs.InitConfig(*config)
	if err != nil {
		panic(fmt.Sprintf("系统配置初始化失败: %+v\n", err))
	}
	//SDK文件监控
	watch, _ := fsnotify.NewWatcher()

	defer watch.Close()
	w := Wacth{
		wacth: watch,
	}
	w.watchDir(libs.Config.InfoWorkFileWatch.SDKPath, libs.Config.InfoWorkFileWatch.HashPath)
}
