package coverityresult

import (
	"encoding/json"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/coverityresult/dcoverityrule"
	"net/url"
	"time"
)

func SyncEpgRule() error {
	now := time.Now()
	objects := []map[string]interface{}{}
	url, err := url.Join<PERSON><PERSON>(libs.Config.CoveritySync.Url, libs.Config.CoveritySync.EpgRuleFileName)
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		return err
	}

	resp, err := covResultClient.R().Get(url)
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		return err
	}

	rules := []EpgRule{}
	err = json.Unmarshal(resp.Bytes(), &rules)
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		return err
	}

	for _, rule := range rules {
		levels := rule.Levels
		for _, level := range levels {
			objects = append(objects, map[string]interface{}{
				"CheckerName": rule.Rule,
				"EpgLevel":    level.EpgLevel,
				"CreatedAt":   now,
			})
		}
	}
	// gorm batch create
	return dcoverityrule.BatchCreate(objects)
}
