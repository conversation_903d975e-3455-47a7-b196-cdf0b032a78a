package services

import (
	"context"
	"fmt"
	"strings"
	"time"

	"irisAdminApi/application/controllers/openfeishu/meeting/models"
	"irisAdminApi/application/logging"
)

// MeetingService 会议服务
type MeetingService struct {
	bitableService      *BitableService
	calendarService     *CalendarService
	userResolverService *SimpleUserResolverService
}

// NewMeetingService 创建会议服务实例
func NewMeetingService(bitableService *BitableService, calendarService *CalendarService, userResolverService *SimpleUserResolverService) *MeetingService {
	return &MeetingService{
		bitableService:      bitableService,
		calendarService:     calendarService,
		userResolverService: userResolverService,
	}
}

// handleError 统一错误处理方法
func (m *MeetingService) handleError(operation string, err error, recordID string) *models.ProcessResult {
	logging.ErrorLogger.Errorf("%s失败: %v", operation, err)
	return &models.ProcessResult{
		RecordID:     recordID,
		Status:       models.StatusFailed,
		ErrorMessage: fmt.Sprintf("%s失败: %v", operation, err),
		ProcessTime:  time.Now(),
	}
}

// logAndReturnError 记录错误并返回格式化错误
func (m *MeetingService) logAndReturnError(operation string, err error, result *models.BatchProcessResult) error {
	logging.ErrorLogger.Errorf("%s失败: %v", operation, err)
	return fmt.Errorf("%s失败: %w", operation, err)
}

// ProcessMeetings 处理会议数据 - 核心业务流程
func (m *MeetingService) ProcessMeetings(ctx context.Context) (*models.BatchProcessResult, error) {
	logging.InfoLogger.Info("开始处理会议数据")
	startTime := time.Now()

	// 初始化处理结果
	result := &models.BatchProcessResult{
		StartTime: startTime,
		Results:   make([]models.ProcessResult, 0),
	}

	defer func() {
		result.EndTime = time.Now()
		result.Duration = result.EndTime.Sub(result.StartTime)
		logging.InfoLogger.Infof("会议处理完成，总耗时: %v, 总数: %d, 成功: %d, 失败: %d",
			result.Duration, result.TotalCount, result.SuccessCount, result.FailedCount)
	}()

	// 1. 从多维表格读取会议数据
	meetingRecords, err := m.bitableService.ReadMeetingRecords(ctx)
	if err != nil {
		return result, m.logAndReturnError("读取会议数据", err, result)
	}

	if len(meetingRecords) == 0 {
		logging.InfoLogger.Info("未找到待处理的会议记录")
		return result, nil
	}

	result.TotalCount = len(meetingRecords)
	logging.InfoLogger.Infof("找到 %d 条待处理的会议记录", result.TotalCount)

	// 2. 逐条处理会议记录
	for _, meetingData := range meetingRecords {
		processResult := m.processSingleMeeting(ctx, meetingData)
		result.Results = append(result.Results, *processResult)

		if processResult.Status == models.StatusSuccess {
			result.SuccessCount++
		} else {
			result.FailedCount++
		}

		// 实时回填状态到多维表格
		if err := m.bitableService.UpdateRecordStatus(ctx, meetingData.RecordID, processResult); err != nil {
			logging.ErrorLogger.Errorf("回填记录 %s 状态失败: %v", meetingData.RecordID, err)
		}
	}

	return result, nil
}

// shouldSkipDueToNoAttendees 检查是否因为无参会人员而跳过处理
func (m *MeetingService) shouldSkipDueToNoAttendees(attendeeNames string) bool {
	// 1. 检查空值情况
	if attendeeNames == "" {
		return true
	}

	// 2. 去除前后空格后检查
	trimmed := strings.TrimSpace(attendeeNames)
	if trimmed == "" {
		return true
	}

	// 3. 检查是否为"无"（忽略大小写）
	if strings.ToLower(trimmed) == "无" {
		return true
	}

	// 4. 检查是否为常见的"无参会人员"表示
	lowerTrimmed := strings.ToLower(trimmed)
	skipValues := []string{"无", "none", "null", "无参会人员", "无人参会", "暂无", "n/a", "na"}
	for _, skipValue := range skipValues {
		if lowerTrimmed == skipValue {
			return true
		}
	}

	return false
}

// fetchActualAttendees 获取日程的实际参会人员信息
func (m *MeetingService) fetchActualAttendees(ctx context.Context, eventID string) ([]string, int, error) {
	if m.calendarService == nil {
		return nil, 0, fmt.Errorf("日历服务未初始化")
	}

	if eventID == "" {
		return nil, 0, fmt.Errorf("EventID为空")
	}

	// 调用日历服务获取实际参会人员
	attendeeOpenIDs, err := m.calendarService.GetEventAttendees(ctx, eventID)
	if err != nil {
		logging.ErrorLogger.Errorf("获取日程 %s 实际参会人员失败: %v", eventID, err)
		return nil, 0, err
	}

	count := len(attendeeOpenIDs)
	logging.InfoLogger.Infof("成功获取日程 %s 的实际参会人员: %d 人", eventID, count)

	return attendeeOpenIDs, count, nil
}

// processSingleMeeting 处理单个会议
func (m *MeetingService) processSingleMeeting(ctx context.Context, meetingData *models.StandardMeetingData) *models.ProcessResult {
	logging.InfoLogger.Infof("开始处理会议: %s (RecordID: %s)", meetingData.Title, meetingData.RecordID)

	// 🔧 新增：检查参会人员字段，如果为"无"或空值则跳过处理
	if m.shouldSkipDueToNoAttendees(meetingData.AttendeeNames) {
		logging.InfoLogger.Infof("记录 %s 参会人员为空或'无'，跳过日程创建 - AttendeeNames: '%s'",
			meetingData.RecordID, meetingData.AttendeeNames)

		return &models.ProcessResult{
			RecordID:     meetingData.RecordID,
			Status:       models.StatusSkipped,
			ErrorMessage: "参会人员为空或'无'，无需创建日程",
			ProcessTime:  time.Now(),
		}
	}

	// 处理操作类型，支持向后兼容
	operationType := meetingData.OperationType
	if operationType == "" {
		operationType = "创建" // 默认为创建操作
	}

	logging.InfoLogger.Infof("操作类型: %s", operationType)

	// 根据操作类型分发处理
	switch operationType {
	case "删除":
		return m.processDeleteOperation(ctx, meetingData)
	case "更新":
		return m.processUpdateOperation(ctx, meetingData)
	case "创建":
		return m.processCreateOperation(ctx, meetingData)
	default:
		logging.ErrorLogger.Errorf("不支持的操作类型: %s", operationType)
		return &models.ProcessResult{
			RecordID:     meetingData.RecordID,
			Status:       models.StatusFailed,
			ErrorMessage: fmt.Sprintf("不支持的操作类型: %s", operationType),
			ProcessTime:  time.Now(),
		}
	}
}

// processCreateOperation 处理创建操作（原有逻辑）
func (m *MeetingService) processCreateOperation(ctx context.Context, meetingData *models.StandardMeetingData) *models.ProcessResult {
	// 验证会议数据
	if err := m.validateMeetingData(meetingData); err != nil {
		return m.handleError("会议数据验证", err, meetingData.RecordID)
	}

	// 处理参会人员解析
	if m.userResolverService != nil {
		if err := m.userResolverService.ProcessAttendeesForMeeting(meetingData); err != nil {
			logging.ErrorLogger.Errorf("参会人员解析失败: %v", err)
			// 不中断流程，但记录错误信息
			if meetingData.ErrorMessage != "" {
				meetingData.ErrorMessage += "; "
			}
			meetingData.ErrorMessage += fmt.Sprintf("参会人员解析失败: %v", err)
		}
	}

	// 创建日程
	event, err := m.calendarService.CreateEvent(ctx, meetingData)
	if err != nil {
		return m.handleError("创建日程", err, meetingData.RecordID)
	}

	// 构建成功结果（日程ID回填增强）
	result := &models.ProcessResult{
		RecordID:      meetingData.RecordID,
		Status:        models.StatusSuccess,
		EventID:       event.EventID,
		EventURL:      event.EventURL,
		OperationType: models.OperationTypeCreate,
		ProcessTime:   time.Now(),
	}

	// 🔧 新增：获取实际参会人员信息用于回填
	if event.EventID != "" {
		actualAttendees, actualCount, err := m.fetchActualAttendees(ctx, event.EventID)
		if err != nil {
			// 获取实际参会人员失败不影响主流程，只记录警告
			logging.ErrorLogger.Errorf("获取日程 %s 实际参会人员失败: %v", event.EventID, err)
		} else {
			result.ActualAttendeeOpenIDs = actualAttendees
			result.ActualAttendeeCount = actualCount
			logging.InfoLogger.Infof("成功获取日程 %s 实际参会人员: %d 人", event.EventID, actualCount)
		}
	}

	logging.InfoLogger.Infof("成功创建会议: %s, EventID: %s", meetingData.Title, event.EventID)
	return result
}

// validateMeetingData 验证会议数据
func (m *MeetingService) validateMeetingData(data *models.StandardMeetingData) error {
	if data == nil {
		return fmt.Errorf("会议数据不能为空")
	}

	if data.RecordID == "" {
		return fmt.Errorf("记录ID不能为空")
	}

	if data.Title == "" {
		return fmt.Errorf("会议标题不能为空")
	}

	if data.StartTime.IsZero() {
		return fmt.Errorf("开始时间不能为空")
	}

	if data.EndTime.IsZero() {
		return fmt.Errorf("结束时间不能为空")
	}

	if data.EndTime.Before(data.StartTime) {
		return fmt.Errorf("结束时间不能早于开始时间")
	}

	// 检查时间是否过于久远
	now := time.Now()
	if data.StartTime.Before(now.AddDate(-1, 0, 0)) {
		return fmt.Errorf("开始时间不能早于一年前")
	}

	if data.StartTime.After(now.AddDate(2, 0, 0)) {
		return fmt.Errorf("开始时间不能晚于两年后")
	}

	// 检查会议时长是否合理（最长7天）
	duration := data.EndTime.Sub(data.StartTime)
	if duration > 7*24*time.Hour {
		return fmt.Errorf("会议时长不能超过7天")
	}

	if duration < time.Minute {
		return fmt.Errorf("会议时长不能少于1分钟")
	}

	return nil
}

// GetProcessingStatus 获取处理状态
func (m *MeetingService) GetProcessingStatus(ctx context.Context, requestID string) (string, error) {
	// 这里可以从Redis或其他存储中获取处理状态
	// 当前简化实现，直接返回完成状态
	return models.StatusSuccess, nil
}

// ValidateServices 验证服务配置
func (m *MeetingService) ValidateServices() error {
	if m.bitableService == nil {
		return fmt.Errorf("BitableService 未初始化")
	}

	if m.calendarService == nil {
		return fmt.Errorf("CalendarService 未初始化")
	}

	// 验证各服务的配置
	if err := m.bitableService.ValidateConfig(); err != nil {
		return fmt.Errorf("BitableService 配置错误: %w", err)
	}

	if err := m.calendarService.ValidateConfig(); err != nil {
		return fmt.Errorf("CalendarService 配置错误: %w", err)
	}

	return nil
}

// ProcessMeetingsWithRetry 带重试的会议处理
func (m *MeetingService) ProcessMeetingsWithRetry(ctx context.Context, maxRetries int) (*models.BatchProcessResult, error) {
	var lastErr error

	for attempt := 1; attempt <= maxRetries; attempt++ {
		logging.InfoLogger.Infof("开始第 %d 次处理尝试", attempt)

		result, err := m.ProcessMeetings(ctx)
		if err == nil {
			return result, nil
		}

		lastErr = err
		logging.ErrorLogger.Errorf("第 %d 次处理失败: %v", attempt, err)

		if attempt < maxRetries {
			// 指数退避重试
			backoffDuration := time.Duration(attempt*attempt) * time.Second
			logging.InfoLogger.Infof("等待 %v 后重试", backoffDuration)

			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(backoffDuration):
				continue
			}
		}
	}

	return nil, fmt.Errorf("处理失败，已重试 %d 次: %w", maxRetries, lastErr)
}

// BatchProcessMeetings 批量处理会议（支持分批）
func (m *MeetingService) BatchProcessMeetings(ctx context.Context, batchSize int) (*models.BatchProcessResult, error) {
	if batchSize <= 0 {
		batchSize = models.DefaultBatchSize
	}

	logging.InfoLogger.Infof("开始批量处理会议，批次大小: %d", batchSize)

	// 读取所有待处理记录
	allRecords, err := m.bitableService.ReadMeetingRecords(ctx)
	if err != nil {
		return nil, fmt.Errorf("读取会议记录失败: %w", err)
	}

	if len(allRecords) == 0 {
		logging.InfoLogger.Info("未找到待处理的会议记录")
		return &models.BatchProcessResult{
			StartTime: time.Now(),
			EndTime:   time.Now(),
		}, nil
	}

	// 分批处理
	totalResult := &models.BatchProcessResult{
		StartTime:  time.Now(),
		TotalCount: len(allRecords),
		Results:    make([]models.ProcessResult, 0, len(allRecords)),
	}

	for i := 0; i < len(allRecords); i += batchSize {
		end := i + batchSize
		if end > len(allRecords) {
			end = len(allRecords)
		}

		batch := allRecords[i:end]
		logging.InfoLogger.Infof("处理第 %d-%d 条记录", i+1, end)

		// 处理当前批次
		for _, meetingData := range batch {
			processResult := m.processSingleMeeting(ctx, meetingData)
			totalResult.Results = append(totalResult.Results, *processResult)

			if processResult.Status == models.StatusSuccess {
				totalResult.SuccessCount++
			} else {
				totalResult.FailedCount++
			}

			// 实时回填状态
			if err := m.bitableService.UpdateRecordStatus(ctx, meetingData.RecordID, processResult); err != nil {
				logging.ErrorLogger.Errorf("回填记录 %s 状态失败: %v", meetingData.RecordID, err)
			}
		}

		// 批次间短暂休息，避免API限流
		if end < len(allRecords) {
			time.Sleep(100 * time.Millisecond)
		}
	}

	totalResult.EndTime = time.Now()
	totalResult.Duration = totalResult.EndTime.Sub(totalResult.StartTime)

	logging.InfoLogger.Infof("批量处理完成，总耗时: %v, 成功: %d, 失败: %d",
		totalResult.Duration, totalResult.SuccessCount, totalResult.FailedCount)

	return totalResult, nil
}

// LogOperation 记录操作日志
func (m *MeetingService) LogOperation(ctx context.Context, log *models.OperationLog) error {
	if log == nil {
		return fmt.Errorf("操作日志不能为空")
	}

	// 设置时间戳
	if log.Timestamp.IsZero() {
		log.Timestamp = time.Now()
	}

	// 生成日志ID
	if log.ID == "" {
		log.ID = fmt.Sprintf("log_%d_%s", time.Now().UnixNano(), log.EventID)
	}

	// 异步记录日志，不影响主业务流程
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logging.ErrorLogger.Errorf("记录操作日志发生panic: %v", r)
			}
		}()

		// 记录到应用日志
		if log.Result == "success" {
			logging.InfoLogger.Infof("操作成功: EventID=%s, Operation=%s, User=%s",
				log.EventID, log.OperationType, log.UserID)
		} else {
			logging.ErrorLogger.Errorf("操作失败: EventID=%s, Operation=%s, User=%s, Error=%s",
				log.EventID, log.OperationType, log.UserID, log.ErrorMessage)
		}
	}()

	return nil
}

// CreateCancelLog 创建取消操作日志
func (m *MeetingService) CreateCancelLog(eventID, userID, reason, result string, duration int64) *models.OperationLog {
	changes := map[string]interface{}{
		"cancel_reason": reason,
		"cancel_time":   time.Now().Format("2006-01-02 15:04:05"),
		"status":        "cancelled",
	}

	return &models.OperationLog{
		EventID:       eventID,
		RecordID:      eventID,
		OperationType: models.OperationTypeCancel,
		UserID:        userID,
		Changes:       changes,
		Result:        result,
		Timestamp:     time.Now(),
		Duration:      duration,
	}
}

// CreateUpdateLog 创建更新操作日志
func (m *MeetingService) CreateUpdateLog(eventID, userID string, changedFields []string, result string, duration int64) *models.OperationLog {
	changes := map[string]interface{}{
		"changed_fields": changedFields,
		"update_time":    time.Now().Format("2006-01-02 15:04:05"),
	}

	return &models.OperationLog{
		EventID:       eventID,
		RecordID:      eventID,
		OperationType: models.OperationTypeUpdate,
		UserID:        userID,
		Changes:       changes,
		Result:        result,
		Timestamp:     time.Now(),
		Duration:      duration,
	}
}

// ProcessDeleteOperation 处理删除操作（导出方法用于测试）
func (m *MeetingService) ProcessDeleteOperation(ctx context.Context, meetingData *models.StandardMeetingData) *models.ProcessResult {
	return m.processDeleteOperation(ctx, meetingData)
}

// processDeleteOperation 处理删除操作（内部实现）
func (m *MeetingService) processDeleteOperation(ctx context.Context, meetingData *models.StandardMeetingData) *models.ProcessResult {
	logging.InfoLogger.Infof("开始删除日程: %s (RecordID: %s)", meetingData.Title, meetingData.RecordID)

	// 验证是否有EventID（从多维表格的日程ID字段获取）
	// 注意：这里需要从多维表格中读取已存储的EventID
	// 暂时使用meetingData中的EventID，实际应用中可能需要额外查询
	eventID := meetingData.EventID
	if eventID == "" {
		logging.ErrorLogger.Error("删除操作需要有效的EventID")
		return &models.ProcessResult{
			RecordID:     meetingData.RecordID,
			Status:       models.StatusFailed,
			ErrorMessage: "删除操作需要有效的EventID，请确保日程已创建",
			ProcessTime:  time.Now(),
		}
	}

	// 调用日历服务删除日程
	err := m.calendarService.CancelEvent(ctx, eventID, "用户删除操作")
	if err != nil {
		return m.handleError("删除日程", err, meetingData.RecordID)
	}

	// 构建成功结果
	now := time.Now()
	result := &models.ProcessResult{
		RecordID:      meetingData.RecordID,
		Status:        models.StatusSuccess,
		EventID:       eventID,
		OperationType: models.OperationTypeCancel,
		CancelStatus:  models.CancelStatusCancelled,
		CancelledAt:   &now,
		CancelReason:  "用户删除操作",
		ProcessTime:   now,
	}

	logging.InfoLogger.Infof("成功删除日程: %s, EventID: %s", meetingData.Title, eventID)
	return result
}

// processUpdateOperation 处理更新操作
func (m *MeetingService) processUpdateOperation(ctx context.Context, meetingData *models.StandardMeetingData) *models.ProcessResult {
	logging.InfoLogger.Infof("开始更新日程: %s (RecordID: %s)", meetingData.Title, meetingData.RecordID)

	// 验证是否有EventID
	eventID := meetingData.EventID
	if eventID == "" {
		logging.ErrorLogger.Error("更新操作需要有效的EventID")
		return &models.ProcessResult{
			RecordID:     meetingData.RecordID,
			Status:       models.StatusFailed,
			ErrorMessage: "更新操作需要有效的EventID，请确保日程已创建",
			ProcessTime:  time.Now(),
		}
	}

	// 🔧 修复：处理参会人员解析（更新操作中缺失的关键步骤）
	logging.DebugLogger.Debugf("更新操作开始前参会人员状态 - AttendeeNames: '%s', AttendeeOpenIDs: %v",
		meetingData.AttendeeNames, meetingData.AttendeeOpenIDs)

	if m.userResolverService != nil {
		if err := m.userResolverService.ProcessAttendeesForMeeting(meetingData); err != nil {
			logging.ErrorLogger.Errorf("更新操作中参会人员解析失败: %v", err)
			// 不中断流程，但记录错误信息
			if meetingData.ErrorMessage != "" {
				meetingData.ErrorMessage += "; "
			}
			meetingData.ErrorMessage += fmt.Sprintf("参会人员解析失败: %v", err)
		} else {
			logging.InfoLogger.Infof("更新操作中成功解析参会人员: %d 人", len(meetingData.AttendeeOpenIDs))
		}
	} else {
		logging.ErrorLogger.Error("用户解析服务未初始化，无法处理参会人员")
	}

	logging.DebugLogger.Debugf("更新操作解析后参会人员状态 - AttendeeOpenIDs: %v", meetingData.AttendeeOpenIDs)

	// 构建更新数据（包含参会人员信息）
	updates := &models.EventUpdates{
		Title:       &meetingData.Title,
		Description: &meetingData.Description,
		StartTime:   &meetingData.StartTime,
		EndTime:     &meetingData.EndTime,
		Location:    &meetingData.Location,
		Timezone:    &meetingData.Timezone,
	}

	// 智能参会人员处理：优先使用状态同步，兼容增量更新
	if len(meetingData.AttendeeOpenIDs) > 0 {
		// 主要业务场景：参会人员状态同步
		updates.AttendeeOpenIDs = meetingData.AttendeeOpenIDs
		logging.InfoLogger.Infof("更新日程时将同步参会人员状态，目标人数: %d, 参会人员: %v",
			len(meetingData.AttendeeOpenIDs), meetingData.AttendeeOpenIDs)
	} else {
		// 检查是否有参会人员名称但解析失败
		if meetingData.AttendeeNames != "" {
			logging.ErrorLogger.Errorf("更新操作中有参会人员名称 '%s' 但未解析出OpenID，请检查用户映射配置",
				meetingData.AttendeeNames)
		}

		// 兼容性场景：增量更新
		if len(meetingData.AddAttendeeOpenIDs) > 0 {
			updates.AddAttendees = meetingData.AddAttendeeOpenIDs
			logging.InfoLogger.Infof("更新日程时将添加 %d 名参会人员", len(meetingData.AddAttendeeOpenIDs))
		}
		if len(meetingData.RemoveAttendeeOpenIDs) > 0 {
			updates.RemoveAttendees = meetingData.RemoveAttendeeOpenIDs
			logging.InfoLogger.Infof("更新日程时将删除 %d 名参会人员", len(meetingData.RemoveAttendeeOpenIDs))
		}

		// 如果没有任何参会人员操作，记录调试信息
		if len(meetingData.AddAttendeeOpenIDs) == 0 && len(meetingData.RemoveAttendeeOpenIDs) == 0 {
			logging.DebugLogger.Debugf("更新操作中没有参会人员变更，AttendeeNames: '%s'", meetingData.AttendeeNames)
		}
	}

	// 调用日历服务更新日程
	_, err := m.calendarService.UpdateEvent(ctx, eventID, updates)
	if err != nil {
		return m.handleError("更新日程", err, meetingData.RecordID)
	}

	// 构建成功结果
	result := &models.ProcessResult{
		RecordID:      meetingData.RecordID,
		Status:        models.StatusSuccess,
		EventID:       eventID,
		OperationType: models.OperationTypeUpdate,
		ChangedFields: []string{"title", "description", "start_time", "end_time", "location", "timezone"},
		ProcessTime:   time.Now(),
	}

	// 🔧 新增：获取实际参会人员信息用于回填
	if eventID != "" {
		actualAttendees, actualCount, err := m.fetchActualAttendees(ctx, eventID)
		if err != nil {
			// 获取实际参会人员失败不影响主流程，只记录警告
			logging.ErrorLogger.Errorf("获取日程 %s 实际参会人员失败: %v", eventID, err)
		} else {
			result.ActualAttendeeOpenIDs = actualAttendees
			result.ActualAttendeeCount = actualCount
			logging.InfoLogger.Infof("成功获取日程 %s 实际参会人员: %d 人", eventID, actualCount)
		}
	}

	logging.InfoLogger.Infof("成功更新日程: %s, EventID: %s", meetingData.Title, eventID)
	return result
}
