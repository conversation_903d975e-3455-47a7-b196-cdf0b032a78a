s = "INSERT INTO resource_pool_interfaces \
    (name, interface, driver, mac, sw_name, sw_port, vlan_id, vlan_mode, type, sw_port_status) VALUES \
        ('{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}');"
with open("result.txt", 'w', encoding='utf8') as r:
    with open('2.txt', 'r', encoding='utf8') as f:
        for i in f:
            a = i.strip().split("\t")
            r.write((s.format(a[0], a[1], a[2], a[3], a[4], a[5], a[6], a[7], a[8], a[9])) + '\n')
