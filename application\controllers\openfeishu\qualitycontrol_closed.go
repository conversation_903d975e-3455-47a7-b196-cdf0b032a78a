package openfeishu

import (
	"encoding/json"
	"fmt"
	"io"
	"irisAdminApi/application/logging"
	"net/http"
	"net/http/cookiejar"
	"net/url"
	"strings"
	"time"
)

// 闭环管理数据获取模块
type ClosedLoopManager struct {
	baseURL    string
	httpClient *http.Client
	cookies    []*http.Cookie
}

// 创建新的闭环管理实例
func NewClosedLoopManager(baseURL string, cookies []*http.Cookie) *ClosedLoopManager {
	// 创建标准的Cookie Jar
	jar, err := cookiejar.New(nil)
	if err != nil {
		logging.ErrorLogger.Errorf("创建CookieJar失败: %v", err)
		// 如果创建失败，返回没有CookieJar的客户端
		return &ClosedLoopManager{
			baseURL: baseURL,
			httpClient: &http.Client{
				Timeout: 30 * time.Second,
			},
			cookies: cookies,
		}
	}

	// 将Cookie设置到CookieJar中
	if len(cookies) > 0 {
		parsedURL, err := url.Parse(baseURL)
		if err != nil {
			logging.ErrorLogger.Errorf("解析基础URL失败: %v", err)
		} else {
			jar.SetCookies(parsedURL, cookies)
			logging.InfoLogger.Infof("闭环管理：成功将 %d 个Cookie设置到CookieJar中", len(cookies))
		}
	}

	return &ClosedLoopManager{
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
			Jar:     jar,
		},
		cookies: cookies,
	}
}

// 判断是否需要获取闭环数据
func (clm *ClosedLoopManager) shouldFetchClosedLoop(faultLevel, devFaultReason string) bool {
	logging.InfoLogger.Infof("判断是否需要闭环 - 故障等级: %s, 研发故障原因: %s", faultLevel, devFaultReason)

	switch faultLevel {
	case "三级故障":
		// 三级故障：只有软件问题或硬件问题需要闭环
		result := devFaultReason == "软件问题" || devFaultReason == "硬件问题"
		logging.InfoLogger.Infof("三级故障判断结果: %v", result)
		return result

	case "二级故障":
		// 二级故障：除了客户原因和环境问题，其他都需要闭环
		result := devFaultReason != "客户原因" && devFaultReason != "环境问题"
		logging.InfoLogger.Infof("二级故障判断结果: %v", result)
		return result

	case "一级故障":
		// 一级故障：都需要闭环
		logging.InfoLogger.Info("一级故障判断结果: true")
		return true

	default:
		// 其他情况不需要闭环
		return false
	}
}

// 获取闭环管理数据（支持指定根因类型）
func (clm *ClosedLoopManager) fetchClosedLoopData(qualityNumber string, rootCauseType int) (*ClosedLoopResponse, error) {
	logging.InfoLogger.Infof("获取品控单号 %s 的闭环数据，根因类型: %d", qualityNumber, rootCauseType)

	// 构建闭环管理API请求URL（使用实际接口）
	apiURL := fmt.Sprintf("%s/pzkz_closed_trace/load_pzkz_closed_trace_init", clm.baseURL)

	// 构建请求参数
	params := url.Values{}
	params.Set("entityId", qualityNumber)
	params.Set("entityType", "PzkzInfo")                          // 使用正确的实体类型
	params.Set("rootCauseType", fmt.Sprintf("%d", rootCauseType)) // 指定根因类型

	// 创建POST请求
	req, err := http.NewRequest("POST", apiURL, strings.NewReader(params.Encode()))
	if err != nil {
		return nil, fmt.Errorf("创建闭环请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	// 发送请求
	resp, err := clm.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送闭环请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("闭环API请求失败，状态码: %d", resp.StatusCode)
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取闭环响应失败: %v", err)
	}

	// 解析JSON响应
	var response ClosedLoopResponse
	err = json.Unmarshal(body, &response)
	if err != nil {
		return nil, fmt.Errorf("解析闭环JSON响应失败: %v", err)
	}

	// 检查响应类型
	if response.Type != "success" {
		logging.InfoLogger.Infof("品控单号 %s 无闭环数据: %s", qualityNumber, response.Content)
		return nil, nil // 返回nil表示无闭环数据，不是错误
	}

	logging.InfoLogger.Infof("成功获取品控单号 %s 的闭环数据", qualityNumber)
	return &response, nil
}

// 获取品控单的完整闭环数据（根据故障等级获取所需的根因类型）
func (clm *ClosedLoopManager) fetchCompleteClosedLoopData(qualityNumber string, faultLevel string, devFaultReason string) (map[int]*ClosedLoopResponse, error) {
	logging.InfoLogger.Infof("获取品控单号 %s 的完整闭环数据", qualityNumber)

	result := make(map[int]*ClosedLoopResponse)

	// 根据故障等级确定需要查询的根因类型
	var rootCauseTypes []int

	switch faultLevel {
	case "三级故障":
		// 三级故障：只有软件问题或硬件问题需要查询发生的技术根因(rootCauseType=1)
		if devFaultReason == "软件问题" || devFaultReason == "硬件问题" {
			rootCauseTypes = []int{1} // 发生的技术根因
		}
	case "二级故障":
		// 二级故障：除了客户原因和环境问题，其他都需要查询四个根因
		if devFaultReason != "客户原因" && devFaultReason != "环境问题" {
			rootCauseTypes = []int{1, 2, 3, 4} // 四个根因
		}
	case "一级故障":
		// 一级故障：都需要查询四个根因
		rootCauseTypes = []int{1, 2, 3, 4} // 四个根因
	}

	// 如果不需要查询闭环数据，直接返回空结果
	if len(rootCauseTypes) == 0 {
		logging.InfoLogger.Infof("品控单号 %s 无需查询闭环数据", qualityNumber)
		return result, nil
	}

	// 逐个查询每种根因类型
	for _, rootCauseType := range rootCauseTypes {
		closedData, err := clm.fetchClosedLoopData(qualityNumber, rootCauseType)
		if err != nil {
			logging.ErrorLogger.Errorf("获取品控单号 %s 根因类型 %d 的闭环数据失败: %v", qualityNumber, rootCauseType, err)
			continue // 继续查询其他根因类型
		}

		if closedData != nil {
			result[rootCauseType] = closedData
		}

		// 添加延迟避免请求过快
		time.Sleep(200 * time.Millisecond)
	}

	logging.InfoLogger.Infof("品控单号 %s 完整闭环数据获取完成，获取到 %d 种根因类型", qualityNumber, len(result))
	return result, nil
}

// 批量获取闭环数据（修改后：无需闭环的记录也会被处理）
func (clm *ClosedLoopManager) BatchFetchClosedLoopData(qualityItems []QualityControlItem) (map[string]map[int]*ClosedLoopResponse, error) {
	logging.InfoLogger.Infof("开始批量获取闭环数据，品控记录数: %d", len(qualityItems))

	closedLoopData := make(map[string]map[int]*ClosedLoopResponse)
	processedCount := 0
	noClosedLoopCount := 0
	fetchedCount := 0

	for _, item := range qualityItems {
		qualityNumber := fmt.Sprintf("%d", item.ID)

		// 判断是否需要获取闭环数据（修复：使用FaultLvlName字段）
		if !clm.shouldFetchClosedLoop(item.FaultLvlName, item.DevFaultReasonName) {
			noClosedLoopCount++
			logging.InfoLogger.Infof("品控单号 %s 无需闭环，设置为空闭环数据以便后续处理基础字段", qualityNumber)
			// 不再跳过，而是设置空的闭环数据，确保记录会被处理
			closedLoopData[qualityNumber] = make(map[int]*ClosedLoopResponse)
			processedCount++
			continue
		}

		// 获取完整闭环数据（修复：使用FaultLvlName字段）
		completeClosedData, err := clm.fetchCompleteClosedLoopData(qualityNumber, item.FaultLvlName, item.DevFaultReasonName)
		if err != nil {
			logging.ErrorLogger.Errorf("获取品控单号 %s 完整闭环数据失败: %v", qualityNumber, err)
			// 即使获取失败，也要设置空数据确保记录被处理
			closedLoopData[qualityNumber] = make(map[int]*ClosedLoopResponse)
			processedCount++
			continue
		}

		// 设置闭环数据（可能为空）
		closedLoopData[qualityNumber] = completeClosedData
		if len(completeClosedData) > 0 {
			fetchedCount++
		}
		processedCount++

		// 添加延迟，避免请求过快
		if processedCount%5 == 0 { // 减少批次大小，因为现在可能有多个API调用
			time.Sleep(1 * time.Second)
		}
	}

	logging.InfoLogger.Infof("闭环数据获取完成 - 总处理: %d, 无需闭环: %d, 成功获取闭环数据: %d",
		processedCount, noClosedLoopCount, fetchedCount)

	return closedLoopData, nil
}
