package kpi

import "irisAdminApi/application/models"

type Contribution struct {
	models.ModelBase
	Description        string `gorm:"not null; type:varchar(512)"`
	ContributedAt      string `gorm:"not null; type:varchar(30)"`
	ContributionTypeID uint   `gorm:"not null"`
	UserID             uint   `gorm:"not null"`
	Status             uint   `gorm:"not null；default:0"` //0:评审中  1:通过 2:不通过
	Uuid               string `gorm:"not null; type:varchar(60)"`
	ContributionID     uint   `gorm:"not null；default:0"`
}

type ContributionType struct {
	models.ModelBase
	Name string `gorm:"not null; type:varchar(60)"`
}

type ContributionPoint struct {
	models.ModelBase
	ContributionID uint `gorm:"not null"`
	ContributorID  uint `gorm:"not null"` // foriegn user id
	DepartmentID   uint `gorm:"not null"`
	Point          int  `gorm:"not null"`
}

type ContributionReview struct {
	models.ModelBase
	ContributionID uint `gorm:"not null"`
	ReviewerID     uint `gorm:"not null"`
	Judge          uint `gorm:"not null；default:0"` //0:评审中  1:通过 2:不通过
	Comment        string
	// Reviewer       user.User `gorm:"foreignKey:ReviewerID;references:ID;"`
}

type ContributionVersion struct {
	models.ModelBase
	ContributionID  uint `gorm:"uniqueIndex; not null"`
	ContributionIDs string
}
