package transdocumentauto

import (
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/documentauto/ddagdocument"
	"irisAdminApi/service/dao/documentauto/ddagdocumentversion"
	"irisAdminApi/service/dao/documentauto/ddagscreenshot"
	"irisAdminApi/service/dao/documentauto/ddagserie"
	"irisAdminApi/service/dao/documentauto/ddagtask"
	"irisAdminApi/service/dao/documentauto/ddagtemplate"
	"irisAdminApi/service/dao/user/duser"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

// 使用事务创建任务记录和文档记录（按照系列进行创建）
// 先创建任务记录 再创建文档记录
// 文档根据系列ID进行创建
// 字段：任务ID 系列ID 版本号 编译记录id（字符串逗号隔开） 产品型号、发布类型、基线版本、截图路径、模板路径
func CreateTaskAndDocumentTransaction(userID int, taskRecord map[string]interface{}, seriesIDToBuildNames map[int]string, buildNameToProductModel map[string]string, seriesToBuildRecordMap map[int][]int, screenshots map[int]string, wordTemplates map[int]string) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		series := []*ddagtask.DagSerie{}
		err := easygorm.GetEasyGormDb().Model(ddagserie.Model()).Where("id in ?", strings.Split(taskRecord["SeriesIDs"].(string), ",")).Find(&series).Error
		if err != nil {
			logging.ErrorLogger.Errorf("find data err ", err)
			return err
		}
		//构建ddagtask.Response
		taskResponse := ddagtask.Response{
			SeriesIDs:       taskRecord["SeriesIDs"].(string),
			BuildRecordIDs:  taskRecord["BuildRecordIDs"].(string),
			ReleaseVersion:  taskRecord["ReleaseVersion"].(string),
			VersionNumber:   taskRecord["VersionNumber"].(string),
			ReleaseType:     taskRecord["ReleaseType"].(string),
			BaselineVersion: taskRecord["BaselineVersion"].(string),
			Description:     taskRecord["Description"].(string),
			UserID:          taskRecord["UserID"].(uint),
			Status:          0,
			UUID:            taskRecord["UUID"].(string),
			StartDate:       taskRecord["StartDate"].(string),
			EndDate:         taskRecord["EndDate"].(string),
			Series:          series,
		}
		// 创建任务记录
		if err := tx.Create(&taskResponse).Error; err != nil {
			return err
		}
		//通过uuid 获取任务ID
		var ddagtaskRec ddagtask.Response
		err = tx.Model(ddagtask.Model()).Where("uuid = ?", taskRecord["UUID"]).Select("id").Find(&ddagtaskRec).Error
		if err != nil {
			return err
		}
		//获取系列ID数据
		seriesData := strings.Split(taskRecord["SeriesIDs"].(string), ",")

		//创建文档记录
		//判断screenshots 是否为空
		if len(screenshots) == 0 {
			//screenshots 为空时，根据系列ID+版本号 在截图表中获取最新的截图
			screenshots = getLatestScreenshot(seriesData, taskRecord["VersionNumber"].(string))
		}

		//判断wordTemplates 是否为空
		if len(wordTemplates) == 0 {
			//wordTemplates 为空时，根据系列ID+版本号 在模板表中获取最新的模板
			wordTemplates = getLatestTemplate(seriesData, taskRecord["VersionNumber"].(string))
		}

		//循环判断wordTemplates 是否为空
		for _, seriesIDStr := range seriesData {
			seriesID, err := strconv.Atoi(strings.TrimSpace(seriesIDStr))
			if err != nil {
				logging.ErrorLogger.Errorf("无效的系列ID: %s", seriesIDStr)
				return err
			}
			if wordTemplates[seriesID] == "" {
				//从系列ID + 基线版本中获取模板
				seriesIDStr := strconv.Itoa(seriesID)
				wordTemplate, err := ddagtemplate.GetTemplateBySeriesID(seriesIDStr, taskRecord["BaselineVersion"].(string))
				if err != nil {
					logging.ErrorLogger.Errorf("版本号 %s 的系列ID %s 的模板不存在", taskRecord["VersionNumber"].(string), seriesIDStr)
					return fmt.Errorf("版本号 %s 的系列ID %s 的模板不存在", taskRecord["VersionNumber"].(string), seriesIDStr)
				}
				if wordTemplate.TemplatePath == "" {
					logging.ErrorLogger.Errorf("版本号 %s 的系列ID %s 的模板不存在", taskRecord["VersionNumber"].(string), seriesIDStr)
					return fmt.Errorf("版本号 %s 的系列ID %s 的模板不存在", taskRecord["VersionNumber"].(string), seriesIDStr)
				} else {
					wordTemplates[seriesID] = wordTemplate.TemplatePath
				}
			}
		}

		for _, seriesIDStr := range seriesData {
			// 将 seriesID 从 string 转换为 int
			seriesID, err := strconv.Atoi(strings.TrimSpace(seriesIDStr))
			if err != nil {
				logging.ErrorLogger.Errorf("无效的系列ID: %s", seriesIDStr)
				return err
			}
			fmt.Println(seriesID, seriesToBuildRecordMap[seriesID])
			// 获取编译名称 通过 | 分割 再获取产品型号
			// 如果有多个编译名称 则需要将编译名称拼接起来
			buildName := seriesIDToBuildNames[seriesID]
			buildNames := strings.Split(buildName, "|")
			productModel := ""
			for _, buildName := range buildNames {
				productModel += buildNameToProductModel[buildName] + "|"
			}
			productModel = strings.TrimRight(productModel, "|")
			//获取文档版本数据
			docVersionData, err := ddagdocumentversion.GetDocumentVersionBySeriesID(uint(seriesID), taskRecord["VersionNumber"].(string))
			if err != nil {
				return err
			}
			//获取用户名
			user := duser.User{}
			err = user.Find(uint(userID))
			if err != nil {
				return err
			}
			userName := user.Name
			//创建文档版本数据
			docVersionCreateData := map[string]interface{}{
				"SeriesID":       uint(seriesID),
				"ReleaseVersion": taskRecord["VersionNumber"].(string),
				"VersionDesc":    taskRecord["Description"].(string),
				"VersionEditor":  userName,
				"CreatedAt":      time.Now(),
				"UpdatedAt":      time.Now(),
			}
			if docVersionData.ID == 0 {
				docVersionCreateData["VersionNumber"] = 1.0
			} else {
				docVersionCreateData["VersionNumber"] = docVersionData.VersionNumber + 0.1
			}
			if err := tx.Model(ddagdocumentversion.Model()).Create(docVersionCreateData).Error; err != nil {
				return err
			}

			docRecord := map[string]interface{}{
				"TaskID":                ddagtaskRec.ID,
				"SeriesID":              uint(seriesID),
				"VersionNumber":         taskRecord["VersionNumber"].(string),
				"ReleaseType":           taskRecord["ReleaseType"].(string),
				"BaselineVersion":       taskRecord["BaselineVersion"].(string),
				"BuildRecordIDs":        joinIntSlice(seriesToBuildRecordMap[seriesID], ","),
				"BuildName":             seriesIDToBuildNames[seriesID],
				"ProductModel":          productModel,
				"UserID":                taskRecord["UserID"].(uint),
				"ScreenshotPath":        screenshots[seriesID],
				"TemplatePath":          wordTemplates[seriesID],
				"DocumentVersionNumber": docVersionCreateData["VersionNumber"],
				"ReleaseReason":         taskRecord["Description"].(string),
				"CreatedAt":             time.Now(),
				"UpdatedAt":             time.Now(),
			}
			if err := tx.Model(ddagdocument.Model()).Create(docRecord).Error; err != nil {
				return err
			}
		}
		return nil
	})
	return err
}

// CreateReSubmitTaskAndDocumentTransaction 使用事务重新提交任务记录和文档记录
func CreateReSubmitTaskAndDocumentTransaction(
	userID int,
	newTaskRecord map[string]interface{},
	previousTaskID uint,
	seriesIDToBuildNames map[int]string,
	buildNameToProductModel map[string]string,
	seriesToBuildRecordMap map[int][]int,
	screenshots map[int]string,
	wordTemplates map[int]string,
	previousVersionNumbers map[int]string,
) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 获取系列信息
		series := []*ddagtask.DagSerie{}
		err := tx.Model(ddagserie.Model()).Where("id IN ?", strings.Split(newTaskRecord["SeriesIDs"].(string), ",")).Find(&series).Error
		if err != nil {
			logging.ErrorLogger.Errorf("查找系列数据出错: %v", err)
			return err
		}

		// 构建新的任务记录
		taskResponse := ddagtask.Response{
			SeriesIDs:       newTaskRecord["SeriesIDs"].(string),
			BuildRecordIDs:  newTaskRecord["BuildRecordIDs"].(string),
			ReleaseVersion:  newTaskRecord["ReleaseVersion"].(string),
			VersionNumber:   newTaskRecord["VersionNumber"].(string),
			ReleaseType:     newTaskRecord["ReleaseType"].(string),
			BaselineVersion: newTaskRecord["BaselineVersion"].(string),
			Description:     newTaskRecord["Description"].(string),
			UserID:          newTaskRecord["UserID"].(uint),
			Status:          0,
			UUID:            newTaskRecord["UUID"].(string),
			StartDate:       newTaskRecord["StartDate"].(string),
			EndDate:         newTaskRecord["EndDate"].(string),
			Series:          series,
		}

		// 创建新的任务记录
		if err := tx.Create(&taskResponse).Error; err != nil {
			logging.ErrorLogger.Errorf("创建任务记录出错: %v", err)
			return err
		}

		// 通过UUID获取新创建的任务ID
		var newTaskRecordFetched ddagtask.Response
		err = tx.Model(ddagtask.Model()).Where("uuid = ?", newTaskRecord["UUID"]).Select("id").Find(&newTaskRecordFetched).Error
		if err != nil {
			logging.ErrorLogger.Errorf("通过UUID获取任务记录出错: %v", err)
			return err
		}

		// 获取系列ID数据
		seriesData := strings.Split(newTaskRecord["SeriesIDs"].(string), ",")

		// 如果screenshots为空，从上一个任务中获取截图
		if len(screenshots) == 0 {
			screenshots = getLatestScreenshotFromPreviousTask(tx, previousTaskID, seriesData, newTaskRecord["VersionNumber"].(string))
			//截图没有可以忽略
		}

		// 如果wordTemplates为空，从上一个任务中获取模板
		if len(wordTemplates) == 0 {
			wordTemplates = getLatestTemplateFromPreviousTask(tx, previousTaskID, seriesData, newTaskRecord["VersionNumber"].(string))
			//模板不能忽略，需要保持提示
			if wordTemplates == nil {
				return fmt.Errorf("无法从上一个任务中获取模板")
			}
		}

		//获取用户名
		user := duser.User{}
		err = user.Find(uint(userID))
		if err != nil {
			return err
		}
		userName := user.Name
		for _, seriesIDStr := range seriesData {
			// 将 seriesID 从 string 转换为 int
			seriesID, err := strconv.Atoi(strings.TrimSpace(seriesIDStr))
			if err != nil {
				logging.ErrorLogger.Errorf("无效的系列ID: %s", seriesIDStr)
				return err
			}
			//更新文档版本数据
			updateData := map[string]interface{}{
				"VersionEditor": userName,
				"VersionDesc":   newTaskRecord["Description"].(string),
				"UpdatedAt":     time.Now(),
			}
			tx.Model(ddagdocumentversion.Model()).
				Where("series_id = ? AND version_number = ?", seriesID, previousVersionNumbers[seriesID]).
				Updates(updateData)
			if err != nil {
				logging.ErrorLogger.Errorf("更新文档版本数据出错: %v", err)
				return err
			}

			// 获取编译名称，通过 | 分割，再获取产品型号
			buildName := seriesIDToBuildNames[seriesID]
			buildNames := strings.Split(buildName, "|")
			productModel := ""
			for _, buildName := range buildNames {
				productModel += buildNameToProductModel[buildName] + "|"
			}
			productModel = strings.TrimRight(productModel, "|")
			// 创建新的文档记录
			docRecord := map[string]interface{}{
				"TaskID":                newTaskRecordFetched.ID,
				"SeriesID":              uint(seriesID),
				"VersionNumber":         newTaskRecord["VersionNumber"].(string),
				"ReleaseType":           newTaskRecord["ReleaseType"].(string),
				"BaselineVersion":       newTaskRecord["BaselineVersion"].(string),
				"BuildRecordIDs":        joinIntSlice(seriesToBuildRecordMap[seriesID], ","),
				"BuildName":             seriesIDToBuildNames[seriesID],
				"ProductModel":          productModel,
				"UserID":                newTaskRecord["UserID"].(uint),
				"ScreenshotPath":        screenshots[seriesID],
				"TemplatePath":          wordTemplates[seriesID],
				"DocumentVersionNumber": previousVersionNumbers[seriesID],
				"ReleaseReason":         newTaskRecord["Description"].(string),
				"CreatedAt":             time.Now(),
				"UpdatedAt":             time.Now(),
			}
			if err := tx.Model(ddagdocument.Model()).Create(docRecord).Error; err != nil {
				logging.ErrorLogger.Errorf("创建文档记录出错: %v", err)
				return err
			}
		}
		return nil
	})
	return err
}

// getLatestScreenshotFromPreviousTask 从上一个任务获取最新的截图
func getLatestScreenshotFromPreviousTask(tx *gorm.DB, previousTaskID uint, seriesData []string, versionNumber string) map[int]string {
	screenshots := make(map[int]string)
	var screenshot ddagdocument.DocumentCompositeData
	for _, series := range seriesData {
		err := tx.Model(ddagdocument.Model()).
			Where("task_id = ? AND series_id = ? AND version_number = ?", previousTaskID, series, versionNumber).
			Order("id DESC").
			Select("screenshot_path").
			Find(&screenshot).Error
		if err == nil && screenshot.ScreenshotPath != "" {
			seriesID, _ := strconv.Atoi(series)
			screenshots[seriesID] = screenshot.ScreenshotPath
		} else {
			logging.ErrorLogger.Errorf("从上一个任务获取系列ID %s 的截图出错或不存在", series)
			//截图没有可以忽略
		}
	}
	return screenshots
}

// getLatestTemplateFromPreviousTask 从上一个任务获取最新的模板
func getLatestTemplateFromPreviousTask(tx *gorm.DB, previousTaskID uint, seriesData []string, versionNumber string) map[int]string {
	wordTemplates := make(map[int]string)
	var template ddagdocument.DocumentCompositeData
	for _, series := range seriesData {
		err := tx.Model(ddagdocument.Model()).
			Where("task_id = ? AND series_id = ? AND version_number = ?", previousTaskID, series, versionNumber).
			Order("id DESC").
			Select("template_path").
			Find(&template).Error
		if err == nil && template.TemplatePath != "" {
			seriesID, _ := strconv.Atoi(series)
			wordTemplates[seriesID] = template.TemplatePath
		} else {
			logging.ErrorLogger.Errorf("从上一个任务获取系列ID %s 的模板出错或不存在", series)
			//模板不能忽略，需要保持提示

		}
	}
	return wordTemplates
}

func joinIntSlice(ids []int, sep string) string {
	strIDs := make([]string, len(ids))
	for i, id := range ids {
		strIDs[i] = strconv.Itoa(id)
	}
	return strings.Join(strIDs, sep)
}

// 获取系列ID+版本号 最新的截图
func getLatestScreenshot(seriesData []string, versionNumber string) map[int]string {
	db := easygorm.GetEasyGormDb()
	//返回的截图路径 key:系列ID value:截图路径
	screenshots := make(map[int]string)
	var screenshot ddagscreenshot.Response
	for _, series := range seriesData {
		db.Model(ddagscreenshot.Model()).Where("series_id = ? AND version = ?", series, versionNumber).Order("id DESC").Find(&screenshot)
		screenshots[int(screenshot.SeriesID)] = screenshot.ScreenshotPath
	}
	return screenshots
}

// 获取系列ID+版本号 最新的模板
func getLatestTemplate(seriesData []string, versionNumber string) map[int]string {
	db := easygorm.GetEasyGormDb()
	//返回模板路径 key:系列ID value:模板路径
	wordTemplates := make(map[int]string)
	var wordTemplate ddagtemplate.Response
	for _, series := range seriesData {
		db.Model(ddagtemplate.Model()).Where("series_id = ? AND version = ?", series, versionNumber).Order("id DESC").Find(&wordTemplate)
		wordTemplates[int(wordTemplate.SeriesID)] = wordTemplate.TemplatePath
	}
	return wordTemplates
}
