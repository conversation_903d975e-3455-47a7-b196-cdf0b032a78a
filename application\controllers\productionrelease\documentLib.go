package productionrelease

import (
	"archive/zip"
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/productionrelease/dproductiondocumenttemplate"
	"irisAdminApi/service/dao/productionrelease/dproductionprocinst"
	"irisAdminApi/service/dao/productionrelease/dproductionproctaskdocument"
	"log"
	"net/http"
	"net/url"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"

	"github.com/beevik/etree"
	"github.com/qifengzhang007/gooxml/document"
)

func downloadFile(url string, filePath string) error {
	// 创建一个 HTTP 客户端
	client := http.Client{}

	// 发送 GET 请求
	resp, err := client.Get(url)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	switch resp.StatusCode {
	case http.StatusOK:
		// 文件存在，继续下载
	case http.StatusNotFound:
		return errors.New("file not found")
	default:
		statusCode := fmt.Sprintf("%d", resp.StatusCode)
		return errors.New("download failed: server returned a non-OK status: " + statusCode)
	}

	// 创建一个文件用于保存下载的内容
	out, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer out.Close()

	// 将 HTTP 响应的内容写入文件
	_, err = io.Copy(out, resp.Body)
	if err != nil {
		return err
	}

	return nil
}

func extractFileNameFromURL(url string) string {
	// 匹配文件名的正则表达式
	re := regexp.MustCompile(`[^/]+\.bin$`)

	// 使用正则表达式查找文件名
	match := re.FindString(url)
	return match
}

func extractFilename(url string) (string, error) {
	pattern := `([^/]+)$`
	re := regexp.MustCompile(pattern)
	matches := re.FindStringSubmatch(url)

	if len(matches) == 0 {
		return "", fmt.Errorf("no filename found in the URL")
	}
	return matches[1], nil
}

func getFileSize(path string) int64 {
	if !exists(path) {
		return 0
	}
	fileInfo, err := os.Stat(path)
	if err != nil {
		return 0
	}
	return fileInfo.Size()
}

func exists(path string) bool {
	_, err := os.Stat(path)
	return err == nil || os.IsExist(err)
}

func CheckKeywordsInDoc(docPath string, keywords []string) bool {
	doc, err := document.Open(docPath)
	if err != nil {
		logging.ErrorLogger.Errorf("无法打开文档：%s", err)
		return false
	}

	// 遍历文档中的段落
	for _, p := range doc.Paragraphs() {
		runs := p.Runs()
		runPtrs := make([]*document.Run, len(runs))
		for i := range runs {
			runPtrs[i] = &runs[i]
		}
		if searchTextInRuns(runPtrs, keywords) {
			return true
		}
	}
	//表格
	for _, table := range doc.Tables() {
		for _, row := range table.Rows() {
			for _, cell := range row.Cells() {
				for _, para := range cell.Paragraphs() {
					runs := para.Runs()
					runPtrs := make([]*document.Run, len(runs))
					for i := range runs {
						runPtrs[i] = &runs[i]
					}
					if searchTextInRuns(runPtrs, keywords) {
						return true
					}
				}
			}
		}
	}
	return false
}

type PlaceholderResult struct {
	Found        bool     `json:"found"`
	Placeholders []string `json:"placeholders"`
}

func CheckKeywordsInDocV2(docPath string, keywords []string) bool {
	keywordsJSON, err := json.Marshal(keywords)
	if err != nil {
		fmt.Println("Error marshalling keywords:", err)
		return false
	}
	shell := filepath.Join(libs.Config.ProductionFileStorage.Plugin, "check_docx_placeholders.py")
	command := fmt.Sprintf("python3 %s '%s' '%s'", shell, docPath, keywordsJSON)
	output, err := libs.ExecCommand(command)
	if err != nil {
		return false
	}
	// 解析结果
	var result PlaceholderResult
	err = json.Unmarshal([]byte(output), &result)
	if err != nil {
		fmt.Println("Error unmarshalling result:", err)
		return false
	}
	if result.Found {
		return true
	}
	return false
}

// 替换文本方法
func ReplaceTextInDoc(templatePath string, replacements map[string]string, outputPath string) (bool, error) {
	doc, err := document.Open(templatePath)
	if err != nil {
		return false, fmt.Errorf("无法打开模板文档：%s", err)
	}

	replaced := false

	// 替换段落中的文本
	for _, para := range doc.Paragraphs() {
		runs := para.Runs()
		runPtrs := make([]*document.Run, len(runs))
		for i := range runs {
			runPtrs[i] = &runs[i]
		}
		replaceTextInRuns(runPtrs, replacements)
		replaced = true
	}

	// 替换表格中的文本
	for _, table := range doc.Tables() {
		for _, row := range table.Rows() {
			for _, cell := range row.Cells() {
				for _, para := range cell.Paragraphs() {
					runs := para.Runs()
					runPtrs := make([]*document.Run, len(runs))
					for i := range runs {
						runPtrs[i] = &runs[i]
					}
					replaceTextInRuns(runPtrs, replacements)
				}
			}
		}
	}

	// 替换页眉中的文本(替换表格内容需要另外处理)
	for _, h := range doc.Headers() {
		for _, p := range h.Paragraphs() {
			runs := p.Runs()
			runPtrs := make([]*document.Run, len(runs))
			for i := range runs {
				runPtrs[i] = &runs[i]
			}
			replaceTextInRuns(runPtrs, replacements)
		}

	}

	// 替换页脚中的文本(替换表格内容需要另外处理)
	for _, f := range doc.Footers() {
		for _, p := range f.Paragraphs() {
			runs := p.Runs()
			runPtrs := make([]*document.Run, len(runs))
			for i := range runs {
				runPtrs[i] = &runs[i]
			}
			replaceTextInRuns(runPtrs, replacements)
			replaced = true
		}
	}
	err = doc.SaveToFile(outputPath)
	if err != nil {
		return false, fmt.Errorf("无法保存文档：%s", err)
	}

	return replaced, nil
}

func replaceTextInRuns(runs []*document.Run, replacements map[string]string) {
	runIndexTextMap := make(map[int]string)
	var replaceText string
	for i, run := range runs {
		runText := run.Text()
		runIndexTextMap[i] = runText
		fmt.Println(runText)
		// 全量替换，遍历所有运行块，查找并替换标记词
		//以下场景
		//{{name}}
		for old, new := range replacements {
			//完全匹配替换
			if strings.Contains(run.Text(), old) {
				newText := strings.ReplaceAll(run.Text(), old, new)
				run.ClearContent()
				run.AddText(newText)
			}
		}
		//检查是否有连续的两个 runs 满足条件
		// 以下场景
		//：{{name
		// }}
		if i >= 1 && strings.Contains(runIndexTextMap[i-1], "{{") && runText == "}}" {
			// 提取第一个 run 中 "{{" 之后的值
			startIndex := strings.Index(runIndexTextMap[i-1], "{{")
			if startIndex != -1 {
				key := runIndexTextMap[i-1][startIndex+2:]
				key = strings.TrimSpace(key)

				// 如果提取的值在 replacements 中，则替换为对应值
				if replacement, exists := replacements["{{"+key+"}}"]; exists {
					replaceText = replacement
					// 替换第一个值中包含提取值的部分（包括 "{{"）
					newText := strings.Replace(runIndexTextMap[i-1], "{{"+key, replaceText, 1)
					// 将第一个 run 文本替换为新值
					runs[i-1].ClearContent()
					runs[i-1].AddText(newText)
					runs[i].ClearContent()
				}
			}
		}

		//检查是否有连续的两个 runs 满足条件
		// 以下场景
		// {{
		// name}}:
		if i >= 1 && runIndexTextMap[i-1] == "{{" && strings.Contains(runText, "}}") {
			// 提取第二个 run 中 "}}" 之前的值
			endIndex := strings.Index(runText, "}}")
			if endIndex != -1 {
				key := runText[:endIndex]
				key = strings.TrimSpace(key)
				// 如果提取的值在 replacements 中，则替换第二个值中包含 "}}" 之前的内容
				if replacement, exists := replacements["{{"+key+"}}"]; exists {
					replaceText = replacement
					// 替换第二个值中包含 "}}" 之前的内容
					newText := strings.Replace(runText, key+"}}", replaceText, 1)
					runIndexTextMap[i] = newText
					// 将第二个 run 文本替换为新值
					runs[i-1].ClearContent()
					runs[i].ClearContent()
					runs[i].AddText(newText)

				}
			}
		}
		// 检查是否有连续的三个 runs 满足条件
		// 以下场景
		// {{
		// name
		// }}
		if i >= 2 && runIndexTextMap[i-2] == "{{" && runIndexTextMap[i-1] != "}}" && runText == "}}" {
			// 检查中间的文本是否在 replacements 中，如果在，则替换为对应值
			key := runIndexTextMap[i-1]
			if replacement, exists := replacements["{{"+key+"}}"]; exists {
				replaceText = replacement
				// 将中间的文本替换为新值
				runs[i-2].ClearContent()
				runs[i-1].ClearContent()
				runs[i].ClearContent()
				runs[i-1].AddText(replaceText)
			}
		}

		// 检查是否有连续的三个 runs 满足条件
		// 以下场景
		//     {{
		// name
		// }}
		if i >= 2 && strings.Join(strings.Fields(runIndexTextMap[i-2]), "") == "{{" && runIndexTextMap[i-1] != "}}" && runText == "}}" {
			// 检查中间的文本是否在 replacements 中，如果在，则替换为对应值
			key := runIndexTextMap[i-1]
			if replacement, exists := replacements["{{"+key+"}}"]; exists {
				replaceText = replacement
				// 将中间的文本替换为新值
				runs[i-2].ClearContent()
				runs[i-1].ClearContent()
				runs[i].ClearContent()
				runs[i-1].AddText(replaceText)
			}
		}

		// 检查是否有连续的三个 runs 满足条件
		// 以下场景
		// {{
		// name
		//              }}
		if i >= 2 && runIndexTextMap[i-2] == "{{" && runIndexTextMap[i-1] != "}}" && strings.Join(strings.Fields(runText), "") == "}}" {
			// 检查中间的文本是否在 replacements 中，如果在，则替换为对应值
			key := runIndexTextMap[i-1]
			if replacement, exists := replacements["{{"+key+"}}"]; exists {
				replaceText = replacement
				// 将中间的文本替换为新值
				runs[i-2].ClearContent()
				runs[i-1].ClearContent()
				runs[i].ClearContent()
				runs[i-1].AddText(replaceText)
			}
		}

		// 检查是否有连续的三个 runs 满足条件
		// 以下场景
		//     {{
		// name
		//        }}
		if i >= 2 && strings.Join(strings.Fields(runIndexTextMap[i-2]), "") == "{{" && runIndexTextMap[i-1] != "}}" && strings.Join(strings.Fields(runText), "") == "}}" {
			// 检查中间的文本是否在 replacements 中，如果在，则替换为对应值
			key := runIndexTextMap[i-1]
			if replacement, exists := replacements["{{"+key+"}}"]; exists {
				replaceText = replacement
				// 将中间的文本替换为新值
				runs[i-2].ClearContent()
				runs[i-1].ClearContent()
				runs[i].ClearContent()
				runs[i-1].AddText(replaceText)
			}
		}

		// 检查是否有连续的三个 runs 满足条件
		// 以下场景
		// {{
		// 	product
		// 	Model}}

		if i >= 2 && strings.Join(strings.Fields(runIndexTextMap[i-2]), "") == "{{" && runIndexTextMap[i-1] != "}}" && strings.Contains(runText, "}}") {
			// 检查中间的文本是否在 replacements 中，如果在，则替换为对应值
			// 提取第三个 run 中 "}}" 之前的值
			endIndex := strings.Index(runText, "}}")
			if endIndex != -1 {
				//第二个的值和第三个 run 中 "}}" 之前的值组成key
				key := runIndexTextMap[i-1] + runText[:endIndex]
				key = strings.TrimSpace(key)
				if replacement, exists := replacements["{{"+key+"}}"]; exists {
					replaceText = replacement
					// 将中间的文本替换为新值
					runs[i-2].ClearContent()
					runs[i-1].ClearContent()
					runs[i].ClearContent()
					runs[i-1].AddText(replaceText)
				}
			}
		}

		// 检查是否有连续的四个 runs 满足条件
		// 以下场景
		// abc {
		// {
		// name
		// }}
		if i >= 3 && strings.Contains(runIndexTextMap[i-3], "{") && runIndexTextMap[i-2] == "{" && runText == "}}" {
			key := runIndexTextMap[i-1]
			// 如果第三个值在 replacements 中，则处理替换
			if replacement, exists := replacements["{{"+key+"}}"]; exists {
				replaceText = replacement
				// 去除第一个值中的 "{"
				newText := strings.Replace(runIndexTextMap[i-3], "{", "", 1)
				runs[i-3].ClearContent()
				runs[i-3].AddText(newText)
				// 去除第二个值中的 "{"
				runs[i-2].ClearContent()
				// 替换第三个值为替换值
				runs[i-1].ClearContent()
				runs[i-1].AddText(replaceText)
				// 去除第四个值中的 "{"
				runs[i].ClearContent()
			}
		}

	}
}

// 查找文本中是否包含关键字
func searchTextInRuns(runs []*document.Run, keywords []string) bool {
	var sb strings.Builder
	runIndexTextMap := make(map[int]string)
	// 合并所有Run的文本并记录每个Run的起始索引
	for i, run := range runs {
		runText := run.Text()
		runIndexTextMap[i] = runText
		sb.WriteString(runText)
	}
	fmt.Println(keywords)
	// 查找合并合并后的文本中的占位符
	combinedText := sb.String()
	fmt.Println(combinedText)
	for _, keyword := range keywords {
		if strings.Contains(combinedText, keyword) {
			// 如果找到一个关键字，直接返回 true
			return true
		}
	}
	return false
}

func replaceTextInDocHeaderFooter(templatePath string, replacements map[string]string, outputPath string) error {

	// 打开Word文档
	r, err := zip.OpenReader(templatePath)
	if err != nil {
		logging.ErrorLogger.Errorf("打开Word文档失败: %v", err)
		return err
	}
	defer r.Close()

	// 创建新的Word文档
	newDoc := new(bytes.Buffer)
	w := zip.NewWriter(newDoc)

	// 遍历文档中的所有文件
	for _, f := range r.File {
		rc, err := f.Open()
		if err != nil {
			logging.ErrorLogger.Errorf("打开Word文档内部文件失败: %v", err)
			return err
		}

		content, err := io.ReadAll(rc)
		if err != nil {
			rc.Close()
			logging.ErrorLogger.Errorf("读取Word文档内部文件内容失败: %v", err)
			return err
		}
		rc.Close()

		// 检查是否是页眉或页脚文件
		if strings.Contains(f.Name, "header") || strings.Contains(f.Name, "footer") {
			// 解析XML
			doc := etree.NewDocument()
			if err := doc.ReadFromBytes(content); err != nil {
				logging.ErrorLogger.Errorf("XML解析失败: %v", err)
				return err
			}

			// 遍历文档中的所有表格
			for _, tbl := range doc.FindElements("//w:tbl") {
				for _, row := range tbl.SelectElements("w:tr") {
					for _, cell := range row.SelectElements("w:tc") {
						for _, para := range cell.SelectElements("w:p") {
							elementRuns := para.SelectElements("w:r")
							replaceTextInElementRuns(elementRuns, replacements)
						}
					}
				}
			}

			// 将修改后的XML写回到内容中
			content, err = doc.WriteToBytes()
			if err != nil {
				logging.ErrorLogger.Errorf("将修改后的XML写回内容失败: %v", err)
				return err
			}
		}

		// 将修改后的内容写入新文档
		wf, err := w.Create(f.Name)
		if err != nil {
			logging.ErrorLogger.Errorf("创建新文档文件失败: %v", err)
			return err
		}
		_, err = wf.Write(content)
		if err != nil {
			logging.ErrorLogger.Errorf("写入新文档内容失败: %v", err)
			return err
		}
	}

	// 关闭写入器以确保所有内容都被写入新文档
	err = w.Close()
	if err != nil {
		logging.ErrorLogger.Errorf("关闭新文档写入器失败: %v", err)
		return err
	}

	// 将新文档保存到文件系统
	err = os.WriteFile(outputPath, newDoc.Bytes(), 0644)
	if err != nil {
		logging.ErrorLogger.Errorf("将新文档保存到文件系统失败: %v", err)
		return err
	}
	return nil
}

func replaceTextInElementRuns(runs []*etree.Element, replacements map[string]string) {
	runIndexTextMap := make(map[int]string)
	var replaceText string
	for i, run := range runs {
		wtElement := run.SelectElement("w:t")
		if wtElement == nil {
			// 如果元素不存在，则跳过当前迭代
			continue
		}
		runText := wtElement.Text()
		runIndexTextMap[i] = runText
		// fmt.Println(runIndexTextMap[i])
		// 全量替换，遍历所有运行块，查找并替换标记词
		//以下场景
		//{{name}}
		for old, new := range replacements {
			//完全匹配替换
			if strings.Contains(runText, old) {
				newText := strings.ReplaceAll(run.SelectElement("w:t").Text(), old, new)
				run.SelectElement("w:t").SetText("")
				run.SelectElement("w:t").SetText(newText)
			}
		}
		//检查是否有连续的两个 runs 满足条件
		// 以下场景
		//：{{name
		// }}
		if i >= 1 && strings.Contains(runIndexTextMap[i-1], "{{") && runText == "}}" {
			// 提取第一个 run 中 "{{" 之后的值
			startIndex := strings.Index(runIndexTextMap[i-1], "{{")
			if startIndex != -1 {
				key := runIndexTextMap[i-1][startIndex+2:]
				key = strings.TrimSpace(key)
				// 如果提取的值在 replacements 中，则替换为对应值
				if replacement, exists := replacements["{{"+key+"}}"]; exists {
					replaceText = replacement
					// 替换第一个值中包含提取值的部分（包括 "{{"）
					newText := strings.Replace(runIndexTextMap[i-1], "{{"+key, replaceText, 1)
					// 将第一个 run 文本替换为新值
					runs[i-1].SelectElement("w:t").SetText("")
					runs[i-1].SelectElement("w:t").SetText(newText)
					runs[i].SelectElement("w:t").SetText("")
				}
			}
		}

		//检查是否有连续的两个 runs 满足条件
		// 以下场景
		// {{
		// name}}:
		if i >= 1 && runIndexTextMap[i-1] == "{{" && strings.Contains(runText, "}}") {
			// 提取第二个 run 中 "}}" 之前的值
			endIndex := strings.Index(runText, "}}")
			if endIndex != -1 {
				key := runText[:endIndex]
				key = strings.TrimSpace(key)
				// 如果提取的值在 replacements 中，则替换第二个值中包含 "}}" 之前的内容
				if replacement, exists := replacements["{{"+key+"}}"]; exists {
					replaceText = replacement
					// 替换第二个值中包含 "}}" 之前的内容
					newText := strings.Replace(runText, key+"}}", replaceText, 1)
					runIndexTextMap[i] = newText
					// 将第二个 run 文本替换为新值
					runs[i-1].SelectElement("w:t").SetText("")
					runs[i].SelectElement("w:t").SetText("")
					runs[i].SelectElement("w:t").SetText(newText)

				}
			}
		}
		// 检查是否有连续的三个 runs 满足条件
		// 以下场景
		// {{
		// name
		//}}
		if i >= 2 && runIndexTextMap[i-2] == "{{" && runIndexTextMap[i-1] != "}}" && runText == "}}" {
			// 检查中间的文本是否在 replacements 中，如果在，则替换为对应值
			key := runIndexTextMap[i-1]
			if replacement, exists := replacements["{{"+key+"}}"]; exists {
				replaceText = replacement
				// 将中间的文本替换为新值
				runs[i-2].SelectElement("w:t").SetText("")
				runs[i-1].SelectElement("w:t").SetText("")
				runs[i].SelectElement("w:t").SetText("")
				runs[i-1].SelectElement("w:t").SetText(replaceText)
			}
		}
		// 检查是否有连续的三个 runs 满足条件
		// 以下场景
		// {{
		// name
		//}}abc
		if i >= 2 && runIndexTextMap[i-2] == "{{" && runIndexTextMap[i-1] != "}}" && strings.Contains(runText, "}}") {
			// 检查中间的文本是否在 replacements 中，如果在，则替换为对应值
			key := runIndexTextMap[i-1]
			if replacement, exists := replacements["{{"+key+"}}"]; exists {
				replaceText = replacement
				// 提取 run 中 "}}" 之后的值
				startIndex := strings.Index(runText, "}}")
				if startIndex != -1 {
					endStr := runText[startIndex+2:]
					runs[i].SelectElement("w:t").SetText(endStr)
				}
				runs[i-2].SelectElement("w:t").SetText("")
				runs[i-1].SelectElement("w:t").SetText(replaceText)
			}
		}
		// 检查是否有连续的四个 runs 满足条件
		// 以下场景
		// abc {
		// {
		// name
		// }}
		if i >= 3 && strings.Contains(runIndexTextMap[i-3], "{") && runIndexTextMap[i-2] == "{" && runText == "}}" {
			key := runIndexTextMap[i-1]
			// 如果第三个值在 replacements 中，则处理替换
			if replacement, exists := replacements["{{"+key+"}}"]; exists {
				replaceText = replacement
				// 去除第一个值中的 "{"
				newText := strings.Replace(runIndexTextMap[i-3], "{", "", 1)
				runs[i-3].SelectElement("w:t").SetText("")
				runs[i-3].SelectElement("w:t").SetText(newText)
				// 去除第二个值中的 "{"
				runs[i-2].SelectElement("w:t").SetText("")
				// 替换第三个值为替换值
				runs[i-1].SelectElement("w:t").SetText("")
				runs[i-1].SelectElement("w:t").SetText(replaceText)
				// 去除第四个值中的 "{"
				runs[i].SelectElement("w:t").SetText("")
			}
		}
	}

}

func CheckMD5InDoc(docPath string, keyword string) (bool, error) {
	doc, err := document.Open(docPath)
	if err != nil {
		log.Printf("无法打开文档：%s", err)
		logging.ErrorLogger.Errorf("无法打开文档：%s", err)
		return false, fmt.Errorf("无法打开文档：%s", err)
	}

	// 遍历文档中的段落
	if len(doc.Paragraphs()) > 0 {
		for _, p := range doc.Paragraphs() {
			runs := p.Runs()
			runPtrs := make([]*document.Run, len(runs))
			for i := range runs {
				runPtrs[i] = &runs[i]
			}
			if searchMD5InRuns(runPtrs, keyword) {
				return true, nil
			}
		}
		//表格
		for _, table := range doc.Tables() {
			for _, row := range table.Rows() {
				for _, cell := range row.Cells() {
					for _, para := range cell.Paragraphs() {
						runs := para.Runs()
						runPtrs := make([]*document.Run, len(runs))
						for i := range runs {
							runPtrs[i] = &runs[i]
						}
						if searchMD5InRuns(runPtrs, keyword) {
							return true, nil
						}
					}
				}
			}
		}
	} else {
		//Word文档(*.docx)
		logging.ErrorLogger.Errorf("文档格式保存不正确，请使用Word文档(*.docx)")
		return false, fmt.Errorf("文档格式保存不正确，请使用Word文档(*.docx)")
	}

	// 如果没有找到任何一个关键字，返回 false
	return false, nil
}

// 查找文本中是否包含MD5大小写
func searchMD5InRuns(runs []*document.Run, keyword string) bool {
	var sb strings.Builder
	runIndexTextMap := make(map[int]string)
	// 合并所有Run的文本并记录每个Run的起始索引
	for i, run := range runs {
		runText := run.Text()
		runIndexTextMap[i] = runText
		sb.WriteString(runText)
	}
	// 查找合并合并后的文本中的占位符
	//统一转换为大写
	combinedText := strings.ToUpper(sb.String())
	return strings.Contains(combinedText, keyword)
}

func CheckNumberInDoc(docPath string, keyNumber int64) (bool, error) {
	doc, err := document.Open(docPath)
	if err != nil {
		logging.ErrorLogger.Errorf("无法打开文档：%s", err)
		return false, fmt.Errorf("无法打开文档：%s", err)
	}
	keyword := strconv.FormatInt(keyNumber, 10)
	// 遍历文档中的段落
	for _, p := range doc.Paragraphs() {
		runs := p.Runs()
		runPtrs := make([]*document.Run, len(runs))
		for i := range runs {
			runPtrs[i] = &runs[i]
		}
		if searchNumberInRuns(runPtrs, keyword) {
			return true, nil
		}
	}
	//表格
	for _, table := range doc.Tables() {
		for _, row := range table.Rows() {
			for _, cell := range row.Cells() {
				for _, para := range cell.Paragraphs() {
					runs := para.Runs()
					runPtrs := make([]*document.Run, len(runs))
					for i := range runs {
						runPtrs[i] = &runs[i]
					}
					if searchNumberInRuns(runPtrs, keyword) {
						return true, nil
					}
				}
			}
		}
	}
	// 如果没有找到任何一个关键字，返回 false
	return false, nil
}

// 查找文本中形如 "1,234,567" 的格式
func searchNumberInRuns(runs []*document.Run, keyword string) bool {
	var sb strings.Builder
	runIndexTextMap := make(map[int]string)
	// 合并所有Run的文本并记录每个Run的起始索引
	for i, run := range runs {
		runText := run.Text()
		runIndexTextMap[i] = runText
		sb.WriteString(runText)
	}
	// 查找合并合并后的文本中的占位符
	combinedText := sb.String()
	if strings.Contains(combinedText, keyword) {
		// 如果找到一个关键字，直接返回 true
		return true
	} else {
		//查找匹配特殊数字格式
		if matchFormattedStr(keyword, combinedText) {
			return true
		}
	}
	return false
}

func matchFormattedStr(str1, str2 string) bool {
	// 使用正则表达式匹配形如 "1,234,567" 的格式
	re := regexp.MustCompile(`\d{1,3}(,\d{3})*`)

	// 查找匹配的格式化数字
	matches := re.FindAllString(str2, -1)
	for _, match := range matches {
		// 去除数字中的逗号
		sanitizedMatch := strings.Replace(match, ",", "", -1)
		// 如果去除逗号后的数字与 str1 完全相同，则返回 true
		if sanitizedMatch == str1 {
			return true
		}
	}
	// 如果没有找到匹配的数字，返回 false
	return false
}

func RemoveSpecificSegmentFromURL(rawURL string, segmentToRemove string) (string, error) {
	parsedURL, err := url.Parse(rawURL)
	if err != nil {
		return "", err
	}
	segments := strings.Split(parsedURL.Path, "/")
	if len(segments) > 0 {
		segments = segments[:len(segments)-1]
	}
	for i, seg := range segments {
		if seg == segmentToRemove {
			// Remove the segmentToRemove.
			segments = append(segments[:i], segments[i+1:]...)
			break
		}
	}
	parsedURL.Path = path.Join(segments...)
	return parsedURL.String(), nil
}

// 提取包含"ospkg"的链接
func ExtractOspkgLinks(content string) []string {
	re := regexp.MustCompile(`href="([^"]*ospkg-install[^"]*)"`)
	matches := re.FindAllStringSubmatch(content, -1)

	var links []string
	for _, match := range matches {
		if len(match) > 1 {
			// 将链接添加到切片中
			links = append(links, match[1])
		}
	}
	return links
}

// 定义消息结构体
type Message struct {
	MsgType string `json:"msg_type"`
	Content struct {
		Text string `json:"text"`
	} `json:"content"`
}

// 发送消息到飞书
func SendToLark(webhookURL string, message Message) error {
	// 序列化消息为 JSON
	payloadBytes, err := json.Marshal(message)
	if err != nil {
		return err
	}
	body := bytes.NewReader(payloadBytes)

	// 发送 POST 请求
	req, err := http.NewRequest("POST", webhookURL, body)
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	return nil
}

// 提取产品后缀
func extractProductModelSuffix(model string) string {
	var re *regexp.Regexp
	var suffix string
	if strings.HasPrefix(model, "RG-WALL") {
		re = regexp.MustCompile(`Z\d{4}(-[A-Z0-9]+)*$`)
		suffix = re.FindString(model)
	} else if strings.HasPrefix(model, "RG-NBR") {
		re = regexp.MustCompile(`N\d{4}-[A-Z]$`)
		suffix = re.FindString(model)
	}

	return suffix
}
func findCommonSubstring(str1, str2 string) string {
	// 确保 str1 是较短的字符串
	if len(str1) > len(str2) {
		str1, str2 = str2, str1
	}

	// 遍历 str1 的所有子串
	for i := len(str1); i > 0; i-- { // i 是子串的长度
		for j := 0; j <= len(str1)-i; j++ { // j 是子串的起始位置
			substr := str1[j : j+i] // 提取子串
			if strings.Contains(str2, substr) {
				return substr
			}
		}
	}
	return ""
}

// 生成新 URL
func generateNewURL(baseURL, link string) string {
	// 找到 baseURL 和 link 的公共部分
	commonSubstring := findCommonSubstring(baseURL, link)

	// 如果没有公共部分，返回错误
	if commonSubstring == "" {
		return baseURL + "/" + link
	}
	// 去掉重复部分
	uniquePart := strings.TrimPrefix(link, commonSubstring)

	// 拼接新 URL
	return fmt.Sprintf("%s%s", baseURL, uniquePart)
}

// 替换文本方法
func replaceTextInDocByPython(templatePath string, replacements map[string]string, outputPath string) error {

	replacementsJSON, err := json.Marshal(replacements)
	if err != nil {
		fmt.Println("Error marshalling replacements:", err)
		return err
	}
	shell := filepath.Join(libs.Config.ProductionFileStorage.Plugin, "modify_docx.py")
	command := fmt.Sprintf("python3 %s '%s' '%s' '%s'", shell, templatePath, outputPath, replacementsJSON)
	_, err = libs.ExecCommand(command)
	if err != nil {
		fmt.Println(err)
		return fmt.Errorf("替换Word文档时出错：%s", err)
	}
	return nil
}

// 提取软件版本号
func extractSoftwareNum(version string) string {
	re := regexp.MustCompile(`([A-Z_]+[0-9]+\.[0-9A-Za-z]+)`)
	matches := re.FindStringSubmatch(version)
	if len(matches) >= 2 {
		return matches[1]
	}
	return ""
}

// 获取模板路径
func getTemplatePath(procInst *dproductionprocinst.Response, docType string, templateID *int, templatePath *string) error {
	document := dproductiondocumenttemplate.Response{}
	path, id, err := document.GetTemplatePathByProductModel(procInst.Production.ProductModel, docType, procInst.ProductionID)
	if err != nil {
		return err
	}
	if id == 0 && procInst.Production.ProductionType != "1" {
		path, id, err = document.GetTemplatePathByProductModel(procInst.Production.ProductModel, docType, procInst.Production.ProductionBaseVersion)
		if err != nil {
			return err
		}
	}
	if id == 0 {
		return fmt.Errorf("未找到匹配模板")
	}
	*templateID = int(id)
	*templatePath = path
	return nil
}

// 删除旧文档
func deleteOldDocuments(procInstID uint) error {
	lastDocuments := []*dproductionproctaskdocument.Response{}
	if err := easygorm.GetEasyGormDb().Model(dproductionproctaskdocument.Model()).Where("proc_inst_id =?", procInstID).Find(&lastDocuments).Error; err != nil {
		return fmt.Errorf("文档生成失败:%s", err)
	}
	if len(lastDocuments) > 0 {
		// 删除数据库记录
		if err := easygorm.GetEasyGormDb().Delete(dproductionproctaskdocument.Model(), "proc_inst_id =? ", procInstID).Error; err != nil {
			return fmt.Errorf("文档数据库记录删除失败:%s", err)
		}
		logging.InfoLogger.Infof("成功删除流程实例 %d 的 %d 个文档记录", procInstID, len(lastDocuments))
	}
	return nil
}
