package urlpack

import (
	"irisAdminApi/application/libs/response"
	"irisAdminApi/service/dao/urlpack/durlpackconfig"

	"github.com/kataras/iris/v12"
)

func GetConfigs(ctx iris.Context) {

	config, err := durlpackconfig.GetAllMap()
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, config, response.NoErr.Msg))
	return
}
