package fileout

import (
	"archive/zip"
	"bytes"
	"compress/gzip"
	"context"
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math/rand"
	"net/url"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"irisAdminApi/application/controllers/ip"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/fileout"
	"irisAdminApi/application/models/user"
	"irisAdminApi/service/cache"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/fileout/dapproval"
	"irisAdminApi/service/dao/fileout/dapprovalshare"
	"irisAdminApi/service/dao/fileout/dfilestruction"
	"irisAdminApi/service/dao/user/ddepartment"
	"irisAdminApi/service/dao/user/dgroup"
	"irisAdminApi/service/dao/user/dgroupdepartment"
	"irisAdminApi/service/dao/user/duser"
	"irisAdminApi/service/dao/user/dusergroup"
	"irisAdminApi/service/dao/user/dusertoken"
	"irisAdminApi/service/dao/user/duserwhitelist"
	"irisAdminApi/service/transaction/fileout/transapproval"

	"github.com/gabriel-vasile/mimetype"
	"github.com/glaslos/ssdeep"
	"github.com/kataras/iris/v12"
)

type Approval struct {
	Id  uint
	md5 string
}

func GetApproval(ctx iris.Context) {
	info := dapproval.Response{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

// GetUsers
func GetApprovals(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	var count int64
	var approvals []*dapproval.ListResponse

	db := easygorm.GetEasyGormDb().Model(dapproval.Model())

	db = db.Where("user_id = ?", id)
	if len(name) > 0 {
		db = db.Where("origin_name like ? or md5 like ?", fmt.Sprintf("%%%s%%", name), fmt.Sprintf("%%%s%%", name))
	}
	err = db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create filedetail get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create filedetail get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	dapproval.FormatResponse(approvals, false)
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func CheckDownload(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}

	info := dapproval.Response{}
	err = dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get approval get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	shares := []*dapprovalshare.ListResponse{}
	err = easygorm.GetEasyGormDb().Model(&fileout.ApprovalShare{}).Where("user_id = ? AND approval_id = ?", uId, info.Id).Find(&shares).Error
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	createDate := info.CreatedAt.Format("20060102")
	// url := fmt.Sprintf("http://%s:%s%s/api/v1/download", libs.Config.Nginx.HOST, strconv.FormatInt(libs.Config.Nginx.Port, 10), libs.Config.Nginx.Path)
	data := map[string]string{
		"url":         "/api/v1/download",
		"name":        info.Name,
		"origin_name": info.OriginName,
		"create_date": createDate,
	}

	// 允许组长及审计员下载文件
	if info.AuditorId == uId || info.Permit && duser.CheckAuditor(uId) {
		ctx.JSON(response.NewResponse(response.NoErr.Code, data, response.NoErr.Msg))
		return
	}

	if info.Permit == false {
		logging.ErrorLogger.Debugf("not allowed to download file")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "此文件不允许下载"))
		return
	}

	if info.UserId != uId && len(shares) == 0 {
		logging.ErrorLogger.Debugf("approval user not match")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "非当前登陆用户申请的文件或者抄送给当前用户的文件，禁止下载"))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, data, response.NoErr.Msg))
	return
}

func Download(ctx iris.Context) {
	name := ctx.FormValue("name")

	approval := dapproval.Response{}
	err := easygorm.GetEasyGormDb().Model(&fileout.Approval{}).Where("name = ?", name).Find(&approval).Error
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if approval.Id == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "文件不存在"))
		return
	}
	createdDate := approval.CreatedAt.Format("20060102")
	originName := approval.OriginName
	fp := filepath.Join(libs.Config.FileStorage.Upload, createdDate, "/", name)
	if approval.Permit == true {
		ctx.SendFile(fp, url.QueryEscape(originName))
		return
	}

	ips := []*Ip{}

	addr := GetRemoteAddr(ctx)
	if addr == "" {
		ctx.StatusCode(400)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	addr = strings.Join(strings.Split(addr, ".")[:3], ".")
	err = easygorm.GetEasyGormDb().Model(&fileout.AllowAuditIp{}).Where("ip like ?", addr+"%").Find(&ips).Error
	if err != nil {
		ctx.StatusCode(400)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if len(ips) == 0 {
		ctx.StatusCode(403)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "不在允许的IP范围，请切换至云办公环境"))
		return
	}

	// 允许组长及审计员下载文件
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.StopWithError(401, errors.New("登陆异常"))
		return
	}
	if approval.AuditorId == uId || (approval.Permit || duser.CheckAuditor(uId)) {
		ctx.SendFile(fp, url.QueryEscape(originName))
		return
	}

	// fp := filepath.Join(libs.Config.FileStorage.Upload, createDate, "/", name)
	ctx.StopWithError(400, errors.New("不允许下载此文件"))
	return
}

func DownloadByNameAndIndex(ctx iris.Context) {
	index, err := dao.GetId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.ParamentErr.Msg))
		return
	}
	filename := ctx.FormValue("filename")
	private_token := ctx.FormValue("private_token")
	if private_token == "" || index == 0 {
		ctx.JSON(response.NewResponse(response.ParamentErr.Code, nil, response.ParamentErr.Msg))
		return
	}

	token := dusertoken.Response{}
	err = token.FindEx("token", private_token)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, map[string]interface{}{"err_msg": err.Error()}, response.SystemErr.Msg))
		return
	}
	if token.Id == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "private_token invalid"))
		return
	}

	approvals := []*dapproval.ListResponse{}
	if filename == "" {
		err := easygorm.GetEasyGormDb().Model(&fileout.Approval{}).Where("user_id = ? and permit = 1", token.UserID).Order("id desc").Limit(int(index)).Find(&approvals).Error
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	} else {
		err := easygorm.GetEasyGormDb().Model(&fileout.Approval{}).Where("origin_name = ? and user_id = ? and permit = 1", filename, token.UserID).Order("id desc").Limit(int(index)).Find(&approvals).Error
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
	}

	if len(approvals) == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未找到文件"))
		return
	}

	approval := approvals[len(approvals)-1]

	fp := filepath.Join(libs.Config.FileStorage.Upload, approval.CreatedAt.Format("20060102"), "/", approval.Name)

	err = ctx.SendFile(fp, url.QueryEscape(approval.OriginName))
	if err != nil {
		ctx.JSON(response.NewResponse(response.FileNotExistsErr.Code, nil, response.FileNotExistsErr.Msg))
		return
	}
	return
}

func CheckApproval(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	md5 := ctx.FormValue("md5")
	if md5 == "" {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未提供查询的md5"))
		return
	}
	approval := Approval{}
	err = easygorm.GetEasyGormDb().Model(fileout.Approval{}).Where("md5 = ?", md5).Where("user_id = ?", id).Find(&approval).Error
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	data := map[string]bool{
		"isExists": true,
	}
	if approval.Id == 0 {
		data = map[string]bool{
			"isExists": false,
		}
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, data, response.NoErr.Msg))
	return
}

type Ip struct {
	ID uint   `json:"id"`
	Ip string `json:"ip"`
}

func GetAllApprovals(ctx iris.Context) {
	// var ip = Ip{}
	// addr := GetRemoteAddr(ctx)
	// if addr == "" {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }

	// addr = strings.Join(strings.Split(addr, ".")[:3], ".")
	// err := easygorm.GetEasyGormDb().Model(&fileout.AllowAuditIp{}).Where("ip like ?", addr+"%").Find(&ip).Error
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }

	// if ip.ID == 0 {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "不在允许审批的IP范围，请切换至云办公环境"))
	// 	return
	// }
	permit, err := ip.CheckIP(ctx)
	if !permit {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	var count int64
	var approvals []*dapproval.ListResponse

	db := easygorm.GetEasyGormDb().Model(&fileout.Approval{})

	db = db.Where("auditor_id = ? AND `force` < 2 AND `status` != 0 AND `status` != 9", id).Where("audit is null or audit = ?", false)
	if len(name) > 0 {
		db = db.Where("origin_name like ? or md5 like ?", fmt.Sprintf("%%%s%%", name), fmt.Sprintf("%%%s%%", name))
	}
	err = db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create filedetail get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create filedetail get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	dapproval.FormatResponse(approvals, true)
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetAllAuditedApprovals(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	var count int64
	var approvals []*dapproval.ListResponse

	db := easygorm.GetEasyGormDb().Model(&fileout.Approval{})
	db = db.Where("auditor_id = ?", id).Where("audit = ?", true)
	if len(name) > 0 {
		db = db.Where("origin_name like ? or md5 like ?", fmt.Sprintf("%%%s%%", name), fmt.Sprintf("%%%s%%", name))
	}
	err = db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create filedetail get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create filedetail get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	dapproval.FormatResponse(approvals, true)
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func DeleteApproval(ctx iris.Context) {
	id, _ := dao.GetAuthId(ctx)

	info := dapproval.Response{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get approval get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if info.Id == 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未找到申请单，请刷新后重试"))
		return
	}
	if info.UserId != id {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "禁止删除其他人的申请单"))
		return
	}
	if info.Force != 2 && info.Audit {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "禁止删除此申请单"))
		return
	}

	err = transapproval.DeleteApprovalTransaction(info.Id)
	if err != nil {
		logging.ErrorLogger.Errorf("get approval get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func unzip(pathname, fpath string) error {
	err := os.MkdirAll(pathname, 0o750)
	os.Chmod(pathname, 0o750)
	if err != nil {
		return err
	}
	r, err := zip.OpenReader(fpath)
	if err != nil {
		return errors.New("系统存在问题，请联系开发！")
	}
	defer r.Close()
	for _, k := range r.Reader.File {
		if k.FileInfo().IsDir() {
			err := os.MkdirAll(pathname+k.Name, 0o750)
			os.Chmod(pathname, 0o750)
			if err != nil {
				return errors.New("系统存在问题，请联系开发！")
			}
			continue
		}

		r, err := k.Open()
		if err != nil {
			return errors.New("请勿使用加密压缩包或压缩包已损坏，请检查")
		}
		defer r.Close()
		NewFile, err := os.Create(pathname + k.Name)
		if err != nil {
			return err
		}
		io.Copy(NewFile, r)
		NewFile.Close()
	}
	return nil
}

func toLinux(basePath string) string {
	return strings.ReplaceAll(basePath, "\\", "/")
}

func walk(fp string) []interface{} {
	linux_path := toLinux(fp)
	var files []interface{}
	var NotAllowExtMap map[string]bool = map[string]bool{
		".rar":  true,
		".7z":   true,
		".tar":  true,
		".tgz":  true,
		".zip":  true,
		".gz":   true,
		".bz":   true,
		".c":    true,
		".h":    true,
		".js":   true,
		".html": true,
		".php":  true,
		".vue":  true,
		".go":   true,
		".py":   true,
		".iso":  true,
	}
	filepath.Walk(linux_path, func(path string, info os.FileInfo, err error) error {
		// fmt.Println(toLinux(path))
		if info.IsDir() {
			return nil
		}
		file := map[string]interface{}{
			"name":    info.Name(),
			"pass":    true,
			"content": "",
			"type":    false,
		}
		mime, err := mimetype.DetectFile(toLinux(path))
		if err == nil {
			// 检查是否包含其他压缩格式文件
			if _, ok := NotAllowExtMap[mime.Extension()]; ok {
				file["type"] = mime.Extension()
				file["pass"] = false
				file["type"] = true
			}
		}
		if _, ok := NotAllowExtMap["."+strings.Split(path, ".")[len(strings.Split(path, "."))-1]]; ok {
			file["ext"] = "." + strings.Split(path, ".")[len(strings.Split(path, "."))-1]
			file["pass"] = false
		}
		out, err := objdump(toLinux(path))
		if err != nil {
			file["objdump"] = strings.Replace(out, toLinux(fp), "./", -1)
			file["pass"] = false
		}

		if !file["pass"].(bool) {
			if !file["type"].(bool) {
				file["content"] = read(toLinux(path))
			}
			files = append(files, file)
		}
		return nil
	})
	return files
}

func walkv2(fp string) []string {
	// var files []dfilestruction.Detail
	var files []string
	dir, fn := filepath.Split(fp)
	ext := path.Ext(fn)
	tempdir := filepath.Join(dir + strings.Replace(fn, ext, "", -1))
	filepath.Walk(tempdir, func(path string, info os.FileInfo, err error) error {
		if info.IsDir() {
			return nil
		}
		files = append(files, path)
		return nil
	})
	return files
}

func objdump(fp string) (string, error) {
	f, err := exec.LookPath("objdump")
	if err != nil {
		return "没有找到objdump命令", errors.New("没有找到objdump命令")
	}
	cmd := exec.Command(f, "-f", fp)
	out, _ := cmd.CombinedOutput()
	return string(out), nil
}

func fileCheck(fp string) (string, error) {
	f, err := exec.LookPath("file")
	if err != nil {
		return "没有找到file命令", errors.New("没有找到file命令")
	}
	cmd := exec.Command(f, fp)
	out, _ := cmd.CombinedOutput()
	return string(out), nil
}

func fileCheckMime(fp string) (string, error) {
	f, err := exec.LookPath("file")
	if err != nil {
		return "没有找到file命令", errors.New("没有找到file命令")
	}
	cmd := exec.Command(f, "-i", fp)
	out, _ := cmd.CombinedOutput()
	return string(out), nil
}

func unzipAll(fp string) (string, error) {
	dir, fn := filepath.Split(fp)
	ext := path.Ext(fn)
	if len(ext) == 0 {
		ext = ".tmp"
	}
	tempdir := filepath.Join(dir + strings.Replace(fn, ext, "", -1))
	err := os.MkdirAll(tempdir, 0o750)
	os.Chmod(tempdir, 0o750)
	if err != nil {
		return "", errors.New("解压失败，需要人工检验")
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(30)*time.Second)
	defer cancel()
	check := checkType(fp)

	var stderr bytes.Buffer
	switch check {
	case "zip":
		f, err := exec.LookPath("unzip")
		if err != nil {
			return "没有找到unzip命令", errors.New("没有找到unzip命令")
		}
		cmd := exec.CommandContext(ctx, f, fp)
		cmd.Dir = tempdir
		cmd.Stderr = &stderr

		err = cmd.Run()
		if err != nil {
			return "", errors.New("解压失败，需要人工检验")
		}
		os.Remove(fp)
	case "gzip":
		err := gunzip(fp, filepath.Join(tempdir, "temp.tar"))
		if err != nil {
			return "", errors.New("解压失败，需要人工检验")
		}
		os.Remove(fp)
	case "tar":
		f, err := exec.LookPath("tar")
		if err != nil {
			return "没有找到tar命令", errors.New("没有找到tar命令")
		}
		cmd := exec.CommandContext(ctx, f, "-xf", fp)
		cmd.Dir = tempdir
		cmd.Stderr = &stderr
		err = cmd.Run()
		if err != nil {
			return "", errors.New("解压失败，需要人工检验")
		}
		os.Remove(fp)
	case "ar":
		f, err := exec.LookPath("ar")
		if err != nil {
			return "没有找到ar命令", errors.New("没有找到ar命令")
		}
		cmd := exec.CommandContext(ctx, f, "-x", fp)
		cmd.Dir = tempdir
		cmd.Stderr = &stderr
		err = cmd.Run()
		if err != nil {
			return "", errors.New("解压失败，需要人工检验")
		}
		os.Remove(fp)
	case "rpm":
		f, err := exec.LookPath("rpm2cpio")
		if err != nil {
			return "没有找到rpm2cpio命令", errors.New("没有找到rpm命令")
		}
		c, err := exec.LookPath("cpio")
		if err != nil {
			return "没有找到cpio命令", errors.New("没有找到rpm命令")
		}
		command := fmt.Sprintf("'%s' '%s' | '%s' -div", f, fp, c)
		cmd := exec.CommandContext(ctx, "bash", "-c", command)
		cmd.Dir = tempdir
		cmd.Stderr = &stderr
		cmd.Stdout = &stderr
		err = cmd.Run()
		if err != nil {
			return "", errors.New("解压失败，需要人工检验")
		}

		// fmt.Println(stderr.String())
		os.Remove(fp)
	case "deb":
		f, err := exec.LookPath("dpkg")
		if err != nil {
			return "", errors.New("没有找到dpkg命令")
		}

		cmd := exec.CommandContext(ctx, f, "-X", fp, tempdir)
		cmd.Dir = tempdir
		cmd.Stderr = &stderr
		cmd.Stdout = &stderr
		err = cmd.Run()
		if err != nil {
			return "", errors.New("解压失败，需要人工检验")
		}

		os.Remove(fp)
	case "bin":
		f, err := exec.LookPath("binwalk")
		if err != nil {
			return "", errors.New("没有找到binwalk命令")
		}

		cmd := exec.CommandContext(ctx, f, "-eM", fp, tempdir)
		cmd.Dir = tempdir
		cmd.Stderr = &stderr
		cmd.Stdout = &stderr
		err = cmd.Run()
		if err != nil {
			return "", errors.New("解压失败，需要人工检验")
		}

		os.Remove(fp)
		return tempdir, nil
	default:
		return tempdir, nil
	}

	filepath.Walk(tempdir, func(path string, info os.FileInfo, err error) error {
		if info.IsDir() {
			return nil
		}
		_, err = unzipAll(path)
		if err != nil {
			logging.DebugLogger.Errorf("解压失败", err)
			return err
		}
		return nil
	})
	return tempdir, nil
}

func checkType(fp string) string {
	fileInfo, err1 := fileCheck(fp)
	fileMime, err2 := fileCheckMime(fp)
	if err1 != nil && err2 != nil {
		logging.ErrorLogger.Errorf("检查类型失败", err1, err2)
	}
	// 检测是否压缩格式，进行循环解压
	switch {
	case strings.Contains(fileInfo, "gzip compressed data") && strings.Contains(fileMime, "application/gzip"):
		return "gzip"
	case strings.Contains(fileInfo, "gzip compressed data") && strings.Contains(fileMime, "application/x-gzip"):
		return "gzip"
	case strings.Contains(fileInfo, "Zip archive data") && strings.Contains(fileMime, "application/zip"):
		return "zip"
	case strings.Contains(fileInfo, "tar archive") && strings.Contains(fileMime, "application/x-tar"):
		return "tar"
	case strings.Contains(fileInfo, "current ar archive") && strings.Contains(fileMime, "application/x-archive"):
		return "ar"
	case strings.Contains(fileInfo, "RPM") && strings.Contains(fileMime, "application/x-rpm"):
		return "rpm"
	case strings.Contains(fileInfo, "Debian binary package") && strings.Contains(fileMime, "application/vnd.debian.binary-package"):
		return "deb"
		// case strings.Contains(fileInfo, "data") && strings.Contains(fileMime, "application/octet-stream"):
		// 	return "bin"
	}

	return ""
}

func CheckRuijieFile(fp string) (bool, error) {
	file, _ := os.Open(fp)
	defer file.Close()

	// 缓冲区
	buf := make([]byte, 8)
	// 存放文件所有内容
	var bytes []byte

	// 每次读取一行放入缓冲区
	count, err := file.Read(buf)
	bytes = append(bytes, buf[:count]...)

	if libs.BytesToHexString(bytes) == "435259505450591f" {
		return true, nil
	}
	return false, err
}

// fp, ext, fileInfo, fileMime, md5, size, hash, objdump, pass
func checkPass(rc *cache.RedisCluster, userId uint, fp string) (string, string, string, string, string, int64, string, bool, bool, error) {
	ext := path.Ext(fp)
	fileStat, err := os.Lstat(fp)
	if err != nil {
		return fp, ext, "", "", "", 0, "", false, false, errors.New(fmt.Sprintf("文件Stat检查失败, %s", err.Error()))
	}
	size := fileStat.Size()
	md5, md5Err := libs.GetFileMd5(fp)

	fileInfo, err := fileCheck(fp)
	if err != nil {
		return fp, ext, "", "", md5, size, "", false, false, errors.New(fmt.Sprintf("文件Info检查失败, %s", err.Error()))
	}

	info := strings.Split(fileInfo, ":")[1]
	fileMime, err := fileCheckMime(fp)
	if err != nil {
		return fp, ext, info, "", md5, size, "", false, false, errors.New(fmt.Sprintf("文件Mime检查失败, %s", err.Error()))
	}
	mime := strings.Split(fileMime, ":")[1]

	if strings.HasPrefix(fileStat.Mode().String(), "L") || size < 100 {
		return fp, ext, info, mime, md5, size, "", true, true, nil
	}

	if md5Err != nil {
		return fp, ext, info, mime, md5, size, "", false, false, errors.New(fmt.Sprintf("文件Md5计算失败, %s", md5Err.Error()))
	}

	hash, _ := ssdeep.FuzzyFilename(fp)
	// if err != nil {
	// 	return fp, ext, info, mime, "", size, "", false, false, errors.New(fmt.Sprintf("文件Hash计算失败, %s", err.Error()))
	// }
	if value, err := rc.GetKey(fmt.Sprintf("FC:%s", md5)); err == nil && value != nil {
		return fp, ext, info, mime, md5, size, hash, false, true, errors.New("历史已审批文件")
	}

	if libs.InArrayS([]string{".c", ".h", ".cpp"}, ext) || (strings.Contains(libs.CompressStr(fileInfo), "text") && strings.Contains(libs.CompressStr(fileInfo), libs.CompressStr("c source")) && strings.Contains(libs.CompressStr(fileMime), libs.CompressStr("text/x-c"))) {
		return fp, ext, info, mime, "", 0, hash, false, false, errors.New(fmt.Sprintf("文件类型禁止外出"))
	}

	// if libs.InArrayS([]string{".py"}, ext) || (strings.Contains(libs.CompressStr(fileInfo), "text") && strings.Contains(libs.CompressStr(fileInfo), libs.CompressStr("Python script")) && strings.Contains(libs.CompressStr(fileMime), libs.CompressStr("text/x-python"))) {
	// 	return fp, ext, info, mime, "", 0, hash, false, true, errors.New(fmt.Sprintf("python文件"))
	// }
	_objdump, _pass, _error := false, false, errors.New("非自动审批文件类型，请提交审批")
	header, _ := libs.GetFileHeader(fp)
	for _, autoAuditFile := range cache.AutoAuditFiles {
		if autoAuditFile.Users == "" || libs.InArrayS(strings.Split(autoAuditFile.Users, ","), strconv.FormatUint(uint64(userId), 10)) {
			if ((autoAuditFile.Ext == "" || ext == autoAuditFile.Ext) && (autoAuditFile.CustomHeader == "" || strings.HasPrefix(header, autoAuditFile.CustomHeader))) &&
				(!strings.Contains(libs.CompressStr(fileInfo), "text") && strings.Contains(libs.CompressStr(fileInfo), libs.CompressStr(autoAuditFile.FileCheck)) && strings.Contains(libs.CompressStr(fileMime), libs.CompressStr(autoAuditFile.FileMime))) {
				if autoAuditFile.Objdump {
					out, err := objdump(fp)
					if err != nil {
						_error = errors.New(fmt.Sprintf("文件objdump检查错误, %s", err.Error()))
					}
					if strings.Contains(libs.CompressStr(string(out)), libs.CompressStr("File format not recognized")) {
						_error = errors.New("objdump检查无法识别文件, %s")
					} else {
						_objdump, _pass, _error = true, true, nil
						break
					}
				}

				if autoAuditFile.Size > 0 {
					if size > int64(autoAuditFile.Size) {
						_error = errors.New("文件大小超过限制")
					} else {
						_pass, _error = true, nil
						break
					}
				} else {
					_pass, _error = true, nil
					break
				}
			}
		}
	}
	return fp, ext, info, mime, md5, size, hash, _objdump, _pass, _error
}

func read(fp string) string {
	file, err := os.Open(toLinux(fp))
	if err != nil {
		return "文件读取错误"
	}
	defer file.Close()
	const BufferSize = 120
	buffer := make([]byte, BufferSize)

	bytesread, err := file.Read(buffer)
	return string(buffer[:bytesread])
}

func gunzip(Src string, Dst string) error {
	file, err := os.Open(Src)
	if err != nil {
		logging.ErrorLogger.Errorf("gunzip err ", err)
	}
	defer file.Close()

	newfile, err := os.Create(Dst)
	if err != nil {
		logging.ErrorLogger.Errorf("gunzip err ", err)
	}
	defer newfile.Close()

	zr, err := gzip.NewReader(file)
	if err != nil {
		logging.ErrorLogger.Errorf("gunzip err ", err)
	}

	filestat, err := file.Stat()
	if err != nil {
		logging.ErrorLogger.Errorf("gunzip err ", err)
	}

	zr.Name = filestat.Name()
	zr.ModTime = filestat.ModTime()
	_, err = io.Copy(newfile, zr)
	if err != nil {
		logging.ErrorLogger.Errorf("gunzip err ", err)
	}

	if err := zr.Close(); err != nil {
		logging.ErrorLogger.Errorf("gunzip err ", err)
	}
	return nil
}

func GetShareUsers(ctx iris.Context) {
	departments := []*ddepartment.ListResponse{}
	err := easygorm.GetEasyGormDb().Model(&user.Department{}).Find(&departments).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get share users err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	option1 := []map[string]interface{}{}
	for _, department := range departments {
		groupDepartments := []*dgroupdepartment.ListResponse{}
		err := easygorm.GetEasyGormDb().Model(&user.GroupDepartment{}).Where("department_id = ?", department.ID).Find(&groupDepartments).Error
		if err != nil {
			logging.ErrorLogger.Errorf("get share users err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		var groupIds []uint
		for _, groupDepartment := range groupDepartments {
			groupIds = append(groupIds, groupDepartment.GroupId)
		}
		groups, err := dgroup.FindInId(groupIds)
		if err != nil {
			logging.ErrorLogger.Errorf("get share users err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}
		option2 := []interface{}{}
		for _, group := range groups {
			userGroups := []*dusergroup.ListResponse{}
			err := easygorm.GetEasyGormDb().Model(&user.UserGroup{}).Where("group_id = ?", group.Id).Find(&userGroups).Error
			if err != nil {
				logging.ErrorLogger.Errorf("get share users err ", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
				return
			}
			var userIds []string
			for _, userGroup := range userGroups {
				userIds = append(userIds, strconv.FormatUint(uint64(userGroup.UserId), 10))
			}
			users, err := duser.FindInId(userIds)
			option3 := []interface{}{}
			for _, user := range users {
				option3 = append(option3, map[string]interface{}{
					"value": user.ID,
					"label": user.Name,
				})
			}
			option2 = append(option2, map[string]interface{}{
				"value":    group.Id,
				"label":    group.Name,
				"children": option3,
			})
		}
		option1 = append(option1, map[string]interface{}{
			"value":    department.ID,
			"label":    department.Name,
			"children": option2,
		})
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, option1, response.NoErr.Msg))
	return
}

func GetShareApprovals(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	shares := []*dapprovalshare.ListResponse{}
	err = easygorm.GetEasyGormDb().Model(&fileout.ApprovalShare{}).Where("user_id = ?", id).Find(&shares).Error
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	var approvalIds []uint
	for _, share := range shares {
		approvalIds = append(approvalIds, share.ApprovalId)
	}

	var count int64
	var approvals []*dapproval.ListResponse

	db := easygorm.GetEasyGormDb().Model(&fileout.Approval{})

	db = db.Where("id in ?", approvalIds).Where("audit = true AND permit = true")
	if len(name) > 0 {
		db = db.Where("origin_name like ? or md5 like ?", fmt.Sprintf("%%%s%%", name), fmt.Sprintf("%%%s%%", name))
	}
	err = db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create filedetail get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create filedetail get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	dapproval.FormatResponse(approvals, true)
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func SystemCheckFailed(approval dapproval.Response, details []*dfilestruction.Detail) {
	UpdateFileStrunction(approval, details)
	err := transapproval.UpdateApprovalTransaction(approval.Id, map[string]interface{}{
		"Permit": nil,
		// "AuditorId": master.Id,
		"Audit":  nil,
		"Force":  1,
		"Status": 2,
	}, []map[string]interface{}{
		{
			"Comment":   "系统检查不通过， 请人工审批",
			"Action":    "创建",
			"UserId":    0,
			"CreatedAt": time.Now(),
			"UpdatedAt": time.Now(),
		},
	})
	PushMailQueue(approval.AuditorId, approval.OriginName, "待审批", "存在不允许自动审批文件，转人工审批")
	if err != nil {
		logging.ErrorLogger.Errorf("create approval get err ", err)
		return
	}
}

func SystemCheckPass(approval dapproval.Response, details []*dfilestruction.Detail) {
	// 增加白名单，默认禁止自动审批
	// whitelist := dautoauditwhitelist.AutoAuditWhiteList{}
	whitelist := duserwhitelist.UserWhiteList{}
	err := whitelist.FindEx("user_id", fmt.Sprintf("%v", approval.UserId))
	if err != nil {
		logging.ErrorLogger.Errorf("query white list err ", err)
		SystemCheckFailed(approval, nil)
		return
	}

	if !whitelist.Fileout {
		SystemCheckFailed(approval, nil)
		return
	}

	UpdateFileStrunction(approval, details)
	err = transapproval.UpdateApprovalTransaction(approval.Id, map[string]interface{}{
		"Permit": true,
		"Audit":  true,
		"Force":  0,
		"Status": 1,
	}, []map[string]interface{}{
		{
			"Comment":   "自动审批文件，通过",
			"Action":    "创建",
			"UserId":    0,
			"CreatedAt": time.Now(),
			"UpdatedAt": time.Now(),
		},
	})
	// PushMailQueue(approval.AuditorId, approval.OriginName, "待审批", "存在不允许自动审批文件，转人工审批")
	if err != nil {
		logging.ErrorLogger.Errorf("create approval get err ", err)
		return
	}
}

func UpdateFileStrunction(approval dapproval.Response, details []*dfilestruction.Detail) {
	if details != nil {
		fileStruction, err := dfilestruction.FindDetailByApprovalId(approval.Id)
		if err != nil {
			logging.ErrorLogger.Errorf("find file struction", err.Error())
		}
		resultJosn, err := json.Marshal(details)
		if err != nil {
			logging.ErrorLogger.Errorf("marshal detail error", err.Error())
		}
		err = fileStruction.Update(fileStruction.Id, map[string]interface{}{"Detail": string(resultJosn)})
		if err != nil {
			logging.ErrorLogger.Errorf("update detail error", err.Error())
		}
	}
}

func ApprovalToMaster(approval dapproval.Response) {
	master := duser.User{}
	err := master.FindEx("name", "陈朝晖")
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		SystemCheckFailed(approval, nil)
	}
	err = transapproval.UpdateApprovalTransaction(approval.Id, map[string]interface{}{
		"Permit":    nil,
		"AuditorId": master.ID,
		"Audit":     nil,
		"Force":     1,
		"Status":    1,
	}, []map[string]interface{}{
		{
			"Comment":   "存在C代码文件，转朝晖审批。",
			"Action":    "创建",
			"UserId":    0,
			"CreatedAt": time.Now(),
			"UpdatedAt": time.Now(),
		},
	})
	if err != nil {
		logging.ErrorLogger.Errorf("create approval get err ", err)
		return
	}
	PushMailQueue(master.ID, approval.OriginName, "待审批", "存在C代码文件，转朝晖审批。")
}

func UpdateApprovalV2(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}

	info := dapproval.Response{}
	err = dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get approval get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	approvalReq := &dapproval.ApprovalReq{}
	if err := ctx.ReadJSON(approvalReq); err != nil {
		logging.ErrorLogger.Errorf("create approval read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*approvalReq)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	data := map[string]interface{}{}
	if approvalReq.Force == 0 {
		if info.AuditorId != uId {
			logging.ErrorLogger.Debugf("approval user not match")
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "非当前登陆用户审批的申请单"))
			return
		}
		data["audit"] = approvalReq.Audit
		data["permit"] = approvalReq.Permit
	}

	if approvalReq.Force == 1 {
		if info.UserId != uId {
			logging.ErrorLogger.Debugf("approval user not match")
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "非当前登陆用户申请的申请单"))
			return
		}
		data["force"] = 1
	}

	master := duser.User{}
	err = master.FindEx("name", "陈朝晖")
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	master2 := duser.User{}
	err = master2.FindEx("name", "刘良清")
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	var action string
	data["auditor_id"] = uId
	if approvalReq.Permit {
		if approvalReq.IsCcode {
			action = "确认有C代码文件，转刘良清审批"
			data["audit"] = nil
			data["permit"] = nil
			data["auditor_id"] = master2.ID
		} else {
			if uId == master2.ID {
				action = "确认有C代码文件，转朝晖审批"
				data["audit"] = nil
				data["permit"] = nil
				data["auditor_id"] = master.ID
			} else if uId != master.ID {
				action = "确认无C代码文件，审批通过"
			} else {
				action = "审批通过"
			}
		}
	} else {
		action = "审批拒绝"
	}

	err = transapproval.UpdateApprovalTransaction(info.Id, data, []map[string]interface{}{
		{
			"Comment":   approvalReq.Comment,
			"Action":    action,
			"UserId":    uId,
			"CreatedAt": time.Now(),
			"UpdatedAt": time.Now(),
		},
	})
	if err != nil {
		logging.ErrorLogger.Errorf("create approval get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	PushMailQueue(info.UserId, info.OriginName, action, approvalReq.Comment)
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func CheckUpdateApprovalV2(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}

	master := duser.User{}
	err = master.FindEx("name", "陈朝晖")
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	master2 := duser.User{}
	err = master2.FindEx("name", "刘良清")
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if uId != master.ID && uId != master2.ID {
		ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]bool{"check_c_code": true}, response.NoErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]bool{"check_c_code": false}, response.NoErr.Msg))
	return
}

func PushMailQueue(userID uint, filename string, status, comment string) {
	if libs.Config.Mail.Enable {
		rc := cache.GetRedisClusterClient()
		from := "文件外出系统"
		to := duser.UserMap[userID].Username + "@ruijie.com.cn"
		subject := fmt.Sprintf("【文件外出系统】【文件名:%s】【%s】", filename, status)
		body := fmt.Sprintf(`%s<br>%s`, subject, comment)
		msg := strings.Join([]string{from, to, subject, body}, "|")
		_, err := rc.LPush(libs.Config.Mail.Queue, msg)
		if err != nil {
			logging.ErrorLogger.Error(err)
		}
	}
}

func CreateApprovalV4(ctx iris.Context) {
	id, _ := dao.GetAuthId(ctx)
	var commentObjects []map[string]interface{}
	var approvalObject map[string]interface{}
	var shareObjects []map[string]interface{}

	approvalReq := &dapproval.ApprovalReq{}
	if err := ctx.ReadForm(approvalReq); err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	// 先执行文件上传，文件上传成功后执行创建申请单操作
	f, fh, err := ctx.FormFile("file")
	defer f.Close()

	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	upload := libs.Config.FileStorage.Upload + time.Now().Format("20060102") + "/"
	err = os.MkdirAll(upload, 0o750)
	os.Chmod(upload, 0o750)

	ext := path.Ext(fh.Filename)
	// 构造文件名称
	rand.Seed(time.Now().UnixNano())
	randNum := fmt.Sprintf("%d", rand.Intn(9999)+1000)
	hashName := md5.Sum([]byte(time.Now().Format("2006_01_02_15_04_05_") + randNum))
	tempName := libs.GetUUID()
	fileName := tempName + ext

	_, err = ctx.SaveFormFile(fh, filepath.Join(upload, fileName))
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	// 获取文件md5为文件名，用于防止重复上传相同文件
	fileMd5, err := libs.GetFileMd5(filepath.Join(upload, fileName))
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	// 检查是否已经上传过，如果已经上传，直接创建映射
	approval := dapproval.Response{}
	err = approval.FindByMd5(fileMd5)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	// 创建最终存放路径以及保存文件

	for _, share := range approvalReq.Shares {
		shareObjects = append(shareObjects, map[string]interface{}{
			"UserId": share,
		})
	}

	if approval.Id > 0 {
		createDate := approval.CreatedAt.Format("20060102")
		fp := filepath.Join(libs.Config.FileStorage.Upload, createDate, "/", approval.Name)
		dest := filepath.Join(upload, fileName)
		err := os.Remove(dest)
		if err != nil {
			defer os.Remove(filepath.Join(upload, fileName))
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while remove tmp file: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}

		err = os.Link(fp, dest)
		if err != nil {
			defer os.Remove(filepath.Join(upload, fileName))
			logging.ErrorLogger.Errorf(fmt.Sprintf("Error while create link: %s", err.Error()))
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}

		approvalObject = map[string]interface{}{
			"UserId":     id,
			"OriginName": fh.Filename,
			"Name":       fileName,
			"Md5":        fileMd5,
			"AuditorId":  approval.AuditorId,
			"CreatedAt":  time.Now(),
			"UpdatedAt":  time.Now(),
			"Force":      approval.Force,
			"Permit":     approval.Permit,
			"Audit":      approval.Audit,
			"Status":     approval.Status,
		}
		approvalReq.Force = approval.Force
		audit := "未审批"
		if approval.Audit == false {
			audit = "未审批"
		}
		if approval.Audit == true {
			if approval.Permit == false {
				audit = "审批不通过"
			} else {
				audit = "审批通过"
			}
		}
		commentObjects = append(commentObjects, map[string]interface{}{
			"Comment":   approvalReq.Comment + fmt.Sprintf("\n已存在申请单，申请人:%s, 申请时间%s, 审批人：%s, 同步审批状态: %s, 审批时间：%s", approval.User.Name, approval.CreatedAt.Format("2006-01-02 15:04:05"), approval.Auditor.Name, audit, approval.UpdatedAt.Format("2006-01-02 15:04:05")),
			"Action":    "创建",
			"UserId":    id,
			"CreatedAt": time.Now(),
			"UpdatedAt": time.Now(),
		})
		err = transapproval.CreateApprovalTransaction(approvalObject, commentObjects, shareObjects)
		if err != nil {
			defer os.Remove(filepath.Join(upload, fileName))
			logging.ErrorLogger.Errorf("create approval get err ", err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
	} else {
		if user, ok := duser.UserMap[id]; ok && (libs.InArrayS(user.Roles, "组长") || libs.InArrayS(user.Roles, "默认审核员")) {
			// approvalObject = map[string]interface{}{
			// 	"UserId":     id,
			// 	"OriginName": fh.Filename,
			// 	"Name":       fileName,
			// 	"Md5":        fileMd5,
			// 	"CreatedAt":  time.Now(),
			// 	"UpdatedAt":  time.Now(),
			// 	"AuditorId":  approvalReq.AuditorId,
			// 	"Permit":     true,
			// 	"Audit":      true,
			// 	"Force":      0,
			// 	"Status":     1,
			// }
			// commentObjects = append(commentObjects, map[string]interface{}{
			// 	"Comment":   approvalReq.Comment,
			// 	"Action":    "创建",
			// 	"UserId":    id,
			// 	"CreatedAt": time.Now(),
			// 	"UpdatedAt": time.Now(),
			// })
			// commentObjects = append(commentObjects, map[string]interface{}{
			// 	"Comment":   "专业组长文件，自动审批通过",
			// 	"Action":    "自动审批文件，通过",
			// 	"UserId":    0,
			// 	"CreatedAt": time.Now(),
			// 	"UpdatedAt": time.Now(),
			// })
			// err = transapproval.CreateApprovalTransaction(approvalObject, commentObjects, shareObjects)
			// if err != nil {
			// 	defer os.Remove(filepath.Join(upload, fileName))
			// 	logging.ErrorLogger.Errorf("create approval get err ", err)
			// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			// 	return
			// }
			approvalObject = map[string]interface{}{
				"UserId":     id,
				"OriginName": fh.Filename,
				"Name":       fileName,
				"Md5":        fileMd5,
				"CreatedAt":  time.Now(),
				"UpdatedAt":  time.Now(),
				"Permit":     nil,
				"AuditorId":  approvalReq.AuditorId,
				"Audit":      nil,
				"Force":      0,
				"Status":     0,
			}
			commentObjects = append(commentObjects, map[string]interface{}{
				"Comment":   approvalReq.Comment,
				"Action":    "创建",
				"UserId":    id,
				"CreatedAt": time.Now(),
				"UpdatedAt": time.Now(),
			})
			err = transapproval.CreateApprovalTransaction(approvalObject, commentObjects, shareObjects)
			if err != nil {
				defer os.Remove(filepath.Join(upload, fileName))
				logging.ErrorLogger.Errorf("create approval get err ", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
				return
			}
			go SystemCheckV4(fmt.Sprintf("%x", hashName), fileName, filepath.Join(upload, fileName))
		} else {
			approvalObject = map[string]interface{}{
				"UserId":     id,
				"OriginName": fh.Filename,
				"Name":       fileName,
				"Md5":        fileMd5,
				"CreatedAt":  time.Now(),
				"UpdatedAt":  time.Now(),
				"Permit":     nil,
				"AuditorId":  approvalReq.AuditorId,
				"Audit":      nil,
				"Force":      0,
				"Status":     0,
			}
			commentObjects = append(commentObjects, map[string]interface{}{
				"Comment":   approvalReq.Comment,
				"Action":    "创建",
				"UserId":    id,
				"CreatedAt": time.Now(),
				"UpdatedAt": time.Now(),
			})
			err = transapproval.CreateApprovalTransaction(approvalObject, commentObjects, shareObjects)
			if err != nil {
				defer os.Remove(filepath.Join(upload, fileName))
				logging.ErrorLogger.Errorf("create approval get err ", err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
				return
			}
			go SystemCheckV4(fmt.Sprintf("%x", hashName), fileName, filepath.Join(upload, fileName))
		}
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, approvalReq, response.NoErr.Msg))
	return
}

func AiCheck(hashName string, approval dapproval.Response, notPassDetails []*dfilestruction.Detail, tempDir string, details []*dfilestruction.Detail) ([]*dfilestruction.Detail, error) {
	notPassResults := []*dfilestruction.Detail{}
	_files := []string{}
	for _, detail := range notPassDetails {
		_files = append(_files, filepath.Join(tempDir, detail.File))
	}
	tmpFile := "/tmp/" + hashName + ".check"
	f, err := os.OpenFile(tmpFile, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0o666)
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		SystemCheckFailed(approval, nil)
		return notPassResults, err
	}
	f.Write([]byte(strings.Join(_files, "\n")))
	f.Close()
	// defer os.Remove(tmpFile)
	outArray := []string{}
	if libs.Config.CheckCCode.Script != "" {
		// cmd := exec.Command(libs.Config.CheckCCode.Type, libs.Config.CheckCCode.Script, libs.Config.CheckCCode.Repository, libs.Config.CheckCCode.Threshold, strings.Join(_files, " "))
		// fmt.Println(libs.Config.CheckCCode.Type, libs.Config.CheckCCode.Script, strings.Join(_files, " "))
		cmd := exec.Command(libs.Config.CheckCCode.Type, libs.Config.CheckCCode.Script, tmpFile)

		out, err := cmd.CombinedOutput()
		outArray = strings.Split(string(out), "\n")
		logging.DebugLogger.Debugf("ai check result: %s", out)
		if err != nil {
			SystemCheckFailed(approval, nil)
			return notPassResults, err
		}
	}
	// ai 检测试文件类型
	outMap := map[string]string{}
	for _, a := range outArray {
		if strings.Contains(a, ":") {
			_a := strings.Split(a, ":")
			outMap[strings.Replace(_a[0], tempDir, "", -1)] = _a[1]
		}
	}
	for _, detail := range notPassDetails {
		if val, ok := outMap[detail.File]; ok {
			if libs.InArrayS([]string{"1", "2", "-1"}, val) {
				notPassResults = append(notPassResults, detail)
			}
		}
	}
	for _, detail := range details {
		if val, ok := outMap[detail.File]; ok {
			if libs.InArrayS([]string{"1", "2", "-1"}, val) {
				detail.AiCheck = 2
				detail.Err = "AI检测不通过"
			} else {
				detail.AiCheck = 1
				detail.Err = "AI检测通过"
			}
		}
	}

	if len(notPassResults) == 0 {
		SystemCheckPass(approval, details)
	} else {
		SystemCheckFailed(approval, details)
	}
	return notPassResults, nil
}

func SystemCheckV4(hashName string, fileName string, fp string) {
	rc := cache.GetRedisClusterClient()
	approval := dapproval.Response{}
	err := approval.FindEx("name", fileName)
	if err != nil {
		logging.ErrorLogger.Errorf("system check get approval failed : %s", err.Error())
		return
	}
	if approval.Id == 0 {
		logging.ErrorLogger.Errorf("system check not find approval")
		SystemCheckFailed(approval, nil)
		return
	}

	tempDir := filepath.Join(libs.Config.FileStorage.Temp, time.Now().Format("20060102"), hashName)
	tempFp := filepath.Join(tempDir, approval.OriginName)
	os.RemoveAll(tempDir)
	err = os.MkdirAll(tempDir, 0o750)
	if err != nil {
		logging.ErrorLogger.Errorf(fmt.Sprintf("Error while uploading: %s", err.Error()))
		return
	}
	os.Chmod(tempDir, 0o750)

	err = os.Link(fp, tempFp)
	if err != nil {
		logging.ErrorLogger.Errorf("os.link err", err)
		SystemCheckFailed(approval, nil)
		return
	}
	var details []*dfilestruction.Detail
	var notPassDetails, notPassResults []*dfilestruction.Detail

	// 解压压缩包，循环检查文件类型
	unzipAll(tempFp)

	files := walkv2(tempDir)
	for _, file := range files {
		logging.DebugLogger.Debug("start check ", file)
		fp, ext, fileInfo, fileMime, md5, size, hash, objdump, pass, err := checkPass(rc, approval.UserId, file)
		errMsg := ""
		if err != nil {
			errMsg = err.Error()
		}
		logging.DebugLogger.Debug("check resutl ", fp, fileInfo, fileMime, md5, size, hash, objdump, pass, errMsg)
		_detail := dfilestruction.Detail{
			File:    strings.Replace(fp, tempDir, "", -1),
			Ext:     ext,
			Info:    fileInfo,
			Mime:    fileMime,
			Md5:     md5,
			Size:    size,
			Hash:    hash,
			Objdump: objdump,
			Pass:    pass,
			Err:     errMsg,
		}
		details = append(details, &_detail)
		if !pass {
			notPassDetails = append(notPassDetails, &_detail)
		}
	}
	// 创建文件详情记录
	resultJosn, err := json.Marshal(details)
	if err != nil {
		logging.ErrorLogger.Errorf("marshal detail error", details)
		return
	}

	var hisFileConstruction dfilestruction.Response

	err = hisFileConstruction.Create(map[string]interface{}{
		"ApprovalID": approval.Id,
		"UserID":     approval.UserId,
		"Name":       approval.OriginName,
		"Detail":     string(resultJosn),
		"Status":     0,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("failed to create hisFileConstruction")
	}

	if len(notPassDetails) == 0 {
		SystemCheckPass(approval, nil)
		return
	}
	// 检查是否存在历史文件结构，用于对比

	hisFileConstructions, err := dfilestruction.FindFileConstructions(approval.UserId, approval.OriginName)
	if err != nil || len(hisFileConstructions) == 0 {
		logging.ErrorLogger.Errorf("system check not find hisFileConstruction")
		// 调用AI检测
		// AiCheck
		AiCheck(hashName, approval, notPassDetails, tempDir, details)
		return
	} else {
		hisDetailMap := map[string][]dfilestruction.Detail{}
		for _, hisFc := range hisFileConstructions {
			hisDetails := []dfilestruction.Detail{}
			err = json.Unmarshal([]byte(hisFc.Detail), &hisDetails)
			if err != nil {
				logging.ErrorLogger.Errorf("system check not find hisFileConstruction")
				// 调用AI检测
				// AiCheck
				AiCheck(hashName, approval, notPassDetails, tempDir, details)
				return

			}

			for _, detail := range hisDetails {
				if _, ok := hisDetailMap[detail.File]; ok {
					hisDetailMap[detail.File] = append(hisDetailMap[detail.File], detail)
				} else {
					hisDetailMap[detail.File] = []dfilestruction.Detail{detail}
				}
			}

		}

		for _, detail := range notPassDetails {
			fileName := detail.File
			if _, ok := hisDetailMap[fileName]; ok {
				detail.Similarity = 0
				for _, hisDetail := range hisDetailMap[fileName] {
					if detail.Info == hisDetail.Info && detail.Mime == hisDetail.Mime {
						score, err := ssdeep.Distance(hisDetail.Hash, detail.Hash)
						if err == nil {
							if detail.Similarity < int64(score) {
								detail.Similarity = int64(score)
							}
						}
					}
				}
				if detail.Similarity < 40 {
					notPassResults = append(notPassResults, detail)
				} else {
					detail.Err = "相似文件自动审批通过"
				}

			} else {
				notPassResults = append(notPassResults, detail)
			}
		}
		if len(notPassResults) > 0 {
			// AI检测
			// AiCheck
			AiCheck(hashName, approval, notPassResults, tempDir, details)
			return
		} else {
			SystemCheckPass(approval, details)
		}
	}
}

func ChangeAuditor(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}

	info := dapproval.Response{}
	err = dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get approval get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	approvalReq := &dapproval.ApprovalReq{}
	if err := ctx.ReadJSON(approvalReq); err != nil {
		logging.ErrorLogger.Errorf("create approval read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*approvalReq)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	if approvalReq.AuditorId == uId {
		logging.ErrorLogger.Errorf("禁止修改为自己审批", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "禁止修改为自己审批"))
		return
	}

	if info.Status == 0 {
		logging.ErrorLogger.Debugf("approval user not match")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "系统检查中，禁止修改"))
		return
	}

	if info.UserId != uId {
		logging.ErrorLogger.Debugf("approval user not match")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "非当前登陆用户申请的申请单"))
		return
	}

	if info.Audit {
		logging.ErrorLogger.Debugf("approval user not match")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "已审批申请单，禁止修改审批人"))
		return
	}

	data := map[string]interface{}{
		"AuditorId": approvalReq.AuditorId,
	}
	comment := fmt.Sprintf("%s -> %s", duser.UserMap[info.AuditorId].Name, duser.UserMap[approvalReq.AuditorId].Name)
	commentObject := map[string]interface{}{
		"ApprovalId": info.Id,
		"Comment":    comment,
		"Action":     "修改审批人",
		"UserId":     uId,
		"CreatedAt":  time.Now(),
		"UpdatedAt":  time.Now(),
	}

	err = transapproval.UpdateAuditorTransaction(info.Id, data, commentObject)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	PushMailQueue(approvalReq.AuditorId, info.OriginName, "修改审批人", comment)

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}
