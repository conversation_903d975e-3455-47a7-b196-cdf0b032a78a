package buildfarm

import (
	// "irisAdminApi/application/libs/easygorm"
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"

	"github.com/kataras/iris/v12"

	// "irisAdminApi/service/dao/buildfarm/dbaseline"

	"irisAdminApi/application/libs"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/buildfarm/dcroncoverityschedule"
	"irisAdminApi/service/dao/release/dreleaseprojectconfig"

	"github.com/bndr/gojenkins"
)

var bgCtx = context.Background()

func InitJenkins() *gojenkins.Jenkins {
	Jenkins := gojenkins.CreateJenkins(libs.HttpClient, libs.Config.Buildfarm.Jenkinsurl, libs.Config.Buildfarm.Jenkinsuser, libs.Config.Buildfarm.Jenkinspass)
	_, err := Jenkins.Init(bgCtx)
	if err != nil {
		logging.ErrorLogger.Errorf("jenkins init err ", err)
		return nil
	}
	return Jenkins
}

func GetCovLastBuild(ctx iris.Context) {
	Jenkins := InitJenkins()
	buildJob, err := Jenkins.GetJob(bgCtx, "cov-build")
	if err != nil {
		logging.ErrorLogger.Errorf("jenkins get job err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	build, err := buildJob.GetLastBuild(bgCtx)
	if err != nil {
		logging.ErrorLogger.Errorf("jenkins get job builds ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	list := []map[string]interface{}{}
	params := []string{}
	for _, p := range build.GetParameters() {
		params = append(params, p.Name+":"+p.Value)
	}
	if build.IsRunning(bgCtx) {
		list = append(list, map[string]interface{}{
			"id":        build.GetBuildNumber(),
			"name":      "cov-build",
			"status":    "RUNNING",
			"duration":  time.Now().Sub(build.GetTimestamp()) / 1000_000_000,
			"timestamp": build.GetTimestamp(),
			"params":    strings.Join(params, ","),
		})
	} else {
		list = append(list, map[string]interface{}{
			"id":        build.GetBuildNumber(),
			"name":      "cov-build",
			"status":    build.GetResult(),
			"duration":  build.GetDuration() / 1000,
			"timestamp": build.GetTimestamp(),
			"params":    strings.Join(params, ","),
		})
	}
	result := map[string]interface{}{
		"items": list,
		"total": 1,
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func GetCovAllBuilds(ctx iris.Context) {
	Jenkins := InitJenkins()
	job, err := Jenkins.GetJob(bgCtx, "cov-build")
	if err != nil {
		logging.ErrorLogger.Errorf("jenkins get job err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	buildIds, err := job.GetAllBuildIds(bgCtx)
	if err != nil {
		logging.ErrorLogger.Errorf("jenkins get job builds ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	if page == 0 {
		page = 1
	}
	if pageSize == 0 {
		pageSize = 10
	}

	start := (page - 1) * pageSize
	end := page * pageSize

	if (page * pageSize) >= len(buildIds) {
		end = len(buildIds)
	}

	_buildIds := buildIds[start:end]
	list := []map[string]interface{}{}
	for _, buildId := range _buildIds {
		_build, err := job.GetBuild(bgCtx, buildId.Number)
		if err != nil {
			logging.ErrorLogger.Errorf("jenkins get job build ", buildId.Number, err)
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}

		params := []string{}
		for _, p := range _build.GetParameters() {
			params = append(params, p.Name+":"+p.Value)
		}

		if _build.IsRunning(bgCtx) {
			list = append(list, map[string]interface{}{
				"id":        _build.GetBuildNumber(),
				"name":      "cov-build",
				"status":    "RUNNING",
				"duration":  time.Now().Sub(_build.GetTimestamp()) / 1000_000_000,
				"timestamp": _build.GetTimestamp(),
				"params":    strings.Join(params, ","),
			})
		} else {
			list = append(list, map[string]interface{}{
				"id":        _build.GetBuildNumber(),
				"name":      "cov-build",
				"status":    _build.GetResult(),
				"duration":  _build.GetDuration() / 1000,
				"timestamp": _build.GetTimestamp(),
				"params":    strings.Join(params, ","),
			})
		}
	}
	result := map[string]interface{}{
		"items": list,
		"total": len(buildIds),
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

func GetCovBuildOutput(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	Jenkins := InitJenkins()
	job, err := Jenkins.GetJob(bgCtx, "cov-build")
	if err != nil {
		logging.ErrorLogger.Errorf("jenkins get job err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	_build, err := job.GetBuild(bgCtx, int64(id))
	if err != nil {
		logging.ErrorLogger.Errorf("jenkins get job build ", id, err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	// result := map[string]string{
	// 	"output": _build.GetConsoleOutput(bgCtx),
	// }
	// ctx.Write([]byte(_build.GetConsoleOutput(bgCtx)))
	ctx.JSON(response.NewResponse(response.NoErr.Code, _build.GetConsoleOutput(bgCtx), response.NoErr.Msg))
	return
}

type CovBuildParams struct {
	Project     string `json:"project"`
	Branch      string `json:"branch"`
	Product     string `json:"product"`
	BaseProject string `json:"base_project"`
	BaseProduct string `json:"base_product"`
}

func StartCovBuild(ctx iris.Context) {
	params := &CovBuildParams{}
	if err := ctx.ReadJSON(params); err != nil {
		logging.ErrorLogger.Errorf("create project read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*params)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		logging.ErrorLogger.Errorf("create project read json err ", strings.Join(errs, ";"))
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	releaseProjectConfig, err := dreleaseprojectconfig.FindConfigByName(params.Project)
	if err != nil {
		logging.ErrorLogger.Errorf("add cron err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	if releaseProjectConfig.ID == 0 {
		logging.ErrorLogger.Errorf("未找到%s项目配置", params.Project)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("未找到%s项目配置", params.Project)))
		return
	}

	branch := releaseProjectConfig.BuildProjectBranch
	if branch == nil || *branch == "" {
		logging.ErrorLogger.Errorf("%s项目未配置编译工程", params.Project)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("%s项目未配置编译工程", params.Project)))
		return
	}

	params.Branch = *branch
	params.BaseProject = releaseProjectConfig.ReleaseProject.BaseProject.Name

	queuieid, err := CovBuildRequest(params)
	if err != nil {
		logging.ErrorLogger.Errorf("start cov build err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, queuieid, response.NoErr.Msg))
	return
}

func CovBuildRequest(params *CovBuildParams) (int64, error) {
	var queueid int64
	Jenkins := InitJenkins()
	// queue, err := Jenkins.GetQueue(bgCtx)
	// if err != nil {
	// 	return queueid, err
	// }
	// tasks := queue.GetTasksForJob("cov-build")

	// if len(tasks) > 0 {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "已有作业在排队"))
	// 	return
	// }

	// job, err := Jenkins.GetJob(bgCtx, "cov-build")
	// if err != nil {
	// 	return queueid, err
	// }
	// running, err := job.IsRunning(bgCtx)
	// if err != nil {
	// 	return queueid, err
	// }
	// if running {
	// 	return queueid, err
	// }
	queueid, err := Jenkins.BuildJob(bgCtx, "cov-build", map[string]string{
		"project":     params.Project,
		"product":     params.Product,
		"branch":      params.Branch,
		"BaseProject": params.BaseProject,
		"BaseProduct": params.BaseProduct,
	}) // or  jenkins.BuildJob(ctx, "#jobname", params)
	if err != nil {
		return queueid, err
	}
	return queueid, nil
}

func StopCovBuild(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	Jenkins := InitJenkins()
	job, err := Jenkins.GetJob(bgCtx, "cov-build")
	if err != nil {
		logging.ErrorLogger.Errorf("jenkins get job err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	_build, err := job.GetBuild(bgCtx, int64(id))
	if err != nil {
		logging.ErrorLogger.Errorf("jenkins get job build ", id, err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if !_build.IsRunning(bgCtx) {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "该构建已经处理停止状态"))
		return
	}
	_, err = _build.Stop(bgCtx)
	if err != nil {
		logging.ErrorLogger.Errorf("jenkins stop job build ", id, err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetCronCoverityTabs(ctx iris.Context) {
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	name := ctx.FormValue("name")

	schedule := dcroncoverityschedule.CronCoveritySchedule{}

	list, err := schedule.All(name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetCronCoverityWindows(ctx iris.Context) {
	name := ctx.FormValue("name")
	list, err := dcroncoverityschedule.FindCronCoverityWindow(0, false)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	result := []*dcroncoverityschedule.CronCoverityWindow{}

	for _, item := range list {
		if item.CronCoveritySchedule == nil || item.CronCoveritySchedule.ID == 0 || item.CronCoveritySchedule.Project == name {
			result = append(result, item)
		}
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

type CronCoverityWindowSummary struct {
	Time      uint        `json:"time"`
	Monday    interface{} `json:"monday"`
	Tuesday   interface{} `json:"tuesday"`
	Wednesday interface{} `json:"wednesday"`
	Thursday  interface{} `json:"thursday"`
	Friday    interface{} `json:"friday"`
	Saturday  interface{} `json:"saturday"`
	Sunday    interface{} `json:"sunday"`
}

func GetCronCoverityWindowSummary(ctx iris.Context) {
	times := []uint{}
	summary := []*CronCoverityWindowSummary{}
	summaryMap := map[uint]*CronCoverityWindowSummary{}
	list, err := dcroncoverityschedule.FindCronCoverityWindow(0, false)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	for _, item := range list {
		if _, ok := summaryMap[item.Time]; !ok {
			times = append(times, item.Time)
			summaryMap[item.Time] = &CronCoverityWindowSummary{Time: item.Time}
		}

		switch item.WeekDay {
		case 0, 7:
			summaryMap[item.Time].Sunday = item
		case 1:
			summaryMap[item.Time].Monday = item
		case 2:
			summaryMap[item.Time].Tuesday = item
		case 3:
			summaryMap[item.Time].Wednesday = item
		case 4:
			summaryMap[item.Time].Thursday = item
		case 5:
			summaryMap[item.Time].Friday = item
		case 6:
			summaryMap[item.Time].Saturday = item
		}
	}

	for _, t := range times {
		summary = append(summary, summaryMap[t])
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, summary, response.NoErr.Msg))
	return
}

func CreateCoverityTab(ctx iris.Context) {
	userID, _ := dao.GetAuthId(ctx)
	request := dcroncoverityschedule.CronCoverityScheduleRequest{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(&request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	releaseProjectConfig, err := dreleaseprojectconfig.FindConfigByName(request.Project)
	if err != nil {
		logging.ErrorLogger.Errorf("find project config err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if releaseProjectConfig.ID == 0 {
		logging.ErrorLogger.Errorf("未找到%s项目配置", request.Project)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("未找到%s项目配置", request.Project)))
		return
	}

	branch := releaseProjectConfig.BuildProjectBranch
	if branch == nil || *branch == "" {
		logging.ErrorLogger.Errorf("%s项目未配置编译工程", request.Project)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("%s项目未配置编译工程", request.Project)))
		return
	}

	objects := []map[string]interface{}{}
	for _, windowID := range request.Schedules {
		objects = append(objects, map[string]interface{}{
			"CronCoverityWindowID": windowID,
			"Project":              request.Project,
			"Product":              request.Product,
			"BaseProduct":          request.BaseProduct,
			"CreatedAt":            time.Now(),
			"UserID":               userID,
		})
	}

	err = dcroncoverityschedule.BatchCreate(request.Project, objects)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	AddNewCronTab()
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func DeleteCoverityTab(ctx iris.Context) {
	request := dcroncoverityschedule.CronCoverityScheduleRequest{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(&request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	err := dcroncoverityschedule.BatchDelete(request.Project)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}
