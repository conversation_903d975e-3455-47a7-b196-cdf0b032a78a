package datasync

import "irisAdminApi/application/models"

type PmsDoc struct {
	models.ModelBase
	ProjectName     string `gorm:"uniqueIndex: idx_project_doc_itc_id; not null; type:varchar(100)" json:"project_name"`
	DocumentName    string `gorm:"uniqueIndex: idx_project_doc_itc_id; not null; type:varchar(100)" json:"document_name"`
	ItcID           string `gorm:"uniqueIndex: idx_project_doc_itc_id; not null; type:varchar(200)" json:"itc_id"`
	RFID            string `gorm:"uniqueIndex: idx_project_doc_itc_id; not null; type:varchar(200)" json:"rf_id"`
	ItcGatherStatus bool   `gorm:"not null; default:false" json:"itc_gather_status" update:"1"`
	ItcGatherError  string `gorm:"not null; default:''" json:"itc_gather_error" update:"1"`
}
