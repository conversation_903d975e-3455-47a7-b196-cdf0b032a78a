package middleware

import (
	"strings"

	"github.com/iris-contrib/middleware/jwt"
	"github.com/kataras/iris/v12"
)

/**
 * 验证 jwt
 * @method JwtHandler
 */
func JwtHandler() *jwt.Middleware {
	var mySecret = []byte("HS2JDFKhu7Y1av7b")
	return jwt.New(jwt.Config{
		ValidationKeyGetter: func(token *jwt.Token) (interface{}, error) {
			return mySecret, nil
		},
		SigningMethod:       jwt.SigningMethodHS256,
		Expiration:          true,
		CredentialsOptional: true,
		// ErrorHandler:  errorHandler,
		Extractor: FromAuthHeader,
	})
}

func FromAuthHeader(ctx iris.Context) (string, error) {
	authHeader := ctx.GetHeader("Authorization")
	// TODO: Make this a bit more robust, parsing-wise
	authHeaderParts := strings.Split(authHeader, " ")
	if len(authHeaderParts) != 2 || strings.ToLower(authHeaderParts[0]) != "bearer" {
		cookie := ctx.GetCookie("AccessToken")
		if len(cookie) > 0 {
			return cookie, nil
		}
		return "", nil
	}
	return authHeaderParts[1], nil
}
