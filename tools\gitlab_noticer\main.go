package main

import (
	"fmt"
	"net/http"
	"os"
	"strings"
)

var client = &http.Client{}
var Config = struct {
	Hosts  string `env:"Host" default:"http://*************:9090"`
	Queues string `env:"Queue" default:"gitlab"`
}{}

type Queue struct {
	Code    int      `json:"code"`
	Data    []string `json:"data"`
	Message string   `json:"message"`
}

// func PushQueue(host, key string, mail []string) (string, error) {
// 	url := fmt.Sprintf("%s/api/v1/queue", host)
// 	result := []interface{}{}
// 	items := strings.Split(msg, "|")
// 	result = append(result, map[string]interface{}{
// 		"key":     key,
// 		"from":    items[0],
// 		"to":      items[1],
// 		"subject": items[2],
// 		"body":    items[3],
// 	})

// 	jsonStr, _ := json.Marshal(result)
// 	reqest, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
// 	if err != nil {
// 		return string(jsonStr), err
// 	}
// 	//处理返回结果
// 	response, err := client.Do(reqest)
// 	if err != nil {
// 		return string(jsonStr), err
// 	}
// 	defer response.Body.Close()
// 	bodyByte, err := ioutil.ReadAll(response.Body)
// 	if err != nil {
// 		return string(jsonStr), err
// 	}
// 	response.Body.Close()

// 	ret := Queue{}
// 	err = json.Unmarshal(bodyByte, &ret)
// 	if err != nil {
// 		return string(jsonStr), err
// 	}
// 	if ret.Code == 20000 {
// 		return string(jsonStr), nil
// 	}
// 	return string(jsonStr), errors.New(ret.Message)

// }

// func Worker(host, queue string) {
// 	ret, err := PushQueue(host, queue, mail)
// 	if err != nil {
// 		logging.ErrorLogger.Errorf("推送重做队列失败", err, ret)
// 	}
// }

// var mail = `Date: Sat, 07 May 2022 16:50:45 +0800
// From: =?UTF-8?B?5p6X5a625qW3?= <gitlab@*************>
// Reply-To: GitLab <noreply@*************>
// To: <EMAIL>
// Message-ID: <note_13@*************>
// In-Reply-To: <merge_request_2@*************>
// References: <merge_request_2@*************>
// Subject: Re: iris-admin-api | Update rbac_model.conf (!2)
// Mime-Version: 1.0
// Content-Type: multipart/alternative;
//  boundary="--==_mimepart_627632e5d5747_205e930534279d2";
//  charset=UTF-8
// Content-Transfer-Encoding: 7bit
// X-GitLab-Project: iris-admin-api
// X-GitLab-Project-Id: 2
// *********************: aqyfzxsystem/iris-admin-api
// List-Id: aqyfzxsystem/iris-admin-api
//  <2.iris-admin-api.aqyfzxsystem.*************>
// List-Unsubscribe: <http://*************/-/sent_notifications/8b1b79441872c2aa82ad88b0b1cfd5ac/unsubscribe?force=true>
// X-GitLab-MergeRequest-ID: 2
// X-GitLab-MergeRequest-IID: 2
// X-GitLab-NotificationReason:
// X-GitLab-Reply-Key: 8b1b79441872c2aa82ad88b0b1cfd5ac
// Auto-Submitted: auto-generated
// X-Auto-Response-Suppress: All

// ----==_mimepart_627632e5d5747_205e930534279d2
// Content-Type: text/plain;
//  charset=UTF-8
// Content-Transfer-Encoding: quoted-printable

// =E6=9E=97=E5=AE=B6=E6=A5=B7 commented:

// 123

// --
// View it on GitLab: http://*************/aqyfzxsystem/iris-admin-api/-/merge_requests/2#note_13
// You're receiving this email because of your account on *************.

// ----==_mimepart_627632e5d5747_205e930534279d2
// Content-Type: text/html;
//  charset=UTF-8
// Content-Transfer-Encoding: quoted-printable

// <!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
// <html lang=3D"en">
// <head>
// <meta content=3D"text/html; charset=3DUS-ASCII" http-equiv=3D"Content-Type">
// <title>
// GitLab
// </title>

// <style>img {
// max-width: 100%; height: auto;
// }
// </style>
// </head>
// <body>
// <div class=3D"content">

// <p style=3D"color: #777777;">
// <a href=3D"http://*************/linjiakai">&#26519;&#23478;&#26999;</a>
// <a href=3D"http://*************/aqyfzxsystem/iris-admin-api/-/merge_requests/2#note_13">commented</a>:
// </p>
// <div style=3D"">
// <p dir=3D"auto">123</p>
// </div>

// </div>
// <div class=3D"footer" style=3D"margin-top: 10px;">
// <p style=3D"font-size: small; color: #666;">
// &#8212;
// <br>
// <a href=3D"http://*************/aqyfzxsystem/iris-admin-api/-/merge_requests/2#note_13">View it on GitLab</a>.
// <br>
// You're receiving this email because of your account on *************.
// If you'd like to receive fewer emails, you can
// <a href=3D"http://*************/-/sent_notifications/8b1b79441872c2aa82ad88b0b1cfd5ac/unsubscribe">unsubscribe</a>
// from this thread or
// adjust your notification settings.
// <script type=3D"application/ld+json">{"@context":"http://schema.org","@type":"EmailMessage","action":{"@type":"ViewAction","name":"View Merge request","url":"http://*************/aqyfzxsystem/iris-admin-api/-/merge_requests/2#note_13"}}</script>

// </p>
// </div>
// </body>
// </html>

// ----==_mimepart_627632e5d5747_205e930534279d2--`

func ParseMail(mail string) (string, string, string) {
	mailSlice := strings.Split(mail, "\n")
	to := mailSlice[3]
	sub := mailSlice[7]
	body := ParseBody(mailSlice)
	return to, sub, body
}

func ParseBody(mail []string) string {
	boundary := "--" + strings.Replace(strings.Replace(mail[10], ` boundary="`, "", -1), `";`, "", -1)
	start := 0
	end := 0
	for i := 0; i < 2; i++ {
		if i == 0 {
			for idx, item := range mail {
				if item == boundary {
					start = idx
					break
				}
			}
		}
		if i == 1 {
			fmt.Println(mail[start+1:])
			for idx, item := range mail[start+1:] {
				if item == boundary {
					end = idx + start + 1
					break
				}
			}
		}
	}
	body := strings.Join(mail[start+5:end], "\n")
	return body
}

func main() {
	mail := os.Args[1]
	to, sub, body := ParseMail(mail)
	fmt.Println(to, sub, body)
	// select {}
}
