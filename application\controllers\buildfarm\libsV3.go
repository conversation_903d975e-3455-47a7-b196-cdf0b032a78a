package buildfarm

import (
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/buildfarm/dserver"
	"sort"
	"strings"
)

func ChooseServerV3(_type string, force bool) uint {
	serverStatus, err := dserver.GetServerStatus()
	if err != nil {
		logging.ErrorLogger.Error(err)
		return 0
	}
	var summary []serverCount
	for _, status := range serverStatus {
		if status.Enable {
			if libs.InArrayS(strings.Split(status.Type, ","), _type) || status.Type == "" {
				summary = append(summary, serverCount{
					ServerId:     status.Id,
					Parallel:     int(status.Parallel),
					RunningCount: int(status.RunningCount),
				})
			}
		}
	}
	//优先返回剩余资源较多，服务器并行数量较大的服务器
	sort.Slice(summary, func(i, j int) bool {
		if (summary[i].Parallel - summary[i].RunningCount) > (summary[j].Parallel - summary[j].RunningCount) {
			return true
		}
		return (summary[i].Parallel-summary[i].RunningCount) == (summary[j].Parallel-summary[j].RunningCount) && summary[i].Parallel > summary[j].Parallel
	})

	if len(summary) > 0 {
		// 组件编译不排队
		if force {
			return summary[0].ServerId
		} else if summary[0].RunningCount < summary[0].Parallel {
			return summary[0].ServerId
		}
	}

	return 0
}

func GetTotalParallel(_type string) (uint, error) {
	total := uint(0)
	serverStatus, err := dserver.GetServerStatus()
	if err != nil {
		logging.ErrorLogger.Error(err)
		return total, err
	}

	for _, status := range serverStatus {
		if status.Enable {
			if strings.Contains(status.Type, _type) || status.Type == "" {
				total = total + status.Parallel
			}
		}
	}
	return total, nil
}
