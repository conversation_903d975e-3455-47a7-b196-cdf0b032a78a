package buildfarm

import "irisAdminApi/application/models"

type PatchJob struct {
	models.ModelBase
	JobId                string  `gorm:"index;not null; type:varchar(60)" json:"job_id"`                       //任务id
	PatchFileName        string  `gorm:"not null; type:varchar(200)" json:"patch_file_name"`                   //补丁文件名
	PatchFileOriginName  string  `gorm:"not null; type:varchar(200)" json:"patch_file_origin_name" `           //补丁原始文件名称
	PatchFileSize        uint    `gorm:"not null" json:"patch_file_size"`                                      //补丁文件大小
	PatchFileDesc        string  `gorm:"not null; type:varchar(200)" json:"patch_file_desc" `                  //补丁包描述
	PatchFileMd5         string  `gorm:"not null; type:varchar(200)"`                                          //补丁文件Md5值
	PatchType            uint    `gorm:"not null" json:"patch_type"`                                           //补丁类型 0：单组件补丁  1 补丁集合包 默认单组件
	PatchForm            uint    `gorm:"not null" json:"patch_form"`                                           //补丁形式：0组件补丁 1函数补丁
	PatchUpgradeType     uint    `gorm:"not null" json:"patch_upgrade_type"`                                   //补丁升级方式 0自动升级 1手动升级 2强制升级 默认手动
	SoftwareVersion      string  `gorm:"type:varchar(200)" json:"software_version"`                            //适配软件版本
	ServerId             uint    `gorm:"index;not null" json:"server_id"`                                      //编译服务器id
	Dir                  string  `gorm:"not null; type:varchar(200)" json:"dir"`                               //远程操作目录名称
	LocalDir             string  `gorm:"not null; type:varchar(200)" json:"local_dir"`                         //本地目录名称
	PatchComponentName   string  `gorm:"type:varchar(200)"  json:"patch_component_name"`                       //补丁包组件名
	PatchObjectPath      string  `gorm:"type:varchar(200)"  json:"patch_object_path" form:"patch_object_path"` //补丁对象路径
	Baseline             string  `gorm:"type:varchar(200)" json:"baseline"`                                    //基线版本
	Defconfig            string  `gorm:"type:varchar(200)" json:"defconfig"`                                   //工具配置文件
	AdapterModel         string  `gorm:"index;not null; type:varchar(200)" json:"adapter_model"`               //适配型号
	AdapterHardware      string  `gorm:"index;not null; type:varchar(200)" json:"adaptation_hardware"`         //适配硬件
	RPMFileName          string  `gorm:"type:varchar(200)" json:"rpm_file_name"`                               //RPM包文件名
	RPMFileSize          uint    `gorm:"default:0" json:"rmp_file_size"`                                       //RPM包文件大小
	RPMFileMd5           string  `gorm:"type:varchar(200)" json:"rmp_file_md5"`                                //RPM包文件Md5值
	UserID               uint    `gorm:"index;not null" json:"user_id"`                                        //上传用户id
	Status               uint    `gorm:"not null,default:0" json:"status"`                                     //作业状态 3：排队，0：运行， 1：成功， 2: 失败
	TaskId               string  `gorm:"not null; type:varchar(60)" json:"task_id"`                            //编译任务id 初始为
	MakeStatus           int     `gorm:"default:-1" json:"make_status"`                                        // 作业状态 3：排队，-1:未启动,0：运行， 1：成功， 2: 失败
	Version              string  `gorm:"not null; type:varchar(60)" json:"version"`                            // 版本hash值,初始为
	Type                 string  `gorm:"not null; type:varchar(60)" json:"type"`
	Post                 string  `gorm:"not null; type:varchar(300)" json:"post"`
	CronMakeJobID        uint    `gorm:"not null" json:"cron_make_job_id"`
	MakeFrom             string  `gorm:"not null; type:varchar(60)" json:"make_from"`
	Crypt                string  `gorm:"not null; type:varchar(60); default:old" json:"crypt" form:"crypt"`
	PatchVersion         int     `gorm:"default:1" json:"patch_version"`                                         // 补丁编译工具版本  1: 第一代 2: 第二代
	DependencyIDs        string  `gorm:"not null; type:varchar(200)" json:"dependency_ids"`                      //补丁依赖项
	SerialNumber         uint    `gorm:"not null" json:"serial_number"`                                          //新版补丁编号
	EnableAutoCollection bool    `gorm:"default:false" json:"enable_auto_collection"`                            //是否自动创建集合补丁
	CustomReleaseDate    *string `gorm:"type:varchar(10)" json:"custom_release_date" form:"custom_release_date"` //自定义发布日期，格式YYYY-MM-DD，为空时使用created_at
}

type SoftVersions struct {
	models.ModelBase
	SoftVersion string `gorm:"uniqueIndex:soft_version_type_idx; not null; type:varchar(128)"  json:"soft_version"`
	Type        string `gorm:"uniqueIndex:soft_version_type_idx; not null; type:varchar(60)" json:"type"`
}

type PatchDefconfig struct {
	models.ModelBase
	ProductModel string `gorm:"index:index_product_model,unique,not null;type:varchar(60)"  json:"product_model"` //产品型号
	Defconfig    string `gorm:"type:varchar(200)" json:"defconfig"`                                               //配置信息
}
