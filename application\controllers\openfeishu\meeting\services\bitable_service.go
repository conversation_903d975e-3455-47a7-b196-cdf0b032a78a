package services

import (
	"context"
	"fmt"

	"irisAdminApi/application/controllers/openfeishu/meeting/libs"
	"irisAdminApi/application/controllers/openfeishu/meeting/models"
	"irisAdminApi/application/logging"

	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkbitable "github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
)

// BitableService 多维表格服务
type BitableService struct {
	client      *lark.Client
	config      *models.ServiceConfig
	fieldMapper *libs.FieldMapper
}

// NewBitableService 创建多维表格服务实例
func NewBitableService(config *models.ServiceConfig) *BitableService {
	// 复用现有飞书客户端初始化
	client := lark.NewClient(config.AppID, config.AppSecret)

	return &BitableService{
		client:      client,
		config:      config,
		fieldMapper: libs.NewFieldMapper(),
	}
}

// handleAPIError 统一API错误处理方法
func (b *BitableService) handleAPIError(operation string, err error) error {
	logging.ErrorLogger.Errorf("%s失败: %v", operation, err)
	return fmt.Errorf("%s失败: %w", operation, err)
}

// handleAPIResponse 统一API响应处理方法
func (b *BitableService) handleAPIResponse(operation string, resp interface{}) error {
	// 这里可以根据实际的响应类型进行类型断言和处理
	// 为了简化，我们先保持现有的处理方式
	return nil
}

// ReadMeetingRecords 读取会议记录
func (b *BitableService) ReadMeetingRecords(ctx context.Context) ([]*models.StandardMeetingData, error) {
	logging.InfoLogger.Info("开始读取多维表格会议数据（仅读取未处理的记录）")

	// 构建查询请求（简化版本，移除不兼容的API）
	// 强制使用会议系统专用的多维表格应用令牌
	if b.config.MeetingAppToken == "" {
		return nil, fmt.Errorf("MeetingAppToken 配置不能为空，会议系统必须使用独立的应用令牌")
	}

	req := larkbitable.NewSearchAppTableRecordReqBuilder().
		AppToken(b.config.MeetingAppToken).
		TableId(b.config.MeetingTableID).
		Body(larkbitable.NewSearchAppTableRecordReqBodyBuilder().
			// 指定返回字段（与field_mapper.go中的映射配置保持一致）
			FieldNames([]string{
				"工作计划",    // 修复：原"日程标题" → "工作计划"
				"会议开始时间",  // 修复：原"开始时间" → "会议开始时间"
				"会议结束时间",  // 修复：原"结束时间" → "会议结束时间"
				"地点/方式",   // 修复：原"地点" → "地点/方式"
				"参会人员",    // 修复：原"参与人员" → "参会人员"
				"主要内容&总结", // 修复：原"日程描述" → "主要内容&总结"
				"运行状态",
				"日程ID",
				"日程链接",
				"错误信息",
				"操作类型",
				"操作状态",
			}).
			Build()).
		Build()

	// 调用API
	resp, err := b.client.Bitable.V1.AppTableRecord.Search(ctx, req)
	if err != nil {
		return nil, b.handleAPIError("查询多维表格记录", err)
	}

	if !resp.Success() {
		logging.ErrorLogger.Errorf("多维表格API调用失败: code=%d, msg=%s", resp.Code, resp.Msg)
		return nil, fmt.Errorf("多维表格API调用失败: %s", resp.Msg)
	}

	// 处理响应数据
	return b.processReadResponse(resp.Data)
}

// processReadResponse 处理读取响应
func (b *BitableService) processReadResponse(data *larkbitable.SearchAppTableRecordRespData) ([]*models.StandardMeetingData, error) {
	if data == nil || data.Items == nil {
		logging.InfoLogger.Info("未找到待处理的会议记录")
		return []*models.StandardMeetingData{}, nil
	}

	var results []*models.StandardMeetingData

	for _, item := range data.Items {
		if item.RecordId == nil || item.Fields == nil {
			logging.ErrorLogger.Error("记录数据不完整，跳过处理")
			continue
		}

		// 智能筛选逻辑：优先检查操作状态，向后兼容运行状态
		if !b.shouldProcessRecord(item.Fields) {
			logging.DebugLogger.Debugf("记录 %s 不需要处理，跳过", *item.RecordId)
			continue
		}

		// 构建会议记录
		record := &models.MeetingRecord{
			RecordID: *item.RecordId,
			Fields:   make(map[string]interface{}),
		}

		// ✅ 验证正确：使用字段名称进行字段映射（符合官方API规范）
		for fieldName, value := range item.Fields {
			record.Fields[fieldName] = value
		}

		// 使用字段映射器转换数据
		standardData, err := b.fieldMapper.MapRecord(record)
		if err != nil {
			logging.ErrorLogger.Errorf("记录 %s 数据映射失败: %v", record.RecordID, err)
			continue
		}

		results = append(results, standardData)
		logging.DebugLogger.Debugf("成功映射记录: %s, 标题: %s", record.RecordID, standardData.Title)
	}

	totalRecords := len(data.Items)
	processedRecords := len(results)
	skippedRecords := totalRecords - processedRecords

	logging.InfoLogger.Infof("数据读取完成 - 总记录数: %d, 待处理记录数: %d, 已跳过记录数: %d",
		totalRecords, processedRecords, skippedRecords)

	if processedRecords == 0 {
		logging.InfoLogger.Info("未找到需要处理的会议记录（所有记录都已处理）")
	}

	return results, nil
}

// shouldProcessRecord 智能筛选逻辑：优先检查操作状态，向后兼容运行状态
func (b *BitableService) shouldProcessRecord(fields map[string]interface{}) bool {
	// 优先检查操作状态字段
	if operationStatus, exists := fields["操作状态"]; exists && operationStatus != nil {
		if status, ok := operationStatus.(string); ok {
			// 只处理操作状态为"待处理"的记录
			shouldProcess := status == "待处理"
			if shouldProcess {
				logging.DebugLogger.Debug("基于操作状态筛选：待处理记录，将进行处理")
			} else {
				logging.DebugLogger.Debugf("基于操作状态筛选：状态为 %s，跳过处理", status)
			}
			return shouldProcess
		}
	}

	// 向后兼容：检查运行状态字段
	if runStatus, exists := fields["运行状态"]; exists && runStatus != nil {
		// 如果运行状态字段不为空，说明已经处理过，跳过
		logging.DebugLogger.Debugf("基于运行状态筛选：状态为 %v，跳过处理", runStatus)
		return false
	}

	// 如果两个状态字段都为空，默认处理（向后兼容旧数据）
	logging.DebugLogger.Debug("状态字段为空，默认处理（向后兼容）")
	return true
}

// UpdateRecordStatus 更新单条记录状态
func (b *BitableService) UpdateRecordStatus(ctx context.Context, recordID string, result *models.ProcessResult) error {
	if recordID == "" {
		return fmt.Errorf("记录ID不能为空")
	}

	// 记录ProcessResult详细信息
	logging.InfoLogger.Infof("开始回填记录 %s 状态: %s", recordID, result.Status)

	// 使用字段映射器构建更新字段
	fields := b.fieldMapper.BuildUpdateFields(result)
	if len(fields) == 0 {

		return nil
	}

	// 构建飞书多维表格API请求
	req := larkbitable.NewUpdateAppTableRecordReqBuilder().
		AppToken(b.config.MeetingAppToken).
		TableId(b.config.MeetingTableID).
		RecordId(recordID).
		AppTableRecord(larkbitable.NewAppTableRecordBuilder().
			Fields(fields).
			Build()).
		Build()

	// 调用飞书API
	resp, err := b.client.Bitable.V1.AppTableRecord.Update(ctx, req)
	if err != nil {
		return b.handleAPIError(fmt.Sprintf("更新记录 %s", recordID), err)
	}

	if !resp.Success() {
		logging.ErrorLogger.Errorf("更新记录API调用失败: code=%d, msg=%s", resp.Code, resp.Msg)
		return fmt.Errorf("更新记录API调用失败: %s (code: %d)", resp.Msg, resp.Code)
	}

	logging.InfoLogger.Infof("成功更新记录 %s 状态为: %s", recordID, result.Status)

	return nil
}

// BatchUpdateStatus 批量更新状态（简化版本）
func (b *BitableService) BatchUpdateStatus(ctx context.Context, results []models.ProcessResult) error {
	if len(results) == 0 {
		return nil
	}

	// 逐个调用单条更新
	for _, result := range results {
		if err := b.UpdateRecordStatus(ctx, result.RecordID, &result); err != nil {
			logging.ErrorLogger.Errorf("更新记录 %s 失败: %v", result.RecordID, err)
			// 继续处理其他记录，不中断整个批次
		}
	}

	return nil
}

// ValidateConfig 验证配置
func (b *BitableService) ValidateConfig() error {
	// 强制验证会议系统专用多维表格应用令牌
	if b.config.MeetingAppToken == "" {
		return fmt.Errorf("MeetingAppToken 配置不能为空，会议系统必须使用独立的应用令牌")
	}
	if b.config.MeetingTableID == "" {
		return fmt.Errorf("MeetingTableID 配置不能为空")
	}
	if b.config.AppID == "" {
		return fmt.Errorf("AppID 配置不能为空")
	}
	if b.config.AppSecret == "" {
		return fmt.Errorf("AppSecret 配置不能为空")
	}
	return nil
}
