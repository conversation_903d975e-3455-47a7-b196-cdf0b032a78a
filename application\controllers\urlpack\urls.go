package urlpack

import (
	"irisAdminApi/application/middleware"

	"github.com/kataras/iris/v12"
)

var Party = func(party iris.Party) {
	party.Get("/jobs/{id:uint}/log", GetJobLog).Name = "查看日志"
	party.Get("/jobs/{id:uint}/full", GetJobFull).Name = "下载全量包"
	party.Get("/jobs/{id:uint}/increment", GetJobIncrement).Name = "下载增量包"
	party.Get("/configs", GetConfigs).Name = "获取配置"
	party.Get("/overseas", GetOverseasCategory).Name = "查看海外分类表"
	party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) //登录验证
	party.Post("/exact", UploadExact).Name = "上传精准库"
	party.Get("/exact", DownloadExact).Name = "下载精准库"
	party.Post("/categories", ImportCategory).Name = "导入分类表"
	party.Get("/categories", GetCategories).Name = "查看分类表"
	party.Post("/jobs", CreateJob).Name = "创建作业"
	party.Get("/jobs", GetJobs).Name = "查看所有作业"
	party.Delete("/jobs/{id:uint}", DeleteJob).Name = "删除作业"
	party.Get("/urls", QueryUrl).Name = "url查询"
	party.Post("/overseas", ImportOverseasCategoryAndCreateConfFiles).Name = "海外分类表导入"
}
