package dcontributionpoint

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/kpi"

	"irisAdminApi/service/dao/kpi/dcontribution"
	"irisAdminApi/service/dao/kpi/dcontributionreview"
	"irisAdminApi/service/dao/user/duser"
)

const ModelName = "贡献积分管理"

/*
	ContributionID uint `gorm:"not null"`
	ContributorID  uint `gorm:"not null"` // foriegn user id
	Point          int  `gorm:"not null"`
*/

type Response struct {
	ID             uint                                `json:"id"`
	UpdatedAt      string                              `json:"updated_at"`
	CreatedAt      string                              `json:"created_at"`
	ContributionID uint                                `json:"contribution_id"`
	ContributorID  uint                                `json:"contributor_id"`
	Point          int                                 `json:"point"`
	DepartmentID   uint                                `json:"department_id"`
	Contributor    *duser.ApprovalResponse             `gorm:"-" json:"contributor"`
	Contributiton  *dcontribution.Response             `gorm:"-" json:"contribution"`
	Reviews        []*dcontributionreview.ListResponse `gorm:"-" json:"reviews,omitempty"`
}

type ListResponse struct {
	Response
}

type Request struct {
	ContributionID uint `json:"contribution_id"`
	ContributorID  uint `json:"contributor_id"`
	Point          int  `json:"point"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *kpi.ContributionPoint {
	return &kpi.ContributionPoint{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var items []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func BatchCreate(object []map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}
	return nil
}

func BatchDelete(contributionId uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Where("contribution_id = ?", contributionId).Delete(Model()).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func FindByContributionID(contributionId uint) ([]*ListResponse, error) {
	items := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("contribution_id = ?", contributionId).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return items, err
	}
	GetContributors(items)
	return items, nil
}

func GetContributors(items []*ListResponse) error {
	userIds := []uint{}
	for _, item := range items {
		userIds = append(userIds, item.ContributorID)
	}
	users, _ := duser.FindSimpleInIds(userIds)
	var userMap = make(map[uint]*duser.ApprovalResponse)
	for _, user := range users {
		userMap[user.Id] = user
	}
	for _, item := range items {
		item.Contributor = userMap[item.ContributorID]
	}
	return nil
}

func FindInContributionID(contributionIds []uint) ([]*ListResponse, error) {
	items := []*ListResponse{}
	err := easygorm.GetEasyGormDb().Model(Model()).Where("contribution_id in ?", contributionIds).Find(&items).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return items, err
	}
	GetContributors(items)
	return items, nil
}
