package libs

import (
	"fmt"

	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/user/duser"
	"irisAdminApi/service/dao/user/duseropenid"
)

// LegacyUserQuery 基于现有 getUserFeishuIDByName 逻辑的用户查询实现
type LegacyUserQuery struct {
	userCache map[string]string // 用户缓存
}

// NewLegacyUserQuery 创建基于现有逻辑的用户查询
func NewLegacyUserQuery() *LegacyUserQuery {
	return &LegacyUserQuery{
		userCache: make(map[string]string),
	}
}

// GetOpenIDByName 根据名称获取OpenID（复用现有逻辑）
func (q *LegacyUserQuery) GetOpenIDByName(name string) string {
	openID, err := q.getUserFeishuIDByName(name)
	if err != nil {
		logging.ErrorLogger.Errorf("获取用户OpenID失败: %v", err)
		return ""
	}
	return openID
}

// GetOpenIDsBatch 批量获取OpenID
func (q *LegacyUserQuery) GetOpenIDsBatch(names []string) map[string]string {
	results := make(map[string]string)

	for _, name := range names {
		if openID := q.GetOpenIDByName(name); openID != "" {
			results[name] = openID
		}
	}

	return results
}

// getUserFeishuIDByName 通过用户姓名获取飞书用户ID（复用现有逻辑）
func (q *LegacyUserQuery) getUserFeishuIDByName(userName string) (string, error) {
	if userName == "" {
		return "", fmt.Errorf("用户名不能为空")
	}

	// 检查缓存
	if cachedID, exists := q.userCache[userName]; exists {
		logging.InfoLogger.Infof("从缓存获取用户飞书ID: %s -> %s", userName, cachedID)
		return cachedID, nil
	}

	logging.InfoLogger.Infof("开始查找用户飞书ID: %s", userName)

	// 策略1: 完全匹配
	_, openID, err := q.findUserByExactMatch(userName)
	if err == nil && openID != "" {
		logging.InfoLogger.Infof("完全匹配成功: %s -> %s", userName, openID)
		q.userCache[userName] = openID // 缓存结果
		return openID, nil
	}

	// 策略2: 模糊匹配
	users, err := q.findUsersByFuzzyMatch(userName)
	if err == nil && len(users) > 0 {
		// 选择最佳匹配用户
		bestUser := q.selectBestMatchUser(users, userName)
		if bestUser != nil {
			// 查找用户对应的飞书OpenID
			userOpenID := duseropenid.Response{}
			err = userOpenID.FindEx("user_id", fmt.Sprintf("%d", bestUser.ID))
			if err == nil && userOpenID.Openid != "" {
				logging.InfoLogger.Infof("模糊匹配成功: %s -> %s (用户: %s)", userName, userOpenID.Openid, bestUser.Name)
				q.userCache[userName] = userOpenID.Openid // 缓存结果
				return userOpenID.Openid, nil
			}
		}
	}

	// 所有策略都失败
	logging.ErrorLogger.Errorf("无法找到用户 %s 的飞书ID", userName)
	return "", fmt.Errorf("无法找到用户 %s 的飞书ID", userName)
}

// findUserByExactMatch 精确匹配用户（通过name字段）
func (q *LegacyUserQuery) findUserByExactMatch(userName string) (uint, string, error) {
	logging.InfoLogger.Infof("精确匹配用户: %s", userName)

	// 通过name字段查找用户
	user := duser.User{}
	err := user.FindEx("name", userName)
	if err != nil {
		logging.InfoLogger.Infof("通过name字段查找失败: %v", err)
		return 0, "", err
	}

	if user.ID == 0 {
		return 0, "", fmt.Errorf("用户 %s 不存在（ID为0）", userName)
	}

	// 查找用户对应的飞书OpenID
	userOpenID := duseropenid.Response{}
	err = userOpenID.FindEx("user_id", fmt.Sprintf("%d", user.ID))
	if err != nil {
		logging.ErrorLogger.Errorf("查找用户 %s (ID: %d) 的飞书OpenID失败: %v", userName, user.ID, err)
		return user.ID, "", fmt.Errorf("用户 %s 没有关联的飞书OpenID", userName)
	}

	if userOpenID.Openid == "" {
		return user.ID, "", fmt.Errorf("用户 %s 的飞书OpenID为空", userName)
	}

	return user.ID, userOpenID.Openid, nil
}

// findUsersByFuzzyMatch 模糊匹配用户
func (q *LegacyUserQuery) findUsersByFuzzyMatch(userName string) ([]*duser.User, error) {
	logging.InfoLogger.Infof("模糊匹配用户: %s", userName)

	// 查询用户（简化实现）
	users := []*duser.User{}

	// 通过name字段模糊匹配
	user := duser.User{}
	err := user.FindEx("name", userName) // 先尝试精确匹配
	if err == nil && user.ID != 0 {
		users = append(users, &user)
	}

	// 如果精确匹配失败，可以添加更多的模糊匹配逻辑
	// 这里为了简化，只返回精确匹配的结果

	if len(users) == 0 {
		return nil, fmt.Errorf("未找到匹配的用户")
	}

	return users, nil
}

// selectBestMatchUser 选择最佳匹配用户
func (q *LegacyUserQuery) selectBestMatchUser(users []*duser.User, targetName string) *duser.User {
	if len(users) == 0 {
		return nil
	}

	// 如果只有一个用户，直接返回
	if len(users) == 1 {
		return users[0]
	}

	// 选择名称最短的用户（通常是最精确的）
	var bestUser *duser.User
	minLength := int(^uint(0) >> 1) // 最大int值

	for i := range users {
		user := users[i]
		nameLength := len(user.Name)
		if nameLength < minLength {
			minLength = nameLength
			bestUser = user
		}
	}

	if bestUser != nil {
		logging.InfoLogger.Infof("选择最佳匹配用户: ID=%d, Name=%s", bestUser.ID, bestUser.Name)
	}

	return bestUser
}

// ClearCache 清空缓存
func (q *LegacyUserQuery) ClearCache() {
	q.userCache = make(map[string]string)
	logging.InfoLogger.Info("用户查询缓存已清空")
}

// GetCacheSize 获取缓存大小
func (q *LegacyUserQuery) GetCacheSize() int {
	return len(q.userCache)
}

// GetCacheStats 获取缓存统计
func (q *LegacyUserQuery) GetCacheStats() map[string]interface{} {
	return map[string]interface{}{
		"cache_size": len(q.userCache),
		"cached_users": func() []string {
			users := make([]string, 0, len(q.userCache))
			for user := range q.userCache {
				users = append(users, user)
			}
			return users
		}(),
	}
}
