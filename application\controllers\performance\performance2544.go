package performance

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/performance/dperformance2544"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

func CreatePerformance2544(ctx iris.Context) {

	request := dperformance2544.Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	validErr := libs.Validate.Struct(&request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	data := []map[string]interface{}{}

	for _, item := range request.Performance {
		data = append(data, map[string]interface{}{
			"CreatedAt":               time.Now(),
			"VersionID":               request.VersionID,
			"Version":                 request.Version,
			"Product":                 request.Product,
			"Branch":                  request.Branch,
			"Category":                request.Category,
			"Framesize":               item.Framesize,
			"AggRxThroughputLineRate": item.AggRxThroughputLineRate,
			"AggL1RxRate":             item.AggL1RxRate,
			"AvgLatency":              item.AvgLatency,
			"Status":                  1,
		})
	}
	if len(data) > 0 {
		err := dperformance2544.BatchCreate(data)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return

		}
	} else {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, fmt.Sprintf("%v", request), "请求无效"))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return

}

func GetPerformances2544(ctx iris.Context) {
	versionID := ctx.FormValue("version_id")
	product := ctx.FormValue("product")
	branch := ctx.FormValue("branch")
	version := ctx.FormValue("version")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")
	var status *bool
	_status, err := ctx.URLParamBool("status")
	if err == nil {
		status = &_status
	}
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dperformance2544.All(versionID, product, branch, version, start, end, status, sort, orderBy, page, pageSize)

	// list, err := dproblem.AllProblems(name, sort, orderBy, page, pageSize, status, start, end, department)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return

}
