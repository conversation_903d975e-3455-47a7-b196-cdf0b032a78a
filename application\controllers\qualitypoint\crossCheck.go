package qualitypoint

import (
	"fmt"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/qualitypoint/dqualitycrosscheckproc"
	"irisAdminApi/service/dao/qualitypoint/dqualitycrosscheckprocdef"
	"irisAdminApi/service/dao/qualitypoint/dqualitycrosscheckprocinst"
	"irisAdminApi/service/transaction/qualitypoint/transqualitycrosscheck"
	"strconv"
	"time"

	"github.com/kataras/iris/v12"
)

func GetCrossCheckProcInsts(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")

	procInst := dqualitycrosscheckprocinst.Response{}
	list, err := procInst.All(name, status, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetCrossCheckProcInstDetail(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	info := dqualitycrosscheckprocinst.Response{}
	err := info.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

func GetCrosscheckProcInstTasks(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	status := ctx.FormValue("status")
	start := ctx.FormValue("start")
	end := ctx.FormValue("end")

	list, err := dqualitycrosscheckproc.AllTasksByProcInst(id, name, sort, orderBy, page, pageSize, status, start, end)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func GetCrossCheckProcPrevNodes(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	nodeID := ctx.FormValue("nodeId")
	procInst := dqualitycrosscheckprocinst.Response{}
	err := procInst.Find(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	resource := procInst.Resource
	if resource == "" {
		ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
		return
	}
	nodes, err := dqualitycrosscheckprocdef.GetNodes(resource)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	result, err := dqualitycrosscheckprocdef.GetBeforeNodes(nodes, nodeID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, result, response.NoErr.Msg))
	return
}

type CrossCheckTaskRequest struct {
	Comment    string `form:"comment"`
	Status     uint   `form:"status"`
	NodeID     string `form:"nodeId"`
	NextNodeID string `form:"nextNodeID"`
	TaskID     uint   `form:"taskId"`
	UserID     uint   `form:"userId"`
}

func UpdateCrossCheckProcInst(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	procInst := dqualitycrosscheckprocinst.Response{}

	err = procInst.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("create approval read form err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	request := CrossCheckTaskRequest{}
	formValues := ctx.FormValues()
	for key, values := range formValues {
		if len(values) > 0 {
			value := values[0]
			switch key {
			case "status":
				fmt.Sscanf(value, "%d", &request.Status)
			case "nodeId":
				request.NodeID = value
			case "nextNodeID":
				request.NextNodeID = value
			case "userId":
				uintValue, _ := strconv.ParseUint(value, 10, 0)
				request.UserID = uint(uintValue)
			case "taskId":
				uintValue, _ := strconv.ParseUint(value, 10, 0)
				request.TaskID = uint(uintValue)
			case "comment":
				request.Comment = value
			}
		}
	}

	violationObject := map[string]interface{}{
		"UpdatedAt": time.Now(),
	}

	fileName := ""
	logging.DebugLogger.Debugf("update", request.NodeID, request.Status)

	taskObject := map[string]interface{}{
		"NodeID":     request.NodeID,
		"UpdatedAt":  time.Now(),
		"Status":     request.Status,
		"NextNodeID": request.NextNodeID,
		"Comment":    request.Comment,
		"Attachment": fileName,
		"UserID":     request.UserID,
	}
	err = transqualitycrosscheck.UpdateViolationTransaction(uId, id, request.TaskID, violationObject, taskObject)
	if err != nil {
		logging.ErrorLogger.Errorf("Error while SaveFormFile ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func SyncQualityPointData() {
	logging.DebugLogger.Debugf("开始同步用户积分数据到飞书")
	SyncQualityPointUserData()
	logging.DebugLogger.Debugf("完成同步用户积分数据到飞书")

	logging.DebugLogger.Debugf("开始同步扣分数据到飞书")
	SyncQualityPointsDeductionData()
	logging.DebugLogger.Debugf("完成同步扣分数据到飞书")

	logging.DebugLogger.Debugf("开始同步消分数据到飞书")
	SyncQualityPointsRecoverieData()
	logging.DebugLogger.Debugf("完成同步消分数据到飞书")

}
