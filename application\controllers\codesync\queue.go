package codesync

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/codesync/dcodesyncqueue"
	"strconv"
	"strings"

	"github.com/kataras/iris/v12"
)

type Qeury struct {
	Scope        string `json:"scope"`
	AssigneeIDs  []uint `json:"assignee_ids"`
	AuthorIDs    []uint `json:"author_ids"`
	Repo         string `json:"repo"`
	SourceBranch string `json:"source_branch"`
	TargetBranch string `json:"target_branch"`
	SourceWebUrl string `json:"source_web_url"`
	TargetWebUrl string `json:"target_web_url"`
	Status       []uint `json:"status"`
	Start        string `json:"start"`
	End          string `json:"end"`
}

func GetCodeSyncQueues(ctx iris.Context) {
	// id, err := dao.GetAuthId(ctx)
	// if err != nil {
	// 	ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
	// 	return
	// }
	scope := ctx.FormValue("scope")
	userID, _ := dao.GetAuthId(ctx)
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	queue := dcodesyncqueue.CodeSyncQueue{}
	var list map[string]interface{}
	var err error
	query := Qeury{}
	if err := ctx.ReadJSON(&query); err != nil {
		logging.ErrorLogger.Errorf("qeury data err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "参数错误"))
		return
	}

	switch scope {
	case "all":
		list, err = queue.UserQueues(query.AssigneeIDs, query.AuthorIDs, query.Status, query.Repo, query.SourceBranch, query.TargetBranch, query.SourceWebUrl, query.TargetWebUrl, query.Start, query.End, name, sort, orderBy, page, pageSize)
	case "created_by_me":
		list, err = queue.UserQueues(query.AssigneeIDs, []uint{userID}, query.Status, query.Repo, query.SourceBranch, query.TargetBranch, query.SourceWebUrl, query.TargetWebUrl, query.Start, query.End, name, sort, orderBy, page, pageSize)
	case "assign_to_me":
		list, err = queue.UserQueues([]uint{userID}, query.AuthorIDs, query.Status, query.Repo, query.SourceBranch, query.TargetBranch, query.SourceWebUrl, query.TargetWebUrl, query.Start, query.End, name, sort, orderBy, page, pageSize)
	default:
		list, err = queue.UserQueues([]uint{userID}, query.AuthorIDs, query.Status, query.Repo, query.SourceBranch, query.TargetBranch, query.SourceWebUrl, query.TargetWebUrl, query.Start, query.End, name, sort, orderBy, page, pageSize)
	}

	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

func UpdateCodeSyncQueue(ctx iris.Context) {
	id, _ := dao.GetId(ctx)
	query := Qeury{}
	if err := ctx.ReadJSON(&query); err != nil {
		logging.ErrorLogger.Errorf("update data ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "参数错误"))
		return
	}
	queue := dcodesyncqueue.CodeSyncQueue{}
	err := queue.Find(id)
	if err != nil {
		logging.ErrorLogger.Errorf("find queue by id err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if queue.ID == 0 {
		logging.ErrorLogger.Errorf("find queue by id not found")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	targetWebSlice := strings.Split(query.TargetWebUrl, "/")
	_mrIID := targetWebSlice[len(targetWebSlice)-1]
	mrIID, err := strconv.Atoi(_mrIID)
	if err != nil {
		logging.ErrorLogger.Errorf("target web url not correct")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "URL有误，请确认"))
		return
	}
	mr, err := GetMergeRequest(queue.RepositoryID, strconv.FormatInt(int64(mrIID), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("target web url not correct", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "URL有误，请确认"))
		return
	}
	if strconv.FormatInt(int64(mr.ProjectID), 10) != queue.RepositoryID {
		logging.ErrorLogger.Errorf("target web url not correct")
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "提交同步MR与来源MR不属于同一个项目"))
		return
	}
	var status uint
	switch mr.State {
	case "opened":
		status = 1
	case "closed":
		status = 4
	case "merged":
		status = 3
	default:
		status = 5
	}
	log := queue.Log
	log = log + "\n" + "手动更新同步MR地址：" + query.TargetWebUrl
	err = queue.Update(id, map[string]interface{}{"target_merge_request_id": mr.ID, "target_merge_request_iid": mr.IID, "target_web_url": query.TargetWebUrl, "status": status, "log": log})
	if err != nil {
		logging.ErrorLogger.Errorf("find queue by id ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if ReplaceUser(&queue, &mr) {
		SendMail(&queue, &mr, status)
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetMergeRequest(repositoryID, mergeRequestIID string) (MergeRequestResponse, error) {
	url := fmt.Sprintf("%s/api/%s/projects/%s/merge_requests/%s", libs.Config.Gitlab.Url, libs.Config.Gitlab.Version, repositoryID, mergeRequestIID)
	var result MergeRequestResponse
	var errMsg string
	data := map[string]string{
		"private_token": libs.Config.CodeSync.Token,
	}
	resp, err := CodeSyncClient.R().SetSuccessResult(&result).SetErrorResult(&errMsg).SetQueryParams(data).Get(url)
	if err != nil {
		return result, fmt.Errorf("%s, %s", err.Error(), errMsg)
	}
	if resp.IsSuccessState() {
		return result, nil
	}
	return result, fmt.Errorf("%s, %s", "未知错误", errMsg)
}
