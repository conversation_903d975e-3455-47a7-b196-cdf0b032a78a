package schedule

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/productionrelease/dproduction"
	"irisAdminApi/service/dao/productionrelease/dproductionprocinst"
	"irisAdminApi/service/dao/productionrelease/dproductionproctask"
	"irisAdminApi/service/dao/user/duser"
	"time"
)

var ProductionTaskLimit = 60 * 60 * 24 // 24小时
var ProductionLimit = 3 * 60 * 60 * 24 // 3天

var ProductionUrgencyTaskLimit = 60 * 60 * 3 // 3小时
var ProductionUrgencyLimit = 60 * 60 * 24    // 24小时

func CheckTaskskOverTime() {
	t := time.NewTicker(60 * time.Minute)
	for {
		<-t.C
		dproductionproctask.UpdateFeatureTasksProccessingCache()
		urgencyProcInstIDs := []uint{}
		for _, item := range dproduction.FeatureProccessingCache {
			if item.Urgency {
				if id, ok := dproductionprocinst.ProductionIDProcInstIDMap[item.ID]; ok {
					urgencyProcInstIDs = append(urgencyProcInstIDs, id)
				}
			}
		}
		now := time.Now().Unix()
		limit := int64(ProductionTaskLimit)
		for _, item := range dproductionproctask.ProductionTasksProccessingCache {
			if !item.OverTimeNotice {
				createdAt, err := time.ParseInLocation("2006-01-02T15:04:05.999+08:00", item.CreatedAt, time.Local)
				if err != nil {
					logging.ErrorLogger.Errorf("parser time error", err)
				}
				urgency := libs.InArrayUint(urgencyProcInstIDs, item.ProcInstID)
				if urgency {
					limit = int64(ProductionUrgencyTaskLimit)
				} else {
					limit = int64(ProductionTaskLimit)
				}
				if now-createdAt.Unix() > int64(limit) {
					if err := SendProductionTaskOverTimeMail(item, urgency); err != nil {
						continue
					}
					err := item.Response.Update(item.ID, map[string]interface{}{"OverTimeNotice": 1})
					if err != nil {
						logging.ErrorLogger.Errorf("parser time error", err)
					}
				}
			}
		}
	}
}

func CheckProductionkOverTime() {
	t := time.NewTicker(60 * time.Minute)
	for {
		<-t.C
		dproduction.UpdateFeatureProccessingCache()
		featureIds := []uint{}
		urgencyFeatureIDs := []uint{}
		for _, item := range dproduction.FeatureProccessingCache {
			featureIds = append(featureIds, item.ID)
			if item.Urgency {
				urgencyFeatureIDs = append(urgencyFeatureIDs, item.ID)
			}
		}
		dproductionprocinst.UpdateFeatureProccessingCache(featureIds)
		now := time.Now().Unix()
		limit := int64(ProductionLimit)
		for _, item := range dproduction.FeatureProccessingCache {
			if !item.OverTimeNotice {
				createdAt := item.CreatedAt
				urgency := libs.InArrayUint(urgencyFeatureIDs, item.ID)
				if urgency {
					limit = int64(ProductionUrgencyLimit)
				} else {
					limit = int64(ProductionLimit)
				}
				if now-createdAt.Unix() > limit {
					if err := SendProductionOverTimeMail(item, urgency); err != nil {
						continue
					}
					err := item.Response.Update(item.ID, map[string]interface{}{"OverTimeNotice": 1})
					if err != nil {
						logging.ErrorLogger.Errorf("parser time error", err)
					}
				}
			}
		}
	}
}

// todo: 增加紧急状态通知提示
func SendProductionTaskOverTimeMail(task *dproductionproctask.ListResponse, urgency bool) error {
	var subject string
	mailTo := []string{fmt.Sprintf("%<EMAIL>", duser.UserMap[task.Assignee].Username), "<EMAIL>"}
	cc := []string{}
	if urgency {
		subject = fmt.Sprintf("[紧急][下生产管理系统][下生产库ID:%d][%s][已超时]", task.ProcInstID, task.NodeName)
	} else {
		subject = fmt.Sprintf("[下生产管理系统][下生产库ID:%d][%s][已超时]", task.ProcInstID, task.NodeName)
	}
	body := fmt.Sprintf(`%s<br><p>下生产系统链接: <a href="http://10.51.134.126/productionSYS/#/prodSys/todo">http://10.51.134.126/productionSYS/#/prodSys/todo</a><p>`, subject)

	if !libs.Config.Debug {
		err := libs.SendMail(mailTo, subject, body, cc)
		// err := libs.SendMail([]string{fmt.Sprintf("%<EMAIL>", "linjiakai")}, subject, body)
		if err != nil {
			logging.ErrorLogger.Error(err)
			return err
		}
	}
	logging.DebugLogger.Debugf("send mail", mailTo, subject, body)
	return nil
}

// todo: 增加紧急状态通知提示
func SendProductionOverTimeMail(feature *dproduction.ListResponse, urgency bool) error {
	var subject string
	mailTo := []string{}
	mailTo = append(mailTo, fmt.Sprintf("%<EMAIL>", duser.UserMap[dproductionprocinst.ProductionProcInstUserMap[feature.ID]].Username))
	cc := []string{"<EMAIL>"}
	// mailTo := []string{"<EMAIL>"}
	if urgency {
		subject = fmt.Sprintf("[紧急][下生产管理系统][规则库ID:%d][发布流程已超时]", feature.ID)
	} else {
		subject = fmt.Sprintf("[下生产管理系统][规则库ID:%d][发布流程已超时]", feature.ID)
	}
	body := fmt.Sprintf(`%s<br><p>下生产管理系统链接: <a href="http://10.51.134.126/productionSYS/#/ntos/list">http://10.51.134.126/productionSYS/#/ntos/list</a><p>`, subject)

	if !libs.Config.Debug {
		err := libs.SendMail(mailTo, subject, body, cc)
		// err := libs.SendMail([]string{fmt.Sprintf("%<EMAIL>", "linjiakai")}, subject, body)
		if err != nil {
			logging.ErrorLogger.Error(err)
			return err
		}
	}
	logging.DebugLogger.Debugf("send mail", mailTo, subject, body)
	return nil
}
