package taskmanagers

import (
	"time"

	"k8s.io/client-go/util/workqueue"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/opensource/dcomponent"
	"irisAdminApi/service/dao/opensource/dvulnerability"
)

var ScheduledSendEmailTaskManager *scheduledSendEmailTaskManager

func InitScheduledSendEmailTaskManager() {
	ScheduledSendEmailTaskManager = newScheduledSendEmailTaskManager()

	go func() {
		if components, err := dcomponent.ListAllComponents(); err == nil {
			for _, componentRes := range components {
				logging.DebugLogger.Debugf("添加组件漏洞超时未处理通知任务: ID-<%d>", componentRes.ID)
				ScheduledSendEmailTaskManager.AddScheduledSendEmailTask(componentRes)
			}
		}
	}()
	ScheduledSendEmailTaskManager.run()
}

type scheduledSendEmailTaskManager struct {
	Queue workqueue.DelayingInterface
}

func newScheduledSendEmailTaskManager() *scheduledSendEmailTaskManager {
	return &scheduledSendEmailTaskManager{
		Queue: workqueue.NewNamedDelayingQueue("组件漏洞超时未处理邮件通知队列"),
	}
}

func sendVulnerabilitiesDelayedEmail(componentRes *dcomponent.OpenSourceComponent, vulnerabilities []*dvulnerability.OpenSourceVulnerability) {
	responsibleUserUsernames := make([]string, len(componentRes.ResponsibleUsers))
	for i, userInfo := range componentRes.ResponsibleUsers {
		responsibleUserUsernames[i] = userInfo.Username
	}

	responsibleLeaderUsernames := make([]string, len(componentRes.ResponsibleLeaders))
	for i, userInfo := range componentRes.ResponsibleLeaders {
		responsibleLeaderUsernames[i] = userInfo.Username
	}
	sendVulnerabilitiesEmail(vulnerabilityDelaySubject(
		componentRes.Name, componentRes.Version, componentRes.Product, componentRes.ProductVersion),
		vulnerabilities, responsibleUserUsernames,
		responsibleLeaderUsernames, nil)

	now := time.Now()
	updateEmailTime := map[string]interface{}{
		"email_time": now,
	}
	for _, vulnerabilityRes := range vulnerabilities {
		vulnerabilityRes.Update(vulnerabilityRes.ID, updateEmailTime)
	}
}

func (manager *scheduledSendEmailTaskManager) AddScheduledSendEmailTask(componentRes *dcomponent.OpenSourceComponent) {
	manager.Queue.AddAfter(componentRes.ID, durationToNextMonday())
}

func (manager *scheduledSendEmailTaskManager) run() {
	var sendEmailTaskWorkers = libs.Config.OpenSource.SendEmailTaskWorkers
	for i := 0; i < sendEmailTaskWorkers; i++ {
		go manager.runSendEmail()
	}
}

func (manager *scheduledSendEmailTaskManager) runSendEmail() {
	for manager.processNextSendEmailTask() {
	}
}

func (manager *scheduledSendEmailTaskManager) processNextSendEmailTask() bool {
	item, quit := manager.Queue.Get()
	if quit {
		return false
	}
	componentId, ok := item.(uint)
	if !ok || componentId == 0 {
		// item出错, 删除
		manager.Queue.Done(item)
		return true
	}

	componentRes, err := dcomponent.FindById(componentId)
	if err != nil {
		manager.Queue.Add(item)
		return true
	} else if componentRes == nil {
		manager.Queue.Done(item)
		return true
	}

	vulnerabilities, err := dvulnerability.ListComponentUnprocessedVulnerabilities(componentId)
	if err != nil {
		manager.Queue.Add(item)
		return true
	}

	var vulnerabilityDelayedTime = time.Duration(libs.Config.OpenSource.VulnerabilityDelayedTimeInHours) * time.Hour

	var delayedVulnerabilities []*dvulnerability.OpenSourceVulnerability
	now := time.Now()
	for _, vulnerabilityRes := range vulnerabilities {
		if now.Sub(vulnerabilityRes.CreatedAt) > vulnerabilityDelayedTime {
			delayedVulnerabilities = append(delayedVulnerabilities, &vulnerabilityRes.OpenSourceVulnerability)
		}
	}
	sendVulnerabilitiesDelayedEmail(componentRes, delayedVulnerabilities)
	manager.Queue.AddAfter(componentRes.ID, durationToNextMonday())
	return true
}

func SendDelegatedEmail(vulnerabilityRes *dvulnerability.OpenSourceVulnerability, delegatedUserUsername string) {
	sendVulnerabilitiesEmail(
		vulnerabilityDelegatedSubject(vulnerabilityRes.Component.Name, vulnerabilityRes.Component.Version,
			vulnerabilityRes.Component.Product, vulnerabilityRes.Component.ProductVersion),
		[]*dvulnerability.OpenSourceVulnerability{vulnerabilityRes}, nil, nil, []string{delegatedUserUsername})
}
