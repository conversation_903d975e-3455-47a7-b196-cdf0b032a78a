package transfeaturerelease

import (
	"errors"
	"fmt"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/featurerelease/dfeature"
	"irisAdminApi/service/dao/featurerelease/dfeaturecloudconfigrelation"
	"irisAdminApi/service/dao/featurerelease/dfeatureprocdef"
	"irisAdminApi/service/dao/featurerelease/dfeatureprocinst"
	"irisAdminApi/service/dao/featurerelease/dfeatureproctask"
	"irisAdminApi/service/dao/featurerelease/dfeatureseccloudconfig"
	"irisAdminApi/service/dao/user/duser"

	"gorm.io/gorm"
)

func CreateFeatureTransaction(userID uint, featureObject map[string]interface{}, secCloudConfigs []map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		resource := featureObject["Resource"].(string)
		delete(featureObject, "Resource")
		// 创建安全云配置表数据并获取对应 ID
		configIDs, err := createSecCloudConfigs(tx, secCloudConfigs)
		if err != nil {
			return fmt.Errorf("failed to create security cloud configs: %w", err)
		}

		if err := tx.Model(dfeature.Model()).Omit("FeatureSecCloudConfigs").Create(featureObject).Error; err != nil {
			// 返回任何错误都会回滚事务
			return err
		}
		feature := dfeature.Response{}
		if err := tx.Model(dfeature.Model()).Where("uuid = ?", featureObject["Uuid"]).Find(&feature).Error; err != nil {
			return err
		}
		if feature.ID == 0 {
			return errors.New("创建规则库发布流程失败")
		}

		// 创建 features 与安全云配置的关联关系
		if err := createFeatureConfigRelations(tx, feature.ID, configIDs); err != nil {
			return fmt.Errorf("failed to create feature-config relations: %w", err)
		}

		procInstObject := map[string]interface{}{
			"Title": feature.FileName + "_" + feature.Version,
			// 当前节点
			"NodeID":      "start",
			"TaskID":      0,
			"StartUserID": userID,
			"FeatureID":   feature.ID,
			"CreatedAt":   time.Now(),
			"UpdatedAt":   time.Now(),
			"Resource":    resource,
			"Status":      0,
		}
		if err := tx.Model(dfeatureprocinst.Model()).Create(procInstObject).Error; err != nil {
			// 返回任何错误都会回滚事务
			return err
		}
		procInst := dfeatureprocinst.FeatureProcInst{}
		if err := tx.Model(dfeatureprocinst.Model()).Where("feature_id = ?", feature.ID).Find(&procInst).Error; err != nil {
			return err
		}
		if procInst.ID == 0 {
			return errors.New("创建规则库发布实例失败")
		}
		procTaskObjects := []map[string]interface{}{}
		defResource := procInst.Resource
		nodes, _ := dfeatureprocdef.GetNodes(defResource)
		for _, node := range nodes {
			if node.NodeID == "start" {
				procTaskObjects = append(procTaskObjects, map[string]interface{}{
					"CreatedAt":  time.Now(),
					"UpdatedAt":  time.Now(),
					"NodeName":   node.Name,
					"NodeID":     node.NodeID,
					"PrevNodeID": node.PrevNodeID,
					"ProcInstID": procInst.ID,
					"Assignee":   userID,
					"Status":     1,
					"Flag":       true,
				})
				break
			}
		}
		for _, node := range nodes {
			assignee := node.Assignee
			if node.Assignee == 0 {
				assignee = userID
			}
			if node.PrevNodeID == "start" {
				procTaskObjects = append(procTaskObjects, map[string]interface{}{
					"CreatedAt":  time.Now(),
					"UpdatedAt":  time.Now(),
					"NodeName":   node.Name,
					"NodeID":     node.NodeID,
					"PrevNodeID": node.PrevNodeID,
					"ProcInstID": procInst.ID,
					"Assignee":   assignee,
					"Status":     0,
					"Flag":       true,
				})
			}
		}
		if len(procTaskObjects) > 0 {
			if err := tx.Model(dfeatureproctask.Model()).Create(procTaskObjects).Error; err != nil {
				return err
			}
			go SendMail(procTaskObjects, feature.ID, feature.Urgency)
		}

		// 返回 nil 提交事务
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func UpdateFeatureTransaction(userID, procInstID, taskID uint, featureObject map[string]interface{}, taskObject map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 查检当前处理节点是否重复处理
		task := dfeatureproctask.FeatureProcTask{}
		if err := tx.Model(dfeatureproctask.Model()).Where("id = ? and status = 0 and flag = true", taskID).Find(&task).Error; err != nil {
			return err
		}

		if task.ID == 0 {
			return errors.New("已处理任务，无法重复处理")
		}
		if task.Assignee != userID {
			return errors.New("不是当前用户的任务")
		}
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		procInst := dfeatureprocinst.FeatureProcInst{}
		if err := tx.Model(dfeatureprocinst.Model()).Where("id = ?", procInstID).Find(&procInst).Error; err != nil {
			return errors.New("Find Proc Inst Error:" + err.Error())
		}
		if procInst.ID == 0 {
			return errors.New("未找到规则库发布实例")
		}

		feature := dfeature.Response{}
		if err := tx.Model(dfeature.Model()).Where("id = ?", procInst.FeatureID).Find(&feature).Error; err != nil {
			return errors.New("Find Feature Error:" + err.Error())
		}
		if feature.ID == 0 {
			return errors.New("未找到规则库")
		}

		resource := procInst.Resource
		nodes, _ := dfeatureprocdef.GetNodes(resource)
		procTaskObjects := []map[string]interface{}{}
		levelNodeIDs := []string{}
		prevTasks := []*dfeatureproctask.FeatureProcTask{}
		levelTasks := []*dfeatureproctask.FeatureProcTask{}
		nextTasks := []*dfeatureproctask.FeatureProcTask{}

		prevNodeIDs, _ := dfeatureprocdef.GetPrevNodeIDs(resource, taskObject["NodeID"].(string))
		nextNodeIDs, _ := dfeatureprocdef.GetNextNodeIDs(resource, taskObject["NodeID"].(string))
		_levelNodeIDs, _ := dfeatureprocdef.GetNextNodeIDs(resource, task.PrevNodeID)

		// 同级任务去掉当前任务节点id
		for _, id := range _levelNodeIDs {
			if id != task.NodeID {
				levelNodeIDs = append(levelNodeIDs, id)
			}
		}

		// fmt.Println(taskObject["NodeID"].(string), task.NodeID, prevNodeIDs, nextNodeIDs, _levelNodeIDs, levelNodeIDs)
		switch taskObject["Status"].(uint) {
		case 1:
			// 检查是否为驳回订单，如果是驳回订单，将后续节点flag 置为false

			// 检查前置任务是否都通过
			if len(prevNodeIDs) > 0 {
				if err := tx.Model(dfeatureproctask.Model()).Where("node_id in ? and proc_inst_id = ? and status = 1 and flag = true", prevNodeIDs, procInst.ID).Find(&prevTasks).Error; err != nil {
					return errors.New("Find Prev Task Error:" + err.Error())
				}
				if len(prevTasks) < len(prevNodeIDs) {
					return errors.New("前置节点未全部通过")
				}
			}

			// 检查同级任务是否都通过
			if err := tx.Model(dfeatureproctask.Model()).Where("node_id in ? and proc_inst_id = ? and status = 1 and flag = true", levelNodeIDs, procInst.ID).Find(&levelTasks).Error; err != nil {
				return errors.New("Find Level Task Error:" + err.Error())
			}
			levelTaskNodeIDs := []string{}
			for _, t := range levelTasks {
				if !libs.InArrayS(levelTaskNodeIDs, t.NodeID) {
					levelTaskNodeIDs = append(levelTaskNodeIDs, t.NodeID)
				}
			}

			if len(levelNodeIDs) == len(levelTaskNodeIDs) {
				if len(nextNodeIDs) > 0 {
					if err := tx.Model(dfeatureproctask.Model()).Where("node_id in ? and proc_inst_id = ? and status = 0 and flag = true", nextNodeIDs, procInst.ID).Find(&nextTasks).Error; err != nil {
						return errors.New("Find Next Task Error:" + err.Error())
					}
				out:
					for _, nextNodeID := range nextNodeIDs {
						for _, nextTask := range nextTasks {
							if nextTask.NodeID == nextNodeID {
								continue out
							}
						}
						// 获取
						nextNodes, _ := dfeatureprocdef.GetNodesByNodeID(procInst.Resource, nextNodeID)
						for _, node := range nextNodes {
							procTaskObjects = append(procTaskObjects, map[string]interface{}{
								"CreatedAt":  time.Now(),
								"NodeName":   node.Name,
								"NodeID":     node.NodeID,
								"PrevNodeID": node.PrevNodeID,
								"ProcInstID": procInst.ID,
								"Assignee":   node.Assignee,
								"Status":     0,
								"Flag":       true,
							})
						}
					}

				} else {
					featureObject["Status"] = 1
				}
			}
		case 2:
			taskObject["Flag"] = false
			// 处理多节点回退
			var nextNodeIDs []string
			// 检查是否为多节点回退
			if multiNodeIDs, ok := taskObject["NextNodeIDs"].([]string); ok && len(multiNodeIDs) > 0 {
				// 验证所有回退目标节点是否属于同一级别（有相同的前置节点）
				var prevParentID string
				isValid := true
				for i, nodeID := range multiNodeIDs {
					// 获取该节点的前置节点
					fmt.Println(resource, nodeID)
					nodePrevIDs, _ := dfeatureprocdef.GetPrevNodeIDs(resource, nodeID)

					// 如果节点没有前置节点，不是有效的回退目标
					if len(nodePrevIDs) == 0 {
						isValid = false
						break
					}

					// 所有节点必须有相同的前置节点才能被视为同级节点
					if i == 0 {
						prevParentID = nodePrevIDs[0]
					} else if nodePrevIDs[0] != prevParentID {
						isValid = false
						break
					}
				}
				if !isValid {
					return errors.New("回退目标节点必须是同级节点（具有相同的前置节点）")
				}
				nextNodeIDs = multiNodeIDs
			} else if nextNodeID, ok := taskObject["NextNodeID"].(string); ok && nextNodeID != "" {
				// 单节点回退处理
				nextNodeIDs = []string{nextNodeID}
			} else {
				return errors.New("未指定有效的回退目标节点")
			}

			// 获取所有处理回退的节点的已处理任务
			prevTasks := []*dfeatureproctask.FeatureProcTask{}
			for _, nextNodeID := range nextNodeIDs {
				prevTask := dfeatureproctask.FeatureProcTask{}
				if err := tx.Model(dfeatureproctask.Model()).Last(&prevTask, "node_id = ? and proc_inst_id = ? and status != 0 and flag = true and (comment != '无需处理' or comment is null)", nextNodeID, procInst.ID).Error; err != nil {
					return errors.New("Find Prev Task Error:" + err.Error())
				}
				prevTasks = append(prevTasks, &prevTask)
			}

			// 处理回退节点之后的未处理任务
			handleIDs := []string{}

			// 获取所有回退节点后续的节点
			for _, nextNodeID := range nextNodeIDs {
				handleNodes, _ := dfeatureprocdef.GetAfterNodes(nodes, nextNodeID)
				for _, node := range handleNodes {
					if !libs.InArrayS(handleIDs, node.NodeID) {
						handleIDs = append(handleIDs, node.NodeID)
					}
				}
			}

			// 添加当前节点ID和回退目标节点IDs
			handleIDs = append(handleIDs, taskObject["NodeID"].(string))
			for _, nextNodeID := range nextNodeIDs {
				if !libs.InArrayS(handleIDs, nextNodeID) {
					handleIDs = append(handleIDs, nextNodeID)
				}
			}

			// 删除未处理的任务
			if err := tx.Delete(dfeatureproctask.Model(), "node_id in ? and proc_inst_id = ? and status = 0 and flag = true and id != ?", handleIDs, procInst.ID, taskID).Error; err != nil {
				return errors.New("Delete Undo Task Error:" + err.Error())
			}

			// 标记已处理的任务为无效
			if err := tx.Model(dfeatureproctask.Model()).Where("node_id in ? and proc_inst_id = ? and status != 0 and flag = true", handleIDs, procInst.ID).UpdateColumns(map[string]interface{}{"Flag": false}).Error; err != nil {
				return errors.New("Delete Undo Task Error:" + err.Error())
			}

			// 生成新的回退任务
			for _, prevTask := range prevTasks {
				procTaskObjects = append(procTaskObjects, map[string]interface{}{
					"CreatedAt":  time.Now(),
					"NodeName":   prevTask.NodeName,
					"NodeID":     prevTask.NodeID,
					"PrevNodeID": prevTask.PrevNodeID,
					"ProcInstID": procInst.ID,
					"Assignee":   prevTask.Assignee,
					"Status":     0,
					"Flag":       true,
				})
			}
			// 如果是多节点回退，删除 NextNodeIDs 字段
			delete(taskObject, "NextNodeIDs")
		case 3:
			var users []*duser.ApprovalResponse
			if err := tx.Model(duser.Model()).Where("id in ?", []uint{userID, taskObject["UserID"].(uint)}).Find(&users).Error; err != nil {
				return errors.New("Find User Error:" + err.Error())
			}
			userMap := make(map[uint]*duser.ApprovalResponse)
			for _, user := range users {
				userMap[user.Id] = user
			}
			procTaskObjects = append(procTaskObjects, map[string]interface{}{
				"CreatedAt":  time.Now(),
				"NodeName":   task.NodeName,
				"NodeID":     task.NodeID,
				"PrevNodeID": task.PrevNodeID,
				"ProcInstID": procInst.ID,
				"Assignee":   taskObject["UserID"].(uint),
				"Status":     0,
				"Flag":       true,
			})
			if len(taskObject["Comment"].(string)) > 0 {
				taskObject["Comment"] = fmt.Sprintf("%s \n %s -> %s", taskObject["Comment"], userMap[userID].Name, userMap[taskObject["UserID"].(uint)].Name)
			} else {
				taskObject["Comment"] = fmt.Sprintf("%s -> %s", userMap[userID].Name, userMap[taskObject["UserID"].(uint)].Name)
			}
		case 4:
			featureObject["Status"] = 4
			featureObject["file_md5"] = feature.FileMd5 + "_deleted_at_" + time.Now().Format("20160102150405")
		}

		delete(taskObject, "NextNodeID")
		delete(taskObject, "NextNodeIDs")
		delete(taskObject, "UserID")

		if len(procTaskObjects) > 0 {
			// 强制去重，避免生成重复记录
			uniqueNodeNames := []string{}
			_procTaskObjects := []map[string]interface{}{}
			for _, procTaskObject := range procTaskObjects {
				if !libs.InArrayS(uniqueNodeNames, procTaskObject["NodeName"].(string)) {
					uniqueNodeNames = append(uniqueNodeNames, procTaskObject["NodeName"].(string))
					_procTaskObjects = append(_procTaskObjects, procTaskObject)
				}
			}
			if err := tx.Model(dfeatureproctask.Model()).Create(procTaskObjects).Error; err != nil {
				return errors.New("Create Task Error:" + err.Error())
			}

			go SendMail(procTaskObjects, feature.ID, feature.Urgency)
		}
		if err := tx.Model(dfeatureproctask.Model()).Where("id = ?", taskID).Updates(taskObject).Error; err != nil {
			return errors.New("Update Task Error:" + err.Error())
		}
		// 将相同nodeid的任务置为完成

		/*
			taskObject := map[string]interface{}{
				"NodeID":     request.NodeID,
				"UpdatedAt":  time.Now(),
				"Status":     request.Status,
				"NextNodeID": request.NextNodeID,
				"Comment":    request.Comment,
				"Attachment": fileName,
				"UserID":     request.UserID,
			}
		*/
		_taskObject := map[string]interface{}{
			"NodeID":    taskObject["NodeID"],
			"UpdatedAt": time.Now(),
			"Status":    taskObject["Status"],
			"Comment":   fmt.Sprintf("无需处理"),
		}
		if err := tx.Model(dfeatureproctask.Model()).Where(" proc_inst_id = ? and id != ? and node_id = ? and node_name != ?", procInstID, taskID, taskObject["NodeID"].(string), task.NodeName).Updates(_taskObject).Error; err != nil {
			return errors.New("Update Task Error:" + err.Error())
		}

		if err := tx.Model(dfeature.Model()).Where("id = ?", procInst.FeatureID).UpdateColumns(featureObject).Error; err != nil {
			return errors.New("Update Feature Error:" + err.Error())
		}
		delete(featureObject, "file_md5")
		if err := tx.Model(dfeatureprocinst.Model()).Where("id = ?", procInst.ID).UpdateColumns(featureObject).Error; err != nil {
			return errors.New("Update Proc Inst Error:" + err.Error())
		}
		if featureObject["Status"] == 1 {
			allTasks := []*dfeatureproctask.FeatureProcTask{}
			if err := tx.Model(dfeatureproctask.Model()).Where("proc_inst_id = ? and status = 1 and flag = true", procInst.ID).Find(&allTasks).Error; err != nil {
				return errors.New("Find All Task Error:" + err.Error())
			}
			go SendMailSuccess(allTasks, &feature)
		}
		// 返回 nil 提交事务
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

// 创建安全云配置表数据并返回 ID 列表
func createSecCloudConfigs(tx *gorm.DB, secCloudConfigs []map[string]interface{}) ([]uint, error) {
	var configIDs []uint

	for _, config := range secCloudConfigs {
		// 使用指针创建 secCloudConfig
		secCloudConfig := &dfeatureseccloudconfig.Response{
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}

		// 设置 SecCloud 字段
		if secCloud, ok := config["sec_cloud"].(string); ok {
			secCloudConfig.SecCloud = secCloud
		} else {
			return nil, errors.New("invalid or missing 'sec_cloud'")
		}

		// 设置 ProductModels 字段
		if productModels, ok := config["product_models"].(string); ok {
			secCloudConfig.ProductModels = productModels
		} else {
			return nil, errors.New("invalid or missing 'product_models'")
		}

		// 设置 SoftVersions 字段
		if softVersions, ok := config["soft_versions"].(string); ok {
			secCloudConfig.SoftVersions = softVersions
		} else {
			return nil, errors.New("invalid or missing 'soft_versions'")
		}
		// 设置 gray_rule_id 字段
		if grayRuleID, ok := config["gray_rule_id"].(string); ok {
			secCloudConfig.GrayRuleID = grayRuleID
		}
		// 设置 version_desc 字段
		if versionDesc, ok := config["version_desc"].(string); ok {
			secCloudConfig.VersionDesc = versionDesc
		}

		// 创建记录并捕获错误
		if err := tx.Model(&dfeatureseccloudconfig.Response{}).Create(secCloudConfig).Error; err != nil {
			return nil, fmt.Errorf("failed to create security cloud config: %w", err)
		}

		// 从 secCloudConfig 中获取 ID 并存储
		configIDs = append(configIDs, secCloudConfig.ID)
	}

	return configIDs, nil
}

// 创建 feature 与安全云配置的关联关系
func createFeatureConfigRelations(tx *gorm.DB, featureID uint, configIDs []uint) error {
	var relations []map[string]interface{}

	for _, configID := range configIDs {
		relations = append(relations, map[string]interface{}{
			"FeatureID": featureID,
			"ConfigID":  configID,
			"CreatedAt": time.Now(),
			"UpdatedAt": time.Now(),
		})
	}

	if err := tx.Model(dfeaturecloudconfigrelation.Model()).Create(relations).Error; err != nil {
		return fmt.Errorf("failed to create feature-config relations: %w", err)
	}

	return nil
}

func SendMail(procTaskObjects []map[string]interface{}, featureID uint, urgency bool) {
	for _, procTaskObject := range procTaskObjects {
		if procTaskObject["Status"] == 0 {
			subject := fmt.Sprintf("[规则库管理系统][规则库ID:%d][%s][待处理]", featureID, procTaskObject["NodeName"].(string))

			if urgency {
				subject = fmt.Sprintf("[紧急][规则库管理系统][规则库ID:%d][%s][待处理]", featureID, procTaskObject["NodeName"].(string))
			}

			body := fmt.Sprintf(`%s<br><p>规则库链接: <a href="http://feature.aqyfzx.ruijie.net/feature/#/ntos/todo">http://feature.aqyfzx.ruijie.net/feature/#/ntos/todo</a><p>`, subject)
			err := libs.SendMail([]string{fmt.Sprintf("%<EMAIL>", duser.UserMap[procTaskObject["Assignee"].(uint)].Username)}, subject, body, []string{})
			// err := libs.SendMail([]string{fmt.Sprintf("%<EMAIL>", "linjiakai")}, subject, body)
			if err != nil {
				logging.ErrorLogger.Error(err)
			}

			logging.DebugLogger.Debugf("send mail", []string{fmt.Sprintf("%<EMAIL>", duser.UserMap[procTaskObject["Assignee"].(uint)].Username)}, subject, body)
		}
	}
}

func SendMailSuccess(taskObjects []*dfeatureproctask.FeatureProcTask, feature *dfeature.Response) {
	mailTo := []string{"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"}
	for _, taskObject := range taskObjects {
		if !libs.InArrayS(mailTo, fmt.Sprintf("%<EMAIL>", duser.UserMap[taskObject.Assignee].Username)) {
			mailTo = append(mailTo, fmt.Sprintf("%<EMAIL>", duser.UserMap[taskObject.Assignee].Username))
		}
	}
	subject := fmt.Sprintf("[规则库管理系统][规则库ID:%d][已发布成功]", feature.ID)
	if feature.Urgency {
		subject = fmt.Sprintf("[紧急][规则库管理系统][规则库ID:%d][已发布成功]", feature.ID)
	}
	body := fmt.Sprintf(
		`
		规则库ID: %d<br>
		规则库文件名: %s<br>
		规则库类型: %s<br>
		版本描述: %s<br>
		适配产品型号: %s<br>
		适配软件版本: %s<br>
		发布时间: %s<br>
		发布安全云: %s<br>
		<p>规则库链接: <a href="http://feature.aqyfzx.ruijie.net/feature/#/ntos/list">http://feature.aqyfzx.ruijie.net/feature/#/ntos/list</a><p><p>详细状态请前往安全云查看</p>
		`,
		feature.ID,
		feature.FileName,
		feature.FeatureType,
		feature.VersionDesc,
		feature.ProductModels,
		feature.SoftVersions,
		feature.ReleaseDate,
		feature.SecCloud,
	)
	err := libs.SendMail(mailTo, subject, body, []string{})
	// err := libs.SendMail([]string{fmt.Sprintf("%<EMAIL>", "linjiakai")}, subject, body)
	if err != nil {
		logging.ErrorLogger.Error(err)
	}
	logging.DebugLogger.Debugf("send mail", mailTo, subject, body)
}
