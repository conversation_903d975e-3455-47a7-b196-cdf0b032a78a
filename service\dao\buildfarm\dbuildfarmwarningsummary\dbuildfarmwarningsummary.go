package dbuildfarmwarningsummary

import (
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/buildfarm"
	"reflect"

	"github.com/pkg/errors"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "编译农场编译Warning概览"

type User struct {
	ID       uint   `json:"id"`
	Name     string `json:"name"`
	Username string `json:"username"`
}

type BuildfarmWarningSummary struct {
	buildfarm.BuildfarmWarningSummary
	Owner *User `gorm:"->;foreignKey:OwnerID;references:ID" json:"owner"`
}

type ListResponse struct {
	BuildfarmWarningSummary
}

type Request struct {
}

func (this *BuildfarmWarningSummary) ModelName() string {
	return ModelName
}

func Model() *buildfarm.BuildfarmWarningSummary {
	return &buildfarm.BuildfarmWarningSummary{}
}

func (this *BuildfarmWarningSummary) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *BuildfarmWarningSummary) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": res, "total": count, "limit": pageSize}
	return list, nil
}

func (this *BuildfarmWarningSummary) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *BuildfarmWarningSummary) CreateV2(object interface{}) error {
	return nil
}

func (this *BuildfarmWarningSummary) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	return errors.Wrap(err, "")
}

func (this *BuildfarmWarningSummary) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and user_id", id, userId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmWarningSummary) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("bug_id = ? and auditor_id", id, auditorId).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmWarningSummary) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmWarningSummary) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Where("bug_id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (this *BuildfarmWarningSummary) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func BatchCreate(objects []map[string]interface{}) error {
	columns := []string{}
	xt := reflect.TypeOf(Model())
	for i := 0; i < xt.Elem().NumField(); i++ {
		_, ok := xt.Elem().Field(i).Tag.Lookup("update")
		name, ok2 := xt.Elem().Field(i).Tag.Lookup("json")
		if ok && ok2 {
			columns = append(columns, name)
		} else if !ok2 {
			return errors.New("未定义json标签")
		}
	}

	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(Model()).Clauses(clause.OnConflict{
			Columns: []clause.Column{
				{Name: "detail"},
				{Name: "position"},
				{Name: "filename"},
				{Name: "project"},
				{Name: "branch"},
				{Name: "product"},
				{Name: "build_type"},
			},
			DoUpdates: clause.AssignmentColumns(columns),
		}).Create(&objects).Error
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}
