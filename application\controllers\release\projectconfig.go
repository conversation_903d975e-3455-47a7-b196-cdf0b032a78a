package release

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/datasync/dpmsprojectrole"
	"irisAdminApi/service/dao/release/dproject"
	"irisAdminApi/service/dao/release/dreleaseprojectconfig"

	"github.com/kataras/iris/v12"
)

func CreateOrUpdateProjectConfig(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	config := dreleaseprojectconfig.ReleaseProjectConfig{}
	err = config.FindEx("release_project_id", strconv.FormatUint(uint64(id), 10))
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	if config.ID == 0 {
		releaseProject := dproject.Response{}
		err := releaseProject.Find(id)
		if err != nil || releaseProject.ID == 0 {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		if uId != 1 && uId != releaseProject.PmID && uId != releaseProject.PmoID {
			ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, response.PermitErr.Msg))
			return
		}
	} else {
		if uId != 1 && uId != config.ReleaseProject.PmID && uId != config.ReleaseProject.PmoID {
			ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, response.PermitErr.Msg))
			return
		}
	}

	request := dreleaseprojectconfig.Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	// 检查项目分支是否已经被占用
	if request.BuildProjectID != nil && request.BuildProjectBranch != nil {
		configs, err := dreleaseprojectconfig.FindConfigByBuildProjectAndBranch(*request.BuildProjectID, *request.BuildProjectBranch)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}

		if len(configs) > 0 && configs[0].ID != config.ID {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, fmt.Sprintf("编译工程%s分支%s已经被%s项目使用", configs[0].BuildProject.Name, *configs[0].BuildProjectBranch, configs[0].ReleaseProject.Name)))
			return
		}
	}

	configObject := map[string]interface{}{
		"ReleaseProjectID":    request.ReleaseProjectID,
		"EnableNotice":        request.EnableNotice,
		"EnableGcov":          request.EnableGcov,
		"BuildProjectID":      request.BuildProjectID,
		"BuildProjectBranch":  request.BuildProjectBranch,
		"FsKey":               request.FsKey,
		"UserID":              uId,
		"Receivers":           request.Receivers,
		"BugMirror":           request.BugMirror,
		"IntegrateStartAt":    request.IntegrateStartAt,
		"TestStartAt":         request.TestStartAt,
		"FirstBugFixStartAt":  request.FirstBugFixStartAt,
		"SecondTestStartAt":   request.SecondTestStartAt,
		"SecondBugFixStartAt": request.SecondBugFixStartAt,
		"RegressTestStartAt":  request.RegressTestStartAt,
		"ExperimentReleaseAt": request.ExperimentReleaseAt,
		"SendTestLinkRule":    request.SendTestLinkRule,
		"TestLinkRoles":       request.TestLinkRoles,
		"TestLinkReceivers":   request.TestLinkReceivers,
		"BugRoles":            request.BugRoles,
		"BugReceivers":        request.BugReceivers,
	}

	if config.BuildProjectBranch == nil || *config.BuildProjectBranch != "Trunk" {
		if request.BuildProjectID != nil && request.BuildProjectBranch != nil && *request.BuildProjectBranch == "Trunk" {
			_, err := UpdateTrunkBaseCommit(request.ReleaseProjectID, *request.BuildProjectID, *request.BuildProjectBranch)
			if err != nil {
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
				return
			}
		}
	} else if *config.BuildProjectBranch == "Trunk" && (request.BuildProjectID != nil && request.BuildProjectBranch != nil && *request.BuildProjectBranch != "Trunk") {
		_, err := UpdateTrunkReleaseName(request.ReleaseProjectID, *request.BuildProjectID, "Trunk")
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
	}

	if config.ID == 0 {
		configObject["CreatedAt"] = time.Now()
		err = config.Create(configObject)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
	} else {
		configObject["UpdatedAt"] = time.Now()
		err = config.Update(config.ID, configObject)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetProjectConfig(ctx iris.Context) {
	id, err := dao.GetId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	config := dreleaseprojectconfig.ReleaseProjectConfig{}
	err = config.FindEx("release_project_id", strconv.FormatUint(uint64(id), 10))
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	if config.ID == 0 {
		config.TestLinkRoles = "PM,PTM,PGTTL,PTGTTL,CA,PGTTM,PTGTTM,PSDE,PSDPE,PSTL,DOC,TAC"
		config.BugRoles = "PM,PTM,PSTL"
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, config, response.NoErr.Msg))
	return
}

func StartGcovSchedule(ctx iris.Context) {
	err := GcovSchedule()
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetPmsProjectRoles(ctx iris.Context) {
	pmsProjectRole := dpmsprojectrole.PmsProjectRole{}
	list, err := pmsProjectRole.All("", "", "", 10, -1)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, list, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}
