package gcovreport

import (
	"fmt"
	"io"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/buildfarm/dcronmakejob"
	"irisAdminApi/service/dao/buildfarm/dgitjob"
	"irisAdminApi/service/dao/buildfarm/dproject"
	"irisAdminApi/service/dao/datasync/dcommoncomponent"
	"irisAdminApi/service/dao/datasync/dcommonntosfileworkpackage"
	dreleaseproject "irisAdminApi/service/dao/release/dproject"
	"irisAdminApi/service/dao/release/dreleaseprojectconfig"
	"irisAdminApi/service/dao/release/dreleasetrunklog"

	"github.com/kataras/iris/v12"
	"github.com/pkg/errors"
)

var AutoMap = map[string]string{
	"0":   "manual-T",
	"1":   "sntf-T",
	"2":   "manual-D",
	"3":   "sntf-D",
	"all": "all",
}

var cssPattern = regexp.MustCompile(`<link rel="stylesheet".*>`)

func GetJobIDByBranchAndReleeaseID(branch, releaseID string) (string, error) {
	// 通过release_id 获取当时仓库version
	cronMakeJobs, err := dcronmakejob.FindCronMakeJobsByLikeReleaseID(branch, releaseID)
	if err != nil {
		return "", err
	}

	if len(cronMakeJobs) > 0 {
		return cronMakeJobs[0].JobId, nil
	}

	cronMakeJobs, err = dcronmakejob.FindCronMakeJobsByLikeReleaseID("Trunk", releaseID)
	if err != nil {
		return "", err
	}

	if len(cronMakeJobs) > 0 {
		return cronMakeJobs[0].JobId, nil
	}

	// 每日编译查找不到，继续查找普通编译
	gitJobs, err := dgitjob.FindGcovGitJobsByLikeReleaseID(branch, releaseID)
	if err != nil {
		return "", err
	}

	if len(gitJobs) > 0 && len(gitJobs[0].MakeJobs) > 0 {
		return gitJobs[0].MakeJobs[0].TaskID, nil
	}

	gitJobs, err = dgitjob.FindGcovGitJobsByLikeReleaseID("Trunk", releaseID)
	if err != nil {
		return "", err
	}

	if len(gitJobs) > 0 && len(gitJobs[0].MakeJobs) > 0 {
		return gitJobs[0].MakeJobs[0].TaskID, nil
	}

	return "", nil
}

func GetReport(ctx iris.Context) {
	branch := ctx.FormValue("branch")
	releaseID := ctx.FormValue("release_id")
	// componentPacket := ctx.FormValue("component_packet")
	// component := ctx.FormValue("component")
	auto := ctx.FormValue("auto")
	incre := ctx.FormValue("incre")
	// _type := ctx.FormValue("type")
	file := ctx.FormValue("file")

	// 判断 release_id不能为空或者all
	if strings.ToLower(releaseID) == "all" || releaseID == "" {
		ctx.WriteString("releaseID不能为空或者all, 请选择releaseID:")
		return
	}

	// fix branch name
	releaseProject := dreleaseproject.ReleaseProject{}
	err := releaseProject.FindProjectLikeName(branch)
	if err == nil && releaseProject.ID > 0 {
		branch = releaseProject.Name
	}

	// 查找仓库
	repoName := strings.Split(file, "/")[0]
	repo, err := dproject.FindRepoByLikeName(repoName)
	if err != nil {
		ctx.WriteString("查找仓库数据库错误:")
		ctx.WriteString(repoName)
		ctx.WriteString(err.Error())
		return
	}

	if repo.Id == 0 {
		ctx.WriteString("未找文件对应的仓库:")
		ctx.WriteString(repoName)
		return
	}

	// 通过release_id 获取当时仓库version
	// cronMakeJobs, err := dcronmakejob.FindCronMakeJobsByLikeReleaseID(branch, releaseID)
	// if err != nil {
	// 	ctx.WriteString("查找每日编译数据库错误:")
	// 	ctx.WriteString(branch + " " + releaseID)
	// 	ctx.WriteString(err.Error())
	// 	return
	// }

	// if len(cronMakeJobs) == 0 {
	// 	ctx.WriteString("未找到对应编译记录:")
	// 	ctx.WriteString(branch + " " + releaseID)
	// 	return
	// }

	jobID, err := GetJobIDByBranchAndReleeaseID(branch, releaseID)
	if err != nil {
		ctx.WriteString("查找编译数据错误:")
		ctx.WriteString(branch + " " + releaseID)
		ctx.WriteString(err.Error())
		return
	}

	if len(jobID) == 0 {
		ctx.WriteString("未找到对应编译记录:")
		ctx.WriteString(branch + " " + releaseID)
		return
	}

	version, err := GetRepoVersion(jobID, repo.Name)
	if err != nil {
		ctx.WriteString("获取每日编译仓库version错误:")
		ctx.WriteString(jobID + " " + repo.Name)
		ctx.WriteString(err.Error())
		return
	}

	// 通过gitlab api获取源码
	gitPath := strings.Join(strings.Split(file, "/")[1:], "/")
	urlEncodeFullPath := url.QueryEscape(gitPath)

	gitlabUrl := fmt.Sprintf("%s/api/%s/projects/%d/repository/files/%s/raw?private_token=%s&ref=%s",
		libs.Config.Gitlab.Url,
		libs.Config.Gitlab.Version,
		repo.GitlabId,
		urlEncodeFullPath,
		libs.Config.Buildfarm.Token,
		version,
	)

	resp, err := WebClient.R().Get(gitlabUrl)

	if err != nil || resp.IsErrorState() {
		ctx.WriteString("获取源码错误:")
		ctx.WriteString(fmt.Sprintf("%s/api/%s/projects/%d/repository/files/%s/raw?private_token=&ref=%s\n",
			libs.Config.Gitlab.Url,
			libs.Config.Gitlab.Version,
			repo.GitlabId,
			urlEncodeFullPath,
			version,
		))
		ctx.WriteString(resp.String())
		if err != nil {
			ctx.WriteString(err.Error())
		}

		return
	}

	tpmSrcDir := filepath.Join("/tmp", branch, releaseID, filepath.Dir(file))

	err = os.MkdirAll(tpmSrcDir, 0o644)
	if err != nil {
		ctx.WriteString("创建源码目录错误:")
		ctx.WriteString(err.Error())
		return
	}

	tmpSrcPath := filepath.Join("/tmp", branch, releaseID, file)
	// gcovHtmlPath := filepath.Join("/tmp", branch, releaseID, filepath.Dir(commonNtosFileComponent.FullPath), fmt.Sprintf("%s_gcov_html", filepath.Base(commonNtosFileComponent.FullPath)))

	// if _, err := os.Stat(gcovHtmlPath); err == nil {
	// 	fmt.Println(gcovHtmlPath, "已存在1")
	// 	html, err := os.ReadFile(gcovHtmlPath)
	// 	if err == nil {
	// 		fmt.Println(gcovHtmlPath, "已存在2")
	// 		ctx.Header("Content-Type", "text/html")
	// 		ctx.Write(html)
	// 		return
	// 	}
	// }

	err = os.WriteFile(tmpSrcPath, resp.Bytes(), 0o644)
	if err != nil {
		ctx.WriteString("源码文件写入错误:")
		ctx.WriteString(err.Error())
		return
	}

	md5, err := libs.GetFileMd5(tmpSrcPath)
	if err != nil {
		ctx.WriteString("源码文件md5计算错误:")
		ctx.WriteString(err.Error())
		return
	}
	var infoUrl string
	if incre == "0" {
		infoUrl = fmt.Sprintf("http://10.51.135.21:8080/%s/%s/%s/%s-%s", branch, releaseID, auto, md5, filepath.Base(file))
	} else if incre == "1" {
		infoUrl = fmt.Sprintf("http://10.51.135.21:8080/%s-increment/%s/%s/%s-%s", branch, releaseID, auto, md5, filepath.Base(file))
	} else {
		ctx.WriteString("获取gcov info错误:")
		ctx.WriteString("incre类型传参错误")
		return
	}

	fmt.Println(infoUrl)
	resp, err = WebClient.R().Get(infoUrl)
	if err != nil || resp.IsErrorState() {
		ctx.WriteString("获取gcov info错误:")
		ctx.WriteString(infoUrl + "\n")
		ctx.WriteString(resp.String())
		if err != nil {
			ctx.WriteString(err.Error())
		}
		return
	}
	tmpInfoPath := filepath.Join("/tmp", branch, releaseID, filepath.Dir(file), fmt.Sprintf("%s_info", filepath.Base(file)))

	fmt.Println(tmpInfoPath)
	infoBytes := []byte{}
	infoBytes = append(infoBytes, []byte(fmt.Sprintf("SF:%s\n", tmpSrcPath))...)
	infoBytes = append(infoBytes, resp.Bytes()...)
	infoBytes = append(infoBytes, []byte("\nend_of_record")...)
	err = os.WriteFile(tmpInfoPath, infoBytes, 0o644)
	if err != nil {
		ctx.WriteString("gcov info 文件写入错误:")
		ctx.WriteString(err.Error())
		return
	}
	command := fmt.Sprintf("genhtml  --output-directory %s %s", tpmSrcDir, tmpInfoPath)
	fmt.Println(command)
	output, err := libs.ExecCommand(command)
	if err != nil {
		ctx.WriteString("执行genhtml命令错误:")
		ctx.WriteString(output)
		return
	}

	var htmlPath string
	var cssPath string
	filepath.Walk(tpmSrcDir, func(path string, info os.FileInfo, err error) error {
		if strings.HasSuffix(path, "gcov.html") && strings.Contains(path, filepath.Base(file)) {
			htmlPath = path
		}
		if strings.HasSuffix(path, "gcov.css") {
			cssPath = path
		}
		return nil
	})

	html, err := os.ReadFile(htmlPath)
	if err != nil {
		ctx.WriteString("读取html错误:")
		ctx.WriteString(err.Error())
		return
	}

	css, err := os.ReadFile(cssPath)
	if err != nil {
		ctx.WriteString("读取css错误:")
		ctx.WriteString(err.Error())
		return
	}

	htmlStr := cssPattern.ReplaceAllString(string(html), fmt.Sprintf(`<style type="text/css" media="screen">%s</style>`, string(css)))
	// err = os.WriteFile(gcovHtmlPath, []byte(htmlStr), 0644)
	// if err != nil {
	// 	ctx.WriteString("gcov hmtl 文件写入错误:")
	// 	ctx.WriteString(err.Error())
	// 	return
	// }

	ctx.Header("Content-Type", "text/html")
	ctx.WriteString(htmlStr)
	return
	// 通过lcov生成html

	// 构造html目录

	// 返回redirect url
}

func GetRepoVersion(jobID, repoName string) (string, error) {
	archivePath := filepath.Join(libs.Config.Buildfarm.Archivepath, jobID)

	var buildInfoPath string
	filepath.Walk(archivePath, func(path string, info os.FileInfo, err error) error {
		pathSlice := strings.Split(path, "/")
		if pathSlice[len(pathSlice)-1] == "build_info" {
			buildInfoPath = path
			return io.EOF
		}
		return nil
	})

	if buildInfoPath == "" {
		return "", fmt.Errorf("未找到build_info")
	}

	f, err := os.ReadFile(buildInfoPath)
	if err != nil {
		return "", errors.Wrap(err, "打开build_info遇到错误")
	}

	for _, item := range strings.Split(string(f), "\n") {
		if strings.Contains(item, "git@") && strings.Contains(item, repoName) {
			slice := strings.Split(strings.Replace(item, "git:", "git ", -1), " ")
			version := slice[len(slice)-1]
			return version, nil
		}
	}
	return "", fmt.Errorf("未知错误")
}

func GetCommonComponents(ctx iris.Context) {
	items, err := dcommoncomponent.All()
	if err != nil {
		ctx.StatusCode(500)
		ctx.JSON(nil)
		return
	}
	result := map[string]interface{}{}
	for _, item := range items {
		result[item.Path] = item
	}

	ctx.JSON(result)
	return
}

func GetFileRaw(ctx iris.Context) {
	repoName := ctx.FormValue("repo_name")
	commitID := ctx.FormValue("commit_id")
	fp := ctx.FormValue("fp")

	repo, err := dproject.FindRepoByLikeName(repoName)
	if err != nil {
		ctx.WriteString("查找仓库数据库错误:")
		ctx.WriteString(repoName)
		ctx.WriteString(err.Error())
		return
	}

	if repo.Id == 0 {
		ctx.WriteString("未找文件对应的仓库:")
		ctx.WriteString(repoName)
		return
	}

	// 通过gitlab api获取源码

	urlEncodeFullPath := url.QueryEscape(fp)

	gitlabUrl := fmt.Sprintf("%s/api/%s/projects/%d/repository/files/%s/raw?private_token=%s&ref=%s",
		libs.Config.Gitlab.Url,
		libs.Config.Gitlab.Version,
		repo.GitlabId,
		urlEncodeFullPath,
		libs.Config.Buildfarm.Token,
		commitID,
	)

	resp, err := WebClient.R().Get(gitlabUrl)

	if err != nil || resp.IsErrorState() {
		ctx.WriteString("获取源码错误:")
		ctx.WriteString(fmt.Sprintf("%s/api/%s/projects/%d/repository/files/%s/raw?private_token=&ref=%s\n",
			libs.Config.Gitlab.Url,
			libs.Config.Gitlab.Version,
			repo.GitlabId,
			urlEncodeFullPath,
			commitID,
		))
		ctx.WriteString(resp.String())
		if err != nil {
			ctx.WriteString(err.Error())
		}

		return
	}
	ctx.WriteString(resp.String())
	return
}

func InArrayPtr(items []*dcommonntosfileworkpackage.RequestResponse, s *dcommonntosfileworkpackage.RequestResponse) bool {
	for _, item := range items {
		if item == s {
			return true
		}
	}
	return false
}

func GetProjectWorkPackageFiles(ctx iris.Context) {
	projectName := ctx.FormValue("project")
	fileMap := map[string][]*dcommonntosfileworkpackage.RequestResponse{}

	configs, err := dreleaseprojectconfig.FindConfigByBranch(projectName)
	if err != nil {
		ctx.JSON(fileMap)
		return
	}
	if len(configs) == 1 {
		projectName = configs[0].ReleaseProject.Name
	}

	workPackageFiles, err := dcommonntosfileworkpackage.All(projectName)
	if err != nil {
		logging.ErrorLogger.Errorf(err.Error())
		ctx.JSON(fileMap)
		return
	}

	for _, workPackageFile := range workPackageFiles {
		if workPackageFile.Request != nil {
			if _, ok := fileMap[workPackageFile.FullPath]; ok {
				for _, request := range workPackageFile.Request {
					if !InArrayPtr(fileMap[workPackageFile.FullPath], request) {
						fileMap[workPackageFile.FullPath] = append(fileMap[workPackageFile.FullPath], request)
					}
				}
			} else {
				fileMap[workPackageFile.FullPath] = workPackageFile.Request
			}
		}
	}

	ctx.JSON(fileMap)
	return
}

type TrunkLog struct {
	Project          string `json:"project"`
	StartedAt        string `json:"started_at"`
	EndedAt          string `json:"ended_at"`
	StartedReleaseID string `json:"started_release_id"`
	EndedReleaseID   string `json:"ended_release_id"`
}

func GetTrunkLogs(ctx iris.Context) {
	limitStr := ctx.FormValue("limit")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 10
	}
	result := []*TrunkLog{}
	items, err := dreleasetrunklog.AllTrunkLog(limit)
	if err != nil {
		ctx.JSON(result)
		return
	}
	for _, item := range items {
		log := &TrunkLog{
			Project:          item.ReleaseProject.Name,
			StartedAt:        item.StartedAt.Format("2006-01-02 15:04:05"),
			StartedReleaseID: GetReleaseID(item.StartedAt),
		}
		if item.EndedAt.IsZero() {
			now := time.Now()
			log.EndedAt = now.Format("2006-01-02 15:04:05")
			log.EndedReleaseID = GetReleaseID(now)
		} else {
			log.EndedAt = item.EndedAt.Format("2006-01-02 15:04:05")
			log.EndedReleaseID = GetReleaseID(item.EndedAt)
		}
		result = append(result, log)
	}
	ctx.JSON(result)
	return
}

func GetReleaseID(t time.Time) string {
	var aa, bb, cc, dd string

	_aa := t.Year() - 2020
	if _aa <= 9 {
		aa = fmt.Sprintf("0%d", _aa)
	} else {
		aa = fmt.Sprintf("%d", _aa)
	}

	bb = fmt.Sprintf("%d", t.Month()+12)

	_cc := t.Day()
	if _cc <= 9 {
		cc = fmt.Sprintf("0%d", _cc)
	} else {
		cc = fmt.Sprintf("%d", _cc)
	}

	_dd := t.Hour()
	if _dd <= 9 {
		dd = fmt.Sprintf("0%d", _dd)
	} else {
		dd = fmt.Sprintf("%d", _dd)
	}

	return fmt.Sprint(aa, bb, cc, dd)
}
