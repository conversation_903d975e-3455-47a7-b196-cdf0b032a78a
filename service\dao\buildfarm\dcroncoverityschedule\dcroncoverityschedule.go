package dcroncoverityschedule

import (
	"fmt"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/buildfarm"

	"gorm.io/gorm"
)

const ModelName = "每日商业代码检查计划管理"

type CronCoverityWindow struct {
	buildfarm.CronCoverityWindow
	CronCoveritySchedule *CronCoveritySchedule `grom:"->;foreignKey:CronCoverityWindowID;references:ID" json:"cron_coverity_schedule"`
}

type CronCoveritySchedule struct {
	buildfarm.CronCoveritySchedule
	CronCoverityWindow *CronCoverityWindow `grom:"->;foreignKey:CronCoverityWindowID;references:ID" json:"cron_coverity_window"`
}

type ListResponse struct {
	CronCoveritySchedule
}

type CronCoverityScheduleRequest struct {
	CronCoveritySchedule
	Schedules []uint `json:"schedules"`
}

func (a *CronCoveritySchedule) ModelName() string {
	return ModelName
}

func Model() *buildfarm.CronCoveritySchedule {
	return &buildfarm.CronCoveritySchedule{}
}

func (a *CronCoveritySchedule) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("CronCoverityWindow")
	if len(name) > 0 {
		db = db.Where("project = ? or branch = ? ", name, name)
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *CronCoveritySchedule) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *CronCoveritySchedule) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	return err
}

func (this *CronCoveritySchedule) CreateV2(object interface{}) error {
	return nil
}

func (a *CronCoveritySchedule) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *CronCoveritySchedule) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *CronCoveritySchedule) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *CronCoveritySchedule) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *CronCoveritySchedule) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *CronCoveritySchedule) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func FindCronCoverityWindow(time uint, avaliable bool) ([]*CronCoverityWindow, error) {
	items := []*CronCoverityWindow{}
	db := easygorm.GetEasyGormDb().Model(&CronCoverityWindow{}).Preload("CronCoveritySchedule")
	if avaliable {
		db = db.Where("id not in ?", easygorm.GetEasyGormDb().Model(Model()).Select("CronCoverityWindowID"))
	}

	if time > 0 {
		db = db.Where("time = ?", time)
	}

	err := db.Debug().Order("week_day asc, time asc").Find(&items).Error
	return items, err
}

func BatchCreate(project string, objects []map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		err := tx.Unscoped().Delete(Model(), "project = ?", project).Error
		if err != nil {
			return err
		}
		if len(objects) > 0 {
			err = tx.Model(Model()).Create(objects).Error
			return err
		}
		return nil
	})
	return err
}

func BatchDelete(project string) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		err := tx.Unscoped().Delete(Model(), "project = ?", project).Error
		if err != nil {
			return err
		}
		return nil
	})
	return err
}
