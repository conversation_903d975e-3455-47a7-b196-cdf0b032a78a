package release

import (
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"irisAdminApi/application/controllers/buildfarm"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao"
	buildfarmProject "irisAdminApi/service/dao/buildfarm/dproject"
	"irisAdminApi/service/dao/release/dbranch"
	"irisAdminApi/service/dao/release/dbranchswitch"
	"irisAdminApi/service/dao/release/dproject"

	"github.com/kataras/iris/v12"
)

func CreateSwitchBranches(ctx iris.Context) {
	userId, _ := dao.GetAuthId(ctx)
	request := dbranchswitch.Request{}
	if err := ctx.ReadJSON(&request); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(request)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	project := dproject.Response{}
	err := project.Find(request.ReleaseProjectID)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	if !libs.InArrayUint([]uint{project.PmID, project.PmoID, 1, project.CmaID}, userId) {
		ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, "仅允许PM进行分支切换"))
		return
	}
	uuid := libs.GetUUID()
	object := map[string]interface{}{
		"UserID":           userId,
		"AuditorID":        userId,
		"CreatedAt":        time.Now(),
		"ReleaseProjectID": request.ReleaseProjectID,
		"Status":           0,
		"Comment":          request.Comment,
		"Content":          request.Content,
		"Uuid":             uuid,
		"Result":           "",
	}

	err = dao.Create(&dbranchswitch.Response{}, ctx, object)
	if err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	reponse := dbranchswitch.Response{}
	err = reponse.FindEx("uuid", uuid)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	result, err := SwitchBranch(request.ReleaseProjectID, request.Content, request.Comment)
	if err != nil {
		err = reponse.Update(reponse.ID, map[string]interface{}{
			"Status":    2,
			"UpdatedAt": time.Now(),
			"Result":    result,
		})
	} else {
		err = reponse.Update(reponse.ID, map[string]interface{}{
			"Status":    1,
			"UpdatedAt": time.Now(),
			"Result":    result,
		})
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, request, response.NoErr.Msg))
	return
}

func GetSwitchBranches(ctx iris.Context) {
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&dbranchswitch.Response{}, ctx, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

type BranchSwitch struct {
	Unitpackage string
	Newbranch   string
}

func SwitchBranch(ReleaseProjectID uint, content, comment string) (string, error) {
	BranchSwitches := []*BranchSwitch{}
	err := json.Unmarshal([]byte(content), &BranchSwitches)
	if err != nil {
		return "解析切换信息失败", errors.New("解析切换信息失败")
	}

	project := dproject.Response{}
	err = project.Find(ReleaseProjectID)
	if err != nil {
		return "未找到此项目", err
	}

	baseProject := project.BaseReleaseProject.Name

	buildProject, branches, err := dbranch.FindAuditedPassedByProjectID(ReleaseProjectID)
	if err != nil {
		return "查找此项目的编译工程错误", err
	}

	if len(branches) == 0 {
		return "未找到此项目的编译工程", fmt.Errorf("未找到此项目的编译工程")
	}

	newbranch := branches[0].NewBranch
	if newbranch == "" {
		return "未找到当前需要切换的编译工程分支", fmt.Errorf("未找到当前需要切换的编译工程分支")
	}

	// err = dreleaseprojectconfig.UpdateProjectInfoForce(ReleaseProjectID, buildProject.Id, newbranch)
	// if err != nil {
	// 	logging.ErrorLogger.Errorf("update project info error ", err)
	// 	return fmt.Sprintf("强制修改项目编译工程失败: %s", err.Error()), err
	// }

	repo := buildProject.Repo
	dir := filepath.Join("/tmp", newbranch)
	log := []string{}
	command := fmt.Sprintf(`rm %s -rf && git clone -b %s %s %s -v`, dir, newbranch, repo, dir)
	output, err := libs.ExecCommand(command)
	if err != nil {
		log = append(log, err.Error())
		log = append(log, output)
		return fmt.Sprintf("拉取仓库失败: %s", output), err
	}
	log = append(log, output)

	command = fmt.Sprintf("cd %s && echo %s > .release", dir, strings.Replace(project.Name, "NTOS", "", -1))
	output, err = libs.ExecCommand(command)
	if err != nil {
		log = append(log, err.Error())
		log = append(log, output)
		return fmt.Sprintf("修改.release文件失败: %s", output), err
	}
	log = append(log, output)

	for _, item := range BranchSwitches {
		if strings.Contains(item.Unitpackage, "base-os") {
			command = fmt.Sprintf(`cd %s && grep -r BR2_PACKAGE_BASEOS_URL *|awk -F ':' '{print $1}' | xargs sed -i 's|/baseos/\(\S*\)/|/baseos/%s/|g'`, dir, item.Newbranch)
			output, err = libs.ExecCommand(command)
			if err != nil {
				log = append(log, err.Error())
				log = append(log, output)
				return fmt.Sprintf("修改feeds.conf.default文件失败：%s", output), err
			}
			log = append(log, output)
		} else if strings.Contains(item.Unitpackage, "build-product") {
			continue
		} else if strings.Contains(item.Unitpackage, "build-framework") {
			command = fmt.Sprintf(`cd %s && sed -i 's|^BUILD_FRAMEWORK_TAG.*$|BUILD_FRAMEWORK_TAG ?= %s|g' %s`, dir, item.Newbranch, "./public/makefiles/env.mk")
			output, err = libs.ExecCommand(command)
			if err != nil {
				log = append(log, err.Error())
				log = append(log, output)
				return fmt.Sprintf("修改build-framework分支失败：%s", output), err
			}
			log = append(log, output)
		} else {
			a := strings.Split(item.Unitpackage, "/")
			repoName := a[len(a)-1]
			command = fmt.Sprintf(`cd %s && find ./ -name feeds.conf.default|xargs -i sed -i 's|ssh://\(.*\)%s;\(.*$\)|%s;%s|' {}`, dir, repoName, item.Unitpackage, item.Newbranch)
			output, err = libs.ExecCommand(command)
			if err != nil {
				log = append(log, err.Error())
				log = append(log, output)
				return fmt.Sprintf("修改feeds.conf.default文件失败：%s", output), err
			}
			log = append(log, output)
		}
	}

	command = fmt.Sprintf("cd %s && git add * && git commit -a -m '%s'", dir, comment)
	output, err = libs.ExecCommand(command)
	if err != nil {
		if !strings.Contains(output, "nothing to commit, working tree clean") {
			log = append(log, err.Error())
			log = append(log, output)
			return fmt.Sprintf("提交修订失败：%s", output), err
		}
	}
	log = append(log, output)

	// todo: 增加basecommit更新逻辑
	basecommitPath := filepath.Join(dir, "public", "scripts", "basecommit.inc")
	_, err = os.Stat(basecommitPath)

	if err == nil {
		basecommitContent, err := os.ReadFile(basecommitPath)
		if err != nil {
			log = append(log, err.Error())
			return fmt.Sprintf("读取basecommit失败：%s", strings.Join(log, "\n")), err
		}
		if strings.Contains(string(basecommitContent), "NTOSTRUNK_basecommit=") {
			command = fmt.Sprintf("sed -i 's@NTOSTRUNK_@%s_@g' %s", strings.ReplaceAll(newbranch, ".", "_"), basecommitPath)
			output, err = libs.ExecCommand(command)
			if err != nil {
				log = append(log, err.Error())
				log = append(log, output)
				return fmt.Sprintf("Trunk出让分支修订basecommit变量名失败：%s", output), err
			}
			log = append(log, output)

		} else {
			dirs, err := os.ReadDir(dir)
			if err != nil {
				log = append(log, err.Error())
				return fmt.Sprintf("读取目录失败：%s", strings.Join(log, "\n")), err
			}

			for _, prjDir := range dirs {
				if prjDir.IsDir() && strings.HasPrefix(prjDir.Name(), "prj_z") {
					feedPath := filepath.Join(dir, prjDir.Name(), "project", "feeds.conf.default")
					basecommit, err := GetBaseCommitFromFeedsFile(baseProject, project.ID, project.Name, newbranch, feedPath)
					if err != nil {
						log = append(log, err.Error())
						return fmt.Sprintf("生成basecommit失败：%s", strings.Join(log, "\n")), err
					}

					f, err := os.Create(basecommitPath)
					if err != nil {
						log = append(log, err.Error())
						return fmt.Sprintf("写入basecommit失败：%s", strings.Join(log, "\n")), err
					}
					f.WriteString(basecommit)
					f.Close()
					break
				}
			}
		}
	}
	if os.IsNotExist(err) {
		dirs, err := os.ReadDir(dir)
		if err != nil {
			log = append(log, err.Error())
			return fmt.Sprintf("读取目录失败：%s", strings.Join(log, "\n")), err
		}

		for _, prjDir := range dirs {
			if prjDir.IsDir() && strings.HasPrefix(prjDir.Name(), "prj_z") {
				feedPath := filepath.Join(dir, prjDir.Name(), "project", "feeds.conf.default")
				basecommit, err := GetBaseCommitFromFeedsFile(baseProject, project.ID, project.Name, newbranch, feedPath)
				if err != nil {
					log = append(log, err.Error())
					return fmt.Sprintf("生成basecommit失败：%s", strings.Join(log, "\n")), err
				}

				f, err := os.Create(basecommitPath)
				if err != nil {
					log = append(log, err.Error())
					return fmt.Sprintf("写入basecommit失败：%s", strings.Join(log, "\n")), err
				}
				f.WriteString(basecommit)
				f.Close()
				break
			}
		}
	}

	command = fmt.Sprintf("cd %s && git add * && git commit -a -m '%s'", dir, fmt.Sprintf("%s 分支更新gcov基线信息", newbranch))
	output, err = libs.ExecCommand(command)
	if err != nil {
		if !strings.Contains(output, "nothing to commit, working tree clean") {
			log = append(log, err.Error())
			log = append(log, output)
			return fmt.Sprintf("提交修订失败：%s", output), err
		}
	}
	log = append(log, output)

	command = fmt.Sprintf("cd %s && git push origin %s", dir, newbranch)
	output, err = libs.ExecCommand(command)
	if err != nil {
		log = append(log, err.Error())
		log = append(log, output)
		return fmt.Sprintf("推送修订失败：%s", output), err
	}
	log = append(log, output)

	return fmt.Sprintf("切换成功：%s", strings.Join(log, "\n")), nil
}

func UpdateTrunkBaseCommit(ReleaseProjectID, buildProjectID uint, buildProjectBranch string) (string, error) {
	project := dproject.Response{}
	err := project.Find(ReleaseProjectID)
	if err != nil {
		return "查找项目数据库错误", err
	}

	baseProject := project.BaseReleaseProject.Name
	buildProject := buildfarmProject.Response{}
	err = buildProject.Find(buildProjectID)
	if err != nil {
		return "查找编译工程数据库错误", err
	}

	if buildProject.Id == 0 {
		return "未配置编译工程", fmt.Errorf("未配置编译工程")
	}

	comment := fmt.Sprintf("%s分支更新gcov基线信息", buildProjectBranch)

	repo := buildProject.Repo
	dir := filepath.Join("/tmp", buildProjectBranch)
	command := fmt.Sprintf(`rm %s -rf && git clone -b %s %s %s -v`, dir, buildProjectBranch, repo, dir)
	log := []string{}
	output, err := libs.ExecCommand(command)
	if err != nil {
		log = append(log, err.Error())
		log = append(log, output)
		return fmt.Sprintf("拉取编译工程失败：%s", strings.Join(log, "\n")), err
	}
	log = append(log, output)

	// todo: 获取仓库commitid

	dirs, err := os.ReadDir(dir)
	if err != nil {
		log = append(log, err.Error())
		log = append(log, output)
		return fmt.Sprintf("读取目录失败：%s", strings.Join(log, "\n")), err
	}

	for _, prjDir := range dirs {
		if prjDir.IsDir() && strings.HasPrefix(prjDir.Name(), "prj_z") {
			feedPath := filepath.Join(dir, prjDir.Name(), "project", "feeds.conf.default")
			basecommit, err := GetBaseCommitFromFeedsFile(baseProject, project.ID, project.Name, buildProjectBranch, feedPath)
			if err != nil {
				log = append(log, err.Error())
				log = append(log, output)
				return fmt.Sprintf("生成basecommit失败：%s", strings.Join(log, "\n")), err
			}
			basecommitPath := filepath.Join(dir, "public", "scripts", "basecommit.inc")
			f, err := os.Create(basecommitPath)
			if err != nil {
				log = append(log, err.Error())
				log = append(log, output)
				return fmt.Sprintf("写入basecommit失败：%s", strings.Join(log, "\n")), err
			}
			f.WriteString(basecommit)
			f.Close()
			break
		}
	}

	command = fmt.Sprintf("cd %s && echo %s > .release", dir, strings.Replace(project.Name, "NTOS", "", -1))
	output, err = libs.ExecCommand(command)
	if err != nil {
		log = append(log, err.Error())
		log = append(log, output)
		return fmt.Sprintf("修改.release文件失败: %s", output), err
	} else {
		comment = fmt.Sprintf("%s 更新.release文件为%s", comment, strings.Replace(project.Name, "NTOS", "", -1))
	}
	log = append(log, output)

	command = fmt.Sprintf("cd %s && git add * && git commit -a -m '%s'", dir, comment)
	output, err = libs.ExecCommand(command)
	if err != nil {
		if !strings.Contains(output, "nothing to commit, working tree clean") {
			log = append(log, err.Error())
			log = append(log, output)
			return fmt.Sprintf("提交修订失败：%s", output), err
		}
	}
	log = append(log, output)

	command = fmt.Sprintf("cd %s && git push origin %s", dir, buildProjectBranch)
	output, err = libs.ExecCommand(command)
	if err != nil {
		log = append(log, err.Error())
		log = append(log, output)
		return fmt.Sprintf("推送修订失败：%s", output), err
	}
	log = append(log, output)

	return fmt.Sprintf("切换成功：%s", strings.Join(log, "\n")), nil
}

func UpdateTrunkReleaseName(ReleaseProjectID, buildProjectID uint, buildProjectBranch string) (string, error) {
	buildProject := buildfarmProject.Response{}
	err := buildProject.Find(buildProjectID)
	if err != nil {
		return "查找编译工程数据库错误", err
	}

	if buildProject.Id == 0 {
		return "未配置编译工程", fmt.Errorf("未配置编译工程")
	}
	var comment string

	repo := buildProject.Repo
	dir := filepath.Join("/tmp", buildProjectBranch)
	command := fmt.Sprintf(`rm %s -rf && git clone -b %s %s %s -v`, dir, buildProjectBranch, repo, dir)
	log := []string{}
	output, err := libs.ExecCommand(command)
	if err != nil {
		log = append(log, err.Error())
		log = append(log, output)
		return fmt.Sprintf("拉取编译工程失败：%s", strings.Join(log, "\n")), err
	}
	log = append(log, output)

	command = fmt.Sprintf("cd %s && echo %s > .release", dir, buildProjectBranch)
	output, err = libs.ExecCommand(command)
	if err != nil {
		log = append(log, err.Error())
		log = append(log, output)
		return fmt.Sprintf("修改.release文件失败: %s", output), err
	} else {
		comment = fmt.Sprintf("%s 更新.release文件为Trunk", comment)
	}
	log = append(log, output)

	command = fmt.Sprintf("cd %s && git add * && git commit -a -m '%s'", dir, comment)
	output, err = libs.ExecCommand(command)
	if err != nil {
		if !strings.Contains(output, "nothing to commit, working tree clean") {
			log = append(log, err.Error())
			log = append(log, output)
			return fmt.Sprintf("提交修订失败：%s", output), err
		}
	}
	log = append(log, output)

	command = fmt.Sprintf("cd %s && git push origin %s", dir, buildProjectBranch)
	output, err = libs.ExecCommand(command)
	if err != nil {
		log = append(log, err.Error())
		log = append(log, output)
		return fmt.Sprintf("推送修订失败：%s", output), err
	}
	log = append(log, output)

	return fmt.Sprintf("执行成功：%s", strings.Join(log, "\n")), nil
}

func GetBaseCommitFromFeedsFile(baseReleaseProject string, releaseProjectID uint, releaseProject, branch, fp string) (string, error) {
	b, err := os.ReadFile(fp)
	if err != nil {
		return "", err
	}

	basecommitSlice := []string{
		fmt.Sprintf("# %s(%s) base from: %s(%s)", branch, releaseProject, baseReleaseProject, time.Now().Format("2006-01-02")),
	}

	// if branch == "Trunk" {
	// 	basecommitSlice = append(
	// 		basecommitSlice,
	// 		"declare -A NTOSTRUNK_basecommit=(",
	// 	)
	// } else {
	basecommitSlice = append(
		basecommitSlice,
		fmt.Sprintf("declare -A %s_basecommit=(", strings.ToUpper(strings.ReplaceAll(releaseProject, ".", "_"))),
	)
	// }

	lines := strings.Split(strings.TrimSpace(string(b)), "\n")
	for _, line := range lines {
		if !strings.HasPrefix(line, "src-git-full") {
			continue
		}
		dirArr := strings.Fields(line)
		dirName := dirArr[2]
		repoName := libs.PickProjectFromFeeds(line)
		branchArr := strings.Split(line, ";")
		branch := branchArr[len(branchArr)-1]
		// 非Trunk分支，改为从分支申请表获取版本号
		// fix:
		var commitID string
		unitpackage := buildfarmProject.Response{}
		err = unitpackage.FindEx("name", repoName)
		if err != nil {
			return "查找编译工程数据库错误", err
		}

		if branch != "Trunk" {
			// fixme: 复用分支的情况下，使用该分支基线版本号
			releaseBranches, err := dbranch.FindBranchByUnitPackageAndNewBranchAndStatus(unitpackage.GitlabId, branch)
			if err != nil {
				return "查找分支数据库错误", err
			}
			if len(releaseBranches) > 0 && releaseBranches[0].BaseVerison != "" {
				commitID = releaseBranches[0].BaseVerison
			} else {
				return "基线版本号为空", fmt.Errorf("%d %d %s %s 基线版本号为空", releaseProjectID, unitpackage.Id, repoName, branch)
			}

		} else {
			// commitID, err = buildfarm.GetCommitIDFromGtilab(repoName, branch)
			// if err != nil {
			// 	return "", err
			// }

			releaseBranches, err := dbranch.FindBranchByUnitPackageAndBaseBranchAndStatus(unitpackage.GitlabId, branch, 1)
			if err != nil {
				return "查找分支数据库错误", err
			}
			if len(releaseBranches) > 0 && releaseBranches[0].BaseVerison != "" {
				commitID = releaseBranches[0].BaseVerison
			} else {
				// return "基线版本号为空", fmt.Errorf("基线版本号为空")
				commitID, err = buildfarm.GetCommitIDFromGtilab(repoName, branch)
				if err != nil {
					return "基线版本号为空", err
				}
			}
		}

		basecommitSlice = append(basecommitSlice, fmt.Sprintf(`    ["%s"]="%s"`, dirName, commitID))
	}

	basecommitSlice = append(basecommitSlice, `)`)
	return strings.Join(basecommitSlice, "\n"), nil
}
